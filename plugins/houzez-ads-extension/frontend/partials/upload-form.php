<?php
/**
 * Campaign upload form template using Houzez property form UI
 *
 * @package HouzezAdsExtension
 * @subpackage HouzezAdsExtension/frontend/partials
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
	die;
}

$ad_types = houzez_ads_get_available_ad_types();
$ad_zones = houzez_ads_get_available_zones();
$durations = houzez_ads_get_available_durations();
$user_properties = houzez_ads_get_user_properties();
?>

<div class="dashboard-content-block-wrap">
	<div class="dashboard-content-block">
		<div class="dashboard-content-block-title">
			<?php _e( 'Create New Ad Campaign', 'houzez-ads-extension' ); ?>
		</div>

		<form autocomplete="off" id="houzez-ads-upload-form" method="post" action="#" enctype="multipart/form-data" class="add-frontend-property" novalidate>
			<?php wp_nonce_field( 'houzez_ads_upload_form', 'houzez_ads_nonce' ); ?>

			<div class="validate-errors alert alert-danger alert-dismissible fade show houzez-hidden" role="alert">
				<strong><?php _e( 'Error!', 'houzez-ads-extension' ); ?></strong> <?php _e( 'Please fill in all required fields.', 'houzez-ads-extension' ); ?>
				<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
			</div>

			<!-- Campaign Type Section -->
			<div class="form-step">
				<div class="dashboard-content-block-wrap">
					<div class="dashboard-content-block">
						<div class="dashboard-content-block-title">
							<?php _e( 'Campaign Details', 'houzez-ads-extension' ); ?>
						</div>

						<div class="row">
							<div class="col-md-6">
								<div class="form-group mb-3">
									<label class="form-label" for="ad_type">
										<?php _e( 'Ad Type', 'houzez-ads-extension' ); ?> <span class="required-field">*</span>
									</label>
									<select name="ad_type" id="ad_type" class="selectpicker form-control" title="<?php _e( 'Select Ad Type', 'houzez-ads-extension' ); ?>" data-live-search="true" required>
										<?php foreach ( $ad_types as $key => $label ) : ?>
											<option value="<?php echo esc_attr( $key ); ?>"><?php echo esc_html( $label ); ?></option>
										<?php endforeach; ?>
									</select>
								</div>
							</div>

							<div class="col-md-6">
								<div class="form-group mb-3">
									<label class="form-label" for="ad_zone">
										<?php _e( 'Ad Zone', 'houzez-ads-extension' ); ?> <span class="required-field">*</span>
									</label>
									<select name="ad_zone" id="ad_zone" class="selectpicker form-control" title="<?php _e( 'Select Ad Zone', 'houzez-ads-extension' ); ?>" data-live-search="true" required>
										<?php foreach ( $ad_zones as $key => $label ) : ?>
											<option value="<?php echo esc_attr( $key ); ?>"><?php echo esc_html( $label ); ?></option>
										<?php endforeach; ?>
									</select>
								</div>
							</div>
						</div>

						<div class="row">
							<div class="col-md-6">
								<div class="form-group mb-3">
									<label class="form-label" for="duration">
										<?php _e( 'Campaign Duration', 'houzez-ads-extension' ); ?> <span class="required-field">*</span>
									</label>
									<select name="duration" id="duration" class="selectpicker form-control" title="<?php _e( 'Select Duration', 'houzez-ads-extension' ); ?>" required>
										<?php foreach ( $durations as $key => $label ) : ?>
											<option value="<?php echo esc_attr( $key ); ?>"><?php echo esc_html( $label ); ?></option>
										<?php endforeach; ?>
									</select>
								</div>
							</div>

							<div class="col-md-6">
								<div class="form-group mb-3 houzez-ads-pricing-display" style="display: none;">
									<label class="form-label">
										<?php _e( 'Campaign Price', 'houzez-ads-extension' ); ?>
									</label>
									<div class="alert alert-info">
										<strong class="houzez-ads-price">$0.00</strong>
									</div>
								</div>
							</div>
						</div>

						<!-- Property Selection (shown only for property type ads) -->
						<div class="houzez-ads-property-selection-wrapper" style="display: none;">
							<div class="form-group mb-3">
								<label class="form-label">
									<?php _e( 'Select Properties to Promote', 'houzez-ads-extension' ); ?> <span class="required-field">*</span>
								</label>
								<div class="houzez-ads-properties-selection border-all p-3" style="max-height: 300px; overflow-y: auto;">
									<?php if ( ! empty( $user_properties ) ) : ?>
										<?php foreach ( $user_properties as $property ) : ?>
											<div class="form-check mb-2">
												<input type="checkbox"
													   name="selected_properties[]"
													   value="<?php echo esc_attr( $property->ID ); ?>"
													   id="property_<?php echo esc_attr( $property->ID ); ?>"
													   class="form-check-input" />
												<label class="form-check-label" for="property_<?php echo esc_attr( $property->ID ); ?>">
													<?php echo esc_html( $property->post_title ); ?>
												</label>
											</div>
										<?php endforeach; ?>
									<?php else : ?>
										<div class="alert alert-warning">
											<?php _e( 'No properties found. Please create some properties first.', 'houzez-ads-extension' ); ?>
										</div>
									<?php endif; ?>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!-- Banner Media Section -->
			<div class="form-step">
				<div class="dashboard-content-block-wrap">
					<div class="dashboard-content-block">
						<div class="dashboard-content-block-title">
							<?php _e( 'Banner Media & Links', 'houzez-ads-extension' ); ?>
						</div>

						<div class="row">
							<div class="col-md-6">
								<div class="form-group mb-3">
									<label class="form-label" for="banner_image">
										<?php _e( 'Banner Image URL', 'houzez-ads-extension' ); ?> <span class="required-field">*</span>
									</label>
									<input type="url"
										   name="banner_image"
										   id="banner_image"
										   class="form-control"
										   placeholder="<?php _e( 'Enter the URL of your banner image', 'houzez-ads-extension' ); ?>"
										   required />
									<small class="form-text text-muted"><?php _e( 'Upload your banner image to a hosting service and paste the URL here', 'houzez-ads-extension' ); ?></small>
								</div>
							</div>

							<div class="col-md-6">
								<div class="form-group mb-3">
									<label class="form-label" for="banner_link">
										<?php _e( 'Banner Link URL', 'houzez-ads-extension' ); ?> <span class="required-field">*</span>
									</label>
									<input type="url"
										   name="banner_link"
										   id="banner_link"
										   class="form-control"
										   placeholder="<?php _e( 'Where should the banner link to?', 'houzez-ads-extension' ); ?>"
										   required />
									<small class="form-text text-muted"><?php _e( 'Enter the destination URL when users click your banner', 'houzez-ads-extension' ); ?></small>
								</div>
							</div>
						</div>

						<div class="row">
							<div class="col-md-6">
								<div class="form-group mb-3">
									<label class="form-label" for="banner_alt">
										<?php _e( 'Banner Alt Text', 'houzez-ads-extension' ); ?>
									</label>
									<input type="text"
										   name="banner_alt"
										   id="banner_alt"
										   class="form-control"
										   placeholder="<?php _e( 'Describe the banner for accessibility', 'houzez-ads-extension' ); ?>" />
									<small class="form-text text-muted"><?php _e( 'Brief description of your banner for screen readers', 'houzez-ads-extension' ); ?></small>
								</div>
							</div>

							<div class="col-md-6">
								<div class="houzez-ads-image-preview-container">
									<!-- Image preview will be shown here -->
								</div>
							</div>
						</div>

						<!-- Banner Guidelines -->
						<div class="alert alert-info">
							<h5><?php _e( 'Banner Guidelines', 'houzez-ads-extension' ); ?></h5>
							<ul class="mb-0">
								<li><?php _e( 'Homepage banners: 1200x300px recommended', 'houzez-ads-extension' ); ?></li>
								<li><?php _e( 'Sidebar banners: 300x250px recommended', 'houzez-ads-extension' ); ?></li>
								<li><?php _e( 'Search result banners: 728x90px recommended', 'houzez-ads-extension' ); ?></li>
								<li><?php _e( 'Property detail banners: 468x60px recommended', 'houzez-ads-extension' ); ?></li>
								<li><?php _e( 'Use high-quality images in JPG or PNG format', 'houzez-ads-extension' ); ?></li>
								<li><?php _e( 'Keep file size under 500KB for faster loading', 'houzez-ads-extension' ); ?></li>
							</ul>
						</div>
					</div>
				</div>
			</div>

			<!-- Submit Section -->
			<div class="d-flex justify-content-between p-2 add-new-listing-bottom-nav-wrap">
				<a href="<?php echo esc_url( houzez_ads_get_dashboard_url() ); ?>" class="btn-cancel btn btn-primary-outlined">
					<?php _e( 'Cancel', 'houzez-ads-extension' ); ?>
				</a>

				<button type="submit" class="btn houzez-submit-js btn-primary">
					<?php get_template_part('template-parts/loader'); ?>
					<?php _e( 'Create Campaign', 'houzez-ads-extension' ); ?>
				</button>
			</div>
		</form>
	</div>
</div>
