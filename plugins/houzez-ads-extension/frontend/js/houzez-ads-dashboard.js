/**
 * Dashboard JavaScript for Houzez Ads Extension
 * Provides enhanced functionality for the Houzez dashboard integration
 */

(function($) {
    'use strict';

    $(document).ready(function() {
        
        // Initialize dashboard functionality
        initDashboardFeatures();
        
        // Handle campaign deletion
        handleCampaignDeletion();
        
        // Handle table filtering and search
        initTableFiltering();
        
        // Handle form submissions
        handleFormSubmissions();
        
        // Initialize tooltips and popovers if available
        initBootstrapComponents();
    });

    /**
     * Initialize dashboard features
     */
    function initDashboardFeatures() {
        // Animate stats on load
        animateStats();
        
        // Initialize selectpicker if available
        if ($.fn.selectpicker) {
            $('.selectpicker').selectpicker({
                style: 'btn-outline-secondary',
                size: 4
            });
        }
        
        // Auto-refresh campaign stats every 5 minutes
        setInterval(refreshCampaignStats, 300000);
    }

    /**
     * Animate dashboard stats
     */
    function animateStats() {
        $('.stat-number').each(function() {
            var $this = $(this);
            var countTo = parseInt($this.text().replace(/,/g, ''));
            
            if (isNaN(countTo)) return;
            
            $({ countNum: 0 }).animate({
                countNum: countTo
            }, {
                duration: 2000,
                easing: 'swing',
                step: function() {
                    $this.text(Math.floor(this.countNum).toLocaleString());
                },
                complete: function() {
                    $this.text(countTo.toLocaleString());
                }
            });
        });
    }

    /**
     * Handle campaign deletion
     */
    function handleCampaignDeletion() {
        $(document).on('click', '.delete-campaign', function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var campaignId = $button.data('campaign-id');
            var campaignTitle = $button.data('campaign-title') || 'this campaign';
            
            // Show confirmation dialog
            if (!confirm(houzez_ads_dashboard.strings.confirm_delete.replace('%s', campaignTitle))) {
                return;
            }
            
            // Show loading state
            $button.prop('disabled', true).text(houzez_ads_dashboard.strings.deleting);
            
            // Send AJAX request
            $.ajax({
                url: houzez_ads_dashboard.ajax_url,
                type: 'POST',
                data: {
                    action: 'houzez_ads_delete_campaign',
                    campaign_id: campaignId,
                    nonce: houzez_ads_dashboard.nonce
                },
                success: function(response) {
                    if (response.success) {
                        // Show success message
                        showNotification(response.data.message, 'success');
                        
                        // Remove row with animation
                        $button.closest('tr').fadeOut(300, function() {
                            $(this).remove();
                            updateTableDisplay();
                        });
                    } else {
                        showNotification(response.data.message || houzez_ads_dashboard.strings.error, 'error');
                        $button.prop('disabled', false).text('Delete');
                    }
                },
                error: function() {
                    showNotification(houzez_ads_dashboard.strings.error, 'error');
                    $button.prop('disabled', false).text('Delete');
                }
            });
        });
    }

    /**
     * Initialize table filtering
     */
    function initTableFiltering() {
        var $table = $('.table tbody');
        var $searchInput = $('#campaign-search');
        var $statusFilter = $('#campaign-status-filter');
        var $zoneFilter = $('#campaign-zone-filter');
        
        // Search functionality
        $searchInput.on('keyup', function() {
            var searchTerm = $(this).val().toLowerCase();
            filterTable();
        });
        
        // Status filter
        $statusFilter.on('change', function() {
            filterTable();
        });
        
        // Zone filter
        $zoneFilter.on('change', function() {
            filterTable();
        });
        
        function filterTable() {
            var searchTerm = $searchInput.val().toLowerCase();
            var statusFilter = $statusFilter.val();
            var zoneFilter = $zoneFilter.val();
            
            $table.find('tr').each(function() {
                var $row = $(this);
                var text = $row.text().toLowerCase();
                var status = $row.data('status');
                var zone = $row.data('zone');
                
                var showRow = true;
                
                // Apply search filter
                if (searchTerm && text.indexOf(searchTerm) === -1) {
                    showRow = false;
                }
                
                // Apply status filter
                if (statusFilter && status !== statusFilter) {
                    showRow = false;
                }
                
                // Apply zone filter
                if (zoneFilter && zone !== zoneFilter) {
                    showRow = false;
                }
                
                $row.toggle(showRow);
            });
            
            updateTableDisplay();
        }
    }

    /**
     * Update table display after filtering
     */
    function updateTableDisplay() {
        var $table = $('.table tbody');
        var visibleRows = $table.find('tr:visible').length;
        
        if (visibleRows === 0) {
            if (!$table.find('.no-results-row').length) {
                $table.append(
                    '<tr class="no-results-row">' +
                    '<td colspan="100%" class="text-center py-4">' +
                    '<i class="houzez-icon icon-search-1" style="font-size: 48px; color: #ddd;"></i>' +
                    '<p class="mt-3 mb-0">No campaigns found matching your criteria.</p>' +
                    '</td>' +
                    '</tr>'
                );
            }
        } else {
            $table.find('.no-results-row').remove();
        }
    }

    /**
     * Handle form submissions
     */
    function handleFormSubmissions() {
        // Handle campaign creation form
        $('#houzez-ads-upload-form').on('submit', function(e) {
            e.preventDefault();
            
            var $form = $(this);
            var $submitBtn = $form.find('.houzez-submit-js');
            var $loader = $submitBtn.find('.loader-ripple');
            
            // Validate form
            if (!validateForm($form)) {
                return;
            }
            
            // Show loading state
            $submitBtn.prop('disabled', true);
            if ($loader.length) {
                $loader.show();
            }
            
            // Collect form data
            var formData = new FormData($form[0]);
            
            // Send AJAX request
            $.ajax({
                url: houzez_ads_dashboard.ajax_url,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.success) {
                        showNotification(response.data.message, 'success');
                        
                        // Redirect after delay
                        setTimeout(function() {
                            if (response.data.redirect_url) {
                                window.location.href = response.data.redirect_url;
                            }
                        }, 1500);
                    } else {
                        showNotification(response.data.message || 'An error occurred', 'error');
                    }
                },
                error: function() {
                    showNotification('An error occurred while processing your request', 'error');
                },
                complete: function() {
                    $submitBtn.prop('disabled', false);
                    if ($loader.length) {
                        $loader.hide();
                    }
                }
            });
        });
    }

    /**
     * Validate form
     */
    function validateForm($form) {
        var isValid = true;
        var errors = [];
        
        // Check required fields
        $form.find('[required]').each(function() {
            var $field = $(this);
            var value = $field.val();
            
            if (!value || ($.isArray(value) && value.length === 0)) {
                isValid = false;
                var label = $field.closest('.form-group').find('label').text().replace('*', '').trim();
                errors.push(label + ' is required');
                $field.addClass('is-invalid');
            } else {
                $field.removeClass('is-invalid');
            }
        });
        
        // Show errors
        if (!isValid) {
            var $errorContainer = $form.find('.validate-errors');
            if ($errorContainer.length) {
                $errorContainer.removeClass('houzez-hidden').find('strong').next().html(errors.join('<br>'));
            } else {
                showNotification(errors.join('<br>'), 'error');
            }
        }
        
        return isValid;
    }

    /**
     * Initialize Bootstrap components
     */
    function initBootstrapComponents() {
        // Initialize tooltips
        if ($.fn.tooltip) {
            $('[data-bs-toggle="tooltip"]').tooltip();
        }
        
        // Initialize popovers
        if ($.fn.popover) {
            $('[data-bs-toggle="popover"]').popover();
        }
    }

    /**
     * Refresh campaign stats
     */
    function refreshCampaignStats() {
        $.ajax({
            url: houzez_ads_dashboard.ajax_url,
            type: 'POST',
            data: {
                action: 'houzez_ads_refresh_stats',
                nonce: houzez_ads_dashboard.nonce
            },
            success: function(response) {
                if (response.success && response.data.stats) {
                    updateStatsDisplay(response.data.stats);
                }
            }
        });
    }

    /**
     * Update stats display
     */
    function updateStatsDisplay(stats) {
        $('.dashboard-stat-item').each(function() {
            var $item = $(this);
            var statType = $item.data('stat-type');
            
            if (stats[statType] !== undefined) {
                $item.find('.stat-number').text(stats[statType].toLocaleString());
            }
        });
    }

    /**
     * Show notification
     */
    function showNotification(message, type) {
        // Try to use Houzez notification system
        if (typeof houzez_show_notification === 'function') {
            houzez_show_notification(message, type);
        } else if (typeof toastr !== 'undefined') {
            toastr[type](message);
        } else {
            // Fallback to custom notification
            var alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
            var $notification = $('<div class="alert ' + alertClass + ' alert-dismissible fade show" role="alert">' +
                message +
                '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                '</div>');
            
            $('body').prepend($notification);
            
            setTimeout(function() {
                $notification.fadeOut(300, function() {
                    $(this).remove();
                });
            }, 5000);
        }
    }

})(jQuery);
