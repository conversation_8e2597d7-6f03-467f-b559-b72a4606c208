/**
 * Frontend JavaScript for Houzez Ads Extension
 */

(function($) {
    'use strict';

    $(document).ready(function() {
        
        // Track ad impressions when 50% visible
        function trackImpressions() {
            $('.houzez-ad-campaign').each(function() {
                var $ad = $(this);
                var campaignId = $ad.data('campaign-id');
                
                if (campaignId && !$ad.data('impression-tracked')) {
                    if (isElementInViewport($ad[0], 0.5)) {
                        $ad.data('impression-tracked', true);
                        
                        $.ajax({
                            url: houzez_ads_ajax.ajax_url,
                            type: 'POST',
                            data: {
                                action: 'houzez_ads_track_impression',
                                campaign_id: campaignId,
                                nonce: houzez_ads_ajax.nonce
                            }
                        });
                    }
                }
            });
        }

        // Check if element is in viewport
        function isElementInViewport(el, threshold) {
            threshold = threshold || 0.5;
            var rect = el.getBoundingClientRect();
            var windowHeight = window.innerHeight || document.documentElement.clientHeight;
            var windowWidth = window.innerWidth || document.documentElement.clientWidth;
            
            var vertInView = (rect.top <= windowHeight) && ((rect.top + rect.height * threshold) >= 0);
            var horInView = (rect.left <= windowWidth) && ((rect.left + rect.width) >= 0);
            
            return vertInView && horInView;
        }

        // Track impressions on scroll and load
        $(window).on('scroll resize', trackImpressions);
        trackImpressions();

        // Handle campaign form submission with Houzez styling
        $('#houzez-ads-upload-form').on('submit', function(e) {
            e.preventDefault();

            var $form = $(this);
            var $submitBtn = $form.find('.houzez-submit-js');
            var $loader = $submitBtn.find('.loader-ripple');

            // Show loader and disable button
            $submitBtn.prop('disabled', true);
            if ($loader.length) {
                $loader.show();
            }

            // Hide any existing errors
            $('.validate-errors').addClass('houzez-hidden');

            // Validate form
            if (!validateHouzezForm($form)) {
                $submitBtn.prop('disabled', false);
                if ($loader.length) {
                    $loader.hide();
                }
                return;
            }

            // Collect campaign data
            var campaignData = {
                ad_type: $form.find('[name="ad_type"]').val(),
                ad_zone: $form.find('[name="ad_zone"]').val(),
                duration: $form.find('[name="duration"]').val(),
                selected_properties: $form.find('[name="selected_properties[]"]:checked').map(function() {
                    return this.value;
                }).get(),
                banner_image: $form.find('[name="banner_image"]').val(),
                banner_link: $form.find('[name="banner_link"]').val(),
                banner_alt: $form.find('[name="banner_alt"]').val()
            };

            // Add to cart via AJAX
            $.ajax({
                url: houzez_ads_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'houzez_ads_add_to_cart',
                    zone: campaignData.ad_zone,
                    duration: campaignData.duration,
                    campaign_data: campaignData,
                    nonce: houzez_ads_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        // Show success message using Houzez notification style
                        showHouzezNotification(response.data.message, 'success');

                        // Redirect to cart after short delay
                        setTimeout(function() {
                            if (response.data.cart_url) {
                                window.location.href = response.data.cart_url;
                            }
                        }, 1500);
                    } else {
                        showHouzezNotification(response.data.message || 'An error occurred', 'error');
                    }
                },
                error: function() {
                    showHouzezNotification('An error occurred while processing your request', 'error');
                },
                complete: function() {
                    $submitBtn.prop('disabled', false);
                    if ($loader.length) {
                        $loader.hide();
                    }
                }
            });
        });

        // Dynamic pricing calculation
        function updatePricing() {
            var $form = $('#houzez-ads-upload-form');
            var adZone = $form.find('[name="ad_zone"]').val();
            var duration = $form.find('[name="duration"]').val();
            var adType = $form.find('[name="ad_type"]').val();
            var selectedProperties = $form.find('[name="selected_properties[]"]:checked').length;
            
            if (adZone && duration) {
                var quantity = (adType === 'property' && selectedProperties > 0) ? selectedProperties : 1;
                
                $.ajax({
                    url: houzez_ads_ajax.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'houzez_ads_get_pricing',
                        ad_zone: adZone,
                        duration: duration,
                        quantity: quantity,
                        ad_type: adType,
                        nonce: houzez_ads_ajax.nonce
                    },
                    success: function(response) {
                        if (response.success) {
                            $('.houzez-ads-pricing-display .houzez-ads-price').text(response.data.formatted_price);
                            $('.houzez-ads-pricing-display').show();
                        }
                    }
                });
            } else {
                $('.houzez-ads-pricing-display').hide();
            }
        }

        // Update pricing when form fields change
        $('#houzez-ads-upload-form').on('change', '[name="ad_zone"], [name="duration"], [name="ad_type"], [name="selected_properties[]"]', updatePricing);

        // Show/hide property selection based on ad type with Houzez animations
        $('[name="ad_type"]').on('change', function() {
            var adType = $(this).val();
            var $propertySelection = $('.houzez-ads-property-selection-wrapper');

            if (adType === 'property') {
                $propertySelection.slideDown(300);
            } else {
                $propertySelection.slideUp(300);
                $('[name="selected_properties[]"]').prop('checked', false);
            }

            updatePricing();
        });

        // Initialize selectpicker if available (Houzez uses Bootstrap Select)
        if ($.fn.selectpicker) {
            $('.selectpicker').selectpicker();
        }

        // Image upload preview
        $('[name="banner_image"]').on('change', function() {
            var imageUrl = $(this).val();
            var $preview = $('.houzez-ads-image-preview');
            
            if (imageUrl) {
                if ($preview.length === 0) {
                    $(this).after('<div class="houzez-ads-image-preview-wrapper"><img src="" class="houzez-ads-image-preview" style="max-width: 300px; margin-top: 10px;" /></div>');
                    $preview = $('.houzez-ads-image-preview');
                }
                $preview.attr('src', imageUrl).show();
            } else {
                $preview.hide();
            }
        });

        // Form validation
        function validateForm() {
            var $form = $('#houzez-ads-upload-form');
            var isValid = true;
            var errors = [];
            
            // Check required fields
            var requiredFields = ['ad_type', 'ad_zone', 'duration', 'banner_image', 'banner_link'];
            
            requiredFields.forEach(function(field) {
                var $field = $form.find('[name="' + field + '"]');
                if (!$field.val()) {
                    isValid = false;
                    errors.push($field.prev('label').text() + ' is required');
                }
            });
            
            // Check if properties are selected for property type ads
            var adType = $form.find('[name="ad_type"]').val();
            if (adType === 'property') {
                var selectedProperties = $form.find('[name="selected_properties[]"]:checked').length;
                if (selectedProperties === 0) {
                    isValid = false;
                    errors.push('Please select at least one property to promote');
                }
            }
            
            // Display errors
            $('.houzez-ads-form-errors').remove();
            if (!isValid) {
                var errorHtml = '<div class="houzez-ads-form-errors" style="background: #f8d7da; color: #721c24; padding: 15px; border-radius: 4px; margin-bottom: 20px;"><ul>';
                errors.forEach(function(error) {
                    errorHtml += '<li>' + error + '</li>';
                });
                errorHtml += '</ul></div>';
                $form.prepend(errorHtml);
            }
            
            return isValid;
        }

        // Validate form before submission
        $('#houzez-ads-upload-form').on('submit', function(e) {
            if (!validateForm()) {
                e.preventDefault();
                $('html, body').animate({
                    scrollTop: $('.houzez-ads-form-errors').offset().top - 50
                }, 500);
            }
        });

        // Initialize form
        if ($('#houzez-ads-upload-form').length) {
            $('[name="ad_type"]').trigger('change');
            updatePricing();
        }
    });

    // Helper functions for Houzez integration
    function validateHouzezForm($form) {
        var isValid = true;
        var errors = [];

        // Check required fields
        $form.find('[required]').each(function() {
            var $field = $(this);
            var value = $field.val();

            if (!value || ($.isArray(value) && value.length === 0)) {
                isValid = false;
                var label = $field.closest('.form-group').find('label').text().replace('*', '').trim();
                errors.push(label + ' is required');
                $field.addClass('is-invalid');
            } else {
                $field.removeClass('is-invalid');
            }
        });

        // Check if properties are selected for property type ads
        var adType = $form.find('[name="ad_type"]').val();
        if (adType === 'property') {
            var selectedProperties = $form.find('[name="selected_properties[]"]:checked').length;
            if (selectedProperties === 0) {
                isValid = false;
                errors.push('Please select at least one property to promote');
            }
        }

        // Show errors if any
        if (!isValid) {
            var $errorContainer = $form.find('.validate-errors');
            if ($errorContainer.length) {
                $errorContainer.removeClass('houzez-hidden').find('strong').next().html(errors.join('<br>'));
            }
        }

        return isValid;
    }

    function showHouzezNotification(message, type) {
        // Try to use Houzez notification system if available
        if (typeof houzez_show_notification === 'function') {
            houzez_show_notification(message, type);
        } else if (typeof toastr !== 'undefined') {
            // Fallback to toastr if available
            toastr[type](message);
        } else {
            // Simple alert fallback
            alert(message);
        }
    }

})(jQuery);
