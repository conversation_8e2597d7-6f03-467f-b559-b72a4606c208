/**
 * Frontend JavaScript for Houzez Ads Extension
 */

(function($) {
    'use strict';

    $(document).ready(function() {
        
        // Track ad impressions when 50% visible
        function trackImpressions() {
            $('.houzez-ad-campaign').each(function() {
                var $ad = $(this);
                var campaignId = $ad.data('campaign-id');
                
                if (campaignId && !$ad.data('impression-tracked')) {
                    if (isElementInViewport($ad[0], 0.5)) {
                        $ad.data('impression-tracked', true);
                        
                        $.ajax({
                            url: houzez_ads_ajax.ajax_url,
                            type: 'POST',
                            data: {
                                action: 'houzez_ads_track_impression',
                                campaign_id: campaignId,
                                nonce: houzez_ads_ajax.nonce
                            }
                        });
                    }
                }
            });
        }

        // Check if element is in viewport
        function isElementInViewport(el, threshold) {
            threshold = threshold || 0.5;
            var rect = el.getBoundingClientRect();
            var windowHeight = window.innerHeight || document.documentElement.clientHeight;
            var windowWidth = window.innerWidth || document.documentElement.clientWidth;
            
            var vertInView = (rect.top <= windowHeight) && ((rect.top + rect.height * threshold) >= 0);
            var horInView = (rect.left <= windowWidth) && ((rect.left + rect.width) >= 0);
            
            return vertInView && horInView;
        }

        // Track impressions on scroll and load
        $(window).on('scroll resize', trackImpressions);
        trackImpressions();

        // Handle campaign form submission
        $('#houzez-ads-upload-form').on('submit', function(e) {
            e.preventDefault();
            
            var $form = $(this);
            var $submitBtn = $form.find('.houzez-ads-submit-btn');
            var formData = new FormData(this);
            
            $submitBtn.prop('disabled', true).text('Processing...');
            
            // Add campaign to cart via AJAX
            var campaignData = {
                ad_type: $form.find('[name="ad_type"]').val(),
                ad_zone: $form.find('[name="ad_zone"]').val(),
                duration: $form.find('[name="duration"]').val(),
                selected_properties: $form.find('[name="selected_properties[]"]:checked').map(function() {
                    return this.value;
                }).get(),
                banner_image: $form.find('[name="banner_image"]').val(),
                banner_link: $form.find('[name="banner_link"]').val(),
                banner_alt: $form.find('[name="banner_alt"]').val()
            };
            
            $.ajax({
                url: houzez_ads_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'houzez_ads_add_to_cart',
                    zone: campaignData.ad_zone,
                    duration: campaignData.duration,
                    campaign_data: campaignData,
                    nonce: houzez_ads_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        alert(response.data.message);
                        if (response.data.cart_url) {
                            window.location.href = response.data.cart_url;
                        }
                    } else {
                        alert(response.data.message || 'An error occurred');
                    }
                },
                error: function() {
                    alert('An error occurred while processing your request');
                },
                complete: function() {
                    $submitBtn.prop('disabled', false).text('Create Campaign');
                }
            });
        });

        // Dynamic pricing calculation
        function updatePricing() {
            var $form = $('#houzez-ads-upload-form');
            var adZone = $form.find('[name="ad_zone"]').val();
            var duration = $form.find('[name="duration"]').val();
            var adType = $form.find('[name="ad_type"]').val();
            var selectedProperties = $form.find('[name="selected_properties[]"]:checked').length;
            
            if (adZone && duration) {
                var quantity = (adType === 'property' && selectedProperties > 0) ? selectedProperties : 1;
                
                $.ajax({
                    url: houzez_ads_ajax.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'houzez_ads_get_pricing',
                        ad_zone: adZone,
                        duration: duration,
                        quantity: quantity,
                        ad_type: adType,
                        nonce: houzez_ads_ajax.nonce
                    },
                    success: function(response) {
                        if (response.success) {
                            $('.houzez-ads-pricing-display .houzez-ads-price').text(response.data.formatted_price);
                            $('.houzez-ads-pricing-display').show();
                        }
                    }
                });
            } else {
                $('.houzez-ads-pricing-display').hide();
            }
        }

        // Update pricing when form fields change
        $('#houzez-ads-upload-form').on('change', '[name="ad_zone"], [name="duration"], [name="ad_type"], [name="selected_properties[]"]', updatePricing);

        // Show/hide property selection based on ad type
        $('[name="ad_type"]').on('change', function() {
            var adType = $(this).val();
            var $propertySelection = $('.houzez-ads-property-selection-wrapper');
            
            if (adType === 'property') {
                $propertySelection.show();
            } else {
                $propertySelection.hide();
                $('[name="selected_properties[]"]').prop('checked', false);
            }
            
            updatePricing();
        });

        // Image upload preview
        $('[name="banner_image"]').on('change', function() {
            var imageUrl = $(this).val();
            var $preview = $('.houzez-ads-image-preview');
            
            if (imageUrl) {
                if ($preview.length === 0) {
                    $(this).after('<div class="houzez-ads-image-preview-wrapper"><img src="" class="houzez-ads-image-preview" style="max-width: 300px; margin-top: 10px;" /></div>');
                    $preview = $('.houzez-ads-image-preview');
                }
                $preview.attr('src', imageUrl).show();
            } else {
                $preview.hide();
            }
        });

        // Form validation
        function validateForm() {
            var $form = $('#houzez-ads-upload-form');
            var isValid = true;
            var errors = [];
            
            // Check required fields
            var requiredFields = ['ad_type', 'ad_zone', 'duration', 'banner_image', 'banner_link'];
            
            requiredFields.forEach(function(field) {
                var $field = $form.find('[name="' + field + '"]');
                if (!$field.val()) {
                    isValid = false;
                    errors.push($field.prev('label').text() + ' is required');
                }
            });
            
            // Check if properties are selected for property type ads
            var adType = $form.find('[name="ad_type"]').val();
            if (adType === 'property') {
                var selectedProperties = $form.find('[name="selected_properties[]"]:checked').length;
                if (selectedProperties === 0) {
                    isValid = false;
                    errors.push('Please select at least one property to promote');
                }
            }
            
            // Display errors
            $('.houzez-ads-form-errors').remove();
            if (!isValid) {
                var errorHtml = '<div class="houzez-ads-form-errors" style="background: #f8d7da; color: #721c24; padding: 15px; border-radius: 4px; margin-bottom: 20px;"><ul>';
                errors.forEach(function(error) {
                    errorHtml += '<li>' + error + '</li>';
                });
                errorHtml += '</ul></div>';
                $form.prepend(errorHtml);
            }
            
            return isValid;
        }

        // Validate form before submission
        $('#houzez-ads-upload-form').on('submit', function(e) {
            if (!validateForm()) {
                e.preventDefault();
                $('html, body').animate({
                    scrollTop: $('.houzez-ads-form-errors').offset().top - 50
                }, 500);
            }
        });

        // Initialize form
        if ($('#houzez-ads-upload-form').length) {
            $('[name="ad_type"]').trigger('change');
            updatePricing();
        }
    });

})(jQuery);
