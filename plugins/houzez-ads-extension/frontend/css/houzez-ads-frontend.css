/**
 * Frontend styles for Houzez Ads Extension
 */

/* Ad Campaign Display */
.houzez-ad-campaign {
    margin: 20px 0;
    text-align: center;
}

.houzez-ad-campaign img {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
    transition: opacity 0.3s ease;
}

.houzez-ad-campaign img:hover {
    opacity: 0.9;
}

/* Zone-specific styling */
.houzez-ad-homepage {
    margin: 30px 0;
}

.houzez-ad-sidebar {
    margin: 15px 0;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.houzez-ad-search {
    margin: 10px 0;
    padding: 10px;
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 4px;
}

.houzez-ad-property-detail {
    margin: 20px 0;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

/* Campaign Dashboard */
.houzez-ads-dashboard {
    background: #fff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.houzez-ads-dashboard h2 {
    color: #2c3e50;
    margin-bottom: 20px;
    border-bottom: 2px solid #3498db;
    padding-bottom: 10px;
}

.houzez-ads-campaigns-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.houzez-ads-campaign-card {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    transition: box-shadow 0.3s ease;
}

.houzez-ads-campaign-card:hover {
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.houzez-ads-campaign-status {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.houzez-ads-status-pending {
    background: #fff3cd;
    color: #856404;
}

.houzez-ads-status-approved {
    background: #d4edda;
    color: #155724;
}

.houzez-ads-status-rejected {
    background: #f8d7da;
    color: #721c24;
}

.houzez-ads-status-expired {
    background: #e2e3e5;
    color: #383d41;
}

/* Upload Form */
.houzez-ads-upload-form {
    background: #fff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.houzez-ads-form-group {
    margin-bottom: 20px;
}

.houzez-ads-form-group label {
    display: block;
    font-weight: 600;
    margin-bottom: 8px;
    color: #2c3e50;
}

.houzez-ads-form-group input,
.houzez-ads-form-group select,
.houzez-ads-form-group textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.houzez-ads-form-group input:focus,
.houzez-ads-form-group select:focus,
.houzez-ads-form-group textarea:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.houzez-ads-properties-selection {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    background: #f8f9fa;
}

.houzez-ads-property-checkbox {
    margin-bottom: 10px;
}

.houzez-ads-property-checkbox label {
    font-weight: normal;
    margin-left: 8px;
    cursor: pointer;
}

.houzez-ads-pricing-display {
    background: #e8f4fd;
    border: 1px solid #3498db;
    border-radius: 4px;
    padding: 15px;
    margin: 20px 0;
    text-align: center;
}

.houzez-ads-price {
    font-size: 24px;
    font-weight: bold;
    color: #2980b9;
}

.houzez-ads-submit-btn {
    background: #3498db;
    color: #fff;
    padding: 12px 30px;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.houzez-ads-submit-btn:hover {
    background: #2980b9;
}

.houzez-ads-submit-btn:disabled {
    background: #bdc3c7;
    cursor: not-allowed;
}

/* Analytics */
.houzez-ads-analytics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin: 20px 0;
}

.houzez-ads-analytics-item {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
}

.houzez-ads-analytics-number {
    font-size: 28px;
    font-weight: bold;
    color: #3498db;
    display: block;
}

.houzez-ads-analytics-label {
    font-size: 12px;
    color: #7f8c8d;
    text-transform: uppercase;
    margin-top: 5px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .houzez-ads-campaigns-grid {
        grid-template-columns: 1fr;
    }
    
    .houzez-ads-analytics {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .houzez-ads-dashboard,
    .houzez-ads-upload-form {
        padding: 20px;
    }
}

/* Sponsored property indicator */
.property-item.sponsored {
    position: relative;
    border: 2px solid #f39c12;
}

.property-item.sponsored::before {
    content: "Sponsored";
    position: absolute;
    top: 10px;
    right: 10px;
    background: #f39c12;
    color: #fff;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 600;
    z-index: 10;
}
