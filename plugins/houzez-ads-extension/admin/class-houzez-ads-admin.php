<?php
/**
 * The admin-specific functionality of the plugin
 *
 * @package HouzezAdsExtension
 * @subpackage HouzezAdsExtension/admin
 */

/**
 * The admin-specific functionality of the plugin.
 *
 * Defines the plugin name, version, and hooks for admin-specific functionality.
 */
class Houzez_Ads_Admin {

	/**
	 * The ID of this plugin.
	 *
	 * @var string $plugin_name The ID of this plugin.
	 */
	private $plugin_name;

	/**
	 * The version of this plugin.
	 *
	 * @var string $version The current version of this plugin.
	 */
	private $version;

	/**
	 * Initialize the class and set its properties.
	 *
	 * @param string $plugin_name The name of this plugin.
	 * @param string $version     The version of this plugin.
	 */
	public function __construct( $plugin_name, $version ) {
		$this->plugin_name = $plugin_name;
		$this->version = $version;
	}

	/**
	 * Register the stylesheets for the admin area.
	 */
	public function enqueue_styles() {
		wp_enqueue_style( 
			$this->plugin_name, 
			HOUZEZ_ADS_EXTENSION_URL . 'admin/css/houzez-ads-admin.css', 
			array(), 
			$this->version, 
			'all' 
		);
	}

	/**
	 * Register the JavaScript for the admin area.
	 */
	public function enqueue_scripts() {
		wp_enqueue_script( 
			$this->plugin_name, 
			HOUZEZ_ADS_EXTENSION_URL . 'admin/js/houzez-ads-admin.js', 
			array( 'jquery' ), 
			$this->version, 
			false 
		);
	}

	/**
	 * Register the campaign post type.
	 */
	public function register_campaign_post_type() {
		$labels = array(
			'name'                  => _x( 'Ad Campaigns', 'Post type general name', 'houzez-ads-extension' ),
			'singular_name'         => _x( 'Ad Campaign', 'Post type singular name', 'houzez-ads-extension' ),
			'menu_name'             => _x( 'Ad Campaigns', 'Admin Menu text', 'houzez-ads-extension' ),
			'name_admin_bar'        => _x( 'Ad Campaign', 'Add New on Toolbar', 'houzez-ads-extension' ),
			'add_new'               => __( 'Add New', 'houzez-ads-extension' ),
			'add_new_item'          => __( 'Add New Campaign', 'houzez-ads-extension' ),
			'new_item'              => __( 'New Campaign', 'houzez-ads-extension' ),
			'edit_item'             => __( 'Edit Campaign', 'houzez-ads-extension' ),
			'view_item'             => __( 'View Campaign', 'houzez-ads-extension' ),
			'all_items'             => __( 'All Campaigns', 'houzez-ads-extension' ),
			'search_items'          => __( 'Search Campaigns', 'houzez-ads-extension' ),
			'parent_item_colon'     => __( 'Parent Campaigns:', 'houzez-ads-extension' ),
			'not_found'             => __( 'No campaigns found.', 'houzez-ads-extension' ),
			'not_found_in_trash'    => __( 'No campaigns found in Trash.', 'houzez-ads-extension' ),
		);

		$args = array(
			'labels'             => $labels,
			'public'             => false,
			'publicly_queryable' => false,
			'show_ui'            => true,
			'show_in_menu'       => true,
			'query_var'          => true,
			'rewrite'            => array( 'slug' => 'ad-campaign' ),
			'capability_type'    => 'post',
			'has_archive'        => false,
			'hierarchical'       => false,
			'menu_position'      => 25,
			'menu_icon'          => 'dashicons-megaphone',
			'supports'           => array( 'title', 'author' ),
			'show_in_rest'       => false,
		);

		register_post_type( 'banner_campaign', $args );
	}

	/**
	 * Add meta boxes for campaign post type.
	 */
	public function add_campaign_meta_boxes() {
		add_meta_box(
			'houzez_campaign_details',
			__( 'Campaign Details', 'houzez-ads-extension' ),
			array( $this, 'campaign_details_meta_box' ),
			'banner_campaign',
			'normal',
			'high'
		);

		add_meta_box(
			'houzez_campaign_banner',
			__( 'Banner Settings', 'houzez-ads-extension' ),
			array( $this, 'campaign_banner_meta_box' ),
			'banner_campaign',
			'normal',
			'high'
		);

		add_meta_box(
			'houzez_campaign_analytics',
			__( 'Campaign Analytics', 'houzez-ads-extension' ),
			array( $this, 'campaign_analytics_meta_box' ),
			'banner_campaign',
			'side',
			'default'
		);
	}

	/**
	 * Campaign details meta box callback.
	 *
	 * @param WP_Post $post Current post object.
	 */
	public function campaign_details_meta_box( $post ) {
		wp_nonce_field( 'houzez_campaign_meta_box', 'houzez_campaign_meta_box_nonce' );

		$campaign = new Houzez_Banner_Campaign( $post );
		$ad_types = houzez_ads_get_available_ad_types();
		$ad_zones = houzez_ads_get_available_zones();
		$durations = houzez_ads_get_available_durations();
		$statuses = houzez_ads_get_campaign_statuses();
		$user_properties = houzez_ads_get_user_properties( $post->post_author );

		include HOUZEZ_ADS_EXTENSION_PATH . 'admin/partials/campaign-details-meta-box.php';
	}

	/**
	 * Campaign banner meta box callback.
	 *
	 * @param WP_Post $post Current post object.
	 */
	public function campaign_banner_meta_box( $post ) {
		$campaign = new Houzez_Banner_Campaign( $post );
		include HOUZEZ_ADS_EXTENSION_PATH . 'admin/partials/campaign-banner-meta-box.php';
	}

	/**
	 * Campaign analytics meta box callback.
	 *
	 * @param WP_Post $post Current post object.
	 */
	public function campaign_analytics_meta_box( $post ) {
		$campaign = new Houzez_Banner_Campaign( $post );
		$analytics = $campaign->get_analytics();
		include HOUZEZ_ADS_EXTENSION_PATH . 'admin/partials/campaign-analytics-meta-box.php';
	}

	/**
	 * Save campaign meta data.
	 *
	 * @param int $post_id Post ID.
	 */
	public function save_campaign_meta( $post_id ) {
		// Check if our nonce is set
		if ( ! isset( $_POST['houzez_campaign_meta_box_nonce'] ) ) {
			return;
		}

		// Verify that the nonce is valid
		if ( ! wp_verify_nonce( $_POST['houzez_campaign_meta_box_nonce'], 'houzez_campaign_meta_box' ) ) {
			return;
		}

		// If this is an autosave, our form has not been submitted, so we don't want to do anything
		if ( defined( 'DOING_AUTOSAVE' ) && DOING_AUTOSAVE ) {
			return;
		}

		// Check the user's permissions
		if ( isset( $_POST['post_type'] ) && 'banner_campaign' == $_POST['post_type'] ) {
			if ( ! current_user_can( 'edit_post', $post_id ) ) {
				return;
			}
		}

		// Sanitize and save the data
		$campaign = new Houzez_Banner_Campaign( $post_id );
		$data = array();

		if ( isset( $_POST['houzez_ad_type'] ) ) {
			$data['ad_type'] = sanitize_text_field( $_POST['houzez_ad_type'] );
		}

		if ( isset( $_POST['houzez_ad_zone'] ) ) {
			$data['ad_zone'] = sanitize_text_field( $_POST['houzez_ad_zone'] );
		}

		if ( isset( $_POST['houzez_duration'] ) ) {
			$data['duration'] = absint( $_POST['houzez_duration'] );
		}

		if ( isset( $_POST['houzez_selected_properties'] ) ) {
			$data['selected_properties'] = array_map( 'absint', $_POST['houzez_selected_properties'] );
		}

		if ( isset( $_POST['houzez_banner_image'] ) ) {
			$data['banner_image'] = esc_url_raw( $_POST['houzez_banner_image'] );
		}

		if ( isset( $_POST['houzez_banner_link'] ) ) {
			$data['banner_link'] = esc_url_raw( $_POST['houzez_banner_link'] );
		}

		if ( isset( $_POST['houzez_banner_alt'] ) ) {
			$data['banner_alt'] = sanitize_text_field( $_POST['houzez_banner_alt'] );
		}

		if ( isset( $_POST['houzez_campaign_status'] ) ) {
			$data['campaign_status'] = sanitize_text_field( $_POST['houzez_campaign_status'] );
		}

		// Calculate and save price
		if ( ! empty( $data['ad_zone'] ) && ! empty( $data['duration'] ) ) {
			$quantity = 1;
			if ( isset( $data['selected_properties'] ) && is_array( $data['selected_properties'] ) ) {
				$quantity = count( $data['selected_properties'] );
			}
			$data['price'] = houzez_ads_calculate_campaign_price( 
				$data['ad_zone'], 
				$data['duration'], 
				$quantity, 
				$data['ad_type'] ?? 'property' 
			);
		}

		$campaign->save( $data );
	}
}
