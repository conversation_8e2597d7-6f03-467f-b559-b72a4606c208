/**
 * Admin JavaScript for Houzez Ads Extension
 */

(function($) {
    'use strict';

    $(document).ready(function() {
        // Media uploader for banner images
        var mediaUploader;

        $('.houzez-ads-upload-button').on('click', function(e) {
            e.preventDefault();
            
            var button = $(this);
            var inputField = button.siblings('input[type="url"]');
            var previewContainer = button.siblings('.houzez-ads-image-preview-container');

            // If the media frame already exists, reopen it
            if (mediaUploader) {
                mediaUploader.open();
                return;
            }

            // Create the media frame
            mediaUploader = wp.media({
                title: 'Select Banner Image',
                button: {
                    text: 'Use this image'
                },
                multiple: false
            });

            // When an image is selected, run a callback
            mediaUploader.on('select', function() {
                var attachment = mediaUploader.state().get('selection').first().toJSON();
                inputField.val(attachment.url);
                
                // Update preview
                var preview = previewContainer.find('.houzez-ads-image-preview');
                if (preview.length) {
                    preview.attr('src', attachment.url);
                } else {
                    previewContainer.html('<img src="' + attachment.url + '" class="houzez-ads-image-preview" />');
                }
            });

            // Open the media frame
            mediaUploader.open();
        });

        // Dynamic pricing calculation
        function updatePricing() {
            var adZone = $('#houzez_ad_zone').val();
            var duration = $('#houzez_duration').val();
            var adType = $('#houzez_ad_type').val();
            var selectedProperties = $('input[name="houzez_selected_properties[]"]:checked').length;

            if (adZone && duration) {
                var quantity = (adType === 'property' && selectedProperties > 0) ? selectedProperties : 1;
                
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'houzez_ads_get_pricing',
                        ad_zone: adZone,
                        duration: duration,
                        quantity: quantity,
                        ad_type: adType,
                        nonce: $('#houzez_campaign_meta_box_nonce').val()
                    },
                    success: function(response) {
                        if (response.success) {
                            $('.houzez-ads-price-display').html('Estimated Price: ' + response.data.formatted_price);
                        }
                    }
                });
            }
        }

        // Update pricing when relevant fields change
        $('#houzez_ad_zone, #houzez_duration, #houzez_ad_type').on('change', updatePricing);
        $('input[name="houzez_selected_properties[]"]').on('change', updatePricing);

        // Show/hide property selection based on ad type
        $('#houzez_ad_type').on('change', function() {
            var adType = $(this).val();
            var propertySelection = $('.houzez-ads-property-selection');
            
            if (adType === 'property') {
                propertySelection.show();
            } else {
                propertySelection.hide();
                $('input[name="houzez_selected_properties[]"]').prop('checked', false);
            }
            
            updatePricing();
        }).trigger('change');

        // Initial pricing calculation
        updatePricing();
    });

})(jQuery);
