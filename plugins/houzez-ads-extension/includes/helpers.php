<?php
/**
 * Helper functions for Houzez Ads Extension
 *
 * @package HouzezAdsExtension
 * @subpackage HouzezAdsExtension/includes
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
	die;
}

/**
 * Get user IP address.
 *
 * @return string User IP address.
 */
function houzez_ads_get_user_ip() {
	$ip = '';

	if ( ! empty( $_SERVER['HTTP_CLIENT_IP'] ) ) {
		$ip = $_SERVER['HTTP_CLIENT_IP'];
	} elseif ( ! empty( $_SERVER['HTTP_X_FORWARDED_FOR'] ) ) {
		$ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
	} elseif ( ! empty( $_SERVER['REMOTE_ADDR'] ) ) {
		$ip = $_SERVER['REMOTE_ADDR'];
	}

	return sanitize_text_field( $ip );
}

/**
 * Check if user can create campaigns.
 *
 * @param int $user_id User ID (optional, defaults to current user).
 * @return bool True if user can create campaigns.
 */
function houzez_ads_user_can_create_campaigns( $user_id = 0 ) {
	if ( ! $user_id ) {
		$user_id = get_current_user_id();
	}

	if ( ! $user_id ) {
		return false;
	}

	// Check if user has required capabilities
	$user = get_user_by( 'id', $user_id );
	if ( ! $user ) {
		return false;
	}

	// Allow administrators, agents, and agencies
	$allowed_roles = array( 'administrator', 'houzez_agent', 'houzez_agency', 'editor' );
	$user_roles = $user->roles;

	return ! empty( array_intersect( $allowed_roles, $user_roles ) );
}

/**
 * Get user's properties for campaign selection.
 *
 * @param int $user_id User ID (optional, defaults to current user).
 * @return array Array of user's properties.
 */
function houzez_ads_get_user_properties( $user_id = 0 ) {
	if ( ! $user_id ) {
		$user_id = get_current_user_id();
	}

	$args = array(
		'post_type' => 'property',
		'post_status' => 'publish',
		'author' => $user_id,
		'posts_per_page' => -1,
		'orderby' => 'title',
		'order' => 'ASC'
	);

	return get_posts( $args );
}

/**
 * Check if campaign is active.
 *
 * @param int $campaign_id Campaign ID.
 * @return bool True if campaign is active.
 */
function houzez_ads_is_campaign_active( $campaign_id ) {
	$status = get_post_meta( $campaign_id, '_houzez_campaign_status', true );
	$end_date = get_post_meta( $campaign_id, '_houzez_end_date', true );

	if ( $status !== 'approved' ) {
		return false;
	}

	if ( $end_date && strtotime( $end_date ) < current_time( 'timestamp' ) ) {
		return false;
	}

	return true;
}

/**
 * Get campaign analytics data.
 *
 * @param int $campaign_id Campaign ID.
 * @return array Analytics data.
 */
function houzez_ads_get_campaign_analytics( $campaign_id ) {
	global $wpdb;

	$table_name = $wpdb->prefix . 'houzez_ad_analytics';

	// Get impressions count
	$impressions = $wpdb->get_var( $wpdb->prepare(
		"SELECT COUNT(*) FROM $table_name WHERE campaign_id = %d AND event_type = 'impression'",
		$campaign_id
	) );

	// Get clicks count
	$clicks = $wpdb->get_var( $wpdb->prepare(
		"SELECT COUNT(*) FROM $table_name WHERE campaign_id = %d AND event_type = 'click'",
		$campaign_id
	) );

	// Calculate CTR
	$ctr = 0;
	if ( $impressions > 0 ) {
		$ctr = ( $clicks / $impressions ) * 100;
	}

	return array(
		'impressions' => (int) $impressions,
		'clicks' => (int) $clicks,
		'ctr' => round( $ctr, 2 )
	);
}

/**
 * Generate campaign tracking URL.
 *
 * @param int    $campaign_id Campaign ID.
 * @param string $target_url  Target URL.
 * @return string Tracking URL.
 */
function houzez_ads_generate_tracking_url( $campaign_id, $target_url ) {
	$tracking_url = add_query_arg( array(
		'houzez_ads_track' => 'click',
		'campaign_id' => $campaign_id,
		'redirect' => urlencode( $target_url )
	), home_url( '/' ) );

	return $tracking_url;
}

/**
 * Sanitize campaign data.
 *
 * @param array $data Campaign data.
 * @return array Sanitized data.
 */
function houzez_ads_sanitize_campaign_data( $data ) {
	$sanitized = array();

	if ( isset( $data['ad_type'] ) ) {
		$sanitized['ad_type'] = sanitize_text_field( $data['ad_type'] );
	}

	if ( isset( $data['ad_zone'] ) ) {
		$sanitized['ad_zone'] = sanitize_text_field( $data['ad_zone'] );
	}

	if ( isset( $data['duration'] ) ) {
		$sanitized['duration'] = absint( $data['duration'] );
	}

	if ( isset( $data['selected_properties'] ) && is_array( $data['selected_properties'] ) ) {
		$sanitized['selected_properties'] = array_map( 'absint', $data['selected_properties'] );
	}

	if ( isset( $data['banner_image'] ) ) {
		$sanitized['banner_image'] = esc_url_raw( $data['banner_image'] );
	}

	if ( isset( $data['banner_link'] ) ) {
		$sanitized['banner_link'] = esc_url_raw( $data['banner_link'] );
	}

	if ( isset( $data['banner_alt'] ) ) {
		$sanitized['banner_alt'] = sanitize_text_field( $data['banner_alt'] );
	}

	return $sanitized;
}

/**
 * Get campaign status options.
 *
 * @return array Status options.
 */
function houzez_ads_get_campaign_statuses() {
	return array(
		'pending' => __( 'Pending Review', 'houzez-ads-extension' ),
		'approved' => __( 'Approved', 'houzez-ads-extension' ),
		'rejected' => __( 'Rejected', 'houzez-ads-extension' ),
		'expired' => __( 'Expired', 'houzez-ads-extension' ),
		'paused' => __( 'Paused', 'houzez-ads-extension' )
	);
}

/**
 * Check if Houzez theme is active.
 *
 * @return bool True if Houzez theme is active.
 */
function houzez_ads_is_houzez_theme_active() {
	$theme = wp_get_theme();
	return ( $theme->get( 'Name' ) === 'Houzez' || $theme->get( 'Template' ) === 'houzez' );
}

/**
 * Get Houzez dashboard URL.
 *
 * @return string Dashboard URL.
 */
function houzez_ads_get_dashboard_url() {
	if ( function_exists( 'houzez_get_template_link' ) ) {
		return houzez_get_template_link( 'template/user_dashboard_profile.php' );
	}

	return home_url( '/dashboard/' );
}