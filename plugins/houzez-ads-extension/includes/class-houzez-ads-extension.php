<?php
/**
 * The file that defines the core plugin class
 *
 * A class definition that includes attributes and functions used across both the
 * public-facing side of the site and the admin area.
 *
 * @package HouzezAdsExtension
 * @subpackage HouzezAdsExtension/includes
 */

/**
 * The core plugin class.
 *
 * This is used to define internationalization, admin-specific hooks, and
 * public-facing site hooks.
 *
 * Also maintains the unique identifier of this plugin as well as the current
 * version of the plugin.
 */
class Houzez_Ads_Extension {

	/**
	 * The loader that's responsible for maintaining and registering all hooks that power
	 * the plugin.
	 *
	 * @var Houzez_Ads_Loader $loader Maintains and registers all hooks for the plugin.
	 */
	protected $loader;

	/**
	 * The unique identifier of this plugin.
	 *
	 * @var string $plugin_name The string used to uniquely identify this plugin.
	 */
	protected $plugin_name;

	/**
	 * The current version of the plugin.
	 *
	 * @var string $version The current version of the plugin.
	 */
	protected $version;

	/**
	 * Define the core functionality of the plugin.
	 *
	 * Set the plugin name and the plugin version that can be used throughout the plugin.
	 * Load the dependencies, define the locale, and set the hooks for the admin area and
	 * the public-facing side of the site.
	 */
	public function __construct() {
		if ( defined( 'HOUZEZ_ADS_EXTENSION_VERSION' ) ) {
			$this->version = HOUZEZ_ADS_EXTENSION_VERSION;
		} else {
			$this->version = '1.0.0';
		}
		$this->plugin_name = 'houzez-ads-extension';

		$this->load_dependencies();
		$this->set_locale();
		$this->define_admin_hooks();
		$this->define_public_hooks();
		$this->init_dashboard_integration();
	}

	/**
	 * Load the required dependencies for this plugin.
	 */
	private function load_dependencies() {
		/**
		 * The class responsible for orchestrating the actions and filters of the
		 * core plugin.
		 */
		require_once HOUZEZ_ADS_EXTENSION_PATH . 'includes/class-houzez-ads-loader.php';

		/**
		 * The class responsible for defining internationalization functionality
		 * of the plugin.
		 */
		require_once HOUZEZ_ADS_EXTENSION_PATH . 'includes/class-houzez-ads-i18n.php';

		/**
		 * The class responsible for defining all actions that occur in the admin area.
		 */
		require_once HOUZEZ_ADS_EXTENSION_PATH . 'admin/class-houzez-ads-admin.php';

		/**
		 * The class responsible for defining all actions that occur in the public-facing
		 * side of the site.
		 */
		require_once HOUZEZ_ADS_EXTENSION_PATH . 'frontend/class-houzez-ads-frontend.php';

		/**
		 * The class responsible for WooCommerce integration.
		 */
		require_once HOUZEZ_ADS_EXTENSION_PATH . 'woocommerce/class-houzez-ads-woocommerce.php';

		/**
		 * The campaign model class.
		 */
		require_once HOUZEZ_ADS_EXTENSION_PATH . 'models/class-houzez-banner-campaign.php';

		/**
		 * Helper functions and utilities.
		 */
		require_once HOUZEZ_ADS_EXTENSION_PATH . 'includes/helpers.php';
		require_once HOUZEZ_ADS_EXTENSION_PATH . 'includes/pricing.php';
		require_once HOUZEZ_ADS_EXTENSION_PATH . 'includes/targeting.php';

		/**
		 * Dashboard integration class.
		 */
		require_once HOUZEZ_ADS_EXTENSION_PATH . 'includes/class-houzez-dashboard-integration.php';

		$this->loader = new Houzez_Ads_Loader();
	}

	/**
	 * Define the locale for this plugin for internationalization.
	 */
	private function set_locale() {
		$plugin_i18n = new Houzez_Ads_i18n();
		$this->loader->add_action( 'plugins_loaded', $plugin_i18n, 'load_plugin_textdomain' );
	}

	/**
	 * Register all of the hooks related to the admin area functionality
	 * of the plugin.
	 */
	private function define_admin_hooks() {
		$plugin_admin = new Houzez_Ads_Admin( $this->get_plugin_name(), $this->get_version() );

		$this->loader->add_action( 'admin_enqueue_scripts', $plugin_admin, 'enqueue_styles' );
		$this->loader->add_action( 'admin_enqueue_scripts', $plugin_admin, 'enqueue_scripts' );
		$this->loader->add_action( 'init', $plugin_admin, 'register_campaign_post_type' );
		$this->loader->add_action( 'add_meta_boxes', $plugin_admin, 'add_campaign_meta_boxes' );
		$this->loader->add_action( 'save_post', $plugin_admin, 'save_campaign_meta' );

		// Admin list table customizations
		$this->loader->add_filter( 'manage_banner_campaign_posts_columns', $plugin_admin, 'add_campaign_columns' );
		$this->loader->add_action( 'manage_banner_campaign_posts_custom_column', $plugin_admin, 'display_campaign_columns', 10, 2 );
		$this->loader->add_filter( 'manage_edit-banner_campaign_sortable_columns', $plugin_admin, 'add_sortable_columns' );
		$this->loader->add_action( 'restrict_manage_posts', $plugin_admin, 'add_campaign_filters' );
		$this->loader->add_action( 'parse_query', $plugin_admin, 'filter_campaigns_by_meta' );
		$this->loader->add_filter( 'bulk_actions-edit-banner_campaign', $plugin_admin, 'add_bulk_actions' );
		$this->loader->add_filter( 'handle_bulk_actions-edit-banner_campaign', $plugin_admin, 'handle_bulk_actions', 10, 3 );
	}

	/**
	 * Register all of the hooks related to the public-facing functionality
	 * of the plugin.
	 */
	private function define_public_hooks() {
		$plugin_public = new Houzez_Ads_Frontend( $this->get_plugin_name(), $this->get_version() );

		$this->loader->add_action( 'wp_enqueue_scripts', $plugin_public, 'enqueue_styles' );
		$this->loader->add_action( 'wp_enqueue_scripts', $plugin_public, 'enqueue_scripts' );
		$this->loader->add_action( 'init', $plugin_public, 'register_shortcodes' );
		
		// WooCommerce integration
		$plugin_woocommerce = new Houzez_Ads_WooCommerce( $this->get_plugin_name(), $this->get_version() );
		$this->loader->add_action( 'init', $plugin_woocommerce, 'init_woocommerce_integration' );
	}

	/**
	 * Initialize dashboard integration.
	 */
	private function init_dashboard_integration() {
		new Houzez_Dashboard_Integration();
	}

	/**
	 * Run the loader to execute all of the hooks with WordPress.
	 */
	public function run() {
		$this->loader->run();
	}

	/**
	 * The name of the plugin used to uniquely identify it within the context of
	 * WordPress and to define internationalization functionality.
	 *
	 * @return string The name of the plugin.
	 */
	public function get_plugin_name() {
		return $this->plugin_name;
	}

	/**
	 * The reference to the class that orchestrates the hooks with the plugin.
	 *
	 * @return Houzez_Ads_Loader Orchestrates the hooks of the plugin.
	 */
	public function get_loader() {
		return $this->loader;
	}

	/**
	 * Retrieve the version number of the plugin.
	 *
	 * @return string The version number of the plugin.
	 */
	public function get_version() {
		return $this->version;
	}
}
