<?php
/**
 * Houzez Dashboard Integration
 *
 * @package HouzezAdsExtension
 * @subpackage HouzezAdsExtension/includes
 */

/**
 * Houzez Dashboard Integration class.
 *
 * Integrates ad campaign functionality into the Houzez user dashboard.
 */
class Houzez_Dashboard_Integration {

	/**
	 * Initialize the dashboard integration.
	 */
	public function __construct() {
		add_action( 'init', array( $this, 'init_dashboard_integration' ) );
	}

	/**
	 * Initialize dashboard integration hooks.
	 */
	public function init_dashboard_integration() {
		if ( ! houzez_ads_is_houzez_theme_active() ) {
			return;
		}

		// Add dashboard menu items
		add_filter( 'houzez_dashboard_nav_menu', array( $this, 'add_dashboard_menu_items' ) );
		
		// Handle dashboard page content
		add_action( 'houzez_dashboard_content', array( $this, 'handle_dashboard_content' ) );
		
		// Add dashboard scripts and styles
		add_action( 'wp_enqueue_scripts', array( $this, 'enqueue_dashboard_assets' ) );
		
		// Handle form submissions
		add_action( 'wp_ajax_houzez_ads_create_campaign', array( $this, 'handle_campaign_creation' ) );
		add_action( 'wp_ajax_houzez_ads_delete_campaign', array( $this, 'handle_campaign_deletion' ) );
	}

	/**
	 * Add dashboard menu items.
	 *
	 * @param array $menu_items Existing menu items.
	 * @return array Modified menu items.
	 */
	public function add_dashboard_menu_items( $menu_items ) {
		if ( ! houzez_ads_user_can_create_campaigns() ) {
			return $menu_items;
		}

		$ads_menu_items = array(
			'ad_campaigns' => array(
				'title' => __( 'Ad Campaigns', 'houzez-ads-extension' ),
				'icon' => 'houzez-icon icon-megaphone',
				'url' => add_query_arg( 'dashboard_page', 'ad_campaigns', houzez_ads_get_dashboard_url() ),
				'active' => isset( $_GET['dashboard_page'] ) && $_GET['dashboard_page'] === 'ad_campaigns'
			),
			'create_campaign' => array(
				'title' => __( 'Create Campaign', 'houzez-ads-extension' ),
				'icon' => 'houzez-icon icon-add-circle',
				'url' => add_query_arg( 'dashboard_page', 'create_campaign', houzez_ads_get_dashboard_url() ),
				'active' => isset( $_GET['dashboard_page'] ) && $_GET['dashboard_page'] === 'create_campaign'
			)
		);

		// Insert after properties menu item
		$position = array_search( 'properties', array_keys( $menu_items ) );
		if ( $position !== false ) {
			$menu_items = array_slice( $menu_items, 0, $position + 1, true ) +
						  $ads_menu_items +
						  array_slice( $menu_items, $position + 1, null, true );
		} else {
			$menu_items = array_merge( $menu_items, $ads_menu_items );
		}

		return $menu_items;
	}

	/**
	 * Handle dashboard content based on page.
	 */
	public function handle_dashboard_content() {
		if ( ! isset( $_GET['dashboard_page'] ) ) {
			return;
		}

		$page = sanitize_text_field( $_GET['dashboard_page'] );

		switch ( $page ) {
			case 'ad_campaigns':
				$this->display_campaigns_page();
				break;
			case 'create_campaign':
				$this->display_create_campaign_page();
				break;
		}
	}

	/**
	 * Display campaigns page.
	 */
	private function display_campaigns_page() {
		if ( ! houzez_ads_user_can_create_campaigns() ) {
			echo '<div class="alert alert-warning">' . __( 'You do not have permission to view campaigns.', 'houzez-ads-extension' ) . '</div>';
			return;
		}

		echo do_shortcode( '[houzez_ads_dashboard]' );
	}

	/**
	 * Display create campaign page.
	 */
	private function display_create_campaign_page() {
		if ( ! houzez_ads_user_can_create_campaigns() ) {
			echo '<div class="alert alert-warning">' . __( 'You do not have permission to create campaigns.', 'houzez-ads-extension' ) . '</div>';
			return;
		}

		echo do_shortcode( '[houzez_ads_upload]' );
	}

	/**
	 * Enqueue dashboard assets.
	 */
	public function enqueue_dashboard_assets() {
		if ( ! is_page_template( 'template/user_dashboard_profile.php' ) && ! is_page_template( 'template/dashboard.php' ) ) {
			return;
		}

		wp_enqueue_style( 
			'houzez-ads-dashboard', 
			HOUZEZ_ADS_EXTENSION_URL . 'frontend/css/houzez-ads-dashboard.css', 
			array(), 
			HOUZEZ_ADS_EXTENSION_VERSION 
		);

		wp_enqueue_script( 
			'houzez-ads-dashboard', 
			HOUZEZ_ADS_EXTENSION_URL . 'frontend/js/houzez-ads-dashboard.js', 
			array( 'jquery' ), 
			HOUZEZ_ADS_EXTENSION_VERSION, 
			true 
		);

		wp_localize_script( 'houzez-ads-dashboard', 'houzez_ads_dashboard', array(
			'ajax_url' => admin_url( 'admin-ajax.php' ),
			'nonce' => wp_create_nonce( 'houzez_ads_dashboard_nonce' ),
			'strings' => array(
				'confirm_delete' => __( 'Are you sure you want to delete this campaign?', 'houzez-ads-extension' ),
				'deleting' => __( 'Deleting...', 'houzez-ads-extension' ),
				'deleted' => __( 'Campaign deleted successfully!', 'houzez-ads-extension' ),
				'error' => __( 'An error occurred. Please try again.', 'houzez-ads-extension' )
			)
		));
	}

	/**
	 * Handle campaign creation AJAX.
	 */
	public function handle_campaign_creation() {
		check_ajax_referer( 'houzez_ads_dashboard_nonce', 'nonce' );

		if ( ! houzez_ads_user_can_create_campaigns() ) {
			wp_send_json_error( array( 'message' => __( 'Permission denied.', 'houzez-ads-extension' ) ) );
		}

		$campaign_data = $_POST['campaign_data'] ?? array();
		$campaign_data = houzez_ads_sanitize_campaign_data( $campaign_data );

		// Create campaign
		$campaign = new Houzez_Banner_Campaign();
		$result = $campaign->save( $campaign_data );

		if ( is_wp_error( $result ) ) {
			wp_send_json_error( array( 'message' => $result->get_error_message() ) );
		}

		wp_send_json_success( array(
			'message' => __( 'Campaign created successfully!', 'houzez-ads-extension' ),
			'campaign_id' => $campaign->id,
			'redirect_url' => add_query_arg( 'dashboard_page', 'ad_campaigns', houzez_ads_get_dashboard_url() )
		) );
	}

	/**
	 * Handle campaign deletion AJAX.
	 */
	public function handle_campaign_deletion() {
		check_ajax_referer( 'houzez_ads_dashboard_nonce', 'nonce' );

		if ( ! houzez_ads_user_can_create_campaigns() ) {
			wp_send_json_error( array( 'message' => __( 'Permission denied.', 'houzez-ads-extension' ) ) );
		}

		$campaign_id = absint( $_POST['campaign_id'] ?? 0 );
		
		if ( ! $campaign_id ) {
			wp_send_json_error( array( 'message' => __( 'Invalid campaign ID.', 'houzez-ads-extension' ) ) );
		}

		// Check if user owns the campaign
		$campaign_post = get_post( $campaign_id );
		if ( ! $campaign_post || $campaign_post->post_author != get_current_user_id() ) {
			wp_send_json_error( array( 'message' => __( 'You can only delete your own campaigns.', 'houzez-ads-extension' ) ) );
		}

		// Delete campaign
		$result = wp_delete_post( $campaign_id, true );

		if ( ! $result ) {
			wp_send_json_error( array( 'message' => __( 'Failed to delete campaign.', 'houzez-ads-extension' ) ) );
		}

		wp_send_json_success( array( 'message' => __( 'Campaign deleted successfully!', 'houzez-ads-extension' ) ) );
	}

	/**
	 * Add campaign stats to dashboard overview.
	 */
	public function add_dashboard_stats() {
		if ( ! houzez_ads_user_can_create_campaigns() ) {
			return;
		}

		$user_id = get_current_user_id();
		$campaigns = get_posts( array(
			'post_type' => 'banner_campaign',
			'author' => $user_id,
			'posts_per_page' => -1,
			'post_status' => array( 'publish', 'draft', 'pending' )
		) );

		$active_campaigns = 0;
		$total_impressions = 0;
		$total_clicks = 0;

		foreach ( $campaigns as $campaign_post ) {
			$campaign = new Houzez_Banner_Campaign( $campaign_post );
			if ( $campaign->is_active() ) {
				$active_campaigns++;
				$analytics = $campaign->get_analytics();
				$total_impressions += $analytics['impressions'];
				$total_clicks += $analytics['clicks'];
			}
		}

		?>
		<div class="dashboard-stats-widget">
			<h4><?php _e( 'Ad Campaign Stats', 'houzez-ads-extension' ); ?></h4>
			<div class="row">
				<div class="col-md-3">
					<div class="dashboard-stat-item">
						<span class="stat-number"><?php echo count( $campaigns ); ?></span>
						<span class="stat-label"><?php _e( 'Total Campaigns', 'houzez-ads-extension' ); ?></span>
					</div>
				</div>
				<div class="col-md-3">
					<div class="dashboard-stat-item">
						<span class="stat-number"><?php echo $active_campaigns; ?></span>
						<span class="stat-label"><?php _e( 'Active Campaigns', 'houzez-ads-extension' ); ?></span>
					</div>
				</div>
				<div class="col-md-3">
					<div class="dashboard-stat-item">
						<span class="stat-number"><?php echo number_format( $total_impressions ); ?></span>
						<span class="stat-label"><?php _e( 'Total Views', 'houzez-ads-extension' ); ?></span>
					</div>
				</div>
				<div class="col-md-3">
					<div class="dashboard-stat-item">
						<span class="stat-number"><?php echo number_format( $total_clicks ); ?></span>
						<span class="stat-label"><?php _e( 'Total Clicks', 'houzez-ads-extension' ); ?></span>
					</div>
				</div>
			</div>
		</div>
		<?php
	}
}
