<?php
/**
 * Ad targeting and rotation functionality for Houzez Ads Extension
 *
 * @package HouzezAdsExtension
 * @subpackage HouzezAdsExtension/includes
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
	die;
}

/**
 * Get active campaigns for a specific zone.
 *
 * @param string $zone The ad zone.
 * @return array Array of campaign posts.
 */
function houzez_ads_get_active_campaigns_by_zone( $zone ) {
	$args = array(
		'post_type' => 'banner_campaign',
		'post_status' => 'publish',
		'meta_query' => array(
			'relation' => 'AND',
			array(
				'key' => '_houzez_ad_zone',
				'value' => $zone,
				'compare' => '='
			),
			array(
				'key' => '_houzez_campaign_status',
				'value' => 'approved',
				'compare' => '='
			),
			array(
				'key' => '_houzez_end_date',
				'value' => current_time( 'Y-m-d H:i:s' ),
				'compare' => '>='
			)
		),
		'posts_per_page' => -1,
		'orderby' => 'rand'
	);

	return get_posts( $args );
}

/**
 * Get rotated campaign for a zone using transients.
 *
 * @param string $zone The ad zone.
 * @return WP_Post|null The campaign post or null if none found.
 */
function houzez_ads_get_rotated_campaign( $zone ) {
	$transient_key = 'houzez_ads_current_' . $zone;
	$current_campaign = get_transient( $transient_key );

	if ( false === $current_campaign ) {
		// Get all active campaigns for this zone
		$campaigns = houzez_ads_get_active_campaigns_by_zone( $zone );

		if ( empty( $campaigns ) ) {
			return null;
		}

		// Select a random campaign
		$current_campaign = $campaigns[ array_rand( $campaigns ) ];

		// Cache for rotation interval (default 30 minutes)
		$rotation_interval = get_option( 'houzez_ads_rotation_interval', 30 );
		set_transient( $transient_key, $current_campaign, $rotation_interval * MINUTE_IN_SECONDS );
	}

	return $current_campaign;
}

/**
 * Clear rotation cache for a specific zone.
 *
 * @param string $zone The ad zone.
 */
function houzez_ads_clear_rotation_cache( $zone = '' ) {
	if ( $zone ) {
		delete_transient( 'houzez_ads_current_' . $zone );
	} else {
		// Clear all zone caches
		$zones = array_keys( houzez_ads_get_available_zones() );
		foreach ( $zones as $zone_key ) {
			delete_transient( 'houzez_ads_current_' . $zone_key );
		}
	}
}

/**
 * Insert promoted properties into search results.
 *
 * @param WP_Query $query The WP_Query object.
 */
function houzez_ads_insert_promoted_properties( $query ) {
	// Only modify main query on property search pages
	if ( ! is_main_query() || is_admin() || ! $query->is_search() ) {
		return;
	}

	// Check if this is a property search
	if ( ! isset( $query->query_vars['post_type'] ) || $query->query_vars['post_type'] !== 'property' ) {
		return;
	}

	// Get promoted property campaigns
	$promoted_campaigns = houzez_ads_get_active_campaigns_by_zone( 'search' );
	$promoted_property_ids = array();

	foreach ( $promoted_campaigns as $campaign ) {
		$selected_properties = get_post_meta( $campaign->ID, '_houzez_selected_properties', true );
		if ( is_array( $selected_properties ) ) {
			$promoted_property_ids = array_merge( $promoted_property_ids, $selected_properties );
		}
	}

	if ( ! empty( $promoted_property_ids ) ) {
		// Add promoted properties to the beginning of results
		$existing_post_in = $query->get( 'post__in' );
		if ( empty( $existing_post_in ) ) {
			$query->set( 'post__in', $promoted_property_ids );
		} else {
			$merged_ids = array_unique( array_merge( $promoted_property_ids, $existing_post_in ) );
			$query->set( 'post__in', $merged_ids );
		}
	}
}

/**
 * Inject sponsored properties every 5th position in search results.
 *
 * @param array $posts Array of post objects.
 * @param WP_Query $query The WP_Query object.
 * @return array Modified posts array.
 */
function houzez_ads_inject_sponsored_properties( $posts, $query ) {
	// Only modify property search results
	if ( is_admin() || ! $query->is_search() || ! isset( $query->query_vars['post_type'] ) || $query->query_vars['post_type'] !== 'property' ) {
		return $posts;
	}

	$promoted_campaigns = houzez_ads_get_active_campaigns_by_zone( 'search' );
	$sponsored_properties = array();

	foreach ( $promoted_campaigns as $campaign ) {
		$selected_properties = get_post_meta( $campaign->ID, '_houzez_selected_properties', true );
		if ( is_array( $selected_properties ) ) {
			foreach ( $selected_properties as $property_id ) {
				$property = get_post( $property_id );
				if ( $property ) {
					$property->is_sponsored = true;
					$sponsored_properties[] = $property;
				}
			}
		}
	}

	if ( empty( $sponsored_properties ) ) {
		return $posts;
	}

	// Inject sponsored properties every 5th position
	$modified_posts = array();
	$sponsored_index = 0;

	foreach ( $posts as $index => $post ) {
		$modified_posts[] = $post;

		// Insert sponsored property every 5th position
		if ( ( $index + 1 ) % 5 === 0 && $sponsored_index < count( $sponsored_properties ) ) {
			$modified_posts[] = $sponsored_properties[ $sponsored_index ];
			$sponsored_index++;
		}
	}

	return $modified_posts;
}

/**
 * Track ad impression.
 *
 * @param int $campaign_id The campaign ID.
 */
function houzez_ads_track_impression( $campaign_id ) {
	global $wpdb;

	$table_name = $wpdb->prefix . 'houzez_ad_analytics';

	$wpdb->insert(
		$table_name,
		array(
			'campaign_id' => $campaign_id,
			'event_type' => 'impression',
			'user_id' => get_current_user_id(),
			'ip_address' => houzez_ads_get_user_ip(),
			'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
			'referrer' => $_SERVER['HTTP_REFERER'] ?? ''
		),
		array( '%d', '%s', '%d', '%s', '%s', '%s' )
	);
}

/**
 * Track ad click.
 *
 * @param int $campaign_id The campaign ID.
 */
function houzez_ads_track_click( $campaign_id ) {
	global $wpdb;

	$table_name = $wpdb->prefix . 'houzez_ad_analytics';

	$wpdb->insert(
		$table_name,
		array(
			'campaign_id' => $campaign_id,
			'event_type' => 'click',
			'user_id' => get_current_user_id(),
			'ip_address' => houzez_ads_get_user_ip(),
			'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
			'referrer' => $_SERVER['HTTP_REFERER'] ?? ''
		),
		array( '%d', '%s', '%d', '%s', '%s', '%s' )
	);
}