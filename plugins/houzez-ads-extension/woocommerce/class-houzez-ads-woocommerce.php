<?php
/**
 * WooCommerce integration functionality
 *
 * @package HouzezAdsExtension
 * @subpackage HouzezAdsExtension/woocommerce
 */

/**
 * WooCommerce integration class.
 *
 * Handles integration with WooCommerce for banner ad purchases and order processing.
 */
class Houzez_Ads_WooCommerce {

	/**
	 * The ID of this plugin.
	 *
	 * @var string $plugin_name The ID of this plugin.
	 */
	private $plugin_name;

	/**
	 * The version of this plugin.
	 *
	 * @var string $version The current version of this plugin.
	 */
	private $version;

	/**
	 * Initialize the class and set its properties.
	 *
	 * @param string $plugin_name The name of the plugin.
	 * @param string $version     The version of this plugin.
	 */
	public function __construct( $plugin_name, $version ) {
		$this->plugin_name = $plugin_name;
		$this->version = $version;
	}

	/**
	 * Initialize WooCommerce integration.
	 */
	public function init_woocommerce_integration() {
		if ( ! class_exists( 'WooCommerce' ) ) {
			return;
		}

		// Create dynamic products
		add_action( 'init', array( $this, 'create_dynamic_campaign_products' ) );
		
		// Handle cart and checkout
		add_action( 'woocommerce_add_to_cart', array( $this, 'handle_campaign_add_to_cart' ), 10, 6 );
		add_action( 'woocommerce_checkout_order_processed', array( $this, 'handle_campaign_order_processed' ), 10, 3 );
		add_action( 'woocommerce_order_status_completed', array( $this, 'handle_order_completed' ) );
		add_action( 'woocommerce_order_status_processing', array( $this, 'handle_order_processing' ) );
		
		// Custom product data
		add_filter( 'woocommerce_add_cart_item_data', array( $this, 'add_campaign_cart_item_data' ), 10, 3 );
		add_filter( 'woocommerce_get_item_data', array( $this, 'display_campaign_cart_item_data' ), 10, 2 );
		
		// AJAX handlers
		add_action( 'wp_ajax_houzez_ads_add_to_cart', array( $this, 'handle_ajax_add_to_cart' ) );
		add_action( 'wp_ajax_nopriv_houzez_ads_add_to_cart', array( $this, 'handle_ajax_add_to_cart' ) );
	}

	/**
	 * Create dynamic campaign products.
	 */
	public function create_dynamic_campaign_products() {
		// Check if products already exist
		if ( get_option( 'houzez_ads_dynamic_products_created' ) ) {
			return;
		}

		$zones = houzez_ads_get_available_zones();
		$durations = houzez_ads_get_available_durations();

		foreach ( $zones as $zone_key => $zone_name ) {
			foreach ( $durations as $duration_key => $duration_name ) {
				$this->create_campaign_product( $zone_key, $duration_key, $zone_name, $duration_name );
			}
		}

		update_option( 'houzez_ads_dynamic_products_created', true );
	}

	/**
	 * Create a campaign product.
	 *
	 * @param string $zone_key      Zone key.
	 * @param string $duration_key  Duration key.
	 * @param string $zone_name     Zone name.
	 * @param string $duration_name Duration name.
	 */
	private function create_campaign_product( $zone_key, $duration_key, $zone_name, $duration_name ) {
		$sku = "houzez-ad-{$zone_key}-{$duration_key}";
		
		// Check if product already exists
		$existing_product = wc_get_product_id_by_sku( $sku );
		if ( $existing_product ) {
			return;
		}

		$price = houzez_ads_calculate_campaign_price( $zone_key, $duration_key, 1, 'property' );
		$product_name = sprintf( __( 'Ad Campaign - %s (%s)', 'houzez-ads-extension' ), $zone_name, $duration_name );

		$product = new WC_Product_Simple();
		$product->set_name( $product_name );
		$product->set_status( 'publish' );
		$product->set_catalog_visibility( 'hidden' );
		$product->set_description( sprintf( __( 'Ad campaign for %s zone, duration: %s', 'houzez-ads-extension' ), $zone_name, $duration_name ) );
		$product->set_sku( $sku );
		$product->set_price( $price );
		$product->set_regular_price( $price );
		$product->set_virtual( true );
		$product->set_downloadable( false );
		$product->set_sold_individually( true );

		// Add custom meta
		$product->update_meta_data( '_houzez_ad_zone', $zone_key );
		$product->update_meta_data( '_houzez_ad_duration', $duration_key );
		$product->update_meta_data( '_houzez_ad_product', 'yes' );

		$product->save();
	}

	/**
	 * Add campaign data to cart item.
	 *
	 * @param array $cart_item_data Cart item data.
	 * @param int   $product_id     Product ID.
	 * @param int   $variation_id   Variation ID.
	 * @return array Modified cart item data.
	 */
	public function add_campaign_cart_item_data( $cart_item_data, $product_id, $variation_id ) {
		if ( isset( $_POST['houzez_campaign_data'] ) ) {
			$campaign_data = json_decode( stripslashes( $_POST['houzez_campaign_data'] ), true );
			if ( $campaign_data ) {
				$cart_item_data['houzez_campaign_data'] = $campaign_data;
			}
		}

		return $cart_item_data;
	}

	/**
	 * Display campaign data in cart.
	 *
	 * @param array $item_data Item data.
	 * @param array $cart_item Cart item.
	 * @return array Modified item data.
	 */
	public function display_campaign_cart_item_data( $item_data, $cart_item ) {
		if ( isset( $cart_item['houzez_campaign_data'] ) ) {
			$campaign_data = $cart_item['houzez_campaign_data'];
			
			if ( isset( $campaign_data['ad_type'] ) ) {
				$ad_types = houzez_ads_get_available_ad_types();
				$item_data[] = array(
					'key'   => __( 'Ad Type', 'houzez-ads-extension' ),
					'value' => $ad_types[ $campaign_data['ad_type'] ] ?? $campaign_data['ad_type'],
				);
			}

			if ( isset( $campaign_data['selected_properties'] ) && is_array( $campaign_data['selected_properties'] ) ) {
				$property_count = count( $campaign_data['selected_properties'] );
				$item_data[] = array(
					'key'   => __( 'Properties', 'houzez-ads-extension' ),
					'value' => sprintf( _n( '%d property', '%d properties', $property_count, 'houzez-ads-extension' ), $property_count ),
				);
			}
		}

		return $item_data;
	}

	/**
	 * Handle campaign add to cart.
	 *
	 * @param string $cart_item_key Cart item key.
	 * @param int    $product_id    Product ID.
	 * @param int    $quantity      Quantity.
	 * @param int    $variation_id  Variation ID.
	 * @param array  $variation     Variation data.
	 * @param array  $cart_item_data Cart item data.
	 */
	public function handle_campaign_add_to_cart( $cart_item_key, $product_id, $quantity, $variation_id, $variation, $cart_item_data ) {
		if ( isset( $cart_item_data['houzez_campaign_data'] ) ) {
			// Update cart item price based on campaign data
			$campaign_data = $cart_item_data['houzez_campaign_data'];
			$product = wc_get_product( $product_id );
			
			if ( $product ) {
				$zone = $product->get_meta( '_houzez_ad_zone' );
				$duration = $product->get_meta( '_houzez_ad_duration' );
				$quantity_multiplier = 1;
				
				if ( isset( $campaign_data['selected_properties'] ) && is_array( $campaign_data['selected_properties'] ) ) {
					$quantity_multiplier = count( $campaign_data['selected_properties'] );
				}
				
				$new_price = houzez_ads_calculate_campaign_price( 
					$zone, 
					$duration, 
					$quantity_multiplier, 
					$campaign_data['ad_type'] ?? 'property' 
				);
				
				WC()->cart->cart_contents[ $cart_item_key ]['data']->set_price( $new_price );
			}
		}
	}

	/**
	 * Handle campaign order processed.
	 *
	 * @param int   $order_id Order ID.
	 * @param array $posted_data Posted data.
	 * @param WC_Order $order Order object.
	 */
	public function handle_campaign_order_processed( $order_id, $posted_data, $order ) {
		foreach ( $order->get_items() as $item_id => $item ) {
			$product = $item->get_product();
			
			if ( $product && $product->get_meta( '_houzez_ad_product' ) === 'yes' ) {
				// Get campaign data from order item meta
				$campaign_data = $item->get_meta( 'houzez_campaign_data' );
				
				if ( $campaign_data ) {
					$this->create_campaign_from_order( $order_id, $item_id, $campaign_data, $product );
				}
			}
		}
	}

	/**
	 * Create campaign from order.
	 *
	 * @param int      $order_id      Order ID.
	 * @param int      $item_id       Item ID.
	 * @param array    $campaign_data Campaign data.
	 * @param WC_Product $product     Product object.
	 */
	private function create_campaign_from_order( $order_id, $item_id, $campaign_data, $product ) {
		$order = wc_get_order( $order_id );
		
		$campaign_title = sprintf( 
			__( 'Campaign - Order #%d', 'houzez-ads-extension' ), 
			$order->get_order_number() 
		);

		$campaign_data['title'] = $campaign_title;
		$campaign_data['status'] = 'draft';
		$campaign_data['campaign_status'] = 'pending';
		$campaign_data['order_id'] = $order_id;
		$campaign_data['ad_zone'] = $product->get_meta( '_houzez_ad_zone' );
		$campaign_data['duration'] = $product->get_meta( '_houzez_ad_duration' );

		// Create campaign
		$campaign = new Houzez_Banner_Campaign();
		$result = $campaign->save( $campaign_data );

		if ( ! is_wp_error( $result ) ) {
			// Store campaign ID in order meta
			$order->update_meta_data( '_houzez_campaign_id_' . $item_id, $campaign->id );
			$order->save();
		}
	}

	/**
	 * Handle order completed.
	 *
	 * @param int $order_id Order ID.
	 */
	public function handle_order_completed( $order_id ) {
		$this->update_campaigns_status( $order_id, 'pending' );
	}

	/**
	 * Handle order processing.
	 *
	 * @param int $order_id Order ID.
	 */
	public function handle_order_processing( $order_id ) {
		$this->update_campaigns_status( $order_id, 'pending' );
	}

	/**
	 * Update campaigns status based on order.
	 *
	 * @param int    $order_id Order ID.
	 * @param string $status   New status.
	 */
	private function update_campaigns_status( $order_id, $status ) {
		$order = wc_get_order( $order_id );
		
		foreach ( $order->get_items() as $item_id => $item ) {
			$campaign_id = $order->get_meta( '_houzez_campaign_id_' . $item_id );
			
			if ( $campaign_id ) {
				$campaign = new Houzez_Banner_Campaign( $campaign_id );
				$campaign->save( array( 'campaign_status' => $status ) );
				
				// Auto-approve if setting is enabled
				if ( get_option( 'houzez_ads_auto_approval', false ) ) {
					$campaign->approve();
				}
			}
		}
	}

	/**
	 * Handle AJAX add to cart.
	 */
	public function handle_ajax_add_to_cart() {
		check_ajax_referer( 'houzez_ads_tracking_nonce', 'nonce' );

		$zone = sanitize_text_field( $_POST['zone'] ?? '' );
		$duration = sanitize_text_field( $_POST['duration'] ?? '' );
		$campaign_data = $_POST['campaign_data'] ?? array();

		if ( $zone && $duration ) {
			$sku = "houzez-ad-{$zone}-{$duration}";
			$product_id = wc_get_product_id_by_sku( $sku );

			if ( $product_id ) {
				$cart_item_data = array(
					'houzez_campaign_data' => $campaign_data
				);

				$cart_item_key = WC()->cart->add_to_cart( $product_id, 1, 0, array(), $cart_item_data );

				if ( $cart_item_key ) {
					wp_send_json_success( array(
						'message' => __( 'Campaign added to cart successfully!', 'houzez-ads-extension' ),
						'cart_url' => wc_get_cart_url()
					) );
				} else {
					wp_send_json_error( array( 'message' => __( 'Failed to add campaign to cart.', 'houzez-ads-extension' ) ) );
				}
			} else {
				wp_send_json_error( array( 'message' => __( 'Product not found.', 'houzez-ads-extension' ) ) );
			}
		} else {
			wp_send_json_error( array( 'message' => __( 'Invalid parameters.', 'houzez-ads-extension' ) ) );
		}
	}
}
