<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'Leadin\\AssetsManager' => $baseDir . '/public/class-assetsmanager.php',
    'Leadin\\Leadin' => $baseDir . '/public/class-leadin.php',
    'Leadin\\PageHooks' => $baseDir . '/public/class-pagehooks.php',
    'Leadin\\Proxy_Mappings' => $baseDir . '/public/class-proxy-mappings.php',
    'Leadin\\admin\\AdminConstants' => $baseDir . '/public/admin/class-adminconstants.php',
    'Leadin\\admin\\Connection' => $baseDir . '/public/admin/class-connection.php',
    'Leadin\\admin\\ContentEmbedInstaller' => $baseDir . '/public/admin/class-contentembedinstaller.php',
    'Leadin\\admin\\DeactivationForm' => $baseDir . '/public/admin/class-deactivationform.php',
    'Leadin\\admin\\Gutenberg' => $baseDir . '/public/admin/class-gutenberg.php',
    'Leadin\\admin\\Impact' => $baseDir . '/public/admin/class-impact.php',
    'Leadin\\admin\\LeadinAdmin' => $baseDir . '/public/admin/class-leadinadmin.php',
    'Leadin\\admin\\Links' => $baseDir . '/public/admin/class-links.php',
    'Leadin\\admin\\MenuConstants' => $baseDir . '/public/admin/class-menuconstants.php',
    'Leadin\\admin\\NoticeManager' => $baseDir . '/public/admin/class-noticemanager.php',
    'Leadin\\admin\\PluginActionsManager' => $baseDir . '/public/admin/class-pluginactionsmanager.php',
    'Leadin\\admin\\ReviewBanner' => $baseDir . '/public/admin/class-reviewbanner.php',
    'Leadin\\admin\\ReviewController' => $baseDir . '/public/admin/class-reviewcontroller.php',
    'Leadin\\admin\\Routing' => $baseDir . '/public/admin/class-routing.php',
    'Leadin\\admin\\api\\Hublet_Api_Controller' => $baseDir . '/public/admin/modules/api/class-hublet-api-controller.php',
    'Leadin\\admin\\api\\Internal_Tracking_Api_Controller' => $baseDir . '/public/admin/modules/api/class-internal-tracking-api-controller.php',
    'Leadin\\admin\\api\\Portal_Api_Controller' => $baseDir . '/public/admin/modules/api/class-portal-api-controller.php',
    'Leadin\\admin\\api\\User_Meta_Api_Controller' => $baseDir . '/public/admin/modules/api/class-user-meta-api-controller.php',
    'Leadin\\admin\\api\\WP_Mappings_Api_Controller' => $baseDir . '/public/admin/modules/api/class-wp-mappings-api-controller.php',
    'Leadin\\admin\\widgets\\ElementorForm' => $baseDir . '/public/admin/widgets/class-elementorform.php',
    'Leadin\\admin\\widgets\\ElementorFormSelect' => $baseDir . '/public/admin/widgets/class-elementorformselect.php',
    'Leadin\\admin\\widgets\\ElementorMeeting' => $baseDir . '/public/admin/widgets/class-elementormeeting.php',
    'Leadin\\admin\\widgets\\ElementorMeetingSelect' => $baseDir . '/public/admin/widgets/class-elementormeetingselect.php',
    'Leadin\\api\\Base_Api_Controller' => $baseDir . '/public/modules/api/class-base-api-controller.php',
    'Leadin\\api\\Healthcheck_Api_Controller' => $baseDir . '/public/modules/api/class-healthcheck-api-controller.php',
    'Leadin\\auth\\OAuth' => $baseDir . '/public/auth/class-oauth.php',
    'Leadin\\auth\\OAuthCrypto' => $baseDir . '/public/auth/class-oauthcrypto.php',
    'Leadin\\auth\\OAuthCryptoError' => $baseDir . '/public/auth/class-oauthcryptoerror.php',
    'Leadin\\data\\Filters' => $baseDir . '/public/data/class-filters.php',
    'Leadin\\data\\Portal_Options' => $baseDir . '/public/data/class-portal-options.php',
    'Leadin\\data\\User' => $baseDir . '/public/data/class-user.php',
    'Leadin\\data\\User_Metadata' => $baseDir . '/public/data/class-user-metadata.php',
    'Leadin\\utils\\ProxyUtils' => $baseDir . '/public/utils/class-proxyutils.php',
    'Leadin\\utils\\QueryParameters' => $baseDir . '/public/utils/class-queryparameters.php',
    'Leadin\\utils\\RequestUtils' => $baseDir . '/public/utils/class-requestutils.php',
    'Leadin\\utils\\ShortcodeRenderUtils' => $baseDir . '/public/utils/class-shortcoderenderutils.php',
    'Leadin\\utils\\Versions' => $baseDir . '/public/utils/class-versions.php',
    'Leadin\\wp\\Page' => $baseDir . '/public/wp/class-page.php',
);
