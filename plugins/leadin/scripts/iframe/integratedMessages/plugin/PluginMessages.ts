export const PluginMessages = {
  PluginSettingsNavigation: 'PLUGIN_SETTINGS_NAVIGATION',
  PluginLeadinConfig: 'PLUGIN_LEADIN_CONFIG',
  TrackConsent: 'INTEGRATED_APP_EMBEDDER_TRACK_CONSENT',
  InternalTrackingFetchRequest: 'INTEGRATED_TRACKING_FETCH_REQUEST',
  InternalTrackingFetchResponse: 'INTEGRATED_TRACKING_FETCH_RESPONSE',
  InternalTrackingFetchError: 'INTEGRATED_TRACKING_FETCH_ERROR',
  InternalTrackingChangeRequest: 'INTEGRATED_TRACKING_CHANGE_REQUEST',
  InternalTrackingChangeError: 'INTEGRATED_TRACKING_CHANGE_ERROR',
  BusinessUnitFetchRequest: 'BUSINESS_UNIT_FETCH_REQUEST',
  BusinessUnitFetchResponse: 'BUSINESS_UNIT_FETCH_FETCH_RESPONSE',
  BusinessUnitFetchError: 'BUSINESS_UNIT_FETCH_FETCH_ERROR',
  BusinessUnitChangeRequest: 'BUSINESS_UNIT_CHANGE_REQUEST',
  BusinessUnitChangeError: 'BUSINESS_UNIT_CHANGE_ERROR',
  SkipReviewRequest: 'SKIP_REVIEW_REQUEST',
  SkipReviewResponse: 'SKIP_REVIEW_RESPONSE',
  SkipReviewError: 'SKIP_REVIEW_ERROR',
  RemoveParentQueryParam: 'REMOVE_PARENT_QUERY_PARAM',
  ContentEmbedInstallRequest: 'CONTENT_EMBED_INSTALL_REQUEST',
  ContentEmbedInstallResponse: 'CONTENT_EMBED_INSTALL_RESPONSE',
  ContentEmbedInstallError: 'CONTENT_EMBED_INSTALL_ERROR',
  ContentEmbedActivationRequest: 'CONTENT_EMBED_ACTIVATION_REQUEST',
  ContentEmbedActivationResponse: 'CONTENT_EMBED_ACTIVATION_RESPONSE',
  ContentEmbedActivationError: 'CONTENT_EMBED_ACTIVATION_ERROR',
  ProxyMappingsEnabledRequest: 'PROXY_MAPPINGS_ENABLED_REQUEST',
  ProxyMappingsEnabledResponse: 'PROXY_MAPPINGS_ENABLED_RESPONSE',
  ProxyMappingsEnabledError: 'PROXY_MAPPINGS_ENABLED_ERROR',
  ProxyMappingsEnabledChangeRequest: 'PROXY_MAPPINGS_ENABLED_CHANGE_REQUEST',
  ProxyMappingsEnabledChangeError: 'PROXY_MAPPINGS_ENABLED_CHANGE_ERROR',
  RefreshProxyMappingsRequest: 'REFRESH_PROXY_MAPPINGS_REQUEST',
  RefreshProxyMappingsResponse: 'REFRESH_PROXY_MAPPINGS_RESPONSE',
  RefreshProxyMappingsError: 'REFRESH_PROXY_MAPPINGS_ERROR',
} as const;

export type PluginMessageType = typeof PluginMessages[keyof typeof PluginMessages];
