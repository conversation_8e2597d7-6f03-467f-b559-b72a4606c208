{"version": 3, "file": "leadin.js", "mappings": ";;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAE8B;;;;;;;;;;;;;;;;;ACRS;;AAEvC;AACA,igIAAigI;;AAEjgI,iCAAiC,4DAAO;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEkC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACfX;AACU;AAC8B;AACJ;AAC3D,SAASK,WAAWA,CAACC,MAAM,EAAEC,IAAI,EAA+B;EAAA,IAA7BC,IAAI,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAAA,IAAEG,WAAW,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAC1D;EACA,IAAMI,UAAU,GAAG,IAAIC,GAAG,IAAAC,MAAA,CAAIZ,4DAAO,eAAAY,MAAA,CAAYR,IAAI,CAAE,CAAC;EACxDH,uEAAmB,CAACS,UAAU,EAAED,WAAW,CAAC;EAC5C,OAAO,IAAII,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;IACpC,IAAMC,OAAO,GAAG;MACZC,GAAG,EAAEP,UAAU,CAACQ,QAAQ,CAAC,CAAC;MAC1Bf,MAAM,EAANA,MAAM;MACNgB,WAAW,EAAE,kBAAkB;MAC/BC,UAAU,EAAE,SAAZA,UAAUA,CAAGC,GAAG;QAAA,OAAKA,GAAG,CAACC,gBAAgB,CAAC,YAAY,EAAEvB,8DAAS,CAAC;MAAA;MAClEwB,OAAO,EAAET,OAAO;MAChBU,KAAK,EAAE,SAAPA,KAAKA,CAAGC,QAAQ,EAAK;QACjB3B,iEAAoB,oBAAAc,MAAA,CAAoBF,UAAU,yBAAAE,MAAA,CAAsBa,QAAQ,CAACE,MAAM,QAAAf,MAAA,CAAKa,QAAQ,CAACG,YAAY,GAAI;UACjHC,WAAW,EAAE,CACT,eAAe,EACfzB,IAAI,EACJqB,QAAQ,CAACE,MAAM,EACfF,QAAQ,CAACG,YAAY;QAE7B,CAAC,CAAC;QACFb,MAAM,CAACU,QAAQ,CAAC;MACpB;IACJ,CAAC;IACD,IAAItB,MAAM,KAAK,KAAK,EAAE;MAClBa,OAAO,CAACX,IAAI,GAAGyB,IAAI,CAACC,SAAS,CAAC1B,IAAI,CAAC;IACvC;IACAR,kDAAM,CAACmB,OAAO,CAAC;EACnB,CAAC,CAAC;AACN;AACO,SAASiB,kBAAkBA,CAAA,EAAG;EACjC,OAAO/B,WAAW,CAAC,KAAK,EAAE,cAAc,CAAC;AAC7C;AACO,SAASgC,uBAAuBA,CAACC,KAAK,EAAE;EAC3C,OAAOjC,WAAW,CAAC,KAAK,EAAE,oBAAoB,EAAEiC,KAAK,GAAG,GAAG,GAAG,GAAG,CAAC;AACtE;AACO,SAASC,4BAA4BA,CAAA,EAAG;EAC3C,OAAOlC,WAAW,CAAC,KAAK,EAAE,oBAAoB,CAAC,CAACmC,IAAI,CAAC,UAAAC,OAAO;IAAA,OAAK;MAC7DA,OAAO,EAAPA;IACJ,CAAC;EAAA,CAAC,CAAC;AACP;AACO,SAASC,YAAYA,CAACC,MAAM,EAAE;EACjC,OAAOtC,WAAW,CAAC,KAAK,EAAE,SAAS,EAAE;IAAEsC,MAAM,EAANA;EAAO,CAAC,CAAC;AACpD;AACO,SAASC,UAAUA,CAAA,EAAG;EACzB,OAAOvC,WAAW,CAAC,MAAM,EAAE,cAAc,CAAC;AAC9C;AACO,SAASwC,YAAYA,CAACC,QAAQ,EAAE;EACnC,OAAOzC,WAAW,CAAC,MAAM,EAAE,gBAAgB,EAAE;IAAEyC,QAAQ,EAARA;EAAS,CAAC,CAAC,CAACN,IAAI,CAAC,UAAAC,OAAO;IAAA,OAAK;MACxEA,OAAO,EAAPA;IACJ,CAAC;EAAA,CAAC,CAAC;AACP;AACO,SAASM,iBAAiBA,CAACC,cAAc,EAAE;EAC9C,OAAO3C,WAAW,CAAC,KAAK,EAAE,gBAAgB,EAAE;IAAE2C,cAAc,EAAdA;EAAe,CAAC,CAAC;AACnE;AACO,SAASC,iBAAiBA,CAAA,EAAG;EAChC,OAAO5C,WAAW,CAAC,KAAK,EAAE,gBAAgB,CAAC;AAC/C;AACO,SAAS6C,yBAAyBA,CAAA,EAAG;EACxC,OAAO7C,WAAW,CAAC,MAAM,EAAE,0BAA0B,CAAC;AAC1D;AACO,SAAS8C,yBAAyBA,CAAA,EAAG;EACxC,OAAO9C,WAAW,CAAC,KAAK,EAAE,4BAA4B,CAAC;AAC3D;AACO,SAAS+C,0BAA0BA,CAACd,KAAK,EAAE;EAC9C,OAAOjC,WAAW,CAAC,KAAK,EAAE,4BAA4B,EAAEiC,KAAK,CAAC;AAClE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrEA,IAAAe,oBAAA,GAAwiBC,MAAM,CAACC,YAAY;EAAnjBC,WAAW,GAAAH,oBAAA,CAAXG,WAAW;EAAEC,QAAQ,GAAAJ,oBAAA,CAARI,QAAQ;EAAEC,cAAc,GAAAL,oBAAA,CAAdK,cAAc;EAAEC,gBAAgB,GAAAN,oBAAA,CAAhBM,gBAAgB;EAAEC,QAAQ,GAAAP,oBAAA,CAARO,QAAQ;EAAEC,aAAa,GAAAR,oBAAA,CAAbQ,aAAa;EAAEC,GAAG,GAAAT,oBAAA,CAAHS,GAAG;EAAEC,WAAW,GAAAV,oBAAA,CAAXU,WAAW;EAAEC,cAAc,GAAAX,oBAAA,CAAdW,cAAc;EAAEC,kBAAkB,GAAAZ,oBAAA,CAAlBY,kBAAkB;EAAEtB,MAAM,GAAAU,oBAAA,CAANV,MAAM;EAAEuB,cAAc,GAAAb,oBAAA,CAAda,cAAc;EAAEC,YAAY,GAAAd,oBAAA,CAAZc,YAAY;EAAEC,SAAS,GAAAf,oBAAA,CAATe,SAAS;EAAEC,UAAU,GAAAhB,oBAAA,CAAVgB,UAAU;EAAEC,iBAAiB,GAAAjB,oBAAA,CAAjBiB,iBAAiB;EAAEC,mBAAmB,GAAAlB,oBAAA,CAAnBkB,mBAAmB;EAAEC,kBAAkB,GAAAnB,oBAAA,CAAlBmB,kBAAkB;EAAEC,mBAAmB,GAAApB,oBAAA,CAAnBoB,mBAAmB;EAAEC,iBAAiB,GAAArB,oBAAA,CAAjBqB,iBAAiB;EAAEC,MAAM,GAAAtB,oBAAA,CAANsB,MAAM;EAAEC,QAAQ,GAAAvB,oBAAA,CAARuB,QAAQ;EAAEC,UAAU,GAAAxB,oBAAA,CAAVwB,UAAU;EAAEC,UAAU,GAAAzB,oBAAA,CAAVyB,UAAU;EAAEC,OAAO,GAAA1B,oBAAA,CAAP0B,OAAO;EAAEC,YAAY,GAAA3B,oBAAA,CAAZ2B,YAAY;EAAEC,WAAW,GAAA5B,oBAAA,CAAX4B,WAAW;EAAEC,QAAQ,GAAA7B,oBAAA,CAAR6B,QAAQ;EAAEC,aAAa,GAAA9B,oBAAA,CAAb8B,aAAa;EAAEjF,SAAS,GAAAmD,oBAAA,CAATnD,SAAS;EAAEC,OAAO,GAAAkD,oBAAA,CAAPlD,OAAO;EAAEiF,YAAY,GAAA/B,oBAAA,CAAZ+B,YAAY;EAAEC,iBAAiB,GAAAhC,oBAAA,CAAjBgC,iBAAiB;EAAEC,KAAK,GAAAjC,oBAAA,CAALiC,KAAK;EAAEzC,YAAY,GAAAQ,oBAAA,CAAZR,YAAY;EAAE0C,SAAS,GAAAlC,oBAAA,CAATkC,SAAS;EAAEC,YAAY,GAAAnC,oBAAA,CAAZmC,YAAY;EAAEC,yBAAyB,GAAApC,oBAAA,CAAzBoC,yBAAyB;EAAEC,YAAY,GAAArC,oBAAA,CAAZqC,YAAY;;;;;;;;;;;;;;;;ACA3hB,IAAMC,WAAW,GAAG;EACvBC,MAAM,EAAE,gBAAgB;EACxBC,OAAO,EAAE,4BAA4B;EACrCC,YAAY,EAAE,8BAA8B;EAC5CC,cAAc,EAAE,iCAAiC;EACjDC,sBAAsB,EAAE,oCAAoC;EAC5DC,sBAAsB,EAAE,6BAA6B;EACrDC,wBAAwB,EAAE,+BAA+B;EACzDC,sBAAsB,EAAE,6BAA6B;EACrDC,kBAAkB,EAAE,qBAAqB;EACzCC,mBAAmB,EAAE,gCAAgC;EACrDC,oBAAoB,EAAE,6BAA6B;EACnDC,qBAAqB,EAAE,uBAAuB;EAC9CC,2BAA2B,EAAE,uBAAuB;EACpDC,yBAAyB,EAAE,gCAAgC;EAC3DC,qBAAqB,EAAE,yBAAyB;EAChDC,YAAY,EAAE,eAAe;EAC7BC,6BAA6B,EAAE;AACnC,CAAC;;;;;;;;;;;;;;;;;;;AClB8D;AAE1B;AACG;AACxC,IAAMO,oBAAoB,gBAAGD,sDAAM;EAAAE,IAAA;EAAAC,SAAA;EAAAC,SAAA;AAAA,EAclC;AACD,IAAMC,WAAW,gBAAGL,sDAAM;EAAAE,IAAA;EAAAC,SAAA;EAAAC,SAAA;AAAA,EAKzB;AACM,IAAME,eAAe,GAAGA,SAAlBA,eAAeA,CAAA;EAAA,OAAUR,uDAAK,CAACG,oBAAoB,EAAE;IAAEM,QAAQ,EAAE,CAACX,sDAAI,CAAC,KAAK,EAAE;MAAEY,GAAG,EAAE,kBAAkB;MAAEC,KAAK,EAAE,KAAK;MAAEC,GAAG,EAAE;IAA0E,CAAC,CAAC,EAAEd,sDAAI,CAACS,WAAW,EAAE;MAAEE,QAAQ,EAAER,mDAAE,CAAC,4DAA4D,EAAE,QAAQ;IAAE,CAAC,CAAC,EAAEH,sDAAI,CAAC,GAAG,EAAE;MAAEW,QAAQ,EAAER,mDAAE,CAAC,8EAA8E,EAAE,QAAQ;IAAE,CAAC,CAAC,EAAEH,sDAAI,CAAC,GAAG,EAAE;MAAEW,QAAQ,EAAER,mDAAE,CAAC,iEAAiE,EAAE,QAAQ;IAAE,CAAC,CAAC;EAAE,CAAC,CAAE;AAAA;;;;;;;;;;;;;;;;;;;;;ACzBjiB,IAAIY,GAAG;AACd,CAAC,UAAUA,GAAG,EAAE;EACZA,GAAG,CAACA,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EAC/BA,GAAG,CAACA,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU;EACrCA,GAAG,CAACA,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;EACjCA,GAAG,CAACA,GAAG,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,GAAG,gBAAgB;EACjDA,GAAG,CAACA,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY;AAC7C,CAAC,EAAEA,GAAG,KAAKA,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AACd,IAAMC,SAAS,GAAAC,eAAA,CAAAA,eAAA,CAAAA,eAAA,CAAAA,eAAA,CAAAA,eAAA,KACjBF,GAAG,CAACG,KAAK,EAAG,qBAAqB,GACjCH,GAAG,CAACI,QAAQ,EAAG,yBAAyB,GACxCJ,GAAG,CAACK,MAAM,EAAG,uBAAuB,GACpCL,GAAG,CAACM,cAAc,EAAG,uBAAuB,GAC5CN,GAAG,CAACO,UAAU,EAAG,yBAAyB,CAC9C;;;;;;;;;;;;;;;ACdM,IAAMC,YAAY,GAAG;EACxBC,gBAAgB,EAAE,4CAA4C;EAC9DC,gBAAgB,EAAE,4CAA4C;EAC9DC,iBAAiB,EAAE,6CAA6C;EAChEC,mBAAmB,EAAE,+CAA+C;EACpEC,UAAU,EAAE,qCAAqC;EACjDC,YAAY,EAAE,wCAAwC;EACtDC,uBAAuB,EAAE;AAC7B,CAAC;;;;;;;;;;;;;;;ACRM,IAAMC,YAAY,GAAG;EACxBC,uBAAuB,EAAE;AAC7B,CAAC;;;;;;;;;;;;;;;;;;;;;;;;ACFmC;AACE;AACM;AACJ;;;;;;;;;;;;;;;;ACHjC,IAAMC,gBAAgB,GAAG;EAC5BC,2BAA2B,EAAE;AACjC,CAAC;;;;;;;;;;;;;;;ACFM,IAAMC,cAAc,GAAG;EAC1BC,wBAAwB,EAAE,4BAA4B;EACtDC,kBAAkB,EAAE,sBAAsB;EAC1CC,YAAY,EAAE,uCAAuC;EACrDC,4BAA4B,EAAE,mCAAmC;EACjEC,6BAA6B,EAAE,oCAAoC;EACnEC,0BAA0B,EAAE,iCAAiC;EAC7DC,6BAA6B,EAAE,oCAAoC;EACnEC,2BAA2B,EAAE,kCAAkC;EAC/DC,wBAAwB,EAAE,6BAA6B;EACvDC,yBAAyB,EAAE,oCAAoC;EAC/DC,sBAAsB,EAAE,iCAAiC;EACzDC,yBAAyB,EAAE,8BAA8B;EACzDC,uBAAuB,EAAE,4BAA4B;EACrDC,iBAAiB,EAAE,qBAAqB;EACxCC,kBAAkB,EAAE,sBAAsB;EAC1CC,eAAe,EAAE,mBAAmB;EACpCC,sBAAsB,EAAE,2BAA2B;EACnDC,0BAA0B,EAAE,+BAA+B;EAC3DC,2BAA2B,EAAE,gCAAgC;EAC7DC,wBAAwB,EAAE,6BAA6B;EACvDC,6BAA6B,EAAE,kCAAkC;EACjEC,8BAA8B,EAAE,mCAAmC;EACnEC,2BAA2B,EAAE,gCAAgC;EAC7DC,2BAA2B,EAAE,gCAAgC;EAC7DC,4BAA4B,EAAE,iCAAiC;EAC/DC,yBAAyB,EAAE,8BAA8B;EACzDC,iCAAiC,EAAE,uCAAuC;EAC1EC,+BAA+B,EAAE,qCAAqC;EACtEC,2BAA2B,EAAE,gCAAgC;EAC7DC,4BAA4B,EAAE,iCAAiC;EAC/DC,yBAAyB,EAAE;AAC/B,CAAC;;;;;;;;;;;;;;;AChCM,IAAMC,aAAa,GAAG;EACzBC,UAAU,EAAE,aAAa;EACzBC,SAAS,EAAE,YAAY;EACvBC,sBAAsB,EAAE,2BAA2B;EACnDC,uBAAuB,EAAE,2BAA2B;EACpDC,SAAS,EAAE,YAAY;EACvBC,qBAAqB,EAAE,0BAA0B;EACjDC,kCAAkC,EAAE,yCAAyC;EAC7EC,wBAAwB,EAAE,8BAA8B;EACxDC,uBAAuB,EAAE,2BAA2B;EACpDC,sBAAsB,EAAE,2BAA2B;EACnDC,4BAA4B,EAAE,kCAAkC;EAChEC,uBAAuB,EAAE,4BAA4B;EACrDC,yBAAyB,EAAE,8BAA8B;EACzDC,sBAAsB,EAAE,2BAA2B;EACnDC,uBAAuB,EAAE,4BAA4B;EACrDC,4BAA4B,EAAE,iCAAiC;EAC/DC,0BAA0B,EAAE,+BAA+B;EAC3DC,uBAAuB,EAAE;AAC7B,CAAC;;;;;;;;;;;;;;;;;;;ACnBqD;AAC+L;AACjL;AACW;AAC/E,IAAMI,aAAa,GAAG,IAAIC,GAAG,CAAC,CAC1B,CACIvD,4EAA2B,EAC3B,UAACxG,OAAO,EAAK;EACTI,qEAAY,CAACJ,OAAO,CAACtB,OAAO,CAAC;AACjC,CAAC,CACJ,EACD,CACI8H,6FAA4C,EAC5C,UAACxG,OAAO,EAAEgK,QAAQ,EAAK;EACnBpK,gFAAuB,CAACI,OAAO,CAACtB,OAAO,CAAC,CACnCqB,IAAI,CAAC,YAAM;IACZiK,QAAQ,CAACC,WAAW,CAAC;MACjBC,GAAG,EAAE1D,6FAA4C;MACjD9H,OAAO,EAAEsB,OAAO,CAACtB;IACrB,CAAC,CAAC;EACN,CAAC,CAAC,SACQ,CAAC,UAAAA,OAAO,EAAI;IAClBsL,QAAQ,CAACC,WAAW,CAAC;MACjBC,GAAG,EAAE1D,2FAA0C;MAC/C9H,OAAO,EAAPA;IACJ,CAAC,CAAC;EACN,CAAC,CAAC;AACN,CAAC,CACJ,EACD,CACI8H,4FAA2C,EAC3C,UAAC2D,SAAS,EAAEH,QAAQ,EAAK;EACrBlK,qFAA4B,CAAC,CAAC,CACzBC,IAAI,CAAC,UAAAqK,IAAA,EAA0B;IAAA,IAAd1L,OAAO,GAAA0L,IAAA,CAAhBpK,OAAO;IAChBgK,QAAQ,CAACC,WAAW,CAAC;MACjBC,GAAG,EAAE1D,6FAA4C;MACjD9H,OAAO,EAAPA;IACJ,CAAC,CAAC;EACN,CAAC,CAAC,SACQ,CAAC,UAAAA,OAAO,EAAI;IAClBsL,QAAQ,CAACC,WAAW,CAAC;MACjBC,GAAG,EAAE1D,0FAAyC;MAC9C9H,OAAO,EAAPA;IACJ,CAAC,CAAC;EACN,CAAC,CAAC;AACN,CAAC,CACJ,EACD,CACI8H,wFAAuC,EACvC,UAAC2D,SAAS,EAAEH,QAAQ,EAAK;EACrBxJ,0EAAiB,CAAC,CAAC,CACdT,IAAI,CAAC,UAAArB,OAAO,EAAI;IACjBsL,QAAQ,CAACC,WAAW,CAAC;MACjBC,GAAG,EAAE1D,yFAAwC;MAC7C9H,OAAO,EAAPA;IACJ,CAAC,CAAC;EACN,CAAC,CAAC,SACQ,CAAC,UAAAA,OAAO,EAAI;IAClBsL,QAAQ,CAACC,WAAW,CAAC;MACjBC,GAAG,EAAE1D,sFAAqC;MAC1C9H,OAAO,EAAPA;IACJ,CAAC,CAAC;EACN,CAAC,CAAC;AACN,CAAC,CACJ,EACD,CACI8H,yFAAwC,EACxC,UAACxG,OAAO,EAAEgK,QAAQ,EAAK;EACnB1J,0EAAiB,CAACN,OAAO,CAACtB,OAAO,CAAC,CAC7BqB,IAAI,CAAC,UAAArB,OAAO,EAAI;IACjBsL,QAAQ,CAACC,WAAW,CAAC;MACjBC,GAAG,EAAE1D,yFAAwC;MAC7C9H,OAAO,EAAPA;IACJ,CAAC,CAAC;EACN,CAAC,CAAC,SACQ,CAAC,UAAAA,OAAO,EAAI;IAClBsL,QAAQ,CAACC,WAAW,CAAC;MACjBC,GAAG,EAAE1D,uFAAsC;MAC3C9H,OAAO,EAAPA;IACJ,CAAC,CAAC;EACN,CAAC,CAAC;AACN,CAAC,CACJ,EACD,CACI8H,iFAAgC,EAChC,UAAC2D,SAAS,EAAEH,QAAQ,EAAK;EACrB7J,mEAAU,CAAC,CAAC,CACPJ,IAAI,CAAC,UAAArB,OAAO,EAAI;IACjBsL,QAAQ,CAACC,WAAW,CAAC;MACjBC,GAAG,EAAE1D,kFAAiC;MACtC9H,OAAO,EAAPA;IACJ,CAAC,CAAC;EACN,CAAC,CAAC,SACQ,CAAC,UAAAA,OAAO,EAAI;IAClBsL,QAAQ,CAACC,WAAW,CAAC;MACjBC,GAAG,EAAE1D,+EAA8B;MACnC9H,OAAO,EAAPA;IACJ,CAAC,CAAC;EACN,CAAC,CAAC;AACN,CAAC,CACJ,EACD,CACI8H,sFAAqC,EACrC,UAACxG,OAAO,EAAK;EACT2J,gFAA4B,CAAC3J,OAAO,CAACtB,OAAO,CAAC;AACjD,CAAC,CACJ,EACD,CACI8H,0FAAyC,EACzC,UAACxG,OAAO,EAAEgK,QAAQ,EAAK;EACnBH,0EAAY,CAAC7J,OAAO,CAACtB,OAAO,CAAC2L,KAAK,CAAC,CAC9BtK,IAAI,CAAC,UAAArB,OAAO,EAAI;IACjBsL,QAAQ,CAACC,WAAW,CAAC;MACjBC,GAAG,EAAE1D,2FAA0C;MAC/C9H,OAAO,EAAEA;IACb,CAAC,CAAC;EACN,CAAC,CAAC,SACQ,CAAC,UAAAA,OAAO,EAAI;IAClBsL,QAAQ,CAACC,WAAW,CAAC;MACjBC,GAAG,EAAE1D,wFAAuC;MAC5C9H,OAAO,EAAPA;IACJ,CAAC,CAAC;EACN,CAAC,CAAC;AACN,CAAC,CACJ,EACD,CACI8H,6FAA4C,EAC5C,UAACxG,OAAO,EAAEgK,QAAQ,EAAK;EACnBJ,6EAAe,CAAC5J,OAAO,CAACtB,OAAO,CAAC4L,eAAe,CAAC,CAC3CvK,IAAI,CAAC,UAAArB,OAAO,EAAI;IACjBsL,QAAQ,CAACC,WAAW,CAAC;MACjBC,GAAG,EAAE1D,8FAA6C;MAClD9H,OAAO,EAAEA;IACb,CAAC,CAAC;EACN,CAAC,CAAC,SACQ,CAAC,UAAAA,OAAO,EAAI;IAClBsL,QAAQ,CAACC,WAAW,CAAC;MACjBC,GAAG,EAAE1D,2FAA0C;MAC/C9H,OAAO,EAAPA;IACJ,CAAC,CAAC;EACN,CAAC,CAAC;AACN,CAAC,CACJ,EACD,CACI8H,2FAA0C,EAC1C,UAAC2D,SAAS,EAAEH,QAAQ,EAAK;EACrBvJ,kFAAyB,CAAC,CAAC,CACtBV,IAAI,CAAC,YAAM;IACZiK,QAAQ,CAACC,WAAW,CAAC;MACjBC,GAAG,EAAE1D,4FAA2C;MAChD9H,OAAO,EAAE,CAAC;IACd,CAAC,CAAC;EACN,CAAC,CAAC,SACQ,CAAC,UAAAA,OAAO,EAAI;IAClBsL,QAAQ,CAACC,WAAW,CAAC;MACjBC,GAAG,EAAE1D,yFAAwC;MAC7C9H,OAAO,EAAPA;IACJ,CAAC,CAAC;EACN,CAAC,CAAC;AACN,CAAC,CACJ,EACD,CACI8H,2FAA0C,EAC1C,UAAC2D,SAAS,EAAEH,QAAQ,EAAK;EACrBtJ,kFAAyB,CAAC,CAAC,CACtBX,IAAI,CAAC,UAAArB,OAAO,EAAI;IACjBsL,QAAQ,CAACC,WAAW,CAAC;MACjBC,GAAG,EAAE1D,4FAA2C;MAChD9H,OAAO,EAAPA;IACJ,CAAC,CAAC;EACN,CAAC,CAAC,SACQ,CAAC,UAAAA,OAAO,EAAI;IAClBsL,QAAQ,CAACC,WAAW,CAAC;MACjBC,GAAG,EAAE1D,yFAAwC;MAC7C9H,OAAO,EAAPA;IACJ,CAAC,CAAC;EACN,CAAC,CAAC;AACN,CAAC,CACJ,EACD,CACI8H,iGAAgD,EAChD,UAAA+D,KAAA,EAAcP,QAAQ,EAAK;EAAA,IAAxBtL,OAAO,GAAA6L,KAAA,CAAP7L,OAAO;EACNiC,mFAA0B,CAACjC,OAAO,CAAC,CAC9BqB,IAAI,CAAC,YAAM;IACZiK,QAAQ,CAACC,WAAW,CAAC;MACjBC,GAAG,EAAE1D,4FAA2C;MAChD9H,OAAO,EAAPA;IACJ,CAAC,CAAC;EACN,CAAC,CAAC,SACQ,CAAC,UAAAA,OAAO,EAAI;IAClBsL,QAAQ,CAACC,WAAW,CAAC;MACjBC,GAAG,EAAE1D,+FAA8C;MACnD9H,OAAO,EAAPA;IACJ,CAAC,CAAC;EACN,CAAC,CAAC;AACN,CAAC,CACJ,CACJ,CAAC;AACK,IAAM8L,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAIR,QAAQ;EAAA,OAAK,UAAChK,OAAO,EAAK;IACxD,IAAMyK,IAAI,GAAGX,aAAa,CAACY,GAAG,CAAC1K,OAAO,CAACkK,GAAG,CAAC;IAC3C,IAAIO,IAAI,EAAE;MACNA,IAAI,CAACzK,OAAO,EAAEgK,QAAQ,CAAC;IAC3B;EACJ,CAAC;AAAA;;;;;;;;;;;;;;;;;;;;;;;;AC3M+C;AACR;AACP;AACoB;AACP;AACZ;AACkB;AACpD,IAAMc,sBAAsB,GAAG,SAAzBA,sBAAsBA,CAAIC,KAAK,EAAK;EACtC,IAAMC,SAAS,GAAGC,QAAQ,CAACC,cAAc,CAAChI,mFAAiC,CAAC;EAC5E,IAAMiI,iBAAiB,GAAGN,2DAAc,CAACE,KAAK,CAACK,GAAG,EAAEL,KAAK,CAACM,WAAW,EAAEL,SAAS,CAAC;EACjF,IAAIA,SAAS,IAAI,CAACG,iBAAiB,EAAE;IACjC,oBAAOP,6DAAqB,CAACG,KAAK,CAAC/F,QAAQ,EAAEgG,SAAS,CAAC;EAC3D;EACA,OAAQ3G,sDAAI,CAACsG,2CAAQ,EAAE;IAAE3F,QAAQ,EAAE,CAAC,CAACgG,SAAS,IAAIG,iBAAiB,KAAK9G,sDAAI,CAACU,6DAAe,EAAE,CAAC,CAAC;EAAE,CAAC,CAAC;AACxG,CAAC;AACD,IAAMwG,eAAe,GAAG,SAAlBA,eAAeA,CAAA,EAAS;EAC1B,IAAMC,uBAAuB,GAAGP,QAAQ,CAACC,cAAc,CAAChI,mFAAiC,CAAC;EAC1F,IAAIkI,GAAG;EACP,IAAMjN,WAAW,GAAG,IAAIsN,eAAe,CAACC,QAAQ,CAACC,MAAM,CAAC;EACxD,IAAMC,IAAI,GAAGzN,WAAW,CAACuM,GAAG,CAAC,MAAM,CAAC;EACpC,IAAMW,WAAW,GAAGlN,WAAW,CAACuM,GAAG,CAAC,iBAAiB,CAAC,KAAK,QAAQ;EACnE,QAAQkB,IAAI;IACR,KAAK,cAAc;MACfR,GAAG,GAAGhG,iDAAS;MACf;IACJ,KAAK,kBAAkB;MACnBgG,GAAG,GAAGhG,oDAAY;MAClB;IACJ,KAAK,iBAAiB;MAClBgG,GAAG,GAAGhG,0DAAkB;MACxB;IACJ,KAAK,mBAAmB;IACxB;MACIgG,GAAG,GAAGhG,kDAAU;MAChB;EACR;EACAwF,uDAAe,CAACvG,sDAAI,CAACyG,sBAAsB,EAAE;IAAEM,GAAG,EAAEA,GAAG;IAAEC,WAAW,EAAEA;EAAY,CAAC,CAAC,EAAEG,uBAAuB,CAAC;AAClH,CAAC;AACD,iEAAeD,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtCI;AACD;AAC0W;AAC9V;AACW;AACa;AACrE,IAAMU,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAA,EAAS;EAC/B,OAAO;IACHjL,QAAQ,EAAEiB,+EAA0BjB;EACxC,CAAC;AACL,CAAC;AACD,IAAMkL,eAAe,GAAG,SAAlBA,eAAeA,CAAA,EAAS;EAC1B,IAAMC,gBAAgB,GAAGC,MAAM,CAACC,IAAI,CAACpK,sEAAiB,CAAC,CAClDqK,MAAM,CAAC,UAAAC,CAAC;IAAA,OAAI,MAAM,CAACC,IAAI,CAACD,CAAC,CAAC;EAAA,EAAC,CAC3BE,MAAM,CAAC,UAACC,CAAC,EAAEC,CAAC;IAAA,OAAAC,aAAA,CAAAtH,eAAA,KACZqH,CAAC,EAAG1K,sEAAiB,CAAC0K,CAAC,CAAC,GACtBD,CAAC;EAAA,CACN,EAAE,CAAC,CAAC,CAAC;EACP,OAAAE,aAAA;IACI7L,WAAW,EAAXA,gEAAW;IACX8L,KAAK,EAAE5K,4EAAuB;IAC9BjB,QAAQ,EAARA,6DAAQ;IACR8L,OAAO,EAAE7K,8EAAyB;IAClCf,gBAAgB,EAAhBA,qEAAgB;IAChBC,QAAQ,EAARA,6DAAQ;IACR4L,KAAK,EAAE9K,4EAAuB;IAC9B+K,SAAS,EAAE/K,gFAA2B;IACtCgL,SAAS,EAAEhL,gFAA2B;IACtCiL,aAAa,EAAEjL,oFAA+B;IAC9CkL,QAAQ,EAAElL,+EAA0B;IACpCJ,iBAAiB,EAAjBA,sEAAiB;IACjBC,mBAAmB,EAAnBA,wEAAmB;IACnBC,kBAAkB,EAAlBA,uEAAkB;IAClBC,mBAAmB,EAAnBA,wEAAmB;IACnBoL,IAAI,EAAEnL,2EAAsB;IAC5BoI,KAAK,EAAEpI,4EAAuB;IAC9BG,UAAU,EAAVA,+DAAU;IACVE,OAAO,EAAPA,4DAAO;IACPC,YAAY,EAAZA,iEAAY;IACZC,WAAW,EAAXA,gEAAW;IACXC,QAAQ,EAARA,6DAAQ;IACRG,iBAAiB,EAAjBA,sEAAiB;IACjBC,KAAK,EAALA,0DAAK;IACLzC,YAAY,EAAE6B,mFAA8B;IAC5CoL,WAAW,EAAEpL,kFAA6B;IAC1Ca,SAAS,EAATA,8DAAS;IACTC,YAAY,EAAZA,iEAAY;IACZC,yBAAyB,EAAzBA,8EAAyB;IACzBC,YAAY,EAAZA,iEAAYA;EAAA,GACTkJ,gBAAgB;AAE3B,CAAC;AACD,IAAMmB,aAAa,GAAG,SAAhBA,aAAaA,CAAIlC,GAAG,EAA0B;EAAA,IAAxBC,WAAW,GAAArN,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EAC3C,IAAAuP,OAAA,GAAyF1M,MAAM;IAAvF2M,oBAAoB,GAAAD,OAAA,CAApBC,oBAAoB;IAAEC,eAAe,GAAAF,OAAA,CAAfE,eAAe;IAAEC,kBAAkB,GAAAH,OAAA,CAAlBG,kBAAkB;IAAEC,gBAAgB,GAAAJ,OAAA,CAAhBI,gBAAgB;EACnF,IAAIC,OAAO;EACX,QAAQxC,GAAG;IACP,KAAKhG,kDAAU;MACXwI,OAAO,GAAG,IAAID,gBAAgB,CAAC,CAAC;MAChC;IACJ,KAAKvI,0DAAkB;MACnBwI,OAAO,GAAG,IAAID,gBAAgB,CAAC,CAAC,CAACE,qBAAqB,CAAC,CAAC;MACxD;IACJ,KAAKzI,iDAAS;MACVwI,OAAO,GAAG,IAAIH,eAAe,CAAC,CAAC,CAACK,sBAAsB,CAAC7B,oBAAoB,CAAC,CAAC,CAAC;MAC9E,IAAIZ,WAAW,EAAE;QACbuC,OAAO,GAAGA,OAAO,CAACG,oBAAoB,CAAC,CAAC;MAC5C;MACA;IACJ,KAAK3I,oDAAY;MACbwI,OAAO,GAAG,IAAIF,kBAAkB,CAAC,CAAC;MAClC,IAAIrC,WAAW,EAAE;QACbuC,OAAO,GAAGA,OAAO,CAACI,wBAAwB,CAAC,CAAC;MAChD;MACA;IACJ;MACIJ,OAAO,GAAG,IAAIJ,oBAAoB,CAAC,CAAC;EAC5C;EACA,OAAOI,OAAO;AAClB,CAAC;AACc,SAAS/C,cAAcA,CAACO,GAAG,EAAEC,WAAW,EAAEL,SAAS,EAAE;EAChEiD,OAAO,CAACC,IAAI,CAAC,6CAA6C,EAAE7I,iDAAS,CAAC+F,GAAG,CAAC,EAAEJ,SAAS,CAAC;EACtF,IAAMG,iBAAiB,GAAGa,mEAAoB,CAAC3G,iDAAS,CAAC+F,GAAG,CAAC,CAAC;EAC9DU,gDAAS,CAAC,YAAM;IACZ,IAAAqC,QAAA,GAAkCtN,MAAM;MAAhCuN,qBAAqB,GAAAD,QAAA,CAArBC,qBAAqB;IAC7B,IAAIA,qBAAqB,EAAE;MACvB,IAAMR,OAAO,GAAGN,aAAa,CAAClC,GAAG,EAAEC,WAAW,CAAC,CAC1CgD,SAAS,CAACnM,2DAAM,CAAC,CACjBoM,WAAW,CAACnN,6DAAQ,CAAC,CACrBoN,eAAe,CAAC5L,iEAAY,CAAC,CAC7B6L,eAAe,CAACtC,eAAe,CAAC,CAAC,CAAC;MACvC,IAAMlC,QAAQ,GAAG,IAAIoE,qBAAqB,CAAC/I,iDAAS,CAAC+F,GAAG,CAAC,EAAE3I,6DAAQ,EAAEhB,mEAAc,EAAEsK,uDAAY,EAAEpJ,iEAAY,GAAG,EAAE,GAAGf,+DAAU,CAAC,CAAC6M,UAAU,CAACb,OAAO,CAAC;MACtJ5D,QAAQ,CAAC0E,SAAS,CAAClE,qEAAiB,CAACR,QAAQ,CAAC,CAAC;MAC/CA,QAAQ,CAAC2E,QAAQ,CAAC3D,SAAS,EAAE,IAAI,CAAC;MAClChB,QAAQ,CAAC4E,mBAAmB,CAAC,CAAC,CAAC,CAAC;MAChC/N,MAAM,CAACmJ,QAAQ,GAAGA,QAAQ;IAC9B;EACJ,CAAC,EAAE,EAAE,CAAC;EACN,IAAImB,iBAAiB,EAAE;IACnB8C,OAAO,CAAC/O,KAAK,CAAC,oCAAoC,EAAE;MAChDuD,QAAQ,EAARA,6DAAQ;MACRuI,SAAS,EAATA,SAAS;MACT6D,OAAO,EAAExJ,iDAAS,CAAC+F,GAAG,CAAC;MACvB0D,wBAAwB,EAAE,CAAC,CAACjO,MAAM,CAACuN;IACvC,CAAC,CAAC;IACF5Q,mEAAsB,CAAC,IAAIwR,KAAK,CAAC,4BAA4B,CAAC,EAAE;MAC5DzP,WAAW,EAAE,CAAC,kBAAkB,EAAE,oBAAoB,CAAC;MACvD0P,KAAK,EAAE;QACHxM,QAAQ,EAARA,6DAAQ;QACRuI,SAAS,EAATA,SAAS;QACTI,GAAG,EAAHA,GAAG;QACH3J,cAAc,EAAdA,mEAAc;QACdG,UAAU,EAAVA,+DAAU;QACViN,OAAO,EAAExJ,iDAAS,CAAC+F,GAAG,CAAC;QACvB8D,eAAe,EAAE,CAAC,CAACvM,iEAAYA;MACnC;IACJ,CAAC,CAAC;EACN;EACA,OAAOwI,iBAAiB;AAC5B;;;;;;;;;;;;;;;;;;;ACtH6B;AAC8F;AACpH,SAASgE,cAAcA,CAAA,EAAG;EAC7B,IAAI1N,2EAAsB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;IACxC;EACJ;EACA,IAAM4N,MAAM,GAAG5N,2EAAsB,CAAC,gBAAgB,EAAE,EAAE,CAAC;EAC3DjE,sDAAY,uDAAAc,MAAA,CAAuD+Q,MAAM,YAAS;IAC9EG,UAAU,EAAE;MACRC,QAAQ,EAAE;IACd,CAAC;IACDC,kBAAkB,WAAlBA,kBAAkBA,CAAC3R,IAAI,EAAE;MACrB,OAAQ,CAAC,CAACA,IAAI,IAAI,CAAC,CAACA,IAAI,CAAC4R,OAAO,IAAI,mBAAmB,CAACnD,IAAI,CAACzO,IAAI,CAAC4R,OAAO,CAAC;IAC9E,CAAC;IACDC,OAAO,EAAE5N,wEAAmBA;EAChC,CAAC,CAAC,CAAC6N,OAAO,CAAC,CAAC;EACZrS,8DAAoB,CAAC;IACjBuS,CAAC,EAAE/N,wEAAmB;IACtBgO,GAAG,EAAE5N,+DAAU;IACf6N,SAAS,EAAEnN,8DAASA;EACxB,CAAC,CAAC;EACFtF,+DAAqB,CAAC;IAClB2S,GAAG,EAAE1N,6DAAQ;IACbH,OAAO,EAAE8J,MAAM,CAACC,IAAI,CAAC/J,4DAAO,CAAC,CACxB8N,GAAG,CAAC,UAAAzL,IAAI;MAAA,UAAArG,MAAA,CAAOqG,IAAI,OAAArG,MAAA,CAAIgE,4DAAO,CAACqC,IAAI,CAAC;IAAA,CAAE,CAAC,CACvC0L,IAAI,CAAC,GAAG;EACjB,CAAC,CAAC;AACN;AACA,iEAAe7S,iDAAK;;;;;;;;;;;;;;;;;;;AC5BG;AAC8B;AAC9C,SAAS8S,OAAOA,CAACC,MAAM,EAAE;EAC5BpB,0DAAc,CAAC,CAAC;EAChB3R,0DAAa,CAAC+S,MAAM,CAAC;AACzB;AACO,SAASE,cAAcA,CAACF,MAAM,EAAE;EACnC,SAASG,IAAIA,CAAA,EAAG;IACZnT,6CAAC,CAACgT,MAAM,CAAC;EACb;EACAD,OAAO,CAACI,IAAI,CAAC;AACjB;;;;;;;;;;;;;;;;ACXO,SAAS7G,YAAYA,CAACQ,KAAK,EAAE;EAChC,IAAMsG,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;EAC/B,IAAMC,OAAO,GAAGhQ,MAAM,CAACiQ,OAAO;EAC9BH,QAAQ,CAACI,MAAM,CAAC,UAAU,EAAE1G,KAAK,CAAC;EAClCsG,QAAQ,CAACI,MAAM,CAAC,QAAQ,EAAE,uBAAuB,CAAC;EAClD,OAAOC,KAAK,CAACH,OAAO,EAAE;IAClBhT,MAAM,EAAE,MAAM;IACdoT,IAAI,EAAEN,QAAQ;IACdO,SAAS,EAAE;EACf,CAAC,CAAC,CAACnR,IAAI,CAAC,UAAAoR,GAAG;IAAA,OAAIA,GAAG,CAACC,IAAI,CAAC,CAAC;EAAA,EAAC;AAC9B;AACO,SAASxH,eAAeA,CAACyH,UAAU,EAAE;EACxC,OAAOL,KAAK,CAACK,UAAU,EAAE;IACrBxT,MAAM,EAAE,MAAM;IACdqT,SAAS,EAAE;EACf,CAAC,CAAC,CAACnR,IAAI,CAAC,UAAAoR,GAAG;IAAA,OAAIA,GAAG,CAACC,IAAI,CAAC,CAAC;EAAA,EAAC;AAC9B;;;;;;;;;;;;;;;;;;;;;;;;AChB4C;AAC5C,IAAMG,sBAAsB,GAAG,IAAI;AAC5B,SAASvF,oBAAoBA,CAACZ,GAAG,EAAE;EACtC,IAAAoG,SAAA,GAAkDF,+CAAQ,CAAC,KAAK,CAAC;IAAAG,UAAA,GAAAC,cAAA,CAAAF,SAAA;IAA1DrG,iBAAiB,GAAAsG,UAAA;IAAEE,oBAAoB,GAAAF,UAAA;EAC9C3F,gDAAS,CAAC,YAAM;IACZ,IAAM8F,KAAK,GAAGC,UAAU,CAAC,YAAM;MAC3B,IAAM1O,MAAM,GAAG8H,QAAQ,CAACC,cAAc,CAACE,GAAG,CAAC;MAC3C,IAAI,CAACjI,MAAM,EAAE;QACTwO,oBAAoB,CAAC,IAAI,CAAC;MAC9B;IACJ,CAAC,EAAEJ,sBAAsB,CAAC;IAC1B,OAAO,YAAM;MACT,IAAIK,KAAK,EAAE;QACPE,YAAY,CAACF,KAAK,CAAC;MACvB;IACJ,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EACN,OAAOzG,iBAAiB;AAC5B;AACO,IAAMY,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;EAC9B,IAAMgG,aAAa,GAAG9G,QAAQ,CAACC,cAAc,CAAC,eAAe,CAAC;EAC9D,IAAM8G,cAAc,GAAGD,aAAa,GAAGA,aAAa,CAACE,YAAY,GAAG,CAAC;EACrE,IAAMC,QAAQ,GAAGjH,QAAQ,CAACC,cAAc,CAAC,YAAY,CAAC;EACtD,IAAMiH,cAAc,GAAID,QAAQ,IAAIA,QAAQ,CAACD,YAAY,IAAK,CAAC;EAC/D,IAAMG,MAAM,GAAG,CAAC;EAChB,IAAIvR,MAAM,CAACwR,WAAW,GAAGL,cAAc,EAAE;IACrC,OAAOA,cAAc,GAAGI,MAAM;EAClC,CAAC,MACI;IACD,OAAOvR,MAAM,CAACwR,WAAW,GAAGF,cAAc,GAAGC,MAAM;EACvD;AACJ,CAAC;;;;;;;;;;;;;;;;AC/BM,SAASzU,mBAAmBA,CAAC2U,SAAS,EAAEnU,WAAW,EAAE;EACxDiO,MAAM,CAACC,IAAI,CAAClO,WAAW,CAAC,CAACoU,OAAO,CAAC,UAAArI,GAAG,EAAI;IACpCoI,SAAS,CAACE,YAAY,CAACzB,MAAM,CAAC7G,GAAG,EAAE/L,WAAW,CAAC+L,GAAG,CAAC,CAAC;EACxD,CAAC,CAAC;AACN;AACO,SAASP,4BAA4BA,CAACO,GAAG,EAAE;EAC9C,IAAMwB,QAAQ,GAAG,IAAIrN,GAAG,CAACwC,MAAM,CAAC6K,QAAQ,CAAC+G,IAAI,CAAC;EAC9C/G,QAAQ,CAAC8G,YAAY,UAAO,CAACtI,GAAG,CAAC;EACjCrJ,MAAM,CAAC6R,OAAO,CAACC,YAAY,CAAC,IAAI,EAAE,EAAE,EAAEjH,QAAQ,CAAC+G,IAAI,CAAC;AACxD;;;;;;;;;;;;ACTA;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;;;;;;;;ACPA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA,gBAAgB,+CAA+C;;AAE/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;;;;;;;;;ACtCA;;AAEA,eAAe,mBAAO,CAAC,wFAA6B;AACpD,gBAAgB,mBAAO,CAAC,gHAAyC;AACjE,uBAAuB,mBAAO,CAAC,iEAAe;;AAE9C,YAAY,mBAAO,CAAC,qDAAS;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,wBAAwB,2FAA+B;;AAEvD;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa,qBAAM,mBAAmB,qBAAM;AAC5C;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,UAAU;AACV;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA,eAAe,QAAQ;AACvB,eAAe,QAAQ;AACvB,gBAAgB;AAChB;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,OAAO;AACP;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;;AAEP;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,UAAU;AACV;AACA,MAAM;AACN;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,UAAU;AACV;AACA,MAAM;AACN;AACA;AACA;;AAEA;;AAEA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA,eAAe,QAAQ;AACvB;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA,eAAe,QAAQ;AACvB,eAAe,UAAU;AACzB,eAAe,OAAO;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA,eAAe,QAAQ;AACvB,eAAe,UAAU;AACzB,eAAe,UAAU;AACzB,gBAAgB,UAAU;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA,eAAe,OAAO;AACtB,eAAe,QAAQ;AACvB,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA,eAAe,QAAQ;AACvB,eAAe,QAAQ;AACvB,gBAAgB;AAChB;AACA;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,OAAO;AACP;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;;AAEA;AACA;;AAEA;AACA;AACA,QAAQ;AACR;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA,eAAe,QAAQ;AACvB,gBAAgB;AAChB;AACA;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA,eAAe,QAAQ;AACvB,gBAAgB;AAChB;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA,eAAe,QAAQ;AACvB,gBAAgB;AAChB;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA,gBAAgB,QAAQ;AACxB;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA,eAAe,QAAQ;AACvB,gBAAgB;AAChB;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA,eAAe,QAAQ;AACvB,gBAAgB;AAChB;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA,eAAe,UAAU;AACzB;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA,eAAe,UAAU;AACzB;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA,eAAe,UAAU;AACzB;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA,eAAe,UAAU;AACzB;AACA;AACA,gBAAgB;AAChB;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA,sCAAsC;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,GAAG;;AAEH;AACA;AACA;;AAEA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iCAAiC;AACjC;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;;AAEA;AACA;AACA;AACA,OAAO;AACP;AACA,GAAG;;AAEH;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,+BAA+B;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,iBAAiB;AACzC;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,yBAAyB;AAC7C;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,SAAS,GAAG;AACZ;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,SAAS;AACT;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;;AAEA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;;AAEA;AACA,4BAA4B,kBAAkB;AAC9C;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA,cAAc;AACd;AACA;AACA;AACA;;AAEA;AACA;AACA,SAAS;AACT;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,iBAAiB;AAC7C;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa;;AAEb;AACA;;AAEA;AACA,aAAa;AACb;AACA,SAAS;AACT;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,+CAA+C;AAC/C;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA,OAAO;AACP;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,GAAG;;AAEH;AACA;AACA,cAAc;AACd;;AAEA;AACA;AACA,MAAM;AACN;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;;AAEP;AACA;AACA,wBAAwB,iDAAiD;AACzE;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,0CAA0C;AAC1C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA,OAAO;AACP;AACA;;AAEA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,oBAAoB,+BAA+B;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,2BAA2B;AAC3B,sBAAsB,qBAAqB;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,0CAA0C;AAC1C,MAAM;AACN;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA;AACA;AACA;AACA,OAAO;AACP;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA,0CAA0C;AAC1C,2CAA2C;;AAE3C;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA,GAAG;;AAEH;AACA;AACA,GAAG;;AAEH;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,SAAS;AACT;AACA,OAAO;AACP;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,KAAK;AACL,GAAG;;AAEH;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA,MAAM;AACN,2EAA2E;AAC3E;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;;;;;;;;;;ACr4DA;AACA;AACA;AACA;AACA;;AAEA,uBAAuB,mBAAO,CAAC,qDAAS;;AAExC;AACA;AACA;AACA;AACA,aAAa,qBAAM,mBAAmB,qBAAM;AAC5C;;AAEA;;AAEA;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;;;;;;;;;;AC9BA;AACA;AACA;AACA,aAAa,qBAAM,mBAAmB,qBAAM;;AAE5C;AACA;AACA;;AAEA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,mCAAmC;AACnC;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,oCAAoC;AACpC;AACA;;AAEA;AACA;AACA,wBAAwB;AACxB;AACA,IAAI;AACJ;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA,kBAAkB,OAAO;AACzB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAS,SAAS;AAClB;AACA;AACA;AACA;AACA,iDAAiD;AACjD,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,gBAAgB,oBAAoB;AACpC;AACA;AACA;AACA;AACA,cAAc,0BAA0B;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA,kCAAkC;AAClC;AACA,kBAAkB,oBAAoB;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,iBAAiB,UAAU;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AChYA,YAAY,mBAAO,CAAC,6DAAiB;;AAErC;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa,qBAAM,mBAAmB,qBAAM;;AAE5C;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,qDAAqD,KAAK;AAC1D,uDAAuD,KAAK;AAC5D;AACA,WAAW,aAAa,YAAY;AACpC;AACA;AACA;AACA,8BAA8B;AAC9B;AACA;AACA,+DAA+D;AAC/D;AACA,+DAA+D;AAC/D;AACA;AACA;AACA;AACA;AACA,oCAAoC;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,eAAe,UAAU;AACzB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,eAAe,UAAU;AACzB;AACA;AACA,sCAAsC,QAAQ;AAC9C;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,eAAe,oBAAoB;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,eAAe,QAAQ;AACvB,eAAe,QAAQ;AACvB,eAAe,iBAAiB;AAChC;AACA,eAAe,kBAAkB;AACjC;AACA,eAAe,QAAQ;AACvB;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN,8BAA8B;;AAE9B;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;;AAEA;AACA,yBAAyB;AACzB;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,eAAe,OAAO;AACtB,eAAe,UAAU;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB,QAAQ;AACR;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA,gBAAgB;AAChB;AACA;;AAEA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B;;AAE1B;AACA;AACA;AACA,eAAe,OAAO;AACtB,gBAAgB,qBAAqB;AACrC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,sCAAsC,OAAO;AAC7C;AACA,qEAAqE;AACrE,iEAAiE;AACjE;AACA;AACA,kCAAkC;AAClC,kCAAkC;AAClC,kCAAkC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA,2BAA2B;AAC3B,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,eAAe,oBAAoB;AACnC;AACA,eAAe,QAAQ;AACvB,eAAe,iBAAiB;AAChC;AACA,eAAe,SAAS;AACxB;AACA,gBAAgB,SAAS;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,0BAA0B;AAC1B,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,MAAM;AACN;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,QAAQ,0CAA0C;AAClD,eAAe,OAAO;AACtB,gBAAgB,qBAAqB;AACrC;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,QAAQ;AACR;AACA;;AAEA;AACA;AACA,qEAAqE;AACrE,UAAU;AACV;;AAEA;AACA;AACA,QAAQ;AACR;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,eAAe,OAAO;AACtB,eAAe,kBAAkB;AACjC;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,CAAC;;AAED;;;;;;;;;;;AC9mBA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,oBAAoB;;AAEpB;AACA,kBAAkB,qBAAqB;AACvC;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,MAAM;AACN;AACA;;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACzEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEa;;AAEb,IAAI,IAAqC;AACzC;AACA;;AAEA,YAAY,mBAAO,CAAC,oBAAO;;AAE3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,iGAAiG,eAAe;AAChH;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,MAAM;;;AAGN;AACA;AACA,KAAK,GAAG;;AAER,kDAAkD;AAClD;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA,4BAA4B;AAC5B;AACA,qCAAqC;;AAErC,gCAAgC;AAChC;AACA;;AAEA,gCAAgC;;AAEhC;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,IAAI;;;AAGJ;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,EAAE;;;AAGF;AACA;AACA,EAAE;;;AAGF;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,YAAY;AACZ;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uCAAuC;;AAEvC;AACA;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA,sBAAsB;AACtB;AACA,SAAS;AACT,uBAAuB;AACvB;AACA,SAAS;AACT,uBAAuB;AACvB;AACA,SAAS;AACT,wBAAwB;AACxB;AACA,SAAS;AACT,wBAAwB;AACxB;AACA,SAAS;AACT,iCAAiC;AACjC;AACA,SAAS;AACT,2BAA2B;AAC3B;AACA,SAAS;AACT,OAAO;AACP;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA,MAAM;;;AAGN;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,2DAA2D;;AAE3D;AACA;;AAEA;AACA,yDAAyD;AACzD;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;;;AAGT;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;;AAEP;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;;AAEA;AACA,QAAQ;AACR;AACA;AACA,UAAU;AACV;AACA;;AAEA;AACA;AACA,MAAM;AACN;AACA;AACA,QAAQ;AACR;AACA;;AAEA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,aAAa,kBAAkB;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;;AAEA;AACA;AACA,gFAAgF;AAChF;AACA;;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,kBAAkB;;;AAGlB;AACA;AACA,cAAc;AACd;;AAEA;AACA;AACA;AACA;AACA,IAAI;AACJ;;AAEA;AACA;AACA;AACA;;AAEA;AACA,IAAI;;;AAGJ;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,8BAA8B;AAC9B;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,2HAA2H;AAC3H;AACA;AACA;;AAEA;AACA,UAAU;AACV;AACA;;AAEA;AACA;;AAEA,oEAAoE;;AAEpE;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA,iCAAiC;;AAEjC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;;;AAGF;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,wCAAwC;AACxC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd,WAAW,GAAG;AACd,WAAW,GAAG;AACd,WAAW,eAAe;AAC1B,WAAW,GAAG;AACd,WAAW,GAAG;AACd;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK,GAAG;;AAER;AACA;AACA;AACA;AACA;AACA,KAAK,GAAG;AACR;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB;;AAEA;AACA;AACA,kBAAkB;;AAElB;AACA;AACA,oBAAoB;AACpB,2DAA2D,UAAU;AACrE,yBAAyB,UAAU;AACnC;AACA,aAAa,UAAU;AACvB;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,MAAM;;;AAGN;AACA;AACA;AACA;AACA,MAAM;;;AAGN;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,SAAS;AACpB,YAAY,SAAS;AACrB;AACA;;;AAGA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,cAAc;AACzB,WAAW,GAAG;AACd;;;AAGA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA,6DAA6D;AAC7D;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,WAAW;AACtB,WAAW,GAAG;AACd;;;AAGA;AACA;AACA;AACA;AACA;;AAEA;AACA,sBAAsB,iBAAiB;AACvC;;AAEA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,MAAM;AACN;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,cAAc;AACzB;;;AAGA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,MAAM;AACN;AACA;;AAEA;AACA;AACA;AACA;AACA,MAAM;AACN,4CAA4C;;AAE5C;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,cAAc;AACzB;;;AAGA;AACA;AACA;;AAEA,oBAAoB,iBAAiB;AACrC;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,8CAA8C;AAC9C;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,QAAQ;AACR;AACA;;AAEA;;AAEA;AACA;AACA,QAAQ;AACR;AACA,QAAQ;AACR;AACA;AACA,QAAQ;AACR;AACA;;AAEA;AACA;;AAEA,0DAA0D;AAC1D;;AAEA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;;AAGA;AACA;;AAEA;AACA;AACA;AACA,4BAA4B,qBAAqB;AACjD;AACA;;AAEA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,gDAAgD,gDAAgD,MAAM,aAAa;;AAEnH;AACA,iDAAiD,kCAAkC,OAAO;;AAE1F,yGAAyG,cAAc,UAAU,gGAAgG,kBAAkB,UAAU,UAAU;;AAEvQ;AACA;AACA;AACA;;AAEA;AACA;AACA,MAAM;AACN;AACA;;AAEA;AACA;AACA,EAAE;AACF;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,sCAAsC;AACtC;;AAEA;;AAEA,gBAAgB;AAChB,WAAW;AACX,YAAY;AACZ,GAAG;AACH;;;;;;;;;;;;ACpzCa;;AAEb,IAAI,KAAqC,EAAE,EAE1C,CAAC;AACF,EAAE,+IAAkE;AACpE;;;;;;;;;;;;ACNA;;;;;;;;;;;ACAA;;;;;;;;;;;ACAA;;;;;;;;;;;ACAA;;;;;;;;;;;;;;;;;;ACAA;AAC+C;AACrB;AACS;AACnC;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,oCAAoC,8DAAS,oBAAoB,SAAS,8DAAS,GAAG,EAAE,8DAAS;AACjG;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,MAAM,IAAqC;AAC3C;AACA;AACA;AACA;AACA;AACA,wCAAwC,YAAY,sBAAsB,cAAc;AACxF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,KAA+B,EAAE,EAKpC;AACH;AACA,QAAQ,IAAwE;AAChF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,sDAAsD;AACpE;AACA;AACA;AACA;AACA;AACA;AACA,iDAAiD,iDAAE,wDAAwD,iDAAE;AAC7G,cAAc,OAAO;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,KAAK,QAAQ,MAAM,EAAE,KAAK;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA,eAAe,gDAAmB;AAClC;AACA,aAAa,gDAAmB;AAChC;AACA,mBAAmB,6CAAgB,GAAG,6CAAgB;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,KAAqC;AAC1D;AACA;AACA;AACA,CAAC,IAAI,CAAM;AAGT;AACF;;;;;;;;;;;;;;;;AC7GA;AACA;AACA;AACA,MAAM,KAA+B,EAAE,EAEpC;AACH;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAIE;AACF;;;;;;UC1CA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;WCtBA;WACA;WACA;WACA;WACA;WACA,iCAAiC,WAAW;WAC5C;WACA;;;;;WCPA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA;;;;;WCPA;WACA;WACA;WACA;WACA,GAAG;WACH;WACA;WACA,CAAC;;;;;WCPD;;;;;WCAA;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D;;;;;;;;;;;;;;ACNmD;AACK;AACxDhC,+DAAc,CAAClF,+DAAe,CAAC,C", "sources": ["webpack://leadin/./node_modules/@emotion/memoize/dist/emotion-memoize.esm.js", "webpack://leadin/./node_modules/@linaria/react/node_modules/@emotion/is-prop-valid/dist/emotion-is-prop-valid.esm.js", "webpack://leadin/./scripts/api/wordpressApiClient.ts", "webpack://leadin/./scripts/constants/leadinConfig.ts", "webpack://leadin/./scripts/constants/selectors.ts", "webpack://leadin/./scripts/iframe/IframeErrorPage.tsx", "webpack://leadin/./scripts/iframe/constants.ts", "webpack://leadin/./scripts/iframe/integratedMessages/core/CoreMessages.ts", "webpack://leadin/./scripts/iframe/integratedMessages/forms/FormsMessages.ts", "webpack://leadin/./scripts/iframe/integratedMessages/index.ts", "webpack://leadin/./scripts/iframe/integratedMessages/livechat/LiveChatMessages.ts", "webpack://leadin/./scripts/iframe/integratedMessages/plugin/PluginMessages.ts", "webpack://leadin/./scripts/iframe/integratedMessages/proxy/ProxyMessages.ts", "webpack://leadin/./scripts/iframe/messageMiddleware.ts", "webpack://leadin/./scripts/iframe/renderIframeApp.tsx", "webpack://leadin/./scripts/iframe/useAppEmbedder.ts", "webpack://leadin/./scripts/lib/Raven.ts", "webpack://leadin/./scripts/utils/appUtils.ts", "webpack://leadin/./scripts/utils/contentEmbedInstaller.ts", "webpack://leadin/./scripts/utils/iframe.ts", "webpack://leadin/./scripts/utils/queryParams.ts", "webpack://leadin/./scripts/iframe/IframeErrorPage.tsx?1b3e", "webpack://leadin/./node_modules/raven-js/src/configError.js", "webpack://leadin/./node_modules/raven-js/src/console.js", "webpack://leadin/./node_modules/raven-js/src/raven.js", "webpack://leadin/./node_modules/raven-js/src/singleton.js", "webpack://leadin/./node_modules/raven-js/src/utils.js", "webpack://leadin/./node_modules/raven-js/vendor/TraceKit/tracekit.js", "webpack://leadin/./node_modules/raven-js/vendor/json-stringify-safe/stringify.js", "webpack://leadin/./node_modules/react/cjs/react-jsx-runtime.development.js", "webpack://leadin/./node_modules/react/jsx-runtime.js", "webpack://leadin/external window \"React\"", "webpack://leadin/external window \"ReactDOM\"", "webpack://leadin/external window \"jQuery\"", "webpack://leadin/external window [\"wp\",\"i18n\"]", "webpack://leadin/./node_modules/@linaria/react/dist/index.mjs", "webpack://leadin/./node_modules/@linaria/react/node_modules/@linaria/core/dist/index.mjs", "webpack://leadin/webpack/bootstrap", "webpack://leadin/webpack/runtime/compat get default export", "webpack://leadin/webpack/runtime/define property getters", "webpack://leadin/webpack/runtime/global", "webpack://leadin/webpack/runtime/hasOwnProperty shorthand", "webpack://leadin/webpack/runtime/make namespace object", "webpack://leadin/./scripts/entries/app.ts"], "sourcesContent": ["function memoize(fn) {\n  var cache = Object.create(null);\n  return function (arg) {\n    if (cache[arg] === undefined) cache[arg] = fn(arg);\n    return cache[arg];\n  };\n}\n\nexport { memoize as default };\n", "import memoize from '@emotion/memoize';\n\n// eslint-disable-next-line no-undef\nvar reactPropsRegex = /^((children|dangerouslySetInnerHTML|key|ref|autoFocus|defaultValue|defaultChecked|innerHTML|suppressContentEditableWarning|suppressHydrationWarning|valueLink|abbr|accept|acceptCharset|accessKey|action|allow|allowUserMedia|allowPaymentRequest|allowFullScreen|allowTransparency|alt|async|autoComplete|autoPlay|capture|cellPadding|cellSpacing|challenge|charSet|checked|cite|classID|className|cols|colSpan|content|contentEditable|contextMenu|controls|controlsList|coords|crossOrigin|data|dateTime|decoding|default|defer|dir|disabled|disablePictureInPicture|disableRemotePlayback|download|draggable|encType|enterKeyHint|form|formAction|formEncType|formMethod|formNoValidate|formTarget|frameBorder|headers|height|hidden|high|href|hrefLang|htmlFor|httpEquiv|id|inputMode|integrity|is|keyParams|keyType|kind|label|lang|list|loading|loop|low|marginHeight|marginWidth|max|maxLength|media|mediaGroup|method|min|minLength|multiple|muted|name|nonce|noValidate|open|optimum|pattern|placeholder|playsInline|poster|preload|profile|radioGroup|readOnly|referrerPolicy|rel|required|reversed|role|rows|rowSpan|sandbox|scope|scoped|scrolling|seamless|selected|shape|size|sizes|slot|span|spellCheck|src|srcDoc|srcLang|srcSet|start|step|style|summary|tabIndex|target|title|translate|type|useMap|value|width|wmode|wrap|about|datatype|inlist|prefix|property|resource|typeof|vocab|autoCapitalize|autoCorrect|autoSave|color|incremental|fallback|inert|itemProp|itemScope|itemType|itemID|itemRef|on|option|results|security|unselectable|accentHeight|accumulate|additive|alignmentBaseline|allowReorder|alphabetic|amplitude|arabicForm|ascent|attributeName|attributeType|autoReverse|azimuth|baseFrequency|baselineShift|baseProfile|bbox|begin|bias|by|calcMode|capHeight|clip|clipPathUnits|clipPath|clipRule|colorInterpolation|colorInterpolationFilters|colorProfile|colorRendering|contentScriptType|contentStyleType|cursor|cx|cy|d|decelerate|descent|diffuseConstant|direction|display|divisor|dominantBaseline|dur|dx|dy|edgeMode|elevation|enableBackground|end|exponent|externalResourcesRequired|fill|fillOpacity|fillRule|filter|filterRes|filterUnits|floodColor|floodOpacity|focusable|fontFamily|fontSize|fontSizeAdjust|fontStretch|fontStyle|fontVariant|fontWeight|format|from|fr|fx|fy|g1|g2|glyphName|glyphOrientationHorizontal|glyphOrientationVertical|glyphRef|gradientTransform|gradientUnits|hanging|horizAdvX|horizOriginX|ideographic|imageRendering|in|in2|intercept|k|k1|k2|k3|k4|kernelMatrix|kernelUnitLength|kerning|keyPoints|keySplines|keyTimes|lengthAdjust|letterSpacing|lightingColor|limitingConeAngle|local|markerEnd|markerMid|markerStart|markerHeight|markerUnits|markerWidth|mask|maskContentUnits|maskUnits|mathematical|mode|numOctaves|offset|opacity|operator|order|orient|orientation|origin|overflow|overlinePosition|overlineThickness|panose1|paintOrder|pathLength|patternContentUnits|patternTransform|patternUnits|pointerEvents|points|pointsAtX|pointsAtY|pointsAtZ|preserveAlpha|preserveAspectRatio|primitiveUnits|r|radius|refX|refY|renderingIntent|repeatCount|repeatDur|requiredExtensions|requiredFeatures|restart|result|rotate|rx|ry|scale|seed|shapeRendering|slope|spacing|specularConstant|specularExponent|speed|spreadMethod|startOffset|stdDeviation|stemh|stemv|stitchTiles|stopColor|stopOpacity|strikethroughPosition|strikethroughThickness|string|stroke|strokeDasharray|strokeDashoffset|strokeLinecap|strokeLinejoin|strokeMiterlimit|strokeOpacity|strokeWidth|surfaceScale|systemLanguage|tableValues|targetX|targetY|textAnchor|textDecoration|textRendering|textLength|to|transform|u1|u2|underlinePosition|underlineThickness|unicode|unicodeBidi|unicodeRange|unitsPerEm|vAlphabetic|vHanging|vIdeographic|vMathematical|values|vectorEffect|version|vertAdvY|vertOriginX|vertOriginY|viewBox|viewTarget|visibility|widths|wordSpacing|writingMode|x|xHeight|x1|x2|xChannelSelector|xlinkActuate|xlinkArcrole|xlinkHref|xlinkRole|xlinkShow|xlinkTitle|xlinkType|xmlBase|xmlns|xmlnsXlink|xmlLang|xmlSpace|y|y1|y2|yChannelSelector|z|zoomAndPan|for|class|autofocus)|(([Dd][Aa][Tt][Aa]|[Aa][Rr][Ii][Aa]|x)-.*))$/; // https://esbench.com/bench/5bfee68a4cd7e6009ef61d23\n\nvar isPropValid = /* #__PURE__ */memoize(function (prop) {\n  return reactPropsRegex.test(prop) || prop.charCodeAt(0) === 111\n  /* o */\n  && prop.charCodeAt(1) === 110\n  /* n */\n  && prop.charCodeAt(2) < 91;\n}\n/* Z+1 */\n);\n\nexport { isPropValid as default };\n", "import $ from 'jquery';\nimport <PERSON> from '../lib/Raven';\nimport { restNonce, restUrl } from '../constants/leadinConfig';\nimport { addQueryObjectToUrl } from '../utils/queryParams';\nfunction makeRequest(method, path, data = {}, queryParams = {}) {\n    // eslint-disable-next-line compat/compat\n    const restApiUrl = new URL(`${restUrl}leadin/v1${path}`);\n    addQueryObjectToUrl(restApiUrl, queryParams);\n    return new Promise((resolve, reject) => {\n        const payload = {\n            url: restApiUrl.toString(),\n            method,\n            contentType: 'application/json',\n            beforeSend: (xhr) => xhr.setRequestHeader('X-WP-Nonce', restNonce),\n            success: resolve,\n            error: (response) => {\n                Raven.captureMessage(`HTTP Request to ${restApiUrl} failed with error ${response.status}: ${response.responseText}`, {\n                    fingerprint: [\n                        '{{ default }}',\n                        path,\n                        response.status,\n                        response.responseText,\n                    ],\n                });\n                reject(response);\n            },\n        };\n        if (method !== 'get') {\n            payload.data = JSON.stringify(data);\n        }\n        $.ajax(payload);\n    });\n}\nexport function healthcheckRestApi() {\n    return makeRequest('get', '/healthcheck');\n}\nexport function disableInternalTracking(value) {\n    return makeRequest('put', '/internal-tracking', value ? '1' : '0');\n}\nexport function fetchDisableInternalTracking() {\n    return makeRequest('get', '/internal-tracking').then(message => ({\n        message,\n    }));\n}\nexport function updateHublet(hublet) {\n    return makeRequest('put', '/hublet', { hublet });\n}\nexport function skipReview() {\n    return makeRequest('post', '/skip-review');\n}\nexport function trackConsent(canTrack) {\n    return makeRequest('post', '/track-consent', { canTrack }).then(message => ({\n        message,\n    }));\n}\nexport function setBusinessUnitId(businessUnitId) {\n    return makeRequest('put', '/business-unit', { businessUnitId });\n}\nexport function getBusinessUnitId() {\n    return makeRequest('get', '/business-unit');\n}\nexport function refreshProxyMappingsCache() {\n    return makeRequest('post', '/wp-mappings-cache-reset');\n}\nexport function fetchProxyMappingsEnabled() {\n    return makeRequest('get', '/wp-mappings-proxy-enabled');\n}\nexport function toggleProxyMappingsEnabled(value) {\n    return makeRequest('put', '/wp-mappings-proxy-enabled', value);\n}\n", "const { accountName, adminUrl, activationTime, connectionStatus, deviceId, didDisconnect, env, formsScript, meetingsScript, formsScriptPayload, hublet, hubspotBaseUrl, hubspotNonce, iframeUrl, impactLink, lastAuthorizeTime, lastDeauthorizeTime, lastDisconnectTime, leadinPluginVersion, leadinQueryParams, locale, loginUrl, phpVersion, pluginPath, plugins, portalDomain, portalEmail, portalId, redirectNonce, restNonce, restUrl, refreshToken, reviewSkippedDate, theme, trackConsent, wpVersion, contentEmbed, requiresContentEmbedScope, decryptError, } = window.leadinConfig;\nexport { accountName, adminUrl, activationTime, connectionStatus, deviceId, didDisconnect, env, formsScript, meetingsScript, formsScriptPayload, hublet, hubspotBaseUrl, hubspotNonce, iframeUrl, impactLink, lastAuthorizeTime, lastDeauthorizeTime, lastDisconnectTime, leadinPluginVersion, leadinQueryParams, loginUrl, locale, phpVersion, pluginPath, plugins, portalDomain, portalEmail, portalId, redirectNonce, restNonce, restUrl, refreshToken, reviewSkippedDate, theme, trackConsent, wpVersion, contentEmbed, requiresContentEmbedScope, decryptError, };\n", "export const domElements = {\n    iframe: '#leadin-iframe',\n    subMenu: '.toplevel_page_leadin > ul',\n    subMenuLinks: '.toplevel_page_leadin > ul a',\n    subMenuButtons: '.toplevel_page_leadin > ul > li',\n    deactivatePluginButton: '[data-slug=\"leadin\"] .deactivate a',\n    deactivateFeedbackForm: 'form.leadin-deactivate-form',\n    deactivateFeedbackSubmit: 'button#leadin-feedback-submit',\n    deactivateFeedbackSkip: 'button#leadin-feedback-skip',\n    thickboxModalClose: '.leadin-modal-close',\n    thickboxModalWindow: 'div#TB_window.thickbox-loading',\n    thickboxModalContent: 'div#TB_ajaxContent.TB_modal',\n    reviewBannerContainer: '#leadin-review-banner',\n    reviewBannerLeaveReviewLink: 'a#leave-review-button',\n    reviewBannerDismissButton: 'a#dismiss-review-banner-button',\n    leadinIframeContainer: 'leadin-iframe-container',\n    leadinIframe: 'leadin-iframe',\n    leadinIframeFallbackContainer: 'leadin-iframe-fallback-container',\n};\n", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport React from 'react';\nimport { __ } from '@wordpress/i18n';\nimport { styled } from '@linaria/react';\nconst IframeErrorContainer = styled.div `\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  margin-top: 120px;\n  font-family: 'Lexend Deca', Helvetica, Arial, sans-serif;\n  font-weight: 400;\n  font-size: 14px;\n  font-size: 0.875rem;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  font-smoothing: antialiased;\n  line-height: 1.5rem;\n`;\nconst ErrorHeader = styled.h1 `\n  text-shadow: 0 0 1px transparent;\n  margin-bottom: 1.25rem;\n  color: #33475b;\n  font-size: 1.25rem;\n`;\nexport const IframeErrorPage = () => (_jsxs(IframeErrorContainer, { children: [_jsx(\"img\", { alt: \"Cannot find page\", width: \"175\", src: \"//static.hsappstatic.net/ui-images/static-1.14/optimized/errors/map.svg\" }), _jsx(ErrorHeader, { children: __('The HubSpot for WordPress plugin is not able to load pages', 'leadin') }), _jsx(\"p\", { children: __('Try disabling your browser extensions and ad blockers, then refresh the page', 'leadin') }), _jsx(\"p\", { children: __('Or open the HubSpot for WordPress plugin in a different browser', 'leadin') })] }));\n", "export var App;\n(function (App) {\n    App[App[\"Forms\"] = 0] = \"Forms\";\n    App[App[\"LiveChat\"] = 1] = \"LiveChat\";\n    App[App[\"Plugin\"] = 2] = \"Plugin\";\n    App[App[\"PluginSettings\"] = 3] = \"PluginSettings\";\n    App[App[\"Background\"] = 4] = \"Background\";\n})(App || (App = {}));\nexport const AppIframe = {\n    [App.Forms]: 'integrated-form-app',\n    [App.LiveChat]: 'integrated-livechat-app',\n    [App.Plugin]: 'integrated-plugin-app',\n    [App.PluginSettings]: 'integrated-plugin-app',\n    [App.Background]: 'integrated-plugin-proxy',\n};\n", "export const CoreMessages = {\n    HandshakeReceive: 'INTEGRATED_APP_EMBEDDER_HANDSHAKE_RECEIVED',\n    SendRefreshToken: 'INTEGRATED_APP_EMBEDDER_SEND_REFRESH_TOKEN',\n    ReloadParentFrame: 'INTEGRATED_APP_EMBEDDER_RELOAD_PARENT_FRAME',\n    RedirectParentFrame: 'INTEGRATED_APP_EMBEDDER_REDIRECT_PARENT_FRAME',\n    SendLocale: 'INTEGRATED_APP_EMBEDDER_SEND_LOCALE',\n    SendDeviceId: 'INTEGRATED_APP_EMBEDDER_SEND_DEVICE_ID',\n    SendIntegratedAppConfig: 'INTEGRATED_APP_EMBEDDER_CONFIG',\n};\n", "export const FormMessages = {\n    CreateFormAppNavigation: 'CREATE_FORM_APP_NAVIGATION',\n};\n", "export * from './core/CoreMessages';\nexport * from './forms/FormsMessages';\nexport * from './livechat/LiveChatMessages';\nexport * from './plugin/PluginMessages';\nexport * from './proxy/ProxyMessages';\n", "export const LiveChatMessages = {\n    CreateLiveChatAppNavigation: 'CREATE_LIVE_CHAT_APP_NAVIGATION',\n};\n", "export const PluginMessages = {\n    PluginSettingsNavigation: 'PLUGIN_SETTINGS_NAVIGATION',\n    PluginLeadinConfig: 'PLUGIN_LEADIN_CONFIG',\n    TrackConsent: 'INTEGRATED_APP_EMBEDDER_TRACK_CONSENT',\n    InternalTrackingFetchRequest: 'INTEGRATED_TRACKING_FETCH_REQUEST',\n    InternalTrackingFetchResponse: 'INTEGRATED_TRACKING_FETCH_RESPONSE',\n    InternalTrackingFetchError: 'INTEGRATED_TRACKING_FETCH_ERROR',\n    InternalTrackingChangeRequest: 'INTEGRATED_TRACKING_CHANGE_REQUEST',\n    InternalTrackingChangeError: 'INTEGRATED_TRACKING_CHANGE_ERROR',\n    BusinessUnitFetchRequest: 'BUSINESS_UNIT_FETCH_REQUEST',\n    BusinessUnitFetchResponse: 'BUSINESS_UNIT_FETCH_FETCH_RESPONSE',\n    BusinessUnitFetchError: 'BUSINESS_UNIT_FETCH_FETCH_ERROR',\n    BusinessUnitChangeRequest: 'BUSINESS_UNIT_CHANGE_REQUEST',\n    BusinessUnitChangeError: 'BUSINESS_UNIT_CHANGE_ERROR',\n    SkipReviewRequest: 'SKIP_REVIEW_REQUEST',\n    SkipReviewResponse: 'SKIP_REVIEW_RESPONSE',\n    SkipReviewError: 'SKIP_REVIEW_ERROR',\n    RemoveParentQueryParam: 'REMOVE_PARENT_QUERY_PARAM',\n    ContentEmbedInstallRequest: 'CONTENT_EMBED_INSTALL_REQUEST',\n    ContentEmbedInstallResponse: 'CONTENT_EMBED_INSTALL_RESPONSE',\n    ContentEmbedInstallError: 'CONTENT_EMBED_INSTALL_ERROR',\n    ContentEmbedActivationRequest: 'CONTENT_EMBED_ACTIVATION_REQUEST',\n    ContentEmbedActivationResponse: 'CONTENT_EMBED_ACTIVATION_RESPONSE',\n    ContentEmbedActivationError: 'CONTENT_EMBED_ACTIVATION_ERROR',\n    ProxyMappingsEnabledRequest: 'PROXY_MAPPINGS_ENABLED_REQUEST',\n    ProxyMappingsEnabledResponse: 'PROXY_MAPPINGS_ENABLED_RESPONSE',\n    ProxyMappingsEnabledError: 'PROXY_MAPPINGS_ENABLED_ERROR',\n    ProxyMappingsEnabledChangeRequest: 'PROXY_MAPPINGS_ENABLED_CHANGE_REQUEST',\n    ProxyMappingsEnabledChangeError: 'PROXY_MAPPINGS_ENABLED_CHANGE_ERROR',\n    RefreshProxyMappingsRequest: 'REFRESH_PROXY_MAPPINGS_REQUEST',\n    RefreshProxyMappingsResponse: 'REFRESH_PROXY_MAPPINGS_RESPONSE',\n    RefreshProxyMappingsError: 'REFRESH_PROXY_MAPPINGS_ERROR',\n};\n", "export const ProxyMessages = {\n    FetchForms: 'FETCH_FORMS',\n    FetchForm: 'FETCH_FORM',\n    CreateFormFromTemplate: 'CREATE_FORM_FROM_TEMPLATE',\n    GetTemplateAvailability: 'GET_TEMPLATE_AVAILABILITY',\n    FetchAuth: 'FETCH_AUTH',\n    FetchMeetingsAndUsers: 'FETCH_MEETINGS_AND_USERS',\n    FetchContactsCreateSinceActivation: 'FETCH_CONTACTS_CREATED_SINCE_ACTIVATION',\n    FetchOrCreateMeetingUser: 'FETCH_OR_CREATE_MEETING_USER',\n    ConnectMeetingsCalendar: 'CONNECT_MEETINGS_CALENDAR',\n    TrackFormPreviewRender: 'TRACK_FORM_PREVIEW_RENDER',\n    TrackFormCreatedFromTemplate: 'TRACK_FORM_CREATED_FROM_TEMPLATE',\n    TrackFormCreationFailed: 'TRACK_FORM_CREATION_FAILED',\n    TrackMeetingPreviewRender: 'TRACK_MEETING_PREVIEW_RENDER',\n    TrackSidebarMetaChange: 'TRACK_SIDEBAR_META_CHANGE',\n    TrackReviewBannerRender: 'TRACK_REVIEW_BANNER_RENDER',\n    TrackReviewBannerInteraction: 'TRACK_REVIEW_BANNER_INTERACTION',\n    TrackReviewBannerDismissed: 'TRACK_REVIEW_BANNER_DISMISSED',\n    TrackPluginDeactivation: 'TRACK_PLUGIN_DEACTIVATION',\n};\n", "import { PluginMessages } from './integratedMessages';\nimport { fetchDisableInternalTracking, trackConsent, disableInternalTracking, getBusinessUnitId, setBusinessUnitId, skipReview, refreshProxyMappingsCache, fetchProxyMappingsEnabled, toggleProxyMappingsEnabled, } from '../api/wordpressApiClient';\nimport { removeQueryParamFromLocation } from '../utils/queryParams';\nimport { startActivation, startInstall } from '../utils/contentEmbedInstaller';\nconst messageMapper = new Map([\n    [\n        PluginMessages.TrackConsent,\n        (message) => {\n            trackConsent(message.payload);\n        },\n    ],\n    [\n        PluginMessages.InternalTrackingChangeRequest,\n        (message, embedder) => {\n            disableInternalTracking(message.payload)\n                .then(() => {\n                embedder.postMessage({\n                    key: PluginMessages.InternalTrackingFetchResponse,\n                    payload: message.payload,\n                });\n            })\n                .catch(payload => {\n                embedder.postMessage({\n                    key: PluginMessages.InternalTrackingChangeError,\n                    payload,\n                });\n            });\n        },\n    ],\n    [\n        PluginMessages.InternalTrackingFetchRequest,\n        (__message, embedder) => {\n            fetchDisableInternalTracking()\n                .then(({ message: payload }) => {\n                embedder.postMessage({\n                    key: PluginMessages.InternalTrackingFetchResponse,\n                    payload,\n                });\n            })\n                .catch(payload => {\n                embedder.postMessage({\n                    key: PluginMessages.InternalTrackingFetchError,\n                    payload,\n                });\n            });\n        },\n    ],\n    [\n        PluginMessages.BusinessUnitFetchRequest,\n        (__message, embedder) => {\n            getBusinessUnitId()\n                .then(payload => {\n                embedder.postMessage({\n                    key: PluginMessages.BusinessUnitFetchResponse,\n                    payload,\n                });\n            })\n                .catch(payload => {\n                embedder.postMessage({\n                    key: PluginMessages.BusinessUnitFetchError,\n                    payload,\n                });\n            });\n        },\n    ],\n    [\n        PluginMessages.BusinessUnitChangeRequest,\n        (message, embedder) => {\n            setBusinessUnitId(message.payload)\n                .then(payload => {\n                embedder.postMessage({\n                    key: PluginMessages.BusinessUnitFetchResponse,\n                    payload,\n                });\n            })\n                .catch(payload => {\n                embedder.postMessage({\n                    key: PluginMessages.BusinessUnitChangeError,\n                    payload,\n                });\n            });\n        },\n    ],\n    [\n        PluginMessages.SkipReviewRequest,\n        (__message, embedder) => {\n            skipReview()\n                .then(payload => {\n                embedder.postMessage({\n                    key: PluginMessages.SkipReviewResponse,\n                    payload,\n                });\n            })\n                .catch(payload => {\n                embedder.postMessage({\n                    key: PluginMessages.SkipReviewError,\n                    payload,\n                });\n            });\n        },\n    ],\n    [\n        PluginMessages.RemoveParentQueryParam,\n        (message) => {\n            removeQueryParamFromLocation(message.payload);\n        },\n    ],\n    [\n        PluginMessages.ContentEmbedInstallRequest,\n        (message, embedder) => {\n            startInstall(message.payload.nonce)\n                .then(payload => {\n                embedder.postMessage({\n                    key: PluginMessages.ContentEmbedInstallResponse,\n                    payload: payload,\n                });\n            })\n                .catch(payload => {\n                embedder.postMessage({\n                    key: PluginMessages.ContentEmbedInstallError,\n                    payload,\n                });\n            });\n        },\n    ],\n    [\n        PluginMessages.ContentEmbedActivationRequest,\n        (message, embedder) => {\n            startActivation(message.payload.activateAjaxUrl)\n                .then(payload => {\n                embedder.postMessage({\n                    key: PluginMessages.ContentEmbedActivationResponse,\n                    payload: payload,\n                });\n            })\n                .catch(payload => {\n                embedder.postMessage({\n                    key: PluginMessages.ContentEmbedActivationError,\n                    payload,\n                });\n            });\n        },\n    ],\n    [\n        PluginMessages.RefreshProxyMappingsRequest,\n        (__message, embedder) => {\n            refreshProxyMappingsCache()\n                .then(() => {\n                embedder.postMessage({\n                    key: PluginMessages.RefreshProxyMappingsResponse,\n                    payload: {},\n                });\n            })\n                .catch(payload => {\n                embedder.postMessage({\n                    key: PluginMessages.RefreshProxyMappingsError,\n                    payload,\n                });\n            });\n        },\n    ],\n    [\n        PluginMessages.ProxyMappingsEnabledRequest,\n        (__message, embedder) => {\n            fetchProxyMappingsEnabled()\n                .then(payload => {\n                embedder.postMessage({\n                    key: PluginMessages.ProxyMappingsEnabledResponse,\n                    payload,\n                });\n            })\n                .catch(payload => {\n                embedder.postMessage({\n                    key: PluginMessages.ProxyMappingsEnabledError,\n                    payload,\n                });\n            });\n        },\n    ],\n    [\n        PluginMessages.ProxyMappingsEnabledChangeRequest,\n        ({ payload }, embedder) => {\n            toggleProxyMappingsEnabled(payload)\n                .then(() => {\n                embedder.postMessage({\n                    key: PluginMessages.ProxyMappingsEnabledResponse,\n                    payload,\n                });\n            })\n                .catch(payload => {\n                embedder.postMessage({\n                    key: PluginMessages.ProxyMappingsEnabledChangeError,\n                    payload,\n                });\n            });\n        },\n    ],\n]);\nexport const messageMiddleware = (embedder) => (message) => {\n    const next = messageMapper.get(message.key);\n    if (next) {\n        next(message, embedder);\n    }\n};\n", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport React, { Fragment } from 'react';\nimport ReactDOM from 'react-dom';\nimport { domElements } from '../constants/selectors';\nimport useAppEmbedder from './useAppEmbedder';\nimport { App } from './constants';\nimport { IframeErrorPage } from './IframeErrorPage';\nconst IntegratedIframePortal = (props) => {\n    const container = document.getElementById(domElements.leadinIframeContainer);\n    const iframeNotRendered = useAppEmbedder(props.app, props.createRoute, container);\n    if (container && !iframeNotRendered) {\n        return ReactDOM.createPortal(props.children, container);\n    }\n    return (_jsx(Fragment, { children: (!container || iframeNotRendered) && _jsx(IframeErrorPage, {}) }));\n};\nconst renderIframeApp = () => {\n    const iframeFallbackContainer = document.getElementById(domElements.leadinIframeContainer);\n    let app;\n    const queryParams = new URLSearchParams(location.search);\n    const page = queryParams.get('page');\n    const createRoute = queryParams.get('leadin_route[0]') === 'create';\n    switch (page) {\n        case 'leadin_forms':\n            app = App.Forms;\n            break;\n        case 'leadin_chatflows':\n            app = App.LiveChat;\n            break;\n        case 'leadin_settings':\n            app = App.PluginSettings;\n            break;\n        case 'leadin_user_guide':\n        default:\n            app = App.Plugin;\n            break;\n    }\n    ReactDOM.render(_jsx(IntegratedIframePortal, { app: app, createRoute: createRoute }), iframeFallbackContainer);\n};\nexport default renderIframeApp;\n", "import { useEffect } from 'react';\nimport <PERSON> from '../lib/Raven';\nimport { accountName, adminUrl, connectionStatus, deviceId, hubspotBaseUrl, leadinQueryParams, locale, plugins, portalDomain, portalEmail, portalId, reviewSkippedDate, refreshToken, impactLink, theme, lastAuthorizeTime, lastDeauthorizeTime, lastDisconnectTime, leadinPluginVersion, phpVersion, wpVersion, contentEmbed, requiresContentEmbedScope, decryptError, } from '../constants/leadinConfig';\nimport { App, AppIframe } from './constants';\nimport { messageMiddleware } from './messageMiddleware';\nimport { resizeWindow, useIframeNotRendered } from '../utils/iframe';\nconst getIntegrationConfig = () => {\n    return {\n        adminUrl: leadinQueryParams.adminUrl,\n    };\n};\nconst getLeadinConfig = () => {\n    const utm_query_params = Object.keys(leadinQueryParams)\n        .filter(x => /^utm/.test(x))\n        .reduce((p, c) => ({\n        [c]: leadinQueryParams[c],\n        ...p,\n    }), {});\n    return {\n        accountName,\n        admin: leadinQueryParams.admin,\n        adminUrl,\n        company: leadinQueryParams.company,\n        connectionStatus,\n        deviceId,\n        email: leadinQueryParams.email,\n        firstName: leadinQueryParams.firstName,\n        irclickid: leadinQueryParams.irclickid,\n        justConnected: leadinQueryParams.justConnected,\n        lastName: leadinQueryParams.lastName,\n        lastAuthorizeTime,\n        lastDeauthorizeTime,\n        lastDisconnectTime,\n        leadinPluginVersion,\n        mpid: leadinQueryParams.mpid,\n        nonce: leadinQueryParams.nonce,\n        phpVersion,\n        plugins,\n        portalDomain,\n        portalEmail,\n        portalId,\n        reviewSkippedDate,\n        theme,\n        trackConsent: leadinQueryParams.trackConsent,\n        websiteName: leadinQueryParams.websiteName,\n        wpVersion,\n        contentEmbed,\n        requiresContentEmbedScope,\n        decryptError,\n        ...utm_query_params,\n    };\n};\nconst getAppOptions = (app, createRoute = false) => {\n    const { IntegratedAppOptions, FormsAppOptions, LiveChatAppOptions, PluginAppOptions, } = window;\n    let options;\n    switch (app) {\n        case App.Plugin:\n            options = new PluginAppOptions();\n            break;\n        case App.PluginSettings:\n            options = new PluginAppOptions().setPluginSettingsInit();\n            break;\n        case App.Forms:\n            options = new FormsAppOptions().setIntegratedAppConfig(getIntegrationConfig());\n            if (createRoute) {\n                options = options.setCreateFormAppInit();\n            }\n            break;\n        case App.LiveChat:\n            options = new LiveChatAppOptions();\n            if (createRoute) {\n                options = options.setCreateLiveChatAppInit();\n            }\n            break;\n        default:\n            options = new IntegratedAppOptions();\n    }\n    return options;\n};\nexport default function useAppEmbedder(app, createRoute, container) {\n    console.info('HubSpot plugin - starting app embedder for:', AppIframe[app], container);\n    const iframeNotRendered = useIframeNotRendered(AppIframe[app]);\n    useEffect(() => {\n        const { IntegratedAppEmbedder } = window;\n        if (IntegratedAppEmbedder) {\n            const options = getAppOptions(app, createRoute)\n                .setLocale(locale)\n                .setDeviceId(deviceId)\n                .setRefreshToken(refreshToken)\n                .setLeadinConfig(getLeadinConfig());\n            const embedder = new IntegratedAppEmbedder(AppIframe[app], portalId, hubspotBaseUrl, resizeWindow, refreshToken ? '' : impactLink).setOptions(options);\n            embedder.subscribe(messageMiddleware(embedder));\n            embedder.attachTo(container, true);\n            embedder.postStartAppMessage(); // lets the app know all all data has been passed to it\n            window.embedder = embedder;\n        }\n    }, []);\n    if (iframeNotRendered) {\n        console.error('HubSpot plugin Iframe not rendered', {\n            portalId,\n            container,\n            appName: AppIframe[app],\n            hasIntegratedAppEmbedder: !!window.IntegratedAppEmbedder,\n        });\n        Raven.captureException(new Error('Leadin Iframe not rendered'), {\n            fingerprint: ['USE_APP_EMBEDDER', 'IFRAME_SETUP_ERROR'],\n            extra: {\n                portalId,\n                container,\n                app,\n                hubspotBaseUrl,\n                impactLink,\n                appName: AppIframe[app],\n                hasRefreshToken: !!refreshToken,\n            },\n        });\n    }\n    return iframeNotRendered;\n}\n", "import Raven from 'raven-js';\nimport { hubspotBaseUrl, phpVersion, wpVersion, leadinPluginVersion, portalId, plugins, } from '../constants/leadinConfig';\nexport function configureRaven() {\n    if (hubspotBaseUrl.indexOf('local') !== -1) {\n        return;\n    }\n    const domain = hubspotBaseUrl.replace(/https?:\\/\\/app/, '');\n    Raven.config(`https://a9f08e536ef66abb0bf90becc905b09e@exceptions${domain}/v2/1`, {\n        instrument: {\n            tryCatch: false,\n        },\n        shouldSendCallback(data) {\n            return (!!data && !!data.culprit && /plugins\\/leadin\\//.test(data.culprit));\n        },\n        release: leadinPluginVersion,\n    }).install();\n    Raven.setTagsContext({\n        v: leadinPluginVersion,\n        php: phpVersion,\n        wordpress: wpVersion,\n    });\n    Raven.setExtraContext({\n        hub: portalId,\n        plugins: Object.keys(plugins)\n            .map(name => `${name}#${plugins[name]}`)\n            .join(','),\n    });\n}\nexport default Raven;\n", "import $ from 'j<PERSON>y';\nimport Raven, { configureRaven } from '../lib/Raven';\nexport function initApp(initFn) {\n    configureRaven();\n    Raven.context(initFn);\n}\nexport function initAppOnReady(initFn) {\n    function main() {\n        $(initFn);\n    }\n    initApp(main);\n}\n", "export function startInstall(nonce) {\n    const formData = new FormData();\n    const ajaxUrl = window.ajaxurl;\n    formData.append('_wpnonce', nonce);\n    formData.append('action', 'content_embed_install');\n    return fetch(ajaxUrl, {\n        method: 'POST',\n        body: formData,\n        keepalive: true,\n    }).then(res => res.json());\n}\nexport function startActivation(requestUrl) {\n    return fetch(requestUrl, {\n        method: 'POST',\n        keepalive: true,\n    }).then(res => res.json());\n}\n", "import { useEffect, useState } from 'react';\nconst IFRAME_DISPLAY_TIMEOUT = 5000;\nexport function useIframeNotRendered(app) {\n    const [iframeNotRendered, setIframeNotRendered] = useState(false);\n    useEffect(() => {\n        const timer = setTimeout(() => {\n            const iframe = document.getElementById(app);\n            if (!iframe) {\n                setIframeNotRendered(true);\n            }\n        }, IFRAME_DISPLAY_TIMEOUT);\n        return () => {\n            if (timer) {\n                clearTimeout(timer);\n            }\n        };\n    }, []);\n    return iframeNotRendered;\n}\nexport const resizeWindow = () => {\n    const adminMenuWrap = document.getElementById('adminmenuwrap');\n    const sideMenuHeight = adminMenuWrap ? adminMenuWrap.offsetHeight : 0;\n    const adminBar = document.getElementById('wpadminbar');\n    const adminBarHeight = (adminBar && adminBar.offsetHeight) || 0;\n    const offset = 4;\n    if (window.innerHeight < sideMenuHeight) {\n        return sideMenuHeight - offset;\n    }\n    else {\n        return window.innerHeight - adminBarHeight - offset;\n    }\n};\n", "export function addQueryObjectToUrl(urlObject, queryParams) {\n    Object.keys(queryParams).forEach(key => {\n        urlObject.searchParams.append(key, queryParams[key]);\n    });\n}\nexport function removeQueryParamFromLocation(key) {\n    const location = new URL(window.location.href);\n    location.searchParams.delete(key);\n    window.history.replaceState(null, '', location.href);\n}\n", "// extracted by mini-css-extract-plugin\nexport {};", "function RavenConfigError(message) {\n  this.name = 'RavenConfigError';\n  this.message = message;\n}\nRavenConfigError.prototype = new Error();\nRavenConfigError.prototype.constructor = RavenConfigError;\n\nmodule.exports = RavenConfigError;\n", "var wrapMethod = function(console, level, callback) {\n  var originalConsoleLevel = console[level];\n  var originalConsole = console;\n\n  if (!(level in console)) {\n    return;\n  }\n\n  var sentryLevel = level === 'warn' ? 'warning' : level;\n\n  console[level] = function() {\n    var args = [].slice.call(arguments);\n\n    var msg = '' + args.join(' ');\n    var data = {level: sentryLevel, logger: 'console', extra: {arguments: args}};\n\n    if (level === 'assert') {\n      if (args[0] === false) {\n        // Default browsers message\n        msg = 'Assertion failed: ' + (args.slice(1).join(' ') || 'console.assert');\n        data.extra.arguments = args.slice(1);\n        callback && callback(msg, data);\n      }\n    } else {\n      callback && callback(msg, data);\n    }\n\n    // this fails for some browsers. :(\n    if (originalConsoleLevel) {\n      // IE9 doesn't allow calling apply on console functions directly\n      // See: https://stackoverflow.com/questions/5472938/does-ie9-support-console-log-and-is-it-a-real-function#answer-5473193\n      Function.prototype.apply.call(originalConsoleLevel, originalConsole, args);\n    }\n  };\n};\n\nmodule.exports = {\n  wrapMethod: wrapMethod\n};\n", "/*global XDomainRequest:false */\n\nvar TraceKit = require('../vendor/TraceKit/tracekit');\nvar stringify = require('../vendor/json-stringify-safe/stringify');\nvar RavenConfigError = require('./configError');\n\nvar utils = require('./utils');\nvar isError = utils.isError;\nvar isObject = utils.isObject;\nvar isObject = utils.isObject;\nvar isErrorEvent = utils.isErrorEvent;\nvar isUndefined = utils.isUndefined;\nvar isFunction = utils.isFunction;\nvar isString = utils.isString;\nvar isEmptyObject = utils.isEmptyObject;\nvar each = utils.each;\nvar objectMerge = utils.objectMerge;\nvar truncate = utils.truncate;\nvar objectFrozen = utils.objectFrozen;\nvar hasKey = utils.hasKey;\nvar joinRegExp = utils.joinRegExp;\nvar urlencode = utils.urlencode;\nvar uuid4 = utils.uuid4;\nvar htmlTreeAsString = utils.htmlTreeAsString;\nvar isSameException = utils.isSameException;\nvar isSameStacktrace = utils.isSameStacktrace;\nvar parseUrl = utils.parseUrl;\nvar fill = utils.fill;\n\nvar wrapConsoleMethod = require('./console').wrapMethod;\n\nvar dsnKeys = 'source protocol user pass host port path'.split(' '),\n  dsnPattern = /^(?:(\\w+):)?\\/\\/(?:(\\w+)(:\\w+)?@)?([\\w\\.-]+)(?::(\\d+))?(\\/.*)/;\n\nfunction now() {\n  return +new Date();\n}\n\n// This is to be defensive in environments where window does not exist (see https://github.com/getsentry/raven-js/pull/785)\nvar _window =\n  typeof window !== 'undefined'\n    ? window\n    : typeof global !== 'undefined' ? global : typeof self !== 'undefined' ? self : {};\nvar _document = _window.document;\nvar _navigator = _window.navigator;\n\nfunction keepOriginalCallback(original, callback) {\n  return isFunction(callback)\n    ? function(data) {\n        return callback(data, original);\n      }\n    : callback;\n}\n\n// First, check for JSON support\n// If there is no JSON, we no-op the core features of Raven\n// since JSON is required to encode the payload\nfunction Raven() {\n  this._hasJSON = !!(typeof JSON === 'object' && JSON.stringify);\n  // Raven can run in contexts where there's no document (react-native)\n  this._hasDocument = !isUndefined(_document);\n  this._hasNavigator = !isUndefined(_navigator);\n  this._lastCapturedException = null;\n  this._lastData = null;\n  this._lastEventId = null;\n  this._globalServer = null;\n  this._globalKey = null;\n  this._globalProject = null;\n  this._globalContext = {};\n  this._globalOptions = {\n    logger: 'javascript',\n    ignoreErrors: [],\n    ignoreUrls: [],\n    whitelistUrls: [],\n    includePaths: [],\n    collectWindowErrors: true,\n    maxMessageLength: 0,\n\n    // By default, truncates URL values to 250 chars\n    maxUrlLength: 250,\n    stackTraceLimit: 50,\n    autoBreadcrumbs: true,\n    instrument: true,\n    sampleRate: 1\n  };\n  this._ignoreOnError = 0;\n  this._isRavenInstalled = false;\n  this._originalErrorStackTraceLimit = Error.stackTraceLimit;\n  // capture references to window.console *and* all its methods first\n  // before the console plugin has a chance to monkey patch\n  this._originalConsole = _window.console || {};\n  this._originalConsoleMethods = {};\n  this._plugins = [];\n  this._startTime = now();\n  this._wrappedBuiltIns = [];\n  this._breadcrumbs = [];\n  this._lastCapturedEvent = null;\n  this._keypressTimeout;\n  this._location = _window.location;\n  this._lastHref = this._location && this._location.href;\n  this._resetBackoff();\n\n  // eslint-disable-next-line guard-for-in\n  for (var method in this._originalConsole) {\n    this._originalConsoleMethods[method] = this._originalConsole[method];\n  }\n}\n\n/*\n * The core Raven singleton\n *\n * @this {Raven}\n */\n\nRaven.prototype = {\n  // Hardcode version string so that raven source can be loaded directly via\n  // webpack (using a build step causes webpack #1617). Grunt verifies that\n  // this value matches package.json during build.\n  //   See: https://github.com/getsentry/raven-js/issues/465\n  VERSION: '3.19.1',\n\n  debug: false,\n\n  TraceKit: TraceKit, // alias to TraceKit\n\n  /*\n     * Configure Raven with a DSN and extra options\n     *\n     * @param {string} dsn The public Sentry DSN\n     * @param {object} options Set of global options [optional]\n     * @return {Raven}\n     */\n  config: function(dsn, options) {\n    var self = this;\n\n    if (self._globalServer) {\n      this._logDebug('error', 'Error: Raven has already been configured');\n      return self;\n    }\n    if (!dsn) return self;\n\n    var globalOptions = self._globalOptions;\n\n    // merge in options\n    if (options) {\n      each(options, function(key, value) {\n        // tags and extra are special and need to be put into context\n        if (key === 'tags' || key === 'extra' || key === 'user') {\n          self._globalContext[key] = value;\n        } else {\n          globalOptions[key] = value;\n        }\n      });\n    }\n\n    self.setDSN(dsn);\n\n    // \"Script error.\" is hard coded into browsers for errors that it can't read.\n    // this is the result of a script being pulled in from an external domain and CORS.\n    globalOptions.ignoreErrors.push(/^Script error\\.?$/);\n    globalOptions.ignoreErrors.push(/^Javascript error: Script error\\.? on line 0$/);\n\n    // join regexp rules into one big rule\n    globalOptions.ignoreErrors = joinRegExp(globalOptions.ignoreErrors);\n    globalOptions.ignoreUrls = globalOptions.ignoreUrls.length\n      ? joinRegExp(globalOptions.ignoreUrls)\n      : false;\n    globalOptions.whitelistUrls = globalOptions.whitelistUrls.length\n      ? joinRegExp(globalOptions.whitelistUrls)\n      : false;\n    globalOptions.includePaths = joinRegExp(globalOptions.includePaths);\n    globalOptions.maxBreadcrumbs = Math.max(\n      0,\n      Math.min(globalOptions.maxBreadcrumbs || 100, 100)\n    ); // default and hard limit is 100\n\n    var autoBreadcrumbDefaults = {\n      xhr: true,\n      console: true,\n      dom: true,\n      location: true\n    };\n\n    var autoBreadcrumbs = globalOptions.autoBreadcrumbs;\n    if ({}.toString.call(autoBreadcrumbs) === '[object Object]') {\n      autoBreadcrumbs = objectMerge(autoBreadcrumbDefaults, autoBreadcrumbs);\n    } else if (autoBreadcrumbs !== false) {\n      autoBreadcrumbs = autoBreadcrumbDefaults;\n    }\n    globalOptions.autoBreadcrumbs = autoBreadcrumbs;\n\n    var instrumentDefaults = {\n      tryCatch: true\n    };\n\n    var instrument = globalOptions.instrument;\n    if ({}.toString.call(instrument) === '[object Object]') {\n      instrument = objectMerge(instrumentDefaults, instrument);\n    } else if (instrument !== false) {\n      instrument = instrumentDefaults;\n    }\n    globalOptions.instrument = instrument;\n\n    TraceKit.collectWindowErrors = !!globalOptions.collectWindowErrors;\n\n    // return for chaining\n    return self;\n  },\n\n  /*\n     * Installs a global window.onerror error handler\n     * to capture and report uncaught exceptions.\n     * At this point, install() is required to be called due\n     * to the way TraceKit is set up.\n     *\n     * @return {Raven}\n     */\n  install: function() {\n    var self = this;\n    if (self.isSetup() && !self._isRavenInstalled) {\n      TraceKit.report.subscribe(function() {\n        self._handleOnErrorStackInfo.apply(self, arguments);\n      });\n      if (self._globalOptions.instrument && self._globalOptions.instrument.tryCatch) {\n        self._instrumentTryCatch();\n      }\n\n      if (self._globalOptions.autoBreadcrumbs) self._instrumentBreadcrumbs();\n\n      // Install all of the plugins\n      self._drainPlugins();\n\n      self._isRavenInstalled = true;\n    }\n\n    Error.stackTraceLimit = self._globalOptions.stackTraceLimit;\n    return this;\n  },\n\n  /*\n     * Set the DSN (can be called multiple time unlike config)\n     *\n     * @param {string} dsn The public Sentry DSN\n     */\n  setDSN: function(dsn) {\n    var self = this,\n      uri = self._parseDSN(dsn),\n      lastSlash = uri.path.lastIndexOf('/'),\n      path = uri.path.substr(1, lastSlash);\n\n    self._dsn = dsn;\n    self._globalKey = uri.user;\n    self._globalSecret = uri.pass && uri.pass.substr(1);\n    self._globalProject = uri.path.substr(lastSlash + 1);\n\n    self._globalServer = self._getGlobalServer(uri);\n\n    self._globalEndpoint =\n      self._globalServer + '/' + path + 'api/' + self._globalProject + '/store/';\n\n    // Reset backoff state since we may be pointing at a\n    // new project/server\n    this._resetBackoff();\n  },\n\n  /*\n     * Wrap code within a context so Raven can capture errors\n     * reliably across domains that is executed immediately.\n     *\n     * @param {object} options A specific set of options for this context [optional]\n     * @param {function} func The callback to be immediately executed within the context\n     * @param {array} args An array of arguments to be called with the callback [optional]\n     */\n  context: function(options, func, args) {\n    if (isFunction(options)) {\n      args = func || [];\n      func = options;\n      options = undefined;\n    }\n\n    return this.wrap(options, func).apply(this, args);\n  },\n\n  /*\n     * Wrap code within a context and returns back a new function to be executed\n     *\n     * @param {object} options A specific set of options for this context [optional]\n     * @param {function} func The function to be wrapped in a new context\n     * @param {function} func A function to call before the try/catch wrapper [optional, private]\n     * @return {function} The newly wrapped functions with a context\n     */\n  wrap: function(options, func, _before) {\n    var self = this;\n    // 1 argument has been passed, and it's not a function\n    // so just return it\n    if (isUndefined(func) && !isFunction(options)) {\n      return options;\n    }\n\n    // options is optional\n    if (isFunction(options)) {\n      func = options;\n      options = undefined;\n    }\n\n    // At this point, we've passed along 2 arguments, and the second one\n    // is not a function either, so we'll just return the second argument.\n    if (!isFunction(func)) {\n      return func;\n    }\n\n    // We don't wanna wrap it twice!\n    try {\n      if (func.__raven__) {\n        return func;\n      }\n\n      // If this has already been wrapped in the past, return that\n      if (func.__raven_wrapper__) {\n        return func.__raven_wrapper__;\n      }\n    } catch (e) {\n      // Just accessing custom props in some Selenium environments\n      // can cause a \"Permission denied\" exception (see raven-js#495).\n      // Bail on wrapping and return the function as-is (defers to window.onerror).\n      return func;\n    }\n\n    function wrapped() {\n      var args = [],\n        i = arguments.length,\n        deep = !options || (options && options.deep !== false);\n\n      if (_before && isFunction(_before)) {\n        _before.apply(this, arguments);\n      }\n\n      // Recursively wrap all of a function's arguments that are\n      // functions themselves.\n      while (i--) args[i] = deep ? self.wrap(options, arguments[i]) : arguments[i];\n\n      try {\n        // Attempt to invoke user-land function\n        // NOTE: If you are a Sentry user, and you are seeing this stack frame, it\n        //       means Raven caught an error invoking your application code. This is\n        //       expected behavior and NOT indicative of a bug with Raven.js.\n        return func.apply(this, args);\n      } catch (e) {\n        self._ignoreNextOnError();\n        self.captureException(e, options);\n        throw e;\n      }\n    }\n\n    // copy over properties of the old function\n    for (var property in func) {\n      if (hasKey(func, property)) {\n        wrapped[property] = func[property];\n      }\n    }\n    wrapped.prototype = func.prototype;\n\n    func.__raven_wrapper__ = wrapped;\n    // Signal that this function has been wrapped already\n    // for both debugging and to prevent it to being wrapped twice\n    wrapped.__raven__ = true;\n    wrapped.__inner__ = func;\n\n    return wrapped;\n  },\n\n  /*\n     * Uninstalls the global error handler.\n     *\n     * @return {Raven}\n     */\n  uninstall: function() {\n    TraceKit.report.uninstall();\n\n    this._restoreBuiltIns();\n\n    Error.stackTraceLimit = this._originalErrorStackTraceLimit;\n    this._isRavenInstalled = false;\n\n    return this;\n  },\n\n  /*\n     * Manually capture an exception and send it over to Sentry\n     *\n     * @param {error} ex An exception to be logged\n     * @param {object} options A specific set of options for this error [optional]\n     * @return {Raven}\n     */\n  captureException: function(ex, options) {\n    // Cases for sending ex as a message, rather than an exception\n    var isNotError = !isError(ex);\n    var isNotErrorEvent = !isErrorEvent(ex);\n    var isErrorEventWithoutError = isErrorEvent(ex) && !ex.error;\n\n    if ((isNotError && isNotErrorEvent) || isErrorEventWithoutError) {\n      return this.captureMessage(\n        ex,\n        objectMerge(\n          {\n            trimHeadFrames: 1,\n            stacktrace: true // if we fall back to captureMessage, default to attempting a new trace\n          },\n          options\n        )\n      );\n    }\n\n    // Get actual Error from ErrorEvent\n    if (isErrorEvent(ex)) ex = ex.error;\n\n    // Store the raw exception object for potential debugging and introspection\n    this._lastCapturedException = ex;\n\n    // TraceKit.report will re-raise any exception passed to it,\n    // which means you have to wrap it in try/catch. Instead, we\n    // can wrap it here and only re-raise if TraceKit.report\n    // raises an exception different from the one we asked to\n    // report on.\n    try {\n      var stack = TraceKit.computeStackTrace(ex);\n      this._handleStackInfo(stack, options);\n    } catch (ex1) {\n      if (ex !== ex1) {\n        throw ex1;\n      }\n    }\n\n    return this;\n  },\n\n  /*\n     * Manually send a message to Sentry\n     *\n     * @param {string} msg A plain message to be captured in Sentry\n     * @param {object} options A specific set of options for this message [optional]\n     * @return {Raven}\n     */\n  captureMessage: function(msg, options) {\n    // config() automagically converts ignoreErrors from a list to a RegExp so we need to test for an\n    // early call; we'll error on the side of logging anything called before configuration since it's\n    // probably something you should see:\n    if (\n      !!this._globalOptions.ignoreErrors.test &&\n      this._globalOptions.ignoreErrors.test(msg)\n    ) {\n      return;\n    }\n\n    options = options || {};\n\n    var data = objectMerge(\n      {\n        message: msg + '' // Make sure it's actually a string\n      },\n      options\n    );\n\n    var ex;\n    // Generate a \"synthetic\" stack trace from this point.\n    // NOTE: If you are a Sentry user, and you are seeing this stack frame, it is NOT indicative\n    //       of a bug with Raven.js. Sentry generates synthetic traces either by configuration,\n    //       or if it catches a thrown object without a \"stack\" property.\n    try {\n      throw new Error(msg);\n    } catch (ex1) {\n      ex = ex1;\n    }\n\n    // null exception name so `Error` isn't prefixed to msg\n    ex.name = null;\n    var stack = TraceKit.computeStackTrace(ex);\n\n    // stack[0] is `throw new Error(msg)` call itself, we are interested in the frame that was just before that, stack[1]\n    var initialCall = stack.stack[1];\n\n    var fileurl = (initialCall && initialCall.url) || '';\n\n    if (\n      !!this._globalOptions.ignoreUrls.test &&\n      this._globalOptions.ignoreUrls.test(fileurl)\n    ) {\n      return;\n    }\n\n    if (\n      !!this._globalOptions.whitelistUrls.test &&\n      !this._globalOptions.whitelistUrls.test(fileurl)\n    ) {\n      return;\n    }\n\n    if (this._globalOptions.stacktrace || (options && options.stacktrace)) {\n      options = objectMerge(\n        {\n          // fingerprint on msg, not stack trace (legacy behavior, could be\n          // revisited)\n          fingerprint: msg,\n          // since we know this is a synthetic trace, the top N-most frames\n          // MUST be from Raven.js, so mark them as in_app later by setting\n          // trimHeadFrames\n          trimHeadFrames: (options.trimHeadFrames || 0) + 1\n        },\n        options\n      );\n\n      var frames = this._prepareFrames(stack, options);\n      data.stacktrace = {\n        // Sentry expects frames oldest to newest\n        frames: frames.reverse()\n      };\n    }\n\n    // Fire away!\n    this._send(data);\n\n    return this;\n  },\n\n  captureBreadcrumb: function(obj) {\n    var crumb = objectMerge(\n      {\n        timestamp: now() / 1000\n      },\n      obj\n    );\n\n    if (isFunction(this._globalOptions.breadcrumbCallback)) {\n      var result = this._globalOptions.breadcrumbCallback(crumb);\n\n      if (isObject(result) && !isEmptyObject(result)) {\n        crumb = result;\n      } else if (result === false) {\n        return this;\n      }\n    }\n\n    this._breadcrumbs.push(crumb);\n    if (this._breadcrumbs.length > this._globalOptions.maxBreadcrumbs) {\n      this._breadcrumbs.shift();\n    }\n    return this;\n  },\n\n  addPlugin: function(plugin /*arg1, arg2, ... argN*/) {\n    var pluginArgs = [].slice.call(arguments, 1);\n\n    this._plugins.push([plugin, pluginArgs]);\n    if (this._isRavenInstalled) {\n      this._drainPlugins();\n    }\n\n    return this;\n  },\n\n  /*\n     * Set/clear a user to be sent along with the payload.\n     *\n     * @param {object} user An object representing user data [optional]\n     * @return {Raven}\n     */\n  setUserContext: function(user) {\n    // Intentionally do not merge here since that's an unexpected behavior.\n    this._globalContext.user = user;\n\n    return this;\n  },\n\n  /*\n     * Merge extra attributes to be sent along with the payload.\n     *\n     * @param {object} extra An object representing extra data [optional]\n     * @return {Raven}\n     */\n  setExtraContext: function(extra) {\n    this._mergeContext('extra', extra);\n\n    return this;\n  },\n\n  /*\n     * Merge tags to be sent along with the payload.\n     *\n     * @param {object} tags An object representing tags [optional]\n     * @return {Raven}\n     */\n  setTagsContext: function(tags) {\n    this._mergeContext('tags', tags);\n\n    return this;\n  },\n\n  /*\n     * Clear all of the context.\n     *\n     * @return {Raven}\n     */\n  clearContext: function() {\n    this._globalContext = {};\n\n    return this;\n  },\n\n  /*\n     * Get a copy of the current context. This cannot be mutated.\n     *\n     * @return {object} copy of context\n     */\n  getContext: function() {\n    // lol javascript\n    return JSON.parse(stringify(this._globalContext));\n  },\n\n  /*\n     * Set environment of application\n     *\n     * @param {string} environment Typically something like 'production'.\n     * @return {Raven}\n     */\n  setEnvironment: function(environment) {\n    this._globalOptions.environment = environment;\n\n    return this;\n  },\n\n  /*\n     * Set release version of application\n     *\n     * @param {string} release Typically something like a git SHA to identify version\n     * @return {Raven}\n     */\n  setRelease: function(release) {\n    this._globalOptions.release = release;\n\n    return this;\n  },\n\n  /*\n     * Set the dataCallback option\n     *\n     * @param {function} callback The callback to run which allows the\n     *                            data blob to be mutated before sending\n     * @return {Raven}\n     */\n  setDataCallback: function(callback) {\n    var original = this._globalOptions.dataCallback;\n    this._globalOptions.dataCallback = keepOriginalCallback(original, callback);\n    return this;\n  },\n\n  /*\n     * Set the breadcrumbCallback option\n     *\n     * @param {function} callback The callback to run which allows filtering\n     *                            or mutating breadcrumbs\n     * @return {Raven}\n     */\n  setBreadcrumbCallback: function(callback) {\n    var original = this._globalOptions.breadcrumbCallback;\n    this._globalOptions.breadcrumbCallback = keepOriginalCallback(original, callback);\n    return this;\n  },\n\n  /*\n     * Set the shouldSendCallback option\n     *\n     * @param {function} callback The callback to run which allows\n     *                            introspecting the blob before sending\n     * @return {Raven}\n     */\n  setShouldSendCallback: function(callback) {\n    var original = this._globalOptions.shouldSendCallback;\n    this._globalOptions.shouldSendCallback = keepOriginalCallback(original, callback);\n    return this;\n  },\n\n  /**\n     * Override the default HTTP transport mechanism that transmits data\n     * to the Sentry server.\n     *\n     * @param {function} transport Function invoked instead of the default\n     *                             `makeRequest` handler.\n     *\n     * @return {Raven}\n     */\n  setTransport: function(transport) {\n    this._globalOptions.transport = transport;\n\n    return this;\n  },\n\n  /*\n     * Get the latest raw exception that was captured by Raven.\n     *\n     * @return {error}\n     */\n  lastException: function() {\n    return this._lastCapturedException;\n  },\n\n  /*\n     * Get the last event id\n     *\n     * @return {string}\n     */\n  lastEventId: function() {\n    return this._lastEventId;\n  },\n\n  /*\n     * Determine if Raven is setup and ready to go.\n     *\n     * @return {boolean}\n     */\n  isSetup: function() {\n    if (!this._hasJSON) return false; // needs JSON support\n    if (!this._globalServer) {\n      if (!this.ravenNotConfiguredError) {\n        this.ravenNotConfiguredError = true;\n        this._logDebug('error', 'Error: Raven has not been configured.');\n      }\n      return false;\n    }\n    return true;\n  },\n\n  afterLoad: function() {\n    // TODO: remove window dependence?\n\n    // Attempt to initialize Raven on load\n    var RavenConfig = _window.RavenConfig;\n    if (RavenConfig) {\n      this.config(RavenConfig.dsn, RavenConfig.config).install();\n    }\n  },\n\n  showReportDialog: function(options) {\n    if (\n      !_document // doesn't work without a document (React native)\n    )\n      return;\n\n    options = options || {};\n\n    var lastEventId = options.eventId || this.lastEventId();\n    if (!lastEventId) {\n      throw new RavenConfigError('Missing eventId');\n    }\n\n    var dsn = options.dsn || this._dsn;\n    if (!dsn) {\n      throw new RavenConfigError('Missing DSN');\n    }\n\n    var encode = encodeURIComponent;\n    var qs = '';\n    qs += '?eventId=' + encode(lastEventId);\n    qs += '&dsn=' + encode(dsn);\n\n    var user = options.user || this._globalContext.user;\n    if (user) {\n      if (user.name) qs += '&name=' + encode(user.name);\n      if (user.email) qs += '&email=' + encode(user.email);\n    }\n\n    var globalServer = this._getGlobalServer(this._parseDSN(dsn));\n\n    var script = _document.createElement('script');\n    script.async = true;\n    script.src = globalServer + '/api/embed/error-page/' + qs;\n    (_document.head || _document.body).appendChild(script);\n  },\n\n  /**** Private functions ****/\n  _ignoreNextOnError: function() {\n    var self = this;\n    this._ignoreOnError += 1;\n    setTimeout(function() {\n      // onerror should trigger before setTimeout\n      self._ignoreOnError -= 1;\n    });\n  },\n\n  _triggerEvent: function(eventType, options) {\n    // NOTE: `event` is a native browser thing, so let's avoid conflicting wiht it\n    var evt, key;\n\n    if (!this._hasDocument) return;\n\n    options = options || {};\n\n    eventType = 'raven' + eventType.substr(0, 1).toUpperCase() + eventType.substr(1);\n\n    if (_document.createEvent) {\n      evt = _document.createEvent('HTMLEvents');\n      evt.initEvent(eventType, true, true);\n    } else {\n      evt = _document.createEventObject();\n      evt.eventType = eventType;\n    }\n\n    for (key in options)\n      if (hasKey(options, key)) {\n        evt[key] = options[key];\n      }\n\n    if (_document.createEvent) {\n      // IE9 if standards\n      _document.dispatchEvent(evt);\n    } else {\n      // IE8 regardless of Quirks or Standards\n      // IE9 if quirks\n      try {\n        _document.fireEvent('on' + evt.eventType.toLowerCase(), evt);\n      } catch (e) {\n        // Do nothing\n      }\n    }\n  },\n\n  /**\n     * Wraps addEventListener to capture UI breadcrumbs\n     * @param evtName the event name (e.g. \"click\")\n     * @returns {Function}\n     * @private\n     */\n  _breadcrumbEventHandler: function(evtName) {\n    var self = this;\n    return function(evt) {\n      // reset keypress timeout; e.g. triggering a 'click' after\n      // a 'keypress' will reset the keypress debounce so that a new\n      // set of keypresses can be recorded\n      self._keypressTimeout = null;\n\n      // It's possible this handler might trigger multiple times for the same\n      // event (e.g. event propagation through node ancestors). Ignore if we've\n      // already captured the event.\n      if (self._lastCapturedEvent === evt) return;\n\n      self._lastCapturedEvent = evt;\n\n      // try/catch both:\n      // - accessing evt.target (see getsentry/raven-js#838, #768)\n      // - `htmlTreeAsString` because it's complex, and just accessing the DOM incorrectly\n      //   can throw an exception in some circumstances.\n      var target;\n      try {\n        target = htmlTreeAsString(evt.target);\n      } catch (e) {\n        target = '<unknown>';\n      }\n\n      self.captureBreadcrumb({\n        category: 'ui.' + evtName, // e.g. ui.click, ui.input\n        message: target\n      });\n    };\n  },\n\n  /**\n     * Wraps addEventListener to capture keypress UI events\n     * @returns {Function}\n     * @private\n     */\n  _keypressEventHandler: function() {\n    var self = this,\n      debounceDuration = 1000; // milliseconds\n\n    // TODO: if somehow user switches keypress target before\n    //       debounce timeout is triggered, we will only capture\n    //       a single breadcrumb from the FIRST target (acceptable?)\n    return function(evt) {\n      var target;\n      try {\n        target = evt.target;\n      } catch (e) {\n        // just accessing event properties can throw an exception in some rare circumstances\n        // see: https://github.com/getsentry/raven-js/issues/838\n        return;\n      }\n      var tagName = target && target.tagName;\n\n      // only consider keypress events on actual input elements\n      // this will disregard keypresses targeting body (e.g. tabbing\n      // through elements, hotkeys, etc)\n      if (\n        !tagName ||\n        (tagName !== 'INPUT' && tagName !== 'TEXTAREA' && !target.isContentEditable)\n      )\n        return;\n\n      // record first keypress in a series, but ignore subsequent\n      // keypresses until debounce clears\n      var timeout = self._keypressTimeout;\n      if (!timeout) {\n        self._breadcrumbEventHandler('input')(evt);\n      }\n      clearTimeout(timeout);\n      self._keypressTimeout = setTimeout(function() {\n        self._keypressTimeout = null;\n      }, debounceDuration);\n    };\n  },\n\n  /**\n     * Captures a breadcrumb of type \"navigation\", normalizing input URLs\n     * @param to the originating URL\n     * @param from the target URL\n     * @private\n     */\n  _captureUrlChange: function(from, to) {\n    var parsedLoc = parseUrl(this._location.href);\n    var parsedTo = parseUrl(to);\n    var parsedFrom = parseUrl(from);\n\n    // because onpopstate only tells you the \"new\" (to) value of location.href, and\n    // not the previous (from) value, we need to track the value of the current URL\n    // state ourselves\n    this._lastHref = to;\n\n    // Use only the path component of the URL if the URL matches the current\n    // document (almost all the time when using pushState)\n    if (parsedLoc.protocol === parsedTo.protocol && parsedLoc.host === parsedTo.host)\n      to = parsedTo.relative;\n    if (parsedLoc.protocol === parsedFrom.protocol && parsedLoc.host === parsedFrom.host)\n      from = parsedFrom.relative;\n\n    this.captureBreadcrumb({\n      category: 'navigation',\n      data: {\n        to: to,\n        from: from\n      }\n    });\n  },\n\n  /**\n     * Wrap timer functions and event targets to catch errors and provide\n     * better metadata.\n     */\n  _instrumentTryCatch: function() {\n    var self = this;\n\n    var wrappedBuiltIns = self._wrappedBuiltIns;\n\n    function wrapTimeFn(orig) {\n      return function(fn, t) {\n        // preserve arity\n        // Make a copy of the arguments to prevent deoptimization\n        // https://github.com/petkaantonov/bluebird/wiki/Optimization-killers#32-leaking-arguments\n        var args = new Array(arguments.length);\n        for (var i = 0; i < args.length; ++i) {\n          args[i] = arguments[i];\n        }\n        var originalCallback = args[0];\n        if (isFunction(originalCallback)) {\n          args[0] = self.wrap(originalCallback);\n        }\n\n        // IE < 9 doesn't support .call/.apply on setInterval/setTimeout, but it\n        // also supports only two arguments and doesn't care what this is, so we\n        // can just call the original function directly.\n        if (orig.apply) {\n          return orig.apply(this, args);\n        } else {\n          return orig(args[0], args[1]);\n        }\n      };\n    }\n\n    var autoBreadcrumbs = this._globalOptions.autoBreadcrumbs;\n\n    function wrapEventTarget(global) {\n      var proto = _window[global] && _window[global].prototype;\n      if (proto && proto.hasOwnProperty && proto.hasOwnProperty('addEventListener')) {\n        fill(\n          proto,\n          'addEventListener',\n          function(orig) {\n            return function(evtName, fn, capture, secure) {\n              // preserve arity\n              try {\n                if (fn && fn.handleEvent) {\n                  fn.handleEvent = self.wrap(fn.handleEvent);\n                }\n              } catch (err) {\n                // can sometimes get 'Permission denied to access property \"handle Event'\n              }\n\n              // More breadcrumb DOM capture ... done here and not in `_instrumentBreadcrumbs`\n              // so that we don't have more than one wrapper function\n              var before, clickHandler, keypressHandler;\n\n              if (\n                autoBreadcrumbs &&\n                autoBreadcrumbs.dom &&\n                (global === 'EventTarget' || global === 'Node')\n              ) {\n                // NOTE: generating multiple handlers per addEventListener invocation, should\n                //       revisit and verify we can just use one (almost certainly)\n                clickHandler = self._breadcrumbEventHandler('click');\n                keypressHandler = self._keypressEventHandler();\n                before = function(evt) {\n                  // need to intercept every DOM event in `before` argument, in case that\n                  // same wrapped method is re-used for different events (e.g. mousemove THEN click)\n                  // see #724\n                  if (!evt) return;\n\n                  var eventType;\n                  try {\n                    eventType = evt.type;\n                  } catch (e) {\n                    // just accessing event properties can throw an exception in some rare circumstances\n                    // see: https://github.com/getsentry/raven-js/issues/838\n                    return;\n                  }\n                  if (eventType === 'click') return clickHandler(evt);\n                  else if (eventType === 'keypress') return keypressHandler(evt);\n                };\n              }\n              return orig.call(\n                this,\n                evtName,\n                self.wrap(fn, undefined, before),\n                capture,\n                secure\n              );\n            };\n          },\n          wrappedBuiltIns\n        );\n        fill(\n          proto,\n          'removeEventListener',\n          function(orig) {\n            return function(evt, fn, capture, secure) {\n              try {\n                fn = fn && (fn.__raven_wrapper__ ? fn.__raven_wrapper__ : fn);\n              } catch (e) {\n                // ignore, accessing __raven_wrapper__ will throw in some Selenium environments\n              }\n              return orig.call(this, evt, fn, capture, secure);\n            };\n          },\n          wrappedBuiltIns\n        );\n      }\n    }\n\n    fill(_window, 'setTimeout', wrapTimeFn, wrappedBuiltIns);\n    fill(_window, 'setInterval', wrapTimeFn, wrappedBuiltIns);\n    if (_window.requestAnimationFrame) {\n      fill(\n        _window,\n        'requestAnimationFrame',\n        function(orig) {\n          return function(cb) {\n            return orig(self.wrap(cb));\n          };\n        },\n        wrappedBuiltIns\n      );\n    }\n\n    // event targets borrowed from bugsnag-js:\n    // https://github.com/bugsnag/bugsnag-js/blob/master/src/bugsnag.js#L666\n    var eventTargets = [\n      'EventTarget',\n      'Window',\n      'Node',\n      'ApplicationCache',\n      'AudioTrackList',\n      'ChannelMergerNode',\n      'CryptoOperation',\n      'EventSource',\n      'FileReader',\n      'HTMLUnknownElement',\n      'IDBDatabase',\n      'IDBRequest',\n      'IDBTransaction',\n      'KeyOperation',\n      'MediaController',\n      'MessagePort',\n      'ModalWindow',\n      'Notification',\n      'SVGElementInstance',\n      'Screen',\n      'TextTrack',\n      'TextTrackCue',\n      'TextTrackList',\n      'WebSocket',\n      'WebSocketWorker',\n      'Worker',\n      'XMLHttpRequest',\n      'XMLHttpRequestEventTarget',\n      'XMLHttpRequestUpload'\n    ];\n    for (var i = 0; i < eventTargets.length; i++) {\n      wrapEventTarget(eventTargets[i]);\n    }\n  },\n\n  /**\n     * Instrument browser built-ins w/ breadcrumb capturing\n     *  - XMLHttpRequests\n     *  - DOM interactions (click/typing)\n     *  - window.location changes\n     *  - console\n     *\n     * Can be disabled or individually configured via the `autoBreadcrumbs` config option\n     */\n  _instrumentBreadcrumbs: function() {\n    var self = this;\n    var autoBreadcrumbs = this._globalOptions.autoBreadcrumbs;\n\n    var wrappedBuiltIns = self._wrappedBuiltIns;\n\n    function wrapProp(prop, xhr) {\n      if (prop in xhr && isFunction(xhr[prop])) {\n        fill(xhr, prop, function(orig) {\n          return self.wrap(orig);\n        }); // intentionally don't track filled methods on XHR instances\n      }\n    }\n\n    if (autoBreadcrumbs.xhr && 'XMLHttpRequest' in _window) {\n      var xhrproto = XMLHttpRequest.prototype;\n      fill(\n        xhrproto,\n        'open',\n        function(origOpen) {\n          return function(method, url) {\n            // preserve arity\n\n            // if Sentry key appears in URL, don't capture\n            if (isString(url) && url.indexOf(self._globalKey) === -1) {\n              this.__raven_xhr = {\n                method: method,\n                url: url,\n                status_code: null\n              };\n            }\n\n            return origOpen.apply(this, arguments);\n          };\n        },\n        wrappedBuiltIns\n      );\n\n      fill(\n        xhrproto,\n        'send',\n        function(origSend) {\n          return function(data) {\n            // preserve arity\n            var xhr = this;\n\n            function onreadystatechangeHandler() {\n              if (xhr.__raven_xhr && xhr.readyState === 4) {\n                try {\n                  // touching statusCode in some platforms throws\n                  // an exception\n                  xhr.__raven_xhr.status_code = xhr.status;\n                } catch (e) {\n                  /* do nothing */\n                }\n\n                self.captureBreadcrumb({\n                  type: 'http',\n                  category: 'xhr',\n                  data: xhr.__raven_xhr\n                });\n              }\n            }\n\n            var props = ['onload', 'onerror', 'onprogress'];\n            for (var j = 0; j < props.length; j++) {\n              wrapProp(props[j], xhr);\n            }\n\n            if ('onreadystatechange' in xhr && isFunction(xhr.onreadystatechange)) {\n              fill(\n                xhr,\n                'onreadystatechange',\n                function(orig) {\n                  return self.wrap(orig, undefined, onreadystatechangeHandler);\n                } /* intentionally don't track this instrumentation */\n              );\n            } else {\n              // if onreadystatechange wasn't actually set by the page on this xhr, we\n              // are free to set our own and capture the breadcrumb\n              xhr.onreadystatechange = onreadystatechangeHandler;\n            }\n\n            return origSend.apply(this, arguments);\n          };\n        },\n        wrappedBuiltIns\n      );\n    }\n\n    if (autoBreadcrumbs.xhr && 'fetch' in _window) {\n      fill(\n        _window,\n        'fetch',\n        function(origFetch) {\n          return function(fn, t) {\n            // preserve arity\n            // Make a copy of the arguments to prevent deoptimization\n            // https://github.com/petkaantonov/bluebird/wiki/Optimization-killers#32-leaking-arguments\n            var args = new Array(arguments.length);\n            for (var i = 0; i < args.length; ++i) {\n              args[i] = arguments[i];\n            }\n\n            var fetchInput = args[0];\n            var method = 'GET';\n            var url;\n\n            if (typeof fetchInput === 'string') {\n              url = fetchInput;\n            } else if ('Request' in _window && fetchInput instanceof _window.Request) {\n              url = fetchInput.url;\n              if (fetchInput.method) {\n                method = fetchInput.method;\n              }\n            } else {\n              url = '' + fetchInput;\n            }\n\n            if (args[1] && args[1].method) {\n              method = args[1].method;\n            }\n\n            var fetchData = {\n              method: method,\n              url: url,\n              status_code: null\n            };\n\n            self.captureBreadcrumb({\n              type: 'http',\n              category: 'fetch',\n              data: fetchData\n            });\n\n            return origFetch.apply(this, args).then(function(response) {\n              fetchData.status_code = response.status;\n\n              return response;\n            });\n          };\n        },\n        wrappedBuiltIns\n      );\n    }\n\n    // Capture breadcrumbs from any click that is unhandled / bubbled up all the way\n    // to the document. Do this before we instrument addEventListener.\n    if (autoBreadcrumbs.dom && this._hasDocument) {\n      if (_document.addEventListener) {\n        _document.addEventListener('click', self._breadcrumbEventHandler('click'), false);\n        _document.addEventListener('keypress', self._keypressEventHandler(), false);\n      } else {\n        // IE8 Compatibility\n        _document.attachEvent('onclick', self._breadcrumbEventHandler('click'));\n        _document.attachEvent('onkeypress', self._keypressEventHandler());\n      }\n    }\n\n    // record navigation (URL) changes\n    // NOTE: in Chrome App environment, touching history.pushState, *even inside\n    //       a try/catch block*, will cause Chrome to output an error to console.error\n    // borrowed from: https://github.com/angular/angular.js/pull/13945/files\n    var chrome = _window.chrome;\n    var isChromePackagedApp = chrome && chrome.app && chrome.app.runtime;\n    var hasPushAndReplaceState =\n      !isChromePackagedApp &&\n      _window.history &&\n      history.pushState &&\n      history.replaceState;\n    if (autoBreadcrumbs.location && hasPushAndReplaceState) {\n      // TODO: remove onpopstate handler on uninstall()\n      var oldOnPopState = _window.onpopstate;\n      _window.onpopstate = function() {\n        var currentHref = self._location.href;\n        self._captureUrlChange(self._lastHref, currentHref);\n\n        if (oldOnPopState) {\n          return oldOnPopState.apply(this, arguments);\n        }\n      };\n\n      var historyReplacementFunction = function(origHistFunction) {\n        // note history.pushState.length is 0; intentionally not declaring\n        // params to preserve 0 arity\n        return function(/* state, title, url */) {\n          var url = arguments.length > 2 ? arguments[2] : undefined;\n\n          // url argument is optional\n          if (url) {\n            // coerce to string (this is what pushState does)\n            self._captureUrlChange(self._lastHref, url + '');\n          }\n\n          return origHistFunction.apply(this, arguments);\n        };\n      };\n\n      fill(history, 'pushState', historyReplacementFunction, wrappedBuiltIns);\n      fill(history, 'replaceState', historyReplacementFunction, wrappedBuiltIns);\n    }\n\n    if (autoBreadcrumbs.console && 'console' in _window && console.log) {\n      // console\n      var consoleMethodCallback = function(msg, data) {\n        self.captureBreadcrumb({\n          message: msg,\n          level: data.level,\n          category: 'console'\n        });\n      };\n\n      each(['debug', 'info', 'warn', 'error', 'log'], function(_, level) {\n        wrapConsoleMethod(console, level, consoleMethodCallback);\n      });\n    }\n  },\n\n  _restoreBuiltIns: function() {\n    // restore any wrapped builtins\n    var builtin;\n    while (this._wrappedBuiltIns.length) {\n      builtin = this._wrappedBuiltIns.shift();\n\n      var obj = builtin[0],\n        name = builtin[1],\n        orig = builtin[2];\n\n      obj[name] = orig;\n    }\n  },\n\n  _drainPlugins: function() {\n    var self = this;\n\n    // FIX ME TODO\n    each(this._plugins, function(_, plugin) {\n      var installer = plugin[0];\n      var args = plugin[1];\n      installer.apply(self, [self].concat(args));\n    });\n  },\n\n  _parseDSN: function(str) {\n    var m = dsnPattern.exec(str),\n      dsn = {},\n      i = 7;\n\n    try {\n      while (i--) dsn[dsnKeys[i]] = m[i] || '';\n    } catch (e) {\n      throw new RavenConfigError('Invalid DSN: ' + str);\n    }\n\n    if (dsn.pass && !this._globalOptions.allowSecretKey) {\n      throw new RavenConfigError(\n        'Do not specify your secret key in the DSN. See: http://bit.ly/raven-secret-key'\n      );\n    }\n\n    return dsn;\n  },\n\n  _getGlobalServer: function(uri) {\n    // assemble the endpoint from the uri pieces\n    var globalServer = '//' + uri.host + (uri.port ? ':' + uri.port : '');\n\n    if (uri.protocol) {\n      globalServer = uri.protocol + ':' + globalServer;\n    }\n    return globalServer;\n  },\n\n  _handleOnErrorStackInfo: function() {\n    // if we are intentionally ignoring errors via onerror, bail out\n    if (!this._ignoreOnError) {\n      this._handleStackInfo.apply(this, arguments);\n    }\n  },\n\n  _handleStackInfo: function(stackInfo, options) {\n    var frames = this._prepareFrames(stackInfo, options);\n\n    this._triggerEvent('handle', {\n      stackInfo: stackInfo,\n      options: options\n    });\n\n    this._processException(\n      stackInfo.name,\n      stackInfo.message,\n      stackInfo.url,\n      stackInfo.lineno,\n      frames,\n      options\n    );\n  },\n\n  _prepareFrames: function(stackInfo, options) {\n    var self = this;\n    var frames = [];\n    if (stackInfo.stack && stackInfo.stack.length) {\n      each(stackInfo.stack, function(i, stack) {\n        var frame = self._normalizeFrame(stack, stackInfo.url);\n        if (frame) {\n          frames.push(frame);\n        }\n      });\n\n      // e.g. frames captured via captureMessage throw\n      if (options && options.trimHeadFrames) {\n        for (var j = 0; j < options.trimHeadFrames && j < frames.length; j++) {\n          frames[j].in_app = false;\n        }\n      }\n    }\n    frames = frames.slice(0, this._globalOptions.stackTraceLimit);\n    return frames;\n  },\n\n  _normalizeFrame: function(frame, stackInfoUrl) {\n    // normalize the frames data\n    var normalized = {\n      filename: frame.url,\n      lineno: frame.line,\n      colno: frame.column,\n      function: frame.func || '?'\n    };\n\n    // Case when we don't have any information about the error\n    // E.g. throwing a string or raw object, instead of an `Error` in Firefox\n    // Generating synthetic error doesn't add any value here\n    //\n    // We should probably somehow let a user know that they should fix their code\n    if (!frame.url) {\n      normalized.filename = stackInfoUrl; // fallback to whole stacks url from onerror handler\n    }\n\n    normalized.in_app = !// determine if an exception came from outside of our app\n    // first we check the global includePaths list.\n    (\n      (!!this._globalOptions.includePaths.test &&\n        !this._globalOptions.includePaths.test(normalized.filename)) ||\n      // Now we check for fun, if the function name is Raven or TraceKit\n      /(Raven|TraceKit)\\./.test(normalized['function']) ||\n      // finally, we do a last ditch effort and check for raven.min.js\n      /raven\\.(min\\.)?js$/.test(normalized.filename)\n    );\n\n    return normalized;\n  },\n\n  _processException: function(type, message, fileurl, lineno, frames, options) {\n    var prefixedMessage = (type ? type + ': ' : '') + (message || '');\n    if (\n      !!this._globalOptions.ignoreErrors.test &&\n      (this._globalOptions.ignoreErrors.test(message) ||\n        this._globalOptions.ignoreErrors.test(prefixedMessage))\n    ) {\n      return;\n    }\n\n    var stacktrace;\n\n    if (frames && frames.length) {\n      fileurl = frames[0].filename || fileurl;\n      // Sentry expects frames oldest to newest\n      // and JS sends them as newest to oldest\n      frames.reverse();\n      stacktrace = {frames: frames};\n    } else if (fileurl) {\n      stacktrace = {\n        frames: [\n          {\n            filename: fileurl,\n            lineno: lineno,\n            in_app: true\n          }\n        ]\n      };\n    }\n\n    if (\n      !!this._globalOptions.ignoreUrls.test &&\n      this._globalOptions.ignoreUrls.test(fileurl)\n    ) {\n      return;\n    }\n\n    if (\n      !!this._globalOptions.whitelistUrls.test &&\n      !this._globalOptions.whitelistUrls.test(fileurl)\n    ) {\n      return;\n    }\n\n    var data = objectMerge(\n      {\n        // sentry.interfaces.Exception\n        exception: {\n          values: [\n            {\n              type: type,\n              value: message,\n              stacktrace: stacktrace\n            }\n          ]\n        },\n        culprit: fileurl\n      },\n      options\n    );\n\n    // Fire away!\n    this._send(data);\n  },\n\n  _trimPacket: function(data) {\n    // For now, we only want to truncate the two different messages\n    // but this could/should be expanded to just trim everything\n    var max = this._globalOptions.maxMessageLength;\n    if (data.message) {\n      data.message = truncate(data.message, max);\n    }\n    if (data.exception) {\n      var exception = data.exception.values[0];\n      exception.value = truncate(exception.value, max);\n    }\n\n    var request = data.request;\n    if (request) {\n      if (request.url) {\n        request.url = truncate(request.url, this._globalOptions.maxUrlLength);\n      }\n      if (request.Referer) {\n        request.Referer = truncate(request.Referer, this._globalOptions.maxUrlLength);\n      }\n    }\n\n    if (data.breadcrumbs && data.breadcrumbs.values)\n      this._trimBreadcrumbs(data.breadcrumbs);\n\n    return data;\n  },\n\n  /**\n     * Truncate breadcrumb values (right now just URLs)\n     */\n  _trimBreadcrumbs: function(breadcrumbs) {\n    // known breadcrumb properties with urls\n    // TODO: also consider arbitrary prop values that start with (https?)?://\n    var urlProps = ['to', 'from', 'url'],\n      urlProp,\n      crumb,\n      data;\n\n    for (var i = 0; i < breadcrumbs.values.length; ++i) {\n      crumb = breadcrumbs.values[i];\n      if (\n        !crumb.hasOwnProperty('data') ||\n        !isObject(crumb.data) ||\n        objectFrozen(crumb.data)\n      )\n        continue;\n\n      data = objectMerge({}, crumb.data);\n      for (var j = 0; j < urlProps.length; ++j) {\n        urlProp = urlProps[j];\n        if (data.hasOwnProperty(urlProp) && data[urlProp]) {\n          data[urlProp] = truncate(data[urlProp], this._globalOptions.maxUrlLength);\n        }\n      }\n      breadcrumbs.values[i].data = data;\n    }\n  },\n\n  _getHttpData: function() {\n    if (!this._hasNavigator && !this._hasDocument) return;\n    var httpData = {};\n\n    if (this._hasNavigator && _navigator.userAgent) {\n      httpData.headers = {\n        'User-Agent': navigator.userAgent\n      };\n    }\n\n    if (this._hasDocument) {\n      if (_document.location && _document.location.href) {\n        httpData.url = _document.location.href;\n      }\n      if (_document.referrer) {\n        if (!httpData.headers) httpData.headers = {};\n        httpData.headers.Referer = _document.referrer;\n      }\n    }\n\n    return httpData;\n  },\n\n  _resetBackoff: function() {\n    this._backoffDuration = 0;\n    this._backoffStart = null;\n  },\n\n  _shouldBackoff: function() {\n    return this._backoffDuration && now() - this._backoffStart < this._backoffDuration;\n  },\n\n  /**\n     * Returns true if the in-process data payload matches the signature\n     * of the previously-sent data\n     *\n     * NOTE: This has to be done at this level because TraceKit can generate\n     *       data from window.onerror WITHOUT an exception object (IE8, IE9,\n     *       other old browsers). This can take the form of an \"exception\"\n     *       data object with a single frame (derived from the onerror args).\n     */\n  _isRepeatData: function(current) {\n    var last = this._lastData;\n\n    if (\n      !last ||\n      current.message !== last.message || // defined for captureMessage\n      current.culprit !== last.culprit // defined for captureException/onerror\n    )\n      return false;\n\n    // Stacktrace interface (i.e. from captureMessage)\n    if (current.stacktrace || last.stacktrace) {\n      return isSameStacktrace(current.stacktrace, last.stacktrace);\n    } else if (current.exception || last.exception) {\n      // Exception interface (i.e. from captureException/onerror)\n      return isSameException(current.exception, last.exception);\n    }\n\n    return true;\n  },\n\n  _setBackoffState: function(request) {\n    // If we are already in a backoff state, don't change anything\n    if (this._shouldBackoff()) {\n      return;\n    }\n\n    var status = request.status;\n\n    // 400 - project_id doesn't exist or some other fatal\n    // 401 - invalid/revoked dsn\n    // 429 - too many requests\n    if (!(status === 400 || status === 401 || status === 429)) return;\n\n    var retry;\n    try {\n      // If Retry-After is not in Access-Control-Expose-Headers, most\n      // browsers will throw an exception trying to access it\n      retry = request.getResponseHeader('Retry-After');\n      retry = parseInt(retry, 10) * 1000; // Retry-After is returned in seconds\n    } catch (e) {\n      /* eslint no-empty:0 */\n    }\n\n    this._backoffDuration = retry\n      ? // If Sentry server returned a Retry-After value, use it\n        retry\n      : // Otherwise, double the last backoff duration (starts at 1 sec)\n        this._backoffDuration * 2 || 1000;\n\n    this._backoffStart = now();\n  },\n\n  _send: function(data) {\n    var globalOptions = this._globalOptions;\n\n    var baseData = {\n        project: this._globalProject,\n        logger: globalOptions.logger,\n        platform: 'javascript'\n      },\n      httpData = this._getHttpData();\n\n    if (httpData) {\n      baseData.request = httpData;\n    }\n\n    // HACK: delete `trimHeadFrames` to prevent from appearing in outbound payload\n    if (data.trimHeadFrames) delete data.trimHeadFrames;\n\n    data = objectMerge(baseData, data);\n\n    // Merge in the tags and extra separately since objectMerge doesn't handle a deep merge\n    data.tags = objectMerge(objectMerge({}, this._globalContext.tags), data.tags);\n    data.extra = objectMerge(objectMerge({}, this._globalContext.extra), data.extra);\n\n    // Send along our own collected metadata with extra\n    data.extra['session:duration'] = now() - this._startTime;\n\n    if (this._breadcrumbs && this._breadcrumbs.length > 0) {\n      // intentionally make shallow copy so that additions\n      // to breadcrumbs aren't accidentally sent in this request\n      data.breadcrumbs = {\n        values: [].slice.call(this._breadcrumbs, 0)\n      };\n    }\n\n    // If there are no tags/extra, strip the key from the payload alltogther.\n    if (isEmptyObject(data.tags)) delete data.tags;\n\n    if (this._globalContext.user) {\n      // sentry.interfaces.User\n      data.user = this._globalContext.user;\n    }\n\n    // Include the environment if it's defined in globalOptions\n    if (globalOptions.environment) data.environment = globalOptions.environment;\n\n    // Include the release if it's defined in globalOptions\n    if (globalOptions.release) data.release = globalOptions.release;\n\n    // Include server_name if it's defined in globalOptions\n    if (globalOptions.serverName) data.server_name = globalOptions.serverName;\n\n    if (isFunction(globalOptions.dataCallback)) {\n      data = globalOptions.dataCallback(data) || data;\n    }\n\n    // Why??????????\n    if (!data || isEmptyObject(data)) {\n      return;\n    }\n\n    // Check if the request should be filtered or not\n    if (\n      isFunction(globalOptions.shouldSendCallback) &&\n      !globalOptions.shouldSendCallback(data)\n    ) {\n      return;\n    }\n\n    // Backoff state: Sentry server previously responded w/ an error (e.g. 429 - too many requests),\n    // so drop requests until \"cool-off\" period has elapsed.\n    if (this._shouldBackoff()) {\n      this._logDebug('warn', 'Raven dropped error due to backoff: ', data);\n      return;\n    }\n\n    if (typeof globalOptions.sampleRate === 'number') {\n      if (Math.random() < globalOptions.sampleRate) {\n        this._sendProcessedPayload(data);\n      }\n    } else {\n      this._sendProcessedPayload(data);\n    }\n  },\n\n  _getUuid: function() {\n    return uuid4();\n  },\n\n  _sendProcessedPayload: function(data, callback) {\n    var self = this;\n    var globalOptions = this._globalOptions;\n\n    if (!this.isSetup()) return;\n\n    // Try and clean up the packet before sending by truncating long values\n    data = this._trimPacket(data);\n\n    // ideally duplicate error testing should occur *before* dataCallback/shouldSendCallback,\n    // but this would require copying an un-truncated copy of the data packet, which can be\n    // arbitrarily deep (extra_data) -- could be worthwhile? will revisit\n    if (!this._globalOptions.allowDuplicates && this._isRepeatData(data)) {\n      this._logDebug('warn', 'Raven dropped repeat event: ', data);\n      return;\n    }\n\n    // Send along an event_id if not explicitly passed.\n    // This event_id can be used to reference the error within Sentry itself.\n    // Set lastEventId after we know the error should actually be sent\n    this._lastEventId = data.event_id || (data.event_id = this._getUuid());\n\n    // Store outbound payload after trim\n    this._lastData = data;\n\n    this._logDebug('debug', 'Raven about to send:', data);\n\n    var auth = {\n      sentry_version: '7',\n      sentry_client: 'raven-js/' + this.VERSION,\n      sentry_key: this._globalKey\n    };\n\n    if (this._globalSecret) {\n      auth.sentry_secret = this._globalSecret;\n    }\n\n    var exception = data.exception && data.exception.values[0];\n    this.captureBreadcrumb({\n      category: 'sentry',\n      message: exception\n        ? (exception.type ? exception.type + ': ' : '') + exception.value\n        : data.message,\n      event_id: data.event_id,\n      level: data.level || 'error' // presume error unless specified\n    });\n\n    var url = this._globalEndpoint;\n    (globalOptions.transport || this._makeRequest).call(this, {\n      url: url,\n      auth: auth,\n      data: data,\n      options: globalOptions,\n      onSuccess: function success() {\n        self._resetBackoff();\n\n        self._triggerEvent('success', {\n          data: data,\n          src: url\n        });\n        callback && callback();\n      },\n      onError: function failure(error) {\n        self._logDebug('error', 'Raven transport failed to send: ', error);\n\n        if (error.request) {\n          self._setBackoffState(error.request);\n        }\n\n        self._triggerEvent('failure', {\n          data: data,\n          src: url\n        });\n        error = error || new Error('Raven send failed (no additional details provided)');\n        callback && callback(error);\n      }\n    });\n  },\n\n  _makeRequest: function(opts) {\n    var request = _window.XMLHttpRequest && new _window.XMLHttpRequest();\n    if (!request) return;\n\n    // if browser doesn't support CORS (e.g. IE7), we are out of luck\n    var hasCORS = 'withCredentials' in request || typeof XDomainRequest !== 'undefined';\n\n    if (!hasCORS) return;\n\n    var url = opts.url;\n\n    if ('withCredentials' in request) {\n      request.onreadystatechange = function() {\n        if (request.readyState !== 4) {\n          return;\n        } else if (request.status === 200) {\n          opts.onSuccess && opts.onSuccess();\n        } else if (opts.onError) {\n          var err = new Error('Sentry error code: ' + request.status);\n          err.request = request;\n          opts.onError(err);\n        }\n      };\n    } else {\n      request = new XDomainRequest();\n      // xdomainrequest cannot go http -> https (or vice versa),\n      // so always use protocol relative\n      url = url.replace(/^https?:/, '');\n\n      // onreadystatechange not supported by XDomainRequest\n      if (opts.onSuccess) {\n        request.onload = opts.onSuccess;\n      }\n      if (opts.onError) {\n        request.onerror = function() {\n          var err = new Error('Sentry error code: XDomainRequest');\n          err.request = request;\n          opts.onError(err);\n        };\n      }\n    }\n\n    // NOTE: auth is intentionally sent as part of query string (NOT as custom\n    //       HTTP header) so as to avoid preflight CORS requests\n    request.open('POST', url + '?' + urlencode(opts.auth));\n    request.send(stringify(opts.data));\n  },\n\n  _logDebug: function(level) {\n    if (this._originalConsoleMethods[level] && this.debug) {\n      // In IE<10 console methods do not have their own 'apply' method\n      Function.prototype.apply.call(\n        this._originalConsoleMethods[level],\n        this._originalConsole,\n        [].slice.call(arguments, 1)\n      );\n    }\n  },\n\n  _mergeContext: function(key, context) {\n    if (isUndefined(context)) {\n      delete this._globalContext[key];\n    } else {\n      this._globalContext[key] = objectMerge(this._globalContext[key] || {}, context);\n    }\n  }\n};\n\n// Deprecations\nRaven.prototype.setUser = Raven.prototype.setUserContext;\nRaven.prototype.setReleaseContext = Raven.prototype.setRelease;\n\nmodule.exports = Raven;\n", "/**\n * Enforces a single instance of the Raven client, and the\n * main entry point for <PERSON>. If you are a consumer of the\n * Raven library, you SHOULD load this file (vs raven.js).\n **/\n\nvar RavenConstructor = require('./raven');\n\n// This is to be defensive in environments where window does not exist (see https://github.com/getsentry/raven-js/pull/785)\nvar _window =\n  typeof window !== 'undefined'\n    ? window\n    : typeof global !== 'undefined' ? global : typeof self !== 'undefined' ? self : {};\nvar _Raven = _window.Raven;\n\nvar Raven = new RavenConstructor();\n\n/*\n * Allow multiple versions of Raven to be installed.\n * Strip Raven from the global context and returns the instance.\n *\n * @return {Raven}\n */\nRaven.noConflict = function() {\n  _window.Raven = _Raven;\n  return Raven;\n};\n\nRaven.afterLoad();\n\nmodule.exports = Raven;\n", "var _window =\n  typeof window !== 'undefined'\n    ? window\n    : typeof global !== 'undefined' ? global : typeof self !== 'undefined' ? self : {};\n\nfunction isObject(what) {\n  return typeof what === 'object' && what !== null;\n}\n\n// Yanked from https://git.io/vS8DV re-used under CC0\n// with some tiny modifications\nfunction isError(value) {\n  switch ({}.toString.call(value)) {\n    case '[object Error]':\n      return true;\n    case '[object Exception]':\n      return true;\n    case '[object DOMException]':\n      return true;\n    default:\n      return value instanceof Error;\n  }\n}\n\nfunction isErrorEvent(value) {\n  return supportsErrorEvent() && {}.toString.call(value) === '[object ErrorEvent]';\n}\n\nfunction isUndefined(what) {\n  return what === void 0;\n}\n\nfunction isFunction(what) {\n  return typeof what === 'function';\n}\n\nfunction isString(what) {\n  return Object.prototype.toString.call(what) === '[object String]';\n}\n\nfunction isEmptyObject(what) {\n  for (var _ in what) return false; // eslint-disable-line guard-for-in, no-unused-vars\n  return true;\n}\n\nfunction supportsErrorEvent() {\n  try {\n    new ErrorEvent(''); // eslint-disable-line no-new\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\n\nfunction wrappedCallback(callback) {\n  function dataCallback(data, original) {\n    var normalizedData = callback(data) || data;\n    if (original) {\n      return original(normalizedData) || normalizedData;\n    }\n    return normalizedData;\n  }\n\n  return dataCallback;\n}\n\nfunction each(obj, callback) {\n  var i, j;\n\n  if (isUndefined(obj.length)) {\n    for (i in obj) {\n      if (hasKey(obj, i)) {\n        callback.call(null, i, obj[i]);\n      }\n    }\n  } else {\n    j = obj.length;\n    if (j) {\n      for (i = 0; i < j; i++) {\n        callback.call(null, i, obj[i]);\n      }\n    }\n  }\n}\n\nfunction objectMerge(obj1, obj2) {\n  if (!obj2) {\n    return obj1;\n  }\n  each(obj2, function(key, value) {\n    obj1[key] = value;\n  });\n  return obj1;\n}\n\n/**\n * This function is only used for react-native.\n * react-native freezes object that have already been sent over the\n * js bridge. We need this function in order to check if the object is frozen.\n * So it's ok that objectFrozen returns false if Object.isFrozen is not\n * supported because it's not relevant for other \"platforms\". See related issue:\n * https://github.com/getsentry/react-native-sentry/issues/57\n */\nfunction objectFrozen(obj) {\n  if (!Object.isFrozen) {\n    return false;\n  }\n  return Object.isFrozen(obj);\n}\n\nfunction truncate(str, max) {\n  return !max || str.length <= max ? str : str.substr(0, max) + '\\u2026';\n}\n\n/**\n * hasKey, a better form of hasOwnProperty\n * Example: hasKey(MainHostObject, property) === true/false\n *\n * @param {Object} host object to check property\n * @param {string} key to check\n */\nfunction hasKey(object, key) {\n  return Object.prototype.hasOwnProperty.call(object, key);\n}\n\nfunction joinRegExp(patterns) {\n  // Combine an array of regular expressions and strings into one large regexp\n  // Be mad.\n  var sources = [],\n    i = 0,\n    len = patterns.length,\n    pattern;\n\n  for (; i < len; i++) {\n    pattern = patterns[i];\n    if (isString(pattern)) {\n      // If it's a string, we need to escape it\n      // Taken from: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Regular_Expressions\n      sources.push(pattern.replace(/([.*+?^=!:${}()|\\[\\]\\/\\\\])/g, '\\\\$1'));\n    } else if (pattern && pattern.source) {\n      // If it's a regexp already, we want to extract the source\n      sources.push(pattern.source);\n    }\n    // Intentionally skip other cases\n  }\n  return new RegExp(sources.join('|'), 'i');\n}\n\nfunction urlencode(o) {\n  var pairs = [];\n  each(o, function(key, value) {\n    pairs.push(encodeURIComponent(key) + '=' + encodeURIComponent(value));\n  });\n  return pairs.join('&');\n}\n\n// borrowed from https://tools.ietf.org/html/rfc3986#appendix-B\n// intentionally using regex and not <a/> href parsing trick because React Native and other\n// environments where DOM might not be available\nfunction parseUrl(url) {\n  var match = url.match(/^(([^:\\/?#]+):)?(\\/\\/([^\\/?#]*))?([^?#]*)(\\?([^#]*))?(#(.*))?$/);\n  if (!match) return {};\n\n  // coerce to undefined values to empty string so we don't get 'undefined'\n  var query = match[6] || '';\n  var fragment = match[8] || '';\n  return {\n    protocol: match[2],\n    host: match[4],\n    path: match[5],\n    relative: match[5] + query + fragment // everything minus origin\n  };\n}\nfunction uuid4() {\n  var crypto = _window.crypto || _window.msCrypto;\n\n  if (!isUndefined(crypto) && crypto.getRandomValues) {\n    // Use window.crypto API if available\n    // eslint-disable-next-line no-undef\n    var arr = new Uint16Array(8);\n    crypto.getRandomValues(arr);\n\n    // set 4 in byte 7\n    arr[3] = (arr[3] & 0xfff) | 0x4000;\n    // set 2 most significant bits of byte 9 to '10'\n    arr[4] = (arr[4] & 0x3fff) | 0x8000;\n\n    var pad = function(num) {\n      var v = num.toString(16);\n      while (v.length < 4) {\n        v = '0' + v;\n      }\n      return v;\n    };\n\n    return (\n      pad(arr[0]) +\n      pad(arr[1]) +\n      pad(arr[2]) +\n      pad(arr[3]) +\n      pad(arr[4]) +\n      pad(arr[5]) +\n      pad(arr[6]) +\n      pad(arr[7])\n    );\n  } else {\n    // http://stackoverflow.com/questions/105034/how-to-create-a-guid-uuid-in-javascript/2117523#2117523\n    return 'xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\n      var r = (Math.random() * 16) | 0,\n        v = c === 'x' ? r : (r & 0x3) | 0x8;\n      return v.toString(16);\n    });\n  }\n}\n\n/**\n * Given a child DOM element, returns a query-selector statement describing that\n * and its ancestors\n * e.g. [HTMLElement] => body > div > input#foo.btn[name=baz]\n * @param elem\n * @returns {string}\n */\nfunction htmlTreeAsString(elem) {\n  /* eslint no-extra-parens:0*/\n  var MAX_TRAVERSE_HEIGHT = 5,\n    MAX_OUTPUT_LEN = 80,\n    out = [],\n    height = 0,\n    len = 0,\n    separator = ' > ',\n    sepLength = separator.length,\n    nextStr;\n\n  while (elem && height++ < MAX_TRAVERSE_HEIGHT) {\n    nextStr = htmlElementAsString(elem);\n    // bail out if\n    // - nextStr is the 'html' element\n    // - the length of the string that would be created exceeds MAX_OUTPUT_LEN\n    //   (ignore this limit if we are on the first iteration)\n    if (\n      nextStr === 'html' ||\n      (height > 1 && len + out.length * sepLength + nextStr.length >= MAX_OUTPUT_LEN)\n    ) {\n      break;\n    }\n\n    out.push(nextStr);\n\n    len += nextStr.length;\n    elem = elem.parentNode;\n  }\n\n  return out.reverse().join(separator);\n}\n\n/**\n * Returns a simple, query-selector representation of a DOM element\n * e.g. [HTMLElement] => input#foo.btn[name=baz]\n * @param HTMLElement\n * @returns {string}\n */\nfunction htmlElementAsString(elem) {\n  var out = [],\n    className,\n    classes,\n    key,\n    attr,\n    i;\n\n  if (!elem || !elem.tagName) {\n    return '';\n  }\n\n  out.push(elem.tagName.toLowerCase());\n  if (elem.id) {\n    out.push('#' + elem.id);\n  }\n\n  className = elem.className;\n  if (className && isString(className)) {\n    classes = className.split(/\\s+/);\n    for (i = 0; i < classes.length; i++) {\n      out.push('.' + classes[i]);\n    }\n  }\n  var attrWhitelist = ['type', 'name', 'title', 'alt'];\n  for (i = 0; i < attrWhitelist.length; i++) {\n    key = attrWhitelist[i];\n    attr = elem.getAttribute(key);\n    if (attr) {\n      out.push('[' + key + '=\"' + attr + '\"]');\n    }\n  }\n  return out.join('');\n}\n\n/**\n * Returns true if either a OR b is truthy, but not both\n */\nfunction isOnlyOneTruthy(a, b) {\n  return !!(!!a ^ !!b);\n}\n\n/**\n * Returns true if the two input exception interfaces have the same content\n */\nfunction isSameException(ex1, ex2) {\n  if (isOnlyOneTruthy(ex1, ex2)) return false;\n\n  ex1 = ex1.values[0];\n  ex2 = ex2.values[0];\n\n  if (ex1.type !== ex2.type || ex1.value !== ex2.value) return false;\n\n  return isSameStacktrace(ex1.stacktrace, ex2.stacktrace);\n}\n\n/**\n * Returns true if the two input stack trace interfaces have the same content\n */\nfunction isSameStacktrace(stack1, stack2) {\n  if (isOnlyOneTruthy(stack1, stack2)) return false;\n\n  var frames1 = stack1.frames;\n  var frames2 = stack2.frames;\n\n  // Exit early if frame count differs\n  if (frames1.length !== frames2.length) return false;\n\n  // Iterate through every frame; bail out if anything differs\n  var a, b;\n  for (var i = 0; i < frames1.length; i++) {\n    a = frames1[i];\n    b = frames2[i];\n    if (\n      a.filename !== b.filename ||\n      a.lineno !== b.lineno ||\n      a.colno !== b.colno ||\n      a['function'] !== b['function']\n    )\n      return false;\n  }\n  return true;\n}\n\n/**\n * Polyfill a method\n * @param obj object e.g. `document`\n * @param name method name present on object e.g. `addEventListener`\n * @param replacement replacement function\n * @param track {optional} record instrumentation to an array\n */\nfunction fill(obj, name, replacement, track) {\n  var orig = obj[name];\n  obj[name] = replacement(orig);\n  if (track) {\n    track.push([obj, name, orig]);\n  }\n}\n\nmodule.exports = {\n  isObject: isObject,\n  isError: isError,\n  isErrorEvent: isErrorEvent,\n  isUndefined: isUndefined,\n  isFunction: isFunction,\n  isString: isString,\n  isEmptyObject: isEmptyObject,\n  supportsErrorEvent: supportsErrorEvent,\n  wrappedCallback: wrappedCallback,\n  each: each,\n  objectMerge: objectMerge,\n  truncate: truncate,\n  objectFrozen: objectFrozen,\n  hasKey: hasKey,\n  joinRegExp: joinRegExp,\n  urlencode: urlencode,\n  uuid4: uuid4,\n  htmlTreeAsString: htmlTreeAsString,\n  htmlElementAsString: htmlElementAsString,\n  isSameException: isSameException,\n  isSameStacktrace: isSameStacktrace,\n  parseUrl: parseUrl,\n  fill: fill\n};\n", "var utils = require('../../src/utils');\n\n/*\n TraceKit - Cross brower stack traces\n\n This was originally forked from github.com/occ/TraceKit, but has since been\n largely re-written and is now maintained as part of raven-js.  Tests for\n this are in test/vendor.\n\n MIT license\n*/\n\nvar TraceKit = {\n  collectWindowErrors: true,\n  debug: false\n};\n\n// This is to be defensive in environments where window does not exist (see https://github.com/getsentry/raven-js/pull/785)\nvar _window =\n  typeof window !== 'undefined'\n    ? window\n    : typeof global !== 'undefined' ? global : typeof self !== 'undefined' ? self : {};\n\n// global reference to slice\nvar _slice = [].slice;\nvar UNKNOWN_FUNCTION = '?';\n\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error#Error_types\nvar ERROR_TYPES_RE = /^(?:[Uu]ncaught (?:exception: )?)?(?:((?:Eval|Internal|Range|Reference|Syntax|Type|URI|)Error): )?(.*)$/;\n\nfunction getLocationHref() {\n  if (typeof document === 'undefined' || document.location == null) return '';\n\n  return document.location.href;\n}\n\n/**\n * TraceKit.report: cross-browser processing of unhandled exceptions\n *\n * Syntax:\n *   TraceKit.report.subscribe(function(stackInfo) { ... })\n *   TraceKit.report.unsubscribe(function(stackInfo) { ... })\n *   TraceKit.report(exception)\n *   try { ...code... } catch(ex) { TraceKit.report(ex); }\n *\n * Supports:\n *   - Firefox: full stack trace with line numbers, plus column number\n *              on top frame; column number is not guaranteed\n *   - Opera:   full stack trace with line and column numbers\n *   - Chrome:  full stack trace with line and column numbers\n *   - Safari:  line and column number for the top frame only; some frames\n *              may be missing, and column number is not guaranteed\n *   - IE:      line and column number for the top frame only; some frames\n *              may be missing, and column number is not guaranteed\n *\n * In theory, TraceKit should work on all of the following versions:\n *   - IE5.5+ (only 8.0 tested)\n *   - Firefox 0.9+ (only 3.5+ tested)\n *   - Opera 7+ (only 10.50 tested; versions 9 and earlier may require\n *     Exceptions Have Stacktrace to be enabled in opera:config)\n *   - Safari 3+ (only 4+ tested)\n *   - Chrome 1+ (only 5+ tested)\n *   - Konqueror 3.5+ (untested)\n *\n * Requires TraceKit.computeStackTrace.\n *\n * Tries to catch all unhandled exceptions and report them to the\n * subscribed handlers. Please note that TraceKit.report will rethrow the\n * exception. This is REQUIRED in order to get a useful stack trace in IE.\n * If the exception does not reach the top of the browser, you will only\n * get a stack trace from the point where TraceKit.report was called.\n *\n * Handlers receive a stackInfo object as described in the\n * TraceKit.computeStackTrace docs.\n */\nTraceKit.report = (function reportModuleWrapper() {\n  var handlers = [],\n    lastArgs = null,\n    lastException = null,\n    lastExceptionStack = null;\n\n  /**\n     * Add a crash handler.\n     * @param {Function} handler\n     */\n  function subscribe(handler) {\n    installGlobalHandler();\n    handlers.push(handler);\n  }\n\n  /**\n     * Remove a crash handler.\n     * @param {Function} handler\n     */\n  function unsubscribe(handler) {\n    for (var i = handlers.length - 1; i >= 0; --i) {\n      if (handlers[i] === handler) {\n        handlers.splice(i, 1);\n      }\n    }\n  }\n\n  /**\n     * Remove all crash handlers.\n     */\n  function unsubscribeAll() {\n    uninstallGlobalHandler();\n    handlers = [];\n  }\n\n  /**\n     * Dispatch stack information to all handlers.\n     * @param {Object.<string, *>} stack\n     */\n  function notifyHandlers(stack, isWindowError) {\n    var exception = null;\n    if (isWindowError && !TraceKit.collectWindowErrors) {\n      return;\n    }\n    for (var i in handlers) {\n      if (handlers.hasOwnProperty(i)) {\n        try {\n          handlers[i].apply(null, [stack].concat(_slice.call(arguments, 2)));\n        } catch (inner) {\n          exception = inner;\n        }\n      }\n    }\n\n    if (exception) {\n      throw exception;\n    }\n  }\n\n  var _oldOnerrorHandler, _onErrorHandlerInstalled;\n\n  /**\n     * Ensures all global unhandled exceptions are recorded.\n     * Supported by Gecko and IE.\n     * @param {string} message Error message.\n     * @param {string} url URL of script that generated the exception.\n     * @param {(number|string)} lineNo The line number at which the error\n     * occurred.\n     * @param {?(number|string)} colNo The column number at which the error\n     * occurred.\n     * @param {?Error} ex The actual Error object.\n     */\n  function traceKitWindowOnError(message, url, lineNo, colNo, ex) {\n    var stack = null;\n\n    if (lastExceptionStack) {\n      TraceKit.computeStackTrace.augmentStackTraceWithInitialElement(\n        lastExceptionStack,\n        url,\n        lineNo,\n        message\n      );\n      processLastException();\n    } else if (ex && utils.isError(ex)) {\n      // non-string `ex` arg; attempt to extract stack trace\n\n      // New chrome and blink send along a real error object\n      // Let's just report that like a normal error.\n      // See: https://mikewest.org/2013/08/debugging-runtime-errors-with-window-onerror\n      stack = TraceKit.computeStackTrace(ex);\n      notifyHandlers(stack, true);\n    } else {\n      var location = {\n        url: url,\n        line: lineNo,\n        column: colNo\n      };\n\n      var name = undefined;\n      var msg = message; // must be new var or will modify original `arguments`\n      var groups;\n      if ({}.toString.call(message) === '[object String]') {\n        var groups = message.match(ERROR_TYPES_RE);\n        if (groups) {\n          name = groups[1];\n          msg = groups[2];\n        }\n      }\n\n      location.func = UNKNOWN_FUNCTION;\n\n      stack = {\n        name: name,\n        message: msg,\n        url: getLocationHref(),\n        stack: [location]\n      };\n      notifyHandlers(stack, true);\n    }\n\n    if (_oldOnerrorHandler) {\n      return _oldOnerrorHandler.apply(this, arguments);\n    }\n\n    return false;\n  }\n\n  function installGlobalHandler() {\n    if (_onErrorHandlerInstalled) {\n      return;\n    }\n    _oldOnerrorHandler = _window.onerror;\n    _window.onerror = traceKitWindowOnError;\n    _onErrorHandlerInstalled = true;\n  }\n\n  function uninstallGlobalHandler() {\n    if (!_onErrorHandlerInstalled) {\n      return;\n    }\n    _window.onerror = _oldOnerrorHandler;\n    _onErrorHandlerInstalled = false;\n    _oldOnerrorHandler = undefined;\n  }\n\n  function processLastException() {\n    var _lastExceptionStack = lastExceptionStack,\n      _lastArgs = lastArgs;\n    lastArgs = null;\n    lastExceptionStack = null;\n    lastException = null;\n    notifyHandlers.apply(null, [_lastExceptionStack, false].concat(_lastArgs));\n  }\n\n  /**\n     * Reports an unhandled Error to TraceKit.\n     * @param {Error} ex\n     * @param {?boolean} rethrow If false, do not re-throw the exception.\n     * Only used for window.onerror to not cause an infinite loop of\n     * rethrowing.\n     */\n  function report(ex, rethrow) {\n    var args = _slice.call(arguments, 1);\n    if (lastExceptionStack) {\n      if (lastException === ex) {\n        return; // already caught by an inner catch block, ignore\n      } else {\n        processLastException();\n      }\n    }\n\n    var stack = TraceKit.computeStackTrace(ex);\n    lastExceptionStack = stack;\n    lastException = ex;\n    lastArgs = args;\n\n    // If the stack trace is incomplete, wait for 2 seconds for\n    // slow slow IE to see if onerror occurs or not before reporting\n    // this exception; otherwise, we will end up with an incomplete\n    // stack trace\n    setTimeout(function() {\n      if (lastException === ex) {\n        processLastException();\n      }\n    }, stack.incomplete ? 2000 : 0);\n\n    if (rethrow !== false) {\n      throw ex; // re-throw to propagate to the top level (and cause window.onerror)\n    }\n  }\n\n  report.subscribe = subscribe;\n  report.unsubscribe = unsubscribe;\n  report.uninstall = unsubscribeAll;\n  return report;\n})();\n\n/**\n * TraceKit.computeStackTrace: cross-browser stack traces in JavaScript\n *\n * Syntax:\n *   s = TraceKit.computeStackTrace(exception) // consider using TraceKit.report instead (see below)\n * Returns:\n *   s.name              - exception name\n *   s.message           - exception message\n *   s.stack[i].url      - JavaScript or HTML file URL\n *   s.stack[i].func     - function name, or empty for anonymous functions (if guessing did not work)\n *   s.stack[i].args     - arguments passed to the function, if known\n *   s.stack[i].line     - line number, if known\n *   s.stack[i].column   - column number, if known\n *\n * Supports:\n *   - Firefox:  full stack trace with line numbers and unreliable column\n *               number on top frame\n *   - Opera 10: full stack trace with line and column numbers\n *   - Opera 9-: full stack trace with line numbers\n *   - Chrome:   full stack trace with line and column numbers\n *   - Safari:   line and column number for the topmost stacktrace element\n *               only\n *   - IE:       no line numbers whatsoever\n *\n * Tries to guess names of anonymous functions by looking for assignments\n * in the source code. In IE and Safari, we have to guess source file names\n * by searching for function bodies inside all page scripts. This will not\n * work for scripts that are loaded cross-domain.\n * Here be dragons: some function names may be guessed incorrectly, and\n * duplicate functions may be mismatched.\n *\n * TraceKit.computeStackTrace should only be used for tracing purposes.\n * Logging of unhandled exceptions should be done with TraceKit.report,\n * which builds on top of TraceKit.computeStackTrace and provides better\n * IE support by utilizing the window.onerror event to retrieve information\n * about the top of the stack.\n *\n * Note: In IE and Safari, no stack trace is recorded on the Error object,\n * so computeStackTrace instead walks its *own* chain of callers.\n * This means that:\n *  * in Safari, some methods may be missing from the stack trace;\n *  * in IE, the topmost function in the stack trace will always be the\n *    caller of computeStackTrace.\n *\n * This is okay for tracing (because you are likely to be calling\n * computeStackTrace from the function you want to be the topmost element\n * of the stack trace anyway), but not okay for logging unhandled\n * exceptions (because your catch block will likely be far away from the\n * inner function that actually caused the exception).\n *\n */\nTraceKit.computeStackTrace = (function computeStackTraceWrapper() {\n  // Contents of Exception in various browsers.\n  //\n  // SAFARI:\n  // ex.message = Can't find variable: qq\n  // ex.line = 59\n  // ex.sourceId = 580238192\n  // ex.sourceURL = http://...\n  // ex.expressionBeginOffset = 96\n  // ex.expressionCaretOffset = 98\n  // ex.expressionEndOffset = 98\n  // ex.name = ReferenceError\n  //\n  // FIREFOX:\n  // ex.message = qq is not defined\n  // ex.fileName = http://...\n  // ex.lineNumber = 59\n  // ex.columnNumber = 69\n  // ex.stack = ...stack trace... (see the example below)\n  // ex.name = ReferenceError\n  //\n  // CHROME:\n  // ex.message = qq is not defined\n  // ex.name = ReferenceError\n  // ex.type = not_defined\n  // ex.arguments = ['aa']\n  // ex.stack = ...stack trace...\n  //\n  // INTERNET EXPLORER:\n  // ex.message = ...\n  // ex.name = ReferenceError\n  //\n  // OPERA:\n  // ex.message = ...message... (see the example below)\n  // ex.name = ReferenceError\n  // ex.opera#sourceloc = 11  (pretty much useless, duplicates the info in ex.message)\n  // ex.stacktrace = n/a; see 'opera:config#UserPrefs|Exceptions Have Stacktrace'\n\n  /**\n     * Computes stack trace information from the stack property.\n     * Chrome and Gecko use this property.\n     * @param {Error} ex\n     * @return {?Object.<string, *>} Stack trace information.\n     */\n  function computeStackTraceFromStackProp(ex) {\n    if (typeof ex.stack === 'undefined' || !ex.stack) return;\n\n    var chrome = /^\\s*at (.*?) ?\\(((?:file|https?|blob|chrome-extension|native|eval|webpack|<anonymous>|[a-z]:|\\/).*?)(?::(\\d+))?(?::(\\d+))?\\)?\\s*$/i,\n      gecko = /^\\s*(.*?)(?:\\((.*?)\\))?(?:^|@)((?:file|https?|blob|chrome|webpack|resource|\\[native).*?|[^@]*bundle)(?::(\\d+))?(?::(\\d+))?\\s*$/i,\n      winjs = /^\\s*at (?:((?:\\[object object\\])?.+) )?\\(?((?:file|ms-appx|https?|webpack|blob):.*?):(\\d+)(?::(\\d+))?\\)?\\s*$/i,\n      // Used to additionally parse URL/line/column from eval frames\n      geckoEval = /(\\S+) line (\\d+)(?: > eval line \\d+)* > eval/i,\n      chromeEval = /\\((\\S*)(?::(\\d+))(?::(\\d+))\\)/,\n      lines = ex.stack.split('\\n'),\n      stack = [],\n      submatch,\n      parts,\n      element,\n      reference = /^(.*) is undefined$/.exec(ex.message);\n\n    for (var i = 0, j = lines.length; i < j; ++i) {\n      if ((parts = chrome.exec(lines[i]))) {\n        var isNative = parts[2] && parts[2].indexOf('native') === 0; // start of line\n        var isEval = parts[2] && parts[2].indexOf('eval') === 0; // start of line\n        if (isEval && (submatch = chromeEval.exec(parts[2]))) {\n          // throw out eval line/column and use top-most line/column number\n          parts[2] = submatch[1]; // url\n          parts[3] = submatch[2]; // line\n          parts[4] = submatch[3]; // column\n        }\n        element = {\n          url: !isNative ? parts[2] : null,\n          func: parts[1] || UNKNOWN_FUNCTION,\n          args: isNative ? [parts[2]] : [],\n          line: parts[3] ? +parts[3] : null,\n          column: parts[4] ? +parts[4] : null\n        };\n      } else if ((parts = winjs.exec(lines[i]))) {\n        element = {\n          url: parts[2],\n          func: parts[1] || UNKNOWN_FUNCTION,\n          args: [],\n          line: +parts[3],\n          column: parts[4] ? +parts[4] : null\n        };\n      } else if ((parts = gecko.exec(lines[i]))) {\n        var isEval = parts[3] && parts[3].indexOf(' > eval') > -1;\n        if (isEval && (submatch = geckoEval.exec(parts[3]))) {\n          // throw out eval line/column and use top-most line number\n          parts[3] = submatch[1];\n          parts[4] = submatch[2];\n          parts[5] = null; // no column when eval\n        } else if (i === 0 && !parts[5] && typeof ex.columnNumber !== 'undefined') {\n          // FireFox uses this awesome columnNumber property for its top frame\n          // Also note, Firefox's column number is 0-based and everything else expects 1-based,\n          // so adding 1\n          // NOTE: this hack doesn't work if top-most frame is eval\n          stack[0].column = ex.columnNumber + 1;\n        }\n        element = {\n          url: parts[3],\n          func: parts[1] || UNKNOWN_FUNCTION,\n          args: parts[2] ? parts[2].split(',') : [],\n          line: parts[4] ? +parts[4] : null,\n          column: parts[5] ? +parts[5] : null\n        };\n      } else {\n        continue;\n      }\n\n      if (!element.func && element.line) {\n        element.func = UNKNOWN_FUNCTION;\n      }\n\n      stack.push(element);\n    }\n\n    if (!stack.length) {\n      return null;\n    }\n\n    return {\n      name: ex.name,\n      message: ex.message,\n      url: getLocationHref(),\n      stack: stack\n    };\n  }\n\n  /**\n     * Adds information about the first frame to incomplete stack traces.\n     * Safari and IE require this to get complete data on the first frame.\n     * @param {Object.<string, *>} stackInfo Stack trace information from\n     * one of the compute* methods.\n     * @param {string} url The URL of the script that caused an error.\n     * @param {(number|string)} lineNo The line number of the script that\n     * caused an error.\n     * @param {string=} message The error generated by the browser, which\n     * hopefully contains the name of the object that caused the error.\n     * @return {boolean} Whether or not the stack information was\n     * augmented.\n     */\n  function augmentStackTraceWithInitialElement(stackInfo, url, lineNo, message) {\n    var initial = {\n      url: url,\n      line: lineNo\n    };\n\n    if (initial.url && initial.line) {\n      stackInfo.incomplete = false;\n\n      if (!initial.func) {\n        initial.func = UNKNOWN_FUNCTION;\n      }\n\n      if (stackInfo.stack.length > 0) {\n        if (stackInfo.stack[0].url === initial.url) {\n          if (stackInfo.stack[0].line === initial.line) {\n            return false; // already in stack trace\n          } else if (\n            !stackInfo.stack[0].line &&\n            stackInfo.stack[0].func === initial.func\n          ) {\n            stackInfo.stack[0].line = initial.line;\n            return false;\n          }\n        }\n      }\n\n      stackInfo.stack.unshift(initial);\n      stackInfo.partial = true;\n      return true;\n    } else {\n      stackInfo.incomplete = true;\n    }\n\n    return false;\n  }\n\n  /**\n     * Computes stack trace information by walking the arguments.caller\n     * chain at the time the exception occurred. This will cause earlier\n     * frames to be missed but is the only way to get any stack trace in\n     * Safari and IE. The top frame is restored by\n     * {@link augmentStackTraceWithInitialElement}.\n     * @param {Error} ex\n     * @return {?Object.<string, *>} Stack trace information.\n     */\n  function computeStackTraceByWalkingCallerChain(ex, depth) {\n    var functionName = /function\\s+([_$a-zA-Z\\xA0-\\uFFFF][_$a-zA-Z0-9\\xA0-\\uFFFF]*)?\\s*\\(/i,\n      stack = [],\n      funcs = {},\n      recursion = false,\n      parts,\n      item,\n      source;\n\n    for (\n      var curr = computeStackTraceByWalkingCallerChain.caller;\n      curr && !recursion;\n      curr = curr.caller\n    ) {\n      if (curr === computeStackTrace || curr === TraceKit.report) {\n        // console.log('skipping internal function');\n        continue;\n      }\n\n      item = {\n        url: null,\n        func: UNKNOWN_FUNCTION,\n        line: null,\n        column: null\n      };\n\n      if (curr.name) {\n        item.func = curr.name;\n      } else if ((parts = functionName.exec(curr.toString()))) {\n        item.func = parts[1];\n      }\n\n      if (typeof item.func === 'undefined') {\n        try {\n          item.func = parts.input.substring(0, parts.input.indexOf('{'));\n        } catch (e) {}\n      }\n\n      if (funcs['' + curr]) {\n        recursion = true;\n      } else {\n        funcs['' + curr] = true;\n      }\n\n      stack.push(item);\n    }\n\n    if (depth) {\n      // console.log('depth is ' + depth);\n      // console.log('stack is ' + stack.length);\n      stack.splice(0, depth);\n    }\n\n    var result = {\n      name: ex.name,\n      message: ex.message,\n      url: getLocationHref(),\n      stack: stack\n    };\n    augmentStackTraceWithInitialElement(\n      result,\n      ex.sourceURL || ex.fileName,\n      ex.line || ex.lineNumber,\n      ex.message || ex.description\n    );\n    return result;\n  }\n\n  /**\n     * Computes a stack trace for an exception.\n     * @param {Error} ex\n     * @param {(string|number)=} depth\n     */\n  function computeStackTrace(ex, depth) {\n    var stack = null;\n    depth = depth == null ? 0 : +depth;\n\n    try {\n      stack = computeStackTraceFromStackProp(ex);\n      if (stack) {\n        return stack;\n      }\n    } catch (e) {\n      if (TraceKit.debug) {\n        throw e;\n      }\n    }\n\n    try {\n      stack = computeStackTraceByWalkingCallerChain(ex, depth + 1);\n      if (stack) {\n        return stack;\n      }\n    } catch (e) {\n      if (TraceKit.debug) {\n        throw e;\n      }\n    }\n    return {\n      name: ex.name,\n      message: ex.message,\n      url: getLocationHref()\n    };\n  }\n\n  computeStackTrace.augmentStackTraceWithInitialElement = augmentStackTraceWithInitialElement;\n  computeStackTrace.computeStackTraceFromStackProp = computeStackTraceFromStackProp;\n\n  return computeStackTrace;\n})();\n\nmodule.exports = TraceKit;\n", "/*\n json-stringify-safe\n Like JSON.stringify, but doesn't throw on circular references.\n\n Originally forked from https://github.com/isaacs/json-stringify-safe\n version 5.0.1 on 3/8/2017 and modified to handle Errors serialization\n and IE8 compatibility. Tests for this are in test/vendor.\n\n ISC license: https://github.com/isaacs/json-stringify-safe/blob/master/LICENSE\n*/\n\nexports = module.exports = stringify;\nexports.getSerialize = serializer;\n\nfunction indexOf(haystack, needle) {\n  for (var i = 0; i < haystack.length; ++i) {\n    if (haystack[i] === needle) return i;\n  }\n  return -1;\n}\n\nfunction stringify(obj, replacer, spaces, cycleReplacer) {\n  return JSON.stringify(obj, serializer(replacer, cycleReplacer), spaces);\n}\n\n// https://github.com/ftlabs/js-abbreviate/blob/fa709e5f139e7770a71827b1893f22418097fbda/index.js#L95-L106\nfunction stringifyError(value) {\n  var err = {\n    // These properties are implemented as magical getters and don't show up in for in\n    stack: value.stack,\n    message: value.message,\n    name: value.name\n  };\n\n  for (var i in value) {\n    if (Object.prototype.hasOwnProperty.call(value, i)) {\n      err[i] = value[i];\n    }\n  }\n\n  return err;\n}\n\nfunction serializer(replacer, cycleReplacer) {\n  var stack = [];\n  var keys = [];\n\n  if (cycleReplacer == null) {\n    cycleReplacer = function(key, value) {\n      if (stack[0] === value) {\n        return '[Circular ~]';\n      }\n      return '[Circular ~.' + keys.slice(0, indexOf(stack, value)).join('.') + ']';\n    };\n  }\n\n  return function(key, value) {\n    if (stack.length > 0) {\n      var thisPos = indexOf(stack, this);\n      ~thisPos ? stack.splice(thisPos + 1) : stack.push(this);\n      ~thisPos ? keys.splice(thisPos, Infinity, key) : keys.push(key);\n\n      if (~indexOf(stack, value)) {\n        value = cycleReplacer.call(this, key, value);\n      }\n    } else {\n      stack.push(value);\n    }\n\n    return replacer == null\n      ? value instanceof Error ? stringifyError(value) : value\n      : replacer.call(this, key, value);\n  };\n}\n", "/**\n * @license React\n * react-jsx-runtime.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function() {\n'use strict';\n\nvar React = require('react');\n\n// ATTENTION\n// When adding new symbols to this file,\n// Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n// The Symbol used to tag the ReactElement-like types.\nvar REACT_ELEMENT_TYPE = Symbol.for('react.element');\nvar REACT_PORTAL_TYPE = Symbol.for('react.portal');\nvar REACT_FRAGMENT_TYPE = Symbol.for('react.fragment');\nvar REACT_STRICT_MODE_TYPE = Symbol.for('react.strict_mode');\nvar REACT_PROFILER_TYPE = Symbol.for('react.profiler');\nvar REACT_PROVIDER_TYPE = Symbol.for('react.provider');\nvar REACT_CONTEXT_TYPE = Symbol.for('react.context');\nvar REACT_FORWARD_REF_TYPE = Symbol.for('react.forward_ref');\nvar REACT_SUSPENSE_TYPE = Symbol.for('react.suspense');\nvar REACT_SUSPENSE_LIST_TYPE = Symbol.for('react.suspense_list');\nvar REACT_MEMO_TYPE = Symbol.for('react.memo');\nvar REACT_LAZY_TYPE = Symbol.for('react.lazy');\nvar REACT_OFFSCREEN_TYPE = Symbol.for('react.offscreen');\nvar MAYBE_ITERATOR_SYMBOL = Symbol.iterator;\nvar FAUX_ITERATOR_SYMBOL = '@@iterator';\nfunction getIteratorFn(maybeIterable) {\n  if (maybeIterable === null || typeof maybeIterable !== 'object') {\n    return null;\n  }\n\n  var maybeIterator = MAYBE_ITERATOR_SYMBOL && maybeIterable[MAYBE_ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL];\n\n  if (typeof maybeIterator === 'function') {\n    return maybeIterator;\n  }\n\n  return null;\n}\n\nvar ReactSharedInternals = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n\nfunction error(format) {\n  {\n    {\n      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        args[_key2 - 1] = arguments[_key2];\n      }\n\n      printWarning('error', format, args);\n    }\n  }\n}\n\nfunction printWarning(level, format, args) {\n  // When changing this logic, you might want to also\n  // update consoleWithStackDev.www.js as well.\n  {\n    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n    var stack = ReactDebugCurrentFrame.getStackAddendum();\n\n    if (stack !== '') {\n      format += '%s';\n      args = args.concat([stack]);\n    } // eslint-disable-next-line react-internal/safe-string-coercion\n\n\n    var argsWithFormat = args.map(function (item) {\n      return String(item);\n    }); // Careful: RN currently depends on this prefix\n\n    argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n    // breaks IE9: https://github.com/facebook/react/issues/13610\n    // eslint-disable-next-line react-internal/no-production-logging\n\n    Function.prototype.apply.call(console[level], console, argsWithFormat);\n  }\n}\n\n// -----------------------------------------------------------------------------\n\nvar enableScopeAPI = false; // Experimental Create Event Handle API.\nvar enableCacheElement = false;\nvar enableTransitionTracing = false; // No known bugs, but needs performance testing\n\nvar enableLegacyHidden = false; // Enables unstable_avoidThisFallback feature in Fiber\n// stuff. Intended to enable React core members to more easily debug scheduling\n// issues in DEV builds.\n\nvar enableDebugTracing = false; // Track which Fiber(s) schedule render work.\n\nvar REACT_MODULE_REFERENCE;\n\n{\n  REACT_MODULE_REFERENCE = Symbol.for('react.module.reference');\n}\n\nfunction isValidElementType(type) {\n  if (typeof type === 'string' || typeof type === 'function') {\n    return true;\n  } // Note: typeof might be other than 'symbol' or 'number' (e.g. if it's a polyfill).\n\n\n  if (type === REACT_FRAGMENT_TYPE || type === REACT_PROFILER_TYPE || enableDebugTracing  || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || enableLegacyHidden  || type === REACT_OFFSCREEN_TYPE || enableScopeAPI  || enableCacheElement  || enableTransitionTracing ) {\n    return true;\n  }\n\n  if (typeof type === 'object' && type !== null) {\n    if (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || // This needs to include all possible module reference object\n    // types supported by any Flight configuration anywhere since\n    // we don't know which Flight build this will end up being used\n    // with.\n    type.$$typeof === REACT_MODULE_REFERENCE || type.getModuleId !== undefined) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nfunction getWrappedName(outerType, innerType, wrapperName) {\n  var displayName = outerType.displayName;\n\n  if (displayName) {\n    return displayName;\n  }\n\n  var functionName = innerType.displayName || innerType.name || '';\n  return functionName !== '' ? wrapperName + \"(\" + functionName + \")\" : wrapperName;\n} // Keep in sync with react-reconciler/getComponentNameFromFiber\n\n\nfunction getContextName(type) {\n  return type.displayName || 'Context';\n} // Note that the reconciler package should generally prefer to use getComponentNameFromFiber() instead.\n\n\nfunction getComponentNameFromType(type) {\n  if (type == null) {\n    // Host root, text node or just invalid type.\n    return null;\n  }\n\n  {\n    if (typeof type.tag === 'number') {\n      error('Received an unexpected object in getComponentNameFromType(). ' + 'This is likely a bug in React. Please file an issue.');\n    }\n  }\n\n  if (typeof type === 'function') {\n    return type.displayName || type.name || null;\n  }\n\n  if (typeof type === 'string') {\n    return type;\n  }\n\n  switch (type) {\n    case REACT_FRAGMENT_TYPE:\n      return 'Fragment';\n\n    case REACT_PORTAL_TYPE:\n      return 'Portal';\n\n    case REACT_PROFILER_TYPE:\n      return 'Profiler';\n\n    case REACT_STRICT_MODE_TYPE:\n      return 'StrictMode';\n\n    case REACT_SUSPENSE_TYPE:\n      return 'Suspense';\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return 'SuspenseList';\n\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_CONTEXT_TYPE:\n        var context = type;\n        return getContextName(context) + '.Consumer';\n\n      case REACT_PROVIDER_TYPE:\n        var provider = type;\n        return getContextName(provider._context) + '.Provider';\n\n      case REACT_FORWARD_REF_TYPE:\n        return getWrappedName(type, type.render, 'ForwardRef');\n\n      case REACT_MEMO_TYPE:\n        var outerName = type.displayName || null;\n\n        if (outerName !== null) {\n          return outerName;\n        }\n\n        return getComponentNameFromType(type.type) || 'Memo';\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            return getComponentNameFromType(init(payload));\n          } catch (x) {\n            return null;\n          }\n        }\n\n      // eslint-disable-next-line no-fallthrough\n    }\n  }\n\n  return null;\n}\n\nvar assign = Object.assign;\n\n// Helpers to patch console.logs to avoid logging during side-effect free\n// replaying on render function. This currently only patches the object\n// lazily which won't cover if the log function was extracted eagerly.\n// We could also eagerly patch the method.\nvar disabledDepth = 0;\nvar prevLog;\nvar prevInfo;\nvar prevWarn;\nvar prevError;\nvar prevGroup;\nvar prevGroupCollapsed;\nvar prevGroupEnd;\n\nfunction disabledLog() {}\n\ndisabledLog.__reactDisabledLog = true;\nfunction disableLogs() {\n  {\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      prevLog = console.log;\n      prevInfo = console.info;\n      prevWarn = console.warn;\n      prevError = console.error;\n      prevGroup = console.group;\n      prevGroupCollapsed = console.groupCollapsed;\n      prevGroupEnd = console.groupEnd; // https://github.com/facebook/react/issues/19099\n\n      var props = {\n        configurable: true,\n        enumerable: true,\n        value: disabledLog,\n        writable: true\n      }; // $FlowFixMe Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        info: props,\n        log: props,\n        warn: props,\n        error: props,\n        group: props,\n        groupCollapsed: props,\n        groupEnd: props\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    disabledDepth++;\n  }\n}\nfunction reenableLogs() {\n  {\n    disabledDepth--;\n\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      var props = {\n        configurable: true,\n        enumerable: true,\n        writable: true\n      }; // $FlowFixMe Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        log: assign({}, props, {\n          value: prevLog\n        }),\n        info: assign({}, props, {\n          value: prevInfo\n        }),\n        warn: assign({}, props, {\n          value: prevWarn\n        }),\n        error: assign({}, props, {\n          value: prevError\n        }),\n        group: assign({}, props, {\n          value: prevGroup\n        }),\n        groupCollapsed: assign({}, props, {\n          value: prevGroupCollapsed\n        }),\n        groupEnd: assign({}, props, {\n          value: prevGroupEnd\n        })\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    if (disabledDepth < 0) {\n      error('disabledDepth fell below zero. ' + 'This is a bug in React. Please file an issue.');\n    }\n  }\n}\n\nvar ReactCurrentDispatcher = ReactSharedInternals.ReactCurrentDispatcher;\nvar prefix;\nfunction describeBuiltInComponentFrame(name, source, ownerFn) {\n  {\n    if (prefix === undefined) {\n      // Extract the VM specific prefix used by each line.\n      try {\n        throw Error();\n      } catch (x) {\n        var match = x.stack.trim().match(/\\n( *(at )?)/);\n        prefix = match && match[1] || '';\n      }\n    } // We use the prefix to ensure our stacks line up with native stack frames.\n\n\n    return '\\n' + prefix + name;\n  }\n}\nvar reentry = false;\nvar componentFrameCache;\n\n{\n  var PossiblyWeakMap = typeof WeakMap === 'function' ? WeakMap : Map;\n  componentFrameCache = new PossiblyWeakMap();\n}\n\nfunction describeNativeComponentFrame(fn, construct) {\n  // If something asked for a stack inside a fake render, it should get ignored.\n  if ( !fn || reentry) {\n    return '';\n  }\n\n  {\n    var frame = componentFrameCache.get(fn);\n\n    if (frame !== undefined) {\n      return frame;\n    }\n  }\n\n  var control;\n  reentry = true;\n  var previousPrepareStackTrace = Error.prepareStackTrace; // $FlowFixMe It does accept undefined.\n\n  Error.prepareStackTrace = undefined;\n  var previousDispatcher;\n\n  {\n    previousDispatcher = ReactCurrentDispatcher.current; // Set the dispatcher in DEV because this might be call in the render function\n    // for warnings.\n\n    ReactCurrentDispatcher.current = null;\n    disableLogs();\n  }\n\n  try {\n    // This should throw.\n    if (construct) {\n      // Something should be setting the props in the constructor.\n      var Fake = function () {\n        throw Error();\n      }; // $FlowFixMe\n\n\n      Object.defineProperty(Fake.prototype, 'props', {\n        set: function () {\n          // We use a throwing setter instead of frozen or non-writable props\n          // because that won't throw in a non-strict mode function.\n          throw Error();\n        }\n      });\n\n      if (typeof Reflect === 'object' && Reflect.construct) {\n        // We construct a different control for this case to include any extra\n        // frames added by the construct call.\n        try {\n          Reflect.construct(Fake, []);\n        } catch (x) {\n          control = x;\n        }\n\n        Reflect.construct(fn, [], Fake);\n      } else {\n        try {\n          Fake.call();\n        } catch (x) {\n          control = x;\n        }\n\n        fn.call(Fake.prototype);\n      }\n    } else {\n      try {\n        throw Error();\n      } catch (x) {\n        control = x;\n      }\n\n      fn();\n    }\n  } catch (sample) {\n    // This is inlined manually because closure doesn't do it for us.\n    if (sample && control && typeof sample.stack === 'string') {\n      // This extracts the first frame from the sample that isn't also in the control.\n      // Skipping one frame that we assume is the frame that calls the two.\n      var sampleLines = sample.stack.split('\\n');\n      var controlLines = control.stack.split('\\n');\n      var s = sampleLines.length - 1;\n      var c = controlLines.length - 1;\n\n      while (s >= 1 && c >= 0 && sampleLines[s] !== controlLines[c]) {\n        // We expect at least one stack frame to be shared.\n        // Typically this will be the root most one. However, stack frames may be\n        // cut off due to maximum stack limits. In this case, one maybe cut off\n        // earlier than the other. We assume that the sample is longer or the same\n        // and there for cut off earlier. So we should find the root most frame in\n        // the sample somewhere in the control.\n        c--;\n      }\n\n      for (; s >= 1 && c >= 0; s--, c--) {\n        // Next we find the first one that isn't the same which should be the\n        // frame that called our sample function and the control.\n        if (sampleLines[s] !== controlLines[c]) {\n          // In V8, the first line is describing the message but other VMs don't.\n          // If we're about to return the first line, and the control is also on the same\n          // line, that's a pretty good indicator that our sample threw at same line as\n          // the control. I.e. before we entered the sample frame. So we ignore this result.\n          // This can happen if you passed a class to function component, or non-function.\n          if (s !== 1 || c !== 1) {\n            do {\n              s--;\n              c--; // We may still have similar intermediate frames from the construct call.\n              // The next one that isn't the same should be our match though.\n\n              if (c < 0 || sampleLines[s] !== controlLines[c]) {\n                // V8 adds a \"new\" prefix for native classes. Let's remove it to make it prettier.\n                var _frame = '\\n' + sampleLines[s].replace(' at new ', ' at '); // If our component frame is labeled \"<anonymous>\"\n                // but we have a user-provided \"displayName\"\n                // splice it in to make the stack more readable.\n\n\n                if (fn.displayName && _frame.includes('<anonymous>')) {\n                  _frame = _frame.replace('<anonymous>', fn.displayName);\n                }\n\n                {\n                  if (typeof fn === 'function') {\n                    componentFrameCache.set(fn, _frame);\n                  }\n                } // Return the line we found.\n\n\n                return _frame;\n              }\n            } while (s >= 1 && c >= 0);\n          }\n\n          break;\n        }\n      }\n    }\n  } finally {\n    reentry = false;\n\n    {\n      ReactCurrentDispatcher.current = previousDispatcher;\n      reenableLogs();\n    }\n\n    Error.prepareStackTrace = previousPrepareStackTrace;\n  } // Fallback to just using the name if we couldn't make it throw.\n\n\n  var name = fn ? fn.displayName || fn.name : '';\n  var syntheticFrame = name ? describeBuiltInComponentFrame(name) : '';\n\n  {\n    if (typeof fn === 'function') {\n      componentFrameCache.set(fn, syntheticFrame);\n    }\n  }\n\n  return syntheticFrame;\n}\nfunction describeFunctionComponentFrame(fn, source, ownerFn) {\n  {\n    return describeNativeComponentFrame(fn, false);\n  }\n}\n\nfunction shouldConstruct(Component) {\n  var prototype = Component.prototype;\n  return !!(prototype && prototype.isReactComponent);\n}\n\nfunction describeUnknownElementTypeFrameInDEV(type, source, ownerFn) {\n\n  if (type == null) {\n    return '';\n  }\n\n  if (typeof type === 'function') {\n    {\n      return describeNativeComponentFrame(type, shouldConstruct(type));\n    }\n  }\n\n  if (typeof type === 'string') {\n    return describeBuiltInComponentFrame(type);\n  }\n\n  switch (type) {\n    case REACT_SUSPENSE_TYPE:\n      return describeBuiltInComponentFrame('Suspense');\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return describeBuiltInComponentFrame('SuspenseList');\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_FORWARD_REF_TYPE:\n        return describeFunctionComponentFrame(type.render);\n\n      case REACT_MEMO_TYPE:\n        // Memo may contain any component type so we recursively resolve it.\n        return describeUnknownElementTypeFrameInDEV(type.type, source, ownerFn);\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            // Lazy may contain any component type so we recursively resolve it.\n            return describeUnknownElementTypeFrameInDEV(init(payload), source, ownerFn);\n          } catch (x) {}\n        }\n    }\n  }\n\n  return '';\n}\n\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\nvar loggedTypeFailures = {};\nvar ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n\nfunction setCurrentlyValidatingElement(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n      ReactDebugCurrentFrame.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame.setExtraStackFrame(null);\n    }\n  }\n}\n\nfunction checkPropTypes(typeSpecs, values, location, componentName, element) {\n  {\n    // $FlowFixMe This is okay but Flow doesn't know it.\n    var has = Function.call.bind(hasOwnProperty);\n\n    for (var typeSpecName in typeSpecs) {\n      if (has(typeSpecs, typeSpecName)) {\n        var error$1 = void 0; // Prop type validation may throw. In case they do, we don't want to\n        // fail the render phase where it didn't fail before. So we log it.\n        // After these have been cleaned up, we'll let them throw.\n\n        try {\n          // This is intentionally an invariant that gets caught. It's the same\n          // behavior as without this statement except with a better message.\n          if (typeof typeSpecs[typeSpecName] !== 'function') {\n            // eslint-disable-next-line react-internal/prod-error-codes\n            var err = Error((componentName || 'React class') + ': ' + location + ' type `' + typeSpecName + '` is invalid; ' + 'it must be a function, usually from the `prop-types` package, but received `' + typeof typeSpecs[typeSpecName] + '`.' + 'This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.');\n            err.name = 'Invariant Violation';\n            throw err;\n          }\n\n          error$1 = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED');\n        } catch (ex) {\n          error$1 = ex;\n        }\n\n        if (error$1 && !(error$1 instanceof Error)) {\n          setCurrentlyValidatingElement(element);\n\n          error('%s: type specification of %s' + ' `%s` is invalid; the type checker ' + 'function must return `null` or an `Error` but returned a %s. ' + 'You may have forgotten to pass an argument to the type checker ' + 'creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and ' + 'shape all require an argument).', componentName || 'React class', location, typeSpecName, typeof error$1);\n\n          setCurrentlyValidatingElement(null);\n        }\n\n        if (error$1 instanceof Error && !(error$1.message in loggedTypeFailures)) {\n          // Only monitor this failure once because there tends to be a lot of the\n          // same error.\n          loggedTypeFailures[error$1.message] = true;\n          setCurrentlyValidatingElement(element);\n\n          error('Failed %s type: %s', location, error$1.message);\n\n          setCurrentlyValidatingElement(null);\n        }\n      }\n    }\n  }\n}\n\nvar isArrayImpl = Array.isArray; // eslint-disable-next-line no-redeclare\n\nfunction isArray(a) {\n  return isArrayImpl(a);\n}\n\n/*\n * The `'' + value` pattern (used in in perf-sensitive code) throws for Symbol\n * and Temporal.* types. See https://github.com/facebook/react/pull/22064.\n *\n * The functions in this module will throw an easier-to-understand,\n * easier-to-debug exception with a clear errors message message explaining the\n * problem. (Instead of a confusing exception thrown inside the implementation\n * of the `value` object).\n */\n// $FlowFixMe only called in DEV, so void return is not possible.\nfunction typeName(value) {\n  {\n    // toStringTag is needed for namespaced types like Temporal.Instant\n    var hasToStringTag = typeof Symbol === 'function' && Symbol.toStringTag;\n    var type = hasToStringTag && value[Symbol.toStringTag] || value.constructor.name || 'Object';\n    return type;\n  }\n} // $FlowFixMe only called in DEV, so void return is not possible.\n\n\nfunction willCoercionThrow(value) {\n  {\n    try {\n      testStringCoercion(value);\n      return false;\n    } catch (e) {\n      return true;\n    }\n  }\n}\n\nfunction testStringCoercion(value) {\n  // If you ended up here by following an exception call stack, here's what's\n  // happened: you supplied an object or symbol value to React (as a prop, key,\n  // DOM attribute, CSS property, string ref, etc.) and when React tried to\n  // coerce it to a string using `'' + value`, an exception was thrown.\n  //\n  // The most common types that will cause this exception are `Symbol` instances\n  // and Temporal objects like `Temporal.Instant`. But any object that has a\n  // `valueOf` or `[Symbol.toPrimitive]` method that throws will also cause this\n  // exception. (Library authors do this to prevent users from using built-in\n  // numeric operators like `+` or comparison operators like `>=` because custom\n  // methods are needed to perform accurate arithmetic or comparison.)\n  //\n  // To fix the problem, coerce this object or symbol value to a string before\n  // passing it to React. The most reliable way is usually `String(value)`.\n  //\n  // To find which value is throwing, check the browser or debugger console.\n  // Before this exception was thrown, there should be `console.error` output\n  // that shows the type (Symbol, Temporal.PlainDate, etc.) that caused the\n  // problem and how that type was used: key, atrribute, input value prop, etc.\n  // In most cases, this console output also shows the component and its\n  // ancestor components where the exception happened.\n  //\n  // eslint-disable-next-line react-internal/safe-string-coercion\n  return '' + value;\n}\nfunction checkKeyStringCoercion(value) {\n  {\n    if (willCoercionThrow(value)) {\n      error('The provided key is an unsupported type %s.' + ' This value must be coerced to a string before before using it here.', typeName(value));\n\n      return testStringCoercion(value); // throw (to help callers find troubleshooting comments)\n    }\n  }\n}\n\nvar ReactCurrentOwner = ReactSharedInternals.ReactCurrentOwner;\nvar RESERVED_PROPS = {\n  key: true,\n  ref: true,\n  __self: true,\n  __source: true\n};\nvar specialPropKeyWarningShown;\nvar specialPropRefWarningShown;\nvar didWarnAboutStringRefs;\n\n{\n  didWarnAboutStringRefs = {};\n}\n\nfunction hasValidRef(config) {\n  {\n    if (hasOwnProperty.call(config, 'ref')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'ref').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.ref !== undefined;\n}\n\nfunction hasValidKey(config) {\n  {\n    if (hasOwnProperty.call(config, 'key')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'key').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.key !== undefined;\n}\n\nfunction warnIfStringRefCannotBeAutoConverted(config, self) {\n  {\n    if (typeof config.ref === 'string' && ReactCurrentOwner.current && self && ReactCurrentOwner.current.stateNode !== self) {\n      var componentName = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n      if (!didWarnAboutStringRefs[componentName]) {\n        error('Component \"%s\" contains the string ref \"%s\". ' + 'Support for string refs will be removed in a future major release. ' + 'This case cannot be automatically converted to an arrow function. ' + 'We ask you to manually fix this case by using useRef() or createRef() instead. ' + 'Learn more about using refs safely here: ' + 'https://reactjs.org/link/strict-mode-string-ref', getComponentNameFromType(ReactCurrentOwner.current.type), config.ref);\n\n        didWarnAboutStringRefs[componentName] = true;\n      }\n    }\n  }\n}\n\nfunction defineKeyPropWarningGetter(props, displayName) {\n  {\n    var warnAboutAccessingKey = function () {\n      if (!specialPropKeyWarningShown) {\n        specialPropKeyWarningShown = true;\n\n        error('%s: `key` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    };\n\n    warnAboutAccessingKey.isReactWarning = true;\n    Object.defineProperty(props, 'key', {\n      get: warnAboutAccessingKey,\n      configurable: true\n    });\n  }\n}\n\nfunction defineRefPropWarningGetter(props, displayName) {\n  {\n    var warnAboutAccessingRef = function () {\n      if (!specialPropRefWarningShown) {\n        specialPropRefWarningShown = true;\n\n        error('%s: `ref` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    };\n\n    warnAboutAccessingRef.isReactWarning = true;\n    Object.defineProperty(props, 'ref', {\n      get: warnAboutAccessingRef,\n      configurable: true\n    });\n  }\n}\n/**\n * Factory method to create a new React element. This no longer adheres to\n * the class pattern, so do not use new to call it. Also, instanceof check\n * will not work. Instead test $$typeof field against Symbol.for('react.element') to check\n * if something is a React Element.\n *\n * @param {*} type\n * @param {*} props\n * @param {*} key\n * @param {string|object} ref\n * @param {*} owner\n * @param {*} self A *temporary* helper to detect places where `this` is\n * different from the `owner` when React.createElement is called, so that we\n * can warn. We want to get rid of owner and replace string `ref`s with arrow\n * functions, and as long as `this` and owner are the same, there will be no\n * change in behavior.\n * @param {*} source An annotation object (added by a transpiler or otherwise)\n * indicating filename, line number, and/or other information.\n * @internal\n */\n\n\nvar ReactElement = function (type, key, ref, self, source, owner, props) {\n  var element = {\n    // This tag allows us to uniquely identify this as a React Element\n    $$typeof: REACT_ELEMENT_TYPE,\n    // Built-in properties that belong on the element\n    type: type,\n    key: key,\n    ref: ref,\n    props: props,\n    // Record the component responsible for creating this element.\n    _owner: owner\n  };\n\n  {\n    // The validation flag is currently mutative. We put it on\n    // an external backing store so that we can freeze the whole object.\n    // This can be replaced with a WeakMap once they are implemented in\n    // commonly used development environments.\n    element._store = {}; // To make comparing ReactElements easier for testing purposes, we make\n    // the validation flag non-enumerable (where possible, which should\n    // include every environment we run tests in), so the test framework\n    // ignores it.\n\n    Object.defineProperty(element._store, 'validated', {\n      configurable: false,\n      enumerable: false,\n      writable: true,\n      value: false\n    }); // self and source are DEV only properties.\n\n    Object.defineProperty(element, '_self', {\n      configurable: false,\n      enumerable: false,\n      writable: false,\n      value: self\n    }); // Two elements created in two different places should be considered\n    // equal for testing purposes and therefore we hide it from enumeration.\n\n    Object.defineProperty(element, '_source', {\n      configurable: false,\n      enumerable: false,\n      writable: false,\n      value: source\n    });\n\n    if (Object.freeze) {\n      Object.freeze(element.props);\n      Object.freeze(element);\n    }\n  }\n\n  return element;\n};\n/**\n * https://github.com/reactjs/rfcs/pull/107\n * @param {*} type\n * @param {object} props\n * @param {string} key\n */\n\nfunction jsxDEV(type, config, maybeKey, source, self) {\n  {\n    var propName; // Reserved names are extracted\n\n    var props = {};\n    var key = null;\n    var ref = null; // Currently, key can be spread in as a prop. This causes a potential\n    // issue if key is also explicitly declared (ie. <div {...props} key=\"Hi\" />\n    // or <div key=\"Hi\" {...props} /> ). We want to deprecate key spread,\n    // but as an intermediary step, we will use jsxDEV for everything except\n    // <div {...props} key=\"Hi\" />, because we aren't currently able to tell if\n    // key is explicitly declared to be undefined or not.\n\n    if (maybeKey !== undefined) {\n      {\n        checkKeyStringCoercion(maybeKey);\n      }\n\n      key = '' + maybeKey;\n    }\n\n    if (hasValidKey(config)) {\n      {\n        checkKeyStringCoercion(config.key);\n      }\n\n      key = '' + config.key;\n    }\n\n    if (hasValidRef(config)) {\n      ref = config.ref;\n      warnIfStringRefCannotBeAutoConverted(config, self);\n    } // Remaining properties are added to a new props object\n\n\n    for (propName in config) {\n      if (hasOwnProperty.call(config, propName) && !RESERVED_PROPS.hasOwnProperty(propName)) {\n        props[propName] = config[propName];\n      }\n    } // Resolve default props\n\n\n    if (type && type.defaultProps) {\n      var defaultProps = type.defaultProps;\n\n      for (propName in defaultProps) {\n        if (props[propName] === undefined) {\n          props[propName] = defaultProps[propName];\n        }\n      }\n    }\n\n    if (key || ref) {\n      var displayName = typeof type === 'function' ? type.displayName || type.name || 'Unknown' : type;\n\n      if (key) {\n        defineKeyPropWarningGetter(props, displayName);\n      }\n\n      if (ref) {\n        defineRefPropWarningGetter(props, displayName);\n      }\n    }\n\n    return ReactElement(type, key, ref, self, source, ReactCurrentOwner.current, props);\n  }\n}\n\nvar ReactCurrentOwner$1 = ReactSharedInternals.ReactCurrentOwner;\nvar ReactDebugCurrentFrame$1 = ReactSharedInternals.ReactDebugCurrentFrame;\n\nfunction setCurrentlyValidatingElement$1(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n      ReactDebugCurrentFrame$1.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame$1.setExtraStackFrame(null);\n    }\n  }\n}\n\nvar propTypesMisspellWarningShown;\n\n{\n  propTypesMisspellWarningShown = false;\n}\n/**\n * Verifies the object is a ReactElement.\n * See https://reactjs.org/docs/react-api.html#isvalidelement\n * @param {?object} object\n * @return {boolean} True if `object` is a ReactElement.\n * @final\n */\n\n\nfunction isValidElement(object) {\n  {\n    return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n  }\n}\n\nfunction getDeclarationErrorAddendum() {\n  {\n    if (ReactCurrentOwner$1.current) {\n      var name = getComponentNameFromType(ReactCurrentOwner$1.current.type);\n\n      if (name) {\n        return '\\n\\nCheck the render method of `' + name + '`.';\n      }\n    }\n\n    return '';\n  }\n}\n\nfunction getSourceInfoErrorAddendum(source) {\n  {\n    if (source !== undefined) {\n      var fileName = source.fileName.replace(/^.*[\\\\\\/]/, '');\n      var lineNumber = source.lineNumber;\n      return '\\n\\nCheck your code at ' + fileName + ':' + lineNumber + '.';\n    }\n\n    return '';\n  }\n}\n/**\n * Warn if there's no key explicitly set on dynamic arrays of children or\n * object keys are not valid. This allows us to keep track of children between\n * updates.\n */\n\n\nvar ownerHasKeyUseWarning = {};\n\nfunction getCurrentComponentErrorInfo(parentType) {\n  {\n    var info = getDeclarationErrorAddendum();\n\n    if (!info) {\n      var parentName = typeof parentType === 'string' ? parentType : parentType.displayName || parentType.name;\n\n      if (parentName) {\n        info = \"\\n\\nCheck the top-level render call using <\" + parentName + \">.\";\n      }\n    }\n\n    return info;\n  }\n}\n/**\n * Warn if the element doesn't have an explicit key assigned to it.\n * This element is in an array. The array could grow and shrink or be\n * reordered. All children that haven't already been validated are required to\n * have a \"key\" property assigned to it. Error statuses are cached so a warning\n * will only be shown once.\n *\n * @internal\n * @param {ReactElement} element Element that requires a key.\n * @param {*} parentType element's parent's type.\n */\n\n\nfunction validateExplicitKey(element, parentType) {\n  {\n    if (!element._store || element._store.validated || element.key != null) {\n      return;\n    }\n\n    element._store.validated = true;\n    var currentComponentErrorInfo = getCurrentComponentErrorInfo(parentType);\n\n    if (ownerHasKeyUseWarning[currentComponentErrorInfo]) {\n      return;\n    }\n\n    ownerHasKeyUseWarning[currentComponentErrorInfo] = true; // Usually the current owner is the offender, but if it accepts children as a\n    // property, it may be the creator of the child that's responsible for\n    // assigning it a key.\n\n    var childOwner = '';\n\n    if (element && element._owner && element._owner !== ReactCurrentOwner$1.current) {\n      // Give the component that originally created this child.\n      childOwner = \" It was passed a child from \" + getComponentNameFromType(element._owner.type) + \".\";\n    }\n\n    setCurrentlyValidatingElement$1(element);\n\n    error('Each child in a list should have a unique \"key\" prop.' + '%s%s See https://reactjs.org/link/warning-keys for more information.', currentComponentErrorInfo, childOwner);\n\n    setCurrentlyValidatingElement$1(null);\n  }\n}\n/**\n * Ensure that every element either is passed in a static location, in an\n * array with an explicit keys property defined, or in an object literal\n * with valid key property.\n *\n * @internal\n * @param {ReactNode} node Statically passed child of any type.\n * @param {*} parentType node's parent's type.\n */\n\n\nfunction validateChildKeys(node, parentType) {\n  {\n    if (typeof node !== 'object') {\n      return;\n    }\n\n    if (isArray(node)) {\n      for (var i = 0; i < node.length; i++) {\n        var child = node[i];\n\n        if (isValidElement(child)) {\n          validateExplicitKey(child, parentType);\n        }\n      }\n    } else if (isValidElement(node)) {\n      // This element was passed in a valid location.\n      if (node._store) {\n        node._store.validated = true;\n      }\n    } else if (node) {\n      var iteratorFn = getIteratorFn(node);\n\n      if (typeof iteratorFn === 'function') {\n        // Entry iterators used to provide implicit keys,\n        // but now we print a separate warning for them later.\n        if (iteratorFn !== node.entries) {\n          var iterator = iteratorFn.call(node);\n          var step;\n\n          while (!(step = iterator.next()).done) {\n            if (isValidElement(step.value)) {\n              validateExplicitKey(step.value, parentType);\n            }\n          }\n        }\n      }\n    }\n  }\n}\n/**\n * Given an element, validate that its props follow the propTypes definition,\n * provided by the type.\n *\n * @param {ReactElement} element\n */\n\n\nfunction validatePropTypes(element) {\n  {\n    var type = element.type;\n\n    if (type === null || type === undefined || typeof type === 'string') {\n      return;\n    }\n\n    var propTypes;\n\n    if (typeof type === 'function') {\n      propTypes = type.propTypes;\n    } else if (typeof type === 'object' && (type.$$typeof === REACT_FORWARD_REF_TYPE || // Note: Memo only checks outer props here.\n    // Inner props are checked in the reconciler.\n    type.$$typeof === REACT_MEMO_TYPE)) {\n      propTypes = type.propTypes;\n    } else {\n      return;\n    }\n\n    if (propTypes) {\n      // Intentionally inside to avoid triggering lazy initializers:\n      var name = getComponentNameFromType(type);\n      checkPropTypes(propTypes, element.props, 'prop', name, element);\n    } else if (type.PropTypes !== undefined && !propTypesMisspellWarningShown) {\n      propTypesMisspellWarningShown = true; // Intentionally inside to avoid triggering lazy initializers:\n\n      var _name = getComponentNameFromType(type);\n\n      error('Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?', _name || 'Unknown');\n    }\n\n    if (typeof type.getDefaultProps === 'function' && !type.getDefaultProps.isReactClassApproved) {\n      error('getDefaultProps is only used on classic React.createClass ' + 'definitions. Use a static property named `defaultProps` instead.');\n    }\n  }\n}\n/**\n * Given a fragment, validate that it can only be provided with fragment props\n * @param {ReactElement} fragment\n */\n\n\nfunction validateFragmentProps(fragment) {\n  {\n    var keys = Object.keys(fragment.props);\n\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i];\n\n      if (key !== 'children' && key !== 'key') {\n        setCurrentlyValidatingElement$1(fragment);\n\n        error('Invalid prop `%s` supplied to `React.Fragment`. ' + 'React.Fragment can only have `key` and `children` props.', key);\n\n        setCurrentlyValidatingElement$1(null);\n        break;\n      }\n    }\n\n    if (fragment.ref !== null) {\n      setCurrentlyValidatingElement$1(fragment);\n\n      error('Invalid attribute `ref` supplied to `React.Fragment`.');\n\n      setCurrentlyValidatingElement$1(null);\n    }\n  }\n}\n\nvar didWarnAboutKeySpread = {};\nfunction jsxWithValidation(type, props, key, isStaticChildren, source, self) {\n  {\n    var validType = isValidElementType(type); // We warn in this case but don't throw. We expect the element creation to\n    // succeed and there will likely be errors in render.\n\n    if (!validType) {\n      var info = '';\n\n      if (type === undefined || typeof type === 'object' && type !== null && Object.keys(type).length === 0) {\n        info += ' You likely forgot to export your component from the file ' + \"it's defined in, or you might have mixed up default and named imports.\";\n      }\n\n      var sourceInfo = getSourceInfoErrorAddendum(source);\n\n      if (sourceInfo) {\n        info += sourceInfo;\n      } else {\n        info += getDeclarationErrorAddendum();\n      }\n\n      var typeString;\n\n      if (type === null) {\n        typeString = 'null';\n      } else if (isArray(type)) {\n        typeString = 'array';\n      } else if (type !== undefined && type.$$typeof === REACT_ELEMENT_TYPE) {\n        typeString = \"<\" + (getComponentNameFromType(type.type) || 'Unknown') + \" />\";\n        info = ' Did you accidentally export a JSX literal instead of a component?';\n      } else {\n        typeString = typeof type;\n      }\n\n      error('React.jsx: type is invalid -- expected a string (for ' + 'built-in components) or a class/function (for composite ' + 'components) but got: %s.%s', typeString, info);\n    }\n\n    var element = jsxDEV(type, props, key, source, self); // The result can be nullish if a mock or a custom function is used.\n    // TODO: Drop this when these are no longer allowed as the type argument.\n\n    if (element == null) {\n      return element;\n    } // Skip key warning if the type isn't valid since our key validation logic\n    // doesn't expect a non-string/function type and can throw confusing errors.\n    // We don't want exception behavior to differ between dev and prod.\n    // (Rendering will throw with a helpful message and as soon as the type is\n    // fixed, the key warnings will appear.)\n\n\n    if (validType) {\n      var children = props.children;\n\n      if (children !== undefined) {\n        if (isStaticChildren) {\n          if (isArray(children)) {\n            for (var i = 0; i < children.length; i++) {\n              validateChildKeys(children[i], type);\n            }\n\n            if (Object.freeze) {\n              Object.freeze(children);\n            }\n          } else {\n            error('React.jsx: Static children should always be an array. ' + 'You are likely explicitly calling React.jsxs or React.jsxDEV. ' + 'Use the Babel transform instead.');\n          }\n        } else {\n          validateChildKeys(children, type);\n        }\n      }\n    }\n\n    {\n      if (hasOwnProperty.call(props, 'key')) {\n        var componentName = getComponentNameFromType(type);\n        var keys = Object.keys(props).filter(function (k) {\n          return k !== 'key';\n        });\n        var beforeExample = keys.length > 0 ? '{key: someKey, ' + keys.join(': ..., ') + ': ...}' : '{key: someKey}';\n\n        if (!didWarnAboutKeySpread[componentName + beforeExample]) {\n          var afterExample = keys.length > 0 ? '{' + keys.join(': ..., ') + ': ...}' : '{}';\n\n          error('A props object containing a \"key\" prop is being spread into JSX:\\n' + '  let props = %s;\\n' + '  <%s {...props} />\\n' + 'React keys must be passed directly to JSX without using spread:\\n' + '  let props = %s;\\n' + '  <%s key={someKey} {...props} />', beforeExample, componentName, afterExample, componentName);\n\n          didWarnAboutKeySpread[componentName + beforeExample] = true;\n        }\n      }\n    }\n\n    if (type === REACT_FRAGMENT_TYPE) {\n      validateFragmentProps(element);\n    } else {\n      validatePropTypes(element);\n    }\n\n    return element;\n  }\n} // These two functions exist to still get child warnings in dev\n// even with the prod transform. This means that jsxDEV is purely\n// opt-in behavior for better messages but that we won't stop\n// giving you warnings if you use production apis.\n\nfunction jsxWithValidationStatic(type, props, key) {\n  {\n    return jsxWithValidation(type, props, key, true);\n  }\n}\nfunction jsxWithValidationDynamic(type, props, key) {\n  {\n    return jsxWithValidation(type, props, key, false);\n  }\n}\n\nvar jsx =  jsxWithValidationDynamic ; // we may want to special case jsxs internally to take advantage of static children.\n// for now we can ship identical prod functions\n\nvar jsxs =  jsxWithValidationStatic ;\n\nexports.Fragment = REACT_FRAGMENT_TYPE;\nexports.jsx = jsx;\nexports.jsxs = jsxs;\n  })();\n}\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-runtime.production.min.js');\n} else {\n  module.exports = require('./cjs/react-jsx-runtime.development.js');\n}\n", "module.exports = window[\"React\"];", "module.exports = window[\"ReactDOM\"];", "module.exports = window[\"jQuery\"];", "module.exports = window[\"wp\"][\"i18n\"];", "// src/styled.ts\nimport validAttr from \"@emotion/is-prop-valid\";\nimport React from \"react\";\nimport { cx } from \"@linaria/core\";\nvar isCapital = (ch) => ch.toUpperCase() === ch;\nvar filterKey = (keys) => (key) => keys.indexOf(key) === -1;\nvar omit = (obj, keys) => {\n  const res = {};\n  Object.keys(obj).filter(filterKey(keys)).forEach((key) => {\n    res[key] = obj[key];\n  });\n  return res;\n};\nfunction filterProps(asIs, props, omitKeys) {\n  const filteredProps = omit(props, omitKeys);\n  if (!asIs) {\n    const interopValidAttr = typeof validAttr === \"function\" ? { default: validAttr } : validAttr;\n    Object.keys(filteredProps).forEach((key) => {\n      if (!interopValidAttr.default(key)) {\n        delete filteredProps[key];\n      }\n    });\n  }\n  return filteredProps;\n}\nvar warnIfInvalid = (value, componentName) => {\n  if (process.env.NODE_ENV !== \"production\") {\n    if (typeof value === \"string\" || typeof value === \"number\" && isFinite(value)) {\n      return;\n    }\n    const stringified = typeof value === \"object\" ? JSON.stringify(value) : String(value);\n    console.warn(\n      `An interpolation evaluated to '${stringified}' in the component '${componentName}', which is probably a mistake. You should explicitly cast or transform the value to a string.`\n    );\n  }\n};\nvar idx = 0;\nfunction styled(tag) {\n  var _a;\n  let mockedClass = \"\";\n  if (process.env.NODE_ENV === \"test\") {\n    mockedClass += `mocked-styled-${idx++}`;\n    if ((_a = tag == null ? void 0 : tag.__linaria) == null ? void 0 : _a.className) {\n      mockedClass += ` ${tag.__linaria.className}`;\n    }\n  }\n  return (options) => {\n    if (process.env.NODE_ENV !== \"production\" && process.env.NODE_ENV !== \"test\") {\n      if (Array.isArray(options)) {\n        throw new Error(\n          'Using the \"styled\" tag in runtime is not supported. Make sure you have set up the Babel plugin correctly. See https://github.com/callstack/linaria#setup'\n        );\n      }\n    }\n    const render = (props, ref) => {\n      const { as: component = tag, class: className = mockedClass } = props;\n      const shouldKeepProps = options.propsAsIs === void 0 ? !(typeof component === \"string\" && component.indexOf(\"-\") === -1 && !isCapital(component[0])) : options.propsAsIs;\n      const filteredProps = filterProps(shouldKeepProps, props, [\n        \"as\",\n        \"class\"\n      ]);\n      filteredProps.ref = ref;\n      filteredProps.className = options.atomic ? cx(options.class, filteredProps.className || className) : cx(filteredProps.className || className, options.class);\n      const { vars } = options;\n      if (vars) {\n        const style = {};\n        for (const name in vars) {\n          const variable = vars[name];\n          const result = variable[0];\n          const unit = variable[1] || \"\";\n          const value = typeof result === \"function\" ? result(props) : result;\n          warnIfInvalid(value, options.name);\n          style[`--${name}`] = `${value}${unit}`;\n        }\n        const ownStyle = filteredProps.style || {};\n        const keys = Object.keys(ownStyle);\n        if (keys.length > 0) {\n          keys.forEach((key) => {\n            style[key] = ownStyle[key];\n          });\n        }\n        filteredProps.style = style;\n      }\n      if (tag.__linaria && tag !== component) {\n        filteredProps.as = component;\n        return React.createElement(tag, filteredProps);\n      }\n      return React.createElement(component, filteredProps);\n    };\n    const Result = React.forwardRef ? React.forwardRef(render) : (props) => {\n      const rest = omit(props, [\"innerRef\"]);\n      return render(rest, props.innerRef);\n    };\n    Result.displayName = options.name;\n    Result.__linaria = {\n      className: options.class || mockedClass,\n      extends: tag\n    };\n    return Result;\n  };\n}\nvar styled_default = process.env.NODE_ENV !== \"production\" ? new Proxy(styled, {\n  get(o, prop) {\n    return o(prop);\n  }\n}) : styled;\nexport {\n  styled_default as styled\n};\n//# sourceMappingURL=index.mjs.map", "// src/css.ts\nvar idx = 0;\nvar css = () => {\n  if (process.env.NODE_ENV === \"test\") {\n    return `mocked-css-${idx++}`;\n  }\n  throw new Error(\n    'Using the \"css\" tag in runtime is not supported. Make sure you have set up the Babel plugin correctly.'\n  );\n};\nvar css_default = css;\n\n// src/cx.ts\nvar cx = function cx2() {\n  const presentClassNames = Array.prototype.slice.call(arguments).filter(Boolean);\n  const atomicClasses = {};\n  const nonAtomicClasses = [];\n  presentClassNames.forEach((arg) => {\n    const individualClassNames = arg ? arg.split(\" \") : [];\n    individualClassNames.forEach((className) => {\n      if (className.startsWith(\"atm_\")) {\n        const [, keyHash] = className.split(\"_\");\n        atomicClasses[keyHash] = className;\n      } else {\n        nonAtomicClasses.push(className);\n      }\n    });\n  });\n  const result = [];\n  for (const keyHash in atomicClasses) {\n    if (Object.prototype.hasOwnProperty.call(atomicClasses, keyHash)) {\n      result.push(atomicClasses[keyHash]);\n    }\n  }\n  result.push(...nonAtomicClasses);\n  return result.join(\" \");\n};\nvar cx_default = cx;\nexport {\n  css_default as css,\n  cx_default as cx\n};\n//# sourceMappingURL=index.mjs.map", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "import { initAppOnReady } from '../utils/appUtils';\nimport renderIframeApp from '../iframe/renderIframeApp';\ninitAppOnReady(renderIframeApp);\n"], "names": ["$", "Raven", "restNonce", "restUrl", "addQueryObjectToUrl", "makeRequest", "method", "path", "data", "arguments", "length", "undefined", "queryParams", "restApiUrl", "URL", "concat", "Promise", "resolve", "reject", "payload", "url", "toString", "contentType", "beforeSend", "xhr", "setRequestHeader", "success", "error", "response", "captureMessage", "status", "responseText", "fingerprint", "JSON", "stringify", "ajax", "healthcheckRestApi", "disableInternalTracking", "value", "fetchDisableInternalTracking", "then", "message", "updateHublet", "hublet", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "trackConsent", "canTrack", "setBusinessUnitId", "businessUnitId", "getBusinessUnitId", "refreshProxyMappingsCache", "fetchProxyMappingsEnabled", "toggleProxyMappingsEnabled", "_window$leadinConfig", "window", "leadinConfig", "accountName", "adminUrl", "activationTime", "connectionStatus", "deviceId", "didDisconnect", "env", "formsScript", "meetingsScript", "formsScriptPayload", "hubspotBaseUrl", "hubspotNonce", "iframeUrl", "impactLink", "lastAuthorizeTime", "lastDeauthorizeTime", "lastDisconnectTime", "leadinPluginVersion", "leadinQueryParams", "locale", "loginUrl", "phpVersion", "pluginPath", "plugins", "portalDomain", "portalEmail", "portalId", "redirectNonce", "refreshToken", "reviewSkippedDate", "theme", "wpVersion", "contentEmbed", "requiresContentEmbedScope", "decryptError", "dom<PERSON><PERSON>s", "iframe", "subMenu", "subMenuLinks", "subMenuButtons", "deactivatePluginButton", "deactivateFeedbackForm", "deactivateFeedbackSubmit", "deactivateFeedbackSkip", "thickboxModalClose", "thickboxModalWindow", "thickboxModalContent", "reviewBannerContainer", "reviewBannerLeaveReviewLink", "reviewBannerDismissButton", "leadinIframeContainer", "leadinIframe", "leadinIframeFallbackContainer", "jsx", "_jsx", "jsxs", "_jsxs", "__", "styled", "Iframe<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name", "class", "propsAsIs", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "IframeErrorPage", "children", "alt", "width", "src", "App", "AppIframe", "_defineProperty", "Forms", "LiveChat", "Plugin", "PluginSettings", "Background", "CoreMessages", "Handshak<PERSON><PERSON><PERSON><PERSON><PERSON>", "SendRefreshToken", "ReloadParentFrame", "RedirectParentFrame", "SendLocale", "SendDeviceId", "SendIntegratedAppConfig", "FormMessages", "CreateFormAppNavigation", "LiveChatMessages", "CreateLiveChatAppNavigation", "PluginMessages", "PluginSettingsNavigation", "PluginLeadinConfig", "TrackConsent", "InternalTrackingFetchRequest", "InternalTrackingFetchResponse", "InternalTrackingFetchError", "InternalTrackingChangeRequest", "InternalTrackingChangeError", "BusinessUnitFetchRequest", "BusinessUnitFetchResponse", "BusinessUnitFetchError", "BusinessUnitChangeRequest", "BusinessUnitChangeError", "SkipReviewRequest", "SkipReviewResponse", "SkipReviewError", "RemoveParentQueryParam", "ContentEmbedInstallRequest", "ContentEmbedInstallResponse", "ContentEmbedInstallError", "ContentEmbedActivationRequest", "ContentEmbedActivationResponse", "ContentEmbedActivationError", "ProxyMappingsEnabledRequest", "ProxyMappingsEnabledResponse", "ProxyMappingsEnabledError", "ProxyMappingsEnabledChangeRequest", "ProxyMappingsEnabledChangeError", "RefreshProxyMappingsRequest", "RefreshProxyMappingsResponse", "RefreshProxyMappingsError", "ProxyMessages", "FetchForms", "FetchForm", "CreateFormFromTemplate", "GetTemplateAvailability", "FetchAuth", "FetchMeetingsAndUsers", "FetchContactsCreateSinceActivation", "FetchOrCreateMeetingUser", "ConnectMeetingsCalendar", "TrackFormPreviewRender", "TrackFormCreatedFromTemplate", "TrackFormCreationFailed", "TrackMeetingPreviewRender", "TrackSidebarMetaChange", "TrackReviewBannerRender", "TrackReviewBannerInteraction", "TrackReviewBannerDismissed", "TrackPluginDeactivation", "removeQueryParamFromLocation", "startActivation", "startInstall", "messageMapper", "Map", "embedder", "postMessage", "key", "__message", "_ref", "nonce", "activateAjaxUrl", "_ref2", "messageMiddleware", "next", "get", "Fragment", "ReactDOM", "useAppEmbedder", "IntegratedIframePortal", "props", "container", "document", "getElementById", "<PERSON><PERSON><PERSON><PERSON>ot<PERSON><PERSON><PERSON>", "app", "createRoute", "createPortal", "renderIframeApp", "iframeFallbackContainer", "URLSearchParams", "location", "search", "page", "render", "useEffect", "resizeWindow", "useIframeNotRendered", "getIntegrationConfig", "getLeadinConfig", "utm_query_params", "Object", "keys", "filter", "x", "test", "reduce", "p", "c", "_objectSpread", "admin", "company", "email", "firstName", "<PERSON><PERSON><PERSON><PERSON>", "justConnected", "lastName", "mpid", "websiteName", "getAppOptions", "_window", "IntegratedAppOptions", "FormsAppOptions", "LiveChatAppOptions", "PluginAppOptions", "options", "setPluginSettingsInit", "setIntegratedAppConfig", "setCreateFormAppInit", "setCreateLiveChatAppInit", "console", "info", "_window2", "IntegratedAppEmbedder", "setLocale", "setDeviceId", "setRefreshToken", "setLeadinConfig", "setOptions", "subscribe", "attachTo", "postStartAppMessage", "appName", "hasIntegratedAppEmbedder", "captureException", "Error", "extra", "hasRefreshToken", "configureRaven", "indexOf", "domain", "replace", "config", "instrument", "tryCatch", "shouldSendCallback", "culprit", "release", "install", "setTagsContext", "v", "php", "wordpress", "setExtraContext", "hub", "map", "join", "initApp", "initFn", "context", "initAppOnReady", "main", "formData", "FormData", "ajaxUrl", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "append", "fetch", "body", "keepalive", "res", "json", "requestUrl", "useState", "IFRAME_DISPLAY_TIMEOUT", "_useState", "_useState2", "_slicedToArray", "setIframeNotRendered", "timer", "setTimeout", "clearTimeout", "adminMenuWrap", "sideMenuHeight", "offsetHeight", "adminBar", "adminBarHeight", "offset", "innerHeight", "urlObject", "for<PERSON>ach", "searchParams", "href", "history", "replaceState"], "sourceRoot": ""}