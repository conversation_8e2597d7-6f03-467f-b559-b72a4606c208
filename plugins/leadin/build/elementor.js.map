{"version": 3, "file": "elementor.js", "mappings": ";;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAE8B;;;;;;;;;;;;;;;;;ACRS;;AAEvC;AACA,igIAAigI;;AAEjgI,iCAAiC,4DAAO;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEkC;;;;;;;;;;;;;;;;;;;ACfG;AACrC,IAAMC,UAAU,GAAG,OAAO;AAC1B,IAAMC,eAAe,GAAG,YAAY;AACpC,IAAMC,eAAe,GAAG,YAAY;AACpC,IAAMC,uBAAuB,GAAG,oBAAoB;AACpD,IAAMC,sBAAsB,GAAG,mBAAmB;AAClD,IAAMC,mBAAmB,GAAG,gBAAgB;AAC5C,IAAMC,kBAAkB,GAAG,eAAe;AACnC,IAAMC,eAAe,GAAG;EAC3BC,KAAK,EAAET,mDAAE,CAAC,WAAW,EAAE,QAAQ,CAAC;EAChCU,OAAO,EAAE,CACL;IAAED,KAAK,EAAET,mDAAE,CAAC,YAAY,EAAE,QAAQ,CAAC;IAAEW,KAAK,EAAEV;EAAW,CAAC,EACxD;IAAEQ,KAAK,EAAET,mDAAE,CAAC,iBAAiB,EAAE,QAAQ,CAAC;IAAEW,KAAK,EAAET;EAAgB,CAAC,EAClE;IAAEO,KAAK,EAAET,mDAAE,CAAC,iBAAiB,EAAE,QAAQ,CAAC;IAAEW,KAAK,EAAER;EAAgB,CAAC,EAClE;IACIM,KAAK,EAAET,mDAAE,CAAC,yBAAyB,EAAE,QAAQ,CAAC;IAC9CW,KAAK,EAAEP;EACX,CAAC,EACD;IACIK,KAAK,EAAET,mDAAE,CAAC,wBAAwB,EAAE,QAAQ,CAAC;IAC7CW,KAAK,EAAEN;EACX,CAAC,EACD;IAAEI,KAAK,EAAET,mDAAE,CAAC,qBAAqB,EAAE,QAAQ,CAAC;IAAEW,KAAK,EAAEL;EAAoB,CAAC,EAC1E;IAAEG,KAAK,EAAET,mDAAE,CAAC,oBAAoB,EAAE,QAAQ,CAAC;IAAEW,KAAK,EAAEJ;EAAmB,CAAC;AAEhF,CAAC;AACM,SAASK,aAAaA,CAACD,KAAK,EAAE;EACjC,OAAQA,KAAK,KAAKV,UAAU,IACxBU,KAAK,KAAKT,eAAe,IACzBS,KAAK,KAAKR,eAAe,IACzBQ,KAAK,KAAKP,uBAAuB,IACjCO,KAAK,KAAKN,sBAAsB,IAChCM,KAAK,KAAKL,mBAAmB,IAC7BK,KAAK,KAAKJ,kBAAkB;AACpC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClCA,IAAAM,oBAAA,GAAwiBC,MAAM,CAACC,YAAY;EAAnjBC,WAAW,GAAAH,oBAAA,CAAXG,WAAW;EAAEC,QAAQ,GAAAJ,oBAAA,CAARI,QAAQ;EAAEC,cAAc,GAAAL,oBAAA,CAAdK,cAAc;EAAEC,gBAAgB,GAAAN,oBAAA,CAAhBM,gBAAgB;EAAEC,QAAQ,GAAAP,oBAAA,CAARO,QAAQ;EAAEC,aAAa,GAAAR,oBAAA,CAAbQ,aAAa;EAAEC,GAAG,GAAAT,oBAAA,CAAHS,GAAG;EAAEC,WAAW,GAAAV,oBAAA,CAAXU,WAAW;EAAEC,cAAc,GAAAX,oBAAA,CAAdW,cAAc;EAAEC,kBAAkB,GAAAZ,oBAAA,CAAlBY,kBAAkB;EAAEC,MAAM,GAAAb,oBAAA,CAANa,MAAM;EAAEC,cAAc,GAAAd,oBAAA,CAAdc,cAAc;EAAEC,YAAY,GAAAf,oBAAA,CAAZe,YAAY;EAAEC,SAAS,GAAAhB,oBAAA,CAATgB,SAAS;EAAEC,UAAU,GAAAjB,oBAAA,CAAViB,UAAU;EAAEC,iBAAiB,GAAAlB,oBAAA,CAAjBkB,iBAAiB;EAAEC,mBAAmB,GAAAnB,oBAAA,CAAnBmB,mBAAmB;EAAEC,kBAAkB,GAAApB,oBAAA,CAAlBoB,kBAAkB;EAAEC,mBAAmB,GAAArB,oBAAA,CAAnBqB,mBAAmB;EAAEC,iBAAiB,GAAAtB,oBAAA,CAAjBsB,iBAAiB;EAAEC,MAAM,GAAAvB,oBAAA,CAANuB,MAAM;EAAEC,QAAQ,GAAAxB,oBAAA,CAARwB,QAAQ;EAAEC,UAAU,GAAAzB,oBAAA,CAAVyB,UAAU;EAAEC,UAAU,GAAA1B,oBAAA,CAAV0B,UAAU;EAAEC,OAAO,GAAA3B,oBAAA,CAAP2B,OAAO;EAAEC,YAAY,GAAA5B,oBAAA,CAAZ4B,YAAY;EAAEC,WAAW,GAAA7B,oBAAA,CAAX6B,WAAW;EAAEC,QAAQ,GAAA9B,oBAAA,CAAR8B,QAAQ;EAAEC,aAAa,GAAA/B,oBAAA,CAAb+B,aAAa;EAAEC,SAAS,GAAAhC,oBAAA,CAATgC,SAAS;EAAEC,OAAO,GAAAjC,oBAAA,CAAPiC,OAAO;EAAEC,YAAY,GAAAlC,oBAAA,CAAZkC,YAAY;EAAEC,iBAAiB,GAAAnC,oBAAA,CAAjBmC,iBAAiB;EAAEC,KAAK,GAAApC,oBAAA,CAALoC,KAAK;EAAEC,YAAY,GAAArC,oBAAA,CAAZqC,YAAY;EAAEC,SAAS,GAAAtC,oBAAA,CAATsC,SAAS;EAAEC,YAAY,GAAAvC,oBAAA,CAAZuC,YAAY;EAAEC,yBAAyB,GAAAxC,oBAAA,CAAzBwC,yBAAyB;EAAEC,YAAY,GAAAzC,oBAAA,CAAZyC,YAAY;;;;;;;;;;;;;;;;;;;;ACAlf;AAEA;AACX;AACtB,SAASI,mBAAmBA,CAAA,EAAG;EAC1C,OAAQF,sDAAI,CAACC,wDAAe,EAAE;IAAEE,QAAQ,EAAEH,sDAAI,CAAC,GAAG,EAAE;MAAEI,uBAAuB,EAAE;QACnEC,MAAM,EAAE7D,mDAAE,CAAC,2HAA2H,CAAC,CAClI8D,OAAO,CAAC,MAAM,EAAE,+EAA+E,CAAC,CAChGA,OAAO,CAAC,MAAM,EAAE,MAAM;MAC/B;IAAE,CAAC;EAAE,CAAC,CAAC;AACnB;;;;;;;;;;;;;;;;ACVgD;AAEjC,SAASL,eAAeA,CAAAM,IAAA,EAAkC;EAAA,IAAAC,SAAA,GAAAD,IAAA,CAA/BE,IAAI;IAAJA,IAAI,GAAAD,SAAA,cAAG,SAAS,GAAAA,SAAA;IAAEL,QAAQ,GAAAI,IAAA,CAARJ,QAAQ;EAChE,OAAQH,sDAAI,CAAC,KAAK,EAAE;IAAEU,SAAS,EAAE,2BAA2B;IAAEP,QAAQ,EAAEH,sDAAI,CAAC,KAAK,EAAE;MAAEU,SAAS,4EAAAC,MAAA,CAA4EF,IAAI,CAAE;MAAEN,QAAQ,EAAEA;IAAS,CAAC;EAAE,CAAC,CAAC;AAC/M;;;;;;;;;;;;;;;;;;;;;;;;;;ACJgD;AACR;AAExC,IAAMU,SAAS,gBAAGD,sDAAM;EAAAE,IAAA;EAAAC,SAAA;EAAAC,SAAA;AAAA,EAIvB;AACc,SAASC,eAAeA,OAA0B;EAAA,IAAvBd,QAAQ,GAAAI,IAAA,CAARJ,QAAQ;IAAKe;EACnD,OAAQlB,sDAAI,CAACa,SAAS,EAAE;IAAEH,SAAS,EAAE,0BAA0B;IAAEP,QAAQ,EAAEH,sDAAI,CAAC,QAAQ,EAAAmB,aAAA,CAAAA,aAAA;MAAIT,SAAS,EAAE,2CAA2C;MAAED,IAAI,EAAE;IAAQ,GAAKS,MAAM;MAAEf,QAAQ,EAAEA;IAAAA,EAAU;EAAE,CAAC,CAAC;AAC3M;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACV+D;AACvB;AAC8B;AACd;AACI;AACvB;AACyD;AACtD;AACkC;AACI;AAC9E,SAAS0B,mBAAmBA,CAAAtB,IAAA,EAA6B;EAAA,IAA1BuB,MAAM,GAAAvB,IAAA,CAANuB,MAAM;IAAEC,aAAa,GAAAxB,IAAA,CAAbwB,aAAa;EAChD,IAAAC,SAAA,GAAqCN,2DAAQ,CAAC,CAAC;IAAvCO,QAAQ,GAAAD,SAAA,CAARC,QAAQ;IAAEC,KAAK,GAAAF,SAAA,CAALE,KAAK;IAAEC,OAAO,GAAAH,SAAA,CAAPG,OAAO;EAChC,OAAOA,OAAO,GAAInC,sDAAI,CAAC,KAAK,EAAE;IAAEG,QAAQ,EAAEH,sDAAI,CAACuB,sEAAS,EAAE,CAAC,CAAC;EAAE,CAAC,CAAC,GAAIU,QAAQ,GAAIjC,sDAAI,CAACC,+DAAe,EAAE;IAAEQ,IAAI,EAAE,QAAQ;IAAEN,QAAQ,EAAE3D,mDAAE,CAAC,yDAAyD,EAAE,QAAQ;EAAE,CAAC,CAAC,GAAK6E,uDAAK,CAAC,QAAQ,EAAE;IAAElE,KAAK,EAAE2E,MAAM;IAAEM,QAAQ,EAAE,SAAVA,QAAQA,CAAEC,KAAK,EAAI;MAC7P,IAAMC,YAAY,GAAGJ,KAAK,CAACK,IAAI,CAAC,UAAAC,IAAI;QAAA,OAAIA,IAAI,CAACrF,KAAK,KAAKkF,KAAK,CAACI,MAAM,CAACtF,KAAK;MAAA,EAAC;MAC1E,IAAImF,YAAY,EAAE;QACdP,aAAa,CAAC;UACV5C,QAAQ,EAARA,6DAAQ;UACR2C,MAAM,EAAEQ,YAAY,CAACnF,KAAK;UAC1BuF,QAAQ,EAAEJ,YAAY,CAACrF,KAAK;UAC5B0F,YAAY,EAAEL,YAAY,CAACK;QAC/B,CAAC,CAAC;MACN;IACJ,CAAC;IAAExC,QAAQ,EAAE,CAACH,sDAAI,CAAC,QAAQ,EAAE;MAAE7C,KAAK,EAAE,EAAE;MAAEyF,QAAQ,EAAE,IAAI;MAAEC,QAAQ,EAAE,IAAI;MAAE1C,QAAQ,EAAE3D,mDAAE,CAAC,mBAAmB,EAAE,QAAQ;IAAE,CAAC,CAAC,EAAE0F,KAAK,CAACY,GAAG,CAAC,UAAAN,IAAI;MAAA,OAAKxC,sDAAI,CAAC,QAAQ,EAAE;QAAE7C,KAAK,EAAEqF,IAAI,CAACrF,KAAK;QAAEgD,QAAQ,EAAEqC,IAAI,CAACvF;MAAM,CAAC,EAAEuF,IAAI,CAACrF,KAAK,CAAC;IAAA,CAAC,CAAC;EAAE,CAAC,CAAE;AACnO;AACA,SAAS4F,0BAA0BA,CAACC,KAAK,EAAE;EACvC,IAAMC,oBAAoB,GAAGxB,iFAAuB,CAAC,CAAC;EACtD,OAAQzB,sDAAI,CAACsB,2CAAQ,EAAE;IAAEnB,QAAQ,EAAE,CAAC8C,oBAAoB,GAAIjD,sDAAI,CAAC,KAAK,EAAE;MAAEG,QAAQ,EAAEH,sDAAI,CAACuB,sEAAS,EAAE,CAAC,CAAC;IAAE,CAAC,CAAC,GAAKvB,sDAAI,CAAC6B,mBAAmB,EAAAV,aAAA,KAAO6B,KAAK,CAAE;EAAG,CAAC,CAAC;AAC9J;AACe,SAASE,4BAA4BA,CAACF,KAAK,EAAE;EACxD,OAAQhD,sDAAI,CAACwB,kFAA4B,EAAE;IAAErE,KAAK,EAAEyE,uFAAuB,CAAC,CAAC,IAAID,mFAAwB,CAACpC,iEAAY,CAAC;IAAEY,QAAQ,EAAEH,sDAAI,CAAC+C,0BAA0B,EAAA5B,aAAA,KAAO6B,KAAK,CAAE;EAAE,CAAC,CAAC;AACxL;;;;;;;;;;;;;;;;;;;;;AC9BgD;AACR;AACwB;AACA;AACR;AACxD,IAAMI,gBAAgB,GAAG;EACrBC,SAAS,EAAE,WAAW;EACtBC,YAAY,EAAE;AAClB,CAAC;AACc,SAASC,qBAAqBA,CAACC,UAAU,EAAEC,QAAQ,EAAE;EAChE,OAAO,YAAM;IACT,IAAMC,MAAM,GAAG,SAATA,MAAMA,CAAA,EAAS;MACjB,IAAI/F,qEAAgB,KAAKyF,gBAAgB,CAACC,SAAS,EAAE;QACjD,OAAQrD,sDAAI,CAAC6B,4DAAmB,EAAE;UAAEC,MAAM,EAAE0B,UAAU,CAAC1B,MAAM;UAAEC,aAAa,EAAE0B;QAAS,CAAC,CAAC;MAC7F,CAAC,MACI;QACD,OAAOzD,sDAAI,CAACE,mEAAmB,EAAE,CAAC,CAAC,CAAC;MACxC;IACJ,CAAC;IACD,OAAOF,sDAAI,CAACsB,2CAAQ,EAAE;MAAEnB,QAAQ,EAAEuD,MAAM,CAAC;IAAE,CAAC,CAAC;EACjD,CAAC;AACL;;;;;;;;;;;;;;;;;;;;;;ACrBgD;AACR;AACwB;AACJ;AACV;AACiB;AACpD,SAASG,oBAAoBA,CAACL,UAAU,EAAEC,QAAQ,EAAE;EAC/D,OAAO,YAAM;IACT,IAAMC,MAAM,GAAG,SAATA,MAAMA,CAAA,EAAS;MACjB,IAAI/F,qEAAgB,KAAKyF,gFAA0B,EAAE;QACjD,OAAQpD,sDAAI,CAAC4D,6DAAQ,EAAE;UAAEJ,UAAU,EAAEA,UAAU;UAAEM,UAAU,EAAE,IAAI;UAAE/B,aAAa,EAAE0B,QAAQ;UAAEM,OAAO,EAAE,KAAK;UAAEC,MAAM,EAAE;QAAY,CAAC,CAAC;MACtI,CAAC,MACI;QACD,OAAOhE,sDAAI,CAAC2D,mEAAY,EAAE;UAAEM,MAAM,EAAE;QAAI,CAAC,CAAC;MAC9C;IACJ,CAAC;IACD,OAAOjE,sDAAI,CAACsB,2CAAQ,EAAE;MAAEnB,QAAQ,EAAEuD,MAAM,CAAC;IAAE,CAAC,CAAC;EACjD,CAAC;AACL;;;;;;;;;;;;;;;;;;;;;;;;;;AClB4C;AACY;AACW;AACc;AAClE,SAAShC,QAAQA,CAAA,EAAG;EAC/B,IAAM6C,KAAK,GAAGD,uFAA6B,CAAC,CAAC;EAC7C,IAAAE,SAAA,GAAkCN,+CAAQ,CAACE,yEAAmB,CAAC;IAAAM,UAAA,GAAAC,cAAA,CAAAH,SAAA;IAAxDI,SAAS,GAAAF,UAAA;IAAEG,YAAY,GAAAH,UAAA;EAC9B,IAAAI,UAAA,GAA6BZ,+CAAQ,CAAC,IAAI,CAAC;IAAAa,UAAA,GAAAJ,cAAA,CAAAG,UAAA;IAApC7C,QAAQ,GAAA8C,UAAA;IAAEC,QAAQ,GAAAD,UAAA;EACzB,IAAAE,UAAA,GAA0Bf,+CAAQ,CAAC,EAAE,CAAC;IAAAgB,UAAA,GAAAP,cAAA,CAAAM,UAAA;IAA/B/C,KAAK,GAAAgD,UAAA;IAAEC,QAAQ,GAAAD,UAAA;EACtBf,gDAAS,CAAC,YAAM;IACZ,IAAIS,SAAS,KAAKR,yEAAmB,EAAE;MACnCG,KAAK,CAAC;QACFa,GAAG,EAAEf,gFAAwB;QAC7BiB,OAAO,EAAE;UACLC,MAAM,EAAE;QACZ;MACJ,CAAC,CAAC,CACGC,IAAI,CAAC,UAAAC,IAAI,EAAI;QACdN,QAAQ,CAACM,IAAI,CAAC3C,GAAG,CAAC,UAACN,IAAI;UAAA,OAAM;YACzBvF,KAAK,EAAEuF,IAAI,CAAC1B,IAAI;YAChB3D,KAAK,EAAEqF,IAAI,CAACkD,IAAI;YAChB/C,YAAY,EAAEH,IAAI,CAACG;UACvB,CAAC;QAAA,CAAC,CAAC,CAAC;QACJkC,YAAY,CAACT,sEAAgB,CAAC;MAClC,CAAC,CAAC,SACQ,CAAC,UAAAwB,KAAK,EAAI;QAChBZ,QAAQ,CAACY,KAAK,CAAC;QACff,YAAY,CAACT,sEAAgB,CAAC;MAClC,CAAC,CAAC;IACN;EACJ,CAAC,EAAE,CAACQ,SAAS,CAAC,CAAC;EACf,OAAO;IAAE1C,KAAK,EAALA,KAAK;IAAEC,OAAO,EAAEyC,SAAS,KAAKR,uEAAiB;IAAEnC,QAAQ,EAARA;EAAS,CAAC;AACxE;;;;;;;;;;;;;;;;;;;;;;;;;;AChCiC;AAC2B;AACF;AAAA,IACrC+D,kBAAkB;EAKnC,SAAAA,mBAAYC,gBAAgB,EAAEC,eAAe,EAAEzC,QAAQ,EAAE;IAAA0C,eAAA,OAAAH,kBAAA;IAAAI,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IACrD,IAAM5C,UAAU,GAAG0C,eAAe,CAACG,OAAO,CAAC7C,UAAU,GAC/C8C,IAAI,CAACC,KAAK,CAACL,eAAe,CAACG,OAAO,CAAC7C,UAAU,CAAC,GAC9C,CAAC,CAAC;IACR,IAAI,CAAC0C,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACD,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACxC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACD,UAAU,GAAGA,UAAU;EAChC;EAAC,OAAAgD,YAAA,CAAAR,kBAAA;IAAAZ,GAAA;IAAAjI,KAAA,EACD,SAAAuG,MAAMA,CAAA,EAAG;MACLqC,uDAAe,CAAClC,iEAAoB,CAAC,IAAI,CAACL,UAAU,EAAE,IAAI,CAACC,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAACyC,eAAe,CAAC;MAC7FH,uDAAe,CAACxC,kEAAqB,CAAC,IAAI,CAACC,UAAU,EAAE,IAAI,CAACC,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAACwC,gBAAgB,CAAC;IACnG;EAAC;IAAAb,GAAA;IAAAjI,KAAA,EACD,SAAAsJ,IAAIA,CAAA,EAAG;MACHV,uEAA+B,CAAC,IAAI,CAACG,eAAe,CAAC;MACrDH,uEAA+B,CAAC,IAAI,CAACE,gBAAgB,CAAC;IAC1D;EAAC;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxB0D;AACb;AACM;AACI;AACI;AACkC;AAC7D;AACR;AACiE;AAClC;AACc;AACI;AAC9E,SAASe,sBAAsBA,CAAAzG,IAAA,EAA0B;EAAA,IAAvB0G,GAAG,GAAA1G,IAAA,CAAH0G,GAAG;IAAElF,aAAa,GAAAxB,IAAA,CAAbwB,aAAa;EAChD,IAAAmF,YAAA,GAA+EL,6EAAW,CAAC,CAAC;IAApEM,QAAQ,GAAAD,YAAA,CAAxBE,cAAc;IAAYjF,OAAO,GAAA+E,YAAA,CAAP/E,OAAO;IAAEyD,KAAK,GAAAsB,YAAA,CAALtB,KAAK;IAAEyB,MAAM,GAAAH,YAAA,CAANG,MAAM;IAAEC,eAAe,GAAAJ,YAAA,CAAfI,eAAe;EACzE,IAAMC,uBAAuB,GAAGT,6FAA0B,CAACG,GAAG,CAAC;EAC/D,IAAAzC,SAAA,GAAgCN,+CAAQ,CAAC+C,GAAG,CAAC;IAAAvC,UAAA,GAAAC,cAAA,CAAAH,SAAA;IAAtCgD,QAAQ,GAAA9C,UAAA;IAAE+C,WAAW,GAAA/C,UAAA;EAC5B,IAAMgD,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAA,EAAS;IAChC,OAAOJ,eAAe,CAAC,CAAC,CACnB9B,IAAI,CAAC,YAAM;MACZ6B,MAAM,CAAC,CAAC;IACZ,CAAC,CAAC,SACQ,CAAC,UAAAzB,KAAK,EAAI;MAChBmB,8DAAoB,CAAC,4BAA4B,EAAE;QAC/Ca,KAAK,EAAE;UAAEhC,KAAK,EAALA;QAAM;MACnB,CAAC,CAAC;IACN,CAAC,CAAC;EACN,CAAC;EACD,OAAQ5F,sDAAI,CAACsB,2CAAQ,EAAE;IAAEnB,QAAQ,EAAEgC,OAAO,GAAInC,sDAAI,CAAC,KAAK,EAAE;MAAEG,QAAQ,EAAEH,sDAAI,CAACuB,sEAAS,EAAE,CAAC,CAAC;IAAE,CAAC,CAAC,GAAIqE,KAAK,GAAI5F,sDAAI,CAACC,+DAAe,EAAE;MAAEQ,IAAI,EAAE,QAAQ;MAAEN,QAAQ,EAAE3D,mDAAE,CAAC,4DAA4D,EAAE,QAAQ;IAAE,CAAC,CAAC,GAAK6E,uDAAK,CAACC,2CAAQ,EAAE;MAAEnB,QAAQ,EAAE,CAACoH,uBAAuB,IAAKvH,sDAAI,CAAC4G,gEAAuB,EAAE;QAAE3C,MAAM,EAAEsD,uBAAuB;QAAEM,iBAAiB,EAAEP;MAAgB,CAAC,CAAE,EAAEH,QAAQ,CAACW,MAAM,GAAG,CAAC,IAAKzG,uDAAK,CAAC,QAAQ,EAAE;QAAElE,KAAK,EAAEqK,QAAQ;QAAEpF,QAAQ,EAAE,SAAVA,QAAQA,CAAEC,KAAK,EAAI;UACzc,IAAM0F,MAAM,GAAG1F,KAAK,CAACI,MAAM,CAACtF,KAAK;UACjCsK,WAAW,CAACM,MAAM,CAAC;UACnBhG,aAAa,CAAC;YACVkF,GAAG,EAAEc;UACT,CAAC,CAAC;QACN,CAAC;QAAE5H,QAAQ,EAAE,CAACH,sDAAI,CAAC,QAAQ,EAAE;UAAE7C,KAAK,EAAE,EAAE;UAAEyF,QAAQ,EAAE,IAAI;UAAEC,QAAQ,EAAE,IAAI;UAAE1C,QAAQ,EAAE3D,mDAAE,CAAC,kBAAkB,EAAE,QAAQ;QAAE,CAAC,CAAC,EAAE2K,QAAQ,CAACrE,GAAG,CAAC,UAAAkF,IAAI;UAAA,OAAKhI,sDAAI,CAAC,QAAQ,EAAE;YAAE7C,KAAK,EAAE6K,IAAI,CAAC7K,KAAK;YAAEgD,QAAQ,EAAE6H,IAAI,CAAC/K;UAAM,CAAC,EAAE+K,IAAI,CAAC7K,KAAK,CAAC;QAAA,CAAC,CAAC;MAAE,CAAC,CAAE;IAAE,CAAC;EAAG,CAAC,CAAC;AACzP;AACA,SAAS8K,6BAA6BA,CAACjF,KAAK,EAAE;EAC1C,IAAMC,oBAAoB,GAAGxB,iFAAuB,CAAC,CAAC;EACtD,OAAQzB,sDAAI,CAACsB,2CAAQ,EAAE;IAAEnB,QAAQ,EAAE,CAAC8C,oBAAoB,GAAIjD,sDAAI,CAAC,KAAK,EAAE;MAAEG,QAAQ,EAAEH,sDAAI,CAACuB,sEAAS,EAAE,CAAC,CAAC;IAAE,CAAC,CAAC,GAAKvB,sDAAI,CAACgH,sBAAsB,EAAA7F,aAAA,KAAO6B,KAAK,CAAE;EAAG,CAAC,CAAC;AACjK;AACe,SAASkF,gCAAgCA,CAAClF,KAAK,EAAE;EAC5D,OAAQhD,sDAAI,CAACwB,kFAA4B,EAAE;IAAErE,KAAK,EAAEyE,wFAAuB,CAAC,CAAC,IAAID,oFAAwB,CAACpC,iEAAY,CAAC;IAAEY,QAAQ,EAAEH,sDAAI,CAACiI,6BAA6B,EAAA9G,aAAA,KAAO6B,KAAK,CAAE;EAAE,CAAC,CAAC;AAC3L;;;;;;;;;;;;;;;;;;;;;;;;ACzC+D;AACvB;AACuC;AACvB;AACA;AAChB;AACH;AACrC,IAAMnC,SAAS,gBAAGD,sDAAM;EAAAE,IAAA;EAAAC,SAAA;EAAAC,SAAA;AAAA,EAEvB;AACc,SAASoH,cAAcA,OAAiC;EAAA,IAA9BP,iBAAiB,GAAAtH,IAAA,CAAjBsH,iBAAiB;IAAE5D;EACxD,IAAMoE,cAAc,GAAGpE,MAAM,KAAKkE,oFAA6B;EAC/D,IAAMG,SAAS,GAAGD,cAAc,GAC1B7L,mDAAE,CAAC,gCAAgC,EAAE,QAAQ,CAAC,GAC9CA,mDAAE,CAAC,2BAA2B,EAAE,QAAQ,CAAC;EAC/C,IAAM+L,YAAY,GAAGF,cAAc,GAC7B7L,mDAAE,CAAC,gEAAgE,EAAE,QAAQ,CAAC,GAC9EA,mDAAE,CAAC,yGAAyG,EAAE,QAAQ,CAAC;EAC7H,OAAQ6E,uDAAK,CAACC,2CAAQ,EAAE;IAAEnB,QAAQ,EAAE,CAACH,sDAAI,CAACa,SAAS,EAAE;MAAEV,QAAQ,EAAEkB,uDAAK,CAACpB,+DAAe,EAAE;QAAEQ,IAAI,EAAE,SAAS;QAAEN,QAAQ,EAAE,CAACH,sDAAI,CAAC,GAAG,EAAE;UAAEG,QAAQ,EAAEmI;QAAU,CAAC,CAAC,EAAEtI,sDAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAEuI,YAAY;MAAE,CAAC;IAAE,CAAC,CAAC,EAAEF,cAAc,IAAKrI,sDAAI,CAACiB,+DAAe,EAAE;MAAEuH,EAAE,EAAE,2BAA2B;MAAEC,OAAO,EAAEZ,iBAAiB;MAAE1H,QAAQ,EAAE3D,mDAAE,CAAC,kBAAkB,EAAE,QAAQ;IAAE,CAAC,CAAE;EAAE,CAAC,CAAC;AAC7V;;;;;;;;;;;;;;;;;;;;;;ACnBgD;AACR;AACwB;AACA;AACF;AAC9D,IAAM4G,gBAAgB,GAAG;EACrBC,SAAS,EAAE,WAAW;EACtBC,YAAY,EAAE;AAClB,CAAC;AACc,SAASoF,wBAAwBA,CAAClF,UAAU,EAAEC,QAAQ,EAAE;EACnE,OAAO,YAAM;IACT,IAAMC,MAAM,GAAG,SAATA,MAAMA,CAAA,EAAS;MACjB,IAAI/F,qEAAgB,KAAKyF,gBAAgB,CAACC,SAAS,EAAE;QACjD,OAAQrD,sDAAI,CAACgH,+DAAsB,EAAE;UAAEC,GAAG,EAAEzD,UAAU,CAACyD,GAAG;UAAElF,aAAa,EAAE0B;QAAS,CAAC,CAAC;MAC1F,CAAC,MACI;QACD,OAAOzD,sDAAI,CAACE,mEAAmB,EAAE,CAAC,CAAC,CAAC;MACxC;IACJ,CAAC;IACD,OAAOF,sDAAI,CAACsB,2CAAQ,EAAE;MAAEnB,QAAQ,EAAEuD,MAAM,CAAC;IAAE,CAAC,CAAC;EACjD,CAAC;AACL;;;;;;;;;;;;;;;;;;;;;ACrBgD;AACR;AACwB;AACJ;AACA;AAC5D,IAAMN,gBAAgB,GAAG;EACrBC,SAAS,EAAE,WAAW;EACtBC,YAAY,EAAE;AAClB,CAAC;AACc,SAASsF,uBAAuBA,CAACpF,UAAU,EAAEC,QAAQ,EAAE;EAClE,OAAO,YAAM;IACT,IAAMC,MAAM,GAAG,SAATA,MAAMA,CAAA,EAAS;MACjB,IAAI/F,qEAAgB,KAAKyF,gBAAgB,CAACC,SAAS,EAAE;QACjD,OAAQrD,sDAAI,CAAC2I,mEAAY,EAAE;UAAEnF,UAAU,EAAEA,UAAU;UAAEM,UAAU,EAAE,IAAI;UAAE/B,aAAa,EAAE0B,QAAQ;UAAEM,OAAO,EAAE,KAAK;UAAEC,MAAM,EAAE;QAAY,CAAC,CAAC;MAC1I,CAAC,MACI;QACD,OAAOhE,sDAAI,CAAC2D,mEAAY,EAAE;UAAEM,MAAM,EAAE;QAAI,CAAC,CAAC;MAC9C;IACJ,CAAC;IACD,OAAOjE,sDAAI,CAACsB,2CAAQ,EAAE;MAAEnB,QAAQ,EAAEuD,MAAM,CAAC;IAAE,CAAC,CAAC;EACjD,CAAC;AACL;;;;;;;;;;;;;;;;;;;;;;;;;;ACrBiC;AACiC;AACF;AAAA,IAC3CmF,sBAAsB;EAKvC,SAAAA,uBAAY5C,gBAAgB,EAAEC,eAAe,EAAEzC,QAAQ,EAAE;IAAA0C,eAAA,OAAA0C,sBAAA;IAAAzC,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IACrD,IAAM5C,UAAU,GAAG0C,eAAe,CAACG,OAAO,CAAC7C,UAAU,GAC/C8C,IAAI,CAACC,KAAK,CAACL,eAAe,CAACG,OAAO,CAAC7C,UAAU,CAAC,GAC9C,CAAC,CAAC;IACR,IAAI,CAAC0C,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACD,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACxC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACD,UAAU,GAAGA,UAAU;EAChC;EAAC,OAAAgD,YAAA,CAAAqC,sBAAA;IAAAzD,GAAA;IAAAjI,KAAA,EACD,SAAAuG,MAAMA,CAAA,EAAG;MACLqC,uDAAe,CAAC6C,oEAAuB,CAAC,IAAI,CAACpF,UAAU,EAAE,IAAI,CAACC,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAACyC,eAAe,CAAC;MAChGH,uDAAe,CAAC2C,qEAAwB,CAAC,IAAI,CAAClF,UAAU,EAAE,IAAI,CAACC,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAACwC,gBAAgB,CAAC;IACtG;EAAC;IAAAb,GAAA;IAAAjI,KAAA,EACD,SAAAsJ,IAAIA,CAAA,EAAG;MACHV,uEAA+B,CAAC,IAAI,CAACG,eAAe,CAAC;MACrDH,uEAA+B,CAAC,IAAI,CAACE,gBAAgB,CAAC;IAC1D;EAAC;AAAA;;;;;;;;;;;;;;;;ACxBU,SAAS6C,eAAeA,CAACC,SAAS,EAAE7L,OAAO,EAAE8L,QAAQ,EAAoB;EAAA,IAAlBvC,IAAI,GAAAwC,SAAA,CAAAnB,MAAA,QAAAmB,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,YAAM,CAAE,CAAC;EAClF,OAAOF,SAAS,CAACI,OAAO,CAACC,QAAQ,CAACC,QAAQ,CAACC,MAAM,CAAC;IAC9CC,OAAO,WAAPA,OAAOA,CAAA,EAAG;MACN,IAAMC,IAAI,GAAG,IAAI;MACjB,IAAMvD,gBAAgB,GAAG,IAAI,CAACwD,EAAE,CAACC,eAAe,CAACC,UAAU,CAAC,CAAC,CAAC,CAACC,aAAa,CAAC1M,OAAO,CAAC2M,eAAe,CAAC;MACrG,IAAI3D,eAAe,GAAG,IAAI,CAAChJ,OAAO,CAAC4M,OAAO,CAACC,GAAG,CAAC,CAAC,CAAC,CAACH,aAAa,CAAC1M,OAAO,CAAC8M,iBAAiB,CAAC;MAC1F,IAAI9D,eAAe,EAAE;QACjB8C,QAAQ,CAAC/C,gBAAgB,EAAEC,eAAe,EAAE,UAAC+D,IAAI;UAAA,OAAKT,IAAI,CAAC/F,QAAQ,CAACwG,IAAI,CAAC;QAAA,EAAC;MAC9E,CAAC,MACI;QACD;QACA3M,MAAM,CAAC4M,iBAAiB,CAACC,KAAK,CAACC,SAAS,2BAAAzJ,MAAA,CAA2BzD,OAAO,CAACmN,UAAU,eAAY,UAACP,OAAO,EAAK;UAC1G5D,eAAe,GAAG4D,OAAO,CAAC,CAAC,CAAC,CAACF,aAAa,CAAC1M,OAAO,CAAC8M,iBAAiB,CAAC;UACrEhB,QAAQ,CAAC/C,gBAAgB,EAAEC,eAAe,EAAE,UAAC+D,IAAI;YAAA,OAAKT,IAAI,CAAC/F,QAAQ,CAACwG,IAAI,CAAC;UAAA,EAAC;QAC9E,CAAC,CAAC;MACN;IACJ,CAAC;IACDK,SAAS,WAATA,SAASA,CAACtH,KAAK,EAAE;MACb,IAAI,CAACS,QAAQ,CAACT,KAAK,CAAC;IACxB,CAAC;IACDuH,eAAe,WAAfA,eAAeA,CAAA,EAAG;MACd;MACAjN,MAAM,CAAC4M,iBAAiB,CAACC,KAAK,CAACK,YAAY,2BAAA7J,MAAA,CAA2BzD,OAAO,CAACmN,UAAU,aAAU,CAAC;MACnG5D,IAAI,CAAC,CAAC;IACV;EACJ,CAAC,CAAC;AACN;;;;;;;;;;;;;;;AC1BO,IAAMgE,YAAY,GAAG;EACxBC,gBAAgB,EAAE,4CAA4C;EAC9DC,gBAAgB,EAAE,4CAA4C;EAC9DC,iBAAiB,EAAE,6CAA6C;EAChEC,mBAAmB,EAAE,+CAA+C;EACpEC,UAAU,EAAE,qCAAqC;EACjDC,YAAY,EAAE,wCAAwC;EACtDC,uBAAuB,EAAE;AAC7B,CAAC;;;;;;;;;;;;;;;ACRM,IAAMC,YAAY,GAAG;EACxBC,uBAAuB,EAAE;AAC7B,CAAC;;;;;;;;;;;;;;;;;;;;;;;;ACFmC;AACE;AACM;AACJ;;;;;;;;;;;;;;;;ACHjC,IAAMC,gBAAgB,GAAG;EAC5BC,2BAA2B,EAAE;AACjC,CAAC;;;;;;;;;;;;;;;ACFM,IAAMC,cAAc,GAAG;EAC1BC,wBAAwB,EAAE,4BAA4B;EACtDC,kBAAkB,EAAE,sBAAsB;EAC1CC,YAAY,EAAE,uCAAuC;EACrDC,4BAA4B,EAAE,mCAAmC;EACjEC,6BAA6B,EAAE,oCAAoC;EACnEC,0BAA0B,EAAE,iCAAiC;EAC7DC,6BAA6B,EAAE,oCAAoC;EACnEC,2BAA2B,EAAE,kCAAkC;EAC/DC,wBAAwB,EAAE,6BAA6B;EACvDC,yBAAyB,EAAE,oCAAoC;EAC/DC,sBAAsB,EAAE,iCAAiC;EACzDC,yBAAyB,EAAE,8BAA8B;EACzDC,uBAAuB,EAAE,4BAA4B;EACrDC,iBAAiB,EAAE,qBAAqB;EACxCC,kBAAkB,EAAE,sBAAsB;EAC1CC,eAAe,EAAE,mBAAmB;EACpCC,sBAAsB,EAAE,2BAA2B;EACnDC,0BAA0B,EAAE,+BAA+B;EAC3DC,2BAA2B,EAAE,gCAAgC;EAC7DC,wBAAwB,EAAE,6BAA6B;EACvDC,6BAA6B,EAAE,kCAAkC;EACjEC,8BAA8B,EAAE,mCAAmC;EACnEC,2BAA2B,EAAE,gCAAgC;EAC7DC,2BAA2B,EAAE,gCAAgC;EAC7DC,4BAA4B,EAAE,iCAAiC;EAC/DC,yBAAyB,EAAE,8BAA8B;EACzDC,iCAAiC,EAAE,uCAAuC;EAC1EC,+BAA+B,EAAE,qCAAqC;EACtEC,2BAA2B,EAAE,gCAAgC;EAC7DC,4BAA4B,EAAE,iCAAiC;EAC/DC,yBAAyB,EAAE;AAC/B,CAAC;;;;;;;;;;;;;;;AChCM,IAAM/I,aAAa,GAAG;EACzBgB,UAAU,EAAE,aAAa;EACzBgI,SAAS,EAAE,YAAY;EACvBC,sBAAsB,EAAE,2BAA2B;EACnDC,uBAAuB,EAAE,2BAA2B;EACpDC,SAAS,EAAE,YAAY;EACvBC,qBAAqB,EAAE,0BAA0B;EACjDC,kCAAkC,EAAE,yCAAyC;EAC7EC,wBAAwB,EAAE,8BAA8B;EACxDC,uBAAuB,EAAE,2BAA2B;EACpDC,sBAAsB,EAAE,2BAA2B;EACnDC,4BAA4B,EAAE,kCAAkC;EAChEC,uBAAuB,EAAE,4BAA4B;EACrDC,yBAAyB,EAAE,8BAA8B;EACzDC,sBAAsB,EAAE,2BAA2B;EACnDC,uBAAuB,EAAE,4BAA4B;EACrDC,4BAA4B,EAAE,iCAAiC;EAC/DC,0BAA0B,EAAE,+BAA+B;EAC3DC,uBAAuB,EAAE;AAC7B,CAAC;;;;;;;;;;;;;;;;;;;;ACnBiD;AAC3C,IAAM7M,mBAAmB,gBAAG8M,oDAAa,CAAC,IAAI,CAAC;AAC/C,SAAS7M,uBAAuBA,CAAA,EAAG;EACtC,OAAO8M,iDAAU,CAAC/M,mBAAmB,CAAC;AAC1C;AACO,SAASgN,wBAAwBA,CAAA,EAAG;EACvC,IAAMC,GAAG,GAAGhN,uBAAuB,CAAC,CAAC;EACrC,OAAO,UAACiN,OAAO,EAAK;IAChBD,GAAG,CAACE,WAAW,CAACD,OAAO,CAAC;EAC5B,CAAC;AACL;AACO,SAASpK,6BAA6BA,CAAA,EAAG;EAC5C,IAAMmK,GAAG,GAAGhN,uBAAuB,CAAC,CAAC;EACrC,OAAO,UAACiN,OAAO;IAAA,OAAKD,GAAG,CAACG,gBAAgB,CAACF,OAAO,CAAC;EAAA;AACrD;;;;;;;;;;;;;;;;;;;ACd6B;AAC8F;AACpH,SAASG,cAAcA,CAAA,EAAG;EAC7B,IAAI1Q,2EAAsB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;IACxC;EACJ;EACA,IAAM4Q,MAAM,GAAG5Q,2EAAsB,CAAC,gBAAgB,EAAE,EAAE,CAAC;EAC3D4I,sDAAY,uDAAApG,MAAA,CAAuDoO,MAAM,YAAS;IAC9EE,UAAU,EAAE;MACRC,QAAQ,EAAE;IACd,CAAC;IACDC,kBAAkB,WAAlBA,kBAAkBA,CAAC1J,IAAI,EAAE;MACrB,OAAQ,CAAC,CAACA,IAAI,IAAI,CAAC,CAACA,IAAI,CAAC2J,OAAO,IAAI,mBAAmB,CAACC,IAAI,CAAC5J,IAAI,CAAC2J,OAAO,CAAC;IAC9E,CAAC;IACDE,OAAO,EAAE5Q,wEAAmBA;EAChC,CAAC,CAAC,CAAC6Q,OAAO,CAAC,CAAC;EACZxI,8DAAoB,CAAC;IACjB0I,CAAC,EAAE/Q,wEAAmB;IACtBgR,GAAG,EAAE5Q,+DAAU;IACf6Q,SAAS,EAAEhQ,8DAASA;EACxB,CAAC,CAAC;EACFoH,+DAAqB,CAAC;IAClB8I,GAAG,EAAE1Q,6DAAQ;IACbH,OAAO,EAAE8Q,MAAM,CAACC,IAAI,CAAC/Q,4DAAO,CAAC,CACxB8D,GAAG,CAAC,UAAAhC,IAAI;MAAA,UAAAH,MAAA,CAAOG,IAAI,OAAAH,MAAA,CAAI3B,4DAAO,CAAC8B,IAAI,CAAC;IAAA,CAAE,CAAC,CACvCkP,IAAI,CAAC,GAAG;EACjB,CAAC,CAAC;AACN;AACA,iEAAejJ,iDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5B2C;AACJ;AACnB;AACmD;AACzC;AACP;AAC3C,IAAMlG,SAAS,gBAAGD,sDAAM;EAAAE,IAAA;EAAAC,SAAA;EAAAC,SAAA;AAAA,EAKvB;AAAC,IAAAoP,KAAA,GAVgB,aAAAA,SAUhBA,MAAA;EAAA,OAOgBpN,eAAK;IAAA,OAAKA,KAAK,CAACqN,OAAO,GAAG,GAAG,GAAG,KAAM;EAAA;AAAA;AAAA,IAAAC,KAAA,GAjBtC,aAAAA,SAiBsCA,MAAA;EAAA,OAUxCtN,eAAK;IAAA,OAAIA,KAAK,CAACqN,OAAO,gBAAA1P,MAAA,CAAgBwP,gEAAc,IAAK,MAAM;EAAA;AAAA;AAhB/E,IAAMI,gBAAgB,gBAAG3P,sDAAM;EAAAE,IAAA;EAAAC,SAAA;EAAAC,SAAA;EAAAwP,IAAA;IAAA,eAMbJ,KAAsC;IAAA,eAUxCE,KAA+D;EAAA;AAAA,EAI9E;AACD,IAAMG,cAAc,gBAAG7P,sDAAM;EAAAE,IAAA;EAAAC,SAAA;EAAAC,SAAA;AAAA,EAS5B;AACD,IAAM0P,WAAW,gBAAG9P,sDAAM;EAAAE,IAAA;EAAAC,SAAA;EAAAC,SAAA;AAAA,EASzB;AACD,IAAM2P,WAAW,gBAAG/P,sDAAM;EAAAE,IAAA;EAAAC,SAAA;EAAAC,SAAA;AAAA,EAYzB;AACD,IAAM4P,kBAAkB,gBAAGhQ,sDAAM;EAAAE,IAAA;EAAAC,SAAA;EAAAC,SAAA;AAAA,EAMhC;AACD,IAAM6P,iBAAiB,gBAAGjQ,sDAAM;EAAAE,IAAA;EAAAC,SAAA;EAAAC,SAAA;AAAA,EAO/B;AACD,IAAM8P,cAAc,gBAAGlQ,sDAAM;EAAAE,IAAA;EAAAC,SAAA;EAAAC,SAAA;AAAA,EAO5B;AACD,IAAM+P,KAAK,gBAAGnQ,sDAAM;EAAAE,IAAA;EAAAC,SAAA;EAAAC,SAAA;AAAA,EAUnB;AACD,IAAMgQ,WAAW,gBAAGpQ,sDAAM;EAAAE,IAAA;EAAAC,SAAA;EAAAC,SAAA;AAAA,EAIzB;AACD,IAAMiQ,aAAa,gBAAGrQ,sDAAM;EAAAE,IAAA;EAAAC,SAAA;EAAAC,SAAA;AAAA,EAU3B;AACD,IAAMkQ,QAAQ,gBAAGtQ,sDAAM;EAAAE,IAAA;EAAAC,SAAA;EAAAC,SAAA;AAAA,EAMtB;AACD,IAAMmQ,SAAS,gBAAGvQ,sDAAM;EAAAE,IAAA;EAAAC,SAAA;EAAAC,SAAA;AAAA,EAGvB;AACD,IAAMoQ,eAAe,gBAAGxQ,sDAAM;EAAAE,IAAA;EAAAC,SAAA;EAAAC,SAAA;AAAA,EAS7B;AAAC,IAAAqQ,KAAA,GAvIgB,aAAAA,SAuIhBA,MAAA;EAAA,OAGoBrO,eAAK;IAAA,OAAIA,KAAK,CAACH,QAAQ,GAAGsN,gEAAc,GAAG,aAAa;EAAA;AAAA;AAAA,IAAAmB,KAAA,GA1I5D,aAAAA,SA0I4DA,MAAA;EAAA,OACnEtO,eAAK;IAAA,OAAKA,KAAK,CAACH,QAAQ,GAAG,MAAM,GAAG,SAAU;EAAA;AAAA;AAAA,IAAA0O,KAAA,GA3IvC,aAAAA,SA2IuCA,MAAA;EAAA,OAMjCvO,eAAK;IAAA,OAAIA,KAAK,CAACH,QAAQ,GAAGsN,gEAAc,GAAGD,+DAAa;EAAA;AAAA;AAThF,IAAMsB,QAAQ,gBAAG5Q,sDAAM;EAAAE,IAAA;EAAAC,SAAA;EAAAC,SAAA;EAAAwP,IAAA;IAAA,eAEDa,KAAwD;IAAA,eACnEC,KAA8C;IAAA,eAMjCC,KAAwD;EAAA;AAAA,EAE/E;AACc,SAASE,WAAWA,OAAiE;EAAA,IAA9DC,WAAW,GAAAnR,IAAA,CAAXmR,WAAW;IAAEvU,KAAK,GAAAoD,IAAA,CAALpD,KAAK;IAAEwU,WAAW,GAAApR,IAAA,CAAXoR,WAAW;IAAEvP,QAAQ,GAAA7B,IAAA,CAAR6B,QAAQ;IAAEwP;EAC7E,IAAMC,OAAO,GAAG5B,6CAAM,CAAC,IAAI,CAAC;EAC5B,IAAM6B,aAAa,GAAG7B,6CAAM,CAAC,IAAI,CAAC;EAClC,IAAAzL,SAAA,GAA8BN,+CAAQ,CAAC,KAAK,CAAC;IAAAQ,UAAA,GAAAC,cAAA,CAAAH,SAAA;IAAtCuN,SAAS,GAAArN,UAAA;IAAEsN,QAAQ,GAAAtN,UAAA;EAC1B,IAAAI,UAAA,GAAkCZ,+CAAQ,CAACE,kEAAmB,CAAC;IAAAW,UAAA,GAAAJ,cAAA,CAAAG,UAAA;IAAxDF,SAAS,GAAAG,UAAA;IAAEF,YAAY,GAAAE,UAAA;EAC9B,IAAAE,UAAA,GAAoCf,+CAAQ,CAAC,EAAE,CAAC;IAAAgB,UAAA,GAAAP,cAAA,CAAAM,UAAA;IAAzCgN,UAAU,GAAA/M,UAAA;IAAEgN,aAAa,GAAAhN,UAAA;EAChC,IAAAiN,UAAA,GAA8BjO,+CAAQ,CAAC0N,cAAc,CAAC;IAAAQ,UAAA,GAAAzN,cAAA,CAAAwN,UAAA;IAA/CjV,OAAO,GAAAkV,UAAA;IAAEC,UAAU,GAAAD,UAAA;EAC1B,IAAME,SAAS,MAAA3R,MAAA,CAAMmR,aAAa,CAACS,OAAO,GAAGT,aAAa,CAACS,OAAO,CAACC,WAAW,GAAG,EAAE,GAAG,CAAC,OAAI;EAC3FrO,gDAAS,CAAC,YAAM;IACZ,IAAIwN,WAAW,IAAI/M,SAAS,KAAKR,kEAAmB,EAAE;MAClDuN,WAAW,CAAC,EAAE,EAAGc,gBAAM,EAAK;QACxBJ,UAAU,CAACI,MAAM,CAAC;QAClB5N,YAAY,CAACT,6DAAc,CAAC;MAChC,CAAC,CAAC;IACN;EACJ,CAAC,EAAE,CAACuN,WAAW,EAAE/M,SAAS,CAAC,CAAC;EAC5B,IAAM+N,YAAW,GAAGA,SAAdA,WAAWA,CAAA,EAA8B;IAAA,IAA1BC,KAAK,GAAA3J,SAAA,CAAAnB,MAAA,QAAAmB,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,EAAE;IAAA,IAAE4J,SAAS,GAAA5J,SAAA,CAAAnB,MAAA,OAAAmB,SAAA,MAAAC,SAAA;IACtC,OAAO0J,KAAK,CAAC9P,GAAG,CAAC,UAACkF,IAAI,EAAE8K,KAAK,EAAK;MAC9B,IAAI9K,IAAI,CAAC9K,OAAO,EAAE;QACd,OAAQmE,uDAAK,CAAC8P,SAAS,EAAE;UAAEhR,QAAQ,EAAE,CAACH,sDAAI,CAACoR,eAAe,EAAE;YAAE5I,EAAE,KAAA7H,MAAA,CAAKmS,KAAK,aAAU;YAAE3S,QAAQ,EAAE6H,IAAI,CAAC/K;UAAM,CAAC,CAAC,EAAE+C,sDAAI,CAAC,KAAK,EAAE;YAAEG,QAAQ,EAAEwS,YAAW,CAAC3K,IAAI,CAAC9K,OAAO,EAAE4V,KAAK;UAAE,CAAC,CAAC;QAAE,CAAC,uBAAAnS,MAAA,CAAuBmS,KAAK,CAAE,CAAC;MAChN,CAAC,MACI;QACD,IAAM1N,GAAG,wBAAAzE,MAAA,CAAwBkS,SAAS,KAAK3J,SAAS,MAAAvI,MAAA,CAAMkS,SAAS,OAAAlS,MAAA,CAAImS,KAAK,IAAKA,KAAK,CAAE;QAC5F,OAAQ9S,sDAAI,CAACwR,QAAQ,EAAE;UAAEhJ,EAAE,EAAEpD,GAAG;UAAEvC,QAAQ,EAAE1F,KAAK,IAAI6K,IAAI,CAAC7K,KAAK,KAAKA,KAAK,CAACA,KAAK;UAAEsL,OAAO,EAAEA,SAATA,OAAOA,CAAA,EAAQ;YACxFrG,QAAQ,CAAC4F,IAAI,CAAC;YACdgK,QAAQ,CAAC,KAAK,CAAC;UACnB,CAAC;UAAE7R,QAAQ,EAAE6H,IAAI,CAAC/K;QAAM,CAAC,EAAEmI,GAAG,CAAC;MACvC;IACJ,CAAC,CAAC;EACN,CAAC;EACD,OAAQ/D,uDAAK,CAACR,SAAS,EAAE;IAAEV,QAAQ,EAAE,CAACkB,uDAAK,CAACkP,gBAAgB,EAAE;MAAE/H,EAAE,EAAE,uBAAuB;MAAE6H,OAAO,EAAE0B,SAAS;MAAEtJ,OAAO,EAAEA,SAATA,OAAOA,CAAA,EAAQ;QAChH,IAAIsJ,SAAS,EAAE;UACX,IAAIF,OAAO,CAACU,OAAO,EAAE;YACjBV,OAAO,CAACU,OAAO,CAACQ,IAAI,CAAC,CAAC;UAC1B;UACAf,QAAQ,CAAC,KAAK,CAAC;UACfE,aAAa,CAAC,EAAE,CAAC;QACrB,CAAC,MACI;UACD,IAAIL,OAAO,CAACU,OAAO,EAAE;YACjBV,OAAO,CAACU,OAAO,CAACS,KAAK,CAAC,CAAC;UAC3B;UACAhB,QAAQ,CAAC,IAAI,CAAC;QAClB;MACJ,CAAC;MAAE7R,QAAQ,EAAE,CAACkB,uDAAK,CAACoP,cAAc,EAAE;QAAEtQ,QAAQ,EAAE,CAAC8R,UAAU,KAAK,EAAE,KACjD,CAAC9U,KAAK,GAAI6C,sDAAI,CAAC0Q,WAAW,EAAE;UAAEvQ,QAAQ,EAAEuR;QAAY,CAAC,CAAC,GAAK1R,sDAAI,CAAC2Q,WAAW,EAAE;UAAExQ,QAAQ,EAAEhD,KAAK,CAACF;QAAM,CAAC,CAAE,CAAC,EAAEoE,uDAAK,CAACyP,cAAc,EAAE;UAAE3Q,QAAQ,EAAE,CAACH,sDAAI,CAAC+Q,KAAK,EAAE;YAAEkC,GAAG,EAAEpB,OAAO;YAAEqB,OAAO,EAAEA,SAATA,OAAOA,CAAA,EAAQ;cAC9KlB,QAAQ,CAAC,IAAI,CAAC;YAClB,CAAC;YAAE5P,QAAQ,EAAE+Q,SAAV/Q,QAAQA,CAAE+Q,CAAC,EAAI;cACdjB,aAAa,CAACiB,CAAC,CAAC1Q,MAAM,CAACtF,KAAK,CAAC;cAC7B0H,YAAY,CAACT,gEAAiB,CAAC;cAC/BuN,WAAW,IACPA,WAAW,CAACwB,CAAC,CAAC1Q,MAAM,CAACtF,KAAK,EAAGsV,gBAAM,EAAK;gBACpCJ,UAAU,CAACI,MAAM,CAAC;gBAClB5N,YAAY,CAACT,6DAAc,CAAC;cAChC,CAAC,CAAC;YACV,CAAC;YAAEjH,KAAK,EAAE8U,UAAU;YAAEmB,KAAK,EAAEd,SAAS;YAAE9J,EAAE,EAAE;UAAqB,CAAC,CAAC,EAAExI,sDAAI,CAACgR,WAAW,EAAE;YAAEiC,GAAG,EAAEnB,aAAa;YAAE3R,QAAQ,EAAE8R;UAAW,CAAC,CAAC;QAAE,CAAC,CAAC;MAAE,CAAC,CAAC,EAAE5Q,uDAAK,CAACuP,kBAAkB,EAAE;QAAEzQ,QAAQ,EAAE,CAACyE,SAAS,KAAKR,gEAAiB,IAAIpE,sDAAI,CAACuB,+DAAS,EAAE,CAAC,CAAC,CAAC,EAAEvB,sDAAI,CAAC6Q,iBAAiB,EAAE,CAAC,CAAC,CAAC;MAAE,CAAC,CAAC;IAAE,CAAC,CAAC,EAAEkB,SAAS,IAAK/R,sDAAI,CAACiR,aAAa,EAAE;MAAE9Q,QAAQ,EAAEH,sDAAI,CAACkR,QAAQ,EAAE;QAAE/Q,QAAQ,EAAEwS,YAAW,CAACzV,OAAO;MAAE,CAAC;IAAE,CAAC,CAAE;EAAE,CAAC,CAAC;AACla;;;;;;;;;;;;;;;;;;;;;;;AC7M+D;AAEf;AACM;AACR;AACyB;AACb;AACrB;AACrC,SAASsW,gBAAgBA,CAAA,EAAG;EACxBlW,MAAM,CAACmW,QAAQ,CAACC,IAAI,MAAA/S,MAAA,CAAMlD,6DAAQ,2CAAAkD,MAAA,CAAwCvB,kEAAa,CAAE;AAC7F;AACe,SAASuE,YAAYA,CAAApD,IAAA,EAAoF;EAAA,IAAjF0D,MAAM,GAAA1D,IAAA,CAAN0D,MAAM;IAAE0P,eAAe,GAAApT,IAAA,CAAfoT,eAAe;IAAAC,cAAA,GAAArT,IAAA,CAAEsT,SAAS;IAATA,SAAS,GAAAD,cAAA,cAAG;MAAEE,MAAM,EAAE,EAAE;MAAEpF,OAAO,EAAE,EAAE;MAAEqF,MAAM,EAAE;IAAG,CAAC,GAAAH,cAAA;EAC/G,IAAMI,cAAc,GAAG/P,MAAM,KAAK,GAAG,IAAIA,MAAM,KAAK,GAAG;EACvD,IAAMgQ,WAAW,GAAGD,cAAc,GAC5BxX,mDAAE,CAAC,8BAA8B,EAAE,QAAQ,CAAC,GAC5CqX,SAAS,CAACC,MAAM;EACtB,IAAMI,YAAY,GAAGF,cAAc,GAC7BxX,mDAAE,CAAC,2DAA2D,EAAE,QAAQ,CAAC,GACzEqX,SAAS,CAACnF,OAAO;EACvB,OAAQ1O,sDAAI,CAACuT,uDAAc,EAAE;IAAExU,UAAU,EAAEA,+DAAU;IAAEoB,QAAQ,EAAEkB,uDAAK,CAACiS,iEAAW,EAAE;MAAEa,SAAS,EAAE,QAAQ;MAAEhU,QAAQ,EAAE,CAACH,sDAAI,CAAC,IAAI,EAAE;QAAEG,QAAQ,EAAE8T;MAAY,CAAC,CAAC,EAAEjU,sDAAI,CAAC,GAAG,EAAE;QAAEG,QAAQ,EAAEH,sDAAI,CAAC,GAAG,EAAE;UAAEG,QAAQ,EAAE+T;QAAa,CAAC;MAAE,CAAC,CAAC,EAAEF,cAAc,GAAIhU,sDAAI,CAACqT,8DAAQ,EAAE;QAAE,cAAc,EAAE,kBAAkB;QAAE5K,OAAO,EAAE+K,gBAAgB;QAAErT,QAAQ,EAAE3D,mDAAE,CAAC,cAAc,EAAE,QAAQ;MAAE,CAAC,CAAC,GAAKwD,sDAAI,CAACqT,8DAAQ,EAAE;QAAE,cAAc,EAAE,cAAc;QAAE5K,OAAO,EAAEkL,eAAe;QAAExT,QAAQ,EAAE0T,SAAS,CAACE;MAAO,CAAC,CAAE;IAAE,CAAC;EAAE,CAAC,CAAC;AACje;;;;;;;;;;;;;;;;ACpBwC;AAAA,IAAAK,IAAA,GACtB,aAAAA,SADsBA,KAAA;EAAA,OAElBpR,eAAK;IAAA,cAAArC,MAAA,CAAWqC,KAAK,CAACjE,UAAU;EAAA,CAAoC;AAAA;AAAA,IAAAqR,KAAA,GADxE,aAAAA,SACwEA,MAAA;EAAA,OAS5EpN,eAAK;IAAA,OAAKA,KAAK,CAACqR,OAAO,IAAI,eAAe;EAAA;AAAA;AAVxD,8EAAezT,sDAAM;EAAAE,IAAA;EAAAC,SAAA;EAAAC,SAAA;EAAAwP,IAAA;IAAA,eACC4D,IAAoE;IAAA,eAS7EhE,KAA2C;EAAA;AAAA;;;;;;;;;;;;;;;;;;;;ACXR;AAEF;AACI;AACQ;AAC3C,SAASkE,YAAYA,CAAA,EAAG;EACnC,OAAQtU,sDAAI,CAACuT,uDAAc,EAAE;IAAExU,UAAU,EAAEA,+DAAU;IAAEoB,QAAQ,EAAEH,sDAAI,CAACuB,+DAAS,EAAE;MAAEgT,IAAI,EAAE;IAAG,CAAC;EAAE,CAAC,CAAC;AACrG;;;;;;;;;;;;;;;;;;;;;ACP+D;AAET;AACR;AACY;AACrB;AACtB,SAASC,eAAeA,CAAA,EAAG;EACtC,IAAMP,WAAW,GAAGzX,mDAAE,CAAC,qBAAqB,EAAE,QAAQ,CAAC;EACvD,IAAM0X,YAAY,MAAAvT,MAAA,CAAMnE,mDAAE,CAAC,4DAA4D,EAAE,QAAQ,CAAC,OAAAmE,MAAA,CAAInE,mDAAE,CAAC,gDAAgD,EAAE,QAAQ,CAAC,CAAE;EACtK,OAAQwD,sDAAI,CAACuT,uDAAc,EAAE;IAAExU,UAAU,EAAEA,+DAAU;IAAEoB,QAAQ,EAAEkB,uDAAK,CAACiS,iEAAW,EAAE;MAAEa,SAAS,EAAE,QAAQ;MAAEhU,QAAQ,EAAE,CAACH,sDAAI,CAAC,IAAI,EAAE;QAAEG,QAAQ,EAAE8T;MAAY,CAAC,CAAC,EAAEjU,sDAAI,CAAC,GAAG,EAAE;QAAEG,QAAQ,EAAEH,sDAAI,CAAC,GAAG,EAAE;UAAEG,QAAQ,EAAE+T;QAAa,CAAC;MAAE,CAAC,CAAC;IAAE,CAAC;EAAE,CAAC,CAAC;AACtO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACV+D;AACZ;AACmB;AACtB;AACR;AACF;AACkF;AACxD;AACd;AACwB;AACI;AAC9E,SAAStQ,QAAQA,CAAArD,IAAA,EAAmG;EAAA,IAAhGiD,UAAU,GAAAjD,IAAA,CAAViD,UAAU;IAAEM,UAAU,GAAAvD,IAAA,CAAVuD,UAAU;IAAE/B,aAAa,GAAAxB,IAAA,CAAbwB,aAAa;IAAA6S,YAAA,GAAArU,IAAA,CAAEwD,OAAO;IAAPA,OAAO,GAAA6Q,YAAA,cAAG,IAAI,GAAAA,YAAA;IAAAC,WAAA,GAAAtU,IAAA,CAAEyD,MAAM;IAANA,MAAM,GAAA6Q,WAAA,cAAG,WAAW,GAAAA,WAAA;IAAEC,cAAc,GAAAvU,IAAA,CAAduU,cAAc;EAC3G,IAAQhT,MAAM,GAA6B0B,UAAU,CAA7C1B,MAAM;IAAEY,QAAQ,GAAmBc,UAAU,CAArCd,QAAQ;IAAEC,YAAY,GAAKa,UAAU,CAA3Bb,YAAY;EACtC,IAAMoS,YAAY,GAAG5V,6DAAQ,IAAI2C,MAAM;EACvC,IAAMmB,oBAAoB,GAAGxB,iFAAuB,CAAC,CAAC;EACtD,IAAMuT,wBAAwB,GAAGxG,kFAAwB,CAAC,CAAC;EAC3D,IAAMyG,YAAY,GAAG,SAAfA,YAAYA,CAAI3S,YAAY,EAAK;IACnCP,aAAa,CAAC;MACV5C,QAAQ,EAARA,6DAAQ;MACR2C,MAAM,EAAEQ,YAAY,CAACnF,KAAK;MAC1BuF,QAAQ,EAAEJ,YAAY,CAACrF,KAAK;MAC5B0F,YAAY,EAAEL,YAAY,CAACK;IAC/B,CAAC,CAAC;EACN,CAAC;EACDwB,gDAAS,CAAC,YAAM;IACZ6Q,wBAAwB,CAAC;MACrB5P,GAAG,EAAEf,4FAAoC;MACzCiB,OAAO,EAAE;QACLtB,MAAM,EAANA;MACJ;IACJ,CAAC,CAAC;EACN,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EACZ,OAAO,CAACf,oBAAoB,GAAIjD,sDAAI,CAACsU,4DAAY,EAAE,CAAC,CAAC,CAAC,GAAKjT,uDAAK,CAACC,2CAAQ,EAAE;IAAEnB,QAAQ,EAAE,CAAC,CAAC2D,UAAU,IAAI,CAACiR,YAAY,KAAM/U,sDAAI,CAAC2U,mDAAU,EAAE;MAAE7S,MAAM,EAAEA,MAAM;MAAEY,QAAQ,EAAEA,QAAQ;MAAEuS,YAAY,EAAEA,YAAY;MAAEjR,MAAM,EAAEA,MAAM;MAAErB,YAAY,EAAEA;IAAa,CAAC,CAAE,EAAEoS,YAAY,IAAK1T,uDAAK,CAACC,2CAAQ,EAAE;MAAEnB,QAAQ,EAAE,CAAC2D,UAAU,IAAI9D,sDAAI,CAACyU,8DAAQ,EAAE,CAAC,CAAC,CAAC,EAAE1Q,OAAO,IAAK/D,sDAAI,CAAC0U,oDAAW,EAAE;QAAEvV,QAAQ,EAAEA,6DAAQ;QAAE2C,MAAM,EAAEA,MAAM;QAAEgT,cAAc,EAAEA,cAAc;QAAEnS,YAAY,EAAEA;MAAa,CAAC,CAAE;IAAE,CAAC,CAAE;EAAE,CAAC,CAAE;AAC7d;AACe,SAASuS,iBAAiBA,CAAClS,KAAK,EAAE;EAC7C,OAAQhD,sDAAI,CAACwB,kFAA4B,EAAE;IAAErE,KAAK,EAAEyE,wFAAuB,CAAC,CAAC,IAAID,mFAAwB,CAACpC,iEAAY,CAAC;IAAEY,QAAQ,EAAEH,sDAAI,CAAC4D,QAAQ,EAAAzC,aAAA,KAAO6B,KAAK,CAAE;EAAE,CAAC,CAAC;AACtK;;;;;;;;;;;;;;;;;;;;;;;;ACpCgD;AAEN;AACQ;AACb;AACG;AACkC;AACP;AACjB;AACnC,SAAS2R,UAAUA,CAAApU,IAAA,EAA0E;EAAA,IAAvEuB,MAAM,GAAAvB,IAAA,CAANuB,MAAM;IAAEY,QAAQ,GAAAnC,IAAA,CAARmC,QAAQ;IAAEuS,YAAY,GAAA1U,IAAA,CAAZ0U,YAAY;IAAAJ,WAAA,GAAAtU,IAAA,CAAEyD,MAAM;IAANA,MAAM,GAAA6Q,WAAA,cAAG,WAAW,GAAAA,WAAA;IAAElS,YAAY,GAAApC,IAAA,CAAZoC,YAAY;EACnG,IAAAX,SAAA,GAAwCN,2DAAQ,CAAC,CAAC;IAA1C6D,MAAM,GAAAvD,SAAA,CAANuD,MAAM;IAAE8P,YAAY,GAAArT,SAAA,CAAZqT,YAAY;IAAEC,KAAK,GAAAtT,SAAA,CAALsT,KAAK;EACnC,IAAAC,qBAAA,GAA0GH,4EAAyB,CAACpR,MAAM,CAAC;IAAnIwR,oBAAoB,GAAAD,qBAAA,CAApBC,oBAAoB;IAASC,WAAW,GAAAF,qBAAA,CAAlBD,KAAK;IAAeI,UAAU,GAAAH,qBAAA,CAAVG,UAAU;IAAEzT,QAAQ,GAAAsT,qBAAA,CAARtT,QAAQ;IAAgB0T,cAAc,GAAAJ,qBAAA,CAA5BF,YAAY;EACpF,IAAMlY,KAAK,GAAG2E,MAAM,IAAIY,QAAQ,GAC1B;IACEzF,KAAK,EAAEyF,QAAQ;IACfvF,KAAK,EAAE2E,MAAM;IACba,YAAY,EAAZA;EACJ,CAAC,GACC,IAAI;EACV,IAAMiT,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAIC,MAAM,EAAK;IAClC,IAAIzY,4EAAa,CAACyY,MAAM,CAAC1Y,KAAK,CAAC,EAAE;MAC7BqY,oBAAoB,CAACK,MAAM,CAAC1Y,KAAK,CAAC,CAACqI,IAAI,CAAC,UAAAsQ,KAAA,EAAoB;QAAA,IAAjBpQ,IAAI,GAAAoQ,KAAA,CAAJpQ,IAAI;UAAE5E,IAAI,GAAAgV,KAAA,CAAJhV,IAAI;QACjDmU,YAAY,CAAC;UACT9X,KAAK,EAAEuI,IAAI;UACXzI,KAAK,EAAE6D,IAAI;UACX6B,YAAY,EAAE;QAClB,CAAC,CAAC;MACN,CAAC,CAAC;IACN,CAAC,MACI;MACDsS,YAAY,CAACY,MAAM,CAAC;IACxB;EACJ,CAAC;EACD,OAAOH,UAAU,GAAI1V,sDAAI,CAACsU,4DAAY,EAAE,CAAC,CAAC,CAAC,GAAIe,YAAY,IAAIM,cAAc,GAAI3V,sDAAI,CAAC2D,4DAAY,EAAE;IAAEM,MAAM,EAAEoR,YAAY,GAAGA,YAAY,CAACpR,MAAM,GAAG0R,cAAc,CAAC1R,MAAM;IAAE0P,eAAe,EAAE,SAAjBA,eAAeA,CAAA,EAAQ;MACzL,IAAI1R,QAAQ,EAAE;QACVwT,WAAW,CAAC,CAAC;MACjB,CAAC,MACI;QACDH,KAAK,CAAC,CAAC;MACX;IACJ,CAAC;IAAEzB,SAAS,EAAE;MACVC,MAAM,EAAEtX,mDAAE,CAAC,2CAA2C,EAAE,QAAQ,CAAC;MACjEkS,OAAO,EAAElS,mDAAE,CAAC,yDAAyD,EAAE,QAAQ,CAAC;MAChFuX,MAAM,EAAEvX,mDAAE,CAAC,eAAe,EAAE,QAAQ;IACxC;EAAE,CAAC,CAAC,GAAKwD,sDAAI,CAACmV,qDAAY,EAAE;IAAExD,WAAW,EAAEpM,MAAM;IAAEnD,QAAQ,EAAE,SAAVA,QAAQA,CAAGyT,MAAM;MAAA,OAAKD,iBAAiB,CAACC,MAAM,CAAC;IAAA;IAAE1Y,KAAK,EAAEA;EAAM,CAAC,CAAE;AAC5H;;;;;;;;;;;;;;;;;;;;;AC7C+D;AAET;AACI;AACV;AACX;AACtB,SAASgY,YAAYA,CAAA5U,IAAA,EAAoC;EAAA,IAAjCoR,WAAW,GAAApR,IAAA,CAAXoR,WAAW;IAAEvP,QAAQ,GAAA7B,IAAA,CAAR6B,QAAQ;IAAEjF,KAAK,GAAAoD,IAAA,CAALpD,KAAK;EAC/D,OAAQkE,uDAAK,CAACkS,8DAAc,EAAE;IAAExU,UAAU,EAAEA,+DAAU;IAAEoB,QAAQ,EAAE,CAACH,sDAAI,CAAC,GAAG,EAAE;MAAE,cAAc,EAAE,oBAAoB;MAAEG,QAAQ,EAAEH,sDAAI,CAAC,GAAG,EAAE;QAAEG,QAAQ,EAAE3D,mDAAE,CAAC,6DAA6D,EAAE,QAAQ;MAAE,CAAC;IAAE,CAAC,CAAC,EAAEwD,sDAAI,CAACyR,2DAAW,EAAE;MAAEC,WAAW,EAAElV,mDAAE,CAAC,mBAAmB,EAAE,QAAQ,CAAC;MAAEW,KAAK,EAAEA,KAAK;MAAEwU,WAAW,EAAEA,WAAW;MAAEvP,QAAQ,EAAEA;IAAS,CAAC,CAAC;EAAE,CAAC,CAAC;AACjX;;;;;;;;;;;;;;;;;;;;;;;;;;;ACRgD;AACC;AACC;AACmC;AAC7B;AACzC,SAASsS,WAAWA,CAAAnU,IAAA,EAAsD;EAAA,IAAnDpB,QAAQ,GAAAoB,IAAA,CAARpB,QAAQ;IAAE2C,MAAM,GAAAvB,IAAA,CAANuB,MAAM;IAAEgT,cAAc,GAAAvU,IAAA,CAAduU,cAAc;IAAEnS,YAAY,GAAApC,IAAA,CAAZoC,YAAY;EAChF,IAAMsT,QAAQ,GAAGtT,YAAY,KAAK,IAAI;EACtC,IAAMkP,OAAO,GAAG5B,6CAAM,CAAC,IAAI,CAAC;EAC5B9L,gDAAS,CAAC,YAAM;IACZ,IAAI0N,OAAO,CAACU,OAAO,EAAE;MACjB;MACA,IAAM2D,KAAK,GAAG5Y,MAAM,CAAC6Y,MAAM,CAACD,KAAK,IAAI5Y,MAAM,CAAC4Y,KAAK;MACjDrE,OAAO,CAACU,OAAO,CAAC6D,SAAS,GAAG,EAAE;MAC9B,IAAMC,IAAI,GAAGpY,gFAA2B,CAAC,IAAI,CAAC;MAC9C,IAAIgY,QAAQ,EAAE;QACV,IAAMM,SAAS,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QAC/CF,SAAS,CAACG,SAAS,CAACC,GAAG,CAAC,eAAe,CAAC;QACxCJ,SAAS,CAAClQ,OAAO,CAAC2P,MAAM,GAAGA,2DAAM;QACjCO,SAAS,CAAClQ,OAAO,CAACvE,MAAM,GAAGA,MAAM;QACjCyU,SAAS,CAAClQ,OAAO,CAAClH,QAAQ,GAAGA,QAAQ,CAACyX,QAAQ,CAAC,CAAC;QAChDL,SAAS,CAAClQ,OAAO,CAACvI,GAAG,GAAGuY,IAAI,GAAG,IAAI,GAAG,EAAE;QACxCxE,OAAO,CAACU,OAAO,CAACsE,WAAW,CAACN,SAAS,CAAC;MAC1C,CAAC,MACI;QACD,IAAMO,gBAAgB,GAAGT,IAAI,GAAG;UAAEvY,GAAG,EAAE;QAAK,CAAC,GAAG,CAAC,CAAC;QAClDoY,KAAK,CAAChU,KAAK,CAAC6U,MAAM,CAAA5V,aAAA;UACdhC,QAAQ,EAARA,QAAQ;UACR2C,MAAM,EAANA,MAAM;UACNkU,MAAM,EAANA,2DAAM;UACNvT,MAAM,MAAA9B,MAAA,CAAMkR,OAAO,CAACU,OAAO,CAAC/J,EAAE;QAAE,GAC7BsO,gBAAgB,CACtB,CAAC;MACN;IACJ;EACJ,CAAC,EAAE,CAAChV,MAAM,EAAE3C,QAAQ,EAAE0S,OAAO,EAAEoE,QAAQ,CAAC,CAAC;EACzC,IAAInB,cAAc,EAAE;IAChB,OAAO9U,sDAAI,CAACwU,+DAAe,EAAE,CAAC,CAAC,CAAC;EACpC;EACA,OAAOxU,sDAAI,CAAC+V,+DAAS,EAAE;IAAE9C,GAAG,EAAEpB,OAAO;IAAErJ,EAAE,uBAAA7H,MAAA,CAAuBmB,MAAM;EAAG,CAAC,CAAC;AAC/E;;;;;;;;;;;;;;;;;;;;;;;;;;ACvCiC;AAC2E;AAC9D;AACqB;AACpD,SAASsT,yBAAyBA,CAAA,EAAuB;EAAA,IAAtBpR,MAAM,GAAAiF,SAAA,CAAAnB,MAAA,QAAAmB,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,WAAW;EAClE,IAAM1E,KAAK,GAAGD,uFAA6B,CAAC,CAAC;EAC7C,IAAM0S,KAAK,GAAGxI,kFAAwB,CAAC,CAAC;EACxC,IAAAhK,SAAA,GAAkCN,+CAAQ,CAACE,6DAAc,CAAC;IAAAM,UAAA,GAAAC,cAAA,CAAAH,SAAA;IAAnDI,SAAS,GAAAF,UAAA;IAAEG,YAAY,GAAAH,UAAA;EAC9B,IAAAI,UAAA,GAAwCZ,+CAAQ,CAAC,IAAI,CAAC;IAAAa,UAAA,GAAAJ,cAAA,CAAAG,UAAA;IAA/CuQ,YAAY,GAAAtQ,UAAA;IAAEkS,eAAe,GAAAlS,UAAA;EACpC,IAAMyQ,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAI/U,IAAI,EAAK;IACnCoE,YAAY,CAACT,gEAAiB,CAAC;IAC/B4S,KAAK,CAAC;MACF5R,GAAG,EAAEf,kGAA0C;MAC/CiB,OAAO,EAAE;QACL7E,IAAI,EAAJA,IAAI;QACJuD,MAAM,EAANA;MACJ;IACJ,CAAC,CAAC;IACF,OAAOO,KAAK,CAAC;MACTa,GAAG,EAAEf,4FAAoC;MACzCiB,OAAO,EAAE;QACL7E,IAAI,EAAJA,IAAI;QACJkC,YAAY,EAAE;MAClB;IACJ,CAAC,CAAC,CACG6C,IAAI,CAAC,UAAAhD,IAAI,EAAI;MACdqC,YAAY,CAACT,6DAAc,CAAC;MAC5B,OAAO5B,IAAI;IACf,CAAC,CAAC,SACQ,CAAC,UAAA0U,GAAG,EAAI;MACdD,eAAe,CAACC,GAAG,CAAC;MACpBF,KAAK,CAAC;QACF5R,GAAG,EAAEf,6FAAqC;QAC1CiB,OAAO,EAAE;UACLtB,MAAM,EAANA;QACJ;MACJ,CAAC,CAAC;MACFa,YAAY,CAACT,+DAAgB,CAAC;IAClC,CAAC,CAAC;EACN,CAAC;EACD,OAAO;IACHsR,UAAU,EAAE9Q,SAAS,KAAKR,gEAAiB;IAC3CnC,QAAQ,EAAE2C,SAAS,KAAKR,+DAAgB;IACxCiR,YAAY,EAAZA,YAAY;IACZG,oBAAoB,EAApBA,oBAAoB;IACpBF,KAAK,EAAE,SAAPA,KAAKA,CAAA;MAAA,OAAQzQ,YAAY,CAACT,6DAAc,CAAC;IAAA;EAC7C,CAAC;AACL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/CiC;AACM;AAC0C;AACd;AAC4B;AAChF,SAAS1C,QAAQA,CAAA,EAAG;EAC/B,IAAM6C,KAAK,GAAGD,uFAA6B,CAAC,CAAC;EAC7C,IAAAE,SAAA,GAAwCN,+CAAQ,CAAC,IAAI,CAAC;IAAAQ,UAAA,GAAAC,cAAA,CAAAH,SAAA;IAA/C6Q,YAAY,GAAA3Q,UAAA;IAAEuS,eAAe,GAAAvS,UAAA;EACpC,IAAA4S,qBAAA,GAAgCF,uEAA0B,CAAC,CAAC;IAApDG,mBAAmB,GAAAD,qBAAA,CAAnBC,mBAAmB;EAC3B,IAAMhS,MAAM,GAAG4R,sDAAQ,CAAC,UAAC5R,MAAM,EAAEyD,QAAQ,EAAK;IAC1C,OAAOwO,OAAO,CAACC,GAAG,CAAC,CACfF,mBAAmB,EACnBhT,KAAK,CAAC;MACFa,GAAG,EAAEf,gFAAwB;MAC7BiB,OAAO,EAAE;QACLC,MAAM,EAANA;MACJ;IACJ,CAAC,CAAC,CACL,CAAC,CACGC,IAAI,CAAC,UAAAjF,IAAA,EAA2C;MAAA,IAAAuV,KAAA,GAAAnR,cAAA,CAAApE,IAAA;QAAzCmX,4BAA4B,GAAA5B,KAAA;QAAE5T,KAAK,GAAA4T,KAAA;MAC3C,IAAM6B,gBAAgB,GAAGN,+EAAkB,CAACK,4BAA4B,CAACE,oBAAoB,CAAC;MAC9F5O,QAAQ,IAAArI,MAAA,CAAAkX,kBAAA,CACD3V,KAAK,CAACY,GAAG,CAAC,UAACN,IAAI;QAAA,OAAM;UACpBvF,KAAK,EAAEuF,IAAI,CAAC1B,IAAI;UAChB3D,KAAK,EAAEqF,IAAI,CAACkD,IAAI;UAChB/C,YAAY,EAAEH,IAAI,CAACG;QACvB,CAAC;MAAA,CAAC,CAAC,IACHgV,gBAAgB,EACnB,CAAC;IACN,CAAC,CAAC,SACQ,CAAC,UAAA/R,KAAK,EAAI;MAChBqR,eAAe,CAACrR,KAAK,CAAC;IAC1B,CAAC,CAAC;EACN,CAAC,EAAE,GAAG,EAAE;IAAEkS,QAAQ,EAAE;EAAK,CAAC,CAAC;EAC3B,OAAO;IACHvS,MAAM,EAANA,MAAM;IACN8P,YAAY,EAAZA,YAAY;IACZC,KAAK,EAAE,SAAPA,KAAKA,CAAA;MAAA,OAAQ2B,eAAe,CAAC,IAAI,CAAC;IAAA;EACtC,CAAC;AACL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvCiC;AACI;AAC4C;AACd;AAC6B;AACjF,SAASG,0BAA0BA,CAAA,EAAG;EACjD,IAAM7S,KAAK,GAAGD,uFAA6B,CAAC,CAAC;EAC7C,IAAAE,SAAA,GAAyDN,+CAAQ,CAAC,IAAI,CAAC;IAAAQ,UAAA,GAAAC,cAAA,CAAAH,SAAA;IAAhEoT,oBAAoB,GAAAlT,UAAA;IAAEwT,uBAAuB,GAAAxT,UAAA;EACpD,IAAAI,UAAA,GAA8BZ,+CAAQ,CAAC;MAAA,OAAM,IAAIsT,OAAO,CAAC,UAAAW,OAAO,EAAI;QAChE5T,KAAK,CAAC;UACFa,GAAG,EAAEf,6FAAqC;UAC1CiB,OAAO,EAAE,CAAC;QACd,CAAC,CAAC,CAACE,IAAI,CAAC,UAAAC,IAAI,EAAI;UACZyS,uBAAuB,CAACzS,IAAI,CAACmS,oBAAoB,CAAC;UAClDO,OAAO,CAAC1S,IAAI,CAAC;QACjB,CAAC,CAAC;MACN,CAAC,CAAC;IAAA,EAAC;IAAAV,UAAA,GAAAJ,cAAA,CAAAG,UAAA;IARIyS,mBAAmB,GAAAxS,UAAA;EAS1B,OAAO;IAAE6S,oBAAoB,EAApBA,oBAAoB;IAAEL,mBAAmB,EAAnBA;EAAoB,CAAC;AACxD;AACO,IAAMF,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAIO,oBAAoB,EAAK;EACxD,IAAI,CAACA,oBAAoB,EAAE;IACvB,OAAO,CAAC,CAAC;EACb;EACA,OAAO;IACH3a,KAAK,EAAET,mDAAE,CAAC,WAAW,EAAE,QAAQ,CAAC;IAChCU,OAAO,EAAE4S,MAAM,CAACC,IAAI,CAAC6H,oBAAoB,CAAC,CACrCQ,MAAM,CAAC,UAAAC,UAAU,EAAI;MACtB,IAAMC,+BAA+B,GAAGV,oBAAoB,CAACS,UAAU,CAAC;MACxE,OAAQ,CAACC,+BAA+B,CAACC,0BAA0B,IAC/D,CAACD,+BAA+B,CAACE,aAAa,CAAC1Q,MAAM,KACrD,CAACgI,MAAM,CAAC2I,MAAM,CAACR,oEAAgC,CAAC,CAAC3B,QAAQ,CAAC+B,UAAU,CAAC;IAC7E,CAAC,CAAC,CACGvV,GAAG,CAAC,UAAAuV,UAAU,EAAI;MACnB,OAAO;QACHpb,KAAK,EAAET,mDAAE,CAACub,kDAAc,CAACM,UAAU,CAAC,EAAE,QAAQ,CAAC;QAC/Clb,KAAK,EAAE6a,kDAAc,CAACK,UAAU;MACpC,CAAC;IACL,CAAC;EACL,CAAC;AACL,CAAC;AACM,SAASjb,aAAaA,CAACD,KAAK,EAAE;EACjC,OAAO2S,MAAM,CAAC2I,MAAM,CAACT,kDAAc,CAAC,CAAC1B,QAAQ,CAACnZ,KAAK,CAAC;AACxD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1C+D;AACZ;AACD;AACF;AACF;AACqD;AAC7C;AACJ;AACQ;AACrB;AACR;AACd,SAASyb,iBAAiBA,CAAArY,IAAA,EAAyB;EAAA,IAAtB0U,YAAY,GAAA1U,IAAA,CAAZ0U,YAAY;IAAEhO,GAAG,GAAA1G,IAAA,CAAH0G,GAAG;EACzD,IAAAC,YAAA,GAA+EL,8DAAW,CAAC,CAAC;IAApEM,QAAQ,GAAAD,YAAA,CAAxBE,cAAc;IAAYjF,OAAO,GAAA+E,YAAA,CAAP/E,OAAO;IAAEyD,KAAK,GAAAsB,YAAA,CAALtB,KAAK;IAAEyB,MAAM,GAAAH,YAAA,CAANG,MAAM;IAAEC,eAAe,GAAAJ,YAAA,CAAfI,eAAe;EACzE,IAAMuR,qBAAqB,GAAGF,sEAAkB,CAAC1R,GAAG,CAAC;EACrD,IAAMM,uBAAuB,GAAGT,8EAA0B,CAACG,GAAG,CAAC;EAC/D9C,gDAAS,CAAC,YAAM;IACZ,IAAI,CAAC8C,GAAG,IAAIE,QAAQ,CAACW,MAAM,GAAG,CAAC,EAAE;MAC7BmN,YAAY,CAAC9N,QAAQ,CAAC,CAAC,CAAC,CAAChK,KAAK,CAAC;IACnC;EACJ,CAAC,EAAE,CAACgK,QAAQ,EAAEF,GAAG,EAAEgO,YAAY,CAAC,CAAC;EACjC,IAAMW,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAIC,MAAM,EAAK;IAClCZ,YAAY,CAACY,MAAM,CAAC1Y,KAAK,CAAC;EAC9B,CAAC;EACD,IAAMuK,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAA,EAAS;IAChC,OAAOJ,eAAe,CAAC,CAAC,CACnB9B,IAAI,CAAC,YAAM;MACZ6B,MAAM,CAAC,CAAC;IACZ,CAAC,CAAC,SACQ,CAAC,UAAAzB,KAAK,EAAI;MAChBmB,+DAAoB,CAAC,4BAA4B,EAAE;QAC/Ca,KAAK,EAAE;UAAEhC,KAAK,EAALA;QAAM;MACnB,CAAC,CAAC;IACN,CAAC,CAAC;EACN,CAAC;EACD,OAAQ5F,sDAAI,CAACsB,2CAAQ,EAAE;IAAEnB,QAAQ,EAAEgC,OAAO,GAAInC,sDAAI,CAACsU,4DAAY,EAAE,CAAC,CAAC,CAAC,GAAI1O,KAAK,GAAI5F,sDAAI,CAAC2D,4DAAY,EAAE;MAAEM,MAAM,EAAG2B,KAAK,IAAIA,KAAK,CAAC3B,MAAM,IAAK2B,KAAK;MAAE+N,eAAe,EAAE,SAAjBA,eAAeA,CAAA;QAAA,OAAQtM,MAAM,CAAC,CAAC;MAAA;MAAEwM,SAAS,EAAE;QAChLC,MAAM,EAAEtX,mDAAE,CAAC,8CAA8C,EAAE,QAAQ,CAAC;QACpEkS,OAAO,EAAElS,mDAAE,CAAC,4DAA4D,EAAE,QAAQ,CAAC;QACnFuX,MAAM,EAAEvX,mDAAE,CAAC,kBAAkB,EAAE,QAAQ;MAC3C;IAAE,CAAC,CAAC,GAAK6E,uDAAK,CAACkS,8DAAc,EAAE;MAAEc,OAAO,EAAE,gBAAgB;MAAEtV,UAAU,EAAEA,+DAAU;MAAEoB,QAAQ,EAAE,CAACoH,uBAAuB,IAAKvH,sDAAI,CAACoI,uDAAc,EAAE;QAAEnE,MAAM,EAAEsD,uBAAuB;QAAEM,iBAAiB,EAAEH;MAAsB,CAAC,CAAE,EAAEP,QAAQ,CAACW,MAAM,GAAG,CAAC,IAAK9H,sDAAI,CAAC0Y,wDAAe,EAAE;QAAEtW,QAAQ,EAAEwT,iBAAiB;QAAE1Y,OAAO,EAAEiK,QAAQ;QAAEhK,KAAK,EAAE0b;MAAsB,CAAC,CAAE;IAAE,CAAC;EAAG,CAAC,CAAC;AACrX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvC+D;AACZ;AACC;AACN;AAC0E;AAC5D;AACI;AACd;AACwB;AACI;AAC9E,SAASE,WAAWA,CAAAxY,IAAA,EAA4G;EAAA,IAA3F0G,GAAG,GAAA1G,IAAA,CAAjBiD,UAAU,CAAIyD,GAAG;IAAInD,UAAU,GAAAvD,IAAA,CAAVuD,UAAU;IAAE/B,aAAa,GAAAxB,IAAA,CAAbwB,aAAa;IAAA6S,YAAA,GAAArU,IAAA,CAAEwD,OAAO;IAAPA,OAAO,GAAA6Q,YAAA,cAAG,IAAI,GAAAA,YAAA;IAAAC,WAAA,GAAAtU,IAAA,CAAEyD,MAAM;IAANA,MAAM,GAAA6Q,WAAA,cAAG,WAAW,GAAAA,WAAA;IAAEC,cAAc,GAAAvU,IAAA,CAAduU,cAAc;EACvH,IAAM7R,oBAAoB,GAAGxB,iFAAuB,CAAC,CAAC;EACtD,IAAMuT,wBAAwB,GAAGxG,kFAAwB,CAAC,CAAC;EAC3D,IAAMyG,YAAY,GAAG,SAAfA,YAAYA,CAAIlN,MAAM,EAAK;IAC7BhG,aAAa,CAAC;MACVkF,GAAG,EAAEc;IACT,CAAC,CAAC;EACN,CAAC;EACD5D,gDAAS,CAAC,YAAM;IACZ6Q,wBAAwB,CAAC;MACrB5P,GAAG,EAAEf,+FAAuC;MAC5CiB,OAAO,EAAE;QACLtB,MAAM,EAANA;MACJ;IACJ,CAAC,CAAC;EACN,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EACZ,OAAO,CAACf,oBAAoB,GAAIjD,sDAAI,CAACsU,4DAAY,EAAE,CAAC,CAAC,CAAC,GAAKjT,uDAAK,CAACC,2CAAQ,EAAE;IAAEnB,QAAQ,EAAE,CAAC,CAAC2D,UAAU,IAAI,CAACmD,GAAG,KAAMjH,sDAAI,CAAC4Y,0DAAiB,EAAE;MAAE3R,GAAG,EAAEA,GAAG;MAAEgO,YAAY,EAAEA;IAAa,CAAC,CAAE,EAAElR,OAAO,IAAIkD,GAAG,IAAKjH,sDAAI,CAAC8Y,uDAAc,EAAE;MAAE7R,GAAG,EAAEA,GAAG;MAAE6N,cAAc,EAAEA;IAAe,CAAC,CAAE;EAAE,CAAC,CAAE;AACpR;AACe,SAASkE,qBAAqBA,CAAChW,KAAK,EAAE;EACjD,OAAQhD,sDAAI,CAACwB,kFAA4B,EAAE;IAAErE,KAAK,EAAEyE,uFAAuB,CAAC,CAAC,IAAID,mFAAwB,CAACpC,iEAAY,CAAC;IAAEY,QAAQ,EAAEH,sDAAI,CAAC+Y,WAAW,EAAA5X,aAAA,KAAO6B,KAAK,CAAE;EAAE,CAAC,CAAC;AACzK;;;;;;;;;;;;;;;;;;;;;;AC9B+D;AACvB;AACQ;AACA;AACX;AACtB,SAAS0V,eAAeA,CAAAnY,IAAA,EAAgC;EAAA,IAA7BrD,OAAO,GAAAqD,IAAA,CAAPrD,OAAO;IAAEkF,QAAQ,GAAA7B,IAAA,CAAR6B,QAAQ;IAAEjF,KAAK,GAAAoD,IAAA,CAALpD,KAAK;EAC9D,IAAM8b,cAAc,GAAG,CACnB;IACIhc,KAAK,EAAET,mDAAE,CAAC,cAAc,EAAE,QAAQ,CAAC;IACnCU,OAAO,EAAPA;EACJ,CAAC,CACJ;EACD,OAAQmE,uDAAK,CAACC,2CAAQ,EAAE;IAAEnB,QAAQ,EAAE,CAACH,sDAAI,CAACyU,8DAAQ,EAAE,CAAC,CAAC,CAAC,EAAEzU,sDAAI,CAAC,GAAG,EAAE;MAAE,cAAc,EAAE,uBAAuB;MAAEG,QAAQ,EAAEH,sDAAI,CAAC,GAAG,EAAE;QAAEG,QAAQ,EAAE3D,mDAAE,CAAC,kCAAkC,EAAE,QAAQ;MAAE,CAAC;IAAE,CAAC,CAAC,EAAEwD,sDAAI,CAACyR,2DAAW,EAAE;MAAEG,cAAc,EAAEqH,cAAc;MAAE7W,QAAQ,EAAEA,QAAQ;MAAEsP,WAAW,EAAElV,mDAAE,CAAC,kBAAkB,EAAE,QAAQ,CAAC;MAAEW,KAAK,EAAEA;IAAM,CAAC,CAAC;EAAE,CAAC,CAAC;AACpV;;;;;;;;;;;;;;;;;;;;;ACbgD;AAEF;AACE;AACY;AACvB;AACtB,SAASiL,cAAcA,CAAA7H,IAAA,EAAiC;EAAA,IAA9B0D,MAAM,GAAA1D,IAAA,CAAN0D,MAAM;IAAE4D,iBAAiB,GAAAtH,IAAA,CAAjBsH,iBAAiB;EAC9D,IAAMQ,cAAc,GAAGpE,MAAM,KAAKkE,qEAA6B;EAC/D,IAAMG,SAAS,GAAGD,cAAc,GAC1B7L,mDAAE,CAAC,gCAAgC,EAAE,QAAQ,CAAC,GAC9CA,mDAAE,CAAC,2BAA2B,EAAE,QAAQ,CAAC;EAC/C,IAAM+L,YAAY,GAAGF,cAAc,GAC7B7L,mDAAE,CAAC,gEAAgE,EAAE,QAAQ,CAAC,GAC9EA,mDAAE,CAAC,yGAAyG,EAAE,QAAQ,CAAC;EAC7H,OAAQwD,sDAAI,CAACkZ,6DAAO,EAAE;IAAE5Q,SAAS,EAAEA,SAAS;IAAEC,YAAY,EAAEA,YAAY;IAAEpI,QAAQ,EAAEkI,cAAc,IAAKrI,sDAAI,CAACqT,8DAAQ,EAAE;MAAE8F,GAAG,EAAE,UAAU;MAAE3Q,EAAE,EAAE,2BAA2B;MAAEC,OAAO,EAAEZ,iBAAiB;MAAE1H,QAAQ,EAAE3D,mDAAE,CAAC,kBAAkB,EAAE,QAAQ;IAAE,CAAC;EAAG,CAAC,CAAC;AAC3P;;;;;;;;;;;;;;;;;;;;ACfgD;AACW;AACT;AACM;AACzC,SAASkY,WAAWA,CAAAnU,IAAA,EAA0B;EAAA,IAAvB0G,GAAG,GAAA1G,IAAA,CAAH0G,GAAG;IAAE6N,cAAc,GAAAvU,IAAA,CAAduU,cAAc;EACrD,IAAMjD,OAAO,GAAG5B,6CAAM,CAAC,IAAI,CAAC;EAC5B9L,gDAAS,CAAC,YAAM;IACZ,IAAI0N,OAAO,CAACU,OAAO,EAAE;MACjB;MACA,IAAM2D,KAAK,GAAG5Y,MAAM,CAAC6Y,MAAM,CAACD,KAAK,IAAI5Y,MAAM,CAAC4Y,KAAK;MACjDA,KAAK,CAAC/O,QAAQ,CAAC4P,MAAM,CAAC,4BAA4B,CAAC;IACvD;EACJ,CAAC,EAAE,CAAC9P,GAAG,EAAE4K,OAAO,CAAC,CAAC;EAClB,IAAIiD,cAAc,EAAE;IAChB,OAAO9U,sDAAI,CAACwU,+DAAe,EAAE,CAAC,CAAC,CAAC;EACpC;EACA,OAAQxU,sDAAI,CAACsB,2CAAQ,EAAE;IAAEnB,QAAQ,EAAE8G,GAAG,IAAKjH,sDAAI,CAAC+V,+DAAS,EAAE;MAAE9C,GAAG,EAAEpB,OAAO;MAAEnR,SAAS,EAAE,2BAA2B;MAAE,UAAU,KAAAC,MAAA,CAAKsG,GAAG;IAAc,CAAC;EAAG,CAAC,CAAC;AAC7J;;;;;;;;;;;;;;;;ACjBO,IAAMmS,2BAA2B,GAAG,6BAA6B;AACjE,IAAMjR,6BAA6B,GAAG,+BAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;ACDhC;AACqC;AACnC;AACqB;AACnE,IAAIkR,IAAI,GAAG,IAAI;AACA,SAASC,mBAAmBA,CAAA,EAAG;EAC1C,IAAM/U,KAAK,GAAGD,uFAA6B,CAAC,CAAC;EAC7C,IAAAE,SAAA,GAAkCN,+CAAQ,CAACE,kEAAmB,CAAC;IAAAM,UAAA,GAAAC,cAAA,CAAAH,SAAA;IAAxDI,SAAS,GAAAF,UAAA;IAAEG,YAAY,GAAAH,UAAA;EAC9B,IAAAI,UAAA,GAA0BZ,+CAAQ,CAAC,IAAI,CAAC;IAAAa,UAAA,GAAAJ,cAAA,CAAAG,UAAA;IAAjCc,KAAK,GAAAb,UAAA;IAAEC,QAAQ,GAAAD,UAAA;EACtB,IAAMwU,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;IACrB,IAAI,CAACF,IAAI,EAAE;MACPxU,YAAY,CAACT,kEAAmB,CAAC;IACrC;EACJ,CAAC;EACD,IAAMiD,MAAM,GAAG,SAATA,MAAMA,CAAA,EAAS;IACjBgS,IAAI,GAAG,IAAI;IACXxU,YAAY,CAACT,kEAAmB,CAAC;IACjCY,QAAQ,CAAC,IAAI,CAAC;EAClB,CAAC;EACDb,gDAAS,CAAC,YAAM;IACZ,IAAIS,SAAS,KAAKR,kEAAmB,IAAI,CAACiV,IAAI,EAAE;MAC5CxU,YAAY,CAACT,gEAAiB,CAAC;MAC/BG,KAAK,CAAC;QACFa,GAAG,EAAEf,8FAAsCsJ;MAC/C,CAAC,CAAC,CACGnI,IAAI,CAAC,UAAAC,IAAI,EAAI;QACd4T,IAAI,GAAG5T,IAAI;QACXZ,YAAY,CAACT,6DAAc,CAAC;MAChC,CAAC,CAAC,SACQ,CAAC,UAAA8S,GAAG,EAAI;QACdlS,QAAQ,CAACkS,GAAG,CAAC;QACbrS,YAAY,CAACT,+DAAgB,CAAC;MAClC,CAAC,CAAC;IACN;EACJ,CAAC,EAAE,CAACQ,SAAS,CAAC,CAAC;EACf,OAAO;IAAEyU,IAAI,EAAJA,IAAI;IAAEG,aAAa,EAAE5U,SAAS;IAAEgB,KAAK,EAALA,KAAK;IAAE2T,UAAU,EAAVA,UAAU;IAAElS,MAAM,EAANA;EAAO,CAAC;AACxE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpCoC;AACC;AACsD;AACzC;AACM;AACV;AACmC;AACd;AACnE,SAASsS,qBAAqBA,CAACC,OAAO,EAAEC,WAAW,EAAEC,YAAY,EAAE;EAC/D,IAAAC,qBAAA,GAAApV,cAAA,CAAyBiV,OAAO,CAACI,eAAe;IAAzCC,cAAc,GAAAF,qBAAA;EACrB,IAAItH,MAAM,GAAGjW,mDAAE,CAAC,SAAS,EAAE,QAAQ,CAAC;EACpC,IAAIqd,WAAW,IACXI,cAAc,KAAKJ,WAAW,CAACrR,EAAE,IACjCsR,YAAY,CAACG,cAAc,CAAC,EAAE;IAC9B,IAAMZ,IAAI,GAAGS,YAAY,CAACG,cAAc,CAAC;IACzCxH,MAAM,SAAA9R,MAAA,CAAS0Y,IAAI,CAACa,WAAW,CAACC,QAAQ,MAAG;EAC/C;EACA,OAAO1H,MAAM;AACjB;AACA,SAAS2H,iBAAiBA,CAACf,IAAI,EAAE;EAC7B,OAAQA,IAAI,IACRA,IAAI,CAACgB,gBAAgB,IACrBhB,IAAI,CAACgB,gBAAgB,CAACC,gBAAgB,IACtCjB,IAAI,CAACgB,gBAAgB,CAACC,gBAAgB,CAACC,KAAK;AACpD;AACe,SAAS1T,WAAWA,CAAA,EAAG;EAClC,IAAMtC,KAAK,GAAGD,uFAA6B,CAAC,CAAC;EAC7C,IAAAkW,iBAAA,GAAqGd,6DAAgB,CAAC,CAAC;IAA/GvS,QAAQ,GAAAqT,iBAAA,CAARrT,QAAQ;IAAE2S,YAAY,GAAAU,iBAAA,CAAZV,YAAY;IAASW,aAAa,GAAAD,iBAAA,CAApB5U,KAAK;IAAiB8U,iBAAiB,GAAAF,iBAAA,CAAjBE,iBAAiB;IAAUC,cAAc,GAAAH,iBAAA,CAAtBnT,MAAM;EAC/E,IAAAuT,oBAAA,GAAoFtB,gEAAmB,CAAC,CAAC;IAA3FO,WAAW,GAAAe,oBAAA,CAAjBvB,IAAI;IAAsBwB,SAAS,GAAAD,oBAAA,CAAhBhV,KAAK;IAAa4T,aAAa,GAAAoB,oBAAA,CAAbpB,aAAa;IAAUsB,UAAU,GAAAF,oBAAA,CAAlBvT,MAAM;EAClE,IAAMA,MAAM,GAAGoS,kDAAW,CAAC,YAAM;IAC7BqB,UAAU,CAAC,CAAC;IACZH,cAAc,CAAC,CAAC;EACpB,CAAC,EAAE,CAACG,UAAU,EAAEH,cAAc,CAAC,CAAC;EAChC,IAAMrT,eAAe,GAAG,SAAlBA,eAAeA,CAAA,EAAS;IAC1B,OAAO/C,KAAK,CAAC;MACTa,GAAG,EAAEf,6FAAqCuJ;IAC9C,CAAC,CAAC;EACN,CAAC;EACD,OAAO;IACHxG,cAAc,EAAED,QAAQ,CAACrE,GAAG,CAAC,UAAAiY,IAAI;MAAA,OAAK;QAClC9d,KAAK,EAAE8d,IAAI,CAACja,IAAI,IAAI6Y,qBAAqB,CAACoB,IAAI,EAAElB,WAAW,EAAEC,YAAY,CAAC;QAC1E3c,KAAK,EAAE4d,IAAI,CAACC;MAChB,CAAC;IAAA,CAAC,CAAC;IACH7T,QAAQ,EAARA,QAAQ;IACR2S,YAAY,EAAZA,YAAY;IACZD,WAAW,EAAXA,WAAW;IACXjU,KAAK,EAAE6U,aAAa,IAAII,SAAS;IACjC1Y,OAAO,EAAEuY,iBAAiB,IAAItW,gEAAiB,IAC3CoV,aAAa,KAAKpV,gEAAiB;IACvCiD,MAAM,EAANA,MAAM;IACNC,eAAe,EAAfA;EACJ,CAAC;AACL;AACO,SAASqR,kBAAkBA,CAAC1R,GAAG,EAAE;EACpC,IAAAC,YAAA,GAAqCL,WAAW,CAAC,CAAC;IAA1BM,QAAQ,GAAAD,YAAA,CAAxBE,cAAc;EACtB,IAAMyO,MAAM,GAAG1O,QAAQ,CAAC5E,IAAI,CAAC,UAAAhC,IAAA;IAAA,IAAGpD,KAAK,GAAAoD,IAAA,CAALpD,KAAK;IAAA,OAAOA,KAAK,KAAK8J,GAAG;EAAA,EAAC;EAC1D,OAAO4O,MAAM;AACjB;AACO,SAAS/O,0BAA0BA,CAACG,GAAG,EAAE;EAC5C,IAAAgU,aAAA,GAAgDpU,WAAW,CAAC,CAAC;IAArDM,QAAQ,GAAA8T,aAAA,CAAR9T,QAAQ;IAAE2S,YAAY,GAAAmB,aAAA,CAAZnB,YAAY;IAAED,WAAW,GAAAoB,aAAA,CAAXpB,WAAW;EAC3C,IAAMD,OAAO,GAAGzS,QAAQ,CAAC5E,IAAI,CAAC,UAAAwY,IAAI;IAAA,OAAIA,IAAI,CAACC,IAAI,KAAK/T,GAAG;EAAA,EAAC;EACxD,IAAMiU,oBAAoB,GAAGpB,YAAY,CAACqB,MAAM,CAAC,UAACC,CAAC,EAAEC,CAAC;IAAA,OAAAla,aAAA,CAAAA,aAAA,KAAWia,CAAC,OAAAhV,eAAA,KAAGiV,CAAC,CAAC7S,EAAE,EAAG6S,CAAC;EAAA,CAAG,EAAE,CAAC,CAAC,CAAC;EACrF,IAAI,CAACzB,OAAO,EAAE;IACV,OAAO,IAAI;EACf,CAAC,MACI;IACD,IAAQI,eAAe,GAAKJ,OAAO,CAA3BI,eAAe;IACvB,IAAIH,WAAW,IACXG,eAAe,CAAC1D,QAAQ,CAACuD,WAAW,CAACrR,EAAE,CAAC,IACxC,CAAC4R,iBAAiB,CAACP,WAAW,CAAC,EAAE;MACjC,OAAO1R,qEAA6B;IACxC,CAAC,MACI,IAAI6R,eAAe,CACnBlX,GAAG,CAAC,UAAA0F,EAAE;MAAA,OAAI0S,oBAAoB,CAAC1S,EAAE,CAAC;IAAA,EAAC,CACnC8S,IAAI,CAAC,UAACjC,IAAI;MAAA,OAAK,CAACe,iBAAiB,CAACf,IAAI,CAAC;IAAA,EAAC,EAAE;MAC3C,OAAOD,mEAA2B;IACtC,CAAC,MACI;MACD,OAAO,IAAI;IACf;EACJ;AACJ;;;;;;;;;;;;;;;;;;;;;;;;;;ACjF4C;AACqC;AACnC;AACqB;AACnE,IAAIjS,QAAQ,GAAG,EAAE;AACjB,IAAI2S,YAAY,GAAG,EAAE;AACN,SAASJ,gBAAgBA,CAAA,EAAG;EACvC,IAAMnV,KAAK,GAAGD,uFAA6B,CAAC,CAAC;EAC7C,IAAAE,SAAA,GAAkCN,+CAAQ,CAACE,kEAAmB,CAAC;IAAAM,UAAA,GAAAC,cAAA,CAAAH,SAAA;IAAxDI,SAAS,GAAAF,UAAA;IAAEG,YAAY,GAAAH,UAAA;EAC9B,IAAAI,UAAA,GAA0BZ,+CAAQ,CAAC,IAAI,CAAC;IAAAa,UAAA,GAAAJ,cAAA,CAAAG,UAAA;IAAjCc,KAAK,GAAAb,UAAA;IAAEC,QAAQ,GAAAD,UAAA;EACtB,IAAMsC,MAAM,GAAG,SAATA,MAAMA,CAAA,EAAS;IACjBF,QAAQ,GAAG,EAAE;IACbnC,QAAQ,CAAC,IAAI,CAAC;IACdH,YAAY,CAACT,kEAAmB,CAAC;EACrC,CAAC;EACDD,gDAAS,CAAC,YAAM;IACZ,IAAIS,SAAS,KAAKR,kEAAmB,IAAI+C,QAAQ,CAACW,MAAM,KAAK,CAAC,EAAE;MAC5DjD,YAAY,CAACT,gEAAiB,CAAC;MAC/BG,KAAK,CAAC;QACFa,GAAG,EAAEf,2FAAmCoJ;MAC5C,CAAC,CAAC,CACGjI,IAAI,CAAC,UAAAC,IAAI,EAAI;QACdZ,YAAY,CAACT,+DAAgB,CAAC;QAC9B+C,QAAQ,GAAG1B,IAAI,IAAIA,IAAI,CAAC8V,YAAY;QACpCzB,YAAY,GAAGrU,IAAI,IAAIA,IAAI,CAACqU,YAAY;MAC5C,CAAC,CAAC,SACQ,CAAC,UAAA3G,CAAC,EAAI;QACZnO,QAAQ,CAACmO,CAAC,CAAC;QACXtO,YAAY,CAACT,+DAAgB,CAAC;MAClC,CAAC,CAAC;IACN;EACJ,CAAC,EAAE,CAACQ,SAAS,CAAC,CAAC;EACf,OAAO;IACHuC,QAAQ,EAARA,QAAQ;IACR2S,YAAY,EAAZA,YAAY;IACZY,iBAAiB,EAAE9V,SAAS;IAC5BgB,KAAK,EAALA,KAAK;IACLyB,MAAM,EAANA;EACJ,CAAC;AACL;;;;;;;;;;;;;;;;;ACvC+D;AAEvB;AAExC,IAAMmU,cAAc,gBAAG5a,sDAAM;EAAAE,IAAA;EAAAC,SAAA;EAAAC,SAAA;AAAA,EAkB5B;AACD,IAAMya,KAAK,gBAAG7a,sDAAM;EAAAE,IAAA;EAAAC,SAAA;EAAAC,SAAA;AAAA,EASnB;AACD,IAAM0a,OAAO,gBAAG9a,sDAAM;EAAAE,IAAA;EAAAC,SAAA;EAAAC,SAAA;AAAA,EAOrB;AACD,IAAM2a,gBAAgB,gBAAG/a,sDAAM;EAAAE,IAAA;EAAAC,SAAA;EAAAC,SAAA;AAAA,EAG9B;AACc,SAASkY,OAAOA,OAAyC;EAAA,IAAtC5Q,SAAS,GAAA/H,IAAA,CAAT+H,SAAS;IAAEC,YAAY,GAAAhI,IAAA,CAAZgI,YAAY;IAAEpI;EACvD,OAAQkB,uDAAK,CAACma,cAAc,EAAE;IAAErb,QAAQ,EAAE,CAACkB,uDAAK,CAACsa,gBAAgB,EAAE;MAAExb,QAAQ,EAAE,CAACH,sDAAI,CAACyb,KAAK,EAAE;QAAEtb,QAAQ,EAAEmI;MAAU,CAAC,CAAC,EAAEtI,sDAAI,CAAC0b,OAAO,EAAE;QAAEvb,QAAQ,EAAEoI;MAAa,CAAC,CAAC;IAAE,CAAC,CAAC,EAAEpI,QAAQ;EAAE,CAAC,CAAC;AACrL;;;;;;;;;;;;;;;;;;AC/CwC;AACU;AAAA,IAAAiQ,KAAA,GAAhC,aAAAA,SAAgCA,MAAA;EAAA,OAG5BpN,eAAK;IAAA,OAAKA,KAAK,CAACmW,GAAG,KAAK,UAAU,GAAGyC,8CAAS,GAAGC,0CAAM;EAAA;AAAA;AAF7E,8EAAejb,sDAAM;EAAAE,IAAA;EAAAC,SAAA;EAAAC,SAAA;EAAAwP,IAAA;IAAA,cAECJ,KAAuD;EAAA;AAAA;;;;;;;;;;;;;;;;;ACJrC;AAAA,IAAAgE,IAAA,GACtB,aAAAA,SADsBA,KAAA;EAAA,OAExBpR,eAAK;IAAA,OAAKA,KAAK,CAACmR,SAAS,GAAGnR,KAAK,CAACmR,SAAS,GAAG,SAAU;EAAA;AAAA;AADxE,8EAAevT,sDAAM;EAAAE,IAAA;EAAAC,SAAA;EAAAC,SAAA;EAAAwP,IAAA;IAAA,cACL4D,IAAwD;EAAA;AAAA;;;;;;;;;;;;;;;;;ACFhC;AACxC,8EAAexT,sDAAM;EAAAE,IAAA;EAAAC,SAAA;EAAAC,SAAA;AAAA;;;;;;;;;;;;;;;;;ACDmB;AACxC,8EAAeJ,sDAAM;EAAAE,IAAA;EAAAC,SAAA;EAAAC,SAAA;AAAA;;;;;;;;;;;;;;;;;;;ACD0C;AAEvB;AACW;AACnD,IAAM+a,YAAY,gBAAGnb,sDAAM;EAAAE,IAAA;EAAAC,SAAA;EAAAC,SAAA;AAAA,EAS1B;AACD,IAAMgb,YAAY,gBAAGpb,sDAAM;EAAAE,IAAA;EAAAC,SAAA;EAAAC,SAAA;AAAA,EAM1B;AAAC,IAAAoT,IAAA,GAnBgB,aAAAA,SAmBhBA,KAAA;EAAA,OAGUpR,eAAK;IAAA,OAAIA,KAAK,CAACiZ,KAAK;EAAA;AAAA;AAFhC,IAAMC,MAAM,gBAAGtb,sDAAM;EAAAE,IAAA;EAAAC,SAAA;EAAAC,SAAA;EAAAwP,IAAA;IAAA,cAET4D,IAAoB;EAAA;AAAA,EAI/B;AAAC,IAAAhE,KAAA,GA1BgB,aAAAA,SA0BhBA,MAAA;EAAA,OAGUpN,eAAK;IAAA,OAAIA,KAAK,CAACiZ,KAAK;EAAA;AAAA;AAFhC,IAAME,cAAc,gBAAGvb,sDAAM;EAAAE,IAAA;EAAAC,SAAA;EAAAC,SAAA;EAAAwP,IAAA;IAAA,cAEjBJ,KAAoB;EAAA;AAAA,EA2B/B;AACc,SAAS7O,SAASA,OAAgB;EAAA,IAAA6a,SAAA,GAAA7b,IAAA,CAAbgU,IAAI;IAAJA,IAAI,GAAA6H,SAAA,cAAG,KAAAA,SAAA;EACvC,OAAQpc,sDAAI,CAAC+b,YAAY,EAAE;IAAE5b,QAAQ,EAAEH,sDAAI,CAACgc,YAAY,EAAE;MAAE7b,QAAQ,EAAEkB,uDAAK,CAAC,KAAK,EAAE;QAAEgb,MAAM,EAAE9H,IAAI;QAAEnB,KAAK,EAAEmB,IAAI;QAAE+H,OAAO,EAAE,WAAW;QAAEnc,QAAQ,EAAE,CAACH,sDAAI,CAACkc,MAAM,EAAE;UAAED,KAAK,EAAE9L,mDAAc;UAAEoM,EAAE,EAAE,IAAI;UAAEC,EAAE,EAAE,IAAI;UAAEC,CAAC,EAAE;QAAO,CAAC,CAAC,EAAEzc,sDAAI,CAACmc,cAAc,EAAE;UAAEF,KAAK,EAAEH,4CAAO;UAAES,EAAE,EAAE,IAAI;UAAEC,EAAE,EAAE,IAAI;UAAEC,CAAC,EAAE;QAAO,CAAC,CAAC;MAAE,CAAC;IAAE,CAAC;EAAE,CAAC,CAAC;AAC9S;;;;;;;;;;;;;;;;;;;;;;;;AC5DO,IAAMX,OAAO,GAAG,SAAS;AACzB,IAAM3L,cAAc,GAAG,SAAS;AAChC,IAAMD,aAAa,GAAG,SAAS;AAC/B,IAAM2L,KAAK,GAAG,SAAS;AACvB,IAAMa,IAAI,GAAG,SAAS;AACtB,IAAMd,SAAS,GAAG,SAAS;AAC3B,IAAMe,cAAc,GAAG,SAAS;AAChC,IAAMC,eAAe,GAAG,SAAS;AACjC,IAAMC,QAAQ,GAAG,SAAS;;;;;;;;;;;;;;;ACRjC,IAAMzZ,gBAAgB,GAAG;EACrBC,SAAS,EAAE,WAAW;EACtBC,YAAY,EAAE;AAClB,CAAC;AACD,iEAAeF,gBAAgB;;;;;;;;;;;;;;;ACJ/B,IAAMgB,SAAS,GAAG;EACdK,SAAS,EAAE,WAAW;EACtBqB,OAAO,EAAE,SAAS;EAClBH,MAAM,EAAE,QAAQ;EAChB+M,IAAI,EAAE,MAAM;EACZ7M,MAAM,EAAE;AACZ,CAAC;AACD,iEAAezB,SAAS;;;;;;;;;;;;;;;;;;;;;;ACPjB,IAAI0Y,mCAAmC;AAC9C,CAAC,UAAUA,mCAAmC,EAAE;EAC5CA,mCAAmC,CAAC,cAAc,CAAC,GAAG,cAAc;EACpEA,mCAAmC,CAAC,OAAO,CAAC,GAAG,OAAO;EACtDA,mCAAmC,CAAC,YAAY,CAAC,GAAG,YAAY;EAChEA,mCAAmC,CAAC,YAAY,CAAC,GAAG,YAAY;EAChEA,mCAAmC,CAAC,oBAAoB,CAAC,GAAG,oBAAoB;EAChFA,mCAAmC,CAAC,mBAAmB,CAAC,GAAG,mBAAmB;EAC9EA,mCAAmC,CAAC,gBAAgB,CAAC,GAAG,gBAAgB;EACxEA,mCAAmC,CAAC,eAAe,CAAC,GAAG,eAAe;EACtEA,mCAAmC,CAAC,SAAS,CAAC,GAAG,SAAS;AAC9D,CAAC,EAAEA,mCAAmC,KAAKA,mCAAmC,GAAG,CAAC,CAAC,CAAC,CAAC;AAC9E,IAAI7E,gCAAgC;AAC3C,CAAC,UAAUA,gCAAgC,EAAE;EACzCA,gCAAgC,CAAC,SAAS,CAAC,GAAG,SAAS;EACvDA,gCAAgC,CAAC,cAAc,CAAC,GAAG,cAAc;AACrE,CAAC,EAAEA,gCAAgC,KAAKA,gCAAgC,GAAG,CAAC,CAAC,CAAC,CAAC;AACxE,IAAMF,cAAc,GAAA3R,eAAA,CAAAA,eAAA,CAAAA,eAAA,CAAAA,eAAA,CAAAA,eAAA,CAAAA,eAAA,CAAAA,eAAA,KACtB0W,mCAAmC,CAACC,KAAK,EAAG,YAAY,GACxDD,mCAAmC,CAACE,UAAU,EAAG,iBAAiB,GAClEF,mCAAmC,CAACG,UAAU,EAAG,iBAAiB,GAClEH,mCAAmC,CAACI,kBAAkB,EAAG,yBAAyB,GAClFJ,mCAAmC,CAACK,iBAAiB,EAAG,wBAAwB,GAChFL,mCAAmC,CAACM,cAAc,EAAG,qBAAqB,GAC1EN,mCAAmC,CAACO,aAAa,EAAG,oBAAoB,CAC5E;AACM,IAAMrF,cAAc,GAAA5R,eAAA,CAAAA,eAAA,CAAAA,eAAA,CAAAA,eAAA,CAAAA,eAAA,CAAAA,eAAA,CAAAA,eAAA,KACtB0W,mCAAmC,CAACC,KAAK,EAAG,OAAO,GACnDD,mCAAmC,CAACE,UAAU,EAAG,YAAY,GAC7DF,mCAAmC,CAACG,UAAU,EAAG,YAAY,GAC7DH,mCAAmC,CAACI,kBAAkB,EAAG,oBAAoB,GAC7EJ,mCAAmC,CAACK,iBAAiB,EAAG,mBAAmB,GAC3EL,mCAAmC,CAACM,cAAc,EAAG,gBAAgB,GACrEN,mCAAmC,CAACO,aAAa,EAAG,eAAe,CACvE;;;;;;;;;;;;;;;;;;;AClCsB;AAC8B;AAC9C,SAASE,OAAOA,CAACC,MAAM,EAAE;EAC5B3O,0DAAc,CAAC,CAAC;EAChB9H,0DAAa,CAACyW,MAAM,CAAC;AACzB;AACO,SAASE,cAAcA,CAACF,MAAM,EAAE;EACnC,SAASG,IAAIA,CAAA,EAAG;IACZL,6CAAC,CAACE,MAAM,CAAC;EACb;EACAD,OAAO,CAACI,IAAI,CAAC;AACjB;;;;;;;;;;;;;;;;;;ACX6G;AACxE;AAC9B,SAASC,iBAAiBA,CAACJ,MAAM,EAAE;EACtC,SAASG,IAAIA,CAAA,EAAG;IACZ,IAAIE,KAAK,CAACC,OAAO,CAACN,MAAM,CAAC,EAAE;MACvBA,MAAM,CAACO,OAAO,CAAC,UAAA/U,QAAQ;QAAA,OAAIA,QAAQ,CAAC,CAAC;MAAA,EAAC;IAC1C,CAAC,MACI;MACDwU,MAAM,CAAC,CAAC;IACZ;EACJ;EACAD,kDAAO,CAACI,IAAI,CAAC;AACjB;AACA,IAAMK,eAAe,GAAG,SAAlBA,eAAeA,CAAA,EAAS;EAC1B,OAAO;IACHtf,mBAAmB,EAAnBA,wEAAmBA;EACvB,CAAC;AACL,CAAC;AACM,IAAMiD,wBAAwB,GAAG,SAA3BA,wBAAwBA,CAAA,EAA0B;EAAA,IAAtBpC,YAAY,GAAA0J,SAAA,CAAAnB,MAAA,QAAAmB,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,EAAE;EACtD,IAAI3L,MAAM,CAAC2gB,mBAAmB,EAAE;IAC5B,OAAO3gB,MAAM,CAAC2gB,mBAAmB;EACrC;EACA,IAAAC,OAAA,GAAwD5gB,MAAM;IAAtD6gB,qBAAqB,GAAAD,OAAA,CAArBC,qBAAqB;IAAEC,oBAAoB,GAAAF,OAAA,CAApBE,oBAAoB;EACnD,IAAMlhB,OAAO,GAAG,IAAIkhB,oBAAoB,CAAC,CAAC,CACrCC,SAAS,CAACzf,2DAAM,CAAC,CACjB0f,WAAW,CAAC1gB,6DAAQ,CAAC,CACrB2gB,eAAe,CAACP,eAAe,CAAC,CAAC,CAAC,CAClCQ,eAAe,CAACjf,YAAY,CAACkf,IAAI,CAAC,CAAC,CAAC;EACzC,IAAMC,QAAQ,GAAG,IAAIP,qBAAqB,CAAC,yBAAyB,EAAEhf,6DAAQ,EAAEhB,mEAAc,EAAE,YAAM,CAAE,CAAC,CAAC,CAACkU,UAAU,CAACnV,OAAO,CAAC;EAC9HwhB,QAAQ,CAACC,QAAQ,CAACnI,QAAQ,CAACoI,IAAI,EAAE,KAAK,CAAC;EACvCF,QAAQ,CAACG,mBAAmB,CAAC,CAAC,CAAC,CAAC;EAChCvhB,MAAM,CAAC2gB,mBAAmB,GAAGS,QAAQ;EACrC,OAAOphB,MAAM,CAAC2gB,mBAAmB;AACrC,CAAC;;;;;;;;;;;;;;;;ACjCwD;AAClD,SAASrc,uBAAuBA,CAAA,EAAG;EACtC,OAAO,CAAC,EAAErC,iEAAY,IAAIA,sEAAiB,CAAC,CAAC,CAAC;AAClD;;;;;;;;;;ACHA,WAAW,mBAAO,CAAC,+CAAS;;AAE5B;AACA;;AAEA;;;;;;;;;;;ACLA,aAAa,mBAAO,CAAC,mDAAW;AAChC,gBAAgB,mBAAO,CAAC,yDAAc;AACtC,qBAAqB,mBAAO,CAAC,mEAAmB;;AAEhD;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd,aAAa,QAAQ;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;;;;;;;;AC3BA,sBAAsB,mBAAO,CAAC,qEAAoB;;AAElD;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,aAAa,QAAQ;AACrB;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;;;;;;;;AClBA;AACA,wBAAwB,qBAAM,gBAAgB,qBAAM,IAAI,qBAAM,sBAAsB,qBAAM;;AAE1F;;;;;;;;;;;ACHA,aAAa,mBAAO,CAAC,mDAAW;;AAEhC;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd,aAAa,QAAQ;AACrB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,IAAI;;AAEJ;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;;AAEA;;;;;;;;;;;AC7CA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd,aAAa,QAAQ;AACrB;AACA;AACA;AACA;;AAEA;;;;;;;;;;;ACrBA,iBAAiB,mBAAO,CAAC,2DAAe;;AAExC;AACA;;AAEA;AACA;;AAEA;;;;;;;;;;;ACRA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,aAAa,QAAQ;AACrB;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;;;;;;;;;;AClBA,eAAe,mBAAO,CAAC,qDAAY;AACnC,UAAU,mBAAO,CAAC,2CAAO;AACzB,eAAe,mBAAO,CAAC,qDAAY;;AAEnC;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,UAAU;AACrB,WAAW,QAAQ;AACnB,WAAW,QAAQ,WAAW;AAC9B,WAAW,SAAS;AACpB;AACA,WAAW,QAAQ;AACnB;AACA,WAAW,SAAS;AACpB;AACA,aAAa,UAAU;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA,+CAA+C,iBAAiB;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;;;;;;;;AC9LA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd,aAAa,SAAS;AACtB;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;;;;;;;;AC9BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd,aAAa,SAAS;AACtB;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;;;;;;;;AC5BA,iBAAiB,mBAAO,CAAC,2DAAe;AACxC,mBAAmB,mBAAO,CAAC,6DAAgB;;AAE3C;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd,aAAa,SAAS;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;;;;;;;;AC5BA,WAAW,mBAAO,CAAC,+CAAS;;AAE5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,QAAQ;AACrB;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;;AAEA;;;;;;;;;;;ACtBA,eAAe,mBAAO,CAAC,uDAAa;AACpC,eAAe,mBAAO,CAAC,qDAAY;AACnC,eAAe,mBAAO,CAAC,qDAAY;;AAEnC;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd,aAAa,QAAQ;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;;;;;;;;;;AC/DA;;;;;;;;;;;;;ACAA;;;;;;;;;;;;;ACAA;;;;;;;;;;;;;ACAA;;;;;;;;;;;;;ACAA;;;;;;;;;;;;;ACAA;;;;;;;;;;;;;ACAA;;;;;;;;;;;;;ACAA;;;;;;;;;;;;;ACAA;;;;;;;;;;;;;ACAA;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;;;;;;;;ACPA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA,gBAAgB,+CAA+C;;AAE/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;;;;;;;;;ACtCA;;AAEA,eAAe,mBAAO,CAAC,wFAA6B;AACpD,gBAAgB,mBAAO,CAAC,gHAAyC;AACjE,uBAAuB,mBAAO,CAAC,iEAAe;;AAE9C,YAAY,mBAAO,CAAC,qDAAS;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,wBAAwB,2FAA+B;;AAEvD;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa,qBAAM,mBAAmB,qBAAM;AAC5C;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,UAAU;AACV;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA,eAAe,QAAQ;AACvB,eAAe,QAAQ;AACvB,gBAAgB;AAChB;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,OAAO;AACP;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;;AAEP;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,UAAU;AACV;AACA,MAAM;AACN;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,UAAU;AACV;AACA,MAAM;AACN;AACA;AACA;;AAEA;;AAEA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA,eAAe,QAAQ;AACvB;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA,eAAe,QAAQ;AACvB,eAAe,UAAU;AACzB,eAAe,OAAO;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA,eAAe,QAAQ;AACvB,eAAe,UAAU;AACzB,eAAe,UAAU;AACzB,gBAAgB,UAAU;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA,eAAe,OAAO;AACtB,eAAe,QAAQ;AACvB,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA,eAAe,QAAQ;AACvB,eAAe,QAAQ;AACvB,gBAAgB;AAChB;AACA;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,OAAO;AACP;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;;AAEA;AACA;;AAEA;AACA;AACA,QAAQ;AACR;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA,eAAe,QAAQ;AACvB,gBAAgB;AAChB;AACA;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA,eAAe,QAAQ;AACvB,gBAAgB;AAChB;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA,eAAe,QAAQ;AACvB,gBAAgB;AAChB;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA,gBAAgB,QAAQ;AACxB;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA,eAAe,QAAQ;AACvB,gBAAgB;AAChB;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA,eAAe,QAAQ;AACvB,gBAAgB;AAChB;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA,eAAe,UAAU;AACzB;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA,eAAe,UAAU;AACzB;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA,eAAe,UAAU;AACzB;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA,eAAe,UAAU;AACzB;AACA;AACA,gBAAgB;AAChB;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA,sCAAsC;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,GAAG;;AAEH;AACA;AACA;;AAEA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iCAAiC;AACjC;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;;AAEA;AACA;AACA;AACA,OAAO;AACP;AACA,GAAG;;AAEH;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,+BAA+B;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,iBAAiB;AACzC;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,yBAAyB;AAC7C;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,SAAS,GAAG;AACZ;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,SAAS;AACT;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;;AAEA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;;AAEA;AACA,4BAA4B,kBAAkB;AAC9C;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA,cAAc;AACd;AACA;AACA;AACA;;AAEA;AACA;AACA,SAAS;AACT;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,iBAAiB;AAC7C;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa;;AAEb;AACA;;AAEA;AACA,aAAa;AACb;AACA,SAAS;AACT;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,+CAA+C;AAC/C;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA,OAAO;AACP;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,GAAG;;AAEH;AACA;AACA,cAAc;AACd;;AAEA;AACA;AACA,MAAM;AACN;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;;AAEP;AACA;AACA,wBAAwB,iDAAiD;AACzE;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,0CAA0C;AAC1C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA,OAAO;AACP;AACA;;AAEA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,oBAAoB,+BAA+B;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,2BAA2B;AAC3B,sBAAsB,qBAAqB;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,0CAA0C;AAC1C,MAAM;AACN;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA;AACA;AACA;AACA,OAAO;AACP;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA,0CAA0C;AAC1C,2CAA2C;;AAE3C;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA,GAAG;;AAEH;AACA;AACA,GAAG;;AAEH;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,SAAS;AACT;AACA,OAAO;AACP;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,KAAK;AACL,GAAG;;AAEH;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA,MAAM;AACN,2EAA2E;AAC3E;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;;;;;;;;;;ACr4DA;AACA;AACA;AACA;AACA;;AAEA,uBAAuB,mBAAO,CAAC,qDAAS;;AAExC;AACA;AACA;AACA;AACA,aAAa,qBAAM,mBAAmB,qBAAM;AAC5C;;AAEA;;AAEA;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;;;;;;;;;;AC9BA;AACA;AACA;AACA,aAAa,qBAAM,mBAAmB,qBAAM;;AAE5C;AACA;AACA;;AAEA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,mCAAmC;AACnC;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,oCAAoC;AACpC;AACA;;AAEA;AACA;AACA,wBAAwB;AACxB;AACA,IAAI;AACJ;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA,kBAAkB,OAAO;AACzB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAS,SAAS;AAClB;AACA;AACA;AACA;AACA,iDAAiD;AACjD,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,gBAAgB,oBAAoB;AACpC;AACA;AACA;AACA;AACA,cAAc,0BAA0B;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA,kCAAkC;AAClC;AACA,kBAAkB,oBAAoB;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,iBAAiB,UAAU;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AChYA,YAAY,mBAAO,CAAC,6DAAiB;;AAErC;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa,qBAAM,mBAAmB,qBAAM;;AAE5C;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,qDAAqD,KAAK;AAC1D,uDAAuD,KAAK;AAC5D;AACA,WAAW,aAAa,YAAY;AACpC;AACA;AACA;AACA,8BAA8B;AAC9B;AACA;AACA,+DAA+D;AAC/D;AACA,+DAA+D;AAC/D;AACA;AACA;AACA;AACA;AACA,oCAAoC;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,eAAe,UAAU;AACzB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,eAAe,UAAU;AACzB;AACA;AACA,sCAAsC,QAAQ;AAC9C;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,eAAe,oBAAoB;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,eAAe,QAAQ;AACvB,eAAe,QAAQ;AACvB,eAAe,iBAAiB;AAChC;AACA,eAAe,kBAAkB;AACjC;AACA,eAAe,QAAQ;AACvB;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN,8BAA8B;;AAE9B;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;;AAEA;AACA,yBAAyB;AACzB;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,eAAe,OAAO;AACtB,eAAe,UAAU;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB,QAAQ;AACR;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA,gBAAgB;AAChB;AACA;;AAEA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B;;AAE1B;AACA;AACA;AACA,eAAe,OAAO;AACtB,gBAAgB,qBAAqB;AACrC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,sCAAsC,OAAO;AAC7C;AACA,qEAAqE;AACrE,iEAAiE;AACjE;AACA;AACA,kCAAkC;AAClC,kCAAkC;AAClC,kCAAkC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA,2BAA2B;AAC3B,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,eAAe,oBAAoB;AACnC;AACA,eAAe,QAAQ;AACvB,eAAe,iBAAiB;AAChC;AACA,eAAe,SAAS;AACxB;AACA,gBAAgB,SAAS;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,0BAA0B;AAC1B,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,MAAM;AACN;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,QAAQ,0CAA0C;AAClD,eAAe,OAAO;AACtB,gBAAgB,qBAAqB;AACrC;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,QAAQ;AACR;AACA;;AAEA;AACA;AACA,qEAAqE;AACrE,UAAU;AACV;;AAEA;AACA;AACA,QAAQ;AACR;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,eAAe,OAAO;AACtB,eAAe,kBAAkB;AACjC;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,CAAC;;AAED;;;;;;;;;;;AC9mBA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,oBAAoB;;AAEpB;AACA,kBAAkB,qBAAqB;AACvC;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,MAAM;AACN;AACA;;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACzEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEa;;AAEb,IAAI,IAAqC;AACzC;AACA;;AAEA,YAAY,mBAAO,CAAC,oBAAO;;AAE3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,iGAAiG,eAAe;AAChH;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,MAAM;;;AAGN;AACA;AACA,KAAK,GAAG;;AAER,kDAAkD;AAClD;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA,4BAA4B;AAC5B;AACA,qCAAqC;;AAErC,gCAAgC;AAChC;AACA;;AAEA,gCAAgC;;AAEhC;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,IAAI;;;AAGJ;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,EAAE;;;AAGF;AACA;AACA,EAAE;;;AAGF;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,YAAY;AACZ;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uCAAuC;;AAEvC;AACA;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA,sBAAsB;AACtB;AACA,SAAS;AACT,uBAAuB;AACvB;AACA,SAAS;AACT,uBAAuB;AACvB;AACA,SAAS;AACT,wBAAwB;AACxB;AACA,SAAS;AACT,wBAAwB;AACxB;AACA,SAAS;AACT,iCAAiC;AACjC;AACA,SAAS;AACT,2BAA2B;AAC3B;AACA,SAAS;AACT,OAAO;AACP;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA,MAAM;;;AAGN;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,2DAA2D;;AAE3D;AACA;;AAEA;AACA,yDAAyD;AACzD;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;;;AAGT;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;;AAEP;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;;AAEA;AACA,QAAQ;AACR;AACA;AACA,UAAU;AACV;AACA;;AAEA;AACA;AACA,MAAM;AACN;AACA;AACA,QAAQ;AACR;AACA;;AAEA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,aAAa,kBAAkB;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;;AAEA;AACA;AACA,gFAAgF;AAChF;AACA;;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,kBAAkB;;;AAGlB;AACA;AACA,cAAc;AACd;;AAEA;AACA;AACA;AACA;AACA,IAAI;AACJ;;AAEA;AACA;AACA;AACA;;AAEA;AACA,IAAI;;;AAGJ;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,8BAA8B;AAC9B;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,2HAA2H;AAC3H;AACA;AACA;;AAEA;AACA,UAAU;AACV;AACA;;AAEA;AACA;;AAEA,oEAAoE;;AAEpE;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA,iCAAiC;;AAEjC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;;;AAGF;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,wCAAwC;AACxC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd,WAAW,GAAG;AACd,WAAW,GAAG;AACd,WAAW,eAAe;AAC1B,WAAW,GAAG;AACd,WAAW,GAAG;AACd;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK,GAAG;;AAER;AACA;AACA;AACA;AACA;AACA,KAAK,GAAG;AACR;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,GAAG;AACd,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB;;AAEA;AACA;AACA,kBAAkB;;AAElB;AACA;AACA,oBAAoB;AACpB,2DAA2D,UAAU;AACrE,yBAAyB,UAAU;AACnC;AACA,aAAa,UAAU;AACvB;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,MAAM;;;AAGN;AACA;AACA;AACA;AACA,MAAM;;;AAGN;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,SAAS;AACpB,YAAY,SAAS;AACrB;AACA;;;AAGA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,cAAc;AACzB,WAAW,GAAG;AACd;;;AAGA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA,6DAA6D;AAC7D;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,WAAW;AACtB,WAAW,GAAG;AACd;;;AAGA;AACA;AACA;AACA;AACA;;AAEA;AACA,sBAAsB,iBAAiB;AACvC;;AAEA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,MAAM;AACN;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,cAAc;AACzB;;;AAGA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,MAAM;AACN;AACA;;AAEA;AACA;AACA;AACA;AACA,MAAM;AACN,4CAA4C;;AAE5C;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,cAAc;AACzB;;;AAGA;AACA;AACA;;AAEA,oBAAoB,iBAAiB;AACrC;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,8CAA8C;AAC9C;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,QAAQ;AACR;AACA;;AAEA;;AAEA;AACA;AACA,QAAQ;AACR;AACA,QAAQ;AACR;AACA;AACA,QAAQ;AACR;AACA;;AAEA;AACA;;AAEA,0DAA0D;AAC1D;;AAEA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;;AAGA;AACA;;AAEA;AACA;AACA;AACA,4BAA4B,qBAAqB;AACjD;AACA;;AAEA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,gDAAgD,gDAAgD,MAAM,aAAa;;AAEnH;AACA,iDAAiD,kCAAkC,OAAO;;AAE1F,yGAAyG,cAAc,UAAU,gGAAgG,kBAAkB,UAAU,UAAU;;AAEvQ;AACA;AACA;AACA;;AAEA;AACA;AACA,MAAM;AACN;AACA;;AAEA;AACA;AACA,EAAE;AACF;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,sCAAsC;AACtC;;AAEA;;AAEA,gBAAgB;AAChB,WAAW;AACX,YAAY;AACZ,GAAG;AACH;;;;;;;;;;;;ACpzCa;;AAEb,IAAI,KAAqC,EAAE,EAE1C,CAAC;AACF,EAAE,+IAAkE;AACpE;;;;;;;;;;;;ACNA;;;;;;;;;;;ACAA;;;;;;;;;;;ACAA;;;;;;;;;;;ACAA;;;;;;;;;;;;;;;;;;ACAA;AAC+C;AACrB;AACS;AACnC;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,oCAAoC,8DAAS,oBAAoB,SAAS,8DAAS,GAAG,EAAE,8DAAS;AACjG;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,MAAM,IAAqC;AAC3C;AACA;AACA;AACA;AACA;AACA,wCAAwC,YAAY,sBAAsB,cAAc;AACxF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,KAA+B,EAAE,EAKpC;AACH;AACA,QAAQ,IAAwE;AAChF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,sDAAsD;AACpE;AACA;AACA;AACA;AACA;AACA;AACA,iDAAiD,iDAAE,wDAAwD,iDAAE;AAC7G,cAAc,OAAO;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,KAAK,QAAQ,MAAM,EAAE,KAAK;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA,eAAe,gDAAmB;AAClC;AACA,aAAa,gDAAmB;AAChC;AACA,mBAAmB,6CAAgB,GAAG,6CAAgB;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,KAAqC;AAC1D;AACA;AACA;AACA,CAAC,IAAI,CAAM;AAGT;AACF;;;;;;;;;;;;;;;;AC7GA;AACA;AACA;AACA,MAAM,KAA+B,EAAE,EAEpC;AACH;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAIE;AACF;;;;;;UC1CA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;WCtBA;WACA;WACA;WACA;WACA;WACA,iCAAiC,WAAW;WAC5C;WACA;;;;;WCPA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA;;;;;WCPA;WACA;WACA;WACA;WACA,GAAG;WACH;WACA;WACA,CAAC;;;;;WCPD;;;;;WCAA;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D;;;;;;;;;;;;;;;;ACN2D;AACiB;AACZ;AACsB;AACtF,IAAMuf,wBAAwB,GAAG,GAAG;AACpC,IAAMC,gBAAgB,GAAG,KAAK;AAC9B,IAAMC,wBAAwB,GAAG,SAA3BA,wBAAwBA,CAAA,EAAS;EACnCpB,4EAAiB,CAAC,YAAM;IACpB,IAAIqB,UAAU;IACd,IAAIC,cAAc;IAClB,IAAMC,wBAAwB,GAAGrW,sEAAe;IAChD;IACAxL,MAAM,CAACyL,SAAS,EAAE;MACdsB,UAAU,EAAE,cAAc;MAC1BR,eAAe,EAAE,gCAAgC;MACjDG,iBAAiB,EAAE;IACvB,CAAC,EAAE,UAAC/D,gBAAgB,EAAEC,eAAe,EAAEzC,QAAQ,EAAK;MAChDwb,UAAU,GAAG,IAAIjZ,gFAAkB,CAACC,gBAAgB,EAAEC,eAAe,EAAEzC,QAAQ,CAAC;MAChFwb,UAAU,CAACvb,MAAM,CAAC,CAAC;IACvB,CAAC,EAAE,YAAM;MACLub,UAAU,CAACxY,IAAI,CAAC,CAAC;IACrB,CAAC,CAAC;IACF,IAAM2Y,2BAA2B,GAAGtW,sEAAe;IACnD;IACAxL,MAAM,CAACyL,SAAS,EAAE;MACdsB,UAAU,EAAE,iBAAiB;MAC7BR,eAAe,EAAE,mCAAmC;MACpDG,iBAAiB,EAAE;IACvB,CAAC,EAAE,UAAC/D,gBAAgB,EAAEC,eAAe,EAAEzC,QAAQ,EAAK;MAChDyb,cAAc,GAAG,IAAIrW,sFAAsB,CAAC5C,gBAAgB,EAAEC,eAAe,EAAEzC,QAAQ,CAAC;MACxFyb,cAAc,CAACxb,MAAM,CAAC,CAAC;IAC3B,CAAC,EAAE,YAAM;MACLwb,cAAc,CAACzY,IAAI,CAAC,CAAC;IACzB,CAAC,CAAC;IACF;IACAnJ,MAAM,CAACyL,SAAS,CAACsW,cAAc,CAAC,kBAAkB,EAAEF,wBAAwB,CAAC;IAC7E;IACA7hB,MAAM,CAACyL,SAAS,CAACsW,cAAc,CAAC,qBAAqB,EAAED,2BAA2B,CAAC;EACvF,CAAC,CAAC;AACN,CAAC;AACD,IAAME,qBAAqB,GAAGC,WAAW,CAAC,YAAM;EAC5C,IAAMrV,iBAAiB,GAAG5M,MAAM,CAAC4M,iBAAiB;EAClD,IAAIA,iBAAiB,EAAE;IACnB8U,wBAAwB,CAAC,CAAC;IAC1BQ,aAAa,CAACF,qBAAqB,CAAC;EACxC;AACJ,CAAC,EAAER,wBAAwB,CAAC;AAC5BW,UAAU,CAAC,YAAM;EACbD,aAAa,CAACF,qBAAqB,CAAC;AACxC,CAAC,EAAEP,gBAAgB,CAAC,C", "sources": ["webpack://leadin/./node_modules/@emotion/memoize/dist/emotion-memoize.esm.js", "webpack://leadin/./node_modules/@linaria/react/node_modules/@emotion/is-prop-valid/dist/emotion-is-prop-valid.esm.js", "webpack://leadin/./scripts/constants/defaultFormOptions.ts", "webpack://leadin/./scripts/constants/leadinConfig.ts", "webpack://leadin/./scripts/elementor/Common/ConnectPluginBanner.tsx", "webpack://leadin/./scripts/elementor/Common/ElementorBanner.tsx", "webpack://leadin/./scripts/elementor/Common/ElementorButton.tsx", "webpack://leadin/./scripts/elementor/FormWidget/ElementorFormSelect.tsx", "webpack://leadin/./scripts/elementor/FormWidget/FormControlController.tsx", "webpack://leadin/./scripts/elementor/FormWidget/FormWidgetController.tsx", "webpack://leadin/./scripts/elementor/FormWidget/hooks/useForms.ts", "webpack://leadin/./scripts/elementor/FormWidget/registerFormWidget.ts", "webpack://leadin/./scripts/elementor/MeetingWidget/ElementorMeetingSelect.tsx", "webpack://leadin/./scripts/elementor/MeetingWidget/ElementorMeetingWarning.tsx", "webpack://leadin/./scripts/elementor/MeetingWidget/MeetingControlController.tsx", "webpack://leadin/./scripts/elementor/MeetingWidget/MeetingWidgetController.tsx", "webpack://leadin/./scripts/elementor/MeetingWidget/registerMeetingWidget.ts", "webpack://leadin/./scripts/elementor/elementorWidget.ts", "webpack://leadin/./scripts/iframe/integratedMessages/core/CoreMessages.ts", "webpack://leadin/./scripts/iframe/integratedMessages/forms/FormsMessages.ts", "webpack://leadin/./scripts/iframe/integratedMessages/index.ts", "webpack://leadin/./scripts/iframe/integratedMessages/livechat/LiveChatMessages.ts", "webpack://leadin/./scripts/iframe/integratedMessages/plugin/PluginMessages.ts", "webpack://leadin/./scripts/iframe/integratedMessages/proxy/ProxyMessages.ts", "webpack://leadin/./scripts/iframe/useBackgroundApp.ts", "webpack://leadin/./scripts/lib/Raven.ts", "webpack://leadin/./scripts/shared/Common/AsyncSelect.tsx", "webpack://leadin/./scripts/shared/Common/ErrorHandler.tsx", "webpack://leadin/./scripts/shared/Common/HubspotWrapper.ts", "webpack://leadin/./scripts/shared/Common/LoadingBlock.tsx", "webpack://leadin/./scripts/shared/Common/PreviewDisabled.tsx", "webpack://leadin/./scripts/shared/Form/FormEdit.tsx", "webpack://leadin/./scripts/shared/Form/FormSelect.tsx", "webpack://leadin/./scripts/shared/Form/FormSelector.tsx", "webpack://leadin/./scripts/shared/Form/PreviewForm.tsx", "webpack://leadin/./scripts/shared/Form/hooks/useCreateFormFromTemplate.ts", "webpack://leadin/./scripts/shared/Form/hooks/useForms.ts", "webpack://leadin/./scripts/shared/Form/hooks/useGetTemplateAvailability.ts", "webpack://leadin/./scripts/shared/Meeting/MeetingController.tsx", "webpack://leadin/./scripts/shared/Meeting/MeetingEdit.tsx", "webpack://leadin/./scripts/shared/Meeting/MeetingSelector.tsx", "webpack://leadin/./scripts/shared/Meeting/MeetingWarning.tsx", "webpack://leadin/./scripts/shared/Meeting/PreviewMeeting.tsx", "webpack://leadin/./scripts/shared/Meeting/constants.ts", "webpack://leadin/./scripts/shared/Meeting/hooks/useCurrentUserFetch.ts", "webpack://leadin/./scripts/shared/Meeting/hooks/useMeetings.ts", "webpack://leadin/./scripts/shared/Meeting/hooks/useMeetingsFetch.ts", "webpack://leadin/./scripts/shared/UIComponents/UIAlert.tsx", "webpack://leadin/./scripts/shared/UIComponents/UIButton.ts", "webpack://leadin/./scripts/shared/UIComponents/UIContainer.ts", "webpack://leadin/./scripts/shared/UIComponents/UIOverlay.ts", "webpack://leadin/./scripts/shared/UIComponents/UISpacer.ts", "webpack://leadin/./scripts/shared/UIComponents/UISpinner.tsx", "webpack://leadin/./scripts/shared/UIComponents/colors.ts", "webpack://leadin/./scripts/shared/enums/connectionStatus.ts", "webpack://leadin/./scripts/shared/enums/loadState.ts", "webpack://leadin/./scripts/shared/types.ts", "webpack://leadin/./scripts/utils/appUtils.ts", "webpack://leadin/./scripts/utils/backgroundAppUtils.ts", "webpack://leadin/./scripts/utils/isRefreshTokenAvailable.ts", "webpack://leadin/./node_modules/lodash/_Symbol.js", "webpack://leadin/./node_modules/lodash/_baseGetTag.js", "webpack://leadin/./node_modules/lodash/_baseTrim.js", "webpack://leadin/./node_modules/lodash/_freeGlobal.js", "webpack://leadin/./node_modules/lodash/_getRawTag.js", "webpack://leadin/./node_modules/lodash/_objectToString.js", "webpack://leadin/./node_modules/lodash/_root.js", "webpack://leadin/./node_modules/lodash/_trimmedEndIndex.js", "webpack://leadin/./node_modules/lodash/debounce.js", "webpack://leadin/./node_modules/lodash/isObject.js", "webpack://leadin/./node_modules/lodash/isObjectLike.js", "webpack://leadin/./node_modules/lodash/isSymbol.js", "webpack://leadin/./node_modules/lodash/now.js", "webpack://leadin/./node_modules/lodash/toNumber.js", "webpack://leadin/./scripts/elementor/Common/ElementorButton.tsx?a429", "webpack://leadin/./scripts/elementor/MeetingWidget/ElementorMeetingWarning.tsx?284e", "webpack://leadin/./scripts/shared/Common/AsyncSelect.tsx?df11", "webpack://leadin/./scripts/shared/Common/HubspotWrapper.ts?a346", "webpack://leadin/./scripts/shared/UIComponents/UIAlert.tsx?0f15", "webpack://leadin/./scripts/shared/UIComponents/UIButton.ts?d089", "webpack://leadin/./scripts/shared/UIComponents/UIContainer.ts?e576", "webpack://leadin/./scripts/shared/UIComponents/UIOverlay.ts?c9f9", "webpack://leadin/./scripts/shared/UIComponents/UISpacer.ts?8e1e", "webpack://leadin/./scripts/shared/UIComponents/UISpinner.tsx?4a02", "webpack://leadin/./node_modules/raven-js/src/configError.js", "webpack://leadin/./node_modules/raven-js/src/console.js", "webpack://leadin/./node_modules/raven-js/src/raven.js", "webpack://leadin/./node_modules/raven-js/src/singleton.js", "webpack://leadin/./node_modules/raven-js/src/utils.js", "webpack://leadin/./node_modules/raven-js/vendor/TraceKit/tracekit.js", "webpack://leadin/./node_modules/raven-js/vendor/json-stringify-safe/stringify.js", "webpack://leadin/./node_modules/react/cjs/react-jsx-runtime.development.js", "webpack://leadin/./node_modules/react/jsx-runtime.js", "webpack://leadin/external window \"React\"", "webpack://leadin/external window \"ReactDOM\"", "webpack://leadin/external window \"jQuery\"", "webpack://leadin/external window [\"wp\",\"i18n\"]", "webpack://leadin/./node_modules/@linaria/react/dist/index.mjs", "webpack://leadin/./node_modules/@linaria/react/node_modules/@linaria/core/dist/index.mjs", "webpack://leadin/webpack/bootstrap", "webpack://leadin/webpack/runtime/compat get default export", "webpack://leadin/webpack/runtime/define property getters", "webpack://leadin/webpack/runtime/global", "webpack://leadin/webpack/runtime/hasOwnProperty shorthand", "webpack://leadin/webpack/runtime/make namespace object", "webpack://leadin/./scripts/entries/elementor.ts"], "sourcesContent": ["function memoize(fn) {\n  var cache = Object.create(null);\n  return function (arg) {\n    if (cache[arg] === undefined) cache[arg] = fn(arg);\n    return cache[arg];\n  };\n}\n\nexport { memoize as default };\n", "import memoize from '@emotion/memoize';\n\n// eslint-disable-next-line no-undef\nvar reactPropsRegex = /^((children|dangerouslySetInnerHTML|key|ref|autoFocus|defaultValue|defaultChecked|innerHTML|suppressContentEditableWarning|suppressHydrationWarning|valueLink|abbr|accept|acceptCharset|accessKey|action|allow|allowUserMedia|allowPaymentRequest|allowFullScreen|allowTransparency|alt|async|autoComplete|autoPlay|capture|cellPadding|cellSpacing|challenge|charSet|checked|cite|classID|className|cols|colSpan|content|contentEditable|contextMenu|controls|controlsList|coords|crossOrigin|data|dateTime|decoding|default|defer|dir|disabled|disablePictureInPicture|disableRemotePlayback|download|draggable|encType|enterKeyHint|form|formAction|formEncType|formMethod|formNoValidate|formTarget|frameBorder|headers|height|hidden|high|href|hrefLang|htmlFor|httpEquiv|id|inputMode|integrity|is|keyParams|keyType|kind|label|lang|list|loading|loop|low|marginHeight|marginWidth|max|maxLength|media|mediaGroup|method|min|minLength|multiple|muted|name|nonce|noValidate|open|optimum|pattern|placeholder|playsInline|poster|preload|profile|radioGroup|readOnly|referrerPolicy|rel|required|reversed|role|rows|rowSpan|sandbox|scope|scoped|scrolling|seamless|selected|shape|size|sizes|slot|span|spellCheck|src|srcDoc|srcLang|srcSet|start|step|style|summary|tabIndex|target|title|translate|type|useMap|value|width|wmode|wrap|about|datatype|inlist|prefix|property|resource|typeof|vocab|autoCapitalize|autoCorrect|autoSave|color|incremental|fallback|inert|itemProp|itemScope|itemType|itemID|itemRef|on|option|results|security|unselectable|accentHeight|accumulate|additive|alignmentBaseline|allowReorder|alphabetic|amplitude|arabicForm|ascent|attributeName|attributeType|autoReverse|azimuth|baseFrequency|baselineShift|baseProfile|bbox|begin|bias|by|calcMode|capHeight|clip|clipPathUnits|clipPath|clipRule|colorInterpolation|colorInterpolationFilters|colorProfile|colorRendering|contentScriptType|contentStyleType|cursor|cx|cy|d|decelerate|descent|diffuseConstant|direction|display|divisor|dominantBaseline|dur|dx|dy|edgeMode|elevation|enableBackground|end|exponent|externalResourcesRequired|fill|fillOpacity|fillRule|filter|filterRes|filterUnits|floodColor|floodOpacity|focusable|fontFamily|fontSize|fontSizeAdjust|fontStretch|fontStyle|fontVariant|fontWeight|format|from|fr|fx|fy|g1|g2|glyphName|glyphOrientationHorizontal|glyphOrientationVertical|glyphRef|gradientTransform|gradientUnits|hanging|horizAdvX|horizOriginX|ideographic|imageRendering|in|in2|intercept|k|k1|k2|k3|k4|kernelMatrix|kernelUnitLength|kerning|keyPoints|keySplines|keyTimes|lengthAdjust|letterSpacing|lightingColor|limitingConeAngle|local|markerEnd|markerMid|markerStart|markerHeight|markerUnits|markerWidth|mask|maskContentUnits|maskUnits|mathematical|mode|numOctaves|offset|opacity|operator|order|orient|orientation|origin|overflow|overlinePosition|overlineThickness|panose1|paintOrder|pathLength|patternContentUnits|patternTransform|patternUnits|pointerEvents|points|pointsAtX|pointsAtY|pointsAtZ|preserveAlpha|preserveAspectRatio|primitiveUnits|r|radius|refX|refY|renderingIntent|repeatCount|repeatDur|requiredExtensions|requiredFeatures|restart|result|rotate|rx|ry|scale|seed|shapeRendering|slope|spacing|specularConstant|specularExponent|speed|spreadMethod|startOffset|stdDeviation|stemh|stemv|stitchTiles|stopColor|stopOpacity|strikethroughPosition|strikethroughThickness|string|stroke|strokeDasharray|strokeDashoffset|strokeLinecap|strokeLinejoin|strokeMiterlimit|strokeOpacity|strokeWidth|surfaceScale|systemLanguage|tableValues|targetX|targetY|textAnchor|textDecoration|textRendering|textLength|to|transform|u1|u2|underlinePosition|underlineThickness|unicode|unicodeBidi|unicodeRange|unitsPerEm|vAlphabetic|vHanging|vIdeographic|vMathematical|values|vectorEffect|version|vertAdvY|vertOriginX|vertOriginY|viewBox|viewTarget|visibility|widths|wordSpacing|writingMode|x|xHeight|x1|x2|xChannelSelector|xlinkActuate|xlinkArcrole|xlinkHref|xlinkRole|xlinkShow|xlinkTitle|xlinkType|xmlBase|xmlns|xmlnsXlink|xmlLang|xmlSpace|y|y1|y2|yChannelSelector|z|zoomAndPan|for|class|autofocus)|(([Dd][Aa][Tt][Aa]|[Aa][Rr][Ii][Aa]|x)-.*))$/; // https://esbench.com/bench/5bfee68a4cd7e6009ef61d23\n\nvar isPropValid = /* #__PURE__ */memoize(function (prop) {\n  return reactPropsRegex.test(prop) || prop.charCodeAt(0) === 111\n  /* o */\n  && prop.charCodeAt(1) === 110\n  /* n */\n  && prop.charCodeAt(2) < 91;\n}\n/* Z+1 */\n);\n\nexport { isPropValid as default };\n", "import { __ } from '@wordpress/i18n';\nconst BLANK_FORM = 'BLANK';\nconst NEWSLETTER_FORM = 'NEWSLETTER';\nconst CONTACT_US_FORM = 'CONTACT_US';\nconst EVENT_REGISTRATION_FORM = 'EVENT_REGISTRATION';\nconst TALK_TO_AN_EXPERT_FORM = 'TALK_TO_AN_EXPERT';\nconst BOOK_A_MEETING_FORM = 'BOOK_A_MEETING';\nconst GATED_CONTENT_FORM = 'GATED_CONTENT';\nexport const DEFAULT_OPTIONS = {\n    label: __('Templates', 'leadin'),\n    options: [\n        { label: __('Blank Form', 'leadin'), value: BLANK_FORM },\n        { label: __('Newsletter Form', 'leadin'), value: NEWSLETTER_FORM },\n        { label: __('Contact Us Form', 'leadin'), value: CONTACT_US_FORM },\n        {\n            label: __('Event Registration Form', 'leadin'),\n            value: EVENT_REGISTRATION_FORM,\n        },\n        {\n            label: __('Talk to an Expert Form', 'leadin'),\n            value: TALK_TO_AN_EXPERT_FORM,\n        },\n        { label: __('Book a Meeting Form', 'leadin'), value: BOOK_A_MEETING_FORM },\n        { label: __('Gated Content Form', 'leadin'), value: GATED_CONTENT_FORM },\n    ],\n};\nexport function isDefaultForm(value) {\n    return (value === BLANK_FORM ||\n        value === NEWSLETTER_FORM ||\n        value === CONTACT_US_FORM ||\n        value === EVENT_REGISTRATION_FORM ||\n        value === TALK_TO_AN_EXPERT_FORM ||\n        value === BOOK_A_MEETING_FORM ||\n        value === GATED_CONTENT_FORM);\n}\n", "const { accountName, adminUrl, activationTime, connectionStatus, deviceId, didDisconnect, env, formsScript, meetingsScript, formsScriptPayload, hublet, hubspotBaseUrl, hubspotNonce, iframeUrl, impactLink, lastAuthorizeTime, lastDeauthorizeTime, lastDisconnectTime, leadinPluginVersion, leadinQueryParams, locale, loginUrl, phpVersion, pluginPath, plugins, portalDomain, portalEmail, portalId, redirectNonce, restNonce, restUrl, refreshToken, reviewSkippedDate, theme, trackConsent, wpVersion, contentEmbed, requiresContentEmbedScope, decryptError, } = window.leadinConfig;\nexport { accountName, adminUrl, activationTime, connectionStatus, deviceId, didDisconnect, env, formsScript, meetingsScript, formsScriptPayload, hublet, hubspotBaseUrl, hubspotNonce, iframeUrl, impactLink, lastAuthorizeTime, lastDeauthorizeTime, lastDisconnectTime, leadinPluginVersion, leadinQueryParams, loginUrl, locale, phpVersion, pluginPath, plugins, portalDomain, portalEmail, portalId, redirectNonce, restNonce, restUrl, refreshToken, reviewSkippedDate, theme, trackConsent, wpVersion, contentEmbed, requiresContentEmbedScope, decryptError, };\n", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport React from 'react';\nimport ElementorBanner from './ElementorBanner';\nimport { __ } from '@wordpress/i18n';\nexport default function ConnectPluginBanner() {\n    return (_jsx(ElementorBanner, { children: _jsx(\"b\", { dangerouslySetInnerHTML: {\n                __html: __('The HubSpot plugin is not connected right now To use HubSpot tools on your WordPress site, %1$sconnect the plugin now%2$s')\n                    .replace('%1$s', '<a class=\"leadin-banner__link\" href=\"admin.php?page=leadin&bannerClick=true\">')\n                    .replace('%2$s', '</a>'),\n            } }) }));\n}\n", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport React from 'react';\nexport default function ElementorBanner({ type = 'warning', children, }) {\n    return (_jsx(\"div\", { className: \"elementor-control-content\", children: _jsx(\"div\", { className: `elementor-control-raw-html elementor-panel-alert elementor-panel-alert-${type}`, children: children }) }));\n}\n", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport { styled } from '@linaria/react';\nimport React from 'react';\nconst Container = styled.div `\n  display: flex;\n  justify-content: center;\n  padding-bottom: 8px;\n`;\nexport default function ElementorButton({ children, ...params }) {\n    return (_jsx(Container, { className: \"elementor-button-wrapper\", children: _jsx(\"button\", { className: \"elementor-button elementor-button-default\", type: \"button\", ...params, children: children }) }));\n}\n", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport React, { Fragment } from 'react';\nimport { portalId, refreshToken } from '../../constants/leadinConfig';\nimport ElementorBanner from '../Common/ElementorBanner';\nimport UISpinner from '../../shared/UIComponents/UISpinner';\nimport { __ } from '@wordpress/i18n';\nimport { BackgroudAppContext, useBackgroundAppContext, } from '../../iframe/useBackgroundApp';\nimport useForms from './hooks/useForms';\nimport { getOrCreateBackgroundApp } from '../../utils/backgroundAppUtils';\nimport { isRefreshTokenAvailable } from '../../utils/isRefreshTokenAvailable';\nfunction ElementorFormSelect({ formId, setAttributes, }) {\n    const { hasError, forms, loading } = useForms();\n    return loading ? (_jsx(\"div\", { children: _jsx(<PERSON><PERSON><PERSON><PERSON>, {}) })) : hasError ? (_jsx(ElementorBanner, { type: \"danger\", children: __('Please refresh your forms or try again in a few minutes', 'leadin') })) : (_jsxs(\"select\", { value: formId, onChange: event => {\n            const selectedForm = forms.find(form => form.value === event.target.value);\n            if (selectedForm) {\n                setAttributes({\n                    portalId,\n                    formId: selectedForm.value,\n                    formName: selectedForm.label,\n                    embedVersion: selectedForm.embedVersion,\n                });\n            }\n        }, children: [_jsx(\"option\", { value: \"\", disabled: true, selected: true, children: __('Search for a form', 'leadin') }), forms.map(form => (_jsx(\"option\", { value: form.value, children: form.label }, form.value)))] }));\n}\nfunction ElementorFormSelectWrapper(props) {\n    const isBackgroundAppReady = useBackgroundAppContext();\n    return (_jsx(Fragment, { children: !isBackgroundAppReady ? (_jsx(\"div\", { children: _jsx(UISpinner, {}) })) : (_jsx(ElementorFormSelect, { ...props })) }));\n}\nexport default function ElementorFormSelectContainer(props) {\n    return (_jsx(BackgroudAppContext.Provider, { value: isRefreshTokenAvailable() && getOrCreateBackgroundApp(refreshToken), children: _jsx(ElementorFormSelectWrapper, { ...props }) }));\n}\n", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport React, { Fragment } from 'react';\nimport { connectionStatus } from '../../constants/leadinConfig';\nimport ConnectPluginBanner from '../Common/ConnectPluginBanner';\nimport ElementorFormSelect from './ElementorFormSelect';\nconst ConnectionStatus = {\n    Connected: 'Connected',\n    NotConnected: 'NotConnected',\n};\nexport default function FormControlController(attributes, setValue) {\n    return () => {\n        const render = () => {\n            if (connectionStatus === ConnectionStatus.Connected) {\n                return (_jsx(ElementorFormSelect, { formId: attributes.formId, setAttributes: setValue }));\n            }\n            else {\n                return _jsx(ConnectPluginBanner, {});\n            }\n        };\n        return _jsx(Fragment, { children: render() });\n    };\n}\n", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport React, { Fragment } from 'react';\nimport { connectionStatus } from '../../constants/leadinConfig';\nimport <PERSON>rrorHandler from '../../shared/Common/ErrorHandler';\nimport FormEdit from '../../shared/Form/FormEdit';\nimport ConnectionStatus from '../../shared/enums/connectionStatus';\nexport default function FormWidgetController(attributes, setValue) {\n    return () => {\n        const render = () => {\n            if (connectionStatus === ConnectionStatus.Connected) {\n                return (_jsx(FormEdit, { attributes: attributes, isSelected: true, setAttributes: setValue, preview: false, origin: \"elementor\" }));\n            }\n            else {\n                return _jsx(<PERSON>rror<PERSON>and<PERSON>, { status: 401 });\n            }\n        };\n        return _jsx(Fragment, { children: render() });\n    };\n}\n", "import { useState, useEffect } from 'react';\nimport LoadState from '../../../shared/enums/loadState';\nimport { ProxyMessages } from '../../../iframe/integratedMessages';\nimport { usePostAsyncBackgroundMessage } from '../../../iframe/useBackgroundApp';\nexport default function useForms() {\n    const proxy = usePostAsyncBackgroundMessage();\n    const [loadState, setLoadState] = useState(LoadState.NotLoaded);\n    const [hasError, setError] = useState(null);\n    const [forms, setForms] = useState([]);\n    useEffect(() => {\n        if (loadState === LoadState.NotLoaded) {\n            proxy({\n                key: ProxyMessages.FetchForms,\n                payload: {\n                    search: '',\n                },\n            })\n                .then(data => {\n                setForms(data.map((form) => ({\n                    label: form.name,\n                    value: form.guid,\n                    embedVersion: form.embedVersion,\n                })));\n                setLoadState(LoadState.Loaded);\n            })\n                .catch(error => {\n                setError(error);\n                setLoadState(LoadState.Failed);\n            });\n        }\n    }, [loadState]);\n    return { forms, loading: loadState === LoadState.Loading, hasError };\n}\n", "import <PERSON>actD<PERSON> from 'react-dom';\nimport FormControlController from './FormControlController';\nimport FormWidgetController from './FormWidgetController';\nexport default class registerFormWidget {\n    widgetContainer;\n    attributes;\n    controlContainer;\n    setValue;\n    constructor(controlContainer, widgetContainer, setValue) {\n        const attributes = widgetContainer.dataset.attributes\n            ? JSON.parse(widgetContainer.dataset.attributes)\n            : {};\n        this.widgetContainer = widgetContainer;\n        this.controlContainer = controlContainer;\n        this.setValue = setValue;\n        this.attributes = attributes;\n    }\n    render() {\n        ReactDOM.render(FormWidgetController(this.attributes, this.setValue)(), this.widgetContainer);\n        ReactDOM.render(FormControlController(this.attributes, this.setValue)(), this.controlContainer);\n    }\n    done() {\n        ReactDOM.unmountComponentAtNode(this.widgetContainer);\n        ReactDOM.unmountComponentAtNode(this.controlContainer);\n    }\n}\n", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport React, { Fragment, useState } from 'react';\nimport ElementorBanner from '../Common/ElementorBanner';\nimport UISpinner from '../../shared/UIComponents/UISpinner';\nimport ElementorMeetingWarning from './ElementorMeetingWarning';\nimport useMeetings, { useSelectedMeetingCalendar, } from '../../shared/Meeting/hooks/useMeetings';\nimport { __ } from '@wordpress/i18n';\nimport Raven from 'raven-js';\nimport { BackgroudAppContext, useBackgroundAppContext, } from '../../iframe/useBackgroundApp';\nimport { refreshToken } from '../../constants/leadinConfig';\nimport { getOrCreateBackgroundApp } from '../../utils/backgroundAppUtils';\nimport { isRefreshTokenAvailable } from '../../utils/isRefreshTokenAvailable';\nfunction ElementorMeetingSelect({ url, setAttributes, }) {\n    const { mappedMeetings: meetings, loading, error, reload, connectCalendar, } = useMeetings();\n    const selectedMeetingCalendar = useSelectedMeetingCalendar(url);\n    const [localUrl, setLocalUrl] = useState(url);\n    const handleConnectCalendar = () => {\n        return connectCalendar()\n            .then(() => {\n            reload();\n        })\n            .catch(error => {\n            Raven.captureMessage('Unable to connect calendar', {\n                extra: { error },\n            });\n        });\n    };\n    return (_jsx(Fragment, { children: loading ? (_jsx(\"div\", { children: _jsx(UISpinner, {}) })) : error ? (_jsx(ElementorBanner, { type: \"danger\", children: __('Please refresh your meetings or try again in a few minutes', 'leadin') })) : (_jsxs(Fragment, { children: [selectedMeetingCalendar && (_jsx(ElementorMeetingWarning, { status: selectedMeetingCalendar, onConnectCalendar: connectCalendar })), meetings.length > 1 && (_jsxs(\"select\", { value: localUrl, onChange: event => {\n                        const newUrl = event.target.value;\n                        setLocalUrl(newUrl);\n                        setAttributes({\n                            url: newUrl,\n                        });\n                    }, children: [_jsx(\"option\", { value: \"\", disabled: true, selected: true, children: __('Select a meeting', 'leadin') }), meetings.map(item => (_jsx(\"option\", { value: item.value, children: item.label }, item.value)))] }))] })) }));\n}\nfunction ElementorMeetingSelectWrapper(props) {\n    const isBackgroundAppReady = useBackgroundAppContext();\n    return (_jsx(Fragment, { children: !isBackgroundAppReady ? (_jsx(\"div\", { children: _jsx(UISpinner, {}) })) : (_jsx(ElementorMeetingSelect, { ...props })) }));\n}\nexport default function ElementorMeetingsSelectContainer(props) {\n    return (_jsx(BackgroudAppContext.Provider, { value: isRefreshTokenAvailable() && getOrCreateBackgroundApp(refreshToken), children: _jsx(ElementorMeetingSelectWrapper, { ...props }) }));\n}\n", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport React, { Fragment } from 'react';\nimport { CURRENT_USER_CALENDAR_MISSING } from '../../shared/Meeting/constants';\nimport ElementorButton from '../Common/ElementorButton';\nimport ElementorBanner from '../Common/ElementorBanner';\nimport { styled } from '@linaria/react';\nimport { __ } from '@wordpress/i18n';\nconst Container = styled.div `\n  padding-bottom: 8px;\n`;\nexport default function MeetingWarning({ onConnectCalendar, status, }) {\n    const isMeetingOwner = status === CURRENT_USER_CALENDAR_MISSING;\n    const titleText = isMeetingOwner\n        ? __('Your calendar is not connected', 'leadin')\n        : __('Calendar is not connected', 'leadin');\n    const titleMessage = isMeetingOwner\n        ? __('Please connect your calendar to activate your scheduling pages', 'leadin')\n        : __('Make sure that everybody in this meeting has connected their calendar from the Meetings page in HubSpot', 'leadin');\n    return (_jsxs(Fragment, { children: [_jsx(Container, { children: _jsxs(ElementorBanner, { type: \"warning\", children: [_jsx(\"b\", { children: titleText }), _jsx(\"br\", {}), titleMessage] }) }), isMeetingOwner && (_jsx(ElementorButton, { id: \"meetings-connect-calendar\", onClick: onConnectCalendar, children: __('Connect calendar', 'leadin') }))] }));\n}\n", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport React, { Fragment } from 'react';\nimport { connectionStatus } from '../../constants/leadinConfig';\nimport ConnectPluginBanner from '../Common/ConnectPluginBanner';\nimport ElementorMeetingSelect from './ElementorMeetingSelect';\nconst ConnectionStatus = {\n    Connected: 'Connected',\n    NotConnected: 'NotConnected',\n};\nexport default function MeetingControlController(attributes, setValue) {\n    return () => {\n        const render = () => {\n            if (connectionStatus === ConnectionStatus.Connected) {\n                return (_jsx(ElementorMeetingSelect, { url: attributes.url, setAttributes: setValue }));\n            }\n            else {\n                return _jsx(ConnectPluginBanner, {});\n            }\n        };\n        return _jsx(Fragment, { children: render() });\n    };\n}\n", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport React, { Fragment } from 'react';\nimport { connectionStatus } from '../../constants/leadinConfig';\nimport ErrorHandler from '../../shared/Common/ErrorHandler';\nimport MeetingsEdit from '../../shared/Meeting/MeetingEdit';\nconst ConnectionStatus = {\n    Connected: 'Connected',\n    NotConnected: 'NotConnected',\n};\nexport default function MeetingWidgetController(attributes, setValue) {\n    return () => {\n        const render = () => {\n            if (connectionStatus === ConnectionStatus.Connected) {\n                return (_jsx(MeetingsEdit, { attributes: attributes, isSelected: true, setAttributes: setValue, preview: false, origin: \"elementor\" }));\n            }\n            else {\n                return _jsx(ErrorHandler, { status: 401 });\n            }\n        };\n        return _jsx(Fragment, { children: render() });\n    };\n}\n", "import ReactDOM from 'react-dom';\nimport MeetingControlController from './MeetingControlController';\nimport MeetingWidgetController from './MeetingWidgetController';\nexport default class registerMeetingsWidget {\n    widgetContainer;\n    controlContainer;\n    setValue;\n    attributes;\n    constructor(controlContainer, widgetContainer, setValue) {\n        const attributes = widgetContainer.dataset.attributes\n            ? JSON.parse(widgetContainer.dataset.attributes)\n            : {};\n        this.widgetContainer = widgetContainer;\n        this.controlContainer = controlContainer;\n        this.setValue = setValue;\n        this.attributes = attributes;\n    }\n    render() {\n        ReactDOM.render(MeetingWidgetController(this.attributes, this.setValue)(), this.widgetContainer);\n        ReactDOM.render(MeetingControlController(this.attributes, this.setValue)(), this.controlContainer);\n    }\n    done() {\n        ReactDOM.unmountComponentAtNode(this.widgetContainer);\n        ReactDOM.unmountComponentAtNode(this.controlContainer);\n    }\n}\n", "export default function elementorWidget(elementor, options, callback, done = () => { }) {\n    return elementor.modules.controls.BaseData.extend({\n        onReady() {\n            const self = this;\n            const controlContainer = this.ui.contentEditable.prevObject[0].querySelector(options.controlSelector);\n            let widgetContainer = this.options.element.$el[0].querySelector(options.containerSelector);\n            if (widgetContainer) {\n                callback(controlContainer, widgetContainer, (args) => self.setValue(args));\n            }\n            else {\n                //@ts-expect-error global\n                window.elementorFrontend.hooks.addAction(`frontend/element_ready/${options.widgetName}.default`, (element) => {\n                    widgetContainer = element[0].querySelector(options.containerSelector);\n                    callback(controlContainer, widgetContainer, (args) => self.setValue(args));\n                });\n            }\n        },\n        saveValue(props) {\n            this.setValue(props);\n        },\n        onBeforeDestroy() {\n            //@ts-expect-error global\n            window.elementorFrontend.hooks.removeAction(`frontend/element_ready/${options.widgetName}.default`);\n            done();\n        },\n    });\n}\n", "export const CoreMessages = {\n    HandshakeReceive: 'INTEGRATED_APP_EMBEDDER_HANDSHAKE_RECEIVED',\n    SendRefreshToken: 'INTEGRATED_APP_EMBEDDER_SEND_REFRESH_TOKEN',\n    ReloadParentFrame: 'INTEGRATED_APP_EMBEDDER_RELOAD_PARENT_FRAME',\n    RedirectParentFrame: 'INTEGRATED_APP_EMBEDDER_REDIRECT_PARENT_FRAME',\n    SendLocale: 'INTEGRATED_APP_EMBEDDER_SEND_LOCALE',\n    SendDeviceId: 'INTEGRATED_APP_EMBEDDER_SEND_DEVICE_ID',\n    SendIntegratedAppConfig: 'INTEGRATED_APP_EMBEDDER_CONFIG',\n};\n", "export const FormMessages = {\n    CreateFormAppNavigation: 'CREATE_FORM_APP_NAVIGATION',\n};\n", "export * from './core/CoreMessages';\nexport * from './forms/FormsMessages';\nexport * from './livechat/LiveChatMessages';\nexport * from './plugin/PluginMessages';\nexport * from './proxy/ProxyMessages';\n", "export const LiveChatMessages = {\n    CreateLiveChatAppNavigation: 'CREATE_LIVE_CHAT_APP_NAVIGATION',\n};\n", "export const PluginMessages = {\n    PluginSettingsNavigation: 'PLUGIN_SETTINGS_NAVIGATION',\n    PluginLeadinConfig: 'PLUGIN_LEADIN_CONFIG',\n    TrackConsent: 'INTEGRATED_APP_EMBEDDER_TRACK_CONSENT',\n    InternalTrackingFetchRequest: 'INTEGRATED_TRACKING_FETCH_REQUEST',\n    InternalTrackingFetchResponse: 'INTEGRATED_TRACKING_FETCH_RESPONSE',\n    InternalTrackingFetchError: 'INTEGRATED_TRACKING_FETCH_ERROR',\n    InternalTrackingChangeRequest: 'INTEGRATED_TRACKING_CHANGE_REQUEST',\n    InternalTrackingChangeError: 'INTEGRATED_TRACKING_CHANGE_ERROR',\n    BusinessUnitFetchRequest: 'BUSINESS_UNIT_FETCH_REQUEST',\n    BusinessUnitFetchResponse: 'BUSINESS_UNIT_FETCH_FETCH_RESPONSE',\n    BusinessUnitFetchError: 'BUSINESS_UNIT_FETCH_FETCH_ERROR',\n    BusinessUnitChangeRequest: 'BUSINESS_UNIT_CHANGE_REQUEST',\n    BusinessUnitChangeError: 'BUSINESS_UNIT_CHANGE_ERROR',\n    SkipReviewRequest: 'SKIP_REVIEW_REQUEST',\n    SkipReviewResponse: 'SKIP_REVIEW_RESPONSE',\n    SkipReviewError: 'SKIP_REVIEW_ERROR',\n    RemoveParentQueryParam: 'REMOVE_PARENT_QUERY_PARAM',\n    ContentEmbedInstallRequest: 'CONTENT_EMBED_INSTALL_REQUEST',\n    ContentEmbedInstallResponse: 'CONTENT_EMBED_INSTALL_RESPONSE',\n    ContentEmbedInstallError: 'CONTENT_EMBED_INSTALL_ERROR',\n    ContentEmbedActivationRequest: 'CONTENT_EMBED_ACTIVATION_REQUEST',\n    ContentEmbedActivationResponse: 'CONTENT_EMBED_ACTIVATION_RESPONSE',\n    ContentEmbedActivationError: 'CONTENT_EMBED_ACTIVATION_ERROR',\n    ProxyMappingsEnabledRequest: 'PROXY_MAPPINGS_ENABLED_REQUEST',\n    ProxyMappingsEnabledResponse: 'PROXY_MAPPINGS_ENABLED_RESPONSE',\n    ProxyMappingsEnabledError: 'PROXY_MAPPINGS_ENABLED_ERROR',\n    ProxyMappingsEnabledChangeRequest: 'PROXY_MAPPINGS_ENABLED_CHANGE_REQUEST',\n    ProxyMappingsEnabledChangeError: 'PROXY_MAPPINGS_ENABLED_CHANGE_ERROR',\n    RefreshProxyMappingsRequest: 'REFRESH_PROXY_MAPPINGS_REQUEST',\n    RefreshProxyMappingsResponse: 'REFRESH_PROXY_MAPPINGS_RESPONSE',\n    RefreshProxyMappingsError: 'REFRESH_PROXY_MAPPINGS_ERROR',\n};\n", "export const ProxyMessages = {\n    FetchForms: 'FETCH_FORMS',\n    FetchForm: 'FETCH_FORM',\n    CreateFormFromTemplate: 'CREATE_FORM_FROM_TEMPLATE',\n    GetTemplateAvailability: 'GET_TEMPLATE_AVAILABILITY',\n    FetchAuth: 'FETCH_AUTH',\n    FetchMeetingsAndUsers: 'FETCH_MEETINGS_AND_USERS',\n    FetchContactsCreateSinceActivation: 'FETCH_CONTACTS_CREATED_SINCE_ACTIVATION',\n    FetchOrCreateMeetingUser: 'FETCH_OR_CREATE_MEETING_USER',\n    ConnectMeetingsCalendar: 'CONNECT_MEETINGS_CALENDAR',\n    TrackFormPreviewRender: 'TRACK_FORM_PREVIEW_RENDER',\n    TrackFormCreatedFromTemplate: 'TRACK_FORM_CREATED_FROM_TEMPLATE',\n    TrackFormCreationFailed: 'TRACK_FORM_CREATION_FAILED',\n    TrackMeetingPreviewRender: 'TRACK_MEETING_PREVIEW_RENDER',\n    TrackSidebarMetaChange: 'TRACK_SIDEBAR_META_CHANGE',\n    TrackReviewBannerRender: 'TRACK_REVIEW_BANNER_RENDER',\n    TrackReviewBannerInteraction: 'TRACK_REVIEW_BANNER_INTERACTION',\n    TrackReviewBannerDismissed: 'TRACK_REVIEW_BANNER_DISMISSED',\n    TrackPluginDeactivation: 'TRACK_PLUGIN_DEACTIVATION',\n};\n", "import { createContext, useContext } from 'react';\nexport const BackgroudAppContext = createContext(null);\nexport function useBackgroundAppContext() {\n    return useContext(BackgroudAppContext);\n}\nexport function usePostBackgroundMessage() {\n    const app = useBackgroundAppContext();\n    return (message) => {\n        app.postMessage(message);\n    };\n}\nexport function usePostAsyncBackgroundMessage() {\n    const app = useBackgroundAppContext();\n    return (message) => app.postAsyncMessage(message);\n}\n", "import Raven from 'raven-js';\nimport { hubspotBaseUrl, phpVersion, wpVersion, leadinPluginVersion, portalId, plugins, } from '../constants/leadinConfig';\nexport function configureRaven() {\n    if (hubspotBaseUrl.indexOf('local') !== -1) {\n        return;\n    }\n    const domain = hubspotBaseUrl.replace(/https?:\\/\\/app/, '');\n    Raven.config(`https://a9f08e536ef66abb0bf90becc905b09e@exceptions${domain}/v2/1`, {\n        instrument: {\n            tryCatch: false,\n        },\n        shouldSendCallback(data) {\n            return (!!data && !!data.culprit && /plugins\\/leadin\\//.test(data.culprit));\n        },\n        release: leadinPluginVersion,\n    }).install();\n    Raven.setTagsContext({\n        v: leadinPluginVersion,\n        php: phpVersion,\n        wordpress: wpVersion,\n    });\n    Raven.setExtraContext({\n        hub: portalId,\n        plugins: Object.keys(plugins)\n            .map(name => `${name}#${plugins[name]}`)\n            .join(','),\n    });\n}\nexport default Raven;\n", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport React, { useRef, useState, useEffect } from 'react';\nimport { styled } from '@linaria/react';\nimport { CALYPSO, CALYPSO_LIGHT, CALYPSO_MEDIUM, OBSIDIAN, } from '../UIComponents/colors';\nimport UISpinner from '../UIComponents/UISpinner';\nimport LoadState from '../enums/loadState';\nconst Container = styled.div `\n  color: ${OBSIDIAN};\n  font-family: 'Lexend Deca', Helvetica, Arial, sans-serif;\n  font-size: 14px;\n  position: relative;\n`;\nconst ControlContainer = styled.div `\n  align-items: center;\n  background-color: hsl(0, 0%, 100%);\n  border-color: hsl(0, 0%, 80%);\n  border-radius: 4px;\n  border-style: solid;\n  border-width: ${props => (props.focused ? '0' : '1px')};\n  cursor: default;\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: space-between;\n  min-height: 38px;\n  outline: 0 !important;\n  position: relative;\n  transition: all 100ms;\n  box-sizing: border-box;\n  box-shadow: ${props => props.focused ? `0 0 0 2px ${CALYPSO_MEDIUM}` : 'none'};\n  &:hover {\n    border-color: hsl(0, 0%, 70%);\n  }\n`;\nconst ValueContainer = styled.div `\n  align-items: center;\n  display: flex;\n  flex: 1;\n  flex-wrap: wrap;\n  padding: 2px 8px;\n  position: relative;\n  overflow: hidden;\n  box-sizing: border-box;\n`;\nconst Placeholder = styled.div `\n  color: hsl(0, 0%, 50%);\n  margin-left: 2px;\n  margin-right: 2px;\n  position: absolute;\n  top: 50%;\n  transform: translateY(-50%);\n  box-sizing: border-box;\n  font-size: 16px;\n`;\nconst SingleValue = styled.div `\n  color: hsl(0, 0%, 20%);\n  margin-left: 2px;\n  margin-right: 2px;\n  max-width: calc(100% - 8px);\n  overflow: hidden;\n  position: absolute;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  top: 50%;\n  transform: translateY(-50%);\n  box-sizing: border-box;\n`;\nconst IndicatorContainer = styled.div `\n  align-items: center;\n  align-self: stretch;\n  display: flex;\n  flex-shrink: 0;\n  box-sizing: border-box;\n`;\nconst DropdownIndicator = styled.div `\n  border-top: 8px solid ${CALYPSO};\n  border-left: 6px solid transparent;\n  border-right: 6px solid transparent;\n  width: 0px;\n  height: 0px;\n  margin: 10px;\n`;\nconst InputContainer = styled.div `\n  margin: 2px;\n  padding-bottom: 2px;\n  padding-top: 2px;\n  visibility: visible;\n  color: hsl(0, 0%, 20%);\n  box-sizing: border-box;\n`;\nconst Input = styled.input `\n  box-sizing: content-box;\n  background: rgba(0, 0, 0, 0) none repeat scroll 0px center;\n  border: 0px none;\n  font-size: inherit;\n  opacity: 1;\n  outline: currentcolor none 0px;\n  padding: 0px;\n  color: inherit;\n  font-family: inherit;\n`;\nconst InputShadow = styled.div `\n  position: absolute;\n  opacity: 0;\n  font-size: inherit;\n`;\nconst MenuContainer = styled.div `\n  position: absolute;\n  top: 100%;\n  background-color: #fff;\n  border-radius: 4px;\n  margin-bottom: 8px;\n  margin-top: 8px;\n  z-index: 9999;\n  box-shadow: 0 0 0 1px hsla(0, 0%, 0%, 0.1), 0 4px 11px hsla(0, 0%, 0%, 0.1);\n  width: 100%;\n`;\nconst MenuList = styled.div `\n  max-height: 300px;\n  overflow-y: auto;\n  padding-bottom: 4px;\n  padding-top: 4px;\n  position: relative;\n`;\nconst MenuGroup = styled.div `\n  padding-bottom: 8px;\n  padding-top: 8px;\n`;\nconst MenuGroupHeader = styled.div `\n  color: #999;\n  cursor: default;\n  font-size: 75%;\n  font-weight: 500;\n  margin-bottom: 0.25em;\n  text-transform: uppercase;\n  padding-left: 12px;\n  padding-left: 12px;\n`;\nconst MenuItem = styled.div `\n  display: block;\n  background-color: ${props => props.selected ? CALYPSO_MEDIUM : 'transparent'};\n  color: ${props => (props.selected ? '#fff' : 'inherit')};\n  cursor: default;\n  font-size: inherit;\n  width: 100%;\n  padding: 8px 12px;\n  &:hover {\n    background-color: ${props => props.selected ? CALYPSO_MEDIUM : CALYPSO_LIGHT};\n  }\n`;\nexport default function AsyncSelect({ placeholder, value, loadOptions, onChange, defaultOptions, }) {\n    const inputEl = useRef(null);\n    const inputShadowEl = useRef(null);\n    const [isFocused, setFocus] = useState(false);\n    const [loadState, setLoadState] = useState(LoadState.NotLoaded);\n    const [localValue, setLocalValue] = useState('');\n    const [options, setOptions] = useState(defaultOptions);\n    const inputSize = `${inputShadowEl.current ? inputShadowEl.current.clientWidth + 10 : 2}px`;\n    useEffect(() => {\n        if (loadOptions && loadState === LoadState.NotLoaded) {\n            loadOptions('', (result) => {\n                setOptions(result);\n                setLoadState(LoadState.Idle);\n            });\n        }\n    }, [loadOptions, loadState]);\n    const renderItems = (items = [], parentKey) => {\n        return items.map((item, index) => {\n            if (item.options) {\n                return (_jsxs(MenuGroup, { children: [_jsx(MenuGroupHeader, { id: `${index}-heading`, children: item.label }), _jsx(\"div\", { children: renderItems(item.options, index) })] }, `async-select-item-${index}`));\n            }\n            else {\n                const key = `async-select-item-${parentKey !== undefined ? `${parentKey}-${index}` : index}`;\n                return (_jsx(MenuItem, { id: key, selected: value && item.value === value.value, onClick: () => {\n                        onChange(item);\n                        setFocus(false);\n                    }, children: item.label }, key));\n            }\n        });\n    };\n    return (_jsxs(Container, { children: [_jsxs(ControlContainer, { id: \"leadin-async-selector\", focused: isFocused, onClick: () => {\n                    if (isFocused) {\n                        if (inputEl.current) {\n                            inputEl.current.blur();\n                        }\n                        setFocus(false);\n                        setLocalValue('');\n                    }\n                    else {\n                        if (inputEl.current) {\n                            inputEl.current.focus();\n                        }\n                        setFocus(true);\n                    }\n                }, children: [_jsxs(ValueContainer, { children: [localValue === '' &&\n                                (!value ? (_jsx(Placeholder, { children: placeholder })) : (_jsx(SingleValue, { children: value.label }))), _jsxs(InputContainer, { children: [_jsx(Input, { ref: inputEl, onFocus: () => {\n                                            setFocus(true);\n                                        }, onChange: e => {\n                                            setLocalValue(e.target.value);\n                                            setLoadState(LoadState.Loading);\n                                            loadOptions &&\n                                                loadOptions(e.target.value, (result) => {\n                                                    setOptions(result);\n                                                    setLoadState(LoadState.Idle);\n                                                });\n                                        }, value: localValue, width: inputSize, id: \"asycn-select-input\" }), _jsx(InputShadow, { ref: inputShadowEl, children: localValue })] })] }), _jsxs(IndicatorContainer, { children: [loadState === LoadState.Loading && _jsx(UISpinner, {}), _jsx(DropdownIndicator, {})] })] }), isFocused && (_jsx(MenuContainer, { children: _jsx(MenuList, { children: renderItems(options) }) }))] }));\n}\n", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport React from 'react';\nimport UIButton from '../UIComponents/UIButton';\nimport UIContainer from '../UIComponents/UIContainer';\nimport <PERSON><PERSON>potWrapper from './HubspotWrapper';\nimport { adminUrl, redirectNonce } from '../../constants/leadinConfig';\nimport { pluginPath } from '../../constants/leadinConfig';\nimport { __ } from '@wordpress/i18n';\nfunction redirectToPlugin() {\n    window.location.href = `${adminUrl}admin.php?page=leadin&leadin_expired=${redirectNonce}`;\n}\nexport default function ErrorHandler({ status, resetErrorState, errorInfo = { header: '', message: '', action: '' }, }) {\n    const isUnauthorized = status === 401 || status === 403;\n    const errorHeader = isUnauthorized\n        ? __(\"Your plugin isn't authorized\", 'leadin')\n        : errorInfo.header;\n    const errorMessage = isUnauthorized\n        ? __('Reauthorize your plugin to access your free HubSpot tools', 'leadin')\n        : errorInfo.message;\n    return (_jsx(HubspotWrapper, { pluginPath: pluginPath, children: _jsxs(UIContainer, { textAlign: \"center\", children: [_jsx(\"h4\", { children: errorHeader }), _jsx(\"p\", { children: _jsx(\"b\", { children: errorMessage }) }), isUnauthorized ? (_jsx(UIButton, { \"data-test-id\": \"authorize-button\", onClick: redirectToPlugin, children: __('Go to plugin', 'leadin') })) : (_jsx(UIButton, { \"data-test-id\": \"retry-button\", onClick: resetErrorState, children: errorInfo.action }))] }) }));\n}\n", "import { styled } from '@linaria/react';\nexport default styled.div `\n  background-image: ${props => `url(${props.pluginPath}/public/assets/images/hubspot.svg)`};\n  background-color: #f5f8fa;\n  background-repeat: no-repeat;\n  background-position: center 25px;\n  background-size: 120px;\n  color: #33475b;\n  font-family: 'Lexend Deca', Helvetica, Arial, sans-serif;\n  font-size: 14px;\n\n  padding: ${(props) => props.padding || '90px 20% 25px'};\n\n  p {\n    font-size: inherit !important;\n    line-height: 24px;\n    margin: 4px 0;\n  }\n`;\n", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport React from 'react';\nimport <PERSON>bspotWrapper from './HubspotWrapper';\nimport UISpinner from '../UIComponents/UISpinner';\nimport { pluginPath } from '../../constants/leadinConfig';\nexport default function LoadingBlock() {\n    return (_jsx(HubspotWrapper, { pluginPath: pluginPath, children: _jsx(UISpinner, { size: 50 }) }));\n}\n", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport React from 'react';\nimport UIContainer from '../UIComponents/UIContainer';\nimport HubspotWrapper from './HubspotWrapper';\nimport { pluginPath } from '../../constants/leadinConfig';\nimport { __ } from '@wordpress/i18n';\nexport default function PreviewDisabled() {\n    const errorHeader = __('Preview Unavailable', 'leadin');\n    const errorMessage = `${__('This block cannot be previewed within the Full Site Editor', 'leadin')} ${__('Switch to the Block Editor to view the content', 'leadin')}`;\n    return (_jsx(HubspotWrapper, { pluginPath: pluginPath, children: _jsxs(UIContainer, { textAlign: \"center\", children: [_jsx(\"h4\", { children: errorHeader }), _jsx(\"p\", { children: _jsx(\"b\", { children: errorMessage }) })] }) }));\n}\n", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport React, { Fragment, useEffect } from 'react';\nimport { portalId, refreshToken } from '../../constants/leadinConfig';\nimport UISpacer from '../UIComponents/UISpacer';\nimport PreviewForm from './PreviewForm';\nimport FormSelect from './FormSelect';\nimport { usePostBackgroundMessage, BackgroudAppContext, useBackgroundAppContext, } from '../../iframe/useBackgroundApp';\nimport { ProxyMessages } from '../../iframe/integratedMessages';\nimport LoadingBlock from '../Common/LoadingBlock';\nimport { getOrCreateBackgroundApp } from '../../utils/backgroundAppUtils';\nimport { isRefreshTokenAvailable } from '../../utils/isRefreshTokenAvailable';\nfunction FormEdit({ attributes, isSelected, setAttributes, preview = true, origin = 'gutenberg', fullSiteEditor, }) {\n    const { formId, formName, embedVersion } = attributes;\n    const formSelected = portalId && formId;\n    const isBackgroundAppReady = useBackgroundAppContext();\n    const monitorFormPreviewRender = usePostBackgroundMessage();\n    const handleChange = (selectedForm) => {\n        setAttributes({\n            portalId,\n            formId: selectedForm.value,\n            formName: selectedForm.label,\n            embedVersion: selectedForm.embedVersion,\n        });\n    };\n    useEffect(() => {\n        monitorFormPreviewRender({\n            key: ProxyMessages.TrackFormPreviewRender,\n            payload: {\n                origin,\n            },\n        });\n    }, [origin]);\n    return !isBackgroundAppReady ? (_jsx(LoadingBlock, {})) : (_jsxs(Fragment, { children: [(isSelected || !formSelected) && (_jsx(FormSelect, { formId: formId, formName: formName, handleChange: handleChange, origin: origin, embedVersion: embedVersion })), formSelected && (_jsxs(Fragment, { children: [isSelected && _jsx(UISpacer, {}), preview && (_jsx(PreviewForm, { portalId: portalId, formId: formId, fullSiteEditor: fullSiteEditor, embedVersion: embedVersion }))] }))] }));\n}\nexport default function FormEditContainer(props) {\n    return (_jsx(BackgroudAppContext.Provider, { value: isRefreshTokenAvailable() && getOrCreateBackgroundApp(refreshToken), children: _jsx(FormEdit, { ...props }) }));\n}\n", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport React from 'react';\nimport FormSelector from './FormSelector';\nimport LoadingBlock from '../Common/LoadingBlock';\nimport { __ } from '@wordpress/i18n';\nimport useForms from './hooks/useForms';\nimport useCreateFormFromTemplate from './hooks/useCreateFormFromTemplate';\nimport { isDefaultForm } from '../../constants/defaultFormOptions';\nimport ErrorHandler from '../Common/ErrorHandler';\nexport default function FormSelect({ formId, formName, handleChange, origin = 'gutenberg', embedVersion, }) {\n    const { search, formApiError, reset } = useForms();\n    const { createFormByTemplate, reset: createReset, isCreating, hasError, formApiError: createApiError, } = useCreateFormFromTemplate(origin);\n    const value = formId && formName\n        ? {\n            label: formName,\n            value: formId,\n            embedVersion,\n        }\n        : null;\n    const handleLocalChange = (option) => {\n        if (isDefaultForm(option.value)) {\n            createFormByTemplate(option.value).then(({ guid, name }) => {\n                handleChange({\n                    value: guid,\n                    label: name,\n                    embedVersion: 'v4',\n                });\n            });\n        }\n        else {\n            handleChange(option);\n        }\n    };\n    return isCreating ? (_jsx(LoadingBlock, {})) : formApiError || createApiError ? (_jsx(ErrorHandler, { status: formApiError ? formApiError.status : createApiError.status, resetErrorState: () => {\n            if (hasError) {\n                createReset();\n            }\n            else {\n                reset();\n            }\n        }, errorInfo: {\n            header: __('There was a problem retrieving your forms', 'leadin'),\n            message: __('Please refresh your forms or try again in a few minutes', 'leadin'),\n            action: __('Refresh forms', 'leadin'),\n        } })) : (_jsx(FormSelector, { loadOptions: search, onChange: (option) => handleLocalChange(option), value: value }));\n}\n", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport React from 'react';\nimport HubspotWrapper from '../Common/HubspotWrapper';\nimport { pluginPath } from '../../constants/leadinConfig';\nimport AsyncSelect from '../Common/AsyncSelect';\nimport { __ } from '@wordpress/i18n';\nexport default function FormSelector({ loadOptions, onChange, value, }) {\n    return (_jsxs(HubspotWrapper, { pluginPath: pluginPath, children: [_jsx(\"p\", { \"data-test-id\": \"leadin-form-select\", children: _jsx(\"b\", { children: __('Select an existing form or create a new one from a template', 'leadin') }) }), _jsx(AsyncSelect, { placeholder: __('Search for a form', 'leadin'), value: value, loadOptions: loadOptions, onChange: onChange })] }));\n}\n", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport React, { useEffect, useRef } from 'react';\nimport UIOverlay from '../UIComponents/UIOverlay';\nimport { formsScriptPayload, hublet as region, } from '../../constants/leadinConfig';\nimport PreviewDisabled from '../Common/PreviewDisabled';\nexport default function PreviewForm({ portalId, formId, fullSiteEditor, embedVersion, }) {\n    const isFormV4 = embedVersion === 'v4';\n    const inputEl = useRef(null);\n    useEffect(() => {\n        if (inputEl.current) {\n            //@ts-expect-error Hubspot global\n            const hbspt = window.parent.hbspt || window.hbspt;\n            inputEl.current.innerHTML = '';\n            const isQa = formsScriptPayload.includes('qa');\n            if (isFormV4) {\n                const container = document.createElement('div');\n                container.classList.add('hs-form-frame');\n                container.dataset.region = region;\n                container.dataset.formId = formId;\n                container.dataset.portalId = portalId.toString();\n                container.dataset.env = isQa ? 'qa' : '';\n                inputEl.current.appendChild(container);\n            }\n            else {\n                const additionalParams = isQa ? { env: 'qa' } : {};\n                hbspt.forms.create({\n                    portalId,\n                    formId,\n                    region,\n                    target: `#${inputEl.current.id}`,\n                    ...additionalParams,\n                });\n            }\n        }\n    }, [formId, portalId, inputEl, isFormV4]);\n    if (fullSiteEditor) {\n        return _jsx(PreviewDisabled, {});\n    }\n    return _jsx(UIOverlay, { ref: inputEl, id: `hbspt-previewform-${formId}` });\n}\n", "import { useState } from 'react';\nimport { usePostAsyncBackgroundMessage, usePostBackgroundMessage, } from '../../../iframe/useBackgroundApp';\nimport LoadState from '../../enums/loadState';\nimport { ProxyMessages } from '../../../iframe/integratedMessages';\nexport default function useCreateFormFromTemplate(origin = 'gutenberg') {\n    const proxy = usePostAsyncBackgroundMessage();\n    const track = usePostBackgroundMessage();\n    const [loadState, setLoadState] = useState(LoadState.Idle);\n    const [formApiError, setFormApiError] = useState(null);\n    const createFormByTemplate = (type) => {\n        setLoadState(LoadState.Loading);\n        track({\n            key: ProxyMessages.TrackFormCreatedFromTemplate,\n            payload: {\n                type,\n                origin,\n            },\n        });\n        return proxy({\n            key: ProxyMessages.CreateFormFromTemplate,\n            payload: {\n                type,\n                embedVersion: 'v4',\n            },\n        })\n            .then(form => {\n            setLoadState(LoadState.Idle);\n            return form;\n        })\n            .catch(err => {\n            setFormApiError(err);\n            track({\n                key: ProxyMessages.TrackFormCreationFailed,\n                payload: {\n                    origin,\n                },\n            });\n            setLoadState(LoadState.Failed);\n        });\n    };\n    return {\n        isCreating: loadState === LoadState.Loading,\n        hasError: loadState === LoadState.Failed,\n        formApiError,\n        createFormByTemplate,\n        reset: () => setLoadState(LoadState.Idle),\n    };\n}\n", "import { useState } from 'react';\nimport debounce from 'lodash/debounce';\nimport { usePostAsyncBackgroundMessage } from '../../../iframe/useBackgroundApp';\nimport { ProxyMessages } from '../../../iframe/integratedMessages';\nimport useGetTemplateAvailability, { getTemplateOptions, } from './useGetTemplateAvailability';\nexport default function useForms() {\n    const proxy = usePostAsyncBackgroundMessage();\n    const [formApiError, setFormApiError] = useState(null);\n    const { availabilityPromise } = useGetTemplateAvailability();\n    const search = debounce((search, callback) => {\n        return Promise.all([\n            availabilityPromise,\n            proxy({\n                key: ProxyMessages.FetchForms,\n                payload: {\n                    search,\n                },\n            }),\n        ])\n            .then(([templateAvailabilityResponse, forms]) => {\n            const TEMPLATE_OPTIONS = getTemplateOptions(templateAvailabilityResponse.templateAvailability);\n            callback([\n                ...forms.map((form) => ({\n                    label: form.name,\n                    value: form.guid,\n                    embedVersion: form.embedVersion,\n                })),\n                TEMPLATE_OPTIONS,\n            ]);\n        })\n            .catch(error => {\n            setFormApiError(error);\n        });\n    }, 300, { trailing: true });\n    return {\n        search,\n        formApiError,\n        reset: () => setFormApiError(null),\n    };\n}\n", "import { useState } from 'react';\nimport { __ } from '@wordpress/i18n';\nimport { usePostAsyncBackgroundMessage } from '../../../iframe/useBackgroundApp';\nimport { ProxyMessages } from '../../../iframe/integratedMessages';\nimport { TemplateLabels, TemplateValues, ExcludedTemplateAvailabilityKeys, } from '../../types';\nexport default function useGetTemplateAvailability() {\n    const proxy = usePostAsyncBackgroundMessage();\n    const [templateAvailability, setTemplateAvailability,] = useState(null);\n    const [availabilityPromise] = useState(() => new Promise(resolve => {\n        proxy({\n            key: ProxyMessages.GetTemplateAvailability,\n            payload: {},\n        }).then(data => {\n            setTemplateAvailability(data.templateAvailability);\n            resolve(data);\n        });\n    }));\n    return { templateAvailability, availabilityPromise };\n}\nexport const getTemplateOptions = (templateAvailability) => {\n    if (!templateAvailability) {\n        return {};\n    }\n    return {\n        label: __('Templates', 'leadin'),\n        options: Object.keys(templateAvailability)\n            .filter(templateId => {\n            const hubspotFormTemplateAvailability = templateAvailability[templateId];\n            return ((hubspotFormTemplateAvailability.canCreateWithMissingScopes ||\n                !hubspotFormTemplateAvailability.missingScopes.length) &&\n                !Object.values(ExcludedTemplateAvailabilityKeys).includes(templateId));\n        })\n            .map(templateId => {\n            return {\n                label: __(TemplateLabels[templateId], 'leadin'),\n                value: TemplateValues[templateId],\n            };\n        }),\n    };\n};\nexport function isDefaultForm(value) {\n    return Object.values(TemplateValues).includes(value);\n}\n", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport React, { Fragment, useEffect } from 'react';\nimport LoadingBlock from '../Common/LoadingBlock';\nimport MeetingSelector from './MeetingSelector';\nimport MeetingWarning from './MeetingWarning';\nimport useMeetings, { useSelectedMeeting, useSelectedMeetingCalendar, } from './hooks/useMeetings';\nimport HubspotWrapper from '../Common/HubspotWrapper';\nimport ErrorHandler from '../Common/ErrorHandler';\nimport { pluginPath } from '../../constants/leadinConfig';\nimport { __ } from '@wordpress/i18n';\nimport Raven from 'raven-js';\nexport default function MeetingController({ handleChange, url, }) {\n    const { mappedMeetings: meetings, loading, error, reload, connectCalendar, } = useMeetings();\n    const selectedMeetingOption = useSelectedMeeting(url);\n    const selectedMeetingCalendar = useSelectedMeetingCalendar(url);\n    useEffect(() => {\n        if (!url && meetings.length > 0) {\n            handleChange(meetings[0].value);\n        }\n    }, [meetings, url, handleChange]);\n    const handleLocalChange = (option) => {\n        handleChange(option.value);\n    };\n    const handleConnectCalendar = () => {\n        return connectCalendar()\n            .then(() => {\n            reload();\n        })\n            .catch(error => {\n            Raven.captureMessage('Unable to connect calendar', {\n                extra: { error },\n            });\n        });\n    };\n    return (_jsx(Fragment, { children: loading ? (_jsx(LoadingBlock, {})) : error ? (_jsx(ErrorHandler, { status: (error && error.status) || error, resetErrorState: () => reload(), errorInfo: {\n                header: __('There was a problem retrieving your meetings', 'leadin'),\n                message: __('Please refresh your meetings or try again in a few minutes', 'leadin'),\n                action: __('Refresh meetings', 'leadin'),\n            } })) : (_jsxs(HubspotWrapper, { padding: \"90px 32px 24px\", pluginPath: pluginPath, children: [selectedMeetingCalendar && (_jsx(MeetingWarning, { status: selectedMeetingCalendar, onConnectCalendar: handleConnectCalendar })), meetings.length > 1 && (_jsx(MeetingSelector, { onChange: handleLocalChange, options: meetings, value: selectedMeetingOption }))] })) }));\n}\n", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport React, { Fragment, useEffect } from 'react';\nimport MeetingController from './MeetingController';\nimport PreviewMeeting from './PreviewMeeting';\nimport { BackgroudAppContext, useBackgroundAppContext, usePostBackgroundMessage, } from '../../iframe/useBackgroundApp';\nimport { refreshToken } from '../../constants/leadinConfig';\nimport { ProxyMessages } from '../../iframe/integratedMessages';\nimport LoadingBlock from '../Common/LoadingBlock';\nimport { getOrCreateBackgroundApp } from '../../utils/backgroundAppUtils';\nimport { isRefreshTokenAvailable } from '../../utils/isRefreshTokenAvailable';\nfunction MeetingEdit({ attributes: { url }, isSelected, setAttributes, preview = true, origin = 'gutenberg', fullSiteEditor, }) {\n    const isBackgroundAppReady = useBackgroundAppContext();\n    const monitorFormPreviewRender = usePostBackgroundMessage();\n    const handleChange = (newUrl) => {\n        setAttributes({\n            url: newUrl,\n        });\n    };\n    useEffect(() => {\n        monitorFormPreviewRender({\n            key: ProxyMessages.TrackMeetingPreviewRender,\n            payload: {\n                origin,\n            },\n        });\n    }, [origin]);\n    return !isBackgroundAppReady ? (_jsx(LoadingBlock, {})) : (_jsxs(Fragment, { children: [(isSelected || !url) && (_jsx(MeetingController, { url: url, handleChange: handleChange })), preview && url && (_jsx(PreviewMeeting, { url: url, fullSiteEditor: fullSiteEditor }))] }));\n}\nexport default function MeetingsEditContainer(props) {\n    return (_jsx(BackgroudAppContext.Provider, { value: isRefreshTokenAvailable() && getOrCreateBackgroundApp(refreshToken), children: _jsx(MeetingEdit, { ...props }) }));\n}\n", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport React, { Fragment } from 'react';\nimport AsyncSelect from '../Common/AsyncSelect';\nimport UISpacer from '../UIComponents/UISpacer';\nimport { __ } from '@wordpress/i18n';\nexport default function MeetingSelector({ options, onChange, value, }) {\n    const optionsWrapper = [\n        {\n            label: __('Meeting name', 'leadin'),\n            options,\n        },\n    ];\n    return (_jsxs(Fragment, { children: [_jsx(UISpacer, {}), _jsx(\"p\", { \"data-test-id\": \"leadin-meeting-select\", children: _jsx(\"b\", { children: __('Select a meeting scheduling page', 'leadin') }) }), _jsx(AsyncSelect, { defaultOptions: optionsWrapper, onChange: onChange, placeholder: __('Select a meeting', 'leadin'), value: value })] }));\n}\n", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport React from 'react';\nimport UIAlert from '../UIComponents/UIAlert';\nimport UIButton from '../UIComponents/UIButton';\nimport { CURRENT_USER_CALENDAR_MISSING } from './constants';\nimport { __ } from '@wordpress/i18n';\nexport default function MeetingWarning({ status, onConnectCalendar, }) {\n    const isMeetingOwner = status === CURRENT_USER_CALENDAR_MISSING;\n    const titleText = isMeetingOwner\n        ? __('Your calendar is not connected', 'leadin')\n        : __('Calendar is not connected', 'leadin');\n    const titleMessage = isMeetingOwner\n        ? __('Please connect your calendar to activate your scheduling pages', 'leadin')\n        : __('Make sure that everybody in this meeting has connected their calendar from the Meetings page in HubSpot', 'leadin');\n    return (_jsx(UIAlert, { titleText: titleText, titleMessage: titleMessage, children: isMeetingOwner && (_jsx(UIButton, { use: \"tertiary\", id: \"meetings-connect-calendar\", onClick: onConnectCalendar, children: __('Connect calendar', 'leadin') })) }));\n}\n", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport React, { Fragment, useEffect, useRef } from 'react';\nimport UIOverlay from '../UIComponents/UIOverlay';\nimport PreviewDisabled from '../Common/PreviewDisabled';\nexport default function PreviewForm({ url, fullSiteEditor }) {\n    const inputEl = useRef(null);\n    useEffect(() => {\n        if (inputEl.current) {\n            //@ts-expect-error Hubspot global\n            const hbspt = window.parent.hbspt || window.hbspt;\n            hbspt.meetings.create('.meetings-iframe-container');\n        }\n    }, [url, inputEl]);\n    if (fullSiteEditor) {\n        return _jsx(PreviewDisabled, {});\n    }\n    return (_jsx(Fragment, { children: url && (_jsx(UIOverlay, { ref: inputEl, className: \"meetings-iframe-container\", \"data-src\": `${url}?embed=true` })) }));\n}\n", "export const OTHER_USER_CALENDAR_MISSING = 'OTHER_USER_CALENDAR_MISSING';\nexport const CURRENT_USER_CALENDAR_MISSING = 'CURRENT_USER_CALENDAR_MISSING';\n", "import { useEffect, useState } from 'react';\nimport { usePostAsyncBackgroundMessage } from '../../../iframe/useBackgroundApp';\nimport LoadState from '../../enums/loadState';\nimport { ProxyMessages } from '../../../iframe/integratedMessages';\nlet user = null;\nexport default function useCurrentUserFetch() {\n    const proxy = usePostAsyncBackgroundMessage();\n    const [loadState, setLoadState] = useState(LoadState.NotLoaded);\n    const [error, setError] = useState(null);\n    const createUser = () => {\n        if (!user) {\n            setLoadState(LoadState.NotLoaded);\n        }\n    };\n    const reload = () => {\n        user = null;\n        setLoadState(LoadState.NotLoaded);\n        setError(null);\n    };\n    useEffect(() => {\n        if (loadState === LoadState.NotLoaded && !user) {\n            setLoadState(LoadState.Loading);\n            proxy({\n                key: ProxyMessages.FetchOrCreateMeetingUser,\n            })\n                .then(data => {\n                user = data;\n                setLoadState(LoadState.Idle);\n            })\n                .catch(err => {\n                setError(err);\n                setLoadState(LoadState.Failed);\n            });\n        }\n    }, [loadState]);\n    return { user, loadUserState: loadState, error, createUser, reload };\n}\n", "import { useCallback } from 'react';\nimport { __ } from '@wordpress/i18n';\nimport { CURRENT_USER_CALENDAR_MISSING, OTHER_USER_CALENDAR_MISSING, } from '../constants';\nimport useMeetingsFetch from './useMeetingsFetch';\nimport useCurrentUserFetch from './useCurrentUserFetch';\nimport LoadState from '../../enums/loadState';\nimport { usePostAsyncBackgroundMessage } from '../../../iframe/useBackgroundApp';\nimport { ProxyMessages } from '../../../iframe/integratedMessages';\nfunction getDefaultMeetingName(meeting, currentUser, meetingUsers) {\n    const [meetingOwnerId] = meeting.meetingsUserIds;\n    let result = __('Default', 'leadin');\n    if (currentUser &&\n        meetingOwnerId !== currentUser.id &&\n        meetingUsers[meetingOwnerId]) {\n        const user = meetingUsers[meetingOwnerId];\n        result += ` (${user.userProfile.fullName})`;\n    }\n    return result;\n}\nfunction hasCalendarObject(user) {\n    return (user &&\n        user.meetingsUserBlob &&\n        user.meetingsUserBlob.calendarSettings &&\n        user.meetingsUserBlob.calendarSettings.email);\n}\nexport default function useMeetings() {\n    const proxy = usePostAsyncBackgroundMessage();\n    const { meetings, meetingUsers, error: meetingsError, loadMeetingsState, reload: reloadMeetings, } = useMeetingsFetch();\n    const { user: currentUser, error: userError, loadUserState, reload: reloadUser, } = useCurrentUserFetch();\n    const reload = useCallback(() => {\n        reloadUser();\n        reloadMeetings();\n    }, [reloadUser, reloadMeetings]);\n    const connectCalendar = () => {\n        return proxy({\n            key: ProxyMessages.ConnectMeetingsCalendar,\n        });\n    };\n    return {\n        mappedMeetings: meetings.map(meet => ({\n            label: meet.name || getDefaultMeetingName(meet, currentUser, meetingUsers),\n            value: meet.link,\n        })),\n        meetings,\n        meetingUsers,\n        currentUser,\n        error: meetingsError || userError,\n        loading: loadMeetingsState == LoadState.Loading ||\n            loadUserState === LoadState.Loading,\n        reload,\n        connectCalendar,\n    };\n}\nexport function useSelectedMeeting(url) {\n    const { mappedMeetings: meetings } = useMeetings();\n    const option = meetings.find(({ value }) => value === url);\n    return option;\n}\nexport function useSelectedMeetingCalendar(url) {\n    const { meetings, meetingUsers, currentUser } = useMeetings();\n    const meeting = meetings.find(meet => meet.link === url);\n    const mappedMeetingUsersId = meetingUsers.reduce((p, c) => ({ ...p, [c.id]: c }), {});\n    if (!meeting) {\n        return null;\n    }\n    else {\n        const { meetingsUserIds } = meeting;\n        if (currentUser &&\n            meetingsUserIds.includes(currentUser.id) &&\n            !hasCalendarObject(currentUser)) {\n            return CURRENT_USER_CALENDAR_MISSING;\n        }\n        else if (meetingsUserIds\n            .map(id => mappedMeetingUsersId[id])\n            .some((user) => !hasCalendarObject(user))) {\n            return OTHER_USER_CALENDAR_MISSING;\n        }\n        else {\n            return null;\n        }\n    }\n}\n", "import { useEffect, useState } from 'react';\nimport { usePostAsyncBackgroundMessage } from '../../../iframe/useBackgroundApp';\nimport LoadState from '../../enums/loadState';\nimport { ProxyMessages } from '../../../iframe/integratedMessages';\nlet meetings = [];\nlet meetingUsers = [];\nexport default function useMeetingsFetch() {\n    const proxy = usePostAsyncBackgroundMessage();\n    const [loadState, setLoadState] = useState(LoadState.NotLoaded);\n    const [error, setError] = useState(null);\n    const reload = () => {\n        meetings = [];\n        setError(null);\n        setLoadState(LoadState.NotLoaded);\n    };\n    useEffect(() => {\n        if (loadState === LoadState.NotLoaded && meetings.length === 0) {\n            setLoadState(LoadState.Loading);\n            proxy({\n                key: ProxyMessages.FetchMeetingsAndUsers,\n            })\n                .then(data => {\n                setLoadState(LoadState.Loaded);\n                meetings = data && data.meetingLinks;\n                meetingUsers = data && data.meetingUsers;\n            })\n                .catch(e => {\n                setError(e);\n                setLoadState(LoadState.Failed);\n            });\n        }\n    }, [loadState]);\n    return {\n        meetings,\n        meetingUsers,\n        loadMeetingsState: loadState,\n        error,\n        reload,\n    };\n}\n", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport React from 'react';\nimport { styled } from '@linaria/react';\nimport { MARIGOLD_LIGHT, MARIGOLD_MEDIUM, OBSIDIAN } from './colors';\nconst AlertContainer = styled.div `\n  background-color: ${MARIGOLD_LIGHT};\n  border-color: ${MARIGOLD_MEDIUM};\n  color: ${OBSIDIAN};\n  font-size: 14px;\n  align-items: center;\n  justify-content: space-between;\n  display: flex;\n  border-style: solid;\n  border-top-style: solid;\n  border-right-style: solid;\n  border-bottom-style: solid;\n  border-left-style: solid;\n  border-width: 1px;\n  min-height: 60px;\n  padding: 8px 20px;\n  position: relative;\n  text-align: left;\n`;\nconst Title = styled.p `\n  font-family: 'Lexend Deca';\n  font-style: normal;\n  font-weight: 700;\n  font-size: 16px;\n  line-height: 19px;\n  color: ${OBSIDIAN};\n  margin: 0;\n  padding: 0;\n`;\nconst Message = styled.p `\n  font-family: 'Lexend Deca';\n  font-style: normal;\n  font-weight: 400;\n  font-size: 14px;\n  margin: 0;\n  padding: 0;\n`;\nconst MessageContainer = styled.div `\n  display: flex;\n  flex-direction: column;\n`;\nexport default function UIAlert({ titleText, titleMessage, children, }) {\n    return (_jsxs(AlertContainer, { children: [_jsxs(MessageContainer, { children: [_jsx(Title, { children: titleText }), _jsx(Message, { children: titleMessage })] }), children] }));\n}\n", "import { styled } from '@linaria/react';\nimport { HEFFALUMP, LORAX, OLAF } from './colors';\nexport default styled.button `\n  background-color:${props => (props.use === 'tertiary' ? HEFFALUMP : LORAX)};\n  border: 3px solid ${props => (props.use === 'tertiary' ? HEFFALUMP : LORAX)};\n  color: ${OLAF}\n  border-radius: 3px;\n  font-size: 14px;\n  line-height: 14px;\n  padding: 12px 24px;\n  font-family: 'Lexend Deca', Helvetica, Arial, sans-serif;\n  font-weight: 500;\n  white-space: nowrap;\n`;\n", "import { styled } from '@linaria/react';\nexport default styled.div `\n  text-align: ${props => (props.textAlign ? props.textAlign : 'inherit')};\n`;\n", "import { styled } from '@linaria/react';\nexport default styled.div `\n  position: relative;\n\n  &:after {\n    content: '';\n    position: absolute;\n    top: 0;\n    bottom: 0;\n    right: 0;\n    left: 0;\n  }\n`;\n", "import { styled } from '@linaria/react';\nexport default styled.div `\n  height: 30px;\n`;\n", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport React from 'react';\nimport { styled } from '@linaria/react';\nimport { CALYPSO_MEDIUM, CALYPSO } from './colors';\nconst SpinnerOuter = styled.div `\n  align-items: center;\n  color: #00a4bd;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  width: 100%;\n  height: 100%;\n  margin: '2px';\n`;\nconst SpinnerInner = styled.div `\n  align-items: center;\n  display: flex;\n  justify-content: center;\n  width: 100%;\n  height: 100%;\n`;\nconst Circle = styled.circle `\n  fill: none;\n  stroke: ${props => props.color};\n  stroke-width: 5;\n  stroke-linecap: round;\n  transform-origin: center;\n`;\nconst AnimatedCircle = styled.circle `\n  fill: none;\n  stroke: ${props => props.color};\n  stroke-width: 5;\n  stroke-linecap: round;\n  transform-origin: center;\n  animation: dashAnimation 2s ease-in-out infinite,\n    spinAnimation 2s linear infinite;\n\n  @keyframes dashAnimation {\n    0% {\n      stroke-dasharray: 1, 150;\n      stroke-dashoffset: 0;\n    }\n\n    50% {\n      stroke-dasharray: 90, 150;\n      stroke-dashoffset: -50;\n    }\n\n    100% {\n      stroke-dasharray: 90, 150;\n      stroke-dashoffset: -140;\n    }\n  }\n\n  @keyframes spinAnimation {\n    transform: rotate(360deg);\n  }\n`;\nexport default function UISpinner({ size = 20 }) {\n    return (_jsx(SpinnerOuter, { children: _jsx(SpinnerInner, { children: _jsxs(\"svg\", { height: size, width: size, viewBox: \"0 0 50 50\", children: [_jsx(Circle, { color: CALYPSO_MEDIUM, cx: \"25\", cy: \"25\", r: \"22.5\" }), _jsx(AnimatedCircle, { color: CALYPSO, cx: \"25\", cy: \"25\", r: \"22.5\" })] }) }) }));\n}\n", "export const CALYPSO = '#00a4bd';\nexport const CALYPSO_MEDIUM = '#7fd1de';\nexport const CALYPSO_LIGHT = '#e5f5f8';\nexport const LORAX = '#ff7a59';\nexport const OLAF = '#ffffff';\nexport const HEFFALUMP = '#425b76';\nexport const MARIGOLD_LIGHT = '#fef8f0';\nexport const MARIGOLD_MEDIUM = '#fae0b5';\nexport const OBSIDIAN = '#33475b';\n", "const ConnectionStatus = {\n    Connected: 'Connected',\n    NotConnected: 'NotConnected',\n};\nexport default ConnectionStatus;\n", "const LoadState = {\n    NotLoaded: 'NotLoaded',\n    Loading: 'Loading',\n    Loaded: 'Loaded',\n    Idle: 'Idle',\n    Failed: 'Failed',\n};\nexport default LoadState;\n", "export var HubSpotFormTemplateAvailabilityKeys;\n(function (HubSpotFormTemplateAvailabilityKeys) {\n    HubSpotFormTemplateAvailabilityKeys[\"AI_GENERATED\"] = \"ai-generated\";\n    HubSpotFormTemplateAvailabilityKeys[\"BLANK\"] = \"blank\";\n    HubSpotFormTemplateAvailabilityKeys[\"NEWSLETTER\"] = \"newsletter\";\n    HubSpotFormTemplateAvailabilityKeys[\"CONTACT_US\"] = \"contact-us\";\n    HubSpotFormTemplateAvailabilityKeys[\"EVENT_REGISTRATION\"] = \"event-registration\";\n    HubSpotFormTemplateAvailabilityKeys[\"TALK_TO_AN_EXPERT\"] = \"talk-to-an-expert\";\n    HubSpotFormTemplateAvailabilityKeys[\"BOOK_A_MEETING\"] = \"book-a-meeting\";\n    HubSpotFormTemplateAvailabilityKeys[\"GATED_CONTENT\"] = \"gated-content\";\n    HubSpotFormTemplateAvailabilityKeys[\"SUPPORT\"] = \"support\";\n})(HubSpotFormTemplateAvailabilityKeys || (HubSpotFormTemplateAvailabilityKeys = {}));\nexport var ExcludedTemplateAvailabilityKeys;\n(function (ExcludedTemplateAvailabilityKeys) {\n    ExcludedTemplateAvailabilityKeys[\"SUPPORT\"] = \"support\";\n    ExcludedTemplateAvailabilityKeys[\"AI_GENERATED\"] = \"ai-generated\";\n})(ExcludedTemplateAvailabilityKeys || (ExcludedTemplateAvailabilityKeys = {}));\nexport const TemplateLabels = {\n    [HubSpotFormTemplateAvailabilityKeys.BLANK]: 'Blank Form',\n    [HubSpotFormTemplateAvailabilityKeys.NEWSLETTER]: 'Newsletter Form',\n    [HubSpotFormTemplateAvailabilityKeys.CONTACT_US]: 'Contact Us Form',\n    [HubSpotFormTemplateAvailabilityKeys.EVENT_REGISTRATION]: 'Event Registration Form',\n    [HubSpotFormTemplateAvailabilityKeys.TALK_TO_AN_EXPERT]: 'Talk to an Expert Form',\n    [HubSpotFormTemplateAvailabilityKeys.BOOK_A_MEETING]: 'Book a Meeting Form',\n    [HubSpotFormTemplateAvailabilityKeys.GATED_CONTENT]: 'Gated Content Form',\n};\nexport const TemplateValues = {\n    [HubSpotFormTemplateAvailabilityKeys.BLANK]: 'BLANK',\n    [HubSpotFormTemplateAvailabilityKeys.NEWSLETTER]: 'NEWSLETTER',\n    [HubSpotFormTemplateAvailabilityKeys.CONTACT_US]: 'CONTACT_US',\n    [HubSpotFormTemplateAvailabilityKeys.EVENT_REGISTRATION]: 'EVENT_REGISTRATION',\n    [HubSpotFormTemplateAvailabilityKeys.TALK_TO_AN_EXPERT]: 'TALK_TO_AN_EXPERT',\n    [HubSpotFormTemplateAvailabilityKeys.BOOK_A_MEETING]: 'BOOK_A_MEETING',\n    [HubSpotFormTemplateAvailabilityKeys.GATED_CONTENT]: 'GATED_CONTENT',\n};\n", "import $ from 'j<PERSON>y';\nimport Raven, { configureRaven } from '../lib/Raven';\nexport function initApp(initFn) {\n    configureRaven();\n    Raven.context(initFn);\n}\nexport function initAppOnReady(initFn) {\n    function main() {\n        $(initFn);\n    }\n    initApp(main);\n}\n", "import { deviceId, hubspotBaseUrl, locale, portalId, leadinPluginVersion, } from '../constants/leadinConfig';\nimport { initApp } from './appUtils';\nexport function initBackgroundApp(initFn) {\n    function main() {\n        if (Array.isArray(initFn)) {\n            initFn.forEach(callback => callback());\n        }\n        else {\n            initFn();\n        }\n    }\n    initApp(main);\n}\nconst getLeadinConfig = () => {\n    return {\n        leadinPluginVersion,\n    };\n};\nexport const getOrCreateBackgroundApp = (refreshToken = '') => {\n    if (window.LeadinBackgroundApp) {\n        return window.LeadinBackgroundApp;\n    }\n    const { IntegratedAppEmbedder, IntegratedAppOptions } = window;\n    const options = new IntegratedAppOptions()\n        .setLocale(locale)\n        .setDeviceId(deviceId)\n        .setLeadinConfig(getLeadinConfig())\n        .setRefreshToken(refreshToken.trim());\n    const embedder = new IntegratedAppEmbedder('integrated-plugin-proxy', portalId, hubspotBaseUrl, () => { }).setOptions(options);\n    embedder.attachTo(document.body, false);\n    embedder.postStartAppMessage(); // lets the app know all all data has been passed to it\n    window.LeadinBackgroundApp = embedder;\n    return window.LeadinBackgroundApp;\n};\n", "import { refreshToken } from '../constants/leadinConfig';\nexport function isRefreshTokenAvailable() {\n    return !!(refreshToken && refreshToken.trim());\n}\n", "var root = require('./_root');\n\n/** Built-in value references. */\nvar Symbol = root.Symbol;\n\nmodule.exports = Symbol;\n", "var Symbol = require('./_Symbol'),\n    getRawTag = require('./_getRawTag'),\n    objectToString = require('./_objectToString');\n\n/** `Object#toString` result references. */\nvar nullTag = '[object Null]',\n    undefinedTag = '[object Undefined]';\n\n/** Built-in value references. */\nvar symToStringTag = Symbol ? Symbol.toStringTag : undefined;\n\n/**\n * The base implementation of `getTag` without fallbacks for buggy environments.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the `toStringTag`.\n */\nfunction baseGetTag(value) {\n  if (value == null) {\n    return value === undefined ? undefinedTag : nullTag;\n  }\n  return (symToStringTag && symToStringTag in Object(value))\n    ? getRawTag(value)\n    : objectToString(value);\n}\n\nmodule.exports = baseGetTag;\n", "var trimmedEndIndex = require('./_trimmedEndIndex');\n\n/** Used to match leading whitespace. */\nvar reTrimStart = /^\\s+/;\n\n/**\n * The base implementation of `_.trim`.\n *\n * @private\n * @param {string} string The string to trim.\n * @returns {string} Returns the trimmed string.\n */\nfunction baseTrim(string) {\n  return string\n    ? string.slice(0, trimmedEndIndex(string) + 1).replace(reTrimStart, '')\n    : string;\n}\n\nmodule.exports = baseTrim;\n", "/** Detect free variable `global` from Node.js. */\nvar freeGlobal = typeof global == 'object' && global && global.Object === Object && global;\n\nmodule.exports = freeGlobal;\n", "var Symbol = require('./_Symbol');\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar nativeObjectToString = objectProto.toString;\n\n/** Built-in value references. */\nvar symToStringTag = Symbol ? Symbol.toStringTag : undefined;\n\n/**\n * A specialized version of `baseGetTag` which ignores `Symbol.toStringTag` values.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the raw `toStringTag`.\n */\nfunction getRawTag(value) {\n  var isOwn = hasOwnProperty.call(value, symToStringTag),\n      tag = value[symToStringTag];\n\n  try {\n    value[symToStringTag] = undefined;\n    var unmasked = true;\n  } catch (e) {}\n\n  var result = nativeObjectToString.call(value);\n  if (unmasked) {\n    if (isOwn) {\n      value[symToStringTag] = tag;\n    } else {\n      delete value[symToStringTag];\n    }\n  }\n  return result;\n}\n\nmodule.exports = getRawTag;\n", "/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar nativeObjectToString = objectProto.toString;\n\n/**\n * Converts `value` to a string using `Object.prototype.toString`.\n *\n * @private\n * @param {*} value The value to convert.\n * @returns {string} Returns the converted string.\n */\nfunction objectToString(value) {\n  return nativeObjectToString.call(value);\n}\n\nmodule.exports = objectToString;\n", "var freeGlobal = require('./_freeGlobal');\n\n/** Detect free variable `self`. */\nvar freeSelf = typeof self == 'object' && self && self.Object === Object && self;\n\n/** Used as a reference to the global object. */\nvar root = freeGlobal || freeSelf || Function('return this')();\n\nmodule.exports = root;\n", "/** Used to match a single whitespace character. */\nvar reWhitespace = /\\s/;\n\n/**\n * Used by `_.trim` and `_.trimEnd` to get the index of the last non-whitespace\n * character of `string`.\n *\n * @private\n * @param {string} string The string to inspect.\n * @returns {number} Returns the index of the last non-whitespace character.\n */\nfunction trimmedEndIndex(string) {\n  var index = string.length;\n\n  while (index-- && reWhitespace.test(string.charAt(index))) {}\n  return index;\n}\n\nmodule.exports = trimmedEndIndex;\n", "var isObject = require('./isObject'),\n    now = require('./now'),\n    toNumber = require('./toNumber');\n\n/** Error message constants. */\nvar FUNC_ERROR_TEXT = 'Expected a function';\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMax = Math.max,\n    nativeMin = Math.min;\n\n/**\n * Creates a debounced function that delays invoking `func` until after `wait`\n * milliseconds have elapsed since the last time the debounced function was\n * invoked. The debounced function comes with a `cancel` method to cancel\n * delayed `func` invocations and a `flush` method to immediately invoke them.\n * Provide `options` to indicate whether `func` should be invoked on the\n * leading and/or trailing edge of the `wait` timeout. The `func` is invoked\n * with the last arguments provided to the debounced function. Subsequent\n * calls to the debounced function return the result of the last `func`\n * invocation.\n *\n * **Note:** If `leading` and `trailing` options are `true`, `func` is\n * invoked on the trailing edge of the timeout only if the debounced function\n * is invoked more than once during the `wait` timeout.\n *\n * If `wait` is `0` and `leading` is `false`, `func` invocation is deferred\n * until to the next tick, similar to `setTimeout` with a timeout of `0`.\n *\n * See [David Corbacho's article](https://css-tricks.com/debouncing-throttling-explained-examples/)\n * for details over the differences between `_.debounce` and `_.throttle`.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Function\n * @param {Function} func The function to debounce.\n * @param {number} [wait=0] The number of milliseconds to delay.\n * @param {Object} [options={}] The options object.\n * @param {boolean} [options.leading=false]\n *  Specify invoking on the leading edge of the timeout.\n * @param {number} [options.maxWait]\n *  The maximum time `func` is allowed to be delayed before it's invoked.\n * @param {boolean} [options.trailing=true]\n *  Specify invoking on the trailing edge of the timeout.\n * @returns {Function} Returns the new debounced function.\n * @example\n *\n * // Avoid costly calculations while the window size is in flux.\n * jQuery(window).on('resize', _.debounce(calculateLayout, 150));\n *\n * // Invoke `sendMail` when clicked, debouncing subsequent calls.\n * jQuery(element).on('click', _.debounce(sendMail, 300, {\n *   'leading': true,\n *   'trailing': false\n * }));\n *\n * // Ensure `batchLog` is invoked once after 1 second of debounced calls.\n * var debounced = _.debounce(batchLog, 250, { 'maxWait': 1000 });\n * var source = new EventSource('/stream');\n * jQuery(source).on('message', debounced);\n *\n * // Cancel the trailing debounced invocation.\n * jQuery(window).on('popstate', debounced.cancel);\n */\nfunction debounce(func, wait, options) {\n  var lastArgs,\n      lastThis,\n      maxWait,\n      result,\n      timerId,\n      lastCallTime,\n      lastInvokeTime = 0,\n      leading = false,\n      maxing = false,\n      trailing = true;\n\n  if (typeof func != 'function') {\n    throw new TypeError(FUNC_ERROR_TEXT);\n  }\n  wait = toNumber(wait) || 0;\n  if (isObject(options)) {\n    leading = !!options.leading;\n    maxing = 'maxWait' in options;\n    maxWait = maxing ? nativeMax(toNumber(options.maxWait) || 0, wait) : maxWait;\n    trailing = 'trailing' in options ? !!options.trailing : trailing;\n  }\n\n  function invokeFunc(time) {\n    var args = lastArgs,\n        thisArg = lastThis;\n\n    lastArgs = lastThis = undefined;\n    lastInvokeTime = time;\n    result = func.apply(thisArg, args);\n    return result;\n  }\n\n  function leadingEdge(time) {\n    // Reset any `maxWait` timer.\n    lastInvokeTime = time;\n    // Start the timer for the trailing edge.\n    timerId = setTimeout(timerExpired, wait);\n    // Invoke the leading edge.\n    return leading ? invokeFunc(time) : result;\n  }\n\n  function remainingWait(time) {\n    var timeSinceLastCall = time - lastCallTime,\n        timeSinceLastInvoke = time - lastInvokeTime,\n        timeWaiting = wait - timeSinceLastCall;\n\n    return maxing\n      ? nativeMin(timeWaiting, maxWait - timeSinceLastInvoke)\n      : timeWaiting;\n  }\n\n  function shouldInvoke(time) {\n    var timeSinceLastCall = time - lastCallTime,\n        timeSinceLastInvoke = time - lastInvokeTime;\n\n    // Either this is the first call, activity has stopped and we're at the\n    // trailing edge, the system time has gone backwards and we're treating\n    // it as the trailing edge, or we've hit the `maxWait` limit.\n    return (lastCallTime === undefined || (timeSinceLastCall >= wait) ||\n      (timeSinceLastCall < 0) || (maxing && timeSinceLastInvoke >= maxWait));\n  }\n\n  function timerExpired() {\n    var time = now();\n    if (shouldInvoke(time)) {\n      return trailingEdge(time);\n    }\n    // Restart the timer.\n    timerId = setTimeout(timerExpired, remainingWait(time));\n  }\n\n  function trailingEdge(time) {\n    timerId = undefined;\n\n    // Only invoke if we have `lastArgs` which means `func` has been\n    // debounced at least once.\n    if (trailing && lastArgs) {\n      return invokeFunc(time);\n    }\n    lastArgs = lastThis = undefined;\n    return result;\n  }\n\n  function cancel() {\n    if (timerId !== undefined) {\n      clearTimeout(timerId);\n    }\n    lastInvokeTime = 0;\n    lastArgs = lastCallTime = lastThis = timerId = undefined;\n  }\n\n  function flush() {\n    return timerId === undefined ? result : trailingEdge(now());\n  }\n\n  function debounced() {\n    var time = now(),\n        isInvoking = shouldInvoke(time);\n\n    lastArgs = arguments;\n    lastThis = this;\n    lastCallTime = time;\n\n    if (isInvoking) {\n      if (timerId === undefined) {\n        return leadingEdge(lastCallTime);\n      }\n      if (maxing) {\n        // Handle invocations in a tight loop.\n        clearTimeout(timerId);\n        timerId = setTimeout(timerExpired, wait);\n        return invokeFunc(lastCallTime);\n      }\n    }\n    if (timerId === undefined) {\n      timerId = setTimeout(timerExpired, wait);\n    }\n    return result;\n  }\n  debounced.cancel = cancel;\n  debounced.flush = flush;\n  return debounced;\n}\n\nmodule.exports = debounce;\n", "/**\n * Checks if `value` is the\n * [language type](http://www.ecma-international.org/ecma-262/7.0/#sec-ecmascript-language-types)\n * of `Object`. (e.g. arrays, functions, objects, regexes, `new Number(0)`, and `new String('')`)\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an object, else `false`.\n * @example\n *\n * _.isObject({});\n * // => true\n *\n * _.isObject([1, 2, 3]);\n * // => true\n *\n * _.isObject(_.noop);\n * // => true\n *\n * _.isObject(null);\n * // => false\n */\nfunction isObject(value) {\n  var type = typeof value;\n  return value != null && (type == 'object' || type == 'function');\n}\n\nmodule.exports = isObject;\n", "/**\n * Checks if `value` is object-like. A value is object-like if it's not `null`\n * and has a `typeof` result of \"object\".\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is object-like, else `false`.\n * @example\n *\n * _.isObjectLike({});\n * // => true\n *\n * _.isObjectLike([1, 2, 3]);\n * // => true\n *\n * _.isObjectLike(_.noop);\n * // => false\n *\n * _.isObjectLike(null);\n * // => false\n */\nfunction isObjectLike(value) {\n  return value != null && typeof value == 'object';\n}\n\nmodule.exports = isObjectLike;\n", "var baseGetTag = require('./_baseGetTag'),\n    isObjectLike = require('./isObjectLike');\n\n/** `Object#toString` result references. */\nvar symbolTag = '[object Symbol]';\n\n/**\n * Checks if `value` is classified as a `Symbol` primitive or object.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a symbol, else `false`.\n * @example\n *\n * _.isSymbol(Symbol.iterator);\n * // => true\n *\n * _.isSymbol('abc');\n * // => false\n */\nfunction isSymbol(value) {\n  return typeof value == 'symbol' ||\n    (isObjectLike(value) && baseGetTag(value) == symbolTag);\n}\n\nmodule.exports = isSymbol;\n", "var root = require('./_root');\n\n/**\n * Gets the timestamp of the number of milliseconds that have elapsed since\n * the Unix epoch (1 January 1970 00:00:00 UTC).\n *\n * @static\n * @memberOf _\n * @since 2.4.0\n * @category Date\n * @returns {number} Returns the timestamp.\n * @example\n *\n * _.defer(function(stamp) {\n *   console.log(_.now() - stamp);\n * }, _.now());\n * // => Logs the number of milliseconds it took for the deferred invocation.\n */\nvar now = function() {\n  return root.Date.now();\n};\n\nmodule.exports = now;\n", "var baseTrim = require('./_baseTrim'),\n    isObject = require('./isObject'),\n    isSymbol = require('./isSymbol');\n\n/** Used as references for various `Number` constants. */\nvar NAN = 0 / 0;\n\n/** Used to detect bad signed hexadecimal string values. */\nvar reIsBadHex = /^[-+]0x[0-9a-f]+$/i;\n\n/** Used to detect binary string values. */\nvar reIsBinary = /^0b[01]+$/i;\n\n/** Used to detect octal string values. */\nvar reIsOctal = /^0o[0-7]+$/i;\n\n/** Built-in method references without a dependency on `root`. */\nvar freeParseInt = parseInt;\n\n/**\n * Converts `value` to a number.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to process.\n * @returns {number} Returns the number.\n * @example\n *\n * _.toNumber(3.2);\n * // => 3.2\n *\n * _.toNumber(Number.MIN_VALUE);\n * // => 5e-324\n *\n * _.toNumber(Infinity);\n * // => Infinity\n *\n * _.toNumber('3.2');\n * // => 3.2\n */\nfunction toNumber(value) {\n  if (typeof value == 'number') {\n    return value;\n  }\n  if (isSymbol(value)) {\n    return NAN;\n  }\n  if (isObject(value)) {\n    var other = typeof value.valueOf == 'function' ? value.valueOf() : value;\n    value = isObject(other) ? (other + '') : other;\n  }\n  if (typeof value != 'string') {\n    return value === 0 ? value : +value;\n  }\n  value = baseTrim(value);\n  var isBinary = reIsBinary.test(value);\n  return (isBinary || reIsOctal.test(value))\n    ? freeParseInt(value.slice(2), isBinary ? 2 : 8)\n    : (reIsBadHex.test(value) ? NAN : +value);\n}\n\nmodule.exports = toNumber;\n", "// extracted by mini-css-extract-plugin\nexport {};", "// extracted by mini-css-extract-plugin\nexport {};", "// extracted by mini-css-extract-plugin\nexport {};", "// extracted by mini-css-extract-plugin\nexport {};", "// extracted by mini-css-extract-plugin\nexport {};", "// extracted by mini-css-extract-plugin\nexport {};", "// extracted by mini-css-extract-plugin\nexport {};", "// extracted by mini-css-extract-plugin\nexport {};", "// extracted by mini-css-extract-plugin\nexport {};", "// extracted by mini-css-extract-plugin\nexport {};", "function RavenConfigError(message) {\n  this.name = 'RavenConfigError';\n  this.message = message;\n}\nRavenConfigError.prototype = new Error();\nRavenConfigError.prototype.constructor = RavenConfigError;\n\nmodule.exports = RavenConfigError;\n", "var wrapMethod = function(console, level, callback) {\n  var originalConsoleLevel = console[level];\n  var originalConsole = console;\n\n  if (!(level in console)) {\n    return;\n  }\n\n  var sentryLevel = level === 'warn' ? 'warning' : level;\n\n  console[level] = function() {\n    var args = [].slice.call(arguments);\n\n    var msg = '' + args.join(' ');\n    var data = {level: sentryLevel, logger: 'console', extra: {arguments: args}};\n\n    if (level === 'assert') {\n      if (args[0] === false) {\n        // Default browsers message\n        msg = 'Assertion failed: ' + (args.slice(1).join(' ') || 'console.assert');\n        data.extra.arguments = args.slice(1);\n        callback && callback(msg, data);\n      }\n    } else {\n      callback && callback(msg, data);\n    }\n\n    // this fails for some browsers. :(\n    if (originalConsoleLevel) {\n      // IE9 doesn't allow calling apply on console functions directly\n      // See: https://stackoverflow.com/questions/5472938/does-ie9-support-console-log-and-is-it-a-real-function#answer-5473193\n      Function.prototype.apply.call(originalConsoleLevel, originalConsole, args);\n    }\n  };\n};\n\nmodule.exports = {\n  wrapMethod: wrapMethod\n};\n", "/*global XDomainRequest:false */\n\nvar TraceKit = require('../vendor/TraceKit/tracekit');\nvar stringify = require('../vendor/json-stringify-safe/stringify');\nvar RavenConfigError = require('./configError');\n\nvar utils = require('./utils');\nvar isError = utils.isError;\nvar isObject = utils.isObject;\nvar isObject = utils.isObject;\nvar isErrorEvent = utils.isErrorEvent;\nvar isUndefined = utils.isUndefined;\nvar isFunction = utils.isFunction;\nvar isString = utils.isString;\nvar isEmptyObject = utils.isEmptyObject;\nvar each = utils.each;\nvar objectMerge = utils.objectMerge;\nvar truncate = utils.truncate;\nvar objectFrozen = utils.objectFrozen;\nvar hasKey = utils.hasKey;\nvar joinRegExp = utils.joinRegExp;\nvar urlencode = utils.urlencode;\nvar uuid4 = utils.uuid4;\nvar htmlTreeAsString = utils.htmlTreeAsString;\nvar isSameException = utils.isSameException;\nvar isSameStacktrace = utils.isSameStacktrace;\nvar parseUrl = utils.parseUrl;\nvar fill = utils.fill;\n\nvar wrapConsoleMethod = require('./console').wrapMethod;\n\nvar dsnKeys = 'source protocol user pass host port path'.split(' '),\n  dsnPattern = /^(?:(\\w+):)?\\/\\/(?:(\\w+)(:\\w+)?@)?([\\w\\.-]+)(?::(\\d+))?(\\/.*)/;\n\nfunction now() {\n  return +new Date();\n}\n\n// This is to be defensive in environments where window does not exist (see https://github.com/getsentry/raven-js/pull/785)\nvar _window =\n  typeof window !== 'undefined'\n    ? window\n    : typeof global !== 'undefined' ? global : typeof self !== 'undefined' ? self : {};\nvar _document = _window.document;\nvar _navigator = _window.navigator;\n\nfunction keepOriginalCallback(original, callback) {\n  return isFunction(callback)\n    ? function(data) {\n        return callback(data, original);\n      }\n    : callback;\n}\n\n// First, check for JSON support\n// If there is no JSON, we no-op the core features of Raven\n// since JSON is required to encode the payload\nfunction Raven() {\n  this._hasJSON = !!(typeof JSON === 'object' && JSON.stringify);\n  // Raven can run in contexts where there's no document (react-native)\n  this._hasDocument = !isUndefined(_document);\n  this._hasNavigator = !isUndefined(_navigator);\n  this._lastCapturedException = null;\n  this._lastData = null;\n  this._lastEventId = null;\n  this._globalServer = null;\n  this._globalKey = null;\n  this._globalProject = null;\n  this._globalContext = {};\n  this._globalOptions = {\n    logger: 'javascript',\n    ignoreErrors: [],\n    ignoreUrls: [],\n    whitelistUrls: [],\n    includePaths: [],\n    collectWindowErrors: true,\n    maxMessageLength: 0,\n\n    // By default, truncates URL values to 250 chars\n    maxUrlLength: 250,\n    stackTraceLimit: 50,\n    autoBreadcrumbs: true,\n    instrument: true,\n    sampleRate: 1\n  };\n  this._ignoreOnError = 0;\n  this._isRavenInstalled = false;\n  this._originalErrorStackTraceLimit = Error.stackTraceLimit;\n  // capture references to window.console *and* all its methods first\n  // before the console plugin has a chance to monkey patch\n  this._originalConsole = _window.console || {};\n  this._originalConsoleMethods = {};\n  this._plugins = [];\n  this._startTime = now();\n  this._wrappedBuiltIns = [];\n  this._breadcrumbs = [];\n  this._lastCapturedEvent = null;\n  this._keypressTimeout;\n  this._location = _window.location;\n  this._lastHref = this._location && this._location.href;\n  this._resetBackoff();\n\n  // eslint-disable-next-line guard-for-in\n  for (var method in this._originalConsole) {\n    this._originalConsoleMethods[method] = this._originalConsole[method];\n  }\n}\n\n/*\n * The core Raven singleton\n *\n * @this {Raven}\n */\n\nRaven.prototype = {\n  // Hardcode version string so that raven source can be loaded directly via\n  // webpack (using a build step causes webpack #1617). Grunt verifies that\n  // this value matches package.json during build.\n  //   See: https://github.com/getsentry/raven-js/issues/465\n  VERSION: '3.19.1',\n\n  debug: false,\n\n  TraceKit: TraceKit, // alias to TraceKit\n\n  /*\n     * Configure Raven with a DSN and extra options\n     *\n     * @param {string} dsn The public Sentry DSN\n     * @param {object} options Set of global options [optional]\n     * @return {Raven}\n     */\n  config: function(dsn, options) {\n    var self = this;\n\n    if (self._globalServer) {\n      this._logDebug('error', 'Error: Raven has already been configured');\n      return self;\n    }\n    if (!dsn) return self;\n\n    var globalOptions = self._globalOptions;\n\n    // merge in options\n    if (options) {\n      each(options, function(key, value) {\n        // tags and extra are special and need to be put into context\n        if (key === 'tags' || key === 'extra' || key === 'user') {\n          self._globalContext[key] = value;\n        } else {\n          globalOptions[key] = value;\n        }\n      });\n    }\n\n    self.setDSN(dsn);\n\n    // \"Script error.\" is hard coded into browsers for errors that it can't read.\n    // this is the result of a script being pulled in from an external domain and CORS.\n    globalOptions.ignoreErrors.push(/^Script error\\.?$/);\n    globalOptions.ignoreErrors.push(/^Javascript error: Script error\\.? on line 0$/);\n\n    // join regexp rules into one big rule\n    globalOptions.ignoreErrors = joinRegExp(globalOptions.ignoreErrors);\n    globalOptions.ignoreUrls = globalOptions.ignoreUrls.length\n      ? joinRegExp(globalOptions.ignoreUrls)\n      : false;\n    globalOptions.whitelistUrls = globalOptions.whitelistUrls.length\n      ? joinRegExp(globalOptions.whitelistUrls)\n      : false;\n    globalOptions.includePaths = joinRegExp(globalOptions.includePaths);\n    globalOptions.maxBreadcrumbs = Math.max(\n      0,\n      Math.min(globalOptions.maxBreadcrumbs || 100, 100)\n    ); // default and hard limit is 100\n\n    var autoBreadcrumbDefaults = {\n      xhr: true,\n      console: true,\n      dom: true,\n      location: true\n    };\n\n    var autoBreadcrumbs = globalOptions.autoBreadcrumbs;\n    if ({}.toString.call(autoBreadcrumbs) === '[object Object]') {\n      autoBreadcrumbs = objectMerge(autoBreadcrumbDefaults, autoBreadcrumbs);\n    } else if (autoBreadcrumbs !== false) {\n      autoBreadcrumbs = autoBreadcrumbDefaults;\n    }\n    globalOptions.autoBreadcrumbs = autoBreadcrumbs;\n\n    var instrumentDefaults = {\n      tryCatch: true\n    };\n\n    var instrument = globalOptions.instrument;\n    if ({}.toString.call(instrument) === '[object Object]') {\n      instrument = objectMerge(instrumentDefaults, instrument);\n    } else if (instrument !== false) {\n      instrument = instrumentDefaults;\n    }\n    globalOptions.instrument = instrument;\n\n    TraceKit.collectWindowErrors = !!globalOptions.collectWindowErrors;\n\n    // return for chaining\n    return self;\n  },\n\n  /*\n     * Installs a global window.onerror error handler\n     * to capture and report uncaught exceptions.\n     * At this point, install() is required to be called due\n     * to the way TraceKit is set up.\n     *\n     * @return {Raven}\n     */\n  install: function() {\n    var self = this;\n    if (self.isSetup() && !self._isRavenInstalled) {\n      TraceKit.report.subscribe(function() {\n        self._handleOnErrorStackInfo.apply(self, arguments);\n      });\n      if (self._globalOptions.instrument && self._globalOptions.instrument.tryCatch) {\n        self._instrumentTryCatch();\n      }\n\n      if (self._globalOptions.autoBreadcrumbs) self._instrumentBreadcrumbs();\n\n      // Install all of the plugins\n      self._drainPlugins();\n\n      self._isRavenInstalled = true;\n    }\n\n    Error.stackTraceLimit = self._globalOptions.stackTraceLimit;\n    return this;\n  },\n\n  /*\n     * Set the DSN (can be called multiple time unlike config)\n     *\n     * @param {string} dsn The public Sentry DSN\n     */\n  setDSN: function(dsn) {\n    var self = this,\n      uri = self._parseDSN(dsn),\n      lastSlash = uri.path.lastIndexOf('/'),\n      path = uri.path.substr(1, lastSlash);\n\n    self._dsn = dsn;\n    self._globalKey = uri.user;\n    self._globalSecret = uri.pass && uri.pass.substr(1);\n    self._globalProject = uri.path.substr(lastSlash + 1);\n\n    self._globalServer = self._getGlobalServer(uri);\n\n    self._globalEndpoint =\n      self._globalServer + '/' + path + 'api/' + self._globalProject + '/store/';\n\n    // Reset backoff state since we may be pointing at a\n    // new project/server\n    this._resetBackoff();\n  },\n\n  /*\n     * Wrap code within a context so Raven can capture errors\n     * reliably across domains that is executed immediately.\n     *\n     * @param {object} options A specific set of options for this context [optional]\n     * @param {function} func The callback to be immediately executed within the context\n     * @param {array} args An array of arguments to be called with the callback [optional]\n     */\n  context: function(options, func, args) {\n    if (isFunction(options)) {\n      args = func || [];\n      func = options;\n      options = undefined;\n    }\n\n    return this.wrap(options, func).apply(this, args);\n  },\n\n  /*\n     * Wrap code within a context and returns back a new function to be executed\n     *\n     * @param {object} options A specific set of options for this context [optional]\n     * @param {function} func The function to be wrapped in a new context\n     * @param {function} func A function to call before the try/catch wrapper [optional, private]\n     * @return {function} The newly wrapped functions with a context\n     */\n  wrap: function(options, func, _before) {\n    var self = this;\n    // 1 argument has been passed, and it's not a function\n    // so just return it\n    if (isUndefined(func) && !isFunction(options)) {\n      return options;\n    }\n\n    // options is optional\n    if (isFunction(options)) {\n      func = options;\n      options = undefined;\n    }\n\n    // At this point, we've passed along 2 arguments, and the second one\n    // is not a function either, so we'll just return the second argument.\n    if (!isFunction(func)) {\n      return func;\n    }\n\n    // We don't wanna wrap it twice!\n    try {\n      if (func.__raven__) {\n        return func;\n      }\n\n      // If this has already been wrapped in the past, return that\n      if (func.__raven_wrapper__) {\n        return func.__raven_wrapper__;\n      }\n    } catch (e) {\n      // Just accessing custom props in some Selenium environments\n      // can cause a \"Permission denied\" exception (see raven-js#495).\n      // Bail on wrapping and return the function as-is (defers to window.onerror).\n      return func;\n    }\n\n    function wrapped() {\n      var args = [],\n        i = arguments.length,\n        deep = !options || (options && options.deep !== false);\n\n      if (_before && isFunction(_before)) {\n        _before.apply(this, arguments);\n      }\n\n      // Recursively wrap all of a function's arguments that are\n      // functions themselves.\n      while (i--) args[i] = deep ? self.wrap(options, arguments[i]) : arguments[i];\n\n      try {\n        // Attempt to invoke user-land function\n        // NOTE: If you are a Sentry user, and you are seeing this stack frame, it\n        //       means Raven caught an error invoking your application code. This is\n        //       expected behavior and NOT indicative of a bug with Raven.js.\n        return func.apply(this, args);\n      } catch (e) {\n        self._ignoreNextOnError();\n        self.captureException(e, options);\n        throw e;\n      }\n    }\n\n    // copy over properties of the old function\n    for (var property in func) {\n      if (hasKey(func, property)) {\n        wrapped[property] = func[property];\n      }\n    }\n    wrapped.prototype = func.prototype;\n\n    func.__raven_wrapper__ = wrapped;\n    // Signal that this function has been wrapped already\n    // for both debugging and to prevent it to being wrapped twice\n    wrapped.__raven__ = true;\n    wrapped.__inner__ = func;\n\n    return wrapped;\n  },\n\n  /*\n     * Uninstalls the global error handler.\n     *\n     * @return {Raven}\n     */\n  uninstall: function() {\n    TraceKit.report.uninstall();\n\n    this._restoreBuiltIns();\n\n    Error.stackTraceLimit = this._originalErrorStackTraceLimit;\n    this._isRavenInstalled = false;\n\n    return this;\n  },\n\n  /*\n     * Manually capture an exception and send it over to Sentry\n     *\n     * @param {error} ex An exception to be logged\n     * @param {object} options A specific set of options for this error [optional]\n     * @return {Raven}\n     */\n  captureException: function(ex, options) {\n    // Cases for sending ex as a message, rather than an exception\n    var isNotError = !isError(ex);\n    var isNotErrorEvent = !isErrorEvent(ex);\n    var isErrorEventWithoutError = isErrorEvent(ex) && !ex.error;\n\n    if ((isNotError && isNotErrorEvent) || isErrorEventWithoutError) {\n      return this.captureMessage(\n        ex,\n        objectMerge(\n          {\n            trimHeadFrames: 1,\n            stacktrace: true // if we fall back to captureMessage, default to attempting a new trace\n          },\n          options\n        )\n      );\n    }\n\n    // Get actual Error from ErrorEvent\n    if (isErrorEvent(ex)) ex = ex.error;\n\n    // Store the raw exception object for potential debugging and introspection\n    this._lastCapturedException = ex;\n\n    // TraceKit.report will re-raise any exception passed to it,\n    // which means you have to wrap it in try/catch. Instead, we\n    // can wrap it here and only re-raise if TraceKit.report\n    // raises an exception different from the one we asked to\n    // report on.\n    try {\n      var stack = TraceKit.computeStackTrace(ex);\n      this._handleStackInfo(stack, options);\n    } catch (ex1) {\n      if (ex !== ex1) {\n        throw ex1;\n      }\n    }\n\n    return this;\n  },\n\n  /*\n     * Manually send a message to Sentry\n     *\n     * @param {string} msg A plain message to be captured in Sentry\n     * @param {object} options A specific set of options for this message [optional]\n     * @return {Raven}\n     */\n  captureMessage: function(msg, options) {\n    // config() automagically converts ignoreErrors from a list to a RegExp so we need to test for an\n    // early call; we'll error on the side of logging anything called before configuration since it's\n    // probably something you should see:\n    if (\n      !!this._globalOptions.ignoreErrors.test &&\n      this._globalOptions.ignoreErrors.test(msg)\n    ) {\n      return;\n    }\n\n    options = options || {};\n\n    var data = objectMerge(\n      {\n        message: msg + '' // Make sure it's actually a string\n      },\n      options\n    );\n\n    var ex;\n    // Generate a \"synthetic\" stack trace from this point.\n    // NOTE: If you are a Sentry user, and you are seeing this stack frame, it is NOT indicative\n    //       of a bug with Raven.js. Sentry generates synthetic traces either by configuration,\n    //       or if it catches a thrown object without a \"stack\" property.\n    try {\n      throw new Error(msg);\n    } catch (ex1) {\n      ex = ex1;\n    }\n\n    // null exception name so `Error` isn't prefixed to msg\n    ex.name = null;\n    var stack = TraceKit.computeStackTrace(ex);\n\n    // stack[0] is `throw new Error(msg)` call itself, we are interested in the frame that was just before that, stack[1]\n    var initialCall = stack.stack[1];\n\n    var fileurl = (initialCall && initialCall.url) || '';\n\n    if (\n      !!this._globalOptions.ignoreUrls.test &&\n      this._globalOptions.ignoreUrls.test(fileurl)\n    ) {\n      return;\n    }\n\n    if (\n      !!this._globalOptions.whitelistUrls.test &&\n      !this._globalOptions.whitelistUrls.test(fileurl)\n    ) {\n      return;\n    }\n\n    if (this._globalOptions.stacktrace || (options && options.stacktrace)) {\n      options = objectMerge(\n        {\n          // fingerprint on msg, not stack trace (legacy behavior, could be\n          // revisited)\n          fingerprint: msg,\n          // since we know this is a synthetic trace, the top N-most frames\n          // MUST be from Raven.js, so mark them as in_app later by setting\n          // trimHeadFrames\n          trimHeadFrames: (options.trimHeadFrames || 0) + 1\n        },\n        options\n      );\n\n      var frames = this._prepareFrames(stack, options);\n      data.stacktrace = {\n        // Sentry expects frames oldest to newest\n        frames: frames.reverse()\n      };\n    }\n\n    // Fire away!\n    this._send(data);\n\n    return this;\n  },\n\n  captureBreadcrumb: function(obj) {\n    var crumb = objectMerge(\n      {\n        timestamp: now() / 1000\n      },\n      obj\n    );\n\n    if (isFunction(this._globalOptions.breadcrumbCallback)) {\n      var result = this._globalOptions.breadcrumbCallback(crumb);\n\n      if (isObject(result) && !isEmptyObject(result)) {\n        crumb = result;\n      } else if (result === false) {\n        return this;\n      }\n    }\n\n    this._breadcrumbs.push(crumb);\n    if (this._breadcrumbs.length > this._globalOptions.maxBreadcrumbs) {\n      this._breadcrumbs.shift();\n    }\n    return this;\n  },\n\n  addPlugin: function(plugin /*arg1, arg2, ... argN*/) {\n    var pluginArgs = [].slice.call(arguments, 1);\n\n    this._plugins.push([plugin, pluginArgs]);\n    if (this._isRavenInstalled) {\n      this._drainPlugins();\n    }\n\n    return this;\n  },\n\n  /*\n     * Set/clear a user to be sent along with the payload.\n     *\n     * @param {object} user An object representing user data [optional]\n     * @return {Raven}\n     */\n  setUserContext: function(user) {\n    // Intentionally do not merge here since that's an unexpected behavior.\n    this._globalContext.user = user;\n\n    return this;\n  },\n\n  /*\n     * Merge extra attributes to be sent along with the payload.\n     *\n     * @param {object} extra An object representing extra data [optional]\n     * @return {Raven}\n     */\n  setExtraContext: function(extra) {\n    this._mergeContext('extra', extra);\n\n    return this;\n  },\n\n  /*\n     * Merge tags to be sent along with the payload.\n     *\n     * @param {object} tags An object representing tags [optional]\n     * @return {Raven}\n     */\n  setTagsContext: function(tags) {\n    this._mergeContext('tags', tags);\n\n    return this;\n  },\n\n  /*\n     * Clear all of the context.\n     *\n     * @return {Raven}\n     */\n  clearContext: function() {\n    this._globalContext = {};\n\n    return this;\n  },\n\n  /*\n     * Get a copy of the current context. This cannot be mutated.\n     *\n     * @return {object} copy of context\n     */\n  getContext: function() {\n    // lol javascript\n    return JSON.parse(stringify(this._globalContext));\n  },\n\n  /*\n     * Set environment of application\n     *\n     * @param {string} environment Typically something like 'production'.\n     * @return {Raven}\n     */\n  setEnvironment: function(environment) {\n    this._globalOptions.environment = environment;\n\n    return this;\n  },\n\n  /*\n     * Set release version of application\n     *\n     * @param {string} release Typically something like a git SHA to identify version\n     * @return {Raven}\n     */\n  setRelease: function(release) {\n    this._globalOptions.release = release;\n\n    return this;\n  },\n\n  /*\n     * Set the dataCallback option\n     *\n     * @param {function} callback The callback to run which allows the\n     *                            data blob to be mutated before sending\n     * @return {Raven}\n     */\n  setDataCallback: function(callback) {\n    var original = this._globalOptions.dataCallback;\n    this._globalOptions.dataCallback = keepOriginalCallback(original, callback);\n    return this;\n  },\n\n  /*\n     * Set the breadcrumbCallback option\n     *\n     * @param {function} callback The callback to run which allows filtering\n     *                            or mutating breadcrumbs\n     * @return {Raven}\n     */\n  setBreadcrumbCallback: function(callback) {\n    var original = this._globalOptions.breadcrumbCallback;\n    this._globalOptions.breadcrumbCallback = keepOriginalCallback(original, callback);\n    return this;\n  },\n\n  /*\n     * Set the shouldSendCallback option\n     *\n     * @param {function} callback The callback to run which allows\n     *                            introspecting the blob before sending\n     * @return {Raven}\n     */\n  setShouldSendCallback: function(callback) {\n    var original = this._globalOptions.shouldSendCallback;\n    this._globalOptions.shouldSendCallback = keepOriginalCallback(original, callback);\n    return this;\n  },\n\n  /**\n     * Override the default HTTP transport mechanism that transmits data\n     * to the Sentry server.\n     *\n     * @param {function} transport Function invoked instead of the default\n     *                             `makeRequest` handler.\n     *\n     * @return {Raven}\n     */\n  setTransport: function(transport) {\n    this._globalOptions.transport = transport;\n\n    return this;\n  },\n\n  /*\n     * Get the latest raw exception that was captured by Raven.\n     *\n     * @return {error}\n     */\n  lastException: function() {\n    return this._lastCapturedException;\n  },\n\n  /*\n     * Get the last event id\n     *\n     * @return {string}\n     */\n  lastEventId: function() {\n    return this._lastEventId;\n  },\n\n  /*\n     * Determine if Raven is setup and ready to go.\n     *\n     * @return {boolean}\n     */\n  isSetup: function() {\n    if (!this._hasJSON) return false; // needs JSON support\n    if (!this._globalServer) {\n      if (!this.ravenNotConfiguredError) {\n        this.ravenNotConfiguredError = true;\n        this._logDebug('error', 'Error: Raven has not been configured.');\n      }\n      return false;\n    }\n    return true;\n  },\n\n  afterLoad: function() {\n    // TODO: remove window dependence?\n\n    // Attempt to initialize Raven on load\n    var RavenConfig = _window.RavenConfig;\n    if (RavenConfig) {\n      this.config(RavenConfig.dsn, RavenConfig.config).install();\n    }\n  },\n\n  showReportDialog: function(options) {\n    if (\n      !_document // doesn't work without a document (React native)\n    )\n      return;\n\n    options = options || {};\n\n    var lastEventId = options.eventId || this.lastEventId();\n    if (!lastEventId) {\n      throw new RavenConfigError('Missing eventId');\n    }\n\n    var dsn = options.dsn || this._dsn;\n    if (!dsn) {\n      throw new RavenConfigError('Missing DSN');\n    }\n\n    var encode = encodeURIComponent;\n    var qs = '';\n    qs += '?eventId=' + encode(lastEventId);\n    qs += '&dsn=' + encode(dsn);\n\n    var user = options.user || this._globalContext.user;\n    if (user) {\n      if (user.name) qs += '&name=' + encode(user.name);\n      if (user.email) qs += '&email=' + encode(user.email);\n    }\n\n    var globalServer = this._getGlobalServer(this._parseDSN(dsn));\n\n    var script = _document.createElement('script');\n    script.async = true;\n    script.src = globalServer + '/api/embed/error-page/' + qs;\n    (_document.head || _document.body).appendChild(script);\n  },\n\n  /**** Private functions ****/\n  _ignoreNextOnError: function() {\n    var self = this;\n    this._ignoreOnError += 1;\n    setTimeout(function() {\n      // onerror should trigger before setTimeout\n      self._ignoreOnError -= 1;\n    });\n  },\n\n  _triggerEvent: function(eventType, options) {\n    // NOTE: `event` is a native browser thing, so let's avoid conflicting wiht it\n    var evt, key;\n\n    if (!this._hasDocument) return;\n\n    options = options || {};\n\n    eventType = 'raven' + eventType.substr(0, 1).toUpperCase() + eventType.substr(1);\n\n    if (_document.createEvent) {\n      evt = _document.createEvent('HTMLEvents');\n      evt.initEvent(eventType, true, true);\n    } else {\n      evt = _document.createEventObject();\n      evt.eventType = eventType;\n    }\n\n    for (key in options)\n      if (hasKey(options, key)) {\n        evt[key] = options[key];\n      }\n\n    if (_document.createEvent) {\n      // IE9 if standards\n      _document.dispatchEvent(evt);\n    } else {\n      // IE8 regardless of Quirks or Standards\n      // IE9 if quirks\n      try {\n        _document.fireEvent('on' + evt.eventType.toLowerCase(), evt);\n      } catch (e) {\n        // Do nothing\n      }\n    }\n  },\n\n  /**\n     * Wraps addEventListener to capture UI breadcrumbs\n     * @param evtName the event name (e.g. \"click\")\n     * @returns {Function}\n     * @private\n     */\n  _breadcrumbEventHandler: function(evtName) {\n    var self = this;\n    return function(evt) {\n      // reset keypress timeout; e.g. triggering a 'click' after\n      // a 'keypress' will reset the keypress debounce so that a new\n      // set of keypresses can be recorded\n      self._keypressTimeout = null;\n\n      // It's possible this handler might trigger multiple times for the same\n      // event (e.g. event propagation through node ancestors). Ignore if we've\n      // already captured the event.\n      if (self._lastCapturedEvent === evt) return;\n\n      self._lastCapturedEvent = evt;\n\n      // try/catch both:\n      // - accessing evt.target (see getsentry/raven-js#838, #768)\n      // - `htmlTreeAsString` because it's complex, and just accessing the DOM incorrectly\n      //   can throw an exception in some circumstances.\n      var target;\n      try {\n        target = htmlTreeAsString(evt.target);\n      } catch (e) {\n        target = '<unknown>';\n      }\n\n      self.captureBreadcrumb({\n        category: 'ui.' + evtName, // e.g. ui.click, ui.input\n        message: target\n      });\n    };\n  },\n\n  /**\n     * Wraps addEventListener to capture keypress UI events\n     * @returns {Function}\n     * @private\n     */\n  _keypressEventHandler: function() {\n    var self = this,\n      debounceDuration = 1000; // milliseconds\n\n    // TODO: if somehow user switches keypress target before\n    //       debounce timeout is triggered, we will only capture\n    //       a single breadcrumb from the FIRST target (acceptable?)\n    return function(evt) {\n      var target;\n      try {\n        target = evt.target;\n      } catch (e) {\n        // just accessing event properties can throw an exception in some rare circumstances\n        // see: https://github.com/getsentry/raven-js/issues/838\n        return;\n      }\n      var tagName = target && target.tagName;\n\n      // only consider keypress events on actual input elements\n      // this will disregard keypresses targeting body (e.g. tabbing\n      // through elements, hotkeys, etc)\n      if (\n        !tagName ||\n        (tagName !== 'INPUT' && tagName !== 'TEXTAREA' && !target.isContentEditable)\n      )\n        return;\n\n      // record first keypress in a series, but ignore subsequent\n      // keypresses until debounce clears\n      var timeout = self._keypressTimeout;\n      if (!timeout) {\n        self._breadcrumbEventHandler('input')(evt);\n      }\n      clearTimeout(timeout);\n      self._keypressTimeout = setTimeout(function() {\n        self._keypressTimeout = null;\n      }, debounceDuration);\n    };\n  },\n\n  /**\n     * Captures a breadcrumb of type \"navigation\", normalizing input URLs\n     * @param to the originating URL\n     * @param from the target URL\n     * @private\n     */\n  _captureUrlChange: function(from, to) {\n    var parsedLoc = parseUrl(this._location.href);\n    var parsedTo = parseUrl(to);\n    var parsedFrom = parseUrl(from);\n\n    // because onpopstate only tells you the \"new\" (to) value of location.href, and\n    // not the previous (from) value, we need to track the value of the current URL\n    // state ourselves\n    this._lastHref = to;\n\n    // Use only the path component of the URL if the URL matches the current\n    // document (almost all the time when using pushState)\n    if (parsedLoc.protocol === parsedTo.protocol && parsedLoc.host === parsedTo.host)\n      to = parsedTo.relative;\n    if (parsedLoc.protocol === parsedFrom.protocol && parsedLoc.host === parsedFrom.host)\n      from = parsedFrom.relative;\n\n    this.captureBreadcrumb({\n      category: 'navigation',\n      data: {\n        to: to,\n        from: from\n      }\n    });\n  },\n\n  /**\n     * Wrap timer functions and event targets to catch errors and provide\n     * better metadata.\n     */\n  _instrumentTryCatch: function() {\n    var self = this;\n\n    var wrappedBuiltIns = self._wrappedBuiltIns;\n\n    function wrapTimeFn(orig) {\n      return function(fn, t) {\n        // preserve arity\n        // Make a copy of the arguments to prevent deoptimization\n        // https://github.com/petkaantonov/bluebird/wiki/Optimization-killers#32-leaking-arguments\n        var args = new Array(arguments.length);\n        for (var i = 0; i < args.length; ++i) {\n          args[i] = arguments[i];\n        }\n        var originalCallback = args[0];\n        if (isFunction(originalCallback)) {\n          args[0] = self.wrap(originalCallback);\n        }\n\n        // IE < 9 doesn't support .call/.apply on setInterval/setTimeout, but it\n        // also supports only two arguments and doesn't care what this is, so we\n        // can just call the original function directly.\n        if (orig.apply) {\n          return orig.apply(this, args);\n        } else {\n          return orig(args[0], args[1]);\n        }\n      };\n    }\n\n    var autoBreadcrumbs = this._globalOptions.autoBreadcrumbs;\n\n    function wrapEventTarget(global) {\n      var proto = _window[global] && _window[global].prototype;\n      if (proto && proto.hasOwnProperty && proto.hasOwnProperty('addEventListener')) {\n        fill(\n          proto,\n          'addEventListener',\n          function(orig) {\n            return function(evtName, fn, capture, secure) {\n              // preserve arity\n              try {\n                if (fn && fn.handleEvent) {\n                  fn.handleEvent = self.wrap(fn.handleEvent);\n                }\n              } catch (err) {\n                // can sometimes get 'Permission denied to access property \"handle Event'\n              }\n\n              // More breadcrumb DOM capture ... done here and not in `_instrumentBreadcrumbs`\n              // so that we don't have more than one wrapper function\n              var before, clickHandler, keypressHandler;\n\n              if (\n                autoBreadcrumbs &&\n                autoBreadcrumbs.dom &&\n                (global === 'EventTarget' || global === 'Node')\n              ) {\n                // NOTE: generating multiple handlers per addEventListener invocation, should\n                //       revisit and verify we can just use one (almost certainly)\n                clickHandler = self._breadcrumbEventHandler('click');\n                keypressHandler = self._keypressEventHandler();\n                before = function(evt) {\n                  // need to intercept every DOM event in `before` argument, in case that\n                  // same wrapped method is re-used for different events (e.g. mousemove THEN click)\n                  // see #724\n                  if (!evt) return;\n\n                  var eventType;\n                  try {\n                    eventType = evt.type;\n                  } catch (e) {\n                    // just accessing event properties can throw an exception in some rare circumstances\n                    // see: https://github.com/getsentry/raven-js/issues/838\n                    return;\n                  }\n                  if (eventType === 'click') return clickHandler(evt);\n                  else if (eventType === 'keypress') return keypressHandler(evt);\n                };\n              }\n              return orig.call(\n                this,\n                evtName,\n                self.wrap(fn, undefined, before),\n                capture,\n                secure\n              );\n            };\n          },\n          wrappedBuiltIns\n        );\n        fill(\n          proto,\n          'removeEventListener',\n          function(orig) {\n            return function(evt, fn, capture, secure) {\n              try {\n                fn = fn && (fn.__raven_wrapper__ ? fn.__raven_wrapper__ : fn);\n              } catch (e) {\n                // ignore, accessing __raven_wrapper__ will throw in some Selenium environments\n              }\n              return orig.call(this, evt, fn, capture, secure);\n            };\n          },\n          wrappedBuiltIns\n        );\n      }\n    }\n\n    fill(_window, 'setTimeout', wrapTimeFn, wrappedBuiltIns);\n    fill(_window, 'setInterval', wrapTimeFn, wrappedBuiltIns);\n    if (_window.requestAnimationFrame) {\n      fill(\n        _window,\n        'requestAnimationFrame',\n        function(orig) {\n          return function(cb) {\n            return orig(self.wrap(cb));\n          };\n        },\n        wrappedBuiltIns\n      );\n    }\n\n    // event targets borrowed from bugsnag-js:\n    // https://github.com/bugsnag/bugsnag-js/blob/master/src/bugsnag.js#L666\n    var eventTargets = [\n      'EventTarget',\n      'Window',\n      'Node',\n      'ApplicationCache',\n      'AudioTrackList',\n      'ChannelMergerNode',\n      'CryptoOperation',\n      'EventSource',\n      'FileReader',\n      'HTMLUnknownElement',\n      'IDBDatabase',\n      'IDBRequest',\n      'IDBTransaction',\n      'KeyOperation',\n      'MediaController',\n      'MessagePort',\n      'ModalWindow',\n      'Notification',\n      'SVGElementInstance',\n      'Screen',\n      'TextTrack',\n      'TextTrackCue',\n      'TextTrackList',\n      'WebSocket',\n      'WebSocketWorker',\n      'Worker',\n      'XMLHttpRequest',\n      'XMLHttpRequestEventTarget',\n      'XMLHttpRequestUpload'\n    ];\n    for (var i = 0; i < eventTargets.length; i++) {\n      wrapEventTarget(eventTargets[i]);\n    }\n  },\n\n  /**\n     * Instrument browser built-ins w/ breadcrumb capturing\n     *  - XMLHttpRequests\n     *  - DOM interactions (click/typing)\n     *  - window.location changes\n     *  - console\n     *\n     * Can be disabled or individually configured via the `autoBreadcrumbs` config option\n     */\n  _instrumentBreadcrumbs: function() {\n    var self = this;\n    var autoBreadcrumbs = this._globalOptions.autoBreadcrumbs;\n\n    var wrappedBuiltIns = self._wrappedBuiltIns;\n\n    function wrapProp(prop, xhr) {\n      if (prop in xhr && isFunction(xhr[prop])) {\n        fill(xhr, prop, function(orig) {\n          return self.wrap(orig);\n        }); // intentionally don't track filled methods on XHR instances\n      }\n    }\n\n    if (autoBreadcrumbs.xhr && 'XMLHttpRequest' in _window) {\n      var xhrproto = XMLHttpRequest.prototype;\n      fill(\n        xhrproto,\n        'open',\n        function(origOpen) {\n          return function(method, url) {\n            // preserve arity\n\n            // if Sentry key appears in URL, don't capture\n            if (isString(url) && url.indexOf(self._globalKey) === -1) {\n              this.__raven_xhr = {\n                method: method,\n                url: url,\n                status_code: null\n              };\n            }\n\n            return origOpen.apply(this, arguments);\n          };\n        },\n        wrappedBuiltIns\n      );\n\n      fill(\n        xhrproto,\n        'send',\n        function(origSend) {\n          return function(data) {\n            // preserve arity\n            var xhr = this;\n\n            function onreadystatechangeHandler() {\n              if (xhr.__raven_xhr && xhr.readyState === 4) {\n                try {\n                  // touching statusCode in some platforms throws\n                  // an exception\n                  xhr.__raven_xhr.status_code = xhr.status;\n                } catch (e) {\n                  /* do nothing */\n                }\n\n                self.captureBreadcrumb({\n                  type: 'http',\n                  category: 'xhr',\n                  data: xhr.__raven_xhr\n                });\n              }\n            }\n\n            var props = ['onload', 'onerror', 'onprogress'];\n            for (var j = 0; j < props.length; j++) {\n              wrapProp(props[j], xhr);\n            }\n\n            if ('onreadystatechange' in xhr && isFunction(xhr.onreadystatechange)) {\n              fill(\n                xhr,\n                'onreadystatechange',\n                function(orig) {\n                  return self.wrap(orig, undefined, onreadystatechangeHandler);\n                } /* intentionally don't track this instrumentation */\n              );\n            } else {\n              // if onreadystatechange wasn't actually set by the page on this xhr, we\n              // are free to set our own and capture the breadcrumb\n              xhr.onreadystatechange = onreadystatechangeHandler;\n            }\n\n            return origSend.apply(this, arguments);\n          };\n        },\n        wrappedBuiltIns\n      );\n    }\n\n    if (autoBreadcrumbs.xhr && 'fetch' in _window) {\n      fill(\n        _window,\n        'fetch',\n        function(origFetch) {\n          return function(fn, t) {\n            // preserve arity\n            // Make a copy of the arguments to prevent deoptimization\n            // https://github.com/petkaantonov/bluebird/wiki/Optimization-killers#32-leaking-arguments\n            var args = new Array(arguments.length);\n            for (var i = 0; i < args.length; ++i) {\n              args[i] = arguments[i];\n            }\n\n            var fetchInput = args[0];\n            var method = 'GET';\n            var url;\n\n            if (typeof fetchInput === 'string') {\n              url = fetchInput;\n            } else if ('Request' in _window && fetchInput instanceof _window.Request) {\n              url = fetchInput.url;\n              if (fetchInput.method) {\n                method = fetchInput.method;\n              }\n            } else {\n              url = '' + fetchInput;\n            }\n\n            if (args[1] && args[1].method) {\n              method = args[1].method;\n            }\n\n            var fetchData = {\n              method: method,\n              url: url,\n              status_code: null\n            };\n\n            self.captureBreadcrumb({\n              type: 'http',\n              category: 'fetch',\n              data: fetchData\n            });\n\n            return origFetch.apply(this, args).then(function(response) {\n              fetchData.status_code = response.status;\n\n              return response;\n            });\n          };\n        },\n        wrappedBuiltIns\n      );\n    }\n\n    // Capture breadcrumbs from any click that is unhandled / bubbled up all the way\n    // to the document. Do this before we instrument addEventListener.\n    if (autoBreadcrumbs.dom && this._hasDocument) {\n      if (_document.addEventListener) {\n        _document.addEventListener('click', self._breadcrumbEventHandler('click'), false);\n        _document.addEventListener('keypress', self._keypressEventHandler(), false);\n      } else {\n        // IE8 Compatibility\n        _document.attachEvent('onclick', self._breadcrumbEventHandler('click'));\n        _document.attachEvent('onkeypress', self._keypressEventHandler());\n      }\n    }\n\n    // record navigation (URL) changes\n    // NOTE: in Chrome App environment, touching history.pushState, *even inside\n    //       a try/catch block*, will cause Chrome to output an error to console.error\n    // borrowed from: https://github.com/angular/angular.js/pull/13945/files\n    var chrome = _window.chrome;\n    var isChromePackagedApp = chrome && chrome.app && chrome.app.runtime;\n    var hasPushAndReplaceState =\n      !isChromePackagedApp &&\n      _window.history &&\n      history.pushState &&\n      history.replaceState;\n    if (autoBreadcrumbs.location && hasPushAndReplaceState) {\n      // TODO: remove onpopstate handler on uninstall()\n      var oldOnPopState = _window.onpopstate;\n      _window.onpopstate = function() {\n        var currentHref = self._location.href;\n        self._captureUrlChange(self._lastHref, currentHref);\n\n        if (oldOnPopState) {\n          return oldOnPopState.apply(this, arguments);\n        }\n      };\n\n      var historyReplacementFunction = function(origHistFunction) {\n        // note history.pushState.length is 0; intentionally not declaring\n        // params to preserve 0 arity\n        return function(/* state, title, url */) {\n          var url = arguments.length > 2 ? arguments[2] : undefined;\n\n          // url argument is optional\n          if (url) {\n            // coerce to string (this is what pushState does)\n            self._captureUrlChange(self._lastHref, url + '');\n          }\n\n          return origHistFunction.apply(this, arguments);\n        };\n      };\n\n      fill(history, 'pushState', historyReplacementFunction, wrappedBuiltIns);\n      fill(history, 'replaceState', historyReplacementFunction, wrappedBuiltIns);\n    }\n\n    if (autoBreadcrumbs.console && 'console' in _window && console.log) {\n      // console\n      var consoleMethodCallback = function(msg, data) {\n        self.captureBreadcrumb({\n          message: msg,\n          level: data.level,\n          category: 'console'\n        });\n      };\n\n      each(['debug', 'info', 'warn', 'error', 'log'], function(_, level) {\n        wrapConsoleMethod(console, level, consoleMethodCallback);\n      });\n    }\n  },\n\n  _restoreBuiltIns: function() {\n    // restore any wrapped builtins\n    var builtin;\n    while (this._wrappedBuiltIns.length) {\n      builtin = this._wrappedBuiltIns.shift();\n\n      var obj = builtin[0],\n        name = builtin[1],\n        orig = builtin[2];\n\n      obj[name] = orig;\n    }\n  },\n\n  _drainPlugins: function() {\n    var self = this;\n\n    // FIX ME TODO\n    each(this._plugins, function(_, plugin) {\n      var installer = plugin[0];\n      var args = plugin[1];\n      installer.apply(self, [self].concat(args));\n    });\n  },\n\n  _parseDSN: function(str) {\n    var m = dsnPattern.exec(str),\n      dsn = {},\n      i = 7;\n\n    try {\n      while (i--) dsn[dsnKeys[i]] = m[i] || '';\n    } catch (e) {\n      throw new RavenConfigError('Invalid DSN: ' + str);\n    }\n\n    if (dsn.pass && !this._globalOptions.allowSecretKey) {\n      throw new RavenConfigError(\n        'Do not specify your secret key in the DSN. See: http://bit.ly/raven-secret-key'\n      );\n    }\n\n    return dsn;\n  },\n\n  _getGlobalServer: function(uri) {\n    // assemble the endpoint from the uri pieces\n    var globalServer = '//' + uri.host + (uri.port ? ':' + uri.port : '');\n\n    if (uri.protocol) {\n      globalServer = uri.protocol + ':' + globalServer;\n    }\n    return globalServer;\n  },\n\n  _handleOnErrorStackInfo: function() {\n    // if we are intentionally ignoring errors via onerror, bail out\n    if (!this._ignoreOnError) {\n      this._handleStackInfo.apply(this, arguments);\n    }\n  },\n\n  _handleStackInfo: function(stackInfo, options) {\n    var frames = this._prepareFrames(stackInfo, options);\n\n    this._triggerEvent('handle', {\n      stackInfo: stackInfo,\n      options: options\n    });\n\n    this._processException(\n      stackInfo.name,\n      stackInfo.message,\n      stackInfo.url,\n      stackInfo.lineno,\n      frames,\n      options\n    );\n  },\n\n  _prepareFrames: function(stackInfo, options) {\n    var self = this;\n    var frames = [];\n    if (stackInfo.stack && stackInfo.stack.length) {\n      each(stackInfo.stack, function(i, stack) {\n        var frame = self._normalizeFrame(stack, stackInfo.url);\n        if (frame) {\n          frames.push(frame);\n        }\n      });\n\n      // e.g. frames captured via captureMessage throw\n      if (options && options.trimHeadFrames) {\n        for (var j = 0; j < options.trimHeadFrames && j < frames.length; j++) {\n          frames[j].in_app = false;\n        }\n      }\n    }\n    frames = frames.slice(0, this._globalOptions.stackTraceLimit);\n    return frames;\n  },\n\n  _normalizeFrame: function(frame, stackInfoUrl) {\n    // normalize the frames data\n    var normalized = {\n      filename: frame.url,\n      lineno: frame.line,\n      colno: frame.column,\n      function: frame.func || '?'\n    };\n\n    // Case when we don't have any information about the error\n    // E.g. throwing a string or raw object, instead of an `Error` in Firefox\n    // Generating synthetic error doesn't add any value here\n    //\n    // We should probably somehow let a user know that they should fix their code\n    if (!frame.url) {\n      normalized.filename = stackInfoUrl; // fallback to whole stacks url from onerror handler\n    }\n\n    normalized.in_app = !// determine if an exception came from outside of our app\n    // first we check the global includePaths list.\n    (\n      (!!this._globalOptions.includePaths.test &&\n        !this._globalOptions.includePaths.test(normalized.filename)) ||\n      // Now we check for fun, if the function name is Raven or TraceKit\n      /(Raven|TraceKit)\\./.test(normalized['function']) ||\n      // finally, we do a last ditch effort and check for raven.min.js\n      /raven\\.(min\\.)?js$/.test(normalized.filename)\n    );\n\n    return normalized;\n  },\n\n  _processException: function(type, message, fileurl, lineno, frames, options) {\n    var prefixedMessage = (type ? type + ': ' : '') + (message || '');\n    if (\n      !!this._globalOptions.ignoreErrors.test &&\n      (this._globalOptions.ignoreErrors.test(message) ||\n        this._globalOptions.ignoreErrors.test(prefixedMessage))\n    ) {\n      return;\n    }\n\n    var stacktrace;\n\n    if (frames && frames.length) {\n      fileurl = frames[0].filename || fileurl;\n      // Sentry expects frames oldest to newest\n      // and JS sends them as newest to oldest\n      frames.reverse();\n      stacktrace = {frames: frames};\n    } else if (fileurl) {\n      stacktrace = {\n        frames: [\n          {\n            filename: fileurl,\n            lineno: lineno,\n            in_app: true\n          }\n        ]\n      };\n    }\n\n    if (\n      !!this._globalOptions.ignoreUrls.test &&\n      this._globalOptions.ignoreUrls.test(fileurl)\n    ) {\n      return;\n    }\n\n    if (\n      !!this._globalOptions.whitelistUrls.test &&\n      !this._globalOptions.whitelistUrls.test(fileurl)\n    ) {\n      return;\n    }\n\n    var data = objectMerge(\n      {\n        // sentry.interfaces.Exception\n        exception: {\n          values: [\n            {\n              type: type,\n              value: message,\n              stacktrace: stacktrace\n            }\n          ]\n        },\n        culprit: fileurl\n      },\n      options\n    );\n\n    // Fire away!\n    this._send(data);\n  },\n\n  _trimPacket: function(data) {\n    // For now, we only want to truncate the two different messages\n    // but this could/should be expanded to just trim everything\n    var max = this._globalOptions.maxMessageLength;\n    if (data.message) {\n      data.message = truncate(data.message, max);\n    }\n    if (data.exception) {\n      var exception = data.exception.values[0];\n      exception.value = truncate(exception.value, max);\n    }\n\n    var request = data.request;\n    if (request) {\n      if (request.url) {\n        request.url = truncate(request.url, this._globalOptions.maxUrlLength);\n      }\n      if (request.Referer) {\n        request.Referer = truncate(request.Referer, this._globalOptions.maxUrlLength);\n      }\n    }\n\n    if (data.breadcrumbs && data.breadcrumbs.values)\n      this._trimBreadcrumbs(data.breadcrumbs);\n\n    return data;\n  },\n\n  /**\n     * Truncate breadcrumb values (right now just URLs)\n     */\n  _trimBreadcrumbs: function(breadcrumbs) {\n    // known breadcrumb properties with urls\n    // TODO: also consider arbitrary prop values that start with (https?)?://\n    var urlProps = ['to', 'from', 'url'],\n      urlProp,\n      crumb,\n      data;\n\n    for (var i = 0; i < breadcrumbs.values.length; ++i) {\n      crumb = breadcrumbs.values[i];\n      if (\n        !crumb.hasOwnProperty('data') ||\n        !isObject(crumb.data) ||\n        objectFrozen(crumb.data)\n      )\n        continue;\n\n      data = objectMerge({}, crumb.data);\n      for (var j = 0; j < urlProps.length; ++j) {\n        urlProp = urlProps[j];\n        if (data.hasOwnProperty(urlProp) && data[urlProp]) {\n          data[urlProp] = truncate(data[urlProp], this._globalOptions.maxUrlLength);\n        }\n      }\n      breadcrumbs.values[i].data = data;\n    }\n  },\n\n  _getHttpData: function() {\n    if (!this._hasNavigator && !this._hasDocument) return;\n    var httpData = {};\n\n    if (this._hasNavigator && _navigator.userAgent) {\n      httpData.headers = {\n        'User-Agent': navigator.userAgent\n      };\n    }\n\n    if (this._hasDocument) {\n      if (_document.location && _document.location.href) {\n        httpData.url = _document.location.href;\n      }\n      if (_document.referrer) {\n        if (!httpData.headers) httpData.headers = {};\n        httpData.headers.Referer = _document.referrer;\n      }\n    }\n\n    return httpData;\n  },\n\n  _resetBackoff: function() {\n    this._backoffDuration = 0;\n    this._backoffStart = null;\n  },\n\n  _shouldBackoff: function() {\n    return this._backoffDuration && now() - this._backoffStart < this._backoffDuration;\n  },\n\n  /**\n     * Returns true if the in-process data payload matches the signature\n     * of the previously-sent data\n     *\n     * NOTE: This has to be done at this level because TraceKit can generate\n     *       data from window.onerror WITHOUT an exception object (IE8, IE9,\n     *       other old browsers). This can take the form of an \"exception\"\n     *       data object with a single frame (derived from the onerror args).\n     */\n  _isRepeatData: function(current) {\n    var last = this._lastData;\n\n    if (\n      !last ||\n      current.message !== last.message || // defined for captureMessage\n      current.culprit !== last.culprit // defined for captureException/onerror\n    )\n      return false;\n\n    // Stacktrace interface (i.e. from captureMessage)\n    if (current.stacktrace || last.stacktrace) {\n      return isSameStacktrace(current.stacktrace, last.stacktrace);\n    } else if (current.exception || last.exception) {\n      // Exception interface (i.e. from captureException/onerror)\n      return isSameException(current.exception, last.exception);\n    }\n\n    return true;\n  },\n\n  _setBackoffState: function(request) {\n    // If we are already in a backoff state, don't change anything\n    if (this._shouldBackoff()) {\n      return;\n    }\n\n    var status = request.status;\n\n    // 400 - project_id doesn't exist or some other fatal\n    // 401 - invalid/revoked dsn\n    // 429 - too many requests\n    if (!(status === 400 || status === 401 || status === 429)) return;\n\n    var retry;\n    try {\n      // If Retry-After is not in Access-Control-Expose-Headers, most\n      // browsers will throw an exception trying to access it\n      retry = request.getResponseHeader('Retry-After');\n      retry = parseInt(retry, 10) * 1000; // Retry-After is returned in seconds\n    } catch (e) {\n      /* eslint no-empty:0 */\n    }\n\n    this._backoffDuration = retry\n      ? // If Sentry server returned a Retry-After value, use it\n        retry\n      : // Otherwise, double the last backoff duration (starts at 1 sec)\n        this._backoffDuration * 2 || 1000;\n\n    this._backoffStart = now();\n  },\n\n  _send: function(data) {\n    var globalOptions = this._globalOptions;\n\n    var baseData = {\n        project: this._globalProject,\n        logger: globalOptions.logger,\n        platform: 'javascript'\n      },\n      httpData = this._getHttpData();\n\n    if (httpData) {\n      baseData.request = httpData;\n    }\n\n    // HACK: delete `trimHeadFrames` to prevent from appearing in outbound payload\n    if (data.trimHeadFrames) delete data.trimHeadFrames;\n\n    data = objectMerge(baseData, data);\n\n    // Merge in the tags and extra separately since objectMerge doesn't handle a deep merge\n    data.tags = objectMerge(objectMerge({}, this._globalContext.tags), data.tags);\n    data.extra = objectMerge(objectMerge({}, this._globalContext.extra), data.extra);\n\n    // Send along our own collected metadata with extra\n    data.extra['session:duration'] = now() - this._startTime;\n\n    if (this._breadcrumbs && this._breadcrumbs.length > 0) {\n      // intentionally make shallow copy so that additions\n      // to breadcrumbs aren't accidentally sent in this request\n      data.breadcrumbs = {\n        values: [].slice.call(this._breadcrumbs, 0)\n      };\n    }\n\n    // If there are no tags/extra, strip the key from the payload alltogther.\n    if (isEmptyObject(data.tags)) delete data.tags;\n\n    if (this._globalContext.user) {\n      // sentry.interfaces.User\n      data.user = this._globalContext.user;\n    }\n\n    // Include the environment if it's defined in globalOptions\n    if (globalOptions.environment) data.environment = globalOptions.environment;\n\n    // Include the release if it's defined in globalOptions\n    if (globalOptions.release) data.release = globalOptions.release;\n\n    // Include server_name if it's defined in globalOptions\n    if (globalOptions.serverName) data.server_name = globalOptions.serverName;\n\n    if (isFunction(globalOptions.dataCallback)) {\n      data = globalOptions.dataCallback(data) || data;\n    }\n\n    // Why??????????\n    if (!data || isEmptyObject(data)) {\n      return;\n    }\n\n    // Check if the request should be filtered or not\n    if (\n      isFunction(globalOptions.shouldSendCallback) &&\n      !globalOptions.shouldSendCallback(data)\n    ) {\n      return;\n    }\n\n    // Backoff state: Sentry server previously responded w/ an error (e.g. 429 - too many requests),\n    // so drop requests until \"cool-off\" period has elapsed.\n    if (this._shouldBackoff()) {\n      this._logDebug('warn', 'Raven dropped error due to backoff: ', data);\n      return;\n    }\n\n    if (typeof globalOptions.sampleRate === 'number') {\n      if (Math.random() < globalOptions.sampleRate) {\n        this._sendProcessedPayload(data);\n      }\n    } else {\n      this._sendProcessedPayload(data);\n    }\n  },\n\n  _getUuid: function() {\n    return uuid4();\n  },\n\n  _sendProcessedPayload: function(data, callback) {\n    var self = this;\n    var globalOptions = this._globalOptions;\n\n    if (!this.isSetup()) return;\n\n    // Try and clean up the packet before sending by truncating long values\n    data = this._trimPacket(data);\n\n    // ideally duplicate error testing should occur *before* dataCallback/shouldSendCallback,\n    // but this would require copying an un-truncated copy of the data packet, which can be\n    // arbitrarily deep (extra_data) -- could be worthwhile? will revisit\n    if (!this._globalOptions.allowDuplicates && this._isRepeatData(data)) {\n      this._logDebug('warn', 'Raven dropped repeat event: ', data);\n      return;\n    }\n\n    // Send along an event_id if not explicitly passed.\n    // This event_id can be used to reference the error within Sentry itself.\n    // Set lastEventId after we know the error should actually be sent\n    this._lastEventId = data.event_id || (data.event_id = this._getUuid());\n\n    // Store outbound payload after trim\n    this._lastData = data;\n\n    this._logDebug('debug', 'Raven about to send:', data);\n\n    var auth = {\n      sentry_version: '7',\n      sentry_client: 'raven-js/' + this.VERSION,\n      sentry_key: this._globalKey\n    };\n\n    if (this._globalSecret) {\n      auth.sentry_secret = this._globalSecret;\n    }\n\n    var exception = data.exception && data.exception.values[0];\n    this.captureBreadcrumb({\n      category: 'sentry',\n      message: exception\n        ? (exception.type ? exception.type + ': ' : '') + exception.value\n        : data.message,\n      event_id: data.event_id,\n      level: data.level || 'error' // presume error unless specified\n    });\n\n    var url = this._globalEndpoint;\n    (globalOptions.transport || this._makeRequest).call(this, {\n      url: url,\n      auth: auth,\n      data: data,\n      options: globalOptions,\n      onSuccess: function success() {\n        self._resetBackoff();\n\n        self._triggerEvent('success', {\n          data: data,\n          src: url\n        });\n        callback && callback();\n      },\n      onError: function failure(error) {\n        self._logDebug('error', 'Raven transport failed to send: ', error);\n\n        if (error.request) {\n          self._setBackoffState(error.request);\n        }\n\n        self._triggerEvent('failure', {\n          data: data,\n          src: url\n        });\n        error = error || new Error('Raven send failed (no additional details provided)');\n        callback && callback(error);\n      }\n    });\n  },\n\n  _makeRequest: function(opts) {\n    var request = _window.XMLHttpRequest && new _window.XMLHttpRequest();\n    if (!request) return;\n\n    // if browser doesn't support CORS (e.g. IE7), we are out of luck\n    var hasCORS = 'withCredentials' in request || typeof XDomainRequest !== 'undefined';\n\n    if (!hasCORS) return;\n\n    var url = opts.url;\n\n    if ('withCredentials' in request) {\n      request.onreadystatechange = function() {\n        if (request.readyState !== 4) {\n          return;\n        } else if (request.status === 200) {\n          opts.onSuccess && opts.onSuccess();\n        } else if (opts.onError) {\n          var err = new Error('Sentry error code: ' + request.status);\n          err.request = request;\n          opts.onError(err);\n        }\n      };\n    } else {\n      request = new XDomainRequest();\n      // xdomainrequest cannot go http -> https (or vice versa),\n      // so always use protocol relative\n      url = url.replace(/^https?:/, '');\n\n      // onreadystatechange not supported by XDomainRequest\n      if (opts.onSuccess) {\n        request.onload = opts.onSuccess;\n      }\n      if (opts.onError) {\n        request.onerror = function() {\n          var err = new Error('Sentry error code: XDomainRequest');\n          err.request = request;\n          opts.onError(err);\n        };\n      }\n    }\n\n    // NOTE: auth is intentionally sent as part of query string (NOT as custom\n    //       HTTP header) so as to avoid preflight CORS requests\n    request.open('POST', url + '?' + urlencode(opts.auth));\n    request.send(stringify(opts.data));\n  },\n\n  _logDebug: function(level) {\n    if (this._originalConsoleMethods[level] && this.debug) {\n      // In IE<10 console methods do not have their own 'apply' method\n      Function.prototype.apply.call(\n        this._originalConsoleMethods[level],\n        this._originalConsole,\n        [].slice.call(arguments, 1)\n      );\n    }\n  },\n\n  _mergeContext: function(key, context) {\n    if (isUndefined(context)) {\n      delete this._globalContext[key];\n    } else {\n      this._globalContext[key] = objectMerge(this._globalContext[key] || {}, context);\n    }\n  }\n};\n\n// Deprecations\nRaven.prototype.setUser = Raven.prototype.setUserContext;\nRaven.prototype.setReleaseContext = Raven.prototype.setRelease;\n\nmodule.exports = Raven;\n", "/**\n * Enforces a single instance of the Raven client, and the\n * main entry point for <PERSON>. If you are a consumer of the\n * Raven library, you SHOULD load this file (vs raven.js).\n **/\n\nvar RavenConstructor = require('./raven');\n\n// This is to be defensive in environments where window does not exist (see https://github.com/getsentry/raven-js/pull/785)\nvar _window =\n  typeof window !== 'undefined'\n    ? window\n    : typeof global !== 'undefined' ? global : typeof self !== 'undefined' ? self : {};\nvar _Raven = _window.Raven;\n\nvar Raven = new RavenConstructor();\n\n/*\n * Allow multiple versions of Raven to be installed.\n * Strip Raven from the global context and returns the instance.\n *\n * @return {Raven}\n */\nRaven.noConflict = function() {\n  _window.Raven = _Raven;\n  return Raven;\n};\n\nRaven.afterLoad();\n\nmodule.exports = Raven;\n", "var _window =\n  typeof window !== 'undefined'\n    ? window\n    : typeof global !== 'undefined' ? global : typeof self !== 'undefined' ? self : {};\n\nfunction isObject(what) {\n  return typeof what === 'object' && what !== null;\n}\n\n// Yanked from https://git.io/vS8DV re-used under CC0\n// with some tiny modifications\nfunction isError(value) {\n  switch ({}.toString.call(value)) {\n    case '[object Error]':\n      return true;\n    case '[object Exception]':\n      return true;\n    case '[object DOMException]':\n      return true;\n    default:\n      return value instanceof Error;\n  }\n}\n\nfunction isErrorEvent(value) {\n  return supportsErrorEvent() && {}.toString.call(value) === '[object ErrorEvent]';\n}\n\nfunction isUndefined(what) {\n  return what === void 0;\n}\n\nfunction isFunction(what) {\n  return typeof what === 'function';\n}\n\nfunction isString(what) {\n  return Object.prototype.toString.call(what) === '[object String]';\n}\n\nfunction isEmptyObject(what) {\n  for (var _ in what) return false; // eslint-disable-line guard-for-in, no-unused-vars\n  return true;\n}\n\nfunction supportsErrorEvent() {\n  try {\n    new ErrorEvent(''); // eslint-disable-line no-new\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\n\nfunction wrappedCallback(callback) {\n  function dataCallback(data, original) {\n    var normalizedData = callback(data) || data;\n    if (original) {\n      return original(normalizedData) || normalizedData;\n    }\n    return normalizedData;\n  }\n\n  return dataCallback;\n}\n\nfunction each(obj, callback) {\n  var i, j;\n\n  if (isUndefined(obj.length)) {\n    for (i in obj) {\n      if (hasKey(obj, i)) {\n        callback.call(null, i, obj[i]);\n      }\n    }\n  } else {\n    j = obj.length;\n    if (j) {\n      for (i = 0; i < j; i++) {\n        callback.call(null, i, obj[i]);\n      }\n    }\n  }\n}\n\nfunction objectMerge(obj1, obj2) {\n  if (!obj2) {\n    return obj1;\n  }\n  each(obj2, function(key, value) {\n    obj1[key] = value;\n  });\n  return obj1;\n}\n\n/**\n * This function is only used for react-native.\n * react-native freezes object that have already been sent over the\n * js bridge. We need this function in order to check if the object is frozen.\n * So it's ok that objectFrozen returns false if Object.isFrozen is not\n * supported because it's not relevant for other \"platforms\". See related issue:\n * https://github.com/getsentry/react-native-sentry/issues/57\n */\nfunction objectFrozen(obj) {\n  if (!Object.isFrozen) {\n    return false;\n  }\n  return Object.isFrozen(obj);\n}\n\nfunction truncate(str, max) {\n  return !max || str.length <= max ? str : str.substr(0, max) + '\\u2026';\n}\n\n/**\n * hasKey, a better form of hasOwnProperty\n * Example: hasKey(MainHostObject, property) === true/false\n *\n * @param {Object} host object to check property\n * @param {string} key to check\n */\nfunction hasKey(object, key) {\n  return Object.prototype.hasOwnProperty.call(object, key);\n}\n\nfunction joinRegExp(patterns) {\n  // Combine an array of regular expressions and strings into one large regexp\n  // Be mad.\n  var sources = [],\n    i = 0,\n    len = patterns.length,\n    pattern;\n\n  for (; i < len; i++) {\n    pattern = patterns[i];\n    if (isString(pattern)) {\n      // If it's a string, we need to escape it\n      // Taken from: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Regular_Expressions\n      sources.push(pattern.replace(/([.*+?^=!:${}()|\\[\\]\\/\\\\])/g, '\\\\$1'));\n    } else if (pattern && pattern.source) {\n      // If it's a regexp already, we want to extract the source\n      sources.push(pattern.source);\n    }\n    // Intentionally skip other cases\n  }\n  return new RegExp(sources.join('|'), 'i');\n}\n\nfunction urlencode(o) {\n  var pairs = [];\n  each(o, function(key, value) {\n    pairs.push(encodeURIComponent(key) + '=' + encodeURIComponent(value));\n  });\n  return pairs.join('&');\n}\n\n// borrowed from https://tools.ietf.org/html/rfc3986#appendix-B\n// intentionally using regex and not <a/> href parsing trick because React Native and other\n// environments where DOM might not be available\nfunction parseUrl(url) {\n  var match = url.match(/^(([^:\\/?#]+):)?(\\/\\/([^\\/?#]*))?([^?#]*)(\\?([^#]*))?(#(.*))?$/);\n  if (!match) return {};\n\n  // coerce to undefined values to empty string so we don't get 'undefined'\n  var query = match[6] || '';\n  var fragment = match[8] || '';\n  return {\n    protocol: match[2],\n    host: match[4],\n    path: match[5],\n    relative: match[5] + query + fragment // everything minus origin\n  };\n}\nfunction uuid4() {\n  var crypto = _window.crypto || _window.msCrypto;\n\n  if (!isUndefined(crypto) && crypto.getRandomValues) {\n    // Use window.crypto API if available\n    // eslint-disable-next-line no-undef\n    var arr = new Uint16Array(8);\n    crypto.getRandomValues(arr);\n\n    // set 4 in byte 7\n    arr[3] = (arr[3] & 0xfff) | 0x4000;\n    // set 2 most significant bits of byte 9 to '10'\n    arr[4] = (arr[4] & 0x3fff) | 0x8000;\n\n    var pad = function(num) {\n      var v = num.toString(16);\n      while (v.length < 4) {\n        v = '0' + v;\n      }\n      return v;\n    };\n\n    return (\n      pad(arr[0]) +\n      pad(arr[1]) +\n      pad(arr[2]) +\n      pad(arr[3]) +\n      pad(arr[4]) +\n      pad(arr[5]) +\n      pad(arr[6]) +\n      pad(arr[7])\n    );\n  } else {\n    // http://stackoverflow.com/questions/105034/how-to-create-a-guid-uuid-in-javascript/2117523#2117523\n    return 'xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\n      var r = (Math.random() * 16) | 0,\n        v = c === 'x' ? r : (r & 0x3) | 0x8;\n      return v.toString(16);\n    });\n  }\n}\n\n/**\n * Given a child DOM element, returns a query-selector statement describing that\n * and its ancestors\n * e.g. [HTMLElement] => body > div > input#foo.btn[name=baz]\n * @param elem\n * @returns {string}\n */\nfunction htmlTreeAsString(elem) {\n  /* eslint no-extra-parens:0*/\n  var MAX_TRAVERSE_HEIGHT = 5,\n    MAX_OUTPUT_LEN = 80,\n    out = [],\n    height = 0,\n    len = 0,\n    separator = ' > ',\n    sepLength = separator.length,\n    nextStr;\n\n  while (elem && height++ < MAX_TRAVERSE_HEIGHT) {\n    nextStr = htmlElementAsString(elem);\n    // bail out if\n    // - nextStr is the 'html' element\n    // - the length of the string that would be created exceeds MAX_OUTPUT_LEN\n    //   (ignore this limit if we are on the first iteration)\n    if (\n      nextStr === 'html' ||\n      (height > 1 && len + out.length * sepLength + nextStr.length >= MAX_OUTPUT_LEN)\n    ) {\n      break;\n    }\n\n    out.push(nextStr);\n\n    len += nextStr.length;\n    elem = elem.parentNode;\n  }\n\n  return out.reverse().join(separator);\n}\n\n/**\n * Returns a simple, query-selector representation of a DOM element\n * e.g. [HTMLElement] => input#foo.btn[name=baz]\n * @param HTMLElement\n * @returns {string}\n */\nfunction htmlElementAsString(elem) {\n  var out = [],\n    className,\n    classes,\n    key,\n    attr,\n    i;\n\n  if (!elem || !elem.tagName) {\n    return '';\n  }\n\n  out.push(elem.tagName.toLowerCase());\n  if (elem.id) {\n    out.push('#' + elem.id);\n  }\n\n  className = elem.className;\n  if (className && isString(className)) {\n    classes = className.split(/\\s+/);\n    for (i = 0; i < classes.length; i++) {\n      out.push('.' + classes[i]);\n    }\n  }\n  var attrWhitelist = ['type', 'name', 'title', 'alt'];\n  for (i = 0; i < attrWhitelist.length; i++) {\n    key = attrWhitelist[i];\n    attr = elem.getAttribute(key);\n    if (attr) {\n      out.push('[' + key + '=\"' + attr + '\"]');\n    }\n  }\n  return out.join('');\n}\n\n/**\n * Returns true if either a OR b is truthy, but not both\n */\nfunction isOnlyOneTruthy(a, b) {\n  return !!(!!a ^ !!b);\n}\n\n/**\n * Returns true if the two input exception interfaces have the same content\n */\nfunction isSameException(ex1, ex2) {\n  if (isOnlyOneTruthy(ex1, ex2)) return false;\n\n  ex1 = ex1.values[0];\n  ex2 = ex2.values[0];\n\n  if (ex1.type !== ex2.type || ex1.value !== ex2.value) return false;\n\n  return isSameStacktrace(ex1.stacktrace, ex2.stacktrace);\n}\n\n/**\n * Returns true if the two input stack trace interfaces have the same content\n */\nfunction isSameStacktrace(stack1, stack2) {\n  if (isOnlyOneTruthy(stack1, stack2)) return false;\n\n  var frames1 = stack1.frames;\n  var frames2 = stack2.frames;\n\n  // Exit early if frame count differs\n  if (frames1.length !== frames2.length) return false;\n\n  // Iterate through every frame; bail out if anything differs\n  var a, b;\n  for (var i = 0; i < frames1.length; i++) {\n    a = frames1[i];\n    b = frames2[i];\n    if (\n      a.filename !== b.filename ||\n      a.lineno !== b.lineno ||\n      a.colno !== b.colno ||\n      a['function'] !== b['function']\n    )\n      return false;\n  }\n  return true;\n}\n\n/**\n * Polyfill a method\n * @param obj object e.g. `document`\n * @param name method name present on object e.g. `addEventListener`\n * @param replacement replacement function\n * @param track {optional} record instrumentation to an array\n */\nfunction fill(obj, name, replacement, track) {\n  var orig = obj[name];\n  obj[name] = replacement(orig);\n  if (track) {\n    track.push([obj, name, orig]);\n  }\n}\n\nmodule.exports = {\n  isObject: isObject,\n  isError: isError,\n  isErrorEvent: isErrorEvent,\n  isUndefined: isUndefined,\n  isFunction: isFunction,\n  isString: isString,\n  isEmptyObject: isEmptyObject,\n  supportsErrorEvent: supportsErrorEvent,\n  wrappedCallback: wrappedCallback,\n  each: each,\n  objectMerge: objectMerge,\n  truncate: truncate,\n  objectFrozen: objectFrozen,\n  hasKey: hasKey,\n  joinRegExp: joinRegExp,\n  urlencode: urlencode,\n  uuid4: uuid4,\n  htmlTreeAsString: htmlTreeAsString,\n  htmlElementAsString: htmlElementAsString,\n  isSameException: isSameException,\n  isSameStacktrace: isSameStacktrace,\n  parseUrl: parseUrl,\n  fill: fill\n};\n", "var utils = require('../../src/utils');\n\n/*\n TraceKit - Cross brower stack traces\n\n This was originally forked from github.com/occ/TraceKit, but has since been\n largely re-written and is now maintained as part of raven-js.  Tests for\n this are in test/vendor.\n\n MIT license\n*/\n\nvar TraceKit = {\n  collectWindowErrors: true,\n  debug: false\n};\n\n// This is to be defensive in environments where window does not exist (see https://github.com/getsentry/raven-js/pull/785)\nvar _window =\n  typeof window !== 'undefined'\n    ? window\n    : typeof global !== 'undefined' ? global : typeof self !== 'undefined' ? self : {};\n\n// global reference to slice\nvar _slice = [].slice;\nvar UNKNOWN_FUNCTION = '?';\n\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error#Error_types\nvar ERROR_TYPES_RE = /^(?:[Uu]ncaught (?:exception: )?)?(?:((?:Eval|Internal|Range|Reference|Syntax|Type|URI|)Error): )?(.*)$/;\n\nfunction getLocationHref() {\n  if (typeof document === 'undefined' || document.location == null) return '';\n\n  return document.location.href;\n}\n\n/**\n * TraceKit.report: cross-browser processing of unhandled exceptions\n *\n * Syntax:\n *   TraceKit.report.subscribe(function(stackInfo) { ... })\n *   TraceKit.report.unsubscribe(function(stackInfo) { ... })\n *   TraceKit.report(exception)\n *   try { ...code... } catch(ex) { TraceKit.report(ex); }\n *\n * Supports:\n *   - Firefox: full stack trace with line numbers, plus column number\n *              on top frame; column number is not guaranteed\n *   - Opera:   full stack trace with line and column numbers\n *   - Chrome:  full stack trace with line and column numbers\n *   - Safari:  line and column number for the top frame only; some frames\n *              may be missing, and column number is not guaranteed\n *   - IE:      line and column number for the top frame only; some frames\n *              may be missing, and column number is not guaranteed\n *\n * In theory, TraceKit should work on all of the following versions:\n *   - IE5.5+ (only 8.0 tested)\n *   - Firefox 0.9+ (only 3.5+ tested)\n *   - Opera 7+ (only 10.50 tested; versions 9 and earlier may require\n *     Exceptions Have Stacktrace to be enabled in opera:config)\n *   - Safari 3+ (only 4+ tested)\n *   - Chrome 1+ (only 5+ tested)\n *   - Konqueror 3.5+ (untested)\n *\n * Requires TraceKit.computeStackTrace.\n *\n * Tries to catch all unhandled exceptions and report them to the\n * subscribed handlers. Please note that TraceKit.report will rethrow the\n * exception. This is REQUIRED in order to get a useful stack trace in IE.\n * If the exception does not reach the top of the browser, you will only\n * get a stack trace from the point where TraceKit.report was called.\n *\n * Handlers receive a stackInfo object as described in the\n * TraceKit.computeStackTrace docs.\n */\nTraceKit.report = (function reportModuleWrapper() {\n  var handlers = [],\n    lastArgs = null,\n    lastException = null,\n    lastExceptionStack = null;\n\n  /**\n     * Add a crash handler.\n     * @param {Function} handler\n     */\n  function subscribe(handler) {\n    installGlobalHandler();\n    handlers.push(handler);\n  }\n\n  /**\n     * Remove a crash handler.\n     * @param {Function} handler\n     */\n  function unsubscribe(handler) {\n    for (var i = handlers.length - 1; i >= 0; --i) {\n      if (handlers[i] === handler) {\n        handlers.splice(i, 1);\n      }\n    }\n  }\n\n  /**\n     * Remove all crash handlers.\n     */\n  function unsubscribeAll() {\n    uninstallGlobalHandler();\n    handlers = [];\n  }\n\n  /**\n     * Dispatch stack information to all handlers.\n     * @param {Object.<string, *>} stack\n     */\n  function notifyHandlers(stack, isWindowError) {\n    var exception = null;\n    if (isWindowError && !TraceKit.collectWindowErrors) {\n      return;\n    }\n    for (var i in handlers) {\n      if (handlers.hasOwnProperty(i)) {\n        try {\n          handlers[i].apply(null, [stack].concat(_slice.call(arguments, 2)));\n        } catch (inner) {\n          exception = inner;\n        }\n      }\n    }\n\n    if (exception) {\n      throw exception;\n    }\n  }\n\n  var _oldOnerrorHandler, _onErrorHandlerInstalled;\n\n  /**\n     * Ensures all global unhandled exceptions are recorded.\n     * Supported by Gecko and IE.\n     * @param {string} message Error message.\n     * @param {string} url URL of script that generated the exception.\n     * @param {(number|string)} lineNo The line number at which the error\n     * occurred.\n     * @param {?(number|string)} colNo The column number at which the error\n     * occurred.\n     * @param {?Error} ex The actual Error object.\n     */\n  function traceKitWindowOnError(message, url, lineNo, colNo, ex) {\n    var stack = null;\n\n    if (lastExceptionStack) {\n      TraceKit.computeStackTrace.augmentStackTraceWithInitialElement(\n        lastExceptionStack,\n        url,\n        lineNo,\n        message\n      );\n      processLastException();\n    } else if (ex && utils.isError(ex)) {\n      // non-string `ex` arg; attempt to extract stack trace\n\n      // New chrome and blink send along a real error object\n      // Let's just report that like a normal error.\n      // See: https://mikewest.org/2013/08/debugging-runtime-errors-with-window-onerror\n      stack = TraceKit.computeStackTrace(ex);\n      notifyHandlers(stack, true);\n    } else {\n      var location = {\n        url: url,\n        line: lineNo,\n        column: colNo\n      };\n\n      var name = undefined;\n      var msg = message; // must be new var or will modify original `arguments`\n      var groups;\n      if ({}.toString.call(message) === '[object String]') {\n        var groups = message.match(ERROR_TYPES_RE);\n        if (groups) {\n          name = groups[1];\n          msg = groups[2];\n        }\n      }\n\n      location.func = UNKNOWN_FUNCTION;\n\n      stack = {\n        name: name,\n        message: msg,\n        url: getLocationHref(),\n        stack: [location]\n      };\n      notifyHandlers(stack, true);\n    }\n\n    if (_oldOnerrorHandler) {\n      return _oldOnerrorHandler.apply(this, arguments);\n    }\n\n    return false;\n  }\n\n  function installGlobalHandler() {\n    if (_onErrorHandlerInstalled) {\n      return;\n    }\n    _oldOnerrorHandler = _window.onerror;\n    _window.onerror = traceKitWindowOnError;\n    _onErrorHandlerInstalled = true;\n  }\n\n  function uninstallGlobalHandler() {\n    if (!_onErrorHandlerInstalled) {\n      return;\n    }\n    _window.onerror = _oldOnerrorHandler;\n    _onErrorHandlerInstalled = false;\n    _oldOnerrorHandler = undefined;\n  }\n\n  function processLastException() {\n    var _lastExceptionStack = lastExceptionStack,\n      _lastArgs = lastArgs;\n    lastArgs = null;\n    lastExceptionStack = null;\n    lastException = null;\n    notifyHandlers.apply(null, [_lastExceptionStack, false].concat(_lastArgs));\n  }\n\n  /**\n     * Reports an unhandled Error to TraceKit.\n     * @param {Error} ex\n     * @param {?boolean} rethrow If false, do not re-throw the exception.\n     * Only used for window.onerror to not cause an infinite loop of\n     * rethrowing.\n     */\n  function report(ex, rethrow) {\n    var args = _slice.call(arguments, 1);\n    if (lastExceptionStack) {\n      if (lastException === ex) {\n        return; // already caught by an inner catch block, ignore\n      } else {\n        processLastException();\n      }\n    }\n\n    var stack = TraceKit.computeStackTrace(ex);\n    lastExceptionStack = stack;\n    lastException = ex;\n    lastArgs = args;\n\n    // If the stack trace is incomplete, wait for 2 seconds for\n    // slow slow IE to see if onerror occurs or not before reporting\n    // this exception; otherwise, we will end up with an incomplete\n    // stack trace\n    setTimeout(function() {\n      if (lastException === ex) {\n        processLastException();\n      }\n    }, stack.incomplete ? 2000 : 0);\n\n    if (rethrow !== false) {\n      throw ex; // re-throw to propagate to the top level (and cause window.onerror)\n    }\n  }\n\n  report.subscribe = subscribe;\n  report.unsubscribe = unsubscribe;\n  report.uninstall = unsubscribeAll;\n  return report;\n})();\n\n/**\n * TraceKit.computeStackTrace: cross-browser stack traces in JavaScript\n *\n * Syntax:\n *   s = TraceKit.computeStackTrace(exception) // consider using TraceKit.report instead (see below)\n * Returns:\n *   s.name              - exception name\n *   s.message           - exception message\n *   s.stack[i].url      - JavaScript or HTML file URL\n *   s.stack[i].func     - function name, or empty for anonymous functions (if guessing did not work)\n *   s.stack[i].args     - arguments passed to the function, if known\n *   s.stack[i].line     - line number, if known\n *   s.stack[i].column   - column number, if known\n *\n * Supports:\n *   - Firefox:  full stack trace with line numbers and unreliable column\n *               number on top frame\n *   - Opera 10: full stack trace with line and column numbers\n *   - Opera 9-: full stack trace with line numbers\n *   - Chrome:   full stack trace with line and column numbers\n *   - Safari:   line and column number for the topmost stacktrace element\n *               only\n *   - IE:       no line numbers whatsoever\n *\n * Tries to guess names of anonymous functions by looking for assignments\n * in the source code. In IE and Safari, we have to guess source file names\n * by searching for function bodies inside all page scripts. This will not\n * work for scripts that are loaded cross-domain.\n * Here be dragons: some function names may be guessed incorrectly, and\n * duplicate functions may be mismatched.\n *\n * TraceKit.computeStackTrace should only be used for tracing purposes.\n * Logging of unhandled exceptions should be done with TraceKit.report,\n * which builds on top of TraceKit.computeStackTrace and provides better\n * IE support by utilizing the window.onerror event to retrieve information\n * about the top of the stack.\n *\n * Note: In IE and Safari, no stack trace is recorded on the Error object,\n * so computeStackTrace instead walks its *own* chain of callers.\n * This means that:\n *  * in Safari, some methods may be missing from the stack trace;\n *  * in IE, the topmost function in the stack trace will always be the\n *    caller of computeStackTrace.\n *\n * This is okay for tracing (because you are likely to be calling\n * computeStackTrace from the function you want to be the topmost element\n * of the stack trace anyway), but not okay for logging unhandled\n * exceptions (because your catch block will likely be far away from the\n * inner function that actually caused the exception).\n *\n */\nTraceKit.computeStackTrace = (function computeStackTraceWrapper() {\n  // Contents of Exception in various browsers.\n  //\n  // SAFARI:\n  // ex.message = Can't find variable: qq\n  // ex.line = 59\n  // ex.sourceId = 580238192\n  // ex.sourceURL = http://...\n  // ex.expressionBeginOffset = 96\n  // ex.expressionCaretOffset = 98\n  // ex.expressionEndOffset = 98\n  // ex.name = ReferenceError\n  //\n  // FIREFOX:\n  // ex.message = qq is not defined\n  // ex.fileName = http://...\n  // ex.lineNumber = 59\n  // ex.columnNumber = 69\n  // ex.stack = ...stack trace... (see the example below)\n  // ex.name = ReferenceError\n  //\n  // CHROME:\n  // ex.message = qq is not defined\n  // ex.name = ReferenceError\n  // ex.type = not_defined\n  // ex.arguments = ['aa']\n  // ex.stack = ...stack trace...\n  //\n  // INTERNET EXPLORER:\n  // ex.message = ...\n  // ex.name = ReferenceError\n  //\n  // OPERA:\n  // ex.message = ...message... (see the example below)\n  // ex.name = ReferenceError\n  // ex.opera#sourceloc = 11  (pretty much useless, duplicates the info in ex.message)\n  // ex.stacktrace = n/a; see 'opera:config#UserPrefs|Exceptions Have Stacktrace'\n\n  /**\n     * Computes stack trace information from the stack property.\n     * Chrome and Gecko use this property.\n     * @param {Error} ex\n     * @return {?Object.<string, *>} Stack trace information.\n     */\n  function computeStackTraceFromStackProp(ex) {\n    if (typeof ex.stack === 'undefined' || !ex.stack) return;\n\n    var chrome = /^\\s*at (.*?) ?\\(((?:file|https?|blob|chrome-extension|native|eval|webpack|<anonymous>|[a-z]:|\\/).*?)(?::(\\d+))?(?::(\\d+))?\\)?\\s*$/i,\n      gecko = /^\\s*(.*?)(?:\\((.*?)\\))?(?:^|@)((?:file|https?|blob|chrome|webpack|resource|\\[native).*?|[^@]*bundle)(?::(\\d+))?(?::(\\d+))?\\s*$/i,\n      winjs = /^\\s*at (?:((?:\\[object object\\])?.+) )?\\(?((?:file|ms-appx|https?|webpack|blob):.*?):(\\d+)(?::(\\d+))?\\)?\\s*$/i,\n      // Used to additionally parse URL/line/column from eval frames\n      geckoEval = /(\\S+) line (\\d+)(?: > eval line \\d+)* > eval/i,\n      chromeEval = /\\((\\S*)(?::(\\d+))(?::(\\d+))\\)/,\n      lines = ex.stack.split('\\n'),\n      stack = [],\n      submatch,\n      parts,\n      element,\n      reference = /^(.*) is undefined$/.exec(ex.message);\n\n    for (var i = 0, j = lines.length; i < j; ++i) {\n      if ((parts = chrome.exec(lines[i]))) {\n        var isNative = parts[2] && parts[2].indexOf('native') === 0; // start of line\n        var isEval = parts[2] && parts[2].indexOf('eval') === 0; // start of line\n        if (isEval && (submatch = chromeEval.exec(parts[2]))) {\n          // throw out eval line/column and use top-most line/column number\n          parts[2] = submatch[1]; // url\n          parts[3] = submatch[2]; // line\n          parts[4] = submatch[3]; // column\n        }\n        element = {\n          url: !isNative ? parts[2] : null,\n          func: parts[1] || UNKNOWN_FUNCTION,\n          args: isNative ? [parts[2]] : [],\n          line: parts[3] ? +parts[3] : null,\n          column: parts[4] ? +parts[4] : null\n        };\n      } else if ((parts = winjs.exec(lines[i]))) {\n        element = {\n          url: parts[2],\n          func: parts[1] || UNKNOWN_FUNCTION,\n          args: [],\n          line: +parts[3],\n          column: parts[4] ? +parts[4] : null\n        };\n      } else if ((parts = gecko.exec(lines[i]))) {\n        var isEval = parts[3] && parts[3].indexOf(' > eval') > -1;\n        if (isEval && (submatch = geckoEval.exec(parts[3]))) {\n          // throw out eval line/column and use top-most line number\n          parts[3] = submatch[1];\n          parts[4] = submatch[2];\n          parts[5] = null; // no column when eval\n        } else if (i === 0 && !parts[5] && typeof ex.columnNumber !== 'undefined') {\n          // FireFox uses this awesome columnNumber property for its top frame\n          // Also note, Firefox's column number is 0-based and everything else expects 1-based,\n          // so adding 1\n          // NOTE: this hack doesn't work if top-most frame is eval\n          stack[0].column = ex.columnNumber + 1;\n        }\n        element = {\n          url: parts[3],\n          func: parts[1] || UNKNOWN_FUNCTION,\n          args: parts[2] ? parts[2].split(',') : [],\n          line: parts[4] ? +parts[4] : null,\n          column: parts[5] ? +parts[5] : null\n        };\n      } else {\n        continue;\n      }\n\n      if (!element.func && element.line) {\n        element.func = UNKNOWN_FUNCTION;\n      }\n\n      stack.push(element);\n    }\n\n    if (!stack.length) {\n      return null;\n    }\n\n    return {\n      name: ex.name,\n      message: ex.message,\n      url: getLocationHref(),\n      stack: stack\n    };\n  }\n\n  /**\n     * Adds information about the first frame to incomplete stack traces.\n     * Safari and IE require this to get complete data on the first frame.\n     * @param {Object.<string, *>} stackInfo Stack trace information from\n     * one of the compute* methods.\n     * @param {string} url The URL of the script that caused an error.\n     * @param {(number|string)} lineNo The line number of the script that\n     * caused an error.\n     * @param {string=} message The error generated by the browser, which\n     * hopefully contains the name of the object that caused the error.\n     * @return {boolean} Whether or not the stack information was\n     * augmented.\n     */\n  function augmentStackTraceWithInitialElement(stackInfo, url, lineNo, message) {\n    var initial = {\n      url: url,\n      line: lineNo\n    };\n\n    if (initial.url && initial.line) {\n      stackInfo.incomplete = false;\n\n      if (!initial.func) {\n        initial.func = UNKNOWN_FUNCTION;\n      }\n\n      if (stackInfo.stack.length > 0) {\n        if (stackInfo.stack[0].url === initial.url) {\n          if (stackInfo.stack[0].line === initial.line) {\n            return false; // already in stack trace\n          } else if (\n            !stackInfo.stack[0].line &&\n            stackInfo.stack[0].func === initial.func\n          ) {\n            stackInfo.stack[0].line = initial.line;\n            return false;\n          }\n        }\n      }\n\n      stackInfo.stack.unshift(initial);\n      stackInfo.partial = true;\n      return true;\n    } else {\n      stackInfo.incomplete = true;\n    }\n\n    return false;\n  }\n\n  /**\n     * Computes stack trace information by walking the arguments.caller\n     * chain at the time the exception occurred. This will cause earlier\n     * frames to be missed but is the only way to get any stack trace in\n     * Safari and IE. The top frame is restored by\n     * {@link augmentStackTraceWithInitialElement}.\n     * @param {Error} ex\n     * @return {?Object.<string, *>} Stack trace information.\n     */\n  function computeStackTraceByWalkingCallerChain(ex, depth) {\n    var functionName = /function\\s+([_$a-zA-Z\\xA0-\\uFFFF][_$a-zA-Z0-9\\xA0-\\uFFFF]*)?\\s*\\(/i,\n      stack = [],\n      funcs = {},\n      recursion = false,\n      parts,\n      item,\n      source;\n\n    for (\n      var curr = computeStackTraceByWalkingCallerChain.caller;\n      curr && !recursion;\n      curr = curr.caller\n    ) {\n      if (curr === computeStackTrace || curr === TraceKit.report) {\n        // console.log('skipping internal function');\n        continue;\n      }\n\n      item = {\n        url: null,\n        func: UNKNOWN_FUNCTION,\n        line: null,\n        column: null\n      };\n\n      if (curr.name) {\n        item.func = curr.name;\n      } else if ((parts = functionName.exec(curr.toString()))) {\n        item.func = parts[1];\n      }\n\n      if (typeof item.func === 'undefined') {\n        try {\n          item.func = parts.input.substring(0, parts.input.indexOf('{'));\n        } catch (e) {}\n      }\n\n      if (funcs['' + curr]) {\n        recursion = true;\n      } else {\n        funcs['' + curr] = true;\n      }\n\n      stack.push(item);\n    }\n\n    if (depth) {\n      // console.log('depth is ' + depth);\n      // console.log('stack is ' + stack.length);\n      stack.splice(0, depth);\n    }\n\n    var result = {\n      name: ex.name,\n      message: ex.message,\n      url: getLocationHref(),\n      stack: stack\n    };\n    augmentStackTraceWithInitialElement(\n      result,\n      ex.sourceURL || ex.fileName,\n      ex.line || ex.lineNumber,\n      ex.message || ex.description\n    );\n    return result;\n  }\n\n  /**\n     * Computes a stack trace for an exception.\n     * @param {Error} ex\n     * @param {(string|number)=} depth\n     */\n  function computeStackTrace(ex, depth) {\n    var stack = null;\n    depth = depth == null ? 0 : +depth;\n\n    try {\n      stack = computeStackTraceFromStackProp(ex);\n      if (stack) {\n        return stack;\n      }\n    } catch (e) {\n      if (TraceKit.debug) {\n        throw e;\n      }\n    }\n\n    try {\n      stack = computeStackTraceByWalkingCallerChain(ex, depth + 1);\n      if (stack) {\n        return stack;\n      }\n    } catch (e) {\n      if (TraceKit.debug) {\n        throw e;\n      }\n    }\n    return {\n      name: ex.name,\n      message: ex.message,\n      url: getLocationHref()\n    };\n  }\n\n  computeStackTrace.augmentStackTraceWithInitialElement = augmentStackTraceWithInitialElement;\n  computeStackTrace.computeStackTraceFromStackProp = computeStackTraceFromStackProp;\n\n  return computeStackTrace;\n})();\n\nmodule.exports = TraceKit;\n", "/*\n json-stringify-safe\n Like JSON.stringify, but doesn't throw on circular references.\n\n Originally forked from https://github.com/isaacs/json-stringify-safe\n version 5.0.1 on 3/8/2017 and modified to handle Errors serialization\n and IE8 compatibility. Tests for this are in test/vendor.\n\n ISC license: https://github.com/isaacs/json-stringify-safe/blob/master/LICENSE\n*/\n\nexports = module.exports = stringify;\nexports.getSerialize = serializer;\n\nfunction indexOf(haystack, needle) {\n  for (var i = 0; i < haystack.length; ++i) {\n    if (haystack[i] === needle) return i;\n  }\n  return -1;\n}\n\nfunction stringify(obj, replacer, spaces, cycleReplacer) {\n  return JSON.stringify(obj, serializer(replacer, cycleReplacer), spaces);\n}\n\n// https://github.com/ftlabs/js-abbreviate/blob/fa709e5f139e7770a71827b1893f22418097fbda/index.js#L95-L106\nfunction stringifyError(value) {\n  var err = {\n    // These properties are implemented as magical getters and don't show up in for in\n    stack: value.stack,\n    message: value.message,\n    name: value.name\n  };\n\n  for (var i in value) {\n    if (Object.prototype.hasOwnProperty.call(value, i)) {\n      err[i] = value[i];\n    }\n  }\n\n  return err;\n}\n\nfunction serializer(replacer, cycleReplacer) {\n  var stack = [];\n  var keys = [];\n\n  if (cycleReplacer == null) {\n    cycleReplacer = function(key, value) {\n      if (stack[0] === value) {\n        return '[Circular ~]';\n      }\n      return '[Circular ~.' + keys.slice(0, indexOf(stack, value)).join('.') + ']';\n    };\n  }\n\n  return function(key, value) {\n    if (stack.length > 0) {\n      var thisPos = indexOf(stack, this);\n      ~thisPos ? stack.splice(thisPos + 1) : stack.push(this);\n      ~thisPos ? keys.splice(thisPos, Infinity, key) : keys.push(key);\n\n      if (~indexOf(stack, value)) {\n        value = cycleReplacer.call(this, key, value);\n      }\n    } else {\n      stack.push(value);\n    }\n\n    return replacer == null\n      ? value instanceof Error ? stringifyError(value) : value\n      : replacer.call(this, key, value);\n  };\n}\n", "/**\n * @license React\n * react-jsx-runtime.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function() {\n'use strict';\n\nvar React = require('react');\n\n// ATTENTION\n// When adding new symbols to this file,\n// Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n// The Symbol used to tag the ReactElement-like types.\nvar REACT_ELEMENT_TYPE = Symbol.for('react.element');\nvar REACT_PORTAL_TYPE = Symbol.for('react.portal');\nvar REACT_FRAGMENT_TYPE = Symbol.for('react.fragment');\nvar REACT_STRICT_MODE_TYPE = Symbol.for('react.strict_mode');\nvar REACT_PROFILER_TYPE = Symbol.for('react.profiler');\nvar REACT_PROVIDER_TYPE = Symbol.for('react.provider');\nvar REACT_CONTEXT_TYPE = Symbol.for('react.context');\nvar REACT_FORWARD_REF_TYPE = Symbol.for('react.forward_ref');\nvar REACT_SUSPENSE_TYPE = Symbol.for('react.suspense');\nvar REACT_SUSPENSE_LIST_TYPE = Symbol.for('react.suspense_list');\nvar REACT_MEMO_TYPE = Symbol.for('react.memo');\nvar REACT_LAZY_TYPE = Symbol.for('react.lazy');\nvar REACT_OFFSCREEN_TYPE = Symbol.for('react.offscreen');\nvar MAYBE_ITERATOR_SYMBOL = Symbol.iterator;\nvar FAUX_ITERATOR_SYMBOL = '@@iterator';\nfunction getIteratorFn(maybeIterable) {\n  if (maybeIterable === null || typeof maybeIterable !== 'object') {\n    return null;\n  }\n\n  var maybeIterator = MAYBE_ITERATOR_SYMBOL && maybeIterable[MAYBE_ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL];\n\n  if (typeof maybeIterator === 'function') {\n    return maybeIterator;\n  }\n\n  return null;\n}\n\nvar ReactSharedInternals = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n\nfunction error(format) {\n  {\n    {\n      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        args[_key2 - 1] = arguments[_key2];\n      }\n\n      printWarning('error', format, args);\n    }\n  }\n}\n\nfunction printWarning(level, format, args) {\n  // When changing this logic, you might want to also\n  // update consoleWithStackDev.www.js as well.\n  {\n    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n    var stack = ReactDebugCurrentFrame.getStackAddendum();\n\n    if (stack !== '') {\n      format += '%s';\n      args = args.concat([stack]);\n    } // eslint-disable-next-line react-internal/safe-string-coercion\n\n\n    var argsWithFormat = args.map(function (item) {\n      return String(item);\n    }); // Careful: RN currently depends on this prefix\n\n    argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n    // breaks IE9: https://github.com/facebook/react/issues/13610\n    // eslint-disable-next-line react-internal/no-production-logging\n\n    Function.prototype.apply.call(console[level], console, argsWithFormat);\n  }\n}\n\n// -----------------------------------------------------------------------------\n\nvar enableScopeAPI = false; // Experimental Create Event Handle API.\nvar enableCacheElement = false;\nvar enableTransitionTracing = false; // No known bugs, but needs performance testing\n\nvar enableLegacyHidden = false; // Enables unstable_avoidThisFallback feature in Fiber\n// stuff. Intended to enable React core members to more easily debug scheduling\n// issues in DEV builds.\n\nvar enableDebugTracing = false; // Track which Fiber(s) schedule render work.\n\nvar REACT_MODULE_REFERENCE;\n\n{\n  REACT_MODULE_REFERENCE = Symbol.for('react.module.reference');\n}\n\nfunction isValidElementType(type) {\n  if (typeof type === 'string' || typeof type === 'function') {\n    return true;\n  } // Note: typeof might be other than 'symbol' or 'number' (e.g. if it's a polyfill).\n\n\n  if (type === REACT_FRAGMENT_TYPE || type === REACT_PROFILER_TYPE || enableDebugTracing  || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || enableLegacyHidden  || type === REACT_OFFSCREEN_TYPE || enableScopeAPI  || enableCacheElement  || enableTransitionTracing ) {\n    return true;\n  }\n\n  if (typeof type === 'object' && type !== null) {\n    if (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || // This needs to include all possible module reference object\n    // types supported by any Flight configuration anywhere since\n    // we don't know which Flight build this will end up being used\n    // with.\n    type.$$typeof === REACT_MODULE_REFERENCE || type.getModuleId !== undefined) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nfunction getWrappedName(outerType, innerType, wrapperName) {\n  var displayName = outerType.displayName;\n\n  if (displayName) {\n    return displayName;\n  }\n\n  var functionName = innerType.displayName || innerType.name || '';\n  return functionName !== '' ? wrapperName + \"(\" + functionName + \")\" : wrapperName;\n} // Keep in sync with react-reconciler/getComponentNameFromFiber\n\n\nfunction getContextName(type) {\n  return type.displayName || 'Context';\n} // Note that the reconciler package should generally prefer to use getComponentNameFromFiber() instead.\n\n\nfunction getComponentNameFromType(type) {\n  if (type == null) {\n    // Host root, text node or just invalid type.\n    return null;\n  }\n\n  {\n    if (typeof type.tag === 'number') {\n      error('Received an unexpected object in getComponentNameFromType(). ' + 'This is likely a bug in React. Please file an issue.');\n    }\n  }\n\n  if (typeof type === 'function') {\n    return type.displayName || type.name || null;\n  }\n\n  if (typeof type === 'string') {\n    return type;\n  }\n\n  switch (type) {\n    case REACT_FRAGMENT_TYPE:\n      return 'Fragment';\n\n    case REACT_PORTAL_TYPE:\n      return 'Portal';\n\n    case REACT_PROFILER_TYPE:\n      return 'Profiler';\n\n    case REACT_STRICT_MODE_TYPE:\n      return 'StrictMode';\n\n    case REACT_SUSPENSE_TYPE:\n      return 'Suspense';\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return 'SuspenseList';\n\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_CONTEXT_TYPE:\n        var context = type;\n        return getContextName(context) + '.Consumer';\n\n      case REACT_PROVIDER_TYPE:\n        var provider = type;\n        return getContextName(provider._context) + '.Provider';\n\n      case REACT_FORWARD_REF_TYPE:\n        return getWrappedName(type, type.render, 'ForwardRef');\n\n      case REACT_MEMO_TYPE:\n        var outerName = type.displayName || null;\n\n        if (outerName !== null) {\n          return outerName;\n        }\n\n        return getComponentNameFromType(type.type) || 'Memo';\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            return getComponentNameFromType(init(payload));\n          } catch (x) {\n            return null;\n          }\n        }\n\n      // eslint-disable-next-line no-fallthrough\n    }\n  }\n\n  return null;\n}\n\nvar assign = Object.assign;\n\n// Helpers to patch console.logs to avoid logging during side-effect free\n// replaying on render function. This currently only patches the object\n// lazily which won't cover if the log function was extracted eagerly.\n// We could also eagerly patch the method.\nvar disabledDepth = 0;\nvar prevLog;\nvar prevInfo;\nvar prevWarn;\nvar prevError;\nvar prevGroup;\nvar prevGroupCollapsed;\nvar prevGroupEnd;\n\nfunction disabledLog() {}\n\ndisabledLog.__reactDisabledLog = true;\nfunction disableLogs() {\n  {\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      prevLog = console.log;\n      prevInfo = console.info;\n      prevWarn = console.warn;\n      prevError = console.error;\n      prevGroup = console.group;\n      prevGroupCollapsed = console.groupCollapsed;\n      prevGroupEnd = console.groupEnd; // https://github.com/facebook/react/issues/19099\n\n      var props = {\n        configurable: true,\n        enumerable: true,\n        value: disabledLog,\n        writable: true\n      }; // $FlowFixMe Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        info: props,\n        log: props,\n        warn: props,\n        error: props,\n        group: props,\n        groupCollapsed: props,\n        groupEnd: props\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    disabledDepth++;\n  }\n}\nfunction reenableLogs() {\n  {\n    disabledDepth--;\n\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      var props = {\n        configurable: true,\n        enumerable: true,\n        writable: true\n      }; // $FlowFixMe Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        log: assign({}, props, {\n          value: prevLog\n        }),\n        info: assign({}, props, {\n          value: prevInfo\n        }),\n        warn: assign({}, props, {\n          value: prevWarn\n        }),\n        error: assign({}, props, {\n          value: prevError\n        }),\n        group: assign({}, props, {\n          value: prevGroup\n        }),\n        groupCollapsed: assign({}, props, {\n          value: prevGroupCollapsed\n        }),\n        groupEnd: assign({}, props, {\n          value: prevGroupEnd\n        })\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    if (disabledDepth < 0) {\n      error('disabledDepth fell below zero. ' + 'This is a bug in React. Please file an issue.');\n    }\n  }\n}\n\nvar ReactCurrentDispatcher = ReactSharedInternals.ReactCurrentDispatcher;\nvar prefix;\nfunction describeBuiltInComponentFrame(name, source, ownerFn) {\n  {\n    if (prefix === undefined) {\n      // Extract the VM specific prefix used by each line.\n      try {\n        throw Error();\n      } catch (x) {\n        var match = x.stack.trim().match(/\\n( *(at )?)/);\n        prefix = match && match[1] || '';\n      }\n    } // We use the prefix to ensure our stacks line up with native stack frames.\n\n\n    return '\\n' + prefix + name;\n  }\n}\nvar reentry = false;\nvar componentFrameCache;\n\n{\n  var PossiblyWeakMap = typeof WeakMap === 'function' ? WeakMap : Map;\n  componentFrameCache = new PossiblyWeakMap();\n}\n\nfunction describeNativeComponentFrame(fn, construct) {\n  // If something asked for a stack inside a fake render, it should get ignored.\n  if ( !fn || reentry) {\n    return '';\n  }\n\n  {\n    var frame = componentFrameCache.get(fn);\n\n    if (frame !== undefined) {\n      return frame;\n    }\n  }\n\n  var control;\n  reentry = true;\n  var previousPrepareStackTrace = Error.prepareStackTrace; // $FlowFixMe It does accept undefined.\n\n  Error.prepareStackTrace = undefined;\n  var previousDispatcher;\n\n  {\n    previousDispatcher = ReactCurrentDispatcher.current; // Set the dispatcher in DEV because this might be call in the render function\n    // for warnings.\n\n    ReactCurrentDispatcher.current = null;\n    disableLogs();\n  }\n\n  try {\n    // This should throw.\n    if (construct) {\n      // Something should be setting the props in the constructor.\n      var Fake = function () {\n        throw Error();\n      }; // $FlowFixMe\n\n\n      Object.defineProperty(Fake.prototype, 'props', {\n        set: function () {\n          // We use a throwing setter instead of frozen or non-writable props\n          // because that won't throw in a non-strict mode function.\n          throw Error();\n        }\n      });\n\n      if (typeof Reflect === 'object' && Reflect.construct) {\n        // We construct a different control for this case to include any extra\n        // frames added by the construct call.\n        try {\n          Reflect.construct(Fake, []);\n        } catch (x) {\n          control = x;\n        }\n\n        Reflect.construct(fn, [], Fake);\n      } else {\n        try {\n          Fake.call();\n        } catch (x) {\n          control = x;\n        }\n\n        fn.call(Fake.prototype);\n      }\n    } else {\n      try {\n        throw Error();\n      } catch (x) {\n        control = x;\n      }\n\n      fn();\n    }\n  } catch (sample) {\n    // This is inlined manually because closure doesn't do it for us.\n    if (sample && control && typeof sample.stack === 'string') {\n      // This extracts the first frame from the sample that isn't also in the control.\n      // Skipping one frame that we assume is the frame that calls the two.\n      var sampleLines = sample.stack.split('\\n');\n      var controlLines = control.stack.split('\\n');\n      var s = sampleLines.length - 1;\n      var c = controlLines.length - 1;\n\n      while (s >= 1 && c >= 0 && sampleLines[s] !== controlLines[c]) {\n        // We expect at least one stack frame to be shared.\n        // Typically this will be the root most one. However, stack frames may be\n        // cut off due to maximum stack limits. In this case, one maybe cut off\n        // earlier than the other. We assume that the sample is longer or the same\n        // and there for cut off earlier. So we should find the root most frame in\n        // the sample somewhere in the control.\n        c--;\n      }\n\n      for (; s >= 1 && c >= 0; s--, c--) {\n        // Next we find the first one that isn't the same which should be the\n        // frame that called our sample function and the control.\n        if (sampleLines[s] !== controlLines[c]) {\n          // In V8, the first line is describing the message but other VMs don't.\n          // If we're about to return the first line, and the control is also on the same\n          // line, that's a pretty good indicator that our sample threw at same line as\n          // the control. I.e. before we entered the sample frame. So we ignore this result.\n          // This can happen if you passed a class to function component, or non-function.\n          if (s !== 1 || c !== 1) {\n            do {\n              s--;\n              c--; // We may still have similar intermediate frames from the construct call.\n              // The next one that isn't the same should be our match though.\n\n              if (c < 0 || sampleLines[s] !== controlLines[c]) {\n                // V8 adds a \"new\" prefix for native classes. Let's remove it to make it prettier.\n                var _frame = '\\n' + sampleLines[s].replace(' at new ', ' at '); // If our component frame is labeled \"<anonymous>\"\n                // but we have a user-provided \"displayName\"\n                // splice it in to make the stack more readable.\n\n\n                if (fn.displayName && _frame.includes('<anonymous>')) {\n                  _frame = _frame.replace('<anonymous>', fn.displayName);\n                }\n\n                {\n                  if (typeof fn === 'function') {\n                    componentFrameCache.set(fn, _frame);\n                  }\n                } // Return the line we found.\n\n\n                return _frame;\n              }\n            } while (s >= 1 && c >= 0);\n          }\n\n          break;\n        }\n      }\n    }\n  } finally {\n    reentry = false;\n\n    {\n      ReactCurrentDispatcher.current = previousDispatcher;\n      reenableLogs();\n    }\n\n    Error.prepareStackTrace = previousPrepareStackTrace;\n  } // Fallback to just using the name if we couldn't make it throw.\n\n\n  var name = fn ? fn.displayName || fn.name : '';\n  var syntheticFrame = name ? describeBuiltInComponentFrame(name) : '';\n\n  {\n    if (typeof fn === 'function') {\n      componentFrameCache.set(fn, syntheticFrame);\n    }\n  }\n\n  return syntheticFrame;\n}\nfunction describeFunctionComponentFrame(fn, source, ownerFn) {\n  {\n    return describeNativeComponentFrame(fn, false);\n  }\n}\n\nfunction shouldConstruct(Component) {\n  var prototype = Component.prototype;\n  return !!(prototype && prototype.isReactComponent);\n}\n\nfunction describeUnknownElementTypeFrameInDEV(type, source, ownerFn) {\n\n  if (type == null) {\n    return '';\n  }\n\n  if (typeof type === 'function') {\n    {\n      return describeNativeComponentFrame(type, shouldConstruct(type));\n    }\n  }\n\n  if (typeof type === 'string') {\n    return describeBuiltInComponentFrame(type);\n  }\n\n  switch (type) {\n    case REACT_SUSPENSE_TYPE:\n      return describeBuiltInComponentFrame('Suspense');\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return describeBuiltInComponentFrame('SuspenseList');\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_FORWARD_REF_TYPE:\n        return describeFunctionComponentFrame(type.render);\n\n      case REACT_MEMO_TYPE:\n        // Memo may contain any component type so we recursively resolve it.\n        return describeUnknownElementTypeFrameInDEV(type.type, source, ownerFn);\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            // Lazy may contain any component type so we recursively resolve it.\n            return describeUnknownElementTypeFrameInDEV(init(payload), source, ownerFn);\n          } catch (x) {}\n        }\n    }\n  }\n\n  return '';\n}\n\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\nvar loggedTypeFailures = {};\nvar ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n\nfunction setCurrentlyValidatingElement(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n      ReactDebugCurrentFrame.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame.setExtraStackFrame(null);\n    }\n  }\n}\n\nfunction checkPropTypes(typeSpecs, values, location, componentName, element) {\n  {\n    // $FlowFixMe This is okay but Flow doesn't know it.\n    var has = Function.call.bind(hasOwnProperty);\n\n    for (var typeSpecName in typeSpecs) {\n      if (has(typeSpecs, typeSpecName)) {\n        var error$1 = void 0; // Prop type validation may throw. In case they do, we don't want to\n        // fail the render phase where it didn't fail before. So we log it.\n        // After these have been cleaned up, we'll let them throw.\n\n        try {\n          // This is intentionally an invariant that gets caught. It's the same\n          // behavior as without this statement except with a better message.\n          if (typeof typeSpecs[typeSpecName] !== 'function') {\n            // eslint-disable-next-line react-internal/prod-error-codes\n            var err = Error((componentName || 'React class') + ': ' + location + ' type `' + typeSpecName + '` is invalid; ' + 'it must be a function, usually from the `prop-types` package, but received `' + typeof typeSpecs[typeSpecName] + '`.' + 'This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.');\n            err.name = 'Invariant Violation';\n            throw err;\n          }\n\n          error$1 = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED');\n        } catch (ex) {\n          error$1 = ex;\n        }\n\n        if (error$1 && !(error$1 instanceof Error)) {\n          setCurrentlyValidatingElement(element);\n\n          error('%s: type specification of %s' + ' `%s` is invalid; the type checker ' + 'function must return `null` or an `Error` but returned a %s. ' + 'You may have forgotten to pass an argument to the type checker ' + 'creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and ' + 'shape all require an argument).', componentName || 'React class', location, typeSpecName, typeof error$1);\n\n          setCurrentlyValidatingElement(null);\n        }\n\n        if (error$1 instanceof Error && !(error$1.message in loggedTypeFailures)) {\n          // Only monitor this failure once because there tends to be a lot of the\n          // same error.\n          loggedTypeFailures[error$1.message] = true;\n          setCurrentlyValidatingElement(element);\n\n          error('Failed %s type: %s', location, error$1.message);\n\n          setCurrentlyValidatingElement(null);\n        }\n      }\n    }\n  }\n}\n\nvar isArrayImpl = Array.isArray; // eslint-disable-next-line no-redeclare\n\nfunction isArray(a) {\n  return isArrayImpl(a);\n}\n\n/*\n * The `'' + value` pattern (used in in perf-sensitive code) throws for Symbol\n * and Temporal.* types. See https://github.com/facebook/react/pull/22064.\n *\n * The functions in this module will throw an easier-to-understand,\n * easier-to-debug exception with a clear errors message message explaining the\n * problem. (Instead of a confusing exception thrown inside the implementation\n * of the `value` object).\n */\n// $FlowFixMe only called in DEV, so void return is not possible.\nfunction typeName(value) {\n  {\n    // toStringTag is needed for namespaced types like Temporal.Instant\n    var hasToStringTag = typeof Symbol === 'function' && Symbol.toStringTag;\n    var type = hasToStringTag && value[Symbol.toStringTag] || value.constructor.name || 'Object';\n    return type;\n  }\n} // $FlowFixMe only called in DEV, so void return is not possible.\n\n\nfunction willCoercionThrow(value) {\n  {\n    try {\n      testStringCoercion(value);\n      return false;\n    } catch (e) {\n      return true;\n    }\n  }\n}\n\nfunction testStringCoercion(value) {\n  // If you ended up here by following an exception call stack, here's what's\n  // happened: you supplied an object or symbol value to React (as a prop, key,\n  // DOM attribute, CSS property, string ref, etc.) and when React tried to\n  // coerce it to a string using `'' + value`, an exception was thrown.\n  //\n  // The most common types that will cause this exception are `Symbol` instances\n  // and Temporal objects like `Temporal.Instant`. But any object that has a\n  // `valueOf` or `[Symbol.toPrimitive]` method that throws will also cause this\n  // exception. (Library authors do this to prevent users from using built-in\n  // numeric operators like `+` or comparison operators like `>=` because custom\n  // methods are needed to perform accurate arithmetic or comparison.)\n  //\n  // To fix the problem, coerce this object or symbol value to a string before\n  // passing it to React. The most reliable way is usually `String(value)`.\n  //\n  // To find which value is throwing, check the browser or debugger console.\n  // Before this exception was thrown, there should be `console.error` output\n  // that shows the type (Symbol, Temporal.PlainDate, etc.) that caused the\n  // problem and how that type was used: key, atrribute, input value prop, etc.\n  // In most cases, this console output also shows the component and its\n  // ancestor components where the exception happened.\n  //\n  // eslint-disable-next-line react-internal/safe-string-coercion\n  return '' + value;\n}\nfunction checkKeyStringCoercion(value) {\n  {\n    if (willCoercionThrow(value)) {\n      error('The provided key is an unsupported type %s.' + ' This value must be coerced to a string before before using it here.', typeName(value));\n\n      return testStringCoercion(value); // throw (to help callers find troubleshooting comments)\n    }\n  }\n}\n\nvar ReactCurrentOwner = ReactSharedInternals.ReactCurrentOwner;\nvar RESERVED_PROPS = {\n  key: true,\n  ref: true,\n  __self: true,\n  __source: true\n};\nvar specialPropKeyWarningShown;\nvar specialPropRefWarningShown;\nvar didWarnAboutStringRefs;\n\n{\n  didWarnAboutStringRefs = {};\n}\n\nfunction hasValidRef(config) {\n  {\n    if (hasOwnProperty.call(config, 'ref')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'ref').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.ref !== undefined;\n}\n\nfunction hasValidKey(config) {\n  {\n    if (hasOwnProperty.call(config, 'key')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'key').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.key !== undefined;\n}\n\nfunction warnIfStringRefCannotBeAutoConverted(config, self) {\n  {\n    if (typeof config.ref === 'string' && ReactCurrentOwner.current && self && ReactCurrentOwner.current.stateNode !== self) {\n      var componentName = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n      if (!didWarnAboutStringRefs[componentName]) {\n        error('Component \"%s\" contains the string ref \"%s\". ' + 'Support for string refs will be removed in a future major release. ' + 'This case cannot be automatically converted to an arrow function. ' + 'We ask you to manually fix this case by using useRef() or createRef() instead. ' + 'Learn more about using refs safely here: ' + 'https://reactjs.org/link/strict-mode-string-ref', getComponentNameFromType(ReactCurrentOwner.current.type), config.ref);\n\n        didWarnAboutStringRefs[componentName] = true;\n      }\n    }\n  }\n}\n\nfunction defineKeyPropWarningGetter(props, displayName) {\n  {\n    var warnAboutAccessingKey = function () {\n      if (!specialPropKeyWarningShown) {\n        specialPropKeyWarningShown = true;\n\n        error('%s: `key` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    };\n\n    warnAboutAccessingKey.isReactWarning = true;\n    Object.defineProperty(props, 'key', {\n      get: warnAboutAccessingKey,\n      configurable: true\n    });\n  }\n}\n\nfunction defineRefPropWarningGetter(props, displayName) {\n  {\n    var warnAboutAccessingRef = function () {\n      if (!specialPropRefWarningShown) {\n        specialPropRefWarningShown = true;\n\n        error('%s: `ref` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    };\n\n    warnAboutAccessingRef.isReactWarning = true;\n    Object.defineProperty(props, 'ref', {\n      get: warnAboutAccessingRef,\n      configurable: true\n    });\n  }\n}\n/**\n * Factory method to create a new React element. This no longer adheres to\n * the class pattern, so do not use new to call it. Also, instanceof check\n * will not work. Instead test $$typeof field against Symbol.for('react.element') to check\n * if something is a React Element.\n *\n * @param {*} type\n * @param {*} props\n * @param {*} key\n * @param {string|object} ref\n * @param {*} owner\n * @param {*} self A *temporary* helper to detect places where `this` is\n * different from the `owner` when React.createElement is called, so that we\n * can warn. We want to get rid of owner and replace string `ref`s with arrow\n * functions, and as long as `this` and owner are the same, there will be no\n * change in behavior.\n * @param {*} source An annotation object (added by a transpiler or otherwise)\n * indicating filename, line number, and/or other information.\n * @internal\n */\n\n\nvar ReactElement = function (type, key, ref, self, source, owner, props) {\n  var element = {\n    // This tag allows us to uniquely identify this as a React Element\n    $$typeof: REACT_ELEMENT_TYPE,\n    // Built-in properties that belong on the element\n    type: type,\n    key: key,\n    ref: ref,\n    props: props,\n    // Record the component responsible for creating this element.\n    _owner: owner\n  };\n\n  {\n    // The validation flag is currently mutative. We put it on\n    // an external backing store so that we can freeze the whole object.\n    // This can be replaced with a WeakMap once they are implemented in\n    // commonly used development environments.\n    element._store = {}; // To make comparing ReactElements easier for testing purposes, we make\n    // the validation flag non-enumerable (where possible, which should\n    // include every environment we run tests in), so the test framework\n    // ignores it.\n\n    Object.defineProperty(element._store, 'validated', {\n      configurable: false,\n      enumerable: false,\n      writable: true,\n      value: false\n    }); // self and source are DEV only properties.\n\n    Object.defineProperty(element, '_self', {\n      configurable: false,\n      enumerable: false,\n      writable: false,\n      value: self\n    }); // Two elements created in two different places should be considered\n    // equal for testing purposes and therefore we hide it from enumeration.\n\n    Object.defineProperty(element, '_source', {\n      configurable: false,\n      enumerable: false,\n      writable: false,\n      value: source\n    });\n\n    if (Object.freeze) {\n      Object.freeze(element.props);\n      Object.freeze(element);\n    }\n  }\n\n  return element;\n};\n/**\n * https://github.com/reactjs/rfcs/pull/107\n * @param {*} type\n * @param {object} props\n * @param {string} key\n */\n\nfunction jsxDEV(type, config, maybeKey, source, self) {\n  {\n    var propName; // Reserved names are extracted\n\n    var props = {};\n    var key = null;\n    var ref = null; // Currently, key can be spread in as a prop. This causes a potential\n    // issue if key is also explicitly declared (ie. <div {...props} key=\"Hi\" />\n    // or <div key=\"Hi\" {...props} /> ). We want to deprecate key spread,\n    // but as an intermediary step, we will use jsxDEV for everything except\n    // <div {...props} key=\"Hi\" />, because we aren't currently able to tell if\n    // key is explicitly declared to be undefined or not.\n\n    if (maybeKey !== undefined) {\n      {\n        checkKeyStringCoercion(maybeKey);\n      }\n\n      key = '' + maybeKey;\n    }\n\n    if (hasValidKey(config)) {\n      {\n        checkKeyStringCoercion(config.key);\n      }\n\n      key = '' + config.key;\n    }\n\n    if (hasValidRef(config)) {\n      ref = config.ref;\n      warnIfStringRefCannotBeAutoConverted(config, self);\n    } // Remaining properties are added to a new props object\n\n\n    for (propName in config) {\n      if (hasOwnProperty.call(config, propName) && !RESERVED_PROPS.hasOwnProperty(propName)) {\n        props[propName] = config[propName];\n      }\n    } // Resolve default props\n\n\n    if (type && type.defaultProps) {\n      var defaultProps = type.defaultProps;\n\n      for (propName in defaultProps) {\n        if (props[propName] === undefined) {\n          props[propName] = defaultProps[propName];\n        }\n      }\n    }\n\n    if (key || ref) {\n      var displayName = typeof type === 'function' ? type.displayName || type.name || 'Unknown' : type;\n\n      if (key) {\n        defineKeyPropWarningGetter(props, displayName);\n      }\n\n      if (ref) {\n        defineRefPropWarningGetter(props, displayName);\n      }\n    }\n\n    return ReactElement(type, key, ref, self, source, ReactCurrentOwner.current, props);\n  }\n}\n\nvar ReactCurrentOwner$1 = ReactSharedInternals.ReactCurrentOwner;\nvar ReactDebugCurrentFrame$1 = ReactSharedInternals.ReactDebugCurrentFrame;\n\nfunction setCurrentlyValidatingElement$1(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n      ReactDebugCurrentFrame$1.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame$1.setExtraStackFrame(null);\n    }\n  }\n}\n\nvar propTypesMisspellWarningShown;\n\n{\n  propTypesMisspellWarningShown = false;\n}\n/**\n * Verifies the object is a ReactElement.\n * See https://reactjs.org/docs/react-api.html#isvalidelement\n * @param {?object} object\n * @return {boolean} True if `object` is a ReactElement.\n * @final\n */\n\n\nfunction isValidElement(object) {\n  {\n    return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n  }\n}\n\nfunction getDeclarationErrorAddendum() {\n  {\n    if (ReactCurrentOwner$1.current) {\n      var name = getComponentNameFromType(ReactCurrentOwner$1.current.type);\n\n      if (name) {\n        return '\\n\\nCheck the render method of `' + name + '`.';\n      }\n    }\n\n    return '';\n  }\n}\n\nfunction getSourceInfoErrorAddendum(source) {\n  {\n    if (source !== undefined) {\n      var fileName = source.fileName.replace(/^.*[\\\\\\/]/, '');\n      var lineNumber = source.lineNumber;\n      return '\\n\\nCheck your code at ' + fileName + ':' + lineNumber + '.';\n    }\n\n    return '';\n  }\n}\n/**\n * Warn if there's no key explicitly set on dynamic arrays of children or\n * object keys are not valid. This allows us to keep track of children between\n * updates.\n */\n\n\nvar ownerHasKeyUseWarning = {};\n\nfunction getCurrentComponentErrorInfo(parentType) {\n  {\n    var info = getDeclarationErrorAddendum();\n\n    if (!info) {\n      var parentName = typeof parentType === 'string' ? parentType : parentType.displayName || parentType.name;\n\n      if (parentName) {\n        info = \"\\n\\nCheck the top-level render call using <\" + parentName + \">.\";\n      }\n    }\n\n    return info;\n  }\n}\n/**\n * Warn if the element doesn't have an explicit key assigned to it.\n * This element is in an array. The array could grow and shrink or be\n * reordered. All children that haven't already been validated are required to\n * have a \"key\" property assigned to it. Error statuses are cached so a warning\n * will only be shown once.\n *\n * @internal\n * @param {ReactElement} element Element that requires a key.\n * @param {*} parentType element's parent's type.\n */\n\n\nfunction validateExplicitKey(element, parentType) {\n  {\n    if (!element._store || element._store.validated || element.key != null) {\n      return;\n    }\n\n    element._store.validated = true;\n    var currentComponentErrorInfo = getCurrentComponentErrorInfo(parentType);\n\n    if (ownerHasKeyUseWarning[currentComponentErrorInfo]) {\n      return;\n    }\n\n    ownerHasKeyUseWarning[currentComponentErrorInfo] = true; // Usually the current owner is the offender, but if it accepts children as a\n    // property, it may be the creator of the child that's responsible for\n    // assigning it a key.\n\n    var childOwner = '';\n\n    if (element && element._owner && element._owner !== ReactCurrentOwner$1.current) {\n      // Give the component that originally created this child.\n      childOwner = \" It was passed a child from \" + getComponentNameFromType(element._owner.type) + \".\";\n    }\n\n    setCurrentlyValidatingElement$1(element);\n\n    error('Each child in a list should have a unique \"key\" prop.' + '%s%s See https://reactjs.org/link/warning-keys for more information.', currentComponentErrorInfo, childOwner);\n\n    setCurrentlyValidatingElement$1(null);\n  }\n}\n/**\n * Ensure that every element either is passed in a static location, in an\n * array with an explicit keys property defined, or in an object literal\n * with valid key property.\n *\n * @internal\n * @param {ReactNode} node Statically passed child of any type.\n * @param {*} parentType node's parent's type.\n */\n\n\nfunction validateChildKeys(node, parentType) {\n  {\n    if (typeof node !== 'object') {\n      return;\n    }\n\n    if (isArray(node)) {\n      for (var i = 0; i < node.length; i++) {\n        var child = node[i];\n\n        if (isValidElement(child)) {\n          validateExplicitKey(child, parentType);\n        }\n      }\n    } else if (isValidElement(node)) {\n      // This element was passed in a valid location.\n      if (node._store) {\n        node._store.validated = true;\n      }\n    } else if (node) {\n      var iteratorFn = getIteratorFn(node);\n\n      if (typeof iteratorFn === 'function') {\n        // Entry iterators used to provide implicit keys,\n        // but now we print a separate warning for them later.\n        if (iteratorFn !== node.entries) {\n          var iterator = iteratorFn.call(node);\n          var step;\n\n          while (!(step = iterator.next()).done) {\n            if (isValidElement(step.value)) {\n              validateExplicitKey(step.value, parentType);\n            }\n          }\n        }\n      }\n    }\n  }\n}\n/**\n * Given an element, validate that its props follow the propTypes definition,\n * provided by the type.\n *\n * @param {ReactElement} element\n */\n\n\nfunction validatePropTypes(element) {\n  {\n    var type = element.type;\n\n    if (type === null || type === undefined || typeof type === 'string') {\n      return;\n    }\n\n    var propTypes;\n\n    if (typeof type === 'function') {\n      propTypes = type.propTypes;\n    } else if (typeof type === 'object' && (type.$$typeof === REACT_FORWARD_REF_TYPE || // Note: Memo only checks outer props here.\n    // Inner props are checked in the reconciler.\n    type.$$typeof === REACT_MEMO_TYPE)) {\n      propTypes = type.propTypes;\n    } else {\n      return;\n    }\n\n    if (propTypes) {\n      // Intentionally inside to avoid triggering lazy initializers:\n      var name = getComponentNameFromType(type);\n      checkPropTypes(propTypes, element.props, 'prop', name, element);\n    } else if (type.PropTypes !== undefined && !propTypesMisspellWarningShown) {\n      propTypesMisspellWarningShown = true; // Intentionally inside to avoid triggering lazy initializers:\n\n      var _name = getComponentNameFromType(type);\n\n      error('Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?', _name || 'Unknown');\n    }\n\n    if (typeof type.getDefaultProps === 'function' && !type.getDefaultProps.isReactClassApproved) {\n      error('getDefaultProps is only used on classic React.createClass ' + 'definitions. Use a static property named `defaultProps` instead.');\n    }\n  }\n}\n/**\n * Given a fragment, validate that it can only be provided with fragment props\n * @param {ReactElement} fragment\n */\n\n\nfunction validateFragmentProps(fragment) {\n  {\n    var keys = Object.keys(fragment.props);\n\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i];\n\n      if (key !== 'children' && key !== 'key') {\n        setCurrentlyValidatingElement$1(fragment);\n\n        error('Invalid prop `%s` supplied to `React.Fragment`. ' + 'React.Fragment can only have `key` and `children` props.', key);\n\n        setCurrentlyValidatingElement$1(null);\n        break;\n      }\n    }\n\n    if (fragment.ref !== null) {\n      setCurrentlyValidatingElement$1(fragment);\n\n      error('Invalid attribute `ref` supplied to `React.Fragment`.');\n\n      setCurrentlyValidatingElement$1(null);\n    }\n  }\n}\n\nvar didWarnAboutKeySpread = {};\nfunction jsxWithValidation(type, props, key, isStaticChildren, source, self) {\n  {\n    var validType = isValidElementType(type); // We warn in this case but don't throw. We expect the element creation to\n    // succeed and there will likely be errors in render.\n\n    if (!validType) {\n      var info = '';\n\n      if (type === undefined || typeof type === 'object' && type !== null && Object.keys(type).length === 0) {\n        info += ' You likely forgot to export your component from the file ' + \"it's defined in, or you might have mixed up default and named imports.\";\n      }\n\n      var sourceInfo = getSourceInfoErrorAddendum(source);\n\n      if (sourceInfo) {\n        info += sourceInfo;\n      } else {\n        info += getDeclarationErrorAddendum();\n      }\n\n      var typeString;\n\n      if (type === null) {\n        typeString = 'null';\n      } else if (isArray(type)) {\n        typeString = 'array';\n      } else if (type !== undefined && type.$$typeof === REACT_ELEMENT_TYPE) {\n        typeString = \"<\" + (getComponentNameFromType(type.type) || 'Unknown') + \" />\";\n        info = ' Did you accidentally export a JSX literal instead of a component?';\n      } else {\n        typeString = typeof type;\n      }\n\n      error('React.jsx: type is invalid -- expected a string (for ' + 'built-in components) or a class/function (for composite ' + 'components) but got: %s.%s', typeString, info);\n    }\n\n    var element = jsxDEV(type, props, key, source, self); // The result can be nullish if a mock or a custom function is used.\n    // TODO: Drop this when these are no longer allowed as the type argument.\n\n    if (element == null) {\n      return element;\n    } // Skip key warning if the type isn't valid since our key validation logic\n    // doesn't expect a non-string/function type and can throw confusing errors.\n    // We don't want exception behavior to differ between dev and prod.\n    // (Rendering will throw with a helpful message and as soon as the type is\n    // fixed, the key warnings will appear.)\n\n\n    if (validType) {\n      var children = props.children;\n\n      if (children !== undefined) {\n        if (isStaticChildren) {\n          if (isArray(children)) {\n            for (var i = 0; i < children.length; i++) {\n              validateChildKeys(children[i], type);\n            }\n\n            if (Object.freeze) {\n              Object.freeze(children);\n            }\n          } else {\n            error('React.jsx: Static children should always be an array. ' + 'You are likely explicitly calling React.jsxs or React.jsxDEV. ' + 'Use the Babel transform instead.');\n          }\n        } else {\n          validateChildKeys(children, type);\n        }\n      }\n    }\n\n    {\n      if (hasOwnProperty.call(props, 'key')) {\n        var componentName = getComponentNameFromType(type);\n        var keys = Object.keys(props).filter(function (k) {\n          return k !== 'key';\n        });\n        var beforeExample = keys.length > 0 ? '{key: someKey, ' + keys.join(': ..., ') + ': ...}' : '{key: someKey}';\n\n        if (!didWarnAboutKeySpread[componentName + beforeExample]) {\n          var afterExample = keys.length > 0 ? '{' + keys.join(': ..., ') + ': ...}' : '{}';\n\n          error('A props object containing a \"key\" prop is being spread into JSX:\\n' + '  let props = %s;\\n' + '  <%s {...props} />\\n' + 'React keys must be passed directly to JSX without using spread:\\n' + '  let props = %s;\\n' + '  <%s key={someKey} {...props} />', beforeExample, componentName, afterExample, componentName);\n\n          didWarnAboutKeySpread[componentName + beforeExample] = true;\n        }\n      }\n    }\n\n    if (type === REACT_FRAGMENT_TYPE) {\n      validateFragmentProps(element);\n    } else {\n      validatePropTypes(element);\n    }\n\n    return element;\n  }\n} // These two functions exist to still get child warnings in dev\n// even with the prod transform. This means that jsxDEV is purely\n// opt-in behavior for better messages but that we won't stop\n// giving you warnings if you use production apis.\n\nfunction jsxWithValidationStatic(type, props, key) {\n  {\n    return jsxWithValidation(type, props, key, true);\n  }\n}\nfunction jsxWithValidationDynamic(type, props, key) {\n  {\n    return jsxWithValidation(type, props, key, false);\n  }\n}\n\nvar jsx =  jsxWithValidationDynamic ; // we may want to special case jsxs internally to take advantage of static children.\n// for now we can ship identical prod functions\n\nvar jsxs =  jsxWithValidationStatic ;\n\nexports.Fragment = REACT_FRAGMENT_TYPE;\nexports.jsx = jsx;\nexports.jsxs = jsxs;\n  })();\n}\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-runtime.production.min.js');\n} else {\n  module.exports = require('./cjs/react-jsx-runtime.development.js');\n}\n", "module.exports = window[\"React\"];", "module.exports = window[\"ReactDOM\"];", "module.exports = window[\"jQuery\"];", "module.exports = window[\"wp\"][\"i18n\"];", "// src/styled.ts\nimport validAttr from \"@emotion/is-prop-valid\";\nimport React from \"react\";\nimport { cx } from \"@linaria/core\";\nvar isCapital = (ch) => ch.toUpperCase() === ch;\nvar filterKey = (keys) => (key) => keys.indexOf(key) === -1;\nvar omit = (obj, keys) => {\n  const res = {};\n  Object.keys(obj).filter(filterKey(keys)).forEach((key) => {\n    res[key] = obj[key];\n  });\n  return res;\n};\nfunction filterProps(asIs, props, omitKeys) {\n  const filteredProps = omit(props, omitKeys);\n  if (!asIs) {\n    const interopValidAttr = typeof validAttr === \"function\" ? { default: validAttr } : validAttr;\n    Object.keys(filteredProps).forEach((key) => {\n      if (!interopValidAttr.default(key)) {\n        delete filteredProps[key];\n      }\n    });\n  }\n  return filteredProps;\n}\nvar warnIfInvalid = (value, componentName) => {\n  if (process.env.NODE_ENV !== \"production\") {\n    if (typeof value === \"string\" || typeof value === \"number\" && isFinite(value)) {\n      return;\n    }\n    const stringified = typeof value === \"object\" ? JSON.stringify(value) : String(value);\n    console.warn(\n      `An interpolation evaluated to '${stringified}' in the component '${componentName}', which is probably a mistake. You should explicitly cast or transform the value to a string.`\n    );\n  }\n};\nvar idx = 0;\nfunction styled(tag) {\n  var _a;\n  let mockedClass = \"\";\n  if (process.env.NODE_ENV === \"test\") {\n    mockedClass += `mocked-styled-${idx++}`;\n    if ((_a = tag == null ? void 0 : tag.__linaria) == null ? void 0 : _a.className) {\n      mockedClass += ` ${tag.__linaria.className}`;\n    }\n  }\n  return (options) => {\n    if (process.env.NODE_ENV !== \"production\" && process.env.NODE_ENV !== \"test\") {\n      if (Array.isArray(options)) {\n        throw new Error(\n          'Using the \"styled\" tag in runtime is not supported. Make sure you have set up the Babel plugin correctly. See https://github.com/callstack/linaria#setup'\n        );\n      }\n    }\n    const render = (props, ref) => {\n      const { as: component = tag, class: className = mockedClass } = props;\n      const shouldKeepProps = options.propsAsIs === void 0 ? !(typeof component === \"string\" && component.indexOf(\"-\") === -1 && !isCapital(component[0])) : options.propsAsIs;\n      const filteredProps = filterProps(shouldKeepProps, props, [\n        \"as\",\n        \"class\"\n      ]);\n      filteredProps.ref = ref;\n      filteredProps.className = options.atomic ? cx(options.class, filteredProps.className || className) : cx(filteredProps.className || className, options.class);\n      const { vars } = options;\n      if (vars) {\n        const style = {};\n        for (const name in vars) {\n          const variable = vars[name];\n          const result = variable[0];\n          const unit = variable[1] || \"\";\n          const value = typeof result === \"function\" ? result(props) : result;\n          warnIfInvalid(value, options.name);\n          style[`--${name}`] = `${value}${unit}`;\n        }\n        const ownStyle = filteredProps.style || {};\n        const keys = Object.keys(ownStyle);\n        if (keys.length > 0) {\n          keys.forEach((key) => {\n            style[key] = ownStyle[key];\n          });\n        }\n        filteredProps.style = style;\n      }\n      if (tag.__linaria && tag !== component) {\n        filteredProps.as = component;\n        return React.createElement(tag, filteredProps);\n      }\n      return React.createElement(component, filteredProps);\n    };\n    const Result = React.forwardRef ? React.forwardRef(render) : (props) => {\n      const rest = omit(props, [\"innerRef\"]);\n      return render(rest, props.innerRef);\n    };\n    Result.displayName = options.name;\n    Result.__linaria = {\n      className: options.class || mockedClass,\n      extends: tag\n    };\n    return Result;\n  };\n}\nvar styled_default = process.env.NODE_ENV !== \"production\" ? new Proxy(styled, {\n  get(o, prop) {\n    return o(prop);\n  }\n}) : styled;\nexport {\n  styled_default as styled\n};\n//# sourceMappingURL=index.mjs.map", "// src/css.ts\nvar idx = 0;\nvar css = () => {\n  if (process.env.NODE_ENV === \"test\") {\n    return `mocked-css-${idx++}`;\n  }\n  throw new Error(\n    'Using the \"css\" tag in runtime is not supported. Make sure you have set up the Babel plugin correctly.'\n  );\n};\nvar css_default = css;\n\n// src/cx.ts\nvar cx = function cx2() {\n  const presentClassNames = Array.prototype.slice.call(arguments).filter(Boolean);\n  const atomicClasses = {};\n  const nonAtomicClasses = [];\n  presentClassNames.forEach((arg) => {\n    const individualClassNames = arg ? arg.split(\" \") : [];\n    individualClassNames.forEach((className) => {\n      if (className.startsWith(\"atm_\")) {\n        const [, keyHash] = className.split(\"_\");\n        atomicClasses[keyHash] = className;\n      } else {\n        nonAtomicClasses.push(className);\n      }\n    });\n  });\n  const result = [];\n  for (const keyHash in atomicClasses) {\n    if (Object.prototype.hasOwnProperty.call(atomicClasses, keyHash)) {\n      result.push(atomicClasses[keyHash]);\n    }\n  }\n  result.push(...nonAtomicClasses);\n  return result.join(\" \");\n};\nvar cx_default = cx;\nexport {\n  css_default as css,\n  cx_default as cx\n};\n//# sourceMappingURL=index.mjs.map", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "import elementorWidget from '../elementor/elementorWidget';\nimport registerFormWidget from '../elementor/FormWidget/registerFormWidget';\nimport { initBackgroundApp } from '../utils/backgroundAppUtils';\nimport registerMeetingsWidget from '../elementor/MeetingWidget/registerMeetingWidget';\nconst ELEMENTOR_READY_INTERVAL = 500;\nconst MAX_POLL_TIMEOUT = 30000;\nconst registerElementorWidgets = () => {\n    initBackgroundApp(() => {\n        let FormWidget;\n        let MeetingsWidget;\n        const leadinSelectFormItemView = elementorWidget(\n        //@ts-expect-error global\n        window.elementor, {\n            widgetName: 'hubspot-form',\n            controlSelector: '.elementor-hbspt-form-selector',\n            containerSelector: '.hubspot-form-edit-mode',\n        }, (controlContainer, widgetContainer, setValue) => {\n            FormWidget = new registerFormWidget(controlContainer, widgetContainer, setValue);\n            FormWidget.render();\n        }, () => {\n            FormWidget.done();\n        });\n        const leadinSelectMeetingItemView = elementorWidget(\n        //@ts-expect-error global\n        window.elementor, {\n            widgetName: 'hubspot-meeting',\n            controlSelector: '.elementor-hbspt-meeting-selector',\n            containerSelector: '.hubspot-meeting-edit-mode',\n        }, (controlContainer, widgetContainer, setValue) => {\n            MeetingsWidget = new registerMeetingsWidget(controlContainer, widgetContainer, setValue);\n            MeetingsWidget.render();\n        }, () => {\n            MeetingsWidget.done();\n        });\n        //@ts-expect-error global\n        window.elementor.addControlView('leadinformselect', leadinSelectFormItemView);\n        //@ts-expect-error global\n        window.elementor.addControlView('leadinmeetingselect', leadinSelectMeetingItemView);\n    });\n};\nconst pollForElementorReady = setInterval(() => {\n    const elementorFrontend = window.elementorFrontend;\n    if (elementorFrontend) {\n        registerElementorWidgets();\n        clearInterval(pollForElementorReady);\n    }\n}, ELEMENTOR_READY_INTERVAL);\nsetTimeout(() => {\n    clearInterval(pollForElementorReady);\n}, MAX_POLL_TIMEOUT);\n"], "names": ["__", "BLANK_FORM", "NEWSLETTER_FORM", "CONTACT_US_FORM", "EVENT_REGISTRATION_FORM", "TALK_TO_AN_EXPERT_FORM", "BOOK_A_MEETING_FORM", "GATED_CONTENT_FORM", "DEFAULT_OPTIONS", "label", "options", "value", "isDefaultForm", "_window$leadinConfig", "window", "leadinConfig", "accountName", "adminUrl", "activationTime", "connectionStatus", "deviceId", "didDisconnect", "env", "formsScript", "meetingsScript", "formsScriptPayload", "hublet", "hubspotBaseUrl", "hubspotNonce", "iframeUrl", "impactLink", "lastAuthorizeTime", "lastDeauthorizeTime", "lastDisconnectTime", "leadinPluginVersion", "leadinQueryParams", "locale", "loginUrl", "phpVersion", "pluginPath", "plugins", "portalDomain", "portalEmail", "portalId", "redirectNonce", "restNonce", "restUrl", "refreshToken", "reviewSkippedDate", "theme", "trackConsent", "wpVersion", "contentEmbed", "requiresContentEmbedScope", "decryptError", "jsx", "_jsx", "ElementorBanner", "ConnectPluginBanner", "children", "dangerouslySetInnerHTML", "__html", "replace", "_ref", "_ref$type", "type", "className", "concat", "styled", "Container", "name", "class", "propsAsIs", "ElementorButton", "params", "_objectSpread", "jsxs", "_jsxs", "Fragment", "U<PERSON><PERSON><PERSON>", "BackgroudAppContext", "useBackgroundAppContext", "useForms", "getOrCreateBackgroundApp", "isRefreshTokenAvailable", "ElementorFormSelect", "formId", "setAttributes", "_useForms", "<PERSON><PERSON><PERSON><PERSON>", "forms", "loading", "onChange", "event", "selectedForm", "find", "form", "target", "formName", "embedVersion", "disabled", "selected", "map", "ElementorFormSelectWrapper", "props", "isBackgroundAppReady", "ElementorFormSelectContainer", "Provider", "ConnectionStatus", "Connected", "NotConnected", "FormControlController", "attributes", "setValue", "render", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FormEdit", "FormWidgetController", "isSelected", "preview", "origin", "status", "useState", "useEffect", "LoadState", "ProxyMessages", "usePostAsyncBackgroundMessage", "proxy", "_useState", "NotLoaded", "_useState2", "_slicedToArray", "loadState", "setLoadState", "_useState3", "_useState4", "setError", "_useState5", "_useState6", "setForms", "key", "FetchForms", "payload", "search", "then", "data", "guid", "Loaded", "error", "Failed", "Loading", "ReactDOM", "registerFormWidget", "controlContainer", "widgetContainer", "_classCallCheck", "_defineProperty", "dataset", "JSON", "parse", "_createClass", "done", "unmountComponentAtNode", "default", "ElementorMeetingWarning", "useMeetings", "useSelectedMeetingCalendar", "Raven", "ElementorMeetingSelect", "url", "_useMeetings", "meetings", "mappedMeetings", "reload", "connectCalendar", "selected<PERSON><PERSON>tingCalendar", "localUrl", "setLocalUrl", "handleConnectCalendar", "captureMessage", "extra", "onConnectCalendar", "length", "newUrl", "item", "ElementorMeetingSelectWrapper", "ElementorMeetingsSelectContainer", "CURRENT_USER_CALENDAR_MISSING", "MeetingWarning", "is<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "titleText", "titleMessage", "id", "onClick", "MeetingControlController", "MeetingsEdit", "MeetingWidgetController", "registerMeetingsWidget", "elementorWidget", "elementor", "callback", "arguments", "undefined", "modules", "controls", "BaseData", "extend", "onReady", "self", "ui", "contentEditable", "prevObject", "querySelector", "controlSelector", "element", "$el", "containerSelector", "args", "elementorFrontend", "hooks", "addAction", "widgetName", "saveValue", "onBeforeDestroy", "removeAction", "CoreMessages", "Handshak<PERSON><PERSON><PERSON><PERSON><PERSON>", "SendRefreshToken", "ReloadParentFrame", "RedirectParentFrame", "SendLocale", "SendDeviceId", "SendIntegratedAppConfig", "FormMessages", "CreateFormAppNavigation", "LiveChatMessages", "CreateLiveChatAppNavigation", "PluginMessages", "PluginSettingsNavigation", "PluginLeadinConfig", "TrackConsent", "InternalTrackingFetchRequest", "InternalTrackingFetchResponse", "InternalTrackingFetchError", "InternalTrackingChangeRequest", "InternalTrackingChangeError", "BusinessUnitFetchRequest", "BusinessUnitFetchResponse", "BusinessUnitFetchError", "BusinessUnitChangeRequest", "BusinessUnitChangeError", "SkipReviewRequest", "SkipReviewResponse", "SkipReviewError", "RemoveParentQueryParam", "ContentEmbedInstallRequest", "ContentEmbedInstallResponse", "ContentEmbedInstallError", "ContentEmbedActivationRequest", "ContentEmbedActivationResponse", "ContentEmbedActivationError", "ProxyMappingsEnabledRequest", "ProxyMappingsEnabledResponse", "ProxyMappingsEnabledError", "ProxyMappingsEnabledChangeRequest", "ProxyMappingsEnabledChangeError", "RefreshProxyMappingsRequest", "RefreshProxyMappingsResponse", "RefreshProxyMappingsError", "FetchForm", "CreateFormFromTemplate", "GetTemplateAvailability", "FetchAuth", "FetchMeetingsAndUsers", "FetchContactsCreateSinceActivation", "FetchOrCreateMeetingUser", "ConnectMeetingsCalendar", "TrackFormPreviewRender", "TrackFormCreatedFromTemplate", "TrackFormCreationFailed", "TrackMeetingPreviewRender", "TrackSidebarMetaChange", "TrackReviewBannerRender", "TrackReviewBannerInteraction", "TrackReviewBannerDismissed", "TrackPluginDeactivation", "createContext", "useContext", "usePostBackgroundMessage", "app", "message", "postMessage", "postAsyncMessage", "configureRaven", "indexOf", "domain", "config", "instrument", "tryCatch", "shouldSendCallback", "culprit", "test", "release", "install", "setTagsContext", "v", "php", "wordpress", "setExtraContext", "hub", "Object", "keys", "join", "useRef", "CALYPSO_LIGHT", "CALYPSO_MEDIUM", "_exp2", "focused", "_exp3", "ControlContainer", "vars", "ValueContainer", "Placeholder", "SingleValue", "IndicatorC<PERSON><PERSON>", "DropdownIndicator", "InputContainer", "Input", "InputShadow", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MenuList", "MenuGroup", "MenuGroupHeader", "_exp5", "_exp6", "_exp7", "MenuItem", "AsyncSelect", "placeholder", "loadOptions", "defaultOptions", "inputEl", "inputShadowEl", "isFocused", "setFocus", "localValue", "setLocalValue", "_useState7", "_useState8", "setOptions", "inputSize", "current", "clientWidth", "result", "Idle", "renderItems", "items", "parent<PERSON><PERSON>", "index", "blur", "focus", "ref", "onFocus", "e", "width", "UIButton", "UIContainer", "HubspotWrapper", "redirectToPlugin", "location", "href", "resetErrorState", "_ref$errorInfo", "errorInfo", "header", "action", "isUnauthorized", "errorHeader", "errorMessage", "textAlign", "_exp", "padding", "LoadingBlock", "size", "PreviewDisabled", "UISpacer", "PreviewForm", "FormSelect", "_ref$preview", "_ref$origin", "fullSiteEditor", "formSelected", "monitorFormPreviewRender", "handleChange", "FormEditContainer", "FormSelector", "useCreateFormFromTemplate", "formApiError", "reset", "_useCreateFormFromTem", "createFormByTemplate", "createReset", "isCreating", "createApiError", "handleLocalChange", "option", "_ref2", "UIOverlay", "region", "isFormV4", "hbspt", "parent", "innerHTML", "isQa", "includes", "container", "document", "createElement", "classList", "add", "toString", "append<PERSON><PERSON><PERSON>", "additionalParams", "create", "track", "setFormApiError", "err", "debounce", "useGetTemplateAvailability", "getTemplateOptions", "_useGetTemplateAvaila", "availabilityPromise", "Promise", "all", "templateAvailabilityResponse", "TEMPLATE_OPTIONS", "templateAvailability", "_toConsumableArray", "trailing", "Template<PERSON><PERSON><PERSON>", "Template<PERSON><PERSON><PERSON>", "ExcludedTemplateAvailabilityKeys", "setTemplateAvailability", "resolve", "filter", "templateId", "hubspotFormTemplateAvailability", "canCreateWithMissingScopes", "missingScopes", "values", "MeetingSelector", "useSelectedMeeting", "MeetingController", "selectedMeetingOption", "PreviewMeeting", "MeetingEdit", "MeetingsEditContainer", "optionsWrapper", "U<PERSON><PERSON>t", "use", "OTHER_USER_CALENDAR_MISSING", "user", "useCurrentUserFetch", "createUser", "loadUserState", "useCallback", "useMeetingsFetch", "getDefaultMeetingName", "meeting", "currentUser", "meetingUsers", "_meeting$meetingsUser", "meetingsUserIds", "meetingOwnerId", "userProfile", "fullName", "hasCalendarObject", "meetingsUserBlob", "calendarSettings", "email", "_useMeetingsFetch", "meetingsError", "loadMeetingsState", "reloadMeetings", "_useCurrentUserFetch", "userError", "reloadUser", "meet", "link", "_useMeetings2", "mappedMeetingUsersId", "reduce", "p", "c", "some", "meetingLinks", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Title", "Message", "MessageContainer", "HEFFALUMP", "LORAX", "CALYPSO", "SpinnerOuter", "SpinnerInner", "color", "Circle", "AnimatedCircle", "_ref$size", "height", "viewBox", "cx", "cy", "r", "OLAF", "MARIGOLD_LIGHT", "MARIGOLD_MEDIUM", "OBSIDIAN", "HubSpotFormTemplateAvailabilityKeys", "BLANK", "NEWSLETTER", "CONTACT_US", "EVENT_REGISTRATION", "TALK_TO_AN_EXPERT", "BOOK_A_MEETING", "GATED_CONTENT", "$", "initApp", "initFn", "context", "initAppOnReady", "main", "initBackgroundApp", "Array", "isArray", "for<PERSON>ach", "getLeadinConfig", "LeadinBackgroundApp", "_window", "IntegratedAppEmbedder", "IntegratedAppOptions", "setLocale", "setDeviceId", "setLeadinConfig", "setRefreshToken", "trim", "embedder", "attachTo", "body", "postStartAppMessage", "ELEMENTOR_READY_INTERVAL", "MAX_POLL_TIMEOUT", "registerElementorWidgets", "FormWidget", "MeetingsWidget", "leadinSelectFormItemView", "leadinSelectMeetingItemView", "addControlView", "pollForElementorReady", "setInterval", "clearInterval", "setTimeout"], "sourceRoot": ""}