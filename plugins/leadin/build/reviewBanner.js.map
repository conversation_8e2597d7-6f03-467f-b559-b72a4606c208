{"version": 3, "file": "reviewBanner.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,oBAAA,GAAwiBC,MAAM,CAACC,YAAY;EAAnjBC,WAAW,GAAAH,oBAAA,CAAXG,WAAW;EAAEC,QAAQ,GAAAJ,oBAAA,CAARI,QAAQ;EAAEC,cAAc,GAAAL,oBAAA,CAAdK,cAAc;EAAEC,gBAAgB,GAAAN,oBAAA,CAAhBM,gBAAgB;EAAEC,QAAQ,GAAAP,oBAAA,CAARO,QAAQ;EAAEC,aAAa,GAAAR,oBAAA,CAAbQ,aAAa;EAAEC,GAAG,GAAAT,oBAAA,CAAHS,GAAG;EAAEC,WAAW,GAAAV,oBAAA,CAAXU,WAAW;EAAEC,cAAc,GAAAX,oBAAA,CAAdW,cAAc;EAAEC,kBAAkB,GAAAZ,oBAAA,CAAlBY,kBAAkB;EAAEC,MAAM,GAAAb,oBAAA,CAANa,MAAM;EAAEC,cAAc,GAAAd,oBAAA,CAAdc,cAAc;EAAEC,YAAY,GAAAf,oBAAA,CAAZe,YAAY;EAAEC,SAAS,GAAAhB,oBAAA,CAATgB,SAAS;EAAEC,UAAU,GAAAjB,oBAAA,CAAViB,UAAU;EAAEC,iBAAiB,GAAAlB,oBAAA,CAAjBkB,iBAAiB;EAAEC,mBAAmB,GAAAnB,oBAAA,CAAnBmB,mBAAmB;EAAEC,kBAAkB,GAAApB,oBAAA,CAAlBoB,kBAAkB;EAAEC,mBAAmB,GAAArB,oBAAA,CAAnBqB,mBAAmB;EAAEC,iBAAiB,GAAAtB,oBAAA,CAAjBsB,iBAAiB;EAAEC,MAAM,GAAAvB,oBAAA,CAANuB,MAAM;EAAEC,QAAQ,GAAAxB,oBAAA,CAARwB,QAAQ;EAAEC,UAAU,GAAAzB,oBAAA,CAAVyB,UAAU;EAAEC,UAAU,GAAA1B,oBAAA,CAAV0B,UAAU;EAAEC,OAAO,GAAA3B,oBAAA,CAAP2B,OAAO;EAAEC,YAAY,GAAA5B,oBAAA,CAAZ4B,YAAY;EAAEC,WAAW,GAAA7B,oBAAA,CAAX6B,WAAW;EAAEC,QAAQ,GAAA9B,oBAAA,CAAR8B,QAAQ;EAAEC,aAAa,GAAA/B,oBAAA,CAAb+B,aAAa;EAAEC,SAAS,GAAAhC,oBAAA,CAATgC,SAAS;EAAEC,OAAO,GAAAjC,oBAAA,CAAPiC,OAAO;EAAEC,YAAY,GAAAlC,oBAAA,CAAZkC,YAAY;EAAEC,iBAAiB,GAAAnC,oBAAA,CAAjBmC,iBAAiB;EAAEC,KAAK,GAAApC,oBAAA,CAALoC,KAAK;EAAEC,YAAY,GAAArC,oBAAA,CAAZqC,YAAY;EAAEC,SAAS,GAAAtC,oBAAA,CAATsC,SAAS;EAAEC,YAAY,GAAAvC,oBAAA,CAAZuC,YAAY;EAAEC,yBAAyB,GAAAxC,oBAAA,CAAzBwC,yBAAyB;EAAEC,YAAY,GAAAzC,oBAAA,CAAZyC,YAAY;;;;;;;;;;;;;;;;ACA3hB,IAAMC,WAAW,GAAG;EACvBC,MAAM,EAAE,gBAAgB;EACxBC,OAAO,EAAE,4BAA4B;EACrCC,YAAY,EAAE,8BAA8B;EAC5CC,cAAc,EAAE,iCAAiC;EACjDC,sBAAsB,EAAE,oCAAoC;EAC5DC,sBAAsB,EAAE,6BAA6B;EACrDC,wBAAwB,EAAE,+BAA+B;EACzDC,sBAAsB,EAAE,6BAA6B;EACrDC,kBAAkB,EAAE,qBAAqB;EACzCC,mBAAmB,EAAE,gCAAgC;EACrDC,oBAAoB,EAAE,6BAA6B;EACnDC,qBAAqB,EAAE,uBAAuB;EAC9CC,2BAA2B,EAAE,uBAAuB;EACpDC,yBAAyB,EAAE,gCAAgC;EAC3DC,qBAAqB,EAAE,yBAAyB;EAChDC,YAAY,EAAE,eAAe;EAC7BC,6BAA6B,EAAE;AACnC,CAAC;;;;;;;;;;;;;;;AClBM,IAAMC,YAAY,GAAG;EACxBC,gBAAgB,EAAE,4CAA4C;EAC9DC,gBAAgB,EAAE,4CAA4C;EAC9DC,iBAAiB,EAAE,6CAA6C;EAChEC,mBAAmB,EAAE,+CAA+C;EACpEC,UAAU,EAAE,qCAAqC;EACjDC,YAAY,EAAE,wCAAwC;EACtDC,uBAAuB,EAAE;AAC7B,CAAC;;;;;;;;;;;;;;;ACRM,IAAMC,YAAY,GAAG;EACxBC,uBAAuB,EAAE;AAC7B,CAAC;;;;;;;;;;;;;;;;;;;;;;;;ACFmC;AACE;AACM;AACJ;;;;;;;;;;;;;;;;ACHjC,IAAMC,gBAAgB,GAAG;EAC5BC,2BAA2B,EAAE;AACjC,CAAC;;;;;;;;;;;;;;;ACFM,IAAMC,cAAc,GAAG;EAC1BC,wBAAwB,EAAE,4BAA4B;EACtDC,kBAAkB,EAAE,sBAAsB;EAC1CC,YAAY,EAAE,uCAAuC;EACrDC,4BAA4B,EAAE,mCAAmC;EACjEC,6BAA6B,EAAE,oCAAoC;EACnEC,0BAA0B,EAAE,iCAAiC;EAC7DC,6BAA6B,EAAE,oCAAoC;EACnEC,2BAA2B,EAAE,kCAAkC;EAC/DC,wBAAwB,EAAE,6BAA6B;EACvDC,yBAAyB,EAAE,oCAAoC;EAC/DC,sBAAsB,EAAE,iCAAiC;EACzDC,yBAAyB,EAAE,8BAA8B;EACzDC,uBAAuB,EAAE,4BAA4B;EACrDC,iBAAiB,EAAE,qBAAqB;EACxCC,kBAAkB,EAAE,sBAAsB;EAC1CC,eAAe,EAAE,mBAAmB;EACpCC,sBAAsB,EAAE,2BAA2B;EACnDC,0BAA0B,EAAE,+BAA+B;EAC3DC,2BAA2B,EAAE,gCAAgC;EAC7DC,wBAAwB,EAAE,6BAA6B;EACvDC,6BAA6B,EAAE,kCAAkC;EACjEC,8BAA8B,EAAE,mCAAmC;EACnEC,2BAA2B,EAAE,gCAAgC;EAC7DC,2BAA2B,EAAE,gCAAgC;EAC7DC,4BAA4B,EAAE,iCAAiC;EAC/DC,yBAAyB,EAAE,8BAA8B;EACzDC,iCAAiC,EAAE,uCAAuC;EAC1EC,+BAA+B,EAAE,qCAAqC;EACtEC,2BAA2B,EAAE,gCAAgC;EAC7DC,4BAA4B,EAAE,iCAAiC;EAC/DC,yBAAyB,EAAE;AAC/B,CAAC;;;;;;;;;;;;;;;AChCM,IAAMC,aAAa,GAAG;EACzBC,UAAU,EAAE,aAAa;EACzBC,SAAS,EAAE,YAAY;EACvBC,sBAAsB,EAAE,2BAA2B;EACnDC,uBAAuB,EAAE,2BAA2B;EACpDC,SAAS,EAAE,YAAY;EACvBC,qBAAqB,EAAE,0BAA0B;EACjDC,kCAAkC,EAAE,yCAAyC;EAC7EC,wBAAwB,EAAE,8BAA8B;EACxDC,uBAAuB,EAAE,2BAA2B;EACpDC,sBAAsB,EAAE,2BAA2B;EACnDC,4BAA4B,EAAE,kCAAkC;EAChEC,uBAAuB,EAAE,4BAA4B;EACrDC,yBAAyB,EAAE,8BAA8B;EACzDC,sBAAsB,EAAE,2BAA2B;EACnDC,uBAAuB,EAAE,4BAA4B;EACrDC,4BAA4B,EAAE,iCAAiC;EAC/DC,0BAA0B,EAAE,+BAA+B;EAC3DC,uBAAuB,EAAE;AAC7B,CAAC;;;;;;;;;;;;;;;;;;;ACnB4B;AAC8F;AACpH,SAASE,cAAcA,CAAA,EAAG;EAC7B,IAAI9G,2EAAsB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;IACxC;EACJ;EACA,IAAMgH,MAAM,GAAGhH,2EAAsB,CAAC,gBAAgB,EAAE,EAAE,CAAC;EAC3D6G,sDAAY,uDAAAM,MAAA,CAAuDH,MAAM,YAAS;IAC9EI,UAAU,EAAE;MACRC,QAAQ,EAAE;IACd,CAAC;IACDC,kBAAkB,WAAlBA,kBAAkBA,CAACC,IAAI,EAAE;MACrB,OAAQ,CAAC,CAACA,IAAI,IAAI,CAAC,CAACA,IAAI,CAACC,OAAO,IAAI,mBAAmB,CAACC,IAAI,CAACF,IAAI,CAACC,OAAO,CAAC;IAC9E,CAAC;IACDE,OAAO,EAAEnH,wEAAmBA;EAChC,CAAC,CAAC,CAACoH,OAAO,CAAC,CAAC;EACZd,8DAAoB,CAAC;IACjBgB,CAAC,EAAEtH,wEAAmB;IACtBuH,GAAG,EAAEnH,+DAAU;IACfoH,SAAS,EAAEvG,8DAASA;EACxB,CAAC,CAAC;EACFqF,+DAAqB,CAAC;IAClBoB,GAAG,EAAEjH,6DAAQ;IACbH,OAAO,EAAEqH,MAAM,CAACC,IAAI,CAACtH,4DAAO,CAAC,CACxBuH,GAAG,CAAC,UAAAC,IAAI;MAAA,UAAAlB,MAAA,CAAOkB,IAAI,OAAAlB,MAAA,CAAItG,4DAAO,CAACwH,IAAI,CAAC;IAAA,CAAE,CAAC,CACvCC,IAAI,CAAC,GAAG;EACjB,CAAC,CAAC;AACN;AACA,iEAAezB,iDAAK;;;;;;;;;;;;;;;;;;;AC5BG;AAC8B;AAC9C,SAAS2B,OAAOA,CAACC,MAAM,EAAE;EAC5B3B,0DAAc,CAAC,CAAC;EAChBD,0DAAa,CAAC4B,MAAM,CAAC;AACzB;AACO,SAASE,cAAcA,CAACF,MAAM,EAAE;EACnC,SAASG,IAAIA,CAAA,EAAG;IACZL,6CAAC,CAACE,MAAM,CAAC;EACb;EACAD,OAAO,CAACI,IAAI,CAAC;AACjB;;;;;;;;;;;;;;;;;;ACX6G;AACxE;AAC9B,SAASC,iBAAiBA,CAACJ,MAAM,EAAE;EACtC,SAASG,IAAIA,CAAA,EAAG;IACZ,IAAIE,KAAK,CAACC,OAAO,CAACN,MAAM,CAAC,EAAE;MACvBA,MAAM,CAACO,OAAO,CAAC,UAAAC,QAAQ;QAAA,OAAIA,QAAQ,CAAC,CAAC;MAAA,EAAC;IAC1C,CAAC,MACI;MACDR,MAAM,CAAC,CAAC;IACZ;EACJ;EACAD,kDAAO,CAACI,IAAI,CAAC;AACjB;AACA,IAAMM,eAAe,GAAG,SAAlBA,eAAeA,CAAA,EAAS;EAC1B,OAAO;IACH3I,mBAAmB,EAAnBA,wEAAmBA;EACvB,CAAC;AACL,CAAC;AACM,IAAM4I,wBAAwB,GAAG,SAA3BA,wBAAwBA,CAAA,EAA0B;EAAA,IAAtB/H,YAAY,GAAAgI,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;EACtD,IAAIjK,MAAM,CAACoK,mBAAmB,EAAE;IAC5B,OAAOpK,MAAM,CAACoK,mBAAmB;EACrC;EACA,IAAAC,OAAA,GAAwDrK,MAAM;IAAtDsK,qBAAqB,GAAAD,OAAA,CAArBC,qBAAqB;IAAEC,oBAAoB,GAAAF,OAAA,CAApBE,oBAAoB;EACnD,IAAMC,OAAO,GAAG,IAAID,oBAAoB,CAAC,CAAC,CACrCE,SAAS,CAACnJ,2DAAM,CAAC,CACjBoJ,WAAW,CAACpK,6DAAQ,CAAC,CACrBqK,eAAe,CAACZ,eAAe,CAAC,CAAC,CAAC,CAClCa,eAAe,CAAC3I,YAAY,CAAC4I,IAAI,CAAC,CAAC,CAAC;EACzC,IAAMC,QAAQ,GAAG,IAAIR,qBAAqB,CAAC,yBAAyB,EAAEzI,6DAAQ,EAAEhB,mEAAc,EAAE,YAAM,CAAE,CAAC,CAAC,CAACkK,UAAU,CAACP,OAAO,CAAC;EAC9HM,QAAQ,CAACE,QAAQ,CAACC,QAAQ,CAACC,IAAI,EAAE,KAAK,CAAC;EACvCJ,QAAQ,CAACK,mBAAmB,CAAC,CAAC,CAAC,CAAC;EAChCnL,MAAM,CAACoK,mBAAmB,GAAGU,QAAQ;EACrC,OAAO9K,MAAM,CAACoK,mBAAmB;AACrC,CAAC;;;;;;;;;;ACjCD;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;;;;;;;;ACPA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA,gBAAgB,+CAA+C;;AAE/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;;;;;;;;;ACtCA;;AAEA,eAAe,mBAAO,CAAC,wFAA6B;AACpD,gBAAgB,mBAAO,CAAC,gHAAyC;AACjE,uBAAuB,mBAAO,CAAC,iEAAe;;AAE9C,YAAY,mBAAO,CAAC,qDAAS;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,wBAAwB,2FAA+B;;AAEvD;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa,qBAAM,mBAAmB,qBAAM;AAC5C;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,UAAU;AACV;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA,eAAe,QAAQ;AACvB,eAAe,QAAQ;AACvB,gBAAgB;AAChB;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,OAAO;AACP;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;;AAEP;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,UAAU;AACV;AACA,MAAM;AACN;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,UAAU;AACV;AACA,MAAM;AACN;AACA;AACA;;AAEA;;AAEA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA,eAAe,QAAQ;AACvB;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA,eAAe,QAAQ;AACvB,eAAe,UAAU;AACzB,eAAe,OAAO;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA,eAAe,QAAQ;AACvB,eAAe,UAAU;AACzB,eAAe,UAAU;AACzB,gBAAgB,UAAU;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA,eAAe,OAAO;AACtB,eAAe,QAAQ;AACvB,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA,eAAe,QAAQ;AACvB,eAAe,QAAQ;AACvB,gBAAgB;AAChB;AACA;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,OAAO;AACP;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;;AAEA;AACA;;AAEA;AACA;AACA,QAAQ;AACR;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA,eAAe,QAAQ;AACvB,gBAAgB;AAChB;AACA;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA,eAAe,QAAQ;AACvB,gBAAgB;AAChB;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA,eAAe,QAAQ;AACvB,gBAAgB;AAChB;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA,gBAAgB,QAAQ;AACxB;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA,eAAe,QAAQ;AACvB,gBAAgB;AAChB;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA,eAAe,QAAQ;AACvB,gBAAgB;AAChB;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA,eAAe,UAAU;AACzB;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA,eAAe,UAAU;AACzB;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA,eAAe,UAAU;AACzB;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA,eAAe,UAAU;AACzB;AACA;AACA,gBAAgB;AAChB;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA,sCAAsC;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,GAAG;;AAEH;AACA;AACA;;AAEA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iCAAiC;AACjC;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;;AAEA;AACA;AACA;AACA,OAAO;AACP;AACA,GAAG;;AAEH;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,+BAA+B;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,iBAAiB;AACzC;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,yBAAyB;AAC7C;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,SAAS,GAAG;AACZ;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,SAAS;AACT;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;;AAEA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;;AAEA;AACA,4BAA4B,kBAAkB;AAC9C;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA,cAAc;AACd;AACA;AACA;AACA;;AAEA;AACA;AACA,SAAS;AACT;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,iBAAiB;AAC7C;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa;;AAEb;AACA;;AAEA;AACA,aAAa;AACb;AACA,SAAS;AACT;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,+CAA+C;AAC/C;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA,OAAO;AACP;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,GAAG;;AAEH;AACA;AACA,cAAc;AACd;;AAEA;AACA;AACA,MAAM;AACN;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;;AAEP;AACA;AACA,wBAAwB,iDAAiD;AACzE;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,0CAA0C;AAC1C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA,OAAO;AACP;AACA;;AAEA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,oBAAoB,+BAA+B;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,2BAA2B;AAC3B,sBAAsB,qBAAqB;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,0CAA0C;AAC1C,MAAM;AACN;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA;AACA;AACA;AACA,OAAO;AACP;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA,0CAA0C;AAC1C,2CAA2C;;AAE3C;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA,GAAG;;AAEH;AACA;AACA,GAAG;;AAEH;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,SAAS;AACT;AACA,OAAO;AACP;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,KAAK;AACL,GAAG;;AAEH;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA,MAAM;AACN,2EAA2E;AAC3E;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;;;;;;;;;;ACr4DA;AACA;AACA;AACA;AACA;;AAEA,uBAAuB,mBAAO,CAAC,qDAAS;;AAExC;AACA;AACA;AACA;AACA,aAAa,qBAAM,mBAAmB,qBAAM;AAC5C;;AAEA;;AAEA;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;;;;;;;;;;AC9BA;AACA;AACA;AACA,aAAa,qBAAM,mBAAmB,qBAAM;;AAE5C;AACA;AACA;;AAEA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,mCAAmC;AACnC;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,oCAAoC;AACpC;AACA;;AAEA;AACA;AACA,wBAAwB;AACxB;AACA,IAAI;AACJ;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA,kBAAkB,OAAO;AACzB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAS,SAAS;AAClB;AACA;AACA;AACA;AACA,iDAAiD;AACjD,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,gBAAgB,oBAAoB;AACpC;AACA;AACA;AACA;AACA,cAAc,0BAA0B;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA,kCAAkC;AAClC;AACA,kBAAkB,oBAAoB;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,iBAAiB,UAAU;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AChYA,YAAY,mBAAO,CAAC,6DAAiB;;AAErC;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa,qBAAM,mBAAmB,qBAAM;;AAE5C;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,qDAAqD,KAAK;AAC1D,uDAAuD,KAAK;AAC5D;AACA,WAAW,aAAa,YAAY;AACpC;AACA;AACA;AACA,8BAA8B;AAC9B;AACA;AACA,+DAA+D;AAC/D;AACA,+DAA+D;AAC/D;AACA;AACA;AACA;AACA;AACA,oCAAoC;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,eAAe,UAAU;AACzB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,eAAe,UAAU;AACzB;AACA;AACA,sCAAsC,QAAQ;AAC9C;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,eAAe,oBAAoB;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,eAAe,QAAQ;AACvB,eAAe,QAAQ;AACvB,eAAe,iBAAiB;AAChC;AACA,eAAe,kBAAkB;AACjC;AACA,eAAe,QAAQ;AACvB;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN,8BAA8B;;AAE9B;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;;AAEA;AACA,yBAAyB;AACzB;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,eAAe,OAAO;AACtB,eAAe,UAAU;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB,QAAQ;AACR;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA,gBAAgB;AAChB;AACA;;AAEA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B;;AAE1B;AACA;AACA;AACA,eAAe,OAAO;AACtB,gBAAgB,qBAAqB;AACrC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,sCAAsC,OAAO;AAC7C;AACA,qEAAqE;AACrE,iEAAiE;AACjE;AACA;AACA,kCAAkC;AAClC,kCAAkC;AAClC,kCAAkC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA,2BAA2B;AAC3B,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,eAAe,oBAAoB;AACnC;AACA,eAAe,QAAQ;AACvB,eAAe,iBAAiB;AAChC;AACA,eAAe,SAAS;AACxB;AACA,gBAAgB,SAAS;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,0BAA0B;AAC1B,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,MAAM;AACN;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,QAAQ,0CAA0C;AAClD,eAAe,OAAO;AACtB,gBAAgB,qBAAqB;AACrC;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,QAAQ;AACR;AACA;;AAEA;AACA;AACA,qEAAqE;AACrE,UAAU;AACV;;AAEA;AACA;AACA,QAAQ;AACR;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,eAAe,OAAO;AACtB,eAAe,kBAAkB;AACjC;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,CAAC;;AAED;;;;;;;;;;;AC9mBA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,oBAAoB;;AAEpB;AACA,kBAAkB,qBAAqB;AACvC;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,MAAM;AACN;AACA;;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACzEA;;;;;;UCAA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;WCtBA;WACA;WACA;WACA;WACA;WACA,iCAAiC,WAAW;WAC5C;WACA;;;;;WCPA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA;;;;;WCPA;WACA;WACA;WACA;WACA,GAAG;WACH;WACA;WACA,CAAC;;;;;WCPD;;;;;WCAA;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D;;;;;;;;;;;;;;;;;;;;;ACNuB;AACoE;AACtC;AACoB;AACZ;AAC7D,IAAMgB,+BAA+B,GAAG,EAAE;AAC1C,IAAMC,6BAA6B,GAAG,SAAhCA,6BAA6BA,CAAA,EAAS;EACxC,IAAMC,cAAc,GAAG,IAAIC,IAAI,CAAC,CAACnL,mEAAc,GAAG,IAAI,CAAC;EACvD,IAAMoL,WAAW,GAAG,IAAID,IAAI,CAAC,CAAC;EAC9B,IAAME,WAAW,GAAG,IAAIF,IAAI,CAACC,WAAW,CAACE,OAAO,CAAC,CAAC,GAAGJ,cAAc,CAACI,OAAO,CAAC,CAAC,CAAC;EAC9E,OAAOD,WAAW,CAACE,UAAU,CAAC,CAAC,GAAG,CAAC,IAAIP,+BAA+B;AAC1E,CAAC;AACD;AACA;AACA;AACA;AACO,SAASQ,uBAAuBA,CAAA,EAAG;EACtC,IAAI3J,iEAAY,EAAE;IACd,IAAM6I,QAAQ,GAAGd,mFAAwB,CAAC/H,iEAAY,CAAC;IACvD,IAAM4J,SAAS,GAAGzC,6CAAC,CAAC3G,mFAAiC,CAAC;IACtD,IAAIoJ,SAAS,IAAIR,6BAA6B,CAAC,CAAC,EAAE;MAC9CjC,6CAAC,CAAC3G,yFAAuC,CAAC,CACrCqJ,GAAG,CAAC,OAAO,CAAC,CACZC,EAAE,CAAC,OAAO,EAAE,YAAM;QACnBjB,QAAQ,CAACkB,WAAW,CAAC;UACjBC,GAAG,EAAE1F,kGAA0CgB;QACnD,CAAC,CAAC;MACN,CAAC,CAAC;MACF6B,6CAAC,CAAC3G,uFAAqC,CAAC,CACnCqJ,GAAG,CAAC,OAAO,CAAC,CACZC,EAAE,CAAC,OAAO,EAAE,YAAM;QACnBjB,QAAQ,CAACkB,WAAW,CAAC;UACjBC,GAAG,EAAE1F,gGAAwCiB;QACjD,CAAC,CAAC;MACN,CAAC,CAAC;MACFsD,QAAQ,CACHoB,gBAAgB,CAAC;QAClBD,GAAG,EAAE1F,wGAAgD;QACrD4F,OAAO,EAAE,CAAC/L,mEAAc,GAAG;MAC/B,CAAC,CAAC,CACGgM,IAAI,CAAC,UAAAC,IAAA,EAAe;QAAA,IAAZC,KAAK,GAAAD,IAAA,CAALC,KAAK;QACd,IAAIA,KAAK,IAAI,CAAC,EAAE;UACZT,SAAS,CAACU,WAAW,CAAC,4BAA4B,CAAC;UACnDzB,QAAQ,CAACkB,WAAW,CAAC;YACjBC,GAAG,EAAE1F,6FAAqCe;UAC9C,CAAC,CAAC;QACN;MACJ,CAAC,CAAC;IACN;EACJ;AACJ;AACAoC,4EAAiB,CAACkC,uBAAuB,CAAC,C", "sources": ["webpack://leadin/./scripts/constants/leadinConfig.ts", "webpack://leadin/./scripts/constants/selectors.ts", "webpack://leadin/./scripts/iframe/integratedMessages/core/CoreMessages.ts", "webpack://leadin/./scripts/iframe/integratedMessages/forms/FormsMessages.ts", "webpack://leadin/./scripts/iframe/integratedMessages/index.ts", "webpack://leadin/./scripts/iframe/integratedMessages/livechat/LiveChatMessages.ts", "webpack://leadin/./scripts/iframe/integratedMessages/plugin/PluginMessages.ts", "webpack://leadin/./scripts/iframe/integratedMessages/proxy/ProxyMessages.ts", "webpack://leadin/./scripts/lib/Raven.ts", "webpack://leadin/./scripts/utils/appUtils.ts", "webpack://leadin/./scripts/utils/backgroundAppUtils.ts", "webpack://leadin/./node_modules/raven-js/src/configError.js", "webpack://leadin/./node_modules/raven-js/src/console.js", "webpack://leadin/./node_modules/raven-js/src/raven.js", "webpack://leadin/./node_modules/raven-js/src/singleton.js", "webpack://leadin/./node_modules/raven-js/src/utils.js", "webpack://leadin/./node_modules/raven-js/vendor/TraceKit/tracekit.js", "webpack://leadin/./node_modules/raven-js/vendor/json-stringify-safe/stringify.js", "webpack://leadin/external window \"jQuery\"", "webpack://leadin/webpack/bootstrap", "webpack://leadin/webpack/runtime/compat get default export", "webpack://leadin/webpack/runtime/define property getters", "webpack://leadin/webpack/runtime/global", "webpack://leadin/webpack/runtime/hasOwnProperty shorthand", "webpack://leadin/webpack/runtime/make namespace object", "webpack://leadin/./scripts/entries/reviewBanner.ts"], "sourcesContent": ["const { accountName, adminUrl, activationTime, connectionStatus, deviceId, didDisconnect, env, formsScript, meetingsScript, formsScriptPayload, hublet, hubspotBaseUrl, hubspotNonce, iframeUrl, impactLink, lastAuthorizeTime, lastDeauthorizeTime, lastDisconnectTime, leadinPluginVersion, leadinQueryParams, locale, loginUrl, phpVersion, pluginPath, plugins, portalDomain, portalEmail, portalId, redirectNonce, restNonce, restUrl, refreshToken, reviewSkippedDate, theme, trackConsent, wpVersion, contentEmbed, requiresContentEmbedScope, decryptError, } = window.leadinConfig;\nexport { accountName, adminUrl, activationTime, connectionStatus, deviceId, didDisconnect, env, formsScript, meetingsScript, formsScriptPayload, hublet, hubspotBaseUrl, hubspotNonce, iframeUrl, impactLink, lastAuthorizeTime, lastDeauthorizeTime, lastDisconnectTime, leadinPluginVersion, leadinQueryParams, loginUrl, locale, phpVersion, pluginPath, plugins, portalDomain, portalEmail, portalId, redirectNonce, restNonce, restUrl, refreshToken, reviewSkippedDate, theme, trackConsent, wpVersion, contentEmbed, requiresContentEmbedScope, decryptError, };\n", "export const domElements = {\n    iframe: '#leadin-iframe',\n    subMenu: '.toplevel_page_leadin > ul',\n    subMenuLinks: '.toplevel_page_leadin > ul a',\n    subMenuButtons: '.toplevel_page_leadin > ul > li',\n    deactivatePluginButton: '[data-slug=\"leadin\"] .deactivate a',\n    deactivateFeedbackForm: 'form.leadin-deactivate-form',\n    deactivateFeedbackSubmit: 'button#leadin-feedback-submit',\n    deactivateFeedbackSkip: 'button#leadin-feedback-skip',\n    thickboxModalClose: '.leadin-modal-close',\n    thickboxModalWindow: 'div#TB_window.thickbox-loading',\n    thickboxModalContent: 'div#TB_ajaxContent.TB_modal',\n    reviewBannerContainer: '#leadin-review-banner',\n    reviewBannerLeaveReviewLink: 'a#leave-review-button',\n    reviewBannerDismissButton: 'a#dismiss-review-banner-button',\n    leadinIframeContainer: 'leadin-iframe-container',\n    leadinIframe: 'leadin-iframe',\n    leadinIframeFallbackContainer: 'leadin-iframe-fallback-container',\n};\n", "export const CoreMessages = {\n    HandshakeReceive: 'INTEGRATED_APP_EMBEDDER_HANDSHAKE_RECEIVED',\n    SendRefreshToken: 'INTEGRATED_APP_EMBEDDER_SEND_REFRESH_TOKEN',\n    ReloadParentFrame: 'INTEGRATED_APP_EMBEDDER_RELOAD_PARENT_FRAME',\n    RedirectParentFrame: 'INTEGRATED_APP_EMBEDDER_REDIRECT_PARENT_FRAME',\n    SendLocale: 'INTEGRATED_APP_EMBEDDER_SEND_LOCALE',\n    SendDeviceId: 'INTEGRATED_APP_EMBEDDER_SEND_DEVICE_ID',\n    SendIntegratedAppConfig: 'INTEGRATED_APP_EMBEDDER_CONFIG',\n};\n", "export const FormMessages = {\n    CreateFormAppNavigation: 'CREATE_FORM_APP_NAVIGATION',\n};\n", "export * from './core/CoreMessages';\nexport * from './forms/FormsMessages';\nexport * from './livechat/LiveChatMessages';\nexport * from './plugin/PluginMessages';\nexport * from './proxy/ProxyMessages';\n", "export const LiveChatMessages = {\n    CreateLiveChatAppNavigation: 'CREATE_LIVE_CHAT_APP_NAVIGATION',\n};\n", "export const PluginMessages = {\n    PluginSettingsNavigation: 'PLUGIN_SETTINGS_NAVIGATION',\n    PluginLeadinConfig: 'PLUGIN_LEADIN_CONFIG',\n    TrackConsent: 'INTEGRATED_APP_EMBEDDER_TRACK_CONSENT',\n    InternalTrackingFetchRequest: 'INTEGRATED_TRACKING_FETCH_REQUEST',\n    InternalTrackingFetchResponse: 'INTEGRATED_TRACKING_FETCH_RESPONSE',\n    InternalTrackingFetchError: 'INTEGRATED_TRACKING_FETCH_ERROR',\n    InternalTrackingChangeRequest: 'INTEGRATED_TRACKING_CHANGE_REQUEST',\n    InternalTrackingChangeError: 'INTEGRATED_TRACKING_CHANGE_ERROR',\n    BusinessUnitFetchRequest: 'BUSINESS_UNIT_FETCH_REQUEST',\n    BusinessUnitFetchResponse: 'BUSINESS_UNIT_FETCH_FETCH_RESPONSE',\n    BusinessUnitFetchError: 'BUSINESS_UNIT_FETCH_FETCH_ERROR',\n    BusinessUnitChangeRequest: 'BUSINESS_UNIT_CHANGE_REQUEST',\n    BusinessUnitChangeError: 'BUSINESS_UNIT_CHANGE_ERROR',\n    SkipReviewRequest: 'SKIP_REVIEW_REQUEST',\n    SkipReviewResponse: 'SKIP_REVIEW_RESPONSE',\n    SkipReviewError: 'SKIP_REVIEW_ERROR',\n    RemoveParentQueryParam: 'REMOVE_PARENT_QUERY_PARAM',\n    ContentEmbedInstallRequest: 'CONTENT_EMBED_INSTALL_REQUEST',\n    ContentEmbedInstallResponse: 'CONTENT_EMBED_INSTALL_RESPONSE',\n    ContentEmbedInstallError: 'CONTENT_EMBED_INSTALL_ERROR',\n    ContentEmbedActivationRequest: 'CONTENT_EMBED_ACTIVATION_REQUEST',\n    ContentEmbedActivationResponse: 'CONTENT_EMBED_ACTIVATION_RESPONSE',\n    ContentEmbedActivationError: 'CONTENT_EMBED_ACTIVATION_ERROR',\n    ProxyMappingsEnabledRequest: 'PROXY_MAPPINGS_ENABLED_REQUEST',\n    ProxyMappingsEnabledResponse: 'PROXY_MAPPINGS_ENABLED_RESPONSE',\n    ProxyMappingsEnabledError: 'PROXY_MAPPINGS_ENABLED_ERROR',\n    ProxyMappingsEnabledChangeRequest: 'PROXY_MAPPINGS_ENABLED_CHANGE_REQUEST',\n    ProxyMappingsEnabledChangeError: 'PROXY_MAPPINGS_ENABLED_CHANGE_ERROR',\n    RefreshProxyMappingsRequest: 'REFRESH_PROXY_MAPPINGS_REQUEST',\n    RefreshProxyMappingsResponse: 'REFRESH_PROXY_MAPPINGS_RESPONSE',\n    RefreshProxyMappingsError: 'REFRESH_PROXY_MAPPINGS_ERROR',\n};\n", "export const ProxyMessages = {\n    FetchForms: 'FETCH_FORMS',\n    FetchForm: 'FETCH_FORM',\n    CreateFormFromTemplate: 'CREATE_FORM_FROM_TEMPLATE',\n    GetTemplateAvailability: 'GET_TEMPLATE_AVAILABILITY',\n    FetchAuth: 'FETCH_AUTH',\n    FetchMeetingsAndUsers: 'FETCH_MEETINGS_AND_USERS',\n    FetchContactsCreateSinceActivation: 'FETCH_CONTACTS_CREATED_SINCE_ACTIVATION',\n    FetchOrCreateMeetingUser: 'FETCH_OR_CREATE_MEETING_USER',\n    ConnectMeetingsCalendar: 'CONNECT_MEETINGS_CALENDAR',\n    TrackFormPreviewRender: 'TRACK_FORM_PREVIEW_RENDER',\n    TrackFormCreatedFromTemplate: 'TRACK_FORM_CREATED_FROM_TEMPLATE',\n    TrackFormCreationFailed: 'TRACK_FORM_CREATION_FAILED',\n    TrackMeetingPreviewRender: 'TRACK_MEETING_PREVIEW_RENDER',\n    TrackSidebarMetaChange: 'TRACK_SIDEBAR_META_CHANGE',\n    TrackReviewBannerRender: 'TRACK_REVIEW_BANNER_RENDER',\n    TrackReviewBannerInteraction: 'TRACK_REVIEW_BANNER_INTERACTION',\n    TrackReviewBannerDismissed: 'TRACK_REVIEW_BANNER_DISMISSED',\n    TrackPluginDeactivation: 'TRACK_PLUGIN_DEACTIVATION',\n};\n", "import Raven from 'raven-js';\nimport { hubspotBaseUrl, phpVersion, wpVersion, leadinPluginVersion, portalId, plugins, } from '../constants/leadinConfig';\nexport function configureRaven() {\n    if (hubspotBaseUrl.indexOf('local') !== -1) {\n        return;\n    }\n    const domain = hubspotBaseUrl.replace(/https?:\\/\\/app/, '');\n    Raven.config(`https://a9f08e536ef66abb0bf90becc905b09e@exceptions${domain}/v2/1`, {\n        instrument: {\n            tryCatch: false,\n        },\n        shouldSendCallback(data) {\n            return (!!data && !!data.culprit && /plugins\\/leadin\\//.test(data.culprit));\n        },\n        release: leadinPluginVersion,\n    }).install();\n    Raven.setTagsContext({\n        v: leadinPluginVersion,\n        php: phpVersion,\n        wordpress: wpVersion,\n    });\n    Raven.setExtraContext({\n        hub: portalId,\n        plugins: Object.keys(plugins)\n            .map(name => `${name}#${plugins[name]}`)\n            .join(','),\n    });\n}\nexport default Raven;\n", "import $ from 'j<PERSON>y';\nimport Raven, { configureRaven } from '../lib/Raven';\nexport function initApp(initFn) {\n    configureRaven();\n    Raven.context(initFn);\n}\nexport function initAppOnReady(initFn) {\n    function main() {\n        $(initFn);\n    }\n    initApp(main);\n}\n", "import { deviceId, hubspotBaseUrl, locale, portalId, leadinPluginVersion, } from '../constants/leadinConfig';\nimport { initApp } from './appUtils';\nexport function initBackgroundApp(initFn) {\n    function main() {\n        if (Array.isArray(initFn)) {\n            initFn.forEach(callback => callback());\n        }\n        else {\n            initFn();\n        }\n    }\n    initApp(main);\n}\nconst getLeadinConfig = () => {\n    return {\n        leadinPluginVersion,\n    };\n};\nexport const getOrCreateBackgroundApp = (refreshToken = '') => {\n    if (window.LeadinBackgroundApp) {\n        return window.LeadinBackgroundApp;\n    }\n    const { IntegratedAppEmbedder, IntegratedAppOptions } = window;\n    const options = new IntegratedAppOptions()\n        .setLocale(locale)\n        .setDeviceId(deviceId)\n        .setLeadinConfig(getLeadinConfig())\n        .setRefreshToken(refreshToken.trim());\n    const embedder = new IntegratedAppEmbedder('integrated-plugin-proxy', portalId, hubspotBaseUrl, () => { }).setOptions(options);\n    embedder.attachTo(document.body, false);\n    embedder.postStartAppMessage(); // lets the app know all all data has been passed to it\n    window.LeadinBackgroundApp = embedder;\n    return window.LeadinBackgroundApp;\n};\n", "function RavenConfigError(message) {\n  this.name = 'RavenConfigError';\n  this.message = message;\n}\nRavenConfigError.prototype = new Error();\nRavenConfigError.prototype.constructor = RavenConfigError;\n\nmodule.exports = RavenConfigError;\n", "var wrapMethod = function(console, level, callback) {\n  var originalConsoleLevel = console[level];\n  var originalConsole = console;\n\n  if (!(level in console)) {\n    return;\n  }\n\n  var sentryLevel = level === 'warn' ? 'warning' : level;\n\n  console[level] = function() {\n    var args = [].slice.call(arguments);\n\n    var msg = '' + args.join(' ');\n    var data = {level: sentryLevel, logger: 'console', extra: {arguments: args}};\n\n    if (level === 'assert') {\n      if (args[0] === false) {\n        // Default browsers message\n        msg = 'Assertion failed: ' + (args.slice(1).join(' ') || 'console.assert');\n        data.extra.arguments = args.slice(1);\n        callback && callback(msg, data);\n      }\n    } else {\n      callback && callback(msg, data);\n    }\n\n    // this fails for some browsers. :(\n    if (originalConsoleLevel) {\n      // IE9 doesn't allow calling apply on console functions directly\n      // See: https://stackoverflow.com/questions/5472938/does-ie9-support-console-log-and-is-it-a-real-function#answer-5473193\n      Function.prototype.apply.call(originalConsoleLevel, originalConsole, args);\n    }\n  };\n};\n\nmodule.exports = {\n  wrapMethod: wrapMethod\n};\n", "/*global XDomainRequest:false */\n\nvar TraceKit = require('../vendor/TraceKit/tracekit');\nvar stringify = require('../vendor/json-stringify-safe/stringify');\nvar RavenConfigError = require('./configError');\n\nvar utils = require('./utils');\nvar isError = utils.isError;\nvar isObject = utils.isObject;\nvar isObject = utils.isObject;\nvar isErrorEvent = utils.isErrorEvent;\nvar isUndefined = utils.isUndefined;\nvar isFunction = utils.isFunction;\nvar isString = utils.isString;\nvar isEmptyObject = utils.isEmptyObject;\nvar each = utils.each;\nvar objectMerge = utils.objectMerge;\nvar truncate = utils.truncate;\nvar objectFrozen = utils.objectFrozen;\nvar hasKey = utils.hasKey;\nvar joinRegExp = utils.joinRegExp;\nvar urlencode = utils.urlencode;\nvar uuid4 = utils.uuid4;\nvar htmlTreeAsString = utils.htmlTreeAsString;\nvar isSameException = utils.isSameException;\nvar isSameStacktrace = utils.isSameStacktrace;\nvar parseUrl = utils.parseUrl;\nvar fill = utils.fill;\n\nvar wrapConsoleMethod = require('./console').wrapMethod;\n\nvar dsnKeys = 'source protocol user pass host port path'.split(' '),\n  dsnPattern = /^(?:(\\w+):)?\\/\\/(?:(\\w+)(:\\w+)?@)?([\\w\\.-]+)(?::(\\d+))?(\\/.*)/;\n\nfunction now() {\n  return +new Date();\n}\n\n// This is to be defensive in environments where window does not exist (see https://github.com/getsentry/raven-js/pull/785)\nvar _window =\n  typeof window !== 'undefined'\n    ? window\n    : typeof global !== 'undefined' ? global : typeof self !== 'undefined' ? self : {};\nvar _document = _window.document;\nvar _navigator = _window.navigator;\n\nfunction keepOriginalCallback(original, callback) {\n  return isFunction(callback)\n    ? function(data) {\n        return callback(data, original);\n      }\n    : callback;\n}\n\n// First, check for JSON support\n// If there is no JSON, we no-op the core features of Raven\n// since JSON is required to encode the payload\nfunction Raven() {\n  this._hasJSON = !!(typeof JSON === 'object' && JSON.stringify);\n  // Raven can run in contexts where there's no document (react-native)\n  this._hasDocument = !isUndefined(_document);\n  this._hasNavigator = !isUndefined(_navigator);\n  this._lastCapturedException = null;\n  this._lastData = null;\n  this._lastEventId = null;\n  this._globalServer = null;\n  this._globalKey = null;\n  this._globalProject = null;\n  this._globalContext = {};\n  this._globalOptions = {\n    logger: 'javascript',\n    ignoreErrors: [],\n    ignoreUrls: [],\n    whitelistUrls: [],\n    includePaths: [],\n    collectWindowErrors: true,\n    maxMessageLength: 0,\n\n    // By default, truncates URL values to 250 chars\n    maxUrlLength: 250,\n    stackTraceLimit: 50,\n    autoBreadcrumbs: true,\n    instrument: true,\n    sampleRate: 1\n  };\n  this._ignoreOnError = 0;\n  this._isRavenInstalled = false;\n  this._originalErrorStackTraceLimit = Error.stackTraceLimit;\n  // capture references to window.console *and* all its methods first\n  // before the console plugin has a chance to monkey patch\n  this._originalConsole = _window.console || {};\n  this._originalConsoleMethods = {};\n  this._plugins = [];\n  this._startTime = now();\n  this._wrappedBuiltIns = [];\n  this._breadcrumbs = [];\n  this._lastCapturedEvent = null;\n  this._keypressTimeout;\n  this._location = _window.location;\n  this._lastHref = this._location && this._location.href;\n  this._resetBackoff();\n\n  // eslint-disable-next-line guard-for-in\n  for (var method in this._originalConsole) {\n    this._originalConsoleMethods[method] = this._originalConsole[method];\n  }\n}\n\n/*\n * The core Raven singleton\n *\n * @this {Raven}\n */\n\nRaven.prototype = {\n  // Hardcode version string so that raven source can be loaded directly via\n  // webpack (using a build step causes webpack #1617). Grunt verifies that\n  // this value matches package.json during build.\n  //   See: https://github.com/getsentry/raven-js/issues/465\n  VERSION: '3.19.1',\n\n  debug: false,\n\n  TraceKit: TraceKit, // alias to TraceKit\n\n  /*\n     * Configure Raven with a DSN and extra options\n     *\n     * @param {string} dsn The public Sentry DSN\n     * @param {object} options Set of global options [optional]\n     * @return {Raven}\n     */\n  config: function(dsn, options) {\n    var self = this;\n\n    if (self._globalServer) {\n      this._logDebug('error', 'Error: Raven has already been configured');\n      return self;\n    }\n    if (!dsn) return self;\n\n    var globalOptions = self._globalOptions;\n\n    // merge in options\n    if (options) {\n      each(options, function(key, value) {\n        // tags and extra are special and need to be put into context\n        if (key === 'tags' || key === 'extra' || key === 'user') {\n          self._globalContext[key] = value;\n        } else {\n          globalOptions[key] = value;\n        }\n      });\n    }\n\n    self.setDSN(dsn);\n\n    // \"Script error.\" is hard coded into browsers for errors that it can't read.\n    // this is the result of a script being pulled in from an external domain and CORS.\n    globalOptions.ignoreErrors.push(/^Script error\\.?$/);\n    globalOptions.ignoreErrors.push(/^Javascript error: Script error\\.? on line 0$/);\n\n    // join regexp rules into one big rule\n    globalOptions.ignoreErrors = joinRegExp(globalOptions.ignoreErrors);\n    globalOptions.ignoreUrls = globalOptions.ignoreUrls.length\n      ? joinRegExp(globalOptions.ignoreUrls)\n      : false;\n    globalOptions.whitelistUrls = globalOptions.whitelistUrls.length\n      ? joinRegExp(globalOptions.whitelistUrls)\n      : false;\n    globalOptions.includePaths = joinRegExp(globalOptions.includePaths);\n    globalOptions.maxBreadcrumbs = Math.max(\n      0,\n      Math.min(globalOptions.maxBreadcrumbs || 100, 100)\n    ); // default and hard limit is 100\n\n    var autoBreadcrumbDefaults = {\n      xhr: true,\n      console: true,\n      dom: true,\n      location: true\n    };\n\n    var autoBreadcrumbs = globalOptions.autoBreadcrumbs;\n    if ({}.toString.call(autoBreadcrumbs) === '[object Object]') {\n      autoBreadcrumbs = objectMerge(autoBreadcrumbDefaults, autoBreadcrumbs);\n    } else if (autoBreadcrumbs !== false) {\n      autoBreadcrumbs = autoBreadcrumbDefaults;\n    }\n    globalOptions.autoBreadcrumbs = autoBreadcrumbs;\n\n    var instrumentDefaults = {\n      tryCatch: true\n    };\n\n    var instrument = globalOptions.instrument;\n    if ({}.toString.call(instrument) === '[object Object]') {\n      instrument = objectMerge(instrumentDefaults, instrument);\n    } else if (instrument !== false) {\n      instrument = instrumentDefaults;\n    }\n    globalOptions.instrument = instrument;\n\n    TraceKit.collectWindowErrors = !!globalOptions.collectWindowErrors;\n\n    // return for chaining\n    return self;\n  },\n\n  /*\n     * Installs a global window.onerror error handler\n     * to capture and report uncaught exceptions.\n     * At this point, install() is required to be called due\n     * to the way TraceKit is set up.\n     *\n     * @return {Raven}\n     */\n  install: function() {\n    var self = this;\n    if (self.isSetup() && !self._isRavenInstalled) {\n      TraceKit.report.subscribe(function() {\n        self._handleOnErrorStackInfo.apply(self, arguments);\n      });\n      if (self._globalOptions.instrument && self._globalOptions.instrument.tryCatch) {\n        self._instrumentTryCatch();\n      }\n\n      if (self._globalOptions.autoBreadcrumbs) self._instrumentBreadcrumbs();\n\n      // Install all of the plugins\n      self._drainPlugins();\n\n      self._isRavenInstalled = true;\n    }\n\n    Error.stackTraceLimit = self._globalOptions.stackTraceLimit;\n    return this;\n  },\n\n  /*\n     * Set the DSN (can be called multiple time unlike config)\n     *\n     * @param {string} dsn The public Sentry DSN\n     */\n  setDSN: function(dsn) {\n    var self = this,\n      uri = self._parseDSN(dsn),\n      lastSlash = uri.path.lastIndexOf('/'),\n      path = uri.path.substr(1, lastSlash);\n\n    self._dsn = dsn;\n    self._globalKey = uri.user;\n    self._globalSecret = uri.pass && uri.pass.substr(1);\n    self._globalProject = uri.path.substr(lastSlash + 1);\n\n    self._globalServer = self._getGlobalServer(uri);\n\n    self._globalEndpoint =\n      self._globalServer + '/' + path + 'api/' + self._globalProject + '/store/';\n\n    // Reset backoff state since we may be pointing at a\n    // new project/server\n    this._resetBackoff();\n  },\n\n  /*\n     * Wrap code within a context so Raven can capture errors\n     * reliably across domains that is executed immediately.\n     *\n     * @param {object} options A specific set of options for this context [optional]\n     * @param {function} func The callback to be immediately executed within the context\n     * @param {array} args An array of arguments to be called with the callback [optional]\n     */\n  context: function(options, func, args) {\n    if (isFunction(options)) {\n      args = func || [];\n      func = options;\n      options = undefined;\n    }\n\n    return this.wrap(options, func).apply(this, args);\n  },\n\n  /*\n     * Wrap code within a context and returns back a new function to be executed\n     *\n     * @param {object} options A specific set of options for this context [optional]\n     * @param {function} func The function to be wrapped in a new context\n     * @param {function} func A function to call before the try/catch wrapper [optional, private]\n     * @return {function} The newly wrapped functions with a context\n     */\n  wrap: function(options, func, _before) {\n    var self = this;\n    // 1 argument has been passed, and it's not a function\n    // so just return it\n    if (isUndefined(func) && !isFunction(options)) {\n      return options;\n    }\n\n    // options is optional\n    if (isFunction(options)) {\n      func = options;\n      options = undefined;\n    }\n\n    // At this point, we've passed along 2 arguments, and the second one\n    // is not a function either, so we'll just return the second argument.\n    if (!isFunction(func)) {\n      return func;\n    }\n\n    // We don't wanna wrap it twice!\n    try {\n      if (func.__raven__) {\n        return func;\n      }\n\n      // If this has already been wrapped in the past, return that\n      if (func.__raven_wrapper__) {\n        return func.__raven_wrapper__;\n      }\n    } catch (e) {\n      // Just accessing custom props in some Selenium environments\n      // can cause a \"Permission denied\" exception (see raven-js#495).\n      // Bail on wrapping and return the function as-is (defers to window.onerror).\n      return func;\n    }\n\n    function wrapped() {\n      var args = [],\n        i = arguments.length,\n        deep = !options || (options && options.deep !== false);\n\n      if (_before && isFunction(_before)) {\n        _before.apply(this, arguments);\n      }\n\n      // Recursively wrap all of a function's arguments that are\n      // functions themselves.\n      while (i--) args[i] = deep ? self.wrap(options, arguments[i]) : arguments[i];\n\n      try {\n        // Attempt to invoke user-land function\n        // NOTE: If you are a Sentry user, and you are seeing this stack frame, it\n        //       means Raven caught an error invoking your application code. This is\n        //       expected behavior and NOT indicative of a bug with Raven.js.\n        return func.apply(this, args);\n      } catch (e) {\n        self._ignoreNextOnError();\n        self.captureException(e, options);\n        throw e;\n      }\n    }\n\n    // copy over properties of the old function\n    for (var property in func) {\n      if (hasKey(func, property)) {\n        wrapped[property] = func[property];\n      }\n    }\n    wrapped.prototype = func.prototype;\n\n    func.__raven_wrapper__ = wrapped;\n    // Signal that this function has been wrapped already\n    // for both debugging and to prevent it to being wrapped twice\n    wrapped.__raven__ = true;\n    wrapped.__inner__ = func;\n\n    return wrapped;\n  },\n\n  /*\n     * Uninstalls the global error handler.\n     *\n     * @return {Raven}\n     */\n  uninstall: function() {\n    TraceKit.report.uninstall();\n\n    this._restoreBuiltIns();\n\n    Error.stackTraceLimit = this._originalErrorStackTraceLimit;\n    this._isRavenInstalled = false;\n\n    return this;\n  },\n\n  /*\n     * Manually capture an exception and send it over to Sentry\n     *\n     * @param {error} ex An exception to be logged\n     * @param {object} options A specific set of options for this error [optional]\n     * @return {Raven}\n     */\n  captureException: function(ex, options) {\n    // Cases for sending ex as a message, rather than an exception\n    var isNotError = !isError(ex);\n    var isNotErrorEvent = !isErrorEvent(ex);\n    var isErrorEventWithoutError = isErrorEvent(ex) && !ex.error;\n\n    if ((isNotError && isNotErrorEvent) || isErrorEventWithoutError) {\n      return this.captureMessage(\n        ex,\n        objectMerge(\n          {\n            trimHeadFrames: 1,\n            stacktrace: true // if we fall back to captureMessage, default to attempting a new trace\n          },\n          options\n        )\n      );\n    }\n\n    // Get actual Error from ErrorEvent\n    if (isErrorEvent(ex)) ex = ex.error;\n\n    // Store the raw exception object for potential debugging and introspection\n    this._lastCapturedException = ex;\n\n    // TraceKit.report will re-raise any exception passed to it,\n    // which means you have to wrap it in try/catch. Instead, we\n    // can wrap it here and only re-raise if TraceKit.report\n    // raises an exception different from the one we asked to\n    // report on.\n    try {\n      var stack = TraceKit.computeStackTrace(ex);\n      this._handleStackInfo(stack, options);\n    } catch (ex1) {\n      if (ex !== ex1) {\n        throw ex1;\n      }\n    }\n\n    return this;\n  },\n\n  /*\n     * Manually send a message to Sentry\n     *\n     * @param {string} msg A plain message to be captured in Sentry\n     * @param {object} options A specific set of options for this message [optional]\n     * @return {Raven}\n     */\n  captureMessage: function(msg, options) {\n    // config() automagically converts ignoreErrors from a list to a RegExp so we need to test for an\n    // early call; we'll error on the side of logging anything called before configuration since it's\n    // probably something you should see:\n    if (\n      !!this._globalOptions.ignoreErrors.test &&\n      this._globalOptions.ignoreErrors.test(msg)\n    ) {\n      return;\n    }\n\n    options = options || {};\n\n    var data = objectMerge(\n      {\n        message: msg + '' // Make sure it's actually a string\n      },\n      options\n    );\n\n    var ex;\n    // Generate a \"synthetic\" stack trace from this point.\n    // NOTE: If you are a Sentry user, and you are seeing this stack frame, it is NOT indicative\n    //       of a bug with Raven.js. Sentry generates synthetic traces either by configuration,\n    //       or if it catches a thrown object without a \"stack\" property.\n    try {\n      throw new Error(msg);\n    } catch (ex1) {\n      ex = ex1;\n    }\n\n    // null exception name so `Error` isn't prefixed to msg\n    ex.name = null;\n    var stack = TraceKit.computeStackTrace(ex);\n\n    // stack[0] is `throw new Error(msg)` call itself, we are interested in the frame that was just before that, stack[1]\n    var initialCall = stack.stack[1];\n\n    var fileurl = (initialCall && initialCall.url) || '';\n\n    if (\n      !!this._globalOptions.ignoreUrls.test &&\n      this._globalOptions.ignoreUrls.test(fileurl)\n    ) {\n      return;\n    }\n\n    if (\n      !!this._globalOptions.whitelistUrls.test &&\n      !this._globalOptions.whitelistUrls.test(fileurl)\n    ) {\n      return;\n    }\n\n    if (this._globalOptions.stacktrace || (options && options.stacktrace)) {\n      options = objectMerge(\n        {\n          // fingerprint on msg, not stack trace (legacy behavior, could be\n          // revisited)\n          fingerprint: msg,\n          // since we know this is a synthetic trace, the top N-most frames\n          // MUST be from Raven.js, so mark them as in_app later by setting\n          // trimHeadFrames\n          trimHeadFrames: (options.trimHeadFrames || 0) + 1\n        },\n        options\n      );\n\n      var frames = this._prepareFrames(stack, options);\n      data.stacktrace = {\n        // Sentry expects frames oldest to newest\n        frames: frames.reverse()\n      };\n    }\n\n    // Fire away!\n    this._send(data);\n\n    return this;\n  },\n\n  captureBreadcrumb: function(obj) {\n    var crumb = objectMerge(\n      {\n        timestamp: now() / 1000\n      },\n      obj\n    );\n\n    if (isFunction(this._globalOptions.breadcrumbCallback)) {\n      var result = this._globalOptions.breadcrumbCallback(crumb);\n\n      if (isObject(result) && !isEmptyObject(result)) {\n        crumb = result;\n      } else if (result === false) {\n        return this;\n      }\n    }\n\n    this._breadcrumbs.push(crumb);\n    if (this._breadcrumbs.length > this._globalOptions.maxBreadcrumbs) {\n      this._breadcrumbs.shift();\n    }\n    return this;\n  },\n\n  addPlugin: function(plugin /*arg1, arg2, ... argN*/) {\n    var pluginArgs = [].slice.call(arguments, 1);\n\n    this._plugins.push([plugin, pluginArgs]);\n    if (this._isRavenInstalled) {\n      this._drainPlugins();\n    }\n\n    return this;\n  },\n\n  /*\n     * Set/clear a user to be sent along with the payload.\n     *\n     * @param {object} user An object representing user data [optional]\n     * @return {Raven}\n     */\n  setUserContext: function(user) {\n    // Intentionally do not merge here since that's an unexpected behavior.\n    this._globalContext.user = user;\n\n    return this;\n  },\n\n  /*\n     * Merge extra attributes to be sent along with the payload.\n     *\n     * @param {object} extra An object representing extra data [optional]\n     * @return {Raven}\n     */\n  setExtraContext: function(extra) {\n    this._mergeContext('extra', extra);\n\n    return this;\n  },\n\n  /*\n     * Merge tags to be sent along with the payload.\n     *\n     * @param {object} tags An object representing tags [optional]\n     * @return {Raven}\n     */\n  setTagsContext: function(tags) {\n    this._mergeContext('tags', tags);\n\n    return this;\n  },\n\n  /*\n     * Clear all of the context.\n     *\n     * @return {Raven}\n     */\n  clearContext: function() {\n    this._globalContext = {};\n\n    return this;\n  },\n\n  /*\n     * Get a copy of the current context. This cannot be mutated.\n     *\n     * @return {object} copy of context\n     */\n  getContext: function() {\n    // lol javascript\n    return JSON.parse(stringify(this._globalContext));\n  },\n\n  /*\n     * Set environment of application\n     *\n     * @param {string} environment Typically something like 'production'.\n     * @return {Raven}\n     */\n  setEnvironment: function(environment) {\n    this._globalOptions.environment = environment;\n\n    return this;\n  },\n\n  /*\n     * Set release version of application\n     *\n     * @param {string} release Typically something like a git SHA to identify version\n     * @return {Raven}\n     */\n  setRelease: function(release) {\n    this._globalOptions.release = release;\n\n    return this;\n  },\n\n  /*\n     * Set the dataCallback option\n     *\n     * @param {function} callback The callback to run which allows the\n     *                            data blob to be mutated before sending\n     * @return {Raven}\n     */\n  setDataCallback: function(callback) {\n    var original = this._globalOptions.dataCallback;\n    this._globalOptions.dataCallback = keepOriginalCallback(original, callback);\n    return this;\n  },\n\n  /*\n     * Set the breadcrumbCallback option\n     *\n     * @param {function} callback The callback to run which allows filtering\n     *                            or mutating breadcrumbs\n     * @return {Raven}\n     */\n  setBreadcrumbCallback: function(callback) {\n    var original = this._globalOptions.breadcrumbCallback;\n    this._globalOptions.breadcrumbCallback = keepOriginalCallback(original, callback);\n    return this;\n  },\n\n  /*\n     * Set the shouldSendCallback option\n     *\n     * @param {function} callback The callback to run which allows\n     *                            introspecting the blob before sending\n     * @return {Raven}\n     */\n  setShouldSendCallback: function(callback) {\n    var original = this._globalOptions.shouldSendCallback;\n    this._globalOptions.shouldSendCallback = keepOriginalCallback(original, callback);\n    return this;\n  },\n\n  /**\n     * Override the default HTTP transport mechanism that transmits data\n     * to the Sentry server.\n     *\n     * @param {function} transport Function invoked instead of the default\n     *                             `makeRequest` handler.\n     *\n     * @return {Raven}\n     */\n  setTransport: function(transport) {\n    this._globalOptions.transport = transport;\n\n    return this;\n  },\n\n  /*\n     * Get the latest raw exception that was captured by Raven.\n     *\n     * @return {error}\n     */\n  lastException: function() {\n    return this._lastCapturedException;\n  },\n\n  /*\n     * Get the last event id\n     *\n     * @return {string}\n     */\n  lastEventId: function() {\n    return this._lastEventId;\n  },\n\n  /*\n     * Determine if Raven is setup and ready to go.\n     *\n     * @return {boolean}\n     */\n  isSetup: function() {\n    if (!this._hasJSON) return false; // needs JSON support\n    if (!this._globalServer) {\n      if (!this.ravenNotConfiguredError) {\n        this.ravenNotConfiguredError = true;\n        this._logDebug('error', 'Error: Raven has not been configured.');\n      }\n      return false;\n    }\n    return true;\n  },\n\n  afterLoad: function() {\n    // TODO: remove window dependence?\n\n    // Attempt to initialize Raven on load\n    var RavenConfig = _window.RavenConfig;\n    if (RavenConfig) {\n      this.config(RavenConfig.dsn, RavenConfig.config).install();\n    }\n  },\n\n  showReportDialog: function(options) {\n    if (\n      !_document // doesn't work without a document (React native)\n    )\n      return;\n\n    options = options || {};\n\n    var lastEventId = options.eventId || this.lastEventId();\n    if (!lastEventId) {\n      throw new RavenConfigError('Missing eventId');\n    }\n\n    var dsn = options.dsn || this._dsn;\n    if (!dsn) {\n      throw new RavenConfigError('Missing DSN');\n    }\n\n    var encode = encodeURIComponent;\n    var qs = '';\n    qs += '?eventId=' + encode(lastEventId);\n    qs += '&dsn=' + encode(dsn);\n\n    var user = options.user || this._globalContext.user;\n    if (user) {\n      if (user.name) qs += '&name=' + encode(user.name);\n      if (user.email) qs += '&email=' + encode(user.email);\n    }\n\n    var globalServer = this._getGlobalServer(this._parseDSN(dsn));\n\n    var script = _document.createElement('script');\n    script.async = true;\n    script.src = globalServer + '/api/embed/error-page/' + qs;\n    (_document.head || _document.body).appendChild(script);\n  },\n\n  /**** Private functions ****/\n  _ignoreNextOnError: function() {\n    var self = this;\n    this._ignoreOnError += 1;\n    setTimeout(function() {\n      // onerror should trigger before setTimeout\n      self._ignoreOnError -= 1;\n    });\n  },\n\n  _triggerEvent: function(eventType, options) {\n    // NOTE: `event` is a native browser thing, so let's avoid conflicting wiht it\n    var evt, key;\n\n    if (!this._hasDocument) return;\n\n    options = options || {};\n\n    eventType = 'raven' + eventType.substr(0, 1).toUpperCase() + eventType.substr(1);\n\n    if (_document.createEvent) {\n      evt = _document.createEvent('HTMLEvents');\n      evt.initEvent(eventType, true, true);\n    } else {\n      evt = _document.createEventObject();\n      evt.eventType = eventType;\n    }\n\n    for (key in options)\n      if (hasKey(options, key)) {\n        evt[key] = options[key];\n      }\n\n    if (_document.createEvent) {\n      // IE9 if standards\n      _document.dispatchEvent(evt);\n    } else {\n      // IE8 regardless of Quirks or Standards\n      // IE9 if quirks\n      try {\n        _document.fireEvent('on' + evt.eventType.toLowerCase(), evt);\n      } catch (e) {\n        // Do nothing\n      }\n    }\n  },\n\n  /**\n     * Wraps addEventListener to capture UI breadcrumbs\n     * @param evtName the event name (e.g. \"click\")\n     * @returns {Function}\n     * @private\n     */\n  _breadcrumbEventHandler: function(evtName) {\n    var self = this;\n    return function(evt) {\n      // reset keypress timeout; e.g. triggering a 'click' after\n      // a 'keypress' will reset the keypress debounce so that a new\n      // set of keypresses can be recorded\n      self._keypressTimeout = null;\n\n      // It's possible this handler might trigger multiple times for the same\n      // event (e.g. event propagation through node ancestors). Ignore if we've\n      // already captured the event.\n      if (self._lastCapturedEvent === evt) return;\n\n      self._lastCapturedEvent = evt;\n\n      // try/catch both:\n      // - accessing evt.target (see getsentry/raven-js#838, #768)\n      // - `htmlTreeAsString` because it's complex, and just accessing the DOM incorrectly\n      //   can throw an exception in some circumstances.\n      var target;\n      try {\n        target = htmlTreeAsString(evt.target);\n      } catch (e) {\n        target = '<unknown>';\n      }\n\n      self.captureBreadcrumb({\n        category: 'ui.' + evtName, // e.g. ui.click, ui.input\n        message: target\n      });\n    };\n  },\n\n  /**\n     * Wraps addEventListener to capture keypress UI events\n     * @returns {Function}\n     * @private\n     */\n  _keypressEventHandler: function() {\n    var self = this,\n      debounceDuration = 1000; // milliseconds\n\n    // TODO: if somehow user switches keypress target before\n    //       debounce timeout is triggered, we will only capture\n    //       a single breadcrumb from the FIRST target (acceptable?)\n    return function(evt) {\n      var target;\n      try {\n        target = evt.target;\n      } catch (e) {\n        // just accessing event properties can throw an exception in some rare circumstances\n        // see: https://github.com/getsentry/raven-js/issues/838\n        return;\n      }\n      var tagName = target && target.tagName;\n\n      // only consider keypress events on actual input elements\n      // this will disregard keypresses targeting body (e.g. tabbing\n      // through elements, hotkeys, etc)\n      if (\n        !tagName ||\n        (tagName !== 'INPUT' && tagName !== 'TEXTAREA' && !target.isContentEditable)\n      )\n        return;\n\n      // record first keypress in a series, but ignore subsequent\n      // keypresses until debounce clears\n      var timeout = self._keypressTimeout;\n      if (!timeout) {\n        self._breadcrumbEventHandler('input')(evt);\n      }\n      clearTimeout(timeout);\n      self._keypressTimeout = setTimeout(function() {\n        self._keypressTimeout = null;\n      }, debounceDuration);\n    };\n  },\n\n  /**\n     * Captures a breadcrumb of type \"navigation\", normalizing input URLs\n     * @param to the originating URL\n     * @param from the target URL\n     * @private\n     */\n  _captureUrlChange: function(from, to) {\n    var parsedLoc = parseUrl(this._location.href);\n    var parsedTo = parseUrl(to);\n    var parsedFrom = parseUrl(from);\n\n    // because onpopstate only tells you the \"new\" (to) value of location.href, and\n    // not the previous (from) value, we need to track the value of the current URL\n    // state ourselves\n    this._lastHref = to;\n\n    // Use only the path component of the URL if the URL matches the current\n    // document (almost all the time when using pushState)\n    if (parsedLoc.protocol === parsedTo.protocol && parsedLoc.host === parsedTo.host)\n      to = parsedTo.relative;\n    if (parsedLoc.protocol === parsedFrom.protocol && parsedLoc.host === parsedFrom.host)\n      from = parsedFrom.relative;\n\n    this.captureBreadcrumb({\n      category: 'navigation',\n      data: {\n        to: to,\n        from: from\n      }\n    });\n  },\n\n  /**\n     * Wrap timer functions and event targets to catch errors and provide\n     * better metadata.\n     */\n  _instrumentTryCatch: function() {\n    var self = this;\n\n    var wrappedBuiltIns = self._wrappedBuiltIns;\n\n    function wrapTimeFn(orig) {\n      return function(fn, t) {\n        // preserve arity\n        // Make a copy of the arguments to prevent deoptimization\n        // https://github.com/petkaantonov/bluebird/wiki/Optimization-killers#32-leaking-arguments\n        var args = new Array(arguments.length);\n        for (var i = 0; i < args.length; ++i) {\n          args[i] = arguments[i];\n        }\n        var originalCallback = args[0];\n        if (isFunction(originalCallback)) {\n          args[0] = self.wrap(originalCallback);\n        }\n\n        // IE < 9 doesn't support .call/.apply on setInterval/setTimeout, but it\n        // also supports only two arguments and doesn't care what this is, so we\n        // can just call the original function directly.\n        if (orig.apply) {\n          return orig.apply(this, args);\n        } else {\n          return orig(args[0], args[1]);\n        }\n      };\n    }\n\n    var autoBreadcrumbs = this._globalOptions.autoBreadcrumbs;\n\n    function wrapEventTarget(global) {\n      var proto = _window[global] && _window[global].prototype;\n      if (proto && proto.hasOwnProperty && proto.hasOwnProperty('addEventListener')) {\n        fill(\n          proto,\n          'addEventListener',\n          function(orig) {\n            return function(evtName, fn, capture, secure) {\n              // preserve arity\n              try {\n                if (fn && fn.handleEvent) {\n                  fn.handleEvent = self.wrap(fn.handleEvent);\n                }\n              } catch (err) {\n                // can sometimes get 'Permission denied to access property \"handle Event'\n              }\n\n              // More breadcrumb DOM capture ... done here and not in `_instrumentBreadcrumbs`\n              // so that we don't have more than one wrapper function\n              var before, clickHandler, keypressHandler;\n\n              if (\n                autoBreadcrumbs &&\n                autoBreadcrumbs.dom &&\n                (global === 'EventTarget' || global === 'Node')\n              ) {\n                // NOTE: generating multiple handlers per addEventListener invocation, should\n                //       revisit and verify we can just use one (almost certainly)\n                clickHandler = self._breadcrumbEventHandler('click');\n                keypressHandler = self._keypressEventHandler();\n                before = function(evt) {\n                  // need to intercept every DOM event in `before` argument, in case that\n                  // same wrapped method is re-used for different events (e.g. mousemove THEN click)\n                  // see #724\n                  if (!evt) return;\n\n                  var eventType;\n                  try {\n                    eventType = evt.type;\n                  } catch (e) {\n                    // just accessing event properties can throw an exception in some rare circumstances\n                    // see: https://github.com/getsentry/raven-js/issues/838\n                    return;\n                  }\n                  if (eventType === 'click') return clickHandler(evt);\n                  else if (eventType === 'keypress') return keypressHandler(evt);\n                };\n              }\n              return orig.call(\n                this,\n                evtName,\n                self.wrap(fn, undefined, before),\n                capture,\n                secure\n              );\n            };\n          },\n          wrappedBuiltIns\n        );\n        fill(\n          proto,\n          'removeEventListener',\n          function(orig) {\n            return function(evt, fn, capture, secure) {\n              try {\n                fn = fn && (fn.__raven_wrapper__ ? fn.__raven_wrapper__ : fn);\n              } catch (e) {\n                // ignore, accessing __raven_wrapper__ will throw in some Selenium environments\n              }\n              return orig.call(this, evt, fn, capture, secure);\n            };\n          },\n          wrappedBuiltIns\n        );\n      }\n    }\n\n    fill(_window, 'setTimeout', wrapTimeFn, wrappedBuiltIns);\n    fill(_window, 'setInterval', wrapTimeFn, wrappedBuiltIns);\n    if (_window.requestAnimationFrame) {\n      fill(\n        _window,\n        'requestAnimationFrame',\n        function(orig) {\n          return function(cb) {\n            return orig(self.wrap(cb));\n          };\n        },\n        wrappedBuiltIns\n      );\n    }\n\n    // event targets borrowed from bugsnag-js:\n    // https://github.com/bugsnag/bugsnag-js/blob/master/src/bugsnag.js#L666\n    var eventTargets = [\n      'EventTarget',\n      'Window',\n      'Node',\n      'ApplicationCache',\n      'AudioTrackList',\n      'ChannelMergerNode',\n      'CryptoOperation',\n      'EventSource',\n      'FileReader',\n      'HTMLUnknownElement',\n      'IDBDatabase',\n      'IDBRequest',\n      'IDBTransaction',\n      'KeyOperation',\n      'MediaController',\n      'MessagePort',\n      'ModalWindow',\n      'Notification',\n      'SVGElementInstance',\n      'Screen',\n      'TextTrack',\n      'TextTrackCue',\n      'TextTrackList',\n      'WebSocket',\n      'WebSocketWorker',\n      'Worker',\n      'XMLHttpRequest',\n      'XMLHttpRequestEventTarget',\n      'XMLHttpRequestUpload'\n    ];\n    for (var i = 0; i < eventTargets.length; i++) {\n      wrapEventTarget(eventTargets[i]);\n    }\n  },\n\n  /**\n     * Instrument browser built-ins w/ breadcrumb capturing\n     *  - XMLHttpRequests\n     *  - DOM interactions (click/typing)\n     *  - window.location changes\n     *  - console\n     *\n     * Can be disabled or individually configured via the `autoBreadcrumbs` config option\n     */\n  _instrumentBreadcrumbs: function() {\n    var self = this;\n    var autoBreadcrumbs = this._globalOptions.autoBreadcrumbs;\n\n    var wrappedBuiltIns = self._wrappedBuiltIns;\n\n    function wrapProp(prop, xhr) {\n      if (prop in xhr && isFunction(xhr[prop])) {\n        fill(xhr, prop, function(orig) {\n          return self.wrap(orig);\n        }); // intentionally don't track filled methods on XHR instances\n      }\n    }\n\n    if (autoBreadcrumbs.xhr && 'XMLHttpRequest' in _window) {\n      var xhrproto = XMLHttpRequest.prototype;\n      fill(\n        xhrproto,\n        'open',\n        function(origOpen) {\n          return function(method, url) {\n            // preserve arity\n\n            // if Sentry key appears in URL, don't capture\n            if (isString(url) && url.indexOf(self._globalKey) === -1) {\n              this.__raven_xhr = {\n                method: method,\n                url: url,\n                status_code: null\n              };\n            }\n\n            return origOpen.apply(this, arguments);\n          };\n        },\n        wrappedBuiltIns\n      );\n\n      fill(\n        xhrproto,\n        'send',\n        function(origSend) {\n          return function(data) {\n            // preserve arity\n            var xhr = this;\n\n            function onreadystatechangeHandler() {\n              if (xhr.__raven_xhr && xhr.readyState === 4) {\n                try {\n                  // touching statusCode in some platforms throws\n                  // an exception\n                  xhr.__raven_xhr.status_code = xhr.status;\n                } catch (e) {\n                  /* do nothing */\n                }\n\n                self.captureBreadcrumb({\n                  type: 'http',\n                  category: 'xhr',\n                  data: xhr.__raven_xhr\n                });\n              }\n            }\n\n            var props = ['onload', 'onerror', 'onprogress'];\n            for (var j = 0; j < props.length; j++) {\n              wrapProp(props[j], xhr);\n            }\n\n            if ('onreadystatechange' in xhr && isFunction(xhr.onreadystatechange)) {\n              fill(\n                xhr,\n                'onreadystatechange',\n                function(orig) {\n                  return self.wrap(orig, undefined, onreadystatechangeHandler);\n                } /* intentionally don't track this instrumentation */\n              );\n            } else {\n              // if onreadystatechange wasn't actually set by the page on this xhr, we\n              // are free to set our own and capture the breadcrumb\n              xhr.onreadystatechange = onreadystatechangeHandler;\n            }\n\n            return origSend.apply(this, arguments);\n          };\n        },\n        wrappedBuiltIns\n      );\n    }\n\n    if (autoBreadcrumbs.xhr && 'fetch' in _window) {\n      fill(\n        _window,\n        'fetch',\n        function(origFetch) {\n          return function(fn, t) {\n            // preserve arity\n            // Make a copy of the arguments to prevent deoptimization\n            // https://github.com/petkaantonov/bluebird/wiki/Optimization-killers#32-leaking-arguments\n            var args = new Array(arguments.length);\n            for (var i = 0; i < args.length; ++i) {\n              args[i] = arguments[i];\n            }\n\n            var fetchInput = args[0];\n            var method = 'GET';\n            var url;\n\n            if (typeof fetchInput === 'string') {\n              url = fetchInput;\n            } else if ('Request' in _window && fetchInput instanceof _window.Request) {\n              url = fetchInput.url;\n              if (fetchInput.method) {\n                method = fetchInput.method;\n              }\n            } else {\n              url = '' + fetchInput;\n            }\n\n            if (args[1] && args[1].method) {\n              method = args[1].method;\n            }\n\n            var fetchData = {\n              method: method,\n              url: url,\n              status_code: null\n            };\n\n            self.captureBreadcrumb({\n              type: 'http',\n              category: 'fetch',\n              data: fetchData\n            });\n\n            return origFetch.apply(this, args).then(function(response) {\n              fetchData.status_code = response.status;\n\n              return response;\n            });\n          };\n        },\n        wrappedBuiltIns\n      );\n    }\n\n    // Capture breadcrumbs from any click that is unhandled / bubbled up all the way\n    // to the document. Do this before we instrument addEventListener.\n    if (autoBreadcrumbs.dom && this._hasDocument) {\n      if (_document.addEventListener) {\n        _document.addEventListener('click', self._breadcrumbEventHandler('click'), false);\n        _document.addEventListener('keypress', self._keypressEventHandler(), false);\n      } else {\n        // IE8 Compatibility\n        _document.attachEvent('onclick', self._breadcrumbEventHandler('click'));\n        _document.attachEvent('onkeypress', self._keypressEventHandler());\n      }\n    }\n\n    // record navigation (URL) changes\n    // NOTE: in Chrome App environment, touching history.pushState, *even inside\n    //       a try/catch block*, will cause Chrome to output an error to console.error\n    // borrowed from: https://github.com/angular/angular.js/pull/13945/files\n    var chrome = _window.chrome;\n    var isChromePackagedApp = chrome && chrome.app && chrome.app.runtime;\n    var hasPushAndReplaceState =\n      !isChromePackagedApp &&\n      _window.history &&\n      history.pushState &&\n      history.replaceState;\n    if (autoBreadcrumbs.location && hasPushAndReplaceState) {\n      // TODO: remove onpopstate handler on uninstall()\n      var oldOnPopState = _window.onpopstate;\n      _window.onpopstate = function() {\n        var currentHref = self._location.href;\n        self._captureUrlChange(self._lastHref, currentHref);\n\n        if (oldOnPopState) {\n          return oldOnPopState.apply(this, arguments);\n        }\n      };\n\n      var historyReplacementFunction = function(origHistFunction) {\n        // note history.pushState.length is 0; intentionally not declaring\n        // params to preserve 0 arity\n        return function(/* state, title, url */) {\n          var url = arguments.length > 2 ? arguments[2] : undefined;\n\n          // url argument is optional\n          if (url) {\n            // coerce to string (this is what pushState does)\n            self._captureUrlChange(self._lastHref, url + '');\n          }\n\n          return origHistFunction.apply(this, arguments);\n        };\n      };\n\n      fill(history, 'pushState', historyReplacementFunction, wrappedBuiltIns);\n      fill(history, 'replaceState', historyReplacementFunction, wrappedBuiltIns);\n    }\n\n    if (autoBreadcrumbs.console && 'console' in _window && console.log) {\n      // console\n      var consoleMethodCallback = function(msg, data) {\n        self.captureBreadcrumb({\n          message: msg,\n          level: data.level,\n          category: 'console'\n        });\n      };\n\n      each(['debug', 'info', 'warn', 'error', 'log'], function(_, level) {\n        wrapConsoleMethod(console, level, consoleMethodCallback);\n      });\n    }\n  },\n\n  _restoreBuiltIns: function() {\n    // restore any wrapped builtins\n    var builtin;\n    while (this._wrappedBuiltIns.length) {\n      builtin = this._wrappedBuiltIns.shift();\n\n      var obj = builtin[0],\n        name = builtin[1],\n        orig = builtin[2];\n\n      obj[name] = orig;\n    }\n  },\n\n  _drainPlugins: function() {\n    var self = this;\n\n    // FIX ME TODO\n    each(this._plugins, function(_, plugin) {\n      var installer = plugin[0];\n      var args = plugin[1];\n      installer.apply(self, [self].concat(args));\n    });\n  },\n\n  _parseDSN: function(str) {\n    var m = dsnPattern.exec(str),\n      dsn = {},\n      i = 7;\n\n    try {\n      while (i--) dsn[dsnKeys[i]] = m[i] || '';\n    } catch (e) {\n      throw new RavenConfigError('Invalid DSN: ' + str);\n    }\n\n    if (dsn.pass && !this._globalOptions.allowSecretKey) {\n      throw new RavenConfigError(\n        'Do not specify your secret key in the DSN. See: http://bit.ly/raven-secret-key'\n      );\n    }\n\n    return dsn;\n  },\n\n  _getGlobalServer: function(uri) {\n    // assemble the endpoint from the uri pieces\n    var globalServer = '//' + uri.host + (uri.port ? ':' + uri.port : '');\n\n    if (uri.protocol) {\n      globalServer = uri.protocol + ':' + globalServer;\n    }\n    return globalServer;\n  },\n\n  _handleOnErrorStackInfo: function() {\n    // if we are intentionally ignoring errors via onerror, bail out\n    if (!this._ignoreOnError) {\n      this._handleStackInfo.apply(this, arguments);\n    }\n  },\n\n  _handleStackInfo: function(stackInfo, options) {\n    var frames = this._prepareFrames(stackInfo, options);\n\n    this._triggerEvent('handle', {\n      stackInfo: stackInfo,\n      options: options\n    });\n\n    this._processException(\n      stackInfo.name,\n      stackInfo.message,\n      stackInfo.url,\n      stackInfo.lineno,\n      frames,\n      options\n    );\n  },\n\n  _prepareFrames: function(stackInfo, options) {\n    var self = this;\n    var frames = [];\n    if (stackInfo.stack && stackInfo.stack.length) {\n      each(stackInfo.stack, function(i, stack) {\n        var frame = self._normalizeFrame(stack, stackInfo.url);\n        if (frame) {\n          frames.push(frame);\n        }\n      });\n\n      // e.g. frames captured via captureMessage throw\n      if (options && options.trimHeadFrames) {\n        for (var j = 0; j < options.trimHeadFrames && j < frames.length; j++) {\n          frames[j].in_app = false;\n        }\n      }\n    }\n    frames = frames.slice(0, this._globalOptions.stackTraceLimit);\n    return frames;\n  },\n\n  _normalizeFrame: function(frame, stackInfoUrl) {\n    // normalize the frames data\n    var normalized = {\n      filename: frame.url,\n      lineno: frame.line,\n      colno: frame.column,\n      function: frame.func || '?'\n    };\n\n    // Case when we don't have any information about the error\n    // E.g. throwing a string or raw object, instead of an `Error` in Firefox\n    // Generating synthetic error doesn't add any value here\n    //\n    // We should probably somehow let a user know that they should fix their code\n    if (!frame.url) {\n      normalized.filename = stackInfoUrl; // fallback to whole stacks url from onerror handler\n    }\n\n    normalized.in_app = !// determine if an exception came from outside of our app\n    // first we check the global includePaths list.\n    (\n      (!!this._globalOptions.includePaths.test &&\n        !this._globalOptions.includePaths.test(normalized.filename)) ||\n      // Now we check for fun, if the function name is Raven or TraceKit\n      /(Raven|TraceKit)\\./.test(normalized['function']) ||\n      // finally, we do a last ditch effort and check for raven.min.js\n      /raven\\.(min\\.)?js$/.test(normalized.filename)\n    );\n\n    return normalized;\n  },\n\n  _processException: function(type, message, fileurl, lineno, frames, options) {\n    var prefixedMessage = (type ? type + ': ' : '') + (message || '');\n    if (\n      !!this._globalOptions.ignoreErrors.test &&\n      (this._globalOptions.ignoreErrors.test(message) ||\n        this._globalOptions.ignoreErrors.test(prefixedMessage))\n    ) {\n      return;\n    }\n\n    var stacktrace;\n\n    if (frames && frames.length) {\n      fileurl = frames[0].filename || fileurl;\n      // Sentry expects frames oldest to newest\n      // and JS sends them as newest to oldest\n      frames.reverse();\n      stacktrace = {frames: frames};\n    } else if (fileurl) {\n      stacktrace = {\n        frames: [\n          {\n            filename: fileurl,\n            lineno: lineno,\n            in_app: true\n          }\n        ]\n      };\n    }\n\n    if (\n      !!this._globalOptions.ignoreUrls.test &&\n      this._globalOptions.ignoreUrls.test(fileurl)\n    ) {\n      return;\n    }\n\n    if (\n      !!this._globalOptions.whitelistUrls.test &&\n      !this._globalOptions.whitelistUrls.test(fileurl)\n    ) {\n      return;\n    }\n\n    var data = objectMerge(\n      {\n        // sentry.interfaces.Exception\n        exception: {\n          values: [\n            {\n              type: type,\n              value: message,\n              stacktrace: stacktrace\n            }\n          ]\n        },\n        culprit: fileurl\n      },\n      options\n    );\n\n    // Fire away!\n    this._send(data);\n  },\n\n  _trimPacket: function(data) {\n    // For now, we only want to truncate the two different messages\n    // but this could/should be expanded to just trim everything\n    var max = this._globalOptions.maxMessageLength;\n    if (data.message) {\n      data.message = truncate(data.message, max);\n    }\n    if (data.exception) {\n      var exception = data.exception.values[0];\n      exception.value = truncate(exception.value, max);\n    }\n\n    var request = data.request;\n    if (request) {\n      if (request.url) {\n        request.url = truncate(request.url, this._globalOptions.maxUrlLength);\n      }\n      if (request.Referer) {\n        request.Referer = truncate(request.Referer, this._globalOptions.maxUrlLength);\n      }\n    }\n\n    if (data.breadcrumbs && data.breadcrumbs.values)\n      this._trimBreadcrumbs(data.breadcrumbs);\n\n    return data;\n  },\n\n  /**\n     * Truncate breadcrumb values (right now just URLs)\n     */\n  _trimBreadcrumbs: function(breadcrumbs) {\n    // known breadcrumb properties with urls\n    // TODO: also consider arbitrary prop values that start with (https?)?://\n    var urlProps = ['to', 'from', 'url'],\n      urlProp,\n      crumb,\n      data;\n\n    for (var i = 0; i < breadcrumbs.values.length; ++i) {\n      crumb = breadcrumbs.values[i];\n      if (\n        !crumb.hasOwnProperty('data') ||\n        !isObject(crumb.data) ||\n        objectFrozen(crumb.data)\n      )\n        continue;\n\n      data = objectMerge({}, crumb.data);\n      for (var j = 0; j < urlProps.length; ++j) {\n        urlProp = urlProps[j];\n        if (data.hasOwnProperty(urlProp) && data[urlProp]) {\n          data[urlProp] = truncate(data[urlProp], this._globalOptions.maxUrlLength);\n        }\n      }\n      breadcrumbs.values[i].data = data;\n    }\n  },\n\n  _getHttpData: function() {\n    if (!this._hasNavigator && !this._hasDocument) return;\n    var httpData = {};\n\n    if (this._hasNavigator && _navigator.userAgent) {\n      httpData.headers = {\n        'User-Agent': navigator.userAgent\n      };\n    }\n\n    if (this._hasDocument) {\n      if (_document.location && _document.location.href) {\n        httpData.url = _document.location.href;\n      }\n      if (_document.referrer) {\n        if (!httpData.headers) httpData.headers = {};\n        httpData.headers.Referer = _document.referrer;\n      }\n    }\n\n    return httpData;\n  },\n\n  _resetBackoff: function() {\n    this._backoffDuration = 0;\n    this._backoffStart = null;\n  },\n\n  _shouldBackoff: function() {\n    return this._backoffDuration && now() - this._backoffStart < this._backoffDuration;\n  },\n\n  /**\n     * Returns true if the in-process data payload matches the signature\n     * of the previously-sent data\n     *\n     * NOTE: This has to be done at this level because TraceKit can generate\n     *       data from window.onerror WITHOUT an exception object (IE8, IE9,\n     *       other old browsers). This can take the form of an \"exception\"\n     *       data object with a single frame (derived from the onerror args).\n     */\n  _isRepeatData: function(current) {\n    var last = this._lastData;\n\n    if (\n      !last ||\n      current.message !== last.message || // defined for captureMessage\n      current.culprit !== last.culprit // defined for captureException/onerror\n    )\n      return false;\n\n    // Stacktrace interface (i.e. from captureMessage)\n    if (current.stacktrace || last.stacktrace) {\n      return isSameStacktrace(current.stacktrace, last.stacktrace);\n    } else if (current.exception || last.exception) {\n      // Exception interface (i.e. from captureException/onerror)\n      return isSameException(current.exception, last.exception);\n    }\n\n    return true;\n  },\n\n  _setBackoffState: function(request) {\n    // If we are already in a backoff state, don't change anything\n    if (this._shouldBackoff()) {\n      return;\n    }\n\n    var status = request.status;\n\n    // 400 - project_id doesn't exist or some other fatal\n    // 401 - invalid/revoked dsn\n    // 429 - too many requests\n    if (!(status === 400 || status === 401 || status === 429)) return;\n\n    var retry;\n    try {\n      // If Retry-After is not in Access-Control-Expose-Headers, most\n      // browsers will throw an exception trying to access it\n      retry = request.getResponseHeader('Retry-After');\n      retry = parseInt(retry, 10) * 1000; // Retry-After is returned in seconds\n    } catch (e) {\n      /* eslint no-empty:0 */\n    }\n\n    this._backoffDuration = retry\n      ? // If Sentry server returned a Retry-After value, use it\n        retry\n      : // Otherwise, double the last backoff duration (starts at 1 sec)\n        this._backoffDuration * 2 || 1000;\n\n    this._backoffStart = now();\n  },\n\n  _send: function(data) {\n    var globalOptions = this._globalOptions;\n\n    var baseData = {\n        project: this._globalProject,\n        logger: globalOptions.logger,\n        platform: 'javascript'\n      },\n      httpData = this._getHttpData();\n\n    if (httpData) {\n      baseData.request = httpData;\n    }\n\n    // HACK: delete `trimHeadFrames` to prevent from appearing in outbound payload\n    if (data.trimHeadFrames) delete data.trimHeadFrames;\n\n    data = objectMerge(baseData, data);\n\n    // Merge in the tags and extra separately since objectMerge doesn't handle a deep merge\n    data.tags = objectMerge(objectMerge({}, this._globalContext.tags), data.tags);\n    data.extra = objectMerge(objectMerge({}, this._globalContext.extra), data.extra);\n\n    // Send along our own collected metadata with extra\n    data.extra['session:duration'] = now() - this._startTime;\n\n    if (this._breadcrumbs && this._breadcrumbs.length > 0) {\n      // intentionally make shallow copy so that additions\n      // to breadcrumbs aren't accidentally sent in this request\n      data.breadcrumbs = {\n        values: [].slice.call(this._breadcrumbs, 0)\n      };\n    }\n\n    // If there are no tags/extra, strip the key from the payload alltogther.\n    if (isEmptyObject(data.tags)) delete data.tags;\n\n    if (this._globalContext.user) {\n      // sentry.interfaces.User\n      data.user = this._globalContext.user;\n    }\n\n    // Include the environment if it's defined in globalOptions\n    if (globalOptions.environment) data.environment = globalOptions.environment;\n\n    // Include the release if it's defined in globalOptions\n    if (globalOptions.release) data.release = globalOptions.release;\n\n    // Include server_name if it's defined in globalOptions\n    if (globalOptions.serverName) data.server_name = globalOptions.serverName;\n\n    if (isFunction(globalOptions.dataCallback)) {\n      data = globalOptions.dataCallback(data) || data;\n    }\n\n    // Why??????????\n    if (!data || isEmptyObject(data)) {\n      return;\n    }\n\n    // Check if the request should be filtered or not\n    if (\n      isFunction(globalOptions.shouldSendCallback) &&\n      !globalOptions.shouldSendCallback(data)\n    ) {\n      return;\n    }\n\n    // Backoff state: Sentry server previously responded w/ an error (e.g. 429 - too many requests),\n    // so drop requests until \"cool-off\" period has elapsed.\n    if (this._shouldBackoff()) {\n      this._logDebug('warn', 'Raven dropped error due to backoff: ', data);\n      return;\n    }\n\n    if (typeof globalOptions.sampleRate === 'number') {\n      if (Math.random() < globalOptions.sampleRate) {\n        this._sendProcessedPayload(data);\n      }\n    } else {\n      this._sendProcessedPayload(data);\n    }\n  },\n\n  _getUuid: function() {\n    return uuid4();\n  },\n\n  _sendProcessedPayload: function(data, callback) {\n    var self = this;\n    var globalOptions = this._globalOptions;\n\n    if (!this.isSetup()) return;\n\n    // Try and clean up the packet before sending by truncating long values\n    data = this._trimPacket(data);\n\n    // ideally duplicate error testing should occur *before* dataCallback/shouldSendCallback,\n    // but this would require copying an un-truncated copy of the data packet, which can be\n    // arbitrarily deep (extra_data) -- could be worthwhile? will revisit\n    if (!this._globalOptions.allowDuplicates && this._isRepeatData(data)) {\n      this._logDebug('warn', 'Raven dropped repeat event: ', data);\n      return;\n    }\n\n    // Send along an event_id if not explicitly passed.\n    // This event_id can be used to reference the error within Sentry itself.\n    // Set lastEventId after we know the error should actually be sent\n    this._lastEventId = data.event_id || (data.event_id = this._getUuid());\n\n    // Store outbound payload after trim\n    this._lastData = data;\n\n    this._logDebug('debug', 'Raven about to send:', data);\n\n    var auth = {\n      sentry_version: '7',\n      sentry_client: 'raven-js/' + this.VERSION,\n      sentry_key: this._globalKey\n    };\n\n    if (this._globalSecret) {\n      auth.sentry_secret = this._globalSecret;\n    }\n\n    var exception = data.exception && data.exception.values[0];\n    this.captureBreadcrumb({\n      category: 'sentry',\n      message: exception\n        ? (exception.type ? exception.type + ': ' : '') + exception.value\n        : data.message,\n      event_id: data.event_id,\n      level: data.level || 'error' // presume error unless specified\n    });\n\n    var url = this._globalEndpoint;\n    (globalOptions.transport || this._makeRequest).call(this, {\n      url: url,\n      auth: auth,\n      data: data,\n      options: globalOptions,\n      onSuccess: function success() {\n        self._resetBackoff();\n\n        self._triggerEvent('success', {\n          data: data,\n          src: url\n        });\n        callback && callback();\n      },\n      onError: function failure(error) {\n        self._logDebug('error', 'Raven transport failed to send: ', error);\n\n        if (error.request) {\n          self._setBackoffState(error.request);\n        }\n\n        self._triggerEvent('failure', {\n          data: data,\n          src: url\n        });\n        error = error || new Error('Raven send failed (no additional details provided)');\n        callback && callback(error);\n      }\n    });\n  },\n\n  _makeRequest: function(opts) {\n    var request = _window.XMLHttpRequest && new _window.XMLHttpRequest();\n    if (!request) return;\n\n    // if browser doesn't support CORS (e.g. IE7), we are out of luck\n    var hasCORS = 'withCredentials' in request || typeof XDomainRequest !== 'undefined';\n\n    if (!hasCORS) return;\n\n    var url = opts.url;\n\n    if ('withCredentials' in request) {\n      request.onreadystatechange = function() {\n        if (request.readyState !== 4) {\n          return;\n        } else if (request.status === 200) {\n          opts.onSuccess && opts.onSuccess();\n        } else if (opts.onError) {\n          var err = new Error('Sentry error code: ' + request.status);\n          err.request = request;\n          opts.onError(err);\n        }\n      };\n    } else {\n      request = new XDomainRequest();\n      // xdomainrequest cannot go http -> https (or vice versa),\n      // so always use protocol relative\n      url = url.replace(/^https?:/, '');\n\n      // onreadystatechange not supported by XDomainRequest\n      if (opts.onSuccess) {\n        request.onload = opts.onSuccess;\n      }\n      if (opts.onError) {\n        request.onerror = function() {\n          var err = new Error('Sentry error code: XDomainRequest');\n          err.request = request;\n          opts.onError(err);\n        };\n      }\n    }\n\n    // NOTE: auth is intentionally sent as part of query string (NOT as custom\n    //       HTTP header) so as to avoid preflight CORS requests\n    request.open('POST', url + '?' + urlencode(opts.auth));\n    request.send(stringify(opts.data));\n  },\n\n  _logDebug: function(level) {\n    if (this._originalConsoleMethods[level] && this.debug) {\n      // In IE<10 console methods do not have their own 'apply' method\n      Function.prototype.apply.call(\n        this._originalConsoleMethods[level],\n        this._originalConsole,\n        [].slice.call(arguments, 1)\n      );\n    }\n  },\n\n  _mergeContext: function(key, context) {\n    if (isUndefined(context)) {\n      delete this._globalContext[key];\n    } else {\n      this._globalContext[key] = objectMerge(this._globalContext[key] || {}, context);\n    }\n  }\n};\n\n// Deprecations\nRaven.prototype.setUser = Raven.prototype.setUserContext;\nRaven.prototype.setReleaseContext = Raven.prototype.setRelease;\n\nmodule.exports = Raven;\n", "/**\n * Enforces a single instance of the Raven client, and the\n * main entry point for <PERSON>. If you are a consumer of the\n * Raven library, you SHOULD load this file (vs raven.js).\n **/\n\nvar RavenConstructor = require('./raven');\n\n// This is to be defensive in environments where window does not exist (see https://github.com/getsentry/raven-js/pull/785)\nvar _window =\n  typeof window !== 'undefined'\n    ? window\n    : typeof global !== 'undefined' ? global : typeof self !== 'undefined' ? self : {};\nvar _Raven = _window.Raven;\n\nvar Raven = new RavenConstructor();\n\n/*\n * Allow multiple versions of Raven to be installed.\n * Strip Raven from the global context and returns the instance.\n *\n * @return {Raven}\n */\nRaven.noConflict = function() {\n  _window.Raven = _Raven;\n  return Raven;\n};\n\nRaven.afterLoad();\n\nmodule.exports = Raven;\n", "var _window =\n  typeof window !== 'undefined'\n    ? window\n    : typeof global !== 'undefined' ? global : typeof self !== 'undefined' ? self : {};\n\nfunction isObject(what) {\n  return typeof what === 'object' && what !== null;\n}\n\n// Yanked from https://git.io/vS8DV re-used under CC0\n// with some tiny modifications\nfunction isError(value) {\n  switch ({}.toString.call(value)) {\n    case '[object Error]':\n      return true;\n    case '[object Exception]':\n      return true;\n    case '[object DOMException]':\n      return true;\n    default:\n      return value instanceof Error;\n  }\n}\n\nfunction isErrorEvent(value) {\n  return supportsErrorEvent() && {}.toString.call(value) === '[object ErrorEvent]';\n}\n\nfunction isUndefined(what) {\n  return what === void 0;\n}\n\nfunction isFunction(what) {\n  return typeof what === 'function';\n}\n\nfunction isString(what) {\n  return Object.prototype.toString.call(what) === '[object String]';\n}\n\nfunction isEmptyObject(what) {\n  for (var _ in what) return false; // eslint-disable-line guard-for-in, no-unused-vars\n  return true;\n}\n\nfunction supportsErrorEvent() {\n  try {\n    new ErrorEvent(''); // eslint-disable-line no-new\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\n\nfunction wrappedCallback(callback) {\n  function dataCallback(data, original) {\n    var normalizedData = callback(data) || data;\n    if (original) {\n      return original(normalizedData) || normalizedData;\n    }\n    return normalizedData;\n  }\n\n  return dataCallback;\n}\n\nfunction each(obj, callback) {\n  var i, j;\n\n  if (isUndefined(obj.length)) {\n    for (i in obj) {\n      if (hasKey(obj, i)) {\n        callback.call(null, i, obj[i]);\n      }\n    }\n  } else {\n    j = obj.length;\n    if (j) {\n      for (i = 0; i < j; i++) {\n        callback.call(null, i, obj[i]);\n      }\n    }\n  }\n}\n\nfunction objectMerge(obj1, obj2) {\n  if (!obj2) {\n    return obj1;\n  }\n  each(obj2, function(key, value) {\n    obj1[key] = value;\n  });\n  return obj1;\n}\n\n/**\n * This function is only used for react-native.\n * react-native freezes object that have already been sent over the\n * js bridge. We need this function in order to check if the object is frozen.\n * So it's ok that objectFrozen returns false if Object.isFrozen is not\n * supported because it's not relevant for other \"platforms\". See related issue:\n * https://github.com/getsentry/react-native-sentry/issues/57\n */\nfunction objectFrozen(obj) {\n  if (!Object.isFrozen) {\n    return false;\n  }\n  return Object.isFrozen(obj);\n}\n\nfunction truncate(str, max) {\n  return !max || str.length <= max ? str : str.substr(0, max) + '\\u2026';\n}\n\n/**\n * hasKey, a better form of hasOwnProperty\n * Example: hasKey(MainHostObject, property) === true/false\n *\n * @param {Object} host object to check property\n * @param {string} key to check\n */\nfunction hasKey(object, key) {\n  return Object.prototype.hasOwnProperty.call(object, key);\n}\n\nfunction joinRegExp(patterns) {\n  // Combine an array of regular expressions and strings into one large regexp\n  // Be mad.\n  var sources = [],\n    i = 0,\n    len = patterns.length,\n    pattern;\n\n  for (; i < len; i++) {\n    pattern = patterns[i];\n    if (isString(pattern)) {\n      // If it's a string, we need to escape it\n      // Taken from: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Regular_Expressions\n      sources.push(pattern.replace(/([.*+?^=!:${}()|\\[\\]\\/\\\\])/g, '\\\\$1'));\n    } else if (pattern && pattern.source) {\n      // If it's a regexp already, we want to extract the source\n      sources.push(pattern.source);\n    }\n    // Intentionally skip other cases\n  }\n  return new RegExp(sources.join('|'), 'i');\n}\n\nfunction urlencode(o) {\n  var pairs = [];\n  each(o, function(key, value) {\n    pairs.push(encodeURIComponent(key) + '=' + encodeURIComponent(value));\n  });\n  return pairs.join('&');\n}\n\n// borrowed from https://tools.ietf.org/html/rfc3986#appendix-B\n// intentionally using regex and not <a/> href parsing trick because React Native and other\n// environments where DOM might not be available\nfunction parseUrl(url) {\n  var match = url.match(/^(([^:\\/?#]+):)?(\\/\\/([^\\/?#]*))?([^?#]*)(\\?([^#]*))?(#(.*))?$/);\n  if (!match) return {};\n\n  // coerce to undefined values to empty string so we don't get 'undefined'\n  var query = match[6] || '';\n  var fragment = match[8] || '';\n  return {\n    protocol: match[2],\n    host: match[4],\n    path: match[5],\n    relative: match[5] + query + fragment // everything minus origin\n  };\n}\nfunction uuid4() {\n  var crypto = _window.crypto || _window.msCrypto;\n\n  if (!isUndefined(crypto) && crypto.getRandomValues) {\n    // Use window.crypto API if available\n    // eslint-disable-next-line no-undef\n    var arr = new Uint16Array(8);\n    crypto.getRandomValues(arr);\n\n    // set 4 in byte 7\n    arr[3] = (arr[3] & 0xfff) | 0x4000;\n    // set 2 most significant bits of byte 9 to '10'\n    arr[4] = (arr[4] & 0x3fff) | 0x8000;\n\n    var pad = function(num) {\n      var v = num.toString(16);\n      while (v.length < 4) {\n        v = '0' + v;\n      }\n      return v;\n    };\n\n    return (\n      pad(arr[0]) +\n      pad(arr[1]) +\n      pad(arr[2]) +\n      pad(arr[3]) +\n      pad(arr[4]) +\n      pad(arr[5]) +\n      pad(arr[6]) +\n      pad(arr[7])\n    );\n  } else {\n    // http://stackoverflow.com/questions/105034/how-to-create-a-guid-uuid-in-javascript/2117523#2117523\n    return 'xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\n      var r = (Math.random() * 16) | 0,\n        v = c === 'x' ? r : (r & 0x3) | 0x8;\n      return v.toString(16);\n    });\n  }\n}\n\n/**\n * Given a child DOM element, returns a query-selector statement describing that\n * and its ancestors\n * e.g. [HTMLElement] => body > div > input#foo.btn[name=baz]\n * @param elem\n * @returns {string}\n */\nfunction htmlTreeAsString(elem) {\n  /* eslint no-extra-parens:0*/\n  var MAX_TRAVERSE_HEIGHT = 5,\n    MAX_OUTPUT_LEN = 80,\n    out = [],\n    height = 0,\n    len = 0,\n    separator = ' > ',\n    sepLength = separator.length,\n    nextStr;\n\n  while (elem && height++ < MAX_TRAVERSE_HEIGHT) {\n    nextStr = htmlElementAsString(elem);\n    // bail out if\n    // - nextStr is the 'html' element\n    // - the length of the string that would be created exceeds MAX_OUTPUT_LEN\n    //   (ignore this limit if we are on the first iteration)\n    if (\n      nextStr === 'html' ||\n      (height > 1 && len + out.length * sepLength + nextStr.length >= MAX_OUTPUT_LEN)\n    ) {\n      break;\n    }\n\n    out.push(nextStr);\n\n    len += nextStr.length;\n    elem = elem.parentNode;\n  }\n\n  return out.reverse().join(separator);\n}\n\n/**\n * Returns a simple, query-selector representation of a DOM element\n * e.g. [HTMLElement] => input#foo.btn[name=baz]\n * @param HTMLElement\n * @returns {string}\n */\nfunction htmlElementAsString(elem) {\n  var out = [],\n    className,\n    classes,\n    key,\n    attr,\n    i;\n\n  if (!elem || !elem.tagName) {\n    return '';\n  }\n\n  out.push(elem.tagName.toLowerCase());\n  if (elem.id) {\n    out.push('#' + elem.id);\n  }\n\n  className = elem.className;\n  if (className && isString(className)) {\n    classes = className.split(/\\s+/);\n    for (i = 0; i < classes.length; i++) {\n      out.push('.' + classes[i]);\n    }\n  }\n  var attrWhitelist = ['type', 'name', 'title', 'alt'];\n  for (i = 0; i < attrWhitelist.length; i++) {\n    key = attrWhitelist[i];\n    attr = elem.getAttribute(key);\n    if (attr) {\n      out.push('[' + key + '=\"' + attr + '\"]');\n    }\n  }\n  return out.join('');\n}\n\n/**\n * Returns true if either a OR b is truthy, but not both\n */\nfunction isOnlyOneTruthy(a, b) {\n  return !!(!!a ^ !!b);\n}\n\n/**\n * Returns true if the two input exception interfaces have the same content\n */\nfunction isSameException(ex1, ex2) {\n  if (isOnlyOneTruthy(ex1, ex2)) return false;\n\n  ex1 = ex1.values[0];\n  ex2 = ex2.values[0];\n\n  if (ex1.type !== ex2.type || ex1.value !== ex2.value) return false;\n\n  return isSameStacktrace(ex1.stacktrace, ex2.stacktrace);\n}\n\n/**\n * Returns true if the two input stack trace interfaces have the same content\n */\nfunction isSameStacktrace(stack1, stack2) {\n  if (isOnlyOneTruthy(stack1, stack2)) return false;\n\n  var frames1 = stack1.frames;\n  var frames2 = stack2.frames;\n\n  // Exit early if frame count differs\n  if (frames1.length !== frames2.length) return false;\n\n  // Iterate through every frame; bail out if anything differs\n  var a, b;\n  for (var i = 0; i < frames1.length; i++) {\n    a = frames1[i];\n    b = frames2[i];\n    if (\n      a.filename !== b.filename ||\n      a.lineno !== b.lineno ||\n      a.colno !== b.colno ||\n      a['function'] !== b['function']\n    )\n      return false;\n  }\n  return true;\n}\n\n/**\n * Polyfill a method\n * @param obj object e.g. `document`\n * @param name method name present on object e.g. `addEventListener`\n * @param replacement replacement function\n * @param track {optional} record instrumentation to an array\n */\nfunction fill(obj, name, replacement, track) {\n  var orig = obj[name];\n  obj[name] = replacement(orig);\n  if (track) {\n    track.push([obj, name, orig]);\n  }\n}\n\nmodule.exports = {\n  isObject: isObject,\n  isError: isError,\n  isErrorEvent: isErrorEvent,\n  isUndefined: isUndefined,\n  isFunction: isFunction,\n  isString: isString,\n  isEmptyObject: isEmptyObject,\n  supportsErrorEvent: supportsErrorEvent,\n  wrappedCallback: wrappedCallback,\n  each: each,\n  objectMerge: objectMerge,\n  truncate: truncate,\n  objectFrozen: objectFrozen,\n  hasKey: hasKey,\n  joinRegExp: joinRegExp,\n  urlencode: urlencode,\n  uuid4: uuid4,\n  htmlTreeAsString: htmlTreeAsString,\n  htmlElementAsString: htmlElementAsString,\n  isSameException: isSameException,\n  isSameStacktrace: isSameStacktrace,\n  parseUrl: parseUrl,\n  fill: fill\n};\n", "var utils = require('../../src/utils');\n\n/*\n TraceKit - Cross brower stack traces\n\n This was originally forked from github.com/occ/TraceKit, but has since been\n largely re-written and is now maintained as part of raven-js.  Tests for\n this are in test/vendor.\n\n MIT license\n*/\n\nvar TraceKit = {\n  collectWindowErrors: true,\n  debug: false\n};\n\n// This is to be defensive in environments where window does not exist (see https://github.com/getsentry/raven-js/pull/785)\nvar _window =\n  typeof window !== 'undefined'\n    ? window\n    : typeof global !== 'undefined' ? global : typeof self !== 'undefined' ? self : {};\n\n// global reference to slice\nvar _slice = [].slice;\nvar UNKNOWN_FUNCTION = '?';\n\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error#Error_types\nvar ERROR_TYPES_RE = /^(?:[Uu]ncaught (?:exception: )?)?(?:((?:Eval|Internal|Range|Reference|Syntax|Type|URI|)Error): )?(.*)$/;\n\nfunction getLocationHref() {\n  if (typeof document === 'undefined' || document.location == null) return '';\n\n  return document.location.href;\n}\n\n/**\n * TraceKit.report: cross-browser processing of unhandled exceptions\n *\n * Syntax:\n *   TraceKit.report.subscribe(function(stackInfo) { ... })\n *   TraceKit.report.unsubscribe(function(stackInfo) { ... })\n *   TraceKit.report(exception)\n *   try { ...code... } catch(ex) { TraceKit.report(ex); }\n *\n * Supports:\n *   - Firefox: full stack trace with line numbers, plus column number\n *              on top frame; column number is not guaranteed\n *   - Opera:   full stack trace with line and column numbers\n *   - Chrome:  full stack trace with line and column numbers\n *   - Safari:  line and column number for the top frame only; some frames\n *              may be missing, and column number is not guaranteed\n *   - IE:      line and column number for the top frame only; some frames\n *              may be missing, and column number is not guaranteed\n *\n * In theory, TraceKit should work on all of the following versions:\n *   - IE5.5+ (only 8.0 tested)\n *   - Firefox 0.9+ (only 3.5+ tested)\n *   - Opera 7+ (only 10.50 tested; versions 9 and earlier may require\n *     Exceptions Have Stacktrace to be enabled in opera:config)\n *   - Safari 3+ (only 4+ tested)\n *   - Chrome 1+ (only 5+ tested)\n *   - Konqueror 3.5+ (untested)\n *\n * Requires TraceKit.computeStackTrace.\n *\n * Tries to catch all unhandled exceptions and report them to the\n * subscribed handlers. Please note that TraceKit.report will rethrow the\n * exception. This is REQUIRED in order to get a useful stack trace in IE.\n * If the exception does not reach the top of the browser, you will only\n * get a stack trace from the point where TraceKit.report was called.\n *\n * Handlers receive a stackInfo object as described in the\n * TraceKit.computeStackTrace docs.\n */\nTraceKit.report = (function reportModuleWrapper() {\n  var handlers = [],\n    lastArgs = null,\n    lastException = null,\n    lastExceptionStack = null;\n\n  /**\n     * Add a crash handler.\n     * @param {Function} handler\n     */\n  function subscribe(handler) {\n    installGlobalHandler();\n    handlers.push(handler);\n  }\n\n  /**\n     * Remove a crash handler.\n     * @param {Function} handler\n     */\n  function unsubscribe(handler) {\n    for (var i = handlers.length - 1; i >= 0; --i) {\n      if (handlers[i] === handler) {\n        handlers.splice(i, 1);\n      }\n    }\n  }\n\n  /**\n     * Remove all crash handlers.\n     */\n  function unsubscribeAll() {\n    uninstallGlobalHandler();\n    handlers = [];\n  }\n\n  /**\n     * Dispatch stack information to all handlers.\n     * @param {Object.<string, *>} stack\n     */\n  function notifyHandlers(stack, isWindowError) {\n    var exception = null;\n    if (isWindowError && !TraceKit.collectWindowErrors) {\n      return;\n    }\n    for (var i in handlers) {\n      if (handlers.hasOwnProperty(i)) {\n        try {\n          handlers[i].apply(null, [stack].concat(_slice.call(arguments, 2)));\n        } catch (inner) {\n          exception = inner;\n        }\n      }\n    }\n\n    if (exception) {\n      throw exception;\n    }\n  }\n\n  var _oldOnerrorHandler, _onErrorHandlerInstalled;\n\n  /**\n     * Ensures all global unhandled exceptions are recorded.\n     * Supported by Gecko and IE.\n     * @param {string} message Error message.\n     * @param {string} url URL of script that generated the exception.\n     * @param {(number|string)} lineNo The line number at which the error\n     * occurred.\n     * @param {?(number|string)} colNo The column number at which the error\n     * occurred.\n     * @param {?Error} ex The actual Error object.\n     */\n  function traceKitWindowOnError(message, url, lineNo, colNo, ex) {\n    var stack = null;\n\n    if (lastExceptionStack) {\n      TraceKit.computeStackTrace.augmentStackTraceWithInitialElement(\n        lastExceptionStack,\n        url,\n        lineNo,\n        message\n      );\n      processLastException();\n    } else if (ex && utils.isError(ex)) {\n      // non-string `ex` arg; attempt to extract stack trace\n\n      // New chrome and blink send along a real error object\n      // Let's just report that like a normal error.\n      // See: https://mikewest.org/2013/08/debugging-runtime-errors-with-window-onerror\n      stack = TraceKit.computeStackTrace(ex);\n      notifyHandlers(stack, true);\n    } else {\n      var location = {\n        url: url,\n        line: lineNo,\n        column: colNo\n      };\n\n      var name = undefined;\n      var msg = message; // must be new var or will modify original `arguments`\n      var groups;\n      if ({}.toString.call(message) === '[object String]') {\n        var groups = message.match(ERROR_TYPES_RE);\n        if (groups) {\n          name = groups[1];\n          msg = groups[2];\n        }\n      }\n\n      location.func = UNKNOWN_FUNCTION;\n\n      stack = {\n        name: name,\n        message: msg,\n        url: getLocationHref(),\n        stack: [location]\n      };\n      notifyHandlers(stack, true);\n    }\n\n    if (_oldOnerrorHandler) {\n      return _oldOnerrorHandler.apply(this, arguments);\n    }\n\n    return false;\n  }\n\n  function installGlobalHandler() {\n    if (_onErrorHandlerInstalled) {\n      return;\n    }\n    _oldOnerrorHandler = _window.onerror;\n    _window.onerror = traceKitWindowOnError;\n    _onErrorHandlerInstalled = true;\n  }\n\n  function uninstallGlobalHandler() {\n    if (!_onErrorHandlerInstalled) {\n      return;\n    }\n    _window.onerror = _oldOnerrorHandler;\n    _onErrorHandlerInstalled = false;\n    _oldOnerrorHandler = undefined;\n  }\n\n  function processLastException() {\n    var _lastExceptionStack = lastExceptionStack,\n      _lastArgs = lastArgs;\n    lastArgs = null;\n    lastExceptionStack = null;\n    lastException = null;\n    notifyHandlers.apply(null, [_lastExceptionStack, false].concat(_lastArgs));\n  }\n\n  /**\n     * Reports an unhandled Error to TraceKit.\n     * @param {Error} ex\n     * @param {?boolean} rethrow If false, do not re-throw the exception.\n     * Only used for window.onerror to not cause an infinite loop of\n     * rethrowing.\n     */\n  function report(ex, rethrow) {\n    var args = _slice.call(arguments, 1);\n    if (lastExceptionStack) {\n      if (lastException === ex) {\n        return; // already caught by an inner catch block, ignore\n      } else {\n        processLastException();\n      }\n    }\n\n    var stack = TraceKit.computeStackTrace(ex);\n    lastExceptionStack = stack;\n    lastException = ex;\n    lastArgs = args;\n\n    // If the stack trace is incomplete, wait for 2 seconds for\n    // slow slow IE to see if onerror occurs or not before reporting\n    // this exception; otherwise, we will end up with an incomplete\n    // stack trace\n    setTimeout(function() {\n      if (lastException === ex) {\n        processLastException();\n      }\n    }, stack.incomplete ? 2000 : 0);\n\n    if (rethrow !== false) {\n      throw ex; // re-throw to propagate to the top level (and cause window.onerror)\n    }\n  }\n\n  report.subscribe = subscribe;\n  report.unsubscribe = unsubscribe;\n  report.uninstall = unsubscribeAll;\n  return report;\n})();\n\n/**\n * TraceKit.computeStackTrace: cross-browser stack traces in JavaScript\n *\n * Syntax:\n *   s = TraceKit.computeStackTrace(exception) // consider using TraceKit.report instead (see below)\n * Returns:\n *   s.name              - exception name\n *   s.message           - exception message\n *   s.stack[i].url      - JavaScript or HTML file URL\n *   s.stack[i].func     - function name, or empty for anonymous functions (if guessing did not work)\n *   s.stack[i].args     - arguments passed to the function, if known\n *   s.stack[i].line     - line number, if known\n *   s.stack[i].column   - column number, if known\n *\n * Supports:\n *   - Firefox:  full stack trace with line numbers and unreliable column\n *               number on top frame\n *   - Opera 10: full stack trace with line and column numbers\n *   - Opera 9-: full stack trace with line numbers\n *   - Chrome:   full stack trace with line and column numbers\n *   - Safari:   line and column number for the topmost stacktrace element\n *               only\n *   - IE:       no line numbers whatsoever\n *\n * Tries to guess names of anonymous functions by looking for assignments\n * in the source code. In IE and Safari, we have to guess source file names\n * by searching for function bodies inside all page scripts. This will not\n * work for scripts that are loaded cross-domain.\n * Here be dragons: some function names may be guessed incorrectly, and\n * duplicate functions may be mismatched.\n *\n * TraceKit.computeStackTrace should only be used for tracing purposes.\n * Logging of unhandled exceptions should be done with TraceKit.report,\n * which builds on top of TraceKit.computeStackTrace and provides better\n * IE support by utilizing the window.onerror event to retrieve information\n * about the top of the stack.\n *\n * Note: In IE and Safari, no stack trace is recorded on the Error object,\n * so computeStackTrace instead walks its *own* chain of callers.\n * This means that:\n *  * in Safari, some methods may be missing from the stack trace;\n *  * in IE, the topmost function in the stack trace will always be the\n *    caller of computeStackTrace.\n *\n * This is okay for tracing (because you are likely to be calling\n * computeStackTrace from the function you want to be the topmost element\n * of the stack trace anyway), but not okay for logging unhandled\n * exceptions (because your catch block will likely be far away from the\n * inner function that actually caused the exception).\n *\n */\nTraceKit.computeStackTrace = (function computeStackTraceWrapper() {\n  // Contents of Exception in various browsers.\n  //\n  // SAFARI:\n  // ex.message = Can't find variable: qq\n  // ex.line = 59\n  // ex.sourceId = 580238192\n  // ex.sourceURL = http://...\n  // ex.expressionBeginOffset = 96\n  // ex.expressionCaretOffset = 98\n  // ex.expressionEndOffset = 98\n  // ex.name = ReferenceError\n  //\n  // FIREFOX:\n  // ex.message = qq is not defined\n  // ex.fileName = http://...\n  // ex.lineNumber = 59\n  // ex.columnNumber = 69\n  // ex.stack = ...stack trace... (see the example below)\n  // ex.name = ReferenceError\n  //\n  // CHROME:\n  // ex.message = qq is not defined\n  // ex.name = ReferenceError\n  // ex.type = not_defined\n  // ex.arguments = ['aa']\n  // ex.stack = ...stack trace...\n  //\n  // INTERNET EXPLORER:\n  // ex.message = ...\n  // ex.name = ReferenceError\n  //\n  // OPERA:\n  // ex.message = ...message... (see the example below)\n  // ex.name = ReferenceError\n  // ex.opera#sourceloc = 11  (pretty much useless, duplicates the info in ex.message)\n  // ex.stacktrace = n/a; see 'opera:config#UserPrefs|Exceptions Have Stacktrace'\n\n  /**\n     * Computes stack trace information from the stack property.\n     * Chrome and Gecko use this property.\n     * @param {Error} ex\n     * @return {?Object.<string, *>} Stack trace information.\n     */\n  function computeStackTraceFromStackProp(ex) {\n    if (typeof ex.stack === 'undefined' || !ex.stack) return;\n\n    var chrome = /^\\s*at (.*?) ?\\(((?:file|https?|blob|chrome-extension|native|eval|webpack|<anonymous>|[a-z]:|\\/).*?)(?::(\\d+))?(?::(\\d+))?\\)?\\s*$/i,\n      gecko = /^\\s*(.*?)(?:\\((.*?)\\))?(?:^|@)((?:file|https?|blob|chrome|webpack|resource|\\[native).*?|[^@]*bundle)(?::(\\d+))?(?::(\\d+))?\\s*$/i,\n      winjs = /^\\s*at (?:((?:\\[object object\\])?.+) )?\\(?((?:file|ms-appx|https?|webpack|blob):.*?):(\\d+)(?::(\\d+))?\\)?\\s*$/i,\n      // Used to additionally parse URL/line/column from eval frames\n      geckoEval = /(\\S+) line (\\d+)(?: > eval line \\d+)* > eval/i,\n      chromeEval = /\\((\\S*)(?::(\\d+))(?::(\\d+))\\)/,\n      lines = ex.stack.split('\\n'),\n      stack = [],\n      submatch,\n      parts,\n      element,\n      reference = /^(.*) is undefined$/.exec(ex.message);\n\n    for (var i = 0, j = lines.length; i < j; ++i) {\n      if ((parts = chrome.exec(lines[i]))) {\n        var isNative = parts[2] && parts[2].indexOf('native') === 0; // start of line\n        var isEval = parts[2] && parts[2].indexOf('eval') === 0; // start of line\n        if (isEval && (submatch = chromeEval.exec(parts[2]))) {\n          // throw out eval line/column and use top-most line/column number\n          parts[2] = submatch[1]; // url\n          parts[3] = submatch[2]; // line\n          parts[4] = submatch[3]; // column\n        }\n        element = {\n          url: !isNative ? parts[2] : null,\n          func: parts[1] || UNKNOWN_FUNCTION,\n          args: isNative ? [parts[2]] : [],\n          line: parts[3] ? +parts[3] : null,\n          column: parts[4] ? +parts[4] : null\n        };\n      } else if ((parts = winjs.exec(lines[i]))) {\n        element = {\n          url: parts[2],\n          func: parts[1] || UNKNOWN_FUNCTION,\n          args: [],\n          line: +parts[3],\n          column: parts[4] ? +parts[4] : null\n        };\n      } else if ((parts = gecko.exec(lines[i]))) {\n        var isEval = parts[3] && parts[3].indexOf(' > eval') > -1;\n        if (isEval && (submatch = geckoEval.exec(parts[3]))) {\n          // throw out eval line/column and use top-most line number\n          parts[3] = submatch[1];\n          parts[4] = submatch[2];\n          parts[5] = null; // no column when eval\n        } else if (i === 0 && !parts[5] && typeof ex.columnNumber !== 'undefined') {\n          // FireFox uses this awesome columnNumber property for its top frame\n          // Also note, Firefox's column number is 0-based and everything else expects 1-based,\n          // so adding 1\n          // NOTE: this hack doesn't work if top-most frame is eval\n          stack[0].column = ex.columnNumber + 1;\n        }\n        element = {\n          url: parts[3],\n          func: parts[1] || UNKNOWN_FUNCTION,\n          args: parts[2] ? parts[2].split(',') : [],\n          line: parts[4] ? +parts[4] : null,\n          column: parts[5] ? +parts[5] : null\n        };\n      } else {\n        continue;\n      }\n\n      if (!element.func && element.line) {\n        element.func = UNKNOWN_FUNCTION;\n      }\n\n      stack.push(element);\n    }\n\n    if (!stack.length) {\n      return null;\n    }\n\n    return {\n      name: ex.name,\n      message: ex.message,\n      url: getLocationHref(),\n      stack: stack\n    };\n  }\n\n  /**\n     * Adds information about the first frame to incomplete stack traces.\n     * Safari and IE require this to get complete data on the first frame.\n     * @param {Object.<string, *>} stackInfo Stack trace information from\n     * one of the compute* methods.\n     * @param {string} url The URL of the script that caused an error.\n     * @param {(number|string)} lineNo The line number of the script that\n     * caused an error.\n     * @param {string=} message The error generated by the browser, which\n     * hopefully contains the name of the object that caused the error.\n     * @return {boolean} Whether or not the stack information was\n     * augmented.\n     */\n  function augmentStackTraceWithInitialElement(stackInfo, url, lineNo, message) {\n    var initial = {\n      url: url,\n      line: lineNo\n    };\n\n    if (initial.url && initial.line) {\n      stackInfo.incomplete = false;\n\n      if (!initial.func) {\n        initial.func = UNKNOWN_FUNCTION;\n      }\n\n      if (stackInfo.stack.length > 0) {\n        if (stackInfo.stack[0].url === initial.url) {\n          if (stackInfo.stack[0].line === initial.line) {\n            return false; // already in stack trace\n          } else if (\n            !stackInfo.stack[0].line &&\n            stackInfo.stack[0].func === initial.func\n          ) {\n            stackInfo.stack[0].line = initial.line;\n            return false;\n          }\n        }\n      }\n\n      stackInfo.stack.unshift(initial);\n      stackInfo.partial = true;\n      return true;\n    } else {\n      stackInfo.incomplete = true;\n    }\n\n    return false;\n  }\n\n  /**\n     * Computes stack trace information by walking the arguments.caller\n     * chain at the time the exception occurred. This will cause earlier\n     * frames to be missed but is the only way to get any stack trace in\n     * Safari and IE. The top frame is restored by\n     * {@link augmentStackTraceWithInitialElement}.\n     * @param {Error} ex\n     * @return {?Object.<string, *>} Stack trace information.\n     */\n  function computeStackTraceByWalkingCallerChain(ex, depth) {\n    var functionName = /function\\s+([_$a-zA-Z\\xA0-\\uFFFF][_$a-zA-Z0-9\\xA0-\\uFFFF]*)?\\s*\\(/i,\n      stack = [],\n      funcs = {},\n      recursion = false,\n      parts,\n      item,\n      source;\n\n    for (\n      var curr = computeStackTraceByWalkingCallerChain.caller;\n      curr && !recursion;\n      curr = curr.caller\n    ) {\n      if (curr === computeStackTrace || curr === TraceKit.report) {\n        // console.log('skipping internal function');\n        continue;\n      }\n\n      item = {\n        url: null,\n        func: UNKNOWN_FUNCTION,\n        line: null,\n        column: null\n      };\n\n      if (curr.name) {\n        item.func = curr.name;\n      } else if ((parts = functionName.exec(curr.toString()))) {\n        item.func = parts[1];\n      }\n\n      if (typeof item.func === 'undefined') {\n        try {\n          item.func = parts.input.substring(0, parts.input.indexOf('{'));\n        } catch (e) {}\n      }\n\n      if (funcs['' + curr]) {\n        recursion = true;\n      } else {\n        funcs['' + curr] = true;\n      }\n\n      stack.push(item);\n    }\n\n    if (depth) {\n      // console.log('depth is ' + depth);\n      // console.log('stack is ' + stack.length);\n      stack.splice(0, depth);\n    }\n\n    var result = {\n      name: ex.name,\n      message: ex.message,\n      url: getLocationHref(),\n      stack: stack\n    };\n    augmentStackTraceWithInitialElement(\n      result,\n      ex.sourceURL || ex.fileName,\n      ex.line || ex.lineNumber,\n      ex.message || ex.description\n    );\n    return result;\n  }\n\n  /**\n     * Computes a stack trace for an exception.\n     * @param {Error} ex\n     * @param {(string|number)=} depth\n     */\n  function computeStackTrace(ex, depth) {\n    var stack = null;\n    depth = depth == null ? 0 : +depth;\n\n    try {\n      stack = computeStackTraceFromStackProp(ex);\n      if (stack) {\n        return stack;\n      }\n    } catch (e) {\n      if (TraceKit.debug) {\n        throw e;\n      }\n    }\n\n    try {\n      stack = computeStackTraceByWalkingCallerChain(ex, depth + 1);\n      if (stack) {\n        return stack;\n      }\n    } catch (e) {\n      if (TraceKit.debug) {\n        throw e;\n      }\n    }\n    return {\n      name: ex.name,\n      message: ex.message,\n      url: getLocationHref()\n    };\n  }\n\n  computeStackTrace.augmentStackTraceWithInitialElement = augmentStackTraceWithInitialElement;\n  computeStackTrace.computeStackTraceFromStackProp = computeStackTraceFromStackProp;\n\n  return computeStackTrace;\n})();\n\nmodule.exports = TraceKit;\n", "/*\n json-stringify-safe\n Like JSON.stringify, but doesn't throw on circular references.\n\n Originally forked from https://github.com/isaacs/json-stringify-safe\n version 5.0.1 on 3/8/2017 and modified to handle Errors serialization\n and IE8 compatibility. Tests for this are in test/vendor.\n\n ISC license: https://github.com/isaacs/json-stringify-safe/blob/master/LICENSE\n*/\n\nexports = module.exports = stringify;\nexports.getSerialize = serializer;\n\nfunction indexOf(haystack, needle) {\n  for (var i = 0; i < haystack.length; ++i) {\n    if (haystack[i] === needle) return i;\n  }\n  return -1;\n}\n\nfunction stringify(obj, replacer, spaces, cycleReplacer) {\n  return JSON.stringify(obj, serializer(replacer, cycleReplacer), spaces);\n}\n\n// https://github.com/ftlabs/js-abbreviate/blob/fa709e5f139e7770a71827b1893f22418097fbda/index.js#L95-L106\nfunction stringifyError(value) {\n  var err = {\n    // These properties are implemented as magical getters and don't show up in for in\n    stack: value.stack,\n    message: value.message,\n    name: value.name\n  };\n\n  for (var i in value) {\n    if (Object.prototype.hasOwnProperty.call(value, i)) {\n      err[i] = value[i];\n    }\n  }\n\n  return err;\n}\n\nfunction serializer(replacer, cycleReplacer) {\n  var stack = [];\n  var keys = [];\n\n  if (cycleReplacer == null) {\n    cycleReplacer = function(key, value) {\n      if (stack[0] === value) {\n        return '[Circular ~]';\n      }\n      return '[Circular ~.' + keys.slice(0, indexOf(stack, value)).join('.') + ']';\n    };\n  }\n\n  return function(key, value) {\n    if (stack.length > 0) {\n      var thisPos = indexOf(stack, this);\n      ~thisPos ? stack.splice(thisPos + 1) : stack.push(this);\n      ~thisPos ? keys.splice(thisPos, Infinity, key) : keys.push(key);\n\n      if (~indexOf(stack, value)) {\n        value = cycleReplacer.call(this, key, value);\n      }\n    } else {\n      stack.push(value);\n    }\n\n    return replacer == null\n      ? value instanceof Error ? stringifyError(value) : value\n      : replacer.call(this, key, value);\n  };\n}\n", "module.exports = window[\"jQuery\"];", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "import $ from 'jquery';\nimport { getOrCreateBackgroundApp, initBackgroundApp, } from '../utils/backgroundAppUtils';\nimport { domElements } from '../constants/selectors';\nimport { refreshToken, activationTime } from '../constants/leadinConfig';\nimport { ProxyMessages } from '../iframe/integratedMessages';\nconst REVIEW_BANNER_INTRO_PERIOD_DAYS = 15;\nconst userIsAfterIntroductoryPeriod = () => {\n    const activationDate = new Date(+activationTime * 1000);\n    const currentDate = new Date();\n    const timeElapsed = new Date(currentDate.getTime() - activationDate.getTime());\n    return timeElapsed.getUTCDate() - 1 >= REVIEW_BANNER_INTRO_PERIOD_DAYS;\n};\n/**\n * Adds some methods to window when review banner is\n * displayed to monitor events\n */\nexport function initMonitorReviewBanner() {\n    if (refreshToken) {\n        const embedder = getOrCreateBackgroundApp(refreshToken);\n        const container = $(domElements.reviewBannerContainer);\n        if (container && userIsAfterIntroductoryPeriod()) {\n            $(domElements.reviewBannerLeaveReviewLink)\n                .off('click')\n                .on('click', () => {\n                embedder.postMessage({\n                    key: ProxyMessages.TrackReviewBannerInteraction,\n                });\n            });\n            $(domElements.reviewBannerDismissButton)\n                .off('click')\n                .on('click', () => {\n                embedder.postMessage({\n                    key: ProxyMessages.TrackReviewBannerDismissed,\n                });\n            });\n            embedder\n                .postAsyncMessage({\n                key: ProxyMessages.FetchContactsCreateSinceActivation,\n                payload: +activationTime * 1000,\n            })\n                .then(({ total }) => {\n                if (total >= 5) {\n                    container.removeClass('leadin-review-banner--hide');\n                    embedder.postMessage({\n                        key: ProxyMessages.TrackReviewBannerRender,\n                    });\n                }\n            });\n        }\n    }\n}\ninitBackgroundApp(initMonitorReviewBanner);\n"], "names": ["_window$leadinConfig", "window", "leadinConfig", "accountName", "adminUrl", "activationTime", "connectionStatus", "deviceId", "didDisconnect", "env", "formsScript", "meetingsScript", "formsScriptPayload", "hublet", "hubspotBaseUrl", "hubspotNonce", "iframeUrl", "impactLink", "lastAuthorizeTime", "lastDeauthorizeTime", "lastDisconnectTime", "leadinPluginVersion", "leadinQueryParams", "locale", "loginUrl", "phpVersion", "pluginPath", "plugins", "portalDomain", "portalEmail", "portalId", "redirectNonce", "restNonce", "restUrl", "refreshToken", "reviewSkippedDate", "theme", "trackConsent", "wpVersion", "contentEmbed", "requiresContentEmbedScope", "decryptError", "dom<PERSON><PERSON>s", "iframe", "subMenu", "subMenuLinks", "subMenuButtons", "deactivatePluginButton", "deactivateFeedbackForm", "deactivateFeedbackSubmit", "deactivateFeedbackSkip", "thickboxModalClose", "thickboxModalWindow", "thickboxModalContent", "reviewBannerContainer", "reviewBannerLeaveReviewLink", "reviewBannerDismissButton", "leadinIframeContainer", "leadinIframe", "leadinIframeFallbackContainer", "CoreMessages", "Handshak<PERSON><PERSON><PERSON><PERSON><PERSON>", "SendRefreshToken", "ReloadParentFrame", "RedirectParentFrame", "SendLocale", "SendDeviceId", "SendIntegratedAppConfig", "FormMessages", "CreateFormAppNavigation", "LiveChatMessages", "CreateLiveChatAppNavigation", "PluginMessages", "PluginSettingsNavigation", "PluginLeadinConfig", "TrackConsent", "InternalTrackingFetchRequest", "InternalTrackingFetchResponse", "InternalTrackingFetchError", "InternalTrackingChangeRequest", "InternalTrackingChangeError", "BusinessUnitFetchRequest", "BusinessUnitFetchResponse", "BusinessUnitFetchError", "BusinessUnitChangeRequest", "BusinessUnitChangeError", "SkipReviewRequest", "SkipReviewResponse", "SkipReviewError", "RemoveParentQueryParam", "ContentEmbedInstallRequest", "ContentEmbedInstallResponse", "ContentEmbedInstallError", "ContentEmbedActivationRequest", "ContentEmbedActivationResponse", "ContentEmbedActivationError", "ProxyMappingsEnabledRequest", "ProxyMappingsEnabledResponse", "ProxyMappingsEnabledError", "ProxyMappingsEnabledChangeRequest", "ProxyMappingsEnabledChangeError", "RefreshProxyMappingsRequest", "RefreshProxyMappingsResponse", "RefreshProxyMappingsError", "ProxyMessages", "FetchForms", "FetchForm", "CreateFormFromTemplate", "GetTemplateAvailability", "FetchAuth", "FetchMeetingsAndUsers", "FetchContactsCreateSinceActivation", "FetchOrCreateMeetingUser", "ConnectMeetingsCalendar", "TrackFormPreviewRender", "TrackFormCreatedFromTemplate", "TrackFormCreationFailed", "TrackMeetingPreviewRender", "TrackSidebarMetaChange", "TrackReviewBannerRender", "TrackReviewBannerInteraction", "TrackReviewBannerDismissed", "TrackPluginDeactivation", "Raven", "configureRaven", "indexOf", "domain", "replace", "config", "concat", "instrument", "tryCatch", "shouldSendCallback", "data", "culprit", "test", "release", "install", "setTagsContext", "v", "php", "wordpress", "setExtraContext", "hub", "Object", "keys", "map", "name", "join", "$", "initApp", "initFn", "context", "initAppOnReady", "main", "initBackgroundApp", "Array", "isArray", "for<PERSON>ach", "callback", "getLeadinConfig", "getOrCreateBackgroundApp", "arguments", "length", "undefined", "LeadinBackgroundApp", "_window", "IntegratedAppEmbedder", "IntegratedAppOptions", "options", "setLocale", "setDeviceId", "setLeadinConfig", "setRefreshToken", "trim", "embedder", "setOptions", "attachTo", "document", "body", "postStartAppMessage", "REVIEW_BANNER_INTRO_PERIOD_DAYS", "userIsAfterIntroductoryPeriod", "activationDate", "Date", "currentDate", "timeElapsed", "getTime", "getUTCDate", "initMonitorReviewBanner", "container", "off", "on", "postMessage", "key", "postAsyncMessage", "payload", "then", "_ref", "total", "removeClass"], "sourceRoot": ""}