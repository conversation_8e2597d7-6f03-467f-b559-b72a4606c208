{"version": 3, "file": "leadin.css", "mappings": ";;;AAI6BA,UAAAA,mBAAAA,CAAAA,oBAAAA,CAAAA,mBAAAA,CAAAA,YAAAA,CAAAA,6BAAAA,CAAAA,yBAAAA,CAAAA,qBAAAA,CAAAA,0BAAAA,CAAAA,wBAAAA,CAAAA,qBAAAA,CAAAA,kBAAAA,CAAAA,uBAAAA,CAAAA,8BAAAA,CAAAA,oBAAAA,CAAAA,sBAAAA,CAAAA,gBAAAA,CAAAA,oDAAAA,CAAAA,eAAAA,CAAAA,cAAAA,CAAAA,kBAAAA,CAAAA,kCAAAA,CAAAA,iCAAAA,CAAAA,0BAAAA,CAAAA,kBAAAA,CAAAA;AAeTC,UAAAA,+BAAAA,CAAAA,qBAAAA,CAAAA,aAAAA,CAAAA,iBAAAA,CAAAA;ACjBpB,usEAAusE,C", "sources": ["webpack://leadin/./scripts/iframe/IframeErrorPage.tsx", "webpack://leadin/./scripts/iframe/IframeErrorPage.tsx"], "sourcesContent": ["import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport React from 'react';\nimport { __ } from '@wordpress/i18n';\nimport { styled } from '@linaria/react';\nconst IframeErrorContainer = styled.div `\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  margin-top: 120px;\n  font-family: 'Lexend Deca', Helvetica, Arial, sans-serif;\n  font-weight: 400;\n  font-size: 14px;\n  font-size: 0.875rem;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  font-smoothing: antialiased;\n  line-height: 1.5rem;\n`;\nconst ErrorHeader = styled.h1 `\n  text-shadow: 0 0 1px transparent;\n  margin-bottom: 1.25rem;\n  color: #33475b;\n  font-size: 1.25rem;\n`;\nexport const IframeErrorPage = () => (_jsxs(IframeErrorContainer, { children: [_jsx(\"img\", { alt: \"Cannot find page\", width: \"175\", src: \"//static.hsappstatic.net/ui-images/static-1.14/optimized/errors/map.svg\" }), _jsx(ErrorHeader, { children: __('The HubSpot for WordPress plugin is not able to load pages', 'leadin') }), _jsx(\"p\", { children: __('Try disabling your browser extensions and ad blockers, then refresh the page', 'leadin') }), _jsx(\"p\", { children: __('Or open the HubSpot for WordPress plugin in a different browser', 'leadin') })] }));\n", ".i1jit3y0{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;margin-top:120px;font-family:'Lexend Deca',Helvetica,Arial,sans-serif;font-weight:400;font-size:14px;font-size:0.875rem;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;font-smoothing:antialiased;line-height:1.5rem;}\n.e12lu7tb{text-shadow:0 0 1px transparent;margin-bottom:1.25rem;color:#33475b;font-size:1.25rem;}\n/*# sourceMappingURL=data:application/json;base64,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*/"], "names": [".i1jit3y0", ".e12lu7tb"], "sourceRoot": ""}