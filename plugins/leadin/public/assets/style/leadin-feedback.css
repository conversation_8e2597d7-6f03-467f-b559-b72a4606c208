.leadin-feedback-window {
  background-color: #fff;
  font-family: 'Lexend Deca', Helvetica, Arial, sans-serif;
  font-size: 16px;
  color: #33475b;
  width: 524px !important; /* need to override the inline styling thickbox applies */
  height: 600px !important;
  margin-left: 0px !important;
  transform: translateX(-50%);
  top: 30px !important;
  margin-top: 0px !important;
  visibility: visible !important;
}

.leadin-feedback-window strong {
  font-weight: 550;
}

.leadin-feedback-content {
  width: auto !important; /* need to override the inline styling thickbox applies */
  height: auto !important;
  background-color: #fff;
}

.leadin-radio-input-container {
  margin-top: 20px;
}

.leadin-deactivate-form {
  margin-top: 30px;
}

.leadin-input,
.leadin-input[type],
.leadin-feedback-text-area {
  border-radius: 3px;
  border: 1px solid #cbd6e2;
  background-color: #f5f8fa;
  margin-top: 10px;
  padding: 9px 10px;
  width: 100%;
}

.leadin-input:focus,
.leadin-input[type]:focus,
.leadin-feedback-text-area:focus {
  border-color: rgba(0, 208, 228, 0.5);
  box-shadow: 0 0 4px 1px rgba(0, 208, 228, 0.3), 0 0 0 1px #00d0e4;
  outline: 0;
}

.leadin-feedback-text-area {
  margin-bottom: 30px;
  text-align: left;
  vertical-align: middle;

  transition-property: all;
  transition-duration: 0.15s;
  transition-timing-function: ease-out;
  transition-delay: 0s;
}

input[type='radio']:checked:before.leadin-feedback-radio {
  background-color: #00a4bd;
}

input[type='radio']:focus.leadin-feedback-radio {
  border-color: #00a4bd;
}

.leadin-button-container {
  margin-top: 30px;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
}

.leadin-button-container .leadin-button {
  margin-left: 12px;
}

.leadin-feedback-header {
  background-color: #00bda5;
  background-image: linear-gradient(-303deg, #00a4bd, #00afb2 56%, #00bda5);
  position: absolute;
  top: 0px;
  left: 0px;
  width: 100%;

  align-items: center;
  min-height: 80px;
}

.leadin-feedback-header h2 {
  color: #fff;
  font-weight: 500;
  font-size: 20px;
  line-height: 40px;
  margin-left: 50px;
}

.leadin-feedback-body {
  margin-top: 80px;
  padding: 10px 20px;
}

.leadin-modal-close {
  color: #fff;
  display: flex;
  flex-shrink: 0;
  justify-content: center;
  line-height: normal;
  position: absolute;
  top: 22px;
  right: 22px;
  height: 16px;
  width: 16px;
  z-index: 1;
  transition: color 150ms ease-out 0s;
  cursor: pointer;
}

.leadin-close-svg {
  display: block;
  -webkit-box-flex: 1;
  flex-grow: 1;
}

.leadin-close-path {
  fill: currentcolor;
  stroke: currentcolor;
  stroke-width: 2;
}

.leadin-modal-close::after {
  padding: 20px;
  content: ' ';
  left: 50%;
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  border-radius: 100%;
  padding: 20px;
  transition-property: background-color;
  transition-duration: 150ms;
  transition-timing-function: ease-out;
  transition-delay: 0s;
}

.leadin-modal-close:hover::after {
  background-color: rgba(255, 255, 255, 0.1);
}

.leadin-loader {
  opacity: 0;
  position: absolute;
  top: 18%;
  left: 43%;
  border-top: 0.2em solid #ffbcac;
  border-right: 0.2em solid #ffbcac;
  border-bottom: 0.2em solid #ffbcac;
  border-left: 0.2em solid #ff7a59;
  border-radius: 50%;
  width: 1.5em;
  height: 1.5em;
  -webkit-transform: translateZ(0);
  -ms-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-animation: load8 1.1s infinite linear;
  animation: load8 1.1s infinite linear;
}
@-webkit-keyframes load8 {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@keyframes load8 {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

.leadin-loader-button.loading .leadin-loader {
  opacity: 1;
}

.leadin-loader-button.loading .leadin-loader-button-content {
  opacity: 0;
}
