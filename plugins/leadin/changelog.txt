= 11.3.6 (2025-04-22) =
* Have default embedVersion for elementor

= 11.3.0 (2025-04-07) =
* Add v4 forms support

= 11.2.8 (2025-03-17) =
* Move cron schedule action to proxy mapping class

= 11.2.0 (2025-03-05) =
* Add reverse proxy to HubSpot domain feature

= 11.1.83 (2025-01-24) =
* Validate refreshToken value before starting app

= 11.1.82 (2025-01-23) =
* Change Raven URL for error capture

= 11.1.75 (2024-11-29) =
* Add Salt/key config global to avoid disconnection in case secret changes

= 11.1.73 (2024-11-19) =
* Remove unknow wp_json_decode function

= 11.1.66 (2024-10-23) =
* Register gutenberg blocks at PHP side

= 11.1.34 (2024-07-18) =
* Fix XSS injection issue with Elementor meetings widget

= 11.1.33 (2024-07-18) =
* Filter exceptions captured from external files

= 11.1.30 (2024-07-10) =
* Bump tested up to WordPress version

= 11.1.27 (2024-06-25) =
* Remove need for refresh token when submitting deactivation feedback

= 11.1.26 (2024-06-24) =
* Add stylesheet loader bondary for gutenberg blocks

= 11.1.22 (2024-06-17) =
* Add error labels to help diagnose connection issues

= 11.1.21 (2024-06-04) =
* Improve load performance by removing redundant retry logic

= 11.1.20 (2024-05-31) =
* Remove extra logs

= 11.1.19 (2024-05-31) =
* Add AppIntegrationConfig

= 11.1.18 (2024-05-30) =
* Add default value in connection process

= 11.1.15 (2024-05-30) =
* Revert breaking changes in 11.1.13 related to the oauth connection

= 11.1.14 (2024-05-30) =
* Add apiVersion to gutenberg blocks

= 11.1.13 (2024-05-23) =
* Add error labels for connection process

= 11.1.11 (2024-05-16) =
* Add another field to integrated app config

= 11.1.10 (2024-05-15) =
* fix race condition with our Elementor widgets which would occasionally prevent them from loading.

= 11.1.9 (2024-05-15) =
* Add extra data to integrated app config

= 11.1.5 (2024-04-19) =
* Pushing new plugin version with tagging mechanism working correctly.

= 11.1.3 (2024-04-17) =
* Optimize metadata update on connection

= 11.1.0 (2024-04-16) =
* Addressing issue with our SVN tagging

= 11.0.57 (2024-04-16) =
* Update logic that is used to disable blocks in the full site editor

= 11.0.55 (2024-04-12) =
* Remove support for full site editor for forms and meetings blocks

= 11.0.37 (2024-04-10) =
* Fix translation keys

= 11.0.36 (2024-04-09) =
* Refactor connection function and add extra leadin config

= 11.0.32 (2024-03-27) =
* Update the gutenberg save blocks for wordpress customizer compatibility

= 11.0.28 (2024-03-22) =
* Fix for the HubSpot gutenberg blogs rendered in the wordpress customizer context

= 11.0.23 (2024-03-14) =
* Fix error when adding the wordpress sidebar

= 11.0.15 (2024-03-13) =
* Add better error logging

= 11.0.9 (2024-03-12) =
* Translations fixed

= 11.0.8 (2024-03-04) =
* Update review banner display logic

= 11.0.5 (2024-02-29) =
* Adds ability to delete query param from embedded app

= 11.0.4 (2024-02-26) =
* Adds version info to leadin config

= 11.0.3 (2024-02-20) =
* Remove unnecessary "type" attribute from javascript resources

= 11.0.0 (2024-02-13) =
* Fix locale bug
* Integrated Plugin App embedder
* Remove 3rd party cookies dependency

= 10.2.23 (2024-01-24) =
* Update link of the review survey

= 10.2.17 (2023-12-13) =
* Change minimum required version in plugin listing

= 10.2.14 (2023-11-15) =
* Remove deprecated block_categories method

= 10.2.13 (2023-11-13) =
* Bump WP tested version and fix feedback banner show up condition

= 10.2.3 (2023-08-23) =
* Sets device id in embedder options

= 10.2.0 (2023-08-09) =
* Live chat app embedder

= 10.1.30 (2023-07-24) =
* Updated readme

= 10.1.28 (2023-07-11) =
* Removes unused jQuery dependency

= 10.1.24 (2023-05-31) =
* Add Norwegian language support

= 10.1.23 (2023-05-17) =
* Fixed form creation by template

= 10.1.16 (2023-04-11) =
* Updated tested up to WordPress version and HubSpot video

= 10.1.13 (2023-03-30) =
* Add url sanitizing for meetings shortcode

= 10.1.0 (2023-03-08) =
* Forms app embedder

= 10.0.24 (2023-03-07) =
* Change font type

= 10.0.21 (2023-02-20) =
* Content type default selection for elementor

= 10.0.18 (2023-01-20) =
* Add business unit proxy

= 10.0.17 (2023-01-19) =
* Add loader animation to prevent blank screen

= 10.0.7 (2023-01-10) =
* Remove getHublet endpoint

= 10.0.0 (2023-01-06) =
* Plugin restructure to use WordPress env and scripts

= 9.2.26 (2022-12-08) =
* Update feedback survey link

= 9.2.0 (2022-12-01) =
* Add support for Business Units

= 9.1.0 (2022-11-30) =
* Make list link external

= 9.0.499 (2022-11-25) =
* Fix custom post type validation for sidebar

= 9.0.497 (2022-11-24) =
* Added fallback page if iframe is blocked

= 9.0.469 (2022-11-21) =
* Replace null parameter for http_build_query method

= 9.0.432 (2022-11-17) =
* Migrate to TypeScript

= 9.0.405 (2022-11-14) =
* Add validation to metadata access

= 9.0.387 (2022-11-01) =
* Use WP set script translations

= 9.0.365 (2022-10-28) =
* Move ajax to rest endpoints

= 9.0.320 (2022-10-24) =
* Make report link external

= 9.0.311 (2022-10-21) =
* Fix for form select sometimes not working in form Gutenberg block

= 9.0.272 (2022-10-12) =
* Fix for Penpal to connect to correct child when Impact Link is present

= 9.0.108 (2022-09-14) =
* Enqueue Meetings script

= 9.0.91 (2022-09-13) =
* Changed the placeholder text for form blocks

= 9.0.77 (2022-09-12) =
* Change review link to external survey

= 9.0.74 (2022-09-09) =
* Add filter to pass additional query parameters to iframe

= 9.0.72 (2022-09-09) =
* Prevent sidebar from rendering when not post or page

= 9.0.20 (2022-08-30) =
* Remove tests folder and composer.phar file

= 9.0.0 (2022-08-29) =
* Added HusbSpot Elementor widgets

= 8.16.25 (2022-08-19) =
* Fixes the headers missing issue by tying the script with a hook

= 8.16.6 (2022-08-17) =
* Revert changes for open upgrade link in new tab

= 8.16.5 (2022-08-17) =
* Removed newline before/after php tag

= 8.16.2 (2022-08-17) =
* Fixes the headers already sent error

= 8.16.0 (2022-08-17) =
* Made upgrade link open up in a new tab instead of the iframe

= 8.15.139 (2022-08-04) =
* Fix for content type customization

= 8.15.65 (2022-07-22) =
* Fix encode URL for affiliate link

= 8.15.36 (2022-07-19) =
* Add validation for content type in meta data

= 8.15.0 (2022-07-15) =
* Add hubspot sidebar to set content type

= 8.14.24 (2022-07-15) =
* Add fallback for affiliate link
* Increase WP tested up to version

= 8.14.0 (2022-07-13) =
* Add Meetings gutenberg block

= 8.13.56 (2022-06-10) =
* Use new script to render forms

= 8.13.52 (2022-06-09) =
* Add defer to forms script

= 8.13.45 (2022-06-08) =
* Remove char from proxy url

= 8.13.39 (2022-06-07) =
* Add tracking of events in review banner

= 8.13.0 (2022-06-01) =
* Add validation to connect method

= 8.12.33 (2022-05-27) =
* Replaced some whitelisted URLs with regex

= 8.12.0 (2022-05-24) =
* Added Gutenberg block preview image

= 8.11.221 (2022-05-24) =
* Update dependencies

