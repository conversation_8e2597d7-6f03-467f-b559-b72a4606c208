{"name": "leadin", "scripts": {"start": "wp-scripts start", "build": "wp-scripts build --stats-error-details", "test-e2e": "wp-scripts test-e2e", "test-e2e:debug": "wp-scripts test-e2e --puppeteer-devtools", "plugin-zip": "wp-scripts plugin-zip"}, "files": ["/public/**", "/build/**", "/languages/*", "/scripts/**/*", "vendor/*", "changelog.txt", "leadin.php", "uninstall.php", "phpunit.xml.dist", "license.txt", "readme.txt"], "devDependencies": {"@babel/preset-typescript": "^7.18.6", "@linaria/babel-preset": "4.0.0-beta.0", "@linaria/core": "4.0.0-beta.0", "@linaria/react": "^4.0.0-beta.0", "@linaria/shaker": "4.0.0-beta.0", "@types/jest": "^29.2.4", "@types/jquery": "^3.5.14", "@types/lodash": "^4.14.188", "@types/styled-components": "^5.1.26", "@types/wordpress__block-editor": "^7.0.0", "@types/wordpress__blocks": "^11.0.7", "@types/wordpress__components": "^23.8.0", "@types/wordpress__data": "^7.0.0", "@types/wordpress__edit-post": "^4.0.1", "@types/wordpress__keycodes": "^2.18.0", "@types/wordpress__plugins": "^3.0.0", "@wordpress/e2e-test-utils": "^8.2.0", "@wordpress/env": "^5.3.0", "@wordpress/jest-preset-default": "^10.0.0", "@wordpress/scripts": "^24.2.0", "eslint-plugin-jsdoc": "^48.9.3", "jest": "^29.1.2", "msw": "^0.49.2", "ts-loader": "^9.4.1", "typescript": "^4.8.4", "ws": "8.17.1"}, "resolutions": {"ws": "8.17.1"}, "dependencies": {"@babel/cli": "7.18.6", "@babel/core": "7.18.6", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/preset-env": "7.18.6", "@babel/preset-react": "7.18.6", "@linaria/webpack-loader": "4.0.0-beta.0", "@wordpress/block-editor": "^10.1.0", "@wordpress/blocks": "^11.17.0", "@wordpress/components": "^28.4.0", "@wordpress/edit-post": "^6.18.0", "@wordpress/i18n": "^4.18.0", "@wordpress/plugins": "^4.19.0", "babel-loader": "8.2.5", "css-loader": "6.7.1", "mini-css-extract-plugin": "2.6.1", "penpal": "3.1.3", "raven-js": "3.19.1", "styled-components": "^4.3.2", "webpack": "5.76.0", "webpack-cli": "4.10.0"}, "msw": {"workerDirectory": ""}}