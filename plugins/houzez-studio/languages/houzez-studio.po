# This file was generated by WPM<PERSON>
# WPML is a WordPress plugin that can turn any WordPress or WordPressMU site into a full featured multilingual content management system.
# https://wpml.org
msgid ""
msgstr ""
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Project-Id-Version:WPML_EXPORT\n"
"POT-Creation-Date: \n"
"PO-Revision-Date: \n"
"Last-Translator: \n"
"Language-Team: \n"
"Language:en\n"
"MIME-Version: 1.0\n"

# 
# $message = sprintf( __( 'The %1$sHouzez Studio%2$s plugin requires %1$sElementor%2$s plugin installed & activated.', 'houzez-studio' ), '<strong>', '</strong>' );
# 

msgid "The %1$sHouzez Studio%2$s plugin requires %1$sElementor%2$s plugin installed & activated."
msgstr ""

# $action_url   = wp_nonce_url( 'plugins.php?action=activate&amp;plugin=' . $plugin . '&amp;plugin_status=all&amp;paged=1&amp;s', 'activate-plugin_' . $plugin );
# $button_label = __( 'Activate Elementor', 'houzez-studio' );
# 

msgid "Activate Elementor"
msgstr ""

# $action_url   = wp_nonce_url( self_admin_url( 'update.php?action=install-plugin&plugin=elementor' ), 'install-plugin_elementor' );
# $button_label = __( 'Install Elementor', 'houzez-studio' );
# }

msgid "Install Elementor"
msgstr ""

# $class = 'notice notice-error';
# $message = sprintf( __( 'The %1$sHouzez Studio%2$s plugin has stopped working because you are using an older version of %1$sElementor%2$s plugin.', 'houzez-studio' ), '<strong>', '</strong>' );
# 

msgid "The %1$sHouzez Studio%2$s plugin has stopped working because you are using an older version of %1$sElementor%2$s plugin."
msgstr ""

# $action_url   = wp_nonce_url( self_admin_url( 'update.php?action=upgrade-plugin&amp;plugin=' ) . $plugin . '&amp;', 'upgrade-plugin_' . $plugin );
# $button_label = __( 'Update Elementor', 'houzez-studio' );
# 

msgid "Update Elementor"
msgstr ""

# public function __clone() {
# _doing_it_wrong( __FUNCTION__, __( 'Cloning is forbidden.', 'houzez-studio' ), '1.0' );
# }

msgid "Cloning is forbidden."
msgstr ""

# public function __wakeup() {
# _doing_it_wrong( __FUNCTION__, __( 'Unserializing instances of this class is forbidden.', 'houzez-studio' ), '1.0' );
# }

msgid "Unserializing instances of this class is forbidden."
msgstr ""

# $labels = array(
# 'name' => __( 'Header & Footer Builder','houzez-studio'),
# 'singular_name' => __( 'Favethemes Builder','houzez-studio' ),

msgid "Header & Footer Builder"
msgstr ""

# 'name' => __( 'Header & Footer Builder','houzez-studio'),
# 'singular_name' => __( 'Favethemes Builder','houzez-studio' ),
# 'add_new_item' => __('Add New Header, Footer','houzez-studio'),

msgid "Favethemes Builder"
msgstr ""

# 'singular_name' => __( 'Favethemes Builder','houzez-studio' ),
# 'add_new_item' => __('Add New Header, Footer','houzez-studio'),
# 'edit_item' => __('Edit Header, Footer','houzez-studio'),

msgid "Add New Header, Footer"
msgstr ""

# 'add_new_item' => __('Add New Header, Footer','houzez-studio'),
# 'edit_item' => __('Edit Header, Footer','houzez-studio'),
# 'all_items'     => esc_html__( 'All Header, Footer', 'houzez-studio' ),

msgid "Edit Header, Footer"
msgstr ""

# unset($columns['date']);
# $columns['type'] = __('Type', 'houzez-studio');
# $columns['display_rules'] = __('Display Rules', 'houzez-studio');

msgid "Type"
msgstr ""

# $columns['type'] = __('Type', 'houzez-studio');
# $columns['display_rules'] = __('Display Rules', 'houzez-studio');
# $columns['shortcode'] = __('Shortcode', 'houzez-studio');

msgid "Display Rules"
msgstr ""

# $columns['display_rules'] = __('Display Rules', 'houzez-studio');
# $columns['shortcode'] = __('Shortcode', 'houzez-studio');
# $columns['date'] = $date_column;

msgid "Shortcode"
msgstr ""

# 'edit_item' => __('Edit Header, Footer','houzez-studio'),
# 'all_items'     => esc_html__( 'All Header, Footer', 'houzez-studio' ),
# );

msgid "All Header, Footer"
msgstr ""

# 'rule_type'      => 'display',
# 'button_label' => __( 'Create Display Rule', 'houzez-studio' ),
# ],

msgid "Create Display Rule"
msgstr ""

# 'rule_type'      => 'exclude',
# 'button_label' => __( 'Create Exlcude Rule', 'houzez-studio' ),
# ],

msgid "Create Exlcude Rule"
msgstr ""

# <th scope="row">
# <label for="select_id"><?php _e( 'Template Type', 'houzez-studio' ); ?></label>
# </th>

msgid "Template Type"
msgstr ""

# <select name="fts_template_type" id="fts_template_type">
# <option value="" <?php selected( $template_type, '' ); ?>><?php _e( 'Select Option', 'houzez-studio' ); ?></option>
# 

msgid "Select Option"
msgstr ""

# 
# <option value="tmp_header" <?php selected( $template_type, 'tmp_header' ); ?>><?php _e( 'Header', 'houzez-studio' ); ?></option>
# 

msgid "Header"
msgstr ""

# 
# <option value="tmp_before_header" <?php selected( $template_type, 'tmp_before_header' ); ?>><?php _e( 'Before Header', 'houzez-studio' ); ?></option>
# 

msgid "Before Header"
msgstr ""

# 
# <option value="tmp_after_header" <?php selected( $template_type, 'tmp_after_header' ); ?>><?php _e( 'After Header', 'houzez-studio' ); ?></option>
# 

msgid "After Header"
msgstr ""

# 
# <option value="tmp_footer" <?php selected( $template_type, 'tmp_footer' ); ?>><?php _e( 'Footer', 'houzez-studio' ); ?></option>
# <option value="tmp_after_footer" <?php selected( $template_type, 'tmp_after_footer' ); ?>><?php _e( 'After Footer', 'houzez-studio' ); ?></option>

msgid "Footer"
msgstr ""

# <option value="tmp_footer" <?php selected( $template_type, 'tmp_footer' ); ?>><?php _e( 'Footer', 'houzez-studio' ); ?></option>
# <option value="tmp_after_footer" <?php selected( $template_type, 'tmp_after_footer' ); ?>><?php _e( 'After Footer', 'houzez-studio' ); ?></option>
# 

msgid "After Footer"
msgstr ""

# 
# <option value="tmp_before_footer" <?php selected( $template_type, 'tmp_before_footer' ); ?>><?php _e( 'Before Footer', 'houzez-studio' ); ?></option>
# 

msgid "Before Footer"
msgstr ""

# 
# <option value="tmp_megamenu" <?php selected( $template_type, 'tmp_megamenu' ); ?>><?php _e( 'Mega Menu', 'houzez-studio' ); ?></option>
# 

msgid "Mega Menu"
msgstr ""

# 
# <option value="tmp_custom_block" <?php selected( $template_type, 'tmp_custom_block' ); ?>><?php _e( 'Custom Block', 'houzez-studio' ); ?></option>
# </select>

msgid "Custom Block"
msgstr ""

# <label for="fts_included_display_rules">
# <?php esc_html_e( 'Display Location', 'houzez-studio' ); ?>
# </label>

msgid "Display Location"
msgstr ""

# <label for="fts_included_display_rules">
# <?php esc_html_e( 'Exlcude Location', 'houzez-studio' ); ?>
# </label>

msgid "Exlcude Location"
msgstr ""

# 'standard' => array(
# 'label' => __('Standard', 'houzez-studio'),
# 'value' => array(

msgid "Standard"
msgstr ""

# 'value' => array(
# 'standard-global'    => __('Entire Website', 'houzez-studio'),
# 'standard-singulars' => __('All Singulars', 'houzez-studio'),

msgid "Entire Website"
msgstr ""

# 'standard-global'    => __('Entire Website', 'houzez-studio'),
# 'standard-singulars' => __('All Singulars', 'houzez-studio'),
# 'standard-archives'  => __('All Archives', 'houzez-studio'),

msgid "All Singulars"
msgstr ""

# 'standard-singulars' => __('All Singulars', 'houzez-studio'),
# 'standard-archives'  => __('All Archives', 'houzez-studio'),
# ),

msgid "All Archives"
msgstr ""

# 'unique-pages' => array(
# 'label' => __('Special Pages', 'houzez-studio'),
# 'value' => $special_pages,

msgid "Special Pages"
msgstr ""

# 'specific-selection' => array(
# 'label' => __('Specific Selection', 'houzez-studio'),
# 'value' => array('specifics' => __('Specific Pages / Posts / Taxonomies, etc.', 'houzez-studio')),

msgid "Specific Selection"
msgstr ""

# 'label' => __('Specific Selection', 'houzez-studio'),
# 'value' => array('specifics' => __('Specific Pages / Posts / Taxonomies, etc.', 'houzez-studio')),
# ),

msgid "Specific Pages / Posts / Taxonomies, etc."
msgstr ""

# $special_pages = array(
# 'unique-all-listings'    => __('All Listings Pages', 'houzez-studio'),
# 'unique-listings-search'    => __('Listings Search Page', 'houzez-studio'),

msgid "All Listings Pages"
msgstr ""

# 'unique-all-listings'    => __('All Listings Pages', 'houzez-studio'),
# 'unique-listings-search'    => __('Listings Search Page', 'houzez-studio'),
# 'unique-all-agents'    => __('All Agents Page', 'houzez-studio'),

msgid "Listings Search Page"
msgstr ""

# 'unique-listings-search'    => __('Listings Search Page', 'houzez-studio'),
# 'unique-all-agents'    => __('All Agents Page', 'houzez-studio'),
# 'unique-all-agencies'    => __('All Agencies Page', 'houzez-studio'),

msgid "All Agents Page"
msgstr ""

# 'unique-all-agents'    => __('All Agents Page', 'houzez-studio'),
# 'unique-all-agencies'    => __('All Agencies Page', 'houzez-studio'),
# 'unique-404'    => __('404 Page', 'houzez-studio'),

msgid "All Agencies Page"
msgstr ""

# 'unique-all-agencies'    => __('All Agencies Page', 'houzez-studio'),
# 'unique-404'    => __('404 Page', 'houzez-studio'),
# 'unique-search' => __('Blog / Search Page', 'houzez-studio'),

msgid "404 Page"
msgstr ""

# 'unique-404'    => __('404 Page', 'houzez-studio'),
# 'unique-search' => __('Blog / Search Page', 'houzez-studio'),
# 'unique-blog'   => __('Blog / Posts Page', 'houzez-studio'),

msgid "Blog / Search Page"
msgstr ""

# 'unique-search' => __('Blog / Search Page', 'houzez-studio'),
# 'unique-blog'   => __('Blog / Posts Page', 'houzez-studio'),
# 'unique-front'  => __('Front Page', 'houzez-studio'),

msgid "Blog / Posts Page"
msgstr ""

# 'unique-blog'   => __('Blog / Posts Page', 'houzez-studio'),
# 'unique-front'  => __('Front Page', 'houzez-studio'),
# 'unique-date'   => __('Date Archive', 'houzez-studio'),

msgid "Front Page"
msgstr ""

# 'unique-front'  => __('Front Page', 'houzez-studio'),
# 'unique-date'   => __('Date Archive', 'houzez-studio'),
# 'unique-author' => __('Author Archive', 'houzez-studio'),

msgid "Date Archive"
msgstr ""

# 'unique-date'   => __('Date Archive', 'houzez-studio'),
# 'unique-author' => __('Author Archive', 'houzez-studio'),
# );

msgid "Author Archive"
msgstr ""

# if (class_exists('WooCommerce')) {
# $special_pages['unique-woo-shop'] = __('WooCommerce Shop Page', 'houzez-studio');
# }

msgid "WooCommerce Shop Page"
msgstr ""

# $all_posts_key = "{$post_name}|all";
# $post_options[$all_posts_key] = sprintf(__('All %s', 'houzez-studio'), $post_label);
# 

msgid "All %s"
msgstr ""

# $all_archive_key = "{$post_name}|all|archive";
# $post_options[$all_archive_key] = sprintf(__('All %s Archive', 'houzez-studio'), $post_label);
# }

msgid "All %s Archive"
msgstr ""

# $output .= '<select name="' . esc_attr($input_name) . '[rule][' . $index . ']" class="fts-selection_dropdown form-control">';
# $output .= '<option value="">' . __('Select', 'houzez-studio') . '</option>';
# 

msgid "Select"
msgstr ""

# private static function fts_RenderAddExclusionRuleButton() {
# return '<div class="fts-create_exclusion_rule"><a href="#" class="button button-secondary">' . __('Define Exclusion Rule', 'houzez-studio') . '</a></div>';
# }

msgid "Define Exclusion Rule"
msgstr ""

# $rule_type = $settings['rule_type'] ?? '';
# $button_label = $settings['button_label'] ?? __('Add Rule', 'houzez-studio');
# $saved_values = $value;

msgid "Add Rule"
msgstr ""

# 'language' => $language,
# 'search' => esc_html__('Search pages / post / categories', 'houzez-studio'),
# 'more_char'     => esc_html__( 'or more characters', 'houzez-studio' ),

msgid "Search pages / post / categories"
msgstr ""

# 'search' => esc_html__('Search pages / post / categories', 'houzez-studio'),
# 'more_char'     => esc_html__( 'or more characters', 'houzez-studio' ),
# 'searching'     => esc_html__( 'Searching…', 'houzez-studio' ),

msgid "or more characters"
msgstr ""

# 'more_char'     => esc_html__( 'or more characters', 'houzez-studio' ),
# 'searching'     => esc_html__( 'Searching…', 'houzez-studio' ),
# 'nonce' => wp_create_nonce( 'fts_nonce' ),

msgid "Searching…"
msgstr ""
