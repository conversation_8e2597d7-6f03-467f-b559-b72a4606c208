MC4WP: Mailchimp for WordPress
======================
[![License: GPLv3](https://img.shields.io/badge/License-GPLv3-blue.svg)](https://www.gnu.org/licenses/gpl-3.0)
![Active installs](https://img.shields.io/wordpress/plugin/installs/mailchimp-for-wp.svg)
![Downloads](https://img.shields.io/wordpress/plugin/dt/mailchimp-for-wp.svg)
[![Rating](https://img.shields.io/wordpress/plugin/r/mailchimp-for-wp.svg)](https://wordpress.org/support/plugin/mailchimp-for-wp/reviews/)

Here, you can browse the source code of the [MC4WP: Mailchimp for WordPress Plugin](https://wordpress.org/plugins/mailchimp-for-wp/), find and discuss open issues or contribute code to the plugin.

Requirements
--------------

- PHP version 7.4 or higher
- WordPress version 4.6 or higher


Installation
------------

If you just want to install this plugin on your WordPress site, please download and install the latest version from WordPress.org: [Mailchimp for WordPress plugin on WordPress.org](https://wordpress.org/plugins/mailchimp-for-wp/).

To install the development version, take the following steps:

1. Clone the GitHub repository:
  ```
  git clone https://github.com/ibericode/mailchimp-for-wordpress.git mailchimp-for-wp
  ```

1. Install Composer dependencies:
  ```sh 
  composer install 
  ```

1. Install NPM dependencies:
  ```
  npm install
  ```

1. Generate plugin asset files:
  ```
  npm run build 
  ```

1. Activate the plugin in your WordPress admin panel.

Bugs
----
If you think you've found a bug, [please open an issue here](https://github.com/ibericode/mailchimp-for-wordpress/issues?state=open)!

Translations
-------------
You can help [help translate Mailchimp for WordPress](https://translate.wordpress.org/projects/wp-plugins/mailchimp-for-wp/stable/) on WordPress.org.

Support
-------
This is a developer's portal for the Mailchimp for WordPress plugin and should not be used for support.
Please visit the [Mailchimp for WordPress support forum on WordPress.org](https://wordpress.org/support/plugin/mailchimp-for-wp).

If you need priority support, [upgrade to Mailchimp for WordPress Premium](https://www.mc4wp.com/).

Developers
----------

Looking for code snippets? Have a look at the [sample code snippets directory](https://github.com/ibericode/mailchimp-for-wordpress/tree/main/sample-code-snippets) for a collection of modification examples.

