(()=>{const e=window.mc4wp_vars.ajaxurl,t=window.mc4wp.settings,n=document.getElementById("notice-additional-fields");function i(){const t=[].filter.call(document.querySelectorAll(".mc4wp-list-input"),e=>e.checked).map(e=>e.value).join(","),i=["EMAIL","FNAME","NAME","LNAME"];let l=!1;window.fetch(`${e}?action=mc4wp_get_list_details&ids=${t}`).then(e=>e.json()).then(e=>{e.forEach(e=>{e.merge_fields.forEach(e=>{e.required&&i.indexOf(e.tag)<0&&(l=!0)})})}).finally(()=>{n.style.display=l?"":"none"})}n&&(i(),t.on("selectedLists.change",i))})();