(()=>{var e={115:(e,t,n)=>{!function(e){"use strict";function t(e){e.state.tagHit&&e.state.tagHit.clear(),e.state.tagOther&&e.state.tagOther.clear(),e.state.tagHit=e.state.tagOther=null}function n(n){n.state.failedTagMatch=!1,n.operation(function(){if(t(n),!n.somethingSelected()){var r=n.getCursor(),i=n.getViewport();i.from=Math.min(i.from,r.line),i.to=Math.max(r.line+1,i.to);var o=e.findMatchingTag(n,r,i);if(o){if(n.state.matchBothTags){var a="open"==o.at?o.open:o.close;a&&(n.state.tagHit=n.markText(a.from,a.to,{className:"CodeMirror-matchingtag"}))}var l="close"==o.at?o.open:o.close;l?n.state.tagOther=n.markText(l.from,l.to,{className:"CodeMirror-matchingtag"}):n.state.failedTagMatch=!0}}})}function r(e){e.state.failedTagMatch&&n(e)}e.defineOption("matchTags",!1,function(i,o,a){a&&a!=e.Init&&(i.off("cursorActivity",n),i.off("viewportChange",r),t(i)),o&&(i.state.matchBothTags="object"==typeof o&&o.bothTags,i.on("cursorActivity",n),i.on("viewportChange",r),n(i))}),e.commands.toMatchingTag=function(t){var n=e.findMatchingTag(t,t.getCursor());if(n){var r="close"==n.at?n.open:n.close;r&&t.extendSelection(r.to,r.from)}}}(n(5237),n(6753))},361:(e,t,n)=>{const r=window.mc4wp_forms_i18n,i=n(4862),o={showType:function(e){let t=e.type;return t=t.charAt(0).toUpperCase()+t.slice(1),i("div",[i("label",r.fieldType),i("span",t)])},label:function(e){return i("div",[i("label",r.fieldLabel),i("input.widefat",{type:"text",value:e.label,onchange:t=>{e.label=t.target.value},placeholder:e.title})])},value:function(e){const t="hidden"===e.type;return i("div",[i("label",[t?r.value:r.initialValue," ",t?"":i("small",{style:"float: right; font-weight: normal;"},r.optional)]),i("input.widefat",{type:"text",value:e.value,onchange:t=>{e.value=t.target.value}}),t?"":i("p.description",r.valueHelp)])},numberMinMax:function(e){return i("div.mc4wp-row",[i("div.mc4wp-col.mc4wp-col-3",[i("label",r.min),i("input",{type:"number",onchange:t=>{e.min=t.target.value}})]),i("div.mc4wp-col.mc4wp-col-3",[i("label",r.max),i("input",{type:"number",onchange:t=>{e.max=t.target.value}})])])},isRequired:function(e){const t={type:"checkbox",checked:e.required,onchange:t=>{e.required=t.target.checked}};let n;return e.forceRequired&&(t.required=!0,t.disabled=!0,n=i("p.description",r.forceRequired)),i("div",[i("label.cb-wrap",[i("input",t),r.isFieldRequired]),n])},placeholder:function(e){return i("div",[i("label",[r.placeholder," ",i("small",{style:"float: right; font-weight: normal;"},r.optional)]),i("input.widefat",{type:"text",value:e.placeholder,onchange:t=>{e.placeholder=t.target.value},placeholder:""}),i("p.description",r.placeholderHelp)])},useParagraphs:function(e){return i("div",[i("label.cb-wrap",[i("input",{type:"checkbox",checked:e.wrap,onchange:t=>{e.wrap=t.target.checked}}),r.wrapInParagraphTags])])},choiceType:function(e){const t=[i("option",{value:"select",selected:"select"===e.type&&"selected"},r.dropdown),i("option",{value:"radio",selected:"radio"===e.type&&"selected"},r.radioButtons)];return e.acceptsMultipleValues&&t.push(i("option",{value:"checkbox",selected:"checkbox"===e.type&&"selected"},r.checkboxes)),i("div",[i("label",r.choiceType),i("select",{value:e.type,onchange:t=>{e.type=t.target.value}},t)])},choices:function(e){const t=[];return t.push(i("div",[i("label",r.choices),i("div.limit-height",[i("table",e.choices.map(function(t,n){return i("tr",{"data-id":n},[i("td.cb",i("input",{name:"selected",type:"checkbox"===e.type?"checkbox":"radio",onchange:t=>{e.choices=e.choices.map(n=>(n.value===t.target.value?n.selected=!n.selected:"checkbox"!==e.type&&(n.selected=!1),n))},checked:t.selected,value:t.value,title:r.preselect})),i("td.stretch",i("input.widefat",{type:"text",value:t.label,placeholder:t.title,onchange:e=>{t.label=e.target.value}})),i("td",i("span",{title:r.remove,class:"dashicons dashicons-no-alt hover-activated",onclick:function(e){this.choices.splice(e,1)}.bind(e,n)},""))])}))])])),t},linkToTerms:function(e){return i("div",[i("label",r.agreeToTermsLink),i("input.widefat",{type:"text",value:e.link,onchange:t=>{e.link=t.target.value},placeholder:"https://..."})])},description:function(e){return""===e.description?[]:i("p",i.trust(e.description))}};e.exports=o},576:(e,t,n)=>{!function(e){"use strict";var t={autoSelfClosers:{area:!0,base:!0,br:!0,col:!0,command:!0,embed:!0,frame:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0,menuitem:!0},implicitlyClosed:{dd:!0,li:!0,optgroup:!0,option:!0,p:!0,rp:!0,rt:!0,tbody:!0,td:!0,tfoot:!0,th:!0,tr:!0},contextGrabbers:{dd:{dd:!0,dt:!0},dt:{dd:!0,dt:!0},li:{li:!0},option:{option:!0,optgroup:!0},optgroup:{optgroup:!0},p:{address:!0,article:!0,aside:!0,blockquote:!0,dir:!0,div:!0,dl:!0,fieldset:!0,footer:!0,form:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,header:!0,hgroup:!0,hr:!0,menu:!0,nav:!0,ol:!0,p:!0,pre:!0,section:!0,table:!0,ul:!0},rp:{rp:!0,rt:!0},rt:{rp:!0,rt:!0},tbody:{tbody:!0,tfoot:!0},td:{td:!0,th:!0},tfoot:{tbody:!0},th:{td:!0,th:!0},thead:{tbody:!0,tfoot:!0},tr:{tr:!0}},doNotIndent:{pre:!0},allowUnquoted:!0,allowMissing:!0,caseFold:!0},n={autoSelfClosers:{},implicitlyClosed:{},contextGrabbers:{},doNotIndent:{},allowUnquoted:!1,allowMissing:!1,allowMissingTagName:!1,caseFold:!1};e.defineMode("xml",function(r,i){var o,a,l=r.indentUnit,s={},c=i.htmlMode?t:n;for(var u in c)s[u]=c[u];for(var u in i)s[u]=i[u];function d(e,t){function n(n){return t.tokenize=n,n(e,t)}var r=e.next();return"<"==r?e.eat("!")?e.eat("[")?e.match("CDATA[")?n(h("atom","]]>")):null:e.match("--")?n(h("comment","--\x3e")):e.match("DOCTYPE",!0,!0)?(e.eatWhile(/[\w\._\-]/),n(p(1))):null:e.eat("?")?(e.eatWhile(/[\w\._\-]/),t.tokenize=h("meta","?>"),"meta"):(o=e.eat("/")?"closeTag":"openTag",t.tokenize=f,"tag bracket"):"&"==r?(e.eat("#")?e.eat("x")?e.eatWhile(/[a-fA-F\d]/)&&e.eat(";"):e.eatWhile(/[\d]/)&&e.eat(";"):e.eatWhile(/[\w\.\-:]/)&&e.eat(";"))?"atom":"error":(e.eatWhile(/[^&<]/),null)}function f(e,t){var n,r,i=e.next();if(">"==i||"/"==i&&e.eat(">"))return t.tokenize=d,o=">"==i?"endTag":"selfcloseTag","tag bracket";if("="==i)return o="equals",null;if("<"==i){t.tokenize=d,t.state=b,t.tagName=t.tagStart=null;var a=t.tokenize(e,t);return a?a+" tag error":"tag error"}return/[\'\"]/.test(i)?(t.tokenize=(n=i,r=function(e,t){for(;!e.eol();)if(e.next()==n){t.tokenize=f;break}return"string"},r.isInAttribute=!0,r),t.stringStartCol=e.column(),t.tokenize(e,t)):(e.match(/^[^\s\u00a0=<>\"\']*[^\s\u00a0=<>\"\'\/]/),"word")}function h(e,t){return function(n,r){for(;!n.eol();){if(n.match(t)){r.tokenize=d;break}n.next()}return e}}function p(e){return function(t,n){for(var r;null!=(r=t.next());){if("<"==r)return n.tokenize=p(e+1),n.tokenize(t,n);if(">"==r){if(1==e){n.tokenize=d;break}return n.tokenize=p(e-1),n.tokenize(t,n)}}return"meta"}}function m(e){return e&&e.toLowerCase()}function g(e,t,n){this.prev=e.context,this.tagName=t||"",this.indent=e.indented,this.startOfLine=n,(s.doNotIndent.hasOwnProperty(t)||e.context&&e.context.noIndent)&&(this.noIndent=!0)}function v(e){e.context&&(e.context=e.context.prev)}function y(e,t){for(var n;;){if(!e.context)return;if(n=e.context.tagName,!s.contextGrabbers.hasOwnProperty(m(n))||!s.contextGrabbers[m(n)].hasOwnProperty(m(t)))return;v(e)}}function b(e,t,n){return"openTag"==e?(n.tagStart=t.column(),w):"closeTag"==e?x:b}function w(e,t,n){return"word"==e?(n.tagName=t.current(),a="tag",S):s.allowMissingTagName&&"endTag"==e?(a="tag bracket",S(e,0,n)):(a="error",w)}function x(e,t,n){if("word"==e){var r=t.current();return n.context&&n.context.tagName!=r&&s.implicitlyClosed.hasOwnProperty(m(n.context.tagName))&&v(n),n.context&&n.context.tagName==r||!1===s.matchClosing?(a="tag",k):(a="tag error",C)}return s.allowMissingTagName&&"endTag"==e?(a="tag bracket",k(e,0,n)):(a="error",C)}function k(e,t,n){return"endTag"!=e?(a="error",k):(v(n),b)}function C(e,t,n){return a="error",k(e,0,n)}function S(e,t,n){if("word"==e)return a="attribute",T;if("endTag"==e||"selfcloseTag"==e){var r=n.tagName,i=n.tagStart;return n.tagName=n.tagStart=null,"selfcloseTag"==e||s.autoSelfClosers.hasOwnProperty(m(r))?y(n,r):(y(n,r),n.context=new g(n,r,i==n.indented)),b}return a="error",S}function T(e,t,n){return"equals"==e?L:(s.allowMissing||(a="error"),S(e,0,n))}function L(e,t,n){return"string"==e?M:"word"==e&&s.allowUnquoted?(a="string",S):(a="error",S(e,0,n))}function M(e,t,n){return"string"==e?M:S(e,0,n)}return d.isInText=!0,{startState:function(e){var t={tokenize:d,state:b,indented:e||0,tagName:null,tagStart:null,context:null};return null!=e&&(t.baseIndent=e),t},token:function(e,t){if(!t.tagName&&e.sol()&&(t.indented=e.indentation()),e.eatSpace())return null;o=null;var n=t.tokenize(e,t);return(n||o)&&"comment"!=n&&(a=null,t.state=t.state(o||n,e,t),a&&(n="error"==a?n+" error":a)),n},indent:function(t,n,r){var i=t.context;if(t.tokenize.isInAttribute)return t.tagStart==t.indented?t.stringStartCol+1:t.indented+l;if(i&&i.noIndent)return e.Pass;if(t.tokenize!=f&&t.tokenize!=d)return r?r.match(/^(\s*)/)[0].length:0;if(t.tagName)return!1!==s.multilineTagIndentPastTag?t.tagStart+t.tagName.length+2:t.tagStart+l*(s.multilineTagIndentFactor||1);if(s.alignCDATA&&/<!\[CDATA\[/.test(n))return 0;var o=n&&/^<(\/)?([\w_:\.-]*)/.exec(n);if(o&&o[1])for(;i;){if(i.tagName==o[2]){i=i.prev;break}if(!s.implicitlyClosed.hasOwnProperty(m(i.tagName)))break;i=i.prev}else if(o)for(;i;){var a=s.contextGrabbers[m(i.tagName)];if(!a||!a.hasOwnProperty(m(o[2])))break;i=i.prev}for(;i&&i.prev&&!i.startOfLine;)i=i.prev;return i?i.indent+l:t.baseIndent||0},electricInput:/<\/[\s\w:]+>$/,blockCommentStart:"\x3c!--",blockCommentEnd:"--\x3e",configuration:s.htmlMode?"html":"xml",helperType:s.htmlMode?"html":"xml",skipAttribute:function(e){e.state==L&&(e.state=S)},xmlCurrentTag:function(e){return e.tagName?{name:e.tagName,close:"closeTag"==e.type}:null},xmlCurrentContext:function(e){for(var t=[],n=e.context;n;n=n.prev)t.push(n.tagName);return t.reverse()}}}),e.defineMIME("text/xml","xml"),e.defineMIME("application/xml","xml"),e.mimeModes.hasOwnProperty("text/html")||e.defineMIME("text/html",{name:"xml",htmlMode:!0})}(n(5237))},795:e=>{"use strict";e.exports={}.hasOwnProperty},1088:(e,t,n)=>{"use strict";e.exports=n(8147)("undefined"!=typeof window?window:null)},1500:(e,t,n)=>{"use strict";var r=n(7224);e.exports=function(e){var t=r(e),n=Object.keys(t.params),i=[],o=new RegExp("^"+t.path.replace(/:([^\/.-]+)(\.{3}|\.(?!\.)|-)?|[\\^$*+.()|\[\]{}]/g,function(e,t,n){return null==t?"\\"+e:(i.push({k:t,r:"..."===n}),"..."===n?"(.*)":"."===n?"([^/]+)\\.":"([^/]+)"+(n||""))})+"\\/?$");return function(e){for(var r=0;r<n.length;r++)if(t.params[n[r]]!==e.params[n[r]])return!1;if(!i.length)return o.test(e.path);var a=o.exec(e.path);if(null==a)return!1;for(r=0;r<i.length;r++)e.params[i[r].k]=i[r].r?a[r+1]:decodeURIComponent(a[r+1]);return!0}}},2232:(e,t,n)=>{const r=new(n(9885)),i={};function o(e){return{name:e.name,title:e.title||e.name,type:e.type,mailchimpType:e.mailchimpType||null,label:e.label||e.title||"",showLabel:"boolean"!=typeof e.showLabel||e.showLabel,value:e.value||"",placeholder:e.placeholder||"",required:"boolean"==typeof e.required&&e.required,forceRequired:"boolean"==typeof e.forceRequired&&e.forceRequired,wrap:"boolean"!=typeof e.wrap||e.wrap,min:e.min,max:e.max,help:e.help||"",choices:e.choices||[],inFormContent:null,acceptsMultipleValues:e.acceptsMultipleValues,link:e.link||"",description:e.description||""}}function a(e){return{title:e.title||e.label,selected:e.selected||!1,value:e.value||e.label,label:e.label}}e.exports={get:function(e){return i[e]},getAll:function(){return Object.values(i)},deregister:function(e){delete i[e.name]},register:function(e,t){const n=i[t.name];if(n)return!n.forceRequired&&t.forceRequired&&(n.forceRequired=!0),n;t.choices&&(t.choices=function(e){return Object.keys(e).map(t=>new a({label:e[t],value:Array.isArray(e)?null:t}))}(t.choices),t.value&&(t.choices=t.choices.map(function(e){return e.value===t.value&&(e.selected=!0),e})));const l=new o(t);return l.category=e,i[t.name]=l,r.emit("change",[]),l},on:r.on.bind(r)}},2325:(e,t,n)=>{const r=n(5237);n(2520),n(115);const i={},o=document.createElement("form");let a,l=!1;const s=document.getElementById("mc4wp-form-content"),c=document.getElementById("mc4wp-form-preview");let u;const d=/\{[^{}]+\}/g;function f(){const e=c.contentDocument||c.contentWindow.document;u=e.querySelector(".mc4wp-form-fields"),u&&h()}function h(){if(!u)return f();let e=i.getValue();e=e.replace(d,"").replace(d,""),u.innerHTML=e,u.dispatchEvent(new Event("mc4wp-refresh"))}function p(){return l&&(o.innerHTML=i.getValue().toLowerCase(),l=!1),o}i.getValue=function(){return a?a.getValue():s.value},i.query=function(e){return p().querySelectorAll(e.toLowerCase())},i.containsField=function(e){return null!==p().elements.namedItem(e.toLowerCase())},i.insert=function(e){a?(a.replaceSelection(e),a.focus()):s.value+=e},i.on=function(e,t){return a?(e="input"===e?"changes":e,a.on(e,t)):s.addEventListener(e,t)},i.refresh=function(){a&&a.refresh()},s&&(o.innerHTML=s.value.toLowerCase(),a=r.fromTextArea(s,{selectionPointer:!0,mode:"htmlmixed",htmlMode:!0,autoCloseTags:!0,autoRefresh:!0,styleActiveLine:!0,matchBrackets:!0,matchTags:{bothTags:!0}}),a.on("change",function(){const e=new Event("change",{bubbles:!0});s.dispatchEvent(e)}),s.addEventListener("change",function(){l=!0,h()}),window.addEventListener("load",function(){r.signal(a,"change")})),c&&(c.addEventListener("load",f),f()),e.exports=i},2419:(e,t,n)=>{"use strict";var r=n(4726);r.trust=n(9665),r.fragment=n(8995),e.exports=r},2520:(e,t,n)=>{!function(e){"use strict";var t={script:[["lang",/(javascript|babel)/i,"javascript"],["type",/^(?:text|application)\/(?:x-)?(?:java|ecma)script$|^module$|^$/i,"javascript"],["type",/./,"text/plain"],[null,null,"javascript"]],style:[["lang",/^css$/i,"css"],["type",/^(text\/)?(x-)?(stylesheet|css)$/i,"css"],["type",/./,"text/plain"],[null,null,"css"]]};var n={};function r(e,t){var r=e.match(function(e){return n[e]||(n[e]=new RegExp("\\s+"+e+"\\s*=\\s*('|\")?([^'\"]+)('|\")?\\s*"))}(t));return r?/^\s*(.*?)\s*$/.exec(r[2])[1]:""}function i(e,t){return new RegExp((t?"^":"")+"</\\s*"+e+"\\s*>","i")}function o(e,t){for(var n in e)for(var r=t[n]||(t[n]=[]),i=e[n],o=i.length-1;o>=0;o--)r.unshift(i[o])}e.defineMode("htmlmixed",function(n,a){var l=e.getMode(n,{name:"xml",htmlMode:!0,multilineTagIndentFactor:a.multilineTagIndentFactor,multilineTagIndentPastTag:a.multilineTagIndentPastTag,allowMissingTagName:a.allowMissingTagName}),s={},c=a&&a.tags,u=a&&a.scriptTypes;if(o(t,s),c&&o(c,s),u)for(var d=u.length-1;d>=0;d--)s.script.unshift(["type",u[d].matches,u[d].mode]);function f(t,o){var a,c=l.token(t,o.htmlState),u=/\btag\b/.test(c);if(u&&!/[<>\s\/]/.test(t.current())&&(a=o.htmlState.tagName&&o.htmlState.tagName.toLowerCase())&&s.hasOwnProperty(a))o.inTag=a+" ";else if(o.inTag&&u&&/>$/.test(t.current())){var d=/^([\S]+) (.*)/.exec(o.inTag);o.inTag=null;var h=">"==t.current()&&function(e,t){for(var n=0;n<e.length;n++){var i=e[n];if(!i[0]||i[1].test(r(t,i[0])))return i[2]}}(s[d[1]],d[2]),p=e.getMode(n,h),m=i(d[1],!0),g=i(d[1],!1);o.token=function(e,t){return e.match(m,!1)?(t.token=f,t.localState=t.localMode=null,null):function(e,t,n){var r=e.current(),i=r.search(t);return i>-1?e.backUp(r.length-i):r.match(/<\/?$/)&&(e.backUp(r.length),e.match(t,!1)||e.match(r)),n}(e,g,t.localMode.token(e,t.localState))},o.localMode=p,o.localState=e.startState(p,l.indent(o.htmlState,"",""))}else o.inTag&&(o.inTag+=t.current(),t.eol()&&(o.inTag+=" "));return c}return{startState:function(){return{token:f,inTag:null,localMode:null,localState:null,htmlState:e.startState(l)}},copyState:function(t){var n;return t.localState&&(n=e.copyState(t.localMode,t.localState)),{token:t.token,inTag:t.inTag,localMode:t.localMode,localState:n,htmlState:e.copyState(l,t.htmlState)}},token:function(e,t){return t.token(e,t)},indent:function(t,n,r){return!t.localMode||/^\s*<\//.test(n)?l.indent(t.htmlState,n,r):t.localMode.indent?t.localMode.indent(t.localState,n,r):e.Pass},innerMode:function(e){return{state:e.localState||e.htmlState,mode:e.localMode||l}}}},"xml","javascript","css"),e.defineMIME("text/html","htmlmixed")}(n(5237),n(576),n(6792),n(8656))},2975:(e,t,n)=>{"use strict";var r=n(5199);e.exports=n(4389)("undefined"!=typeof window?window:null,r.redraw)},3322:(e,t,n)=>{const r=n(4862),i=window.mc4wp_forms_i18n;function o(){}function a(e){const t=e.children[0],n=window.innerWidth||document.documentElement.clientWidth||document.body.clientWidth,r=window.innerHeight||document.documentElement.clientHeight||document.body.clientHeight,i=(n-t.clientWidth-40)/2,o=(r-t.clientHeight-40)/2;t.style.left=(i>0?i:0)+"px",t.style.top=(o>0?o:0)+"px"}function l(e,t){switch(t.keyCode){case 27:e();break;case 13:t.preventDefault()}}o.prototype.oncreate=function(e){this.onDocumentKeydown=l.bind(null,e.attrs.onClose),this.onWindowResize=a.bind(null,e.dom),document.addEventListener("keydown",this.onDocumentKeydown),window.addEventListener("resize",this.onWindowResize),this.onWindowResize()},o.prototype.onremove=function(){document.removeEventListener("keydown",this.onDocumentKeydown),window.removeEventListener("resize",this.onWindowResize)},o.prototype.view=function(e){return[r("div.mc4wp-overlay-wrap",r("div.mc4wp-overlay",[r("span",{class:"close dashicons dashicons-no",title:i.close,onclick:e.attrs.onClose}),e.children])),r("div.mc4wp-overlay-background",{title:i.close,onclick:e.attrs.onClose})]},e.exports=o},3804:(e,t,n)=>{"use strict";var r=n(7165),i=n(4726),o=n(8555),a=n(7224),l=n(1500),s=n(8333);function c(e){try{return decodeURIComponent(e)}catch(t){return e}}e.exports=function(e,t){var n,u,d,f,h,p,m,g,v=null==e?null:"function"==typeof e.setImmediate?e.setImmediate:e.setTimeout,y=Promise.resolve(),b=!1,w=!1,x=!1,k={onremove:function(){w=x=!1,e.removeEventListener("popstate",T,!1)},view:function(){var e=r(h,p.key,p);return f?f.render(e):[e]}},C=L.SKIP={};function S(){b=!1;var r=e.location.hash;"#"!==L.prefix[0]&&(r=e.location.search+r,"?"!==L.prefix[0]&&"/"!==(r=e.location.pathname+r)[0]&&(r="/"+r));var i=r.concat().replace(/(?:%[a-f89][a-f0-9])+/gim,c).slice(L.prefix.length),o=a(i);function l(e){console.error(e),L.set(d,null,{replace:!0})}Object.assign(o.params,e.history.state),function e(r){for(;r<u.length;r++)if(u[r].check(o)){var a=u[r].component,s=u[r].route,c=a,v=g=function(l){if(v===g){if(l===C)return e(r+1);h=null==l||"function"!=typeof l.view&&"function"!=typeof l?"div":l,p=o.params,m=i,g=null,f=a.render?a:null,x?t.redraw():(x=!0,t.mount(n,k))}};return void(a.view||"function"==typeof a?(a={},v(c)):a.onmatch?y.then(function(){return a.onmatch(o.params,i,s)}).then(v,i===d?null:l):v())}if(i===d)throw new Error("Could not resolve default route "+d+".");L.set(d,null,{replace:!0})}(0)}function T(){b||(b=!0,v(S))}function L(t,r,i){if(!t)throw new TypeError("DOM element being rendered to does not exist.");if(u=Object.keys(i).map(function(e){if("/"!==e[0])throw new SyntaxError("Routes must start with a '/'.");if(/:([^\/\.-]+)(\.{3})?:/.test(e))throw new SyntaxError("Route parameter names must be separated with either '/', '.', or '-'.");return{route:e,component:i[e],check:l(e)}}),d=r,null!=r){var o=a(r);if(!u.some(function(e){return e.check(o)}))throw new ReferenceError("Default route doesn't match any known routes.")}n=t,e.addEventListener("popstate",T,!1),w=!0,S()}return L.set=function(t,n,r){if(null!=g&&((r=r||{}).replace=!0),g=null,t=o(t,n),w){T();var i=r?r.state:null,a=r?r.title:null;r&&r.replace?e.history.replaceState(i,a,L.prefix+t):e.history.pushState(i,a,L.prefix+t)}else e.location.href=L.prefix+t},L.get=function(){return m},L.prefix="#!",L.Link={view:function(e){var t,n,r,a=i(e.attrs.selector||"a",s(e.attrs,["options","params","selector","onclick"]),e.children);return(a.attrs.disabled=Boolean(a.attrs.disabled))?(a.attrs.href=null,a.attrs["aria-disabled"]="true"):(t=e.attrs.options,n=e.attrs.onclick,r=o(a.attrs.href,e.attrs.params),a.attrs.href=L.prefix+r,a.attrs.onclick=function(e){var i;"function"==typeof n?i=n.call(e.currentTarget,e):null==n||"object"!=typeof n||"function"==typeof n.handleEvent&&n.handleEvent(e),!1===i||e.defaultPrevented||0!==e.button&&0!==e.which&&1!==e.which||e.currentTarget.target&&"_self"!==e.currentTarget.target||e.ctrlKey||e.metaKey||e.shiftKey||e.altKey||(e.preventDefault(),e.redraw=!1,L.set(r,null,t))}),a}},L.param=function(e){return p&&null!=e?p[e]:p},L}},4224:e=>{"use strict";e.exports=function(e){if("[object Object]"!==Object.prototype.toString.call(e))return"";var t=[];for(var n in e)r(n,e[n]);return t.join("&");function r(e,n){if(Array.isArray(n))for(var i=0;i<n.length;i++)r(e+"["+i+"]",n[i]);else if("[object Object]"===Object.prototype.toString.call(n))for(var i in n)r(e+"["+i+"]",n[i]);else t.push(encodeURIComponent(e)+(null!=n&&""!==n?"="+encodeURIComponent(n):""))}}},4389:(e,t,n)=>{"use strict";var r=n(8555),i=n(795);e.exports=function(e,t){function n(e){return new Promise(e)}function o(e,t){for(var n in e.headers)if(i.call(e.headers,n)&&n.toLowerCase()===t)return!0;return!1}return n.prototype=Promise.prototype,n.__proto__=Promise,{request:function(a,l){"string"!=typeof a?(l=a,a=a.url):null==l&&(l={});var s=function(t,n){return new Promise(function(a,l){t=r(t,n.params);var s,c=null!=n.method?n.method.toUpperCase():"GET",u=n.body,d=(null==n.serialize||n.serialize===JSON.serialize)&&!(u instanceof e.FormData||u instanceof e.URLSearchParams),f=n.responseType||("function"==typeof n.extract?"":"json"),h=new e.XMLHttpRequest,p=!1,m=!1,g=h,v=h.abort;for(var y in h.abort=function(){p=!0,v.call(this)},h.open(c,t,!1!==n.async,"string"==typeof n.user?n.user:void 0,"string"==typeof n.password?n.password:void 0),d&&null!=u&&!o(n,"content-type")&&h.setRequestHeader("Content-Type","application/json; charset=utf-8"),"function"==typeof n.deserialize||o(n,"accept")||h.setRequestHeader("Accept","application/json, text/*"),n.withCredentials&&(h.withCredentials=n.withCredentials),n.timeout&&(h.timeout=n.timeout),h.responseType=f,n.headers)i.call(n.headers,y)&&h.setRequestHeader(y,n.headers[y]);h.onreadystatechange=function(e){if(!p&&4===e.target.readyState)try{var r,i=e.target.status>=200&&e.target.status<300||304===e.target.status||/^file:\/\//i.test(t),o=e.target.response;if("json"===f){if(!e.target.responseType&&"function"!=typeof n.extract)try{o=JSON.parse(e.target.responseText)}catch(e){o=null}}else f&&"text"!==f||null==o&&(o=e.target.responseText);if("function"==typeof n.extract?(o=n.extract(e.target,n),i=!0):"function"==typeof n.deserialize&&(o=n.deserialize(o)),i){if("function"==typeof n.type)if(Array.isArray(o))for(var s=0;s<o.length;s++)o[s]=new n.type(o[s]);else o=new n.type(o);a(o)}else{var c=function(){try{r=e.target.responseText}catch(e){r=o}var t=new Error(r);t.code=e.target.status,t.response=o,l(t)};0===h.status?setTimeout(function(){m||c()}):c()}}catch(e){l(e)}},h.ontimeout=function(e){m=!0;var t=new Error("Request timed out");t.code=e.target.status,l(t)},"function"==typeof n.config&&(h=n.config(h,n,t)||h)!==g&&(s=h.abort,h.abort=function(){p=!0,s.call(this)}),null==u?h.send():"function"==typeof n.serialize?h.send(n.serialize(u)):u instanceof e.FormData||u instanceof e.URLSearchParams?h.send(u):h.send(JSON.stringify(u))})}(a,l);if(!0===l.background)return s;var c=0;function u(){0===--c&&"function"==typeof t&&t()}return function e(t){var r=t.then;return t.constructor=n,t.then=function(){c++;var n=r.apply(t,arguments);return n.then(u,function(e){if(u(),0===c)throw e}),e(n)},t}(s)}}}},4550:(e,t,n)=>{const r=n(4862),i=n(2325),o=n(2232),a=window.mc4wp_forms_i18n,l=n(6685),s=n(3322),c=n(7779);let u;function d(e){u=null!==e?o.get(e):null,u&&"hidden"===u.type&&u.choices.length>0&&(u.value=u.choices.map(function(e){return e.label}).join("|")),r.redraw()}function f(){const e=l(u);i.insert(e),d(null)}i.on("blur",()=>r.redraw());const h=document.getElementById("mc4wp-field-wizard");h&&r.mount(h,{view:function(){const e=o.getAll(),t=r("div#mc4wp-available-fields.mc4wp-margin-s",[r("h4",{style:{marginTop:0}},a.chooseField),[a.listFields,a.interestCategories,a.formFields].map(function(t){const n=e.filter(function(e){return e.category===t});return 0===n.length?"":r("div.mc4wp-margin-s",[r("h4",t),n.map(function(e){let t="button";e.forceRequired&&(t+=" is-required");const n=e.inFormContent;return null!==n&&(t+=" "+(n?"in-form":"not-in-form")),r("button",{className:t,type:"button",onclick:e=>d(e.target.value),value:e.name},e.title)})])})]);let n=null;return u&&(n=r(s,{onClose:()=>d(null)},r("div#mc4wp-add-form-field",[r("h3",[u.title,u.forceRequired?r("span.mc4wp-red","*"):"",u.name.length?r("code",u.name):""]),u.help.length?r("p",r.trust(u.help)):"",c.render(u),r("p",[r("button",{class:"button-primary",type:"button",onkeydown:function(e){13===e.keyCode&&f()},onclick:f},a.addToForm)])]))),[t,n]}})},4726:(e,t,n)=>{"use strict";var r=n(7165),i=n(5178),o=n(795),a=/(?:(^|#|\.)([^#\.\[\]]+))|(\[(.+?)(?:\s*=\s*("|'|)((?:\\["'\]]|.)*?)\5)?\])/g,l=Object.create(null);e.exports=function(e){if(null==e||"string"!=typeof e&&"function"!=typeof e&&"function"!=typeof e.view)throw Error("The selector must be either a string or a component.");var t=i.apply(1,arguments);return"string"==typeof e&&(t.children=r.normalizeChildren(t.children),"["!==e)?function(e,t){var n=t.attrs,r=o.call(n,"class"),i=r?n.class:n.className;return t.tag=e.tag,null!=e.attrs?(n=Object.assign({},e.attrs,n),null==i&&null==e.attrs.className||(n.className=null!=i?null!=e.attrs.className?String(e.attrs.className)+" "+String(i):i:null!=e.attrs.className?e.attrs.className:null)):null!=i&&(n.className=i),r&&(n.class=null),"input"===e.tag&&o.call(n,"type")&&(n=Object.assign({type:n.type},n)),t.is=n.is,t.attrs=n,t}(l[e]||function(e){for(var t,n="div",r=[],i={};t=a.exec(e);){var s=t[1],c=t[2];if(""===s&&""!==c)n=c;else if("#"===s)i.id=c;else if("."===s)r.push(c);else if("["===t[3][0]){var u=t[6];u&&(u=u.replace(/\\(["'])/g,"$1").replace(/\\\\/g,"\\")),"class"===t[4]?r.push(u):i[t[4]]=""===u?u:u||!0}}return r.length>0&&(i.className=r.join(" ")),function(e){for(var t in e)if(o.call(e,t))return!1;return!0}(i)&&(i=null),l[e]={tag:n,attrs:i}}(e),t):(t.tag=e,t)}},4862:(e,t,n)=>{"use strict";var r=n(2419),i=n(2975),o=n(5199),a=n(9788),l=function(){return r.apply(this,arguments)};l.m=r,l.trust=r.trust,l.fragment=r.fragment,l.Fragment="[",l.mount=o.mount,l.route=n(6843),l.render=n(1088),l.redraw=o.redraw,l.request=i.request,l.parseQueryString=n(7755),l.buildQueryString=n(4224),l.parsePathname=n(7224),l.buildPathname=n(8555),l.vnode=n(7165),l.censor=n(8333),l.domFor=a.domFor,e.exports=l},5051:(e,t,n)=>{const r=n(4862),i=n(2232),o=window.mc4wp.settings,a=window.mc4wp_vars.ajaxurl,l=window.mc4wp_forms_i18n,s=window.mc4wp_vars.mailchimp,c=window.mc4wp_vars.countries,u=[];function d(e,t,n){const r=i.register(e,t);n||u.push(r)}function f(e){const t={phone:"tel",dropdown:"select",checkboxes:"checkbox",birthday:"text"};return void 0!==t[e]?t[e]:e}function h(e){const t=l.listFields,n=f(e.type),r={name:e.tag,title:e.name,required:e.required,forceRequired:e.required,type:n,choices:e.options.choices,acceptsMultipleValues:!1};return"address"!==r.type?d(t,r,!1):(d(t,{name:r.name+"[addr1]",type:"text",mailchimpType:"address",title:l.streetAddress},!1),d(t,{name:r.name+"[city]",type:"text",mailchimpType:"address",title:l.city},!1),d(t,{name:r.name+"[state]",type:"text",mailchimpType:"address",title:l.state},!1),d(t,{name:r.name+"[zip]",type:"text",mailchimpType:"address",title:l.zip},!1),d(t,{name:r.name+"[country]",type:"select",mailchimpType:"address",title:l.country,choices:c},!1)),!0}function p(e){const t=f(e.type),n={title:e.title,name:"INTERESTS["+e.id+"]",type:t,choices:e.interests,acceptsMultipleValues:"checkbox"===t};d(l.interestCategories,n,!1)}function m(e){e.merge_fields=e.merge_fields.sort(function(e,t){return"EMAIL"===e.tag||e.public&&!t.public?-1:!e.public&&t.public?1:0}),e.merge_fields.forEach(h),e.interest_categories.forEach(p),r.redraw()}function g(e){const t=a+"?action=mc4wp_get_list_details&ids="+e.map(e=>e.id).join(",");r.request({url:t,method:"GET"}).then(e=>{u.forEach(i.deregister),r.redraw(),e.forEach(m)})}o.on("selectedLists.change",g),g(o.getSelectedLists()),function(e){let t;d(l.listFields,{name:"EMAIL",title:l.emailAddress,required:!0,forceRequired:!0,type:"email"},!0),d(l.formFields,{name:"",value:l.subscribe,type:"submit",title:l.submitButton},!0),d(l.formFields,{name:"procaptcha",type:"procaptcha",label:"Procaptcha",title:"Procaptcha",wrap:!1,showLabel:!1,description:'Privacy-friendly and GDPR-compliant anti-bot protection. Go to <a href="admin.php?page=mailchimp-for-wp-integrations&integration=prosopo-procaptcha">MC4WP > Integrations > Procaptcha</a> to configure it.'},!0),t={};for(const n in e)t[e[n].id]=e[n].name;d(l.formFields,{name:"_mc4wp_lists",type:"checkbox",title:l.listChoice,choices:t,help:l.listChoiceDescription,acceptsMultipleValues:!0},!0),t={subscribe:"Subscribe",unsubscribe:"Unsubscribe"},d(l.formFields,{name:"_mc4wp_action",type:"radio",title:l.formAction,choices:t,value:"subscribe",help:l.formActionDescription},!0),d(l.formFields,{name:"AGREE_TO_TERMS",value:1,type:"terms-checkbox",label:l.agreeToTerms,title:l.agreeToTermsShort,showLabel:!1,required:!0},!0)}(s.lists)},5178:(e,t,n)=>{"use strict";var r=n(7165);e.exports=function(){var e,t=arguments[this],n=this+1;if(null==t?t={}:("object"!=typeof t||null!=t.tag||Array.isArray(t))&&(t={},n=this),arguments.length===n+1)e=arguments[n],Array.isArray(e)||(e=[e]);else for(e=[];n<arguments.length;)e.push(arguments[n++]);return r("",t.key,t,e)}},5199:(e,t,n)=>{"use strict";var r=n(1088);e.exports=n(9674)(r,"undefined"!=typeof requestAnimationFrame?requestAnimationFrame:null,"undefined"!=typeof console?console:null)},5237:function(e){e.exports=function(){"use strict";var e=navigator.userAgent,t=navigator.platform,n=/gecko\/\d/i.test(e),r=/MSIE \d/.test(e),i=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(e),o=/Edge\/(\d+)/.exec(e),a=r||i||o,l=a&&(r?document.documentMode||6:+(o||i)[1]),s=!o&&/WebKit\//.test(e),c=s&&/Qt\/\d+\.\d+/.test(e),u=!o&&/Chrome\/(\d+)/.exec(e),d=u&&+u[1],f=/Opera\//.test(e),h=/Apple Computer/.test(navigator.vendor),p=/Mac OS X 1\d\D([8-9]|\d\d)\D/.test(e),m=/PhantomJS/.test(e),g=h&&(/Mobile\/\w+/.test(e)||navigator.maxTouchPoints>2),v=/Android/.test(e),y=g||v||/webOS|BlackBerry|Opera Mini|Opera Mobi|IEMobile/i.test(e),b=g||/Mac/.test(t),w=/\bCrOS\b/.test(e),x=/win/i.test(t),k=f&&e.match(/Version\/(\d*\.\d*)/);k&&(k=Number(k[1])),k&&k>=15&&(f=!1,s=!0);var C=b&&(c||f&&(null==k||k<12.11)),S=n||a&&l>=9;function T(e){return new RegExp("(^|\\s)"+e+"(?:$|\\s)\\s*")}var L,M=function(e,t){var n=e.className,r=T(t).exec(n);if(r){var i=n.slice(r.index+r[0].length);e.className=n.slice(0,r.index)+(i?r[1]+i:"")}};function A(e){for(var t=e.childNodes.length;t>0;--t)e.removeChild(e.firstChild);return e}function N(e,t){return A(e).appendChild(t)}function O(e,t,n,r){var i=document.createElement(e);if(n&&(i.className=n),r&&(i.style.cssText=r),"string"==typeof t)i.appendChild(document.createTextNode(t));else if(t)for(var o=0;o<t.length;++o)i.appendChild(t[o]);return i}function _(e,t,n,r){var i=O(e,t,n,r);return i.setAttribute("role","presentation"),i}function E(e,t){if(3==t.nodeType&&(t=t.parentNode),e.contains)return e.contains(t);do{if(11==t.nodeType&&(t=t.host),t==e)return!0}while(t=t.parentNode)}function z(e){var t,n=e.ownerDocument||e;try{t=e.activeElement}catch(e){t=n.body||null}for(;t&&t.shadowRoot&&t.shadowRoot.activeElement;)t=t.shadowRoot.activeElement;return t}function D(e,t){var n=e.className;T(t).test(n)||(e.className+=(n?" ":"")+t)}function P(e,t){for(var n=e.split(" "),r=0;r<n.length;r++)n[r]&&!T(n[r]).test(t)&&(t+=" "+n[r]);return t}L=document.createRange?function(e,t,n,r){var i=document.createRange();return i.setEnd(r||e,n),i.setStart(e,t),i}:function(e,t,n){var r=document.body.createTextRange();try{r.moveToElementText(e.parentNode)}catch(e){return r}return r.collapse(!0),r.moveEnd("character",n),r.moveStart("character",t),r};var W=function(e){e.select()};function F(e){return e.display.wrapper.ownerDocument}function I(e){return H(e.display.wrapper)}function H(e){return e.getRootNode?e.getRootNode():e.ownerDocument}function R(e){return F(e).defaultView}function j(e){var t=Array.prototype.slice.call(arguments,1);return function(){return e.apply(null,t)}}function B(e,t,n){for(var r in t||(t={}),e)!e.hasOwnProperty(r)||!1===n&&t.hasOwnProperty(r)||(t[r]=e[r]);return t}function q(e,t,n,r,i){null==t&&-1==(t=e.search(/[^\s\u00a0]/))&&(t=e.length);for(var o=r||0,a=i||0;;){var l=e.indexOf("\t",o);if(l<0||l>=t)return a+(t-o);a+=l-o,a+=n-a%n,o=l+1}}g?W=function(e){e.selectionStart=0,e.selectionEnd=e.value.length}:a&&(W=function(e){try{e.select()}catch(e){}});var U=function(){this.id=null,this.f=null,this.time=0,this.handler=j(this.onTimeout,this)};function K(e,t){for(var n=0;n<e.length;++n)if(e[n]==t)return n;return-1}U.prototype.onTimeout=function(e){e.id=0,e.time<=+new Date?e.f():setTimeout(e.handler,e.time-+new Date)},U.prototype.set=function(e,t){this.f=t;var n=+new Date+e;(!this.id||n<this.time)&&(clearTimeout(this.id),this.id=setTimeout(this.handler,e),this.time=n)};var V={toString:function(){return"CodeMirror.Pass"}},G={scroll:!1},$={origin:"*mouse"},Y={origin:"+move"};function X(e,t,n){for(var r=0,i=0;;){var o=e.indexOf("\t",r);-1==o&&(o=e.length);var a=o-r;if(o==e.length||i+a>=t)return r+Math.min(a,t-i);if(i+=o-r,r=o+1,(i+=n-i%n)>=t)return r}}var Z=[""];function J(e){for(;Z.length<=e;)Z.push(Q(Z)+" ");return Z[e]}function Q(e){return e[e.length-1]}function ee(e,t){for(var n=[],r=0;r<e.length;r++)n[r]=t(e[r],r);return n}function te(){}function ne(e,t){var n;return Object.create?n=Object.create(e):(te.prototype=e,n=new te),t&&B(t,n),n}var re=/[\u00df\u0587\u0590-\u05f4\u0600-\u06ff\u3040-\u309f\u30a0-\u30ff\u3400-\u4db5\u4e00-\u9fcc\uac00-\ud7af]/;function ie(e){return/\w/.test(e)||e>""&&(e.toUpperCase()!=e.toLowerCase()||re.test(e))}function oe(e,t){return t?!!(t.source.indexOf("\\w")>-1&&ie(e))||t.test(e):ie(e)}function ae(e){for(var t in e)if(e.hasOwnProperty(t)&&e[t])return!1;return!0}var le=/[\u0300-\u036f\u0483-\u0489\u0591-\u05bd\u05bf\u05c1\u05c2\u05c4\u05c5\u05c7\u0610-\u061a\u064b-\u065e\u0670\u06d6-\u06dc\u06de-\u06e4\u06e7\u06e8\u06ea-\u06ed\u0711\u0730-\u074a\u07a6-\u07b0\u07eb-\u07f3\u0816-\u0819\u081b-\u0823\u0825-\u0827\u0829-\u082d\u0900-\u0902\u093c\u0941-\u0948\u094d\u0951-\u0955\u0962\u0963\u0981\u09bc\u09be\u09c1-\u09c4\u09cd\u09d7\u09e2\u09e3\u0a01\u0a02\u0a3c\u0a41\u0a42\u0a47\u0a48\u0a4b-\u0a4d\u0a51\u0a70\u0a71\u0a75\u0a81\u0a82\u0abc\u0ac1-\u0ac5\u0ac7\u0ac8\u0acd\u0ae2\u0ae3\u0b01\u0b3c\u0b3e\u0b3f\u0b41-\u0b44\u0b4d\u0b56\u0b57\u0b62\u0b63\u0b82\u0bbe\u0bc0\u0bcd\u0bd7\u0c3e-\u0c40\u0c46-\u0c48\u0c4a-\u0c4d\u0c55\u0c56\u0c62\u0c63\u0cbc\u0cbf\u0cc2\u0cc6\u0ccc\u0ccd\u0cd5\u0cd6\u0ce2\u0ce3\u0d3e\u0d41-\u0d44\u0d4d\u0d57\u0d62\u0d63\u0dca\u0dcf\u0dd2-\u0dd4\u0dd6\u0ddf\u0e31\u0e34-\u0e3a\u0e47-\u0e4e\u0eb1\u0eb4-\u0eb9\u0ebb\u0ebc\u0ec8-\u0ecd\u0f18\u0f19\u0f35\u0f37\u0f39\u0f71-\u0f7e\u0f80-\u0f84\u0f86\u0f87\u0f90-\u0f97\u0f99-\u0fbc\u0fc6\u102d-\u1030\u1032-\u1037\u1039\u103a\u103d\u103e\u1058\u1059\u105e-\u1060\u1071-\u1074\u1082\u1085\u1086\u108d\u109d\u135f\u1712-\u1714\u1732-\u1734\u1752\u1753\u1772\u1773\u17b7-\u17bd\u17c6\u17c9-\u17d3\u17dd\u180b-\u180d\u18a9\u1920-\u1922\u1927\u1928\u1932\u1939-\u193b\u1a17\u1a18\u1a56\u1a58-\u1a5e\u1a60\u1a62\u1a65-\u1a6c\u1a73-\u1a7c\u1a7f\u1b00-\u1b03\u1b34\u1b36-\u1b3a\u1b3c\u1b42\u1b6b-\u1b73\u1b80\u1b81\u1ba2-\u1ba5\u1ba8\u1ba9\u1c2c-\u1c33\u1c36\u1c37\u1cd0-\u1cd2\u1cd4-\u1ce0\u1ce2-\u1ce8\u1ced\u1dc0-\u1de6\u1dfd-\u1dff\u200c\u200d\u20d0-\u20f0\u2cef-\u2cf1\u2de0-\u2dff\u302a-\u302f\u3099\u309a\ua66f-\ua672\ua67c\ua67d\ua6f0\ua6f1\ua802\ua806\ua80b\ua825\ua826\ua8c4\ua8e0-\ua8f1\ua926-\ua92d\ua947-\ua951\ua980-\ua982\ua9b3\ua9b6-\ua9b9\ua9bc\uaa29-\uaa2e\uaa31\uaa32\uaa35\uaa36\uaa43\uaa4c\uaab0\uaab2-\uaab4\uaab7\uaab8\uaabe\uaabf\uaac1\uabe5\uabe8\uabed\udc00-\udfff\ufb1e\ufe00-\ufe0f\ufe20-\ufe26\uff9e\uff9f]/;function se(e){return e.charCodeAt(0)>=768&&le.test(e)}function ce(e,t,n){for(;(n<0?t>0:t<e.length)&&se(e.charAt(t));)t+=n;return t}function ue(e,t,n){for(var r=t>n?-1:1;;){if(t==n)return t;var i=(t+n)/2,o=r<0?Math.ceil(i):Math.floor(i);if(o==t)return e(o)?t:n;e(o)?n=o:t=o+r}}var de=null;function fe(e,t,n){var r;de=null;for(var i=0;i<e.length;++i){var o=e[i];if(o.from<t&&o.to>t)return i;o.to==t&&(o.from!=o.to&&"before"==n?r=i:de=i),o.from==t&&(o.from!=o.to&&"before"!=n?r=i:de=i)}return null!=r?r:de}var he=function(){function e(e){return e<=247?"bbbbbbbbbtstwsbbbbbbbbbbbbbbssstwNN%%%NNNNNN,N,N1111111111NNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNbbbbbbsbbbbbbbbbbbbbbbbbbbbbbbbbb,N%%%%NNNNLNNNNN%%11NLNNN1LNNNNNLLLLLLLLLLLLLLLLLLLLLLLNLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLN".charAt(e):1424<=e&&e<=1524?"R":1536<=e&&e<=1785?"nnnnnnNNr%%r,rNNmmmmmmmmmmmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmmmmmmmmmmmmmmmnnnnnnnnnn%nnrrrmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmnNmmmmmmrrmmNmmmmrr1111111111".charAt(e-1536):1774<=e&&e<=2220?"r":8192<=e&&e<=8203?"w":8204==e?"b":"L"}var t=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/,n=/[stwN]/,r=/[LRr]/,i=/[Lb1n]/,o=/[1n]/;function a(e,t,n){this.level=e,this.from=t,this.to=n}return function(l,s){var c="ltr"==s?"L":"R";if(0==l.length||"ltr"==s&&!t.test(l))return!1;for(var u=l.length,d=[],f=0;f<u;++f)d.push(e(l.charCodeAt(f)));for(var h=0,p=c;h<u;++h){var m=d[h];"m"==m?d[h]=p:p=m}for(var g=0,v=c;g<u;++g){var y=d[g];"1"==y&&"r"==v?d[g]="n":r.test(y)&&(v=y,"r"==y&&(d[g]="R"))}for(var b=1,w=d[0];b<u-1;++b){var x=d[b];"+"==x&&"1"==w&&"1"==d[b+1]?d[b]="1":","!=x||w!=d[b+1]||"1"!=w&&"n"!=w||(d[b]=w),w=x}for(var k=0;k<u;++k){var C=d[k];if(","==C)d[k]="N";else if("%"==C){var S=void 0;for(S=k+1;S<u&&"%"==d[S];++S);for(var T=k&&"!"==d[k-1]||S<u&&"1"==d[S]?"1":"N",L=k;L<S;++L)d[L]=T;k=S-1}}for(var M=0,A=c;M<u;++M){var N=d[M];"L"==A&&"1"==N?d[M]="L":r.test(N)&&(A=N)}for(var O=0;O<u;++O)if(n.test(d[O])){var _=void 0;for(_=O+1;_<u&&n.test(d[_]);++_);for(var E="L"==(O?d[O-1]:c),z=E==("L"==(_<u?d[_]:c))?E?"L":"R":c,D=O;D<_;++D)d[D]=z;O=_-1}for(var P,W=[],F=0;F<u;)if(i.test(d[F])){var I=F;for(++F;F<u&&i.test(d[F]);++F);W.push(new a(0,I,F))}else{var H=F,R=W.length,j="rtl"==s?1:0;for(++F;F<u&&"L"!=d[F];++F);for(var B=H;B<F;)if(o.test(d[B])){H<B&&(W.splice(R,0,new a(1,H,B)),R+=j);var q=B;for(++B;B<F&&o.test(d[B]);++B);W.splice(R,0,new a(2,q,B)),R+=j,H=B}else++B;H<F&&W.splice(R,0,new a(1,H,F))}return"ltr"==s&&(1==W[0].level&&(P=l.match(/^\s+/))&&(W[0].from=P[0].length,W.unshift(new a(0,0,P[0].length))),1==Q(W).level&&(P=l.match(/\s+$/))&&(Q(W).to-=P[0].length,W.push(new a(0,u-P[0].length,u)))),"rtl"==s?W.reverse():W}}();function pe(e,t){var n=e.order;return null==n&&(n=e.order=he(e.text,t)),n}var me=[],ge=function(e,t,n){if(e.addEventListener)e.addEventListener(t,n,!1);else if(e.attachEvent)e.attachEvent("on"+t,n);else{var r=e._handlers||(e._handlers={});r[t]=(r[t]||me).concat(n)}};function ve(e,t){return e._handlers&&e._handlers[t]||me}function ye(e,t,n){if(e.removeEventListener)e.removeEventListener(t,n,!1);else if(e.detachEvent)e.detachEvent("on"+t,n);else{var r=e._handlers,i=r&&r[t];if(i){var o=K(i,n);o>-1&&(r[t]=i.slice(0,o).concat(i.slice(o+1)))}}}function be(e,t){var n=ve(e,t);if(n.length)for(var r=Array.prototype.slice.call(arguments,2),i=0;i<n.length;++i)n[i].apply(null,r)}function we(e,t,n){return"string"==typeof t&&(t={type:t,preventDefault:function(){this.defaultPrevented=!0}}),be(e,n||t.type,e,t),Le(t)||t.codemirrorIgnore}function xe(e){var t=e._handlers&&e._handlers.cursorActivity;if(t)for(var n=e.curOp.cursorActivityHandlers||(e.curOp.cursorActivityHandlers=[]),r=0;r<t.length;++r)-1==K(n,t[r])&&n.push(t[r])}function ke(e,t){return ve(e,t).length>0}function Ce(e){e.prototype.on=function(e,t){ge(this,e,t)},e.prototype.off=function(e,t){ye(this,e,t)}}function Se(e){e.preventDefault?e.preventDefault():e.returnValue=!1}function Te(e){e.stopPropagation?e.stopPropagation():e.cancelBubble=!0}function Le(e){return null!=e.defaultPrevented?e.defaultPrevented:0==e.returnValue}function Me(e){Se(e),Te(e)}function Ae(e){return e.target||e.srcElement}function Ne(e){var t=e.which;return null==t&&(1&e.button?t=1:2&e.button?t=3:4&e.button&&(t=2)),b&&e.ctrlKey&&1==t&&(t=3),t}var Oe,_e,Ee=function(){if(a&&l<9)return!1;var e=O("div");return"draggable"in e||"dragDrop"in e}();function ze(e){if(null==Oe){var t=O("span","​");N(e,O("span",[t,document.createTextNode("x")])),0!=e.firstChild.offsetHeight&&(Oe=t.offsetWidth<=1&&t.offsetHeight>2&&!(a&&l<8))}var n=Oe?O("span","​"):O("span"," ",null,"display: inline-block; width: 1px; margin-right: -1px");return n.setAttribute("cm-text",""),n}function De(e){if(null!=_e)return _e;var t=N(e,document.createTextNode("AخA")),n=L(t,0,1).getBoundingClientRect(),r=L(t,1,2).getBoundingClientRect();return A(e),!(!n||n.left==n.right)&&(_e=r.right-n.right<3)}var Pe,We=3!="\n\nb".split(/\n/).length?function(e){for(var t=0,n=[],r=e.length;t<=r;){var i=e.indexOf("\n",t);-1==i&&(i=e.length);var o=e.slice(t,"\r"==e.charAt(i-1)?i-1:i),a=o.indexOf("\r");-1!=a?(n.push(o.slice(0,a)),t+=a+1):(n.push(o),t=i+1)}return n}:function(e){return e.split(/\r\n?|\n/)},Fe=window.getSelection?function(e){try{return e.selectionStart!=e.selectionEnd}catch(e){return!1}}:function(e){var t;try{t=e.ownerDocument.selection.createRange()}catch(e){}return!(!t||t.parentElement()!=e)&&0!=t.compareEndPoints("StartToEnd",t)},Ie="oncopy"in(Pe=O("div"))||(Pe.setAttribute("oncopy","return;"),"function"==typeof Pe.oncopy),He=null;var Re={},je={};function Be(e,t){arguments.length>2&&(t.dependencies=Array.prototype.slice.call(arguments,2)),Re[e]=t}function qe(e){if("string"==typeof e&&je.hasOwnProperty(e))e=je[e];else if(e&&"string"==typeof e.name&&je.hasOwnProperty(e.name)){var t=je[e.name];"string"==typeof t&&(t={name:t}),(e=ne(t,e)).name=t.name}else{if("string"==typeof e&&/^[\w\-]+\/[\w\-]+\+xml$/.test(e))return qe("application/xml");if("string"==typeof e&&/^[\w\-]+\/[\w\-]+\+json$/.test(e))return qe("application/json")}return"string"==typeof e?{name:e}:e||{name:"null"}}function Ue(e,t){t=qe(t);var n=Re[t.name];if(!n)return Ue(e,"text/plain");var r=n(e,t);if(Ke.hasOwnProperty(t.name)){var i=Ke[t.name];for(var o in i)i.hasOwnProperty(o)&&(r.hasOwnProperty(o)&&(r["_"+o]=r[o]),r[o]=i[o])}if(r.name=t.name,t.helperType&&(r.helperType=t.helperType),t.modeProps)for(var a in t.modeProps)r[a]=t.modeProps[a];return r}var Ke={};function Ve(e,t){B(t,Ke.hasOwnProperty(e)?Ke[e]:Ke[e]={})}function Ge(e,t){if(!0===t)return t;if(e.copyState)return e.copyState(t);var n={};for(var r in t){var i=t[r];i instanceof Array&&(i=i.concat([])),n[r]=i}return n}function $e(e,t){for(var n;e.innerMode&&(n=e.innerMode(t))&&n.mode!=e;)t=n.state,e=n.mode;return n||{mode:e,state:t}}function Ye(e,t,n){return!e.startState||e.startState(t,n)}var Xe=function(e,t,n){this.pos=this.start=0,this.string=e,this.tabSize=t||8,this.lastColumnPos=this.lastColumnValue=0,this.lineStart=0,this.lineOracle=n};function Ze(e,t){if((t-=e.first)<0||t>=e.size)throw new Error("There is no line "+(t+e.first)+" in the document.");for(var n=e;!n.lines;)for(var r=0;;++r){var i=n.children[r],o=i.chunkSize();if(t<o){n=i;break}t-=o}return n.lines[t]}function Je(e,t,n){var r=[],i=t.line;return e.iter(t.line,n.line+1,function(e){var o=e.text;i==n.line&&(o=o.slice(0,n.ch)),i==t.line&&(o=o.slice(t.ch)),r.push(o),++i}),r}function Qe(e,t,n){var r=[];return e.iter(t,n,function(e){r.push(e.text)}),r}function et(e,t){var n=t-e.height;if(n)for(var r=e;r;r=r.parent)r.height+=n}function tt(e){if(null==e.parent)return null;for(var t=e.parent,n=K(t.lines,e),r=t.parent;r;t=r,r=r.parent)for(var i=0;r.children[i]!=t;++i)n+=r.children[i].chunkSize();return n+t.first}function nt(e,t){var n=e.first;e:do{for(var r=0;r<e.children.length;++r){var i=e.children[r],o=i.height;if(t<o){e=i;continue e}t-=o,n+=i.chunkSize()}return n}while(!e.lines);for(var a=0;a<e.lines.length;++a){var l=e.lines[a].height;if(t<l)break;t-=l}return n+a}function rt(e,t){return t>=e.first&&t<e.first+e.size}function it(e,t){return String(e.lineNumberFormatter(t+e.firstLineNumber))}function ot(e,t,n){if(void 0===n&&(n=null),!(this instanceof ot))return new ot(e,t,n);this.line=e,this.ch=t,this.sticky=n}function at(e,t){return e.line-t.line||e.ch-t.ch}function lt(e,t){return e.sticky==t.sticky&&0==at(e,t)}function st(e){return ot(e.line,e.ch)}function ct(e,t){return at(e,t)<0?t:e}function ut(e,t){return at(e,t)<0?e:t}function dt(e,t){return Math.max(e.first,Math.min(t,e.first+e.size-1))}function ft(e,t){if(t.line<e.first)return ot(e.first,0);var n=e.first+e.size-1;return t.line>n?ot(n,Ze(e,n).text.length):function(e,t){var n=e.ch;return null==n||n>t?ot(e.line,t):n<0?ot(e.line,0):e}(t,Ze(e,t.line).text.length)}function ht(e,t){for(var n=[],r=0;r<t.length;r++)n[r]=ft(e,t[r]);return n}Xe.prototype.eol=function(){return this.pos>=this.string.length},Xe.prototype.sol=function(){return this.pos==this.lineStart},Xe.prototype.peek=function(){return this.string.charAt(this.pos)||void 0},Xe.prototype.next=function(){if(this.pos<this.string.length)return this.string.charAt(this.pos++)},Xe.prototype.eat=function(e){var t=this.string.charAt(this.pos);if("string"==typeof e?t==e:t&&(e.test?e.test(t):e(t)))return++this.pos,t},Xe.prototype.eatWhile=function(e){for(var t=this.pos;this.eat(e););return this.pos>t},Xe.prototype.eatSpace=function(){for(var e=this.pos;/[\s\u00a0]/.test(this.string.charAt(this.pos));)++this.pos;return this.pos>e},Xe.prototype.skipToEnd=function(){this.pos=this.string.length},Xe.prototype.skipTo=function(e){var t=this.string.indexOf(e,this.pos);if(t>-1)return this.pos=t,!0},Xe.prototype.backUp=function(e){this.pos-=e},Xe.prototype.column=function(){return this.lastColumnPos<this.start&&(this.lastColumnValue=q(this.string,this.start,this.tabSize,this.lastColumnPos,this.lastColumnValue),this.lastColumnPos=this.start),this.lastColumnValue-(this.lineStart?q(this.string,this.lineStart,this.tabSize):0)},Xe.prototype.indentation=function(){return q(this.string,null,this.tabSize)-(this.lineStart?q(this.string,this.lineStart,this.tabSize):0)},Xe.prototype.match=function(e,t,n){if("string"!=typeof e){var r=this.string.slice(this.pos).match(e);return r&&r.index>0?null:(r&&!1!==t&&(this.pos+=r[0].length),r)}var i=function(e){return n?e.toLowerCase():e};if(i(this.string.substr(this.pos,e.length))==i(e))return!1!==t&&(this.pos+=e.length),!0},Xe.prototype.current=function(){return this.string.slice(this.start,this.pos)},Xe.prototype.hideFirstChars=function(e,t){this.lineStart+=e;try{return t()}finally{this.lineStart-=e}},Xe.prototype.lookAhead=function(e){var t=this.lineOracle;return t&&t.lookAhead(e)},Xe.prototype.baseToken=function(){var e=this.lineOracle;return e&&e.baseToken(this.pos)};var pt=function(e,t){this.state=e,this.lookAhead=t},mt=function(e,t,n,r){this.state=t,this.doc=e,this.line=n,this.maxLookAhead=r||0,this.baseTokens=null,this.baseTokenPos=1};function gt(e,t,n,r){var i=[e.state.modeGen],o={};Tt(e,t.text,e.doc.mode,n,function(e,t){return i.push(e,t)},o,r);for(var a=n.state,l=function(r){n.baseTokens=i;var l=e.state.overlays[r],s=1,c=0;n.state=!0,Tt(e,t.text,l.mode,n,function(e,t){for(var n=s;c<e;){var r=i[s];r>e&&i.splice(s,1,e,i[s+1],r),s+=2,c=Math.min(e,r)}if(t)if(l.opaque)i.splice(n,s-n,e,"overlay "+t),s=n+2;else for(;n<s;n+=2){var o=i[n+1];i[n+1]=(o?o+" ":"")+"overlay "+t}},o),n.state=a,n.baseTokens=null,n.baseTokenPos=1},s=0;s<e.state.overlays.length;++s)l(s);return{styles:i,classes:o.bgClass||o.textClass?o:null}}function vt(e,t,n){if(!t.styles||t.styles[0]!=e.state.modeGen){var r=yt(e,tt(t)),i=t.text.length>e.options.maxHighlightLength&&Ge(e.doc.mode,r.state),o=gt(e,t,r);i&&(r.state=i),t.stateAfter=r.save(!i),t.styles=o.styles,o.classes?t.styleClasses=o.classes:t.styleClasses&&(t.styleClasses=null),n===e.doc.highlightFrontier&&(e.doc.modeFrontier=Math.max(e.doc.modeFrontier,++e.doc.highlightFrontier))}return t.styles}function yt(e,t,n){var r=e.doc,i=e.display;if(!r.mode.startState)return new mt(r,!0,t);var o=function(e,t,n){for(var r,i,o=e.doc,a=n?-1:t-(e.doc.mode.innerMode?1e3:100),l=t;l>a;--l){if(l<=o.first)return o.first;var s=Ze(o,l-1),c=s.stateAfter;if(c&&(!n||l+(c instanceof pt?c.lookAhead:0)<=o.modeFrontier))return l;var u=q(s.text,null,e.options.tabSize);(null==i||r>u)&&(i=l-1,r=u)}return i}(e,t,n),a=o>r.first&&Ze(r,o-1).stateAfter,l=a?mt.fromSaved(r,a,o):new mt(r,Ye(r.mode),o);return r.iter(o,t,function(n){bt(e,n.text,l);var r=l.line;n.stateAfter=r==t-1||r%5==0||r>=i.viewFrom&&r<i.viewTo?l.save():null,l.nextLine()}),n&&(r.modeFrontier=l.line),l}function bt(e,t,n,r){var i=e.doc.mode,o=new Xe(t,e.options.tabSize,n);for(o.start=o.pos=r||0,""==t&&wt(i,n.state);!o.eol();)xt(i,o,n.state),o.start=o.pos}function wt(e,t){if(e.blankLine)return e.blankLine(t);if(e.innerMode){var n=$e(e,t);return n.mode.blankLine?n.mode.blankLine(n.state):void 0}}function xt(e,t,n,r){for(var i=0;i<10;i++){r&&(r[0]=$e(e,n).mode);var o=e.token(t,n);if(t.pos>t.start)return o}throw new Error("Mode "+e.name+" failed to advance stream.")}mt.prototype.lookAhead=function(e){var t=this.doc.getLine(this.line+e);return null!=t&&e>this.maxLookAhead&&(this.maxLookAhead=e),t},mt.prototype.baseToken=function(e){if(!this.baseTokens)return null;for(;this.baseTokens[this.baseTokenPos]<=e;)this.baseTokenPos+=2;var t=this.baseTokens[this.baseTokenPos+1];return{type:t&&t.replace(/( |^)overlay .*/,""),size:this.baseTokens[this.baseTokenPos]-e}},mt.prototype.nextLine=function(){this.line++,this.maxLookAhead>0&&this.maxLookAhead--},mt.fromSaved=function(e,t,n){return t instanceof pt?new mt(e,Ge(e.mode,t.state),n,t.lookAhead):new mt(e,Ge(e.mode,t),n)},mt.prototype.save=function(e){var t=!1!==e?Ge(this.doc.mode,this.state):this.state;return this.maxLookAhead>0?new pt(t,this.maxLookAhead):t};var kt=function(e,t,n){this.start=e.start,this.end=e.pos,this.string=e.current(),this.type=t||null,this.state=n};function Ct(e,t,n,r){var i,o,a=e.doc,l=a.mode,s=Ze(a,(t=ft(a,t)).line),c=yt(e,t.line,n),u=new Xe(s.text,e.options.tabSize,c);for(r&&(o=[]);(r||u.pos<t.ch)&&!u.eol();)u.start=u.pos,i=xt(l,u,c.state),r&&o.push(new kt(u,i,Ge(a.mode,c.state)));return r?o:new kt(u,i,c.state)}function St(e,t){if(e)for(;;){var n=e.match(/(?:^|\s+)line-(background-)?(\S+)/);if(!n)break;e=e.slice(0,n.index)+e.slice(n.index+n[0].length);var r=n[1]?"bgClass":"textClass";null==t[r]?t[r]=n[2]:new RegExp("(?:^|\\s)"+n[2]+"(?:$|\\s)").test(t[r])||(t[r]+=" "+n[2])}return e}function Tt(e,t,n,r,i,o,a){var l=n.flattenSpans;null==l&&(l=e.options.flattenSpans);var s,c=0,u=null,d=new Xe(t,e.options.tabSize,r),f=e.options.addModeClass&&[null];for(""==t&&St(wt(n,r.state),o);!d.eol();){if(d.pos>e.options.maxHighlightLength?(l=!1,a&&bt(e,t,r,d.pos),d.pos=t.length,s=null):s=St(xt(n,d,r.state,f),o),f){var h=f[0].name;h&&(s="m-"+(s?h+" "+s:h))}if(!l||u!=s){for(;c<d.start;)i(c=Math.min(d.start,c+5e3),u);u=s}d.start=d.pos}for(;c<d.pos;){var p=Math.min(d.pos,c+5e3);i(p,u),c=p}}var Lt=!1,Mt=!1;function At(e,t,n){this.marker=e,this.from=t,this.to=n}function Nt(e,t){if(e)for(var n=0;n<e.length;++n){var r=e[n];if(r.marker==t)return r}}function Ot(e,t){for(var n,r=0;r<e.length;++r)e[r]!=t&&(n||(n=[])).push(e[r]);return n}function _t(e,t){if(t.full)return null;var n=rt(e,t.from.line)&&Ze(e,t.from.line).markedSpans,r=rt(e,t.to.line)&&Ze(e,t.to.line).markedSpans;if(!n&&!r)return null;var i=t.from.ch,o=t.to.ch,a=0==at(t.from,t.to),l=function(e,t,n){var r;if(e)for(var i=0;i<e.length;++i){var o=e[i],a=o.marker;if(null==o.from||(a.inclusiveLeft?o.from<=t:o.from<t)||o.from==t&&"bookmark"==a.type&&(!n||!o.marker.insertLeft)){var l=null==o.to||(a.inclusiveRight?o.to>=t:o.to>t);(r||(r=[])).push(new At(a,o.from,l?null:o.to))}}return r}(n,i,a),s=function(e,t,n){var r;if(e)for(var i=0;i<e.length;++i){var o=e[i],a=o.marker;if(null==o.to||(a.inclusiveRight?o.to>=t:o.to>t)||o.from==t&&"bookmark"==a.type&&(!n||o.marker.insertLeft)){var l=null==o.from||(a.inclusiveLeft?o.from<=t:o.from<t);(r||(r=[])).push(new At(a,l?null:o.from-t,null==o.to?null:o.to-t))}}return r}(r,o,a),c=1==t.text.length,u=Q(t.text).length+(c?i:0);if(l)for(var d=0;d<l.length;++d){var f=l[d];if(null==f.to){var h=Nt(s,f.marker);h?c&&(f.to=null==h.to?null:h.to+u):f.to=i}}if(s)for(var p=0;p<s.length;++p){var m=s[p];null!=m.to&&(m.to+=u),null==m.from?Nt(l,m.marker)||(m.from=u,c&&(l||(l=[])).push(m)):(m.from+=u,c&&(l||(l=[])).push(m))}l&&(l=Et(l)),s&&s!=l&&(s=Et(s));var g=[l];if(!c){var v,y=t.text.length-2;if(y>0&&l)for(var b=0;b<l.length;++b)null==l[b].to&&(v||(v=[])).push(new At(l[b].marker,null,null));for(var w=0;w<y;++w)g.push(v);g.push(s)}return g}function Et(e){for(var t=0;t<e.length;++t){var n=e[t];null!=n.from&&n.from==n.to&&!1!==n.marker.clearWhenEmpty&&e.splice(t--,1)}return e.length?e:null}function zt(e){var t=e.markedSpans;if(t){for(var n=0;n<t.length;++n)t[n].marker.detachLine(e);e.markedSpans=null}}function Dt(e,t){if(t){for(var n=0;n<t.length;++n)t[n].marker.attachLine(e);e.markedSpans=t}}function Pt(e){return e.inclusiveLeft?-1:0}function Wt(e){return e.inclusiveRight?1:0}function Ft(e,t){var n=e.lines.length-t.lines.length;if(0!=n)return n;var r=e.find(),i=t.find(),o=at(r.from,i.from)||Pt(e)-Pt(t);return o?-o:at(r.to,i.to)||Wt(e)-Wt(t)||t.id-e.id}function It(e,t){var n,r=Mt&&e.markedSpans;if(r)for(var i=void 0,o=0;o<r.length;++o)(i=r[o]).marker.collapsed&&null==(t?i.from:i.to)&&(!n||Ft(n,i.marker)<0)&&(n=i.marker);return n}function Ht(e){return It(e,!0)}function Rt(e){return It(e,!1)}function jt(e,t){var n,r=Mt&&e.markedSpans;if(r)for(var i=0;i<r.length;++i){var o=r[i];o.marker.collapsed&&(null==o.from||o.from<t)&&(null==o.to||o.to>t)&&(!n||Ft(n,o.marker)<0)&&(n=o.marker)}return n}function Bt(e,t,n,r,i){var o=Ze(e,t),a=Mt&&o.markedSpans;if(a)for(var l=0;l<a.length;++l){var s=a[l];if(s.marker.collapsed){var c=s.marker.find(0),u=at(c.from,n)||Pt(s.marker)-Pt(i),d=at(c.to,r)||Wt(s.marker)-Wt(i);if(!(u>=0&&d<=0||u<=0&&d>=0)&&(u<=0&&(s.marker.inclusiveRight&&i.inclusiveLeft?at(c.to,n)>=0:at(c.to,n)>0)||u>=0&&(s.marker.inclusiveRight&&i.inclusiveLeft?at(c.from,r)<=0:at(c.from,r)<0)))return!0}}}function qt(e){for(var t;t=Ht(e);)e=t.find(-1,!0).line;return e}function Ut(e,t){var n=Ze(e,t),r=qt(n);return n==r?t:tt(r)}function Kt(e,t){if(t>e.lastLine())return t;var n,r=Ze(e,t);if(!Vt(e,r))return t;for(;n=Rt(r);)r=n.find(1,!0).line;return tt(r)+1}function Vt(e,t){var n=Mt&&t.markedSpans;if(n)for(var r=void 0,i=0;i<n.length;++i)if((r=n[i]).marker.collapsed){if(null==r.from)return!0;if(!r.marker.widgetNode&&0==r.from&&r.marker.inclusiveLeft&&Gt(e,t,r))return!0}}function Gt(e,t,n){if(null==n.to){var r=n.marker.find(1,!0);return Gt(e,r.line,Nt(r.line.markedSpans,n.marker))}if(n.marker.inclusiveRight&&n.to==t.text.length)return!0;for(var i=void 0,o=0;o<t.markedSpans.length;++o)if((i=t.markedSpans[o]).marker.collapsed&&!i.marker.widgetNode&&i.from==n.to&&(null==i.to||i.to!=n.from)&&(i.marker.inclusiveLeft||n.marker.inclusiveRight)&&Gt(e,t,i))return!0}function $t(e){for(var t=0,n=(e=qt(e)).parent,r=0;r<n.lines.length;++r){var i=n.lines[r];if(i==e)break;t+=i.height}for(var o=n.parent;o;o=(n=o).parent)for(var a=0;a<o.children.length;++a){var l=o.children[a];if(l==n)break;t+=l.height}return t}function Yt(e){if(0==e.height)return 0;for(var t,n=e.text.length,r=e;t=Ht(r);){var i=t.find(0,!0);r=i.from.line,n+=i.from.ch-i.to.ch}for(r=e;t=Rt(r);){var o=t.find(0,!0);n-=r.text.length-o.from.ch,n+=(r=o.to.line).text.length-o.to.ch}return n}function Xt(e){var t=e.display,n=e.doc;t.maxLine=Ze(n,n.first),t.maxLineLength=Yt(t.maxLine),t.maxLineChanged=!0,n.iter(function(e){var n=Yt(e);n>t.maxLineLength&&(t.maxLineLength=n,t.maxLine=e)})}var Zt=function(e,t,n){this.text=e,Dt(this,t),this.height=n?n(this):1};function Jt(e){e.parent=null,zt(e)}Zt.prototype.lineNo=function(){return tt(this)},Ce(Zt);var Qt={},en={};function tn(e,t){if(!e||/^\s*$/.test(e))return null;var n=t.addModeClass?en:Qt;return n[e]||(n[e]=e.replace(/\S+/g,"cm-$&"))}function nn(e,t){var n=_("span",null,null,s?"padding-right: .1px":null),r={pre:_("pre",[n],"CodeMirror-line"),content:n,col:0,pos:0,cm:e,trailingSpace:!1,splitSpaces:e.getOption("lineWrapping")};t.measure={};for(var i=0;i<=(t.rest?t.rest.length:0);i++){var o=i?t.rest[i-1]:t.line,a=void 0;r.pos=0,r.addToken=on,De(e.display.measure)&&(a=pe(o,e.doc.direction))&&(r.addToken=an(r.addToken,a)),r.map=[],sn(o,r,vt(e,o,t!=e.display.externalMeasured&&tt(o))),o.styleClasses&&(o.styleClasses.bgClass&&(r.bgClass=P(o.styleClasses.bgClass,r.bgClass||"")),o.styleClasses.textClass&&(r.textClass=P(o.styleClasses.textClass,r.textClass||""))),0==r.map.length&&r.map.push(0,0,r.content.appendChild(ze(e.display.measure))),0==i?(t.measure.map=r.map,t.measure.cache={}):((t.measure.maps||(t.measure.maps=[])).push(r.map),(t.measure.caches||(t.measure.caches=[])).push({}))}if(s){var l=r.content.lastChild;(/\bcm-tab\b/.test(l.className)||l.querySelector&&l.querySelector(".cm-tab"))&&(r.content.className="cm-tab-wrap-hack")}return be(e,"renderLine",e,t.line,r.pre),r.pre.className&&(r.textClass=P(r.pre.className,r.textClass||"")),r}function rn(e){var t=O("span","•","cm-invalidchar");return t.title="\\u"+e.charCodeAt(0).toString(16),t.setAttribute("aria-label",t.title),t}function on(e,t,n,r,i,o,s){if(t){var c,u=e.splitSpaces?function(e,t){if(e.length>1&&!/  /.test(e))return e;for(var n=t,r="",i=0;i<e.length;i++){var o=e.charAt(i);" "!=o||!n||i!=e.length-1&&32!=e.charCodeAt(i+1)||(o=" "),r+=o,n=" "==o}return r}(t,e.trailingSpace):t,d=e.cm.state.specialChars,f=!1;if(d.test(t)){c=document.createDocumentFragment();for(var h=0;;){d.lastIndex=h;var p=d.exec(t),m=p?p.index-h:t.length-h;if(m){var g=document.createTextNode(u.slice(h,h+m));a&&l<9?c.appendChild(O("span",[g])):c.appendChild(g),e.map.push(e.pos,e.pos+m,g),e.col+=m,e.pos+=m}if(!p)break;h+=m+1;var v=void 0;if("\t"==p[0]){var y=e.cm.options.tabSize,b=y-e.col%y;(v=c.appendChild(O("span",J(b),"cm-tab"))).setAttribute("role","presentation"),v.setAttribute("cm-text","\t"),e.col+=b}else"\r"==p[0]||"\n"==p[0]?((v=c.appendChild(O("span","\r"==p[0]?"␍":"␤","cm-invalidchar"))).setAttribute("cm-text",p[0]),e.col+=1):((v=e.cm.options.specialCharPlaceholder(p[0])).setAttribute("cm-text",p[0]),a&&l<9?c.appendChild(O("span",[v])):c.appendChild(v),e.col+=1);e.map.push(e.pos,e.pos+1,v),e.pos++}}else e.col+=t.length,c=document.createTextNode(u),e.map.push(e.pos,e.pos+t.length,c),a&&l<9&&(f=!0),e.pos+=t.length;if(e.trailingSpace=32==u.charCodeAt(t.length-1),n||r||i||f||o||s){var w=n||"";r&&(w+=r),i&&(w+=i);var x=O("span",[c],w,o);if(s)for(var k in s)s.hasOwnProperty(k)&&"style"!=k&&"class"!=k&&x.setAttribute(k,s[k]);return e.content.appendChild(x)}e.content.appendChild(c)}}function an(e,t){return function(n,r,i,o,a,l,s){i=i?i+" cm-force-border":"cm-force-border";for(var c=n.pos,u=c+r.length;;){for(var d=void 0,f=0;f<t.length&&!((d=t[f]).to>c&&d.from<=c);f++);if(d.to>=u)return e(n,r,i,o,a,l,s);e(n,r.slice(0,d.to-c),i,o,null,l,s),o=null,r=r.slice(d.to-c),c=d.to}}}function ln(e,t,n,r){var i=!r&&n.widgetNode;i&&e.map.push(e.pos,e.pos+t,i),!r&&e.cm.display.input.needsContentAttribute&&(i||(i=e.content.appendChild(document.createElement("span"))),i.setAttribute("cm-marker",n.id)),i&&(e.cm.display.input.setUneditable(i),e.content.appendChild(i)),e.pos+=t,e.trailingSpace=!1}function sn(e,t,n){var r=e.markedSpans,i=e.text,o=0;if(r)for(var a,l,s,c,u,d,f,h=i.length,p=0,m=1,g="",v=0;;){if(v==p){s=c=u=l="",f=null,d=null,v=1/0;for(var y=[],b=void 0,w=0;w<r.length;++w){var x=r[w],k=x.marker;if("bookmark"==k.type&&x.from==p&&k.widgetNode)y.push(k);else if(x.from<=p&&(null==x.to||x.to>p||k.collapsed&&x.to==p&&x.from==p)){if(null!=x.to&&x.to!=p&&v>x.to&&(v=x.to,c=""),k.className&&(s+=" "+k.className),k.css&&(l=(l?l+";":"")+k.css),k.startStyle&&x.from==p&&(u+=" "+k.startStyle),k.endStyle&&x.to==v&&(b||(b=[])).push(k.endStyle,x.to),k.title&&((f||(f={})).title=k.title),k.attributes)for(var C in k.attributes)(f||(f={}))[C]=k.attributes[C];k.collapsed&&(!d||Ft(d.marker,k)<0)&&(d=x)}else x.from>p&&v>x.from&&(v=x.from)}if(b)for(var S=0;S<b.length;S+=2)b[S+1]==v&&(c+=" "+b[S]);if(!d||d.from==p)for(var T=0;T<y.length;++T)ln(t,0,y[T]);if(d&&(d.from||0)==p){if(ln(t,(null==d.to?h+1:d.to)-p,d.marker,null==d.from),null==d.to)return;d.to==p&&(d=!1)}}if(p>=h)break;for(var L=Math.min(h,v);;){if(g){var M=p+g.length;if(!d){var A=M>L?g.slice(0,L-p):g;t.addToken(t,A,a?a+s:s,u,p+A.length==v?c:"",l,f)}if(M>=L){g=g.slice(L-p),p=L;break}p=M,u=""}g=i.slice(o,o=n[m++]),a=tn(n[m++],t.cm.options)}}else for(var N=1;N<n.length;N+=2)t.addToken(t,i.slice(o,o=n[N]),tn(n[N+1],t.cm.options))}function cn(e,t,n){this.line=t,this.rest=function(e){for(var t,n;t=Rt(e);)e=t.find(1,!0).line,(n||(n=[])).push(e);return n}(t),this.size=this.rest?tt(Q(this.rest))-n+1:1,this.node=this.text=null,this.hidden=Vt(e,t)}function un(e,t,n){for(var r,i=[],o=t;o<n;o=r){var a=new cn(e.doc,Ze(e.doc,o),o);r=o+a.size,i.push(a)}return i}var dn=null;var fn=null;function hn(e,t){var n=ve(e,t);if(n.length){var r,i=Array.prototype.slice.call(arguments,2);dn?r=dn.delayedCallbacks:fn?r=fn:(r=fn=[],setTimeout(pn,0));for(var o=function(e){r.push(function(){return n[e].apply(null,i)})},a=0;a<n.length;++a)o(a)}}function pn(){var e=fn;fn=null;for(var t=0;t<e.length;++t)e[t]()}function mn(e,t,n,r){for(var i=0;i<t.changes.length;i++){var o=t.changes[i];"text"==o?yn(e,t):"gutter"==o?wn(e,t,n,r):"class"==o?bn(e,t):"widget"==o&&xn(e,t,r)}t.changes=null}function gn(e){return e.node==e.text&&(e.node=O("div",null,null,"position: relative"),e.text.parentNode&&e.text.parentNode.replaceChild(e.node,e.text),e.node.appendChild(e.text),a&&l<8&&(e.node.style.zIndex=2)),e.node}function vn(e,t){var n=e.display.externalMeasured;return n&&n.line==t.line?(e.display.externalMeasured=null,t.measure=n.measure,n.built):nn(e,t)}function yn(e,t){var n=t.text.className,r=vn(e,t);t.text==t.node&&(t.node=r.pre),t.text.parentNode.replaceChild(r.pre,t.text),t.text=r.pre,r.bgClass!=t.bgClass||r.textClass!=t.textClass?(t.bgClass=r.bgClass,t.textClass=r.textClass,bn(e,t)):n&&(t.text.className=n)}function bn(e,t){(function(e,t){var n=t.bgClass?t.bgClass+" "+(t.line.bgClass||""):t.line.bgClass;if(n&&(n+=" CodeMirror-linebackground"),t.background)n?t.background.className=n:(t.background.parentNode.removeChild(t.background),t.background=null);else if(n){var r=gn(t);t.background=r.insertBefore(O("div",null,n),r.firstChild),e.display.input.setUneditable(t.background)}})(e,t),t.line.wrapClass?gn(t).className=t.line.wrapClass:t.node!=t.text&&(t.node.className="");var n=t.textClass?t.textClass+" "+(t.line.textClass||""):t.line.textClass;t.text.className=n||""}function wn(e,t,n,r){if(t.gutter&&(t.node.removeChild(t.gutter),t.gutter=null),t.gutterBackground&&(t.node.removeChild(t.gutterBackground),t.gutterBackground=null),t.line.gutterClass){var i=gn(t);t.gutterBackground=O("div",null,"CodeMirror-gutter-background "+t.line.gutterClass,"left: "+(e.options.fixedGutter?r.fixedPos:-r.gutterTotalWidth)+"px; width: "+r.gutterTotalWidth+"px"),e.display.input.setUneditable(t.gutterBackground),i.insertBefore(t.gutterBackground,t.text)}var o=t.line.gutterMarkers;if(e.options.lineNumbers||o){var a=gn(t),l=t.gutter=O("div",null,"CodeMirror-gutter-wrapper","left: "+(e.options.fixedGutter?r.fixedPos:-r.gutterTotalWidth)+"px");if(l.setAttribute("aria-hidden","true"),e.display.input.setUneditable(l),a.insertBefore(l,t.text),t.line.gutterClass&&(l.className+=" "+t.line.gutterClass),!e.options.lineNumbers||o&&o["CodeMirror-linenumbers"]||(t.lineNumber=l.appendChild(O("div",it(e.options,n),"CodeMirror-linenumber CodeMirror-gutter-elt","left: "+r.gutterLeft["CodeMirror-linenumbers"]+"px; width: "+e.display.lineNumInnerWidth+"px"))),o)for(var s=0;s<e.display.gutterSpecs.length;++s){var c=e.display.gutterSpecs[s].className,u=o.hasOwnProperty(c)&&o[c];u&&l.appendChild(O("div",[u],"CodeMirror-gutter-elt","left: "+r.gutterLeft[c]+"px; width: "+r.gutterWidth[c]+"px"))}}}function xn(e,t,n){t.alignable&&(t.alignable=null);for(var r=T("CodeMirror-linewidget"),i=t.node.firstChild,o=void 0;i;i=o)o=i.nextSibling,r.test(i.className)&&t.node.removeChild(i);Cn(e,t,n)}function kn(e,t,n,r){var i=vn(e,t);return t.text=t.node=i.pre,i.bgClass&&(t.bgClass=i.bgClass),i.textClass&&(t.textClass=i.textClass),bn(e,t),wn(e,t,n,r),Cn(e,t,r),t.node}function Cn(e,t,n){if(Sn(e,t.line,t,n,!0),t.rest)for(var r=0;r<t.rest.length;r++)Sn(e,t.rest[r],t,n,!1)}function Sn(e,t,n,r,i){if(t.widgets)for(var o=gn(n),a=0,l=t.widgets;a<l.length;++a){var s=l[a],c=O("div",[s.node],"CodeMirror-linewidget"+(s.className?" "+s.className:""));s.handleMouseEvents||c.setAttribute("cm-ignore-events","true"),Tn(s,c,n,r),e.display.input.setUneditable(c),i&&s.above?o.insertBefore(c,n.gutter||n.text):o.appendChild(c),hn(s,"redraw")}}function Tn(e,t,n,r){if(e.noHScroll){(n.alignable||(n.alignable=[])).push(t);var i=r.wrapperWidth;t.style.left=r.fixedPos+"px",e.coverGutter||(i-=r.gutterTotalWidth,t.style.paddingLeft=r.gutterTotalWidth+"px"),t.style.width=i+"px"}e.coverGutter&&(t.style.zIndex=5,t.style.position="relative",e.noHScroll||(t.style.marginLeft=-r.gutterTotalWidth+"px"))}function Ln(e){if(null!=e.height)return e.height;var t=e.doc.cm;if(!t)return 0;if(!E(document.body,e.node)){var n="position: relative;";e.coverGutter&&(n+="margin-left: -"+t.display.gutters.offsetWidth+"px;"),e.noHScroll&&(n+="width: "+t.display.wrapper.clientWidth+"px;"),N(t.display.measure,O("div",[e.node],null,n))}return e.height=e.node.parentNode.offsetHeight}function Mn(e,t){for(var n=Ae(t);n!=e.wrapper;n=n.parentNode)if(!n||1==n.nodeType&&"true"==n.getAttribute("cm-ignore-events")||n.parentNode==e.sizer&&n!=e.mover)return!0}function An(e){return e.lineSpace.offsetTop}function Nn(e){return e.mover.offsetHeight-e.lineSpace.offsetHeight}function On(e){if(e.cachedPaddingH)return e.cachedPaddingH;var t=N(e.measure,O("pre","x","CodeMirror-line-like")),n=window.getComputedStyle?window.getComputedStyle(t):t.currentStyle,r={left:parseInt(n.paddingLeft),right:parseInt(n.paddingRight)};return isNaN(r.left)||isNaN(r.right)||(e.cachedPaddingH=r),r}function _n(e){return 50-e.display.nativeBarWidth}function En(e){return e.display.scroller.clientWidth-_n(e)-e.display.barWidth}function zn(e){return e.display.scroller.clientHeight-_n(e)-e.display.barHeight}function Dn(e,t,n){if(e.line==t)return{map:e.measure.map,cache:e.measure.cache};if(e.rest){for(var r=0;r<e.rest.length;r++)if(e.rest[r]==t)return{map:e.measure.maps[r],cache:e.measure.caches[r]};for(var i=0;i<e.rest.length;i++)if(tt(e.rest[i])>n)return{map:e.measure.maps[i],cache:e.measure.caches[i],before:!0}}}function Pn(e,t,n,r){return In(e,Fn(e,t),n,r)}function Wn(e,t){if(t>=e.display.viewFrom&&t<e.display.viewTo)return e.display.view[mr(e,t)];var n=e.display.externalMeasured;return n&&t>=n.lineN&&t<n.lineN+n.size?n:void 0}function Fn(e,t){var n=tt(t),r=Wn(e,n);r&&!r.text?r=null:r&&r.changes&&(mn(e,r,n,ur(e)),e.curOp.forceUpdate=!0),r||(r=function(e,t){var n=tt(t=qt(t)),r=e.display.externalMeasured=new cn(e.doc,t,n);r.lineN=n;var i=r.built=nn(e,r);return r.text=i.pre,N(e.display.lineMeasure,i.pre),r}(e,t));var i=Dn(r,t,n);return{line:t,view:r,rect:null,map:i.map,cache:i.cache,before:i.before,hasHeights:!1}}function In(e,t,n,r,i){t.before&&(n=-1);var o,s=n+(r||"");return t.cache.hasOwnProperty(s)?o=t.cache[s]:(t.rect||(t.rect=t.view.text.getBoundingClientRect()),t.hasHeights||(function(e,t,n){var r=e.options.lineWrapping,i=r&&En(e);if(!t.measure.heights||r&&t.measure.width!=i){var o=t.measure.heights=[];if(r){t.measure.width=i;for(var a=t.text.firstChild.getClientRects(),l=0;l<a.length-1;l++){var s=a[l],c=a[l+1];Math.abs(s.bottom-c.bottom)>2&&o.push((s.bottom+c.top)/2-n.top)}}o.push(n.bottom-n.top)}}(e,t.view,t.rect),t.hasHeights=!0),(o=function(e,t,n,r){var i,o=jn(t.map,n,r),s=o.node,c=o.start,u=o.end,d=o.collapse;if(3==s.nodeType){for(var f=0;f<4;f++){for(;c&&se(t.line.text.charAt(o.coverStart+c));)--c;for(;o.coverStart+u<o.coverEnd&&se(t.line.text.charAt(o.coverStart+u));)++u;if((i=a&&l<9&&0==c&&u==o.coverEnd-o.coverStart?s.parentNode.getBoundingClientRect():Bn(L(s,c,u).getClientRects(),r)).left||i.right||0==c)break;u=c,c-=1,d="right"}a&&l<11&&(i=function(e,t){if(!window.screen||null==screen.logicalXDPI||screen.logicalXDPI==screen.deviceXDPI||!function(e){if(null!=He)return He;var t=N(e,O("span","x")),n=t.getBoundingClientRect(),r=L(t,0,1).getBoundingClientRect();return He=Math.abs(n.left-r.left)>1}(e))return t;var n=screen.logicalXDPI/screen.deviceXDPI,r=screen.logicalYDPI/screen.deviceYDPI;return{left:t.left*n,right:t.right*n,top:t.top*r,bottom:t.bottom*r}}(e.display.measure,i))}else{var h;c>0&&(d=r="right"),i=e.options.lineWrapping&&(h=s.getClientRects()).length>1?h["right"==r?h.length-1:0]:s.getBoundingClientRect()}if(a&&l<9&&!c&&(!i||!i.left&&!i.right)){var p=s.parentNode.getClientRects()[0];i=p?{left:p.left,right:p.left+cr(e.display),top:p.top,bottom:p.bottom}:Rn}for(var m=i.top-t.rect.top,g=i.bottom-t.rect.top,v=(m+g)/2,y=t.view.measure.heights,b=0;b<y.length-1&&!(v<y[b]);b++);var w=b?y[b-1]:0,x=y[b],k={left:("right"==d?i.right:i.left)-t.rect.left,right:("left"==d?i.left:i.right)-t.rect.left,top:w,bottom:x};return i.left||i.right||(k.bogus=!0),e.options.singleCursorHeightPerLine||(k.rtop=m,k.rbottom=g),k}(e,t,n,r)).bogus||(t.cache[s]=o)),{left:o.left,right:o.right,top:i?o.rtop:o.top,bottom:i?o.rbottom:o.bottom}}var Hn,Rn={left:0,right:0,top:0,bottom:0};function jn(e,t,n){for(var r,i,o,a,l,s,c=0;c<e.length;c+=3)if(l=e[c],s=e[c+1],t<l?(i=0,o=1,a="left"):t<s?o=1+(i=t-l):(c==e.length-3||t==s&&e[c+3]>t)&&(i=(o=s-l)-1,t>=s&&(a="right")),null!=i){if(r=e[c+2],l==s&&n==(r.insertLeft?"left":"right")&&(a=n),"left"==n&&0==i)for(;c&&e[c-2]==e[c-3]&&e[c-1].insertLeft;)r=e[2+(c-=3)],a="left";if("right"==n&&i==s-l)for(;c<e.length-3&&e[c+3]==e[c+4]&&!e[c+5].insertLeft;)r=e[(c+=3)+2],a="right";break}return{node:r,start:i,end:o,collapse:a,coverStart:l,coverEnd:s}}function Bn(e,t){var n=Rn;if("left"==t)for(var r=0;r<e.length&&(n=e[r]).left==n.right;r++);else for(var i=e.length-1;i>=0&&(n=e[i]).left==n.right;i--);return n}function qn(e){if(e.measure&&(e.measure.cache={},e.measure.heights=null,e.rest))for(var t=0;t<e.rest.length;t++)e.measure.caches[t]={}}function Un(e){e.display.externalMeasure=null,A(e.display.lineMeasure);for(var t=0;t<e.display.view.length;t++)qn(e.display.view[t])}function Kn(e){Un(e),e.display.cachedCharWidth=e.display.cachedTextHeight=e.display.cachedPaddingH=null,e.options.lineWrapping||(e.display.maxLineChanged=!0),e.display.lineNumChars=null}function Vn(e){return u&&v?-(e.body.getBoundingClientRect().left-parseInt(getComputedStyle(e.body).marginLeft)):e.defaultView.pageXOffset||(e.documentElement||e.body).scrollLeft}function Gn(e){return u&&v?-(e.body.getBoundingClientRect().top-parseInt(getComputedStyle(e.body).marginTop)):e.defaultView.pageYOffset||(e.documentElement||e.body).scrollTop}function $n(e){var t=qt(e).widgets,n=0;if(t)for(var r=0;r<t.length;++r)t[r].above&&(n+=Ln(t[r]));return n}function Yn(e,t,n,r,i){if(!i){var o=$n(t);n.top+=o,n.bottom+=o}if("line"==r)return n;r||(r="local");var a=$t(t);if("local"==r?a+=An(e.display):a-=e.display.viewOffset,"page"==r||"window"==r){var l=e.display.lineSpace.getBoundingClientRect();a+=l.top+("window"==r?0:Gn(F(e)));var s=l.left+("window"==r?0:Vn(F(e)));n.left+=s,n.right+=s}return n.top+=a,n.bottom+=a,n}function Xn(e,t,n){if("div"==n)return t;var r=t.left,i=t.top;if("page"==n)r-=Vn(F(e)),i-=Gn(F(e));else if("local"==n||!n){var o=e.display.sizer.getBoundingClientRect();r+=o.left,i+=o.top}var a=e.display.lineSpace.getBoundingClientRect();return{left:r-a.left,top:i-a.top}}function Zn(e,t,n,r,i){return r||(r=Ze(e.doc,t.line)),Yn(e,r,Pn(e,r,t.ch,i),n)}function Jn(e,t,n,r,i,o){function a(t,a){var l=In(e,i,t,a?"right":"left",o);return a?l.left=l.right:l.right=l.left,Yn(e,r,l,n)}r=r||Ze(e.doc,t.line),i||(i=Fn(e,r));var l=pe(r,e.doc.direction),s=t.ch,c=t.sticky;if(s>=r.text.length?(s=r.text.length,c="before"):s<=0&&(s=0,c="after"),!l)return a("before"==c?s-1:s,"before"==c);function u(e,t,n){return a(n?e-1:e,1==l[t].level!=n)}var d=fe(l,s,c),f=de,h=u(s,d,"before"==c);return null!=f&&(h.other=u(s,f,"before"!=c)),h}function Qn(e,t){var n=0;t=ft(e.doc,t),e.options.lineWrapping||(n=cr(e.display)*t.ch);var r=Ze(e.doc,t.line),i=$t(r)+An(e.display);return{left:n,right:n,top:i,bottom:i+r.height}}function er(e,t,n,r,i){var o=ot(e,t,n);return o.xRel=i,r&&(o.outside=r),o}function tr(e,t,n){var r=e.doc;if((n+=e.display.viewOffset)<0)return er(r.first,0,null,-1,-1);var i=nt(r,n),o=r.first+r.size-1;if(i>o)return er(r.first+r.size-1,Ze(r,o).text.length,null,1,1);t<0&&(t=0);for(var a=Ze(r,i);;){var l=or(e,a,i,t,n),s=jt(a,l.ch+(l.xRel>0||l.outside>0?1:0));if(!s)return l;var c=s.find(1);if(c.line==i)return c;a=Ze(r,i=c.line)}}function nr(e,t,n,r){r-=$n(t);var i=t.text.length,o=ue(function(t){return In(e,n,t-1).bottom<=r},i,0);return{begin:o,end:i=ue(function(t){return In(e,n,t).top>r},o,i)}}function rr(e,t,n,r){return n||(n=Fn(e,t)),nr(e,t,n,Yn(e,t,In(e,n,r),"line").top)}function ir(e,t,n,r){return!(e.bottom<=n)&&(e.top>n||(r?e.left:e.right)>t)}function or(e,t,n,r,i){i-=$t(t);var o=Fn(e,t),a=$n(t),l=0,s=t.text.length,c=!0,u=pe(t,e.doc.direction);if(u){var d=(e.options.lineWrapping?lr:ar)(e,t,n,o,u,r,i);l=(c=1!=d.level)?d.from:d.to-1,s=c?d.to:d.from-1}var f,h,p=null,m=null,g=ue(function(t){var n=In(e,o,t);return n.top+=a,n.bottom+=a,!!ir(n,r,i,!1)&&(n.top<=i&&n.left<=r&&(p=t,m=n),!0)},l,s),v=!1;if(m){var y=r-m.left<m.right-r,b=y==c;g=p+(b?0:1),h=b?"after":"before",f=y?m.left:m.right}else{c||g!=s&&g!=l||g++,h=0==g?"after":g==t.text.length?"before":In(e,o,g-(c?1:0)).bottom+a<=i==c?"after":"before";var w=Jn(e,ot(n,g,h),"line",t,o);f=w.left,v=i<w.top?-1:i>=w.bottom?1:0}return er(n,g=ce(t.text,g,1),h,v,r-f)}function ar(e,t,n,r,i,o,a){var l=ue(function(l){var s=i[l],c=1!=s.level;return ir(Jn(e,ot(n,c?s.to:s.from,c?"before":"after"),"line",t,r),o,a,!0)},0,i.length-1),s=i[l];if(l>0){var c=1!=s.level,u=Jn(e,ot(n,c?s.from:s.to,c?"after":"before"),"line",t,r);ir(u,o,a,!0)&&u.top>a&&(s=i[l-1])}return s}function lr(e,t,n,r,i,o,a){var l=nr(e,t,r,a),s=l.begin,c=l.end;/\s/.test(t.text.charAt(c-1))&&c--;for(var u=null,d=null,f=0;f<i.length;f++){var h=i[f];if(!(h.from>=c||h.to<=s)){var p=In(e,r,1!=h.level?Math.min(c,h.to)-1:Math.max(s,h.from)).right,m=p<o?o-p+1e9:p-o;(!u||d>m)&&(u=h,d=m)}}return u||(u=i[i.length-1]),u.from<s&&(u={from:s,to:u.to,level:u.level}),u.to>c&&(u={from:u.from,to:c,level:u.level}),u}function sr(e){if(null!=e.cachedTextHeight)return e.cachedTextHeight;if(null==Hn){Hn=O("pre",null,"CodeMirror-line-like");for(var t=0;t<49;++t)Hn.appendChild(document.createTextNode("x")),Hn.appendChild(O("br"));Hn.appendChild(document.createTextNode("x"))}N(e.measure,Hn);var n=Hn.offsetHeight/50;return n>3&&(e.cachedTextHeight=n),A(e.measure),n||1}function cr(e){if(null!=e.cachedCharWidth)return e.cachedCharWidth;var t=O("span","xxxxxxxxxx"),n=O("pre",[t],"CodeMirror-line-like");N(e.measure,n);var r=t.getBoundingClientRect(),i=(r.right-r.left)/10;return i>2&&(e.cachedCharWidth=i),i||10}function ur(e){for(var t=e.display,n={},r={},i=t.gutters.clientLeft,o=t.gutters.firstChild,a=0;o;o=o.nextSibling,++a){var l=e.display.gutterSpecs[a].className;n[l]=o.offsetLeft+o.clientLeft+i,r[l]=o.clientWidth}return{fixedPos:dr(t),gutterTotalWidth:t.gutters.offsetWidth,gutterLeft:n,gutterWidth:r,wrapperWidth:t.wrapper.clientWidth}}function dr(e){return e.scroller.getBoundingClientRect().left-e.sizer.getBoundingClientRect().left}function fr(e){var t=sr(e.display),n=e.options.lineWrapping,r=n&&Math.max(5,e.display.scroller.clientWidth/cr(e.display)-3);return function(i){if(Vt(e.doc,i))return 0;var o=0;if(i.widgets)for(var a=0;a<i.widgets.length;a++)i.widgets[a].height&&(o+=i.widgets[a].height);return n?o+(Math.ceil(i.text.length/r)||1)*t:o+t}}function hr(e){var t=e.doc,n=fr(e);t.iter(function(e){var t=n(e);t!=e.height&&et(e,t)})}function pr(e,t,n,r){var i=e.display;if(!n&&"true"==Ae(t).getAttribute("cm-not-content"))return null;var o,a,l=i.lineSpace.getBoundingClientRect();try{o=t.clientX-l.left,a=t.clientY-l.top}catch(e){return null}var s,c=tr(e,o,a);if(r&&c.xRel>0&&(s=Ze(e.doc,c.line).text).length==c.ch){var u=q(s,s.length,e.options.tabSize)-s.length;c=ot(c.line,Math.max(0,Math.round((o-On(e.display).left)/cr(e.display))-u))}return c}function mr(e,t){if(t>=e.display.viewTo)return null;if((t-=e.display.viewFrom)<0)return null;for(var n=e.display.view,r=0;r<n.length;r++)if((t-=n[r].size)<0)return r}function gr(e,t,n,r){null==t&&(t=e.doc.first),null==n&&(n=e.doc.first+e.doc.size),r||(r=0);var i=e.display;if(r&&n<i.viewTo&&(null==i.updateLineNumbers||i.updateLineNumbers>t)&&(i.updateLineNumbers=t),e.curOp.viewChanged=!0,t>=i.viewTo)Mt&&Ut(e.doc,t)<i.viewTo&&yr(e);else if(n<=i.viewFrom)Mt&&Kt(e.doc,n+r)>i.viewFrom?yr(e):(i.viewFrom+=r,i.viewTo+=r);else if(t<=i.viewFrom&&n>=i.viewTo)yr(e);else if(t<=i.viewFrom){var o=br(e,n,n+r,1);o?(i.view=i.view.slice(o.index),i.viewFrom=o.lineN,i.viewTo+=r):yr(e)}else if(n>=i.viewTo){var a=br(e,t,t,-1);a?(i.view=i.view.slice(0,a.index),i.viewTo=a.lineN):yr(e)}else{var l=br(e,t,t,-1),s=br(e,n,n+r,1);l&&s?(i.view=i.view.slice(0,l.index).concat(un(e,l.lineN,s.lineN)).concat(i.view.slice(s.index)),i.viewTo+=r):yr(e)}var c=i.externalMeasured;c&&(n<c.lineN?c.lineN+=r:t<c.lineN+c.size&&(i.externalMeasured=null))}function vr(e,t,n){e.curOp.viewChanged=!0;var r=e.display,i=e.display.externalMeasured;if(i&&t>=i.lineN&&t<i.lineN+i.size&&(r.externalMeasured=null),!(t<r.viewFrom||t>=r.viewTo)){var o=r.view[mr(e,t)];if(null!=o.node){var a=o.changes||(o.changes=[]);-1==K(a,n)&&a.push(n)}}}function yr(e){e.display.viewFrom=e.display.viewTo=e.doc.first,e.display.view=[],e.display.viewOffset=0}function br(e,t,n,r){var i,o=mr(e,t),a=e.display.view;if(!Mt||n==e.doc.first+e.doc.size)return{index:o,lineN:n};for(var l=e.display.viewFrom,s=0;s<o;s++)l+=a[s].size;if(l!=t){if(r>0){if(o==a.length-1)return null;i=l+a[o].size-t,o++}else i=l-t;t+=i,n+=i}for(;Ut(e.doc,n)!=n;){if(o==(r<0?0:a.length-1))return null;n+=r*a[o-(r<0?1:0)].size,o+=r}return{index:o,lineN:n}}function wr(e){for(var t=e.display.view,n=0,r=0;r<t.length;r++){var i=t[r];i.hidden||i.node&&!i.changes||++n}return n}function xr(e){e.display.input.showSelection(e.display.input.prepareSelection())}function kr(e,t){void 0===t&&(t=!0);var n=e.doc,r={},i=r.cursors=document.createDocumentFragment(),o=r.selection=document.createDocumentFragment(),a=e.options.$customCursor;a&&(t=!0);for(var l=0;l<n.sel.ranges.length;l++)if(t||l!=n.sel.primIndex){var s=n.sel.ranges[l];if(!(s.from().line>=e.display.viewTo||s.to().line<e.display.viewFrom)){var c=s.empty();if(a){var u=a(e,s);u&&Cr(e,u,i)}else(c||e.options.showCursorWhenSelecting)&&Cr(e,s.head,i);c||Tr(e,s,o)}}return r}function Cr(e,t,n){var r=Jn(e,t,"div",null,null,!e.options.singleCursorHeightPerLine),i=n.appendChild(O("div"," ","CodeMirror-cursor"));if(i.style.left=r.left+"px",i.style.top=r.top+"px",i.style.height=Math.max(0,r.bottom-r.top)*e.options.cursorHeight+"px",/\bcm-fat-cursor\b/.test(e.getWrapperElement().className)){var o=Zn(e,t,"div",null,null),a=o.right-o.left;i.style.width=(a>0?a:e.defaultCharWidth())+"px"}if(r.other){var l=n.appendChild(O("div"," ","CodeMirror-cursor CodeMirror-secondarycursor"));l.style.display="",l.style.left=r.other.left+"px",l.style.top=r.other.top+"px",l.style.height=.85*(r.other.bottom-r.other.top)+"px"}}function Sr(e,t){return e.top-t.top||e.left-t.left}function Tr(e,t,n){var r=e.display,i=e.doc,o=document.createDocumentFragment(),a=On(e.display),l=a.left,s=Math.max(r.sizerWidth,En(e)-r.sizer.offsetLeft)-a.right,c="ltr"==i.direction;function u(e,t,n,r){t<0&&(t=0),t=Math.round(t),r=Math.round(r),o.appendChild(O("div",null,"CodeMirror-selected","position: absolute; left: "+e+"px;\n                             top: "+t+"px; width: "+(null==n?s-e:n)+"px;\n                             height: "+(r-t)+"px"))}function d(t,n,r){var o,a,d=Ze(i,t),f=d.text.length;function h(n,r){return Zn(e,ot(t,n),"div",d,r)}function p(t,n,r){var i=rr(e,d,null,t),o="ltr"==n==("after"==r)?"left":"right";return h("after"==r?i.begin:i.end-(/\s/.test(d.text.charAt(i.end-1))?2:1),o)[o]}var m=pe(d,i.direction);return function(e,t,n,r){if(!e)return r(t,n,"ltr",0);for(var i=!1,o=0;o<e.length;++o){var a=e[o];(a.from<n&&a.to>t||t==n&&a.to==t)&&(r(Math.max(a.from,t),Math.min(a.to,n),1==a.level?"rtl":"ltr",o),i=!0)}i||r(t,n,"ltr")}(m,n||0,null==r?f:r,function(e,t,i,d){var g="ltr"==i,v=h(e,g?"left":"right"),y=h(t-1,g?"right":"left"),b=null==n&&0==e,w=null==r&&t==f,x=0==d,k=!m||d==m.length-1;if(y.top-v.top<=3){var C=(c?w:b)&&k,S=(c?b:w)&&x?l:(g?v:y).left,T=C?s:(g?y:v).right;u(S,v.top,T-S,v.bottom)}else{var L,M,A,N;g?(L=c&&b&&x?l:v.left,M=c?s:p(e,i,"before"),A=c?l:p(t,i,"after"),N=c&&w&&k?s:y.right):(L=c?p(e,i,"before"):l,M=!c&&b&&x?s:v.right,A=!c&&w&&k?l:y.left,N=c?p(t,i,"after"):s),u(L,v.top,M-L,v.bottom),v.bottom<y.top&&u(l,v.bottom,null,y.top),u(A,y.top,N-A,y.bottom)}(!o||Sr(v,o)<0)&&(o=v),Sr(y,o)<0&&(o=y),(!a||Sr(v,a)<0)&&(a=v),Sr(y,a)<0&&(a=y)}),{start:o,end:a}}var f=t.from(),h=t.to();if(f.line==h.line)d(f.line,f.ch,h.ch);else{var p=Ze(i,f.line),m=Ze(i,h.line),g=qt(p)==qt(m),v=d(f.line,f.ch,g?p.text.length+1:null).end,y=d(h.line,g?0:null,h.ch).start;g&&(v.top<y.top-2?(u(v.right,v.top,null,v.bottom),u(l,y.top,y.left,y.bottom)):u(v.right,v.top,y.left-v.right,v.bottom)),v.bottom<y.top&&u(l,v.bottom,null,y.top)}n.appendChild(o)}function Lr(e){if(e.state.focused){var t=e.display;clearInterval(t.blinker);var n=!0;t.cursorDiv.style.visibility="",e.options.cursorBlinkRate>0?t.blinker=setInterval(function(){e.hasFocus()||Or(e),t.cursorDiv.style.visibility=(n=!n)?"":"hidden"},e.options.cursorBlinkRate):e.options.cursorBlinkRate<0&&(t.cursorDiv.style.visibility="hidden")}}function Mr(e){e.hasFocus()||(e.display.input.focus(),e.state.focused||Nr(e))}function Ar(e){e.state.delayingBlurEvent=!0,setTimeout(function(){e.state.delayingBlurEvent&&(e.state.delayingBlurEvent=!1,e.state.focused&&Or(e))},100)}function Nr(e,t){e.state.delayingBlurEvent&&!e.state.draggingText&&(e.state.delayingBlurEvent=!1),"nocursor"!=e.options.readOnly&&(e.state.focused||(be(e,"focus",e,t),e.state.focused=!0,D(e.display.wrapper,"CodeMirror-focused"),e.curOp||e.display.selForContextMenu==e.doc.sel||(e.display.input.reset(),s&&setTimeout(function(){return e.display.input.reset(!0)},20)),e.display.input.receivedFocus()),Lr(e))}function Or(e,t){e.state.delayingBlurEvent||(e.state.focused&&(be(e,"blur",e,t),e.state.focused=!1,M(e.display.wrapper,"CodeMirror-focused")),clearInterval(e.display.blinker),setTimeout(function(){e.state.focused||(e.display.shift=!1)},150))}function _r(e){for(var t=e.display,n=t.lineDiv.offsetTop,r=Math.max(0,t.scroller.getBoundingClientRect().top),i=t.lineDiv.getBoundingClientRect().top,o=0,s=0;s<t.view.length;s++){var c=t.view[s],u=e.options.lineWrapping,d=void 0,f=0;if(!c.hidden){if(i+=c.line.height,a&&l<8){var h=c.node.offsetTop+c.node.offsetHeight;d=h-n,n=h}else{var p=c.node.getBoundingClientRect();d=p.bottom-p.top,!u&&c.text.firstChild&&(f=c.text.firstChild.getBoundingClientRect().right-p.left-1)}var m=c.line.height-d;if((m>.005||m<-.005)&&(i<r&&(o-=m),et(c.line,d),Er(c.line),c.rest))for(var g=0;g<c.rest.length;g++)Er(c.rest[g]);if(f>e.display.sizerWidth){var v=Math.ceil(f/cr(e.display));v>e.display.maxLineLength&&(e.display.maxLineLength=v,e.display.maxLine=c.line,e.display.maxLineChanged=!0)}}}Math.abs(o)>2&&(t.scroller.scrollTop+=o)}function Er(e){if(e.widgets)for(var t=0;t<e.widgets.length;++t){var n=e.widgets[t],r=n.node.parentNode;r&&(n.height=r.offsetHeight)}}function zr(e,t,n){var r=n&&null!=n.top?Math.max(0,n.top):e.scroller.scrollTop;r=Math.floor(r-An(e));var i=n&&null!=n.bottom?n.bottom:r+e.wrapper.clientHeight,o=nt(t,r),a=nt(t,i);if(n&&n.ensure){var l=n.ensure.from.line,s=n.ensure.to.line;l<o?(o=l,a=nt(t,$t(Ze(t,l))+e.wrapper.clientHeight)):Math.min(s,t.lastLine())>=a&&(o=nt(t,$t(Ze(t,s))-e.wrapper.clientHeight),a=s)}return{from:o,to:Math.max(a,o+1)}}function Dr(e,t){var n=e.display,r=sr(e.display);t.top<0&&(t.top=0);var i=e.curOp&&null!=e.curOp.scrollTop?e.curOp.scrollTop:n.scroller.scrollTop,o=zn(e),a={};t.bottom-t.top>o&&(t.bottom=t.top+o);var l=e.doc.height+Nn(n),s=t.top<r,c=t.bottom>l-r;if(t.top<i)a.scrollTop=s?0:t.top;else if(t.bottom>i+o){var u=Math.min(t.top,(c?l:t.bottom)-o);u!=i&&(a.scrollTop=u)}var d=e.options.fixedGutter?0:n.gutters.offsetWidth,f=e.curOp&&null!=e.curOp.scrollLeft?e.curOp.scrollLeft:n.scroller.scrollLeft-d,h=En(e)-n.gutters.offsetWidth,p=t.right-t.left>h;return p&&(t.right=t.left+h),t.left<10?a.scrollLeft=0:t.left<f?a.scrollLeft=Math.max(0,t.left+d-(p?0:10)):t.right>h+f-3&&(a.scrollLeft=t.right+(p?0:10)-h),a}function Pr(e,t){null!=t&&(Ir(e),e.curOp.scrollTop=(null==e.curOp.scrollTop?e.doc.scrollTop:e.curOp.scrollTop)+t)}function Wr(e){Ir(e);var t=e.getCursor();e.curOp.scrollToPos={from:t,to:t,margin:e.options.cursorScrollMargin}}function Fr(e,t,n){null==t&&null==n||Ir(e),null!=t&&(e.curOp.scrollLeft=t),null!=n&&(e.curOp.scrollTop=n)}function Ir(e){var t=e.curOp.scrollToPos;t&&(e.curOp.scrollToPos=null,Hr(e,Qn(e,t.from),Qn(e,t.to),t.margin))}function Hr(e,t,n,r){var i=Dr(e,{left:Math.min(t.left,n.left),top:Math.min(t.top,n.top)-r,right:Math.max(t.right,n.right),bottom:Math.max(t.bottom,n.bottom)+r});Fr(e,i.scrollLeft,i.scrollTop)}function Rr(e,t){Math.abs(e.doc.scrollTop-t)<2||(n||hi(e,{top:t}),jr(e,t,!0),n&&hi(e),si(e,100))}function jr(e,t,n){t=Math.max(0,Math.min(e.display.scroller.scrollHeight-e.display.scroller.clientHeight,t)),(e.display.scroller.scrollTop!=t||n)&&(e.doc.scrollTop=t,e.display.scrollbars.setScrollTop(t),e.display.scroller.scrollTop!=t&&(e.display.scroller.scrollTop=t))}function Br(e,t,n,r){t=Math.max(0,Math.min(t,e.display.scroller.scrollWidth-e.display.scroller.clientWidth)),(n?t==e.doc.scrollLeft:Math.abs(e.doc.scrollLeft-t)<2)&&!r||(e.doc.scrollLeft=t,gi(e),e.display.scroller.scrollLeft!=t&&(e.display.scroller.scrollLeft=t),e.display.scrollbars.setScrollLeft(t))}function qr(e){var t=e.display,n=t.gutters.offsetWidth,r=Math.round(e.doc.height+Nn(e.display));return{clientHeight:t.scroller.clientHeight,viewHeight:t.wrapper.clientHeight,scrollWidth:t.scroller.scrollWidth,clientWidth:t.scroller.clientWidth,viewWidth:t.wrapper.clientWidth,barLeft:e.options.fixedGutter?n:0,docHeight:r,scrollHeight:r+_n(e)+t.barHeight,nativeBarWidth:t.nativeBarWidth,gutterWidth:n}}var Ur=function(e,t,n){this.cm=n;var r=this.vert=O("div",[O("div",null,null,"min-width: 1px")],"CodeMirror-vscrollbar"),i=this.horiz=O("div",[O("div",null,null,"height: 100%; min-height: 1px")],"CodeMirror-hscrollbar");r.tabIndex=i.tabIndex=-1,e(r),e(i),ge(r,"scroll",function(){r.clientHeight&&t(r.scrollTop,"vertical")}),ge(i,"scroll",function(){i.clientWidth&&t(i.scrollLeft,"horizontal")}),this.checkedZeroWidth=!1,a&&l<8&&(this.horiz.style.minHeight=this.vert.style.minWidth="18px")};Ur.prototype.update=function(e){var t=e.scrollWidth>e.clientWidth+1,n=e.scrollHeight>e.clientHeight+1,r=e.nativeBarWidth;if(n){this.vert.style.display="block",this.vert.style.bottom=t?r+"px":"0";var i=e.viewHeight-(t?r:0);this.vert.firstChild.style.height=Math.max(0,e.scrollHeight-e.clientHeight+i)+"px"}else this.vert.scrollTop=0,this.vert.style.display="",this.vert.firstChild.style.height="0";if(t){this.horiz.style.display="block",this.horiz.style.right=n?r+"px":"0",this.horiz.style.left=e.barLeft+"px";var o=e.viewWidth-e.barLeft-(n?r:0);this.horiz.firstChild.style.width=Math.max(0,e.scrollWidth-e.clientWidth+o)+"px"}else this.horiz.style.display="",this.horiz.firstChild.style.width="0";return!this.checkedZeroWidth&&e.clientHeight>0&&(0==r&&this.zeroWidthHack(),this.checkedZeroWidth=!0),{right:n?r:0,bottom:t?r:0}},Ur.prototype.setScrollLeft=function(e){this.horiz.scrollLeft!=e&&(this.horiz.scrollLeft=e),this.disableHoriz&&this.enableZeroWidthBar(this.horiz,this.disableHoriz,"horiz")},Ur.prototype.setScrollTop=function(e){this.vert.scrollTop!=e&&(this.vert.scrollTop=e),this.disableVert&&this.enableZeroWidthBar(this.vert,this.disableVert,"vert")},Ur.prototype.zeroWidthHack=function(){var e=b&&!p?"12px":"18px";this.horiz.style.height=this.vert.style.width=e,this.horiz.style.visibility=this.vert.style.visibility="hidden",this.disableHoriz=new U,this.disableVert=new U},Ur.prototype.enableZeroWidthBar=function(e,t,n){e.style.visibility="",t.set(1e3,function r(){var i=e.getBoundingClientRect();("vert"==n?document.elementFromPoint(i.right-1,(i.top+i.bottom)/2):document.elementFromPoint((i.right+i.left)/2,i.bottom-1))!=e?e.style.visibility="hidden":t.set(1e3,r)})},Ur.prototype.clear=function(){var e=this.horiz.parentNode;e.removeChild(this.horiz),e.removeChild(this.vert)};var Kr=function(){};function Vr(e,t){t||(t=qr(e));var n=e.display.barWidth,r=e.display.barHeight;Gr(e,t);for(var i=0;i<4&&n!=e.display.barWidth||r!=e.display.barHeight;i++)n!=e.display.barWidth&&e.options.lineWrapping&&_r(e),Gr(e,qr(e)),n=e.display.barWidth,r=e.display.barHeight}function Gr(e,t){var n=e.display,r=n.scrollbars.update(t);n.sizer.style.paddingRight=(n.barWidth=r.right)+"px",n.sizer.style.paddingBottom=(n.barHeight=r.bottom)+"px",n.heightForcer.style.borderBottom=r.bottom+"px solid transparent",r.right&&r.bottom?(n.scrollbarFiller.style.display="block",n.scrollbarFiller.style.height=r.bottom+"px",n.scrollbarFiller.style.width=r.right+"px"):n.scrollbarFiller.style.display="",r.bottom&&e.options.coverGutterNextToScrollbar&&e.options.fixedGutter?(n.gutterFiller.style.display="block",n.gutterFiller.style.height=r.bottom+"px",n.gutterFiller.style.width=t.gutterWidth+"px"):n.gutterFiller.style.display=""}Kr.prototype.update=function(){return{bottom:0,right:0}},Kr.prototype.setScrollLeft=function(){},Kr.prototype.setScrollTop=function(){},Kr.prototype.clear=function(){};var $r={native:Ur,null:Kr};function Yr(e){e.display.scrollbars&&(e.display.scrollbars.clear(),e.display.scrollbars.addClass&&M(e.display.wrapper,e.display.scrollbars.addClass)),e.display.scrollbars=new $r[e.options.scrollbarStyle](function(t){e.display.wrapper.insertBefore(t,e.display.scrollbarFiller),ge(t,"mousedown",function(){e.state.focused&&setTimeout(function(){return e.display.input.focus()},0)}),t.setAttribute("cm-not-content","true")},function(t,n){"horizontal"==n?Br(e,t):Rr(e,t)},e),e.display.scrollbars.addClass&&D(e.display.wrapper,e.display.scrollbars.addClass)}var Xr=0;function Zr(e){var t;e.curOp={cm:e,viewChanged:!1,startHeight:e.doc.height,forceUpdate:!1,updateInput:0,typing:!1,changeObjs:null,cursorActivityHandlers:null,cursorActivityCalled:0,selectionChanged:!1,updateMaxLine:!1,scrollLeft:null,scrollTop:null,scrollToPos:null,focus:!1,id:++Xr,markArrays:null},t=e.curOp,dn?dn.ops.push(t):t.ownsGroup=dn={ops:[t],delayedCallbacks:[]}}function Jr(e){var t=e.curOp;t&&function(e,t){var n=e.ownsGroup;if(n)try{!function(e){var t=e.delayedCallbacks,n=0;do{for(;n<t.length;n++)t[n].call(null);for(var r=0;r<e.ops.length;r++){var i=e.ops[r];if(i.cursorActivityHandlers)for(;i.cursorActivityCalled<i.cursorActivityHandlers.length;)i.cursorActivityHandlers[i.cursorActivityCalled++].call(null,i.cm)}}while(n<t.length)}(n)}finally{dn=null,t(n)}}(t,function(e){for(var t=0;t<e.ops.length;t++)e.ops[t].cm.curOp=null;!function(e){for(var t=e.ops,n=0;n<t.length;n++)Qr(t[n]);for(var r=0;r<t.length;r++)ei(t[r]);for(var i=0;i<t.length;i++)ti(t[i]);for(var o=0;o<t.length;o++)ni(t[o]);for(var a=0;a<t.length;a++)ri(t[a])}(e)})}function Qr(e){var t=e.cm,n=t.display;(function(e){var t=e.display;!t.scrollbarsClipped&&t.scroller.offsetWidth&&(t.nativeBarWidth=t.scroller.offsetWidth-t.scroller.clientWidth,t.heightForcer.style.height=_n(e)+"px",t.sizer.style.marginBottom=-t.nativeBarWidth+"px",t.sizer.style.borderRightWidth=_n(e)+"px",t.scrollbarsClipped=!0)})(t),e.updateMaxLine&&Xt(t),e.mustUpdate=e.viewChanged||e.forceUpdate||null!=e.scrollTop||e.scrollToPos&&(e.scrollToPos.from.line<n.viewFrom||e.scrollToPos.to.line>=n.viewTo)||n.maxLineChanged&&t.options.lineWrapping,e.update=e.mustUpdate&&new ui(t,e.mustUpdate&&{top:e.scrollTop,ensure:e.scrollToPos},e.forceUpdate)}function ei(e){e.updatedDisplay=e.mustUpdate&&di(e.cm,e.update)}function ti(e){var t=e.cm,n=t.display;e.updatedDisplay&&_r(t),e.barMeasure=qr(t),n.maxLineChanged&&!t.options.lineWrapping&&(e.adjustWidthTo=Pn(t,n.maxLine,n.maxLine.text.length).left+3,t.display.sizerWidth=e.adjustWidthTo,e.barMeasure.scrollWidth=Math.max(n.scroller.clientWidth,n.sizer.offsetLeft+e.adjustWidthTo+_n(t)+t.display.barWidth),e.maxScrollLeft=Math.max(0,n.sizer.offsetLeft+e.adjustWidthTo-En(t))),(e.updatedDisplay||e.selectionChanged)&&(e.preparedSelection=n.input.prepareSelection())}function ni(e){var t=e.cm;null!=e.adjustWidthTo&&(t.display.sizer.style.minWidth=e.adjustWidthTo+"px",e.maxScrollLeft<t.doc.scrollLeft&&Br(t,Math.min(t.display.scroller.scrollLeft,e.maxScrollLeft),!0),t.display.maxLineChanged=!1);var n=e.focus&&e.focus==z(I(t));e.preparedSelection&&t.display.input.showSelection(e.preparedSelection,n),(e.updatedDisplay||e.startHeight!=t.doc.height)&&Vr(t,e.barMeasure),e.updatedDisplay&&mi(t,e.barMeasure),e.selectionChanged&&Lr(t),t.state.focused&&e.updateInput&&t.display.input.reset(e.typing),n&&Mr(e.cm)}function ri(e){var t=e.cm,n=t.display,r=t.doc;e.updatedDisplay&&fi(t,e.update),null==n.wheelStartX||null==e.scrollTop&&null==e.scrollLeft&&!e.scrollToPos||(n.wheelStartX=n.wheelStartY=null),null!=e.scrollTop&&jr(t,e.scrollTop,e.forceScroll),null!=e.scrollLeft&&Br(t,e.scrollLeft,!0,!0),e.scrollToPos&&function(e,t){if(!we(e,"scrollCursorIntoView")){var n=e.display,r=n.sizer.getBoundingClientRect(),i=null,o=n.wrapper.ownerDocument;if(t.top+r.top<0?i=!0:t.bottom+r.top>(o.defaultView.innerHeight||o.documentElement.clientHeight)&&(i=!1),null!=i&&!m){var a=O("div","​",null,"position: absolute;\n                         top: "+(t.top-n.viewOffset-An(e.display))+"px;\n                         height: "+(t.bottom-t.top+_n(e)+n.barHeight)+"px;\n                         left: "+t.left+"px; width: "+Math.max(2,t.right-t.left)+"px;");e.display.lineSpace.appendChild(a),a.scrollIntoView(i),e.display.lineSpace.removeChild(a)}}}(t,function(e,t,n,r){var i;null==r&&(r=0),e.options.lineWrapping||t!=n||(n="before"==t.sticky?ot(t.line,t.ch+1,"before"):t,t=t.ch?ot(t.line,"before"==t.sticky?t.ch-1:t.ch,"after"):t);for(var o=0;o<5;o++){var a=!1,l=Jn(e,t),s=n&&n!=t?Jn(e,n):l,c=Dr(e,i={left:Math.min(l.left,s.left),top:Math.min(l.top,s.top)-r,right:Math.max(l.left,s.left),bottom:Math.max(l.bottom,s.bottom)+r}),u=e.doc.scrollTop,d=e.doc.scrollLeft;if(null!=c.scrollTop&&(Rr(e,c.scrollTop),Math.abs(e.doc.scrollTop-u)>1&&(a=!0)),null!=c.scrollLeft&&(Br(e,c.scrollLeft),Math.abs(e.doc.scrollLeft-d)>1&&(a=!0)),!a)break}return i}(t,ft(r,e.scrollToPos.from),ft(r,e.scrollToPos.to),e.scrollToPos.margin));var i=e.maybeHiddenMarkers,o=e.maybeUnhiddenMarkers;if(i)for(var a=0;a<i.length;++a)i[a].lines.length||be(i[a],"hide");if(o)for(var l=0;l<o.length;++l)o[l].lines.length&&be(o[l],"unhide");n.wrapper.offsetHeight&&(r.scrollTop=t.display.scroller.scrollTop),e.changeObjs&&be(t,"changes",t,e.changeObjs),e.update&&e.update.finish()}function ii(e,t){if(e.curOp)return t();Zr(e);try{return t()}finally{Jr(e)}}function oi(e,t){return function(){if(e.curOp)return t.apply(e,arguments);Zr(e);try{return t.apply(e,arguments)}finally{Jr(e)}}}function ai(e){return function(){if(this.curOp)return e.apply(this,arguments);Zr(this);try{return e.apply(this,arguments)}finally{Jr(this)}}}function li(e){return function(){var t=this.cm;if(!t||t.curOp)return e.apply(this,arguments);Zr(t);try{return e.apply(this,arguments)}finally{Jr(t)}}}function si(e,t){e.doc.highlightFrontier<e.display.viewTo&&e.state.highlight.set(t,j(ci,e))}function ci(e){var t=e.doc;if(!(t.highlightFrontier>=e.display.viewTo)){var n=+new Date+e.options.workTime,r=yt(e,t.highlightFrontier),i=[];t.iter(r.line,Math.min(t.first+t.size,e.display.viewTo+500),function(o){if(r.line>=e.display.viewFrom){var a=o.styles,l=o.text.length>e.options.maxHighlightLength?Ge(t.mode,r.state):null,s=gt(e,o,r,!0);l&&(r.state=l),o.styles=s.styles;var c=o.styleClasses,u=s.classes;u?o.styleClasses=u:c&&(o.styleClasses=null);for(var d=!a||a.length!=o.styles.length||c!=u&&(!c||!u||c.bgClass!=u.bgClass||c.textClass!=u.textClass),f=0;!d&&f<a.length;++f)d=a[f]!=o.styles[f];d&&i.push(r.line),o.stateAfter=r.save(),r.nextLine()}else o.text.length<=e.options.maxHighlightLength&&bt(e,o.text,r),o.stateAfter=r.line%5==0?r.save():null,r.nextLine();if(+new Date>n)return si(e,e.options.workDelay),!0}),t.highlightFrontier=r.line,t.modeFrontier=Math.max(t.modeFrontier,r.line),i.length&&ii(e,function(){for(var t=0;t<i.length;t++)vr(e,i[t],"text")})}}var ui=function(e,t,n){var r=e.display;this.viewport=t,this.visible=zr(r,e.doc,t),this.editorIsHidden=!r.wrapper.offsetWidth,this.wrapperHeight=r.wrapper.clientHeight,this.wrapperWidth=r.wrapper.clientWidth,this.oldDisplayWidth=En(e),this.force=n,this.dims=ur(e),this.events=[]};function di(e,t){var n=e.display,r=e.doc;if(t.editorIsHidden)return yr(e),!1;if(!t.force&&t.visible.from>=n.viewFrom&&t.visible.to<=n.viewTo&&(null==n.updateLineNumbers||n.updateLineNumbers>=n.viewTo)&&n.renderedView==n.view&&0==wr(e))return!1;vi(e)&&(yr(e),t.dims=ur(e));var i=r.first+r.size,o=Math.max(t.visible.from-e.options.viewportMargin,r.first),a=Math.min(i,t.visible.to+e.options.viewportMargin);n.viewFrom<o&&o-n.viewFrom<20&&(o=Math.max(r.first,n.viewFrom)),n.viewTo>a&&n.viewTo-a<20&&(a=Math.min(i,n.viewTo)),Mt&&(o=Ut(e.doc,o),a=Kt(e.doc,a));var l=o!=n.viewFrom||a!=n.viewTo||n.lastWrapHeight!=t.wrapperHeight||n.lastWrapWidth!=t.wrapperWidth;(function(e,t,n){var r=e.display;0==r.view.length||t>=r.viewTo||n<=r.viewFrom?(r.view=un(e,t,n),r.viewFrom=t):(r.viewFrom>t?r.view=un(e,t,r.viewFrom).concat(r.view):r.viewFrom<t&&(r.view=r.view.slice(mr(e,t))),r.viewFrom=t,r.viewTo<n?r.view=r.view.concat(un(e,r.viewTo,n)):r.viewTo>n&&(r.view=r.view.slice(0,mr(e,n)))),r.viewTo=n})(e,o,a),n.viewOffset=$t(Ze(e.doc,n.viewFrom)),e.display.mover.style.top=n.viewOffset+"px";var c=wr(e);if(!l&&0==c&&!t.force&&n.renderedView==n.view&&(null==n.updateLineNumbers||n.updateLineNumbers>=n.viewTo))return!1;var u=function(e){if(e.hasFocus())return null;var t=z(I(e));if(!t||!E(e.display.lineDiv,t))return null;var n={activeElt:t};if(window.getSelection){var r=R(e).getSelection();r.anchorNode&&r.extend&&E(e.display.lineDiv,r.anchorNode)&&(n.anchorNode=r.anchorNode,n.anchorOffset=r.anchorOffset,n.focusNode=r.focusNode,n.focusOffset=r.focusOffset)}return n}(e);return c>4&&(n.lineDiv.style.display="none"),function(e,t,n){var r=e.display,i=e.options.lineNumbers,o=r.lineDiv,a=o.firstChild;function l(t){var n=t.nextSibling;return s&&b&&e.display.currentWheelTarget==t?t.style.display="none":t.parentNode.removeChild(t),n}for(var c=r.view,u=r.viewFrom,d=0;d<c.length;d++){var f=c[d];if(f.hidden);else if(f.node&&f.node.parentNode==o){for(;a!=f.node;)a=l(a);var h=i&&null!=t&&t<=u&&f.lineNumber;f.changes&&(K(f.changes,"gutter")>-1&&(h=!1),mn(e,f,u,n)),h&&(A(f.lineNumber),f.lineNumber.appendChild(document.createTextNode(it(e.options,u)))),a=f.node.nextSibling}else{var p=kn(e,f,u,n);o.insertBefore(p,a)}u+=f.size}for(;a;)a=l(a)}(e,n.updateLineNumbers,t.dims),c>4&&(n.lineDiv.style.display=""),n.renderedView=n.view,function(e){if(e&&e.activeElt&&e.activeElt!=z(H(e.activeElt))&&(e.activeElt.focus(),!/^(INPUT|TEXTAREA)$/.test(e.activeElt.nodeName)&&e.anchorNode&&E(document.body,e.anchorNode)&&E(document.body,e.focusNode))){var t=e.activeElt.ownerDocument,n=t.defaultView.getSelection(),r=t.createRange();r.setEnd(e.anchorNode,e.anchorOffset),r.collapse(!1),n.removeAllRanges(),n.addRange(r),n.extend(e.focusNode,e.focusOffset)}}(u),A(n.cursorDiv),A(n.selectionDiv),n.gutters.style.height=n.sizer.style.minHeight=0,l&&(n.lastWrapHeight=t.wrapperHeight,n.lastWrapWidth=t.wrapperWidth,si(e,400)),n.updateLineNumbers=null,!0}function fi(e,t){for(var n=t.viewport,r=!0;;r=!1){if(r&&e.options.lineWrapping&&t.oldDisplayWidth!=En(e))r&&(t.visible=zr(e.display,e.doc,n));else if(n&&null!=n.top&&(n={top:Math.min(e.doc.height+Nn(e.display)-zn(e),n.top)}),t.visible=zr(e.display,e.doc,n),t.visible.from>=e.display.viewFrom&&t.visible.to<=e.display.viewTo)break;if(!di(e,t))break;_r(e);var i=qr(e);xr(e),Vr(e,i),mi(e,i),t.force=!1}t.signal(e,"update",e),e.display.viewFrom==e.display.reportedViewFrom&&e.display.viewTo==e.display.reportedViewTo||(t.signal(e,"viewportChange",e,e.display.viewFrom,e.display.viewTo),e.display.reportedViewFrom=e.display.viewFrom,e.display.reportedViewTo=e.display.viewTo)}function hi(e,t){var n=new ui(e,t);if(di(e,n)){_r(e),fi(e,n);var r=qr(e);xr(e),Vr(e,r),mi(e,r),n.finish()}}function pi(e){var t=e.gutters.offsetWidth;e.sizer.style.marginLeft=t+"px",hn(e,"gutterChanged",e)}function mi(e,t){e.display.sizer.style.minHeight=t.docHeight+"px",e.display.heightForcer.style.top=t.docHeight+"px",e.display.gutters.style.height=t.docHeight+e.display.barHeight+_n(e)+"px"}function gi(e){var t=e.display,n=t.view;if(t.alignWidgets||t.gutters.firstChild&&e.options.fixedGutter){for(var r=dr(t)-t.scroller.scrollLeft+e.doc.scrollLeft,i=t.gutters.offsetWidth,o=r+"px",a=0;a<n.length;a++)if(!n[a].hidden){e.options.fixedGutter&&(n[a].gutter&&(n[a].gutter.style.left=o),n[a].gutterBackground&&(n[a].gutterBackground.style.left=o));var l=n[a].alignable;if(l)for(var s=0;s<l.length;s++)l[s].style.left=o}e.options.fixedGutter&&(t.gutters.style.left=r+i+"px")}}function vi(e){if(!e.options.lineNumbers)return!1;var t=e.doc,n=it(e.options,t.first+t.size-1),r=e.display;if(n.length!=r.lineNumChars){var i=r.measure.appendChild(O("div",[O("div",n)],"CodeMirror-linenumber CodeMirror-gutter-elt")),o=i.firstChild.offsetWidth,a=i.offsetWidth-o;return r.lineGutter.style.width="",r.lineNumInnerWidth=Math.max(o,r.lineGutter.offsetWidth-a)+1,r.lineNumWidth=r.lineNumInnerWidth+a,r.lineNumChars=r.lineNumInnerWidth?n.length:-1,r.lineGutter.style.width=r.lineNumWidth+"px",pi(e.display),!0}return!1}function yi(e,t){for(var n=[],r=!1,i=0;i<e.length;i++){var o=e[i],a=null;if("string"!=typeof o&&(a=o.style,o=o.className),"CodeMirror-linenumbers"==o){if(!t)continue;r=!0}n.push({className:o,style:a})}return t&&!r&&n.push({className:"CodeMirror-linenumbers",style:null}),n}function bi(e){var t=e.gutters,n=e.gutterSpecs;A(t),e.lineGutter=null;for(var r=0;r<n.length;++r){var i=n[r],o=i.className,a=i.style,l=t.appendChild(O("div",null,"CodeMirror-gutter "+o));a&&(l.style.cssText=a),"CodeMirror-linenumbers"==o&&(e.lineGutter=l,l.style.width=(e.lineNumWidth||1)+"px")}t.style.display=n.length?"":"none",pi(e)}function wi(e){bi(e.display),gr(e),gi(e)}function xi(e,t,r,i){var o=this;this.input=r,o.scrollbarFiller=O("div",null,"CodeMirror-scrollbar-filler"),o.scrollbarFiller.setAttribute("cm-not-content","true"),o.gutterFiller=O("div",null,"CodeMirror-gutter-filler"),o.gutterFiller.setAttribute("cm-not-content","true"),o.lineDiv=_("div",null,"CodeMirror-code"),o.selectionDiv=O("div",null,null,"position: relative; z-index: 1"),o.cursorDiv=O("div",null,"CodeMirror-cursors"),o.measure=O("div",null,"CodeMirror-measure"),o.lineMeasure=O("div",null,"CodeMirror-measure"),o.lineSpace=_("div",[o.measure,o.lineMeasure,o.selectionDiv,o.cursorDiv,o.lineDiv],null,"position: relative; outline: none");var c=_("div",[o.lineSpace],"CodeMirror-lines");o.mover=O("div",[c],null,"position: relative"),o.sizer=O("div",[o.mover],"CodeMirror-sizer"),o.sizerWidth=null,o.heightForcer=O("div",null,null,"position: absolute; height: 50px; width: 1px;"),o.gutters=O("div",null,"CodeMirror-gutters"),o.lineGutter=null,o.scroller=O("div",[o.sizer,o.heightForcer,o.gutters],"CodeMirror-scroll"),o.scroller.setAttribute("tabIndex","-1"),o.wrapper=O("div",[o.scrollbarFiller,o.gutterFiller,o.scroller],"CodeMirror"),u&&d>=105&&(o.wrapper.style.clipPath="inset(0px)"),o.wrapper.setAttribute("translate","no"),a&&l<8&&(o.gutters.style.zIndex=-1,o.scroller.style.paddingRight=0),s||n&&y||(o.scroller.draggable=!0),e&&(e.appendChild?e.appendChild(o.wrapper):e(o.wrapper)),o.viewFrom=o.viewTo=t.first,o.reportedViewFrom=o.reportedViewTo=t.first,o.view=[],o.renderedView=null,o.externalMeasured=null,o.viewOffset=0,o.lastWrapHeight=o.lastWrapWidth=0,o.updateLineNumbers=null,o.nativeBarWidth=o.barHeight=o.barWidth=0,o.scrollbarsClipped=!1,o.lineNumWidth=o.lineNumInnerWidth=o.lineNumChars=null,o.alignWidgets=!1,o.cachedCharWidth=o.cachedTextHeight=o.cachedPaddingH=null,o.maxLine=null,o.maxLineLength=0,o.maxLineChanged=!1,o.wheelDX=o.wheelDY=o.wheelStartX=o.wheelStartY=null,o.shift=!1,o.selForContextMenu=null,o.activeTouch=null,o.gutterSpecs=yi(i.gutters,i.lineNumbers),bi(o),r.init(o)}ui.prototype.signal=function(e,t){ke(e,t)&&this.events.push(arguments)},ui.prototype.finish=function(){for(var e=0;e<this.events.length;e++)be.apply(null,this.events[e])};var ki=0,Ci=null;function Si(e){var t=e.wheelDeltaX,n=e.wheelDeltaY;return null==t&&e.detail&&e.axis==e.HORIZONTAL_AXIS&&(t=e.detail),null==n&&e.detail&&e.axis==e.VERTICAL_AXIS?n=e.detail:null==n&&(n=e.wheelDelta),{x:t,y:n}}function Ti(e){var t=Si(e);return t.x*=Ci,t.y*=Ci,t}function Li(e,t){u&&102==d&&(null==e.display.chromeScrollHack?e.display.sizer.style.pointerEvents="none":clearTimeout(e.display.chromeScrollHack),e.display.chromeScrollHack=setTimeout(function(){e.display.chromeScrollHack=null,e.display.sizer.style.pointerEvents=""},100));var r=Si(t),i=r.x,o=r.y,a=Ci;0===t.deltaMode&&(i=t.deltaX,o=t.deltaY,a=1);var l=e.display,c=l.scroller,h=c.scrollWidth>c.clientWidth,p=c.scrollHeight>c.clientHeight;if(i&&h||o&&p){if(o&&b&&s)e:for(var m=t.target,g=l.view;m!=c;m=m.parentNode)for(var v=0;v<g.length;v++)if(g[v].node==m){e.display.currentWheelTarget=m;break e}if(i&&!n&&!f&&null!=a)return o&&p&&Rr(e,Math.max(0,c.scrollTop+o*a)),Br(e,Math.max(0,c.scrollLeft+i*a)),(!o||o&&p)&&Se(t),void(l.wheelStartX=null);if(o&&null!=a){var y=o*a,w=e.doc.scrollTop,x=w+l.wrapper.clientHeight;y<0?w=Math.max(0,w+y-50):x=Math.min(e.doc.height,x+y+50),hi(e,{top:w,bottom:x})}ki<20&&0!==t.deltaMode&&(null==l.wheelStartX?(l.wheelStartX=c.scrollLeft,l.wheelStartY=c.scrollTop,l.wheelDX=i,l.wheelDY=o,setTimeout(function(){if(null!=l.wheelStartX){var e=c.scrollLeft-l.wheelStartX,t=c.scrollTop-l.wheelStartY,n=t&&l.wheelDY&&t/l.wheelDY||e&&l.wheelDX&&e/l.wheelDX;l.wheelStartX=l.wheelStartY=null,n&&(Ci=(Ci*ki+n)/(ki+1),++ki)}},200)):(l.wheelDX+=i,l.wheelDY+=o))}}a?Ci=-.53:n?Ci=15:u?Ci=-.7:h&&(Ci=-1/3);var Mi=function(e,t){this.ranges=e,this.primIndex=t};Mi.prototype.primary=function(){return this.ranges[this.primIndex]},Mi.prototype.equals=function(e){if(e==this)return!0;if(e.primIndex!=this.primIndex||e.ranges.length!=this.ranges.length)return!1;for(var t=0;t<this.ranges.length;t++){var n=this.ranges[t],r=e.ranges[t];if(!lt(n.anchor,r.anchor)||!lt(n.head,r.head))return!1}return!0},Mi.prototype.deepCopy=function(){for(var e=[],t=0;t<this.ranges.length;t++)e[t]=new Ai(st(this.ranges[t].anchor),st(this.ranges[t].head));return new Mi(e,this.primIndex)},Mi.prototype.somethingSelected=function(){for(var e=0;e<this.ranges.length;e++)if(!this.ranges[e].empty())return!0;return!1},Mi.prototype.contains=function(e,t){t||(t=e);for(var n=0;n<this.ranges.length;n++){var r=this.ranges[n];if(at(t,r.from())>=0&&at(e,r.to())<=0)return n}return-1};var Ai=function(e,t){this.anchor=e,this.head=t};function Ni(e,t,n){var r=e&&e.options.selectionsMayTouch,i=t[n];t.sort(function(e,t){return at(e.from(),t.from())}),n=K(t,i);for(var o=1;o<t.length;o++){var a=t[o],l=t[o-1],s=at(l.to(),a.from());if(r&&!a.empty()?s>0:s>=0){var c=ut(l.from(),a.from()),u=ct(l.to(),a.to()),d=l.empty()?a.from()==a.head:l.from()==l.head;o<=n&&--n,t.splice(--o,2,new Ai(d?u:c,d?c:u))}}return new Mi(t,n)}function Oi(e,t){return new Mi([new Ai(e,t||e)],0)}function _i(e){return e.text?ot(e.from.line+e.text.length-1,Q(e.text).length+(1==e.text.length?e.from.ch:0)):e.to}function Ei(e,t){if(at(e,t.from)<0)return e;if(at(e,t.to)<=0)return _i(t);var n=e.line+t.text.length-(t.to.line-t.from.line)-1,r=e.ch;return e.line==t.to.line&&(r+=_i(t).ch-t.to.ch),ot(n,r)}function zi(e,t){for(var n=[],r=0;r<e.sel.ranges.length;r++){var i=e.sel.ranges[r];n.push(new Ai(Ei(i.anchor,t),Ei(i.head,t)))}return Ni(e.cm,n,e.sel.primIndex)}function Di(e,t,n){return e.line==t.line?ot(n.line,e.ch-t.ch+n.ch):ot(n.line+(e.line-t.line),e.ch)}function Pi(e){e.doc.mode=Ue(e.options,e.doc.modeOption),Wi(e)}function Wi(e){e.doc.iter(function(e){e.stateAfter&&(e.stateAfter=null),e.styles&&(e.styles=null)}),e.doc.modeFrontier=e.doc.highlightFrontier=e.doc.first,si(e,100),e.state.modeGen++,e.curOp&&gr(e)}function Fi(e,t){return 0==t.from.ch&&0==t.to.ch&&""==Q(t.text)&&(!e.cm||e.cm.options.wholeLineUpdateBefore)}function Ii(e,t,n,r){function i(e){return n?n[e]:null}function o(e,n,i){(function(e,t,n,r){e.text=t,e.stateAfter&&(e.stateAfter=null),e.styles&&(e.styles=null),null!=e.order&&(e.order=null),zt(e),Dt(e,n);var i=r?r(e):1;i!=e.height&&et(e,i)})(e,n,i,r),hn(e,"change",e,t)}function a(e,t){for(var n=[],o=e;o<t;++o)n.push(new Zt(c[o],i(o),r));return n}var l=t.from,s=t.to,c=t.text,u=Ze(e,l.line),d=Ze(e,s.line),f=Q(c),h=i(c.length-1),p=s.line-l.line;if(t.full)e.insert(0,a(0,c.length)),e.remove(c.length,e.size-c.length);else if(Fi(e,t)){var m=a(0,c.length-1);o(d,d.text,h),p&&e.remove(l.line,p),m.length&&e.insert(l.line,m)}else if(u==d)if(1==c.length)o(u,u.text.slice(0,l.ch)+f+u.text.slice(s.ch),h);else{var g=a(1,c.length-1);g.push(new Zt(f+u.text.slice(s.ch),h,r)),o(u,u.text.slice(0,l.ch)+c[0],i(0)),e.insert(l.line+1,g)}else if(1==c.length)o(u,u.text.slice(0,l.ch)+c[0]+d.text.slice(s.ch),i(0)),e.remove(l.line+1,p);else{o(u,u.text.slice(0,l.ch)+c[0],i(0)),o(d,f+d.text.slice(s.ch),h);var v=a(1,c.length-1);p>1&&e.remove(l.line+1,p-1),e.insert(l.line+1,v)}hn(e,"change",e,t)}function Hi(e,t,n){!function e(r,i,o){if(r.linked)for(var a=0;a<r.linked.length;++a){var l=r.linked[a];if(l.doc!=i){var s=o&&l.sharedHist;n&&!s||(t(l.doc,s),e(l.doc,r,s))}}}(e,null,!0)}function Ri(e,t){if(t.cm)throw new Error("This document is already in use.");e.doc=t,t.cm=e,hr(e),Pi(e),ji(e),e.options.direction=t.direction,e.options.lineWrapping||Xt(e),e.options.mode=t.modeOption,gr(e)}function ji(e){("rtl"==e.doc.direction?D:M)(e.display.lineDiv,"CodeMirror-rtl")}function Bi(e){this.done=[],this.undone=[],this.undoDepth=e?e.undoDepth:1/0,this.lastModTime=this.lastSelTime=0,this.lastOp=this.lastSelOp=null,this.lastOrigin=this.lastSelOrigin=null,this.generation=this.maxGeneration=e?e.maxGeneration:1}function qi(e,t){var n={from:st(t.from),to:_i(t),text:Je(e,t.from,t.to)};return $i(e,n,t.from.line,t.to.line+1),Hi(e,function(e){return $i(e,n,t.from.line,t.to.line+1)},!0),n}function Ui(e){for(;e.length&&Q(e).ranges;)e.pop()}function Ki(e,t,n,r){var i=e.history;i.undone.length=0;var o,a,l=+new Date;if((i.lastOp==r||i.lastOrigin==t.origin&&t.origin&&("+"==t.origin.charAt(0)&&i.lastModTime>l-(e.cm?e.cm.options.historyEventDelay:500)||"*"==t.origin.charAt(0)))&&(o=function(e,t){return t?(Ui(e.done),Q(e.done)):e.done.length&&!Q(e.done).ranges?Q(e.done):e.done.length>1&&!e.done[e.done.length-2].ranges?(e.done.pop(),Q(e.done)):void 0}(i,i.lastOp==r)))a=Q(o.changes),0==at(t.from,t.to)&&0==at(t.from,a.to)?a.to=_i(t):o.changes.push(qi(e,t));else{var s=Q(i.done);for(s&&s.ranges||Gi(e.sel,i.done),o={changes:[qi(e,t)],generation:i.generation},i.done.push(o);i.done.length>i.undoDepth;)i.done.shift(),i.done[0].ranges||i.done.shift()}i.done.push(n),i.generation=++i.maxGeneration,i.lastModTime=i.lastSelTime=l,i.lastOp=i.lastSelOp=r,i.lastOrigin=i.lastSelOrigin=t.origin,a||be(e,"historyAdded")}function Vi(e,t,n,r){var i=e.history,o=r&&r.origin;n==i.lastSelOp||o&&i.lastSelOrigin==o&&(i.lastModTime==i.lastSelTime&&i.lastOrigin==o||function(e,t,n,r){var i=t.charAt(0);return"*"==i||"+"==i&&n.ranges.length==r.ranges.length&&n.somethingSelected()==r.somethingSelected()&&new Date-e.history.lastSelTime<=(e.cm?e.cm.options.historyEventDelay:500)}(e,o,Q(i.done),t))?i.done[i.done.length-1]=t:Gi(t,i.done),i.lastSelTime=+new Date,i.lastSelOrigin=o,i.lastSelOp=n,r&&!1!==r.clearRedo&&Ui(i.undone)}function Gi(e,t){var n=Q(t);n&&n.ranges&&n.equals(e)||t.push(e)}function $i(e,t,n,r){var i=t["spans_"+e.id],o=0;e.iter(Math.max(e.first,n),Math.min(e.first+e.size,r),function(n){n.markedSpans&&((i||(i=t["spans_"+e.id]={}))[o]=n.markedSpans),++o})}function Yi(e){if(!e)return null;for(var t,n=0;n<e.length;++n)e[n].marker.explicitlyCleared?t||(t=e.slice(0,n)):t&&t.push(e[n]);return t?t.length?t:null:e}function Xi(e,t){var n=function(e,t){var n=t["spans_"+e.id];if(!n)return null;for(var r=[],i=0;i<t.text.length;++i)r.push(Yi(n[i]));return r}(e,t),r=_t(e,t);if(!n)return r;if(!r)return n;for(var i=0;i<n.length;++i){var o=n[i],a=r[i];if(o&&a)e:for(var l=0;l<a.length;++l){for(var s=a[l],c=0;c<o.length;++c)if(o[c].marker==s.marker)continue e;o.push(s)}else a&&(n[i]=a)}return n}function Zi(e,t,n){for(var r=[],i=0;i<e.length;++i){var o=e[i];if(o.ranges)r.push(n?Mi.prototype.deepCopy.call(o):o);else{var a=o.changes,l=[];r.push({changes:l});for(var s=0;s<a.length;++s){var c=a[s],u=void 0;if(l.push({from:c.from,to:c.to,text:c.text}),t)for(var d in c)(u=d.match(/^spans_(\d+)$/))&&K(t,Number(u[1]))>-1&&(Q(l)[d]=c[d],delete c[d])}}}return r}function Ji(e,t,n,r){if(r){var i=e.anchor;if(n){var o=at(t,i)<0;o!=at(n,i)<0?(i=t,t=n):o!=at(t,n)<0&&(t=n)}return new Ai(i,t)}return new Ai(n||t,t)}function Qi(e,t,n,r,i){null==i&&(i=e.cm&&(e.cm.display.shift||e.extend)),io(e,new Mi([Ji(e.sel.primary(),t,n,i)],0),r)}function eo(e,t,n){for(var r=[],i=e.cm&&(e.cm.display.shift||e.extend),o=0;o<e.sel.ranges.length;o++)r[o]=Ji(e.sel.ranges[o],t[o],null,i);io(e,Ni(e.cm,r,e.sel.primIndex),n)}function to(e,t,n,r){var i=e.sel.ranges.slice(0);i[t]=n,io(e,Ni(e.cm,i,e.sel.primIndex),r)}function no(e,t,n,r){io(e,Oi(t,n),r)}function ro(e,t,n){var r=e.history.done,i=Q(r);i&&i.ranges?(r[r.length-1]=t,oo(e,t,n)):io(e,t,n)}function io(e,t,n){oo(e,t,n),Vi(e,e.sel,e.cm?e.cm.curOp.id:NaN,n)}function oo(e,t,n){(ke(e,"beforeSelectionChange")||e.cm&&ke(e.cm,"beforeSelectionChange"))&&(t=function(e,t,n){var r={ranges:t.ranges,update:function(t){this.ranges=[];for(var n=0;n<t.length;n++)this.ranges[n]=new Ai(ft(e,t[n].anchor),ft(e,t[n].head))},origin:n&&n.origin};return be(e,"beforeSelectionChange",e,r),e.cm&&be(e.cm,"beforeSelectionChange",e.cm,r),r.ranges!=t.ranges?Ni(e.cm,r.ranges,r.ranges.length-1):t}(e,t,n));var r=n&&n.bias||(at(t.primary().head,e.sel.primary().head)<0?-1:1);ao(e,so(e,t,r,!0)),n&&!1===n.scroll||!e.cm||"nocursor"==e.cm.getOption("readOnly")||Wr(e.cm)}function ao(e,t){t.equals(e.sel)||(e.sel=t,e.cm&&(e.cm.curOp.updateInput=1,e.cm.curOp.selectionChanged=!0,xe(e.cm)),hn(e,"cursorActivity",e))}function lo(e){ao(e,so(e,e.sel,null,!1))}function so(e,t,n,r){for(var i,o=0;o<t.ranges.length;o++){var a=t.ranges[o],l=t.ranges.length==e.sel.ranges.length&&e.sel.ranges[o],s=uo(e,a.anchor,l&&l.anchor,n,r),c=a.head==a.anchor?s:uo(e,a.head,l&&l.head,n,r);(i||s!=a.anchor||c!=a.head)&&(i||(i=t.ranges.slice(0,o)),i[o]=new Ai(s,c))}return i?Ni(e.cm,i,t.primIndex):t}function co(e,t,n,r,i){var o=Ze(e,t.line);if(o.markedSpans)for(var a=0;a<o.markedSpans.length;++a){var l=o.markedSpans[a],s=l.marker,c="selectLeft"in s?!s.selectLeft:s.inclusiveLeft,u="selectRight"in s?!s.selectRight:s.inclusiveRight;if((null==l.from||(c?l.from<=t.ch:l.from<t.ch))&&(null==l.to||(u?l.to>=t.ch:l.to>t.ch))){if(i&&(be(s,"beforeCursorEnter"),s.explicitlyCleared)){if(o.markedSpans){--a;continue}break}if(!s.atomic)continue;if(n){var d=s.find(r<0?1:-1),f=void 0;if((r<0?u:c)&&(d=fo(e,d,-r,d&&d.line==t.line?o:null)),d&&d.line==t.line&&(f=at(d,n))&&(r<0?f<0:f>0))return co(e,d,t,r,i)}var h=s.find(r<0?-1:1);return(r<0?c:u)&&(h=fo(e,h,r,h.line==t.line?o:null)),h?co(e,h,t,r,i):null}}return t}function uo(e,t,n,r,i){var o=r||1;return co(e,t,n,o,i)||!i&&co(e,t,n,o,!0)||co(e,t,n,-o,i)||!i&&co(e,t,n,-o,!0)||(e.cantEdit=!0,ot(e.first,0))}function fo(e,t,n,r){return n<0&&0==t.ch?t.line>e.first?ft(e,ot(t.line-1)):null:n>0&&t.ch==(r||Ze(e,t.line)).text.length?t.line<e.first+e.size-1?ot(t.line+1,0):null:new ot(t.line,t.ch+n)}function ho(e){e.setSelection(ot(e.firstLine(),0),ot(e.lastLine()),G)}function po(e,t,n){var r={canceled:!1,from:t.from,to:t.to,text:t.text,origin:t.origin,cancel:function(){return r.canceled=!0}};return n&&(r.update=function(t,n,i,o){t&&(r.from=ft(e,t)),n&&(r.to=ft(e,n)),i&&(r.text=i),void 0!==o&&(r.origin=o)}),be(e,"beforeChange",e,r),e.cm&&be(e.cm,"beforeChange",e.cm,r),r.canceled?(e.cm&&(e.cm.curOp.updateInput=2),null):{from:r.from,to:r.to,text:r.text,origin:r.origin}}function mo(e,t,n){if(e.cm){if(!e.cm.curOp)return oi(e.cm,mo)(e,t,n);if(e.cm.state.suppressEdits)return}if(!(ke(e,"beforeChange")||e.cm&&ke(e.cm,"beforeChange"))||(t=po(e,t,!0))){var r=Lt&&!n&&function(e,t,n){var r=null;if(e.iter(t.line,n.line+1,function(e){if(e.markedSpans)for(var t=0;t<e.markedSpans.length;++t){var n=e.markedSpans[t].marker;!n.readOnly||r&&-1!=K(r,n)||(r||(r=[])).push(n)}}),!r)return null;for(var i=[{from:t,to:n}],o=0;o<r.length;++o)for(var a=r[o],l=a.find(0),s=0;s<i.length;++s){var c=i[s];if(!(at(c.to,l.from)<0||at(c.from,l.to)>0)){var u=[s,1],d=at(c.from,l.from),f=at(c.to,l.to);(d<0||!a.inclusiveLeft&&!d)&&u.push({from:c.from,to:l.from}),(f>0||!a.inclusiveRight&&!f)&&u.push({from:l.to,to:c.to}),i.splice.apply(i,u),s+=u.length-3}}return i}(e,t.from,t.to);if(r)for(var i=r.length-1;i>=0;--i)go(e,{from:r[i].from,to:r[i].to,text:i?[""]:t.text,origin:t.origin});else go(e,t)}}function go(e,t){if(1!=t.text.length||""!=t.text[0]||0!=at(t.from,t.to)){var n=zi(e,t);Ki(e,t,n,e.cm?e.cm.curOp.id:NaN),bo(e,t,n,_t(e,t));var r=[];Hi(e,function(e,n){n||-1!=K(r,e.history)||(Co(e.history,t),r.push(e.history)),bo(e,t,null,_t(e,t))})}}function vo(e,t,n){var r=e.cm&&e.cm.state.suppressEdits;if(!r||n){for(var i,o=e.history,a=e.sel,l="undo"==t?o.done:o.undone,s="undo"==t?o.undone:o.done,c=0;c<l.length&&(i=l[c],n?!i.ranges||i.equals(e.sel):i.ranges);c++);if(c!=l.length){for(o.lastOrigin=o.lastSelOrigin=null;;){if(!(i=l.pop()).ranges){if(r)return void l.push(i);break}if(Gi(i,s),n&&!i.equals(e.sel))return void io(e,i,{clearRedo:!1});a=i}var u=[];Gi(a,s),s.push({changes:u,generation:o.generation}),o.generation=i.generation||++o.maxGeneration;for(var d=ke(e,"beforeChange")||e.cm&&ke(e.cm,"beforeChange"),f=function(n){var r=i.changes[n];if(r.origin=t,d&&!po(e,r,!1))return l.length=0,{};u.push(qi(e,r));var o=n?zi(e,r):Q(l);bo(e,r,o,Xi(e,r)),!n&&e.cm&&e.cm.scrollIntoView({from:r.from,to:_i(r)});var a=[];Hi(e,function(e,t){t||-1!=K(a,e.history)||(Co(e.history,r),a.push(e.history)),bo(e,r,null,Xi(e,r))})},h=i.changes.length-1;h>=0;--h){var p=f(h);if(p)return p.v}}}}function yo(e,t){if(0!=t&&(e.first+=t,e.sel=new Mi(ee(e.sel.ranges,function(e){return new Ai(ot(e.anchor.line+t,e.anchor.ch),ot(e.head.line+t,e.head.ch))}),e.sel.primIndex),e.cm)){gr(e.cm,e.first,e.first-t,t);for(var n=e.cm.display,r=n.viewFrom;r<n.viewTo;r++)vr(e.cm,r,"gutter")}}function bo(e,t,n,r){if(e.cm&&!e.cm.curOp)return oi(e.cm,bo)(e,t,n,r);if(t.to.line<e.first)yo(e,t.text.length-1-(t.to.line-t.from.line));else if(!(t.from.line>e.lastLine())){if(t.from.line<e.first){var i=t.text.length-1-(e.first-t.from.line);yo(e,i),t={from:ot(e.first,0),to:ot(t.to.line+i,t.to.ch),text:[Q(t.text)],origin:t.origin}}var o=e.lastLine();t.to.line>o&&(t={from:t.from,to:ot(o,Ze(e,o).text.length),text:[t.text[0]],origin:t.origin}),t.removed=Je(e,t.from,t.to),n||(n=zi(e,t)),e.cm?function(e,t,n){var r=e.doc,i=e.display,o=t.from,a=t.to,l=!1,s=o.line;e.options.lineWrapping||(s=tt(qt(Ze(r,o.line))),r.iter(s,a.line+1,function(e){if(e==i.maxLine)return l=!0,!0})),r.sel.contains(t.from,t.to)>-1&&xe(e),Ii(r,t,n,fr(e)),e.options.lineWrapping||(r.iter(s,o.line+t.text.length,function(e){var t=Yt(e);t>i.maxLineLength&&(i.maxLine=e,i.maxLineLength=t,i.maxLineChanged=!0,l=!1)}),l&&(e.curOp.updateMaxLine=!0)),function(e,t){if(e.modeFrontier=Math.min(e.modeFrontier,t),!(e.highlightFrontier<t-10)){for(var n=e.first,r=t-1;r>n;r--){var i=Ze(e,r).stateAfter;if(i&&(!(i instanceof pt)||r+i.lookAhead<t)){n=r+1;break}}e.highlightFrontier=Math.min(e.highlightFrontier,n)}}(r,o.line),si(e,400);var c=t.text.length-(a.line-o.line)-1;t.full?gr(e):o.line!=a.line||1!=t.text.length||Fi(e.doc,t)?gr(e,o.line,a.line+1,c):vr(e,o.line,"text");var u=ke(e,"changes"),d=ke(e,"change");if(d||u){var f={from:o,to:a,text:t.text,removed:t.removed,origin:t.origin};d&&hn(e,"change",e,f),u&&(e.curOp.changeObjs||(e.curOp.changeObjs=[])).push(f)}e.display.selForContextMenu=null}(e.cm,t,r):Ii(e,t,r),oo(e,n,G),e.cantEdit&&uo(e,ot(e.firstLine(),0))&&(e.cantEdit=!1)}}function wo(e,t,n,r,i){var o;r||(r=n),at(r,n)<0&&(n=(o=[r,n])[0],r=o[1]),"string"==typeof t&&(t=e.splitLines(t)),mo(e,{from:n,to:r,text:t,origin:i})}function xo(e,t,n,r){n<e.line?e.line+=r:t<e.line&&(e.line=t,e.ch=0)}function ko(e,t,n,r){for(var i=0;i<e.length;++i){var o=e[i],a=!0;if(o.ranges){o.copied||((o=e[i]=o.deepCopy()).copied=!0);for(var l=0;l<o.ranges.length;l++)xo(o.ranges[l].anchor,t,n,r),xo(o.ranges[l].head,t,n,r)}else{for(var s=0;s<o.changes.length;++s){var c=o.changes[s];if(n<c.from.line)c.from=ot(c.from.line+r,c.from.ch),c.to=ot(c.to.line+r,c.to.ch);else if(t<=c.to.line){a=!1;break}}a||(e.splice(0,i+1),i=0)}}}function Co(e,t){var n=t.from.line,r=t.to.line,i=t.text.length-(r-n)-1;ko(e.done,n,r,i),ko(e.undone,n,r,i)}function So(e,t,n,r){var i=t,o=t;return"number"==typeof t?o=Ze(e,dt(e,t)):i=tt(t),null==i?null:(r(o,i)&&e.cm&&vr(e.cm,i,n),o)}function To(e){this.lines=e,this.parent=null;for(var t=0,n=0;n<e.length;++n)e[n].parent=this,t+=e[n].height;this.height=t}function Lo(e){this.children=e;for(var t=0,n=0,r=0;r<e.length;++r){var i=e[r];t+=i.chunkSize(),n+=i.height,i.parent=this}this.size=t,this.height=n,this.parent=null}Ai.prototype.from=function(){return ut(this.anchor,this.head)},Ai.prototype.to=function(){return ct(this.anchor,this.head)},Ai.prototype.empty=function(){return this.head.line==this.anchor.line&&this.head.ch==this.anchor.ch},To.prototype={chunkSize:function(){return this.lines.length},removeInner:function(e,t){for(var n=e,r=e+t;n<r;++n){var i=this.lines[n];this.height-=i.height,Jt(i),hn(i,"delete")}this.lines.splice(e,t)},collapse:function(e){e.push.apply(e,this.lines)},insertInner:function(e,t,n){this.height+=n,this.lines=this.lines.slice(0,e).concat(t).concat(this.lines.slice(e));for(var r=0;r<t.length;++r)t[r].parent=this},iterN:function(e,t,n){for(var r=e+t;e<r;++e)if(n(this.lines[e]))return!0}},Lo.prototype={chunkSize:function(){return this.size},removeInner:function(e,t){this.size-=t;for(var n=0;n<this.children.length;++n){var r=this.children[n],i=r.chunkSize();if(e<i){var o=Math.min(t,i-e),a=r.height;if(r.removeInner(e,o),this.height-=a-r.height,i==o&&(this.children.splice(n--,1),r.parent=null),0==(t-=o))break;e=0}else e-=i}if(this.size-t<25&&(this.children.length>1||!(this.children[0]instanceof To))){var l=[];this.collapse(l),this.children=[new To(l)],this.children[0].parent=this}},collapse:function(e){for(var t=0;t<this.children.length;++t)this.children[t].collapse(e)},insertInner:function(e,t,n){this.size+=t.length,this.height+=n;for(var r=0;r<this.children.length;++r){var i=this.children[r],o=i.chunkSize();if(e<=o){if(i.insertInner(e,t,n),i.lines&&i.lines.length>50){for(var a=i.lines.length%25+25,l=a;l<i.lines.length;){var s=new To(i.lines.slice(l,l+=25));i.height-=s.height,this.children.splice(++r,0,s),s.parent=this}i.lines=i.lines.slice(0,a),this.maybeSpill()}break}e-=o}},maybeSpill:function(){if(!(this.children.length<=10)){var e=this;do{var t=new Lo(e.children.splice(e.children.length-5,5));if(e.parent){e.size-=t.size,e.height-=t.height;var n=K(e.parent.children,e);e.parent.children.splice(n+1,0,t)}else{var r=new Lo(e.children);r.parent=e,e.children=[r,t],e=r}t.parent=e.parent}while(e.children.length>10);e.parent.maybeSpill()}},iterN:function(e,t,n){for(var r=0;r<this.children.length;++r){var i=this.children[r],o=i.chunkSize();if(e<o){var a=Math.min(t,o-e);if(i.iterN(e,a,n))return!0;if(0==(t-=a))break;e=0}else e-=o}}};var Mo=function(e,t,n){if(n)for(var r in n)n.hasOwnProperty(r)&&(this[r]=n[r]);this.doc=e,this.node=t};function Ao(e,t,n){$t(t)<(e.curOp&&e.curOp.scrollTop||e.doc.scrollTop)&&Pr(e,n)}Mo.prototype.clear=function(){var e=this.doc.cm,t=this.line.widgets,n=this.line,r=tt(n);if(null!=r&&t){for(var i=0;i<t.length;++i)t[i]==this&&t.splice(i--,1);t.length||(n.widgets=null);var o=Ln(this);et(n,Math.max(0,n.height-o)),e&&(ii(e,function(){Ao(e,n,-o),vr(e,r,"widget")}),hn(e,"lineWidgetCleared",e,this,r))}},Mo.prototype.changed=function(){var e=this,t=this.height,n=this.doc.cm,r=this.line;this.height=null;var i=Ln(this)-t;i&&(Vt(this.doc,r)||et(r,r.height+i),n&&ii(n,function(){n.curOp.forceUpdate=!0,Ao(n,r,i),hn(n,"lineWidgetChanged",n,e,tt(r))}))},Ce(Mo);var No=0,Oo=function(e,t){this.lines=[],this.type=t,this.doc=e,this.id=++No};function _o(e,t,n,r,i){if(r&&r.shared)return function(e,t,n,r,i){(r=B(r)).shared=!1;var o=[_o(e,t,n,r,i)],a=o[0],l=r.widgetNode;return Hi(e,function(e){l&&(r.widgetNode=l.cloneNode(!0)),o.push(_o(e,ft(e,t),ft(e,n),r,i));for(var s=0;s<e.linked.length;++s)if(e.linked[s].isParent)return;a=Q(o)}),new Eo(o,a)}(e,t,n,r,i);if(e.cm&&!e.cm.curOp)return oi(e.cm,_o)(e,t,n,r,i);var o=new Oo(e,i),a=at(t,n);if(r&&B(r,o,!1),a>0||0==a&&!1!==o.clearWhenEmpty)return o;if(o.replacedWith&&(o.collapsed=!0,o.widgetNode=_("span",[o.replacedWith],"CodeMirror-widget"),r.handleMouseEvents||o.widgetNode.setAttribute("cm-ignore-events","true"),r.insertLeft&&(o.widgetNode.insertLeft=!0)),o.collapsed){if(Bt(e,t.line,t,n,o)||t.line!=n.line&&Bt(e,n.line,t,n,o))throw new Error("Inserting collapsed marker partially overlapping an existing one");Mt=!0}o.addToHistory&&Ki(e,{from:t,to:n,origin:"markText"},e.sel,NaN);var l,s=t.line,c=e.cm;if(e.iter(s,n.line+1,function(r){c&&o.collapsed&&!c.options.lineWrapping&&qt(r)==c.display.maxLine&&(l=!0),o.collapsed&&s!=t.line&&et(r,0),function(e,t,n){var r=n&&window.WeakSet&&(n.markedSpans||(n.markedSpans=new WeakSet));r&&e.markedSpans&&r.has(e.markedSpans)?e.markedSpans.push(t):(e.markedSpans=e.markedSpans?e.markedSpans.concat([t]):[t],r&&r.add(e.markedSpans)),t.marker.attachLine(e)}(r,new At(o,s==t.line?t.ch:null,s==n.line?n.ch:null),e.cm&&e.cm.curOp),++s}),o.collapsed&&e.iter(t.line,n.line+1,function(t){Vt(e,t)&&et(t,0)}),o.clearOnEnter&&ge(o,"beforeCursorEnter",function(){return o.clear()}),o.readOnly&&(Lt=!0,(e.history.done.length||e.history.undone.length)&&e.clearHistory()),o.collapsed&&(o.id=++No,o.atomic=!0),c){if(l&&(c.curOp.updateMaxLine=!0),o.collapsed)gr(c,t.line,n.line+1);else if(o.className||o.startStyle||o.endStyle||o.css||o.attributes||o.title)for(var u=t.line;u<=n.line;u++)vr(c,u,"text");o.atomic&&lo(c.doc),hn(c,"markerAdded",c,o)}return o}Oo.prototype.clear=function(){if(!this.explicitlyCleared){var e=this.doc.cm,t=e&&!e.curOp;if(t&&Zr(e),ke(this,"clear")){var n=this.find();n&&hn(this,"clear",n.from,n.to)}for(var r=null,i=null,o=0;o<this.lines.length;++o){var a=this.lines[o],l=Nt(a.markedSpans,this);e&&!this.collapsed?vr(e,tt(a),"text"):e&&(null!=l.to&&(i=tt(a)),null!=l.from&&(r=tt(a))),a.markedSpans=Ot(a.markedSpans,l),null==l.from&&this.collapsed&&!Vt(this.doc,a)&&e&&et(a,sr(e.display))}if(e&&this.collapsed&&!e.options.lineWrapping)for(var s=0;s<this.lines.length;++s){var c=qt(this.lines[s]),u=Yt(c);u>e.display.maxLineLength&&(e.display.maxLine=c,e.display.maxLineLength=u,e.display.maxLineChanged=!0)}null!=r&&e&&this.collapsed&&gr(e,r,i+1),this.lines.length=0,this.explicitlyCleared=!0,this.atomic&&this.doc.cantEdit&&(this.doc.cantEdit=!1,e&&lo(e.doc)),e&&hn(e,"markerCleared",e,this,r,i),t&&Jr(e),this.parent&&this.parent.clear()}},Oo.prototype.find=function(e,t){var n,r;null==e&&"bookmark"==this.type&&(e=1);for(var i=0;i<this.lines.length;++i){var o=this.lines[i],a=Nt(o.markedSpans,this);if(null!=a.from&&(n=ot(t?o:tt(o),a.from),-1==e))return n;if(null!=a.to&&(r=ot(t?o:tt(o),a.to),1==e))return r}return n&&{from:n,to:r}},Oo.prototype.changed=function(){var e=this,t=this.find(-1,!0),n=this,r=this.doc.cm;t&&r&&ii(r,function(){var i=t.line,o=tt(t.line),a=Wn(r,o);if(a&&(qn(a),r.curOp.selectionChanged=r.curOp.forceUpdate=!0),r.curOp.updateMaxLine=!0,!Vt(n.doc,i)&&null!=n.height){var l=n.height;n.height=null;var s=Ln(n)-l;s&&et(i,i.height+s)}hn(r,"markerChanged",r,e)})},Oo.prototype.attachLine=function(e){if(!this.lines.length&&this.doc.cm){var t=this.doc.cm.curOp;t.maybeHiddenMarkers&&-1!=K(t.maybeHiddenMarkers,this)||(t.maybeUnhiddenMarkers||(t.maybeUnhiddenMarkers=[])).push(this)}this.lines.push(e)},Oo.prototype.detachLine=function(e){if(this.lines.splice(K(this.lines,e),1),!this.lines.length&&this.doc.cm){var t=this.doc.cm.curOp;(t.maybeHiddenMarkers||(t.maybeHiddenMarkers=[])).push(this)}},Ce(Oo);var Eo=function(e,t){this.markers=e,this.primary=t;for(var n=0;n<e.length;++n)e[n].parent=this};function zo(e){return e.findMarks(ot(e.first,0),e.clipPos(ot(e.lastLine())),function(e){return e.parent})}function Do(e){for(var t=function(t){var n=e[t],r=[n.primary.doc];Hi(n.primary.doc,function(e){return r.push(e)});for(var i=0;i<n.markers.length;i++){var o=n.markers[i];-1==K(r,o.doc)&&(o.parent=null,n.markers.splice(i--,1))}},n=0;n<e.length;n++)t(n)}Eo.prototype.clear=function(){if(!this.explicitlyCleared){this.explicitlyCleared=!0;for(var e=0;e<this.markers.length;++e)this.markers[e].clear();hn(this,"clear")}},Eo.prototype.find=function(e,t){return this.primary.find(e,t)},Ce(Eo);var Po=0,Wo=function(e,t,n,r,i){if(!(this instanceof Wo))return new Wo(e,t,n,r,i);null==n&&(n=0),Lo.call(this,[new To([new Zt("",null)])]),this.first=n,this.scrollTop=this.scrollLeft=0,this.cantEdit=!1,this.cleanGeneration=1,this.modeFrontier=this.highlightFrontier=n;var o=ot(n,0);this.sel=Oi(o),this.history=new Bi(null),this.id=++Po,this.modeOption=t,this.lineSep=r,this.direction="rtl"==i?"rtl":"ltr",this.extend=!1,"string"==typeof e&&(e=this.splitLines(e)),Ii(this,{from:o,to:o,text:e}),io(this,Oi(o),G)};Wo.prototype=ne(Lo.prototype,{constructor:Wo,iter:function(e,t,n){n?this.iterN(e-this.first,t-e,n):this.iterN(this.first,this.first+this.size,e)},insert:function(e,t){for(var n=0,r=0;r<t.length;++r)n+=t[r].height;this.insertInner(e-this.first,t,n)},remove:function(e,t){this.removeInner(e-this.first,t)},getValue:function(e){var t=Qe(this,this.first,this.first+this.size);return!1===e?t:t.join(e||this.lineSeparator())},setValue:li(function(e){var t=ot(this.first,0),n=this.first+this.size-1;mo(this,{from:t,to:ot(n,Ze(this,n).text.length),text:this.splitLines(e),origin:"setValue",full:!0},!0),this.cm&&Fr(this.cm,0,0),io(this,Oi(t),G)}),replaceRange:function(e,t,n,r){wo(this,e,t=ft(this,t),n=n?ft(this,n):t,r)},getRange:function(e,t,n){var r=Je(this,ft(this,e),ft(this,t));return!1===n?r:""===n?r.join(""):r.join(n||this.lineSeparator())},getLine:function(e){var t=this.getLineHandle(e);return t&&t.text},getLineHandle:function(e){if(rt(this,e))return Ze(this,e)},getLineNumber:function(e){return tt(e)},getLineHandleVisualStart:function(e){return"number"==typeof e&&(e=Ze(this,e)),qt(e)},lineCount:function(){return this.size},firstLine:function(){return this.first},lastLine:function(){return this.first+this.size-1},clipPos:function(e){return ft(this,e)},getCursor:function(e){var t=this.sel.primary();return null==e||"head"==e?t.head:"anchor"==e?t.anchor:"end"==e||"to"==e||!1===e?t.to():t.from()},listSelections:function(){return this.sel.ranges},somethingSelected:function(){return this.sel.somethingSelected()},setCursor:li(function(e,t,n){no(this,ft(this,"number"==typeof e?ot(e,t||0):e),null,n)}),setSelection:li(function(e,t,n){no(this,ft(this,e),ft(this,t||e),n)}),extendSelection:li(function(e,t,n){Qi(this,ft(this,e),t&&ft(this,t),n)}),extendSelections:li(function(e,t){eo(this,ht(this,e),t)}),extendSelectionsBy:li(function(e,t){eo(this,ht(this,ee(this.sel.ranges,e)),t)}),setSelections:li(function(e,t,n){if(e.length){for(var r=[],i=0;i<e.length;i++)r[i]=new Ai(ft(this,e[i].anchor),ft(this,e[i].head||e[i].anchor));null==t&&(t=Math.min(e.length-1,this.sel.primIndex)),io(this,Ni(this.cm,r,t),n)}}),addSelection:li(function(e,t,n){var r=this.sel.ranges.slice(0);r.push(new Ai(ft(this,e),ft(this,t||e))),io(this,Ni(this.cm,r,r.length-1),n)}),getSelection:function(e){for(var t,n=this.sel.ranges,r=0;r<n.length;r++){var i=Je(this,n[r].from(),n[r].to());t=t?t.concat(i):i}return!1===e?t:t.join(e||this.lineSeparator())},getSelections:function(e){for(var t=[],n=this.sel.ranges,r=0;r<n.length;r++){var i=Je(this,n[r].from(),n[r].to());!1!==e&&(i=i.join(e||this.lineSeparator())),t[r]=i}return t},replaceSelection:function(e,t,n){for(var r=[],i=0;i<this.sel.ranges.length;i++)r[i]=e;this.replaceSelections(r,t,n||"+input")},replaceSelections:li(function(e,t,n){for(var r=[],i=this.sel,o=0;o<i.ranges.length;o++){var a=i.ranges[o];r[o]={from:a.from(),to:a.to(),text:this.splitLines(e[o]),origin:n}}for(var l=t&&"end"!=t&&function(e,t,n){for(var r=[],i=ot(e.first,0),o=i,a=0;a<t.length;a++){var l=t[a],s=Di(l.from,i,o),c=Di(_i(l),i,o);if(i=l.to,o=c,"around"==n){var u=e.sel.ranges[a],d=at(u.head,u.anchor)<0;r[a]=new Ai(d?c:s,d?s:c)}else r[a]=new Ai(s,s)}return new Mi(r,e.sel.primIndex)}(this,r,t),s=r.length-1;s>=0;s--)mo(this,r[s]);l?ro(this,l):this.cm&&Wr(this.cm)}),undo:li(function(){vo(this,"undo")}),redo:li(function(){vo(this,"redo")}),undoSelection:li(function(){vo(this,"undo",!0)}),redoSelection:li(function(){vo(this,"redo",!0)}),setExtending:function(e){this.extend=e},getExtending:function(){return this.extend},historySize:function(){for(var e=this.history,t=0,n=0,r=0;r<e.done.length;r++)e.done[r].ranges||++t;for(var i=0;i<e.undone.length;i++)e.undone[i].ranges||++n;return{undo:t,redo:n}},clearHistory:function(){var e=this;this.history=new Bi(this.history),Hi(this,function(t){return t.history=e.history},!0)},markClean:function(){this.cleanGeneration=this.changeGeneration(!0)},changeGeneration:function(e){return e&&(this.history.lastOp=this.history.lastSelOp=this.history.lastOrigin=null),this.history.generation},isClean:function(e){return this.history.generation==(e||this.cleanGeneration)},getHistory:function(){return{done:Zi(this.history.done),undone:Zi(this.history.undone)}},setHistory:function(e){var t=this.history=new Bi(this.history);t.done=Zi(e.done.slice(0),null,!0),t.undone=Zi(e.undone.slice(0),null,!0)},setGutterMarker:li(function(e,t,n){return So(this,e,"gutter",function(e){var r=e.gutterMarkers||(e.gutterMarkers={});return r[t]=n,!n&&ae(r)&&(e.gutterMarkers=null),!0})}),clearGutter:li(function(e){var t=this;this.iter(function(n){n.gutterMarkers&&n.gutterMarkers[e]&&So(t,n,"gutter",function(){return n.gutterMarkers[e]=null,ae(n.gutterMarkers)&&(n.gutterMarkers=null),!0})})}),lineInfo:function(e){var t;if("number"==typeof e){if(!rt(this,e))return null;if(t=e,!(e=Ze(this,e)))return null}else if(null==(t=tt(e)))return null;return{line:t,handle:e,text:e.text,gutterMarkers:e.gutterMarkers,textClass:e.textClass,bgClass:e.bgClass,wrapClass:e.wrapClass,widgets:e.widgets}},addLineClass:li(function(e,t,n){return So(this,e,"gutter"==t?"gutter":"class",function(e){var r="text"==t?"textClass":"background"==t?"bgClass":"gutter"==t?"gutterClass":"wrapClass";if(e[r]){if(T(n).test(e[r]))return!1;e[r]+=" "+n}else e[r]=n;return!0})}),removeLineClass:li(function(e,t,n){return So(this,e,"gutter"==t?"gutter":"class",function(e){var r="text"==t?"textClass":"background"==t?"bgClass":"gutter"==t?"gutterClass":"wrapClass",i=e[r];if(!i)return!1;if(null==n)e[r]=null;else{var o=i.match(T(n));if(!o)return!1;var a=o.index+o[0].length;e[r]=i.slice(0,o.index)+(o.index&&a!=i.length?" ":"")+i.slice(a)||null}return!0})}),addLineWidget:li(function(e,t,n){return function(e,t,n,r){var i=new Mo(e,n,r),o=e.cm;return o&&i.noHScroll&&(o.display.alignWidgets=!0),So(e,t,"widget",function(t){var n=t.widgets||(t.widgets=[]);if(null==i.insertAt?n.push(i):n.splice(Math.min(n.length,Math.max(0,i.insertAt)),0,i),i.line=t,o&&!Vt(e,t)){var r=$t(t)<e.scrollTop;et(t,t.height+Ln(i)),r&&Pr(o,i.height),o.curOp.forceUpdate=!0}return!0}),o&&hn(o,"lineWidgetAdded",o,i,"number"==typeof t?t:tt(t)),i}(this,e,t,n)}),removeLineWidget:function(e){e.clear()},markText:function(e,t,n){return _o(this,ft(this,e),ft(this,t),n,n&&n.type||"range")},setBookmark:function(e,t){var n={replacedWith:t&&(null==t.nodeType?t.widget:t),insertLeft:t&&t.insertLeft,clearWhenEmpty:!1,shared:t&&t.shared,handleMouseEvents:t&&t.handleMouseEvents};return _o(this,e=ft(this,e),e,n,"bookmark")},findMarksAt:function(e){var t=[],n=Ze(this,(e=ft(this,e)).line).markedSpans;if(n)for(var r=0;r<n.length;++r){var i=n[r];(null==i.from||i.from<=e.ch)&&(null==i.to||i.to>=e.ch)&&t.push(i.marker.parent||i.marker)}return t},findMarks:function(e,t,n){e=ft(this,e),t=ft(this,t);var r=[],i=e.line;return this.iter(e.line,t.line+1,function(o){var a=o.markedSpans;if(a)for(var l=0;l<a.length;l++){var s=a[l];null!=s.to&&i==e.line&&e.ch>=s.to||null==s.from&&i!=e.line||null!=s.from&&i==t.line&&s.from>=t.ch||n&&!n(s.marker)||r.push(s.marker.parent||s.marker)}++i}),r},getAllMarks:function(){var e=[];return this.iter(function(t){var n=t.markedSpans;if(n)for(var r=0;r<n.length;++r)null!=n[r].from&&e.push(n[r].marker)}),e},posFromIndex:function(e){var t,n=this.first,r=this.lineSeparator().length;return this.iter(function(i){var o=i.text.length+r;if(o>e)return t=e,!0;e-=o,++n}),ft(this,ot(n,t))},indexFromPos:function(e){var t=(e=ft(this,e)).ch;if(e.line<this.first||e.ch<0)return 0;var n=this.lineSeparator().length;return this.iter(this.first,e.line,function(e){t+=e.text.length+n}),t},copy:function(e){var t=new Wo(Qe(this,this.first,this.first+this.size),this.modeOption,this.first,this.lineSep,this.direction);return t.scrollTop=this.scrollTop,t.scrollLeft=this.scrollLeft,t.sel=this.sel,t.extend=!1,e&&(t.history.undoDepth=this.history.undoDepth,t.setHistory(this.getHistory())),t},linkedDoc:function(e){e||(e={});var t=this.first,n=this.first+this.size;null!=e.from&&e.from>t&&(t=e.from),null!=e.to&&e.to<n&&(n=e.to);var r=new Wo(Qe(this,t,n),e.mode||this.modeOption,t,this.lineSep,this.direction);return e.sharedHist&&(r.history=this.history),(this.linked||(this.linked=[])).push({doc:r,sharedHist:e.sharedHist}),r.linked=[{doc:this,isParent:!0,sharedHist:e.sharedHist}],function(e,t){for(var n=0;n<t.length;n++){var r=t[n],i=r.find(),o=e.clipPos(i.from),a=e.clipPos(i.to);if(at(o,a)){var l=_o(e,o,a,r.primary,r.primary.type);r.markers.push(l),l.parent=r}}}(r,zo(this)),r},unlinkDoc:function(e){if(e instanceof Ea&&(e=e.doc),this.linked)for(var t=0;t<this.linked.length;++t)if(this.linked[t].doc==e){this.linked.splice(t,1),e.unlinkDoc(this),Do(zo(this));break}if(e.history==this.history){var n=[e.id];Hi(e,function(e){return n.push(e.id)},!0),e.history=new Bi(null),e.history.done=Zi(this.history.done,n),e.history.undone=Zi(this.history.undone,n)}},iterLinkedDocs:function(e){Hi(this,e)},getMode:function(){return this.mode},getEditor:function(){return this.cm},splitLines:function(e){return this.lineSep?e.split(this.lineSep):We(e)},lineSeparator:function(){return this.lineSep||"\n"},setDirection:li(function(e){var t;"rtl"!=e&&(e="ltr"),e!=this.direction&&(this.direction=e,this.iter(function(e){return e.order=null}),this.cm&&ii(t=this.cm,function(){ji(t),gr(t)}))})}),Wo.prototype.eachLine=Wo.prototype.iter;var Fo=0;function Io(e){var t=this;if(Ho(t),!we(t,e)&&!Mn(t.display,e)){Se(e),a&&(Fo=+new Date);var n=pr(t,e,!0),r=e.dataTransfer.files;if(n&&!t.isReadOnly())if(r&&r.length&&window.FileReader&&window.File)for(var i=r.length,o=Array(i),l=0,s=function(){++l==i&&oi(t,function(){var e={from:n=ft(t.doc,n),to:n,text:t.doc.splitLines(o.filter(function(e){return null!=e}).join(t.doc.lineSeparator())),origin:"paste"};mo(t.doc,e),ro(t.doc,Oi(ft(t.doc,n),ft(t.doc,_i(e))))})()},c=function(e,n){if(t.options.allowDropFileTypes&&-1==K(t.options.allowDropFileTypes,e.type))s();else{var r=new FileReader;r.onerror=function(){return s()},r.onload=function(){var e=r.result;/[\x00-\x08\x0e-\x1f]{2}/.test(e)||(o[n]=e),s()},r.readAsText(e)}},u=0;u<r.length;u++)c(r[u],u);else{if(t.state.draggingText&&t.doc.sel.contains(n)>-1)return t.state.draggingText(e),void setTimeout(function(){return t.display.input.focus()},20);try{var d=e.dataTransfer.getData("Text");if(d){var f;if(t.state.draggingText&&!t.state.draggingText.copy&&(f=t.listSelections()),oo(t.doc,Oi(n,n)),f)for(var h=0;h<f.length;++h)wo(t.doc,"",f[h].anchor,f[h].head,"drag");t.replaceSelection(d,"around","paste"),t.display.input.focus()}}catch(e){}}}}function Ho(e){e.display.dragCursor&&(e.display.lineSpace.removeChild(e.display.dragCursor),e.display.dragCursor=null)}function Ro(e){if(document.getElementsByClassName){for(var t=document.getElementsByClassName("CodeMirror"),n=[],r=0;r<t.length;r++){var i=t[r].CodeMirror;i&&n.push(i)}n.length&&n[0].operation(function(){for(var t=0;t<n.length;t++)e(n[t])})}}var jo=!1;function Bo(){var e;jo||(ge(window,"resize",function(){null==e&&(e=setTimeout(function(){e=null,Ro(qo)},100))}),ge(window,"blur",function(){return Ro(Or)}),jo=!0)}function qo(e){var t=e.display;t.cachedCharWidth=t.cachedTextHeight=t.cachedPaddingH=null,t.scrollbarsClipped=!1,e.setSize()}for(var Uo={3:"Pause",8:"Backspace",9:"Tab",13:"Enter",16:"Shift",17:"Ctrl",18:"Alt",19:"Pause",20:"CapsLock",27:"Esc",32:"Space",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"Left",38:"Up",39:"Right",40:"Down",44:"PrintScrn",45:"Insert",46:"Delete",59:";",61:"=",91:"Mod",92:"Mod",93:"Mod",106:"*",107:"=",109:"-",110:".",111:"/",145:"ScrollLock",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'",224:"Mod",63232:"Up",63233:"Down",63234:"Left",63235:"Right",63272:"Delete",63273:"Home",63275:"End",63276:"PageUp",63277:"PageDown",63302:"Insert"},Ko=0;Ko<10;Ko++)Uo[Ko+48]=Uo[Ko+96]=String(Ko);for(var Vo=65;Vo<=90;Vo++)Uo[Vo]=String.fromCharCode(Vo);for(var Go=1;Go<=12;Go++)Uo[Go+111]=Uo[Go+63235]="F"+Go;var $o={};function Yo(e){var t,n,r,i,o=e.split(/-(?!$)/);e=o[o.length-1];for(var a=0;a<o.length-1;a++){var l=o[a];if(/^(cmd|meta|m)$/i.test(l))i=!0;else if(/^a(lt)?$/i.test(l))t=!0;else if(/^(c|ctrl|control)$/i.test(l))n=!0;else{if(!/^s(hift)?$/i.test(l))throw new Error("Unrecognized modifier name: "+l);r=!0}}return t&&(e="Alt-"+e),n&&(e="Ctrl-"+e),i&&(e="Cmd-"+e),r&&(e="Shift-"+e),e}function Xo(e){var t={};for(var n in e)if(e.hasOwnProperty(n)){var r=e[n];if(/^(name|fallthrough|(de|at)tach)$/.test(n))continue;if("..."==r){delete e[n];continue}for(var i=ee(n.split(" "),Yo),o=0;o<i.length;o++){var a=void 0,l=void 0;o==i.length-1?(l=i.join(" "),a=r):(l=i.slice(0,o+1).join(" "),a="...");var s=t[l];if(s){if(s!=a)throw new Error("Inconsistent bindings for "+l)}else t[l]=a}delete e[n]}for(var c in t)e[c]=t[c];return e}function Zo(e,t,n,r){var i=(t=ta(t)).call?t.call(e,r):t[e];if(!1===i)return"nothing";if("..."===i)return"multi";if(null!=i&&n(i))return"handled";if(t.fallthrough){if("[object Array]"!=Object.prototype.toString.call(t.fallthrough))return Zo(e,t.fallthrough,n,r);for(var o=0;o<t.fallthrough.length;o++){var a=Zo(e,t.fallthrough[o],n,r);if(a)return a}}}function Jo(e){var t="string"==typeof e?e:Uo[e.keyCode];return"Ctrl"==t||"Alt"==t||"Shift"==t||"Mod"==t}function Qo(e,t,n){var r=e;return t.altKey&&"Alt"!=r&&(e="Alt-"+e),(C?t.metaKey:t.ctrlKey)&&"Ctrl"!=r&&(e="Ctrl-"+e),(C?t.ctrlKey:t.metaKey)&&"Mod"!=r&&(e="Cmd-"+e),!n&&t.shiftKey&&"Shift"!=r&&(e="Shift-"+e),e}function ea(e,t){if(f&&34==e.keyCode&&e.char)return!1;var n=Uo[e.keyCode];return null!=n&&!e.altGraphKey&&(3==e.keyCode&&e.code&&(n=e.code),Qo(n,e,t))}function ta(e){return"string"==typeof e?$o[e]:e}function na(e,t){for(var n=e.doc.sel.ranges,r=[],i=0;i<n.length;i++){for(var o=t(n[i]);r.length&&at(o.from,Q(r).to)<=0;){var a=r.pop();if(at(a.from,o.from)<0){o.from=a.from;break}}r.push(o)}ii(e,function(){for(var t=r.length-1;t>=0;t--)wo(e.doc,"",r[t].from,r[t].to,"+delete");Wr(e)})}function ra(e,t,n){var r=ce(e.text,t+n,n);return r<0||r>e.text.length?null:r}function ia(e,t,n){var r=ra(e,t.ch,n);return null==r?null:new ot(t.line,r,n<0?"after":"before")}function oa(e,t,n,r,i){if(e){"rtl"==t.doc.direction&&(i=-i);var o=pe(n,t.doc.direction);if(o){var a,l=i<0?Q(o):o[0],s=i<0==(1==l.level)?"after":"before";if(l.level>0||"rtl"==t.doc.direction){var c=Fn(t,n);a=i<0?n.text.length-1:0;var u=In(t,c,a).top;a=ue(function(e){return In(t,c,e).top==u},i<0==(1==l.level)?l.from:l.to-1,a),"before"==s&&(a=ra(n,a,1))}else a=i<0?l.to:l.from;return new ot(r,a,s)}}return new ot(r,i<0?n.text.length:0,i<0?"before":"after")}$o.basic={Left:"goCharLeft",Right:"goCharRight",Up:"goLineUp",Down:"goLineDown",End:"goLineEnd",Home:"goLineStartSmart",PageUp:"goPageUp",PageDown:"goPageDown",Delete:"delCharAfter",Backspace:"delCharBefore","Shift-Backspace":"delCharBefore",Tab:"defaultTab","Shift-Tab":"indentAuto",Enter:"newlineAndIndent",Insert:"toggleOverwrite",Esc:"singleSelection"},$o.pcDefault={"Ctrl-A":"selectAll","Ctrl-D":"deleteLine","Ctrl-Z":"undo","Shift-Ctrl-Z":"redo","Ctrl-Y":"redo","Ctrl-Home":"goDocStart","Ctrl-End":"goDocEnd","Ctrl-Up":"goLineUp","Ctrl-Down":"goLineDown","Ctrl-Left":"goGroupLeft","Ctrl-Right":"goGroupRight","Alt-Left":"goLineStart","Alt-Right":"goLineEnd","Ctrl-Backspace":"delGroupBefore","Ctrl-Delete":"delGroupAfter","Ctrl-S":"save","Ctrl-F":"find","Ctrl-G":"findNext","Shift-Ctrl-G":"findPrev","Shift-Ctrl-F":"replace","Shift-Ctrl-R":"replaceAll","Ctrl-[":"indentLess","Ctrl-]":"indentMore","Ctrl-U":"undoSelection","Shift-Ctrl-U":"redoSelection","Alt-U":"redoSelection",fallthrough:"basic"},$o.emacsy={"Ctrl-F":"goCharRight","Ctrl-B":"goCharLeft","Ctrl-P":"goLineUp","Ctrl-N":"goLineDown","Ctrl-A":"goLineStart","Ctrl-E":"goLineEnd","Ctrl-V":"goPageDown","Shift-Ctrl-V":"goPageUp","Ctrl-D":"delCharAfter","Ctrl-H":"delCharBefore","Alt-Backspace":"delWordBefore","Ctrl-K":"killLine","Ctrl-T":"transposeChars","Ctrl-O":"openLine"},$o.macDefault={"Cmd-A":"selectAll","Cmd-D":"deleteLine","Cmd-Z":"undo","Shift-Cmd-Z":"redo","Cmd-Y":"redo","Cmd-Home":"goDocStart","Cmd-Up":"goDocStart","Cmd-End":"goDocEnd","Cmd-Down":"goDocEnd","Alt-Left":"goGroupLeft","Alt-Right":"goGroupRight","Cmd-Left":"goLineLeft","Cmd-Right":"goLineRight","Alt-Backspace":"delGroupBefore","Ctrl-Alt-Backspace":"delGroupAfter","Alt-Delete":"delGroupAfter","Cmd-S":"save","Cmd-F":"find","Cmd-G":"findNext","Shift-Cmd-G":"findPrev","Cmd-Alt-F":"replace","Shift-Cmd-Alt-F":"replaceAll","Cmd-[":"indentLess","Cmd-]":"indentMore","Cmd-Backspace":"delWrappedLineLeft","Cmd-Delete":"delWrappedLineRight","Cmd-U":"undoSelection","Shift-Cmd-U":"redoSelection","Ctrl-Up":"goDocStart","Ctrl-Down":"goDocEnd",fallthrough:["basic","emacsy"]},$o.default=b?$o.macDefault:$o.pcDefault;var aa={selectAll:ho,singleSelection:function(e){return e.setSelection(e.getCursor("anchor"),e.getCursor("head"),G)},killLine:function(e){return na(e,function(t){if(t.empty()){var n=Ze(e.doc,t.head.line).text.length;return t.head.ch==n&&t.head.line<e.lastLine()?{from:t.head,to:ot(t.head.line+1,0)}:{from:t.head,to:ot(t.head.line,n)}}return{from:t.from(),to:t.to()}})},deleteLine:function(e){return na(e,function(t){return{from:ot(t.from().line,0),to:ft(e.doc,ot(t.to().line+1,0))}})},delLineLeft:function(e){return na(e,function(e){return{from:ot(e.from().line,0),to:e.from()}})},delWrappedLineLeft:function(e){return na(e,function(t){var n=e.charCoords(t.head,"div").top+5;return{from:e.coordsChar({left:0,top:n},"div"),to:t.from()}})},delWrappedLineRight:function(e){return na(e,function(t){var n=e.charCoords(t.head,"div").top+5,r=e.coordsChar({left:e.display.lineDiv.offsetWidth+100,top:n},"div");return{from:t.from(),to:r}})},undo:function(e){return e.undo()},redo:function(e){return e.redo()},undoSelection:function(e){return e.undoSelection()},redoSelection:function(e){return e.redoSelection()},goDocStart:function(e){return e.extendSelection(ot(e.firstLine(),0))},goDocEnd:function(e){return e.extendSelection(ot(e.lastLine()))},goLineStart:function(e){return e.extendSelectionsBy(function(t){return la(e,t.head.line)},{origin:"+move",bias:1})},goLineStartSmart:function(e){return e.extendSelectionsBy(function(t){return sa(e,t.head)},{origin:"+move",bias:1})},goLineEnd:function(e){return e.extendSelectionsBy(function(t){return function(e,t){var n=Ze(e.doc,t),r=function(e){for(var t;t=Rt(e);)e=t.find(1,!0).line;return e}(n);return r!=n&&(t=tt(r)),oa(!0,e,n,t,-1)}(e,t.head.line)},{origin:"+move",bias:-1})},goLineRight:function(e){return e.extendSelectionsBy(function(t){var n=e.cursorCoords(t.head,"div").top+5;return e.coordsChar({left:e.display.lineDiv.offsetWidth+100,top:n},"div")},Y)},goLineLeft:function(e){return e.extendSelectionsBy(function(t){var n=e.cursorCoords(t.head,"div").top+5;return e.coordsChar({left:0,top:n},"div")},Y)},goLineLeftSmart:function(e){return e.extendSelectionsBy(function(t){var n=e.cursorCoords(t.head,"div").top+5,r=e.coordsChar({left:0,top:n},"div");return r.ch<e.getLine(r.line).search(/\S/)?sa(e,t.head):r},Y)},goLineUp:function(e){return e.moveV(-1,"line")},goLineDown:function(e){return e.moveV(1,"line")},goPageUp:function(e){return e.moveV(-1,"page")},goPageDown:function(e){return e.moveV(1,"page")},goCharLeft:function(e){return e.moveH(-1,"char")},goCharRight:function(e){return e.moveH(1,"char")},goColumnLeft:function(e){return e.moveH(-1,"column")},goColumnRight:function(e){return e.moveH(1,"column")},goWordLeft:function(e){return e.moveH(-1,"word")},goGroupRight:function(e){return e.moveH(1,"group")},goGroupLeft:function(e){return e.moveH(-1,"group")},goWordRight:function(e){return e.moveH(1,"word")},delCharBefore:function(e){return e.deleteH(-1,"codepoint")},delCharAfter:function(e){return e.deleteH(1,"char")},delWordBefore:function(e){return e.deleteH(-1,"word")},delWordAfter:function(e){return e.deleteH(1,"word")},delGroupBefore:function(e){return e.deleteH(-1,"group")},delGroupAfter:function(e){return e.deleteH(1,"group")},indentAuto:function(e){return e.indentSelection("smart")},indentMore:function(e){return e.indentSelection("add")},indentLess:function(e){return e.indentSelection("subtract")},insertTab:function(e){return e.replaceSelection("\t")},insertSoftTab:function(e){for(var t=[],n=e.listSelections(),r=e.options.tabSize,i=0;i<n.length;i++){var o=n[i].from(),a=q(e.getLine(o.line),o.ch,r);t.push(J(r-a%r))}e.replaceSelections(t)},defaultTab:function(e){e.somethingSelected()?e.indentSelection("add"):e.execCommand("insertTab")},transposeChars:function(e){return ii(e,function(){for(var t=e.listSelections(),n=[],r=0;r<t.length;r++)if(t[r].empty()){var i=t[r].head,o=Ze(e.doc,i.line).text;if(o)if(i.ch==o.length&&(i=new ot(i.line,i.ch-1)),i.ch>0)i=new ot(i.line,i.ch+1),e.replaceRange(o.charAt(i.ch-1)+o.charAt(i.ch-2),ot(i.line,i.ch-2),i,"+transpose");else if(i.line>e.doc.first){var a=Ze(e.doc,i.line-1).text;a&&(i=new ot(i.line,1),e.replaceRange(o.charAt(0)+e.doc.lineSeparator()+a.charAt(a.length-1),ot(i.line-1,a.length-1),i,"+transpose"))}n.push(new Ai(i,i))}e.setSelections(n)})},newlineAndIndent:function(e){return ii(e,function(){for(var t=e.listSelections(),n=t.length-1;n>=0;n--)e.replaceRange(e.doc.lineSeparator(),t[n].anchor,t[n].head,"+input");t=e.listSelections();for(var r=0;r<t.length;r++)e.indentLine(t[r].from().line,null,!0);Wr(e)})},openLine:function(e){return e.replaceSelection("\n","start")},toggleOverwrite:function(e){return e.toggleOverwrite()}};function la(e,t){var n=Ze(e.doc,t),r=qt(n);return r!=n&&(t=tt(r)),oa(!0,e,r,t,1)}function sa(e,t){var n=la(e,t.line),r=Ze(e.doc,n.line),i=pe(r,e.doc.direction);if(!i||0==i[0].level){var o=Math.max(n.ch,r.text.search(/\S/)),a=t.line==n.line&&t.ch<=o&&t.ch;return ot(n.line,a?0:o,n.sticky)}return n}function ca(e,t,n){if("string"==typeof t&&!(t=aa[t]))return!1;e.display.input.ensurePolled();var r=e.display.shift,i=!1;try{e.isReadOnly()&&(e.state.suppressEdits=!0),n&&(e.display.shift=!1),i=t(e)!=V}finally{e.display.shift=r,e.state.suppressEdits=!1}return i}var ua=new U;function da(e,t,n,r){var i=e.state.keySeq;if(i){if(Jo(t))return"handled";if(/\'$/.test(t)?e.state.keySeq=null:ua.set(50,function(){e.state.keySeq==i&&(e.state.keySeq=null,e.display.input.reset())}),fa(e,i+" "+t,n,r))return!0}return fa(e,t,n,r)}function fa(e,t,n,r){var i=function(e,t,n){for(var r=0;r<e.state.keyMaps.length;r++){var i=Zo(t,e.state.keyMaps[r],n,e);if(i)return i}return e.options.extraKeys&&Zo(t,e.options.extraKeys,n,e)||Zo(t,e.options.keyMap,n,e)}(e,t,r);return"multi"==i&&(e.state.keySeq=t),"handled"==i&&hn(e,"keyHandled",e,t,n),"handled"!=i&&"multi"!=i||(Se(n),Lr(e)),!!i}function ha(e,t){var n=ea(t,!0);return!!n&&(t.shiftKey&&!e.state.keySeq?da(e,"Shift-"+n,t,function(t){return ca(e,t,!0)})||da(e,n,t,function(t){if("string"==typeof t?/^go[A-Z]/.test(t):t.motion)return ca(e,t)}):da(e,n,t,function(t){return ca(e,t)}))}var pa=null;function ma(e){var t=this;if(!(e.target&&e.target!=t.display.input.getField()||(t.curOp.focus=z(I(t)),we(t,e)))){a&&l<11&&27==e.keyCode&&(e.returnValue=!1);var r=e.keyCode;t.display.shift=16==r||e.shiftKey;var i=ha(t,e);f&&(pa=i?r:null,i||88!=r||Ie||!(b?e.metaKey:e.ctrlKey)||t.replaceSelection("",null,"cut")),n&&!b&&!i&&46==r&&e.shiftKey&&!e.ctrlKey&&document.execCommand&&document.execCommand("cut"),18!=r||/\bCodeMirror-crosshair\b/.test(t.display.lineDiv.className)||function(e){var t=e.display.lineDiv;function n(e){18!=e.keyCode&&e.altKey||(M(t,"CodeMirror-crosshair"),ye(document,"keyup",n),ye(document,"mouseover",n))}D(t,"CodeMirror-crosshair"),ge(document,"keyup",n),ge(document,"mouseover",n)}(t)}}function ga(e){16==e.keyCode&&(this.doc.sel.shift=!1),we(this,e)}function va(e){var t=this;if(!(e.target&&e.target!=t.display.input.getField()||Mn(t.display,e)||we(t,e)||e.ctrlKey&&!e.altKey||b&&e.metaKey)){var n=e.keyCode,r=e.charCode;if(f&&n==pa)return pa=null,void Se(e);if(!f||e.which&&!(e.which<10)||!ha(t,e)){var i=String.fromCharCode(null==r?n:r);"\b"!=i&&(function(e,t,n){return da(e,"'"+n+"'",t,function(t){return ca(e,t,!0)})}(t,e,i)||t.display.input.onKeyPress(e))}}}var ya,ba,wa=function(e,t,n){this.time=e,this.pos=t,this.button=n};function xa(e){var t=this,n=t.display;if(!(we(t,e)||n.activeTouch&&n.input.supportsTouch()))if(n.input.ensurePolled(),n.shift=e.shiftKey,Mn(n,e))s||(n.scroller.draggable=!1,setTimeout(function(){return n.scroller.draggable=!0},100));else if(!Sa(t,e)){var r=pr(t,e),i=Ne(e),o=r?function(e,t){var n=+new Date;return ba&&ba.compare(n,e,t)?(ya=ba=null,"triple"):ya&&ya.compare(n,e,t)?(ba=new wa(n,e,t),ya=null,"double"):(ya=new wa(n,e,t),ba=null,"single")}(r,i):"single";R(t).focus(),1==i&&t.state.selectingText&&t.state.selectingText(e),r&&function(e,t,n,r,i){var o="Click";return"double"==r?o="Double"+o:"triple"==r&&(o="Triple"+o),da(e,Qo(o=(1==t?"Left":2==t?"Middle":"Right")+o,i),i,function(t){if("string"==typeof t&&(t=aa[t]),!t)return!1;var r=!1;try{e.isReadOnly()&&(e.state.suppressEdits=!0),r=t(e,n)!=V}finally{e.state.suppressEdits=!1}return r})}(t,i,r,o,e)||(1==i?r?function(e,t,n,r){a?setTimeout(j(Mr,e),0):e.curOp.focus=z(I(e));var i,o=function(e,t,n){var r=e.getOption("configureMouse"),i=r?r(e,t,n):{};if(null==i.unit){var o=w?n.shiftKey&&n.metaKey:n.altKey;i.unit=o?"rectangle":"single"==t?"char":"double"==t?"word":"line"}return(null==i.extend||e.doc.extend)&&(i.extend=e.doc.extend||n.shiftKey),null==i.addNew&&(i.addNew=b?n.metaKey:n.ctrlKey),null==i.moveOnDrag&&(i.moveOnDrag=!(b?n.altKey:n.ctrlKey)),i}(e,n,r),c=e.doc.sel;e.options.dragDrop&&Ee&&!e.isReadOnly()&&"single"==n&&(i=c.contains(t))>-1&&(at((i=c.ranges[i]).from(),t)<0||t.xRel>0)&&(at(i.to(),t)>0||t.xRel<0)?function(e,t,n,r){var i=e.display,o=!1,c=oi(e,function(t){s&&(i.scroller.draggable=!1),e.state.draggingText=!1,e.state.delayingBlurEvent&&(e.hasFocus()?e.state.delayingBlurEvent=!1:Ar(e)),ye(i.wrapper.ownerDocument,"mouseup",c),ye(i.wrapper.ownerDocument,"mousemove",u),ye(i.scroller,"dragstart",d),ye(i.scroller,"drop",c),o||(Se(t),r.addNew||Qi(e.doc,n,null,null,r.extend),s&&!h||a&&9==l?setTimeout(function(){i.wrapper.ownerDocument.body.focus({preventScroll:!0}),i.input.focus()},20):i.input.focus())}),u=function(e){o=o||Math.abs(t.clientX-e.clientX)+Math.abs(t.clientY-e.clientY)>=10},d=function(){return o=!0};s&&(i.scroller.draggable=!0),e.state.draggingText=c,c.copy=!r.moveOnDrag,ge(i.wrapper.ownerDocument,"mouseup",c),ge(i.wrapper.ownerDocument,"mousemove",u),ge(i.scroller,"dragstart",d),ge(i.scroller,"drop",c),e.state.delayingBlurEvent=!0,setTimeout(function(){return i.input.focus()},20),i.scroller.dragDrop&&i.scroller.dragDrop()}(e,r,t,o):function(e,t,n,r){a&&Ar(e);var i=e.display,o=e.doc;Se(t);var l,s,c=o.sel,u=c.ranges;if(r.addNew&&!r.extend?(s=o.sel.contains(n),l=s>-1?u[s]:new Ai(n,n)):(l=o.sel.primary(),s=o.sel.primIndex),"rectangle"==r.unit)r.addNew||(l=new Ai(n,n)),n=pr(e,t,!0,!0),s=-1;else{var d=ka(e,n,r.unit);l=r.extend?Ji(l,d.anchor,d.head,r.extend):d}r.addNew?-1==s?(s=u.length,io(o,Ni(e,u.concat([l]),s),{scroll:!1,origin:"*mouse"})):u.length>1&&u[s].empty()&&"char"==r.unit&&!r.extend?(io(o,Ni(e,u.slice(0,s).concat(u.slice(s+1)),0),{scroll:!1,origin:"*mouse"}),c=o.sel):to(o,s,l,$):(s=0,io(o,new Mi([l],0),$),c=o.sel);var f=n;function h(t){if(0!=at(f,t))if(f=t,"rectangle"==r.unit){for(var i=[],a=e.options.tabSize,u=q(Ze(o,n.line).text,n.ch,a),d=q(Ze(o,t.line).text,t.ch,a),h=Math.min(u,d),p=Math.max(u,d),m=Math.min(n.line,t.line),g=Math.min(e.lastLine(),Math.max(n.line,t.line));m<=g;m++){var v=Ze(o,m).text,y=X(v,h,a);h==p?i.push(new Ai(ot(m,y),ot(m,y))):v.length>y&&i.push(new Ai(ot(m,y),ot(m,X(v,p,a))))}i.length||i.push(new Ai(n,n)),io(o,Ni(e,c.ranges.slice(0,s).concat(i),s),{origin:"*mouse",scroll:!1}),e.scrollIntoView(t)}else{var b,w=l,x=ka(e,t,r.unit),k=w.anchor;at(x.anchor,k)>0?(b=x.head,k=ut(w.from(),x.anchor)):(b=x.anchor,k=ct(w.to(),x.head));var C=c.ranges.slice(0);C[s]=function(e,t){var n=t.anchor,r=t.head,i=Ze(e.doc,n.line);if(0==at(n,r)&&n.sticky==r.sticky)return t;var o=pe(i);if(!o)return t;var a=fe(o,n.ch,n.sticky),l=o[a];if(l.from!=n.ch&&l.to!=n.ch)return t;var s,c=a+(l.from==n.ch==(1!=l.level)?0:1);if(0==c||c==o.length)return t;if(r.line!=n.line)s=(r.line-n.line)*("ltr"==e.doc.direction?1:-1)>0;else{var u=fe(o,r.ch,r.sticky),d=u-a||(r.ch-n.ch)*(1==l.level?-1:1);s=u==c-1||u==c?d<0:d>0}var f=o[c+(s?-1:0)],h=s==(1==f.level),p=h?f.from:f.to,m=h?"after":"before";return n.ch==p&&n.sticky==m?t:new Ai(new ot(n.line,p,m),r)}(e,new Ai(ft(o,k),b)),io(o,Ni(e,C,s),$)}}var p=i.wrapper.getBoundingClientRect(),m=0;function g(t){var n=++m,a=pr(e,t,!0,"rectangle"==r.unit);if(a)if(0!=at(a,f)){e.curOp.focus=z(I(e)),h(a);var l=zr(i,o);(a.line>=l.to||a.line<l.from)&&setTimeout(oi(e,function(){m==n&&g(t)}),150)}else{var s=t.clientY<p.top?-20:t.clientY>p.bottom?20:0;s&&setTimeout(oi(e,function(){m==n&&(i.scroller.scrollTop+=s,g(t))}),50)}}function v(t){e.state.selectingText=!1,m=1/0,t&&(Se(t),i.input.focus()),ye(i.wrapper.ownerDocument,"mousemove",y),ye(i.wrapper.ownerDocument,"mouseup",b),o.history.lastSelOrigin=null}var y=oi(e,function(e){0!==e.buttons&&Ne(e)?g(e):v(e)}),b=oi(e,v);e.state.selectingText=b,ge(i.wrapper.ownerDocument,"mousemove",y),ge(i.wrapper.ownerDocument,"mouseup",b)}(e,r,t,o)}(t,r,o,e):Ae(e)==n.scroller&&Se(e):2==i?(r&&Qi(t.doc,r),setTimeout(function(){return n.input.focus()},20)):3==i&&(S?t.display.input.onContextMenu(e):Ar(t)))}}function ka(e,t,n){if("char"==n)return new Ai(t,t);if("word"==n)return e.findWordAt(t);if("line"==n)return new Ai(ot(t.line,0),ft(e.doc,ot(t.line+1,0)));var r=n(e,t);return new Ai(r.from,r.to)}function Ca(e,t,n,r){var i,o;if(t.touches)i=t.touches[0].clientX,o=t.touches[0].clientY;else try{i=t.clientX,o=t.clientY}catch(e){return!1}if(i>=Math.floor(e.display.gutters.getBoundingClientRect().right))return!1;r&&Se(t);var a=e.display,l=a.lineDiv.getBoundingClientRect();if(o>l.bottom||!ke(e,n))return Le(t);o-=l.top-a.viewOffset;for(var s=0;s<e.display.gutterSpecs.length;++s){var c=a.gutters.childNodes[s];if(c&&c.getBoundingClientRect().right>=i)return be(e,n,e,nt(e.doc,o),e.display.gutterSpecs[s].className,t),Le(t)}}function Sa(e,t){return Ca(e,t,"gutterClick",!0)}function Ta(e,t){Mn(e.display,t)||function(e,t){return!!ke(e,"gutterContextMenu")&&Ca(e,t,"gutterContextMenu",!1)}(e,t)||we(e,t,"contextmenu")||S||e.display.input.onContextMenu(t)}function La(e){e.display.wrapper.className=e.display.wrapper.className.replace(/\s*cm-s-\S+/g,"")+e.options.theme.replace(/(^|\s)\s*/g," cm-s-"),Kn(e)}wa.prototype.compare=function(e,t,n){return this.time+400>e&&0==at(t,this.pos)&&n==this.button};var Ma={toString:function(){return"CodeMirror.Init"}},Aa={},Na={};function Oa(e,t,n){if(!t!=!(n&&n!=Ma)){var r=e.display.dragFunctions,i=t?ge:ye;i(e.display.scroller,"dragstart",r.start),i(e.display.scroller,"dragenter",r.enter),i(e.display.scroller,"dragover",r.over),i(e.display.scroller,"dragleave",r.leave),i(e.display.scroller,"drop",r.drop)}}function _a(e){e.options.lineWrapping?(D(e.display.wrapper,"CodeMirror-wrap"),e.display.sizer.style.minWidth="",e.display.sizerWidth=null):(M(e.display.wrapper,"CodeMirror-wrap"),Xt(e)),hr(e),gr(e),Kn(e),setTimeout(function(){return Vr(e)},100)}function Ea(e,t){var n=this;if(!(this instanceof Ea))return new Ea(e,t);this.options=t=t?B(t):{},B(Aa,t,!1);var r=t.value;"string"==typeof r?r=new Wo(r,t.mode,null,t.lineSeparator,t.direction):t.mode&&(r.modeOption=t.mode),this.doc=r;var i=new Ea.inputStyles[t.inputStyle](this),o=this.display=new xi(e,r,i,t);for(var c in o.wrapper.CodeMirror=this,La(this),t.lineWrapping&&(this.display.wrapper.className+=" CodeMirror-wrap"),Yr(this),this.state={keyMaps:[],overlays:[],modeGen:0,overwrite:!1,delayingBlurEvent:!1,focused:!1,suppressEdits:!1,pasteIncoming:-1,cutIncoming:-1,selectingText:!1,draggingText:!1,highlight:new U,keySeq:null,specialChars:null},t.autofocus&&!y&&o.input.focus(),a&&l<11&&setTimeout(function(){return n.display.input.reset(!0)},20),function(e){var t=e.display;ge(t.scroller,"mousedown",oi(e,xa)),ge(t.scroller,"dblclick",a&&l<11?oi(e,function(t){if(!we(e,t)){var n=pr(e,t);if(n&&!Sa(e,t)&&!Mn(e.display,t)){Se(t);var r=e.findWordAt(n);Qi(e.doc,r.anchor,r.head)}}}):function(t){return we(e,t)||Se(t)}),ge(t.scroller,"contextmenu",function(t){return Ta(e,t)}),ge(t.input.getField(),"contextmenu",function(n){t.scroller.contains(n.target)||Ta(e,n)});var n,r={end:0};function i(){t.activeTouch&&(n=setTimeout(function(){return t.activeTouch=null},1e3),(r=t.activeTouch).end=+new Date)}function o(e){if(1!=e.touches.length)return!1;var t=e.touches[0];return t.radiusX<=1&&t.radiusY<=1}function s(e,t){if(null==t.left)return!0;var n=t.left-e.left,r=t.top-e.top;return n*n+r*r>400}ge(t.scroller,"touchstart",function(i){if(!we(e,i)&&!o(i)&&!Sa(e,i)){t.input.ensurePolled(),clearTimeout(n);var a=+new Date;t.activeTouch={start:a,moved:!1,prev:a-r.end<=300?r:null},1==i.touches.length&&(t.activeTouch.left=i.touches[0].pageX,t.activeTouch.top=i.touches[0].pageY)}}),ge(t.scroller,"touchmove",function(){t.activeTouch&&(t.activeTouch.moved=!0)}),ge(t.scroller,"touchend",function(n){var r=t.activeTouch;if(r&&!Mn(t,n)&&null!=r.left&&!r.moved&&new Date-r.start<300){var o,a=e.coordsChar(t.activeTouch,"page");o=!r.prev||s(r,r.prev)?new Ai(a,a):!r.prev.prev||s(r,r.prev.prev)?e.findWordAt(a):new Ai(ot(a.line,0),ft(e.doc,ot(a.line+1,0))),e.setSelection(o.anchor,o.head),e.focus(),Se(n)}i()}),ge(t.scroller,"touchcancel",i),ge(t.scroller,"scroll",function(){t.scroller.clientHeight&&(Rr(e,t.scroller.scrollTop),Br(e,t.scroller.scrollLeft,!0),be(e,"scroll",e))}),ge(t.scroller,"mousewheel",function(t){return Li(e,t)}),ge(t.scroller,"DOMMouseScroll",function(t){return Li(e,t)}),ge(t.wrapper,"scroll",function(){return t.wrapper.scrollTop=t.wrapper.scrollLeft=0}),t.dragFunctions={enter:function(t){we(e,t)||Me(t)},over:function(t){we(e,t)||(function(e,t){var n=pr(e,t);if(n){var r=document.createDocumentFragment();Cr(e,n,r),e.display.dragCursor||(e.display.dragCursor=O("div",null,"CodeMirror-cursors CodeMirror-dragcursors"),e.display.lineSpace.insertBefore(e.display.dragCursor,e.display.cursorDiv)),N(e.display.dragCursor,r)}}(e,t),Me(t))},start:function(t){return function(e,t){if(a&&(!e.state.draggingText||+new Date-Fo<100))Me(t);else if(!we(e,t)&&!Mn(e.display,t)&&(t.dataTransfer.setData("Text",e.getSelection()),t.dataTransfer.effectAllowed="copyMove",t.dataTransfer.setDragImage&&!h)){var n=O("img",null,null,"position: fixed; left: 0; top: 0;");n.src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==",f&&(n.width=n.height=1,e.display.wrapper.appendChild(n),n._top=n.offsetTop),t.dataTransfer.setDragImage(n,0,0),f&&n.parentNode.removeChild(n)}}(e,t)},drop:oi(e,Io),leave:function(t){we(e,t)||Ho(e)}};var c=t.input.getField();ge(c,"keyup",function(t){return ga.call(e,t)}),ge(c,"keydown",oi(e,ma)),ge(c,"keypress",oi(e,va)),ge(c,"focus",function(t){return Nr(e,t)}),ge(c,"blur",function(t){return Or(e,t)})}(this),Bo(),Zr(this),this.curOp.forceUpdate=!0,Ri(this,r),t.autofocus&&!y||this.hasFocus()?setTimeout(function(){n.hasFocus()&&!n.state.focused&&Nr(n)},20):Or(this),Na)Na.hasOwnProperty(c)&&Na[c](this,t[c],Ma);vi(this),t.finishInit&&t.finishInit(this);for(var u=0;u<za.length;++u)za[u](this);Jr(this),s&&t.lineWrapping&&"optimizelegibility"==getComputedStyle(o.lineDiv).textRendering&&(o.lineDiv.style.textRendering="auto")}Ea.defaults=Aa,Ea.optionHandlers=Na;var za=[];function Da(e,t,n,r){var i,o=e.doc;null==n&&(n="add"),"smart"==n&&(o.mode.indent?i=yt(e,t).state:n="prev");var a=e.options.tabSize,l=Ze(o,t),s=q(l.text,null,a);l.stateAfter&&(l.stateAfter=null);var c,u=l.text.match(/^\s*/)[0];if(r||/\S/.test(l.text)){if("smart"==n&&((c=o.mode.indent(i,l.text.slice(u.length),l.text))==V||c>150)){if(!r)return;n="prev"}}else c=0,n="not";"prev"==n?c=t>o.first?q(Ze(o,t-1).text,null,a):0:"add"==n?c=s+e.options.indentUnit:"subtract"==n?c=s-e.options.indentUnit:"number"==typeof n&&(c=s+n),c=Math.max(0,c);var d="",f=0;if(e.options.indentWithTabs)for(var h=Math.floor(c/a);h;--h)f+=a,d+="\t";if(f<c&&(d+=J(c-f)),d!=u)return wo(o,d,ot(t,0),ot(t,u.length),"+input"),l.stateAfter=null,!0;for(var p=0;p<o.sel.ranges.length;p++){var m=o.sel.ranges[p];if(m.head.line==t&&m.head.ch<u.length){var g=ot(t,u.length);to(o,p,new Ai(g,g));break}}}Ea.defineInitHook=function(e){return za.push(e)};var Pa=null;function Wa(e){Pa=e}function Fa(e,t,n,r,i){var o=e.doc;e.display.shift=!1,r||(r=o.sel);var a=+new Date-200,l="paste"==i||e.state.pasteIncoming>a,s=We(t),c=null;if(l&&r.ranges.length>1)if(Pa&&Pa.text.join("\n")==t){if(r.ranges.length%Pa.text.length==0){c=[];for(var u=0;u<Pa.text.length;u++)c.push(o.splitLines(Pa.text[u]))}}else s.length==r.ranges.length&&e.options.pasteLinesPerSelection&&(c=ee(s,function(e){return[e]}));for(var d=e.curOp.updateInput,f=r.ranges.length-1;f>=0;f--){var h=r.ranges[f],p=h.from(),m=h.to();h.empty()&&(n&&n>0?p=ot(p.line,p.ch-n):e.state.overwrite&&!l?m=ot(m.line,Math.min(Ze(o,m.line).text.length,m.ch+Q(s).length)):l&&Pa&&Pa.lineWise&&Pa.text.join("\n")==s.join("\n")&&(p=m=ot(p.line,0)));var g={from:p,to:m,text:c?c[f%c.length]:s,origin:i||(l?"paste":e.state.cutIncoming>a?"cut":"+input")};mo(e.doc,g),hn(e,"inputRead",e,g)}t&&!l&&Ha(e,t),Wr(e),e.curOp.updateInput<2&&(e.curOp.updateInput=d),e.curOp.typing=!0,e.state.pasteIncoming=e.state.cutIncoming=-1}function Ia(e,t){var n=e.clipboardData&&e.clipboardData.getData("Text");if(n)return e.preventDefault(),t.isReadOnly()||t.options.disableInput||!t.hasFocus()||ii(t,function(){return Fa(t,n,0,null,"paste")}),!0}function Ha(e,t){if(e.options.electricChars&&e.options.smartIndent)for(var n=e.doc.sel,r=n.ranges.length-1;r>=0;r--){var i=n.ranges[r];if(!(i.head.ch>100||r&&n.ranges[r-1].head.line==i.head.line)){var o=e.getModeAt(i.head),a=!1;if(o.electricChars){for(var l=0;l<o.electricChars.length;l++)if(t.indexOf(o.electricChars.charAt(l))>-1){a=Da(e,i.head.line,"smart");break}}else o.electricInput&&o.electricInput.test(Ze(e.doc,i.head.line).text.slice(0,i.head.ch))&&(a=Da(e,i.head.line,"smart"));a&&hn(e,"electricInput",e,i.head.line)}}}function Ra(e){for(var t=[],n=[],r=0;r<e.doc.sel.ranges.length;r++){var i=e.doc.sel.ranges[r].head.line,o={anchor:ot(i,0),head:ot(i+1,0)};n.push(o),t.push(e.getRange(o.anchor,o.head))}return{text:t,ranges:n}}function ja(e,t,n,r){e.setAttribute("autocorrect",n?"on":"off"),e.setAttribute("autocapitalize",r?"on":"off"),e.setAttribute("spellcheck",!!t)}function Ba(){var e=O("textarea",null,null,"position: absolute; bottom: -1em; padding: 0; width: 1px; height: 1em; min-height: 1em; outline: none"),t=O("div",[e],null,"overflow: hidden; position: relative; width: 3px; height: 0px;");return s?e.style.width="1000px":e.setAttribute("wrap","off"),g&&(e.style.border="1px solid black"),t}function qa(e,t,n,r,i){var o=t,a=n,l=Ze(e,t.line),s=i&&"rtl"==e.direction?-n:n;function c(o){var a,c;if("codepoint"==r){var u=l.text.charCodeAt(t.ch+(n>0?0:-1));if(isNaN(u))a=null;else{var d=n>0?u>=55296&&u<56320:u>=56320&&u<57343;a=new ot(t.line,Math.max(0,Math.min(l.text.length,t.ch+n*(d?2:1))),-n)}}else a=i?function(e,t,n,r){var i=pe(t,e.doc.direction);if(!i)return ia(t,n,r);n.ch>=t.text.length?(n.ch=t.text.length,n.sticky="before"):n.ch<=0&&(n.ch=0,n.sticky="after");var o=fe(i,n.ch,n.sticky),a=i[o];if("ltr"==e.doc.direction&&a.level%2==0&&(r>0?a.to>n.ch:a.from<n.ch))return ia(t,n,r);var l,s=function(e,n){return ra(t,e instanceof ot?e.ch:e,n)},c=function(n){return e.options.lineWrapping?(l=l||Fn(e,t),rr(e,t,l,n)):{begin:0,end:t.text.length}},u=c("before"==n.sticky?s(n,-1):n.ch);if("rtl"==e.doc.direction||1==a.level){var d=1==a.level==r<0,f=s(n,d?1:-1);if(null!=f&&(d?f<=a.to&&f<=u.end:f>=a.from&&f>=u.begin)){var h=d?"before":"after";return new ot(n.line,f,h)}}var p=function(e,t,r){for(var o=function(e,t){return t?new ot(n.line,s(e,1),"before"):new ot(n.line,e,"after")};e>=0&&e<i.length;e+=t){var a=i[e],l=t>0==(1!=a.level),c=l?r.begin:s(r.end,-1);if(a.from<=c&&c<a.to)return o(c,l);if(c=l?a.from:s(a.to,-1),r.begin<=c&&c<r.end)return o(c,l)}},m=p(o+r,r,u);if(m)return m;var g=r>0?u.end:s(u.begin,-1);return null==g||r>0&&g==t.text.length||!(m=p(r>0?0:i.length-1,r,c(g)))?null:m}(e.cm,l,t,n):ia(l,t,n);if(null==a){if(o||((c=t.line+s)<e.first||c>=e.first+e.size||(t=new ot(c,t.ch,t.sticky),!(l=Ze(e,c)))))return!1;t=oa(i,e.cm,l,t.line,s)}else t=a;return!0}if("char"==r||"codepoint"==r)c();else if("column"==r)c(!0);else if("word"==r||"group"==r)for(var u=null,d="group"==r,f=e.cm&&e.cm.getHelper(t,"wordChars"),h=!0;!(n<0)||c(!h);h=!1){var p=l.text.charAt(t.ch)||"\n",m=oe(p,f)?"w":d&&"\n"==p?"n":!d||/\s/.test(p)?null:"p";if(!d||h||m||(m="s"),u&&u!=m){n<0&&(n=1,c(),t.sticky="after");break}if(m&&(u=m),n>0&&!c(!h))break}var g=uo(e,t,o,a,!0);return lt(o,g)&&(g.hitSide=!0),g}function Ua(e,t,n,r){var i,o,a=e.doc,l=t.left;if("page"==r){var s=Math.min(e.display.wrapper.clientHeight,R(e).innerHeight||a(e).documentElement.clientHeight),c=Math.max(s-.5*sr(e.display),3);i=(n>0?t.bottom:t.top)+n*c}else"line"==r&&(i=n>0?t.bottom+3:t.top-3);for(;(o=tr(e,l,i)).outside;){if(n<0?i<=0:i>=a.height){o.hitSide=!0;break}i+=5*n}return o}var Ka=function(e){this.cm=e,this.lastAnchorNode=this.lastAnchorOffset=this.lastFocusNode=this.lastFocusOffset=null,this.polling=new U,this.composing=null,this.gracePeriod=!1,this.readDOMTimeout=null};function Va(e,t){var n=Wn(e,t.line);if(!n||n.hidden)return null;var r=Ze(e.doc,t.line),i=Dn(n,r,t.line),o=pe(r,e.doc.direction),a="left";o&&(a=fe(o,t.ch)%2?"right":"left");var l=jn(i.map,t.ch,a);return l.offset="right"==l.collapse?l.end:l.start,l}function Ga(e,t){return t&&(e.bad=!0),e}function $a(e,t,n){var r;if(t==e.display.lineDiv){if(!(r=e.display.lineDiv.childNodes[n]))return Ga(e.clipPos(ot(e.display.viewTo-1)),!0);t=null,n=0}else for(r=t;;r=r.parentNode){if(!r||r==e.display.lineDiv)return null;if(r.parentNode&&r.parentNode==e.display.lineDiv)break}for(var i=0;i<e.display.view.length;i++){var o=e.display.view[i];if(o.node==r)return Ya(o,t,n)}}function Ya(e,t,n){var r=e.text.firstChild,i=!1;if(!t||!E(r,t))return Ga(ot(tt(e.line),0),!0);if(t==r&&(i=!0,t=r.childNodes[n],n=0,!t)){var o=e.rest?Q(e.rest):e.line;return Ga(ot(tt(o),o.text.length),i)}var a=3==t.nodeType?t:null,l=t;for(a||1!=t.childNodes.length||3!=t.firstChild.nodeType||(a=t.firstChild,n&&(n=a.nodeValue.length));l.parentNode!=r;)l=l.parentNode;var s=e.measure,c=s.maps;function u(t,n,r){for(var i=-1;i<(c?c.length:0);i++)for(var o=i<0?s.map:c[i],a=0;a<o.length;a+=3){var l=o[a+2];if(l==t||l==n){var u=tt(i<0?e.line:e.rest[i]),d=o[a]+r;return(r<0||l!=t)&&(d=o[a+(r?1:0)]),ot(u,d)}}}var d=u(a,l,n);if(d)return Ga(d,i);for(var f=l.nextSibling,h=a?a.nodeValue.length-n:0;f;f=f.nextSibling){if(d=u(f,f.firstChild,0))return Ga(ot(d.line,d.ch-h),i);h+=f.textContent.length}for(var p=l.previousSibling,m=n;p;p=p.previousSibling){if(d=u(p,p.firstChild,-1))return Ga(ot(d.line,d.ch+m),i);m+=p.textContent.length}}Ka.prototype.init=function(e){var t=this,n=this,r=n.cm,i=n.div=e.lineDiv;function o(e){for(var t=e.target;t;t=t.parentNode){if(t==i)return!0;if(/\bCodeMirror-(?:line)?widget\b/.test(t.className))break}return!1}function a(e){if(o(e)&&!we(r,e)){if(r.somethingSelected())Wa({lineWise:!1,text:r.getSelections()}),"cut"==e.type&&r.replaceSelection("",null,"cut");else{if(!r.options.lineWiseCopyCut)return;var t=Ra(r);Wa({lineWise:!0,text:t.text}),"cut"==e.type&&r.operation(function(){r.setSelections(t.ranges,0,G),r.replaceSelection("",null,"cut")})}if(e.clipboardData){e.clipboardData.clearData();var a=Pa.text.join("\n");if(e.clipboardData.setData("Text",a),e.clipboardData.getData("Text")==a)return void e.preventDefault()}var l=Ba(),s=l.firstChild;ja(s),r.display.lineSpace.insertBefore(l,r.display.lineSpace.firstChild),s.value=Pa.text.join("\n");var c=z(H(i));W(s),setTimeout(function(){r.display.lineSpace.removeChild(l),c.focus(),c==i&&n.showPrimarySelection()},50)}}i.contentEditable=!0,ja(i,r.options.spellcheck,r.options.autocorrect,r.options.autocapitalize),ge(i,"paste",function(e){!o(e)||we(r,e)||Ia(e,r)||l<=11&&setTimeout(oi(r,function(){return t.updateFromDOM()}),20)}),ge(i,"compositionstart",function(e){t.composing={data:e.data,done:!1}}),ge(i,"compositionupdate",function(e){t.composing||(t.composing={data:e.data,done:!1})}),ge(i,"compositionend",function(e){t.composing&&(e.data!=t.composing.data&&t.readFromDOMSoon(),t.composing.done=!0)}),ge(i,"touchstart",function(){return n.forceCompositionEnd()}),ge(i,"input",function(){t.composing||t.readFromDOMSoon()}),ge(i,"copy",a),ge(i,"cut",a)},Ka.prototype.screenReaderLabelChanged=function(e){e?this.div.setAttribute("aria-label",e):this.div.removeAttribute("aria-label")},Ka.prototype.prepareSelection=function(){var e=kr(this.cm,!1);return e.focus=z(H(this.div))==this.div,e},Ka.prototype.showSelection=function(e,t){e&&this.cm.display.view.length&&((e.focus||t)&&this.showPrimarySelection(),this.showMultipleSelections(e))},Ka.prototype.getSelection=function(){return this.cm.display.wrapper.ownerDocument.getSelection()},Ka.prototype.showPrimarySelection=function(){var e=this.getSelection(),t=this.cm,r=t.doc.sel.primary(),i=r.from(),o=r.to();if(t.display.viewTo==t.display.viewFrom||i.line>=t.display.viewTo||o.line<t.display.viewFrom)e.removeAllRanges();else{var a=$a(t,e.anchorNode,e.anchorOffset),l=$a(t,e.focusNode,e.focusOffset);if(!a||a.bad||!l||l.bad||0!=at(ut(a,l),i)||0!=at(ct(a,l),o)){var s=t.display.view,c=i.line>=t.display.viewFrom&&Va(t,i)||{node:s[0].measure.map[2],offset:0},u=o.line<t.display.viewTo&&Va(t,o);if(!u){var d=s[s.length-1].measure,f=d.maps?d.maps[d.maps.length-1]:d.map;u={node:f[f.length-1],offset:f[f.length-2]-f[f.length-3]}}if(c&&u){var h,p=e.rangeCount&&e.getRangeAt(0);try{h=L(c.node,c.offset,u.offset,u.node)}catch(e){}h&&(!n&&t.state.focused?(e.collapse(c.node,c.offset),h.collapsed||(e.removeAllRanges(),e.addRange(h))):(e.removeAllRanges(),e.addRange(h)),p&&null==e.anchorNode?e.addRange(p):n&&this.startGracePeriod()),this.rememberSelection()}else e.removeAllRanges()}}},Ka.prototype.startGracePeriod=function(){var e=this;clearTimeout(this.gracePeriod),this.gracePeriod=setTimeout(function(){e.gracePeriod=!1,e.selectionChanged()&&e.cm.operation(function(){return e.cm.curOp.selectionChanged=!0})},20)},Ka.prototype.showMultipleSelections=function(e){N(this.cm.display.cursorDiv,e.cursors),N(this.cm.display.selectionDiv,e.selection)},Ka.prototype.rememberSelection=function(){var e=this.getSelection();this.lastAnchorNode=e.anchorNode,this.lastAnchorOffset=e.anchorOffset,this.lastFocusNode=e.focusNode,this.lastFocusOffset=e.focusOffset},Ka.prototype.selectionInEditor=function(){var e=this.getSelection();if(!e.rangeCount)return!1;var t=e.getRangeAt(0).commonAncestorContainer;return E(this.div,t)},Ka.prototype.focus=function(){"nocursor"!=this.cm.options.readOnly&&(this.selectionInEditor()&&z(H(this.div))==this.div||this.showSelection(this.prepareSelection(),!0),this.div.focus())},Ka.prototype.blur=function(){this.div.blur()},Ka.prototype.getField=function(){return this.div},Ka.prototype.supportsTouch=function(){return!0},Ka.prototype.receivedFocus=function(){var e=this,t=this;this.selectionInEditor()?setTimeout(function(){return e.pollSelection()},20):ii(this.cm,function(){return t.cm.curOp.selectionChanged=!0}),this.polling.set(this.cm.options.pollInterval,function e(){t.cm.state.focused&&(t.pollSelection(),t.polling.set(t.cm.options.pollInterval,e))})},Ka.prototype.selectionChanged=function(){var e=this.getSelection();return e.anchorNode!=this.lastAnchorNode||e.anchorOffset!=this.lastAnchorOffset||e.focusNode!=this.lastFocusNode||e.focusOffset!=this.lastFocusOffset},Ka.prototype.pollSelection=function(){if(null==this.readDOMTimeout&&!this.gracePeriod&&this.selectionChanged()){var e=this.getSelection(),t=this.cm;if(v&&u&&this.cm.display.gutterSpecs.length&&function(e){for(var t=e;t;t=t.parentNode)if(/CodeMirror-gutter-wrapper/.test(t.className))return!0;return!1}(e.anchorNode))return this.cm.triggerOnKeyDown({type:"keydown",keyCode:8,preventDefault:Math.abs}),this.blur(),void this.focus();if(!this.composing){this.rememberSelection();var n=$a(t,e.anchorNode,e.anchorOffset),r=$a(t,e.focusNode,e.focusOffset);n&&r&&ii(t,function(){io(t.doc,Oi(n,r),G),(n.bad||r.bad)&&(t.curOp.selectionChanged=!0)})}}},Ka.prototype.pollContent=function(){null!=this.readDOMTimeout&&(clearTimeout(this.readDOMTimeout),this.readDOMTimeout=null);var e,t,n,r=this.cm,i=r.display,o=r.doc.sel.primary(),a=o.from(),l=o.to();if(0==a.ch&&a.line>r.firstLine()&&(a=ot(a.line-1,Ze(r.doc,a.line-1).length)),l.ch==Ze(r.doc,l.line).text.length&&l.line<r.lastLine()&&(l=ot(l.line+1,0)),a.line<i.viewFrom||l.line>i.viewTo-1)return!1;a.line==i.viewFrom||0==(e=mr(r,a.line))?(t=tt(i.view[0].line),n=i.view[0].node):(t=tt(i.view[e].line),n=i.view[e-1].node.nextSibling);var s,c,u=mr(r,l.line);if(u==i.view.length-1?(s=i.viewTo-1,c=i.lineDiv.lastChild):(s=tt(i.view[u+1].line)-1,c=i.view[u+1].node.previousSibling),!n)return!1;for(var d=r.doc.splitLines(function(e,t,n,r,i){var o="",a=!1,l=e.doc.lineSeparator(),s=!1;function c(){a&&(o+=l,s&&(o+=l),a=s=!1)}function u(e){e&&(c(),o+=e)}function d(t){if(1==t.nodeType){var n=t.getAttribute("cm-text");if(n)return void u(n);var o,f=t.getAttribute("cm-marker");if(f){var h=e.findMarks(ot(r,0),ot(i+1,0),(g=+f,function(e){return e.id==g}));return void(h.length&&(o=h[0].find(0))&&u(Je(e.doc,o.from,o.to).join(l)))}if("false"==t.getAttribute("contenteditable"))return;var p=/^(pre|div|p|li|table|br)$/i.test(t.nodeName);if(!/^br$/i.test(t.nodeName)&&0==t.textContent.length)return;p&&c();for(var m=0;m<t.childNodes.length;m++)d(t.childNodes[m]);/^(pre|p)$/i.test(t.nodeName)&&(s=!0),p&&(a=!0)}else 3==t.nodeType&&u(t.nodeValue.replace(/\u200b/g,"").replace(/\u00a0/g," "));var g}for(;d(t),t!=n;)t=t.nextSibling,s=!1;return o}(r,n,c,t,s)),f=Je(r.doc,ot(t,0),ot(s,Ze(r.doc,s).text.length));d.length>1&&f.length>1;)if(Q(d)==Q(f))d.pop(),f.pop(),s--;else{if(d[0]!=f[0])break;d.shift(),f.shift(),t++}for(var h=0,p=0,m=d[0],g=f[0],v=Math.min(m.length,g.length);h<v&&m.charCodeAt(h)==g.charCodeAt(h);)++h;for(var y=Q(d),b=Q(f),w=Math.min(y.length-(1==d.length?h:0),b.length-(1==f.length?h:0));p<w&&y.charCodeAt(y.length-p-1)==b.charCodeAt(b.length-p-1);)++p;if(1==d.length&&1==f.length&&t==a.line)for(;h&&h>a.ch&&y.charCodeAt(y.length-p-1)==b.charCodeAt(b.length-p-1);)h--,p++;d[d.length-1]=y.slice(0,y.length-p).replace(/^\u200b+/,""),d[0]=d[0].slice(h).replace(/\u200b+$/,"");var x=ot(t,h),k=ot(s,f.length?Q(f).length-p:0);return d.length>1||d[0]||at(x,k)?(wo(r.doc,d,x,k,"+input"),!0):void 0},Ka.prototype.ensurePolled=function(){this.forceCompositionEnd()},Ka.prototype.reset=function(){this.forceCompositionEnd()},Ka.prototype.forceCompositionEnd=function(){this.composing&&(clearTimeout(this.readDOMTimeout),this.composing=null,this.updateFromDOM(),this.div.blur(),this.div.focus())},Ka.prototype.readFromDOMSoon=function(){var e=this;null==this.readDOMTimeout&&(this.readDOMTimeout=setTimeout(function(){if(e.readDOMTimeout=null,e.composing){if(!e.composing.done)return;e.composing=null}e.updateFromDOM()},80))},Ka.prototype.updateFromDOM=function(){var e=this;!this.cm.isReadOnly()&&this.pollContent()||ii(this.cm,function(){return gr(e.cm)})},Ka.prototype.setUneditable=function(e){e.contentEditable="false"},Ka.prototype.onKeyPress=function(e){0==e.charCode||this.composing||(e.preventDefault(),this.cm.isReadOnly()||oi(this.cm,Fa)(this.cm,String.fromCharCode(null==e.charCode?e.keyCode:e.charCode),0))},Ka.prototype.readOnlyChanged=function(e){this.div.contentEditable=String("nocursor"!=e)},Ka.prototype.onContextMenu=function(){},Ka.prototype.resetPosition=function(){},Ka.prototype.needsContentAttribute=!0;var Xa=function(e){this.cm=e,this.prevInput="",this.pollingFast=!1,this.polling=new U,this.hasSelection=!1,this.composing=null,this.resetting=!1};Xa.prototype.init=function(e){var t=this,n=this,r=this.cm;this.createField(e);var i=this.textarea;function o(e){if(!we(r,e)){if(r.somethingSelected())Wa({lineWise:!1,text:r.getSelections()});else{if(!r.options.lineWiseCopyCut)return;var t=Ra(r);Wa({lineWise:!0,text:t.text}),"cut"==e.type?r.setSelections(t.ranges,null,G):(n.prevInput="",i.value=t.text.join("\n"),W(i))}"cut"==e.type&&(r.state.cutIncoming=+new Date)}}e.wrapper.insertBefore(this.wrapper,e.wrapper.firstChild),g&&(i.style.width="0px"),ge(i,"input",function(){a&&l>=9&&t.hasSelection&&(t.hasSelection=null),n.poll()}),ge(i,"paste",function(e){we(r,e)||Ia(e,r)||(r.state.pasteIncoming=+new Date,n.fastPoll())}),ge(i,"cut",o),ge(i,"copy",o),ge(e.scroller,"paste",function(t){if(!Mn(e,t)&&!we(r,t)){if(!i.dispatchEvent)return r.state.pasteIncoming=+new Date,void n.focus();var o=new Event("paste");o.clipboardData=t.clipboardData,i.dispatchEvent(o)}}),ge(e.lineSpace,"selectstart",function(t){Mn(e,t)||Se(t)}),ge(i,"compositionstart",function(){var e=r.getCursor("from");n.composing&&n.composing.range.clear(),n.composing={start:e,range:r.markText(e,r.getCursor("to"),{className:"CodeMirror-composing"})}}),ge(i,"compositionend",function(){n.composing&&(n.poll(),n.composing.range.clear(),n.composing=null)})},Xa.prototype.createField=function(e){this.wrapper=Ba(),this.textarea=this.wrapper.firstChild;var t=this.cm.options;ja(this.textarea,t.spellcheck,t.autocorrect,t.autocapitalize)},Xa.prototype.screenReaderLabelChanged=function(e){e?this.textarea.setAttribute("aria-label",e):this.textarea.removeAttribute("aria-label")},Xa.prototype.prepareSelection=function(){var e=this.cm,t=e.display,n=e.doc,r=kr(e);if(e.options.moveInputWithCursor){var i=Jn(e,n.sel.primary().head,"div"),o=t.wrapper.getBoundingClientRect(),a=t.lineDiv.getBoundingClientRect();r.teTop=Math.max(0,Math.min(t.wrapper.clientHeight-10,i.top+a.top-o.top)),r.teLeft=Math.max(0,Math.min(t.wrapper.clientWidth-10,i.left+a.left-o.left))}return r},Xa.prototype.showSelection=function(e){var t=this.cm.display;N(t.cursorDiv,e.cursors),N(t.selectionDiv,e.selection),null!=e.teTop&&(this.wrapper.style.top=e.teTop+"px",this.wrapper.style.left=e.teLeft+"px")},Xa.prototype.reset=function(e){if(!(this.contextMenuPending||this.composing&&e)){var t=this.cm;if(this.resetting=!0,t.somethingSelected()){this.prevInput="";var n=t.getSelection();this.textarea.value=n,t.state.focused&&W(this.textarea),a&&l>=9&&(this.hasSelection=n)}else e||(this.prevInput=this.textarea.value="",a&&l>=9&&(this.hasSelection=null));this.resetting=!1}},Xa.prototype.getField=function(){return this.textarea},Xa.prototype.supportsTouch=function(){return!1},Xa.prototype.focus=function(){if("nocursor"!=this.cm.options.readOnly&&(!y||z(H(this.textarea))!=this.textarea))try{this.textarea.focus()}catch(e){}},Xa.prototype.blur=function(){this.textarea.blur()},Xa.prototype.resetPosition=function(){this.wrapper.style.top=this.wrapper.style.left=0},Xa.prototype.receivedFocus=function(){this.slowPoll()},Xa.prototype.slowPoll=function(){var e=this;this.pollingFast||this.polling.set(this.cm.options.pollInterval,function(){e.poll(),e.cm.state.focused&&e.slowPoll()})},Xa.prototype.fastPoll=function(){var e=!1,t=this;t.pollingFast=!0,t.polling.set(20,function n(){t.poll()||e?(t.pollingFast=!1,t.slowPoll()):(e=!0,t.polling.set(60,n))})},Xa.prototype.poll=function(){var e=this,t=this.cm,n=this.textarea,r=this.prevInput;if(this.contextMenuPending||this.resetting||!t.state.focused||Fe(n)&&!r&&!this.composing||t.isReadOnly()||t.options.disableInput||t.state.keySeq)return!1;var i=n.value;if(i==r&&!t.somethingSelected())return!1;if(a&&l>=9&&this.hasSelection===i||b&&/[\uf700-\uf7ff]/.test(i))return t.display.input.reset(),!1;if(t.doc.sel==t.display.selForContextMenu){var o=i.charCodeAt(0);if(8203!=o||r||(r="​"),8666==o)return this.reset(),this.cm.execCommand("undo")}for(var s=0,c=Math.min(r.length,i.length);s<c&&r.charCodeAt(s)==i.charCodeAt(s);)++s;return ii(t,function(){Fa(t,i.slice(s),r.length-s,null,e.composing?"*compose":null),i.length>1e3||i.indexOf("\n")>-1?n.value=e.prevInput="":e.prevInput=i,e.composing&&(e.composing.range.clear(),e.composing.range=t.markText(e.composing.start,t.getCursor("to"),{className:"CodeMirror-composing"}))}),!0},Xa.prototype.ensurePolled=function(){this.pollingFast&&this.poll()&&(this.pollingFast=!1)},Xa.prototype.onKeyPress=function(){a&&l>=9&&(this.hasSelection=null),this.fastPoll()},Xa.prototype.onContextMenu=function(e){var t=this,n=t.cm,r=n.display,i=t.textarea;t.contextMenuPending&&t.contextMenuPending();var o=pr(n,e),c=r.scroller.scrollTop;if(o&&!f){n.options.resetSelectionOnContextMenu&&-1==n.doc.sel.contains(o)&&oi(n,io)(n.doc,Oi(o),G);var u,d=i.style.cssText,h=t.wrapper.style.cssText,p=t.wrapper.offsetParent.getBoundingClientRect();if(t.wrapper.style.cssText="position: static",i.style.cssText="position: absolute; width: 30px; height: 30px;\n      top: "+(e.clientY-p.top-5)+"px; left: "+(e.clientX-p.left-5)+"px;\n      z-index: 1000; background: "+(a?"rgba(255, 255, 255, .05)":"transparent")+";\n      outline: none; border-width: 0; outline: none; overflow: hidden; opacity: .05; filter: alpha(opacity=5);",s&&(u=i.ownerDocument.defaultView.scrollY),r.input.focus(),s&&i.ownerDocument.defaultView.scrollTo(null,u),r.input.reset(),n.somethingSelected()||(i.value=t.prevInput=" "),t.contextMenuPending=v,r.selForContextMenu=n.doc.sel,clearTimeout(r.detectingSelectAll),a&&l>=9&&g(),S){Me(e);var m=function(){ye(window,"mouseup",m),setTimeout(v,20)};ge(window,"mouseup",m)}else setTimeout(v,50)}function g(){if(null!=i.selectionStart){var e=n.somethingSelected(),o="​"+(e?i.value:"");i.value="⇚",i.value=o,t.prevInput=e?"":"​",i.selectionStart=1,i.selectionEnd=o.length,r.selForContextMenu=n.doc.sel}}function v(){if(t.contextMenuPending==v&&(t.contextMenuPending=!1,t.wrapper.style.cssText=h,i.style.cssText=d,a&&l<9&&r.scrollbars.setScrollTop(r.scroller.scrollTop=c),null!=i.selectionStart)){(!a||a&&l<9)&&g();var e=0,o=function(){r.selForContextMenu==n.doc.sel&&0==i.selectionStart&&i.selectionEnd>0&&"​"==t.prevInput?oi(n,ho)(n):e++<10?r.detectingSelectAll=setTimeout(o,500):(r.selForContextMenu=null,r.input.reset())};r.detectingSelectAll=setTimeout(o,200)}}},Xa.prototype.readOnlyChanged=function(e){e||this.reset(),this.textarea.disabled="nocursor"==e,this.textarea.readOnly=!!e},Xa.prototype.setUneditable=function(){},Xa.prototype.needsContentAttribute=!1,function(e){var t=e.optionHandlers;function n(n,r,i,o){e.defaults[n]=r,i&&(t[n]=o?function(e,t,n){n!=Ma&&i(e,t,n)}:i)}e.defineOption=n,e.Init=Ma,n("value","",function(e,t){return e.setValue(t)},!0),n("mode",null,function(e,t){e.doc.modeOption=t,Pi(e)},!0),n("indentUnit",2,Pi,!0),n("indentWithTabs",!1),n("smartIndent",!0),n("tabSize",4,function(e){Wi(e),Kn(e),gr(e)},!0),n("lineSeparator",null,function(e,t){if(e.doc.lineSep=t,t){var n=[],r=e.doc.first;e.doc.iter(function(e){for(var i=0;;){var o=e.text.indexOf(t,i);if(-1==o)break;i=o+t.length,n.push(ot(r,o))}r++});for(var i=n.length-1;i>=0;i--)wo(e.doc,t,n[i],ot(n[i].line,n[i].ch+t.length))}}),n("specialChars",/[\u0000-\u001f\u007f-\u009f\u00ad\u061c\u200b\u200e\u200f\u2028\u2029\u202d\u202e\u2066\u2067\u2069\ufeff\ufff9-\ufffc]/g,function(e,t,n){e.state.specialChars=new RegExp(t.source+(t.test("\t")?"":"|\t"),"g"),n!=Ma&&e.refresh()}),n("specialCharPlaceholder",rn,function(e){return e.refresh()},!0),n("electricChars",!0),n("inputStyle",y?"contenteditable":"textarea",function(){throw new Error("inputStyle can not (yet) be changed in a running editor")},!0),n("spellcheck",!1,function(e,t){return e.getInputField().spellcheck=t},!0),n("autocorrect",!1,function(e,t){return e.getInputField().autocorrect=t},!0),n("autocapitalize",!1,function(e,t){return e.getInputField().autocapitalize=t},!0),n("rtlMoveVisually",!x),n("wholeLineUpdateBefore",!0),n("theme","default",function(e){La(e),wi(e)},!0),n("keyMap","default",function(e,t,n){var r=ta(t),i=n!=Ma&&ta(n);i&&i.detach&&i.detach(e,r),r.attach&&r.attach(e,i||null)}),n("extraKeys",null),n("configureMouse",null),n("lineWrapping",!1,_a,!0),n("gutters",[],function(e,t){e.display.gutterSpecs=yi(t,e.options.lineNumbers),wi(e)},!0),n("fixedGutter",!0,function(e,t){e.display.gutters.style.left=t?dr(e.display)+"px":"0",e.refresh()},!0),n("coverGutterNextToScrollbar",!1,function(e){return Vr(e)},!0),n("scrollbarStyle","native",function(e){Yr(e),Vr(e),e.display.scrollbars.setScrollTop(e.doc.scrollTop),e.display.scrollbars.setScrollLeft(e.doc.scrollLeft)},!0),n("lineNumbers",!1,function(e,t){e.display.gutterSpecs=yi(e.options.gutters,t),wi(e)},!0),n("firstLineNumber",1,wi,!0),n("lineNumberFormatter",function(e){return e},wi,!0),n("showCursorWhenSelecting",!1,xr,!0),n("resetSelectionOnContextMenu",!0),n("lineWiseCopyCut",!0),n("pasteLinesPerSelection",!0),n("selectionsMayTouch",!1),n("readOnly",!1,function(e,t){"nocursor"==t&&(Or(e),e.display.input.blur()),e.display.input.readOnlyChanged(t)}),n("screenReaderLabel",null,function(e,t){t=""===t?null:t,e.display.input.screenReaderLabelChanged(t)}),n("disableInput",!1,function(e,t){t||e.display.input.reset()},!0),n("dragDrop",!0,Oa),n("allowDropFileTypes",null),n("cursorBlinkRate",530),n("cursorScrollMargin",0),n("cursorHeight",1,xr,!0),n("singleCursorHeightPerLine",!0,xr,!0),n("workTime",100),n("workDelay",100),n("flattenSpans",!0,Wi,!0),n("addModeClass",!1,Wi,!0),n("pollInterval",100),n("undoDepth",200,function(e,t){return e.doc.history.undoDepth=t}),n("historyEventDelay",1250),n("viewportMargin",10,function(e){return e.refresh()},!0),n("maxHighlightLength",1e4,Wi,!0),n("moveInputWithCursor",!0,function(e,t){t||e.display.input.resetPosition()}),n("tabindex",null,function(e,t){return e.display.input.getField().tabIndex=t||""}),n("autofocus",null),n("direction","ltr",function(e,t){return e.doc.setDirection(t)},!0),n("phrases",null)}(Ea),function(e){var t=e.optionHandlers,n=e.helpers={};e.prototype={constructor:e,focus:function(){R(this).focus(),this.display.input.focus()},setOption:function(e,n){var r=this.options,i=r[e];r[e]==n&&"mode"!=e||(r[e]=n,t.hasOwnProperty(e)&&oi(this,t[e])(this,n,i),be(this,"optionChange",this,e))},getOption:function(e){return this.options[e]},getDoc:function(){return this.doc},addKeyMap:function(e,t){this.state.keyMaps[t?"push":"unshift"](ta(e))},removeKeyMap:function(e){for(var t=this.state.keyMaps,n=0;n<t.length;++n)if(t[n]==e||t[n].name==e)return t.splice(n,1),!0},addOverlay:ai(function(t,n){var r=t.token?t:e.getMode(this.options,t);if(r.startState)throw new Error("Overlays may not be stateful.");(function(e,t,n){for(var r=0,i=n(t);r<e.length&&n(e[r])<=i;)r++;e.splice(r,0,t)})(this.state.overlays,{mode:r,modeSpec:t,opaque:n&&n.opaque,priority:n&&n.priority||0},function(e){return e.priority}),this.state.modeGen++,gr(this)}),removeOverlay:ai(function(e){for(var t=this.state.overlays,n=0;n<t.length;++n){var r=t[n].modeSpec;if(r==e||"string"==typeof e&&r.name==e)return t.splice(n,1),this.state.modeGen++,void gr(this)}}),indentLine:ai(function(e,t,n){"string"!=typeof t&&"number"!=typeof t&&(t=null==t?this.options.smartIndent?"smart":"prev":t?"add":"subtract"),rt(this.doc,e)&&Da(this,e,t,n)}),indentSelection:ai(function(e){for(var t=this.doc.sel.ranges,n=-1,r=0;r<t.length;r++){var i=t[r];if(i.empty())i.head.line>n&&(Da(this,i.head.line,e,!0),n=i.head.line,r==this.doc.sel.primIndex&&Wr(this));else{var o=i.from(),a=i.to(),l=Math.max(n,o.line);n=Math.min(this.lastLine(),a.line-(a.ch?0:1))+1;for(var s=l;s<n;++s)Da(this,s,e);var c=this.doc.sel.ranges;0==o.ch&&t.length==c.length&&c[r].from().ch>0&&to(this.doc,r,new Ai(o,c[r].to()),G)}}}),getTokenAt:function(e,t){return Ct(this,e,t)},getLineTokens:function(e,t){return Ct(this,ot(e),t,!0)},getTokenTypeAt:function(e){e=ft(this.doc,e);var t,n=vt(this,Ze(this.doc,e.line)),r=0,i=(n.length-1)/2,o=e.ch;if(0==o)t=n[2];else for(;;){var a=r+i>>1;if((a?n[2*a-1]:0)>=o)i=a;else{if(!(n[2*a+1]<o)){t=n[2*a+2];break}r=a+1}}var l=t?t.indexOf("overlay "):-1;return l<0?t:0==l?null:t.slice(0,l-1)},getModeAt:function(t){var n=this.doc.mode;return n.innerMode?e.innerMode(n,this.getTokenAt(t).state).mode:n},getHelper:function(e,t){return this.getHelpers(e,t)[0]},getHelpers:function(e,t){var r=[];if(!n.hasOwnProperty(t))return r;var i=n[t],o=this.getModeAt(e);if("string"==typeof o[t])i[o[t]]&&r.push(i[o[t]]);else if(o[t])for(var a=0;a<o[t].length;a++){var l=i[o[t][a]];l&&r.push(l)}else o.helperType&&i[o.helperType]?r.push(i[o.helperType]):i[o.name]&&r.push(i[o.name]);for(var s=0;s<i._global.length;s++){var c=i._global[s];c.pred(o,this)&&-1==K(r,c.val)&&r.push(c.val)}return r},getStateAfter:function(e,t){var n=this.doc;return yt(this,(e=dt(n,null==e?n.first+n.size-1:e))+1,t).state},cursorCoords:function(e,t){var n=this.doc.sel.primary();return Jn(this,null==e?n.head:"object"==typeof e?ft(this.doc,e):e?n.from():n.to(),t||"page")},charCoords:function(e,t){return Zn(this,ft(this.doc,e),t||"page")},coordsChar:function(e,t){return tr(this,(e=Xn(this,e,t||"page")).left,e.top)},lineAtHeight:function(e,t){return e=Xn(this,{top:e,left:0},t||"page").top,nt(this.doc,e+this.display.viewOffset)},heightAtLine:function(e,t,n){var r,i=!1;if("number"==typeof e){var o=this.doc.first+this.doc.size-1;e<this.doc.first?e=this.doc.first:e>o&&(e=o,i=!0),r=Ze(this.doc,e)}else r=e;return Yn(this,r,{top:0,left:0},t||"page",n||i).top+(i?this.doc.height-$t(r):0)},defaultTextHeight:function(){return sr(this.display)},defaultCharWidth:function(){return cr(this.display)},getViewport:function(){return{from:this.display.viewFrom,to:this.display.viewTo}},addWidget:function(e,t,n,r,i){var o,a,l,s=this.display,c=(e=Jn(this,ft(this.doc,e))).bottom,u=e.left;if(t.style.position="absolute",t.setAttribute("cm-ignore-events","true"),this.display.input.setUneditable(t),s.sizer.appendChild(t),"over"==r)c=e.top;else if("above"==r||"near"==r){var d=Math.max(s.wrapper.clientHeight,this.doc.height),f=Math.max(s.sizer.clientWidth,s.lineSpace.clientWidth);("above"==r||e.bottom+t.offsetHeight>d)&&e.top>t.offsetHeight?c=e.top-t.offsetHeight:e.bottom+t.offsetHeight<=d&&(c=e.bottom),u+t.offsetWidth>f&&(u=f-t.offsetWidth)}t.style.top=c+"px",t.style.left=t.style.right="","right"==i?(u=s.sizer.clientWidth-t.offsetWidth,t.style.right="0px"):("left"==i?u=0:"middle"==i&&(u=(s.sizer.clientWidth-t.offsetWidth)/2),t.style.left=u+"px"),n&&(o=this,a={left:u,top:c,right:u+t.offsetWidth,bottom:c+t.offsetHeight},null!=(l=Dr(o,a)).scrollTop&&Rr(o,l.scrollTop),null!=l.scrollLeft&&Br(o,l.scrollLeft))},triggerOnKeyDown:ai(ma),triggerOnKeyPress:ai(va),triggerOnKeyUp:ga,triggerOnMouseDown:ai(xa),execCommand:function(e){if(aa.hasOwnProperty(e))return aa[e].call(null,this)},triggerElectric:ai(function(e){Ha(this,e)}),findPosH:function(e,t,n,r){var i=1;t<0&&(i=-1,t=-t);for(var o=ft(this.doc,e),a=0;a<t&&!(o=qa(this.doc,o,i,n,r)).hitSide;++a);return o},moveH:ai(function(e,t){var n=this;this.extendSelectionsBy(function(r){return n.display.shift||n.doc.extend||r.empty()?qa(n.doc,r.head,e,t,n.options.rtlMoveVisually):e<0?r.from():r.to()},Y)}),deleteH:ai(function(e,t){var n=this.doc.sel,r=this.doc;n.somethingSelected()?r.replaceSelection("",null,"+delete"):na(this,function(n){var i=qa(r,n.head,e,t,!1);return e<0?{from:i,to:n.head}:{from:n.head,to:i}})}),findPosV:function(e,t,n,r){var i=1,o=r;t<0&&(i=-1,t=-t);for(var a=ft(this.doc,e),l=0;l<t;++l){var s=Jn(this,a,"div");if(null==o?o=s.left:s.left=o,(a=Ua(this,s,i,n)).hitSide)break}return a},moveV:ai(function(e,t){var n=this,r=this.doc,i=[],o=!this.display.shift&&!r.extend&&r.sel.somethingSelected();if(r.extendSelectionsBy(function(a){if(o)return e<0?a.from():a.to();var l=Jn(n,a.head,"div");null!=a.goalColumn&&(l.left=a.goalColumn),i.push(l.left);var s=Ua(n,l,e,t);return"page"==t&&a==r.sel.primary()&&Pr(n,Zn(n,s,"div").top-l.top),s},Y),i.length)for(var a=0;a<r.sel.ranges.length;a++)r.sel.ranges[a].goalColumn=i[a]}),findWordAt:function(e){var t=Ze(this.doc,e.line).text,n=e.ch,r=e.ch;if(t){var i=this.getHelper(e,"wordChars");"before"!=e.sticky&&r!=t.length||!n?++r:--n;for(var o=t.charAt(n),a=oe(o,i)?function(e){return oe(e,i)}:/\s/.test(o)?function(e){return/\s/.test(e)}:function(e){return!/\s/.test(e)&&!oe(e)};n>0&&a(t.charAt(n-1));)--n;for(;r<t.length&&a(t.charAt(r));)++r}return new Ai(ot(e.line,n),ot(e.line,r))},toggleOverwrite:function(e){null!=e&&e==this.state.overwrite||((this.state.overwrite=!this.state.overwrite)?D(this.display.cursorDiv,"CodeMirror-overwrite"):M(this.display.cursorDiv,"CodeMirror-overwrite"),be(this,"overwriteToggle",this,this.state.overwrite))},hasFocus:function(){return this.display.input.getField()==z(I(this))},isReadOnly:function(){return!(!this.options.readOnly&&!this.doc.cantEdit)},scrollTo:ai(function(e,t){Fr(this,e,t)}),getScrollInfo:function(){var e=this.display.scroller;return{left:e.scrollLeft,top:e.scrollTop,height:e.scrollHeight-_n(this)-this.display.barHeight,width:e.scrollWidth-_n(this)-this.display.barWidth,clientHeight:zn(this),clientWidth:En(this)}},scrollIntoView:ai(function(e,t){null==e?(e={from:this.doc.sel.primary().head,to:null},null==t&&(t=this.options.cursorScrollMargin)):"number"==typeof e?e={from:ot(e,0),to:null}:null==e.from&&(e={from:e,to:null}),e.to||(e.to=e.from),e.margin=t||0,null!=e.from.line?function(e,t){Ir(e),e.curOp.scrollToPos=t}(this,e):Hr(this,e.from,e.to,e.margin)}),setSize:ai(function(e,t){var n=this,r=function(e){return"number"==typeof e||/^\d+$/.test(String(e))?e+"px":e};null!=e&&(this.display.wrapper.style.width=r(e)),null!=t&&(this.display.wrapper.style.height=r(t)),this.options.lineWrapping&&Un(this);var i=this.display.viewFrom;this.doc.iter(i,this.display.viewTo,function(e){if(e.widgets)for(var t=0;t<e.widgets.length;t++)if(e.widgets[t].noHScroll){vr(n,i,"widget");break}++i}),this.curOp.forceUpdate=!0,be(this,"refresh",this)}),operation:function(e){return ii(this,e)},startOperation:function(){return Zr(this)},endOperation:function(){return Jr(this)},refresh:ai(function(){var e=this.display.cachedTextHeight;gr(this),this.curOp.forceUpdate=!0,Kn(this),Fr(this,this.doc.scrollLeft,this.doc.scrollTop),pi(this.display),(null==e||Math.abs(e-sr(this.display))>.5||this.options.lineWrapping)&&hr(this),be(this,"refresh",this)}),swapDoc:ai(function(e){var t=this.doc;return t.cm=null,this.state.selectingText&&this.state.selectingText(),Ri(this,e),Kn(this),this.display.input.reset(),Fr(this,e.scrollLeft,e.scrollTop),this.curOp.forceScroll=!0,hn(this,"swapDoc",this,t),t}),phrase:function(e){var t=this.options.phrases;return t&&Object.prototype.hasOwnProperty.call(t,e)?t[e]:e},getInputField:function(){return this.display.input.getField()},getWrapperElement:function(){return this.display.wrapper},getScrollerElement:function(){return this.display.scroller},getGutterElement:function(){return this.display.gutters}},Ce(e),e.registerHelper=function(t,r,i){n.hasOwnProperty(t)||(n[t]=e[t]={_global:[]}),n[t][r]=i},e.registerGlobalHelper=function(t,r,i,o){e.registerHelper(t,r,o),n[t]._global.push({pred:i,val:o})}}(Ea);var Za="iter insert remove copy getEditor constructor".split(" ");for(var Ja in Wo.prototype)Wo.prototype.hasOwnProperty(Ja)&&K(Za,Ja)<0&&(Ea.prototype[Ja]=function(e){return function(){return e.apply(this.doc,arguments)}}(Wo.prototype[Ja]));return Ce(Wo),Ea.inputStyles={textarea:Xa,contenteditable:Ka},Ea.defineMode=function(e){Ea.defaults.mode||"null"==e||(Ea.defaults.mode=e),Be.apply(this,arguments)},Ea.defineMIME=function(e,t){je[e]=t},Ea.defineMode("null",function(){return{token:function(e){return e.skipToEnd()}}}),Ea.defineMIME("text/plain","null"),Ea.defineExtension=function(e,t){Ea.prototype[e]=t},Ea.defineDocExtension=function(e,t){Wo.prototype[e]=t},Ea.fromTextArea=function(e,t){if((t=t?B(t):{}).value=e.value,!t.tabindex&&e.tabIndex&&(t.tabindex=e.tabIndex),!t.placeholder&&e.placeholder&&(t.placeholder=e.placeholder),null==t.autofocus){var n=z(H(e));t.autofocus=n==e||null!=e.getAttribute("autofocus")&&n==document.body}function r(){e.value=l.getValue()}var i;if(e.form&&(ge(e.form,"submit",r),!t.leaveSubmitMethodAlone)){var o=e.form;i=o.submit;try{var a=o.submit=function(){r(),o.submit=i,o.submit(),o.submit=a}}catch(e){}}t.finishInit=function(n){n.save=r,n.getTextArea=function(){return e},n.toTextArea=function(){n.toTextArea=isNaN,r(),e.parentNode.removeChild(n.getWrapperElement()),e.style.display="",e.form&&(ye(e.form,"submit",r),t.leaveSubmitMethodAlone||"function"!=typeof e.form.submit||(e.form.submit=i))}},e.style.display="none";var l=Ea(function(t){return e.parentNode.insertBefore(t,e.nextSibling)},t);return l},function(e){e.off=ye,e.on=ge,e.wheelEventPixels=Ti,e.Doc=Wo,e.splitLines=We,e.countColumn=q,e.findColumn=X,e.isWordChar=ie,e.Pass=V,e.signal=be,e.Line=Zt,e.changeEnd=_i,e.scrollbarModel=$r,e.Pos=ot,e.cmpPos=at,e.modes=Re,e.mimeModes=je,e.resolveMode=qe,e.getMode=Ue,e.modeExtensions=Ke,e.extendMode=Ve,e.copyState=Ge,e.startState=Ye,e.innerMode=$e,e.commands=aa,e.keyMap=$o,e.keyName=ea,e.isModifierKey=Jo,e.lookupKey=Zo,e.normalizeKeyMap=Xo,e.StringStream=Xe,e.SharedTextMarker=Eo,e.TextMarker=Oo,e.LineWidget=Mo,e.e_preventDefault=Se,e.e_stopPropagation=Te,e.e_stop=Me,e.addClass=D,e.contains=E,e.rmClass=M,e.keyNames=Uo}(Ea),Ea.version="5.65.19",Ea}()},6154:(e,t,n)=>{const r=n(4862),i=n(2325),o=n(2232),a=/\[(\w+)\]/g,l=/\[\]$/,s=document.getElementById("required-fields");function c(){o.getAll().forEach(function(e){if(e.name.length<=0)return;let t=e.name;if("checkbox"===e.type&&(t+="[]"),e.inFormContent=i.containsField(t),"address"===e.mailchimpType){void 0===e.originalRequiredValue&&(e.originalRequiredValue=e.forceRequired);const t=e.name.replace(a,"");i.query('[name^="'+t+'"]').length>0?e.forceRequired=!0:e.forceRequired=e.originalRequiredValue}}),function(){const e=o.getAll().filter(e=>!0===e.forceRequired).map(e=>e.name.toUpperCase().replace(a,".$1")),t=i.query("[required]");[].forEach.call(t,function(t){let n=t.name;if(!n||n.length<0||"_"===n[0])return;n=n.replace(a,".$1"),n=n.replace(l,"");let r=n.indexOf(".");r=r>0?r:n.length,n=n.substr(0,r).toUpperCase()+n.substr(r),-1===e.indexOf(n)&&e.push(n)}),s.value=e.join(",")}(),r.redraw()}function u(e,t){let n;return()=>{n&&clearTimeout(n),n=window.setTimeout(e,t)}}i.on("change",u(c,500)),o.on("change",u(c,100))},6423:(e,t,n)=>{const r=n(2325),i=n(2232),o=n(7785),a={};function l(e,t){a[e]=t,c()}function s(e){delete a[e],c()}function c(){const e=Object.values(a).map(e=>'<div class="notice notice-warning inline"><p>'+e+"</p></div>").join();let t=document.querySelector(".mc4wp-notices");if(!t){t=document.createElement("div"),t.className="mc4wp-notices";const e=document.querySelector("h1, h2");e.parentNode.insertBefore(t,e.nextSibling)}t.innerHTML=e}const u=function(){r.getValue().toLowerCase().indexOf('name="groupings')>-1?l("deprecated_groupings","Your form contains deprecated <code>GROUPINGS</code> fields. <br /><br />Please remove these fields from your form and then re-add them through the available field buttons to make sure your data is getting through to Mailchimp correctly."):s("deprecated_groupings")},d=function(){const e=i.getAll().filter(e=>!0===e.forceRequired&&!r.containsField(e.name.toUpperCase()));let t="<strong>Heads up!</strong> Your form is missing fields that are required in Mailchimp. Either add these fields to your form or mark them as optional in Mailchimp.";t+='<br /><ul class="ul-square" style="margin-bottom: 0;"><li>'+e.map(function(e){return e.title}).join("</li><li>")+"</li></ul>",e.length>0?l("required_fields_missing",t):s("required_fields_missing")};u(),r.on("focus",u),r.on("blur",u),d(),r.on("blur",d),r.on("focus",d),document.body.addEventListener("change",function(){o.getSelectedLists().length>0?s("no_lists_selected"):l("no_lists_selected",'<strong>Heads up!</strong> You have not yet selected a Mailchimp audience to subscribe people to. Please select at least one audience from the <a href="javascript:void(0)" data-tab="settings" class="tab-link">settings tab</a>.')})},6685:(e,t,n)=>{const r=n(8915),i=n(4862),o=function(e){e.dom.checked&&e.dom.setAttribute("checked","true"),e.dom.value&&e.dom.setAttribute("value",e.dom.value),e.dom.selected&&e.dom.setAttribute("selected","true")},a={select:function(e){const t={name:e.name,required:e.required};let n=!1;const r=e.choices.map(function(e){return e.selected&&(n=!0),i("option",{value:e.value!==e.label?e.value:void 0,selected:e.selected,oncreate:o},e.label)}),a=e.placeholder;return a.length>0&&r.unshift(i("option",{disabled:!0,value:"",selected:!n,oncreate:o},a)),i("select",t,r)},"terms-checkbox":function(e){let t;return t=e.link.length>0?i("a",{href:e.link,target:"_blank"},e.label):e.label,i("label",[i("input",{name:e.name,type:"checkbox",value:e.value,required:e.required})," ",t])},checkbox:function(e){return e.choices.map(function(t){const n=e.name+("checkbox"===e.type?"[]":""),r=e.required&&"radio"===e.type;return i("label",[i("input",{name:n,type:e.type,value:t.value,checked:t.selected,required:r,oncreate:o})," ",i("span",t.label)])})}};a.radio=a.checkbox,a.default=function(e){const t={type:e.type};return e.name&&(t.name=e.name),e.min&&(t.min=e.min),e.max&&(t.max=e.max),e.value.length>0&&(t.value=e.value),e.placeholder.length>0&&(t.placeholder=e.placeholder),t.required=e.required,t.oncreate=o,i("input",t)},a.procaptcha=function(e){return i("input",{type:"hidden",name:"procaptcha"})},e.exports=function(e){const t=e.label.length>0&&e.showLabel?i("label",{},e.label):"",n="function"==typeof a[e.type]?a[e.type](e):a.default(e),o=e.wrap?i("p",[t,n]):[t,n],l=document.createElement("div");return i.render(l,o),r.prettyPrint(l.innerHTML)+"\n"}},6753:(e,t,n)=>{!function(e){"use strict";var t=e.Pos;function n(e,t){return e.line-t.line||e.ch-t.ch}var r="A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD",i=new RegExp("<(/?)(["+r+"]["+r+"-:.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*)","g");function o(e,t,n,r){this.line=t,this.ch=n,this.cm=e,this.text=e.getLine(t),this.min=r?Math.max(r.from,e.firstLine()):e.firstLine(),this.max=r?Math.min(r.to-1,e.lastLine()):e.lastLine()}function a(e,n){var r=e.cm.getTokenTypeAt(t(e.line,n));return r&&/\btag\b/.test(r)}function l(e){if(!(e.line>=e.max))return e.ch=0,e.text=e.cm.getLine(++e.line),!0}function s(e){if(!(e.line<=e.min))return e.text=e.cm.getLine(--e.line),e.ch=e.text.length,!0}function c(e){for(;;){var t=e.text.indexOf(">",e.ch);if(-1==t){if(l(e))continue;return}if(a(e,t+1)){var n=e.text.lastIndexOf("/",t),r=n>-1&&!/\S/.test(e.text.slice(n+1,t));return e.ch=t+1,r?"selfClose":"regular"}e.ch=t+1}}function u(e){for(;;){var t=e.ch?e.text.lastIndexOf("<",e.ch-1):-1;if(-1==t){if(s(e))continue;return}if(a(e,t+1)){i.lastIndex=t,e.ch=t;var n=i.exec(e.text);if(n&&n.index==t)return n}else e.ch=t}}function d(e){for(;;){i.lastIndex=e.ch;var t=i.exec(e.text);if(!t){if(l(e))continue;return}if(a(e,t.index+1))return e.ch=t.index+t[0].length,t;e.ch=t.index+1}}function f(e){for(;;){var t=e.ch?e.text.lastIndexOf(">",e.ch-1):-1;if(-1==t){if(s(e))continue;return}if(a(e,t+1)){var n=e.text.lastIndexOf("/",t),r=n>-1&&!/\S/.test(e.text.slice(n+1,t));return e.ch=t+1,r?"selfClose":"regular"}e.ch=t}}function h(e,n){for(var r=[];;){var i,o=d(e),a=e.line,l=e.ch-(o?o[0].length:0);if(!o||!(i=c(e)))return;if("selfClose"!=i)if(o[1]){for(var s=r.length-1;s>=0;--s)if(r[s]==o[2]){r.length=s;break}if(s<0&&(!n||n==o[2]))return{tag:o[2],from:t(a,l),to:t(e.line,e.ch)}}else r.push(o[2])}}function p(e,n){for(var r=[];;){var i=f(e);if(!i)return;if("selfClose"!=i){var o=e.line,a=e.ch,l=u(e);if(!l)return;if(l[1])r.push(l[2]);else{for(var s=r.length-1;s>=0;--s)if(r[s]==l[2]){r.length=s;break}if(s<0&&(!n||n==l[2]))return{tag:l[2],from:t(e.line,e.ch),to:t(o,a)}}}else u(e)}}e.registerHelper("fold","xml",function(e,r){for(var i=new o(e,r.line,0);;){var a=d(i);if(!a||i.line!=r.line)return;var l=c(i);if(!l)return;if(!a[1]&&"selfClose"!=l){var s=t(i.line,i.ch),u=h(i,a[2]);return u&&n(u.from,s)>0?{from:s,to:u.from}:null}}}),e.findMatchingTag=function(e,r,i){var a=new o(e,r.line,r.ch,i);if(-1!=a.text.indexOf(">")||-1!=a.text.indexOf("<")){var l=c(a),s=l&&t(a.line,a.ch),d=l&&u(a);if(l&&d&&!(n(a,r)>0)){var f={from:t(a.line,a.ch),to:s,tag:d[2]};return"selfClose"==l?{open:f,close:null,at:"open"}:d[1]?{open:p(a,d[2]),close:f,at:"close"}:{open:f,close:h(a=new o(e,s.line,s.ch,i),d[2]),at:"open"}}}},e.findEnclosingTag=function(e,t,n,r){for(var i=new o(e,t.line,t.ch,n);;){var a=p(i,r);if(!a)break;var l=h(new o(e,t.line,t.ch,n),a.tag);if(l)return{open:a,close:l}}},e.scanForClosingTag=function(e,t,n,r){return h(new o(e,t.line,t.ch,r?{from:0,to:r}:null),n)}}(n(5237))},6792:(e,t,n)=>{!function(e){"use strict";e.defineMode("javascript",function(t,n){var r,i,o=t.indentUnit,a=n.statementIndent,l=n.jsonld,s=n.json||l,c=!1!==n.trackScope,u=n.typescript,d=n.wordCharacters||/[\w$\xa1-\uffff]/,f=function(){function e(e){return{type:e,style:"keyword"}}var t=e("keyword a"),n=e("keyword b"),r=e("keyword c"),i=e("keyword d"),o=e("operator"),a={type:"atom",style:"atom"};return{if:e("if"),while:t,with:t,else:n,do:n,try:n,finally:n,return:i,break:i,continue:i,new:e("new"),delete:r,void:r,throw:r,debugger:e("debugger"),var:e("var"),const:e("var"),let:e("var"),function:e("function"),catch:e("catch"),for:e("for"),switch:e("switch"),case:e("case"),default:e("default"),in:o,typeof:o,instanceof:o,true:a,false:a,null:a,undefined:a,NaN:a,Infinity:a,this:e("this"),class:e("class"),super:e("atom"),yield:r,export:e("export"),import:e("import"),extends:r,await:r}}(),h=/[+\-*&%=<>!?|~^@]/,p=/^@(context|id|value|language|type|container|list|set|reverse|index|base|vocab|graph)"/;function m(e,t,n){return r=e,i=n,t}function g(e,t){var n,r=e.next();if('"'==r||"'"==r)return t.tokenize=(n=r,function(e,t){var r,i=!1;if(l&&"@"==e.peek()&&e.match(p))return t.tokenize=g,m("jsonld-keyword","meta");for(;null!=(r=e.next())&&(r!=n||i);)i=!i&&"\\"==r;return i||(t.tokenize=g),m("string","string")}),t.tokenize(e,t);if("."==r&&e.match(/^\d[\d_]*(?:[eE][+\-]?[\d_]+)?/))return m("number","number");if("."==r&&e.match(".."))return m("spread","meta");if(/[\[\]{}\(\),;\:\.]/.test(r))return m(r);if("="==r&&e.eat(">"))return m("=>","operator");if("0"==r&&e.match(/^(?:x[\dA-Fa-f_]+|o[0-7_]+|b[01_]+)n?/))return m("number","number");if(/\d/.test(r))return e.match(/^[\d_]*(?:n|(?:\.[\d_]*)?(?:[eE][+\-]?[\d_]+)?)?/),m("number","number");if("/"==r)return e.eat("*")?(t.tokenize=v,v(e,t)):e.eat("/")?(e.skipToEnd(),m("comment","comment")):Qe(e,t,1)?(function(e){for(var t,n=!1,r=!1;null!=(t=e.next());){if(!n){if("/"==t&&!r)return;"["==t?r=!0:r&&"]"==t&&(r=!1)}n=!n&&"\\"==t}}(e),e.match(/^\b(([gimyus])(?![gimyus]*\2))+\b/),m("regexp","string-2")):(e.eat("="),m("operator","operator",e.current()));if("`"==r)return t.tokenize=y,y(e,t);if("#"==r&&"!"==e.peek())return e.skipToEnd(),m("meta","meta");if("#"==r&&e.eatWhile(d))return m("variable","property");if("<"==r&&e.match("!--")||"-"==r&&e.match("->")&&!/\S/.test(e.string.slice(0,e.start)))return e.skipToEnd(),m("comment","comment");if(h.test(r))return">"==r&&t.lexical&&">"==t.lexical.type||(e.eat("=")?"!"!=r&&"="!=r||e.eat("="):/[<>*+\-|&?]/.test(r)&&(e.eat(r),">"==r&&e.eat(r))),"?"==r&&e.eat(".")?m("."):m("operator","operator",e.current());if(d.test(r)){e.eatWhile(d);var i=e.current();if("."!=t.lastType){if(f.propertyIsEnumerable(i)){var o=f[i];return m(o.type,o.style,i)}if("async"==i&&e.match(/^(\s|\/\*([^*]|\*(?!\/))*?\*\/)*[\[\(\w]/,!1))return m("async","keyword",i)}return m("variable","variable",i)}}function v(e,t){for(var n,r=!1;n=e.next();){if("/"==n&&r){t.tokenize=g;break}r="*"==n}return m("comment","comment")}function y(e,t){for(var n,r=!1;null!=(n=e.next());){if(!r&&("`"==n||"$"==n&&e.eat("{"))){t.tokenize=g;break}r=!r&&"\\"==n}return m("quasi","string-2",e.current())}function b(e,t){t.fatArrowAt&&(t.fatArrowAt=null);var n=e.string.indexOf("=>",e.start);if(!(n<0)){if(u){var r=/:\s*(?:\w+(?:<[^>]*>|\[\])?|\{[^}]*\})\s*$/.exec(e.string.slice(e.start,n));r&&(n=r.index)}for(var i=0,o=!1,a=n-1;a>=0;--a){var l=e.string.charAt(a),s="([{}])".indexOf(l);if(s>=0&&s<3){if(!i){++a;break}if(0==--i){"("==l&&(o=!0);break}}else if(s>=3&&s<6)++i;else if(d.test(l))o=!0;else if(/["'\/`]/.test(l))for(;;--a){if(0==a)return;if(e.string.charAt(a-1)==l&&"\\"!=e.string.charAt(a-2)){a--;break}}else if(o&&!i){++a;break}}o&&!i&&(t.fatArrowAt=a)}}var w={atom:!0,number:!0,variable:!0,string:!0,regexp:!0,this:!0,import:!0,"jsonld-keyword":!0};function x(e,t,n,r,i,o){this.indented=e,this.column=t,this.type=n,this.prev=i,this.info=o,null!=r&&(this.align=r)}function k(e,t){if(!c)return!1;for(var n=e.localVars;n;n=n.next)if(n.name==t)return!0;for(var r=e.context;r;r=r.prev)for(n=r.vars;n;n=n.next)if(n.name==t)return!0}function C(e,t,n,r,i){var o=e.cc;for(S.state=e,S.stream=i,S.marked=null,S.cc=o,S.style=t,e.lexical.hasOwnProperty("align")||(e.lexical.align=!0);;)if((o.length?o.pop():s?B:R)(n,r)){for(;o.length&&o[o.length-1].lex;)o.pop()();return S.marked?S.marked:"variable"==n&&k(e,r)?"variable-2":t}}var S={state:null,column:null,marked:null,cc:null};function T(){for(var e=arguments.length-1;e>=0;e--)S.cc.push(arguments[e])}function L(){return T.apply(null,arguments),!0}function M(e,t){for(var n=t;n;n=n.next)if(n.name==e)return!0;return!1}function A(e){var t=S.state;if(S.marked="def",c){if(t.context)if("var"==t.lexical.info&&t.context&&t.context.block){var r=N(e,t.context);if(null!=r)return void(t.context=r)}else if(!M(e,t.localVars))return void(t.localVars=new E(e,t.localVars));n.globalVars&&!M(e,t.globalVars)&&(t.globalVars=new E(e,t.globalVars))}}function N(e,t){if(t){if(t.block){var n=N(e,t.prev);return n?n==t.prev?t:new _(n,t.vars,!0):null}return M(e,t.vars)?t:new _(t.prev,new E(e,t.vars),!1)}return null}function O(e){return"public"==e||"private"==e||"protected"==e||"abstract"==e||"readonly"==e}function _(e,t,n){this.prev=e,this.vars=t,this.block=n}function E(e,t){this.name=e,this.next=t}var z=new E("this",new E("arguments",null));function D(){S.state.context=new _(S.state.context,S.state.localVars,!1),S.state.localVars=z}function P(){S.state.context=new _(S.state.context,S.state.localVars,!0),S.state.localVars=null}function W(){S.state.localVars=S.state.context.vars,S.state.context=S.state.context.prev}function F(e,t){var n=function(){var n=S.state,r=n.indented;if("stat"==n.lexical.type)r=n.lexical.indented;else for(var i=n.lexical;i&&")"==i.type&&i.align;i=i.prev)r=i.indented;n.lexical=new x(r,S.stream.column(),e,null,n.lexical,t)};return n.lex=!0,n}function I(){var e=S.state;e.lexical.prev&&(")"==e.lexical.type&&(e.indented=e.lexical.indented),e.lexical=e.lexical.prev)}function H(e){return function t(n){return n==e?L():";"==e||"}"==n||")"==n||"]"==n?T():L(t)}}function R(e,t){return"var"==e?L(F("vardef",t),Se,H(";"),I):"keyword a"==e?L(F("form"),U,R,I):"keyword b"==e?L(F("form"),R,I):"keyword d"==e?S.stream.match(/^\s*$/,!1)?L():L(F("stat"),V,H(";"),I):"debugger"==e?L(H(";")):"{"==e?L(F("}"),P,se,I,W):";"==e?L():"if"==e?("else"==S.state.lexical.info&&S.state.cc[S.state.cc.length-1]==I&&S.state.cc.pop()(),L(F("form"),U,R,I,Oe)):"function"==e?L(De):"for"==e?L(F("form"),P,_e,R,W,I):"class"==e||u&&"interface"==t?(S.marked="keyword",L(F("form","class"==e?e:t),He,I)):"variable"==e?u&&"declare"==t?(S.marked="keyword",L(R)):u&&("module"==t||"enum"==t||"type"==t)&&S.stream.match(/^\s*\w/,!1)?(S.marked="keyword","enum"==t?L(Ze):"type"==t?L(We,H("operator"),he,H(";")):L(F("form"),Te,H("{"),F("}"),se,I,I)):u&&"namespace"==t?(S.marked="keyword",L(F("form"),B,R,I)):u&&"abstract"==t?(S.marked="keyword",L(R)):L(F("stat"),te):"switch"==e?L(F("form"),U,H("{"),F("}","switch"),P,se,I,I,W):"case"==e?L(B,H(":")):"default"==e?L(H(":")):"catch"==e?L(F("form"),D,j,R,I,W):"export"==e?L(F("stat"),qe,I):"import"==e?L(F("stat"),Ke,I):"async"==e?L(R):"@"==t?L(B,R):T(F("stat"),B,H(";"),I)}function j(e){if("("==e)return L(Fe,H(")"))}function B(e,t){return K(e,t,!1)}function q(e,t){return K(e,t,!0)}function U(e){return"("!=e?T():L(F(")"),V,H(")"),I)}function K(e,t,n){if(S.state.fatArrowAt==S.stream.start){var r=n?J:Z;if("("==e)return L(D,F(")"),ae(Fe,")"),I,H("=>"),r,W);if("variable"==e)return T(D,Te,H("=>"),r,W)}var i=n?$:G;return w.hasOwnProperty(e)?L(i):"function"==e?L(De,i):"class"==e||u&&"interface"==t?(S.marked="keyword",L(F("form"),Ie,I)):"keyword c"==e||"async"==e?L(n?q:B):"("==e?L(F(")"),V,H(")"),I,i):"operator"==e||"spread"==e?L(n?q:B):"["==e?L(F("]"),Xe,I,i):"{"==e?le(re,"}",null,i):"quasi"==e?T(Y,i):"new"==e?L(function(e){return function(t){return"."==t?L(e?ee:Q):"variable"==t&&u?L(xe,e?$:G):T(e?q:B)}}(n)):L()}function V(e){return e.match(/[;\}\)\],]/)?T():T(B)}function G(e,t){return","==e?L(V):$(e,t,!1)}function $(e,t,n){var r=0==n?G:$,i=0==n?B:q;return"=>"==e?L(D,n?J:Z,W):"operator"==e?/\+\+|--/.test(t)||u&&"!"==t?L(r):u&&"<"==t&&S.stream.match(/^([^<>]|<[^<>]*>)*>\s*\(/,!1)?L(F(">"),ae(he,">"),I,r):"?"==t?L(B,H(":"),i):L(i):"quasi"==e?T(Y,r):";"!=e?"("==e?le(q,")","call",r):"."==e?L(ne,r):"["==e?L(F("]"),V,H("]"),I,r):u&&"as"==t?(S.marked="keyword",L(he,r)):"regexp"==e?(S.state.lastType=S.marked="operator",S.stream.backUp(S.stream.pos-S.stream.start-1),L(i)):void 0:void 0}function Y(e,t){return"quasi"!=e?T():"${"!=t.slice(t.length-2)?L(Y):L(V,X)}function X(e){if("}"==e)return S.marked="string-2",S.state.tokenize=y,L(Y)}function Z(e){return b(S.stream,S.state),T("{"==e?R:B)}function J(e){return b(S.stream,S.state),T("{"==e?R:q)}function Q(e,t){if("target"==t)return S.marked="keyword",L(G)}function ee(e,t){if("target"==t)return S.marked="keyword",L($)}function te(e){return":"==e?L(I,R):T(G,H(";"),I)}function ne(e){if("variable"==e)return S.marked="property",L()}function re(e,t){return"async"==e?(S.marked="property",L(re)):"variable"==e||"keyword"==S.style?(S.marked="property","get"==t||"set"==t?L(ie):(u&&S.state.fatArrowAt==S.stream.start&&(n=S.stream.match(/^\s*:\s*/,!1))&&(S.state.fatArrowAt=S.stream.pos+n[0].length),L(oe))):"number"==e||"string"==e?(S.marked=l?"property":S.style+" property",L(oe)):"jsonld-keyword"==e?L(oe):u&&O(t)?(S.marked="keyword",L(re)):"["==e?L(B,ce,H("]"),oe):"spread"==e?L(q,oe):"*"==t?(S.marked="keyword",L(re)):":"==e?T(oe):void 0;var n}function ie(e){return"variable"!=e?T(oe):(S.marked="property",L(De))}function oe(e){return":"==e?L(q):"("==e?T(De):void 0}function ae(e,t,n){function r(i,o){if(n?n.indexOf(i)>-1:","==i){var a=S.state.lexical;return"call"==a.info&&(a.pos=(a.pos||0)+1),L(function(n,r){return n==t||r==t?T():T(e)},r)}return i==t||o==t?L():n&&n.indexOf(";")>-1?T(e):L(H(t))}return function(n,i){return n==t||i==t?L():T(e,r)}}function le(e,t,n){for(var r=3;r<arguments.length;r++)S.cc.push(arguments[r]);return L(F(t,n),ae(e,t),I)}function se(e){return"}"==e?L():T(R,se)}function ce(e,t){if(u){if(":"==e)return L(he);if("?"==t)return L(ce)}}function ue(e,t){if(u&&(":"==e||"in"==t))return L(he)}function de(e){if(u&&":"==e)return S.stream.match(/^\s*\w+\s+is\b/,!1)?L(B,fe,he):L(he)}function fe(e,t){if("is"==t)return S.marked="keyword",L()}function he(e,t){return"keyof"==t||"typeof"==t||"infer"==t||"readonly"==t?(S.marked="keyword",L("typeof"==t?q:he)):"variable"==e||"void"==t?(S.marked="type",L(we)):"|"==t||"&"==t?L(he):"string"==e||"number"==e||"atom"==e?L(we):"["==e?L(F("]"),ae(he,"]",","),I,we):"{"==e?L(F("}"),me,I,we):"("==e?L(ae(be,")"),pe,we):"<"==e?L(ae(he,">"),he):"quasi"==e?T(ve,we):void 0}function pe(e){if("=>"==e)return L(he)}function me(e){return e.match(/[\}\)\]]/)?L():","==e||";"==e?L(me):T(ge,me)}function ge(e,t){return"variable"==e||"keyword"==S.style?(S.marked="property",L(ge)):"?"==t||"number"==e||"string"==e?L(ge):":"==e?L(he):"["==e?L(H("variable"),ue,H("]"),ge):"("==e?T(Pe,ge):e.match(/[;\}\)\],]/)?void 0:L()}function ve(e,t){return"quasi"!=e?T():"${"!=t.slice(t.length-2)?L(ve):L(he,ye)}function ye(e){if("}"==e)return S.marked="string-2",S.state.tokenize=y,L(ve)}function be(e,t){return"variable"==e&&S.stream.match(/^\s*[?:]/,!1)||"?"==t?L(be):":"==e?L(he):"spread"==e?L(be):T(he)}function we(e,t){return"<"==t?L(F(">"),ae(he,">"),I,we):"|"==t||"."==e||"&"==t?L(he):"["==e?L(he,H("]"),we):"extends"==t||"implements"==t?(S.marked="keyword",L(he)):"?"==t?L(he,H(":"),he):void 0}function xe(e,t){if("<"==t)return L(F(">"),ae(he,">"),I,we)}function ke(){return T(he,Ce)}function Ce(e,t){if("="==t)return L(he)}function Se(e,t){return"enum"==t?(S.marked="keyword",L(Ze)):T(Te,ce,Ae,Ne)}function Te(e,t){return u&&O(t)?(S.marked="keyword",L(Te)):"variable"==e?(A(t),L()):"spread"==e?L(Te):"["==e?le(Me,"]"):"{"==e?le(Le,"}"):void 0}function Le(e,t){return"variable"!=e||S.stream.match(/^\s*:/,!1)?("variable"==e&&(S.marked="property"),"spread"==e?L(Te):"}"==e?T():"["==e?L(B,H("]"),H(":"),Le):L(H(":"),Te,Ae)):(A(t),L(Ae))}function Me(){return T(Te,Ae)}function Ae(e,t){if("="==t)return L(q)}function Ne(e){if(","==e)return L(Se)}function Oe(e,t){if("keyword b"==e&&"else"==t)return L(F("form","else"),R,I)}function _e(e,t){return"await"==t?L(_e):"("==e?L(F(")"),Ee,I):void 0}function Ee(e){return"var"==e?L(Se,ze):"variable"==e?L(ze):T(ze)}function ze(e,t){return")"==e?L():";"==e?L(ze):"in"==t||"of"==t?(S.marked="keyword",L(B,ze)):T(B,ze)}function De(e,t){return"*"==t?(S.marked="keyword",L(De)):"variable"==e?(A(t),L(De)):"("==e?L(D,F(")"),ae(Fe,")"),I,de,R,W):u&&"<"==t?L(F(">"),ae(ke,">"),I,De):void 0}function Pe(e,t){return"*"==t?(S.marked="keyword",L(Pe)):"variable"==e?(A(t),L(Pe)):"("==e?L(D,F(")"),ae(Fe,")"),I,de,W):u&&"<"==t?L(F(">"),ae(ke,">"),I,Pe):void 0}function We(e,t){return"keyword"==e||"variable"==e?(S.marked="type",L(We)):"<"==t?L(F(">"),ae(ke,">"),I):void 0}function Fe(e,t){return"@"==t&&L(B,Fe),"spread"==e?L(Fe):u&&O(t)?(S.marked="keyword",L(Fe)):u&&"this"==e?L(ce,Ae):T(Te,ce,Ae)}function Ie(e,t){return"variable"==e?He(e,t):Re(e,t)}function He(e,t){if("variable"==e)return A(t),L(Re)}function Re(e,t){return"<"==t?L(F(">"),ae(ke,">"),I,Re):"extends"==t||"implements"==t||u&&","==e?("implements"==t&&(S.marked="keyword"),L(u?he:B,Re)):"{"==e?L(F("}"),je,I):void 0}function je(e,t){return"async"==e||"variable"==e&&("static"==t||"get"==t||"set"==t||u&&O(t))&&S.stream.match(/^\s+#?[\w$\xa1-\uffff]/,!1)?(S.marked="keyword",L(je)):"variable"==e||"keyword"==S.style?(S.marked="property",L(Be,je)):"number"==e||"string"==e?L(Be,je):"["==e?L(B,ce,H("]"),Be,je):"*"==t?(S.marked="keyword",L(je)):u&&"("==e?T(Pe,je):";"==e||","==e?L(je):"}"==e?L():"@"==t?L(B,je):void 0}function Be(e,t){if("!"==t)return L(Be);if("?"==t)return L(Be);if(":"==e)return L(he,Ae);if("="==t)return L(q);var n=S.state.lexical.prev;return T(n&&"interface"==n.info?Pe:De)}function qe(e,t){return"*"==t?(S.marked="keyword",L(Ye,H(";"))):"default"==t?(S.marked="keyword",L(B,H(";"))):"{"==e?L(ae(Ue,"}"),Ye,H(";")):T(R)}function Ue(e,t){return"as"==t?(S.marked="keyword",L(H("variable"))):"variable"==e?T(q,Ue):void 0}function Ke(e){return"string"==e?L():"("==e?T(B):"."==e?T(G):T(Ve,Ge,Ye)}function Ve(e,t){return"{"==e?le(Ve,"}"):("variable"==e&&A(t),"*"==t&&(S.marked="keyword"),L($e))}function Ge(e){if(","==e)return L(Ve,Ge)}function $e(e,t){if("as"==t)return S.marked="keyword",L(Ve)}function Ye(e,t){if("from"==t)return S.marked="keyword",L(B)}function Xe(e){return"]"==e?L():T(ae(q,"]"))}function Ze(){return T(F("form"),Te,H("{"),F("}"),ae(Je,"}"),I,I)}function Je(){return T(Te,Ae)}function Qe(e,t,n){return t.tokenize==g&&/^(?:operator|sof|keyword [bcd]|case|new|export|default|spread|[\[{}\(,;:]|=>)$/.test(t.lastType)||"quasi"==t.lastType&&/\{\s*$/.test(e.string.slice(0,e.pos-(n||0)))}return D.lex=P.lex=!0,W.lex=!0,I.lex=!0,{startState:function(e){var t={tokenize:g,lastType:"sof",cc:[],lexical:new x((e||0)-o,0,"block",!1),localVars:n.localVars,context:n.localVars&&new _(null,null,!1),indented:e||0};return n.globalVars&&"object"==typeof n.globalVars&&(t.globalVars=n.globalVars),t},token:function(e,t){if(e.sol()&&(t.lexical.hasOwnProperty("align")||(t.lexical.align=!1),t.indented=e.indentation(),b(e,t)),t.tokenize!=v&&e.eatSpace())return null;var n=t.tokenize(e,t);return"comment"==r?n:(t.lastType="operator"!=r||"++"!=i&&"--"!=i?r:"incdec",C(t,n,r,i,e))},indent:function(t,r){if(t.tokenize==v||t.tokenize==y)return e.Pass;if(t.tokenize!=g)return 0;var i,l=r&&r.charAt(0),s=t.lexical;if(!/^\s*else\b/.test(r))for(var c=t.cc.length-1;c>=0;--c){var u=t.cc[c];if(u==I)s=s.prev;else if(u!=Oe&&u!=W)break}for(;("stat"==s.type||"form"==s.type)&&("}"==l||(i=t.cc[t.cc.length-1])&&(i==G||i==$)&&!/^[,\.=+\-*:?[\(]/.test(r));)s=s.prev;a&&")"==s.type&&"stat"==s.prev.type&&(s=s.prev);var d=s.type,f=l==d;return"vardef"==d?s.indented+("operator"==t.lastType||","==t.lastType?s.info.length+1:0):"form"==d&&"{"==l?s.indented:"form"==d?s.indented+o:"stat"==d?s.indented+(function(e,t){return"operator"==e.lastType||","==e.lastType||h.test(t.charAt(0))||/[,.]/.test(t.charAt(0))}(t,r)?a||o:0):"switch"!=s.info||f||0==n.doubleIndentSwitch?s.align?s.column+(f?0:1):s.indented+(f?0:o):s.indented+(/^(?:case|default)\b/.test(r)?o:2*o)},electricInput:/^\s*(?:case .*?:|default:|\{|\})$/,blockCommentStart:s?null:"/*",blockCommentEnd:s?null:"*/",blockCommentContinue:s?null:" * ",lineComment:s?null:"//",fold:"brace",closeBrackets:"()[]{}''\"\"``",helperType:s?"json":"javascript",jsonldMode:l,jsonMode:s,expressionAllowed:Qe,skipExpression:function(t){C(t,"atom","atom","true",new e.StringStream("",2,null))}}}),e.registerHelper("wordChars","javascript",/[\w$]/),e.defineMIME("text/javascript","javascript"),e.defineMIME("text/ecmascript","javascript"),e.defineMIME("application/javascript","javascript"),e.defineMIME("application/x-javascript","javascript"),e.defineMIME("application/ecmascript","javascript"),e.defineMIME("application/json",{name:"javascript",json:!0}),e.defineMIME("application/x-json",{name:"javascript",json:!0}),e.defineMIME("application/manifest+json",{name:"javascript",json:!0}),e.defineMIME("application/ld+json",{name:"javascript",jsonld:!0}),e.defineMIME("text/typescript",{name:"javascript",typescript:!0}),e.defineMIME("application/typescript",{name:"javascript",typescript:!0})}(n(5237))},6843:(e,t,n)=>{"use strict";var r=n(5199);e.exports=n(3804)("undefined"!=typeof window?window:null,r)},7165:e=>{"use strict";function t(e,t,n,r,i,o){return{tag:e,key:t,attrs:n,children:r,text:i,dom:o,is:void 0,domSize:void 0,state:void 0,events:void 0,instance:void 0}}t.normalize=function(e){return Array.isArray(e)?t("[",void 0,void 0,t.normalizeChildren(e),void 0,void 0):null==e||"boolean"==typeof e?null:"object"==typeof e?e:t("#",void 0,void 0,String(e),void 0,void 0)},t.normalizeChildren=function(e){var n=[];if(e.length){for(var r=null!=e[0]&&null!=e[0].key,i=1;i<e.length;i++)if((null!=e[i]&&null!=e[i].key)!==r)throw new TypeError(!r||null==e[i]&&"boolean"!=typeof e[i]?"In fragments, vnodes must either all have keys or none have keys.":"In fragments, vnodes must either all have keys or none have keys. You may wish to consider using an explicit keyed empty fragment, m.fragment({key: ...}), instead of a hole.");for(i=0;i<e.length;i++)n[i]=t.normalize(e[i])}return n},e.exports=t},7224:(e,t,n)=>{"use strict";var r=n(7755);e.exports=function(e){var t=e.indexOf("?"),n=e.indexOf("#"),i=n<0?e.length:n,o=t<0?i:t,a=e.slice(0,o).replace(/\/{2,}/g,"/");return a?"/"!==a[0]&&(a="/"+a):a="/",{path:a,params:t<0?{}:r(e.slice(t+1,i))}}},7755:e=>{"use strict";function t(e){try{return decodeURIComponent(e)}catch(t){return e}}e.exports=function(e){if(""===e||null==e)return{};"?"===e.charAt(0)&&(e=e.slice(1));for(var n=e.split("&"),r={},i={},o=0;o<n.length;o++){var a=n[o].split("="),l=t(a[0]),s=2===a.length?t(a[1]):"";"true"===s?s=!0:"false"===s&&(s=!1);var c=l.split(/\]\[?|\[/),u=i;l.indexOf("[")>-1&&c.pop();for(var d=0;d<c.length;d++){var f=c[d],h=c[d+1],p=""==h||!isNaN(parseInt(h,10));if(""===f)null==r[l=c.slice(0,d).join()]&&(r[l]=Array.isArray(u)?u.length:0),f=r[l]++;else if("__proto__"===f)break;if(d===c.length-1)u[f]=s;else{var m=Object.getOwnPropertyDescriptor(u,f);null!=m&&(m=m.value),null==m&&(u[f]=m=p?[]:{}),u=m}}}return i}},7779:(e,t,n)=>{const r={},i=n(361),o=n(4862);function a(e){for(let t=0;t<e.length;t++)e[t]=o("div.mc4wp-margin-s",e[t]);return e}r.render=function(e){const t=e.type;return"function"==typeof r[t]?a(r[t](e)):["select","radio","checkbox"].indexOf(t)>-1?a(r.choice(e)):a(r.text(e))},r.text=function(e){return[i.label(e),i.placeholder(e),i.value(e),i.isRequired(e),i.useParagraphs(e)]},r.choice=function(e){const t=[i.label(e),i.choiceType(e),i.choices(e)];return"select"===e.type&&t.push(i.placeholder(e)),t.push(i.useParagraphs(e)),"select"!==e.type&&"radio"!==e.type||t.push(i.isRequired(e)),t},r.hidden=function(e){return e.placeholder="",e.label="",e.wrap=!1,[i.showType(e),i.value(e)]},r.submit=function(e){return e.label="",e.placeholder="",[i.value(e),i.useParagraphs(e)]},r["terms-checkbox"]=function(e){return[i.label(e),i.linkToTerms(e),i.isRequired(e),i.useParagraphs(e)]},r.number=function(e){return[r.text(e),i.numberMinMax(e)]},r.procaptcha=function(e){return[i.description(e)]},e.exports=r},7785:(e,t,n)=>{const r=document.getElementById("mc4wp-admin").querySelectorAll(".mc4wp-list-input"),i=window.mc4wp_vars.mailchimp.lists;let o=[];const a=new(n(9885));function l(){o=[];for(let e=0;e<r.length;e++){const t=r[e];("boolean"!=typeof t.checked||t.checked)&&"object"==typeof i[t.value]&&o.push(i[t.value])}return function(){const e=document.querySelectorAll(".lists--only-selected > *");for(let t=0;t<e.length;t++){const n=e[t].getAttribute("data-list-id"),r=o.filter(e=>e.id===n).length>0;e[t].style.display=r?"":"none"}}(),a.emit("selectedLists.change",[o]),o}const s=document.getElementById("mc4wp-lists");s&&s.addEventListener("change",l),l(),e.exports={getSelectedLists:function(){return o},on:a.on.bind(a)}},8147:(e,t,n)=>{"use strict";var r=n(7165),i=n(9788),o=i.delayedRemoval,a=i.domFor;e.exports=function(){var e,t,n={svg:"http://www.w3.org/2000/svg",math:"http://www.w3.org/1998/Math/MathML"};function i(e){return e.ownerDocument}function l(e){return e.attrs&&e.attrs.xmlns||n[e.tag]}function s(e,t){if(e.state!==t)throw new Error("'vnode.state' must not be modified.")}function c(e){var t=e.state;try{return this.apply(t,arguments)}finally{s(e,t)}}function u(e){try{return i(e).activeElement}catch(e){return null}}function d(e,t,n,r,i,o,a){for(var l=n;l<r;l++){var s=t[l];null!=s&&f(e,s,i,a,o)}}function f(e,t,n,o,a){var s=t.tag;if("string"==typeof s)switch(t.state={},null!=t.attrs&&F(t.attrs,t,n),s){case"#":!function(e,t,n){t.dom=i(e).createTextNode(t.children),k(e,t.dom,n)}(e,t,a);break;case"<":p(e,t,o,a);break;case"[":!function(e,t,n,r,o){var a=i(e).createDocumentFragment();if(null!=t.children){var l=t.children;d(a,l,0,l.length,n,null,r)}t.dom=a.firstChild,t.domSize=a.childNodes.length,k(e,a,o)}(e,t,n,o,a);break;default:!function(e,t,n,r,o){var a=t.tag,s=t.attrs,c=t.is,u=(r=l(t)||r)?c?i(e).createElementNS(r,a,{is:c}):i(e).createElementNS(r,a):c?i(e).createElement(a,{is:c}):i(e).createElement(a);if(t.dom=u,null!=s&&function(e,t,n){for(var r in t)O(e,r,null,t[r],n)}(t,s,r),k(e,u,o),!C(t)&&null!=t.children){var f=t.children;d(u,f,0,f.length,n,null,r),"select"===t.tag&&null!=s&&function(e,t){if("value"in t)if(null===t.value)-1!==e.dom.selectedIndex&&(e.dom.value=null);else{var n=""+t.value;e.dom.value===n&&-1!==e.dom.selectedIndex||(e.dom.value=n)}"selectedIndex"in t&&O(e,"selectedIndex",null,t.selectedIndex,void 0)}(t,s)}}(e,t,n,o,a)}else!function(e,t,n,i,o){(function(e,t){var n;if("function"==typeof e.tag.view){if(e.state=Object.create(e.tag),null!=(n=e.state.view).$$reentrantLock$$)return;n.$$reentrantLock$$=!0}else{if(e.state=void 0,null!=(n=e.tag).$$reentrantLock$$)return;n.$$reentrantLock$$=!0,e.state=null!=e.tag.prototype&&"function"==typeof e.tag.prototype.view?new e.tag(e):e.tag(e)}if(F(e.state,e,t),null!=e.attrs&&F(e.attrs,e,t),e.instance=r.normalize(c.call(e.state.view,e)),e.instance===e)throw Error("A view cannot return the vnode it received as argument");n.$$reentrantLock$$=null})(t,n),null!=t.instance?(f(e,t.instance,n,i,o),t.dom=t.instance.dom,t.domSize=null!=t.dom?t.instance.domSize:0):t.domSize=0}(e,t,n,o,a)}var h={caption:"table",thead:"table",tbody:"table",tfoot:"table",tr:"tbody",th:"tr",td:"tr",colgroup:"table",col:"colgroup"};function p(e,t,n,r){var o=t.children.match(/^\s*?<(\w+)/im)||[],a=i(e).createElement(h[o[1]]||"div");"http://www.w3.org/2000/svg"===n?(a.innerHTML='<svg xmlns="http://www.w3.org/2000/svg">'+t.children+"</svg>",a=a.firstChild):a.innerHTML=t.children,t.dom=a.firstChild,t.domSize=a.childNodes.length;for(var l,s=i(e).createDocumentFragment();l=a.firstChild;)s.appendChild(l);k(e,s,r)}function m(e,t,n,r,i,o){if(t!==n&&(null!=t||null!=n))if(null==t||0===t.length)d(e,n,0,n.length,r,i,o);else if(null==n||0===n.length)S(e,t,0,t.length);else{var a=null!=t[0]&&null!=t[0].key,l=null!=n[0]&&null!=n[0].key,s=0,c=0;if(!a)for(;c<t.length&&null==t[c];)c++;if(!l)for(;s<n.length&&null==n[s];)s++;if(a!==l)S(e,t,c,t.length),d(e,n,s,n.length,r,i,o);else if(l){for(var u,h,p,m,y,k=t.length-1,C=n.length-1;k>=c&&C>=s&&(p=t[k],m=n[C],p.key===m.key);)p!==m&&g(e,p,m,r,i,o),null!=m.dom&&(i=m.dom),k--,C--;for(;k>=c&&C>=s&&(u=t[c],h=n[s],u.key===h.key);)c++,s++,u!==h&&g(e,u,h,r,w(t,c,i),o);for(;k>=c&&C>=s&&s!==C&&u.key===m.key&&p.key===h.key;)x(e,p,y=w(t,c,i)),p!==h&&g(e,p,h,r,y,o),++s<=--C&&x(e,u,i),u!==m&&g(e,u,m,r,i,o),null!=m.dom&&(i=m.dom),c++,p=t[--k],m=n[C],u=t[c],h=n[s];for(;k>=c&&C>=s&&p.key===m.key;)p!==m&&g(e,p,m,r,i,o),null!=m.dom&&(i=m.dom),C--,p=t[--k],m=n[C];if(s>C)S(e,t,c,k+1);else if(c>k)d(e,n,s,C+1,r,i,o);else{var T,L,A=i,N=C-s+1,O=new Array(N),_=0,E=0,z=2147483647,D=0;for(E=0;E<N;E++)O[E]=-1;for(E=C;E>=s;E--){null==T&&(T=v(t,c,k+1));var P=T[(m=n[E]).key];null!=P&&(z=P<z?P:-1,O[E-s]=P,p=t[P],t[P]=null,p!==m&&g(e,p,m,r,i,o),null!=m.dom&&(i=m.dom),D++)}if(i=A,D!==k-c+1&&S(e,t,c,k+1),0===D)d(e,n,s,C+1,r,i,o);else if(-1===z)for(L=function(e){var t=[0],n=0,r=0,i=0,o=b.length=e.length;for(i=0;i<o;i++)b[i]=e[i];for(i=0;i<o;++i)if(-1!==e[i]){var a=t[t.length-1];if(e[a]<e[i])b[i]=a,t.push(i);else{for(n=0,r=t.length-1;n<r;){var l=(n>>>1)+(r>>>1)+(n&r&1);e[t[l]]<e[i]?n=l+1:r=l}e[i]<e[t[n]]&&(n>0&&(b[i]=t[n-1]),t[n]=i)}}for(r=t[(n=t.length)-1];n-- >0;)t[n]=r,r=b[r];return b.length=0,t}(O),_=L.length-1,E=C;E>=s;E--)h=n[E],-1===O[E-s]?f(e,h,r,o,i):L[_]===E-s?_--:x(e,h,i),null!=h.dom&&(i=n[E].dom);else for(E=C;E>=s;E--)h=n[E],-1===O[E-s]&&f(e,h,r,o,i),null!=h.dom&&(i=n[E].dom)}}else{var W=t.length<n.length?t.length:n.length;for(s=s<c?s:c;s<W;s++)(u=t[s])===(h=n[s])||null==u&&null==h||(null==u?f(e,h,r,o,w(t,s+1,i)):null==h?M(e,u):g(e,u,h,r,w(t,s+1,i),o));t.length>W&&S(e,t,s,t.length),n.length>W&&d(e,n,s,n.length,r,i,o)}}}function g(e,t,n,i,o,a){var s=t.tag;if(s===n.tag&&t.is===n.is){if(n.state=t.state,n.events=t.events,function(e,t){do{var n;if(null!=e.attrs&&"function"==typeof e.attrs.onbeforeupdate&&void 0!==(n=c.call(e.attrs.onbeforeupdate,e,t))&&!n)break;if("string"!=typeof e.tag&&"function"==typeof e.state.onbeforeupdate&&void 0!==(n=c.call(e.state.onbeforeupdate,e,t))&&!n)break;return!1}while(0);return e.dom=t.dom,e.domSize=t.domSize,e.instance=t.instance,e.attrs=t.attrs,e.children=t.children,e.text=t.text,!0}(n,t))return;if("string"==typeof s)switch(null!=n.attrs&&I(n.attrs,n,i),s){case"#":!function(e,t){e.children.toString()!==t.children.toString()&&(e.dom.nodeValue=t.children),t.dom=e.dom}(t,n);break;case"<":!function(e,t,n,r,i){t.children!==n.children?(A(e,t),p(e,n,r,i)):(n.dom=t.dom,n.domSize=t.domSize)}(e,t,n,a,o);break;case"[":!function(e,t,n,r,i,o){m(e,t.children,n.children,r,i,o);var a=0,l=n.children;if(n.dom=null,null!=l){for(var s=0;s<l.length;s++){var c=l[s];null!=c&&null!=c.dom&&(null==n.dom&&(n.dom=c.dom),a+=c.domSize||1)}1!==a&&(n.domSize=a)}}(e,t,n,i,o,a);break;default:!function(e,t,n,r){var i=t.dom=e.dom;r=l(t)||r,function(e,t,n,r){var i;if(null!=t)for(var o in t===n&&console.warn("Don't reuse attrs object, use new object for every redraw, this will throw in next major"),t)null==(i=t[o])||null!=n&&null!=n[o]||_(e,o,i,r);if(null!=n)for(var o in n)O(e,o,t&&t[o],n[o],r)}(t,e.attrs,t.attrs,r),C(t)||m(i,e.children,t.children,n,null,r)}(t,n,i,a)}else!function(e,t,n,i,o,a){if(n.instance=r.normalize(c.call(n.state.view,n)),n.instance===n)throw Error("A view cannot return the vnode it received as argument");I(n.state,n,i),null!=n.attrs&&I(n.attrs,n,i),null!=n.instance?(null==t.instance?f(e,n.instance,i,a,o):g(e,t.instance,n.instance,i,o,a),n.dom=n.instance.dom,n.domSize=n.instance.domSize):null!=t.instance?(M(e,t.instance),n.dom=void 0,n.domSize=0):(n.dom=t.dom,n.domSize=t.domSize)}(e,t,n,i,o,a)}else M(e,t),f(e,n,i,a,o)}function v(e,t,n){for(var r=Object.create(null);t<n;t++){var i=e[t];if(null!=i){var o=i.key;null!=o&&(r[o]=t)}}return r}var y,b=[];function w(e,t,n){for(;t<e.length;t++)if(null!=e[t]&&null!=e[t].dom)return e[t].dom;return n}function x(e,t,n){if(null!=t.dom){var r;if(null==t.domSize)r=t.dom;else for(var o of(r=i(e).createDocumentFragment(),a(t)))r.appendChild(o);k(e,r,n)}}function k(e,t,n){null!=n?e.insertBefore(t,n):e.appendChild(t)}function C(e){if(null==e.attrs||null==e.attrs.contenteditable&&null==e.attrs.contentEditable)return!1;var t=e.children;if(null!=t&&1===t.length&&"<"===t[0].tag){var n=t[0].children;e.dom.innerHTML!==n&&(e.dom.innerHTML=n)}else if(null!=t&&0!==t.length)throw new Error("Child node of a contenteditable must be trusted.");return!0}function S(e,t,n,r){for(var i=n;i<r;i++){var o=t[i];null!=o&&M(e,o)}}function T(e,n,r,i){var l=n.state,u=c.call(r.onbeforeremove,n);if(null!=u){var d=t;for(var f of a(n))o.set(f,d);i.v++,Promise.resolve(u).finally(function(){s(n,l),L(e,n,i)})}}function L(e,t,n){0===--n.v&&(N(t),A(e,t))}function M(e,t){var n={v:1};"string"!=typeof t.tag&&"function"==typeof t.state.onbeforeremove&&T(e,t,t.state,n),t.attrs&&"function"==typeof t.attrs.onbeforeremove&&T(e,t,t.attrs,n),L(e,t,n)}function A(e,t){if(null!=t.dom)if(null==t.domSize)e.removeChild(t.dom);else for(var n of a(t))e.removeChild(n)}function N(e){if("string"!=typeof e.tag&&"function"==typeof e.state.onremove&&c.call(e.state.onremove,e),e.attrs&&"function"==typeof e.attrs.onremove&&c.call(e.attrs.onremove,e),"string"!=typeof e.tag)null!=e.instance&&N(e.instance);else{null!=e.events&&(e.events._=null);var t=e.children;if(Array.isArray(t))for(var n=0;n<t.length;n++){var r=t[n];null!=r&&N(r)}}}function O(e,t,n,r,i){if("key"!==t&&null!=r&&!E(t)&&(n!==r||function(e,t){return"value"===t||"checked"===t||"selectedIndex"===t||"selected"===t&&(e.dom===u(e.dom)||"option"===e.tag&&e.dom.parentNode===u(e.dom))}(e,t)||"object"==typeof r)){if("o"===t[0]&&"n"===t[1])return W(e,t,r);if("xlink:"===t.slice(0,6))e.dom.setAttributeNS("http://www.w3.org/1999/xlink",t.slice(6),r);else if("style"===t)D(e.dom,n,r);else if(z(e,t,i)){if("value"===t){if(("input"===e.tag||"textarea"===e.tag)&&e.dom.value===""+r)return;if("select"===e.tag&&null!==n&&e.dom.value===""+r)return;if("option"===e.tag&&null!==n&&e.dom.value===""+r)return;if("input"===e.tag&&"file"===e.attrs.type&&""+r!="")return void console.error("`value` is read-only on file inputs!")}"input"===e.tag&&"type"===t?e.dom.setAttribute(t,r):e.dom[t]=r}else"boolean"==typeof r?r?e.dom.setAttribute(t,""):e.dom.removeAttribute(t):e.dom.setAttribute("className"===t?"class":t,r)}}function _(e,t,n,r){if("key"!==t&&null!=n&&!E(t))if("o"===t[0]&&"n"===t[1])W(e,t,void 0);else if("style"===t)D(e.dom,n,null);else if(!z(e,t,r)||"className"===t||"title"===t||"value"===t&&("option"===e.tag||"select"===e.tag&&-1===e.dom.selectedIndex&&e.dom===u(e.dom))||"input"===e.tag&&"type"===t){var i=t.indexOf(":");-1!==i&&(t=t.slice(i+1)),!1!==n&&e.dom.removeAttribute("className"===t?"class":t)}else e.dom[t]=null}function E(e){return"oninit"===e||"oncreate"===e||"onupdate"===e||"onremove"===e||"onbeforeremove"===e||"onbeforeupdate"===e}function z(e,t,n){return void 0===n&&(e.tag.indexOf("-")>-1||e.is||"href"!==t&&"list"!==t&&"form"!==t&&"width"!==t&&"height"!==t)&&t in e.dom}function D(e,t,n){if(t===n);else if(null==n)e.style="";else if("object"!=typeof n)e.style=n;else if(null==t||"object"!=typeof t)for(var r in e.style="",n)null!=(i=n[r])&&(r.includes("-")?e.style.setProperty(r,String(i)):e.style[r]=String(i));else{for(var r in t)null!=t[r]&&null==n[r]&&(r.includes("-")?e.style.removeProperty(r):e.style[r]="");for(var r in n){var i;null!=(i=n[r])&&(i=String(i))!==String(t[r])&&(r.includes("-")?e.style.setProperty(r,i):e.style[r]=i)}}}function P(){this._=e}function W(t,n,r){if(null!=t.events){if(t.events._=e,t.events[n]===r)return;null==r||"function"!=typeof r&&"object"!=typeof r?(null!=t.events[n]&&t.dom.removeEventListener(n.slice(2),t.events,!1),t.events[n]=void 0):(null==t.events[n]&&t.dom.addEventListener(n.slice(2),t.events,!1),t.events[n]=r)}else null==r||"function"!=typeof r&&"object"!=typeof r||(t.events=new P,t.dom.addEventListener(n.slice(2),t.events,!1),t.events[n]=r)}function F(e,t,n){"function"==typeof e.oninit&&c.call(e.oninit,t),"function"==typeof e.oncreate&&n.push(c.bind(e.oncreate,t))}function I(e,t,n){"function"==typeof e.onupdate&&n.push(c.bind(e.onupdate,t))}return P.prototype=Object.create(null),P.prototype.handleEvent=function(e){var t,n=this["on"+e.type];"function"==typeof n?t=n.call(e.currentTarget,e):"function"==typeof n.handleEvent&&n.handleEvent(e);var r=this;null!=r._&&(!1!==e.redraw&&(0,r._)(),null!=t&&"function"==typeof t.then&&Promise.resolve(t).then(function(){null!=r._&&!1!==e.redraw&&(0,r._)()})),!1===t&&(e.preventDefault(),e.stopPropagation())},function(n,i,o){if(!n)throw new TypeError("DOM element being rendered to does not exist.");if(null!=y&&n.contains(y))throw new TypeError("Node is currently being rendered to and thus is locked.");var a=e,l=y,s=[],c=u(n),d=n.namespaceURI;y=n,e="function"==typeof o?o:void 0,t={};try{null==n.vnodes&&(n.textContent=""),i=r.normalizeChildren(Array.isArray(i)?i:[i]),m(n,n.vnodes,i,s,null,"http://www.w3.org/1999/xhtml"===d?void 0:d),n.vnodes=i,null!=c&&u(n)!==c&&"function"==typeof c.focus&&c.focus();for(var f=0;f<s.length;f++)s[f]()}finally{e=a,y=l}}}},8333:(e,t,n)=>{"use strict";var r=n(795),i=new RegExp("^(?:key|oninit|oncreate|onbeforeupdate|onupdate|onbeforeremove|onremove)$");e.exports=function(e,t){var n={};if(null!=t)for(var o in e)r.call(e,o)&&!i.test(o)&&t.indexOf(o)<0&&(n[o]=e[o]);else for(var o in e)r.call(e,o)&&!i.test(o)&&(n[o]=e[o]);return n}},8555:(e,t,n)=>{"use strict";var r=n(4224);e.exports=function(e,t){if(/:([^\/\.-]+)(\.{3})?:/.test(e))throw new SyntaxError("Template parameter names must be separated by either a '/', '-', or '.'.");if(null==t)return e;var n=e.indexOf("?"),i=e.indexOf("#"),o=i<0?e.length:i,a=n<0?o:n,l=e.slice(0,a),s={};Object.assign(s,t);var c=l.replace(/:([^\/\.-]+)(\.{3})?/g,function(e,n,r){return delete s[n],null==t[n]?e:r?t[n]:encodeURIComponent(String(t[n]))}),u=c.indexOf("?"),d=c.indexOf("#"),f=d<0?c.length:d,h=u<0?f:u,p=c.slice(0,h);n>=0&&(p+=e.slice(n,o)),u>=0&&(p+=(n<0?"?":"&")+c.slice(u,f));var m=r(s);return m&&(p+=(n<0&&u<0?"?":"&")+m),i>=0&&(p+=e.slice(i)),d>=0&&(p+=(i<0?"":"&")+c.slice(d)),p}},8656:(e,t,n)=>{!function(e){"use strict";function t(e){for(var t={},n=0;n<e.length;++n)t[e[n].toLowerCase()]=!0;return t}e.defineMode("css",function(t,n){var r=n.inline;n.propertyKeywords||(n=e.resolveMode("text/css"));var i,o,a=t.indentUnit,l=n.tokenHooks,s=n.documentTypes||{},c=n.mediaTypes||{},u=n.mediaFeatures||{},d=n.mediaValueKeywords||{},f=n.propertyKeywords||{},h=n.nonStandardPropertyKeywords||{},p=n.fontProperties||{},m=n.counterDescriptors||{},g=n.colorKeywords||{},v=n.valueKeywords||{},y=n.allowNested,b=n.lineComment,w=!0===n.supportsAtComponent,x=!1!==t.highlightNonStandardPropertyKeywords;function k(e,t){return i=t,e}function C(e,t){var n=e.next();if(l[n]){var r=l[n](e,t);if(!1!==r)return r}return"@"==n?(e.eatWhile(/[\w\\\-]/),k("def",e.current())):"="==n||("~"==n||"|"==n)&&e.eat("=")?k(null,"compare"):'"'==n||"'"==n?(t.tokenize=S(n),t.tokenize(e,t)):"#"==n?(e.eatWhile(/[\w\\\-]/),k("atom","hash")):"!"==n?(e.match(/^\s*\w*/),k("keyword","important")):/\d/.test(n)||"."==n&&e.eat(/\d/)?(e.eatWhile(/[\w.%]/),k("number","unit")):"-"!==n?/[,+>*\/]/.test(n)?k(null,"select-op"):"."==n&&e.match(/^-?[_a-z][_a-z0-9-]*/i)?k("qualifier","qualifier"):/[:;{}\[\]\(\)]/.test(n)?k(null,n):e.match(/^[\w-.]+(?=\()/)?(/^(url(-prefix)?|domain|regexp)$/i.test(e.current())&&(t.tokenize=T),k("variable callee","variable")):/[\w\\\-]/.test(n)?(e.eatWhile(/[\w\\\-]/),k("property","word")):k(null,null):/[\d.]/.test(e.peek())?(e.eatWhile(/[\w.%]/),k("number","unit")):e.match(/^-[\w\\\-]*/)?(e.eatWhile(/[\w\\\-]/),e.match(/^\s*:/,!1)?k("variable-2","variable-definition"):k("variable-2","variable")):e.match(/^\w+-/)?k("meta","meta"):void 0}function S(e){return function(t,n){for(var r,i=!1;null!=(r=t.next());){if(r==e&&!i){")"==e&&t.backUp(1);break}i=!i&&"\\"==r}return(r==e||!i&&")"!=e)&&(n.tokenize=null),k("string","string")}}function T(e,t){return e.next(),e.match(/^\s*[\"\')]/,!1)?t.tokenize=null:t.tokenize=S(")"),k(null,"(")}function L(e,t,n){this.type=e,this.indent=t,this.prev=n}function M(e,t,n,r){return e.context=new L(n,t.indentation()+(!1===r?0:a),e.context),n}function A(e){return e.context.prev&&(e.context=e.context.prev),e.context.type}function N(e,t,n){return E[n.context.type](e,t,n)}function O(e,t,n,r){for(var i=r||1;i>0;i--)n.context=n.context.prev;return N(e,t,n)}function _(e){var t=e.current().toLowerCase();o=v.hasOwnProperty(t)?"atom":g.hasOwnProperty(t)?"keyword":"variable"}var E={top:function(e,t,n){if("{"==e)return M(n,t,"block");if("}"==e&&n.context.prev)return A(n);if(w&&/@component/i.test(e))return M(n,t,"atComponentBlock");if(/^@(-moz-)?document$/i.test(e))return M(n,t,"documentTypes");if(/^@(media|supports|(-moz-)?document|import)$/i.test(e))return M(n,t,"atBlock");if(/^@(font-face|counter-style)/i.test(e))return n.stateArg=e,"restricted_atBlock_before";if(/^@(-(moz|ms|o|webkit)-)?keyframes$/i.test(e))return"keyframes";if(e&&"@"==e.charAt(0))return M(n,t,"at");if("hash"==e)o="builtin";else if("word"==e)o="tag";else{if("variable-definition"==e)return"maybeprop";if("interpolation"==e)return M(n,t,"interpolation");if(":"==e)return"pseudo";if(y&&"("==e)return M(n,t,"parens")}return n.context.type},block:function(e,t,n){if("word"==e){var r=t.current().toLowerCase();return f.hasOwnProperty(r)?(o="property","maybeprop"):h.hasOwnProperty(r)?(o=x?"string-2":"property","maybeprop"):y?(o=t.match(/^\s*:(?:\s|$)/,!1)?"property":"tag","block"):(o+=" error","maybeprop")}return"meta"==e?"block":y||"hash"!=e&&"qualifier"!=e?E.top(e,t,n):(o="error","block")},maybeprop:function(e,t,n){return":"==e?M(n,t,"prop"):N(e,t,n)},prop:function(e,t,n){if(";"==e)return A(n);if("{"==e&&y)return M(n,t,"propBlock");if("}"==e||"{"==e)return O(e,t,n);if("("==e)return M(n,t,"parens");if("hash"!=e||/^#([0-9a-fA-F]{3,4}|[0-9a-fA-F]{6}|[0-9a-fA-F]{8})$/.test(t.current())){if("word"==e)_(t);else if("interpolation"==e)return M(n,t,"interpolation")}else o+=" error";return"prop"},propBlock:function(e,t,n){return"}"==e?A(n):"word"==e?(o="property","maybeprop"):n.context.type},parens:function(e,t,n){return"{"==e||"}"==e?O(e,t,n):")"==e?A(n):"("==e?M(n,t,"parens"):"interpolation"==e?M(n,t,"interpolation"):("word"==e&&_(t),"parens")},pseudo:function(e,t,n){return"meta"==e?"pseudo":"word"==e?(o="variable-3",n.context.type):N(e,t,n)},documentTypes:function(e,t,n){return"word"==e&&s.hasOwnProperty(t.current())?(o="tag",n.context.type):E.atBlock(e,t,n)},atBlock:function(e,t,n){if("("==e)return M(n,t,"atBlock_parens");if("}"==e||";"==e)return O(e,t,n);if("{"==e)return A(n)&&M(n,t,y?"block":"top");if("interpolation"==e)return M(n,t,"interpolation");if("word"==e){var r=t.current().toLowerCase();o="only"==r||"not"==r||"and"==r||"or"==r?"keyword":c.hasOwnProperty(r)?"attribute":u.hasOwnProperty(r)?"property":d.hasOwnProperty(r)?"keyword":f.hasOwnProperty(r)?"property":h.hasOwnProperty(r)?x?"string-2":"property":v.hasOwnProperty(r)?"atom":g.hasOwnProperty(r)?"keyword":"error"}return n.context.type},atComponentBlock:function(e,t,n){return"}"==e?O(e,t,n):"{"==e?A(n)&&M(n,t,y?"block":"top",!1):("word"==e&&(o="error"),n.context.type)},atBlock_parens:function(e,t,n){return")"==e?A(n):"{"==e||"}"==e?O(e,t,n,2):E.atBlock(e,t,n)},restricted_atBlock_before:function(e,t,n){return"{"==e?M(n,t,"restricted_atBlock"):"word"==e&&"@counter-style"==n.stateArg?(o="variable","restricted_atBlock_before"):N(e,t,n)},restricted_atBlock:function(e,t,n){return"}"==e?(n.stateArg=null,A(n)):"word"==e?(o="@font-face"==n.stateArg&&!p.hasOwnProperty(t.current().toLowerCase())||"@counter-style"==n.stateArg&&!m.hasOwnProperty(t.current().toLowerCase())?"error":"property","maybeprop"):"restricted_atBlock"},keyframes:function(e,t,n){return"word"==e?(o="variable","keyframes"):"{"==e?M(n,t,"top"):N(e,t,n)},at:function(e,t,n){return";"==e?A(n):"{"==e||"}"==e?O(e,t,n):("word"==e?o="tag":"hash"==e&&(o="builtin"),"at")},interpolation:function(e,t,n){return"}"==e?A(n):"{"==e||";"==e?O(e,t,n):("word"==e?o="variable":"variable"!=e&&"("!=e&&")"!=e&&(o="error"),"interpolation")}};return{startState:function(e){return{tokenize:null,state:r?"block":"top",stateArg:null,context:new L(r?"block":"top",e||0,null)}},token:function(e,t){if(!t.tokenize&&e.eatSpace())return null;var n=(t.tokenize||C)(e,t);return n&&"object"==typeof n&&(i=n[1],n=n[0]),o=n,"comment"!=i&&(t.state=E[t.state](i,e,t)),o},indent:function(e,t){var n=e.context,r=t&&t.charAt(0),i=n.indent;return"prop"!=n.type||"}"!=r&&")"!=r||(n=n.prev),n.prev&&("}"!=r||"block"!=n.type&&"top"!=n.type&&"interpolation"!=n.type&&"restricted_atBlock"!=n.type?(")"!=r||"parens"!=n.type&&"atBlock_parens"!=n.type)&&("{"!=r||"at"!=n.type&&"atBlock"!=n.type)||(i=Math.max(0,n.indent-a)):i=(n=n.prev).indent),i},electricChars:"}",blockCommentStart:"/*",blockCommentEnd:"*/",blockCommentContinue:" * ",lineComment:b,fold:"brace"}});var n=["domain","regexp","url","url-prefix"],r=t(n),i=["all","aural","braille","handheld","print","projection","screen","tty","tv","embossed"],o=t(i),a=["width","min-width","max-width","height","min-height","max-height","device-width","min-device-width","max-device-width","device-height","min-device-height","max-device-height","aspect-ratio","min-aspect-ratio","max-aspect-ratio","device-aspect-ratio","min-device-aspect-ratio","max-device-aspect-ratio","color","min-color","max-color","color-index","min-color-index","max-color-index","monochrome","min-monochrome","max-monochrome","resolution","min-resolution","max-resolution","scan","grid","orientation","device-pixel-ratio","min-device-pixel-ratio","max-device-pixel-ratio","pointer","any-pointer","hover","any-hover","prefers-color-scheme","dynamic-range","video-dynamic-range"],l=t(a),s=["landscape","portrait","none","coarse","fine","on-demand","hover","interlace","progressive","dark","light","standard","high"],c=t(s),u=["align-content","align-items","align-self","alignment-adjust","alignment-baseline","all","anchor-point","animation","animation-delay","animation-direction","animation-duration","animation-fill-mode","animation-iteration-count","animation-name","animation-play-state","animation-timing-function","appearance","azimuth","backdrop-filter","backface-visibility","background","background-attachment","background-blend-mode","background-clip","background-color","background-image","background-origin","background-position","background-position-x","background-position-y","background-repeat","background-size","baseline-shift","binding","bleed","block-size","bookmark-label","bookmark-level","bookmark-state","bookmark-target","border","border-bottom","border-bottom-color","border-bottom-left-radius","border-bottom-right-radius","border-bottom-style","border-bottom-width","border-collapse","border-color","border-image","border-image-outset","border-image-repeat","border-image-slice","border-image-source","border-image-width","border-left","border-left-color","border-left-style","border-left-width","border-radius","border-right","border-right-color","border-right-style","border-right-width","border-spacing","border-style","border-top","border-top-color","border-top-left-radius","border-top-right-radius","border-top-style","border-top-width","border-width","bottom","box-decoration-break","box-shadow","box-sizing","break-after","break-before","break-inside","caption-side","caret-color","clear","clip","color","color-profile","column-count","column-fill","column-gap","column-rule","column-rule-color","column-rule-style","column-rule-width","column-span","column-width","columns","contain","content","counter-increment","counter-reset","crop","cue","cue-after","cue-before","cursor","direction","display","dominant-baseline","drop-initial-after-adjust","drop-initial-after-align","drop-initial-before-adjust","drop-initial-before-align","drop-initial-size","drop-initial-value","elevation","empty-cells","fit","fit-content","fit-position","flex","flex-basis","flex-direction","flex-flow","flex-grow","flex-shrink","flex-wrap","float","float-offset","flow-from","flow-into","font","font-family","font-feature-settings","font-kerning","font-language-override","font-optical-sizing","font-size","font-size-adjust","font-stretch","font-style","font-synthesis","font-variant","font-variant-alternates","font-variant-caps","font-variant-east-asian","font-variant-ligatures","font-variant-numeric","font-variant-position","font-variation-settings","font-weight","gap","grid","grid-area","grid-auto-columns","grid-auto-flow","grid-auto-rows","grid-column","grid-column-end","grid-column-gap","grid-column-start","grid-gap","grid-row","grid-row-end","grid-row-gap","grid-row-start","grid-template","grid-template-areas","grid-template-columns","grid-template-rows","hanging-punctuation","height","hyphens","icon","image-orientation","image-rendering","image-resolution","inline-box-align","inset","inset-block","inset-block-end","inset-block-start","inset-inline","inset-inline-end","inset-inline-start","isolation","justify-content","justify-items","justify-self","left","letter-spacing","line-break","line-height","line-height-step","line-stacking","line-stacking-ruby","line-stacking-shift","line-stacking-strategy","list-style","list-style-image","list-style-position","list-style-type","margin","margin-bottom","margin-left","margin-right","margin-top","marks","marquee-direction","marquee-loop","marquee-play-count","marquee-speed","marquee-style","mask-clip","mask-composite","mask-image","mask-mode","mask-origin","mask-position","mask-repeat","mask-size","mask-type","max-block-size","max-height","max-inline-size","max-width","min-block-size","min-height","min-inline-size","min-width","mix-blend-mode","move-to","nav-down","nav-index","nav-left","nav-right","nav-up","object-fit","object-position","offset","offset-anchor","offset-distance","offset-path","offset-position","offset-rotate","opacity","order","orphans","outline","outline-color","outline-offset","outline-style","outline-width","overflow","overflow-style","overflow-wrap","overflow-x","overflow-y","padding","padding-bottom","padding-left","padding-right","padding-top","page","page-break-after","page-break-before","page-break-inside","page-policy","pause","pause-after","pause-before","perspective","perspective-origin","pitch","pitch-range","place-content","place-items","place-self","play-during","position","presentation-level","punctuation-trim","quotes","region-break-after","region-break-before","region-break-inside","region-fragment","rendering-intent","resize","rest","rest-after","rest-before","richness","right","rotate","rotation","rotation-point","row-gap","ruby-align","ruby-overhang","ruby-position","ruby-span","scale","scroll-behavior","scroll-margin","scroll-margin-block","scroll-margin-block-end","scroll-margin-block-start","scroll-margin-bottom","scroll-margin-inline","scroll-margin-inline-end","scroll-margin-inline-start","scroll-margin-left","scroll-margin-right","scroll-margin-top","scroll-padding","scroll-padding-block","scroll-padding-block-end","scroll-padding-block-start","scroll-padding-bottom","scroll-padding-inline","scroll-padding-inline-end","scroll-padding-inline-start","scroll-padding-left","scroll-padding-right","scroll-padding-top","scroll-snap-align","scroll-snap-type","shape-image-threshold","shape-inside","shape-margin","shape-outside","size","speak","speak-as","speak-header","speak-numeral","speak-punctuation","speech-rate","stress","string-set","tab-size","table-layout","target","target-name","target-new","target-position","text-align","text-align-last","text-combine-upright","text-decoration","text-decoration-color","text-decoration-line","text-decoration-skip","text-decoration-skip-ink","text-decoration-style","text-emphasis","text-emphasis-color","text-emphasis-position","text-emphasis-style","text-height","text-indent","text-justify","text-orientation","text-outline","text-overflow","text-rendering","text-shadow","text-size-adjust","text-space-collapse","text-transform","text-underline-position","text-wrap","top","touch-action","transform","transform-origin","transform-style","transition","transition-delay","transition-duration","transition-property","transition-timing-function","translate","unicode-bidi","user-select","vertical-align","visibility","voice-balance","voice-duration","voice-family","voice-pitch","voice-range","voice-rate","voice-stress","voice-volume","volume","white-space","widows","width","will-change","word-break","word-spacing","word-wrap","writing-mode","z-index","clip-path","clip-rule","mask","enable-background","filter","flood-color","flood-opacity","lighting-color","stop-color","stop-opacity","pointer-events","color-interpolation","color-interpolation-filters","color-rendering","fill","fill-opacity","fill-rule","image-rendering","marker","marker-end","marker-mid","marker-start","paint-order","shape-rendering","stroke","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke-width","text-rendering","baseline-shift","dominant-baseline","glyph-orientation-horizontal","glyph-orientation-vertical","text-anchor","writing-mode"],d=t(u),f=["accent-color","aspect-ratio","border-block","border-block-color","border-block-end","border-block-end-color","border-block-end-style","border-block-end-width","border-block-start","border-block-start-color","border-block-start-style","border-block-start-width","border-block-style","border-block-width","border-inline","border-inline-color","border-inline-end","border-inline-end-color","border-inline-end-style","border-inline-end-width","border-inline-start","border-inline-start-color","border-inline-start-style","border-inline-start-width","border-inline-style","border-inline-width","content-visibility","margin-block","margin-block-end","margin-block-start","margin-inline","margin-inline-end","margin-inline-start","overflow-anchor","overscroll-behavior","padding-block","padding-block-end","padding-block-start","padding-inline","padding-inline-end","padding-inline-start","scroll-snap-stop","scrollbar-3d-light-color","scrollbar-arrow-color","scrollbar-base-color","scrollbar-dark-shadow-color","scrollbar-face-color","scrollbar-highlight-color","scrollbar-shadow-color","scrollbar-track-color","searchfield-cancel-button","searchfield-decoration","searchfield-results-button","searchfield-results-decoration","shape-inside","zoom"],h=t(f),p=t(["font-display","font-family","src","unicode-range","font-variant","font-feature-settings","font-stretch","font-weight","font-style"]),m=t(["additive-symbols","fallback","negative","pad","prefix","range","speak-as","suffix","symbols","system"]),g=["aliceblue","antiquewhite","aqua","aquamarine","azure","beige","bisque","black","blanchedalmond","blue","blueviolet","brown","burlywood","cadetblue","chartreuse","chocolate","coral","cornflowerblue","cornsilk","crimson","cyan","darkblue","darkcyan","darkgoldenrod","darkgray","darkgreen","darkgrey","darkkhaki","darkmagenta","darkolivegreen","darkorange","darkorchid","darkred","darksalmon","darkseagreen","darkslateblue","darkslategray","darkslategrey","darkturquoise","darkviolet","deeppink","deepskyblue","dimgray","dimgrey","dodgerblue","firebrick","floralwhite","forestgreen","fuchsia","gainsboro","ghostwhite","gold","goldenrod","gray","grey","green","greenyellow","honeydew","hotpink","indianred","indigo","ivory","khaki","lavender","lavenderblush","lawngreen","lemonchiffon","lightblue","lightcoral","lightcyan","lightgoldenrodyellow","lightgray","lightgreen","lightgrey","lightpink","lightsalmon","lightseagreen","lightskyblue","lightslategray","lightslategrey","lightsteelblue","lightyellow","lime","limegreen","linen","magenta","maroon","mediumaquamarine","mediumblue","mediumorchid","mediumpurple","mediumseagreen","mediumslateblue","mediumspringgreen","mediumturquoise","mediumvioletred","midnightblue","mintcream","mistyrose","moccasin","navajowhite","navy","oldlace","olive","olivedrab","orange","orangered","orchid","palegoldenrod","palegreen","paleturquoise","palevioletred","papayawhip","peachpuff","peru","pink","plum","powderblue","purple","rebeccapurple","red","rosybrown","royalblue","saddlebrown","salmon","sandybrown","seagreen","seashell","sienna","silver","skyblue","slateblue","slategray","slategrey","snow","springgreen","steelblue","tan","teal","thistle","tomato","turquoise","violet","wheat","white","whitesmoke","yellow","yellowgreen"],v=t(g),y=["above","absolute","activeborder","additive","activecaption","afar","after-white-space","ahead","alias","all","all-scroll","alphabetic","alternate","always","amharic","amharic-abegede","antialiased","appworkspace","arabic-indic","armenian","asterisks","attr","auto","auto-flow","avoid","avoid-column","avoid-page","avoid-region","axis-pan","background","backwards","baseline","below","bidi-override","binary","bengali","blink","block","block-axis","blur","bold","bolder","border","border-box","both","bottom","break","break-all","break-word","brightness","bullets","button","buttonface","buttonhighlight","buttonshadow","buttontext","calc","cambodian","capitalize","caps-lock-indicator","caption","captiontext","caret","cell","center","checkbox","circle","cjk-decimal","cjk-earthly-branch","cjk-heavenly-stem","cjk-ideographic","clear","clip","close-quote","col-resize","collapse","color","color-burn","color-dodge","column","column-reverse","compact","condensed","conic-gradient","contain","content","contents","content-box","context-menu","continuous","contrast","copy","counter","counters","cover","crop","cross","crosshair","cubic-bezier","currentcolor","cursive","cyclic","darken","dashed","decimal","decimal-leading-zero","default","default-button","dense","destination-atop","destination-in","destination-out","destination-over","devanagari","difference","disc","discard","disclosure-closed","disclosure-open","document","dot-dash","dot-dot-dash","dotted","double","down","drop-shadow","e-resize","ease","ease-in","ease-in-out","ease-out","element","ellipse","ellipsis","embed","end","ethiopic","ethiopic-abegede","ethiopic-abegede-am-et","ethiopic-abegede-gez","ethiopic-abegede-ti-er","ethiopic-abegede-ti-et","ethiopic-halehame-aa-er","ethiopic-halehame-aa-et","ethiopic-halehame-am-et","ethiopic-halehame-gez","ethiopic-halehame-om-et","ethiopic-halehame-sid-et","ethiopic-halehame-so-et","ethiopic-halehame-ti-er","ethiopic-halehame-ti-et","ethiopic-halehame-tig","ethiopic-numeric","ew-resize","exclusion","expanded","extends","extra-condensed","extra-expanded","fantasy","fast","fill","fill-box","fixed","flat","flex","flex-end","flex-start","footnotes","forwards","from","geometricPrecision","georgian","grayscale","graytext","grid","groove","gujarati","gurmukhi","hand","hangul","hangul-consonant","hard-light","hebrew","help","hidden","hide","higher","highlight","highlighttext","hiragana","hiragana-iroha","horizontal","hsl","hsla","hue","hue-rotate","icon","ignore","inactiveborder","inactivecaption","inactivecaptiontext","infinite","infobackground","infotext","inherit","initial","inline","inline-axis","inline-block","inline-flex","inline-grid","inline-table","inset","inside","intrinsic","invert","italic","japanese-formal","japanese-informal","justify","kannada","katakana","katakana-iroha","keep-all","khmer","korean-hangul-formal","korean-hanja-formal","korean-hanja-informal","landscape","lao","large","larger","left","level","lighter","lighten","line-through","linear","linear-gradient","lines","list-item","listbox","listitem","local","logical","loud","lower","lower-alpha","lower-armenian","lower-greek","lower-hexadecimal","lower-latin","lower-norwegian","lower-roman","lowercase","ltr","luminosity","malayalam","manipulation","match","matrix","matrix3d","media-play-button","media-slider","media-sliderthumb","media-volume-slider","media-volume-sliderthumb","medium","menu","menulist","menulist-button","menutext","message-box","middle","min-intrinsic","mix","mongolian","monospace","move","multiple","multiple_mask_images","multiply","myanmar","n-resize","narrower","ne-resize","nesw-resize","no-close-quote","no-drop","no-open-quote","no-repeat","none","normal","not-allowed","nowrap","ns-resize","numbers","numeric","nw-resize","nwse-resize","oblique","octal","opacity","open-quote","optimizeLegibility","optimizeSpeed","oriya","oromo","outset","outside","outside-shape","overlay","overline","padding","padding-box","painted","page","paused","persian","perspective","pinch-zoom","plus-darker","plus-lighter","pointer","polygon","portrait","pre","pre-line","pre-wrap","preserve-3d","progress","push-button","radial-gradient","radio","read-only","read-write","read-write-plaintext-only","rectangle","region","relative","repeat","repeating-linear-gradient","repeating-radial-gradient","repeating-conic-gradient","repeat-x","repeat-y","reset","reverse","rgb","rgba","ridge","right","rotate","rotate3d","rotateX","rotateY","rotateZ","round","row","row-resize","row-reverse","rtl","run-in","running","s-resize","sans-serif","saturate","saturation","scale","scale3d","scaleX","scaleY","scaleZ","screen","scroll","scrollbar","scroll-position","se-resize","searchfield","searchfield-cancel-button","searchfield-decoration","searchfield-results-button","searchfield-results-decoration","self-start","self-end","semi-condensed","semi-expanded","separate","sepia","serif","show","sidama","simp-chinese-formal","simp-chinese-informal","single","skew","skewX","skewY","skip-white-space","slide","slider-horizontal","slider-vertical","sliderthumb-horizontal","sliderthumb-vertical","slow","small","small-caps","small-caption","smaller","soft-light","solid","somali","source-atop","source-in","source-out","source-over","space","space-around","space-between","space-evenly","spell-out","square","square-button","start","static","status-bar","stretch","stroke","stroke-box","sub","subpixel-antialiased","svg_masks","super","sw-resize","symbolic","symbols","system-ui","table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row","table-row-group","tamil","telugu","text","text-bottom","text-top","textarea","textfield","thai","thick","thin","threeddarkshadow","threedface","threedhighlight","threedlightshadow","threedshadow","tibetan","tigre","tigrinya-er","tigrinya-er-abegede","tigrinya-et","tigrinya-et-abegede","to","top","trad-chinese-formal","trad-chinese-informal","transform","translate","translate3d","translateX","translateY","translateZ","transparent","ultra-condensed","ultra-expanded","underline","unidirectional-pan","unset","up","upper-alpha","upper-armenian","upper-greek","upper-hexadecimal","upper-latin","upper-norwegian","upper-roman","uppercase","urdu","url","var","vertical","vertical-text","view-box","visible","visibleFill","visiblePainted","visibleStroke","visual","w-resize","wait","wave","wider","window","windowframe","windowtext","words","wrap","wrap-reverse","x-large","x-small","xor","xx-large","xx-small"],b=t(y),w=n.concat(i).concat(a).concat(s).concat(u).concat(f).concat(g).concat(y);function x(e,t){for(var n,r=!1;null!=(n=e.next());){if(r&&"/"==n){t.tokenize=null;break}r="*"==n}return["comment","comment"]}e.registerHelper("hintWords","css",w),e.defineMIME("text/css",{documentTypes:r,mediaTypes:o,mediaFeatures:l,mediaValueKeywords:c,propertyKeywords:d,nonStandardPropertyKeywords:h,fontProperties:p,counterDescriptors:m,colorKeywords:v,valueKeywords:b,tokenHooks:{"/":function(e,t){return!!e.eat("*")&&(t.tokenize=x,x(e,t))}},name:"css"}),e.defineMIME("text/x-scss",{mediaTypes:o,mediaFeatures:l,mediaValueKeywords:c,propertyKeywords:d,nonStandardPropertyKeywords:h,colorKeywords:v,valueKeywords:b,fontProperties:p,allowNested:!0,lineComment:"//",tokenHooks:{"/":function(e,t){return e.eat("/")?(e.skipToEnd(),["comment","comment"]):e.eat("*")?(t.tokenize=x,x(e,t)):["operator","operator"]},":":function(e){return!!e.match(/^\s*\{/,!1)&&[null,null]},$:function(e){return e.match(/^[\w-]+/),e.match(/^\s*:/,!1)?["variable-2","variable-definition"]:["variable-2","variable"]},"#":function(e){return!!e.eat("{")&&[null,"interpolation"]}},name:"css",helperType:"scss"}),e.defineMIME("text/x-less",{mediaTypes:o,mediaFeatures:l,mediaValueKeywords:c,propertyKeywords:d,nonStandardPropertyKeywords:h,colorKeywords:v,valueKeywords:b,fontProperties:p,allowNested:!0,lineComment:"//",tokenHooks:{"/":function(e,t){return e.eat("/")?(e.skipToEnd(),["comment","comment"]):e.eat("*")?(t.tokenize=x,x(e,t)):["operator","operator"]},"@":function(e){return e.eat("{")?[null,"interpolation"]:!e.match(/^(charset|document|font-face|import|(-(moz|ms|o|webkit)-)?keyframes|media|namespace|page|supports)\b/i,!1)&&(e.eatWhile(/[\w\\\-]/),e.match(/^\s*:/,!1)?["variable-2","variable-definition"]:["variable-2","variable"])},"&":function(){return["atom","atom"]}},name:"css",helperType:"less"}),e.defineMIME("text/x-gss",{documentTypes:r,mediaTypes:o,mediaFeatures:l,propertyKeywords:d,nonStandardPropertyKeywords:h,fontProperties:p,counterDescriptors:m,colorKeywords:v,valueKeywords:b,supportsAtComponent:!0,tokenHooks:{"/":function(e,t){return!!e.eat("*")&&(t.tokenize=x,x(e,t))}},name:"css",helperType:"gss"})}(n(5237))},8915:e=>{e.exports={prettyPrint:function(e,t){var n,r,i,o,a,l;for(r=(t=t||{}).indent_size||4,i=t.indent_char||" ",a=t.brace_style||"collapse",o=0==t.max_char?1/0:t.max_char||70,l=t.unformatted||["a","span","bdo","em","strong","dfn","code","samp","kbd","var","cite","abbr","acronym","q","sub","sup","tt","i","b","big","small","u","s","strike","font","ins","del","pre","address","dt","h1","h2","h3","h4","h5","h6"],n=new function(){return this.pos=0,this.token="",this.current_mode="CONTENT",this.tags={parent:"parent1",parentcount:1,parent1:""},this.tag_type="",this.token_text=this.last_token=this.last_text=this.token_type="",this.Utils={whitespace:"\n\r\t ".split(""),single_token:"br,input,link,meta,!doctype,basefont,base,area,hr,wbr,param,img,isindex,?xml,embed,?php,?,?=".split(","),extra_liners:"head,body,/html".split(","),in_array:function(e,t){for(var n=0;n<t.length;n++)if(e===t[n])return!0;return!1}},this.get_content=function(){for(var e="",t=[],n=!1;"<"!==this.input.charAt(this.pos);){if(this.pos>=this.input.length)return t.length?t.join(""):["","TK_EOF"];if(e=this.input.charAt(this.pos),this.pos++,this.line_char_count++,this.Utils.in_array(e,this.Utils.whitespace))t.length&&(n=!0),this.line_char_count--;else{if(n){if(this.line_char_count>=this.max_char){t.push("\n");for(var r=0;r<this.indent_level;r++)t.push(this.indent_string);this.line_char_count=0}else t.push(" "),this.line_char_count++;n=!1}t.push(e)}}return t.length?t.join(""):""},this.get_contents_to=function(e){if(this.pos==this.input.length)return["","TK_EOF"];var t="",n=new RegExp("</"+e+"\\s*>","igm");n.lastIndex=this.pos;var r=n.exec(this.input),i=r?r.index:this.input.length;return this.pos<i&&(t=this.input.substring(this.pos,i),this.pos=i),t},this.record_tag=function(e){this.tags[e+"count"]?(this.tags[e+"count"]++,this.tags[e+this.tags[e+"count"]]=this.indent_level):(this.tags[e+"count"]=1,this.tags[e+this.tags[e+"count"]]=this.indent_level),this.tags[e+this.tags[e+"count"]+"parent"]=this.tags.parent,this.tags.parent=e+this.tags[e+"count"]},this.retrieve_tag=function(e){if(this.tags[e+"count"]){for(var t=this.tags.parent;t&&e+this.tags[e+"count"]!==t;)t=this.tags[t+"parent"];t&&(this.indent_level=this.tags[e+this.tags[e+"count"]],this.tags.parent=this.tags[t+"parent"]),delete this.tags[e+this.tags[e+"count"]+"parent"],delete this.tags[e+this.tags[e+"count"]],1==this.tags[e+"count"]?delete this.tags[e+"count"]:this.tags[e+"count"]--}},this.get_tag=function(){var e,t,n="",r=[],i=!1;do{if(this.pos>=this.input.length)return r.length?r.join(""):["","TK_EOF"];n=this.input.charAt(this.pos),this.pos++,this.line_char_count++,this.Utils.in_array(n,this.Utils.whitespace)?(i=!0,this.line_char_count--):("'"!==n&&'"'!==n||r[1]&&"!"===r[1]||(n+=this.get_unformatted(n),i=!0),"="===n&&(i=!1),r.length&&"="!==r[r.length-1]&&">"!==n&&i&&(this.line_char_count>=this.max_char?(this.print_newline(!1,r),this.line_char_count=0):(r.push(" "),this.line_char_count++),i=!1),"<"===n&&(e=this.pos-1),r.push(n))}while(">"!==n);var o,a=r.join("");o=-1!=a.indexOf(" ")?a.indexOf(" "):a.indexOf(">");var s=a.substring(1,o).toLowerCase();if("/"===a.charAt(a.length-2)||this.Utils.in_array(s,this.Utils.single_token))this.tag_type="SINGLE";else if("script"===s)this.record_tag(s),this.tag_type="SCRIPT";else if("style"===s)this.record_tag(s),this.tag_type="STYLE";else if(this.Utils.in_array(s,l)){var c=this.get_unformatted("</"+s+">",a);r.push(c),e>0&&this.Utils.in_array(this.input.charAt(e-1),this.Utils.whitespace)&&r.splice(0,0,this.input.charAt(e-1)),t=this.pos-1,this.Utils.in_array(this.input.charAt(t+1),this.Utils.whitespace)&&r.push(this.input.charAt(t+1)),this.tag_type="SINGLE"}else"!"===s.charAt(0)?-1!=s.indexOf("[if")?(-1!=a.indexOf("!IE")&&(c=this.get_unformatted("--\x3e",a),r.push(c)),this.tag_type="START"):-1!=s.indexOf("[endif")?(this.tag_type="END",this.unindent()):-1!=s.indexOf("[cdata[")?(c=this.get_unformatted("]]>",a),r.push(c),this.tag_type="SINGLE"):(c=this.get_unformatted("--\x3e",a),r.push(c),this.tag_type="SINGLE"):("/"===s.charAt(0)?(this.retrieve_tag(s.substring(1)),this.tag_type="END"):(this.record_tag(s),this.tag_type="START"),this.Utils.in_array(s,this.Utils.extra_liners)&&this.print_newline(!0,this.output));return r.join("")},this.get_unformatted=function(e,t){if(t&&-1!=t.toLowerCase().indexOf(e))return"";var n="",r="",i=!0;do{if(this.pos>=this.input.length)return r;if(n=this.input.charAt(this.pos),this.pos++,this.Utils.in_array(n,this.Utils.whitespace)){if(!i){this.line_char_count--;continue}if("\n"===n||"\r"===n){r+="\n",this.line_char_count=0;continue}}r+=n,this.line_char_count++,i=!0}while(-1==r.toLowerCase().indexOf(e));return r},this.get_token=function(){var e;if("TK_TAG_SCRIPT"===this.last_token||"TK_TAG_STYLE"===this.last_token){var t=this.last_token.substr(7);return"string"!=typeof(e=this.get_contents_to(t))?e:[e,"TK_"+t]}return"CONTENT"===this.current_mode?"string"!=typeof(e=this.get_content())?e:[e,"TK_CONTENT"]:"TAG"===this.current_mode?"string"!=typeof(e=this.get_tag())?e:[e,"TK_TAG_"+this.tag_type]:void 0},this.get_full_indent=function(e){return(e=this.indent_level+e||0)<1?"":Array(e+1).join(this.indent_string)},this.printer=function(e,t,n,r,i){this.input=e||"",this.output=[],this.indent_character=t,this.indent_string="",this.indent_size=n,this.brace_style=i,this.indent_level=0,this.max_char=r,this.line_char_count=0;for(var o=0;o<this.indent_size;o++)this.indent_string+=this.indent_character;this.print_newline=function(e,t){if(this.line_char_count=0,t&&t.length){if(!e)for(;this.Utils.in_array(t[t.length-1],this.Utils.whitespace);)t.pop();t.push("\n");for(var n=0;n<this.indent_level;n++)t.push(this.indent_string)}},this.print_token=function(e){this.output.push(e)},this.indent=function(){this.indent_level++},this.unindent=function(){this.indent_level>0&&this.indent_level--}},this},n.printer(e,i,r,o,a);;){var s=n.get_token();if(n.token_text=s[0],n.token_type=s[1],"TK_EOF"===n.token_type)break;switch(n.token_type){case"TK_TAG_START":n.print_newline(!1,n.output),n.print_token(n.token_text),n.indent(),n.current_mode="CONTENT";break;case"TK_TAG_STYLE":case"TK_TAG_SCRIPT":n.print_newline(!1,n.output),n.print_token(n.token_text),n.current_mode="CONTENT";break;case"TK_TAG_END":if("TK_CONTENT"===n.last_token&&""===n.last_text){var c=n.token_text.match(/\w+/)[0],u=n.output[n.output.length-1].match(/<\s*(\w+)/);null!==u&&u[1]===c||n.print_newline(!0,n.output)}n.print_token(n.token_text),n.current_mode="CONTENT";break;case"TK_TAG_SINGLE":var d=n.token_text.match(/^\s*<([a-z]+)/i);d&&n.Utils.in_array(d[1],l)||n.print_newline(!1,n.output),n.print_token(n.token_text),n.current_mode="CONTENT";break;case"TK_CONTENT":""!==n.token_text&&n.print_token(n.token_text),n.current_mode="TAG";break;case"TK_STYLE":case"TK_SCRIPT":if(""!==n.token_text){n.output.push("\n");var f=n.token_text;if("TK_SCRIPT"==n.token_type)var h="function"==typeof js_beautify&&js_beautify;else"TK_STYLE"==n.token_type&&(h="function"==typeof css_beautify&&css_beautify);if("keep"==t.indent_scripts)var p=0;else p="separate"==t.indent_scripts?-n.indent_level:1;var m=n.get_full_indent(p);if(h)f=h(f.replace(/^\s*/,m),t);else{var g=f.match(/^\s*/)[0].match(/[^\n\r]*$/)[0].split(n.indent_string).length-1,v=n.get_full_indent(p-g);f=f.replace(/^\s*/,m).replace(/\r\n|\r|\n/g,"\n"+v).replace(/\s*$/,"")}f&&(n.print_token(f),n.print_newline(!0,n.output))}n.current_mode="TAG"}n.last_token=n.token_type,n.last_text=n.token_text}return n.output.join("")}}},8995:(e,t,n)=>{"use strict";var r=n(7165),i=n(5178);e.exports=function(){var e=i.apply(0,arguments);return e.tag="[",e.children=r.normalizeChildren(e.children),e}},9665:(e,t,n)=>{"use strict";var r=n(7165);e.exports=function(e){return null==e&&(e=""),r("<",void 0,void 0,e,void 0,void 0)}},9674:(e,t,n)=>{"use strict";var r=n(7165);e.exports=function(e,t,n){var i=[],o=!1,a=-1;function l(){for(a=0;a<i.length;a+=2)try{e(i[a],r(i[a+1]),s)}catch(e){n.error(e)}a=-1}function s(){o||(o=!0,t(function(){o=!1,l()}))}return s.sync=l,{mount:function(t,n){if(null!=n&&null==n.view&&"function"!=typeof n)throw new TypeError("m.mount expects a component, not a vnode.");var o=i.indexOf(t);o>=0&&(i.splice(o,2),o<=a&&(a-=2),e(t,[])),null!=n&&(i.push(t,n),e(t,r(n),s))},redraw:s}}},9788:e=>{"use strict";var t=new WeakMap;e.exports={delayedRemoval:t,domFor:function*(e){var n=e.dom,r=e.domSize,i=t.get(n);if(null!=n)do{var o=n.nextSibling;t.get(n)===i&&(yield n,r--),n=o}while(r)}}},9885:e=>{function t(){this.listeners={}}t.prototype.emit=function(e,t){this.listeners[e]=this.listeners[e]??[],this.listeners[e].forEach(e=>e.apply(null,t))},t.prototype.on=function(e,t){this.listeners[e]=this.listeners[e]??[],this.listeners[e].push(t)},e.exports=t}},t={};function n(r){var i=t[r];if(void 0!==i)return i.exports;var o=t[r]={exports:{}};return e[r].call(o.exports,o,o.exports,n),o.exports}const r=n(2325);n(6154),n(4550),n(5051),n(6423),window.mc4wp.forms=window.mc4wp.forms||{},window.mc4wp.forms.editor=r})();