# Copyright (C) 2024 ibericode
# This file is distributed under the GPL v3.
msgid ""
msgstr ""
"Project-Id-Version: MC4WP: Mailchimp for WordPress 4.9.12\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/mailchimp-for-wp\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2024-04-25T16:06:39+00:00\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"X-Generator: WP-CLI 2.10.0\n"
"X-Domain: mailchimp-for-wp\n"

#. Plugin Name of the plugin
#: mailchimp-for-wp.php
msgid "MC4WP: Mailchimp for WordPress"
msgstr ""

#. Plugin URI of the plugin
#: mailchimp-for-wp.php
msgid "https://www.mc4wp.com/#utm_source=wp-plugin&utm_medium=mailchimp-for-wp&utm_campaign=plugins-page"
msgstr ""

#. Description of the plugin
#: mailchimp-for-wp.php
msgid "Mailchimp for WordPress by ibericode. Adds various highly effective sign-up methods to your site."
msgstr ""

#. Author of the plugin
#: mailchimp-for-wp.php
msgid "ibericode"
msgstr ""

#. Author URI of the plugin
#: mailchimp-for-wp.php
msgid "https://www.ibericode.com/"
msgstr ""

#: config/default-form-content.php:3
#: includes/class-mailchimp.php:250
#: includes/forms/class-admin.php:82
msgid "Email address"
msgstr ""

#: config/default-form-content.php:4
msgid "Your email address"
msgstr ""

#: config/default-form-content.php:5
msgid "Sign up"
msgstr ""

#: config/default-form-messages.php:5
msgid "Thank you, your sign-up request was successful! Please check your email inbox to confirm."
msgstr ""

#: config/default-form-messages.php:9
msgid "Thank you, your records have been updated!"
msgstr ""

#: config/default-form-messages.php:13
msgid "You were successfully unsubscribed."
msgstr ""

#: config/default-form-messages.php:17
msgid "Given email address is not subscribed."
msgstr ""

#: config/default-form-messages.php:21
msgid "Oops. Something went wrong. Please try again later."
msgstr ""

#: config/default-form-messages.php:25
msgid "Please provide a valid email address."
msgstr ""

#: config/default-form-messages.php:29
msgid "Given email address is already subscribed, thank you!"
msgstr ""

#: config/default-form-messages.php:33
msgid "Please fill in the required fields."
msgstr ""

#: config/default-form-messages.php:37
msgid "Please select at least one list."
msgstr ""

#: includes/admin/class-admin-texts.php:66
#: includes/forms/views/edit-form.php:6
msgid "Settings"
msgstr ""

#: includes/admin/class-admin-texts.php:84
msgid "Documentation"
msgstr ""

#: includes/admin/class-admin.php:209
msgid "Success! The cached configuration for your Mailchimp lists has been renewed."
msgstr ""

#: includes/admin/class-admin.php:289
msgid "The given value does not look like a valid Mailchimp API key."
msgstr ""

#: includes/admin/class-admin.php:290
msgid "This is a premium feature. Please upgrade to Mailchimp for WordPress Premium to be able to use it."
msgstr ""

#: includes/admin/class-admin.php:291
#: includes/forms/views/parts/add-fields-help.php:58
#: includes/views/parts/lists-overview.php:10
msgid "Renew Mailchimp lists"
msgstr ""

#: includes/admin/class-admin.php:292
msgid "Fetching Mailchimp lists"
msgstr ""

#: includes/admin/class-admin.php:293
msgid "Done! Mailchimp lists renewed."
msgstr ""

#: includes/admin/class-admin.php:294
msgid "Failed to renew your lists. An error occured."
msgstr ""

#: includes/admin/class-admin.php:320
msgid "Mailchimp API Settings"
msgstr ""

#: includes/admin/class-admin.php:327
#: includes/views/other-settings.php:14
#: includes/views/other-settings.php:24
msgid "Other Settings"
msgstr ""

#: includes/admin/class-admin.php:328
msgid "Other"
msgstr ""

#: includes/admin/class-admin.php:406
msgid "Error connecting to Mailchimp:"
msgstr ""

#: includes/admin/class-admin.php:409
msgid "Looks like your server is blocked by Mailchimp's firewall. Please contact Mailchimp support and include the following reference number: %s"
msgstr ""

#: includes/admin/class-admin.php:412
msgid "Here's some info on solving common connectivity issues."
msgstr ""

#: includes/admin/class-admin.php:417
msgid "Mailchimp returned the following error:"
msgstr ""

#: includes/admin/class-admin.php:456
msgid "Log successfully emptied."
msgstr ""

#: includes/admin/class-admin.php:486
msgid "To get started with Mailchimp for WordPress, please <a href=\"%s\">enter your Mailchimp API key on the settings page of the plugin</a>."
msgstr ""

#: includes/admin/class-ads.php:37
#: includes/admin/class-ads.php:38
msgid "Add-ons"
msgstr ""

#: includes/admin/class-ads.php:54
msgid "Want to customize the style of your form? <a href=\"%s\">Try our Styles Builder</a> & edit the look of your forms with just a few clicks."
msgstr ""

#: includes/admin/class-ads.php:69
msgid "Be notified whenever someone subscribes? <a href=\"%s\">Mailchimp for WordPress Premium</a> allows you to set up email notifications for your forms."
msgstr ""

#: includes/admin/class-ads.php:71
msgid "Increased conversions? <a href=\"%s\">Mailchimp for WordPress Premium</a> submits forms without reloading the entire page, resulting in a much better experience for your visitors."
msgstr ""

#: includes/admin/class-ads.php:85
msgid "Upgrade to Premium"
msgstr ""

#: includes/admin/class-ads.php:97
msgid "Do you want translated forms for all of your languages? <a href=\"%s\">Try Mailchimp for WordPress Premium</a>, which does just that plus more."
msgstr ""

#: includes/admin/class-ads.php:102
msgid "Do you want to create more than one form? Our Premium add-on does just that! <a href=\"%s\">Have a look at all Premium benefits</a>."
msgstr ""

#: includes/admin/class-ads.php:107
msgid "Are you enjoying this plugin? The Premium add-on unlocks several powerful features. <a href=\"%s\">Find out about all benefits now</a>."
msgstr ""

#: includes/admin/class-ads.php:145
msgid "Do you want to track all WooCommerce orders in Mailchimp so you can send emails based on the purchase activity of your subscribers?"
msgstr ""

#: includes/admin/class-ads.php:148
msgid "<a href=\"%1$s\">Upgrade to Mailchimp for WordPress Premium</a> or <a href=\"%2$s\">read more about Mailchimp's E-Commerce features</a>."
msgstr ""

#: includes/admin/class-review-notice.php:69
msgid "You've been using Mailchimp for WordPress for some time now; we hope you love it!"
msgstr ""

#: includes/admin/class-review-notice.php:70
msgid "If you do, please <a href=\"%s\">leave us a 5★ rating on WordPress.org</a>. It would be of great help to us."
msgstr ""

#: includes/admin/class-review-notice.php:72
msgid "Dismiss this notice."
msgstr ""

#: includes/admin/migrations/3.0.0-form-1-post-type.php:35
msgid "Default sign-up form"
msgstr ""

#: includes/class-dynamic-content-tags.php:27
msgid "Data from a cookie."
msgstr ""

#: includes/class-dynamic-content-tags.php:33
msgid "The email address of the current visitor (if known)."
msgstr ""

#: includes/class-dynamic-content-tags.php:38
msgid "The URL of the page."
msgstr ""

#: includes/class-dynamic-content-tags.php:43
msgid "The path of the page."
msgstr ""

#: includes/class-dynamic-content-tags.php:48
msgid "The current date. Example: %s."
msgstr ""

#: includes/class-dynamic-content-tags.php:53
msgid "The current time. Example: %s."
msgstr ""

#: includes/class-dynamic-content-tags.php:58
msgid "The site's language. Example: %s."
msgstr ""

#: includes/class-dynamic-content-tags.php:63
msgid "The visitor's IP address. Example: %s."
msgstr ""

#: includes/class-dynamic-content-tags.php:68
msgid "The property of the currently logged-in user."
msgstr ""

#: includes/class-dynamic-content-tags.php:74
msgid "Property of the current page or post."
msgstr ""

#: includes/forms/class-admin.php:70
msgid "Add to form"
msgstr ""

#: includes/forms/class-admin.php:71
msgid "I have read and agree to the terms & conditions"
msgstr ""

#: includes/forms/class-admin.php:72
msgid "Agree to terms"
msgstr ""

#: includes/forms/class-admin.php:73
msgid "Link to your terms & conditions page"
msgstr ""

#: includes/forms/class-admin.php:74
msgid "City"
msgstr ""

#: includes/forms/class-admin.php:75
msgid "Checkboxes"
msgstr ""

#: includes/forms/class-admin.php:76
msgid "Choices"
msgstr ""

#: includes/forms/class-admin.php:77
msgid "Choice type"
msgstr ""

#: includes/forms/class-admin.php:78
msgid "Choose a field to add to the form"
msgstr ""

#: includes/forms/class-admin.php:79
msgid "Close"
msgstr ""

#: includes/forms/class-admin.php:80
msgid "Country"
msgstr ""

#: includes/forms/class-admin.php:81
msgid "Dropdown"
msgstr ""

#: includes/forms/class-admin.php:83
msgid "Field type"
msgstr ""

#: includes/forms/class-admin.php:84
msgid "Field label"
msgstr ""

#: includes/forms/class-admin.php:85
msgid "Form action"
msgstr ""

#: includes/forms/class-admin.php:86
msgid "This field will allow your visitors to choose whether they would like to subscribe or unsubscribe"
msgstr ""

#: includes/forms/class-admin.php:87
msgid "Form fields"
msgstr ""

#: includes/forms/class-admin.php:88
msgid "This field is marked as required in Mailchimp."
msgstr ""

#: includes/forms/class-admin.php:89
msgid "Initial value"
msgstr ""

#: includes/forms/class-admin.php:90
msgid "Interest categories"
msgstr ""

#: includes/forms/class-admin.php:91
msgid "Is this field required?"
msgstr ""

#: includes/forms/class-admin.php:92
msgid "List choice"
msgstr ""

#: includes/forms/class-admin.php:93
msgid "This field will allow your visitors to choose a list to subscribe to."
msgstr ""

#: includes/forms/class-admin.php:94
msgid "List fields"
msgstr ""

#: includes/forms/class-admin.php:95
msgid "Min"
msgstr ""

#: includes/forms/class-admin.php:96
msgid "Max"
msgstr ""

#: includes/forms/class-admin.php:97
msgid "No available fields. Did you select a Mailchimp list in the form settings?"
msgstr ""

#: includes/forms/class-admin.php:98
msgid "Optional"
msgstr ""

#: includes/forms/class-admin.php:99
msgid "Placeholder"
msgstr ""

#: includes/forms/class-admin.php:100
msgid "Text to show when field has no value."
msgstr ""

#: includes/forms/class-admin.php:101
msgid "Preselect"
msgstr ""

#: includes/forms/class-admin.php:102
msgid "Remove"
msgstr ""

#: includes/forms/class-admin.php:103
msgid "Radio buttons"
msgstr ""

#: includes/forms/class-admin.php:104
msgid "Street Address"
msgstr ""

#: includes/forms/class-admin.php:105
msgid "State"
msgstr ""

#: includes/forms/class-admin.php:106
msgid "Subscribe"
msgstr ""

#: includes/forms/class-admin.php:107
msgid "Submit button"
msgstr ""

#: includes/forms/class-admin.php:108
msgid "Wrap in paragraph tags?"
msgstr ""

#: includes/forms/class-admin.php:109
msgid "Value"
msgstr ""

#: includes/forms/class-admin.php:110
msgid "Text to prefill this field with."
msgstr ""

#: includes/forms/class-admin.php:111
msgid "ZIP"
msgstr ""

#: includes/forms/class-admin.php:123
#: includes/forms/views/edit-form.php:24
msgid "Forms"
msgstr ""

#: includes/forms/class-admin.php:124
#: includes/forms/views/edit-form.php:26
msgid "Form"
msgstr ""

#: includes/forms/class-admin.php:161
#: includes/forms/class-admin.php:288
msgid "Form saved."
msgstr ""

#: includes/forms/class-admin.php:397
msgid "Form not found."
msgstr ""

#: includes/forms/class-admin.php:399
msgid "Go back"
msgstr ""

#: includes/forms/class-admin.php:462
#: includes/forms/class-widget.php:31
msgid "Mailchimp Sign-Up Form"
msgstr ""

#: includes/forms/class-admin.php:466
msgid "Select the form to show"
msgstr ""

#: includes/forms/class-form-amp.php:33
msgid "Submitting..."
msgstr ""

#: includes/forms/class-form-element.php:82
msgid "Leave this field empty if you're human:"
msgstr ""

#: includes/forms/class-form-manager.php:144
msgid "Resource does not exist."
msgstr ""

#: includes/forms/class-form-tags.php:34
msgid "Replaced with the form response (error or success messages)."
msgstr ""

#: includes/forms/class-form-tags.php:39
msgid "Data from the URL or a submitted form."
msgstr ""

#: includes/forms/class-form-tags.php:45
#: includes/integrations/class-integration-tags.php:30
msgid "Replaced with the number of subscribers on the selected list(s)"
msgstr ""

#: includes/forms/class-form.php:26
msgid "There is no form with ID %d, perhaps it was deleted?"
msgstr ""

#: includes/forms/class-widget.php:27
msgid "Newsletter"
msgstr ""

#: includes/forms/class-widget.php:33
msgid "Displays your Mailchimp for WordPress sign-up form"
msgstr ""

#: includes/forms/class-widget.php:79
msgid "Title:"
msgstr ""

#: includes/forms/class-widget.php:96
msgid "You can edit your sign-up form in the <a href=\"%s\">Mailchimp for WordPress form settings</a>."
msgstr ""

#: includes/forms/views/add-form.php:10
#: includes/forms/views/add-form.php:69
msgid "Add new form"
msgstr ""

#: includes/forms/views/add-form.php:26
msgid "What is the name of this form?"
msgstr ""

#: includes/forms/views/add-form.php:29
msgid "Enter your form title.."
msgstr ""

#: includes/forms/views/add-form.php:36
msgid "To which Mailchimp lists should this form subscribe?"
msgstr ""

#: includes/forms/views/add-form.php:61
msgid "No lists found. Did you <a href=\"%s\">connect with Mailchimp</a>?"
msgstr ""

#: includes/forms/views/edit-form.php:4
msgid "Fields"
msgstr ""

#: includes/forms/views/edit-form.php:5
msgid "Messages"
msgstr ""

#: includes/forms/views/edit-form.php:7
msgid "Appearance"
msgstr ""

#: includes/forms/views/edit-form.php:22
#: includes/integrations/views/integration-settings.php:8
#: includes/integrations/views/integrations.php:99
#: includes/views/general-settings.php:7
#: includes/views/other-settings.php:12
msgid "You are here: "
msgstr ""

#: includes/forms/views/edit-form.php:34
msgid "Edit Form"
msgstr ""

#: includes/forms/views/edit-form.php:59
msgid "Enter form title here"
msgstr ""

#: includes/forms/views/edit-form.php:63
msgid "Enter the title of your sign-up form"
msgstr ""

#: includes/forms/views/edit-form.php:67
#: includes/forms/views/tabs/form-fields.php:39
msgid "Use the shortcode %s to display this form inside a post, page or text widget."
msgstr ""

#: includes/forms/views/parts/add-fields-help.php:4
#: includes/forms/views/tabs/form-fields.php:10
msgid "Add more fields"
msgstr ""

#: includes/forms/views/parts/add-fields-help.php:9
msgid "To add more fields to your form, you will need to create those fields in Mailchimp first."
msgstr ""

#: includes/forms/views/parts/add-fields-help.php:12
msgid "Here's how:"
msgstr ""

#: includes/forms/views/parts/add-fields-help.php:17
msgid "Log in to your Mailchimp account."
msgstr ""

#: includes/forms/views/parts/add-fields-help.php:22
msgid "Add list fields to any of your selected lists."
msgstr ""

#: includes/forms/views/parts/add-fields-help.php:23
msgid "Clicking the following links will take you to the right screen."
msgstr ""

#: includes/forms/views/parts/add-fields-help.php:31
msgid "Edit list fields for"
msgstr ""

#: includes/forms/views/parts/add-fields-help.php:42
msgid "Click the following button to have Mailchimp for WordPress pick up on your changes."
msgstr ""

#: includes/forms/views/parts/dynamic-content-tags.php:6
msgid "Add dynamic form variable"
msgstr ""

#: includes/forms/views/parts/dynamic-content-tags.php:8
msgid "The following list of variables can be used to <a href=\"%s\">add some dynamic content to your form or success and error messages</a>."
msgstr ""

#: includes/forms/views/parts/dynamic-content-tags.php:8
msgid "This allows you to personalise your form or response messages."
msgstr ""

#: includes/forms/views/tabs/form-appearance.php:5
msgid "Inherit from %s theme"
msgstr ""

#: includes/forms/views/tabs/form-appearance.php:6
msgid "Basic"
msgstr ""

#: includes/forms/views/tabs/form-appearance.php:7
msgid "Form Themes"
msgstr ""

#: includes/forms/views/tabs/form-appearance.php:8
msgid "Light Theme"
msgstr ""

#: includes/forms/views/tabs/form-appearance.php:9
msgid "Dark Theme"
msgstr ""

#: includes/forms/views/tabs/form-appearance.php:10
msgid "Red Theme"
msgstr ""

#: includes/forms/views/tabs/form-appearance.php:11
msgid "Green Theme"
msgstr ""

#: includes/forms/views/tabs/form-appearance.php:12
msgid "Blue Theme"
msgstr ""

#: includes/forms/views/tabs/form-appearance.php:25
msgid "Form Appearance"
msgstr ""

#: includes/forms/views/tabs/form-appearance.php:29
msgid "Form Style"
msgstr ""

#: includes/forms/views/tabs/form-appearance.php:50
msgid "If you want to load some default CSS styles, select \"basic formatting styles\" or choose one of the color themes"
msgstr ""

#: includes/forms/views/tabs/form-fields.php:6
msgid "Form variables"
msgstr ""

#: includes/forms/views/tabs/form-fields.php:13
msgid "Form Fields"
msgstr ""

#: includes/forms/views/tabs/form-fields.php:20
msgid "Form code"
msgstr ""

#: includes/forms/views/tabs/form-fields.php:22
msgid "Enter the HTML code for your form fields.."
msgstr ""

#: includes/forms/views/tabs/form-fields.php:26
msgid "Form preview"
msgstr ""

#: includes/forms/views/tabs/form-fields.php:27
msgid "The form may look slightly different than this when shown in a post, page or widget area."
msgstr ""

#: includes/forms/views/tabs/form-messages.php:6
msgid "Form Messages"
msgstr ""

#: includes/forms/views/tabs/form-messages.php:16
msgid "Successfully subscribed"
msgstr ""

#: includes/forms/views/tabs/form-messages.php:19
msgid "The text that shows when an email address is successfully subscribed to the selected list(s)."
msgstr ""

#: includes/forms/views/tabs/form-messages.php:23
msgid "Invalid email address"
msgstr ""

#: includes/forms/views/tabs/form-messages.php:26
msgid "The text that shows when an invalid email address is given."
msgstr ""

#: includes/forms/views/tabs/form-messages.php:30
msgid "Required field missing"
msgstr ""

#: includes/forms/views/tabs/form-messages.php:33
msgid "The text that shows when a required field for the selected list(s) is missing."
msgstr ""

#: includes/forms/views/tabs/form-messages.php:37
msgid "Already subscribed"
msgstr ""

#: includes/forms/views/tabs/form-messages.php:40
msgid "The text that shows when the given email is already subscribed to the selected list(s)."
msgstr ""

#: includes/forms/views/tabs/form-messages.php:44
msgid "General error"
msgstr ""

#: includes/forms/views/tabs/form-messages.php:47
msgid "The text that shows when a general error occured."
msgstr ""

#: includes/forms/views/tabs/form-messages.php:51
msgid "Unsubscribed"
msgstr ""

#: includes/forms/views/tabs/form-messages.php:54
msgid "When using the unsubscribe method, this is the text that shows when the given email address is successfully unsubscribed from the selected list(s)."
msgstr ""

#: includes/forms/views/tabs/form-messages.php:58
msgid "Not subscribed"
msgstr ""

#: includes/forms/views/tabs/form-messages.php:61
msgid "When using the unsubscribe method, this is the text that shows when the given email address is not on the selected list(s)."
msgstr ""

#: includes/forms/views/tabs/form-messages.php:65
msgid "No list selected"
msgstr ""

#: includes/forms/views/tabs/form-messages.php:68
msgid "When offering a list choice, this is the text that shows when no lists were selected."
msgstr ""

#: includes/forms/views/tabs/form-messages.php:79
msgid "Updated"
msgstr ""

#: includes/forms/views/tabs/form-messages.php:82
msgid "The text that shows when an existing subscriber is updated."
msgstr ""

#: includes/forms/views/tabs/form-messages.php:94
msgid "HTML tags like %s are allowed in the message fields."
msgstr ""

#: includes/forms/views/tabs/form-settings.php:1
msgid "Form Settings"
msgstr ""

#: includes/forms/views/tabs/form-settings.php:5
msgid "Mailchimp specific settings"
msgstr ""

#: includes/forms/views/tabs/form-settings.php:15
msgid "Lists this form subscribes to"
msgstr ""

#: includes/forms/views/tabs/form-settings.php:20
#: includes/integrations/views/integration-settings.php:140
msgid "No lists found, <a href=\"%s\">are you connected to Mailchimp</a>?"
msgstr ""

#: includes/forms/views/tabs/form-settings.php:39
msgid "Select the list(s) to which people who submit this form should be subscribed."
msgstr ""

#: includes/forms/views/tabs/form-settings.php:47
#: integrations/ninja-forms/class-action.php:29
msgid "Use double opt-in?"
msgstr ""

#: includes/forms/views/tabs/form-settings.php:51
#: includes/forms/views/tabs/form-settings.php:66
#: includes/forms/views/tabs/form-settings.php:87
#: includes/forms/views/tabs/form-settings.php:135
#: includes/integrations/views/integration-settings.php:66
#: includes/integrations/views/integration-settings.php:90
#: includes/integrations/views/integration-settings.php:177
#: includes/integrations/views/integration-settings.php:210
#: includes/integrations/views/integration-settings.php:227
#: includes/integrations/views/integration-settings.php:250
#: includes/integrations/views/integration-settings.php:275
#: integrations/contact-form-7/class-contact-form-7.php:74
#: integrations/gravity-forms/class-gravity-forms.php:112
#: integrations/gravity-forms/class-gravity-forms.php:124
#: integrations/wpforms/class-field.php:247
msgid "Yes"
msgstr ""

#: includes/forms/views/tabs/form-settings.php:54
msgid "Are you sure you want to disable double opt-in?"
msgstr ""

#: includes/forms/views/tabs/form-settings.php:55
#: includes/forms/views/tabs/form-settings.php:70
#: includes/forms/views/tabs/form-settings.php:91
#: includes/forms/views/tabs/form-settings.php:139
#: includes/integrations/views/integration-settings.php:67
#: includes/integrations/views/integration-settings.php:91
#: includes/integrations/views/integration-settings.php:178
#: includes/integrations/views/integration-settings.php:211
#: includes/integrations/views/integration-settings.php:231
#: includes/integrations/views/integration-settings.php:254
#: includes/integrations/views/integration-settings.php:279
#: integrations/contact-form-7/class-contact-form-7.php:74
#: integrations/gravity-forms/class-gravity-forms.php:113
#: integrations/gravity-forms/class-gravity-forms.php:125
#: integrations/wpforms/class-field.php:247
msgid "No"
msgstr ""

#: includes/forms/views/tabs/form-settings.php:57
msgid "We strongly suggest keeping double opt-in enabled. Disabling double opt-in may affect your GDPR compliance."
msgstr ""

#: includes/forms/views/tabs/form-settings.php:62
#: includes/integrations/views/integration-settings.php:246
#: integrations/ninja-forms/class-action.php:48
msgid "Update existing subscribers?"
msgstr ""

#: includes/forms/views/tabs/form-settings.php:72
#: includes/integrations/views/integration-settings.php:256
msgid "Select \"yes\" if you want to update existing subscribers with the data that is sent."
msgstr ""

#: includes/forms/views/tabs/form-settings.php:83
#: includes/integrations/views/integration-settings.php:271
msgid "Replace interest groups?"
msgstr ""

#: includes/forms/views/tabs/form-settings.php:94
#: includes/integrations/views/integration-settings.php:282
msgid "Select \"no\" if you want to add the selected interests to any previously selected interests when updating a subscriber."
msgstr ""

#: includes/forms/views/tabs/form-settings.php:95
#: includes/integrations/views/integration-settings.php:283
msgid "What does this do?"
msgstr ""

#: includes/forms/views/tabs/form-settings.php:101
msgid "Subscriber tags"
msgstr ""

#: includes/forms/views/tabs/form-settings.php:103
msgid "Example: My tag, another tag"
msgstr ""

#: includes/forms/views/tabs/form-settings.php:105
msgid "The listed tags will be applied to all subscribers added or updated by this form."
msgstr ""

#: includes/forms/views/tabs/form-settings.php:106
msgid "Separate multiple values with a comma."
msgstr ""

#: includes/forms/views/tabs/form-settings.php:121
msgid "Form behaviour"
msgstr ""

#: includes/forms/views/tabs/form-settings.php:131
msgid "Hide form after a successful sign-up?"
msgstr ""

#: includes/forms/views/tabs/form-settings.php:142
msgid "Select \"yes\" to hide the form fields after a successful sign-up."
msgstr ""

#: includes/forms/views/tabs/form-settings.php:147
msgid "Redirect to URL after successful sign-ups"
msgstr ""

#: includes/forms/views/tabs/form-settings.php:149
msgid "Example: %s"
msgstr ""

#: includes/forms/views/tabs/form-settings.php:151
msgid "Leave empty or enter <code>0</code> for no redirect. Otherwise, use complete (absolute) URLs, including <code>http://</code>."
msgstr ""

#: includes/forms/views/tabs/form-settings.php:154
msgid "Your \"subscribed\" message will not show when redirecting to another page, so make sure to let your visitors know they were successfully subscribed."
msgstr ""

#: includes/integrations/class-admin.php:72
#: includes/integrations/class-admin.php:73
#: includes/integrations/views/integration-settings.php:10
#: includes/integrations/views/integrations.php:101
#: includes/integrations/views/integrations.php:109
#: includes/integrations/views/integrations.php:123
msgid "Integrations"
msgstr ""

#: includes/integrations/class-integration.php:79
msgid "Sign me up for the newsletter!"
msgstr ""

#: includes/integrations/views/integration-settings.php:20
msgid "%s integration"
msgstr ""

#: includes/integrations/views/integration-settings.php:27
msgid "The selected Mailchimp lists require non-default fields, which may prevent this integration from working."
msgstr ""

#: includes/integrations/views/integration-settings.php:28
msgid "Please ensure you <a href=\"%1$s\">configure the plugin to send all required fields</a> or <a href=\"%2$s\">log into your Mailchimp account</a> and make sure only the email & name fields are marked as required fields for the selected list(s)."
msgstr ""

#: includes/integrations/views/integration-settings.php:64
msgid "Enabled?"
msgstr ""

#: includes/integrations/views/integration-settings.php:68
msgid "Enable the %s integration? This will add a sign-up checkbox to the form."
msgstr ""

#: includes/integrations/views/integration-settings.php:88
msgid "Implicit?"
msgstr ""

#: includes/integrations/views/integration-settings.php:91
#: includes/integrations/views/integration-settings.php:178
msgid "(recommended)"
msgstr ""

#: includes/integrations/views/integration-settings.php:95
msgid "Select \"yes\" if you want to subscribe people without asking them explicitly."
msgstr ""

#: includes/integrations/views/integration-settings.php:100
#: includes/integrations/views/integration-settings.php:185
#: integrations/gravity-forms/class-gravity-forms.php:131
msgid "<strong>Warning: </strong> enabling this may affect your <a href=\"%s\">GDPR compliance</a>."
msgstr ""

#: includes/integrations/views/integration-settings.php:122
msgid "Mailchimp Lists"
msgstr ""

#: includes/integrations/views/integration-settings.php:136
#: integrations/gravity-forms/class-gravity-forms.php:104
msgid "Select the list(s) to which people who check the checkbox should be subscribed."
msgstr ""

#: includes/integrations/views/integration-settings.php:156
msgid "Checkbox label text"
msgstr ""

#: includes/integrations/views/integration-settings.php:159
msgid "HTML tags like %s are allowed in the label text."
msgstr ""

#: includes/integrations/views/integration-settings.php:175
#: integrations/gravity-forms/class-gravity-forms.php:121
msgid "Pre-check the checkbox?"
msgstr ""

#: includes/integrations/views/integration-settings.php:181
#: integrations/gravity-forms/class-gravity-forms.php:129
msgid "Select \"yes\" if the checkbox should be pre-checked."
msgstr ""

#: includes/integrations/views/integration-settings.php:208
msgid "Load some default CSS?"
msgstr ""

#: includes/integrations/views/integration-settings.php:212
msgid "Select \"yes\" if the checkbox appears in a weird place."
msgstr ""

#: includes/integrations/views/integration-settings.php:223
#: integrations/gravity-forms/class-gravity-forms.php:109
msgid "Double opt-in?"
msgstr ""

#: includes/integrations/views/integration-settings.php:234
#: integrations/gravity-forms/class-gravity-forms.php:116
msgid "Select \"yes\" if you want people to confirm their email address before being subscribed (recommended)"
msgstr ""

#: includes/integrations/views/integrations.php:20
msgid "Configure this integration"
msgstr ""

#: includes/integrations/views/integrations.php:36
msgid "Active"
msgstr ""

#: includes/integrations/views/integrations.php:38
msgid "Inactive"
msgstr ""

#: includes/integrations/views/integrations.php:40
msgid "Not installed"
msgstr ""

#: includes/integrations/views/integrations.php:60
msgid "Name"
msgstr ""

#: includes/integrations/views/integrations.php:61
msgid "Description"
msgstr ""

#: includes/integrations/views/integrations.php:62
#: includes/views/general-settings.php:34
msgid "Status"
msgstr ""

#: includes/integrations/views/integrations.php:115
msgid "The table below shows all available integrations."
msgstr ""

#: includes/integrations/views/integrations.php:116
msgid "Click on the name of an integration to edit all settings specific to that integration."
msgstr ""

#: includes/integrations/views/integrations.php:126
msgid "Greyed out integrations will become available after installing & activating the corresponding plugin."
msgstr ""

#: includes/views/general-settings.php:18
msgid "API Settings"
msgstr ""

#: includes/views/general-settings.php:40
msgid "CONNECTED"
msgstr ""

#: includes/views/general-settings.php:44
msgid "NOT CONNECTED"
msgstr ""

#: includes/views/general-settings.php:53
msgid "API Key"
msgstr ""

#: includes/views/general-settings.php:55
msgid "Your Mailchimp API key"
msgstr ""

#: includes/views/general-settings.php:57
msgid "The API key for connecting with your Mailchimp account."
msgstr ""

#: includes/views/general-settings.php:58
msgid "Get your API key here."
msgstr ""

#: includes/views/general-settings.php:63
msgid "You defined your Mailchimp API key using the <code>MC4WP_API_KEY</code> constant."
msgstr ""

#: includes/views/other-settings.php:42
msgid "Miscellaneous settings"
msgstr ""

#: includes/views/other-settings.php:45
msgid "Logging"
msgstr ""

#: includes/views/other-settings.php:48
msgid "Errors & warnings only"
msgstr ""

#: includes/views/other-settings.php:49
msgid "Everything"
msgstr ""

#: includes/views/other-settings.php:52
msgid "Determines what events should be written to <a href=\"%s\">the debug log</a> (see below)."
msgstr ""

#: includes/views/other-settings.php:71
msgid "Debug Log"
msgstr ""

#: includes/views/other-settings.php:71
msgid "Filter.."
msgstr ""

#: includes/views/other-settings.php:76
msgid "Log file is not writable."
msgstr ""

#: includes/views/other-settings.php:77
msgid "Please ensure %1$s has the proper <a href=\"%2$s\">file permissions</a>."
msgstr ""

#: includes/views/other-settings.php:98
msgid "Nothing here. Which means there are no errors!"
msgstr ""

#: includes/views/other-settings.php:108
msgid "Empty Log"
msgstr ""

#: includes/views/other-settings.php:116
msgid "Right now, the plugin is configured to only log errors and warnings."
msgstr ""

#. translators: %s links to the WordPress.org translation project
#: includes/views/parts/admin-footer.php:13
msgid "Mailchimp for WordPress is in need of translations. Is the plugin not translated in your language or do you spot errors with the current translations? Helping out is easy! Please <a href=\"%s\">help translate the plugin using your WordPress.org account</a>."
msgstr ""

#: includes/views/parts/admin-footer.php:31
msgid "This plugin is not developed by or affiliated with Mailchimp in any way."
msgstr ""

#: includes/views/parts/admin-sidebar.php:11
msgid "Looking for help?"
msgstr ""

#: includes/views/parts/admin-sidebar.php:12
msgid "We have some resources available to help you in the right direction."
msgstr ""

#: includes/views/parts/admin-sidebar.php:14
msgid "Knowledge Base"
msgstr ""

#: includes/views/parts/admin-sidebar.php:15
msgid "Frequently Asked Questions"
msgstr ""

#: includes/views/parts/admin-sidebar.php:17
msgid "If your answer can not be found in the resources listed above, please use the <a href=\"%s\">support forums on WordPress.org</a>."
msgstr ""

#: includes/views/parts/admin-sidebar.php:18
msgid "Found a bug? Please <a href=\"%s\">open an issue on GitHub</a>."
msgstr ""

#: includes/views/parts/admin-sidebar.php:28
msgid "Other plugins by ibericode"
msgstr ""

#: includes/views/parts/admin-sidebar.php:34
msgid "Privacy-friendly analytics plugin that does not use any external services."
msgstr ""

#: includes/views/parts/admin-sidebar.php:40
msgid "Pop-ups or boxes that slide-in with a newsletter sign-up form. A sure-fire way to grow your email lists."
msgstr ""

#: includes/views/parts/admin-sidebar.php:46
msgid "Super flexible forms using native HTML. Just like Mailchimp for WordPress forms but for other purposes, like a contact form."
msgstr ""

#: includes/views/parts/lists-overview.php:1
msgid "Your Mailchimp Account"
msgstr ""

#: includes/views/parts/lists-overview.php:2
msgid "The table below shows your Mailchimp lists and their details. If you just applied changes to your Mailchimp lists, please use the following button to renew the cached lists configuration."
msgstr ""

#: includes/views/parts/lists-overview.php:19
msgid "No lists were found in your Mailchimp account"
msgstr ""

#: includes/views/parts/lists-overview.php:22
msgid "A total of %d lists were found in your Mailchimp account."
msgstr ""

#: includes/views/parts/lists-overview.php:27
msgid "List Name"
msgstr ""

#: includes/views/parts/lists-overview.php:28
msgid "ID"
msgstr ""

#: includes/views/parts/lists-overview.php:29
msgid "Subscribers"
msgstr ""

#: includes/views/parts/lists-overview.php:49
msgid "Edit this list in Mailchimp"
msgstr ""

#: includes/views/parts/lists-overview.php:50
msgid "Loading... Please wait."
msgstr ""

#: integrations/contact-form-7/admin-before.php:2
msgid "To integrate with Contact Form 7, configure the settings below and then add %s to your CF7 form mark-up."
msgstr ""

#: integrations/custom/admin-before.php:2
msgid "To get a custom integration to work, include the following HTML in the form you are trying to integrate with."
msgstr ""

#: integrations/custom/admin-before.php:9
msgid "Subscribe to our newsletter."
msgstr ""

#. translators: %s links to the Gravity Forms overview page
#: integrations/gravity-forms/admin-before.php:4
msgid "To integrate with Gravity Forms, add the \"Mailchimp for WordPress\" field to <a href=\"%s\">one of your Gravity Forms forms</a>."
msgstr ""

#: integrations/gravity-forms/class-field.php:38
msgid "Mailchimp for WordPress"
msgstr ""

#: integrations/gravity-forms/class-gravity-forms.php:93
#: integrations/wpforms/class-field.php:79
msgid "Mailchimp list"
msgstr ""

#: integrations/gravity-forms/class-gravity-forms.php:96
msgid "Select a Mailchimp list"
msgstr ""

#: integrations/ninja-forms-2/admin-before.php:2
msgid "To integrate with Ninja Forms, add the \"Mailchimp\" field to your Ninja Forms forms."
msgstr ""

#: integrations/ninja-forms/admin-before.php:2
msgid "To integrate with Ninja Forms, add the \"Mailchimp\" action to <a href=\"%s\">one of your Ninja Forms forms</a>."
msgstr ""

#: integrations/ninja-forms/class-action.php:21
msgid "Mailchimp"
msgstr ""

#: integrations/ninja-forms/class-field.php:35
msgid "Mailchimp opt-in"
msgstr ""

#: integrations/woocommerce/admin-after.php:4
msgid "After email field"
msgstr ""

#: integrations/woocommerce/admin-after.php:5
msgid "After billing details"
msgstr ""

#: integrations/woocommerce/admin-after.php:6
msgid "After shipping details"
msgstr ""

#: integrations/woocommerce/admin-after.php:7
msgid "After customer details"
msgstr ""

#: integrations/woocommerce/admin-after.php:8
msgid "Before submit button"
msgstr ""

#: integrations/woocommerce/admin-after.php:9
msgid "After order notes"
msgstr ""

#: integrations/woocommerce/admin-after.php:13
msgid "Checkout for WooCommerce: Before complete order button"
msgstr ""

#: integrations/woocommerce/admin-after.php:14
msgid "Checkout for WooCommerce: After account info"
msgstr ""

#: integrations/woocommerce/admin-after.php:15
msgid "Checkout for WooCommerce: After customer info"
msgstr ""

#: integrations/woocommerce/admin-after.php:36
msgid "Position"
msgstr ""

#: integrations/woocommerce/class-woocommerce.php:179
msgid "Order #%d"
msgstr ""

#: integrations/wpforms/admin-before.php:2
msgid "Use this integration by adding the \"Mailchimp\" field to <a href=\"%s\">your WPForms forms</a>."
msgstr ""

#: integrations/wpforms/class-field.php:18
msgid "Sign-up to our newsletter?"
msgstr ""

#: integrations/wpforms/class-field.php:73
msgid "Select the Mailchimp list to subscribe to."
msgstr ""

#: integrations/wpforms/class-field.php:104
msgid "Set your sign-up label text and whether it should be pre-checked."
msgstr ""

#: integrations/wpforms/class-field.php:115
msgid "Sign-up checkbox"
msgstr ""
