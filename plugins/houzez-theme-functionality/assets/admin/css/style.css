#toplevel_page_houzez_dashboard .wp-menu-image img,
#toplevel_page_houzez-real-estate .wp-menu-image img {
    width: 20px;
    padding: 5px 0 0;
    opacity: 1;
}

p.houzez-info-control {
    font-size: 12px;
    font-style: italic;
    background-color: #f7f6d4;
    padding: 10px;
    border-left: 3px solid #e0c948;
}

ul.houzez-sortable-list li.ui-sortable-handle {
    background-color: #004274;
    margin-top: 5px;
    padding: 10px 6px;
    color: #fff;
    vertical-align: middle;
    cursor: pointer;
}

.houzez_actions .houzez-button-icon,
.review_actions .houzez-button-icon {
    height: 30px;
    width: 30px;
    overflow: hidden;
    margin: 0 0 4px 4px;
    padding: 5px 0px 0px 5px;
    line-height: 30px;
    border: none;
    overflow: visible;
}

/* Tooltip CSS */
a.tips {
    position: relative;
    display: inline;
}
a.tips:after {
    display: block;
    visibility: hidden;
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translate(-50%);
    opacity: 0;
    content: attr(data-tip); /* might also use attr(title) */
    height: auto;
    padding: 0px 12px;
    z-index: 999;
    color: #fff;
    text-decoration: none;
    text-align: center;
    background: rgba(0, 0, 0, 0.85);
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    border-radius: 5px;
    font-size: 12px;
}
a.tips:before {
    position: absolute;
    visibility: hidden;
    width: 0;
    height: 0;
    left: 32%;
    bottom: 0px;
    opacity: 0;
    content: '';
    border-style: solid;
    border-width: 6px 6px 0 6px;
    border-color: rgba(0, 0, 0, 0.85) transparent transparent transparent;
}
a.tips:hover:after {
    visibility: visible;
    opacity: 1;
    bottom: 34px;
}
a.tips:hover:before {
    visibility: visible;
    opacity: 1;
    bottom: 28px;
}

.column-thumbnail,
.column-info,
.column-price,
.column-featured,
.column-listing_posted,
.column-status,
.column-houzez_actions,
.column-review_actions,
.column-title,
.column-ratings,
.column-post_title,
.column-date {
    vertical-align: middle !important;
}

.column-thumbnail img {
    max-width: 100%;
    height: auto;
}

.wp-core-ui .icon-accept {
    color: #099109;
}

.wp-core-ui .icon-reject {
    color: #b01313;
}

span.hz-expiry {
    font-style: italic;
    color: green;
}
.column-status .listing-status {
    display: flex;
}

.column-status .listing-status span {
    background-color: #6c757d;
    border-radius: 2px;
    display: inline-block;
    height: 9px;
    margin: auto 8px auto 0;
    width: 9px;
}

.column-status .listing-status.status-publish span {
    background-color: #28a745;
}
.column-status .listing-status.status-expired span {
    background-color: #dc3545;
}
.column-status .listing-status.status-pending span {
    background-color: #ffc107;
}

.column-status .listing-status.status-disapproved span {
    background-color: #343a40;
}

.column-status .listing-status.status-on_hold span {
    background-color: #17a2b8;
}

.column-status .listing-status.status-houzez_sold span {
    background-color: #007bff;
}

@media (min-width: 1350px) {
    .column-price {
        width: 155px;
    }
    .column-listing_posted {
        width: 145px;
    }
    .column-featured {
        width: 70px;
        text-align: center;
    }
    .column-thumbnail {
        width: 100px;
    }
    .column-info {
        width: 225px;
    }
    .column-houzez_actions,
    .column-review_actions {
        width: 102px;
    }
    .column-status {
        width: 130px;
    }
}
.import-locations-wrap {
    max-width: 500px;
}
