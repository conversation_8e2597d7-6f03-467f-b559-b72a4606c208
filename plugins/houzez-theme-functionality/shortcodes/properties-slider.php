<?php
/*-----------------------------------------------------------------------------------*/
/*	Properties
/*-----------------------------------------------------------------------------------*/
if( !function_exists('houzez_properties_slider') ) {
	function houzez_properties_slider($atts, $content = null)
	{
		extract(shortcode_atts(array(
			'property_type' => '',
			'property_status' => '',
			'posts_limit' => '',
		), $atts));

		ob_start();
		global $paged;
		if (is_front_page()) {
			$paged = (get_query_var('page')) ? get_query_var('page') : 1;
		}

		//do the query
		$the_query = Houzez_Data_Source::get_wp_query($atts, $paged); //by ref  do the query
		?>
		
		<section class="top-banner-wrap <?php houzez_banner_fullscreen(); ?> property-slider-wrap">
			<div class="property-slider property-banner-slider houzez-all-slider-wrap" data-autoplay="<?php echo esc_attr(houzez_option('banner_slider_autoplay', 1)); ?>" data-loop="<?php echo esc_attr(houzez_option('banner_slider_loop', 1)); ?>" data-speed="<?php echo esc_attr(houzez_option('banner_slider_autoplayspeed', '4000')); ?>">
				<?php 
				if( $the_query->have_posts() ): 
					while( $the_query->have_posts() ): $the_query->the_post();
						
						$slider_img = get_post_meta( get_the_ID(), 'fave_prop_slider_image', true );
						$img_url = wp_get_attachment_image_src( $slider_img, 'full', true );
						$img_url = $img_url[0];
						if(empty($slider_img)) {
							$img_url = wp_get_attachment_url( get_post_thumbnail_id() );
						}
						?>
						<?php get_template_part('template-parts/banners/partials/slider-item'); ?>
				<?php
					endwhile;
				endif;
				wp_reset_postdata();
				?>
			</div><!-- property-slider -->

		</section><!-- property-slider-wrap -->

		<?php
		$result = ob_get_contents();
		ob_end_clean();
		return $result;

	}

	add_shortcode('houzez_properties_slider', 'houzez_properties_slider');
}
?>