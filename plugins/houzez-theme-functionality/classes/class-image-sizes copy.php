<?php
/**
 * Houzez Image Sizes Class
 * Adds image size configuration capability to Houzez
 */
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

class Houzez_Image_Sizes {

    /**
     * Singleton instance
     */
    private static $instance = null;

    /**
     * Returns the instance of this class
     */
    public static function instance() {
        if ( is_null( self::$instance ) ) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    public function __construct() {
        add_filter( 'houzez_admin_sub_menus', array( $this, 'add_submenu_page' ), 20 );
        add_action( 'admin_init', array( $this, 'register_settings' ) );
        add_action( 'after_setup_theme', array( $this, 'setup_dynamic_image_sizes' ), 11 );
        add_action( 'admin_enqueue_scripts', array( $this, 'admin_enqueue_scripts' ) );
        
        // Filter WordPress image sizes to remove disabled ones
        add_filter( 'intermediate_image_sizes_advanced', array( $this, 'filter_image_sizes' ), 10, 1 );
        
        // AJAX handlers for custom image sizes
        add_action( 'wp_ajax_houzez_add_custom_image_size', array( $this, 'ajax_add_custom_image_size' ) );
        add_action( 'wp_ajax_houzez_get_custom_image_size', array( $this, 'ajax_get_custom_image_size' ) );
        add_action( 'wp_ajax_houzez_update_custom_image_size', array( $this, 'ajax_update_custom_image_size' ) );
        add_action( 'wp_ajax_houzez_delete_custom_image_size', array( $this, 'ajax_delete_custom_image_size' ) );
    }

    /**
     * Enqueue admin scripts and styles
     */
    public function admin_enqueue_scripts($hook) {
        if ($hook != 'houzez_page_houzez_image_sizes') {
            return;
        }

        wp_enqueue_style('wp-color-picker');
        wp_enqueue_script('wp-color-picker');
        wp_enqueue_script('jquery-ui-dialog');
        wp_enqueue_style('wp-jquery-ui-dialog');
        
        // Add inline styles for the settings page
        $custom_css = '
            .houzez-image-sizes-wrap {
                max-width: 100%;
                margin: 20px 0;
            }
            .houzez-image-sizes-wrap .form-table th {
                padding: 15px 10px 15px 0;
                width: 200px;
            }
            .houzez-size-group {
                background: #fff;
                border: 1px solid #ccd0d4;
                border-radius: 4px;
                padding: 15px 20px;
                margin-bottom: 20px;
            }
            .houzez-size-group h3 {
                border-bottom: 1px solid #eee;
                padding-bottom: 10px;
                margin-top: 0;
                display: flex;
                align-items: center;
            }
            .houzez-size-group h3 .dashicons {
                margin-right: 8px;
                color: #2271b1;
            }
            .image-size-note {
                background-color: #f8f8f8;
                padding: 15px;
                border-left: 4px solid #2271b1;
                margin: 15px 0;
            }
            .image-size-warning {
                background-color: #fef8ee;
                padding: 15px;
                border-left: 4px solid #d63638;
                margin: 15px 0;
            }
            .enable-settings-toggle {
                margin-bottom: 15px;
            }
            .default-value {
                color: #666;
                font-style: italic;
                font-size: 0.9em;
                display: block;
                margin-top: 5px;
            }
            .custom-sizes-table {
                margin-bottom: 20px;
            }
            #add-new-size-form {
                background: #f9f9f9;
                padding: 15px;
                border: 1px solid #e5e5e5;
                margin: 15px 0;
                border-radius: 4px;
            }
            .image-sizes-table {
                table-layout: fixed;
            }
            .image-sizes-table .column-name {
                width: 15%;
            }
            .image-sizes-table .column-slug {
                width: 15%;
            }
            .image-sizes-table .column-width,
            .image-sizes-table .column-height {
                width: 12%;
            }
            .image-sizes-table .column-crop,
            .image-sizes-table .column-enabled {
                width: 10%;
            }
            .image-sizes-table .column-actions {
                width: 20%;
            }
            .table-section {
                background-color: #f0f0f1;
            }
            .table-section td {
                padding: 10px 15px;
            }
            /* Toggle Switch styling */
            .switch {
                position: relative;
                display: inline-block;
                width: 50px;
                height: 24px;
            }
            .switch input {
                opacity: 0;
                width: 0;
                height: 0;
            }
            .slider {
                position: absolute;
                cursor: pointer;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: #ccc;
                -webkit-transition: .4s;
                transition: .4s;
            }
            .slider:before {
                position: absolute;
                content: "";
                height: 16px;
                width: 16px;
                left: 4px;
                bottom: 4px;
                background-color: white;
                -webkit-transition: .4s;
                transition: .4s;
            }
            input:checked + .slider {
                background-color: #2196F3;
            }
            input:focus + .slider {
                box-shadow: 0 0 1px #2196F3;
            }
            input:checked + .slider:before {
                -webkit-transform: translateX(26px);
                -ms-transform: translateX(26px);
                transform: translateX(26px);
            }
            .slider.round {
                border-radius: 24px;
            }
            .slider.round:before {
                border-radius: 50%;
            }
            .button + .button {
                margin-left: 5px;
            }
            
            /* Layout Image Assignments styling */
            .layout-assignments-grid {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
                grid-gap: 20px;
                margin-top: 15px;
            }
            .layout-assignment-item {
                border: 1px solid #e5e5e5;
                border-radius: 4px;
                padding: 15px;
                background: #f9f9f9;
                box-shadow: 0 1px 1px rgba(0,0,0,0.04);
                transition: all 0.2s ease;
            }
            .layout-assignment-item:hover {
                border-color: #2271b1;
                box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            }
            .layout-assignment-item .layout-label {
                margin-bottom: 10px;
                font-weight: 600;
            }
            .layout-assignment-item .layout-control select {
                width: 100%;
            }
            .submit-wrapper {
                margin-top: 20px;
                display: flex;
                align-items: center;
            }
            .assignment-help {
                margin-left: 20px;
                font-style: italic;
            }
            .tab-content {
                margin-top: 20px;
            }
        ';
        wp_add_inline_style('wp-admin', $custom_css);

        // Add JavaScript to handle the enable/disable toggle and custom image sizes
        $custom_js = '
            jQuery(document).ready(function($) {
                // Toggle add new size form
                $("#toggle-add-new-size").on("click", function(e) {
                    e.preventDefault();
                    $("#add-new-size-form").slideToggle();
                });
                
                $("#cancel_add_new_size").on("click", function(e) {
                    e.preventDefault();
                    $("#add-new-size-form").slideUp();
                    // Clear form
                    $("#new_size_name").val("");
                    $("#new_size_width").val("");
                    $("#new_size_height").val("");
                    $("#new_size_crop").prop("checked", false);
                });
                
                // Handle the Houzez size toggles - Fixed to preserve values when disabled
                $(".houzez-size-toggle").on("change", function() {
                    var prefix = $(this).data("prefix");
                    var inputs = $("input[name^=\'" + prefix + "\']").not($(this)).not("[type=hidden]");
                    var hiddenFields = $("input[name^=\'" + prefix + "_hidden\']");
                    
                    if($(this).is(":checked")) {
                        // Enable the fields and use their values
                        inputs.prop("disabled", false);
                        
                        // Clear any hidden fields
                        hiddenFields.remove();
                    } else {
                        // Disable the fields but preserve their values with hidden fields
                        inputs.each(function() {
                            var name = $(this).attr("name");
                            var value = $(this).val();
                            
                            // For checkboxes, we need to handle differently
                            if($(this).attr("type") === "checkbox") {
                                value = $(this).is(":checked") ? "1" : "0";
                            }
                            
                            // Create hidden field with same name to preserve value
                            var hiddenField = $("<input>")
                                .attr("type", "hidden")
                                .attr("name", name + "_hidden")
                                .attr("data-original", name)
                                .val(value);
                                
                            $(this).after(hiddenField);
                        });
                        
                        // Now disable visible fields
                        inputs.prop("disabled", true);
                    }
                });
                
                // Handle WordPress core size toggles (inverted logic)
                $(".wp-core-size-toggle").on("change", function() {
                    var sizeName = $(this).data("size");
                    
                    // No confirmation needed, just let the user toggle
                });
                
                // Form submission handling - properly handle disabled fields
                $("form").on("submit", function(e) {
                    // Before submit, process hidden fields for disabled sections
                    $("input[name$=_hidden]").each(function() {
                        var originalName = $(this).data("original");
                        $(this).attr("name", originalName);
                    });
                });
                
                // Reset to default values
                $(".reset-houzez-size").on("click", function() {
                    var prefix = $(this).data("prefix");
                    var width = $(this).data("width");
                    var height = $(this).data("height");
                    var crop = $(this).data("crop");
                    
                    $("input[name=\'" + prefix + "_w\']").val(width);
                    $("input[name=\'" + prefix + "_h\']").val(height);
                    
                    // Force reset the crop checkbox (directly set to true since all default values use crop=1)
                    $("input[name=\'" + prefix + "_crop\']").prop("checked", true);
                    
                    // Log the action for debugging
                    console.log("Reset " + prefix + " to defaults - Width: " + width + ", Height: " + height + ", Crop set to: true");
                });
                
                // Custom Image Sizes Handling
                $("#add_new_size").on("click", function(e) {
                    e.preventDefault();
                    
                    var name = $("#new_size_name").val();
                    var width = $("#new_size_width").val();
                    var height = $("#new_size_height").val();
                    var crop = $("#new_size_crop").is(":checked") ? 1 : 0;
                    
                    if(!name || !width || !height) {
                        alert("' . esc_js(__('Please fill in all required fields.', 'houzez')) . '");
                        return;
                    }
                    
                    // Send AJAX request to add the new size
                    $.ajax({
                        url: ajaxurl,
                        type: "POST",
                        data: {
                            action: "houzez_add_custom_image_size",
                            name: name,
                            width: width,
                            height: height,
                            crop: crop,
                            nonce: "' . wp_create_nonce('houzez_image_sizes_nonce') . '"
                        },
                        success: function(response) {
                            if(response.success) {
                                window.location.reload();
                            } else {
                                alert(response.data.message);
                            }
                        }
                    });
                });
                
                // Edit Custom Size
                $(".edit-custom-size").on("click", function(e) {
                    e.preventDefault();
                    
                    var name = $(this).data("name");
                    
                    // Get current size data via AJAX
                    $.ajax({
                        url: ajaxurl,
                        type: "POST",
                        data: {
                            action: "houzez_get_custom_image_size",
                            name: name,
                            nonce: "' . wp_create_nonce('houzez_image_sizes_nonce') . '"
                        },
                        success: function(response) {
                            if(response.success) {
                                var data = response.data;
                                
                                // Populate edit form
                                $("#edit_size_original_name").val(name);
                                $("#edit_size_name").val(name);
                                $("#edit_size_width").val(data.width);
                                $("#edit_size_height").val(data.height);
                                
                                if(data.crop) {
                                    $("#edit_size_crop").prop("checked", true);
                                } else {
                                    $("#edit_size_crop").prop("checked", false);
                                }
                                
                                if(data.enabled) {
                                    $("#edit_size_enabled").prop("checked", true);
                                } else {
                                    $("#edit_size_enabled").prop("checked", false);
                                }
                                
                                // Open dialog
                                $("#edit-size-dialog").dialog({
                                    resizable: false,
                                    height: "auto",
                                    width: 400,
                                    modal: true,
                                    buttons: {
                                        "' . esc_js(__('Save Changes', 'houzez')) . '": function() {
                                            var originalName = $("#edit_size_original_name").val();
                                            var newName = $("#edit_size_name").val();
                                            var width = $("#edit_size_width").val();
                                            var height = $("#edit_size_height").val();
                                            var crop = $("#edit_size_crop").is(":checked") ? 1 : 0;
                                            var enabled = $("#edit_size_enabled").is(":checked") ? 1 : 0;
                                            
                                            if(!newName || !width || !height) {
                                                alert("' . esc_js(__('Please fill in all required fields.', 'houzez')) . '");
                                                return;
                                            }
                                            
                                            // Send AJAX request to update the size
                                            $.ajax({
                                                url: ajaxurl,
                                                type: "POST",
                                                data: {
                                                    action: "houzez_update_custom_image_size",
                                                    original_name: originalName,
                                                    name: newName,
                                                    width: width,
                                                    height: height,
                                                    crop: crop,
                                                    enabled: enabled,
                                                    nonce: "' . wp_create_nonce('houzez_image_sizes_nonce') . '"
                                                },
                                                success: function(response) {
                                                    if(response.success) {
                                                        window.location.reload();
                                                    } else {
                                                        alert(response.data.message);
                                                    }
                                                }
                                            });
                                            
                                            $(this).dialog("close");
                                        },
                                        "' . esc_js(__('Cancel', 'houzez')) . '": function() {
                                            $(this).dialog("close");
                                        }
                                    }
                                });
                            } else {
                                alert(response.data.message);
                            }
                        }
                    });
                });
                
                // Delete Custom Size
                $(".delete-custom-size").on("click", function(e) {
                    e.preventDefault();
                    
                    if(!confirm("' . esc_js(__('Are you sure you want to delete this image size? This cannot be undone.', 'houzez')) . '")) {
                        return;
                    }
                    
                    var name = $(this).data("name");
                    
                    // Send AJAX request to delete the size
                    $.ajax({
                        url: ajaxurl,
                        type: "POST",
                        data: {
                            action: "houzez_delete_custom_image_size",
                            name: name,
                            nonce: "' . wp_create_nonce('houzez_image_sizes_nonce') . '"
                        },
                        success: function(response) {
                            if(response.success) {
                                window.location.reload();
                            } else {
                                alert(response.data.message);
                            }
                        }
                    });
                });
                
                // Initialize toggle states on page load
                $(".houzez-size-toggle").each(function() {
                    var prefix = $(this).data("prefix");
                    var inputs = $("input[name^=\'" + prefix + "\']").not(this);
                    
                    if(!$(this).is(":checked")) {
                        // Create hidden fields to preserve values even when disabled
                        inputs.each(function() {
                            var name = $(this).attr("name");
                            var value = $(this).val();
                            
                            // For checkboxes, we need to handle differently
                            if($(this).attr("type") === "checkbox") {
                                value = $(this).is(":checked") ? "1" : "0";
                            }
                            
                            // Create hidden field with same name to preserve value
                            var hiddenField = $("<input>")
                                .attr("type", "hidden")
                                .attr("name", name + "_hidden")
                                .attr("data-original", name)
                                .val(value);
                                
                            $(this).after(hiddenField);
                        });
                        
                        inputs.prop("disabled", true);
                    }
                });

                // Handle Custom Size Toggle
                $(".custom-size-toggle").on("change", function() {
                    var name = $(this).data("name");
                    var enabled = $(this).is(":checked") ? 1 : 0;
                    
                    // Send AJAX request to update the size
                    $.ajax({
                        url: ajaxurl,
                        type: "POST",
                        data: {
                            action: "houzez_update_custom_image_size",
                            original_name: name,
                            name: name,
                            enabled: enabled,
                            update_enabled_only: true,
                            nonce: "' . wp_create_nonce('houzez_image_sizes_nonce') . '"
                        },
                        success: function(response) {
                            if(!response.success) {
                                alert(response.data.message);
                                // Revert toggle if failed
                                $(this).prop("checked", !enabled);
                            }
                        }.bind(this)
                    });
                });
            });
        ';
        wp_add_inline_script('jquery', $custom_js);
    }

    /**
     * Add the submenu page to Houzez menu
     */
    public function add_submenu_page( $sub_menus ) {
        $sub_menus['houzez_image_sizes'] = array(
            'houzez_dashboard',
            esc_html__( 'Image Sizes', 'houzez' ),
            esc_html__( 'Image Sizes', 'houzez' ),
            'manage_options',
            'houzez_image_sizes',
            array( $this, 'render_page' )
        );
        
        return $sub_menus;
    }

    /**
     * Register all settings for the image sizes
     */
    public function register_settings() {
        // Register image assignments setting
        register_setting(
            'houzez_layout_image_assignments_group',
            'houzez_layout_image_assignments',
            array(
                'type'              => 'array',
                'sanitize_callback' => array($this, 'sanitize_image_assignments'),
                'default'           => array(),
            )
        );

        // Register WordPress core size disable options
        register_setting( 'houzez_image_sizes_group', 'houzez_enable_thumbnail_size',  [
            'type'              => 'boolean',
            'sanitize_callback' => 'wp_validate_boolean',
            'default'           => true,
        ] );
        register_setting( 'houzez_image_sizes_group', 'houzez_enable_medium_size',  [
            'type'              => 'boolean',
            'sanitize_callback' => 'wp_validate_boolean',
            'default'           => true,
        ] );
        register_setting( 'houzez_image_sizes_group', 'houzez_enable_medium_large_size',  [
            'type'              => 'boolean',
            'sanitize_callback' => 'wp_validate_boolean',
            'default'           => true,
        ] );
        register_setting( 'houzez_image_sizes_group', 'houzez_enable_large_size',  [
            'type'              => 'boolean',
            'sanitize_callback' => 'wp_validate_boolean',
            'default'           => true,
        ] );
        
        // Register enable settings
        register_setting( 'houzez_image_sizes_group', 'houzez_enable_gallery_size',  [
            'type'              => 'boolean',
            'sanitize_callback' => 'wp_validate_boolean',
            'default'           => true,
        ] );
        register_setting( 'houzez_image_sizes_group', 'houzez_enable_top_v7_size',  [
            'type'              => 'boolean',
            'sanitize_callback' => 'wp_validate_boolean',
            'default'           => true,
        ] );
        register_setting( 'houzez_image_sizes_group', 'houzez_enable_item_image_6_size',  [
            'type'              => 'boolean',
            'sanitize_callback' => 'wp_validate_boolean',
            'default'           => true,
        ] );

        // Register individual size settings
        register_setting( 'houzez_image_sizes_group', 'houzez_gallery_w',  [
            'type'              => 'integer',
            'sanitize_callback' => 'absint',
            'default'           => 1170,
        ] );
        register_setting( 'houzez_image_sizes_group', 'houzez_gallery_h',  [
            'type'              => 'integer',
            'sanitize_callback' => 'absint',
            'default'           => 785,
        ] );
        register_setting( 'houzez_image_sizes_group', 'houzez_gallery_crop', [
            'type'              => 'boolean',
            'sanitize_callback' => 'wp_validate_boolean',
            'default'           => true,
        ] );

        register_setting( 'houzez_image_sizes_group', 'houzez_top_v7_w',  [
            'type'              => 'integer',
            'sanitize_callback' => 'absint',
            'default'           => 780,
        ] );
        register_setting( 'houzez_image_sizes_group', 'houzez_top_v7_h',  [
            'type'              => 'integer',
            'sanitize_callback' => 'absint',
            'default'           => 780,
        ] );
        register_setting( 'houzez_image_sizes_group', 'houzez_top_v7_crop', [
            'type'              => 'boolean',
            'sanitize_callback' => 'wp_validate_boolean',
            'default'           => true,
        ] );

        register_setting( 'houzez_image_sizes_group', 'houzez_item_image_6_w',  [
            'type'              => 'integer',
            'sanitize_callback' => 'absint',
            'default'           => 584,
        ] );
        register_setting( 'houzez_image_sizes_group', 'houzez_item_image_6_h',  [
            'type'              => 'integer',
            'sanitize_callback' => 'absint',
            'default'           => 438,
        ] );
        register_setting( 'houzez_image_sizes_group', 'houzez_item_image_6_crop', [
            'type'              => 'boolean',
            'sanitize_callback' => 'wp_validate_boolean',
            'default'           => true,
        ] );

        // Add a single section to hold all fields
        add_settings_section(
            'houzez_image_sizes_section',
            __( 'Manage the dimensions and cropping for built-in Houzez image sizes.', 'houzez' ),
            array( $this, 'section_description' ),
            'houzez_image_sizes'  // Page slug
        );
    }

    /**
     * Settings section description
     */
    public function section_description() {
        ?>
        <div class="image-size-warning">
            <p>
                <strong><?php esc_html_e('Important:', 'houzez'); ?></strong> 
                <?php esc_html_e('Changes to these settings will only apply to newly uploaded images. For existing images, you will need to regenerate thumbnails.', 'houzez'); ?>
                <a href="<?php echo esc_url(admin_url('plugin-install.php?s=force-regenerate-thumbnails&tab=search&type=term')); ?>" target="_blank">
                    <?php esc_html_e('Install Force Regenerate Thumbnails plugin', 'houzez'); ?>
                </a>
            </p>
        </div>
        <?php
    }

    /**
     * Render the settings page
     */
    public function render_page() {
        // Get default sizes for reference
        $defaults = array(
            'gallery' => array(
                'width' => 1170,
                'height' => 785,
                'crop' => 1
            ),
            'top_v7' => array(
                'width' => 780,
                'height' => 780,
                'crop' => 1
            ),
            'item_image_6' => array(
                'width' => 584,
                'height' => 438,
                'crop' => 1
            )
        );

        // Get custom sizes
        $custom_sizes = get_option('houzez_custom_image_sizes', array());
        
        // Get WordPress core sizes
        $wp_core_sizes = array(
            'thumbnail' => array(
                'width' => get_option('thumbnail_size_w', 150),
                'height' => get_option('thumbnail_size_h', 150),
                'crop' => (bool) get_option('thumbnail_crop', 1),
                'enabled' => true,
                'core' => true
            ),
            'medium' => array(
                'width' => get_option('medium_size_w', 300),
                'height' => get_option('medium_size_h', 300),
                'crop' => false,
                'enabled' => true,
                'core' => true
            ),
            'medium_large' => array(
                'width' => get_option('medium_large_size_w', 768),
                'height' => get_option('medium_large_size_h', 0),
                'crop' => false,
                'enabled' => true,
                'core' => true
            ),
            'large' => array(
                'width' => get_option('large_size_w', 1024),
                'height' => get_option('large_size_h', 1024),
                'crop' => false,
                'enabled' => true,
                'core' => true
            )
        );
        
        // Get Houzez built-in sizes
        $houzez_sizes = array(
            'Gallery' => array(
                'width' => get_option('houzez_gallery_w', $defaults['gallery']['width']),
                'height' => get_option('houzez_gallery_h', $defaults['gallery']['height']),
                'crop' => (bool) get_option('houzez_gallery_crop', $defaults['gallery']['crop']),
                'enabled' => (bool) get_option('houzez_enable_gallery_size', true),
                'option_prefix' => 'houzez_gallery',
                'default_width' => $defaults['gallery']['width'],
                'default_height' => $defaults['gallery']['height'],
                'default_crop' => $defaults['gallery']['crop']
            ),
            'Top-v7' => array(
                'width' => get_option('houzez_top_v7_w', $defaults['top_v7']['width']),
                'height' => get_option('houzez_top_v7_h', $defaults['top_v7']['height']),
                'crop' => (bool) get_option('houzez_top_v7_crop', $defaults['top_v7']['crop']),
                'enabled' => (bool) get_option('houzez_enable_top_v7_size', true),
                'option_prefix' => 'houzez_top_v7',
                'default_width' => $defaults['top_v7']['width'],
                'default_height' => $defaults['top_v7']['height'],
                'default_crop' => $defaults['top_v7']['crop']
            ),
            'Item-6' => array(
                'width' => get_option('houzez_item_image_6_w', $defaults['item_image_6']['width']),
                'height' => get_option('houzez_item_image_6_h', $defaults['item_image_6']['height']),
                'crop' => (bool) get_option('houzez_item_image_6_crop', $defaults['item_image_6']['crop']),
                'enabled' => (bool) get_option('houzez_enable_item_image_6_size', true),
                'option_prefix' => 'houzez_item_image_6',
                'default_width' => $defaults['item_image_6']['width'],
                'default_height' => $defaults['item_image_6']['height'],
                'default_crop' => $defaults['item_image_6']['crop']
            )
        );

        // Prepare available image sizes for dropdowns
        $available_image_sizes = array();
        
        // Add WordPress core sizes
        foreach ($wp_core_sizes as $slug => $size) {
            if (get_option('houzez_enable_' . $slug . '_size', true)) {
                $width = $size['width'];
                $height = $size['height'] ?: '(auto)';
                $available_image_sizes[$slug] = sprintf('%s (%dx%s) [WordPress]', ucfirst($slug), $width, $height);
            }
        }
        
        // Add Houzez sizes
        foreach ($houzez_sizes as $name => $size) {
            $slug = sanitize_title('houzez-' . $name);
            if ($size['enabled']) {
                $available_image_sizes[$slug] = sprintf('%s (%dx%d) [Houzez]', $name, $size['width'], $size['height']);
            }
        }
        
        // Add custom sizes
        if (!empty($custom_sizes) && is_array($custom_sizes)) {
            foreach ($custom_sizes as $name => $size) {
                if (!empty($size['enabled'])) {
                    $slug = sanitize_title($name);
                    $available_image_sizes[$slug] = sprintf('%s (%dx%d) [Custom]', $name, $size['width'], $size['height']);
                }
            }
        }
        
        // Get layout image assignments
        $default_assignments = array(
            'listing_grid_v1' => 'houzez-item-image-6',
            'listing_grid_v2' => 'houzez-item-image-6',
            'listing_grid_v3' => 'houzez-item-image-6',
            'listing_grid_v4' => 'houzez-item-image-6',
            'listing_grid_v5' => 'houzez-item-image-6',
            'listing_grid_v6' => 'houzez-item-image-6', 
            'listing_grid_v7' => 'houzez-item-image-6',
            'listing_list_v1' => 'houzez-item-image-6',
            'listing_list_v2' => 'houzez-item-image-6',
            'listing_list_v4' => 'houzez-item-image-6',
            'listing_list_v7' => 'houzez-item-image-6',
            'property_slider' => 'houzez-gallery',
            'property_detail' => 'houzez-gallery',
            'property_detail_v2' => 'houzez-gallery',
            'property_detail_v3' => 'houzez-gallery',
            'property_detail_v4' => 'houzez-gallery',
            'property_detail_v5' => 'houzez-top-v7',
            'agent_profile' => 'thumbnail',
            'agency_profile' => 'medium',
            'blog_post' => 'large'
        );
        
        $image_assignments = get_option('houzez_layout_image_assignments', $default_assignments);

        // Determine active tab
        $active_tab = isset($_GET['tab']) ? sanitize_text_field($_GET['tab']) : 'layout_assignments';
        ?>
        <div class="wrap houzez-image-sizes-wrap">
            <h1><?php esc_html_e('Houzez Image Sizes', 'houzez'); ?></h1>
            
            <div class="image-size-warning">
                <p>
                    <strong><?php esc_html_e('Important:', 'houzez'); ?></strong> 
                    <?php esc_html_e('Changes to these settings will only apply to newly uploaded images. For existing images, you will need to regenerate thumbnails.', 'houzez'); ?>
                    <a href="<?php echo esc_url(admin_url('plugin-install.php?s=force-regenerate-thumbnails&tab=search&type=term')); ?>" target="_blank">
                        <?php esc_html_e('Install Force Regenerate Thumbnails plugin', 'houzez'); ?>
                    </a>
                </p>
            </div>
            
            <h2 class="nav-tab-wrapper">
                <a href="?page=houzez_image_sizes&tab=layout_assignments" class="nav-tab <?php echo $active_tab == 'layout_assignments' ? 'nav-tab-active' : ''; ?>">
                    <?php esc_html_e('Layout Image Assignments', 'houzez'); ?>
                </a>
                <a href="?page=houzez_image_sizes&tab=manage_sizes" class="nav-tab <?php echo $active_tab == 'manage_sizes' ? 'nav-tab-active' : ''; ?>">
                    <?php esc_html_e('Manage Image Sizes', 'houzez'); ?>
                </a>
            </h2>
            
            <?php if ($active_tab == 'layout_assignments') : ?>
                <!-- Layout Image Assignments Tab -->
                <div id="layout-assignments-tab" class="tab-content">
                    <p class="description">
                        <?php esc_html_e('Assign specific image sizes to different areas of your website. This controls which image dimensions are used in each layout element.', 'houzez'); ?>
                    </p>
                    
                    <form method="post" action="options.php">
                        <?php settings_fields('houzez_layout_image_assignments_group'); ?>
                        
                        <!-- Group 1: Property Listings -->
                        <div class="houzez-size-group">
                            <h3>
                                <span class="dashicons dashicons-grid-view"></span> 
                                <?php esc_html_e('Property Listings', 'houzez'); ?>
                            </h3>
                            <p class="description"><?php esc_html_e('Control image sizes for property listings across different views.', 'houzez'); ?></p>
                            
                            <div class="layout-assignments-grid">
                                <?php 
                                $listing_elements = [
                                    'listing_grid_v1' => esc_html__('Listing Grid v1', 'houzez'),
                                    'listing_grid_v2' => esc_html__('Listing Grid v2', 'houzez'),
                                    'listing_grid_v3' => esc_html__('Listing Grid v3', 'houzez'),
                                    'listing_grid_v4' => esc_html__('Listing Grid v4', 'houzez'),
                                    'listing_grid_v5' => esc_html__('Listing Grid v5', 'houzez'),
                                    'listing_grid_v6' => esc_html__('Listing Grid v6', 'houzez'),
                                    'listing_grid_v7' => esc_html__('Listing Grid v7', 'houzez'),
                                    'listing_list_v1' => esc_html__('Listing List v1', 'houzez'),
                                    'listing_list_v2' => esc_html__('Listing List v2', 'houzez'),
                                    'listing_list_v4' => esc_html__('Listing List v4', 'houzez'),
                                    'listing_list_v7' => esc_html__('Listing List v7', 'houzez'),
                                    
                                ];
                                
                                foreach ($listing_elements as $key => $label) : 
                                ?>
                                    <div class="layout-assignment-item">
                                        <div class="layout-label">
                                            <label for="<?php echo esc_attr($key); ?>"><?php echo esc_html($label); ?></label>
                                        </div>
                                        <div class="layout-control">
                                            <select name="houzez_layout_image_assignments[<?php echo esc_attr($key); ?>]" id="<?php echo esc_attr($key); ?>" class="regular-text">
                                                <?php foreach ($available_image_sizes as $size_key => $size_label) : ?>
                                                    <option value="<?php echo esc_attr($size_key); ?>" <?php selected(isset($image_assignments[$key]) ? $image_assignments[$key] : 'houzez-item-image-6', $size_key); ?>>
                                                        <?php echo esc_html($size_label); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        
                        <!-- Group 2: Property Detail Pages -->
                        <div class="houzez-size-group">
                            <h3>
                                <span class="dashicons dashicons-admin-home"></span> 
                                <?php esc_html_e('Property Detail Pages', 'houzez'); ?>
                            </h3>
                            <p class="description"><?php esc_html_e('Control image sizes for property detail pages and sliders.', 'houzez'); ?></p>
                            
                            <div class="layout-assignments-grid">
                                <?php 
                                $detail_elements = [
                                    'property_slider' => esc_html__('Property Slider', 'houzez'),
                                    'property_detail' => esc_html__('Property Detail v1', 'houzez'),
                                    'property_detail_v2' => esc_html__('Property Detail v2', 'houzez'),
                                    'property_detail_v3' => esc_html__('Property Detail v3', 'houzez'),
                                    'property_detail_v4' => esc_html__('Property Detail v4', 'houzez'),
                                    'property_detail_v5' => esc_html__('Property Detail v5', 'houzez'),
                                ];
                                
                                foreach ($detail_elements as $key => $label) : 
                                ?>
                                    <div class="layout-assignment-item">
                                        <div class="layout-label">
                                            <label for="<?php echo esc_attr($key); ?>"><?php echo esc_html($label); ?></label>
                                        </div>
                                        <div class="layout-control">
                                            <select name="houzez_layout_image_assignments[<?php echo esc_attr($key); ?>]" id="<?php echo esc_attr($key); ?>" class="regular-text">
                                                <?php foreach ($available_image_sizes as $size_key => $size_label) : ?>
                                                    <option value="<?php echo esc_attr($size_key); ?>" <?php selected(isset($image_assignments[$key]) ? $image_assignments[$key] : 'houzez-item-image-6', $size_key); ?>>
                                                        <?php echo esc_html($size_label); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        
                        <!-- Group 3: Profiles and Blog -->
                        <div class="houzez-size-group">
                            <h3>
                                <span class="dashicons dashicons-admin-users"></span> 
                                <?php esc_html_e('Profiles & Blog', 'houzez'); ?>
                            </h3>
                            <p class="description"><?php esc_html_e('Control image sizes for agent/agency profiles and blog posts.', 'houzez'); ?></p>
                            
                            <div class="layout-assignments-grid">
                                <?php 
                                $profile_blog_elements = [
                                    'agent_profile' => esc_html__('Agent Profile', 'houzez'),
                                    'agency_profile' => esc_html__('Agency Profile', 'houzez'),
                                    'blog_post' => esc_html__('Blog Post', 'houzez'),
                                ];
                                
                                foreach ($profile_blog_elements as $key => $label) : 
                                ?>
                                    <div class="layout-assignment-item">
                                        <div class="layout-label">
                                            <label for="<?php echo esc_attr($key); ?>"><?php echo esc_html($label); ?></label>
                                        </div>
                                        <div class="layout-control">
                                            <select name="houzez_layout_image_assignments[<?php echo esc_attr($key); ?>]" id="<?php echo esc_attr($key); ?>" class="regular-text">
                                                <?php foreach ($available_image_sizes as $size_key => $size_label) : ?>
                                                    <option value="<?php echo esc_attr($size_key); ?>" <?php selected(isset($image_assignments[$key]) ? $image_assignments[$key] : 'houzez-item-image-6', $size_key); ?>>
                                                        <?php echo esc_html($size_label); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        
                        <div class="submit-wrapper">
                            <?php submit_button(__('Save Assignments', 'houzez'), 'primary', 'submit', false); ?>
                            <p class="description assignment-help">
                                <?php esc_html_e('Tip: You can create custom image sizes in the "Manage Image Sizes" tab if you need specific dimensions.', 'houzez'); ?>
                            </p>
                        </div>
                    </form>
                </div>
            <?php else : ?>
                <!-- Manage Image Sizes Tab -->
                <div id="manage-sizes-tab" class="tab-content">
                    <form method="post" action="options.php">
                        <?php settings_fields('houzez_image_sizes_group'); ?>
                        <?php do_settings_sections('houzez_image_sizes'); ?>
                        
                        <div class="tablenav top">
                            <div class="alignleft actions">
                                <a href="#" id="toggle-add-new-size" class="button button-primary">
                                    <span class="dashicons dashicons-plus" style="margin-top: 4px;"></span>
                                    <?php esc_html_e('Add New Image Size', 'houzez'); ?>
                                </a>
                            </div>
                            <br class="clear">
                        </div>
                        
                        <!-- Add New Size Form (initially hidden) -->
                        <div id="add-new-size-form" style="display:none;">
                            <div class="houzez-size-group">
                                <h3><?php esc_html_e('Create New Custom Image Size', 'houzez'); ?></h3>
                                <table class="form-table">
                                    <tr>
                                        <th scope="row"><?php esc_html_e('Name', 'houzez'); ?></th>
                                        <td>
                                            <input type="text" id="new_size_name" name="new_size_name" class="regular-text" />
                                            <p class="description"><?php esc_html_e('A descriptive name for this image size', 'houzez'); ?></p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th scope="row"><?php esc_html_e('Width', 'houzez'); ?></th>
                                        <td>
                                            <input type="number" id="new_size_width" name="new_size_width" class="small-text" />
                                            <p class="description"><?php esc_html_e('Width in pixels', 'houzez'); ?></p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th scope="row"><?php esc_html_e('Height', 'houzez'); ?></th>
                                        <td>
                                            <input type="number" id="new_size_height" name="new_size_height" class="small-text" />
                                            <p class="description"><?php esc_html_e('Height in pixels', 'houzez'); ?></p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th scope="row"><?php esc_html_e('Hard Crop?', 'houzez'); ?></th>
                                        <td>
                                            <input type="checkbox" id="new_size_crop" name="new_size_crop" value="1" />
                                            <p class="description"><?php esc_html_e('Whether to crop the image to exact dimensions', 'houzez'); ?></p>
                                        </td>
                                    </tr>
                                </table>
                                <div class="submit-wrapper" style="padding: 10px 0;">
                                    <button type="button" id="add_new_size" class="button button-primary">
                                        <?php esc_html_e('Add Custom Image Size', 'houzez'); ?>
                                    </button>
                                    <button type="button" id="cancel_add_new_size" class="button button-secondary">
                                        <?php esc_html_e('Cancel', 'houzez'); ?>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Image sizes unified table -->
                        <table class="wp-list-table widefat fixed striped image-sizes-table">
                            <thead>
                                <tr>
                                    <th class="column-name"><?php esc_html_e('Name', 'houzez'); ?></th>
                                    <th class="column-slug"><?php esc_html_e('Slug', 'houzez'); ?></th>
                                    <th class="column-width"><?php esc_html_e('Width', 'houzez'); ?></th>
                                    <th class="column-height"><?php esc_html_e('Height', 'houzez'); ?></th>
                                    <th class="column-crop"><?php esc_html_e('Crop', 'houzez'); ?></th>
                                    <th class="column-enabled"><?php esc_html_e('Enabled', 'houzez'); ?></th>
                                    <th class="column-actions"><?php esc_html_e('Actions', 'houzez'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- WordPress Core Sizes -->
                                <tr class="table-section">
                                    <td colspan="7">
                                        <strong><?php esc_html_e('WordPress Core Sizes', 'houzez'); ?></strong>
                                        <span class="description"><?php esc_html_e('These sizes are defined by WordPress core. You can disable generation but dimensions must be changed in Media Settings.', 'houzez'); ?></span>
                                    </td>
                                </tr>
                                <?php foreach ($wp_core_sizes as $name => $size) : ?>
                                    <tr class="core-size-row">
                                        <td><?php echo esc_html(ucfirst($name)); ?></td>
                                        <td><?php echo esc_html($name); ?></td>
                                        <td><?php echo esc_html($size['width']); ?></td>
                                        <td><?php echo esc_html($size['height']); ?></td>
                                        <td><?php echo $size['crop'] ? esc_html__('Yes', 'houzez') : esc_html__('No', 'houzez'); ?></td>
                                        <td>
                                            <!-- Hidden field to ensure unchecked toggles are tracked -->
                                            <input type="hidden" name="houzez_core_sizes_submitted[]" value="<?php echo esc_attr($name); ?>">
                                            
                                            <label class="switch">
                                                <input type="checkbox" name="houzez_enable_<?php echo esc_attr($name); ?>_size" 
                                                       value="1" <?php checked(get_option('houzez_enable_' . $name . '_size', true), true); ?> 
                                                       class="wp-core-size-toggle"
                                                       data-size="<?php echo esc_attr($name); ?>" />
                                                <span class="slider round"></span>
                                            </label>
                                        </td>
                                        <td>
                                            <a href="<?php echo esc_url(admin_url('options-media.php')); ?>" class="button button-small">
                                                <?php esc_html_e('Edit in Media Settings', 'houzez'); ?>
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                                
                                <!-- Houzez Built-in Sizes -->
                                <tr class="table-section">
                                    <td colspan="7">
                                        <strong><?php esc_html_e('Houzez Built-in Sizes', 'houzez'); ?></strong>
                                        <span class="description"><?php esc_html_e('These sizes are used by the Houzez theme for property listings and galleries.', 'houzez'); ?></span>
                                    </td>
                                </tr>
                                <?php foreach ($houzez_sizes as $name => $size) : ?>
                                    <tr class="houzez-size-row">
                                        <td><?php echo esc_html($name); ?></td>
                                        <td>houzez-<?php echo esc_html(sanitize_title($name)); ?></td>
                                        <td>
                                            <input type="number" name="<?php echo esc_attr($size['option_prefix']); ?>_w" 
                                                value="<?php echo esc_attr($size['width']); ?>" 
                                                class="small-text" <?php disabled(!$size['enabled']); ?> />
                                            <span class="default-value">
                                                (<?php esc_html_e('Default', 'houzez'); ?>: <?php echo esc_html($size['default_width']); ?>px)
                                            </span>
                                        </td>
                                        <td>
                                            <input type="number" name="<?php echo esc_attr($size['option_prefix']); ?>_h" 
                                                value="<?php echo esc_attr($size['height']); ?>" 
                                                class="small-text" <?php disabled(!$size['enabled']); ?> />
                                            <span class="default-value">
                                                (<?php esc_html_e('Default', 'houzez'); ?>: <?php echo esc_html($size['default_height']); ?>px)
                                            </span>
                                        </td>
                                        <td>
                                            <input type="checkbox" name="<?php echo esc_attr($size['option_prefix']); ?>_crop" 
                                                value="1" <?php checked($size['crop'], true); ?> <?php disabled(!$size['enabled']); ?> />
                                        </td>
                                        <td>
                                            <label class="switch">
                                                <input type="checkbox" name="houzez_enable_<?php echo esc_attr(str_replace('houzez_', '', $size['option_prefix'])); ?>_size" 
                                                    value="1" <?php checked($size['enabled'], true); ?> 
                                                    class="houzez-size-toggle" data-prefix="<?php echo esc_attr($size['option_prefix']); ?>" />
                                                <span class="slider round"></span>
                                            </label>
                                        </td>
                                        <td>
                                            <button type="button" class="button button-small reset-houzez-size" 
                                                data-prefix="<?php echo esc_attr($size['option_prefix']); ?>"
                                                data-width="<?php echo esc_attr($size['default_width']); ?>"
                                                data-height="<?php echo esc_attr($size['default_height']); ?>"
                                                data-crop="<?php echo $size['default_crop'] ? '1' : '0'; ?>">
                                                <?php esc_html_e('Reset to Default', 'houzez'); ?>
                                            </button>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                                
                                <!-- Custom Image Sizes -->
                                <tr class="table-section">
                                    <td colspan="7">
                                        <strong><?php esc_html_e('Custom Image Sizes', 'houzez'); ?></strong>
                                        <span class="description"><?php esc_html_e('These are custom image sizes that you have created.', 'houzez'); ?></span>
                                    </td>
                                </tr>
                                <?php if (!empty($custom_sizes) && is_array($custom_sizes)) : ?>
                                    <?php foreach ($custom_sizes as $name => $size) : ?>
                                        <tr class="custom-size-row">
                                            <td><?php echo esc_html($name); ?></td>
                                            <td><?php echo esc_html(sanitize_title($name)); ?></td>
                                            <td><?php echo esc_html($size['width']); ?></td>
                                            <td><?php echo esc_html($size['height']); ?></td>
                                            <td><?php echo $size['crop'] ? esc_html__('Yes', 'houzez') : esc_html__('No', 'houzez'); ?></td>
                                            <td>
                                                <label class="switch">
                                                    <input type="checkbox" name="custom_size_enabled_<?php echo esc_attr(sanitize_title($name)); ?>"
                                                           value="1" <?php checked(!empty($size['enabled']), true); ?> 
                                                           class="custom-size-toggle"
                                                           data-name="<?php echo esc_attr($name); ?>" />
                                                    <span class="slider round"></span>
                                                </label>
                                            </td>
                                            <td>
                                                <a href="#" class="button button-small edit-custom-size" data-name="<?php echo esc_attr($name); ?>">
                                                    <?php esc_html_e('Edit', 'houzez'); ?>
                                                </a>
                                                <a href="#" class="button button-small delete-custom-size" data-name="<?php echo esc_attr($name); ?>">
                                                    <?php esc_html_e('Delete', 'houzez'); ?>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else : ?>
                                    <tr class="no-custom-sizes">
                                        <td colspan="7"><?php esc_html_e('No custom image sizes defined.', 'houzez'); ?></td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                        
                        <?php submit_button(__('Save All Changes', 'houzez')); ?>
                    </form>
                    
                    <div class="image-size-warning">
                        <p>
                            <strong><?php esc_html_e('Warning:', 'houzez'); ?></strong> 
                            <?php esc_html_e('Disabling any image size will prevent WordPress from generating that specific thumbnail size for new uploads. This may affect how images appear in certain areas of your website.', 'houzez'); ?>
                        </p>
                        <p>
                            <?php esc_html_e('If you later decide to re-enable a previously disabled size, you will need to regenerate thumbnails for existing images.', 'houzez'); ?>
                        </p>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Edit Modal Dialog -->
            <div id="edit-size-dialog" style="display:none;" title="<?php esc_attr_e('Edit Custom Image Size', 'houzez'); ?>">
                <form id="edit-size-form">
                    <input type="hidden" id="edit_size_original_name" name="edit_size_original_name" value="" />
                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php esc_html_e('Name', 'houzez'); ?></th>
                            <td>
                                <input type="text" id="edit_size_name" name="edit_size_name" class="regular-text" />
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><?php esc_html_e('Width', 'houzez'); ?></th>
                            <td>
                                <input type="number" id="edit_size_width" name="edit_size_width" class="small-text" />
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><?php esc_html_e('Height', 'houzez'); ?></th>
                            <td>
                                <input type="number" id="edit_size_height" name="edit_size_height" class="small-text" />
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><?php esc_html_e('Hard Crop?', 'houzez'); ?></th>
                            <td>
                                <input type="checkbox" id="edit_size_crop" name="edit_size_crop" value="1" />
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><?php esc_html_e('Enabled?', 'houzez'); ?></th>
                            <td>
                                <input type="checkbox" id="edit_size_enabled" name="edit_size_enabled" value="1" />
                            </td>
                        </tr>
                    </table>
                </form>
            </div>
        </div>
        <?php
    }

    /**
     * Apply the settings when registering image sizes
     */
    public function setup_dynamic_image_sizes() {
        // Define default sizes as constants so they're always available
        $defaults = array(
            'gallery' => array(
                'width' => 1170,
                'height' => 785,
                'crop' => 1
            ),
            'top_v7' => array(
                'width' => 780,
                'height' => 780,
                'crop' => 1
            ),
            'item_image_6' => array(
                'width' => 584,
                'height' => 438,
                'crop' => 1
            )
        );

        // Store defaults for reference in admin UI
        update_option('houzez_image_sizes_defaults', $defaults);

        // Gallery
        if (get_option('houzez_enable_gallery_size', true)) {
            $gw = get_option('houzez_gallery_w', $defaults['gallery']['width']);
            $gh = get_option('houzez_gallery_h', $defaults['gallery']['height']);
            $gc = get_option('houzez_gallery_crop', $defaults['gallery']['crop']);
            // Ensure we don't register sizes with 0 dimensions
            if ($gw > 0 && $gh > 0) {
                add_image_size('houzez-gallery', absint($gw), absint($gh), (bool)$gc);
            }
        }

        // Top-v7
        if (get_option('houzez_enable_top_v7_size', true)) {
            $tw = get_option('houzez_top_v7_w', $defaults['top_v7']['width']);
            $th = get_option('houzez_top_v7_h', $defaults['top_v7']['height']);
            $tc = get_option('houzez_top_v7_crop', $defaults['top_v7']['crop']);
            // Ensure we don't register sizes with 0 dimensions
            if ($tw > 0 && $th > 0) {
                add_image_size('houzez-top-v7', absint($tw), absint($th), (bool)$tc);
            }
        }

        // Item-image-6
        if (get_option('houzez_enable_item_image_6_size', true)) {
            $iw = get_option('houzez_item_image_6_w', $defaults['item_image_6']['width']);
            $ih = get_option('houzez_item_image_6_h', $defaults['item_image_6']['height']);
            $ic = get_option('houzez_item_image_6_crop', $defaults['item_image_6']['crop']);
            // Ensure we don't register sizes with 0 dimensions
            if ($iw > 0 && $ih > 0) {
                add_image_size('houzez-item-image-6', absint($iw), absint($ih), (bool)$ic);
            }
        }

        // Handle custom image sizes
        $custom_sizes = get_option('houzez_custom_image_sizes', array());
        
        if (!empty($custom_sizes) && is_array($custom_sizes)) {
            foreach ($custom_sizes as $size_key => $size_data) {
                if (!empty($size_data['enabled']) && $size_data['enabled'] && 
                    $size_data['width'] > 0 && $size_data['height'] > 0) {
                    add_image_size(
                        sanitize_title($size_key),
                        absint($size_data['width']),
                        absint($size_data['height']), 
                        (bool)$size_data['crop']
                    );
                }
            }
        }
    }

    /**
     * Filter WordPress image sizes to remove disabled ones
     */
    public function filter_image_sizes( $sizes ) {
        // Check if WordPress core sizes are disabled
        if ( !get_option( 'houzez_enable_thumbnail_size', true ) ) {
            unset( $sizes['thumbnail'] );
        }
        
        if ( !get_option( 'houzez_enable_medium_size', true ) ) {
            unset( $sizes['medium'] );
        }
        
        if ( !get_option( 'houzez_enable_medium_large_size', true ) ) {
            unset( $sizes['medium_large'] );
        }
        
        if ( !get_option( 'houzez_enable_large_size', true ) ) {
            unset( $sizes['large'] );
        }
        
        return $sizes;
    }

    /**
     * AJAX handler for adding a custom image size
     */
    public function ajax_add_custom_image_size() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'houzez_image_sizes_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed.', 'houzez')));
        }

        // Get and validate data
        $name = isset($_POST['name']) ? sanitize_text_field($_POST['name']) : '';
        $width = isset($_POST['width']) ? absint($_POST['width']) : 0;
        $height = isset($_POST['height']) ? absint($_POST['height']) : 0;
        $crop = isset($_POST['crop']) ? (bool)($_POST['crop']) : false;

        // Validate inputs
        if (empty($name) || empty($width) || empty($height)) {
            wp_send_json_error(array('message' => __('Please provide all required fields.', 'houzez')));
        }

        // Get existing custom sizes
        $custom_sizes = get_option('houzez_custom_image_sizes', array());

        // Check if name already exists
        if (isset($custom_sizes[$name])) {
            wp_send_json_error(array('message' => __('An image size with this name already exists.', 'houzez')));
        }

        // Add new size
        $custom_sizes[$name] = array(
            'width' => $width,
            'height' => $height,
            'crop' => $crop,
            'enabled' => true
        );

        // Save updated sizes
        update_option('houzez_custom_image_sizes', $custom_sizes);

        wp_send_json_success();
    }

    /**
     * AJAX handler for getting a custom image size
     */
    public function ajax_get_custom_image_size() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'houzez_image_sizes_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed.', 'houzez')));
        }

        // Get and validate data
        $name = isset($_POST['name']) ? sanitize_text_field($_POST['name']) : '';

        // Validate inputs
        if (empty($name)) {
            wp_send_json_error(array('message' => __('Invalid image size name.', 'houzez')));
        }

        // Get existing custom sizes
        $custom_sizes = get_option('houzez_custom_image_sizes', array());

        // Check if name exists
        if (!isset($custom_sizes[$name])) {
            wp_send_json_error(array('message' => __('Image size not found.', 'houzez')));
        }

        // Return the size data
        wp_send_json_success($custom_sizes[$name]);
    }

    /**
     * AJAX handler for updating a custom image size
     */
    public function ajax_update_custom_image_size() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'houzez_image_sizes_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed.', 'houzez')));
        }

        // Get and validate data
        $original_name = isset($_POST['original_name']) ? sanitize_text_field($_POST['original_name']) : '';
        $name = isset($_POST['name']) ? sanitize_text_field($_POST['name']) : '';
        $update_enabled_only = isset($_POST['update_enabled_only']) && $_POST['update_enabled_only'];
        $enabled = isset($_POST['enabled']) ? (bool)($_POST['enabled']) : false;

        // For enabled-only toggle from the data table
        if ($update_enabled_only) {
            if (empty($original_name)) {
                wp_send_json_error(array('message' => __('Invalid image size name.', 'houzez')));
            }

            // Get existing custom sizes
            $custom_sizes = get_option('houzez_custom_image_sizes', array());

            // Check if original name exists
            if (!isset($custom_sizes[$original_name])) {
                wp_send_json_error(array('message' => __('Image size not found.', 'houzez')));
            }

            // Update only the enabled status
            $custom_sizes[$original_name]['enabled'] = $enabled;

            // Save updated sizes
            update_option('houzez_custom_image_sizes', $custom_sizes);

            wp_send_json_success();
            return;
        }

        // For full edit from the edit dialog
        $width = isset($_POST['width']) ? absint($_POST['width']) : 0;
        $height = isset($_POST['height']) ? absint($_POST['height']) : 0;
        $crop = isset($_POST['crop']) ? (bool)($_POST['crop']) : false;

        // Validate inputs
        if (empty($original_name) || empty($name) || empty($width) || empty($height)) {
            wp_send_json_error(array('message' => __('Please provide all required fields.', 'houzez')));
        }

        // Get existing custom sizes
        $custom_sizes = get_option('houzez_custom_image_sizes', array());

        // Check if original name exists
        if (!isset($custom_sizes[$original_name])) {
            wp_send_json_error(array('message' => __('Original image size not found.', 'houzez')));
        }

        // Check if new name already exists (if different from original)
        if ($name !== $original_name && isset($custom_sizes[$name])) {
            wp_send_json_error(array('message' => __('An image size with this name already exists.', 'houzez')));
        }

        // If name changed, remove old entry
        if ($name !== $original_name) {
            unset($custom_sizes[$original_name]);
        }

        // Add/update the size
        $custom_sizes[$name] = array(
            'width' => $width,
            'height' => $height,
            'crop' => $crop,
            'enabled' => $enabled
        );

        // Save updated sizes
        update_option('houzez_custom_image_sizes', $custom_sizes);

        wp_send_json_success();
    }

    /**
     * AJAX handler for deleting a custom image size
     */
    public function ajax_delete_custom_image_size() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'houzez_image_sizes_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed.', 'houzez')));
        }

        // Get and validate data
        $name = isset($_POST['name']) ? sanitize_text_field($_POST['name']) : '';

        // Validate inputs
        if (empty($name)) {
            wp_send_json_error(array('message' => __('Invalid image size name.', 'houzez')));
        }

        // Get existing custom sizes
        $custom_sizes = get_option('houzez_custom_image_sizes', array());

        // Check if name exists
        if (!isset($custom_sizes[$name])) {
            wp_send_json_error(array('message' => __('Image size not found.', 'houzez')));
        }

        // Remove the size
        unset($custom_sizes[$name]);

        // Save updated sizes
        update_option('houzez_custom_image_sizes', $custom_sizes);

        wp_send_json_success();
    }

    /**
     * Sanitize the image assignments array
     */
    public function sanitize_image_assignments($input) {
        $sanitized_input = array();
        
        if (is_array($input)) {
            foreach ($input as $key => $value) {
                $sanitized_input[sanitize_key($key)] = sanitize_text_field($value);
            }
        }
        
        return $sanitized_input;
    }
}

// Initialize the class
Houzez_Image_Sizes::instance(); 