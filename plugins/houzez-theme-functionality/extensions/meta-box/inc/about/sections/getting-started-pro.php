<?php defined( 'ABSPATH' ) || die ?>

<div id="getting-started" class="gt-tab-pane gt-is-active">
	<p class="about-description"><?php esc_html_e( 'Please follow this video tutorial to get started with Meta Box and extensions:', 'meta-box' ); ?></p>
	<div class="youtube-video-container">
		<iframe width="560" height="315" src="https://www.youtube.com/embed/M0nEF7b0woU" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
	</div>
	<h3 class="tutorial"><a href="https://docs.metabox.io/tutorials/" target="_blank"><?php esc_html_e( 'Tutorials', 'meta-box' ) ?></a></h3>
	<p class="about-description"><?php esc_html_e( "We've made bunches of tutorials that come with videos, let's take a look to have detailed guides to create custom fields and apply them in real cases.", 'meta-box' ); ?></p>
	<div class="tutorial-items">
		<a href="https://docs.metabox.io/tutorials/beginners/" target="_blank"><span class="dashicons dashicons-text-page"></span><?php esc_html_e( 'Beginners', 'meta-box' ); ?><p><?php esc_html_e( 'Let’s start with some basic practices with Meta Box.', 'meta-box' ) ?></p></a>
		<a href="https://docs.metabox.io/tutorials/case-studies/" target="_blank"><span class="dashicons dashicons-text-page"></span><?php esc_html_e( 'Case Studies', 'meta-box' ); ?><p><?php esc_html_e( 'See how to use Meta Box in the real case studies with comprehensive tutorials.', 'meta-box' ) ?></p></a>
		<a href="https://docs.metabox.io/tutorials/general-guide/" target="_blank"><span class="dashicons dashicons-text-page"></span><?php esc_html_e( 'General Guide', 'meta-box' ); ?><p><?php esc_html_e( 'See how to use Meta box in common tasks.', 'meta-box' ) ?></p></a>
		<a href="https://docs.metabox.io/tutorials/extensions/" target="_blank"><span class="dashicons dashicons-text-page"></span><?php esc_html_e( 'Extensions', 'meta-box' ); ?><p><?php esc_html_e( 'Learn about Meta Box extensions, what features they offer and how to use them.', 'meta-box' ) ?></p></a>
		<a href="https://docs.metabox.io/tutorials/builders/" target="_blank"><span class="dashicons dashicons-text-page"></span><?php esc_html_e( 'Page Builders', 'meta-box' ); ?><p><?php esc_html_e( 'Tutorials on combining Meta Box and other builders or tools for real case studies.', 'meta-box' ) ?></p></a>
		<a href="https://docs.metabox.io/tutorials/mb-views/" target="_blank"><span class="dashicons dashicons-text-page"></span><?php esc_html_e( 'MB Views', 'meta-box' ); ?><p><?php esc_html_e( 'Build front-end templates for WordPress without touching theme files. Support Twig and all field types.', 'meta-box' ) ?></p></a>
	</div>
</div>
