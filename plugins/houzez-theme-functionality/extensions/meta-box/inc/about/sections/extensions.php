<?php defined( 'ABSPATH' ) || die ?>

<div id="extensions" class="gt-tab-pane">
	<p class="about-description">
		<?php esc_html_e( 'Extend custom fields in WordPress well beyond what others would ever consider ordinary!', 'meta-box' ); ?><br>
		<?php esc_html_e( 'Save over 80% with our extensions bundles.', 'meta-box' ); ?> <a target="_blank" href="https://metabox.io/pricing/?utm_source=dashboard&utm_medium=link&utm_campaign=meta_box"><?php esc_html_e( 'View Bundles', 'meta-box' ); ?> &rarr;</a>
	</p>
	<div class="extensions wp-clearfix">
		<div class="extension">
			<div class="extension-inner">
				<svg viewBox="0 0 1792 1792" xmlns="http://www.w3.org/2000/svg"><path d="M1596 476q14 14 28 36h-472v-472q22 14 36 28zm-476 164h544v1056q0 40-28 68t-68 28h-1344q-40 0-68-28t-28-68v-1600q0-40 28-68t68-28h800v544q0 40 28 68t68 28zm160 736v-64q0-14-9-23t-23-9h-704q-14 0-23 9t-9 23v64q0 14 9 23t23 9h704q14 0 23-9t9-23zm0-256v-64q0-14-9-23t-23-9h-704q-14 0-23 9t-9 23v64q0 14 9 23t23 9h704q14 0 23-9t9-23zm0-256v-64q0-14-9-23t-23-9h-704q-14 0-23 9t-9 23v64q0 14 9 23t23 9h704q14 0 23-9t9-23z" /></svg>
				<div class="extension-info">
					<h3>MB Custom Post Type</h3>
					<p><?php esc_html_e( 'Create and manage custom post types easily in WordPress with an easy-to-use interface.', 'meta-box' ); ?></p>
				</div>
			</div>
			<div class="extension-action">
				<a class="button" target="_blank" href="https://metabox.io/plugins/custom-post-type/?utm_source=dashboard&utm_medium=link&utm_campaign=meta_box"><?php esc_html_e( 'Free Download', 'meta-box' ); ?></a>
			</div>
		</div>
		<div class="extension">
			<div class="extension-inner">
				<svg viewBox="0 0 1792 1792" xmlns="http://www.w3.org/2000/svg"><path d="M736 1440q0 12-10 24l-319 319q-10 9-23 9-12 0-23-9l-320-320q-15-16-7-35 8-20 30-20h192v-1376q0-14 9-23t23-9h192q14 0 23 9t9 23v1376h192q14 0 23 9t9 23zm1056 128v192q0 14-9 23t-23 9h-832q-14 0-23-9t-9-23v-192q0-14 9-23t23-9h832q14 0 23 9t9 23zm-192-512v192q0 14-9 23t-23 9h-640q-14 0-23-9t-9-23v-192q0-14 9-23t23-9h640q14 0 23 9t9 23zm-192-512v192q0 14-9 23t-23 9h-448q-14 0-23-9t-9-23v-192q0-14 9-23t23-9h448q14 0 23 9t9 23zm-192-512v192q0 14-9 23t-23 9h-256q-14 0-23-9t-9-23v-192q0-14 9-23t23-9h256q14 0 23 9t9 23z"/></svg>
				<div class="extension-info">
					<h3>Meta Box Builder</h3>
					<p><?php esc_html_e( 'Drag and drop your custom fields into place without a single line of code.', 'meta-box' ); ?></p>
				</div>
			</div>
			<div class="extension-action">
				<a class="button" target="_blank" href="https://metabox.io/plugins/meta-box-builder/?utm_source=dashboard&utm_medium=link&utm_campaign=meta_box"><?php esc_html_e( 'Learn More', 'meta-box' ); ?></a>
			</div>
		</div>
		<div class="extension">
			<div class="extension-inner">
				<svg viewBox="0 0 1792 1792" xmlns="http://www.w3.org/2000/svg"><path d="M512 1248v192q0 40-28 68t-68 28h-320q-40 0-68-28t-28-68v-192q0-40 28-68t68-28h320q40 0 68 28t28 68zm0-512v192q0 40-28 68t-68 28h-320q-40 0-68-28t-28-68v-192q0-40 28-68t68-28h320q40 0 68 28t28 68zm1280 512v192q0 40-28 68t-68 28h-960q-40 0-68-28t-28-68v-192q0-40 28-68t68-28h960q40 0 68 28t28 68zm-1280-1024v192q0 40-28 68t-68 28h-320q-40 0-68-28t-28-68v-192q0-40 28-68t68-28h320q40 0 68 28t28 68zm1280 512v192q0 40-28 68t-68 28h-960q-40 0-68-28t-28-68v-192q0-40 28-68t68-28h960q40 0 68 28t28 68zm0-512v192q0 40-28 68t-68 28h-960q-40 0-68-28t-28-68v-192q0-40 28-68t68-28h960q40 0 68 28t28 68z" /></svg>
				<div class="extension-info">
					<h3>Meta Box Group</h3>
					<p><?php esc_html_e( 'Create repeatable groups of custom fields for better appearance and structure.', 'meta-box' ); ?></p>
				</div>
			</div>
			<div class="extension-action">
				<a class="button" target="_blank" href="https://metabox.io/plugins/meta-box-group/?utm_source=dashboard&utm_medium=link&utm_campaign=meta_box"><?php esc_html_e( 'Learn More', 'meta-box' ); ?></a>
			</div>
		</div>
		<div class="extension">
			<div class="extension-inner">
				<svg viewBox="0 0 1792 1792" xmlns="http://www.w3.org/2000/svg"><path d="M1152 896q0-106-75-181t-181-75-181 75-75 181 75 181 181 75 181-75 75-181zm512-109v222q0 12-8 23t-20 13l-185 28q-19 54-39 91 35 50 107 138 10 12 10 25t-9 23q-27 37-99 108t-94 71q-12 0-26-9l-138-108q-44 23-91 38-16 136-29 186-7 28-36 28h-222q-14 0-24.5-8.5t-11.5-21.5l-28-184q-49-16-90-37l-141 107q-10 9-25 9-14 0-25-11-126-114-165-168-7-10-7-23 0-12 8-23 15-21 51-66.5t54-70.5q-27-50-41-99l-183-27q-13-2-21-12.5t-8-23.5v-222q0-12 8-23t19-13l186-28q14-46 39-92-40-57-107-138-10-12-10-24 0-10 9-23 26-36 98.5-107.5t94.5-71.5q13 0 26 10l138 107q44-23 91-38 16-136 29-186 7-28 36-28h222q14 0 24.5 8.5t11.5 21.5l28 184q49 16 90 37l142-107q9-9 24-9 13 0 25 10 129 119 165 170 7 8 7 22 0 12-8 23-15 21-51 66.5t-54 70.5q26 50 41 98l183 28q13 2 21 12.5t8 23.5z" /></svg>
				<div class="extension-info">
					<h3>MB Settings Page</h3>
					<p><?php esc_html_e( 'Create a powerful settings page for your theme, plugin or website.', 'meta-box' ); ?></p>
				</div>
			</div>
			<div class="extension-action">
				<a class="button" target="_blank" href="https://metabox.io/plugins/mb-settings-page/?utm_source=dashboard&utm_medium=link&utm_campaign=meta_box"><?php esc_html_e( 'Learn More', 'meta-box' ); ?></a>
			</div>
		</div>
		<div class="extension">
			<div class="extension-inner">
				<svg viewBox="0 0 1792 1792" xmlns="http://www.w3.org/2000/svg"><path d="M1792 1184v192q0 13-9.5 22.5t-22.5 9.5h-1376v192q0 13-9.5 22.5t-22.5 9.5q-12 0-24-10l-319-320q-9-9-9-22 0-14 9-23l320-320q9-9 23-9 13 0 22.5 9.5t9.5 22.5v192h1376q13 0 22.5 9.5t9.5 22.5zm0-544q0 14-9 23l-320 320q-9 9-23 9-13 0-22.5-9.5t-9.5-22.5v-192h-1376q-13 0-22.5-9.5t-9.5-22.5v-192q0-13 9.5-22.5t22.5-9.5h1376v-192q0-14 9-23t23-9q12 0 24 10l319 319q9 9 9 23z" /></svg>
				<div class="extension-info">
					<h3>Meta Box Conditional Logic</h3>
					<p><?php esc_html_e( 'Control the visibility of meta boxes and fields or even HTML elements with ease.', 'meta-box' ); ?></p>
				</div>
			</div>
			<div class="extension-action">
				<a class="button" target="_blank" href="https://metabox.io/plugins/meta-box-conditional-logic/?utm_source=dashboard&utm_medium=link&utm_campaign=meta_box"><?php esc_html_e( 'Learn More', 'meta-box' ); ?></a>
			</div>
		</div>
		<div class="extension">
			<div class="extension-inner">
				<svg viewBox="0 0 2048 1792" xmlns="http://www.w3.org/2000/svg"><path d="M657 896q-162 5-265 128h-134q-82 0-138-40.5t-56-118.5q0-353 124-353 6 0 43.5 21t97.5 42.5 119 21.5q67 0 133-23-5 37-5 66 0 139 81 256zm1071 637q0 120-73 189.5t-194 69.5h-874q-121 0-194-69.5t-73-189.5q0-53 3.5-103.5t14-109 26.5-108.5 43-97.5 62-81 85.5-53.5 111.5-20q10 0 43 21.5t73 48 107 48 135 21.5 135-21.5 107-48 73-48 43-21.5q61 0 111.5 20t85.5 53.5 62 81 43 97.5 26.5 108.5 14 109 3.5 103.5zm-1024-1277q0 106-75 181t-181 75-181-75-75-181 75-181 181-75 181 75 75 181zm704 384q0 159-112.5 271.5t-271.5 112.5-271.5-112.5-112.5-271.5 112.5-271.5 271.5-112.5 271.5 112.5 112.5 271.5zm576 225q0 78-56 118.5t-138 40.5h-134q-103-123-265-128 81-117 81-256 0-29-5-66 66 23 133 23 59 0 119-21.5t97.5-42.5 43.5-21q124 0 124 353zm-128-609q0 106-75 181t-181 75-181-75-75-181 75-181 181-75 181 75 75 181z" /></svg>
				<div class="extension-info">
					<h3>MB User Profile</h3>
					<p><?php esc_html_e( 'Create register, login and edit user profile forms in the frontend. Embed everywhere with shortcodes.', 'meta-box' ); ?></p>
				</div>
			</div>
			<div class="extension-action">
				<a class="button" target="_blank" href="https://metabox.io/plugins/mb-user-profile/?utm_source=dashboard&utm_medium=link&utm_campaign=meta_box"><?php esc_html_e( 'Learn More', 'meta-box' ); ?></a>
			</div>
		</div>
		<div class="extension">
			<div class="extension-inner">
				<svg viewBox="0 0 1792 1792" xmlns="http://www.w3.org/2000/svg"><path d="M381 1620q0 80-54.5 126t-135.5 46q-106 0-172-66l57-88q49 45 106 45 29 0 50.5-14.5t21.5-42.5q0-64-105-56l-26-56q8-10 32.5-43.5t42.5-54 37-38.5v-1q-16 0-48.5 1t-48.5 1v53h-106v-152h333v88l-95 115q51 12 81 49t30 88zm2-627v159h-362q-6-36-6-54 0-51 23.5-93t56.5-68 66-47.5 56.5-43.5 23.5-45q0-25-14.5-38.5t-39.5-13.5q-46 0-81 58l-85-59q24-51 71.5-79.5t105.5-28.5q73 0 123 41.5t50 112.5q0 50-34 91.5t-75 64.5-75.5 50.5-35.5 52.5h127v-60h105zm1409 319v192q0 13-9.5 22.5t-22.5 9.5h-1216q-13 0-22.5-9.5t-9.5-22.5v-192q0-14 9-23t23-9h1216q13 0 22.5 9.5t9.5 22.5zm-1408-899v99h-335v-99h107q0-41 .5-121.5t.5-121.5v-12h-2q-8 17-50 54l-71-76 136-127h106v404h108zm1408 387v192q0 13-9.5 22.5t-22.5 9.5h-1216q-13 0-22.5-9.5t-9.5-22.5v-192q0-14 9-23t23-9h1216q13 0 22.5 9.5t9.5 22.5zm0-512v192q0 13-9.5 22.5t-22.5 9.5h-1216q-13 0-22.5-9.5t-9.5-22.5v-192q0-13 9.5-22.5t22.5-9.5h1216q13 0 22.5 9.5t9.5 22.5z" /></svg>
				<div class="extension-info">
					<h3>MB Frontend Submission</h3>
					<p><?php esc_html_e( 'Create frontend forms for users to submit custom content. Embed everywhere with shortcode.', 'meta-box' ); ?></p>
				</div>
			</div>
			<div class="extension-action">
				<a class="button" target="_blank" href="https://metabox.io/plugins/mb-frontend-submission/?utm_source=dashboard&utm_medium=link&utm_campaign=meta_box"><?php esc_html_e( 'Learn More', 'meta-box' ); ?></a>
			</div>
		</div>
		<div class="extension">
			<div class="extension-inner">
				<svg viewBox="0 0 1792 1792" xmlns="http://www.w3.org/2000/svg"><path d="M576 1376v-192q0-14-9-23t-23-9h-320q-14 0-23 9t-9 23v192q0 14 9 23t23 9h320q14 0 23-9t9-23zm0-384v-192q0-14-9-23t-23-9h-320q-14 0-23 9t-9 23v192q0 14 9 23t23 9h320q14 0 23-9t9-23zm512 384v-192q0-14-9-23t-23-9h-320q-14 0-23 9t-9 23v192q0 14 9 23t23 9h320q14 0 23-9t9-23zm-512-768v-192q0-14-9-23t-23-9h-320q-14 0-23 9t-9 23v192q0 14 9 23t23 9h320q14 0 23-9t9-23zm512 384v-192q0-14-9-23t-23-9h-320q-14 0-23 9t-9 23v192q0 14 9 23t23 9h320q14 0 23-9t9-23zm512 384v-192q0-14-9-23t-23-9h-320q-14 0-23 9t-9 23v192q0 14 9 23t23 9h320q14 0 23-9t9-23zm-512-768v-192q0-14-9-23t-23-9h-320q-14 0-23 9t-9 23v192q0 14 9 23t23 9h320q14 0 23-9t9-23zm512 384v-192q0-14-9-23t-23-9h-320q-14 0-23 9t-9 23v192q0 14 9 23t23 9h320q14 0 23-9t9-23zm0-384v-192q0-14-9-23t-23-9h-320q-14 0-23 9t-9 23v192q0 14 9 23t23 9h320q14 0 23-9t9-23zm128-320v1088q0 66-47 113t-113 47h-1344q-66 0-113-47t-47-113v-1088q0-66 47-113t113-47h1344q66 0 113 47t47 113z" /></svg>
				<div class="extension-info">
					<h3>MB Custom Table</h3>
					<p><?php esc_html_e( 'Save custom fields data to custom table. Reduce database size and increase performance.', 'meta-box' ); ?></p>
				</div>
			</div>
			<div class="extension-action">
				<a class="button" target="_blank" href="https://metabox.io/plugins/mb-custom-table/?utm_source=dashboard&utm_medium=link&utm_campaign=meta_box"><?php esc_html_e( 'Learn More', 'meta-box' ); ?></a>
			</div>
		</div>
		<div class="extension">
			<div class="extension-inner">
				<svg viewBox="0 0 1792 1792" xmlns="http://www.w3.org/2000/svg"><path d="M1792 1184v192q0 13-9.5 22.5t-22.5 9.5h-1376v192q0 13-9.5 22.5t-22.5 9.5q-12 0-24-10l-319-320q-9-9-9-22 0-14 9-23l320-320q9-9 23-9 13 0 22.5 9.5t9.5 22.5v192h1376q13 0 22.5 9.5t9.5 22.5zm0-544q0 14-9 23l-320 320q-9 9-23 9-13 0-22.5-9.5t-9.5-22.5v-192h-1376q-13 0-22.5-9.5t-9.5-22.5v-192q0-13 9.5-22.5t22.5-9.5h1376v-192q0-14 9-23t23-9q12 0 24 10l319 319q9 9 9 23z" /></svg>
				<div class="extension-info">
					<h3>MB Relationships</h3>
					<p><?php esc_html_e( 'Create many-to-many relationships between posts, terms and users.', 'meta-box' ); ?></p>
				</div>
			</div>
			<div class="extension-action">
				<a class="button" target="_blank" href="https://metabox.io/plugins/mb-relationships/?utm_source=dashboard&utm_medium=link&utm_campaign=meta_box"><?php esc_html_e( 'Free Download', 'meta-box' ); ?></a>
			</div>
		</div>
	</div>
	<a href="https://metabox.io/plugins/?utm_source=dashboard&utm_medium=link&utm_campaign=meta_box"><?php esc_html_e( 'View all extensions', 'meta-box' ); ?> &rarr;</a>
</div>
