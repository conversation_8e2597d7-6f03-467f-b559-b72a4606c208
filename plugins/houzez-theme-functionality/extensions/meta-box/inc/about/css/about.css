.about-wrap {
	padding-right: 20px;
}
.about-wrap.about-wrap .wp-badge {
	background-image: url(../images/meta-box.svg);
	background-color: #fff;
	color: #222;
	text-transform: uppercase;
	font-weight: bold;
	text-decoration: none;
}

.wp-badge:hover {
	text-decoration: none;
}

.about-buttons .dashicons {
	position: relative;
	top: 5px;
	width: 16px;
	height: 16px;
	font-size: 16px;
}

#poststuff .nav-tab-wrapper {
	padding: 0;
	margin-top: 60px;
}

.nav-tab-active:focus {
	box-shadow: none;
}

.gt-tab-pane {
	display: none;
	padding-top: 24px;
}
.gt-tab-pane .about-description.about-description {
	margin-top: 0;
}

.gt-is-active {
	display: block;
}

.two {
	display: flex;
}
.two .col + .col {
	margin-left: 40px;
}
.two h3 {
	margin-top: 2em;
}
.two h3:first-child {
	margin-top: 0;
}
.two img {
	display: block;
	box-shadow: 0 0 20px rgba(0, 0, 0, .1);
	border-radius: 4px;
}
.screenshot {
	display: block;
	margin: 3em auto;
}
.col {
	flex: 1;
	max-width: 50%;
}
.col ul {
	font-size: 14px;
	margin: 2em 0;
}
.col li a {
	text-decoration: none;
}

/* Extensions tab */
.extensions {
	margin-top: 40px;
	display: flex;
	justify-content: space-between;
	flex-wrap: wrap;
}
.extension {
	width: 100%;
	margin-bottom: 20px;
}
.extension-inner {
	display: flex;
	background: #fff;
	padding: 25px 20px;
}
.extension-inner svg {
	width: 36px;
	height: 36px;
	margin: 8px 15px 0 0;
	fill: #b4b9be;
}
.extension-info {
	flex: 1;
}
.extension-info.extension-info h3 {
	margin: 0 0 5px;
	font-size: 1.2em;
}
.extension-info p {
	margin: 0;
}
.extension-action {
	border-top: 1px solid #ddd;
	text-align: center;
	padding: 20px;
	background: #f7f7f7;
}

/* Extensions tab: 2 columns */
@media (min-width: 768px) {
	.extension {
		width: 49%;
	}
}
/* For large screen: 3 columns */
@media (min-width: 1600px) {
	.extension {
		width: 32%;
	}
}

/* Right column */
#post-body.columns-2 #postbox-container-1 {
	position: fixed;
	right: 320px;
	top: 80px;
}

@media (max-width: 1279px) {
	#postbox-container-1 {
		display: none;
	}
	#poststuff #post-body.columns-2 {
		margin-right: 0;
	}
}

/* Upgrade */
.upgrade {
	border: 3px dashed #82878c;
	background: #fff;
	padding: 15px;
}
.upgrade .dashicons {
	color: #ef4836;
	width: 26px;
	height: 26px;
	font-size: 30px;
	position: relative;
	top: -4px;
	left: -5px;
}
.upgrade h3 {
	margin: 0;
}
.upgrade li {
	padding-left: 20px;
	position: relative;
}
.upgrade svg {
	width: 1em;
	height: 1em;
	fill: #0073aa;
	position: absolute;
	top: 4px;
	left: 0;
}

.youtube-video-container {
	position: relative;
	overflow: hidden;
	width: 100%;
}

.youtube-video-container::after {
	display: block;
	content: "";
	padding-top: 56.25%;
}

.youtube-video-container iframe {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
}
#getting-started .tutorial a {
	font-size: 32px;
	font-weight: 600;
	color:#32373c;
	text-decoration: unset;
}
#getting-started .tutorial a:hover {
	color: #2271b1;
}
.tutorial-items{
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 15px 30px;
	margin-top: 30px;
}
@media (max-width: 767px) {
	.tutorial-items{
		grid-template-columns: repeat(1, 1fr);
		gap: 15px 0px;
	}
}
.tutorial-items a{
	font-size: 20px;
	font-weight: 500;
	color:#32373c;
	text-decoration: unset;
}
.tutorial-items a:hover{
	color:#2271b1;
}
.tutorial-items a span{
	margin-right: 5px;
}
.tutorial-items a p{
	font-size: 14px;
	font-weight: 400;
	color:#32373c;
}
