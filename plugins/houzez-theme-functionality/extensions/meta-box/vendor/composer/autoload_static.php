<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit2ecfb313af70de53ae33703de60ea8aa
{
    public static $prefixLengthsPsr4 = array (
        'M' => 
        array (
            'Meta<PERSON><PERSON>\\' => 8,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'Meta<PERSON><PERSON>\\' => 
        array (
            0 => __DIR__ . '/../..' . '/src',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit2ecfb313af70de53ae33703de60ea8aa::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit2ecfb313af70de53ae33703de60ea8aa::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit2ecfb313af70de53ae33703de60ea8aa::$classMap;

        }, null, ClassLoader::class);
    }
}
