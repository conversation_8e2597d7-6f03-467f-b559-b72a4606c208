/* Styles for 'normal' meta boxes
-------------------------------------------------------------- */

/* Clearfix for field */
.rwmb-field {
	display: flex;
}
.rwmb-field:not(:last-of-type) {
	margin: 0 0 12px;
}
.rwmb-label {
	width: 25%;
}
.rwmb-input {
	flex: 1;
}

.rwmb-label > label {
	font-weight: 600;
}
.rwmb-required {
	color: #dc3232;
	font-weight: bold;
	margin-left: 3px;
}

.rwmb-input h4 {
	margin: 0;
}
.rwmb-input input:not([size]),
.rwmb-input-group,
.rwmb-input select,
.rwmb-input .select2-container,
.rwmb-input textarea:not([cols]) {
	width: 100%;
	box-sizing: border-box;
}
.rwmb-input input[type="checkbox"],
.rwmb-input input[type="radio"] {
	width: 1em;
}
.rwmb-input input[type="button"] {
	width: auto;
}
.rwmb-input input:not([type="checkbox"]):not([type="radio"]),
.rwmb-input textarea,
.rwmb-input select {
	max-width: 100%;
	margin-inline: 0;
}
.rwmb-textarea {
	resize: vertical;
}

/* Clone */
.rwmb-clone {
	min-height: 24px;
	margin-bottom: 12px;
	padding-right: 24px;
	position: relative;
	clear: both;
	background: #fff;
}
.rwmb-clone > input[type='radio'],
.rwmb-clone > input[type='checkbox'] {
	margin: 6px 0 0 4px;
}
.rwmb-button.remove-clone {
	text-decoration: none;
	color: #ccc;
	display: inline-block;
	position: absolute;
	top: 0;
	right: 0;
	width: 20px;
	height: 20px;
	transition: color 200ms;
}
.rwmb-button.remove-clone .dashicons {
	font-size: 20px;
}
.rwmb-button.remove-clone:hover {
	color: #dc3232;
}
.remove-clone:focus {
	outline: 0;
	box-shadow: none;
}
.rwmb-button.add-clone {
	margin-top: 4px;
}
.rwmb-clone-icon {
	cursor: move;
	background: url(../img/drag_icon.gif) no-repeat;
	height: 23px;
	width: 15px;
	vertical-align: top;
	display: inline-block;
	position: absolute;
	left: 0;
	top: 0;
}
.rwmb-sort-clone {
	padding-left: 15px;
}

/* jQuery validation */
p.rwmb-error {
	color: #dc3232;
	margin: 4px 0;
	clear: both;
}
input.rwmb-error.rwmb-error,
textarea.rwmb-error,
select.rwmb-error {
	border-color: #dc3232;
	background: #ffebe8;
}

/* Utilities
-------------------------------------------------------------- */
.rwmb-sortable-placeholder {
	background: #fcf8e3;
	border: 1px solid #faebcc;
	display: block;
}


/* Styles for 'side' meta boxes
-------------------------------------------------------------- */
#side-sortables .rwmb-field {
	flex-direction: column;
}
#side-sortables .rwmb-label {
	width: 100%;
	margin-bottom: 4px;
}

/* Mobile style */
@media (max-width: 782px) {
	.rwmb-field {
		flex-direction: column;
	}
	.rwmb-label {
		width: 100%;
		margin-bottom: 4px;
	}
	.rwmb-input input[type="radio"],
	.rwmb-input input[type="checkbox"] {
		width: 1.5625rem;
	}
}

/* Seamless style
--------------------------------------------------------------*/
.rwmb-seamless {
	background: none;
	border: none;
	box-shadow: none;
}
.rwmb-seamless .inside.inside {
	padding-left: 0;
	padding-right: 0;
}
.postbox.rwmb-seamless .hndle,
.postbox.rwmb-seamless .handlediv,
.postbox.rwmb-seamless .postbox-header {
	display: none;
}
.rwmb-seamless .rwmb-clone {
	background: none;
}

/* CSS fixes
--------------------------------------------------------------*/
/* Fix color picker field is hidden by the post editor at after_title position. https://metabox.io/support/topic/bug-color-picker-field-is-showed-below-the-title-field/ */
.postarea {
	position: relative;
	z-index: 0;
}
.rwmb-hidden-wrapper {
	display: none;
}