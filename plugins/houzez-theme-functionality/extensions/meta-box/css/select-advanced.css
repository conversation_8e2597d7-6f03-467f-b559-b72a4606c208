.rwmb-field .select2-container {
	height: auto;
	min-width: 160px;
	max-width: 100%;
}
.select2-container .select2-selection--single,
.select2-container .select2-selection--multiple,
.select2-dropdown {
	border-color: #7e8993;
}
.select2-container .select2-selection--multiple {
	min-height: 30px;
}
.select2-container--open .select2-dropdown--below {
	border-top: 1px solid #7e8993;
	top: -1px;
}
.select2-container--open .select2-dropdown--above {
	border-bottom: 1px solid #7e8993;
	bottom: -1px;
}
.select2-container .select2-selection--single {
	height: 30px;
}
.select2-container .select2-selection--multiple .select2-selection__rendered {
	display: block;
}
.select2-container--default .select2-selection--single .select2-selection__rendered {
	line-height: 30px;
}
.select2-container--default .select2-selection--single .select2-selection__arrow {
	height: 28px;
}
.select2-results__option {
	margin-bottom: 0;
}
.select2-container .select2-search--inline {
	margin-bottom: 0;
}
.select2-container .select2-search--inline .select2-search__field {
	margin-top: 0;
	min-height: auto;
}
.select2-search--dropdown .select2-search__field {
	padding: 0 4px;
	min-height: 30px;
}

body > .select2-container {
	z-index: 999999;
}

@media (max-width: 782px) {
	.select2-container .select2-selection--single {
		height: 40px;
	}
	.select2-container--default .select2-selection--single .select2-selection__rendered {
		line-height: 40px;
	}
	.select2-container--default .select2-selection--single .select2-selection__arrow {
		height: 38px;
	}
}