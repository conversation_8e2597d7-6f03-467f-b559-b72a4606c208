.rwmb-image-item {
	position: relative;
	float: left;
	margin: 0 12px 12px 0;
	list-style: none;
	box-sizing: border-box;
	cursor: move;
}
.rwmb-image-item .rwmb-file-icon {
	width: 150px;
	height: 150px;
	margin-right: 0;
}

.rwmb-image-actions {
	position: absolute;
	z-index: 2;
	right: 8px;
	top: 8px;
	opacity: 0;
	transition: opacity .2s;
	color: #fff;
}
.rwmb-image-edit,
.rwmb-image-delete {
	color: inherit;
	text-decoration: none;
}
.rwmb-image-actions a:hover {
	color: #fff;
	opacity: .8;
}
.rwmb-image-overlay {
	position: absolute;
	z-index: 1;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	background: #000;
	opacity: 0;
	transition: opacity .2s;
}
.rwmb-image-item:hover .rwmb-image-actions {
	opacity: 1;
}
.rwmb-image-item:hover .rwmb-image-overlay {
	opacity: .6;
}
.rwmb-image-item .rwmb-edit-media:after {
	display: none;
}

@media (max-width: 767px) {
	.rwmb-image-actions {
		opacity: 1;
		z-index: 99;
	}
}