.rwmb-switch-label {
	position: relative;
	display: inline-block;
	background-color: #ccc;
	padding: 2px;
	border-radius: 3px;
	min-width: 40px;
	height: 22px;
	box-sizing: border-box;

	--color: var(--wp-admin-theme-color, #2271b1);
}

.rwmb-switch.rwmb-switch {
	display: none;
}

.rwmb-switch:checked + .rwmb-switch-status .rwmb-switch-slider {
	background-color: var(--color);
	box-shadow: 0 0 1px var(--color);
}

.rwmb-switch:checked + .rwmb-switch-status .rwmb-switch-slider:before {
	left: calc(100% - 20px);
}

.rwmb-switch:checked + .rwmb-switch-status .rwmb-switch-off {
	visibility: hidden;
	display: none;
}

.rwmb-switch:not(:checked) + .rwmb-switch-status .rwmb-switch-on {
	visibility: hidden;
	display: none;
}

.rwmb-switch-slider {
	position: absolute;
	cursor: pointer;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 15;
	transition: .4s;
}

.rwmb-switch-slider:before {
	position: absolute;
	content: attr(title-before) "";
	height: 18px;
	width: 18px;
	left: 2px;
	bottom: 2px;
	z-index: 99;
	background-color: white;
	transition: .4s;
	border-radius: 2px;
}

.rwmb-switch-label--square .rwmb-switch-slider {
	border-radius: 3px;
}

.rwmb-switch-label--rounded,
.rwmb-switch-label--rounded .rwmb-switch-slider {
	border-radius: 34px;
}

.rwmb-switch-label--rounded .rwmb-switch-slider:before {
	border-radius: 50%;
}

.rwmb-switch-on,
.rwmb-switch-off {
	display: inline-block;
	float: left;
	margin: 0 4px;
	color: #fff;
	text-transform: uppercase;
	font-size: 11px;
	position: relative;
	z-index: 20;
	line-height: 18px;
}

.rwmb-switch-on {
	padding-right: 20px;
}

.rwmb-switch-off {
	padding-left: 20px;
}

/* Admin color schemes */
.admin-color-blue .rwmb-switch-label  {
	--color: var(--wp-admin-theme-color, #e1a948);
}
.admin-color-coffee .rwmb-switch-label {
	--color: var(--wp-admin-theme-color, #c7a589);
}
.admin-color-ectoplasm .rwmb-switch-label {
	--color: var(--wp-admin-theme-color, #a3b745);
}
.admin-color-light .rwmb-switch-label {
	--color: var(--wp-admin-theme-color, #04a4cc);
}
.admin-color-midnight .rwmb-switch-label {
	--color: var(--wp-admin-theme-color, #e14d43);
}
.admin-color-modern .rwmb-switch-label {
	--color: var(--wp-admin-theme-color, #3858e9);
}
.admin-color-ocean .rwmb-switch-label {
	--color: var(--wp-admin-theme-color, #9ebaa0);
}
.admin-color-sunrise .rwmb-switch-label {
	--color: var(--wp-admin-theme-color, #dd823b);
}
