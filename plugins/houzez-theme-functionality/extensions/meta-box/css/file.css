.rwmb-files {
	margin: 0;
	overflow: hidden;
}
.rwmb-file {
	display: flex;
	align-items: flex-start;
	margin-bottom: 12px;
	cursor: move;
}
.rwmb-file-icon {
	width: 48px;
	height: 64px;
	margin-right: 8px;
	border: 1px solid rgba(0, 0, 0, .07);
	display: flex;
	align-items: center;
	justify-content: center;
}
.rwmb-file-icon img {
	max-width: 100%;
	max-height: 100%;
	height: auto;
	display: block;
}
.rwmb-file-title {
	font-weight: 600;
	text-decoration: none;
}
.rwmb-file-name {
	margin: 2px 0 6px;
	white-space: nowrap;
}
.rwmb-file-actions {
	font-size: 11px;
}
.rwmb-file-edit,
.rwmb-file-delete {
	color: inherit;
	text-decoration: none;
}
.rwmb-file-edit:after {
	content: "|";
	color: #dcdcde;
	margin: 0 6px;
}
.rwmb-file-delete {
	color: #b32d2e;
}
.rwmb-file-input {
	width: 100%;
}
