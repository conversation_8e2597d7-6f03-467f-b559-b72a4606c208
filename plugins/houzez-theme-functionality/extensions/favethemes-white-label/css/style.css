.white-label-wrap {
    width: 100%;
    max-width: 100% !important;
}

.white-label-wrap .field-wrap {
    width: 50%;
    float: left;
}
.white-label-wrap .field-wrap.full-width {
    width: 100%;
}
.favethemes-media-live-preview, .favethemes-logo-live-preview {
    width: 20%;
    height: 100px;
    float: left;
    margin-right: 20px;
}
.favethemes-media-live-preview img, .favethemes-logo-live-preview img {
    max-width: 100%;
}
.favethemes-upload-field, .favethemes-logo-upload-field {
    float: left;
    width: 76%;
}
.mb-5 {
    margin-bottom: 5px !important;
}
.mb-10 {
    margin-bottom: 10px !important;
}

.label-danger {
    background-color: #c31b1b;
}
.label-grey {
    background-color: #4f5962;
}
.label-success {
    background-color: #85c341;
}
.label-secondary {
    background-color: #54c4d9;
}

.label-green {
    background-color: green;
    color: #fff;
}

.float-none {
    float: none;
}

.fwl-admin-wrapper {
    margin: 10px 20px 0 2px;
}
.admin-houzez-header {
    margin-top: 20px; 
    border-left: 1px solid #ddd;    
    border-right: 1px solid #ddd;    
    border-top: 1px solid #ddd;    
}
.fwl-content {
    border-left: 1px solid #ddd;    
    border-right: 1px solid #ddd;    
    border-bottom: 1px solid #ddd;    
}
.fwl-content h2 {
    margin: 30px 0;
    font-size: 20px;
}
.fwl-content {
    width: 100%;
    padding-top: 1px;
    padding-right: 36px;
    padding-left: 35px;
    padding-bottom: 10px;
    margin-right: auto;
    margin-left: auto;
    box-sizing: border-box;
}
.fwl-content a {
    text-decoration: none;
}
.fwl-content .dashicons {
    vertical-align: baseline;
    font-size: inherit;
    width: auto;
    height: auto;
}

.fwl-content {
    background: #fff;
}   
.fwl-row {
    margin-left: -15px;
    margin-right: -15px;
}
.fwl-row:after, 
.fwl-row:before {
    content: " ";
    display: table;
}
.fwl-row:after {
    clear: both;
}

.fwl-box-wrap {
    position: relative;
    min-height: 1px;
    padding-left: 15px;
    padding-right: 15px;
    box-sizing: border-box;
}
.fwl-box {
    border: 1px solid #ddd; 
    border-radius: 4px;
    margin-bottom: 30px;
}
.fwl-box-header {
    /*border-bottom: 1px solid #ddd;    
    background-color: rgba(0, 0, 0, 0.05); */
    padding: 20px 20px 0;
}
.fwl-box-header .dashicons-before {
    float: left;
    margin: 0 10px 0 0;
    text-align: center;
}
.fwl-box-header h3, .fwl-box-header h1, .fwl-box-header h2 {
    margin: 0;
}
.fwl-box-content {
    padding: 10px 20px 0;
    position: relative;
}
.fwl-required-label,
.fwl-recommended-label {
    display: inline-block;
    padding: 3px 7px;
    border-radius: 4px;
    margin-top: 10px;
}
.fwl-required-label {
    color: #0c5460;
    background-color: #d1ecf1;
    border-color: #bee5eb;
}
.fwl-recommended-label {
    color: #0c5460;
    background-color: #d1ecf1;
    border-color: #bee5eb;
}
.fwl-box-content .actions {
    position: absolute;
    right: 20px;
    top: -18px;
}

.fwl-content .wp-list-table {
    margin-bottom: 25px;
}

.fwl {
    max-width: 500px;
}
.fwl-form label {
    font-weight: bold;
    display: block;
    margin: 10px 0;
}
.fwl-form .submit {
    padding: 10px 0;
}
.fwl-form .field-wrap {
    margin-bottom: 15px;
}
.fwl-form .field-wrap .form-field {
   min-height: 45px;
}
.fwl-form .field-wrap input, .fwl-form .field-wrap select {
    border-style: solid;
    border-width: 1px;
}
.fwl-form .field-wrap .form-field {
    width: 95%;
}
#form-messages .error, .error, .houzez-blocked-user {
    color: red;
}
#form-messages .success, .success, .houzez-approved-user {
    color: green;
}
.houzez-pending-user {
    color: #ffc107;
}