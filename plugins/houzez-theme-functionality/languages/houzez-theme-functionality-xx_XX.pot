# Loco Gettext template
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Houzez Theme - Functionality 1.0.6\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-18 16:38+0500\n"
"PO-Revision-Date: 2023-02-11 02:22+0500\n"
"Last-Translator: \n"
"Language-Team: Favethemes <<EMAIL>>\n"
"Language: en\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"POT-Revision-Date: Sun Jul 24 2016 23:27:11 GMT+0500 (PKT)\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-KeywordsList: "
"_:1;gettext:1;dgettext:2;ngettext:1,2;dngettext:2,3;__:1;_e:1;_c:1;_n:1,2;_n_noop:1,2;_nc:1,2;__ngettext:1,2;__ngettext_noop:1,2;_x:1,2c;_ex:1,2c;_nx:1,2,4c;_nx_noop:1,2,3c;_n_js:1,2;_nx_js:1,2,3c;esc_attr__:1;esc_html__:1;esc_attr_e:1;esc_html_e:1;esc_attr_x:1,2c;esc_html_x:1,2c;comments_number_link:2,3;t:1;st:1;trans:1;transChoice:1,2\n"
"X-Poedit-SearchPath-0: .\n"

#: classes/class-agency-post-type.php:36 classes/class-agency-post-type.php:42
#: classes/class-agency-post-type.php:48 classes/class-menu.php:62
#: classes/class-menu.php:63 classes/class-post-types.php:153
#: classes/class-post-types.php:275 statistics/houzez-statistics.php:62
msgid "Agencies"
msgstr ""

#: classes/class-agency-post-type.php:37 classes/class-image-sizes.php:145
#: classes/class-image-sizes.php:1903
msgid "Agency"
msgstr ""

#: classes/class-agency-post-type.php:38 classes/class-agency-post-type.php:39
msgid "Add New Agency"
msgstr ""

#: classes/class-agency-post-type.php:40
msgid "Edit Agency"
msgstr ""

#: classes/class-agency-post-type.php:41
msgid "New Agency"
msgstr ""

#: classes/class-agency-post-type.php:43
msgid "View Agency"
msgstr ""

#: classes/class-agency-post-type.php:44
msgid "Search Agency"
msgstr ""

#: classes/class-agency-post-type.php:45
msgid "No agencies found"
msgstr ""

#: classes/class-agency-post-type.php:46
msgid "No agencies found in Trash"
msgstr ""

#: classes/class-agency-post-type.php:87
msgid "Agency ID"
msgstr ""

#: classes/class-agency-post-type.php:88
#: classes/class-property-post-type.php:509
#: classes/class-reviews-post-type.php:102
#: classes/class-user-packages-post-type.php:85
#: elementor/traits/Houzez_Testimonials_Traits.php:38
#: elementor/traits/Houzez_Testimonials_Traits.php:162
#: elementor/widgets/advanced-search.php:87
#: elementor/widgets/agents-grid.php:168 elementor/widgets/agents.php:166
#: elementor/widgets/custom-carousel.php:101
#: elementor/widgets/custom-carousel.php:311
#: elementor/widgets/custom-carousel.php:794
#: elementor/widgets/custom-carousel.php:1164
#: elementor/widgets/grid-builder.php:116 elementor/widgets/icon-box.php:150
#: elementor/widgets/icon-box.php:393 elementor/widgets/listings-tabs.php:307
#: elementor/widgets/partners.php:93 elementor/widgets/section-title.php:87
#: elementor/widgets/single-agency/agency-about.php:53
#: elementor/widgets/single-agency/agency-listings-review.php:552
#: elementor/widgets/single-agency/agency-search.php:55
#: elementor/widgets/single-agency/agency-single-stats.php:53
#: elementor/widgets/single-agent/agent-about.php:53
#: elementor/widgets/single-agent/agent-listings-review.php:557
#: elementor/widgets/single-agent/agent-search.php:55
#: elementor/widgets/single-agent/agent-single-stats.php:53
#: elementor/widgets/single-post/post-navigation.php:195
#: elementor/widgets/sort-by.php:86 elementor/widgets/taxonomies-list.php:146
msgid "Title"
msgstr ""

#: classes/class-agency-post-type.php:89
#: elementor/widgets/single-agency/agency-meta.php:55
#: elementor/widgets/single-agency/agency-profile-v1.php:65
#: elementor/widgets/single-agent/agent-meta.php:57
#: elementor/widgets/single-agent/agent-profile-v1.php:77
#: extensions/meta-box/src/Updater/Settings.php:39
msgid "License"
msgstr ""

#: classes/class-agency-post-type.php:90 statistics/houzez-statistics.php:115
#: statistics/houzez-statistics.php:192 statistics/houzez-statistics.php:240
#: statistics/houzez-statistics.php:320
msgid "Thumbnail"
msgstr ""

#: classes/class-agency-post-type.php:91 classes/class-agent-post-type.php:129
msgid "E-mail"
msgstr ""

#: classes/class-agency-post-type.php:92 classes/class-agent-post-type.php:130
msgid "Web"
msgstr ""

#: classes/class-agency-post-type.php:93
#: elementor/template-part/single-agency/contact-form.php:73
#: elementor/template-part/single-agent/contact-form.php:71
#: elementor/widgets/single-agency/agency-contact.php:88
#: elementor/widgets/single-agent/agent-contact.php:88
msgid "Phone"
msgstr ""

#: classes/class-agency-post-type.php:94 classes/class-agent-post-type.php:30
#: classes/class-houzez-init.php:523 classes/class-menu.php:72
#: classes/class-menu.php:73 classes/class-post-types.php:139
#: classes/class-post-types.php:273
#: elementor/template-part/single-agency/agency-listings-review.php:135
#: elementor/widgets/agents.php:36 statistics/houzez-statistics.php:56
msgid "Agents"
msgstr ""

#: classes/class-agency-post-type.php:95
#: elementor/widgets/single-post/author-box.php:472
#: elementor/widgets/single-post/post-info.php:75
msgid "Author"
msgstr ""

#: classes/class-agent-post-type.php:31 classes/class-image-sizes.php:144
#: classes/class-image-sizes.php:1902
#: elementor/traits/Houzez_Property_Cards_Traits.php:1203
#: elementor/widgets/properties-recent-viewed.php:361
#: elementor/widgets/property-cards-v1.php:313
#: elementor/widgets/property-cards-v2.php:289
#: elementor/widgets/property-cards-v4.php:308
#: elementor/widgets/property-cards-v7.php:297
#: elementor/widgets/property-cards-v8.php:283
#: elementor/widgets/property-cards-v8.php:529
#: elementor/widgets/property-carousel-v1.php:288
#: elementor/widgets/property-carousel-v2.php:265
#: elementor/widgets/property-carousel-v7.php:273
msgid "Agent"
msgstr ""

#: classes/class-agent-post-type.php:32 classes/class-invoice-post-type.php:38
#: classes/class-partners-post-type.php:33
#: classes/class-property-post-type.php:213
#: classes/class-reviews-post-type.php:32
#: classes/class-testimonials-post-type.php:35
#: classes/class-user-packages-post-type.php:41
#: classes/class-user-packages-post-type.php:42
msgid "Add New"
msgstr ""

#: classes/class-agent-post-type.php:33
msgid "Add New Agent"
msgstr ""

#: classes/class-agent-post-type.php:34
msgid "Edit Agent"
msgstr ""

#: classes/class-agent-post-type.php:35
msgid "New Agent"
msgstr ""

#: classes/class-agent-post-type.php:36
msgid "View Agent"
msgstr ""

#: classes/class-agent-post-type.php:37
#: classes/class-testimonials-post-type.php:40
msgid "Search Agent"
msgstr ""

#: classes/class-agent-post-type.php:38
msgid "No Agent found"
msgstr ""

#: classes/class-agent-post-type.php:39
msgid "No Agent found in Trash"
msgstr ""

#: classes/class-agent-post-type.php:75 classes/class-houzez-init.php:529
msgid "Categories"
msgstr ""

#: classes/class-agent-post-type.php:76
msgid "Add New Category"
msgstr ""

#: classes/class-agent-post-type.php:77
msgid "New Category"
msgstr ""

#: classes/class-agent-post-type.php:97 classes/class-houzez-init.php:534
#: elementor/widgets/search-builder-old.php:470
#: elementor/widgets/search-builder.php:457 templates/locations/form.php:87
msgid "Cities"
msgstr ""

#: classes/class-agent-post-type.php:98
#: classes/class-property-post-type.php:366
msgid "Add New City"
msgstr ""

#: classes/class-agent-post-type.php:99
#: classes/class-property-post-type.php:367
msgid "New City"
msgstr ""

#: classes/class-agent-post-type.php:125
msgid "Agent ID"
msgstr ""

#: classes/class-agent-post-type.php:126 elementor/widgets/agent-card.php:439
#: elementor/widgets/agents-grid.php:436 elementor/widgets/agents.php:254
#: elementor/widgets/single-agent/agent-name.php:17
#: elementor/widgets/single-agent/agent-name.php:42
msgid "Agent Name"
msgstr ""

#: classes/class-agent-post-type.php:127
msgid "Picture"
msgstr ""

#: classes/class-agent-post-type.php:128 elementor/widgets/agents-grid.php:120
#: elementor/widgets/agents.php:120
#: elementor/widgets/blog-posts-carousel.php:104
#: elementor/widgets/blog-posts-v2.php:98 elementor/widgets/blog-posts.php:112
msgid "Category"
msgstr ""

#: classes/class-agent-post-type.php:131 elementor/widgets/contact-form.php:89
#: elementor/widgets/inquiry-form.php:107
#: elementor/widgets/single-agency/agency-contact.php:100
#: elementor/widgets/single-agent/agent-contact.php:100
msgid "Mobile"
msgstr ""

#: classes/class-api-settings.php:44 classes/class-api-settings.php:162
msgid "Currency Switcher Settings"
msgstr ""

#: classes/class-api-settings.php:45
#: elementor/widgets/header-footer/currency.php:43
msgid "Currency Switcher"
msgstr ""

#: classes/class-api-settings.php:58 classes/class-api-settings.php:83
#: ⁨classes/class-image-sizes copy.php⁩:1374
#: ⁨classes/class-image-sizes copy.php⁩:1416
#: ⁨classes/class-image-sizes copy.php⁩:1445
#: ⁨classes/class-image-sizes copy.php⁩:1526 classes/class-image-sizes.php:2330
#: classes/class-image-sizes.php:2372 classes/class-image-sizes.php:2401
#: classes/class-image-sizes.php:2482
msgid "Security check failed."
msgstr ""

#: classes/class-api-settings.php:63 classes/class-api-settings.php:88
msgid "You do not have permission to perform this action."
msgstr ""

#: classes/class-api-settings.php:74
msgid "API key removed successfully."
msgstr ""

#: classes/class-api-settings.php:94
msgid "API key is required to update rates."
msgstr ""

#: classes/class-api-settings.php:103
msgid "Failed to update rates. Please check your API key and try again."
msgstr ""

#: classes/class-api-settings.php:105
msgid "Exchange rates updated successfully."
msgstr ""

#: classes/class-api-settings.php:134
msgid "Settings saved successfully!"
msgstr ""

#: classes/class-api-settings.php:137
msgid "API key removed successfully!"
msgstr ""

#: classes/class-api-settings.php:140
msgid "Exchange rates updated successfully!"
msgstr ""

#: classes/class-api-settings.php:146
msgid "Invalid API key. Please check your Open Exchange Rates API key."
msgstr ""

#: classes/class-api-settings.php:149
msgid "Error connecting to the exchange rates API. Please try again later."
msgstr ""

#: classes/class-api-settings.php:152
msgid "An error occurred while processing your request."
msgstr ""

#: classes/class-api-settings.php:168
msgid "Update Rates"
msgstr ""

#: classes/class-api-settings.php:172
msgid "Remove API Key"
msgstr ""

#: classes/class-api-settings.php:195 classes/class-api-settings.php:248
msgid "Connected"
msgstr ""

#: classes/class-api-settings.php:195 classes/class-api-settings.php:253
msgid "Not Connected"
msgstr ""

#: classes/class-api-settings.php:196
msgid "API Status"
msgstr ""

#: classes/class-api-settings.php:206
msgid "Exchange Rates"
msgstr ""

#: classes/class-api-settings.php:219
msgid "Never"
msgstr ""

#: classes/class-api-settings.php:222
msgid "Last Update"
msgstr ""

#: classes/class-api-settings.php:231
msgid "Manual"
msgstr ""

#: classes/class-api-settings.php:232 classes/class-api-settings.php:299
msgid "Update Interval"
msgstr ""

#: classes/class-api-settings.php:242
msgid "API Configuration"
msgstr ""

#: classes/class-api-settings.php:261
msgid ""
"Connect to Open Exchange Rates API to enable automatic currency conversion. "
"Get your free API key from openexchangerates.org"
msgstr ""

#: classes/class-api-settings.php:265
msgid ""
"Your API is connected and ready to fetch exchange rates. You can update "
"rates manually or configure automatic updates."
msgstr ""

#: classes/class-api-settings.php:277
msgid "API Key"
msgstr ""

#: classes/class-api-settings.php:286
msgid "Enter your Open Exchange Rates API key"
msgstr ""

#: classes/class-api-settings.php:292
msgid "Get your free API key from openexchangerates.org"
msgstr ""

#: classes/class-api-settings.php:304
msgid "Manual Updates Only"
msgstr ""

#: classes/class-api-settings.php:305
msgid "Every Hour"
msgstr ""

#: classes/class-api-settings.php:306 classes/class-api-settings.php:568
msgid "Daily"
msgstr ""

#: classes/class-api-settings.php:307 classes/class-api-settings.php:569
msgid "Weekly"
msgstr ""

#: classes/class-api-settings.php:308 classes/class-api-settings.php:570
msgid "Biweekly"
msgstr ""

#: classes/class-api-settings.php:309 classes/class-api-settings.php:571
msgid "Monthly"
msgstr ""

#: classes/class-api-settings.php:312
msgid "How often to automatically update exchange rates"
msgstr ""

#: classes/class-api-settings.php:321
msgid "Get API Key"
msgstr ""

#: classes/class-api-settings.php:327 classes/class-post-types.php:58
#: classes/class-taxonomies.php:58
msgid "Save Settings"
msgstr ""

#: classes/class-api-settings.php:340
msgid "How It Works"
msgstr ""

#: classes/class-api-settings.php:351
msgid "API Integration"
msgstr ""

#: classes/class-api-settings.php:352
msgid ""
"Connects to Open Exchange Rates API to fetch real-time currency exchange "
"rates from a trusted financial data provider."
msgstr ""

#: classes/class-api-settings.php:361
msgid "Automatic Updates"
msgstr ""

#: classes/class-api-settings.php:362
msgid ""
"Exchange rates are automatically updated based on your selected frequency "
"using WordPress cron jobs for reliable scheduling."
msgstr ""

#: classes/class-api-settings.php:371
msgid "Local Storage"
msgstr ""

#: classes/class-api-settings.php:372
msgid ""
"Rates are stored locally in your WordPress database for fast access, reduced "
"API calls, and improved performance."
msgstr ""

#: classes/class-api-settings.php:381
msgid "Currency Switching"
msgstr ""

#: classes/class-api-settings.php:382
msgid ""
"Enables visitors to view property prices in their preferred currency with "
"accurate, up-to-date conversion rates."
msgstr ""

#: classes/class-api-settings.php:424
msgid ""
"Are you sure you want to remove the API key? This will disable automatic "
"currency updates."
msgstr ""

#: classes/class-api-settings.php:435 classes/class-api-settings.php:439
msgid "Error removing API key. Please try again."
msgstr ""

#: classes/class-api-settings.php:450
msgid "Updating..."
msgstr ""

#: classes/class-api-settings.php:462 classes/class-api-settings.php:467
msgid "Error updating rates. Please try again."
msgstr ""

#: classes/class-api-settings.php:478 classes/class-permalinks.php:355
#: classes/class-post-types.php:252 classes/class-taxonomies.php:264
msgid "Saving..."
msgstr ""

#: classes/class-api-settings.php:541
msgid "Enter the Open Exchange Rates API key"
msgstr ""

#: classes/class-api-settings.php:544
#, php-format
msgctxt "URL where to get the API key"
msgid "Get yours at: %1s"
msgstr ""

#: classes/class-api-settings.php:567
msgid "Hourly"
msgstr ""

#: classes/class-api-settings.php:576
msgid "Specify the frequency when to update currencies exchange rates"
msgstr ""

#: classes/class-cron.php:52
msgid "Once a Minute"
msgstr ""

#: classes/class-cron.php:56
msgid "Once Weekly"
msgstr ""

#: classes/class-cron.php:60
msgid "Once Biweekly"
msgstr ""

#: classes/class-cron.php:64
msgid "Once Monthly"
msgstr ""

#: classes/class-currencies.php:37 templates/currency/currency-list.php:42
msgid "Currencies Management"
msgstr ""

#: classes/class-currencies.php:42
msgid "Add New Currency"
msgstr ""

#: classes/class-currencies.php:263
msgid "The currency has been added, excellent!"
msgstr ""

#: classes/class-currencies.php:270
msgid "The currency has been updated, excellent!"
msgstr ""

#: classes/class-currencies.php:277 classes/class-fields-builder.php:259
msgid "There has been an error. Bummer!"
msgstr ""

#: classes/class-fields-builder.php:30
msgid "Fields Builder"
msgstr ""

#: classes/class-fields-builder.php:35
msgid "Add New Field"
msgstr ""

#: classes/class-fields-builder.php:133 elementor/widgets/banner-image.php:133
#: elementor/widgets/banner-image.php:451
#: elementor/widgets/contact-form.php:429
#: elementor/widgets/header-footer/menu.php:233
#: elementor/widgets/icon-box.php:157 elementor/widgets/inquiry-form.php:414
#: elementor/widgets/single-agent/agent-position.php:49
#: elementor/widgets/single-post/post-info.php:632
#: elementor/widgets/single-property/section-contact-bottom.php:373
#: elementor/widgets/single-property/section-description.php:165
#: elementor/widgets/taxonomies-list.php:576
msgid "Text"
msgstr ""

#: classes/class-fields-builder.php:134
#: elementor/widgets/single-agency/agency-listings-review.php:136
#: elementor/widgets/single-agency/agency-listings.php:136
#: elementor/widgets/single-agent/agent-listings-review.php:141
#: elementor/widgets/single-agent/agent-listings.php:141
#: functions/functions.php:225
msgid "Number"
msgstr ""

#: classes/class-fields-builder.php:135
msgid "URL/Link"
msgstr ""

#: classes/class-fields-builder.php:136
msgid "Text area"
msgstr ""

#: classes/class-fields-builder.php:137 classes/class-houzez-init.php:347
#: elementor/widgets/search-builder-old.php:283
#: elementor/widgets/search-builder.php:270
#: extensions/meta-box/inc/fields/file-input.php:46
#: extensions/meta-box/inc/fields/select.php:80
msgid "Select"
msgstr ""

#: classes/class-fields-builder.php:138
msgid "Multi Select"
msgstr ""

#: classes/class-fields-builder.php:139
msgid "Checkbox List"
msgstr ""

#: classes/class-fields-builder.php:140
msgid "Radio"
msgstr ""

#: classes/class-fields-builder.php:245
msgid "The field has been added, excellent!"
msgstr ""

#: classes/class-fields-builder.php:252
msgid "The field has been updated, excellent!"
msgstr ""

#: classes/class-fields-builder.php:467
msgid "A field with the name provided already exists"
msgstr ""

#: classes/class-houzez-init.php:342
msgid "Processing, Please wait..."
msgstr ""

#: classes/class-houzez-init.php:343
msgid "Are you sure you want to do this?"
msgstr ""

#: classes/class-houzez-init.php:344 ⁨classes/class-image-sizes copy.php⁩:1197
#: classes/class-image-sizes.php:2150
#: extensions/meta-box/inc/fields/autocomplete.php:14
#: extensions/meta-box/inc/fields/autocomplete.php:79
#: extensions/meta-box/inc/fields/autocomplete.php:91
msgid "Delete"
msgstr ""

#: classes/class-houzez-init.php:345 ⁨classes/class-image-sizes copy.php⁩:449
#: ⁨classes/class-image-sizes copy.php⁩:1063 classes/class-image-sizes.php:968
#: classes/class-image-sizes.php:1039 classes/class-image-sizes.php:1205
#: classes/class-image-sizes.php:1247 classes/class-image-sizes.php:1342
#: classes/class-image-sizes.php:1994 templates/currency/form.php:229
#: templates/fields-builder/fields-form.php:197
msgid "Cancel"
msgstr ""

#: classes/class-houzez-init.php:346
msgid "Confirm"
msgstr ""

#: classes/class-houzez-init.php:348
msgid "Import"
msgstr ""

#: classes/class-houzez-init.php:349 classes/class-import-locations.php:132
msgid "Please map at least one field."
msgstr ""

#: classes/class-houzez-init.php:350
msgid "Error in Importing Data."
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:341
#: ⁨classes/class-image-sizes copy.php⁩:420 classes/class-image-sizes.php:1086
#: classes/class-image-sizes.php:1165
msgid "Please fill in all required fields."
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:411 classes/class-image-sizes.php:1156
#: extensions/favethemes-white-label/template/form.php:38
#: extensions/favethemes-white-label/template/form.php:275
#: extensions/meta-box/src/Updater/Settings.php:92
msgid "Save Changes"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:465 classes/class-image-sizes.php:1256
msgid "Are you sure you want to delete this image size? This cannot be undone."
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:557
#: ⁨classes/class-image-sizes copy.php⁩:558
msgid "Image Sizes"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:673
msgid "Manage the dimensions and cropping for built-in Houzez image sizes."
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:686
#: ⁨classes/class-image-sizes copy.php⁩:852 classes/class-image-sizes.php:1553
msgid "Important:"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:687
#: ⁨classes/class-image-sizes copy.php⁩:853 classes/class-image-sizes.php:1554
msgid ""
"Changes to these settings will only apply to newly uploaded images. For "
"existing images, you will need to regenerate thumbnails."
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:689
#: ⁨classes/class-image-sizes copy.php⁩:855 classes/class-image-sizes.php:1556
msgid "Install Force Regenerate Thumbnails plugin"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:848
msgid "Houzez Image Sizes"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:862 classes/class-image-sizes.php:1789
msgid "Layout Image Assignments"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:865 classes/class-image-sizes.php:1792
msgid "Manage Image Sizes"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:873 classes/class-image-sizes.php:1800
msgid ""
"Assign specific image sizes to different areas of your website. This "
"controls which image dimensions are used in each layout element."
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:883 classes/class-image-sizes.php:1810
msgid "Property Listings"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:885 classes/class-image-sizes.php:1812
msgid "Control image sizes for property listings across different views."
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:890 classes/class-image-sizes.php:124
#: classes/class-image-sizes.php:1817
msgid "Listing Grid v1"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:891 classes/class-image-sizes.php:125
#: classes/class-image-sizes.php:1818
msgid "Listing Grid v2"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:892 classes/class-image-sizes.php:126
#: classes/class-image-sizes.php:1819
msgid "Listing Grid v3"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:893 classes/class-image-sizes.php:127
#: classes/class-image-sizes.php:1820
msgid "Listing Grid v4"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:894 classes/class-image-sizes.php:128
#: classes/class-image-sizes.php:1821
msgid "Listing Grid v5"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:895 classes/class-image-sizes.php:129
#: classes/class-image-sizes.php:1822
msgid "Listing Grid v6"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:896 classes/class-image-sizes.php:130
#: classes/class-image-sizes.php:1823
msgid "Listing Grid v7"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:897 classes/class-image-sizes.php:131
#: classes/class-image-sizes.php:1824
msgid "Listing List v1"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:898 classes/class-image-sizes.php:132
#: classes/class-image-sizes.php:1825
msgid "Listing List v2"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:899 classes/class-image-sizes.php:133
#: classes/class-image-sizes.php:1826
msgid "Listing List v4"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:900 classes/class-image-sizes.php:134
#: classes/class-image-sizes.php:1827
msgid "Listing List v7"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:928 classes/class-image-sizes.php:1855
msgid "Property Detail Pages"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:930 classes/class-image-sizes.php:1857
msgid "Control image sizes for property detail pages and sliders."
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:935
msgid "Property Slider"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:936 classes/class-image-sizes.php:136
#: classes/class-image-sizes.php:1862
msgid "Property Detail v1"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:937 classes/class-image-sizes.php:137
#: classes/class-image-sizes.php:1863
msgid "Property Detail v2"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:938
msgid "Property Detail v3"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:939
msgid "Property Detail v4"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:940 classes/class-image-sizes.php:139
#: classes/class-image-sizes.php:1865
msgid "Property Detail v5"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:967
msgid "Profiles & Blog"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:969
msgid "Control image sizes for agent/agency profiles and blog posts."
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:974
msgid "Agent Profile"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:975
msgid "Agency Profile"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:976 classes/class-image-sizes.php:146
#: classes/class-image-sizes.php:1904
msgid "Blog Post"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1000 classes/class-image-sizes.php:1929
msgid "Save Assignments"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1002 classes/class-image-sizes.php:1931
msgid ""
"Tip: You can create custom image sizes in the \"Manage Image Sizes\" tab if "
"you need specific dimensions."
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1018 classes/class-image-sizes.php:1947
msgid "Add New Image Size"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1027 classes/class-image-sizes.php:1956
msgid "Create New Custom Image Size"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1030
#: ⁨classes/class-image-sizes copy.php⁩:1073
#: ⁨classes/class-image-sizes copy.php⁩:1231 classes/class-image-sizes.php:1959
#: classes/class-image-sizes.php:2004 classes/class-image-sizes.php:2178
#: classes/class-property-post-type.php:893
#: classes/class-property-post-type.php:932
#: classes/class-property-post-type.php:973
#: elementor/traits/Houzez_Filters_Traits.php:471
#: elementor/traits/Houzez_Testimonials_Traits.php:115
#: elementor/widgets/contact-form.php:270
#: elementor/widgets/contact-form.php:271 elementor/widgets/grids.php:278
#: elementor/widgets/single-agency/agency-about.php:62
#: elementor/widgets/single-agent/agent-about.php:62
#: elementor/widgets/single-post/author-box.php:480
#: elementor/widgets/team-member.php:95 elementor/widgets/team-member.php:261
msgid "Name"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1033 classes/class-image-sizes.php:1962
msgid "A descriptive name for this image size"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1037
#: ⁨classes/class-image-sizes copy.php⁩:1075
#: ⁨classes/class-image-sizes copy.php⁩:1237 classes/class-image-sizes.php:1966
#: classes/class-image-sizes.php:2006 classes/class-image-sizes.php:2184
#: elementor/traits/Houzez_Style_Traits.php:174
#: elementor/widgets/agent-card.php:359 elementor/widgets/banner-image.php:181
#: elementor/widgets/custom-carousel.php:466
#: elementor/widgets/header-footer/menu.php:649
#: elementor/widgets/header-footer/site-logo.php:173
#: elementor/widgets/single-post/post-info.php:494
#: elementor/widgets/single-property/featured-image.php:125
#: elementor/widgets/taxonomies-list.php:290
msgid "Width"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1040 classes/class-image-sizes.php:1969
msgid "Width in pixels"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1044
#: ⁨classes/class-image-sizes copy.php⁩:1076
#: ⁨classes/class-image-sizes copy.php⁩:1243 classes/class-image-sizes.php:1973
#: classes/class-image-sizes.php:2007 classes/class-image-sizes.php:2190
#: elementor/traits/Houzez_Style_Traits.php:244
#: elementor/widgets/agent-card.php:381 elementor/widgets/banner-image.php:251
#: elementor/widgets/custom-carousel.php:536
#: elementor/widgets/grid-builder.php:446
#: elementor/widgets/header-footer/menu.php:668
#: elementor/widgets/single-post/post-info.php:529
#: elementor/widgets/single-property/images-gallery-v1.php:63
#: elementor/widgets/taxonomies-grids.php:196
msgid "Height"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1047 classes/class-image-sizes.php:1976
msgid "Height in pixels"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1051
#: ⁨classes/class-image-sizes copy.php⁩:1249 classes/class-image-sizes.php:1980
#: classes/class-image-sizes.php:2196
msgid "Hard Crop?"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1054 classes/class-image-sizes.php:1984
msgid "Whether to crop the image to exact dimensions"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1060 classes/class-image-sizes.php:1991
msgid "Add Custom Image Size"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1074 classes/class-image-sizes.php:2005
#: classes/class-property-post-type.php:896
#: classes/class-property-post-type.php:935
#: classes/class-property-post-type.php:976
msgid "Slug"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1077 classes/class-image-sizes.php:2008
msgid "Crop"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1078 classes/class-image-sizes.php:2009
#: classes/class-post-types.php:142 classes/class-post-types.php:156
#: classes/class-post-types.php:170 classes/class-post-types.php:184
#: classes/class-post-types.php:198 classes/class-post-types.php:212
#: classes/class-post-types.php:226 classes/class-post-types.php:338
#: classes/class-post-types.php:357 classes/class-post-types.php:375
#: classes/class-post-types.php:393 classes/class-post-types.php:411
#: classes/class-post-types.php:429 classes/class-post-types.php:447
#: classes/class-taxonomies.php:142 classes/class-taxonomies.php:156
#: classes/class-taxonomies.php:170 classes/class-taxonomies.php:184
#: classes/class-taxonomies.php:341 classes/class-taxonomies.php:359
#: classes/class-taxonomies.php:377 classes/class-taxonomies.php:395
msgid "Enabled"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1079 classes/class-image-sizes.php:2010
#: classes/class-invoice-post-type.php:94
#: classes/class-property-post-type.php:516
#: classes/class-reviews-post-type.php:105 statistics/houzez-statistics.php:122
#: statistics/houzez-statistics.php:199 statistics/houzez-statistics.php:247
#: statistics/houzez-statistics.php:327 statistics/houzez-statistics.php:355
#: statistics/houzez-statistics.php:558 templates/currency/currency-list.php:81
#: templates/fields-builder/index.php:61
msgid "Actions"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1086 classes/class-image-sizes.php:2087
msgid "WordPress Core Sizes"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1087 classes/class-image-sizes.php:2088
msgid ""
"These sizes are defined by WordPress core. You can disable generation but "
"dimensions must be changed in Media Settings."
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1096
#: ⁨classes/class-image-sizes copy.php⁩:1182 classes/class-image-sizes.php:2097
#: classes/class-image-sizes.php:2135 classes/class-property-post-type.php:628
#: classes/class-property-post-type.php:1587
#: elementor/traits/Houzez_Filters_Traits.php:430
#: elementor/traits/Houzez_Filters_Traits.php:444
#: elementor/traits/Houzez_Filters_Traits.php:458
#: elementor/traits/Houzez_Property_Cards_Traits.php:421
#: elementor/traits/Houzez_Property_Cards_Traits.php:435
#: elementor/traits/Houzez_Property_Cards_Traits.php:459
#: elementor/traits/Houzez_Property_Cards_Traits.php:473
#: elementor/traits/Houzez_Property_Cards_Traits.php:1310
#: elementor/traits/Houzez_Property_Cards_Traits.php:1345
#: elementor/traits/Houzez_Property_Cards_Traits.php:1360
#: elementor/traits/Houzez_Property_Cards_Traits.php:1375
#: elementor/traits/Houzez_Property_Cards_Traits.php:1390
#: elementor/traits/Houzez_Property_Cards_Traits.php:1405
#: elementor/traits/Houzez_Property_Cards_Traits.php:1420
#: elementor/traits/Houzez_Property_Cards_Traits.php:1435
#: elementor/traits/Houzez_Property_Cards_Traits.php:1467
#: elementor/traits/Houzez_Property_Cards_Traits.php:2118
#: elementor/traits/Houzez_Property_Cards_Traits.php:2134
#: elementor/traits/Houzez_Property_Cards_Traits.php:2150
#: elementor/traits/Houzez_Property_Cards_Traits.php:2166
#: elementor/traits/Houzez_Property_Cards_Traits.php:2182
#: elementor/traits/Houzez_Property_Cards_Traits.php:2198
#: elementor/traits/Houzez_Style_Traits.php:109
#: elementor/widgets/agent-card.php:148 elementor/widgets/agent-card.php:159
#: elementor/widgets/agent-card.php:176 elementor/widgets/agent-card.php:187
#: elementor/widgets/agent-card.php:198 elementor/widgets/agent-card.php:219
#: elementor/widgets/agent-card.php:229 elementor/widgets/agent-card.php:240
#: elementor/widgets/agent-card.php:251 elementor/widgets/agents-grid.php:207
#: elementor/widgets/agents-grid.php:222 elementor/widgets/agents-grid.php:237
#: elementor/widgets/agents-grid.php:252 elementor/widgets/agents-grid.php:267
#: elementor/widgets/agents-grid.php:357
#: elementor/widgets/blog-posts-carousel.php:206
#: elementor/widgets/blog-posts-carousel.php:220
#: elementor/widgets/blog-posts-carousel.php:244
#: elementor/widgets/blog-posts-carousel.php:258
#: elementor/widgets/contact-form.php:612
#: elementor/widgets/create-listing-btn.php:196
#: elementor/widgets/custom-carousel.php:197
#: elementor/widgets/custom-carousel.php:211
#: elementor/widgets/custom-carousel.php:235
#: elementor/widgets/custom-carousel.php:249
#: elementor/widgets/google-map.php:167 elementor/widgets/google-map.php:182
#: elementor/widgets/google-map.php:312 elementor/widgets/grid-builder.php:340
#: elementor/widgets/grids.php:237 elementor/widgets/grids.php:251
#: elementor/widgets/grids.php:265
#: elementor/widgets/header-footer/create-listing-btn.php:228
#: elementor/widgets/header-footer/login-modal.php:81
#: elementor/widgets/header-footer/login-modal.php:95
#: elementor/widgets/header-footer/site-logo.php:467
#: elementor/widgets/inquiry-form.php:596 elementor/widgets/login-modal.php:68
#: elementor/widgets/login-modal.php:82 elementor/widgets/mapbox.php:151
#: elementor/widgets/mapbox.php:166 elementor/widgets/mapbox.php:296
#: elementor/widgets/open-street-map.php:121
#: elementor/widgets/open-street-map.php:136
#: elementor/widgets/open-street-map.php:302
#: elementor/widgets/properties-grids.php:134
#: elementor/widgets/properties-recent-viewed.php:155
#: elementor/widgets/properties-recent-viewed.php:170
#: elementor/widgets/properties-recent-viewed.php:185
#: elementor/widgets/properties-recent-viewed.php:200
#: elementor/widgets/properties-recent-viewed.php:215
#: elementor/widgets/properties-recent-viewed.php:230
#: elementor/widgets/properties-recent-viewed.php:245
#: elementor/widgets/properties-recent-viewed.php:257
#: elementor/widgets/property-cards-v1.php:170
#: elementor/widgets/property-cards-v1.php:185
#: elementor/widgets/property-cards-v1.php:197
#: elementor/widgets/property-cards-v2.php:166
#: elementor/widgets/property-cards-v2.php:181
#: elementor/widgets/property-cards-v4.php:165
#: elementor/widgets/property-cards-v4.php:180
#: elementor/widgets/property-cards-v4.php:192
#: elementor/widgets/property-cards-v7.php:169
#: elementor/widgets/property-cards-v7.php:187
#: elementor/widgets/property-cards-v8.php:150
#: elementor/widgets/property-cards-v8.php:167
#: elementor/widgets/property-carousel-v1.php:145
#: elementor/widgets/property-carousel-v1.php:160
#: elementor/widgets/property-carousel-v1.php:172
#: elementor/widgets/property-carousel-v2.php:142
#: elementor/widgets/property-carousel-v2.php:157
#: elementor/widgets/property-carousel-v7.php:145
#: elementor/widgets/property-carousel-v7.php:163
#: elementor/widgets/search-builder.php:661
#: elementor/widgets/search-builder.php:774
#: elementor/widgets/single-agency/agency-call-btn.php:62
#: elementor/widgets/single-agency/agency-contact-form.php:57
#: elementor/widgets/single-agency/agency-meta.php:161
#: elementor/widgets/single-agency/agency-name.php:72
#: elementor/widgets/single-agency/agency-profile-v1.php:115
#: elementor/widgets/single-agent/agent-call-btn.php:62
#: elementor/widgets/single-agent/agent-contact-form.php:57
#: elementor/widgets/single-agent/agent-meta.php:163
#: elementor/widgets/single-agent/agent-name.php:72
#: elementor/widgets/single-agent/agent-profile-v1.php:127
#: elementor/widgets/single-property/featured-image.php:62
#: elementor/widgets/single-property/images-gallery-v1.php:88
#: elementor/widgets/single-property/images-gallery-v1.php:111
#: elementor/widgets/single-property/images-gallery-v1.php:123
#: elementor/widgets/single-property/images-gallery-v1.php:135
#: elementor/widgets/single-property/images-gallery-v1.php:158
#: elementor/widgets/single-property/images-gallery-v2.php:81
#: elementor/widgets/single-property/images-gallery-v2.php:104
#: elementor/widgets/single-property/images-gallery-v2.php:116
#: elementor/widgets/single-property/images-gallery-v2.php:128
#: elementor/widgets/single-property/images-gallery-v2.php:151
#: elementor/widgets/single-property/images-gallery-v3.php:64
#: elementor/widgets/single-property/images-gallery-v3.php:87
#: elementor/widgets/single-property/images-gallery-v3.php:99
#: elementor/widgets/single-property/images-gallery-v3.php:111
#: elementor/widgets/single-property/images-gallery-v3.php:134
#: elementor/widgets/single-property/item-tools.php:54
#: elementor/widgets/single-property/item-tools.php:66
#: elementor/widgets/single-property/item-tools.php:78
#: elementor/widgets/single-property/property-address.php:95
#: elementor/widgets/single-property/property-price.php:54
#: elementor/widgets/single-property/property-title-area.php:141
#: elementor/widgets/single-property/property-title-area.php:206
#: elementor/widgets/single-property/property-title-area.php:303
#: elementor/widgets/single-property/property-title-area.php:326
#: elementor/widgets/single-property/property-title-area.php:353
#: elementor/widgets/single-property/property-title-area.php:408
#: elementor/widgets/single-property/property-title-area.php:535
#: elementor/widgets/single-property/property-title-area.php:547
#: elementor/widgets/single-property/property-title-area.php:559
#: elementor/widgets/single-property/property-title.php:85
#: elementor/widgets/single-property/section-attachments.php:111
#: elementor/widgets/single-property/section-contact-2.php:57
#: elementor/widgets/single-property/section-contact-2.php:109
#: elementor/widgets/single-property/section-contact-2.php:121
#: elementor/widgets/single-property/section-contact-bottom.php:567
#: elementor/widgets/single-property/section-floorplan.php:199
#: elementor/widgets/single-property/section-overview.php:286
#: elementor/widgets/single-property/section-schedule-tour.php:208
#: elementor/widgets/single-property/section-sublistings.php:90
#: elementor/widgets/single-property/section-toparea-v1.php:85
#: elementor/widgets/single-property/section-toparea-v1.php:108
#: elementor/widgets/single-property/section-toparea-v1.php:205
#: elementor/widgets/single-property/section-toparea-v1.php:228
#: elementor/widgets/single-property/section-toparea-v1.php:255
#: elementor/widgets/single-property/section-toparea-v1.php:310
#: elementor/widgets/single-property/section-toparea-v1.php:437
#: elementor/widgets/single-property/section-toparea-v1.php:449
#: elementor/widgets/single-property/section-toparea-v1.php:461
#: elementor/widgets/single-property/section-toparea-v1.php:556
#: elementor/widgets/single-property/section-toparea-v1.php:568
#: elementor/widgets/single-property/section-toparea-v1.php:580
#: elementor/widgets/single-property/section-toparea-v1.php:592
#: elementor/widgets/single-property/section-toparea-v1.php:615
#: elementor/widgets/single-property/section-toparea-v1.php:649
#: elementor/widgets/single-property/section-toparea-v2.php:83
#: elementor/widgets/single-property/section-toparea-v2.php:131
#: elementor/widgets/single-property/section-toparea-v2.php:228
#: elementor/widgets/single-property/section-toparea-v2.php:251
#: elementor/widgets/single-property/section-toparea-v2.php:278
#: elementor/widgets/single-property/section-toparea-v2.php:333
#: elementor/widgets/single-property/section-toparea-v2.php:459
#: elementor/widgets/single-property/section-toparea-v2.php:471
#: elementor/widgets/single-property/section-toparea-v2.php:483
#: elementor/widgets/single-property/section-toparea-v2.php:578
#: elementor/widgets/single-property/section-toparea-v2.php:590
#: elementor/widgets/single-property/section-toparea-v2.php:602
#: elementor/widgets/single-property/section-toparea-v2.php:614
#: elementor/widgets/single-property/section-toparea-v2.php:637
#: elementor/widgets/single-property/section-toparea-v3.php:108
#: elementor/widgets/single-property/section-toparea-v3.php:133
#: elementor/widgets/single-property/section-toparea-v3.php:230
#: elementor/widgets/single-property/section-toparea-v3.php:253
#: elementor/widgets/single-property/section-toparea-v3.php:280
#: elementor/widgets/single-property/section-toparea-v3.php:335
#: elementor/widgets/single-property/section-toparea-v3.php:461
#: elementor/widgets/single-property/section-toparea-v3.php:473
#: elementor/widgets/single-property/section-toparea-v3.php:485
#: elementor/widgets/single-property/section-toparea-v3.php:580
#: elementor/widgets/single-property/section-toparea-v3.php:592
#: elementor/widgets/single-property/section-toparea-v3.php:604
#: elementor/widgets/single-property/section-toparea-v3.php:616
#: elementor/widgets/single-property/section-toparea-v3.php:639
#: elementor/widgets/single-property/section-toparea-v5.php:83
#: elementor/widgets/single-property/section-toparea-v5.php:108
#: elementor/widgets/single-property/section-toparea-v5.php:205
#: elementor/widgets/single-property/section-toparea-v5.php:228
#: elementor/widgets/single-property/section-toparea-v5.php:255
#: elementor/widgets/single-property/section-toparea-v5.php:310
#: elementor/widgets/single-property/section-toparea-v5.php:436
#: elementor/widgets/single-property/section-toparea-v5.php:448
#: elementor/widgets/single-property/section-toparea-v5.php:460
#: elementor/widgets/single-property/section-toparea-v5.php:555
#: elementor/widgets/single-property/section-toparea-v5.php:567
#: elementor/widgets/single-property/section-toparea-v5.php:579
#: elementor/widgets/single-property/section-toparea-v5.php:591
#: elementor/widgets/single-property/section-toparea-v5.php:614
#: elementor/widgets/single-property/section-toparea-v6.php:128
#: elementor/widgets/single-property/section-toparea-v6.php:153
#: elementor/widgets/single-property/section-toparea-v6.php:250
#: elementor/widgets/single-property/section-toparea-v6.php:273
#: elementor/widgets/single-property/section-toparea-v6.php:300
#: elementor/widgets/single-property/section-toparea-v6.php:355
#: elementor/widgets/single-property/section-toparea-v6.php:481
#: elementor/widgets/single-property/section-toparea-v6.php:493
#: elementor/widgets/single-property/section-toparea-v6.php:505
#: elementor/widgets/single-property/section-toparea-v7.php:103
#: elementor/widgets/single-property/section-toparea-v7.php:128
#: elementor/widgets/single-property/section-toparea-v7.php:225
#: elementor/widgets/single-property/section-toparea-v7.php:248
#: elementor/widgets/single-property/section-toparea-v7.php:275
#: elementor/widgets/single-property/section-toparea-v7.php:330
#: elementor/widgets/single-property/section-toparea-v7.php:456
#: elementor/widgets/single-property/section-toparea-v7.php:468
#: elementor/widgets/single-property/section-toparea-v7.php:480
#: elementor/widgets/taxonomies-cards-carousel.php:180
#: elementor/widgets/taxonomies-cards-carousel.php:194
#: elementor/widgets/taxonomies-cards-carousel.php:218
#: elementor/widgets/taxonomies-cards-carousel.php:232
#: elementor/widgets/taxonomies-grids-carousel.php:165
#: elementor/widgets/taxonomies-grids-carousel.php:179
#: elementor/widgets/taxonomies-grids-carousel.php:203
#: elementor/widgets/taxonomies-grids-carousel.php:217
#: extensions/meta-box/inc/fields/checkbox.php:43
#: templates/fields-builder/fields-form.php:155
msgid "Yes"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1096
#: ⁨classes/class-image-sizes copy.php⁩:1182 classes/class-image-sizes.php:2097
#: classes/class-image-sizes.php:2135 classes/class-property-post-type.php:626
#: elementor/traits/Houzez_Filters_Traits.php:429
#: elementor/traits/Houzez_Filters_Traits.php:443
#: elementor/traits/Houzez_Filters_Traits.php:457
#: elementor/traits/Houzez_Property_Cards_Traits.php:422
#: elementor/traits/Houzez_Property_Cards_Traits.php:434
#: elementor/traits/Houzez_Property_Cards_Traits.php:458
#: elementor/traits/Houzez_Property_Cards_Traits.php:472
#: elementor/traits/Houzez_Property_Cards_Traits.php:1311
#: elementor/traits/Houzez_Property_Cards_Traits.php:1346
#: elementor/traits/Houzez_Property_Cards_Traits.php:1361
#: elementor/traits/Houzez_Property_Cards_Traits.php:1376
#: elementor/traits/Houzez_Property_Cards_Traits.php:1391
#: elementor/traits/Houzez_Property_Cards_Traits.php:1406
#: elementor/traits/Houzez_Property_Cards_Traits.php:1421
#: elementor/traits/Houzez_Property_Cards_Traits.php:1436
#: elementor/traits/Houzez_Property_Cards_Traits.php:1468
#: elementor/traits/Houzez_Property_Cards_Traits.php:2119
#: elementor/traits/Houzez_Property_Cards_Traits.php:2135
#: elementor/traits/Houzez_Property_Cards_Traits.php:2151
#: elementor/traits/Houzez_Property_Cards_Traits.php:2167
#: elementor/traits/Houzez_Property_Cards_Traits.php:2183
#: elementor/traits/Houzez_Property_Cards_Traits.php:2199
#: elementor/traits/Houzez_Style_Traits.php:110
#: elementor/widgets/agent-card.php:149 elementor/widgets/agent-card.php:160
#: elementor/widgets/agent-card.php:177 elementor/widgets/agent-card.php:188
#: elementor/widgets/agent-card.php:199 elementor/widgets/agent-card.php:220
#: elementor/widgets/agent-card.php:230 elementor/widgets/agent-card.php:241
#: elementor/widgets/agent-card.php:252 elementor/widgets/agents-grid.php:208
#: elementor/widgets/agents-grid.php:223 elementor/widgets/agents-grid.php:238
#: elementor/widgets/agents-grid.php:253 elementor/widgets/agents-grid.php:268
#: elementor/widgets/agents-grid.php:358
#: elementor/widgets/blog-posts-carousel.php:207
#: elementor/widgets/blog-posts-carousel.php:219
#: elementor/widgets/blog-posts-carousel.php:243
#: elementor/widgets/blog-posts-carousel.php:257
#: elementor/widgets/contact-form.php:613
#: elementor/widgets/create-listing-btn.php:197
#: elementor/widgets/custom-carousel.php:198
#: elementor/widgets/custom-carousel.php:210
#: elementor/widgets/custom-carousel.php:234
#: elementor/widgets/custom-carousel.php:248
#: elementor/widgets/google-map.php:168 elementor/widgets/google-map.php:183
#: elementor/widgets/google-map.php:313 elementor/widgets/grid-builder.php:341
#: elementor/widgets/grids.php:236 elementor/widgets/grids.php:250
#: elementor/widgets/grids.php:264
#: elementor/widgets/header-footer/create-listing-btn.php:229
#: elementor/widgets/header-footer/login-modal.php:80
#: elementor/widgets/header-footer/login-modal.php:94
#: elementor/widgets/header-footer/site-logo.php:466
#: elementor/widgets/inquiry-form.php:597 elementor/widgets/login-modal.php:67
#: elementor/widgets/login-modal.php:81 elementor/widgets/mapbox.php:152
#: elementor/widgets/mapbox.php:167 elementor/widgets/mapbox.php:297
#: elementor/widgets/open-street-map.php:122
#: elementor/widgets/open-street-map.php:137
#: elementor/widgets/open-street-map.php:303
#: elementor/widgets/properties-grids.php:135
#: elementor/widgets/properties-recent-viewed.php:156
#: elementor/widgets/properties-recent-viewed.php:171
#: elementor/widgets/properties-recent-viewed.php:186
#: elementor/widgets/properties-recent-viewed.php:201
#: elementor/widgets/properties-recent-viewed.php:216
#: elementor/widgets/properties-recent-viewed.php:231
#: elementor/widgets/properties-recent-viewed.php:246
#: elementor/widgets/properties-recent-viewed.php:258
#: elementor/widgets/property-cards-v1.php:171
#: elementor/widgets/property-cards-v1.php:186
#: elementor/widgets/property-cards-v1.php:198
#: elementor/widgets/property-cards-v2.php:167
#: elementor/widgets/property-cards-v2.php:182
#: elementor/widgets/property-cards-v4.php:166
#: elementor/widgets/property-cards-v4.php:181
#: elementor/widgets/property-cards-v4.php:193
#: elementor/widgets/property-cards-v7.php:170
#: elementor/widgets/property-cards-v7.php:188
#: elementor/widgets/property-cards-v8.php:151
#: elementor/widgets/property-cards-v8.php:168
#: elementor/widgets/property-carousel-v1.php:146
#: elementor/widgets/property-carousel-v1.php:161
#: elementor/widgets/property-carousel-v1.php:173
#: elementor/widgets/property-carousel-v2.php:143
#: elementor/widgets/property-carousel-v2.php:158
#: elementor/widgets/property-carousel-v7.php:146
#: elementor/widgets/property-carousel-v7.php:164
#: elementor/widgets/search-builder.php:662
#: elementor/widgets/search-builder.php:775
#: elementor/widgets/single-agency/agency-call-btn.php:63
#: elementor/widgets/single-agency/agency-contact-form.php:58
#: elementor/widgets/single-agency/agency-meta.php:162
#: elementor/widgets/single-agency/agency-name.php:73
#: elementor/widgets/single-agency/agency-profile-v1.php:116
#: elementor/widgets/single-agent/agent-call-btn.php:63
#: elementor/widgets/single-agent/agent-contact-form.php:58
#: elementor/widgets/single-agent/agent-meta.php:164
#: elementor/widgets/single-agent/agent-name.php:73
#: elementor/widgets/single-agent/agent-profile-v1.php:128
#: elementor/widgets/single-property/featured-image.php:63
#: elementor/widgets/single-property/images-gallery-v1.php:89
#: elementor/widgets/single-property/images-gallery-v1.php:112
#: elementor/widgets/single-property/images-gallery-v1.php:124
#: elementor/widgets/single-property/images-gallery-v1.php:136
#: elementor/widgets/single-property/images-gallery-v1.php:159
#: elementor/widgets/single-property/images-gallery-v2.php:82
#: elementor/widgets/single-property/images-gallery-v2.php:105
#: elementor/widgets/single-property/images-gallery-v2.php:117
#: elementor/widgets/single-property/images-gallery-v2.php:129
#: elementor/widgets/single-property/images-gallery-v2.php:152
#: elementor/widgets/single-property/images-gallery-v3.php:65
#: elementor/widgets/single-property/images-gallery-v3.php:88
#: elementor/widgets/single-property/images-gallery-v3.php:100
#: elementor/widgets/single-property/images-gallery-v3.php:112
#: elementor/widgets/single-property/images-gallery-v3.php:135
#: elementor/widgets/single-property/item-tools.php:55
#: elementor/widgets/single-property/item-tools.php:67
#: elementor/widgets/single-property/item-tools.php:79
#: elementor/widgets/single-property/property-address.php:96
#: elementor/widgets/single-property/property-price.php:55
#: elementor/widgets/single-property/property-title-area.php:142
#: elementor/widgets/single-property/property-title-area.php:207
#: elementor/widgets/single-property/property-title-area.php:304
#: elementor/widgets/single-property/property-title-area.php:327
#: elementor/widgets/single-property/property-title-area.php:354
#: elementor/widgets/single-property/property-title-area.php:409
#: elementor/widgets/single-property/property-title-area.php:536
#: elementor/widgets/single-property/property-title-area.php:548
#: elementor/widgets/single-property/property-title-area.php:560
#: elementor/widgets/single-property/property-title.php:86
#: elementor/widgets/single-property/section-attachments.php:112
#: elementor/widgets/single-property/section-contact-2.php:58
#: elementor/widgets/single-property/section-contact-2.php:110
#: elementor/widgets/single-property/section-contact-2.php:122
#: elementor/widgets/single-property/section-contact-bottom.php:568
#: elementor/widgets/single-property/section-floorplan.php:200
#: elementor/widgets/single-property/section-overview.php:287
#: elementor/widgets/single-property/section-schedule-tour.php:209
#: elementor/widgets/single-property/section-sublistings.php:91
#: elementor/widgets/single-property/section-toparea-v1.php:86
#: elementor/widgets/single-property/section-toparea-v1.php:109
#: elementor/widgets/single-property/section-toparea-v1.php:206
#: elementor/widgets/single-property/section-toparea-v1.php:229
#: elementor/widgets/single-property/section-toparea-v1.php:256
#: elementor/widgets/single-property/section-toparea-v1.php:311
#: elementor/widgets/single-property/section-toparea-v1.php:438
#: elementor/widgets/single-property/section-toparea-v1.php:450
#: elementor/widgets/single-property/section-toparea-v1.php:462
#: elementor/widgets/single-property/section-toparea-v1.php:557
#: elementor/widgets/single-property/section-toparea-v1.php:569
#: elementor/widgets/single-property/section-toparea-v1.php:581
#: elementor/widgets/single-property/section-toparea-v1.php:593
#: elementor/widgets/single-property/section-toparea-v1.php:616
#: elementor/widgets/single-property/section-toparea-v1.php:650
#: elementor/widgets/single-property/section-toparea-v2.php:84
#: elementor/widgets/single-property/section-toparea-v2.php:132
#: elementor/widgets/single-property/section-toparea-v2.php:229
#: elementor/widgets/single-property/section-toparea-v2.php:252
#: elementor/widgets/single-property/section-toparea-v2.php:279
#: elementor/widgets/single-property/section-toparea-v2.php:334
#: elementor/widgets/single-property/section-toparea-v2.php:460
#: elementor/widgets/single-property/section-toparea-v2.php:472
#: elementor/widgets/single-property/section-toparea-v2.php:484
#: elementor/widgets/single-property/section-toparea-v2.php:579
#: elementor/widgets/single-property/section-toparea-v2.php:591
#: elementor/widgets/single-property/section-toparea-v2.php:603
#: elementor/widgets/single-property/section-toparea-v2.php:615
#: elementor/widgets/single-property/section-toparea-v2.php:638
#: elementor/widgets/single-property/section-toparea-v3.php:109
#: elementor/widgets/single-property/section-toparea-v3.php:134
#: elementor/widgets/single-property/section-toparea-v3.php:231
#: elementor/widgets/single-property/section-toparea-v3.php:254
#: elementor/widgets/single-property/section-toparea-v3.php:281
#: elementor/widgets/single-property/section-toparea-v3.php:336
#: elementor/widgets/single-property/section-toparea-v3.php:462
#: elementor/widgets/single-property/section-toparea-v3.php:474
#: elementor/widgets/single-property/section-toparea-v3.php:486
#: elementor/widgets/single-property/section-toparea-v3.php:581
#: elementor/widgets/single-property/section-toparea-v3.php:593
#: elementor/widgets/single-property/section-toparea-v3.php:605
#: elementor/widgets/single-property/section-toparea-v3.php:617
#: elementor/widgets/single-property/section-toparea-v3.php:640
#: elementor/widgets/single-property/section-toparea-v5.php:84
#: elementor/widgets/single-property/section-toparea-v5.php:109
#: elementor/widgets/single-property/section-toparea-v5.php:206
#: elementor/widgets/single-property/section-toparea-v5.php:229
#: elementor/widgets/single-property/section-toparea-v5.php:256
#: elementor/widgets/single-property/section-toparea-v5.php:311
#: elementor/widgets/single-property/section-toparea-v5.php:437
#: elementor/widgets/single-property/section-toparea-v5.php:449
#: elementor/widgets/single-property/section-toparea-v5.php:461
#: elementor/widgets/single-property/section-toparea-v5.php:556
#: elementor/widgets/single-property/section-toparea-v5.php:568
#: elementor/widgets/single-property/section-toparea-v5.php:580
#: elementor/widgets/single-property/section-toparea-v5.php:592
#: elementor/widgets/single-property/section-toparea-v5.php:615
#: elementor/widgets/single-property/section-toparea-v6.php:129
#: elementor/widgets/single-property/section-toparea-v6.php:154
#: elementor/widgets/single-property/section-toparea-v6.php:251
#: elementor/widgets/single-property/section-toparea-v6.php:274
#: elementor/widgets/single-property/section-toparea-v6.php:301
#: elementor/widgets/single-property/section-toparea-v6.php:356
#: elementor/widgets/single-property/section-toparea-v6.php:482
#: elementor/widgets/single-property/section-toparea-v6.php:494
#: elementor/widgets/single-property/section-toparea-v6.php:506
#: elementor/widgets/single-property/section-toparea-v7.php:104
#: elementor/widgets/single-property/section-toparea-v7.php:129
#: elementor/widgets/single-property/section-toparea-v7.php:226
#: elementor/widgets/single-property/section-toparea-v7.php:249
#: elementor/widgets/single-property/section-toparea-v7.php:276
#: elementor/widgets/single-property/section-toparea-v7.php:331
#: elementor/widgets/single-property/section-toparea-v7.php:457
#: elementor/widgets/single-property/section-toparea-v7.php:469
#: elementor/widgets/single-property/section-toparea-v7.php:481
#: elementor/widgets/taxonomies-cards-carousel.php:181
#: elementor/widgets/taxonomies-cards-carousel.php:193
#: elementor/widgets/taxonomies-cards-carousel.php:217
#: elementor/widgets/taxonomies-cards-carousel.php:231
#: elementor/widgets/taxonomies-grids-carousel.php:166
#: elementor/widgets/taxonomies-grids-carousel.php:178
#: elementor/widgets/taxonomies-grids-carousel.php:202
#: elementor/widgets/taxonomies-grids-carousel.php:216
#: extensions/meta-box/inc/fields/checkbox.php:43
#: templates/fields-builder/fields-form.php:152
msgid "No"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1111 classes/class-image-sizes.php:2112
msgid "Edit in Media Settings"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1120 classes/class-image-sizes.php:2017
msgid "Houzez Built-in Sizes"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1121
msgid ""
"These sizes are used by the Houzez theme for property listings and galleries."
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1133
#: ⁨classes/class-image-sizes copy.php⁩:1141 classes/class-image-sizes.php:2046
#: classes/class-image-sizes.php:2054 classes/class-permalinks.php:136
#: elementor/traits/Houzez_Property_Cards_Traits.php:844
#: elementor/traits/Houzez_Style_Traits.php:272
#: elementor/traits/Houzez_Style_Traits.php:983
#: elementor/traits/Houzez_Style_Traits.php:1336
#: elementor/widgets/banner-image.php:279
#: elementor/widgets/contact-form.php:206
#: elementor/widgets/contact-form.php:458
#: elementor/widgets/custom-carousel.php:564
#: elementor/widgets/header-footer/menu.php:409
#: elementor/widgets/header-footer/site-logo.php:498
#: elementor/widgets/inquiry-form.php:216
#: elementor/widgets/inquiry-form.php:443
#: elementor/widgets/property-cards-v1.php:498
#: elementor/widgets/property-cards-v4.php:493
#: elementor/widgets/property-cards-v7.php:464
#: elementor/widgets/property-carousel-v1.php:473
#: elementor/widgets/property-carousel-v7.php:434
#: elementor/widgets/search-builder-old.php:235
#: elementor/widgets/search-builder.php:222
#: elementor/widgets/search-builder.php:715
#: elementor/widgets/single-agency/agency-call-btn.php:75
#: elementor/widgets/single-agency/agency-contact-btn.php:63
#: elementor/widgets/single-agency/agency-line-btn.php:63
#: elementor/widgets/single-agency/agency-telegram-btn.php:63
#: elementor/widgets/single-agency/agency-whatsapp-btn.php:63
#: elementor/widgets/single-agent/agent-call-btn.php:75
#: elementor/widgets/single-agent/agent-contact-btn.php:63
#: elementor/widgets/single-agent/agent-line-btn.php:63
#: elementor/widgets/single-agent/agent-telegram-btn.php:63
#: elementor/widgets/single-agent/agent-whatsapp-btn.php:63
#: elementor/widgets/single-post/post-info.php:53
#: elementor/widgets/single-post/post-info.php:299
#: elementor/widgets/single-property/section-address.php:313
#: elementor/widgets/single-property/section-calculator.php:144
#: elementor/widgets/single-property/section-contact-bottom.php:124
#: elementor/widgets/single-property/section-details.php:407
#: elementor/widgets/single-property/section-details.php:543
#: elementor/widgets/single-property/section-energy.php:286
#: elementor/widgets/single-property/section-overview-v2.php:269
#: elementor/widgets/single-property/section-overview.php:299
#: elementor/widgets/single-property/section-overview.php:346
#: elementor/widgets/single-property/section-similar.php:210
#: elementor/widgets/taxonomies-list.php:344
#: extensions/favethemes-white-label/template/form.php:94
#: functions/functions.php:248
msgid "Default"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1162 classes/class-image-sizes.php:2075
msgid "Reset to Default"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1171 classes/class-image-sizes.php:2124
msgid "Custom Image Sizes"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1172 classes/class-image-sizes.php:2125
msgid "These are custom image sizes that you have created."
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1194 classes/class-image-sizes.php:2147
msgid "Edit"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1204 classes/class-image-sizes.php:2160
msgid "No custom image sizes defined."
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1210 classes/class-image-sizes.php:2166
msgid "Save All Changes"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1215
msgid "Warning:"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1216
msgid ""
"Disabling any image size will prevent WordPress from generating that "
"specific thumbnail size for new uploads. This may affect how images appear "
"in certain areas of your website."
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1219
msgid ""
"If you later decide to re-enable a previously disabled size, you will need "
"to regenerate thumbnails for existing images."
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1226 classes/class-image-sizes.php:2173
msgid "Edit Custom Image Size"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1255 classes/class-image-sizes.php:2202
msgid "Enabled?"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1385
#: ⁨classes/class-image-sizes copy.php⁩:1485 classes/class-image-sizes.php:2341
#: classes/class-image-sizes.php:2441
msgid "Please provide all required fields."
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1393
#: ⁨classes/class-image-sizes copy.php⁩:1498 classes/class-image-sizes.php:2349
#: classes/class-image-sizes.php:2454
msgid "An image size with this name already exists."
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1424
#: ⁨classes/class-image-sizes copy.php⁩:1457
#: ⁨classes/class-image-sizes copy.php⁩:1534 classes/class-image-sizes.php:2380
#: classes/class-image-sizes.php:2413 classes/class-image-sizes.php:2490
msgid "Invalid image size name."
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1432
#: ⁨classes/class-image-sizes copy.php⁩:1465
#: ⁨classes/class-image-sizes copy.php⁩:1542 classes/class-image-sizes.php:2388
#: classes/class-image-sizes.php:2421 classes/class-image-sizes.php:2498
msgid "Image size not found."
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1493 classes/class-image-sizes.php:2449
msgid "Original image size not found."
msgstr ""

#: classes/class-image-sizes.php:138 classes/class-image-sizes.php:1864
msgid "Property Detail v3-4"
msgstr ""

#: classes/class-image-sizes.php:140 classes/class-image-sizes.php:1866
msgid "Property Detail v6"
msgstr ""

#: classes/class-image-sizes.php:141 classes/class-image-sizes.php:1867
msgid "Property Detail v7"
msgstr ""

#: classes/class-image-sizes.php:142 classes/class-image-sizes.php:1868
msgid "Property Detail Block Gallery"
msgstr ""

#: classes/class-image-sizes.php:147 classes/class-image-sizes.php:1905
msgid "Blog Grid"
msgstr ""

#: classes/class-image-sizes.php:887
msgid "Image Size Assignments"
msgstr ""

#: classes/class-image-sizes.php:897 classes/class-image-sizes.php:957
#: classes/class-image-sizes.php:1028 classes/class-image-sizes.php:1236
#: classes/class-image-sizes.php:1331
msgid "This image size is currently assigned to the following layouts:"
msgstr ""

#: classes/class-image-sizes.php:899
msgid "This image size is not currently assigned to any layouts."
msgstr ""

#: classes/class-image-sizes.php:908 classes/class-image-sizes.php:965
#: classes/class-image-sizes.php:1036 classes/class-image-sizes.php:1244
#: classes/class-image-sizes.php:1339
msgid "Go to Layout Assignments"
msgstr ""

#: classes/class-image-sizes.php:911 elementor/widgets/agent-card.php:1087
msgid "Close"
msgstr ""

#: classes/class-image-sizes.php:956 classes/class-image-sizes.php:1027
#: classes/class-image-sizes.php:1330
msgid "Cannot Disable Image Size"
msgstr ""

#: classes/class-image-sizes.php:959 classes/class-image-sizes.php:1030
#: classes/class-image-sizes.php:1333
msgid ""
"Please unassign this image size from these layouts in the Layout Image "
"Assignments tab before disabling it."
msgstr ""

#: classes/class-image-sizes.php:1175
msgid ""
"This image size is assigned to layouts and cannot be disabled. Please "
"unassign it first."
msgstr ""

#: classes/class-image-sizes.php:1235
msgid "Cannot Delete Image Size"
msgstr ""

#: classes/class-image-sizes.php:1238
msgid ""
"Please unassign this image size from these layouts in the Layout Image "
"Assignments tab before deleting it."
msgstr ""

#: classes/class-image-sizes.php:1424 classes/class-image-sizes.php:1425
#: classes/class-image-sizes.php:1723
msgid "Media Manager"
msgstr ""

#: classes/class-image-sizes.php:1660
msgid "Full Size (Original Image)"
msgstr ""

#: classes/class-image-sizes.php:1740
msgid "Total Image Sizes"
msgstr ""

#: classes/class-image-sizes.php:1750
msgid "Enabled Sizes"
msgstr ""

#: classes/class-image-sizes.php:1760
msgid "Custom Sizes"
msgstr ""

#: classes/class-image-sizes.php:1770
msgid "Layout Assignments"
msgstr ""

#: classes/class-image-sizes.php:1780
msgid "Image Size Management"
msgstr ""

#: classes/class-image-sizes.php:1783
msgid ""
"Control how images are displayed throughout your website by managing "
"dimensions, cropping, and layout assignments."
msgstr ""

#: classes/class-image-sizes.php:1895
msgid "Others"
msgstr ""

#: classes/class-image-sizes.php:1897
msgid "Control image sizes for agent/agency and blog posts."
msgstr ""

#: classes/class-image-sizes.php:2018
msgid ""
"These optimized image sizes are specifically designed for Houzez theme "
"property listings, galleries, and various layout components to ensure "
"optimal performance and visual quality."
msgstr ""

#: classes/class-image-sizes.php:2565
msgid "Global (Use Media Settings)"
msgstr ""

#: classes/class-image-sizes.php:2568
msgid "Full Size (Original)"
msgstr ""

#: classes/class-import-locations.php:138
msgid "File not found."
msgstr ""

#: classes/class-import-locations.php:169
#, php-format
msgid "Ready to import %d records. Starting batch import..."
msgstr ""

#: classes/class-import-locations.php:181
msgid "No active import session found."
msgstr ""

#: classes/class-import-locations.php:196
msgid "Import file not found."
msgstr ""

#: classes/class-import-locations.php:285
#, php-format
msgid ""
"Import completed! Processed %d records in %d seconds. %d successful, %d "
"errors."
msgstr ""

#: classes/class-import-locations.php:445
msgid "Importing..."
msgstr ""

#: classes/class-import-locations.php:446
msgid "Processing batch"
msgstr ""

#: classes/class-import-locations.php:447
msgid "Import Complete!"
msgstr ""

#: classes/class-import-locations.php:448
msgid "Import Error"
msgstr ""

#: classes/class-import-locations.php:449
msgid "records processed"
msgstr ""

#: classes/class-import-locations.php:450
msgid "successful"
msgstr ""

#: classes/class-import-locations.php:451
msgid "errors"
msgstr ""

#: classes/class-invoice-post-type.php:36 classes/class-menu.php:110
#: classes/class-menu.php:111
msgid "Invoices"
msgstr ""

#: classes/class-invoice-post-type.php:37
msgid "Invoice"
msgstr ""

#: classes/class-invoice-post-type.php:39
msgid "Add New Invoice"
msgstr ""

#: classes/class-invoice-post-type.php:40
msgid "Edit Invoice"
msgstr ""

#: classes/class-invoice-post-type.php:41
msgid "New Invoice"
msgstr ""

#: classes/class-invoice-post-type.php:42
msgid "View Invoice"
msgstr ""

#: classes/class-invoice-post-type.php:43
msgid "Search Invoice"
msgstr ""

#: classes/class-invoice-post-type.php:44
msgid "No Invoice found"
msgstr ""

#: classes/class-invoice-post-type.php:45
msgid "No Invoice found in Trash"
msgstr ""

#: classes/class-invoice-post-type.php:87
msgid "Invoice Title"
msgstr ""

#: classes/class-invoice-post-type.php:88
#: classes/class-property-post-type.php:512
#: elementor/traits/Houzez_Property_Cards_Traits.php:643
#: elementor/traits/Houzez_Property_Cards_Traits.php:1075
#: elementor/traits/Houzez_Property_Cards_Traits.php:1513
#: elementor/widgets/properties-recent-viewed.php:316
#: elementor/widgets/properties-recent-viewed.php:553
#: elementor/widgets/property-cards-v1.php:268
#: elementor/widgets/property-cards-v2.php:252
#: elementor/widgets/property-cards-v3.php:196
#: elementor/widgets/property-cards-v4.php:263
#: elementor/widgets/property-cards-v5.php:195
#: elementor/widgets/property-cards-v6.php:201
#: elementor/widgets/property-cards-v7.php:252
#: elementor/widgets/property-cards-v8.php:238
#: elementor/widgets/property-carousel-v1.php:243
#: elementor/widgets/property-carousel-v2.php:228
#: elementor/widgets/property-carousel-v3.php:172
#: elementor/widgets/property-carousel-v5.php:172
#: elementor/widgets/property-carousel-v6.php:173
#: elementor/widgets/property-carousel-v7.php:228
#: elementor/widgets/single-property/property-price.php:96
#: elementor/widgets/single-property/section-details.php:224
#: statistics/houzez-statistics.php:119 statistics/houzez-statistics.php:196
#: statistics/houzez-statistics.php:244 statistics/houzez-statistics.php:324
#: statistics/houzez-statistics.php:506 statistics/houzez-statistics.php:509
msgid "Price"
msgstr ""

#: classes/class-invoice-post-type.php:89
msgid "Payment Method"
msgstr ""

#: classes/class-invoice-post-type.php:90
msgid "Buyer"
msgstr ""

#: classes/class-invoice-post-type.php:91
msgid "Buyer Email"
msgstr ""

#: classes/class-invoice-post-type.php:92
msgid "Invoice Type"
msgstr ""

#: classes/class-invoice-post-type.php:93
msgid "Billion For"
msgstr ""

#: classes/class-invoice-post-type.php:95
#: classes/class-reviews-post-type.php:106
#: elementor/traits/Houzez_Property_Cards_Traits.php:1233
#: elementor/traits/Houzez_Testimonials_Traits.php:39
#: elementor/widgets/agents-grid.php:169 elementor/widgets/agents.php:167
#: elementor/widgets/partners.php:94
#: elementor/widgets/properties-recent-viewed.php:370
#: elementor/widgets/property-cards-v1.php:322
#: elementor/widgets/property-cards-v2.php:298
#: elementor/widgets/property-cards-v4.php:317
#: elementor/widgets/property-cards-v7.php:309
#: elementor/widgets/property-carousel-v1.php:297
#: elementor/widgets/property-carousel-v2.php:274
#: elementor/widgets/property-carousel-v7.php:282
#: elementor/widgets/single-post/post-info.php:76
msgid "Date"
msgstr ""

#: classes/class-invoice-post-type.php:126
msgid "Direct Bank Transfer"
msgstr ""

#: classes/class-invoice-post-type.php:140
msgid "Listing"
msgstr ""

#: classes/class-invoice-post-type.php:142
msgid "Upgrade to Featured"
msgstr ""

#: classes/class-invoice-post-type.php:178
#: classes/class-invoice-post-type.php:181
msgid "Activate Purchase"
msgstr ""

#: classes/class-invoice-post-type.php:179
#: classes/class-invoice-post-type.php:182
msgid "Not Paid"
msgstr ""

#: classes/class-invoice-post-type.php:185
msgid "Paid"
msgstr ""

#: classes/class-membership-post-type.php:33
#: classes/class-membership-post-type.php:34 classes/class-menu.php:100
#: classes/class-menu.php:101 classes/class-user-packages-post-type.php:40
msgid "Packages"
msgstr ""

#: classes/class-membership-post-type.php:35
msgid "Add New Package"
msgstr ""

#: classes/class-membership-post-type.php:36
msgid "Add Packages"
msgstr ""

#: classes/class-membership-post-type.php:37
msgid "Edit Packages"
msgstr ""

#: classes/class-membership-post-type.php:38
msgid "Edit Package"
msgstr ""

#: classes/class-membership-post-type.php:39
msgid "New Packages"
msgstr ""

#: classes/class-membership-post-type.php:40
#: classes/class-membership-post-type.php:41
msgid "View Packages"
msgstr ""

#: classes/class-membership-post-type.php:42
msgid "Search Packages"
msgstr ""

#: classes/class-membership-post-type.php:43
#: classes/class-membership-post-type.php:44
msgid "No Packages found"
msgstr ""

#: classes/class-membership-post-type.php:45
msgid "Parent Package"
msgstr ""

#: classes/class-menu.php:28
msgid "Real Estate"
msgstr ""

#: classes/class-menu.php:41 classes/class-property-post-type.php:212
msgid "Add New Property"
msgstr ""

#: classes/class-menu.php:42 classes/class-property-post-type.php:215
msgid "New Property"
msgstr ""

#: classes/class-menu.php:82 classes/class-menu.php:83
#: classes/class-partners-post-type.php:31 classes/class-post-types.php:195
#: classes/class-post-types.php:281 elementor/widgets/partners.php:36
msgid "Partners"
msgstr ""

#: classes/class-menu.php:91 classes/class-menu.php:92
#: classes/class-reviews-post-type.php:30
#: elementor/template-part/single-agency/agency-listings-review.php:141
#: elementor/template-part/single-agency/agency-reviews.php:24
#: elementor/template-part/single-agent/agent-listings-review.php:75
#: elementor/template-part/single-agent/agent-reviews.php:24
msgid "Reviews"
msgstr ""

#: classes/class-menu.php:121 classes/class-menu.php:122
msgid "Packages Info"
msgstr ""

#: classes/class-partners-post-type.php:32
msgid "Partner"
msgstr ""

#: classes/class-partners-post-type.php:34
msgid "Add New Partner"
msgstr ""

#: classes/class-partners-post-type.php:35
msgid "Edit Partner"
msgstr ""

#: classes/class-partners-post-type.php:36
msgid "New Partner"
msgstr ""

#: classes/class-partners-post-type.php:37
msgid "View Partner"
msgstr ""

#: classes/class-partners-post-type.php:38
msgid "Search Partner"
msgstr ""

#: classes/class-partners-post-type.php:39
msgid "No Partner found"
msgstr ""

#: classes/class-partners-post-type.php:40
msgid "No Partner found in Trash"
msgstr ""

#: classes/class-permalinks.php:52
msgid "Permalink settings saved successfully!"
msgstr ""

#: classes/class-permalinks.php:61
msgid "Permalinks Management"
msgstr ""

#: classes/class-permalinks.php:66
msgid "Save Permalinks"
msgstr ""

#: classes/class-permalinks.php:70
msgid "Reset to Defaults"
msgstr ""

#: classes/class-permalinks.php:93
msgid "Total Permalinks"
msgstr ""

#: classes/class-permalinks.php:103
msgid "Configured URLs"
msgstr ""

#: classes/class-permalinks.php:113
msgid "Base Domain"
msgstr ""

#: classes/class-permalinks.php:123
msgid "Configuration Rate"
msgstr ""

#: classes/class-permalinks.php:133
msgid "Custom Permalink Configuration"
msgstr ""

#: classes/class-permalinks.php:136
msgid "Configured"
msgstr ""

#: classes/class-permalinks.php:141
msgid ""
"Customize URL structures for properties, taxonomies, and other content "
"types. Changes will take effect after saving and may require permalink "
"refresh."
msgstr ""

#: classes/class-permalinks.php:152
msgid "Property URLs"
msgstr ""

#: classes/class-permalinks.php:159 classes/class-permalinks.php:394
msgid "Property Slug"
msgstr ""

#: classes/class-permalinks.php:167
msgid "Base URL structure for individual property pages"
msgstr ""

#: classes/class-permalinks.php:174 classes/class-permalinks.php:412
msgid "Agent Slug"
msgstr ""

#: classes/class-permalinks.php:182
msgid "URL structure for agent profile pages"
msgstr ""

#: classes/class-permalinks.php:189 classes/class-permalinks.php:414
msgid "Agency Slug"
msgstr ""

#: classes/class-permalinks.php:197
msgid "URL structure for agency profile pages"
msgstr ""

#: classes/class-permalinks.php:205
msgid "Property Taxonomy URLs"
msgstr ""

#: classes/class-permalinks.php:212 classes/class-permalinks.php:396
msgid "Property Type Slug"
msgstr ""

#: classes/class-permalinks.php:220
msgid "URL structure for property type archives (e.g., apartments, houses)"
msgstr ""

#: classes/class-permalinks.php:227 classes/class-permalinks.php:400
msgid "Property Status Slug"
msgstr ""

#: classes/class-permalinks.php:235
msgid "URL structure for property status archives (e.g., for-sale, for-rent)"
msgstr ""

#: classes/class-permalinks.php:242 classes/class-permalinks.php:398
msgid "Property Feature Slug"
msgstr ""

#: classes/class-permalinks.php:250
msgid ""
"URL structure for property feature archives (e.g., swimming-pool, garage)"
msgstr ""

#: classes/class-permalinks.php:257 classes/class-permalinks.php:410
msgid "Property Label Slug"
msgstr ""

#: classes/class-permalinks.php:265
msgid "URL structure for property label archives (e.g., featured, hot-offer)"
msgstr ""

#: classes/class-permalinks.php:273
msgid "Location URLs"
msgstr ""

#: classes/class-permalinks.php:280 classes/class-permalinks.php:402
msgid "Property Country Slug"
msgstr ""

#: classes/class-permalinks.php:288
msgid "URL structure for country-based property listings"
msgstr ""

#: classes/class-permalinks.php:295 classes/class-permalinks.php:404
msgid "Property State Slug"
msgstr ""

#: classes/class-permalinks.php:303
msgid "URL structure for state/province-based property listings"
msgstr ""

#: classes/class-permalinks.php:310 classes/class-permalinks.php:406
msgid "Property City Slug"
msgstr ""

#: classes/class-permalinks.php:318
msgid "URL structure for city-based property listings"
msgstr ""

#: classes/class-permalinks.php:325 classes/class-permalinks.php:408
msgid "Property Area Slug"
msgstr ""

#: classes/class-permalinks.php:333
msgid "URL structure for area/neighborhood-based property listings"
msgstr ""

#: classes/class-permalinks.php:365
msgid "Are you sure you want to reset all permalinks to their default values?"
msgstr ""

#: classes/class-permalinks.php:391
msgid "Permalinks"
msgstr ""

#: classes/class-permalinks.php:456
msgid "Set up custom permalinks for the property section on your site."
msgstr ""

#: classes/class-post-types.php:44
msgid "Post type settings saved successfully!"
msgstr ""

#: classes/class-post-types.php:53
msgid "Post Types Management"
msgstr ""

#: classes/class-post-types.php:81
msgid "Total Post Types"
msgstr ""

#: classes/class-post-types.php:91
msgid "Enabled Post Types"
msgstr ""

#: classes/class-post-types.php:101
msgid "Disabled Post Types"
msgstr ""

#: classes/class-post-types.php:111
msgid "Activation Rate"
msgstr ""

#: classes/class-post-types.php:121
msgid "Custom Post Types Configuration"
msgstr ""

#: classes/class-post-types.php:124 classes/class-taxonomies.php:124
#: elementor/widgets/header-footer/menu.php:557
#: elementor/widgets/header-footer/menu.php:878
#: elementor/widgets/single-agency/agency-listings-review.php:310
#: elementor/widgets/single-agent/agent-listings-review.php:315
#: functions/functions.php:237 templates/currency/currency-list.php:45
#: templates/fields-builder/index.php:33
msgid "Active"
msgstr ""

#: classes/class-post-types.php:124 classes/class-taxonomies.php:124
msgid "Inactive"
msgstr ""

#: classes/class-post-types.php:129
msgid ""
"Enable or disable custom post types for your Houzez website. Disabled post "
"types will not appear in the admin area or frontend."
msgstr ""

#: classes/class-post-types.php:143 classes/class-post-types.php:157
#: classes/class-post-types.php:171 classes/class-post-types.php:185
#: classes/class-post-types.php:199 classes/class-post-types.php:213
#: classes/class-post-types.php:227 classes/class-post-types.php:339
#: classes/class-post-types.php:358 classes/class-post-types.php:376
#: classes/class-post-types.php:394 classes/class-post-types.php:412
#: classes/class-post-types.php:430 classes/class-post-types.php:448
#: classes/class-taxonomies.php:143 classes/class-taxonomies.php:157
#: classes/class-taxonomies.php:171 classes/class-taxonomies.php:185
#: classes/class-taxonomies.php:342 classes/class-taxonomies.php:360
#: classes/class-taxonomies.php:378 classes/class-taxonomies.php:396
msgid "Disabled"
msgstr ""

#: classes/class-post-types.php:146
msgid "Manage real estate agents and their profiles"
msgstr ""

#: classes/class-post-types.php:160
msgid "Manage real estate agencies and their information"
msgstr ""

#: classes/class-post-types.php:167 classes/class-post-types.php:277
msgid "Houzez Packages"
msgstr ""

#: classes/class-post-types.php:174
msgid "Manage subscription packages for property listings"
msgstr ""

#: classes/class-post-types.php:181 classes/class-post-types.php:279
msgid "Houzez Invoices"
msgstr ""

#: classes/class-post-types.php:188
msgid "Manage billing and invoice records"
msgstr ""

#: classes/class-post-types.php:202
msgid "Manage business partners and collaborations"
msgstr ""

#: classes/class-post-types.php:209 classes/class-post-types.php:283
#: classes/class-testimonials-post-type.php:33
msgid "Testimonials"
msgstr ""

#: classes/class-post-types.php:216
msgid "Manage customer testimonials and reviews"
msgstr ""

#: classes/class-post-types.php:223 classes/class-post-types.php:285
msgid "User Packages Info"
msgstr ""

#: classes/class-post-types.php:230
msgid "Track user package information and usage"
msgstr ""

#: classes/class-post-types.php:270
msgid "Custom Post Types"
msgstr ""

#: classes/class-post-types.php:322
msgid ""
"Disable Custom Post Types which you do not want to show(if disabled then "
"these will not show on back-end and front-end)"
msgstr ""

#: classes/class-property-post-type.php:210
#: elementor/widgets/google-map.php:195
#: elementor/widgets/properties-ajax-tabs.php:548
#: elementor/widgets/properties.php:42
#: elementor/widgets/property-cards-v1.php:83
#: elementor/widgets/property-cards-v5.php:82
#: elementor/widgets/property-cards-v6.php:83
#: elementor/widgets/property-cards-v7.php:82
#: elementor/widgets/property-cards-v8.php:82
msgid "Properties"
msgstr ""

#: classes/class-property-post-type.php:211
msgid "Property"
msgstr ""

#: classes/class-property-post-type.php:214
msgid "Edit Property"
msgstr ""

#: classes/class-property-post-type.php:216
msgid "View Property"
msgstr ""

#: classes/class-property-post-type.php:217
msgid "Search Property"
msgstr ""

#: classes/class-property-post-type.php:218
#: statistics/houzez-statistics.php:183 statistics/houzez-statistics.php:311
msgid "No Property found"
msgstr ""

#: classes/class-property-post-type.php:219
msgid "No Property found in Trash"
msgstr ""

#: classes/class-property-post-type.php:241
msgid "properties"
msgstr ""

#: classes/class-property-post-type.php:264
#: classes/class-property-post-type.php:582
#: elementor/traits/Houzez_Filters_Traits.php:26
#: elementor/traits/Houzez_Filters_Traits.php:315
#: elementor/widgets/listings-tabs.php:97
#: elementor/widgets/search-builder-old.php:81
#: elementor/widgets/search-builder-old.php:686
#: elementor/widgets/search-builder.php:81
#: elementor/widgets/search-builder.php:821
#: elementor/widgets/single-agency/agency-listings-review.php:163
#: elementor/widgets/single-agency/agency-listings.php:163
#: elementor/widgets/single-agent/agent-listings-review.php:168
#: elementor/widgets/single-agent/agent-listings.php:168
#: elementor/widgets/single-post/post-info.php:71
#: statistics/houzez-statistics.php:117 statistics/houzez-statistics.php:194
#: statistics/houzez-statistics.php:242 statistics/houzez-statistics.php:322
#: statistics/houzez-statistics.php:410 statistics/houzez-statistics.php:581
#: templates/fields-builder/index.php:53
msgid "Type"
msgstr ""

#: classes/class-property-post-type.php:265
msgid "Add New Type"
msgstr ""

#: classes/class-property-post-type.php:266
msgid "New Type"
msgstr ""

#: classes/class-property-post-type.php:288
#: classes/class-property-post-type.php:515
#: classes/class-property-post-type.php:591
#: elementor/traits/Houzez_Filters_Traits.php:39
#: elementor/traits/Houzez_Filters_Traits.php:331
#: elementor/widgets/listings-tabs.php:96
#: elementor/widgets/search-builder-old.php:82
#: elementor/widgets/search-builder-old.php:685
#: elementor/widgets/search-builder.php:82
#: elementor/widgets/search-builder.php:820
#: elementor/widgets/single-agency/agency-listings-review.php:162
#: elementor/widgets/single-agency/agency-listings.php:162
#: elementor/widgets/single-agent/agent-listings-review.php:167
#: elementor/widgets/single-agent/agent-listings.php:167
#: statistics/houzez-statistics.php:118 statistics/houzez-statistics.php:195
#: statistics/houzez-statistics.php:243 statistics/houzez-statistics.php:323
#: statistics/houzez-statistics.php:418 statistics/houzez-statistics.php:580
msgid "Status"
msgstr ""

#: classes/class-property-post-type.php:289
msgid "Add New Status"
msgstr ""

#: classes/class-property-post-type.php:290
msgid "New Status"
msgstr ""

#: classes/class-property-post-type.php:313
#: elementor/widgets/search-builder-old.php:87
#: elementor/widgets/search-builder.php:87
msgid "Features"
msgstr ""

#: classes/class-property-post-type.php:314
msgid "Add New Feature"
msgstr ""

#: classes/class-property-post-type.php:315
msgid "New Feature"
msgstr ""

#: classes/class-property-post-type.php:338
#: elementor/traits/Houzez_Filters_Traits.php:52
#: elementor/traits/Houzez_Filters_Traits.php:347
#: elementor/traits/Houzez_Property_Cards_Traits.php:716
#: elementor/traits/Houzez_Property_Cards_Traits.php:1605
#: elementor/widgets/contact-form.php:314
#: elementor/widgets/inquiry-form.php:301
#: elementor/widgets/properties-recent-viewed.php:662
#: elementor/widgets/search-builder-old.php:640
#: elementor/widgets/search-builder.php:627
#: elementor/widgets/single-agency/agency-review.php:63
#: elementor/widgets/single-agent/agent-review.php:63
#: elementor/widgets/single-property/section-contact-bottom.php:480
#: elementor/widgets/single-property/section-energy.php:79
#: elementor/widgets/single-property/section-review.php:63
#: elementor/widgets/single-property/section-schedule-tour.php:144
msgid "Labels"
msgstr ""

#: classes/class-property-post-type.php:339
msgid "Add New Label"
msgstr ""

#: classes/class-property-post-type.php:340
msgid "New Label"
msgstr ""

#: classes/class-property-post-type.php:365
#: classes/class-property-post-type.php:573
#: classes/class-property-post-type.php:894 classes/class-taxonomies.php:167
#: classes/class-taxonomies.php:288
#: elementor/traits/Houzez_Filters_Traits.php:95
#: elementor/traits/Houzez_Filters_Traits.php:394
#: elementor/widgets/agents-grid.php:132 elementor/widgets/agents.php:131
#: elementor/widgets/contact-form.php:96 elementor/widgets/google-map.php:283
#: elementor/widgets/grid-builder.php:291 elementor/widgets/grids.php:200
#: elementor/widgets/inquiry-form.php:98 elementor/widgets/listings-tabs.php:98
#: elementor/widgets/mapbox.php:267 elementor/widgets/open-street-map.php:273
#: elementor/widgets/properties-ajax-tabs.php:375
#: elementor/widgets/search-builder-old.php:89
#: elementor/widgets/search-builder-old.php:687
#: elementor/widgets/search-builder.php:89
#: elementor/widgets/search-builder.php:822
#: elementor/widgets/single-agency/agency-listings-review.php:164
#: elementor/widgets/single-agency/agency-listings.php:164
#: elementor/widgets/single-agent/agent-listings-review.php:169
#: elementor/widgets/single-agent/agent-listings.php:169
#: elementor/widgets/single-property/property-address.php:48
#: elementor/widgets/single-property/property-title-area.php:51
#: elementor/widgets/single-property/section-address.php:166
#: elementor/widgets/single-property/section-toparea-v1.php:51
#: elementor/widgets/single-property/section-toparea-v2.php:50
#: elementor/widgets/single-property/section-toparea-v3.php:55
#: elementor/widgets/single-property/section-toparea-v5.php:50
#: elementor/widgets/single-property/section-toparea-v6.php:51
#: elementor/widgets/single-property/section-toparea-v7.php:50
#: statistics/houzez-statistics.php:116 statistics/houzez-statistics.php:193
#: statistics/houzez-statistics.php:241 statistics/houzez-statistics.php:321
msgid "City"
msgstr ""

#: classes/class-property-post-type.php:392 classes/class-taxonomies.php:181
#: classes/class-taxonomies.php:290
#: elementor/traits/Houzez_Filters_Traits.php:110
#: elementor/traits/Houzez_Filters_Traits.php:410
#: elementor/widgets/google-map.php:296 elementor/widgets/grid-builder.php:308
#: elementor/widgets/grids.php:216
#: elementor/widgets/header-footer/area-switcher.php:165
#: elementor/widgets/inquiry-form.php:99 elementor/widgets/mapbox.php:280
#: elementor/widgets/open-street-map.php:286
#: elementor/widgets/properties-ajax-tabs.php:388
#: elementor/widgets/search-builder-old.php:90
#: elementor/widgets/search-builder.php:90
#: elementor/widgets/single-property/section-address.php:175
#: statistics/houzez-statistics.php:585
msgid "Area"
msgstr ""

#: classes/class-property-post-type.php:393
msgid "Add New Area"
msgstr ""

#: classes/class-property-post-type.php:394
msgid "New Area"
msgstr ""

#: classes/class-property-post-type.php:418
#: classes/class-property-post-type.php:974 classes/class-taxonomies.php:139
#: classes/class-taxonomies.php:286
#: elementor/traits/Houzez_Filters_Traits.php:65
#: elementor/traits/Houzez_Filters_Traits.php:362
#: elementor/widgets/contact-form.php:95 elementor/widgets/google-map.php:257
#: elementor/widgets/grid-builder.php:257 elementor/widgets/grids.php:168
#: elementor/widgets/inquiry-form.php:97 elementor/widgets/mapbox.php:241
#: elementor/widgets/open-street-map.php:247
#: elementor/widgets/properties-ajax-tabs.php:349
#: elementor/widgets/search-builder-old.php:88
#: elementor/widgets/search-builder.php:88
#: elementor/widgets/single-property/property-address.php:46
#: elementor/widgets/single-property/property-title-area.php:49
#: elementor/widgets/single-property/section-address.php:148
#: elementor/widgets/single-property/section-toparea-v1.php:49
#: elementor/widgets/single-property/section-toparea-v2.php:48
#: elementor/widgets/single-property/section-toparea-v3.php:53
#: elementor/widgets/single-property/section-toparea-v5.php:48
#: elementor/widgets/single-property/section-toparea-v6.php:49
#: elementor/widgets/single-property/section-toparea-v7.php:48
#: statistics/houzez-statistics.php:584
msgid "Country"
msgstr ""

#: classes/class-property-post-type.php:419
msgid "Add New Country"
msgstr ""

#: classes/class-property-post-type.php:420
msgid "New Country"
msgstr ""

#: classes/class-property-post-type.php:444
#: elementor/traits/Houzez_Filters_Traits.php:80
#: elementor/traits/Houzez_Filters_Traits.php:378
#: elementor/widgets/contact-form.php:97 elementor/widgets/google-map.php:270
#: elementor/widgets/grid-builder.php:274 elementor/widgets/grids.php:184
#: elementor/widgets/inquiry-form.php:100 elementor/widgets/mapbox.php:254
#: elementor/widgets/open-street-map.php:260
#: elementor/widgets/properties-ajax-tabs.php:362
#: elementor/widgets/search-builder-old.php:91
#: elementor/widgets/search-builder.php:91
#: elementor/widgets/single-property/property-address.php:47
#: elementor/widgets/single-property/property-title-area.php:50
#: elementor/widgets/single-property/section-address.php:157
#: elementor/widgets/single-property/section-toparea-v1.php:50
#: elementor/widgets/single-property/section-toparea-v2.php:49
#: elementor/widgets/single-property/section-toparea-v3.php:54
#: elementor/widgets/single-property/section-toparea-v5.php:49
#: elementor/widgets/single-property/section-toparea-v6.php:50
#: elementor/widgets/single-property/section-toparea-v7.php:49
#: statistics/houzez-statistics.php:434 statistics/houzez-statistics.php:583
msgid "State"
msgstr ""

#: classes/class-property-post-type.php:445
msgid "Add New State"
msgstr ""

#: classes/class-property-post-type.php:446
msgid "New State"
msgstr ""

#: classes/class-property-post-type.php:510
msgid "Info"
msgstr ""

#: classes/class-property-post-type.php:513
#: classes/class-property-post-type.php:1586
#: elementor/widgets/single-property/featured-label.php:207
msgid "Featured"
msgstr ""

#: classes/class-property-post-type.php:514
msgid "Posted"
msgstr ""

#: classes/class-property-post-type.php:521
msgid "Translations"
msgstr ""

#: classes/class-property-post-type.php:563
#: classes/class-property-post-type.php:601
#: classes/class-property-post-type.php:667
#: classes/class-property-post-type.php:676
#: classes/class-property-post-type.php:685
#: statistics/houzez-statistics.php:169 statistics/houzez-statistics.php:296
msgid "NA"
msgstr ""

#: classes/class-property-post-type.php:598
#: classes/class-property-post-type.php:601
msgid "Listing ID"
msgstr ""

#: classes/class-property-post-type.php:610
msgid "Expires"
msgstr ""

#: classes/class-property-post-type.php:640
msgid "No Address Provided!"
msgstr ""

#: classes/class-property-post-type.php:700
#, php-format
msgid "by %s"
msgstr ""

#: classes/class-property-post-type.php:709
msgid "a guest"
msgstr ""

#: classes/class-property-post-type.php:746
#: classes/class-reviews-post-type.php:163
msgid "Approve"
msgstr ""

#: classes/class-property-post-type.php:758
msgid "Disapprove"
msgstr ""

#: classes/class-property-post-type.php:770
msgid "Mark as Featured"
msgstr ""

#: classes/class-property-post-type.php:782
msgid "Remove from Featured"
msgstr ""

#: classes/class-property-post-type.php:794
msgid "Expire"
msgstr ""

#: classes/class-property-post-type.php:807
msgid "Mark as Sold"
msgstr ""

#: classes/class-property-post-type.php:819
msgid "Put on Hold"
msgstr ""

#: classes/class-property-post-type.php:831
msgid "Go Live"
msgstr ""

#: classes/class-property-post-type.php:843
msgid "Duplicate"
msgstr ""

#: classes/class-property-post-type.php:855
msgid "Publish"
msgstr ""

#: classes/class-property-post-type.php:897
#: classes/class-property-post-type.php:936
#: classes/class-property-post-type.php:977
msgid "Posts"
msgstr ""

#: classes/class-property-post-type.php:933
msgid "County/State"
msgstr ""

#: classes/class-property-post-type.php:1446
#: elementor/widgets/search-builder-old.php:478
#: elementor/widgets/search-builder.php:465
msgid "All Types"
msgstr ""

#: classes/class-property-post-type.php:1479
#: elementor/widgets/search-builder-old.php:489
#: elementor/widgets/search-builder.php:476
msgid "All Status"
msgstr ""

#: classes/class-property-post-type.php:1512
msgid "All Labels"
msgstr ""

#: classes/class-property-post-type.php:1545
#: elementor/widgets/search-builder-old.php:471
#: elementor/widgets/search-builder.php:458
msgid "All Cities"
msgstr ""

#: classes/class-property-post-type.php:1583
#: elementor/controls/details-control.php:44
#: elementor/widgets/property-by-id.php:136
#: elementor/widgets/property-meta-data.php:55
#: elementor/widgets/search-builder-old.php:100
#: elementor/widgets/search-builder-old.php:547
#: elementor/widgets/search-builder-old.php:548
#: elementor/widgets/search-builder.php:100
#: elementor/widgets/search-builder.php:534
#: elementor/widgets/search-builder.php:535
#: elementor/widgets/single-property/section-details.php:216
#: elementor/widgets/single-property/section-overview-v2.php:58
#: elementor/widgets/single-property/section-overview.php:57
#: statistics/houzez-statistics.php:120 statistics/houzez-statistics.php:197
#: statistics/houzez-statistics.php:245 statistics/houzez-statistics.php:325
msgid "Property ID"
msgstr ""

#: classes/class-rates.php:63
msgid ""
"There was a problem connecting to the exchange rates API. Please check your "
"internet connection."
msgstr ""

#: classes/class-rates.php:72
msgid "Empty response received from the exchange rates API."
msgstr ""

#: classes/class-rates.php:83
msgid "Invalid JSON response received from the exchange rates API."
msgstr ""

#: classes/class-rates.php:92
msgid ""
"No exchange rates found in the API response. Please check your API key is "
"valid and you have usage quota."
msgstr ""

#: classes/class-rates.php:103
msgid ""
"Insufficient exchange rates received from the API. Please check your API key "
"is valid and you have usage quota."
msgstr ""

#: classes/class-reviews-post-type.php:31
#: elementor/template-part/single-agency/agency-reviews.php:26
#: elementor/template-part/single-agent/agent-reviews.php:26
msgid "Review"
msgstr ""

#: classes/class-reviews-post-type.php:33
msgid "Add New Review"
msgstr ""

#: classes/class-reviews-post-type.php:34
msgid "Edit Review"
msgstr ""

#: classes/class-reviews-post-type.php:35
msgid "New Review"
msgstr ""

#: classes/class-reviews-post-type.php:36
msgid "View Review"
msgstr ""

#: classes/class-reviews-post-type.php:37
msgid "Search Review"
msgstr ""

#: classes/class-reviews-post-type.php:38
msgid "No Review found"
msgstr ""

#: classes/class-reviews-post-type.php:39
msgid "No Review found in Trash"
msgstr ""

#: classes/class-reviews-post-type.php:103
msgid "Stars"
msgstr ""

#: classes/class-reviews-post-type.php:104
msgid "Review On"
msgstr ""

#: classes/class-reviews-post-type.php:175
msgid "Unapprove"
msgstr ""

#: classes/class-taxonomies.php:44
msgid "Taxonomy settings saved successfully!"
msgstr ""

#: classes/class-taxonomies.php:53
msgid "Taxonomies Management"
msgstr ""

#: classes/class-taxonomies.php:81
msgid "Total Taxonomies"
msgstr ""

#: classes/class-taxonomies.php:91
msgid "Enabled Taxonomies"
msgstr ""

#: classes/class-taxonomies.php:101
msgid "Disabled Taxonomies"
msgstr ""

#: classes/class-taxonomies.php:111
msgid "Location Coverage"
msgstr ""

#: classes/class-taxonomies.php:121
msgid "Location Taxonomies Configuration"
msgstr ""

#: classes/class-taxonomies.php:129
msgid ""
"Enable or disable location taxonomies for property categorization. Disabled "
"taxonomies will not appear in the admin area or frontend search filters."
msgstr ""

#: classes/class-taxonomies.php:146
msgid "Organize properties by country for international listings"
msgstr ""

#: classes/class-taxonomies.php:153 classes/class-taxonomies.php:292
msgid "County / State"
msgstr ""

#: classes/class-taxonomies.php:160
msgid "Categorize properties by state, province, or county"
msgstr ""

#: classes/class-taxonomies.php:174
msgid "Group properties by city for local market organization"
msgstr ""

#: classes/class-taxonomies.php:188
msgid "Define specific neighborhoods or areas within cities"
msgstr ""

#: classes/class-taxonomies.php:201
msgid "Location Hierarchy"
msgstr ""

#: classes/class-taxonomies.php:212
msgid "Country Level"
msgstr ""

#: classes/class-taxonomies.php:213
msgid ""
"Top-level geographic classification for international property listings and "
"global market coverage."
msgstr ""

#: classes/class-taxonomies.php:222
msgid "State/Province Level"
msgstr ""

#: classes/class-taxonomies.php:223
msgid ""
"Regional classification within countries for better property organization "
"and regional market analysis."
msgstr ""

#: classes/class-taxonomies.php:232
msgid "City Level"
msgstr ""

#: classes/class-taxonomies.php:233
msgid ""
"Urban area classification for local market focus and city-specific property "
"searches."
msgstr ""

#: classes/class-taxonomies.php:242
msgid "Area/Neighborhood Level"
msgstr ""

#: classes/class-taxonomies.php:243
msgid ""
"Granular location classification for specific neighborhoods, districts, or "
"local areas within cities."
msgstr ""

#: classes/class-taxonomies.php:282
msgid "Taxonomies"
msgstr ""

#: classes/class-taxonomies.php:326
msgid ""
"Disable Taxonomies which you do not want to show(if disabled then these will "
"not show on back-end and front-end)"
msgstr ""

#: classes/class-testimonials-post-type.php:34
msgid "Testimonial"
msgstr ""

#: classes/class-testimonials-post-type.php:36
msgid "Add New Testimonial"
msgstr ""

#: classes/class-testimonials-post-type.php:37
msgid "Edit Testimonial"
msgstr ""

#: classes/class-testimonials-post-type.php:38
msgid "New Testimonial"
msgstr ""

#: classes/class-testimonials-post-type.php:39
msgid "View Testimonial"
msgstr ""

#: classes/class-testimonials-post-type.php:41
msgid "No Testimonial found"
msgstr ""

#: classes/class-testimonials-post-type.php:42
msgid "No Testimonial found in Trash"
msgstr ""

#: classes/class-user-packages-post-type.php:39
msgid "Users Package Info"
msgstr ""

#: classes/class-user-packages-post-type.php:86
msgid "Package Holder"
msgstr ""

#: classes/class-user-packages-post-type.php:87
msgid "Package"
msgstr ""

#: classes/class-user-packages-post-type.php:88
msgid "Available Listings"
msgstr ""

#: classes/class-user-packages-post-type.php:89
msgid "Featured Available"
msgstr ""

#: classes/class-user-packages-post-type.php:90
msgid "Activation"
msgstr ""

#: classes/class-user-packages-post-type.php:91
msgid "Expiry"
msgstr ""

#: demo-data/demo-importer.php:3
msgid "Best if used on new WordPress install."
msgstr ""

#: demo-data/demo-importer.php:4
msgid "Images are for demo purpose only."
msgstr ""

#: demo-data/demo-importer.php:27
msgid "Demo Import"
msgstr ""

#: demo-data/demo-importer.php:28
msgid "Demo Importer"
msgstr ""

#: demo-data/demo-importer.php:53 demo-data/demo-importer.php:70
#: demo-data/demo-importer.php:87 demo-data/demo-importer.php:104
#: demo-data/demo-importer.php:121 demo-data/demo-importer.php:138
#: demo-data/demo-importer.php:155 demo-data/demo-importer.php:172
#: demo-data/demo-importer.php:189 demo-data/demo-importer.php:206
#: demo-data/demo-importer.php:223 demo-data/demo-importer.php:240
#: demo-data/demo-importer.php:257 demo-data/demo-importer.php:274
#: demo-data/demo-importer.php:291 demo-data/demo-importer.php:308
#: demo-data/demo-importer.php:325 demo-data/demo-importer.php:342
#: demo-data/demo-importer.php:359 demo-data/demo-importer.php:376
#: demo-data/demo-importer.php:393 demo-data/demo-importer.php:409
#: demo-data/demo-importer.php:425 demo-data/demo-importer.php:441
#: demo-data/demo-importer.php:457 demo-data/demo-importer.php:473
#: demo-data/demo-importer.php:489 demo-data/demo-importer.php:505
#: demo-data/demo-importer.php:521 demo-data/demo-importer.php:537
#: demo-data/demo-importer.php:553 demo-data/demo-importer.php:569
#: demo-data/demo-importer.php:585 demo-data/demo-importer.php:601
#: demo-data/demo-importer.php:617 demo-data/demo-importer.php:634
#: demo-data/demo-importer.php:651 demo-data/demo-importer.php:668
#: demo-data/demo-importer.php:685 demo-data/demo-importer.php:702
#: demo-data/demo-importer.php:719 demo-data/demo-importer.php:736
#: demo-data/demo-importer.php:753 demo-data/demo-importer.php:769
#: demo-data/demo-importer.php:785 demo-data/demo-importer.php:801
#: demo-data/demo-importer.php:817
msgid ""
"After you import this demo, you will have to setup the slider separately."
msgstr ""

#: elementor/controls/address-control.php:37
#: elementor/widgets/single-property/property-address.php:19
#: elementor/widgets/single-property/property-title-area.php:316
#: elementor/widgets/single-property/section-toparea-v1.php:218
#: elementor/widgets/single-property/section-toparea-v2.php:241
#: elementor/widgets/single-property/section-toparea-v3.php:243
#: elementor/widgets/single-property/section-toparea-v5.php:218
#: elementor/widgets/single-property/section-toparea-v6.php:263
#: elementor/widgets/single-property/section-toparea-v7.php:238
msgid "Property Address"
msgstr ""

#: elementor/controls/address-control.php:38
#: elementor/widgets/property-meta-data.php:51
#: elementor/widgets/single-agency/agency-single-stats.php:69
#: elementor/widgets/single-agent/agent-single-stats.php:69
#: elementor/widgets/single-property/section-similar.php:85
msgid "Property Country"
msgstr ""

#: elementor/controls/address-control.php:39
#: elementor/widgets/property-meta-data.php:53
#: elementor/widgets/single-agency/agency-single-stats.php:68
#: elementor/widgets/single-agent/agent-single-stats.php:68
#: elementor/widgets/single-property/section-similar.php:86
msgid "Property State"
msgstr ""

#: elementor/controls/address-control.php:40
#: elementor/widgets/property-meta-data.php:52
#: elementor/widgets/single-agency/agency-single-stats.php:67
#: elementor/widgets/single-agent/agent-single-stats.php:67
#: elementor/widgets/single-property/section-similar.php:87
msgid "Property City"
msgstr ""

#: elementor/controls/address-control.php:41
#: elementor/widgets/property-meta-data.php:54
#: elementor/widgets/single-property/section-similar.php:88
msgid "Property Area"
msgstr ""

#: elementor/controls/address-control.php:42
msgid "Property Zip/Postal Code"
msgstr ""

#: elementor/controls/autocomplete.php:50
#: elementor/widgets/search-builder-old.php:555
#: elementor/widgets/search-builder.php:542
#: templates/fields-builder/index.php:57
msgid "Search"
msgstr ""

#: elementor/controls/details-control.php:45
#: elementor/widgets/single-property/property-price.php:20
#: elementor/widgets/single-property/property-title-area.php:398
#: elementor/widgets/single-property/section-toparea-v1.php:300
#: elementor/widgets/single-property/section-toparea-v2.php:323
#: elementor/widgets/single-property/section-toparea-v3.php:325
#: elementor/widgets/single-property/section-toparea-v5.php:300
#: elementor/widgets/single-property/section-toparea-v6.php:345
#: elementor/widgets/single-property/section-toparea-v7.php:320
msgid "Property Price"
msgstr ""

#: elementor/controls/details-control.php:46
#: elementor/widgets/single-property/section-details.php:233
msgid "Property Size"
msgstr ""

#: elementor/controls/details-control.php:47
msgid "Property Land Size"
msgstr ""

#: elementor/controls/details-control.php:48
msgid "Property Bedrooms"
msgstr ""

#: elementor/controls/details-control.php:49
msgid "Property Rooms"
msgstr ""

#: elementor/controls/details-control.php:50
msgid "Property Bathrooms"
msgstr ""

#: elementor/controls/details-control.php:51
msgid "Property Garages"
msgstr ""

#: elementor/controls/details-control.php:52
msgid "Property Garage Size"
msgstr ""

#: elementor/controls/details-control.php:53
msgid "Property Year Built"
msgstr ""

#: elementor/controls/details-control.php:54
#: elementor/widgets/inquiry-form.php:86
#: elementor/widgets/properties-slider.php:115
#: elementor/widgets/property-meta-data.php:48
#: elementor/widgets/search-builder-old.php:488
#: elementor/widgets/search-builder.php:475
#: elementor/widgets/single-agency/agency-single-stats.php:66
#: elementor/widgets/single-agent/agent-single-stats.php:66
#: elementor/widgets/single-property/section-details.php:346
#: elementor/widgets/single-property/section-overview-v2.php:57
#: elementor/widgets/single-property/section-overview.php:56
#: elementor/widgets/single-property/section-similar.php:82
#: elementor/widgets/single-property/status-label.php:19
msgid "Property Status"
msgstr ""

#: elementor/controls/details-control.php:55
#: elementor/traits/Houzez_Property_Cards_Traits.php:1113
#: elementor/widgets/inquiry-form.php:85
#: elementor/widgets/properties-recent-viewed.php:334
#: elementor/widgets/properties-slider.php:103
#: elementor/widgets/property-cards-v1.php:286
#: elementor/widgets/property-cards-v2.php:270
#: elementor/widgets/property-cards-v4.php:281
#: elementor/widgets/property-cards-v5.php:205
#: elementor/widgets/property-cards-v7.php:270
#: elementor/widgets/property-cards-v8.php:256
#: elementor/widgets/property-carousel-v1.php:261
#: elementor/widgets/property-carousel-v2.php:246
#: elementor/widgets/property-carousel-v5.php:182
#: elementor/widgets/property-carousel-v7.php:246
#: elementor/widgets/property-meta-data.php:49
#: elementor/widgets/search-builder-old.php:477
#: elementor/widgets/search-builder.php:464
#: elementor/widgets/single-agency/agency-single-stats.php:65
#: elementor/widgets/single-agent/agent-single-stats.php:65
#: elementor/widgets/single-property/section-details.php:337
#: elementor/widgets/single-property/section-overview-v2.php:50
#: elementor/widgets/single-property/section-overview-v2.php:193
#: elementor/widgets/single-property/section-overview.php:48
#: elementor/widgets/single-property/section-overview.php:192
#: elementor/widgets/single-property/section-similar.php:81
msgid "Property Type"
msgstr ""

#: elementor/elementor.php:76
msgid "Houzez Elements"
msgstr ""

#: elementor/elementor.php:84 elementor/elementor.php:92
msgid "Houzez Header & Footer"
msgstr ""

#: elementor/elementor.php:100 elementor/elementor.php:108
msgid "Houzez Single Property"
msgstr ""

#: elementor/elementor.php:116 elementor/elementor.php:124
msgid "Houzez Single Agent"
msgstr ""

#: elementor/elementor.php:132 elementor/elementor.php:140
msgid "Houzez Single Agency"
msgstr ""

#: elementor/elementor.php:148
msgid "Houzez Loop Builder"
msgstr ""

#: elementor/elementor.php:156 elementor/elementor.php:164
msgid "Houzez Single Post"
msgstr ""

#: elementor/elementor.php:172
msgid "Houzez Sidebar/Footer Widgets "
msgstr ""

#: elementor/tags/property-content.php:9
#: elementor/widgets/single-property/property-content.php:16
msgid "Property Content"
msgstr ""

#: elementor/tags/property-excerpt.php:9
#: elementor/widgets/single-property/property-excerpt.php:16
msgid "Property Excerpt"
msgstr ""

#: elementor/tags/property-title.php:9
#: elementor/traits/Houzez_Property_Cards_Traits.php:969
#: elementor/widgets/properties-recent-viewed.php:281
#: elementor/widgets/property-cards-v1.php:221
#: elementor/widgets/property-cards-v2.php:205
#: elementor/widgets/property-cards-v3.php:177
#: elementor/widgets/property-cards-v4.php:216
#: elementor/widgets/property-cards-v5.php:177
#: elementor/widgets/property-cards-v6.php:183
#: elementor/widgets/property-cards-v7.php:217
#: elementor/widgets/property-cards-v8.php:191
#: elementor/widgets/property-carousel-v1.php:196
#: elementor/widgets/property-carousel-v2.php:181
#: elementor/widgets/property-carousel-v3.php:153
#: elementor/widgets/property-carousel-v5.php:154
#: elementor/widgets/property-carousel-v6.php:155
#: elementor/widgets/property-carousel-v7.php:193
#: elementor/widgets/single-property/property-title-area.php:196
#: elementor/widgets/single-property/property-title.php:17
#: elementor/widgets/single-property/section-toparea-v1.php:98
#: elementor/widgets/single-property/section-toparea-v2.php:121
#: elementor/widgets/single-property/section-toparea-v3.php:123
#: elementor/widgets/single-property/section-toparea-v5.php:98
#: elementor/widgets/single-property/section-toparea-v6.php:143
#: elementor/widgets/single-property/section-toparea-v7.php:118
#: statistics/houzez-statistics.php:113 statistics/houzez-statistics.php:190
#: statistics/houzez-statistics.php:238 statistics/houzez-statistics.php:318
msgid "Property Title"
msgstr ""

#: elementor/tags/test.php:29
msgid "Server Variable"
msgstr ""

#: elementor/tags/test.php:82
msgid "Param Name"
msgstr ""

#: elementor/template-part/listing-tabs.php:40
#: elementor/traits/Houzez_Filters_Traits.php:202
#: elementor/widgets/listings-tabs.php:356
#: elementor/widgets/properties-ajax-tabs.php:478
#: extensions/meta-box/inc/fields/select.php:80
msgid "All"
msgstr ""

#: elementor/template-part/single-agency/agency-listings-review.php:129
#: elementor/template-part/single-agent/agent-listings-review.php:69
msgid "Listings"
msgstr ""

#: elementor/template-part/single-agency/agency-profile-v1.php:59
#: elementor/template-part/single-agent/agent-profile-v1.php:75
msgid "Send Email"
msgstr ""

#: elementor/template-part/single-agency/agency-profile-v1.php:65
#: elementor/template-part/single-agent/agent-profile-v1.php:81
#: elementor/widgets/agent-card.php:1028
msgid "Call"
msgstr ""

#: elementor/template-part/single-agency/agency-profile-v2.php:34
#: elementor/template-part/single-agent/agent-profile-v2.php:53
msgid "Ask a question"
msgstr ""

#: elementor/template-part/single-agency/agency-reviews.php:41
#: elementor/template-part/single-agent/agent-reviews.php:41
msgid "out of"
msgstr ""

#: elementor/template-part/single-agency/agency-reviews.php:49
#: elementor/template-part/single-agent/agent-reviews.php:49
msgid "Leave a Review"
msgstr ""

#: elementor/template-part/single-agency/agency-reviews.php:78
#: elementor/template-part/single-agent/agent-reviews.php:78
#: elementor/widgets/single-post/post-navigation.php:68
msgid "Previous"
msgstr ""

#: elementor/template-part/single-agency/agency-reviews.php:83
#: elementor/template-part/single-agent/agent-reviews.php:83
#: elementor/widgets/google-map.php:330 elementor/widgets/mapbox.php:351
#: elementor/widgets/open-street-map.php:323
#: elementor/widgets/single-post/post-navigation.php:80
#: functions/helpers.php:477 shortcodes/agents.php:159
#: shortcodes/blog-posts-carousel.php:114 shortcodes/partners.php:35
msgid "Next"
msgstr ""

#: elementor/template-part/single-agency/agency-single-stats.php:68
#: elementor/template-part/single-agent/agent-single-stats.php:55
#: elementor/widgets/single-property/section-energy.php:273
msgid "Other"
msgstr ""

#: elementor/template-part/single-agency/contact-form.php:67
#: elementor/template-part/single-agent/contact-form.php:65
msgid "Your Name"
msgstr ""

#: elementor/template-part/single-agency/contact-form.php:78
#: elementor/template-part/single-agent/contact-form.php:76
#: elementor/widgets/contact-form.php:88 elementor/widgets/contact-form.php:278
#: elementor/widgets/contact-form.php:279
#: elementor/widgets/contact-form.php:521
#: elementor/widgets/inquiry-form.php:106
#: elementor/widgets/inquiry-form.php:506
#: elementor/widgets/single-agency/agency-contact.php:124
#: elementor/widgets/single-agent/agent-contact.php:124
msgid "Email"
msgstr ""

#: elementor/template-part/single-agency/contact-form.php:83
#: elementor/template-part/single-agent/contact-form.php:81
#: elementor/widgets/contact-form.php:90 elementor/widgets/contact-form.php:285
#: elementor/widgets/contact-form.php:286
#: elementor/widgets/inquiry-form.php:109
msgid "Message"
msgstr ""

#: elementor/template-part/single-agency/contact-form.php:83
#: elementor/template-part/single-agent/contact-form.php:81
#, php-format
msgid ""
"Hi %s, I saw your profile on %s and wanted to see if i can get some help"
msgstr ""

#: elementor/template-part/single-agency/contact-form.php:126
#: elementor/template-part/single-agent/contact-form.php:124
#: elementor/widgets/contact-form.php:431
#: elementor/widgets/contact-form.php:432
#: elementor/widgets/inquiry-form.php:416
#: elementor/widgets/inquiry-form.php:417
msgid "Submit"
msgstr ""

#: elementor/template-part/single-property/media-tabs-gallery.php:34
#: elementor/template-part/single-property/media-tabs.php:34
msgid "Loading Virtual Tour..."
msgstr ""

#: elementor/traits/Houzez_Button_Traits.php:33
#: elementor/widgets/contact-form.php:300
#: elementor/widgets/contact-form.php:443
#: elementor/widgets/inquiry-form.php:287
#: elementor/widgets/inquiry-form.php:428
#: elementor/widgets/search-builder-old.php:569
#: elementor/widgets/search-builder-old.php:588
#: elementor/widgets/search-builder.php:556
#: elementor/widgets/search-builder.php:575
#: elementor/widgets/search-builder.php:756
msgid "Extra Small"
msgstr ""

#: elementor/traits/Houzez_Button_Traits.php:34
#: elementor/widgets/contact-form.php:301
#: elementor/widgets/contact-form.php:444
#: elementor/widgets/inquiry-form.php:288
#: elementor/widgets/inquiry-form.php:429
#: elementor/widgets/search-builder-old.php:570
#: elementor/widgets/search-builder-old.php:589
#: elementor/widgets/search-builder.php:557
#: elementor/widgets/search-builder.php:576
#: elementor/widgets/search-builder.php:757
msgid "Small"
msgstr ""

#: elementor/traits/Houzez_Button_Traits.php:35
#: elementor/widgets/contact-form.php:302
#: elementor/widgets/contact-form.php:445
#: elementor/widgets/inquiry-form.php:289
#: elementor/widgets/inquiry-form.php:430
#: elementor/widgets/search-builder-old.php:571
#: elementor/widgets/search-builder-old.php:590
#: elementor/widgets/search-builder.php:558
#: elementor/widgets/search-builder.php:577
#: elementor/widgets/search-builder.php:758
msgid "Medium"
msgstr ""

#: elementor/traits/Houzez_Button_Traits.php:36
#: elementor/widgets/contact-form.php:303
#: elementor/widgets/contact-form.php:446
#: elementor/widgets/inquiry-form.php:290
#: elementor/widgets/inquiry-form.php:431
#: elementor/widgets/search-builder-old.php:572
#: elementor/widgets/search-builder-old.php:591
#: elementor/widgets/search-builder.php:559
#: elementor/widgets/search-builder.php:578
#: elementor/widgets/search-builder.php:759
msgid "Large"
msgstr ""

#: elementor/traits/Houzez_Button_Traits.php:37
#: elementor/widgets/contact-form.php:304
#: elementor/widgets/contact-form.php:447
#: elementor/widgets/inquiry-form.php:291
#: elementor/widgets/inquiry-form.php:432
#: elementor/widgets/search-builder-old.php:573
#: elementor/widgets/search-builder-old.php:592
#: elementor/widgets/search-builder.php:560
#: elementor/widgets/search-builder.php:579
#: elementor/widgets/search-builder.php:760
msgid "Extra Large"
msgstr ""

#: elementor/traits/Houzez_Button_Traits.php:55
#: elementor/widgets/agents-grid.php:445 elementor/widgets/agents.php:263
#: elementor/widgets/properties-ajax-tabs.php:116
#: elementor/widgets/single-agent/agent-meta.php:55
#: elementor/widgets/single-agent/agent-profile-v1.php:65
#: elementor/widgets/team-member.php:104 elementor/widgets/team-member.php:270
#: templates/currency/currency-list.php:69
msgid "Position"
msgstr ""

#: elementor/traits/Houzez_Button_Traits.php:59
#: elementor/traits/Houzez_Style_Traits.php:18
#: elementor/traits/Houzez_Style_Traits.php:153
#: elementor/traits/Houzez_Style_Traits.php:471
#: elementor/widgets/contact-form.php:481
#: elementor/widgets/create-listing-btn.php:82
#: elementor/widgets/header-footer/create-listing-btn.php:98
#: elementor/widgets/header-footer/login-modal.php:142
#: elementor/widgets/header-footer/login-modal.php:167
#: elementor/widgets/header-footer/menu.php:195
#: elementor/widgets/header-footer/menu.php:382
#: elementor/widgets/header-footer/site-logo.php:441
#: elementor/widgets/inquiry-form.php:466
#: elementor/widgets/listings-tabs.php:278
#: elementor/widgets/login-modal.php:129 elementor/widgets/login-modal.php:154
#: elementor/widgets/page-title.php:114
#: elementor/widgets/properties-ajax-tabs.php:121
#: elementor/widgets/search-builder-old.php:616
#: elementor/widgets/search-builder-old.php:1210
#: elementor/widgets/search-builder.php:603
#: elementor/widgets/search-builder.php:1465
#: elementor/widgets/section-title.php:148
#: elementor/widgets/single-agency/agency-content.php:51
#: elementor/widgets/single-agency/agency-excerpt.php:51
#: elementor/widgets/single-agent/agent-content.php:51
#: elementor/widgets/single-agent/agent-excerpt.php:51
#: elementor/widgets/single-post/author-box.php:173
#: elementor/widgets/single-post/author-box.php:197
#: elementor/widgets/single-post/post-content.php:51
#: elementor/widgets/single-post/post-excerpt.php:51
#: elementor/widgets/single-property/featured-image.php:94
#: elementor/widgets/single-property/featured-label.php:56
#: elementor/widgets/single-property/item-label.php:56
#: elementor/widgets/single-property/item-tools.php:175
#: elementor/widgets/single-property/property-address.php:125
#: elementor/widgets/single-property/property-content.php:53
#: elementor/widgets/single-property/property-excerpt.php:52
#: elementor/widgets/single-property/property-price.php:72
#: elementor/widgets/single-property/section-description.php:198
#: elementor/widgets/single-property/section-sublistings.php:132
#: elementor/widgets/single-property/status-label.php:57
#: elementor/widgets/taxonomies-list.php:210
#: elementor/widgets/taxonomies-list.php:495
msgid "Left"
msgstr ""

#: elementor/traits/Houzez_Button_Traits.php:63
#: elementor/traits/Houzez_Button_Traits.php:95
#: elementor/traits/Houzez_Style_Traits.php:22
#: elementor/traits/Houzez_Style_Traits.php:157
#: elementor/traits/Houzez_Style_Traits.php:475
#: elementor/widgets/contact-form.php:485
#: elementor/widgets/create-listing-btn.php:86
#: elementor/widgets/header-footer/create-listing-btn.php:102
#: elementor/widgets/header-footer/login-modal.php:146
#: elementor/widgets/header-footer/menu.php:199
#: elementor/widgets/header-footer/menu.php:386
#: elementor/widgets/header-footer/site-logo.php:445
#: elementor/widgets/inquiry-form.php:470
#: elementor/widgets/listings-tabs.php:282
#: elementor/widgets/login-modal.php:133 elementor/widgets/page-title.php:118
#: elementor/widgets/properties-ajax-tabs.php:125
#: elementor/widgets/search-builder-old.php:620
#: elementor/widgets/search-builder-old.php:1214
#: elementor/widgets/search-builder.php:607
#: elementor/widgets/search-builder.php:1469
#: elementor/widgets/section-title.php:152
#: elementor/widgets/single-agency/agency-content.php:55
#: elementor/widgets/single-agency/agency-excerpt.php:55
#: elementor/widgets/single-agent/agent-content.php:55
#: elementor/widgets/single-agent/agent-excerpt.php:55
#: elementor/widgets/single-post/author-box.php:201
#: elementor/widgets/single-post/post-content.php:55
#: elementor/widgets/single-post/post-excerpt.php:55
#: elementor/widgets/single-post/post-info.php:413
#: elementor/widgets/single-property/featured-image.php:98
#: elementor/widgets/single-property/featured-label.php:60
#: elementor/widgets/single-property/item-label.php:60
#: elementor/widgets/single-property/item-tools.php:179
#: elementor/widgets/single-property/property-address.php:129
#: elementor/widgets/single-property/property-content.php:57
#: elementor/widgets/single-property/property-excerpt.php:56
#: elementor/widgets/single-property/property-price.php:76
#: elementor/widgets/single-property/section-description.php:202
#: elementor/widgets/single-property/section-overview-v2.php:270
#: elementor/widgets/single-property/section-overview.php:347
#: elementor/widgets/single-property/section-sublistings.php:136
#: elementor/widgets/single-property/status-label.php:61
#: elementor/widgets/taxonomies-list.php:214
#: elementor/widgets/taxonomies-list.php:499
#: elementor/widgets/taxonomies-list.php:530
msgid "Center"
msgstr ""

#: elementor/traits/Houzez_Button_Traits.php:67
#: elementor/traits/Houzez_Style_Traits.php:26
#: elementor/traits/Houzez_Style_Traits.php:161
#: elementor/traits/Houzez_Style_Traits.php:479
#: elementor/widgets/contact-form.php:489
#: elementor/widgets/create-listing-btn.php:90
#: elementor/widgets/header-footer/create-listing-btn.php:106
#: elementor/widgets/header-footer/login-modal.php:150
#: elementor/widgets/header-footer/login-modal.php:171
#: elementor/widgets/header-footer/menu.php:203
#: elementor/widgets/header-footer/menu.php:390
#: elementor/widgets/header-footer/site-logo.php:449
#: elementor/widgets/inquiry-form.php:474 elementor/widgets/login-modal.php:137
#: elementor/widgets/login-modal.php:158 elementor/widgets/page-title.php:122
#: elementor/widgets/properties-ajax-tabs.php:129
#: elementor/widgets/search-builder-old.php:624
#: elementor/widgets/search-builder.php:611
#: elementor/widgets/section-title.php:156
#: elementor/widgets/single-agency/agency-content.php:59
#: elementor/widgets/single-agency/agency-excerpt.php:59
#: elementor/widgets/single-agent/agent-content.php:59
#: elementor/widgets/single-agent/agent-excerpt.php:59
#: elementor/widgets/single-post/author-box.php:181
#: elementor/widgets/single-post/author-box.php:205
#: elementor/widgets/single-post/post-content.php:59
#: elementor/widgets/single-post/post-excerpt.php:59
#: elementor/widgets/single-property/featured-image.php:102
#: elementor/widgets/single-property/featured-label.php:64
#: elementor/widgets/single-property/item-label.php:64
#: elementor/widgets/single-property/item-tools.php:183
#: elementor/widgets/single-property/property-address.php:133
#: elementor/widgets/single-property/property-content.php:61
#: elementor/widgets/single-property/property-excerpt.php:60
#: elementor/widgets/single-property/property-price.php:80
#: elementor/widgets/single-property/section-description.php:206
#: elementor/widgets/single-property/section-sublistings.php:140
#: elementor/widgets/single-property/status-label.php:65
#: elementor/widgets/taxonomies-list.php:218
#: elementor/widgets/taxonomies-list.php:503
msgid "Right"
msgstr ""

#: elementor/traits/Houzez_Button_Traits.php:71
#: elementor/widgets/header-footer/menu.php:207
msgid "Stretch"
msgstr ""

#: elementor/traits/Houzez_Button_Traits.php:87
#: elementor/traits/Houzez_Style_Traits.php:14
#: elementor/traits/Houzez_Style_Traits.php:149
#: elementor/traits/Houzez_Style_Traits.php:467
#: elementor/widgets/contact-form.php:477
#: elementor/widgets/create-listing-btn.php:78
#: elementor/widgets/header-footer/create-listing-btn.php:94
#: elementor/widgets/header-footer/login-modal.php:138
#: elementor/widgets/header-footer/site-logo.php:437
#: elementor/widgets/inquiry-form.php:462
#: elementor/widgets/listings-tabs.php:274
#: elementor/widgets/login-modal.php:125 elementor/widgets/page-title.php:110
#: elementor/widgets/search-builder-old.php:1206
#: elementor/widgets/search-builder.php:1461
#: elementor/widgets/section-title.php:144
#: elementor/widgets/single-agency/agency-content.php:47
#: elementor/widgets/single-agency/agency-excerpt.php:47
#: elementor/widgets/single-agent/agent-content.php:47
#: elementor/widgets/single-agent/agent-excerpt.php:47
#: elementor/widgets/single-post/author-box.php:193
#: elementor/widgets/single-post/post-content.php:47
#: elementor/widgets/single-post/post-excerpt.php:47
#: elementor/widgets/single-post/post-info.php:405
#: elementor/widgets/single-property/featured-image.php:90
#: elementor/widgets/single-property/featured-label.php:52
#: elementor/widgets/single-property/item-label.php:52
#: elementor/widgets/single-property/item-tools.php:170
#: elementor/widgets/single-property/property-address.php:120
#: elementor/widgets/single-property/property-content.php:49
#: elementor/widgets/single-property/property-excerpt.php:48
#: elementor/widgets/single-property/property-price.php:67
#: elementor/widgets/single-property/section-description.php:194
#: elementor/widgets/single-property/section-sublistings.php:128
#: elementor/widgets/single-property/status-label.php:53
#: elementor/widgets/taxonomies-list.php:206
msgid "Alignment"
msgstr ""

#: elementor/traits/Houzez_Button_Traits.php:91
#: elementor/widgets/single-agency/agency-call-btn.php:108
#: elementor/widgets/single-agency/agency-contact-btn.php:96
#: elementor/widgets/single-agency/agency-line-btn.php:96
#: elementor/widgets/single-agency/agency-telegram-btn.php:96
#: elementor/widgets/single-agency/agency-whatsapp-btn.php:96
#: elementor/widgets/single-agent/agent-call-btn.php:108
#: elementor/widgets/single-agent/agent-contact-btn.php:96
#: elementor/widgets/single-agent/agent-line-btn.php:96
#: elementor/widgets/single-agent/agent-telegram-btn.php:96
#: elementor/widgets/single-agent/agent-whatsapp-btn.php:96
#: elementor/widgets/single-post/post-info.php:409
#: elementor/widgets/taxonomies-list.php:526
msgid "Start"
msgstr ""

#: elementor/traits/Houzez_Button_Traits.php:99
#: elementor/widgets/single-agency/agency-call-btn.php:112
#: elementor/widgets/single-agency/agency-contact-btn.php:100
#: elementor/widgets/single-agency/agency-line-btn.php:100
#: elementor/widgets/single-agency/agency-telegram-btn.php:100
#: elementor/widgets/single-agency/agency-whatsapp-btn.php:100
#: elementor/widgets/single-agent/agent-call-btn.php:112
#: elementor/widgets/single-agent/agent-contact-btn.php:100
#: elementor/widgets/single-agent/agent-line-btn.php:100
#: elementor/widgets/single-agent/agent-telegram-btn.php:100
#: elementor/widgets/single-agent/agent-whatsapp-btn.php:100
#: elementor/widgets/single-post/post-info.php:417
#: elementor/widgets/taxonomies-list.php:534
msgid "End"
msgstr ""

#: elementor/traits/Houzez_Button_Traits.php:103
msgid "Space between"
msgstr ""

#: elementor/traits/Houzez_Button_Traits.php:143
#: elementor/traits/Houzez_Button_Traits.php:346
#: elementor/traits/Houzez_Button_Traits.php:558
#: elementor/traits/Houzez_Button_Traits.php:761
#: elementor/traits/Houzez_Button_Traits.php:933
#: elementor/traits/Houzez_Form_Traits.php:338
#: elementor/traits/Houzez_Property_Cards_Traits.php:29
#: elementor/traits/Houzez_Property_Cards_Traits.php:158
#: elementor/traits/Houzez_Property_Cards_Traits.php:502
#: elementor/traits/Houzez_Property_Cards_Traits.php:660
#: elementor/traits/Houzez_Property_Cards_Traits.php:1530
#: elementor/traits/Houzez_Property_Cards_Traits.php:1802
#: elementor/traits/Houzez_Style_Traits.php:83
#: elementor/traits/Houzez_Style_Traits.php:324
#: elementor/widgets/advanced-search.php:242
#: elementor/widgets/agent-card.php:820 elementor/widgets/agents-grid.php:505
#: elementor/widgets/agents.php:305 elementor/widgets/banner-image.php:329
#: elementor/widgets/blog-posts-carousel.php:757
#: elementor/widgets/custom-carousel.php:616
#: elementor/widgets/custom-carousel.php:853
#: elementor/widgets/custom-carousel.php:1025
#: elementor/widgets/header-footer/area-switcher.php:188
#: elementor/widgets/header-footer/create-listing-btn.php:126
#: elementor/widgets/header-footer/currency.php:188
#: elementor/widgets/header-footer/login-modal.php:306
#: elementor/widgets/header-footer/menu.php:468
#: elementor/widgets/header-footer/menu.php:792
#: elementor/widgets/header-footer/menu.php:1072
#: elementor/widgets/header-footer/site-logo.php:244
#: elementor/widgets/page-title.php:179
#: elementor/widgets/properties-slider.php:137
#: elementor/widgets/property-cards-v8.php:559
#: elementor/widgets/search-builder-old.php:1241
#: elementor/widgets/search-builder.php:1496
#: elementor/widgets/search-builder.php:1639
#: elementor/widgets/search-builder.php:1808
#: elementor/widgets/single-agency/agency-listings-review.php:384
#: elementor/widgets/single-agency/agency-name.php:106
#: elementor/widgets/single-agency/agency-profile-v1.php:290
#: elementor/widgets/single-agency/agency-profile-v1.php:422
#: elementor/widgets/single-agency/agency-profile-v2.php:224
#: elementor/widgets/single-agency/agency-search.php:283
#: elementor/widgets/single-agent/agent-listings-review.php:389
#: elementor/widgets/single-agent/agent-name.php:106
#: elementor/widgets/single-agent/agent-profile-v1.php:302
#: elementor/widgets/single-agent/agent-profile-v1.php:434
#: elementor/widgets/single-agent/agent-profile-v2.php:236
#: elementor/widgets/single-agent/agent-search.php:283
#: elementor/widgets/single-post/author-box.php:669
#: elementor/widgets/single-post/post-navigation.php:123
#: elementor/widgets/single-post/post-navigation.php:208
#: elementor/widgets/single-property/featured-image.php:204
#: elementor/widgets/single-property/featured-label.php:131
#: elementor/widgets/single-property/item-label.php:131
#: elementor/widgets/single-property/section-contact-bottom.php:236
#: elementor/widgets/single-property/status-label.php:132
#: elementor/widgets/taxonomies-cards-carousel.php:415
#: elementor/widgets/taxonomies-cards.php:303
#: elementor/widgets/taxonomies-grids-carousel.php:340
#: elementor/widgets/taxonomies-grids-carousel.php:442
#: elementor/widgets/taxonomies-grids.php:288
#: elementor/widgets/taxonomies-list.php:373
#: elementor/widgets/taxonomies-list.php:605
#: elementor/widgets/taxonomies-list.php:721
#: elementor/widgets/testimonials-v3.php:122
msgid "Normal"
msgstr ""

#: elementor/traits/Houzez_Button_Traits.php:151
#: elementor/traits/Houzez_Button_Traits.php:192
#: elementor/traits/Houzez_Button_Traits.php:354
#: elementor/traits/Houzez_Button_Traits.php:407
#: elementor/traits/Houzez_Button_Traits.php:566
#: elementor/traits/Houzez_Button_Traits.php:607
#: elementor/traits/Houzez_Button_Traits.php:769
#: elementor/traits/Houzez_Button_Traits.php:810
#: elementor/traits/Houzez_Button_Traits.php:940
#: elementor/traits/Houzez_Button_Traits.php:989
#: elementor/traits/Houzez_Form_Traits.php:107
#: elementor/traits/Houzez_Form_Traits.php:205
#: elementor/traits/Houzez_Form_Traits.php:299
#: elementor/traits/Houzez_Form_Traits.php:357
#: elementor/traits/Houzez_Form_Traits.php:429
#: elementor/traits/Houzez_Property_Cards_Traits.php:36
#: elementor/traits/Houzez_Property_Cards_Traits.php:81
#: elementor/traits/Houzez_Property_Cards_Traits.php:1809
#: elementor/traits/Houzez_Property_Cards_Traits.php:1857
#: elementor/traits/Houzez_Style_Traits.php:44
#: elementor/traits/Houzez_Style_Traits.php:497
#: elementor/traits/Houzez_Style_Traits.php:578
#: elementor/traits/Houzez_Style_Traits.php:622
#: elementor/traits/Houzez_Style_Traits.php:733
#: elementor/traits/Houzez_Style_Traits.php:796
#: elementor/traits/Houzez_Style_Traits.php:842
#: elementor/traits/Houzez_Style_Traits.php:1441
#: elementor/traits/Houzez_Testimonials_Traits.php:74
#: elementor/traits/Houzez_Testimonials_Traits.php:123
#: elementor/traits/Houzez_Testimonials_Traits.php:170
#: elementor/widgets/advanced-search.php:170
#: elementor/widgets/advanced-search.php:261
#: elementor/widgets/advanced-search.php:333
#: elementor/widgets/agent-card.php:519 elementor/widgets/agent-card.php:659
#: elementor/widgets/agents-grid.php:523 elementor/widgets/agents-grid.php:563
#: elementor/widgets/agents.php:323 elementor/widgets/agents.php:363
#: elementor/widgets/custom-carousel.php:319
#: elementor/widgets/custom-carousel.php:355
#: elementor/widgets/custom-carousel.php:767
#: elementor/widgets/custom-carousel.php:803
#: elementor/widgets/custom-carousel.php:861
#: elementor/widgets/custom-carousel.php:902
#: elementor/widgets/header-footer/menu.php:475
#: elementor/widgets/header-footer/menu.php:499
#: elementor/widgets/header-footer/menu.php:517
#: elementor/widgets/header-footer/menu.php:564
#: elementor/widgets/header-footer/menu.php:799
#: elementor/widgets/header-footer/menu.php:837
#: elementor/widgets/header-footer/menu.php:885
#: elementor/widgets/header-footer/site-logo.php:547
#: elementor/widgets/icon-box.php:418 elementor/widgets/icon-box.php:447
#: elementor/widgets/icon-box.php:477 elementor/widgets/page-title.php:140
#: elementor/widgets/property-cards-v8.php:566
#: elementor/widgets/search-builder-old.php:858
#: elementor/widgets/search-builder-old.php:952
#: elementor/widgets/search-builder-old.php:1043
#: elementor/widgets/search-builder.php:996
#: elementor/widgets/search-builder.php:1204
#: elementor/widgets/search-builder.php:1298
#: elementor/widgets/single-agency/agency-about.php:108
#: elementor/widgets/single-agency/agency-contact.php:285
#: elementor/widgets/single-agency/agency-contact.php:318
#: elementor/widgets/single-agency/agency-contact.php:348
#: elementor/widgets/single-agency/agency-contact.php:417
#: elementor/widgets/single-agency/agency-content.php:76
#: elementor/widgets/single-agency/agency-excerpt.php:76
#: elementor/widgets/single-agency/agency-listings-review.php:329
#: elementor/widgets/single-agency/agency-listings-review.php:403
#: elementor/widgets/single-agency/agency-name.php:182
#: elementor/widgets/single-agency/agency-profile-v1.php:166
#: elementor/widgets/single-agency/agency-profile-v1.php:309
#: elementor/widgets/single-agency/agency-profile-v1.php:381
#: elementor/widgets/single-agency/agency-profile-v1.php:441
#: elementor/widgets/single-agency/agency-profile-v1.php:513
#: elementor/widgets/single-agency/agency-profile-v2.php:100
#: elementor/widgets/single-agency/agency-search.php:86
#: elementor/widgets/single-agency/agency-search.php:204
#: elementor/widgets/single-agency/agency-search.php:302
#: elementor/widgets/single-agency/agency-search.php:374
#: elementor/widgets/single-agency/agency-single-stats.php:101
#: elementor/widgets/single-agency/agency-stats.php:65
#: elementor/widgets/single-agent/agent-about.php:108
#: elementor/widgets/single-agent/agent-contact.php:285
#: elementor/widgets/single-agent/agent-contact.php:318
#: elementor/widgets/single-agent/agent-contact.php:348
#: elementor/widgets/single-agent/agent-contact.php:417
#: elementor/widgets/single-agent/agent-content.php:76
#: elementor/widgets/single-agent/agent-excerpt.php:76
#: elementor/widgets/single-agent/agent-listings-review.php:334
#: elementor/widgets/single-agent/agent-listings-review.php:408
#: elementor/widgets/single-agent/agent-name.php:182
#: elementor/widgets/single-agent/agent-profile-v1.php:178
#: elementor/widgets/single-agent/agent-profile-v1.php:321
#: elementor/widgets/single-agent/agent-profile-v1.php:393
#: elementor/widgets/single-agent/agent-profile-v1.php:453
#: elementor/widgets/single-agent/agent-profile-v1.php:525
#: elementor/widgets/single-agent/agent-profile-v2.php:112
#: elementor/widgets/single-agent/agent-search.php:86
#: elementor/widgets/single-agent/agent-search.php:204
#: elementor/widgets/single-agent/agent-search.php:302
#: elementor/widgets/single-agent/agent-search.php:374
#: elementor/widgets/single-agent/agent-single-stats.php:101
#: elementor/widgets/single-agent/agent-stats.php:65
#: elementor/widgets/single-post/author-box.php:679
#: elementor/widgets/single-post/post-content.php:76
#: elementor/widgets/single-post/post-excerpt.php:76
#: elementor/widgets/single-post/post-info.php:664
#: elementor/widgets/single-property/breadcrumb.php:51
#: elementor/widgets/single-property/property-content.php:78
#: elementor/widgets/single-property/property-excerpt.php:77
#: elementor/widgets/single-property/property-title-area.php:151
#: elementor/widgets/single-property/section-contact-2.php:327
#: elementor/widgets/single-property/section-contact-2.php:531
#: elementor/widgets/single-property/section-contact-bottom.php:243
#: elementor/widgets/single-property/section-contact-bottom.php:281
#: elementor/widgets/single-property/section-contact-bottom.php:500
#: elementor/widgets/single-property/section-features.php:194
#: elementor/widgets/single-property/section-schedule-tour-v2.php:150
#: elementor/widgets/single-property/section-schedule-tour.php:164
#: elementor/widgets/single-property/section-toparea-v1.php:268
#: elementor/widgets/single-property/section-toparea-v1.php:321
#: elementor/widgets/single-property/section-toparea-v1.php:396
#: elementor/widgets/single-property/section-toparea-v5.php:118
#: elementor/widgets/sort-by.php:114 elementor/widgets/testimonials-v2.php:113
#: elementor/widgets/testimonials-v2.php:133
msgid "Text Color"
msgstr ""

#: elementor/traits/Houzez_Button_Traits.php:184
#: elementor/traits/Houzez_Button_Traits.php:399
#: elementor/traits/Houzez_Button_Traits.php:599
#: elementor/traits/Houzez_Button_Traits.php:802
#: elementor/traits/Houzez_Button_Traits.php:982
#: elementor/traits/Houzez_Form_Traits.php:410
#: elementor/traits/Houzez_Property_Cards_Traits.php:74
#: elementor/traits/Houzez_Property_Cards_Traits.php:202
#: elementor/traits/Houzez_Property_Cards_Traits.php:553
#: elementor/traits/Houzez_Property_Cards_Traits.php:758
#: elementor/traits/Houzez_Property_Cards_Traits.php:1647
#: elementor/traits/Houzez_Property_Cards_Traits.php:1850
#: elementor/traits/Houzez_Style_Traits.php:358
#: elementor/widgets/advanced-search.php:314
#: elementor/widgets/agent-card.php:876 elementor/widgets/agents-grid.php:545
#: elementor/widgets/agents.php:345 elementor/widgets/banner-image.php:363
#: elementor/widgets/blog-posts-carousel.php:801
#: elementor/widgets/custom-carousel.php:650
#: elementor/widgets/custom-carousel.php:894
#: elementor/widgets/custom-carousel.php:1063
#: elementor/widgets/header-footer/area-switcher.php:208
#: elementor/widgets/header-footer/create-listing-btn.php:168
#: elementor/widgets/header-footer/currency.php:208
#: elementor/widgets/header-footer/login-modal.php:373
#: elementor/widgets/header-footer/menu.php:492
#: elementor/widgets/header-footer/menu.php:830
#: elementor/widgets/header-footer/menu.php:1104
#: elementor/widgets/header-footer/site-logo.php:278
#: elementor/widgets/properties-slider.php:178
#: elementor/widgets/property-cards-v8.php:605
#: elementor/widgets/search-builder-old.php:1313
#: elementor/widgets/search-builder.php:1568
#: elementor/widgets/search-builder.php:1737
#: elementor/widgets/search-builder.php:1906
#: elementor/widgets/single-agency/agency-name.php:127
#: elementor/widgets/single-agency/agency-profile-v1.php:362
#: elementor/widgets/single-agency/agency-profile-v1.php:494
#: elementor/widgets/single-agency/agency-profile-v2.php:253
#: elementor/widgets/single-agency/agency-search.php:355
#: elementor/widgets/single-agent/agent-name.php:127
#: elementor/widgets/single-agent/agent-profile-v1.php:374
#: elementor/widgets/single-agent/agent-profile-v1.php:506
#: elementor/widgets/single-agent/agent-profile-v2.php:265
#: elementor/widgets/single-agent/agent-search.php:355
#: elementor/widgets/single-post/author-box.php:721
#: elementor/widgets/single-post/post-navigation.php:145
#: elementor/widgets/single-post/post-navigation.php:228
#: elementor/widgets/single-property/featured-image.php:238
#: elementor/widgets/single-property/featured-label.php:162
#: elementor/widgets/single-property/item-label.php:162
#: elementor/widgets/single-property/section-contact-bottom.php:274
#: elementor/widgets/single-property/status-label.php:163
#: elementor/widgets/taxonomies-cards-carousel.php:459
#: elementor/widgets/taxonomies-cards.php:329
#: elementor/widgets/taxonomies-grids-carousel.php:378
#: elementor/widgets/taxonomies-grids-carousel.php:486
#: elementor/widgets/taxonomies-grids.php:326
#: elementor/widgets/taxonomies-list.php:398
#: elementor/widgets/taxonomies-list.php:629
#: elementor/widgets/taxonomies-list.php:745
#: elementor/widgets/testimonials-v3.php:155
msgid "Hover"
msgstr ""

#: elementor/traits/Houzez_Button_Traits.php:221
#: elementor/traits/Houzez_Button_Traits.php:385
#: elementor/traits/Houzez_Button_Traits.php:436
#: elementor/traits/Houzez_Button_Traits.php:636
#: elementor/traits/Houzez_Button_Traits.php:839
#: elementor/traits/Houzez_Button_Traits.php:969
#: elementor/traits/Houzez_Button_Traits.php:1016
#: elementor/traits/Houzez_Form_Traits.php:247
#: elementor/traits/Houzez_Form_Traits.php:440
#: elementor/traits/Houzez_Property_Cards_Traits.php:60
#: elementor/traits/Houzez_Property_Cards_Traits.php:105
#: elementor/traits/Houzez_Property_Cards_Traits.php:189
#: elementor/traits/Houzez_Property_Cards_Traits.php:234
#: elementor/traits/Houzez_Property_Cards_Traits.php:535
#: elementor/traits/Houzez_Property_Cards_Traits.php:586
#: elementor/traits/Houzez_Property_Cards_Traits.php:884
#: elementor/traits/Houzez_Property_Cards_Traits.php:1835
#: elementor/traits/Houzez_Property_Cards_Traits.php:1883
#: elementor/traits/Houzez_Style_Traits.php:1001
#: elementor/traits/Houzez_Style_Traits.php:1354
#: elementor/widgets/advanced-search.php:344
#: elementor/widgets/blog-posts-carousel.php:680
#: elementor/widgets/blog-posts-carousel.php:788
#: elementor/widgets/blog-posts-carousel.php:833
#: elementor/widgets/blog-posts.php:635
#: elementor/widgets/custom-carousel.php:931
#: elementor/widgets/custom-carousel.php:1096
#: elementor/widgets/properties-slider.php:166
#: elementor/widgets/properties-slider.php:208
#: elementor/widgets/property-cards-v7.php:483
#: elementor/widgets/property-cards-v8.php:590
#: elementor/widgets/property-cards-v8.php:636
#: elementor/widgets/property-carousel-v7.php:453
#: elementor/widgets/search-builder-old.php:998
#: elementor/widgets/search-builder-old.php:1343
#: elementor/widgets/search-builder.php:1250
#: elementor/widgets/search-builder.php:1598
#: elementor/widgets/search-builder.php:1767
#: elementor/widgets/search-builder.php:1936
#: elementor/widgets/single-agency/agency-listings-review.php:414
#: elementor/widgets/single-agency/agency-profile-v1.php:128
#: elementor/widgets/single-agency/agency-profile-v1.php:392
#: elementor/widgets/single-agency/agency-profile-v1.php:524
#: elementor/widgets/single-agency/agency-search.php:240
#: elementor/widgets/single-agency/agency-search.php:385
#: elementor/widgets/single-agent/agent-listings-review.php:419
#: elementor/widgets/single-agent/agent-profile-v1.php:140
#: elementor/widgets/single-agent/agent-profile-v1.php:404
#: elementor/widgets/single-agent/agent-profile-v1.php:536
#: elementor/widgets/single-agent/agent-search.php:240
#: elementor/widgets/single-agent/agent-search.php:385
#: elementor/widgets/single-post/author-box.php:362
#: elementor/widgets/single-property/item-tools.php:146
#: elementor/widgets/single-property/property-title-area.php:617
#: elementor/widgets/single-property/section-address.php:331
#: elementor/widgets/single-property/section-attachments.php:125
#: elementor/widgets/single-property/section-calculator.php:162
#: elementor/widgets/single-property/section-contact-2.php:436
#: elementor/widgets/single-property/section-contact-bottom.php:142
#: elementor/widgets/single-property/section-contact-bottom.php:308
#: elementor/widgets/single-property/section-contact-bottom.php:580
#: elementor/widgets/single-property/section-details.php:425
#: elementor/widgets/single-property/section-energy.php:304
#: elementor/widgets/single-property/section-schedule-tour-v2.php:238
#: elementor/widgets/single-property/section-schedule-tour.php:221
#: elementor/widgets/single-property/section-similar.php:228
#: elementor/widgets/single-property/section-sublistings.php:104
#: elementor/widgets/single-property/section-toparea-v1.php:519
#: elementor/widgets/single-property/section-toparea-v2.php:541
#: elementor/widgets/single-property/section-toparea-v3.php:543
#: elementor/widgets/single-property/section-toparea-v5.php:518
#: elementor/widgets/single-property/section-toparea-v6.php:563
#: elementor/widgets/single-property/section-toparea-v7.php:538
#: elementor/widgets/taxonomies-cards-carousel.php:446
#: elementor/widgets/taxonomies-cards-carousel.php:490
#: elementor/widgets/taxonomies-grids-carousel.php:473
#: elementor/widgets/taxonomies-grids-carousel.php:517
#: elementor/widgets/testimonials-v3.php:185
msgid "Border Color"
msgstr ""

#: elementor/traits/Houzez_Button_Traits.php:236
#: elementor/traits/Houzez_Button_Traits.php:448
#: elementor/traits/Houzez_Button_Traits.php:651
#: elementor/traits/Houzez_Button_Traits.php:854
#: elementor/traits/Houzez_Button_Traits.php:1027
#: elementor/traits/Houzez_Style_Traits.php:391
#: elementor/widgets/banner-image.php:396
#: elementor/widgets/custom-carousel.php:683
#: elementor/widgets/custom-carousel.php:946
#: elementor/widgets/custom-carousel.php:1110
#: elementor/widgets/header-footer/site-logo.php:311
#: elementor/widgets/single-post/author-box.php:759
#: elementor/widgets/single-post/post-navigation.php:164
#: elementor/widgets/single-post/post-navigation.php:246
#: elementor/widgets/single-property/featured-image.php:271
#: elementor/widgets/single-property/section-contact-bottom.php:322
#: elementor/widgets/taxonomies-cards.php:354
#: elementor/widgets/taxonomies-grids-carousel.php:403
#: elementor/widgets/taxonomies-grids.php:351
#: elementor/widgets/taxonomies-list.php:418
#: elementor/widgets/taxonomies-list.php:648
#: elementor/widgets/taxonomies-list.php:764
#: elementor/widgets/testimonials-v3.php:199
msgid "Transition Duration"
msgstr ""

#: elementor/traits/Houzez_Button_Traits.php:251
#: elementor/traits/Houzez_Button_Traits.php:463
#: elementor/traits/Houzez_Button_Traits.php:666
#: elementor/traits/Houzez_Button_Traits.php:869
#: elementor/traits/Houzez_Style_Traits.php:409
#: elementor/widgets/single-property/featured-image.php:288
#: elementor/widgets/testimonials-v3.php:214
msgid "Hover Animation"
msgstr ""

#: elementor/traits/Houzez_Button_Traits.php:274
#: elementor/traits/Houzez_Button_Traits.php:486
#: elementor/traits/Houzez_Button_Traits.php:689
#: elementor/traits/Houzez_Button_Traits.php:892
#: elementor/traits/Houzez_Button_Traits.php:1056
#: elementor/traits/Houzez_Form_Traits.php:276
#: elementor/traits/Houzez_Form_Traits.php:384
#: elementor/traits/Houzez_Property_Cards_Traits.php:130
#: elementor/traits/Houzez_Style_Traits.php:430
#: elementor/traits/Houzez_Style_Traits.php:663
#: elementor/traits/Houzez_Style_Traits.php:778
#: elementor/traits/Houzez_Testimonials_Traits.php:219
#: elementor/widgets/advanced-search.php:215
#: elementor/widgets/advanced-search.php:288
#: elementor/widgets/agent-card.php:319 elementor/widgets/agent-card.php:585
#: elementor/widgets/agent-card.php:862 elementor/widgets/agent-card.php:914
#: elementor/widgets/agents-grid.php:335 elementor/widgets/agents.php:223
#: elementor/widgets/banner-image.php:426
#: elementor/widgets/custom-carousel.php:427
#: elementor/widgets/custom-carousel.php:714
#: elementor/widgets/custom-carousel.php:975
#: elementor/widgets/custom-carousel.php:1138
#: elementor/widgets/header-footer/menu.php:1175
#: elementor/widgets/header-footer/site-logo.php:341
#: elementor/widgets/icon-box.php:253
#: elementor/widgets/search-builder-old.php:1017
#: elementor/widgets/search-builder-old.php:1287
#: elementor/widgets/search-builder.php:1269
#: elementor/widgets/search-builder.php:1542
#: elementor/widgets/search-builder.php:1699
#: elementor/widgets/search-builder.php:1868
#: elementor/widgets/single-agency/agency-listings-review.php:356
#: elementor/widgets/single-agency/agency-name.php:260
#: elementor/widgets/single-agency/agency-profile-v1.php:336
#: elementor/widgets/single-agency/agency-profile-v1.php:468
#: elementor/widgets/single-agency/agency-search.php:257
#: elementor/widgets/single-agency/agency-search.php:329
#: elementor/widgets/single-agent/agent-listings-review.php:361
#: elementor/widgets/single-agent/agent-name.php:260
#: elementor/widgets/single-agent/agent-profile-v1.php:348
#: elementor/widgets/single-agent/agent-profile-v1.php:480
#: elementor/widgets/single-agent/agent-search.php:257
#: elementor/widgets/single-agent/agent-search.php:329
#: elementor/widgets/single-post/author-box.php:427
#: elementor/widgets/single-post/author-box.php:819
#: elementor/widgets/single-property/featured-image.php:309
#: elementor/widgets/single-property/section-contact-bottom.php:350
#: elementor/widgets/testimonials-v3.php:235
msgid "Border Radius"
msgstr ""

#: elementor/traits/Houzez_Button_Traits.php:296
#: elementor/traits/Houzez_Button_Traits.php:508
#: elementor/traits/Houzez_Button_Traits.php:711
#: elementor/traits/Houzez_Button_Traits.php:914
#: elementor/traits/Houzez_Form_Traits.php:141
#: elementor/traits/Houzez_Style_Traits.php:634
#: elementor/widgets/advanced-search.php:106
#: elementor/widgets/agent-card.php:290 elementor/widgets/agent-card.php:424
#: elementor/widgets/agent-card.php:555
#: elementor/widgets/blog-posts-carousel.php:716
#: elementor/widgets/blog-posts-v2.php:223 elementor/widgets/blog-posts.php:671
#: elementor/widgets/create-listing-btn.php:48
#: elementor/widgets/custom-carousel.php:442
#: elementor/widgets/custom-carousel.php:747
#: elementor/widgets/custom-carousel.php:997 elementor/widgets/grids.php:453
#: elementor/widgets/header-footer/create-listing-btn.php:64
#: elementor/widgets/header-footer/login-modal.php:219
#: elementor/widgets/header-footer/site-logo.php:586
#: elementor/widgets/icon-box.php:226 elementor/widgets/listings-tabs.php:247
#: elementor/widgets/login-modal.php:172
#: elementor/widgets/search-builder-old.php:900
#: elementor/widgets/search-builder-old.php:1169
#: elementor/widgets/search-builder-old.php:1299
#: elementor/widgets/search-builder.php:1039
#: elementor/widgets/search-builder.php:1424
#: elementor/widgets/search-builder.php:1554
#: elementor/widgets/search-builder.php:1711
#: elementor/widgets/search-builder.php:1880
#: elementor/widgets/single-agency/agency-about.php:120
#: elementor/widgets/single-agency/agency-contact.php:182
#: elementor/widgets/single-agency/agency-listings-review.php:511
#: elementor/widgets/single-agency/agency-listings.php:333
#: elementor/widgets/single-agency/agency-name.php:218
#: elementor/widgets/single-agency/agency-profile-v1.php:190
#: elementor/widgets/single-agency/agency-profile-v2.php:124
#: elementor/widgets/single-agency/agency-search.php:98
#: elementor/widgets/single-agency/agency-single-stats.php:133
#: elementor/widgets/single-agency/agency-stats.php:97
#: elementor/widgets/single-agent/agent-about.php:120
#: elementor/widgets/single-agent/agent-contact.php:182
#: elementor/widgets/single-agent/agent-listings-review.php:516
#: elementor/widgets/single-agent/agent-listings.php:338
#: elementor/widgets/single-agent/agent-name.php:218
#: elementor/widgets/single-agent/agent-profile-v1.php:202
#: elementor/widgets/single-agent/agent-profile-v2.php:136
#: elementor/widgets/single-agent/agent-search.php:98
#: elementor/widgets/single-agent/agent-single-stats.php:133
#: elementor/widgets/single-agent/agent-stats.php:97
#: elementor/widgets/single-post/author-box.php:846
#: elementor/widgets/single-property/featured-label.php:90
#: elementor/widgets/single-property/item-label.php:90
#: elementor/widgets/single-property/section-contact-2.php:284
#: elementor/widgets/single-property/section-contact-2.php:466
#: elementor/widgets/single-property/section-details.php:391
#: elementor/widgets/single-property/section-floorplan-v2.php:221
#: elementor/widgets/single-property/section-schedule-tour-v2.php:262
#: elementor/widgets/single-property/status-label.php:91
msgid "Padding"
msgstr ""

#: elementor/traits/Houzez_Filters_Traits.php:125
#: elementor/widgets/properties-ajax-tabs.php:402
msgid "Properties by Agents"
msgstr ""

#: elementor/traits/Houzez_Filters_Traits.php:136
#: elementor/widgets/properties-ajax-tabs.php:413
msgid "Properties by Agencies"
msgstr ""

#: elementor/traits/Houzez_Filters_Traits.php:147
#: elementor/widgets/inquiry-form.php:89
#: elementor/widgets/properties-ajax-tabs.php:424
msgid "Minimum Price"
msgstr ""

#: elementor/traits/Houzez_Filters_Traits.php:155
#: elementor/widgets/inquiry-form.php:90
#: elementor/widgets/properties-ajax-tabs.php:432
msgid "Maximum Price"
msgstr ""

#: elementor/traits/Houzez_Filters_Traits.php:164
#: elementor/widgets/properties-ajax-tabs.php:441
msgid "Minimum Beds"
msgstr ""

#: elementor/traits/Houzez_Filters_Traits.php:172
#: elementor/widgets/properties-ajax-tabs.php:449
msgid "Maximum Beds"
msgstr ""

#: elementor/traits/Houzez_Filters_Traits.php:181
#: elementor/widgets/properties-ajax-tabs.php:458
msgid "Minimum Baths"
msgstr ""

#: elementor/traits/Houzez_Filters_Traits.php:189
#: elementor/widgets/properties-ajax-tabs.php:466
msgid "Maximum Baths"
msgstr ""

#: elementor/traits/Houzez_Filters_Traits.php:199
#: elementor/widgets/properties-ajax-tabs.php:475
msgid "User Role"
msgstr ""

#: elementor/traits/Houzez_Filters_Traits.php:217
#: elementor/widgets/google-map.php:309 elementor/widgets/mapbox.php:293
#: elementor/widgets/open-street-map.php:299
#: elementor/widgets/properties-ajax-tabs.php:493
msgid "Featured Properties"
msgstr ""

#: elementor/traits/Houzez_Filters_Traits.php:220
#: elementor/widgets/properties-ajax-tabs.php:496
msgid "- Any -"
msgstr ""

#: elementor/traits/Houzez_Filters_Traits.php:221
#: elementor/widgets/properties-ajax-tabs.php:497
msgid "Without Featured"
msgstr ""

#: elementor/traits/Houzez_Filters_Traits.php:222
#: elementor/widgets/properties-ajax-tabs.php:498
msgid "Only Featured"
msgstr ""

#: elementor/traits/Houzez_Filters_Traits.php:224
#: elementor/widgets/properties-ajax-tabs.php:500
msgid ""
"You can make a post featured by clicking featured properties checkbox while "
"add/edit post"
msgstr ""

#: elementor/traits/Houzez_Filters_Traits.php:232
#: elementor/widgets/property-by-ids.php:134
msgid "Properties IDs"
msgstr ""

#: elementor/traits/Houzez_Filters_Traits.php:234
#: elementor/widgets/property-by-ids.php:136
msgid "Enter properties ids comma separated. Ex 12,305,34"
msgstr ""

#: elementor/traits/Houzez_Filters_Traits.php:244
#: elementor/widgets/blog-posts-v2.php:88 elementor/widgets/blog-posts.php:88
#: elementor/widgets/properties-ajax-tabs.php:610
#: elementor/widgets/properties-recent-viewed.php:129
#: elementor/widgets/single-agency/agency-image.php:50
#: elementor/widgets/single-agent/agent-image.php:50
#: elementor/widgets/single-post/post-image.php:50
#: elementor/widgets/single-property/featured-image.php:50
msgid "Thumbnail Size"
msgstr ""

#: elementor/traits/Houzez_Filters_Traits.php:257
#: elementor/widgets/properties-ajax-tabs.php:620
msgid "Sort By"
msgstr ""

#: elementor/traits/Houzez_Filters_Traits.php:268
#: elementor/widgets/google-map.php:203 elementor/widgets/mapbox.php:187
#: elementor/widgets/open-street-map.php:193
#: elementor/widgets/properties-ajax-tabs.php:631
#: elementor/widgets/properties-slider.php:93
#: elementor/widgets/single-agency/agency-listings-review.php:119
#: elementor/widgets/single-agency/agency-listings.php:119
#: elementor/widgets/single-agent/agent-listings-review.php:124
#: elementor/widgets/single-agent/agent-listings.php:124
msgid "Number of properties"
msgstr ""

#: elementor/traits/Houzez_Filters_Traits.php:289
#: elementor/widgets/properties-ajax-tabs.php:508
msgid "Post Status"
msgstr ""

#: elementor/traits/Houzez_Filters_Traits.php:426
#: elementor/widgets/grids.php:233
msgid "Show Child"
msgstr ""

#: elementor/traits/Houzez_Filters_Traits.php:440
#: elementor/widgets/grids.php:247
msgid "Hide Empty"
msgstr ""

#: elementor/traits/Houzez_Filters_Traits.php:454
#: elementor/widgets/grids.php:261
msgid "Hide Count"
msgstr ""

#: elementor/traits/Houzez_Filters_Traits.php:468
#: elementor/traits/Houzez_Testimonials_Traits.php:33
#: elementor/widgets/agents-grid.php:163 elementor/widgets/agents.php:161
#: elementor/widgets/grids.php:275 elementor/widgets/partners.php:88
msgid "Order By"
msgstr ""

#: elementor/traits/Houzez_Filters_Traits.php:472
#: elementor/widgets/grids.php:279 elementor/widgets/taxonomies-list.php:671
msgid "Count"
msgstr ""

#: elementor/traits/Houzez_Filters_Traits.php:473
#: elementor/traits/Houzez_Testimonials_Traits.php:37
#: elementor/widgets/agents-grid.php:167 elementor/widgets/agents.php:165
#: elementor/widgets/grids.php:280 elementor/widgets/partners.php:92
msgid "ID"
msgstr ""

#: elementor/traits/Houzez_Filters_Traits.php:483
#: elementor/traits/Houzez_Testimonials_Traits.php:49
#: elementor/widgets/agents-grid.php:179 elementor/widgets/agents.php:177
#: elementor/widgets/grids.php:290 elementor/widgets/partners.php:106
msgid "Order"
msgstr ""

#: elementor/traits/Houzez_Filters_Traits.php:486
#: elementor/traits/Houzez_Testimonials_Traits.php:52
#: elementor/widgets/agents-grid.php:182 elementor/widgets/agents.php:180
#: elementor/widgets/grids.php:293 elementor/widgets/partners.php:109
msgid "ASC"
msgstr ""

#: elementor/traits/Houzez_Filters_Traits.php:487
#: elementor/traits/Houzez_Testimonials_Traits.php:53
#: elementor/widgets/agents-grid.php:183 elementor/widgets/agents.php:181
#: elementor/widgets/grids.php:294 elementor/widgets/partners.php:110
msgid "DESC"
msgstr ""

#: elementor/traits/Houzez_Filters_Traits.php:497
#: elementor/widgets/grids.php:304
msgid "Number of Items to Show"
msgstr ""

#: elementor/traits/Houzez_Form_Traits.php:26
#: elementor/widgets/advanced-search.php:98
#: elementor/widgets/search-builder-old.php:777
#: elementor/widgets/search-builder.php:912 templates/currency/form.php:67
#: templates/fields-builder/fields-form.php:70
msgid "Form"
msgstr ""

#: elementor/traits/Houzez_Form_Traits.php:34
#: elementor/widgets/search-builder-old.php:785
#: elementor/widgets/search-builder.php:920
msgid "Columns Gap"
msgstr ""

#: elementor/traits/Houzez_Form_Traits.php:55
#: elementor/widgets/search-builder-old.php:806
#: elementor/widgets/search-builder.php:944
msgid "Rows Gap"
msgstr ""

#: elementor/traits/Houzez_Form_Traits.php:76
#: elementor/widgets/contact-form.php:121
#: elementor/widgets/inquiry-form.php:132
#: elementor/widgets/property-meta-data.php:102
#: elementor/widgets/property-meta-data.php:343
#: elementor/widgets/search-builder-old.php:83
#: elementor/widgets/search-builder-old.php:143
#: elementor/widgets/search-builder-old.php:827
#: elementor/widgets/search-builder.php:83
#: elementor/widgets/search-builder.php:153
#: elementor/widgets/search-builder.php:965
#: elementor/widgets/single-post/post-navigation.php:49
#: elementor/widgets/single-post/post-navigation.php:110
#: elementor/widgets/single-property/section-overview-v2.php:83
#: elementor/widgets/single-property/section-overview.php:82
msgid "Label"
msgstr ""

#: elementor/traits/Houzez_Form_Traits.php:85
#: elementor/traits/Houzez_Style_Traits.php:536
#: elementor/widgets/header-footer/site-logo.php:597
#: elementor/widgets/icon-box.php:401
#: elementor/widgets/search-builder-old.php:836
#: elementor/widgets/search-builder.php:974
#: elementor/widgets/single-agency/agency-rating.php:135
#: elementor/widgets/single-agent/agent-rating.php:135
msgid "Spacing"
msgstr ""

#: elementor/traits/Houzez_Form_Traits.php:118
msgid "Mark Color"
msgstr ""

#: elementor/traits/Houzez_Form_Traits.php:153
#: elementor/traits/Houzez_Form_Traits.php:224
#: elementor/traits/Houzez_Form_Traits.php:345
#: elementor/traits/Houzez_Form_Traits.php:417
#: elementor/traits/Houzez_Property_Cards_Traits.php:48
#: elementor/traits/Houzez_Property_Cards_Traits.php:93
#: elementor/traits/Houzez_Property_Cards_Traits.php:164
#: elementor/traits/Houzez_Property_Cards_Traits.php:209
#: elementor/traits/Houzez_Property_Cards_Traits.php:522
#: elementor/traits/Houzez_Property_Cards_Traits.php:573
#: elementor/traits/Houzez_Property_Cards_Traits.php:1822
#: elementor/traits/Houzez_Property_Cards_Traits.php:1870
#: elementor/traits/Houzez_Style_Traits.php:509
#: elementor/traits/Houzez_Style_Traits.php:610
#: elementor/traits/Houzez_Style_Traits.php:755
#: elementor/widgets/advanced-search.php:118
#: elementor/widgets/advanced-search.php:192
#: elementor/widgets/advanced-search.php:249
#: elementor/widgets/advanced-search.php:321
#: elementor/widgets/agent-card.php:531 elementor/widgets/agents-grid.php:315
#: elementor/widgets/agents-grid.php:512 elementor/widgets/agents-grid.php:552
#: elementor/widgets/agents.php:203 elementor/widgets/agents.php:312
#: elementor/widgets/agents.php:352
#: elementor/widgets/blog-posts-carousel.php:292
#: elementor/widgets/blog-posts-carousel.php:728
#: elementor/widgets/blog-posts-carousel.php:763
#: elementor/widgets/blog-posts-carousel.php:808
#: elementor/widgets/blog-posts-v2.php:184 elementor/widgets/blog-posts.php:228
#: elementor/widgets/blog-posts.php:683
#: elementor/widgets/custom-carousel.php:407
#: elementor/widgets/header-footer/area-switcher.php:267
#: elementor/widgets/header-footer/currency.php:267
#: elementor/widgets/header-footer/menu.php:813
#: elementor/widgets/header-footer/menu.php:857
#: elementor/widgets/header-footer/menu.php:900
#: elementor/widgets/header-footer/menu.php:1091
#: elementor/widgets/header-footer/menu.php:1123
#: elementor/widgets/header-footer/menu.php:1209
#: elementor/widgets/header-footer/site-logo.php:559
#: elementor/widgets/properties-slider.php:143
#: elementor/widgets/properties-slider.php:185
#: elementor/widgets/property-cards-v8.php:578
#: elementor/widgets/search-builder-old.php:888
#: elementor/widgets/search-builder-old.php:974
#: elementor/widgets/search-builder-old.php:1248
#: elementor/widgets/search-builder-old.php:1320
#: elementor/widgets/search-builder.php:1026
#: elementor/widgets/search-builder.php:1226
#: elementor/widgets/search-builder.php:1503
#: elementor/widgets/search-builder.php:1575
#: elementor/widgets/search-builder.php:1646
#: elementor/widgets/search-builder.php:1744
#: elementor/widgets/search-builder.php:1815
#: elementor/widgets/search-builder.php:1913
#: elementor/widgets/single-agency/agency-about.php:96
#: elementor/widgets/single-agency/agency-contact.php:170
#: elementor/widgets/single-agency/agency-listings-review.php:317
#: elementor/widgets/single-agency/agency-listings-review.php:391
#: elementor/widgets/single-agency/agency-name.php:194
#: elementor/widgets/single-agency/agency-profile-v1.php:154
#: elementor/widgets/single-agency/agency-profile-v1.php:297
#: elementor/widgets/single-agency/agency-profile-v1.php:369
#: elementor/widgets/single-agency/agency-profile-v1.php:429
#: elementor/widgets/single-agency/agency-profile-v1.php:501
#: elementor/widgets/single-agency/agency-profile-v2.php:88
#: elementor/widgets/single-agency/agency-search.php:74
#: elementor/widgets/single-agency/agency-search.php:226
#: elementor/widgets/single-agency/agency-search.php:290
#: elementor/widgets/single-agency/agency-search.php:362
#: elementor/widgets/single-agency/agency-single-stats.php:89
#: elementor/widgets/single-agency/agency-stats.php:53
#: elementor/widgets/single-agent/agent-about.php:96
#: elementor/widgets/single-agent/agent-contact.php:170
#: elementor/widgets/single-agent/agent-listings-review.php:322
#: elementor/widgets/single-agent/agent-listings-review.php:396
#: elementor/widgets/single-agent/agent-name.php:194
#: elementor/widgets/single-agent/agent-profile-v1.php:166
#: elementor/widgets/single-agent/agent-profile-v1.php:309
#: elementor/widgets/single-agent/agent-profile-v1.php:381
#: elementor/widgets/single-agent/agent-profile-v1.php:441
#: elementor/widgets/single-agent/agent-profile-v1.php:513
#: elementor/widgets/single-agent/agent-profile-v2.php:100
#: elementor/widgets/single-agent/agent-search.php:74
#: elementor/widgets/single-agent/agent-search.php:226
#: elementor/widgets/single-agent/agent-search.php:290
#: elementor/widgets/single-agent/agent-search.php:362
#: elementor/widgets/single-agent/agent-single-stats.php:89
#: elementor/widgets/single-agent/agent-stats.php:53
#: elementor/widgets/single-post/author-box.php:694
#: elementor/widgets/single-post/author-box.php:745
#: elementor/widgets/single-property/featured-label.php:149
#: elementor/widgets/single-property/featured-label.php:180
#: elementor/widgets/single-property/item-label.php:149
#: elementor/widgets/single-property/item-label.php:180
#: elementor/widgets/single-property/item-tools.php:98
#: elementor/widgets/single-property/property-title-area.php:569
#: elementor/widgets/single-property/section-toparea-v1.php:471
#: elementor/widgets/single-property/section-toparea-v2.php:493
#: elementor/widgets/single-property/section-toparea-v3.php:495
#: elementor/widgets/single-property/section-toparea-v5.php:470
#: elementor/widgets/single-property/section-toparea-v6.php:515
#: elementor/widgets/single-property/section-toparea-v7.php:490
#: elementor/widgets/single-property/status-label.php:150
#: elementor/widgets/single-property/status-label.php:181
#: elementor/widgets/taxonomies-cards-carousel.php:298
#: elementor/widgets/taxonomies-cards-carousel.php:422
#: elementor/widgets/taxonomies-cards-carousel.php:466
#: elementor/widgets/taxonomies-cards.php:203
#: elementor/widgets/taxonomies-grids-carousel.php:449
#: elementor/widgets/taxonomies-grids-carousel.php:493
#: elementor/widgets/testimonials-v2.php:145
#: elementor/widgets/testimonials-v3.php:141
#: elementor/widgets/testimonials-v3.php:173
msgid "Background Color"
msgstr ""

#: elementor/traits/Houzez_Form_Traits.php:166
#: elementor/widgets/advanced-search.php:131
#: elementor/widgets/agent-card.php:311 elementor/widgets/agent-card.php:577
#: elementor/widgets/agent-card.php:854 elementor/widgets/agent-card.php:906
#: elementor/widgets/grids.php:445 elementor/widgets/listings-tabs.php:227
#: elementor/widgets/properties-recent-viewed.php:541
#: elementor/widgets/property-cards-v8.php:499
#: elementor/widgets/search-builder-old.php:913
#: elementor/widgets/search-builder.php:1053
#: elementor/widgets/single-agency/agency-listings-review.php:491
#: elementor/widgets/single-agency/agency-listings.php:313
#: elementor/widgets/single-agency/agency-name.php:252
#: elementor/widgets/single-agent/agent-listings-review.php:496
#: elementor/widgets/single-agent/agent-listings.php:318
#: elementor/widgets/single-agent/agent-name.php:252
#: elementor/widgets/single-post/author-box.php:342
#: elementor/widgets/single-property/section-address.php:301
#: elementor/widgets/single-property/section-calculator.php:132
#: elementor/widgets/single-property/section-contact-2.php:258
#: elementor/widgets/single-property/section-contact-2.php:424
#: elementor/widgets/single-property/section-details.php:531
#: elementor/widgets/single-property/section-schedule-tour-v2.php:229
msgid "Border"
msgstr ""

#: elementor/traits/Houzez_Form_Traits.php:174
#: elementor/traits/Houzez_Property_Cards_Traits.php:2099
#: elementor/widgets/advanced-search.php:139
#: elementor/widgets/grid-builder.php:553 elementor/widgets/grids.php:465
#: elementor/widgets/listings-tabs.php:262
#: elementor/widgets/search-builder-old.php:104
#: elementor/widgets/search-builder-old.php:921
#: elementor/widgets/search-builder-old.php:1194
#: elementor/widgets/search-builder.php:104
#: elementor/widgets/search-builder.php:1062
#: elementor/widgets/search-builder.php:1449
#: elementor/widgets/single-agency/agency-about.php:140
#: elementor/widgets/single-agency/agency-contact.php:202
#: elementor/widgets/single-agency/agency-listings-review.php:526
#: elementor/widgets/single-agency/agency-listings.php:348
#: elementor/widgets/single-agency/agency-profile-v1.php:210
#: elementor/widgets/single-agency/agency-profile-v2.php:144
#: elementor/widgets/single-agency/agency-search.php:126
#: elementor/widgets/single-agency/agency-single-stats.php:121
#: elementor/widgets/single-agency/agency-stats.php:85
#: elementor/widgets/single-agent/agent-about.php:140
#: elementor/widgets/single-agent/agent-contact.php:202
#: elementor/widgets/single-agent/agent-listings-review.php:531
#: elementor/widgets/single-agent/agent-listings.php:350
#: elementor/widgets/single-agent/agent-profile-v1.php:222
#: elementor/widgets/single-agent/agent-profile-v2.php:156
#: elementor/widgets/single-agent/agent-search.php:126
#: elementor/widgets/single-agent/agent-single-stats.php:121
#: elementor/widgets/single-agent/agent-stats.php:85
#: elementor/widgets/single-property/featured-label.php:102
#: elementor/widgets/single-property/item-label.php:102
#: elementor/widgets/single-property/section-contact-2.php:299
#: elementor/widgets/single-property/section-contact-2.php:481
#: elementor/widgets/single-property/section-schedule-tour-v2.php:274
#: elementor/widgets/single-property/status-label.php:103
#: elementor/widgets/taxonomies-grids-carousel.php:317
#: elementor/widgets/taxonomies-grids.php:265
#: elementor/widgets/team-member.php:230
msgid "Radius"
msgstr ""

#: elementor/traits/Houzez_Form_Traits.php:187
#: elementor/widgets/advanced-search.php:152
#: elementor/widgets/agent-card.php:340 elementor/widgets/agent-card.php:599
#: elementor/widgets/header-footer/login-modal.php:547
#: elementor/widgets/login-modal.php:482
#: elementor/widgets/properties-recent-viewed.php:499
#: elementor/widgets/properties-recent-viewed.php:507
#: elementor/widgets/property-cards-v8.php:446
#: elementor/widgets/property-cards-v8.php:454
#: elementor/widgets/search-builder-old.php:934
#: elementor/widgets/search-builder.php:1076
#: elementor/widgets/single-agency/agency-name.php:274
#: elementor/widgets/single-agent/agent-name.php:274
msgid "Box Shadow"
msgstr ""

#: elementor/traits/Houzez_Form_Traits.php:197
#: elementor/widgets/advanced-search.php:162
#: elementor/widgets/contact-form.php:111
#: elementor/widgets/inquiry-form.php:122
#: elementor/widgets/search-builder-old.php:133
#: elementor/widgets/search-builder.php:143
#: elementor/widgets/single-property/property-address.php:60
#: elementor/widgets/single-property/property-title-area.php:63
#: elementor/widgets/single-property/section-overview-v2.php:73
#: elementor/widgets/single-property/section-overview.php:72
#: elementor/widgets/single-property/section-toparea-v1.php:63
#: elementor/widgets/single-property/section-toparea-v2.php:62
#: elementor/widgets/single-property/section-toparea-v3.php:67
#: elementor/widgets/single-property/section-toparea-v5.php:62
#: elementor/widgets/single-property/section-toparea-v6.php:63
#: elementor/widgets/single-property/section-toparea-v7.php:62
msgid "Field"
msgstr ""

#: elementor/traits/Houzez_Form_Traits.php:262
#: elementor/widgets/header-footer/menu.php:999
#: elementor/widgets/header-footer/menu.php:1155
#: elementor/widgets/single-post/author-box.php:389
#: elementor/widgets/single-post/author-box.php:792
#: elementor/widgets/single-property/section-overview.php:321
msgid "Border Width"
msgstr ""

#: elementor/traits/Houzez_Form_Traits.php:291
msgid "GDPR"
msgstr ""

#: elementor/traits/Houzez_Form_Traits.php:328
#: elementor/traits/Houzez_Property_Cards_Traits.php:17
#: elementor/traits/Houzez_Property_Cards_Traits.php:1769
#: elementor/widgets/advanced-search.php:232
#: elementor/widgets/agents-grid.php:472 elementor/widgets/agents-grid.php:483
#: elementor/widgets/agents.php:282 elementor/widgets/agents.php:293
#: elementor/widgets/property-cards-v8.php:547
#: elementor/widgets/search-builder-old.php:1231
#: elementor/widgets/search-builder.php:1486
#: elementor/widgets/single-agency/agency-search.php:273
#: elementor/widgets/single-agent/agent-search.php:273
msgid "Button"
msgstr ""

#: elementor/traits/Houzez_Form_Traits.php:396
#: elementor/widgets/advanced-search.php:300
#: elementor/widgets/single-agency/agency-listings-review.php:369
#: elementor/widgets/single-agency/agency-profile-v1.php:348
#: elementor/widgets/single-agency/agency-profile-v1.php:480
#: elementor/widgets/single-agency/agency-search.php:341
#: elementor/widgets/single-agent/agent-listings-review.php:374
#: elementor/widgets/single-agent/agent-profile-v1.php:360
#: elementor/widgets/single-agent/agent-profile-v1.php:492
#: elementor/widgets/single-agent/agent-search.php:341
msgid "Text Padding"
msgstr ""

#: elementor/traits/Houzez_Form_Traits.php:454
#: elementor/widgets/header-footer/menu.php:245
#: elementor/widgets/header-footer/menu.php:266
#: elementor/widgets/header-footer/menu.php:285
#: elementor/widgets/header-footer/menu.php:308
#: elementor/widgets/search-builder-old.php:1357
#: elementor/widgets/search-builder.php:1612
#: elementor/widgets/search-builder.php:1781
#: elementor/widgets/search-builder.php:1950
#: elementor/widgets/single-post/author-box.php:777
msgid "Animation"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:149
#: elementor/widgets/blog-posts-carousel.php:748
#: elementor/widgets/properties-slider.php:128
#: elementor/widgets/taxonomies-cards-carousel.php:402
#: elementor/widgets/taxonomies-grids-carousel.php:429
msgid "Next/Prev buttons"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:176
#: elementor/traits/Houzez_Property_Cards_Traits.php:221
#: elementor/traits/Houzez_Property_Cards_Traits.php:352
#: elementor/traits/Houzez_Property_Cards_Traits.php:509
#: elementor/traits/Houzez_Property_Cards_Traits.php:560
#: elementor/traits/Houzez_Style_Traits.php:91
#: elementor/traits/Houzez_Style_Traits.php:887
#: elementor/traits/Houzez_Style_Traits.php:924
#: elementor/traits/Houzez_Style_Traits.php:1251
#: elementor/traits/Houzez_Style_Traits.php:1285
#: elementor/traits/Houzez_Style_Traits.php:1312
#: elementor/widgets/agent-card.php:455 elementor/widgets/agent-card.php:629
#: elementor/widgets/banner-image.php:468
#: elementor/widgets/blog-posts-carousel.php:397
#: elementor/widgets/blog-posts-carousel.php:464
#: elementor/widgets/blog-posts-carousel.php:555
#: elementor/widgets/blog-posts-carousel.php:638
#: elementor/widgets/blog-posts-carousel.php:775
#: elementor/widgets/blog-posts-carousel.php:820
#: elementor/widgets/blog-posts-carousel.php:948
#: elementor/widgets/blog-posts-v2.php:249
#: elementor/widgets/blog-posts-v2.php:313
#: elementor/widgets/blog-posts-v2.php:389
#: elementor/widgets/blog-posts-v2.php:466 elementor/widgets/blog-posts.php:352
#: elementor/widgets/blog-posts.php:419 elementor/widgets/blog-posts.php:510
#: elementor/widgets/blog-posts.php:593
#: elementor/widgets/custom-carousel.php:1032
#: elementor/widgets/custom-carousel.php:1070
#: elementor/widgets/header-footer/area-switcher.php:195
#: elementor/widgets/header-footer/area-switcher.php:215
#: elementor/widgets/header-footer/area-switcher.php:278
#: elementor/widgets/header-footer/currency.php:195
#: elementor/widgets/header-footer/currency.php:215
#: elementor/widgets/header-footer/currency.php:278
#: elementor/widgets/header-footer/lang.php:177
#: elementor/widgets/header-footer/menu.php:691
#: elementor/widgets/header-footer/menu.php:1079
#: elementor/widgets/header-footer/menu.php:1111
#: elementor/widgets/header-footer/menu.php:1198
#: elementor/widgets/page-title.php:187
#: elementor/widgets/properties-slider.php:154
#: elementor/widgets/properties-slider.php:196
#: elementor/widgets/property-meta-data.php:222
#: elementor/widgets/property-meta-data.php:329
#: elementor/widgets/property-meta-data.php:389
#: elementor/widgets/search-builder-old.php:1260
#: elementor/widgets/search-builder-old.php:1332
#: elementor/widgets/search-builder.php:1515
#: elementor/widgets/search-builder.php:1587
#: elementor/widgets/search-builder.php:1658
#: elementor/widgets/search-builder.php:1756
#: elementor/widgets/search-builder.php:1827
#: elementor/widgets/search-builder.php:1925
#: elementor/widgets/single-agency/agency-address.php:57
#: elementor/widgets/single-agency/agency-meta.php:299
#: elementor/widgets/single-agency/agency-meta.php:353
#: elementor/widgets/single-agency/agency-meta.php:406
#: elementor/widgets/single-agency/agency-profile-v2.php:231
#: elementor/widgets/single-agency/agency-profile-v2.php:260
#: elementor/widgets/single-agent/agent-meta.php:300
#: elementor/widgets/single-agent/agent-meta.php:354
#: elementor/widgets/single-agent/agent-meta.php:407
#: elementor/widgets/single-agent/agent-position.php:76
#: elementor/widgets/single-agent/agent-profile-v2.php:243
#: elementor/widgets/single-agent/agent-profile-v2.php:272
#: elementor/widgets/single-post/author-box.php:498
#: elementor/widgets/single-post/author-box.php:585
#: elementor/widgets/single-post/author-box.php:731
#: elementor/widgets/single-post/post-info.php:564
#: elementor/widgets/single-post/post-info.php:589
#: elementor/widgets/single-post/post-navigation.php:130
#: elementor/widgets/single-post/post-navigation.php:152
#: elementor/widgets/single-post/post-navigation.php:215
#: elementor/widgets/single-post/post-navigation.php:235
#: elementor/widgets/single-property/featured-label.php:138
#: elementor/widgets/single-property/featured-label.php:169
#: elementor/widgets/single-property/item-label.php:138
#: elementor/widgets/single-property/item-label.php:169
#: elementor/widgets/single-property/item-tools.php:122
#: elementor/widgets/single-property/property-address.php:157
#: elementor/widgets/single-property/property-price.php:105
#: elementor/widgets/single-property/property-price.php:180
#: elementor/widgets/single-property/property-title-area.php:216
#: elementor/widgets/single-property/property-title-area.php:366
#: elementor/widgets/single-property/property-title-area.php:419
#: elementor/widgets/single-property/property-title-area.php:494
#: elementor/widgets/single-property/property-title-area.php:593
#: elementor/widgets/single-property/section-360-virtual.php:110
#: elementor/widgets/single-property/section-address.php:217
#: elementor/widgets/single-property/section-address.php:248
#: elementor/widgets/single-property/section-address.php:278
#: elementor/widgets/single-property/section-attachments.php:263
#: elementor/widgets/single-property/section-attachments.php:292
#: elementor/widgets/single-property/section-calculator.php:109
#: elementor/widgets/single-property/section-calendar.php:109
#: elementor/widgets/single-property/section-contact-bottom.php:100
#: elementor/widgets/single-property/section-contact-bottom.php:381
#: elementor/widgets/single-property/section-description.php:145
#: elementor/widgets/single-property/section-description.php:174
#: elementor/widgets/single-property/section-description.php:232
#: elementor/widgets/single-property/section-details.php:447
#: elementor/widgets/single-property/section-details.php:478
#: elementor/widgets/single-property/section-details.php:508
#: elementor/widgets/single-property/section-energy.php:195
#: elementor/widgets/single-property/section-energy.php:224
#: elementor/widgets/single-property/section-energy.php:253
#: elementor/widgets/single-property/section-features.php:123
#: elementor/widgets/single-property/section-floorplan-v2.php:156
#: elementor/widgets/single-property/section-floorplan-v2.php:195
#: elementor/widgets/single-property/section-floorplan-v2.php:244
#: elementor/widgets/single-property/section-floorplan-v2.php:276
#: elementor/widgets/single-property/section-floorplan.php:164
#: elementor/widgets/single-property/section-floorplan.php:264
#: elementor/widgets/single-property/section-floorplan.php:296
#: elementor/widgets/single-property/section-nearby.php:117
#: elementor/widgets/single-property/section-overview-v2.php:290
#: elementor/widgets/single-property/section-overview-v2.php:319
#: elementor/widgets/single-property/section-overview-v2.php:348
#: elementor/widgets/single-property/section-overview-v2.php:377
#: elementor/widgets/single-property/section-overview.php:370
#: elementor/widgets/single-property/section-overview.php:399
#: elementor/widgets/single-property/section-overview.php:428
#: elementor/widgets/single-property/section-overview.php:457
#: elementor/widgets/single-property/section-schedule-tour-v2.php:120
#: elementor/widgets/single-property/section-schedule-tour.php:110
#: elementor/widgets/single-property/section-toparea-v1.php:118
#: elementor/widgets/single-property/section-toparea-v1.php:495
#: elementor/widgets/single-property/section-toparea-v2.php:141
#: elementor/widgets/single-property/section-toparea-v2.php:291
#: elementor/widgets/single-property/section-toparea-v2.php:343
#: elementor/widgets/single-property/section-toparea-v2.php:418
#: elementor/widgets/single-property/section-toparea-v2.php:517
#: elementor/widgets/single-property/section-toparea-v3.php:143
#: elementor/widgets/single-property/section-toparea-v3.php:293
#: elementor/widgets/single-property/section-toparea-v3.php:345
#: elementor/widgets/single-property/section-toparea-v3.php:420
#: elementor/widgets/single-property/section-toparea-v3.php:519
#: elementor/widgets/single-property/section-toparea-v5.php:268
#: elementor/widgets/single-property/section-toparea-v5.php:320
#: elementor/widgets/single-property/section-toparea-v5.php:395
#: elementor/widgets/single-property/section-toparea-v5.php:494
#: elementor/widgets/single-property/section-toparea-v6.php:163
#: elementor/widgets/single-property/section-toparea-v6.php:313
#: elementor/widgets/single-property/section-toparea-v6.php:365
#: elementor/widgets/single-property/section-toparea-v6.php:440
#: elementor/widgets/single-property/section-toparea-v6.php:539
#: elementor/widgets/single-property/section-toparea-v7.php:138
#: elementor/widgets/single-property/section-toparea-v7.php:288
#: elementor/widgets/single-property/section-toparea-v7.php:340
#: elementor/widgets/single-property/section-toparea-v7.php:415
#: elementor/widgets/single-property/section-toparea-v7.php:514
#: elementor/widgets/single-property/section-video.php:109
#: elementor/widgets/single-property/section-walkscore.php:117
#: elementor/widgets/single-property/status-label.php:139
#: elementor/widgets/single-property/status-label.php:170
#: elementor/widgets/taxonomies-cards-carousel.php:433
#: elementor/widgets/taxonomies-cards-carousel.php:477
#: elementor/widgets/taxonomies-cards-carousel.php:608
#: elementor/widgets/taxonomies-grids-carousel.php:460
#: elementor/widgets/taxonomies-grids-carousel.php:504
#: elementor/widgets/taxonomies-grids-carousel.php:635
#: elementor/widgets/taxonomies-list.php:166
#: elementor/widgets/taxonomies-list.php:308
#: elementor/widgets/taxonomies-list.php:380
#: elementor/widgets/taxonomies-list.php:405
#: elementor/widgets/taxonomies-list.php:612
#: elementor/widgets/taxonomies-list.php:636
#: elementor/widgets/taxonomies-list.php:728
#: elementor/widgets/taxonomies-list.php:752
#: elementor/widgets/testimonials-v3.php:129
#: elementor/widgets/testimonials-v3.php:162
msgid "Color"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:253
#: elementor/widgets/blog-posts-carousel.php:852
#: elementor/widgets/taxonomies-cards-carousel.php:509
#: elementor/widgets/taxonomies-grids-carousel.php:536
msgid "Carousel Dots"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:264
#: elementor/widgets/blog-posts-carousel.php:860
#: elementor/widgets/contact-form.php:439
#: elementor/widgets/header-footer/menu.php:1138
#: elementor/widgets/inquiry-form.php:424
#: elementor/widgets/property-meta-data.php:235
#: elementor/widgets/single-agency/agency-meta.php:428
#: elementor/widgets/single-agent/agent-meta.php:429
#: elementor/widgets/single-post/post-info.php:188
#: elementor/widgets/single-post/post-info.php:602
#: elementor/widgets/taxonomies-cards-carousel.php:520
#: elementor/widgets/taxonomies-grids-carousel.php:547
#: elementor/widgets/taxonomies-list.php:439
#: statistics/houzez-statistics.php:517 statistics/houzez-statistics.php:520
msgid "Size"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:281
#: elementor/widgets/blog-posts-carousel.php:877
#: elementor/widgets/custom-carousel.php:386
#: elementor/widgets/property-meta-data.php:190
#: elementor/widgets/single-agency/agency-meta.php:243
#: elementor/widgets/single-agent/agent-meta.php:244
#: elementor/widgets/single-post/post-info.php:377
#: elementor/widgets/taxonomies-cards-carousel.php:537
#: elementor/widgets/taxonomies-grids-carousel.php:564
#: elementor/widgets/taxonomies-list.php:188
msgid "Space Between"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:298
#: elementor/traits/Houzez_Style_Traits.php:1016
#: elementor/traits/Houzez_Style_Traits.php:1220
#: elementor/widgets/blog-posts-carousel.php:418
#: elementor/widgets/blog-posts-carousel.php:491
#: elementor/widgets/blog-posts-carousel.php:592
#: elementor/widgets/blog-posts-carousel.php:894
#: elementor/widgets/blog-posts-v2.php:270
#: elementor/widgets/blog-posts-v2.php:334
#: elementor/widgets/blog-posts-v2.php:422 elementor/widgets/blog-posts.php:373
#: elementor/widgets/blog-posts.php:446 elementor/widgets/blog-posts.php:547
#: elementor/widgets/search-builder-old.php:1063
#: elementor/widgets/search-builder.php:1318
#: elementor/widgets/single-agency/agency-about.php:152
#: elementor/widgets/single-agency/agency-contact-form.php:67
#: elementor/widgets/single-agency/agency-contact.php:216
#: elementor/widgets/single-agency/agency-listings-review.php:252
#: elementor/widgets/single-agency/agency-profile-v1.php:222
#: elementor/widgets/single-agency/agency-profile-v2.php:156
#: elementor/widgets/single-agency/agency-search.php:138
#: elementor/widgets/single-agency/agency-stats.php:117
#: elementor/widgets/single-agent/agent-about.php:152
#: elementor/widgets/single-agent/agent-contact-form.php:67
#: elementor/widgets/single-agent/agent-contact.php:216
#: elementor/widgets/single-agent/agent-listings-review.php:257
#: elementor/widgets/single-agent/agent-profile-v1.php:234
#: elementor/widgets/single-agent/agent-profile-v2.php:168
#: elementor/widgets/single-agent/agent-search.php:138
#: elementor/widgets/single-agent/agent-stats.php:117
#: elementor/widgets/single-property/property-price.php:116
#: elementor/widgets/single-property/property-price.php:191
#: elementor/widgets/single-property/property-title-area.php:244
#: elementor/widgets/single-property/property-title-area.php:430
#: elementor/widgets/single-property/section-attachments.php:140
#: elementor/widgets/single-property/section-block-gallery.php:101
#: elementor/widgets/single-property/section-contact-2.php:131
#: elementor/widgets/single-property/section-contact-bottom.php:422
#: elementor/widgets/single-property/section-contact-bottom.php:519
#: elementor/widgets/single-property/section-schedule-tour-v2.php:65
#: elementor/widgets/single-property/section-similar.php:243
#: elementor/widgets/single-property/section-sublistings.php:153
#: elementor/widgets/single-property/section-toparea-v1.php:146
#: elementor/widgets/single-property/section-toparea-v1.php:332
#: elementor/widgets/single-property/section-toparea-v2.php:169
#: elementor/widgets/single-property/section-toparea-v2.php:354
#: elementor/widgets/single-property/section-toparea-v3.php:171
#: elementor/widgets/single-property/section-toparea-v3.php:356
#: elementor/widgets/single-property/section-toparea-v5.php:146
#: elementor/widgets/single-property/section-toparea-v5.php:331
#: elementor/widgets/single-property/section-toparea-v6.php:191
#: elementor/widgets/single-property/section-toparea-v6.php:376
#: elementor/widgets/single-property/section-toparea-v7.php:166
#: elementor/widgets/single-property/section-toparea-v7.php:351
#: elementor/widgets/taxonomies-cards-carousel.php:554
#: elementor/widgets/taxonomies-grids-carousel.php:581
msgid "Margin Top"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:316
#: elementor/traits/Houzez_Style_Traits.php:331
#: elementor/traits/Houzez_Style_Traits.php:365
#: elementor/widgets/banner-image.php:336
#: elementor/widgets/banner-image.php:370
#: elementor/widgets/blog-posts-carousel.php:912
#: elementor/widgets/custom-carousel.php:623
#: elementor/widgets/custom-carousel.php:657
#: elementor/widgets/header-footer/site-logo.php:251
#: elementor/widgets/header-footer/site-logo.php:285
#: elementor/widgets/single-property/featured-image.php:211
#: elementor/widgets/single-property/featured-image.php:245
#: elementor/widgets/taxonomies-cards-carousel.php:572
#: elementor/widgets/taxonomies-cards.php:310
#: elementor/widgets/taxonomies-cards.php:336
#: elementor/widgets/taxonomies-grids-carousel.php:347
#: elementor/widgets/taxonomies-grids-carousel.php:385
#: elementor/widgets/taxonomies-grids-carousel.php:599
#: elementor/widgets/taxonomies-grids.php:295
#: elementor/widgets/taxonomies-grids.php:333
msgid "Opacity"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:334
#: elementor/widgets/blog-posts-carousel.php:930
#: elementor/widgets/taxonomies-cards-carousel.php:590
#: elementor/widgets/taxonomies-grids-carousel.php:617
msgid "Opacity Active"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:364
#: elementor/widgets/blog-posts-carousel.php:960
#: elementor/widgets/single-property/section-floorplan-v2.php:208
#: elementor/widgets/taxonomies-cards-carousel.php:620
#: elementor/widgets/taxonomies-grids-carousel.php:647
msgid "Active Color"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:384
#: elementor/widgets/blog-posts-carousel.php:189
#: elementor/widgets/custom-carousel.php:160
#: elementor/widgets/taxonomies-cards-carousel.php:143
#: elementor/widgets/taxonomies-grids-carousel.php:128
msgid "Slides To Show"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:401
#: elementor/widgets/custom-carousel.php:177
#: elementor/widgets/taxonomies-cards-carousel.php:160
#: elementor/widgets/taxonomies-grids-carousel.php:145
msgid "Slides To Scroll"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:418
#: elementor/widgets/blog-posts-carousel.php:203
#: elementor/widgets/custom-carousel.php:194
#: elementor/widgets/single-agency/agency-listings-review.php:137
#: elementor/widgets/single-agency/agency-listings.php:137
#: elementor/widgets/single-agent/agent-listings-review.php:142
#: elementor/widgets/single-agent/agent-listings.php:142
#: elementor/widgets/taxonomies-cards-carousel.php:177
#: elementor/widgets/taxonomies-grids-carousel.php:162
#: functions/functions.php:226
msgid "Infinite Scroll"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:431
#: elementor/widgets/blog-posts-carousel.php:216
#: elementor/widgets/custom-carousel.php:207
#: elementor/widgets/taxonomies-cards-carousel.php:190
#: elementor/widgets/taxonomies-grids-carousel.php:175
msgid "Auto Play"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:448
#: elementor/widgets/blog-posts-carousel.php:233
#: elementor/widgets/custom-carousel.php:224
#: elementor/widgets/taxonomies-cards-carousel.php:207
#: elementor/widgets/taxonomies-grids-carousel.php:192
msgid "Autoplay Speed in milliseconds. Default 3000"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:455
#: elementor/widgets/blog-posts-carousel.php:240
#: elementor/widgets/custom-carousel.php:231
#: elementor/widgets/taxonomies-cards-carousel.php:214
#: elementor/widgets/taxonomies-grids-carousel.php:199
msgid "Next/Prev Navigation"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:469
#: elementor/widgets/blog-posts-carousel.php:254
#: elementor/widgets/custom-carousel.php:245
#: elementor/widgets/taxonomies-cards-carousel.php:228
#: elementor/widgets/taxonomies-grids-carousel.php:213
msgid "Dots Nav"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:490
#: elementor/widgets/properties-ajax-tabs.php:652
#: elementor/widgets/properties.php:136
#: elementor/widgets/property-cards-v1.php:111
#: elementor/widgets/property-cards-v2.php:109
#: elementor/widgets/property-cards-v3.php:108
#: elementor/widgets/property-cards-v4.php:108
#: elementor/widgets/property-cards-v5.php:108
#: elementor/widgets/property-cards-v6.php:112
#: elementor/widgets/property-cards-v7.php:110
#: elementor/widgets/property-cards-v8.php:94
#: elementor/widgets/single-agency/agency-listings-review.php:131
#: elementor/widgets/single-agency/agency-listings.php:131
#: elementor/widgets/single-agent/agent-listings-review.php:136
#: elementor/widgets/single-agent/agent-listings.php:136
msgid "Pagination"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:605
msgid "Background Color Active"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:620
msgid "Color Active"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:667
#: elementor/traits/Houzez_Property_Cards_Traits.php:765
#: elementor/traits/Houzez_Property_Cards_Traits.php:1537
#: elementor/traits/Houzez_Property_Cards_Traits.php:1654
#: elementor/widgets/grid-builder.php:605 elementor/widgets/grids.php:408
#: elementor/widgets/properties-ajax-tabs.php:686
#: elementor/widgets/properties-recent-viewed.php:614
#: elementor/widgets/taxonomies-cards-carousel.php:274
#: elementor/widgets/taxonomies-cards.php:179
#: elementor/widgets/taxonomies-grids-carousel.php:259
#: elementor/widgets/taxonomies-grids.php:172
msgid "Title Color"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:680
#: elementor/traits/Houzez_Property_Cards_Traits.php:1549
#: elementor/widgets/properties-recent-viewed.php:626
msgid "Address Color"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:692
#: elementor/traits/Houzez_Property_Cards_Traits.php:1581
#: elementor/widgets/properties-recent-viewed.php:638
msgid "Icons"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:704
#: elementor/traits/Houzez_Property_Cards_Traits.php:1593
#: elementor/widgets/properties-recent-viewed.php:650
msgid "Figure"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:732
#: elementor/traits/Houzez_Property_Cards_Traits.php:777
#: elementor/traits/Houzez_Property_Cards_Traits.php:1621
#: elementor/traits/Houzez_Property_Cards_Traits.php:1666
#: elementor/widgets/properties-recent-viewed.php:566
msgid "Item Tools Background Color"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:744
#: elementor/traits/Houzez_Property_Cards_Traits.php:789
#: elementor/traits/Houzez_Property_Cards_Traits.php:1633
#: elementor/traits/Houzez_Property_Cards_Traits.php:1678
#: elementor/widgets/properties-recent-viewed.php:590
msgid "Item Tools Color"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:810
#: elementor/widgets/property-cards-v1.php:484
#: elementor/widgets/property-cards-v2.php:460
#: elementor/widgets/property-cards-v4.php:479
#: elementor/widgets/property-cards-v5.php:342
#: elementor/widgets/property-cards-v6.php:300
#: elementor/widgets/property-cards-v7.php:441
#: elementor/widgets/property-carousel-v1.php:459
#: elementor/widgets/property-carousel-v2.php:436
#: elementor/widgets/property-carousel-v5.php:319
#: elementor/widgets/property-carousel-v6.php:272
#: elementor/widgets/property-carousel-v7.php:411
msgid "Card Box"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:840
#: elementor/widgets/property-cards-v1.php:494
#: elementor/widgets/property-cards-v4.php:489
#: elementor/widgets/property-carousel-v1.php:469
msgid "Box Footer Border Type"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:845
#: elementor/traits/Houzez_Style_Traits.php:984
#: elementor/traits/Houzez_Style_Traits.php:1337
#: elementor/traits/Houzez_Testimonials_Traits.php:36
#: elementor/widgets/agents-grid.php:166 elementor/widgets/agents.php:164
#: elementor/widgets/banner-image.php:147
#: elementor/widgets/header-footer/menu.php:227
#: elementor/widgets/header-footer/menu.php:333
#: elementor/widgets/header-footer/menu.php:367
#: elementor/widgets/header-footer/site-logo.php:499
#: elementor/widgets/partners.php:91 elementor/widgets/price-table.php:76
#: elementor/widgets/properties-ajax-tabs.php:250
#: elementor/widgets/properties-ajax-tabs.php:655
#: elementor/widgets/property-cards-v1.php:499
#: elementor/widgets/property-cards-v4.php:494
#: elementor/widgets/property-cards-v7.php:465
#: elementor/widgets/property-carousel-v1.php:474
#: elementor/widgets/property-carousel-v7.php:435
#: elementor/widgets/single-agency/agency-listings-review.php:134
#: elementor/widgets/single-agency/agency-listings.php:134
#: elementor/widgets/single-agent/agent-listings-review.php:139
#: elementor/widgets/single-agent/agent-listings.php:139
#: elementor/widgets/single-post/author-box.php:121
#: elementor/widgets/single-post/post-info.php:298
#: elementor/widgets/single-property/section-address.php:314
#: elementor/widgets/single-property/section-calculator.php:145
#: elementor/widgets/single-property/section-contact-bottom.php:125
#: elementor/widgets/single-property/section-details.php:408
#: elementor/widgets/single-property/section-details.php:544
#: elementor/widgets/single-property/section-energy.php:287
#: elementor/widgets/single-property/section-overview.php:300
#: elementor/widgets/single-property/section-similar.php:211
#: extensions/meta-box/inc/fields/select.php:80 functions/functions.php:223
msgid "None"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:846
#: elementor/traits/Houzez_Style_Traits.php:985
#: elementor/traits/Houzez_Style_Traits.php:1338
#: elementor/widgets/header-footer/menu.php:633
#: elementor/widgets/property-cards-v1.php:500
#: elementor/widgets/property-cards-v4.php:495
#: elementor/widgets/property-cards-v7.php:466
#: elementor/widgets/property-carousel-v1.php:475
#: elementor/widgets/property-carousel-v7.php:436
#: elementor/widgets/single-post/post-info.php:445
#: elementor/widgets/single-property/section-address.php:315
#: elementor/widgets/single-property/section-calculator.php:146
#: elementor/widgets/single-property/section-contact-bottom.php:126
#: elementor/widgets/single-property/section-details.php:409
#: elementor/widgets/single-property/section-details.php:545
#: elementor/widgets/single-property/section-energy.php:288
#: elementor/widgets/single-property/section-overview.php:301
#: elementor/widgets/single-property/section-similar.php:212
#: elementor/widgets/taxonomies-list.php:248
msgid "Solid"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:847
#: elementor/traits/Houzez_Style_Traits.php:986
#: elementor/traits/Houzez_Style_Traits.php:1339
#: elementor/widgets/header-footer/menu.php:636
#: elementor/widgets/property-cards-v1.php:501
#: elementor/widgets/property-cards-v4.php:496
#: elementor/widgets/property-cards-v7.php:467
#: elementor/widgets/property-carousel-v1.php:476
#: elementor/widgets/property-carousel-v7.php:437
#: elementor/widgets/single-post/post-info.php:448
#: elementor/widgets/single-property/section-address.php:316
#: elementor/widgets/single-property/section-calculator.php:147
#: elementor/widgets/single-property/section-contact-bottom.php:127
#: elementor/widgets/single-property/section-details.php:410
#: elementor/widgets/single-property/section-details.php:546
#: elementor/widgets/single-property/section-energy.php:289
#: elementor/widgets/single-property/section-overview.php:304
#: elementor/widgets/single-property/section-similar.php:213
#: elementor/widgets/taxonomies-list.php:251
msgid "Dashed"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:848
#: elementor/traits/Houzez_Style_Traits.php:987
#: elementor/traits/Houzez_Style_Traits.php:1340
#: elementor/widgets/header-footer/menu.php:635
#: elementor/widgets/property-cards-v1.php:502
#: elementor/widgets/property-cards-v4.php:497
#: elementor/widgets/property-cards-v7.php:468
#: elementor/widgets/property-carousel-v1.php:477
#: elementor/widgets/property-carousel-v7.php:438
#: elementor/widgets/single-post/post-info.php:447
#: elementor/widgets/single-property/section-address.php:317
#: elementor/widgets/single-property/section-calculator.php:148
#: elementor/widgets/single-property/section-contact-bottom.php:128
#: elementor/widgets/single-property/section-details.php:411
#: elementor/widgets/single-property/section-details.php:547
#: elementor/widgets/single-property/section-energy.php:290
#: elementor/widgets/single-property/section-overview.php:303
#: elementor/widgets/single-property/section-similar.php:214
#: elementor/widgets/taxonomies-list.php:250
msgid "Dotted"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:849
#: elementor/traits/Houzez_Style_Traits.php:988
#: elementor/traits/Houzez_Style_Traits.php:1341
#: elementor/widgets/property-cards-v1.php:503
#: elementor/widgets/property-cards-v4.php:498
#: elementor/widgets/property-cards-v7.php:469
#: elementor/widgets/property-carousel-v1.php:478
#: elementor/widgets/property-carousel-v7.php:439
#: elementor/widgets/single-property/section-address.php:318
#: elementor/widgets/single-property/section-calculator.php:149
#: elementor/widgets/single-property/section-contact-bottom.php:129
#: elementor/widgets/single-property/section-details.php:412
#: elementor/widgets/single-property/section-details.php:548
#: elementor/widgets/single-property/section-energy.php:291
#: elementor/widgets/single-property/section-overview.php:305
#: elementor/widgets/single-property/section-similar.php:215
msgid "Groove"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:850
#: elementor/traits/Houzez_Style_Traits.php:989
#: elementor/traits/Houzez_Style_Traits.php:1342
#: elementor/widgets/header-footer/menu.php:634
#: elementor/widgets/property-cards-v1.php:504
#: elementor/widgets/property-cards-v4.php:499
#: elementor/widgets/property-cards-v7.php:470
#: elementor/widgets/property-carousel-v1.php:479
#: elementor/widgets/property-carousel-v7.php:440
#: elementor/widgets/single-post/post-info.php:446
#: elementor/widgets/single-property/section-address.php:319
#: elementor/widgets/single-property/section-calculator.php:150
#: elementor/widgets/single-property/section-contact-bottom.php:130
#: elementor/widgets/single-property/section-details.php:413
#: elementor/widgets/single-property/section-details.php:549
#: elementor/widgets/single-property/section-energy.php:292
#: elementor/widgets/single-property/section-overview.php:302
#: elementor/widgets/single-property/section-similar.php:216
#: elementor/widgets/taxonomies-list.php:249
msgid "Double"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:851
#: elementor/traits/Houzez_Style_Traits.php:990
#: elementor/traits/Houzez_Style_Traits.php:1343
#: elementor/widgets/property-cards-v1.php:505
#: elementor/widgets/property-cards-v4.php:500
#: elementor/widgets/property-cards-v7.php:471
#: elementor/widgets/property-carousel-v1.php:480
#: elementor/widgets/property-carousel-v7.php:441
#: elementor/widgets/single-property/section-address.php:320
#: elementor/widgets/single-property/section-calculator.php:151
#: elementor/widgets/single-property/section-contact-bottom.php:131
#: elementor/widgets/single-property/section-details.php:414
#: elementor/widgets/single-property/section-details.php:550
#: elementor/widgets/single-property/section-energy.php:293
#: elementor/widgets/single-property/section-similar.php:217
msgid "Ridge"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:922
#: elementor/traits/Houzez_Style_Traits.php:1273
#: elementor/widgets/agents-grid.php:403 elementor/widgets/grids.php:347
#: elementor/widgets/property-cards-v1.php:536
#: elementor/widgets/property-cards-v2.php:475
#: elementor/widgets/property-cards-v4.php:531
#: elementor/widgets/property-cards-v5.php:357
#: elementor/widgets/property-cards-v6.php:315
#: elementor/widgets/property-cards-v7.php:501
#: elementor/widgets/property-carousel-v1.php:511
#: elementor/widgets/property-carousel-v2.php:451
#: elementor/widgets/property-carousel-v5.php:334
#: elementor/widgets/property-carousel-v6.php:287
#: elementor/widgets/property-carousel-v7.php:471
#: elementor/widgets/single-agency/agency-profile-v2.php:53
#: elementor/widgets/single-agent/agent-profile-v2.php:53
#: elementor/widgets/taxonomies-cards-carousel.php:364
#: elementor/widgets/taxonomies-cards.php:269
msgid "Image Radius"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:960
#: elementor/traits/Houzez_Style_Traits.php:902
#: elementor/traits/Houzez_Style_Traits.php:939
#: elementor/traits/Houzez_Style_Traits.php:1324
#: elementor/widgets/agent-card.php:511 elementor/widgets/agents-grid.php:427
#: elementor/widgets/agents.php:245 elementor/widgets/banner-image.php:460
#: elementor/widgets/blog-posts-carousel.php:410
#: elementor/widgets/blog-posts-carousel.php:480
#: elementor/widgets/blog-posts-carousel.php:584
#: elementor/widgets/blog-posts-carousel.php:663
#: elementor/widgets/blog-posts-carousel.php:708
#: elementor/widgets/blog-posts-v2.php:262
#: elementor/widgets/blog-posts-v2.php:326
#: elementor/widgets/blog-posts-v2.php:414
#: elementor/widgets/blog-posts-v2.php:491 elementor/widgets/blog-posts.php:365
#: elementor/widgets/blog-posts.php:435 elementor/widgets/blog-posts.php:539
#: elementor/widgets/blog-posts.php:618 elementor/widgets/blog-posts.php:663
#: elementor/widgets/header-footer/login-modal.php:268
#: elementor/widgets/listings-tabs.php:298
#: elementor/widgets/login-modal.php:221
#: elementor/widgets/properties-recent-viewed.php:272
#: elementor/widgets/property-cards-v1.php:212
#: elementor/widgets/property-cards-v2.php:196
#: elementor/widgets/property-cards-v3.php:168
#: elementor/widgets/property-cards-v4.php:207
#: elementor/widgets/property-cards-v5.php:168
#: elementor/widgets/property-cards-v6.php:174
#: elementor/widgets/property-cards-v7.php:208
#: elementor/widgets/property-cards-v8.php:182
#: elementor/widgets/property-carousel-v1.php:187
#: elementor/widgets/property-carousel-v2.php:172
#: elementor/widgets/property-carousel-v3.php:144
#: elementor/widgets/property-carousel-v5.php:145
#: elementor/widgets/property-carousel-v6.php:146
#: elementor/widgets/property-carousel-v7.php:184
#: elementor/widgets/section-title.php:107
#: elementor/widgets/single-agency/agency-name.php:174
#: elementor/widgets/single-agent/agent-name.php:174
#: elementor/widgets/single-property/section-360-virtual.php:122
#: elementor/widgets/single-property/section-address.php:229
#: elementor/widgets/single-property/section-address.php:260
#: elementor/widgets/single-property/section-address.php:290
#: elementor/widgets/single-property/section-attachments.php:275
#: elementor/widgets/single-property/section-attachments.php:304
#: elementor/widgets/single-property/section-calculator.php:121
#: elementor/widgets/single-property/section-calendar.php:121
#: elementor/widgets/single-property/section-contact-bottom.php:112
#: elementor/widgets/single-property/section-description.php:157
#: elementor/widgets/single-property/section-description.php:186
#: elementor/widgets/single-property/section-description.php:244
#: elementor/widgets/single-property/section-details.php:459
#: elementor/widgets/single-property/section-details.php:490
#: elementor/widgets/single-property/section-details.php:520
#: elementor/widgets/single-property/section-energy.php:207
#: elementor/widgets/single-property/section-energy.php:236
#: elementor/widgets/single-property/section-energy.php:265
#: elementor/widgets/single-property/section-features.php:135
#: elementor/widgets/single-property/section-floorplan-v2.php:168
#: elementor/widgets/single-property/section-floorplan-v2.php:256
#: elementor/widgets/single-property/section-floorplan-v2.php:288
#: elementor/widgets/single-property/section-floorplan.php:176
#: elementor/widgets/single-property/section-floorplan.php:276
#: elementor/widgets/single-property/section-floorplan.php:308
#: elementor/widgets/single-property/section-nearby.php:129
#: elementor/widgets/single-property/section-overview-v2.php:302
#: elementor/widgets/single-property/section-overview-v2.php:331
#: elementor/widgets/single-property/section-overview-v2.php:360
#: elementor/widgets/single-property/section-overview.php:382
#: elementor/widgets/single-property/section-overview.php:411
#: elementor/widgets/single-property/section-overview.php:440
#: elementor/widgets/single-property/section-schedule-tour-v2.php:132
#: elementor/widgets/single-property/section-schedule-tour.php:122
#: elementor/widgets/single-property/section-sublistings.php:120
#: elementor/widgets/single-property/section-video.php:121
#: elementor/widgets/single-property/section-walkscore.php:129
#: elementor/widgets/taxonomies-list.php:158
#: elementor/widgets/team-member.php:252
msgid "Typography"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:978
#: elementor/widgets/contact-form.php:94
#: elementor/widgets/properties-recent-viewed.php:290
#: elementor/widgets/property-cards-v1.php:230
#: elementor/widgets/property-cards-v2.php:214
#: elementor/widgets/property-cards-v4.php:225
#: elementor/widgets/property-cards-v7.php:226
#: elementor/widgets/property-cards-v8.php:200
#: elementor/widgets/property-carousel-v1.php:205
#: elementor/widgets/property-carousel-v2.php:190
#: elementor/widgets/property-carousel-v7.php:202
#: elementor/widgets/single-agency/agency-contact.php:76
#: elementor/widgets/single-agency/agency-contact.php:307
#: elementor/widgets/single-agency/agency-meta.php:61
#: elementor/widgets/single-agency/agency-profile-v1.php:53
#: elementor/widgets/single-agency/agency-profile-v2.php:65
#: elementor/widgets/single-agent/agent-contact.php:76
#: elementor/widgets/single-agent/agent-contact.php:307
#: elementor/widgets/single-agent/agent-meta.php:63
#: elementor/widgets/single-property/property-address.php:44
#: elementor/widgets/single-property/property-title-area.php:47
#: elementor/widgets/single-property/section-address.php:140
#: elementor/widgets/single-property/section-toparea-v1.php:47
#: elementor/widgets/single-property/section-toparea-v2.php:46
#: elementor/widgets/single-property/section-toparea-v3.php:51
#: elementor/widgets/single-property/section-toparea-v5.php:46
#: elementor/widgets/single-property/section-toparea-v6.php:47
#: elementor/widgets/single-property/section-toparea-v7.php:46
msgid "Address"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:1008
#: elementor/widgets/property-cards-v1.php:239
#: elementor/widgets/property-cards-v2.php:223
#: elementor/widgets/property-cards-v4.php:234
#: elementor/widgets/property-cards-v8.php:209
#: elementor/widgets/property-carousel-v1.php:214
#: elementor/widgets/property-carousel-v2.php:199
msgid "Excerpt"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:1038
#: elementor/widgets/properties-recent-viewed.php:299
#: elementor/widgets/property-cards-v1.php:251
#: elementor/widgets/property-cards-v2.php:235
#: elementor/widgets/property-cards-v4.php:246
#: elementor/widgets/property-cards-v7.php:235
#: elementor/widgets/property-cards-v8.php:221
#: elementor/widgets/property-carousel-v1.php:226
#: elementor/widgets/property-carousel-v2.php:211
#: elementor/widgets/property-carousel-v7.php:211
#: elementor/widgets/single-property/section-energy.php:215
#: elementor/widgets/single-property/section-overview-v2.php:310
#: elementor/widgets/single-property/section-overview.php:390
msgid "Meta Labels"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:1066
#: elementor/widgets/properties-recent-viewed.php:307
#: elementor/widgets/property-cards-v1.php:259
#: elementor/widgets/property-cards-v2.php:243
#: elementor/widgets/property-cards-v3.php:187
#: elementor/widgets/property-cards-v4.php:254
#: elementor/widgets/property-cards-v5.php:186
#: elementor/widgets/property-cards-v6.php:192
#: elementor/widgets/property-cards-v7.php:243
#: elementor/widgets/property-cards-v8.php:229
#: elementor/widgets/property-carousel-v1.php:234
#: elementor/widgets/property-carousel-v2.php:219
#: elementor/widgets/property-carousel-v3.php:163
#: elementor/widgets/property-carousel-v5.php:163
#: elementor/widgets/property-carousel-v6.php:164
#: elementor/widgets/property-carousel-v7.php:219
msgid "Meta Figure"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:1084
#: elementor/widgets/properties-recent-viewed.php:325
#: elementor/widgets/property-cards-v1.php:277
#: elementor/widgets/property-cards-v2.php:261
#: elementor/widgets/property-cards-v4.php:272
#: elementor/widgets/property-cards-v7.php:261
#: elementor/widgets/property-cards-v8.php:247
#: elementor/widgets/property-carousel-v1.php:252
#: elementor/widgets/property-carousel-v2.php:237
#: elementor/widgets/property-carousel-v7.php:237
msgid "Sub Price"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:1143
#: elementor/widgets/properties-recent-viewed.php:343
#: elementor/widgets/property-cards-v1.php:295
#: elementor/widgets/property-cards-v2.php:279
#: elementor/widgets/property-cards-v3.php:206
#: elementor/widgets/property-cards-v4.php:290
#: elementor/widgets/property-cards-v5.php:214
#: elementor/widgets/property-cards-v7.php:279
#: elementor/widgets/property-cards-v8.php:265
#: elementor/widgets/property-carousel-v1.php:270
#: elementor/widgets/property-carousel-v2.php:255
#: elementor/widgets/property-carousel-v3.php:182
#: elementor/widgets/property-carousel-v5.php:191
#: elementor/widgets/property-carousel-v7.php:255
msgid "Area Postfix"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:1173
#: elementor/widgets/properties-recent-viewed.php:352
#: elementor/widgets/property-cards-v1.php:304
#: elementor/widgets/property-cards-v4.php:299
#: elementor/widgets/property-carousel-v1.php:279
#: elementor/widgets/property-carousel-v7.php:264
msgid "Detail Button"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:1263
#: elementor/widgets/property-cards-v7.php:288
#: elementor/widgets/property-cards-v8.php:274
#: elementor/widgets/single-property/section-contact-2.php:354
#: elementor/widgets/single-property/section-schedule-tour-v2.php:171
#: elementor/widgets/testimonials-v3.php:110
msgid "Buttons"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:1300
#: elementor/widgets/properties-recent-viewed.php:145
#: elementor/widgets/property-cards-v1.php:158
#: elementor/widgets/property-cards-v2.php:154
#: elementor/widgets/property-cards-v3.php:153
#: elementor/widgets/property-cards-v4.php:153
#: elementor/widgets/property-cards-v5.php:153
#: elementor/widgets/property-cards-v6.php:159
#: elementor/widgets/property-cards-v7.php:157
#: elementor/widgets/property-cards-v8.php:140
#: elementor/widgets/property-carousel-v1.php:133
#: elementor/widgets/property-carousel-v2.php:130
#: elementor/widgets/property-carousel-v3.php:129
#: elementor/widgets/property-carousel-v5.php:130
#: elementor/widgets/property-carousel-v6.php:131
#: elementor/widgets/property-carousel-v7.php:133
msgid "Show/Hide Data"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:1308
msgid "Hide Description"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:1343
#: elementor/traits/Houzez_Property_Cards_Traits.php:2116
#: elementor/widgets/properties-recent-viewed.php:153
msgid "Hide Compare Button"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:1358
#: elementor/traits/Houzez_Property_Cards_Traits.php:2132
#: elementor/widgets/properties-recent-viewed.php:168
msgid "Hide Favorite Button"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:1373
#: elementor/traits/Houzez_Property_Cards_Traits.php:2148
#: elementor/widgets/properties-recent-viewed.php:183
msgid "Hide Preview Button"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:1388
#: elementor/traits/Houzez_Property_Cards_Traits.php:2164
#: elementor/widgets/properties-recent-viewed.php:198
msgid "Hide Featured Label"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:1403
#: elementor/traits/Houzez_Property_Cards_Traits.php:2180
#: elementor/widgets/properties-recent-viewed.php:213
msgid "Hide Status"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:1418
#: elementor/traits/Houzez_Property_Cards_Traits.php:2196
#: elementor/widgets/properties-recent-viewed.php:228
msgid "Hide Labels"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:1433
#: elementor/widgets/properties-recent-viewed.php:243
#: elementor/widgets/property-cards-v1.php:183
#: elementor/widgets/property-cards-v4.php:178
#: elementor/widgets/property-carousel-v1.php:158
msgid "Hide Details Button"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:1465
#: elementor/widgets/properties-recent-viewed.php:255
#: elementor/widgets/property-cards-v1.php:195
#: elementor/widgets/property-cards-v2.php:179
#: elementor/widgets/property-cards-v4.php:190
#: elementor/widgets/property-carousel-v1.php:170
#: elementor/widgets/property-carousel-v2.php:155
msgid "Hide Date & Agent"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:1506
#: elementor/widgets/header-footer/login-modal.php:298
#: elementor/widgets/login-modal.php:251
#: elementor/widgets/properties-recent-viewed.php:520
#: elementor/widgets/property-cards-v1.php:551
#: elementor/widgets/property-cards-v2.php:490
#: elementor/widgets/property-cards-v3.php:262
#: elementor/widgets/property-cards-v4.php:546
#: elementor/widgets/property-cards-v5.php:372
#: elementor/widgets/property-cards-v6.php:330
#: elementor/widgets/property-cards-v7.php:516
#: elementor/widgets/property-cards-v8.php:467
#: elementor/widgets/property-carousel-v1.php:526
#: elementor/widgets/property-carousel-v2.php:466
#: elementor/widgets/property-carousel-v3.php:239
#: elementor/widgets/property-carousel-v5.php:349
#: elementor/widgets/property-carousel-v6.php:302
#: elementor/widgets/property-carousel-v7.php:486
msgid "Colors"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:1694
#: elementor/widgets/property-cards-v1.php:561
#: elementor/widgets/property-cards-v2.php:500
#: elementor/widgets/property-cards-v4.php:556
#: elementor/widgets/property-cards-v8.php:513
#: elementor/widgets/property-carousel-v1.php:536
#: elementor/widgets/property-carousel-v2.php:476
msgid "Excerpt Color"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:1728
#: elementor/widgets/properties-recent-viewed.php:675
#: elementor/widgets/property-cards-v1.php:578
#: elementor/widgets/property-cards-v2.php:516
#: elementor/widgets/property-cards-v4.php:573
#: elementor/widgets/property-carousel-v1.php:553
#: elementor/widgets/property-carousel-v2.php:492
msgid "Agent & Date"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:1900
#: elementor/widgets/property-cards-v8.php:652
msgid "360° Button Color"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:1934
#: elementor/widgets/property-cards-v8.php:665
msgid "360° Button Background Color"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:1967
#: elementor/widgets/property-cards-v8.php:677
msgid "Video Button Color"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:2000
#: elementor/widgets/property-cards-v8.php:689
msgid "Video Button Background Color"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:2041
#: elementor/traits/Houzez_Style_Traits.php:680
#: elementor/traits/Houzez_Style_Traits.php:954
#: elementor/traits/Houzez_Style_Traits.php:1135
#: elementor/widgets/agent-card.php:273 elementor/widgets/agent-card.php:839
#: elementor/widgets/agent-card.php:895
#: elementor/widgets/header-footer/menu.php:232
#: elementor/widgets/property-cards-v7.php:449
#: elementor/widgets/property-carousel-v7.php:419
#: elementor/widgets/search-builder-old.php:879
#: elementor/widgets/search-builder.php:1017
#: elementor/widgets/single-property/section-attachments.php:100
#: elementor/widgets/single-property/section-contact-2.php:522
#: elementor/widgets/single-property/section-contact-bottom.php:404
#: elementor/widgets/single-property/section-details.php:382
#: elementor/widgets/single-property/section-schedule-tour-v2.php:56
msgid "Background"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:2052
#: elementor/traits/Houzez_Style_Traits.php:689
#: elementor/traits/Houzez_Style_Traits.php:1097
#: elementor/traits/Houzez_Style_Traits.php:1144
#: elementor/widgets/blog-posts.php:243
#: elementor/widgets/single-property/section-attachments.php:221
#: elementor/widgets/single-property/section-contact-2.php:545
#: elementor/widgets/single-property/section-contact-bottom.php:442
#: elementor/widgets/single-property/section-schedule-tour-v2.php:86
msgid "Box Padding"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:2073
#: elementor/traits/Houzez_Style_Traits.php:710
#: elementor/traits/Houzez_Style_Traits.php:1109
#: elementor/traits/Houzez_Style_Traits.php:1165
#: elementor/widgets/blog-posts.php:264
#: elementor/widgets/single-property/section-attachments.php:233
#: elementor/widgets/single-property/section-contact-2.php:566
#: elementor/widgets/single-property/section-contact-bottom.php:454
#: elementor/widgets/single-property/section-schedule-tour-v2.php:98
#: elementor/widgets/taxonomies-cards-carousel.php:352
#: elementor/widgets/taxonomies-cards.php:257
msgid "Box Radius"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:30
#: elementor/traits/Houzez_Style_Traits.php:483
#: elementor/widgets/contact-form.php:493
#: elementor/widgets/inquiry-form.php:478 elementor/widgets/page-title.php:126
#: elementor/widgets/search-builder-old.php:628
#: elementor/widgets/search-builder.php:615
#: elementor/widgets/single-agency/agency-content.php:63
#: elementor/widgets/single-agency/agency-excerpt.php:63
#: elementor/widgets/single-agent/agent-content.php:63
#: elementor/widgets/single-agent/agent-excerpt.php:63
#: elementor/widgets/single-post/post-content.php:63
#: elementor/widgets/single-post/post-excerpt.php:63
#: elementor/widgets/single-property/property-content.php:65
#: elementor/widgets/single-property/property-excerpt.php:64
#: elementor/widgets/single-property/section-description.php:210
msgid "Justified"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:80
#: elementor/widgets/page-title.php:176
msgid "Blend Mode"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:84
#: elementor/widgets/page-title.php:180
msgid "Multiply"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:85
#: elementor/widgets/page-title.php:181
msgid "Screen"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:86
#: elementor/widgets/page-title.php:182
msgid "Overlay"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:87
#: elementor/widgets/page-title.php:183
msgid "Darken"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:88
#: elementor/widgets/page-title.php:184
msgid "Lighten"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:89
#: elementor/widgets/page-title.php:185
msgid "Color Dodge"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:90
#: elementor/widgets/page-title.php:186
msgid "Saturation"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:92
#: elementor/widgets/page-title.php:188
msgid "Difference"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:93
#: elementor/widgets/page-title.php:189
msgid "Exclusion"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:94
#: elementor/widgets/page-title.php:190
msgid "Hue"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:95
#: elementor/widgets/page-title.php:191
msgid "Luminosity"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:107
msgid "Permalink"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:119
#: elementor/widgets/blog-posts-carousel.php:650
#: elementor/widgets/blog-posts-v2.php:478 elementor/widgets/blog-posts.php:605
msgid "Hover Color"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:141
#: elementor/widgets/agents-grid.php:395 elementor/widgets/banner-image.php:173
#: elementor/widgets/custom-carousel.php:458
#: elementor/widgets/custom-carousel.php:1190
#: elementor/widgets/grid-builder.php:376
#: elementor/widgets/properties-ajax-tabs.php:258
#: elementor/widgets/properties-ajax-tabs.php:280
#: elementor/widgets/single-post/author-box.php:221
#: elementor/widgets/single-property/featured-image.php:117
#: elementor/widgets/single-property/section-toparea-v3.php:77
#: elementor/widgets/single-property/section-toparea-v7.php:73
#: elementor/widgets/team-member.php:86
#: elementor/widgets/testimonials-v2.php:167
#: elementor/widgets/testimonials-v3.php:93
#: elementor/widgets/testimonials.php:107
msgid "Image"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:209
#: elementor/widgets/banner-image.php:216
#: elementor/widgets/custom-carousel.php:295
#: elementor/widgets/custom-carousel.php:501
#: elementor/widgets/header-footer/site-logo.php:208
#: elementor/widgets/single-property/featured-image.php:160
msgid "Max Width"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:266
#: elementor/widgets/banner-image.php:273
#: elementor/widgets/custom-carousel.php:558
msgid "Object Fit"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:273
#: elementor/widgets/banner-image.php:280
#: elementor/widgets/custom-carousel.php:565
msgid "Fill"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:274
#: elementor/widgets/banner-image.php:281
#: elementor/widgets/custom-carousel.php:566
#: extensions/meta-box/inc/fields/background.php:126
msgid "Cover"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:275
#: elementor/widgets/banner-image.php:282
#: elementor/widgets/custom-carousel.php:567
#: extensions/meta-box/inc/fields/background.php:127
msgid "Contain"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:276
#: elementor/widgets/custom-carousel.php:568
msgid "Scale Down"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:288
#: elementor/widgets/banner-image.php:294
#: elementor/widgets/custom-carousel.php:580
msgid "Object Position"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:291
#: elementor/widgets/banner-image.php:297
#: elementor/widgets/custom-carousel.php:583
#: extensions/meta-box/inc/fields/background.php:95
msgid "Center Center"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:292
#: elementor/widgets/banner-image.php:298
#: elementor/widgets/custom-carousel.php:584
#: extensions/meta-box/inc/fields/background.php:94
msgid "Center Left"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:293
#: elementor/widgets/banner-image.php:299
#: elementor/widgets/custom-carousel.php:585
#: extensions/meta-box/inc/fields/background.php:96
msgid "Center Right"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:294
#: elementor/widgets/banner-image.php:300
#: elementor/widgets/custom-carousel.php:586
#: extensions/meta-box/inc/fields/background.php:92
msgid "Top Center"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:295
#: elementor/widgets/banner-image.php:301
#: elementor/widgets/custom-carousel.php:587
#: extensions/meta-box/inc/fields/background.php:91
msgid "Top Left"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:296
#: elementor/widgets/banner-image.php:302
#: elementor/widgets/custom-carousel.php:588
#: extensions/meta-box/inc/fields/background.php:93
msgid "Top Right"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:297
#: elementor/widgets/banner-image.php:303
#: elementor/widgets/custom-carousel.php:589
#: extensions/meta-box/inc/fields/background.php:98
msgid "Bottom Center"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:298
#: elementor/widgets/banner-image.php:304
#: elementor/widgets/custom-carousel.php:590
#: extensions/meta-box/inc/fields/background.php:97
msgid "Bottom Left"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:299
#: elementor/widgets/banner-image.php:305
#: elementor/widgets/custom-carousel.php:591
#: extensions/meta-box/inc/fields/background.php:99
msgid "Bottom Right"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:455
#: elementor/widgets/header-footer/site-logo.php:463
#: elementor/widgets/header-footer/site-logo.php:536
msgid "Caption"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:569
msgid "Widget Title"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:601
msgid "Widget Box"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:815
#: elementor/widgets/blog-posts-carousel.php:330
#: elementor/widgets/grid-builder.php:512
#: elementor/widgets/single-property/property-title-area.php:106
#: elementor/widgets/single-property/section-contact-bottom.php:157
#: elementor/widgets/single-property/section-contact-bottom.php:542
#: elementor/widgets/single-property/section-schedule-tour.php:183
#: elementor/widgets/single-property/section-toparea-v6.php:91
msgid "Padding Bottom"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:853
#: elementor/widgets/single-agency/agency-profile-v1.php:178
#: elementor/widgets/single-agency/agency-profile-v2.php:112
#: elementor/widgets/single-agent/agent-profile-v1.php:190
#: elementor/widgets/single-agent/agent-profile-v2.php:124
msgid "Anchor Color"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:876
#: elementor/traits/Houzez_Style_Traits.php:1207
#: elementor/widgets/section-title.php:37
#: elementor/widgets/section-title.php:116
#: elementor/widgets/single-agency/agency-contact.php:274
#: elementor/widgets/single-agent/agent-contact.php:274
#: elementor/widgets/single-property/section-360-virtual.php:65
#: elementor/widgets/single-property/section-360-virtual.php:102
#: elementor/widgets/single-property/section-address.php:65
#: elementor/widgets/single-property/section-address.php:210
#: elementor/widgets/single-property/section-attachments.php:64
#: elementor/widgets/single-property/section-attachments.php:255
#: elementor/widgets/single-property/section-calculator.php:64
#: elementor/widgets/single-property/section-calculator.php:101
#: elementor/widgets/single-property/section-calendar.php:64
#: elementor/widgets/single-property/section-calendar.php:101
#: elementor/widgets/single-property/section-contact-bottom.php:64
#: elementor/widgets/single-property/section-description.php:64
#: elementor/widgets/single-property/section-description.php:137
#: elementor/widgets/single-property/section-details.php:65
#: elementor/widgets/single-property/section-details.php:440
#: elementor/widgets/single-property/section-energy.php:64
#: elementor/widgets/single-property/section-energy.php:187
#: elementor/widgets/single-property/section-features.php:64
#: elementor/widgets/single-property/section-features.php:115
#: elementor/widgets/single-property/section-floorplan-v2.php:64
#: elementor/widgets/single-property/section-floorplan-v2.php:145
#: elementor/widgets/single-property/section-floorplan.php:64
#: elementor/widgets/single-property/section-floorplan.php:156
#: elementor/widgets/single-property/section-nearby.php:64
#: elementor/widgets/single-property/section-nearby.php:109
#: elementor/widgets/single-property/section-overview-v2.php:161
#: elementor/widgets/single-property/section-overview-v2.php:282
#: elementor/widgets/single-property/section-overview.php:160
#: elementor/widgets/single-property/section-overview.php:362
#: elementor/widgets/single-property/section-schedule-tour.php:65
#: elementor/widgets/single-property/section-schedule-tour.php:94
#: elementor/widgets/single-property/section-schedule-tour.php:102
#: elementor/widgets/single-property/section-similar.php:65
#: elementor/widgets/single-property/section-sublistings.php:63
#: elementor/widgets/single-property/section-video.php:64
#: elementor/widgets/single-property/section-video.php:101
#: elementor/widgets/single-property/section-walkscore.php:64
#: elementor/widgets/single-property/section-walkscore.php:109
msgid "Section Title"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:913
msgid "View Listing"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:979
#: elementor/traits/Houzez_Style_Traits.php:1332
#: elementor/widgets/property-cards-v7.php:460
#: elementor/widgets/property-carousel-v7.php:430
#: elementor/widgets/single-property/section-address.php:309
#: elementor/widgets/single-property/section-contact-bottom.php:120
#: elementor/widgets/single-property/section-details.php:539
#: elementor/widgets/single-property/section-overview.php:296
#: elementor/widgets/single-property/section-similar.php:206
msgid "Border Type"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:1036
#: elementor/traits/Houzez_Style_Traits.php:1369
#: elementor/widgets/single-agency/agency-contact-form.php:87
#: elementor/widgets/single-agent/agent-contact-form.php:87
#: elementor/widgets/single-property/section-attachments.php:160
#: elementor/widgets/single-property/section-contact-2.php:151
#: elementor/widgets/single-property/section-similar.php:263
#: elementor/widgets/single-property/section-sublistings.php:173
msgid "Title Padding Bottom"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:1066
#: elementor/traits/Houzez_Style_Traits.php:1399
#: elementor/widgets/single-property/section-attachments.php:190
#: elementor/widgets/single-property/section-similar.php:293
#: elementor/widgets/single-property/section-sublistings.php:203
msgid "Title Margin Bottom"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:1126
msgid "Form Box"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:1189
#: elementor/traits/Houzez_Style_Traits.php:1243
#: elementor/traits/Houzez_Testimonials_Traits.php:66
#: elementor/widgets/advanced-search.php:79 elementor/widgets/agent-card.php:84
#: elementor/widgets/agents-grid.php:84 elementor/widgets/agents.php:84
#: elementor/widgets/agents.php:272 elementor/widgets/banner-image.php:98
#: elementor/widgets/blog-posts-carousel.php:82
#: elementor/widgets/blog-posts-v2.php:80 elementor/widgets/blog-posts.php:80
#: elementor/widgets/custom-carousel.php:739
#: elementor/widgets/custom-carousel.php:1179
#: elementor/widgets/grid-builder.php:80 elementor/widgets/grids.php:79
#: elementor/widgets/header-footer/area-switcher.php:114
#: elementor/widgets/header-footer/currency.php:114
#: elementor/widgets/header-footer/lang.php:114
#: elementor/widgets/header-footer/menu.php:142
#: elementor/widgets/icon-box.php:88 elementor/widgets/listings-tabs.php:85
#: elementor/widgets/page-title.php:80 elementor/widgets/page-title.php:102
#: elementor/widgets/partners.php:79 elementor/widgets/price-table.php:87
#: elementor/widgets/price-table.php:176
#: elementor/widgets/properties-grids.php:82
#: elementor/widgets/properties-recent-viewed.php:80
#: elementor/widgets/properties-slider.php:84
#: elementor/widgets/properties.php:85 elementor/widgets/property-by-id.php:80
#: elementor/widgets/property-by-ids.php:80
#: elementor/widgets/property-cards-v2.php:82
#: elementor/widgets/property-cards-v3.php:82
#: elementor/widgets/property-cards-v4.php:82
#: elementor/widgets/property-meta-data.php:179
#: elementor/widgets/section-title.php:79
#: elementor/widgets/single-agency/agency-about.php:45
#: elementor/widgets/single-agency/agency-about.php:88
#: elementor/widgets/single-agency/agency-agents.php:45
#: elementor/widgets/single-agency/agency-contact-form.php:47
#: elementor/widgets/single-agency/agency-contact.php:44
#: elementor/widgets/single-agency/agency-listings-review.php:52
#: elementor/widgets/single-agency/agency-listings.php:52
#: elementor/widgets/single-agency/agency-map.php:41
#: elementor/widgets/single-agency/agency-meta.php:208
#: elementor/widgets/single-agency/agency-name.php:85
#: elementor/widgets/single-agency/agency-profile-v1.php:45
#: elementor/widgets/single-agency/agency-profile-v1.php:146
#: elementor/widgets/single-agency/agency-profile-v2.php:45
#: elementor/widgets/single-agency/agency-profile-v2.php:80
#: elementor/widgets/single-agent/agent-about.php:45
#: elementor/widgets/single-agent/agent-about.php:88
#: elementor/widgets/single-agent/agent-contact-form.php:47
#: elementor/widgets/single-agent/agent-contact.php:44
#: elementor/widgets/single-agent/agent-listings-review.php:57
#: elementor/widgets/single-agent/agent-listings.php:57
#: elementor/widgets/single-agent/agent-map.php:41
#: elementor/widgets/single-agent/agent-meta.php:210
#: elementor/widgets/single-agent/agent-name.php:85
#: elementor/widgets/single-agent/agent-profile-v1.php:45
#: elementor/widgets/single-agent/agent-profile-v2.php:45
#: elementor/widgets/single-agent/agent-profile-v2.php:92
#: elementor/widgets/single-post/post-image.php:42
#: elementor/widgets/single-post/post-title.php:72
#: elementor/widgets/single-property/featured-image.php:42
#: elementor/widgets/single-property/images-gallery-v1.php:42
#: elementor/widgets/single-property/images-gallery-v2.php:48
#: elementor/widgets/single-property/images-gallery-v3.php:41
#: elementor/widgets/single-property/images-gallery-v4.php:41
#: elementor/widgets/single-property/images-gallery-v5.php:41
#: elementor/widgets/single-property/item-tools.php:44
#: elementor/widgets/single-property/item-tools.php:90
#: elementor/widgets/single-property/property-address.php:70
#: elementor/widgets/single-property/property-excerpt.php:40
#: elementor/widgets/single-property/property-price.php:44
#: elementor/widgets/single-property/property-title-area.php:75
#: elementor/widgets/single-property/property-title.php:72
#: elementor/widgets/single-property/section-360-virtual.php:47
#: elementor/widgets/single-property/section-address.php:47
#: elementor/widgets/single-property/section-attachments.php:46
#: elementor/widgets/single-property/section-block-gallery.php:42
#: elementor/widgets/single-property/section-calculator.php:46
#: elementor/widgets/single-property/section-calendar.php:46
#: elementor/widgets/single-property/section-contact-2.php:47
#: elementor/widgets/single-property/section-contact-bottom.php:46
#: elementor/widgets/single-property/section-description.php:46
#: elementor/widgets/single-property/section-details.php:47
#: elementor/widgets/single-property/section-energy.php:46
#: elementor/widgets/single-property/section-features.php:46
#: elementor/widgets/single-property/section-features.php:143
#: elementor/widgets/single-property/section-floorplan-v2.php:46
#: elementor/widgets/single-property/section-floorplan-v2.php:268
#: elementor/widgets/single-property/section-floorplan.php:46
#: elementor/widgets/single-property/section-google-map.php:42
#: elementor/widgets/single-property/section-map.php:42
#: elementor/widgets/single-property/section-nearby.php:46
#: elementor/widgets/single-property/section-open-street-map.php:42
#: elementor/widgets/single-property/section-overview-v2.php:143
#: elementor/widgets/single-property/section-overview.php:142
#: elementor/widgets/single-property/section-schedule-tour-v2.php:47
#: elementor/widgets/single-property/section-schedule-tour.php:47
#: elementor/widgets/single-property/section-similar.php:47
#: elementor/widgets/single-property/section-sublistings.php:45
#: elementor/widgets/single-property/section-toparea-v6.php:73
#: elementor/widgets/single-property/section-video.php:46
#: elementor/widgets/single-property/section-walkscore.php:46
#: elementor/widgets/sort-by.php:78 elementor/widgets/space.php:78
#: elementor/widgets/taxonomies-cards-carousel.php:79
#: elementor/widgets/taxonomies-cards.php:79
#: elementor/widgets/taxonomies-grids-carousel.php:79
#: elementor/widgets/taxonomies-grids-carousel.php:233
#: elementor/widgets/taxonomies-grids.php:82
#: elementor/widgets/taxonomies-grids.php:146
#: elementor/widgets/taxonomies-list.php:84
#: elementor/widgets/team-member.php:78 elementor/widgets/team-member.php:279
#: elementor/widgets/testimonials-v2.php:79
#: elementor/widgets/testimonials-v2.php:105
#: elementor/widgets/testimonials-v3.php:79
#: elementor/widgets/testimonials.php:79
msgid "Content"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:1197
#: elementor/widgets/single-property/section-360-virtual.php:55
#: elementor/widgets/single-property/section-address.php:55
#: elementor/widgets/single-property/section-attachments.php:54
#: elementor/widgets/single-property/section-calculator.php:54
#: elementor/widgets/single-property/section-calendar.php:54
#: elementor/widgets/single-property/section-contact-bottom.php:54
#: elementor/widgets/single-property/section-contact-bottom.php:89
#: elementor/widgets/single-property/section-description.php:54
#: elementor/widgets/single-property/section-details.php:55
#: elementor/widgets/single-property/section-energy.php:54
#: elementor/widgets/single-property/section-features.php:54
#: elementor/widgets/single-property/section-floorplan-v2.php:54
#: elementor/widgets/single-property/section-floorplan.php:54
#: elementor/widgets/single-property/section-nearby.php:54
#: elementor/widgets/single-property/section-overview-v2.php:151
#: elementor/widgets/single-property/section-overview.php:150
#: elementor/widgets/single-property/section-schedule-tour.php:55
#: elementor/widgets/single-property/section-similar.php:55
#: elementor/widgets/single-property/section-sublistings.php:53
#: elementor/widgets/single-property/section-video.php:54
#: elementor/widgets/single-property/section-walkscore.php:54
msgid "Section Header"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:1265
msgid "Review List"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:1301
#: elementor/widgets/single-property/section-contact-bottom.php:492
#: elementor/widgets/single-property/section-schedule-tour-v2.php:112
#: elementor/widgets/single-property/section-schedule-tour.php:156
msgid "Form Title"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:1434
#: elementor/widgets/blog-posts-carousel.php:272
#: elementor/widgets/blog-posts-v2.php:167 elementor/widgets/blog-posts.php:208
#: elementor/widgets/header-footer/menu.php:630
#: elementor/widgets/single-agency/agency-content.php:39
#: elementor/widgets/single-agency/agency-excerpt.php:39
#: elementor/widgets/single-agency/agency-search.php:66
#: elementor/widgets/single-agency/agency-single-stats.php:81
#: elementor/widgets/single-agent/agent-content.php:39
#: elementor/widgets/single-agent/agent-excerpt.php:39
#: elementor/widgets/single-agent/agent-position.php:60
#: elementor/widgets/single-agent/agent-profile-v1.php:158
#: elementor/widgets/single-agent/agent-search.php:66
#: elementor/widgets/single-agent/agent-single-stats.php:81
#: elementor/widgets/single-post/post-content.php:39
#: elementor/widgets/single-post/post-excerpt.php:39
#: elementor/widgets/single-post/post-info.php:442
#: elementor/widgets/single-property/breadcrumb.php:43
#: elementor/widgets/single-property/featured-label.php:44
#: elementor/widgets/single-property/item-label.php:44
#: elementor/widgets/single-property/property-address.php:148
#: elementor/widgets/single-property/property-content.php:41
#: elementor/widgets/single-property/status-label.php:45
#: elementor/widgets/sort-by.php:98
#: elementor/widgets/taxonomies-cards-carousel.php:248
#: elementor/widgets/taxonomies-cards.php:153
#: elementor/widgets/taxonomies-list.php:245
msgid "Style"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:1452
#: elementor/widgets/single-property/breadcrumb.php:62
#: elementor/widgets/single-property/property-content.php:107
#: elementor/widgets/single-property/property-title-area.php:162
#: elementor/widgets/single-property/section-attachments.php:312
#: elementor/widgets/single-property/section-description.php:252
#: elementor/widgets/single-property/section-description.php:281
msgid "Link Color"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:1463
#: elementor/widgets/agents-grid.php:371
#: elementor/widgets/single-property/property-title-area.php:173
msgid "Separator Color"
msgstr ""

#: elementor/traits/Houzez_Testimonials_Traits.php:16
#: elementor/widgets/partners.php:128
#: elementor/widgets/single-property/section-similar.php:185
msgid "Limit"
msgstr ""

#: elementor/traits/Houzez_Testimonials_Traits.php:18
#: elementor/widgets/partners.php:130
msgid "Number of testimonials to show."
msgstr ""

#: elementor/traits/Houzez_Testimonials_Traits.php:25
msgid "Offset posts"
msgstr ""

#: elementor/traits/Houzez_Testimonials_Traits.php:40
#: elementor/widgets/agents-grid.php:170 elementor/widgets/agents.php:168
#: elementor/widgets/partners.php:95
#: elementor/widgets/single-property/section-similar.php:176
#: functions/functions.php:257
msgid "Random"
msgstr ""

#: elementor/traits/Houzez_Testimonials_Traits.php:41
#: elementor/widgets/agents-grid.php:171 elementor/widgets/agents.php:169
#: elementor/widgets/partners.php:96
msgid "Menu Order"
msgstr ""

#: elementor/widgets/advanced-search.php:37
msgid "Advanced Search"
msgstr ""

#: elementor/widgets/advanced-search.php:89
msgid "Enter search title"
msgstr ""

#: elementor/widgets/agent-card.php:37
msgid "Agent Card"
msgstr ""

#: elementor/widgets/agent-card.php:91 elementor/widgets/agents-grid.php:92
#: elementor/widgets/properties-ajax-tabs.php:592
#: elementor/widgets/properties.php:117
#: elementor/widgets/property-cards-v1.php:91
#: elementor/widgets/property-cards-v2.php:90
#: elementor/widgets/property-cards-v3.php:90
#: elementor/widgets/property-cards-v4.php:90
#: elementor/widgets/property-cards-v5.php:90
#: elementor/widgets/property-cards-v6.php:91
#: elementor/widgets/property-cards-v7.php:90
#: elementor/widgets/single-post/author-box.php:169
#: elementor/widgets/single-post/post-info.php:48
msgid "Layout"
msgstr ""

#: elementor/widgets/agent-card.php:95
msgid "Variation 1"
msgstr ""

#: elementor/widgets/agent-card.php:96
msgid "Variation 2"
msgstr ""

#: elementor/widgets/agent-card.php:97
msgid "Variation 3"
msgstr ""

#: elementor/widgets/agent-card.php:105
msgid "Agent Source"
msgstr ""

#: elementor/widgets/agent-card.php:109
msgid "Auto"
msgstr ""

#: elementor/widgets/agent-card.php:110
msgid "Specific Agent"
msgstr ""

#: elementor/widgets/agent-card.php:112
msgid ""
"Select \"Auto\" to display the agent associated with the current property "
"(for property detail pages). Select \"Specific Agent\" to choose an agent."
msgstr ""

#: elementor/widgets/agent-card.php:119
msgid "Select Agent"
msgstr ""

#: elementor/widgets/agent-card.php:133
msgid "View Listings Text"
msgstr ""

#: elementor/widgets/agent-card.php:135 elementor/widgets/agent-card.php:136
msgid "View my listings"
msgstr ""

#: elementor/widgets/agent-card.php:137
msgid "Customize the \"View my listings\" link text"
msgstr ""

#: elementor/widgets/agent-card.php:145
msgid "Show Verification Badge"
msgstr ""

#: elementor/widgets/agent-card.php:150
msgid "Show verification badge for verified agents"
msgstr ""

#: elementor/widgets/agent-card.php:157
#: elementor/widgets/single-agency/agency-name.php:70
#: elementor/widgets/single-agent/agent-name.php:70
msgid "Hide \"Verified\" Text"
msgstr ""

#: elementor/widgets/agent-card.php:163
#: elementor/widgets/single-agency/agency-name.php:76
#: elementor/widgets/single-agent/agent-name.php:76
msgid "Show only the verification icon without text"
msgstr ""

#: elementor/widgets/agent-card.php:173
msgid "Show Phone Number"
msgstr ""

#: elementor/widgets/agent-card.php:184
msgid "Show Mobile Number"
msgstr ""

#: elementor/widgets/agent-card.php:195
msgid "Click to Reveal"
msgstr ""

#: elementor/widgets/agent-card.php:200
msgid "Enable to hide phone numbers until clicked"
msgstr ""

#: elementor/widgets/agent-card.php:207 elementor/widgets/agent-card.php:768
msgid "Action Buttons"
msgstr ""

#: elementor/widgets/agent-card.php:216
msgid "Show Call Button"
msgstr ""

#: elementor/widgets/agent-card.php:226
msgid "Show WhatsApp Button"
msgstr ""

#: elementor/widgets/agent-card.php:237
msgid "Show Telegram Button"
msgstr ""

#: elementor/widgets/agent-card.php:248
msgid "Show Line Button"
msgstr ""

#: elementor/widgets/agent-card.php:264
msgid "Card Styling"
msgstr ""

#: elementor/widgets/agent-card.php:351
msgid "Agent Image"
msgstr ""

#: elementor/widgets/agent-card.php:402
msgid "Image Border Radius"
msgstr ""

#: elementor/widgets/agent-card.php:408
#, no-php-format
msgid ""
"Override card border radius for the image. For Variation 2, use 50% for a "
"circle."
msgstr ""

#: elementor/widgets/agent-card.php:416
msgid "Image Border"
msgstr ""

#: elementor/widgets/agent-card.php:467
#: elementor/widgets/single-agency/agency-meta.php:417
#: elementor/widgets/single-agent/agent-meta.php:418
#: elementor/widgets/single-property/item-tools.php:134
#: elementor/widgets/single-property/property-title-area.php:605
#: elementor/widgets/single-property/section-toparea-v1.php:507
#: elementor/widgets/single-property/section-toparea-v2.php:529
#: elementor/widgets/single-property/section-toparea-v3.php:531
#: elementor/widgets/single-property/section-toparea-v5.php:506
#: elementor/widgets/single-property/section-toparea-v6.php:551
#: elementor/widgets/single-property/section-toparea-v7.php:526
msgid "Color Hover"
msgstr ""

#: elementor/widgets/agent-card.php:478
msgid "Bottom Spacing"
msgstr ""

#: elementor/widgets/agent-card.php:499
#: elementor/widgets/single-agency/agency-name.php:165
#: elementor/widgets/single-agent/agent-name.php:165
msgid "Verification Badge"
msgstr ""

#: elementor/widgets/agent-card.php:543 elementor/widgets/agent-card.php:714
#: elementor/widgets/agent-card.php:827 elementor/widgets/agent-card.php:883
#: elementor/widgets/header-footer/login-modal.php:312
#: elementor/widgets/header-footer/login-modal.php:379
#: elementor/widgets/icon-box.php:324 elementor/widgets/login-modal.php:259
#: elementor/widgets/single-agency/agency-name.php:206
#: elementor/widgets/single-agent/agent-name.php:206
#: elementor/widgets/single-property/section-floorplan.php:212
msgid "Icon Color"
msgstr ""

#: elementor/widgets/agent-card.php:610
msgid "View Listing Text"
msgstr ""

#: elementor/widgets/agent-card.php:643
msgid "Contact Info Text"
msgstr ""

#: elementor/widgets/agent-card.php:670
#: elementor/widgets/property-cards-v8.php:612
msgid "Text Color Hover"
msgstr ""

#: elementor/widgets/agent-card.php:681
msgid "Item Bottom Spacing"
msgstr ""

#: elementor/widgets/agent-card.php:706
msgid "Contact Icons"
msgstr ""

#: elementor/widgets/agent-card.php:725 elementor/widgets/icon-box.php:336
#: elementor/widgets/single-property/section-floorplan.php:223
msgid "Icon Size"
msgstr ""

#: elementor/widgets/agent-card.php:743
msgid "Icon Right Spacing"
msgstr ""

#: elementor/widgets/agent-card.php:776
msgid "Buttons Top Spacing"
msgstr ""

#: elementor/widgets/agent-card.php:794
msgid "Button Icon Size"
msgstr ""

#: elementor/widgets/agent-card.php:806
msgid "Button Padding"
msgstr ""

#: elementor/widgets/agent-card.php:941
#, php-format
msgid "Profile photo of agent %s"
msgstr ""

#: elementor/widgets/agent-card.php:971
#: elementor/widgets/single-agency/agency-name.php:306
#: elementor/widgets/single-agent/agent-name.php:306
msgid "Verified"
msgstr ""

#: elementor/widgets/agent-card.php:1039
#: elementor/widgets/single-agency/agency-meta.php:63
#: elementor/widgets/single-agent/agent-meta.php:65
msgid "WhatsApp"
msgstr ""

#: elementor/widgets/agent-card.php:1050
#: elementor/widgets/single-agency/agency-meta.php:65
#: elementor/widgets/single-agent/agent-meta.php:67
msgid "Telegram"
msgstr ""

#: elementor/widgets/agent-card.php:1061
msgid "Line"
msgstr ""

#: elementor/widgets/agent-card.php:1086
msgid "Contact us"
msgstr ""

#: elementor/widgets/agent-card.php:1093
msgid "Please quote property reference"
msgstr ""

#: elementor/widgets/agent-card.php:1268
msgid ""
"Please select an agent or ensure the widget is on a property page with an "
"assigned agent."
msgstr ""

#: elementor/widgets/agents-grid.php:36
msgid "Agents Grid"
msgstr ""

#: elementor/widgets/agents-grid.php:95 elementor/widgets/properties.php:106
msgid "Version 1"
msgstr ""

#: elementor/widgets/agents-grid.php:96 elementor/widgets/properties.php:107
msgid "Version 2"
msgstr ""

#: elementor/widgets/agents-grid.php:106 elementor/widgets/agents.php:106
#: elementor/widgets/properties-recent-viewed.php:106
#: elementor/widgets/property-by-ids.php:143
#: elementor/widgets/single-agency/agency-listings-review.php:101
#: elementor/widgets/single-agency/agency-listings.php:101
#: elementor/widgets/single-agent/agent-listings-review.php:106
#: elementor/widgets/single-agent/agent-listings.php:106
#: elementor/widgets/single-property/section-overview.php:243
#: elementor/widgets/single-property/section-similar.php:139
#: elementor/widgets/taxonomies-cards.php:111
#: elementor/widgets/taxonomies-grids.php:90
msgid "Columns"
msgstr ""

#: elementor/widgets/agents-grid.php:109 elementor/widgets/agents.php:109
#: elementor/widgets/properties-ajax-tabs.php:596
msgid "4 Columns"
msgstr ""

#: elementor/widgets/agents-grid.php:110 elementor/widgets/agents.php:110
#: elementor/widgets/properties-ajax-tabs.php:595
#: elementor/widgets/properties-recent-viewed.php:109
#: elementor/widgets/property-by-ids.php:146
#: elementor/widgets/single-property/section-address.php:84
#: elementor/widgets/single-property/section-details.php:85
#: elementor/widgets/single-property/section-details.php:179
#: elementor/widgets/single-property/section-features.php:83
msgid "3 Columns"
msgstr ""

#: elementor/widgets/agents-grid.php:144 elementor/widgets/agents.php:142
msgid "Number of Agents"
msgstr ""

#: elementor/widgets/agents-grid.php:197
#: elementor/widgets/blog-posts-carousel.php:137
#: elementor/widgets/blog-posts-v2.php:133 elementor/widgets/blog-posts.php:163
msgid "Show/Hide"
msgstr ""

#: elementor/widgets/agents-grid.php:205
msgid "Hide Agent Position"
msgstr ""

#: elementor/widgets/agents-grid.php:220
msgid "Hide Listings Count"
msgstr ""

#: elementor/widgets/agents-grid.php:235
msgid "Hide Languages"
msgstr ""

#: elementor/widgets/agents-grid.php:250
msgid "Hide Button"
msgstr ""

#: elementor/widgets/agents-grid.php:265
msgid "Hide Empty Bottom"
msgstr ""

#: elementor/widgets/agents-grid.php:307 elementor/widgets/agents.php:195
#: elementor/widgets/single-agency/agency-contact-form.php:137
#: elementor/widgets/single-agency/agency-contact.php:162
#: elementor/widgets/single-agent/agent-contact-form.php:137
#: elementor/widgets/single-agent/agent-contact.php:162
#: elementor/widgets/single-property/section-contact-2.php:513
#: elementor/widgets/single-property/section-contact-bottom.php:395
#: elementor/widgets/team-member.php:221
msgid "Box"
msgstr ""

#: elementor/widgets/agents-grid.php:355
msgid "Hide Separator"
msgstr ""

#: elementor/widgets/agents-grid.php:454
msgid "Listings Count"
msgstr ""

#: elementor/widgets/agents-grid.php:463
#: elementor/widgets/single-agency/agency-about.php:74
#: elementor/widgets/single-agent/agent-about.php:74
msgid "Languages"
msgstr ""

#: elementor/widgets/agents.php:92
msgid "Module Type"
msgstr ""

#: elementor/widgets/agents.php:95
msgid "Grid"
msgstr ""

#: elementor/widgets/agents.php:96
msgid "Carousel"
msgstr ""

#: elementor/widgets/banner-image.php:42
msgid "Image Banner"
msgstr ""

#: elementor/widgets/banner-image.php:106
#: elementor/widgets/grid-builder.php:102
#: elementor/widgets/grid-builder.php:405
#: elementor/widgets/header-footer/site-logo.php:413
msgid "Choose Image"
msgstr ""

#: elementor/widgets/banner-image.php:143
#: elementor/widgets/banner-image.php:156
#: elementor/widgets/create-listing-btn.php:206
#: elementor/widgets/grid-builder.php:150
#: elementor/widgets/header-footer/create-listing-btn.php:238
#: elementor/widgets/header-footer/site-logo.php:494
#: elementor/widgets/header-footer/site-logo.php:511
#: elementor/widgets/single-post/author-box.php:118
#: elementor/widgets/single-post/post-info.php:269
msgid "Link"
msgstr ""

#: elementor/widgets/banner-image.php:148
#: elementor/widgets/header-footer/site-logo.php:500
#: elementor/widgets/single-post/post-info.php:281
msgid "Custom URL"
msgstr ""

#: elementor/widgets/blog-posts-carousel.php:36
msgid "Blog Posts Carousel"
msgstr ""

#: elementor/widgets/blog-posts-carousel.php:90
#: elementor/widgets/blog-posts.php:98
msgid "Grid Version"
msgstr ""

#: elementor/widgets/blog-posts-carousel.php:116
#: elementor/widgets/blog-posts.php:140
msgid "Number of posts to show"
msgstr ""

#: elementor/widgets/blog-posts-carousel.php:144
#: elementor/widgets/blog-posts.php:170
msgid "Post Author"
msgstr ""

#: elementor/widgets/blog-posts-carousel.php:146
#: elementor/widgets/blog-posts-carousel.php:157
#: elementor/widgets/blog-posts-carousel.php:169
#: elementor/widgets/blog-posts-v2.php:142
#: elementor/widgets/blog-posts-v2.php:154 elementor/widgets/blog-posts.php:172
#: elementor/widgets/blog-posts.php:183 elementor/widgets/blog-posts.php:195
#: elementor/widgets/contact-form.php:316
#: elementor/widgets/contact-form.php:329
#: elementor/widgets/contact-form.php:343
#: elementor/widgets/contact-form.php:356
#: elementor/widgets/inquiry-form.php:303
#: elementor/widgets/inquiry-form.php:316
#: elementor/widgets/inquiry-form.php:330
#: elementor/widgets/inquiry-form.php:343
#: elementor/widgets/listings-tabs.php:157 elementor/widgets/page-title.php:90
#: elementor/widgets/search-builder-old.php:602
#: elementor/widgets/search-builder-old.php:642
#: elementor/widgets/search-builder-old.php:672
#: elementor/widgets/search-builder-old.php:753
#: elementor/widgets/search-builder.php:589
#: elementor/widgets/search-builder.php:629
#: elementor/widgets/search-builder.php:807
#: elementor/widgets/search-builder.php:888
#: elementor/widgets/single-agency/agency-about.php:64
#: elementor/widgets/single-agency/agency-about.php:76
#: elementor/widgets/single-agency/agency-contact.php:54
#: elementor/widgets/single-agency/agency-contact.php:66
#: elementor/widgets/single-agency/agency-contact.php:78
#: elementor/widgets/single-agency/agency-contact.php:90
#: elementor/widgets/single-agency/agency-contact.php:102
#: elementor/widgets/single-agency/agency-contact.php:114
#: elementor/widgets/single-agency/agency-contact.php:126
#: elementor/widgets/single-agency/agency-contact.php:138
#: elementor/widgets/single-agency/agency-contact.php:150
#: elementor/widgets/single-agency/agency-listings-review.php:149
#: elementor/widgets/single-agency/agency-listings-review.php:229
#: elementor/widgets/single-agency/agency-listings.php:149
#: elementor/widgets/single-agency/agency-listings.php:229
#: elementor/widgets/single-agency/agency-profile-v1.php:55
#: elementor/widgets/single-agency/agency-profile-v1.php:67
#: elementor/widgets/single-agency/agency-profile-v1.php:79
#: elementor/widgets/single-agency/agency-profile-v1.php:91
#: elementor/widgets/single-agency/agency-profile-v1.php:103
#: elementor/widgets/single-agency/agency-profile-v2.php:67
#: elementor/widgets/single-agency/agency-rating.php:51
#: elementor/widgets/single-agency/agency-rating.php:63
#: elementor/widgets/single-agency/agency-rating.php:75
#: elementor/widgets/single-agent/agent-about.php:64
#: elementor/widgets/single-agent/agent-about.php:76
#: elementor/widgets/single-agent/agent-contact.php:54
#: elementor/widgets/single-agent/agent-contact.php:66
#: elementor/widgets/single-agent/agent-contact.php:78
#: elementor/widgets/single-agent/agent-contact.php:90
#: elementor/widgets/single-agent/agent-contact.php:102
#: elementor/widgets/single-agent/agent-contact.php:114
#: elementor/widgets/single-agent/agent-contact.php:126
#: elementor/widgets/single-agent/agent-contact.php:138
#: elementor/widgets/single-agent/agent-contact.php:150
#: elementor/widgets/single-agent/agent-listings-review.php:154
#: elementor/widgets/single-agent/agent-listings-review.php:234
#: elementor/widgets/single-agent/agent-listings.php:154
#: elementor/widgets/single-agent/agent-listings.php:234
#: elementor/widgets/single-agent/agent-profile-v1.php:55
#: elementor/widgets/single-agent/agent-profile-v1.php:67
#: elementor/widgets/single-agent/agent-profile-v1.php:79
#: elementor/widgets/single-agent/agent-profile-v1.php:91
#: elementor/widgets/single-agent/agent-profile-v1.php:103
#: elementor/widgets/single-agent/agent-profile-v1.php:115
#: elementor/widgets/single-agent/agent-profile-v2.php:67
#: elementor/widgets/single-agent/agent-profile-v2.php:79
#: elementor/widgets/single-agent/agent-rating.php:51
#: elementor/widgets/single-agent/agent-rating.php:63
#: elementor/widgets/single-agent/agent-rating.php:75
#: elementor/widgets/single-post/author-box.php:62
#: elementor/widgets/single-post/author-box.php:88
#: elementor/widgets/single-post/author-box.php:135
#: elementor/widgets/single-post/author-box.php:149
#: elementor/widgets/single-post/post-navigation.php:51
#: elementor/widgets/single-post/post-navigation.php:98
msgid "Show"
msgstr ""

#: elementor/widgets/blog-posts-carousel.php:147
#: elementor/widgets/blog-posts-carousel.php:158
#: elementor/widgets/blog-posts-carousel.php:170
#: elementor/widgets/blog-posts-v2.php:143
#: elementor/widgets/blog-posts-v2.php:155 elementor/widgets/blog-posts.php:173
#: elementor/widgets/blog-posts.php:184 elementor/widgets/blog-posts.php:196
#: elementor/widgets/contact-form.php:317
#: elementor/widgets/contact-form.php:330
#: elementor/widgets/contact-form.php:344
#: elementor/widgets/contact-form.php:357
#: elementor/widgets/inquiry-form.php:304
#: elementor/widgets/inquiry-form.php:317
#: elementor/widgets/inquiry-form.php:331
#: elementor/widgets/inquiry-form.php:344
#: elementor/widgets/listings-tabs.php:158 elementor/widgets/page-title.php:91
#: elementor/widgets/search-builder-old.php:603
#: elementor/widgets/search-builder-old.php:643
#: elementor/widgets/search-builder-old.php:673
#: elementor/widgets/search-builder-old.php:754
#: elementor/widgets/search-builder.php:590
#: elementor/widgets/search-builder.php:630
#: elementor/widgets/search-builder.php:808
#: elementor/widgets/search-builder.php:889
#: elementor/widgets/single-agency/agency-about.php:65
#: elementor/widgets/single-agency/agency-about.php:77
#: elementor/widgets/single-agency/agency-contact.php:55
#: elementor/widgets/single-agency/agency-contact.php:67
#: elementor/widgets/single-agency/agency-contact.php:79
#: elementor/widgets/single-agency/agency-contact.php:91
#: elementor/widgets/single-agency/agency-contact.php:103
#: elementor/widgets/single-agency/agency-contact.php:115
#: elementor/widgets/single-agency/agency-contact.php:127
#: elementor/widgets/single-agency/agency-contact.php:139
#: elementor/widgets/single-agency/agency-contact.php:151
#: elementor/widgets/single-agency/agency-listings-review.php:150
#: elementor/widgets/single-agency/agency-listings-review.php:230
#: elementor/widgets/single-agency/agency-listings.php:150
#: elementor/widgets/single-agency/agency-listings.php:230
#: elementor/widgets/single-agency/agency-profile-v1.php:56
#: elementor/widgets/single-agency/agency-profile-v1.php:68
#: elementor/widgets/single-agency/agency-profile-v1.php:80
#: elementor/widgets/single-agency/agency-profile-v1.php:92
#: elementor/widgets/single-agency/agency-profile-v1.php:104
#: elementor/widgets/single-agency/agency-profile-v2.php:68
#: elementor/widgets/single-agency/agency-rating.php:52
#: elementor/widgets/single-agency/agency-rating.php:64
#: elementor/widgets/single-agency/agency-rating.php:76
#: elementor/widgets/single-agent/agent-about.php:65
#: elementor/widgets/single-agent/agent-about.php:77
#: elementor/widgets/single-agent/agent-contact.php:55
#: elementor/widgets/single-agent/agent-contact.php:67
#: elementor/widgets/single-agent/agent-contact.php:79
#: elementor/widgets/single-agent/agent-contact.php:91
#: elementor/widgets/single-agent/agent-contact.php:103
#: elementor/widgets/single-agent/agent-contact.php:115
#: elementor/widgets/single-agent/agent-contact.php:127
#: elementor/widgets/single-agent/agent-contact.php:139
#: elementor/widgets/single-agent/agent-contact.php:151
#: elementor/widgets/single-agent/agent-listings-review.php:155
#: elementor/widgets/single-agent/agent-listings-review.php:235
#: elementor/widgets/single-agent/agent-listings.php:155
#: elementor/widgets/single-agent/agent-listings.php:235
#: elementor/widgets/single-agent/agent-profile-v1.php:56
#: elementor/widgets/single-agent/agent-profile-v1.php:68
#: elementor/widgets/single-agent/agent-profile-v1.php:80
#: elementor/widgets/single-agent/agent-profile-v1.php:92
#: elementor/widgets/single-agent/agent-profile-v1.php:104
#: elementor/widgets/single-agent/agent-profile-v1.php:116
#: elementor/widgets/single-agent/agent-profile-v2.php:68
#: elementor/widgets/single-agent/agent-profile-v2.php:80
#: elementor/widgets/single-agent/agent-rating.php:52
#: elementor/widgets/single-agent/agent-rating.php:64
#: elementor/widgets/single-agent/agent-rating.php:76
#: elementor/widgets/single-post/author-box.php:63
#: elementor/widgets/single-post/author-box.php:89
#: elementor/widgets/single-post/author-box.php:136
#: elementor/widgets/single-post/author-box.php:150
#: elementor/widgets/single-post/post-navigation.php:52
#: elementor/widgets/single-post/post-navigation.php:99
msgid "Hide"
msgstr ""

#: elementor/widgets/blog-posts-carousel.php:155
#: elementor/widgets/blog-posts-v2.php:140 elementor/widgets/blog-posts.php:181
msgid "Post Date"
msgstr ""

#: elementor/widgets/blog-posts-carousel.php:167
#: elementor/widgets/blog-posts-v2.php:152 elementor/widgets/blog-posts.php:193
msgid "Post Category"
msgstr ""

#: elementor/widgets/blog-posts-carousel.php:182
#: elementor/widgets/custom-carousel.php:152
#: elementor/widgets/property-carousel-v1.php:82
#: elementor/widgets/property-carousel-v2.php:82
#: elementor/widgets/property-carousel-v3.php:81
#: elementor/widgets/property-carousel-v5.php:82
#: elementor/widgets/property-carousel-v6.php:83
#: elementor/widgets/property-carousel-v7.php:82
#: elementor/widgets/taxonomies-cards-carousel.php:136
#: elementor/widgets/taxonomies-grids-carousel.php:121
msgid "Carousel Settings"
msgstr ""

#: elementor/widgets/blog-posts-carousel.php:280
#: elementor/widgets/blog-posts-v2.php:175 elementor/widgets/blog-posts.php:216
msgid "Post Box"
msgstr ""

#: elementor/widgets/blog-posts-carousel.php:307
#: elementor/widgets/single-property/property-title-area.php:83
msgid "Padding Top"
msgstr ""

#: elementor/widgets/blog-posts-carousel.php:353
#: elementor/widgets/blog-posts-v2.php:214 elementor/widgets/blog-posts.php:308
msgid "Image Style"
msgstr ""

#: elementor/widgets/blog-posts-carousel.php:365
#: elementor/widgets/blog-posts.php:320
msgid "Margin left & right"
msgstr ""

#: elementor/widgets/blog-posts-carousel.php:388
#: elementor/widgets/blog-posts-v2.php:240 elementor/widgets/blog-posts.php:343
#: elementor/widgets/single-post/post-navigation.php:96
#: elementor/widgets/single-post/post-title.php:17
#: elementor/widgets/single-post/post-title.php:42
msgid "Post Title"
msgstr ""

#: elementor/widgets/blog-posts-carousel.php:435
#: elementor/widgets/blog-posts-carousel.php:511
#: elementor/widgets/blog-posts-carousel.php:612
#: elementor/widgets/blog-posts-v2.php:196
#: elementor/widgets/blog-posts-v2.php:287
#: elementor/widgets/blog-posts-v2.php:351
#: elementor/widgets/blog-posts-v2.php:439 elementor/widgets/blog-posts.php:284
#: elementor/widgets/blog-posts.php:390 elementor/widgets/blog-posts.php:466
#: elementor/widgets/blog-posts.php:567 elementor/widgets/grid-builder.php:484
#: elementor/widgets/search-builder-old.php:1083
#: elementor/widgets/search-builder.php:1338
#: elementor/widgets/single-agency/agency-listings-review.php:280
#: elementor/widgets/single-agency/agency-profile-v1.php:250
#: elementor/widgets/single-agency/agency-profile-v2.php:184
#: elementor/widgets/single-agent/agent-listings-review.php:285
#: elementor/widgets/single-agent/agent-profile-v1.php:262
#: elementor/widgets/single-agent/agent-profile-v2.php:196
#: elementor/widgets/single-property/property-price.php:135
#: elementor/widgets/single-property/property-price.php:210
#: elementor/widgets/single-property/property-title-area.php:267
#: elementor/widgets/single-property/property-title-area.php:449
#: elementor/widgets/single-property/section-contact-bottom.php:187
#: elementor/widgets/single-property/section-toparea-v1.php:169
#: elementor/widgets/single-property/section-toparea-v1.php:351
#: elementor/widgets/single-property/section-toparea-v2.php:192
#: elementor/widgets/single-property/section-toparea-v2.php:373
#: elementor/widgets/single-property/section-toparea-v3.php:194
#: elementor/widgets/single-property/section-toparea-v3.php:375
#: elementor/widgets/single-property/section-toparea-v5.php:169
#: elementor/widgets/single-property/section-toparea-v5.php:350
#: elementor/widgets/single-property/section-toparea-v6.php:214
#: elementor/widgets/single-property/section-toparea-v6.php:395
#: elementor/widgets/single-property/section-toparea-v7.php:189
#: elementor/widgets/single-property/section-toparea-v7.php:370
msgid "Margin Bottom"
msgstr ""

#: elementor/widgets/blog-posts-carousel.php:452
#: elementor/widgets/blog-posts-v2.php:304 elementor/widgets/blog-posts.php:407
#: elementor/widgets/single-post/post-content.php:16
msgid "Post Content"
msgstr ""

#: elementor/widgets/blog-posts-carousel.php:531
#: elementor/widgets/blog-posts-v2.php:368 elementor/widgets/blog-posts.php:486
msgid "Content Padding"
msgstr ""

#: elementor/widgets/blog-posts-carousel.php:546
#: elementor/widgets/blog-posts-v2.php:380 elementor/widgets/blog-posts.php:501
msgid "Post Meta"
msgstr ""

#: elementor/widgets/blog-posts-carousel.php:568
#: elementor/widgets/blog-posts-v2.php:401 elementor/widgets/blog-posts.php:523
msgid "Category Color"
msgstr ""

#: elementor/widgets/blog-posts-carousel.php:629
#: elementor/widgets/blog-posts.php:584
msgid "Continue Link"
msgstr ""

#: elementor/widgets/blog-posts-carousel.php:671
#: elementor/widgets/blog-posts.php:626
msgid "Footer"
msgstr ""

#: elementor/widgets/blog-posts-carousel.php:695
#: elementor/widgets/blog-posts.php:650
msgid "Author Text Color"
msgstr ""

#: elementor/widgets/blog-posts-v2.php:34
msgid "Blog Posts Grid v2"
msgstr ""

#: elementor/widgets/blog-posts-v2.php:457
msgid "Continue Reading"
msgstr ""

#: elementor/widgets/blog-posts.php:34
msgid "Blog Posts Grid"
msgstr ""

#: elementor/widgets/blog-posts.php:124
msgid "Posts in Row"
msgstr ""

#: elementor/widgets/contact-form.php:43
msgid "Contact Form"
msgstr ""

#: elementor/widgets/contact-form.php:85 elementor/widgets/inquiry-form.php:103
msgid "Full Name"
msgstr ""

#: elementor/widgets/contact-form.php:86 elementor/widgets/inquiry-form.php:104
msgid "First Name"
msgstr ""

#: elementor/widgets/contact-form.php:87 elementor/widgets/inquiry-form.php:105
msgid "Last Name"
msgstr ""

#: elementor/widgets/contact-form.php:91
msgid "Home Phone"
msgstr ""

#: elementor/widgets/contact-form.php:92
msgid "Work Phone"
msgstr ""

#: elementor/widgets/contact-form.php:93 elementor/widgets/inquiry-form.php:108
msgid "User Type"
msgstr ""

#: elementor/widgets/contact-form.php:98 elementor/widgets/inquiry-form.php:101
#: elementor/widgets/single-property/section-address.php:184
msgid "Zip/Postal Code"
msgstr ""

#: elementor/widgets/contact-form.php:130
#: elementor/widgets/inquiry-form.php:141
#: elementor/widgets/search-builder-old.php:152
#: elementor/widgets/search-builder.php:162
msgid "Placeholder"
msgstr ""

#: elementor/widgets/contact-form.php:149
#: elementor/widgets/contact-form.php:381
#: elementor/widgets/inquiry-form.php:159
#: elementor/widgets/inquiry-form.php:366
msgid "Required"
msgstr ""

#: elementor/widgets/contact-form.php:169
#: elementor/widgets/contact-form.php:394
#: elementor/widgets/inquiry-form.php:179
#: elementor/widgets/inquiry-form.php:379
msgid "Validation Message"
msgstr ""

#: elementor/widgets/contact-form.php:181
#: elementor/widgets/inquiry-form.php:191
msgid "Options"
msgstr ""

#: elementor/widgets/contact-form.php:184
#: elementor/widgets/inquiry-form.php:194
msgid ""
"Enter each option in a separate line. To differentiate between label and "
"value, separate them with a pipe char (\"|\"). For example: First Name|f_name"
msgstr ""

#: elementor/widgets/contact-form.php:203
#: elementor/widgets/contact-form.php:455
#: elementor/widgets/inquiry-form.php:213
#: elementor/widgets/inquiry-form.php:440
#: elementor/widgets/search-builder-old.php:232
#: elementor/widgets/search-builder.php:219
msgid "Column Width"
msgstr ""

#: elementor/widgets/contact-form.php:225
#: elementor/widgets/inquiry-form.php:235
msgid "Rows"
msgstr ""

#: elementor/widgets/contact-form.php:247
#: elementor/widgets/inquiry-form.php:257
#: elementor/widgets/search-builder-old.php:457
#: elementor/widgets/search-builder.php:444
msgid "Form Fields"
msgstr ""

#: elementor/widgets/contact-form.php:254
#: elementor/widgets/contact-form.php:257
#: elementor/widgets/inquiry-form.php:264
#: elementor/widgets/inquiry-form.php:267
msgid "Form Name"
msgstr ""

#: elementor/widgets/contact-form.php:256
#: elementor/widgets/inquiry-form.php:266
msgid "New Form"
msgstr ""

#: elementor/widgets/contact-form.php:297
#: elementor/widgets/inquiry-form.php:284
#: elementor/widgets/search-builder-old.php:566
#: elementor/widgets/search-builder.php:553
msgid "Input Size"
msgstr ""

#: elementor/widgets/contact-form.php:327
#: elementor/widgets/inquiry-form.php:314
msgid "Required Mark"
msgstr ""

#: elementor/widgets/contact-form.php:341
#: elementor/widgets/inquiry-form.php:328
msgid "Google reCaptcha"
msgstr ""

#: elementor/widgets/contact-form.php:346
#: elementor/widgets/inquiry-form.php:333
msgid ""
"Please make sure you have enabled google reCaptcha in Theme Options -> "
"Google reCaptcha and have add reCaptcha API keys"
msgstr ""

#: elementor/widgets/contact-form.php:354
#: elementor/widgets/inquiry-form.php:341
msgid "GDPR Agreement"
msgstr ""

#: elementor/widgets/contact-form.php:367
#: elementor/widgets/inquiry-form.php:352
msgid "Hide checkbox"
msgstr ""

#: elementor/widgets/contact-form.php:368
#: elementor/widgets/inquiry-form.php:353
msgid "If you want to hide the checkbox, you can enable this option."
msgstr ""

#: elementor/widgets/contact-form.php:406
#: elementor/widgets/inquiry-form.php:391
msgid "GDPR Agreement Text"
msgstr ""

#: elementor/widgets/contact-form.php:422
#: elementor/widgets/inquiry-form.php:407
#: elementor/widgets/single-agency/agency-contact-form.php:161
#: elementor/widgets/single-agency/agency-review.php:75
#: elementor/widgets/single-agent/agent-contact-form.php:161
#: elementor/widgets/single-agent/agent-review.php:75
#: elementor/widgets/single-property/section-contact-bottom.php:597
#: elementor/widgets/single-property/section-review.php:75
#: elementor/widgets/single-property/section-schedule-tour-v2.php:300
#: elementor/widgets/single-property/section-schedule-tour.php:238
msgid "Submit Button"
msgstr ""

#: elementor/widgets/contact-form.php:505
#: elementor/widgets/inquiry-form.php:490
msgid "Button ID"
msgstr ""

#: elementor/widgets/contact-form.php:508
#: elementor/widgets/inquiry-form.php:493
msgid "Add your custom id WITHOUT the Pound key. e.g: my-id"
msgstr ""

#: elementor/widgets/contact-form.php:510
#: elementor/widgets/inquiry-form.php:495
msgid ""
"Please make sure the ID is unique and not used elsewhere on the page this "
"form is displayed. This field allows <code>A-z 0-9</code> & underscore chars "
"without spaces."
msgstr ""

#: elementor/widgets/contact-form.php:528
#: elementor/widgets/inquiry-form.php:513
#: elementor/widgets/search-builder-old.php:184
#: elementor/widgets/search-builder-old.php:220
#: elementor/widgets/search-builder.php:195
msgid "To"
msgstr ""

#: elementor/widgets/contact-form.php:533
#: elementor/widgets/contact-form.php:558
#: elementor/widgets/contact-form.php:569
#: elementor/widgets/inquiry-form.php:518
#: elementor/widgets/inquiry-form.php:543
#: elementor/widgets/inquiry-form.php:554
msgid "Separate emails with commas"
msgstr ""

#: elementor/widgets/contact-form.php:538
#: elementor/widgets/inquiry-form.php:523
#, php-format
msgid "New message from \"%s\""
msgstr ""

#: elementor/widgets/contact-form.php:543
#: elementor/widgets/inquiry-form.php:528
msgid "Subject"
msgstr ""

#: elementor/widgets/contact-form.php:555
#: elementor/widgets/inquiry-form.php:540
msgid "Cc"
msgstr ""

#: elementor/widgets/contact-form.php:566
#: elementor/widgets/inquiry-form.php:551
msgid "Bcc"
msgstr ""

#: elementor/widgets/contact-form.php:580
#: elementor/widgets/inquiry-form.php:564
msgid "Redirect"
msgstr ""

#: elementor/widgets/contact-form.php:587
#: elementor/widgets/inquiry-form.php:571
msgid "Redirect To"
msgstr ""

#: elementor/widgets/contact-form.php:589
#: elementor/widgets/create-listing-btn.php:208
#: elementor/widgets/header-footer/create-listing-btn.php:240
#: elementor/widgets/header-footer/site-logo.php:516
#: elementor/widgets/inquiry-form.php:573
msgid "https://your-link.com"
msgstr ""

#: elementor/widgets/contact-form.php:603
#: elementor/widgets/contact-form.php:610
#: elementor/widgets/inquiry-form.php:587
#: elementor/widgets/inquiry-form.php:594
msgid "Webhook"
msgstr ""

#: elementor/widgets/contact-form.php:622
#: elementor/widgets/inquiry-form.php:606
msgid "Webhook URL"
msgstr ""

#: elementor/widgets/contact-form.php:624
#: elementor/widgets/inquiry-form.php:608
msgid "https://your-webhook-url.com"
msgstr ""

#: elementor/widgets/contact-form.php:630
#: elementor/widgets/inquiry-form.php:614
msgid ""
"Enter the integration URL (like Zapier) that will receive the form's "
"submitted data."
msgstr ""

#: elementor/widgets/create-listing-btn.php:19
#: elementor/widgets/create-listing-btn.php:40
#: elementor/widgets/header-footer/create-listing-btn.php:21
#: elementor/widgets/header-footer/create-listing-btn.php:56
msgid "Create Listing Button"
msgstr ""

#: elementor/widgets/create-listing-btn.php:66
#: elementor/widgets/header-footer/create-listing-btn.php:82
#: elementor/widgets/listings-tabs.php:235
#: elementor/widgets/search-builder-old.php:1182
#: elementor/widgets/search-builder.php:1437
#: elementor/widgets/search-builder.php:1723
#: elementor/widgets/search-builder.php:1892
#: elementor/widgets/single-agency/agency-listings-review.php:499
#: elementor/widgets/single-agency/agency-listings.php:321
#: elementor/widgets/single-agency/agency-name.php:149
#: elementor/widgets/single-agency/agency-name.php:238
#: elementor/widgets/single-agent/agent-listings-review.php:504
#: elementor/widgets/single-agent/agent-listings.php:326
#: elementor/widgets/single-agent/agent-name.php:149
#: elementor/widgets/single-agent/agent-name.php:238
#: elementor/widgets/single-property/featured-label.php:78
#: elementor/widgets/single-property/item-label.php:78
#: elementor/widgets/single-property/property-address.php:108
#: elementor/widgets/single-property/section-contact-2.php:269
#: elementor/widgets/single-property/status-label.php:79
msgid "Margin"
msgstr ""

#: elementor/widgets/create-listing-btn.php:110
#: elementor/widgets/header-footer/create-listing-btn.php:132
#: elementor/widgets/header-footer/create-listing-btn.php:174
msgid "Button Color"
msgstr ""

#: elementor/widgets/create-listing-btn.php:122
msgid "Button Color Hover"
msgstr ""

#: elementor/widgets/create-listing-btn.php:135
#: elementor/widgets/header-footer/create-listing-btn.php:144
#: elementor/widgets/header-footer/create-listing-btn.php:187
msgid "Button Background Color"
msgstr ""

#: elementor/widgets/create-listing-btn.php:147
msgid "Button Background Color Hover"
msgstr ""

#: elementor/widgets/create-listing-btn.php:159
#: elementor/widgets/header-footer/create-listing-btn.php:156
#: elementor/widgets/header-footer/create-listing-btn.php:199
msgid "Button Border Color"
msgstr ""

#: elementor/widgets/create-listing-btn.php:171
msgid "Button Border Color Hover"
msgstr ""

#: elementor/widgets/create-listing-btn.php:186
#: elementor/widgets/header-footer/create-listing-btn.php:218
msgid "Button Link"
msgstr ""

#: elementor/widgets/create-listing-btn.php:194
#: elementor/widgets/header-footer/create-listing-btn.php:226
#: elementor/widgets/team-member.php:120
msgid "Custom Link"
msgstr ""

#: elementor/widgets/custom-carousel.php:44
msgid "Custom Carousel"
msgstr ""

#: elementor/widgets/custom-carousel.php:93
#: elementor/widgets/custom-carousel.php:123
#: elementor/widgets/custom-carousel.php:377
#: elementor/widgets/testimonials-v2.php:92
#: elementor/widgets/testimonials.php:92
msgid "Slides"
msgstr ""

#: elementor/widgets/custom-carousel.php:110
#: elementor/widgets/custom-carousel.php:346
msgid "Tagline"
msgstr ""

#: elementor/widgets/custom-carousel.php:135
msgid "Nav Position"
msgstr ""

#: elementor/widgets/custom-carousel.php:139
msgid "On Top"
msgstr ""

#: elementor/widgets/custom-carousel.php:140
msgid "Inside"
msgstr ""

#: elementor/widgets/custom-carousel.php:262
msgid "Carousel Title"
msgstr ""

#: elementor/widgets/custom-carousel.php:824
msgid "Read More Button"
msgstr ""

#: elementor/widgets/custom-carousel.php:1013
msgid "Navigation Buttons"
msgstr ""

#: elementor/widgets/custom-carousel.php:1166
#: elementor/widgets/custom-carousel.php:1238
#: elementor/widgets/custom-carousel.php:1247
#: elementor/widgets/custom-carousel.php:1256
msgid "Slide Title"
msgstr ""

#: elementor/widgets/custom-carousel.php:1201
msgid "More Button"
msgstr ""

#: elementor/widgets/custom-carousel.php:1211
#: elementor/widgets/icon-box.php:164
msgid "Read More Text"
msgstr ""

#: elementor/widgets/custom-carousel.php:1222
#: elementor/widgets/icon-box.php:171
#: elementor/widgets/single-property/property-content.php:99
#: elementor/widgets/single-property/section-description.php:272
msgid "Read More Link"
msgstr ""

#: elementor/widgets/custom-carousel.php:1237
#: elementor/widgets/custom-carousel.php:1246
#: elementor/widgets/custom-carousel.php:1255
msgid ""
"Lorem ipsum, dolor sit amet, consectetur adipisicing elit. Debitis, sequi "
"sed tempora qui ad accusantium eius, ab quo adipisci beatae illo, distinctio "
"numquam veritatis autem obcaecati blanditiis consectetur consequatur "
"perspiciatis.."
msgstr ""

#: elementor/widgets/google-map.php:41
msgid "Properties Google Map"
msgstr ""

#: elementor/widgets/google-map.php:93 elementor/widgets/mapbox.php:94
#: elementor/widgets/open-street-map.php:97
msgid "Map Options"
msgstr ""

#: elementor/widgets/google-map.php:101 elementor/widgets/mapbox.php:306
#: elementor/widgets/open-street-map.php:148
#: elementor/widgets/single-property/section-google-map.php:115
#: elementor/widgets/single-property/section-map.php:73
#: elementor/widgets/single-property/section-open-street-map.php:91
msgid "Map Height (px)"
msgstr ""

#: elementor/widgets/google-map.php:136
#: elementor/widgets/open-street-map.php:105
msgid "Default Zoom"
msgstr ""

#: elementor/widgets/google-map.php:142
#: elementor/widgets/open-street-map.php:111
msgid "Set the default zoom level for the map"
msgstr ""

#: elementor/widgets/google-map.php:149
#: elementor/widgets/single-property/section-google-map.php:81
msgid "Map Type"
msgstr ""

#: elementor/widgets/google-map.php:152
msgid "RoadMap"
msgstr ""

#: elementor/widgets/google-map.php:153 elementor/widgets/mapbox.php:110
#: elementor/widgets/single-property/section-google-map.php:86
msgid "Satellite"
msgstr ""

#: elementor/widgets/google-map.php:154
#: elementor/widgets/single-property/section-google-map.php:87
msgid "Hybrid"
msgstr ""

#: elementor/widgets/google-map.php:155
#: elementor/widgets/single-property/section-google-map.php:88
msgid "Terrain"
msgstr ""

#: elementor/widgets/google-map.php:164 elementor/widgets/mapbox.php:163
#: elementor/widgets/open-street-map.php:118
msgid "Hide Zoom Control"
msgstr ""

#: elementor/widgets/google-map.php:179 elementor/widgets/mapbox.php:148
#: elementor/widgets/open-street-map.php:133
msgid "Hide Next/Previous Control"
msgstr ""

#: elementor/widgets/google-map.php:311 elementor/widgets/mapbox.php:295
#: elementor/widgets/open-street-map.php:301
msgid ""
"You can make a property featured by clicking featured properties checkbox "
"while add/edit property"
msgstr ""

#: elementor/widgets/google-map.php:329 elementor/widgets/mapbox.php:350
#: elementor/widgets/open-street-map.php:322 functions/helpers.php:476
#: shortcodes/agents.php:158 shortcodes/blog-posts-carousel.php:113
#: shortcodes/partners.php:34
msgid "Prev"
msgstr ""

#: elementor/widgets/grid-builder.php:37
msgid "Grids Builder"
msgstr ""

#: elementor/widgets/grid-builder.php:88
msgid "Data"
msgstr ""

#: elementor/widgets/grid-builder.php:92 elementor/widgets/grid-builder.php:379
#: elementor/widgets/grid-builder.php:437
#: elementor/widgets/single-post/post-info.php:80
#: elementor/widgets/single-post/post-info.php:97
#: elementor/widgets/single-post/post-info.php:134
#: elementor/widgets/single-post/post-info.php:254
#: elementor/widgets/single-post/post-info.php:300
#: elementor/widgets/single-property/section-overview-v2.php:178
#: elementor/widgets/single-property/section-overview.php:177
#: extensions/favethemes-white-label/template/form.php:94
msgid "Custom"
msgstr ""

#: elementor/widgets/grid-builder.php:93 elementor/widgets/grid-builder.php:378
msgid "Dynamic"
msgstr ""

#: elementor/widgets/grid-builder.php:127
#: elementor/widgets/section-title.php:182
msgid "Subtitle"
msgstr ""

#: elementor/widgets/grid-builder.php:138
#: elementor/widgets/grid-builder.php:326
msgid "More Details Text"
msgstr ""

#: elementor/widgets/grid-builder.php:168 elementor/widgets/grids.php:103
#: elementor/widgets/taxonomies-cards-carousel.php:112
#: elementor/widgets/taxonomies-cards.php:125
#: elementor/widgets/taxonomies-grids-carousel.php:97
#: elementor/widgets/taxonomies-grids.php:120
#: elementor/widgets/taxonomies-list.php:102
msgid "Choose Taxonomy"
msgstr ""

#: elementor/widgets/grid-builder.php:338
msgid "Listing Count"
msgstr ""

#: elementor/widgets/grid-builder.php:353
msgid "Properties Text"
msgstr ""

#: elementor/widgets/grid-builder.php:364
msgid "Property Text"
msgstr ""

#: elementor/widgets/grid-builder.php:391
#: elementor/widgets/header-footer/site-logo.php:427
#: elementor/widgets/single-post/author-box.php:272
#: elementor/widgets/single-property/images-gallery-v2.php:56
#: elementor/widgets/single-property/images-gallery-v4.php:49
#: elementor/widgets/single-property/images-gallery-v5.php:49
#: elementor/widgets/single-property/section-toparea-v3.php:85
#: elementor/widgets/single-property/section-toparea-v6.php:81
#: elementor/widgets/single-property/section-toparea-v7.php:81
#: elementor/widgets/taxonomies-cards-carousel.php:102
#: elementor/widgets/taxonomies-cards.php:101
#: elementor/widgets/taxonomies-grids-carousel.php:87
#: elementor/widgets/taxonomies-grids.php:110
msgid "Image Size"
msgstr ""

#: elementor/widgets/grid-builder.php:423
msgid "Grid Styling"
msgstr ""

#: elementor/widgets/grid-builder.php:431
msgid "Grid Type"
msgstr ""

#: elementor/widgets/grid-builder.php:434
msgid "Landscape"
msgstr ""

#: elementor/widgets/grid-builder.php:435
msgid "Square"
msgstr ""

#: elementor/widgets/grid-builder.php:436
msgid "Portrait"
msgstr ""

#: elementor/widgets/grid-builder.php:540
msgid "Text Padding(px)"
msgstr ""

#: elementor/widgets/grid-builder.php:566 elementor/widgets/grids.php:369
#: elementor/widgets/single-agency/agency-contact.php:360
#: elementor/widgets/single-agent/agent-contact.php:360
#: elementor/widgets/taxonomies-cards-carousel.php:257
#: elementor/widgets/taxonomies-cards.php:162
#: elementor/widgets/taxonomies-grids-carousel.php:242
#: elementor/widgets/taxonomies-grids.php:155
msgid "Title Typography"
msgstr ""

#: elementor/widgets/grid-builder.php:586 elementor/widgets/grids.php:389
msgid "Subtitle Typography"
msgstr ""

#: elementor/widgets/grid-builder.php:617 elementor/widgets/grids.php:420
msgid "Subtitle Color"
msgstr ""

#: elementor/widgets/grids.php:36
msgid "Houzez Grids"
msgstr ""

#: elementor/widgets/grids.php:87
msgid "Choose Grid"
msgstr ""

#: elementor/widgets/grids.php:90
msgid "Grid v1"
msgstr ""

#: elementor/widgets/grids.php:91
msgid "Grid v2"
msgstr ""

#: elementor/widgets/grids.php:92
msgid "Grid v3"
msgstr ""

#: elementor/widgets/grids.php:93
msgid "Grid v4"
msgstr ""

#: elementor/widgets/grids.php:320 elementor/widgets/icon-box.php:307
msgid "Styling"
msgstr ""

#: elementor/widgets/grids.php:328
#: elementor/widgets/taxonomies-grids-carousel.php:283
#: elementor/widgets/taxonomies-grids.php:231
msgid "Grid Gap"
msgstr ""

#: elementor/widgets/grids.php:359
msgid "Title & Subtitle"
msgstr ""

#: elementor/widgets/grids.php:432
msgid "Title Background Color"
msgstr ""

#: elementor/widgets/header-footer/area-switcher.php:43
#: elementor/widgets/header-footer/area-switcher.php:321
msgid "Area Switcher"
msgstr ""

#: elementor/widgets/header-footer/area-switcher.php:122
#: elementor/widgets/header-footer/area-switcher.php:324
msgid "You need enable it under Theme Options -> Top Bar -> Area Switcher"
msgstr ""

#: elementor/widgets/header-footer/area-switcher.php:130
#: elementor/widgets/header-footer/currency.php:130
#: elementor/widgets/header-footer/lang.php:130
#: elementor/widgets/header-footer/menu.php:754
#: elementor/widgets/header-footer/menu.php:951
#: elementor/widgets/header-footer/menu.php:1053
#: elementor/widgets/header-footer/site-logo.php:129
msgid "Vertical Padding"
msgstr ""

#: elementor/widgets/header-footer/area-switcher.php:147
#: elementor/widgets/header-footer/currency.php:147
#: elementor/widgets/header-footer/lang.php:147
#: elementor/widgets/header-footer/menu.php:737
#: elementor/widgets/header-footer/menu.php:933
#: elementor/widgets/header-footer/menu.php:1039
msgid "Horizontal Padding"
msgstr ""

#: elementor/widgets/header-footer/area-switcher.php:230
#: elementor/widgets/header-footer/currency.php:230
msgid "Currency Dropdown"
msgstr ""

#: elementor/widgets/header-footer/area-switcher.php:239
#: elementor/widgets/header-footer/currency.php:239
#: elementor/widgets/header-footer/login-modal.php:515
#: elementor/widgets/login-modal.php:450
msgid "Position from Top"
msgstr ""

#: elementor/widgets/header-footer/area-switcher.php:289
#: elementor/widgets/header-footer/currency.php:289
#: elementor/widgets/header-footer/lang.php:188
msgid "Color :hover"
msgstr ""

#: elementor/widgets/header-footer/currency.php:122
#: elementor/widgets/header-footer/currency.php:345
msgid "You need enable it under Theme Options -> Top Bar -> Currency Switcher"
msgstr ""

#: elementor/widgets/header-footer/currency.php:165
#: elementor/widgets/header-footer/currency.php:342
#: elementor/widgets/search-builder-old.php:99
#: elementor/widgets/search-builder.php:99
msgid "Currency"
msgstr ""

#: elementor/widgets/header-footer/lang.php:43
#: elementor/widgets/single-agency/agency-meta.php:60
#: elementor/widgets/single-agent/agent-meta.php:62
msgid "Language"
msgstr ""

#: elementor/widgets/header-footer/lang.php:122
msgid "You need Polylang or WPML plugin for this to work"
msgstr ""

#: elementor/widgets/header-footer/login-modal.php:19
#: elementor/widgets/header-footer/login-modal.php:54
#: elementor/widgets/login-modal.php:19 elementor/widgets/login-modal.php:41
msgid "Login Modal"
msgstr ""

#: elementor/widgets/header-footer/login-modal.php:63
#: elementor/widgets/login-modal.php:50
msgid "Login, register type"
msgstr ""

#: elementor/widgets/header-footer/login-modal.php:66
#: elementor/widgets/login-modal.php:53
msgid "Show as Icon"
msgstr ""

#: elementor/widgets/header-footer/login-modal.php:67
#: elementor/widgets/login-modal.php:54
msgid "Show as Text"
msgstr ""

#: elementor/widgets/header-footer/login-modal.php:77
#: elementor/widgets/login-modal.php:64
msgid "Show LoggedIn View"
msgstr ""

#: elementor/widgets/header-footer/login-modal.php:83
#: elementor/widgets/header-footer/login-modal.php:97
#: elementor/widgets/login-modal.php:70 elementor/widgets/login-modal.php:84
msgid "Only for design purpose"
msgstr ""

#: elementor/widgets/header-footer/login-modal.php:91
#: elementor/widgets/login-modal.php:78
msgid "Show Drop Down"
msgstr ""

#: elementor/widgets/header-footer/login-modal.php:105
#: elementor/widgets/login-modal.php:92
msgid "Icon Size(px)"
msgstr ""

#: elementor/widgets/header-footer/login-modal.php:163
#: elementor/widgets/login-modal.php:150
msgid "LoggedIn Position"
msgstr ""

#: elementor/widgets/header-footer/login-modal.php:185
msgid "LoggedIn Horizontal Padding"
msgstr ""

#: elementor/widgets/header-footer/login-modal.php:202
msgid "LoggedIn Vertical Padding"
msgstr ""

#: elementor/widgets/header-footer/login-modal.php:234
#: elementor/widgets/login-modal.php:187
msgid "Login Text Padding"
msgstr ""

#: elementor/widgets/header-footer/login-modal.php:249
#: elementor/widgets/login-modal.php:202
msgid "Register Text Padding"
msgstr ""

#: elementor/widgets/header-footer/login-modal.php:279
#: elementor/widgets/login-modal.php:232
msgid "Nav Links"
msgstr ""

#: elementor/widgets/header-footer/login-modal.php:288
#: elementor/widgets/header-footer/login-modal.php:324
#: elementor/widgets/header-footer/login-modal.php:391
#: elementor/widgets/login-modal.php:241 elementor/widgets/login-modal.php:282
msgid "Login/Register Text"
msgstr ""

#: elementor/widgets/header-footer/login-modal.php:337
#: elementor/widgets/header-footer/login-modal.php:404
#: elementor/widgets/login-modal.php:308
msgid "Drop Down Background"
msgstr ""

#: elementor/widgets/header-footer/login-modal.php:349
#: elementor/widgets/header-footer/login-modal.php:416
#: elementor/widgets/login-modal.php:320
msgid "Drop Down border"
msgstr ""

#: elementor/widgets/header-footer/login-modal.php:361
#: elementor/widgets/header-footer/login-modal.php:428
#: elementor/widgets/login-modal.php:332
msgid "User Nav Links"
msgstr ""

#: elementor/widgets/header-footer/login-modal.php:447
#: elementor/widgets/login-modal.php:382
msgid "Sizes & Spacing"
msgstr ""

#: elementor/widgets/header-footer/login-modal.php:455
#: elementor/widgets/login-modal.php:390
msgid "Avatar Border Radius(px)"
msgstr ""

#: elementor/widgets/header-footer/login-modal.php:485
#: elementor/widgets/login-modal.php:420
msgid "Drop Down Size(px)"
msgstr ""

#: elementor/widgets/header-footer/login-modal.php:556
#: elementor/widgets/login-modal.php:491
msgid "Drop Down Box Shadow"
msgstr ""

#: elementor/widgets/header-footer/login-modal.php:580
#: elementor/widgets/header-footer/login-modal.php:615
#: elementor/widgets/login-modal.php:514 elementor/widgets/login-modal.php:549
msgid "Login"
msgstr ""

#: elementor/widgets/header-footer/login-modal.php:586
#: elementor/widgets/header-footer/login-modal.php:622
#: elementor/widgets/login-modal.php:520 elementor/widgets/login-modal.php:554
msgid "Register"
msgstr ""

#: elementor/widgets/header-footer/menu.php:49
msgid "Navigation"
msgstr ""

#: elementor/widgets/header-footer/menu.php:152
#: elementor/widgets/header-footer/menu.php:1361
msgid "Menu"
msgstr ""

#: elementor/widgets/header-footer/menu.php:156
#, php-format
msgid ""
"Go to the <a href=\"%s\" target=\"_blank\">Menus screen</a> to manage your "
"menus."
msgstr ""

#: elementor/widgets/header-footer/menu.php:165
#, php-format
msgid ""
"<strong>There are no menus in your site.</strong><br>Go to the <a "
"href=\"%s\" target=\"_blank\">Menus screen</a> to create one."
msgstr ""

#: elementor/widgets/header-footer/menu.php:174
msgid "layout"
msgstr ""

#: elementor/widgets/header-footer/menu.php:177
msgid "Horizontal"
msgstr ""

#: elementor/widgets/header-footer/menu.php:178
msgid "Vertical"
msgstr ""

#: elementor/widgets/header-footer/menu.php:179
#: elementor/widgets/header-footer/menu.php:773
msgid "Dropdown"
msgstr ""

#: elementor/widgets/header-footer/menu.php:191
msgid "Align"
msgstr ""

#: elementor/widgets/header-footer/menu.php:223
msgid "Pointer"
msgstr ""

#: elementor/widgets/header-footer/menu.php:228
msgid "Underline"
msgstr ""

#: elementor/widgets/header-footer/menu.php:229
msgid "Overline"
msgstr ""

#: elementor/widgets/header-footer/menu.php:230
msgid "Double Line"
msgstr ""

#: elementor/widgets/header-footer/menu.php:231
msgid "Framed"
msgstr ""

#: elementor/widgets/header-footer/menu.php:330
msgid "Submenu Indicator"
msgstr ""

#: elementor/widgets/header-footer/menu.php:334
msgid "Angle"
msgstr ""

#: elementor/widgets/header-footer/menu.php:335
msgid "Caret"
msgstr ""

#: elementor/widgets/header-footer/menu.php:336
msgid "Plus"
msgstr ""

#: elementor/widgets/header-footer/menu.php:349
msgid "Mobile Dropdown"
msgstr ""

#: elementor/widgets/header-footer/menu.php:361
msgid "Breakpoint"
msgstr ""

#: elementor/widgets/header-footer/menu.php:365
msgid "Tablet Portrait & Less"
msgstr ""

#: elementor/widgets/header-footer/menu.php:366
msgid "Mobile Portrait & Less"
msgstr ""

#: elementor/widgets/header-footer/menu.php:378
msgid "Toggle Align"
msgstr ""

#: elementor/widgets/header-footer/menu.php:401
#: elementor/widgets/icon-box.php:112 elementor/widgets/icon-box.php:316
#: elementor/widgets/properties-ajax-tabs.php:254
#: elementor/widgets/properties-ajax-tabs.php:268
#: elementor/widgets/property-meta-data.php:212
#: elementor/widgets/single-agency/agency-call-btn.php:86
#: elementor/widgets/single-agency/agency-contact-btn.php:74
#: elementor/widgets/single-agency/agency-line-btn.php:74
#: elementor/widgets/single-agency/agency-meta.php:374
#: elementor/widgets/single-agency/agency-telegram-btn.php:74
#: elementor/widgets/single-agency/agency-whatsapp-btn.php:74
#: elementor/widgets/single-agent/agent-call-btn.php:86
#: elementor/widgets/single-agent/agent-contact-btn.php:74
#: elementor/widgets/single-agent/agent-line-btn.php:74
#: elementor/widgets/single-agent/agent-meta.php:375
#: elementor/widgets/single-agent/agent-telegram-btn.php:74
#: elementor/widgets/single-agent/agent-whatsapp-btn.php:74
#: elementor/widgets/single-post/post-info.php:295
#: elementor/widgets/single-post/post-info.php:581
#: elementor/widgets/single-property/section-floorplan.php:189
#: elementor/widgets/taxonomies-list.php:328
#: elementor/widgets/taxonomies-list.php:336
msgid "Icon"
msgstr ""

#: elementor/widgets/header-footer/menu.php:443
msgid "Main Menu"
msgstr ""

#: elementor/widgets/header-footer/menu.php:533
#: elementor/widgets/header-footer/menu.php:577
msgid "Pointer Color"
msgstr ""

#: elementor/widgets/header-footer/menu.php:612
#: elementor/widgets/header-footer/menu.php:972
#: elementor/widgets/single-post/post-info.php:428
#: elementor/widgets/taxonomies-list.php:231
msgid "Divider"
msgstr ""

#: elementor/widgets/header-footer/menu.php:614
#: elementor/widgets/single-post/post-info.php:430
#: elementor/widgets/taxonomies-list.php:233
#: extensions/meta-box/inc/fields/switch.php:85
msgid "Off"
msgstr ""

#: elementor/widgets/header-footer/menu.php:615
#: elementor/widgets/single-post/post-info.php:431
#: elementor/widgets/taxonomies-list.php:234
#: extensions/meta-box/inc/fields/switch.php:84
msgid "On"
msgstr ""

#: elementor/widgets/header-footer/menu.php:714
msgid "Pointer Width"
msgstr ""

#: elementor/widgets/header-footer/menu.php:781
msgid ""
"On desktop, this will affect the submenu. On mobile, this will affect the "
"entire menu."
msgstr ""

#: elementor/widgets/header-footer/menu.php:1028
msgid "Toggle Button"
msgstr ""

#: elementor/widgets/header-footer/menu.php:1189
msgid "Offcanvas Header"
msgstr ""

#: elementor/widgets/header-footer/site-logo.php:51
msgid "Site Logo"
msgstr ""

#: elementor/widgets/header-footer/site-logo.php:121
msgid "Site logo"
msgstr ""

#: elementor/widgets/header-footer/site-logo.php:146
#: elementor/widgets/single-post/author-box.php:242
msgid "Top"
msgstr ""

#: elementor/widgets/header-footer/site-logo.php:375
msgid "Logo"
msgstr ""

#: elementor/widgets/header-footer/site-logo.php:382
msgid "Logo Source"
msgstr ""

#: elementor/widgets/header-footer/site-logo.php:385
msgid "Theme Options"
msgstr ""

#: elementor/widgets/header-footer/site-logo.php:386
msgid "Custom Logo"
msgstr ""

#: elementor/widgets/header-footer/site-logo.php:397
#, php-format
msgid ""
"Please select or upload your <strong>Logo</strong> in the <a "
"target=\"_blank\" href=\"%1$s\"><em>Theme Options</em></a>."
msgstr ""

#: elementor/widgets/header-footer/site-logo.php:479
msgid "Custom Caption"
msgstr ""

#: elementor/widgets/header-footer/site-logo.php:482
msgid "Enter caption"
msgstr ""

#: elementor/widgets/icon-box.php:43 elementor/widgets/icon-box.php:179
msgid "Icon Box"
msgstr ""

#: elementor/widgets/icon-box.php:98
#: elementor/widgets/properties-ajax-tabs.php:245
msgid "Icon Type"
msgstr ""

#: elementor/widgets/icon-box.php:128
msgid "Fontawesome Icon"
msgstr ""

#: elementor/widgets/icon-box.php:139
#: elementor/widgets/property-meta-data.php:152
#: elementor/widgets/single-agency/agency-call-btn.php:76
#: elementor/widgets/single-agency/agency-contact-btn.php:64
#: elementor/widgets/single-agency/agency-line-btn.php:64
#: elementor/widgets/single-agency/agency-meta.php:159
#: elementor/widgets/single-agency/agency-telegram-btn.php:64
#: elementor/widgets/single-agency/agency-whatsapp-btn.php:64
#: elementor/widgets/single-agent/agent-call-btn.php:76
#: elementor/widgets/single-agent/agent-contact-btn.php:64
#: elementor/widgets/single-agent/agent-line-btn.php:64
#: elementor/widgets/single-agent/agent-meta.php:161
#: elementor/widgets/single-agent/agent-telegram-btn.php:64
#: elementor/widgets/single-agent/agent-whatsapp-btn.php:64
#: elementor/widgets/single-property/section-overview-v2.php:121
#: elementor/widgets/single-property/section-overview.php:120
msgid "Custom Icon"
msgstr ""

#: elementor/widgets/icon-box.php:191
msgid "Icon Boxes Style"
msgstr ""

#: elementor/widgets/icon-box.php:287
msgid "Background Overlay"
msgstr ""

#: elementor/widgets/icon-box.php:355
msgid "Icon Padding"
msgstr ""

#: elementor/widgets/icon-box.php:375
msgid "Icon Rotate"
msgstr ""

#: elementor/widgets/icon-box.php:439 elementor/widgets/team-member.php:112
msgid "Description"
msgstr ""

#: elementor/widgets/icon-box.php:469
#: elementor/widgets/single-property/property-content.php:163
#: elementor/widgets/single-property/section-description.php:365
msgid "Read More"
msgstr ""

#: elementor/widgets/inquiry-form.php:42
msgid "Inquiry Form"
msgstr ""

#: elementor/widgets/inquiry-form.php:84
msgid "Inquiry Type"
msgstr ""

#: elementor/widgets/inquiry-form.php:87
#: elementor/widgets/property-meta-data.php:50
#: elementor/widgets/single-agency/agency-single-stats.php:70
#: elementor/widgets/single-agent/agent-single-stats.php:70
#: elementor/widgets/single-property/item-label.php:18
#: elementor/widgets/single-property/section-similar.php:83
msgid "Property Label"
msgstr ""

#: elementor/widgets/inquiry-form.php:91
msgid "Minimum Bedrooms"
msgstr ""

#: elementor/widgets/inquiry-form.php:92
msgid "Maximum Bedrooms"
msgstr ""

#: elementor/widgets/inquiry-form.php:93
msgid "Minimum Bathrooms"
msgstr ""

#: elementor/widgets/inquiry-form.php:94
msgid "Maximum Bathrooms"
msgstr ""

#: elementor/widgets/inquiry-form.php:95
msgid "Minimum Area Size"
msgstr ""

#: elementor/widgets/inquiry-form.php:96
msgid "Maximum Area Size"
msgstr ""

#: elementor/widgets/inquiry-form.php:102
msgid "Street Address"
msgstr ""

#: elementor/widgets/inquiry-form.php:652
msgid "Please install and activate Houzez CRM plugin."
msgstr ""

#: elementor/widgets/inquiry-form.php:1171
msgid "Purchase, Rent, Sell, Miss, Evaluation, Mortgage"
msgstr ""

#: elementor/widgets/listings-tabs.php:36
msgid "Listings Tabs"
msgstr ""

#: elementor/widgets/listings-tabs.php:93
#: elementor/widgets/search-builder-old.php:682
#: elementor/widgets/search-builder.php:817
#: elementor/widgets/single-agency/agency-listings-review.php:159
#: elementor/widgets/single-agency/agency-listings.php:159
#: elementor/widgets/single-agent/agent-listings-review.php:164
#: elementor/widgets/single-agent/agent-listings.php:164
msgid "Tab Type"
msgstr ""

#: elementor/widgets/listings-tabs.php:108
#: elementor/widgets/search-builder-old.php:700
#: elementor/widgets/search-builder.php:835
#: elementor/widgets/single-agency/agency-listings-review.php:177
#: elementor/widgets/single-agency/agency-listings.php:177
#: elementor/widgets/single-agent/agent-listings-review.php:182
#: elementor/widgets/single-agent/agent-listings.php:182
msgid "Select Types"
msgstr ""

#: elementor/widgets/listings-tabs.php:123
#: elementor/widgets/search-builder-old.php:717
#: elementor/widgets/search-builder.php:852
#: elementor/widgets/single-agency/agency-listings-review.php:193
#: elementor/widgets/single-agency/agency-listings.php:193
#: elementor/widgets/single-agent/agent-listings-review.php:198
#: elementor/widgets/single-agent/agent-listings.php:198
msgid "Select Statuses"
msgstr ""

#: elementor/widgets/listings-tabs.php:139
#: elementor/widgets/search-builder-old.php:734
#: elementor/widgets/search-builder.php:869
#: elementor/widgets/single-agency/agency-listings-review.php:210
#: elementor/widgets/single-agency/agency-listings.php:210
#: elementor/widgets/single-agent/agent-listings-review.php:215
#: elementor/widgets/single-agent/agent-listings.php:215
msgid "Select Cities"
msgstr ""

#: elementor/widgets/listings-tabs.php:155
#: elementor/widgets/search-builder-old.php:751
#: elementor/widgets/search-builder.php:886
#: elementor/widgets/single-agency/agency-listings-review.php:227
#: elementor/widgets/single-agency/agency-listings.php:227
#: elementor/widgets/single-agent/agent-listings-review.php:232
#: elementor/widgets/single-agent/agent-listings.php:232
msgid "Show All Tab"
msgstr ""

#: elementor/widgets/listings-tabs.php:170
#: elementor/widgets/properties-ajax-tabs.php:99
#: elementor/widgets/search-builder-old.php:1105
#: elementor/widgets/search-builder.php:1360
#: elementor/widgets/single-agency/agency-listings.php:245
#: elementor/widgets/single-agent/agent-listings.php:250
msgid "Tabs Style"
msgstr ""

#: elementor/widgets/listings-tabs.php:178
#: elementor/widgets/search-builder-old.php:1121
#: elementor/widgets/search-builder.php:1376
#: elementor/widgets/single-agency/agency-listings-review.php:442
#: elementor/widgets/single-agency/agency-listings.php:264
#: elementor/widgets/single-agent/agent-listings-review.php:447
#: elementor/widgets/single-agent/agent-listings.php:269
#: elementor/widgets/single-property/section-contact-2.php:197
#: elementor/widgets/single-property/section-contact-2.php:363
#: elementor/widgets/single-property/section-schedule-tour-v2.php:180
msgid "Tabs Color"
msgstr ""

#: elementor/widgets/listings-tabs.php:190
#: elementor/widgets/search-builder-old.php:1133
#: elementor/widgets/search-builder.php:1388
#: elementor/widgets/single-agency/agency-listings-review.php:454
#: elementor/widgets/single-agency/agency-listings.php:276
#: elementor/widgets/single-agent/agent-listings-review.php:459
#: elementor/widgets/single-agent/agent-listings.php:281
#: elementor/widgets/single-property/section-contact-2.php:212
#: elementor/widgets/single-property/section-contact-2.php:378
#: elementor/widgets/single-property/section-schedule-tour-v2.php:192
msgid "Tabs Active Color"
msgstr ""

#: elementor/widgets/listings-tabs.php:202
#: elementor/widgets/search-builder-old.php:1145
#: elementor/widgets/search-builder.php:1400
#: elementor/widgets/single-agency/agency-listings-review.php:466
#: elementor/widgets/single-agency/agency-listings.php:288
#: elementor/widgets/single-agent/agent-listings-review.php:471
#: elementor/widgets/single-agent/agent-listings.php:293
#: elementor/widgets/single-property/section-contact-2.php:227
#: elementor/widgets/single-property/section-contact-2.php:393
#: elementor/widgets/single-property/section-schedule-tour-v2.php:204
msgid "Tabs Background Color"
msgstr ""

#: elementor/widgets/listings-tabs.php:214
#: elementor/widgets/search-builder-old.php:1157
#: elementor/widgets/search-builder.php:1412
#: elementor/widgets/single-agency/agency-listings-review.php:478
#: elementor/widgets/single-agency/agency-listings.php:300
#: elementor/widgets/single-agent/agent-listings-review.php:483
#: elementor/widgets/single-agent/agent-listings.php:305
#: elementor/widgets/single-property/section-contact-2.php:242
#: elementor/widgets/single-property/section-contact-2.php:408
#: elementor/widgets/single-property/section-schedule-tour-v2.php:216
msgid "Active Tabs Background Color"
msgstr ""

#: elementor/widgets/login-modal.php:270
msgid "Icon Color Hover"
msgstr ""

#: elementor/widgets/login-modal.php:295
msgid "Login/Register Text Hover"
msgstr ""

#: elementor/widgets/login-modal.php:344
msgid "Drop Down Hover Background"
msgstr ""

#: elementor/widgets/login-modal.php:356
msgid "Drop Down border hover"
msgstr ""

#: elementor/widgets/login-modal.php:368
msgid "User Nav Links Hover"
msgstr ""

#: elementor/widgets/mapbox.php:42
msgid "Properties Mapbox"
msgstr ""

#: elementor/widgets/mapbox.php:102
msgid "Map Style"
msgstr ""

#: elementor/widgets/mapbox.php:105
msgid "Global (Use Theme Options)"
msgstr ""

#: elementor/widgets/mapbox.php:106
msgid "Streets"
msgstr ""

#: elementor/widgets/mapbox.php:107
msgid "Outdoors"
msgstr ""

#: elementor/widgets/mapbox.php:108
msgid "Light"
msgstr ""

#: elementor/widgets/mapbox.php:109
msgid "Dark"
msgstr ""

#: elementor/widgets/mapbox.php:111
msgid "Satellite Streets"
msgstr ""

#: elementor/widgets/mapbox.php:112 elementor/widgets/mapbox.php:121
msgid "Custom Style URL"
msgstr ""

#: elementor/widgets/mapbox.php:134
#, php-format
msgid ""
"To use a custom Mapbox style URL, select \"Custom Style URL\" above and "
"enter your Mapbox style URL. You can create custom styles in the %1$sMapbox "
"Studio%2$s."
msgstr ""

#: elementor/widgets/mapbox.php:179 elementor/widgets/open-street-map.php:185
msgid "Properties Filters"
msgstr ""

#: elementor/widgets/open-street-map.php:45
msgid "Properties Open Street Map"
msgstr ""

#: elementor/widgets/page-title.php:38
msgid "Page Title"
msgstr ""

#: elementor/widgets/page-title.php:88
#: elementor/widgets/single-property/property-title-area.php:139
#: elementor/widgets/single-property/section-toparea-v1.php:83
#: elementor/widgets/single-property/section-toparea-v2.php:81
#: elementor/widgets/single-property/section-toparea-v3.php:106
#: elementor/widgets/single-property/section-toparea-v5.php:81
#: elementor/widgets/single-property/section-toparea-v6.php:126
#: elementor/widgets/single-property/section-toparea-v7.php:101
msgid "Show Breadcrumb"
msgstr ""

#: elementor/widgets/price-table.php:37
msgid "Price Table"
msgstr ""

#: elementor/widgets/price-table.php:95
msgid "Select Package"
msgstr ""

#: elementor/widgets/price-table.php:106
#: elementor/widgets/single-property/section-overview-v2.php:174
#: elementor/widgets/single-property/section-overview.php:173
msgid "Data Type"
msgstr ""

#: elementor/widgets/price-table.php:120
msgid "Popular?"
msgstr ""

#: elementor/widgets/price-table.php:139
msgid "Package Name"
msgstr ""

#: elementor/widgets/price-table.php:151
msgid "Package Price"
msgstr ""

#: elementor/widgets/price-table.php:163
msgid "Package Currency"
msgstr ""

#: elementor/widgets/price-table.php:193
#: elementor/widgets/search-builder.php:699
msgid "Button Text"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:47
msgid "Properties Ajax Tabs"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:92
msgid "General"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:103
msgid "Tabs v1"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:104
msgid "Tabs v2"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:105
msgid "Tabs v3"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:106
msgid "Tabs v4"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:107
msgid "Tabs v5"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:140
msgid "Tab Spacer"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:159
msgid "Bottom Spacer"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:178
msgid "Enable Icon"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:188
#: elementor/widgets/single-agency/agency-call-btn.php:103
#: elementor/widgets/single-agency/agency-contact-btn.php:91
#: elementor/widgets/single-agency/agency-line-btn.php:91
#: elementor/widgets/single-agency/agency-telegram-btn.php:91
#: elementor/widgets/single-agency/agency-whatsapp-btn.php:91
#: elementor/widgets/single-agent/agent-call-btn.php:103
#: elementor/widgets/single-agent/agent-contact-btn.php:91
#: elementor/widgets/single-agent/agent-line-btn.php:91
#: elementor/widgets/single-agent/agent-telegram-btn.php:91
#: elementor/widgets/single-agent/agent-whatsapp-btn.php:91
msgid "Icon Position"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:193
#: elementor/widgets/single-post/author-box.php:177
msgid "Above"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:194
msgid "Before title"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:203
msgid "Show title"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:218
#: elementor/widgets/properties-ajax-tabs.php:678
#: elementor/widgets/search-builder-old.php:662
#: elementor/widgets/search-builder.php:797
#: elementor/widgets/single-property/section-floorplan-v2.php:179
msgid "Tabs"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:231
msgid "Tab"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:237
#: elementor/widgets/properties-ajax-tabs.php:239
msgid "Tab Title"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:297
msgid "Query"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:556
msgid "Cards Style"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:574
#: elementor/widgets/properties-ajax-tabs.php:583
#: elementor/widgets/property-by-id.php:118
#: elementor/widgets/property-by-id.php:127
#: elementor/widgets/property-by-ids.php:116
#: elementor/widgets/property-by-ids.php:125
#: elementor/widgets/single-agency/agency-listings-review.php:83
#: elementor/widgets/single-agency/agency-listings-review.php:92
#: elementor/widgets/single-agency/agency-listings.php:83
#: elementor/widgets/single-agency/agency-listings.php:92
#: elementor/widgets/single-agent/agent-listings-review.php:88
#: elementor/widgets/single-agent/agent-listings-review.php:97
#: elementor/widgets/single-agent/agent-listings.php:88
#: elementor/widgets/single-agent/agent-listings.php:97
#: elementor/widgets/single-property/section-similar.php:121
#: elementor/widgets/single-property/section-similar.php:130
msgid "only for hack"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:597
#: elementor/widgets/properties-recent-viewed.php:110
#: elementor/widgets/property-by-ids.php:147
#: elementor/widgets/single-property/section-address.php:83
#: elementor/widgets/single-property/section-details.php:84
#: elementor/widgets/single-property/section-details.php:178
#: elementor/widgets/single-property/section-features.php:82
msgid "2 Columns"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:656
#: elementor/widgets/single-agency/agency-listings-review.php:135
#: elementor/widgets/single-agency/agency-listings.php:135
#: elementor/widgets/single-agent/agent-listings-review.php:140
#: elementor/widgets/single-agent/agent-listings.php:140
#: functions/functions.php:224 functions/helpers.php:302
#: functions/helpers.php:363 shortcodes/properties.php:117
msgid "Load More"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:697
msgid "Title Color Hover"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:708
msgid "Active tab border color"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:722
msgid "Active tab border after color"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:765
#: elementor/widgets/properties-recent-viewed.php:384
#: elementor/widgets/property-by-id.php:163
#: elementor/widgets/property-by-ids.php:177
#: elementor/widgets/property-cards-v1.php:336
#: elementor/widgets/property-cards-v2.php:312
#: elementor/widgets/property-cards-v3.php:219
#: elementor/widgets/property-cards-v4.php:331
#: elementor/widgets/property-cards-v5.php:227
#: elementor/widgets/property-cards-v6.php:214
#: elementor/widgets/property-cards-v7.php:326
#: elementor/widgets/property-cards-v8.php:298
#: elementor/widgets/property-carousel-v1.php:311
#: elementor/widgets/property-carousel-v2.php:288
#: elementor/widgets/property-carousel-v3.php:195
#: elementor/widgets/property-carousel-v5.php:204
#: elementor/widgets/property-carousel-v6.php:186
#: elementor/widgets/property-carousel-v7.php:296
#: elementor/widgets/single-agency/agency-listings-review.php:577
#: elementor/widgets/single-agency/agency-listings.php:377
#: elementor/widgets/single-agent/agent-listings-review.php:582
#: elementor/widgets/single-agent/agent-listings.php:379
#: elementor/widgets/single-property/section-similar.php:340
msgid "Spaces & Sizes"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:773
#: elementor/widgets/properties-recent-viewed.php:392
#: elementor/widgets/property-by-id.php:171
#: elementor/widgets/property-by-ids.php:185
#: elementor/widgets/property-cards-v1.php:344
#: elementor/widgets/property-cards-v2.php:320
#: elementor/widgets/property-cards-v3.php:227
#: elementor/widgets/property-cards-v4.php:339
#: elementor/widgets/property-cards-v5.php:235
#: elementor/widgets/property-cards-v6.php:222
#: elementor/widgets/property-cards-v7.php:334
#: elementor/widgets/property-cards-v8.php:306
#: elementor/widgets/property-carousel-v1.php:319
#: elementor/widgets/property-carousel-v2.php:296
#: elementor/widgets/property-carousel-v3.php:203
#: elementor/widgets/property-carousel-v5.php:212
#: elementor/widgets/property-carousel-v6.php:194
#: elementor/widgets/property-carousel-v7.php:304
#: elementor/widgets/single-agency/agency-listings-review.php:585
#: elementor/widgets/single-agency/agency-listings.php:385
#: elementor/widgets/single-agent/agent-listings-review.php:590
#: elementor/widgets/single-agent/agent-listings.php:387
#: elementor/widgets/single-property/section-similar.php:348
msgid "Title Margin Bottom(px)"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:803
msgid "Labels Margin Bottom(px)"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:824
#: elementor/widgets/properties-recent-viewed.php:422
#: elementor/widgets/property-by-id.php:201
#: elementor/widgets/property-by-ids.php:215
#: elementor/widgets/property-cards-v1.php:374
#: elementor/widgets/property-cards-v2.php:350
#: elementor/widgets/property-cards-v4.php:369
#: elementor/widgets/property-cards-v7.php:364
#: elementor/widgets/property-cards-v8.php:336
#: elementor/widgets/property-carousel-v1.php:349
#: elementor/widgets/property-carousel-v2.php:326
#: elementor/widgets/property-carousel-v7.php:334
#: elementor/widgets/single-agency/agency-listings-review.php:615
#: elementor/widgets/single-agency/agency-listings.php:415
#: elementor/widgets/single-agent/agent-listings-review.php:620
#: elementor/widgets/single-agent/agent-listings.php:417
#: elementor/widgets/single-property/section-similar.php:378
msgid "Address Margin Bottom(px)"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:854
#: elementor/widgets/property-cards-v1.php:404
#: elementor/widgets/property-cards-v2.php:380
#: elementor/widgets/property-cards-v4.php:399
#: elementor/widgets/property-cards-v8.php:366
#: elementor/widgets/property-carousel-v1.php:379
#: elementor/widgets/property-carousel-v2.php:356
msgid "Excerpt Margin Bottom(px)"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:887
#: elementor/widgets/properties-recent-viewed.php:452
#: elementor/widgets/property-by-id.php:231
#: elementor/widgets/property-by-ids.php:245
#: elementor/widgets/property-cards-v1.php:437
#: elementor/widgets/property-cards-v2.php:413
#: elementor/widgets/property-cards-v4.php:432
#: elementor/widgets/property-cards-v5.php:295
#: elementor/widgets/property-cards-v6.php:253
#: elementor/widgets/property-cards-v7.php:394
#: elementor/widgets/property-cards-v8.php:399
#: elementor/widgets/property-carousel-v1.php:412
#: elementor/widgets/property-carousel-v2.php:389
#: elementor/widgets/property-carousel-v5.php:272
#: elementor/widgets/property-carousel-v6.php:225
#: elementor/widgets/property-carousel-v7.php:364
#: elementor/widgets/single-agency/agency-listings-review.php:645
#: elementor/widgets/single-agency/agency-listings.php:445
#: elementor/widgets/single-agent/agent-listings-review.php:650
#: elementor/widgets/single-agent/agent-listings.php:447
#: elementor/widgets/single-property/section-similar.php:408
msgid "Meta Icons Size(px)"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:917
#: elementor/widgets/properties-recent-viewed.php:482
#: elementor/widgets/property-by-id.php:261
#: elementor/widgets/property-by-ids.php:275
#: elementor/widgets/property-cards-v1.php:467
#: elementor/widgets/property-cards-v2.php:443
#: elementor/widgets/property-cards-v4.php:462
#: elementor/widgets/property-cards-v5.php:325
#: elementor/widgets/property-cards-v6.php:283
#: elementor/widgets/property-cards-v7.php:424
#: elementor/widgets/property-cards-v8.php:429
#: elementor/widgets/property-carousel-v1.php:442
#: elementor/widgets/property-carousel-v2.php:419
#: elementor/widgets/property-carousel-v5.php:302
#: elementor/widgets/property-carousel-v6.php:255
#: elementor/widgets/property-carousel-v7.php:394
#: elementor/widgets/single-agency/agency-listings-review.php:675
#: elementor/widgets/single-agency/agency-listings.php:475
#: elementor/widgets/single-agent/agent-listings-review.php:680
#: elementor/widgets/single-agent/agent-listings.php:477
#: elementor/widgets/single-property/section-similar.php:438
msgid "Content Area Padding"
msgstr ""

#: elementor/widgets/properties-grids.php:39
msgid "Property Grids"
msgstr ""

#: elementor/widgets/properties-grids.php:90
#: elementor/widgets/properties-recent-viewed.php:88
#: elementor/widgets/property-by-id.php:88
#: elementor/widgets/property-by-ids.php:88
msgid "Grid Style"
msgstr ""

#: elementor/widgets/properties-grids.php:112
#: elementor/widgets/properties.php:150
#: elementor/widgets/property-cards-v1.php:142
#: elementor/widgets/property-cards-v2.php:139
#: elementor/widgets/property-cards-v3.php:138
#: elementor/widgets/property-cards-v4.php:138
#: elementor/widgets/property-cards-v5.php:138
#: elementor/widgets/property-cards-v6.php:142
#: elementor/widgets/property-cards-v7.php:141
#: elementor/widgets/property-cards-v8.php:124
#: elementor/widgets/property-carousel-v1.php:99
#: elementor/widgets/property-carousel-v2.php:96
#: elementor/widgets/property-carousel-v3.php:95
#: elementor/widgets/property-carousel-v5.php:96
#: elementor/widgets/property-carousel-v6.php:97
#: elementor/widgets/property-carousel-v7.php:99
msgid "Filters"
msgstr ""

#: elementor/widgets/properties-grids.php:124
msgid "Settings"
msgstr ""

#: elementor/widgets/properties-grids.php:132
msgid "Hide Tools"
msgstr ""

#: elementor/widgets/properties-recent-viewed.php:38
msgid "Recently Viewed Properties"
msgstr ""

#: elementor/widgets/properties-recent-viewed.php:91
#: elementor/widgets/property-by-id.php:91
#: elementor/widgets/property-by-ids.php:91
msgid "Property Card v1"
msgstr ""

#: elementor/widgets/properties-recent-viewed.php:92
#: elementor/widgets/property-by-id.php:92
#: elementor/widgets/property-by-ids.php:92
msgid "Property Card v2"
msgstr ""

#: elementor/widgets/properties-recent-viewed.php:93
#: elementor/widgets/property-by-id.php:93
#: elementor/widgets/property-by-ids.php:93
msgid "Property Card v3"
msgstr ""

#: elementor/widgets/properties-recent-viewed.php:94
#: elementor/widgets/property-by-id.php:95
#: elementor/widgets/property-by-ids.php:94
msgid "Property Card v5"
msgstr ""

#: elementor/widgets/properties-recent-viewed.php:95
#: elementor/widgets/property-by-id.php:96
#: elementor/widgets/property-by-ids.php:95
msgid "Property Card v6"
msgstr ""

#: elementor/widgets/properties-recent-viewed.php:96
#: elementor/widgets/property-by-id.php:97
#: elementor/widgets/property-by-ids.php:96
msgid "Property Card v7"
msgstr ""

#: elementor/widgets/properties-recent-viewed.php:98
#: elementor/widgets/property-by-id.php:100
#: elementor/widgets/property-by-ids.php:99
msgid "Choose grid style, default will be propety card v1"
msgstr ""

#: elementor/widgets/properties-recent-viewed.php:119
msgid "Number of Properties"
msgstr ""

#: elementor/widgets/properties-recent-viewed.php:528
#: elementor/widgets/property-cards-v8.php:475
msgid "Grid Background"
msgstr ""

#: elementor/widgets/properties-recent-viewed.php:578
msgid "Item Tools Background Color Hover"
msgstr ""

#: elementor/widgets/properties-recent-viewed.php:602
msgid "Item Tools Color Hover"
msgstr ""

#: elementor/widgets/properties-recent-viewed.php:689
msgid "Details Button Background Color"
msgstr ""

#: elementor/widgets/properties-recent-viewed.php:701
msgid "Details Button Background Color Hover"
msgstr ""

#: elementor/widgets/properties-recent-viewed.php:713
msgid "Detail Button Color"
msgstr ""

#: elementor/widgets/properties-recent-viewed.php:725
msgid "Detail Button Color Hover"
msgstr ""

#: elementor/widgets/properties-slider.php:37
msgid "Properties Slider"
msgstr ""

#: elementor/widgets/properties.php:95
msgid ""
"<span style=\"color: red; font-weight: bold;\">DEPRECATED:</span> This "
"widget is deprecated. Please use Property Cards V1 or Property Cards V2 "
"instead."
msgstr ""

#: elementor/widgets/properties.php:103
msgid "Grid/List Style"
msgstr ""

#: elementor/widgets/properties.php:109
msgid "Choose grid/list style, default will be version 1"
msgstr ""

#: elementor/widgets/properties.php:120
#: elementor/widgets/testimonials-v2.php:91
#: elementor/widgets/testimonials.php:91
msgid "Grid 3 Columns"
msgstr ""

#: elementor/widgets/properties.php:121
#: elementor/widgets/testimonials-v2.php:90
msgid "Grid 2 Columns"
msgstr ""

#: elementor/widgets/properties.php:122
#: elementor/widgets/property-cards-v1.php:97
#: elementor/widgets/property-cards-v2.php:95
#: elementor/widgets/property-cards-v7.php:96
msgid "List View"
msgstr ""

#: elementor/widgets/property-by-id.php:38
msgid "Property by ID"
msgstr ""

#: elementor/widgets/property-by-id.php:94
msgid "Property Card v4"
msgstr ""

#: elementor/widgets/property-by-id.php:98
#: elementor/widgets/property-by-ids.php:97
msgid "Property Card v8"
msgstr ""

#: elementor/widgets/property-by-id.php:138
msgid "Enter property ID. Ex 305"
msgstr ""

#: elementor/widgets/property-by-ids.php:38
msgid "Property by IDs"
msgstr ""

#: elementor/widgets/property-by-ids.php:149
msgid "Note: Property card v4 will show only 2 columns"
msgstr ""

#: elementor/widgets/property-cards-v1.php:40
msgid "Property Cards v1"
msgstr ""

#: elementor/widgets/property-cards-v1.php:94
#: elementor/widgets/property-cards-v2.php:93
#: elementor/widgets/property-cards-v3.php:93
#: elementor/widgets/property-cards-v4.php:94
#: elementor/widgets/property-cards-v5.php:93
#: elementor/widgets/property-cards-v6.php:95
#: elementor/widgets/property-cards-v7.php:94
#: elementor/widgets/single-agency/agency-listings-review.php:105
#: elementor/widgets/single-agency/agency-listings.php:105
#: elementor/widgets/single-agent/agent-listings-review.php:110
#: elementor/widgets/single-agent/agent-listings.php:110
msgid "Grid View 3 Columns"
msgstr ""

#: elementor/widgets/property-cards-v1.php:95
#: elementor/widgets/property-cards-v6.php:96
#: elementor/widgets/property-cards-v7.php:95
#: elementor/widgets/single-agency/agency-listings-review.php:106
#: elementor/widgets/single-agency/agency-listings.php:106
#: elementor/widgets/single-agent/agent-listings-review.php:111
#: elementor/widgets/single-agent/agent-listings.php:111
msgid "Grid View 4 Columns"
msgstr ""

#: elementor/widgets/property-cards-v1.php:96
#: elementor/widgets/property-cards-v2.php:94
#: elementor/widgets/property-cards-v3.php:94
#: elementor/widgets/property-cards-v4.php:93
#: elementor/widgets/property-cards-v5.php:94
#: elementor/widgets/property-cards-v6.php:94
#: elementor/widgets/property-cards-v7.php:93
#: elementor/widgets/single-agency/agency-listings-review.php:104
#: elementor/widgets/single-agency/agency-listings.php:104
#: elementor/widgets/single-agent/agent-listings-review.php:109
#: elementor/widgets/single-agent/agent-listings.php:109
msgid "Grid View 2 Columns"
msgstr ""

#: elementor/widgets/property-cards-v1.php:125
#: elementor/widgets/property-cards-v2.php:123
#: elementor/widgets/property-cards-v3.php:122
#: elementor/widgets/property-cards-v4.php:122
#: elementor/widgets/property-cards-v5.php:122
#: elementor/widgets/property-cards-v6.php:126
#: elementor/widgets/property-cards-v7.php:124
#: elementor/widgets/property-cards-v8.php:108
msgid "Warning"
msgstr ""

#: elementor/widgets/property-cards-v1.php:126
#: elementor/widgets/property-cards-v2.php:124
#: elementor/widgets/property-cards-v3.php:123
#: elementor/widgets/property-cards-v4.php:123
#: elementor/widgets/property-cards-v5.php:123
#: elementor/widgets/property-cards-v6.php:127
#: elementor/widgets/property-cards-v7.php:125
#: elementor/widgets/property-cards-v8.php:109
msgid ""
"Infinite Scroll works with only one widget per page; enabling it for "
"multiple widgets on the same page will cause issues."
msgstr ""

#: elementor/widgets/property-cards-v1.php:168
#: elementor/widgets/property-cards-v2.php:164
#: elementor/widgets/property-cards-v4.php:163
#: elementor/widgets/property-cards-v8.php:148
#: elementor/widgets/property-carousel-v1.php:143
#: elementor/widgets/property-carousel-v2.php:140
msgid "Hide Excerpt"
msgstr ""

#: elementor/widgets/property-cards-v1.php:516
msgid "Box Footer Border Color"
msgstr ""

#: elementor/widgets/property-cards-v2.php:40
msgid "Property Cards v2"
msgstr ""

#: elementor/widgets/property-cards-v3.php:39
msgid "Property Cards v3"
msgstr ""

#: elementor/widgets/property-cards-v4.php:39
msgid "Property Cards v4"
msgstr ""

#: elementor/widgets/property-cards-v4.php:511
#: elementor/widgets/property-carousel-v1.php:491
msgid "Bos Footer Border Color"
msgstr ""

#: elementor/widgets/property-cards-v5.php:39
msgid "Property Cards v5"
msgstr ""

#: elementor/widgets/property-cards-v5.php:265
#: elementor/widgets/property-carousel-v5.php:242
msgid "Price Margin Bottom(px)"
msgstr ""

#: elementor/widgets/property-cards-v6.php:40
msgid "Property Cards v6"
msgstr ""

#: elementor/widgets/property-cards-v7.php:39
msgid "Property Cards v7"
msgstr ""

#: elementor/widgets/property-cards-v7.php:167
#: elementor/widgets/property-carousel-v7.php:143
msgid "Hide Date"
msgstr ""

#: elementor/widgets/property-cards-v7.php:185
#: elementor/widgets/property-cards-v8.php:165
#: elementor/widgets/property-carousel-v7.php:161
msgid "Hide Author"
msgstr ""

#: elementor/widgets/property-cards-v8.php:39
msgid "Property Cards v8"
msgstr ""

#: elementor/widgets/property-cards-v8.php:487
msgid "Grid Footer Background"
msgstr ""

#: elementor/widgets/property-cards-v8.php:624
#: elementor/widgets/single-property/item-tools.php:110
#: elementor/widgets/single-property/property-title-area.php:581
#: elementor/widgets/single-property/section-toparea-v1.php:483
#: elementor/widgets/single-property/section-toparea-v2.php:505
#: elementor/widgets/single-property/section-toparea-v3.php:507
#: elementor/widgets/single-property/section-toparea-v5.php:482
#: elementor/widgets/single-property/section-toparea-v6.php:527
#: elementor/widgets/single-property/section-toparea-v7.php:502
msgid "Background Color Hover"
msgstr ""

#: elementor/widgets/property-carousel-v1.php:39
msgid "Property Cards Carousel v1"
msgstr ""

#: elementor/widgets/property-carousel-v1.php:111
#: elementor/widgets/property-carousel-v2.php:108
#: elementor/widgets/property-carousel-v3.php:107
#: elementor/widgets/property-carousel-v5.php:108
#: elementor/widgets/property-carousel-v6.php:109
#: elementor/widgets/property-carousel-v7.php:111
msgid "All - Button Text"
msgstr ""

#: elementor/widgets/property-carousel-v1.php:119
#: elementor/widgets/property-carousel-v2.php:116
#: elementor/widgets/property-carousel-v3.php:115
#: elementor/widgets/property-carousel-v5.php:116
#: elementor/widgets/property-carousel-v6.php:117
#: elementor/widgets/property-carousel-v7.php:119
msgid "All - Button URL"
msgstr ""

#: elementor/widgets/property-carousel-v2.php:40
msgid "Property Cards Carousel v2"
msgstr ""

#: elementor/widgets/property-carousel-v3.php:39
msgid "Property Cards Carousel v3"
msgstr ""

#: elementor/widgets/property-carousel-v5.php:39
msgid "Property Cards Carousel v5"
msgstr ""

#: elementor/widgets/property-carousel-v6.php:39
msgid "Property Cards Carousel v6"
msgstr ""

#: elementor/widgets/property-carousel-v7.php:39
msgid "Property Cards Carousel v7"
msgstr ""

#: elementor/widgets/property-meta-data.php:16
msgid "Property Meta Data"
msgstr ""

#: elementor/widgets/property-meta-data.php:41
#: elementor/widgets/search-builder-old.php:85
#: elementor/widgets/search-builder.php:85
#: elementor/widgets/single-property/section-details.php:276
#: elementor/widgets/single-property/section-overview.php:50
msgid "Rooms"
msgstr ""

#: elementor/widgets/property-meta-data.php:42
#: elementor/widgets/search-builder-old.php:84
#: elementor/widgets/search-builder-old.php:499
#: elementor/widgets/search-builder-old.php:500
#: elementor/widgets/search-builder.php:84
#: elementor/widgets/search-builder.php:486
#: elementor/widgets/search-builder.php:487
#: elementor/widgets/single-property/section-details.php:259
#: elementor/widgets/single-property/section-overview-v2.php:51
#: elementor/widgets/single-property/section-overview-v2.php:201
#: elementor/widgets/single-property/section-overview.php:49
#: elementor/widgets/single-property/section-overview.php:200
#: statistics/houzez-statistics.php:494
msgid "Bedrooms"
msgstr ""

#: elementor/widgets/property-meta-data.php:43
#: elementor/widgets/search-builder-old.php:86
#: elementor/widgets/search-builder-old.php:507
#: elementor/widgets/search-builder-old.php:508
#: elementor/widgets/search-builder.php:86
#: elementor/widgets/search-builder.php:494
#: elementor/widgets/search-builder.php:495
#: elementor/widgets/single-property/section-details.php:293
#: elementor/widgets/single-property/section-overview-v2.php:52
#: elementor/widgets/single-property/section-overview-v2.php:208
#: elementor/widgets/single-property/section-overview.php:51
#: elementor/widgets/single-property/section-overview.php:207
#: statistics/houzez-statistics.php:499
msgid "Bathrooms"
msgstr ""

#: elementor/widgets/property-meta-data.php:44
#: elementor/widgets/single-property/section-overview-v2.php:53
#: elementor/widgets/single-property/section-overview-v2.php:221
#: elementor/widgets/single-property/section-overview.php:52
#: elementor/widgets/single-property/section-overview.php:220
msgid "Area Size"
msgstr ""

#: elementor/widgets/property-meta-data.php:45
#: elementor/widgets/single-property/section-details.php:242
#: elementor/widgets/single-property/section-overview-v2.php:54
#: elementor/widgets/single-property/section-overview.php:53
msgid "Land Area"
msgstr ""

#: elementor/widgets/property-meta-data.php:46
#: elementor/widgets/single-property/section-details.php:310
#: elementor/widgets/single-property/section-overview-v2.php:55
#: elementor/widgets/single-property/section-overview-v2.php:215
#: elementor/widgets/single-property/section-overview.php:54
#: elementor/widgets/single-property/section-overview.php:214
msgid "Garages"
msgstr ""

#: elementor/widgets/property-meta-data.php:47
#: elementor/widgets/single-property/section-overview-v2.php:56
#: elementor/widgets/single-property/section-overview.php:55
msgid "Built Year"
msgstr ""

#: elementor/widgets/property-meta-data.php:69
msgid "Property Meta"
msgstr ""

#: elementor/widgets/property-meta-data.php:77
msgid "Meta Layout"
msgstr ""

#: elementor/widgets/property-meta-data.php:80
msgid "Layout v1"
msgstr ""

#: elementor/widgets/property-meta-data.php:81
msgid "Layout v2"
msgstr ""

#: elementor/widgets/property-meta-data.php:82
msgid "Layout v3"
msgstr ""

#: elementor/widgets/property-meta-data.php:83
msgid "Layout v4"
msgstr ""

#: elementor/widgets/property-meta-data.php:92
msgid "Select Field"
msgstr ""

#: elementor/widgets/property-meta-data.php:123
#: elementor/widgets/single-property/section-overview-v2.php:92
#: elementor/widgets/single-property/section-overview.php:91
msgid "Label Plural"
msgstr ""

#: elementor/widgets/property-meta-data.php:148
#: elementor/widgets/single-property/section-overview-v2.php:117
#: elementor/widgets/single-property/section-overview.php:116
msgid "Icons From"
msgstr ""

#: elementor/widgets/property-meta-data.php:151
#: elementor/widgets/single-property/section-overview-v2.php:120
#: elementor/widgets/single-property/section-overview.php:119
msgid "Theme Options "
msgstr ""

#: elementor/widgets/property-meta-data.php:153
#: elementor/widgets/single-agency/agency-call-btn.php:77
#: elementor/widgets/single-agency/agency-contact-btn.php:65
#: elementor/widgets/single-agency/agency-line-btn.php:65
#: elementor/widgets/single-agency/agency-telegram-btn.php:65
#: elementor/widgets/single-agency/agency-whatsapp-btn.php:65
#: elementor/widgets/single-agent/agent-call-btn.php:77
#: elementor/widgets/single-agent/agent-contact-btn.php:65
#: elementor/widgets/single-agent/agent-line-btn.php:65
#: elementor/widgets/single-agent/agent-telegram-btn.php:65
#: elementor/widgets/single-agent/agent-whatsapp-btn.php:65
#: elementor/widgets/single-property/section-overview-v2.php:122
#: elementor/widgets/single-property/section-overview.php:121
msgid "No Icon"
msgstr ""

#: elementor/widgets/property-meta-data.php:165
#: elementor/widgets/single-agency/agency-meta.php:195
#: elementor/widgets/single-agent/agent-meta.php:197
#: elementor/widgets/single-property/section-overview-v2.php:131
#: elementor/widgets/single-property/section-overview.php:130
msgid "upload Icon"
msgstr ""

#: elementor/widgets/property-meta-data.php:263
#: elementor/widgets/property-meta-data.php:291
#: elementor/widgets/property-meta-data.php:351
#: elementor/widgets/single-post/author-box.php:304
#: elementor/widgets/single-post/author-box.php:535
#: elementor/widgets/single-post/author-box.php:622
#: elementor/widgets/single-property/section-block-gallery.php:89
#: elementor/widgets/taxonomies-list.php:468
msgid "Gap"
msgstr ""

#: elementor/widgets/property-meta-data.php:283
#: elementor/widgets/single-agency/agency-meta.php:320
#: elementor/widgets/single-agent/agent-meta.php:321
msgid "Meta Value"
msgstr ""

#: elementor/widgets/search-builder-old.php:37
#: elementor/widgets/search-builder.php:37
msgid "Search Builder"
msgstr ""

#: elementor/widgets/search-builder-old.php:80
#: elementor/widgets/search-builder.php:80
msgid "Keyword"
msgstr ""

#: elementor/widgets/search-builder-old.php:92
#: elementor/widgets/search-builder.php:92
msgid "Min Area"
msgstr ""

#: elementor/widgets/search-builder-old.php:93
#: elementor/widgets/search-builder.php:93
msgid "Max Area"
msgstr ""

#: elementor/widgets/search-builder-old.php:94
#: elementor/widgets/search-builder.php:94
msgid "Min Land Area"
msgstr ""

#: elementor/widgets/search-builder-old.php:95
#: elementor/widgets/search-builder.php:95
msgid "Max Land Area"
msgstr ""

#: elementor/widgets/search-builder-old.php:96
#: elementor/widgets/search-builder.php:96 statistics/houzez-statistics.php:578
msgid "Min Price"
msgstr ""

#: elementor/widgets/search-builder-old.php:97
#: elementor/widgets/search-builder.php:97 statistics/houzez-statistics.php:579
msgid "Max Price"
msgstr ""

#: elementor/widgets/search-builder-old.php:98
#: elementor/widgets/search-builder.php:98
msgid "Price Range Slider"
msgstr ""

#: elementor/widgets/search-builder-old.php:101
#: elementor/widgets/search-builder.php:101
#: elementor/widgets/single-property/section-details.php:302
#: elementor/widgets/single-property/section-overview-v2.php:214
#: elementor/widgets/single-property/section-overview.php:213
msgid "Garage"
msgstr ""

#: elementor/widgets/search-builder-old.php:102
#: elementor/widgets/search-builder.php:102
#: elementor/widgets/single-property/section-details.php:328
#: elementor/widgets/single-property/section-overview-v2.php:228
#: elementor/widgets/single-property/section-overview.php:227
msgid "Year Built"
msgstr ""

#: elementor/widgets/search-builder-old.php:103
#: elementor/widgets/search-builder.php:103
msgid "Geo Location"
msgstr ""

#: elementor/widgets/search-builder-old.php:114
#: elementor/widgets/search-builder-old.php:554
#: elementor/widgets/search-builder.php:115
#: elementor/widgets/search-builder.php:541
msgid "Search Button"
msgstr ""

#: elementor/widgets/search-builder-old.php:122
msgid "Search button settings are below under \"Search Button\" section"
msgstr ""

#: elementor/widgets/search-builder-old.php:172
#: elementor/widgets/search-builder-old.php:208
#: elementor/widgets/search-builder.php:183
msgid "From"
msgstr ""

#: elementor/widgets/search-builder-old.php:196
#: elementor/widgets/search-builder.php:207
msgid "Default Radius"
msgstr ""

#: elementor/widgets/search-builder-old.php:279
#: elementor/widgets/search-builder.php:266
#: templates/fields-builder/fields-form.php:121
msgid "Field Type"
msgstr ""

#: elementor/widgets/search-builder-old.php:284
#: elementor/widgets/search-builder.php:271
msgid "Input"
msgstr ""

#: elementor/widgets/search-builder-old.php:304
#: elementor/widgets/search-builder.php:291
msgid "Data Live Search"
msgstr ""

#: elementor/widgets/search-builder-old.php:334
msgid "Slider Width(%)"
msgstr ""

#: elementor/widgets/search-builder-old.php:357
#: elementor/widgets/search-builder.php:344
msgid "Multi Selection"
msgstr ""

#: elementor/widgets/search-builder-old.php:383
#: elementor/widgets/search-builder.php:370
msgid "Select/Deselect All"
msgstr ""

#: elementor/widgets/search-builder-old.php:396
#: elementor/widgets/search-builder.php:383
msgid "Selected Items Text"
msgstr ""

#: elementor/widgets/search-builder-old.php:408
#: elementor/widgets/search-builder.php:395
msgid "Responsive"
msgstr ""

#: elementor/widgets/search-builder-old.php:419
#: elementor/widgets/search-builder.php:406
msgid ""
"Responsive visibility will take effect only on preview or live page, and not "
"while editing in Elementor."
msgstr ""

#: elementor/widgets/search-builder-old.php:427
#: elementor/widgets/search-builder.php:414
msgid "Hide On Desktop"
msgstr ""

#: elementor/widgets/search-builder-old.php:437
#: elementor/widgets/search-builder.php:424
msgid "Hide On Tablet"
msgstr ""

#: elementor/widgets/search-builder-old.php:447
#: elementor/widgets/search-builder.php:434
msgid "Hide On Mobile"
msgstr ""

#: elementor/widgets/search-builder-old.php:515
#: elementor/widgets/search-builder-old.php:516
#: elementor/widgets/search-builder.php:502
#: elementor/widgets/search-builder.php:503
msgid "Min. Price"
msgstr ""

#: elementor/widgets/search-builder-old.php:523
#: elementor/widgets/search-builder-old.php:524
#: elementor/widgets/search-builder.php:510
#: elementor/widgets/search-builder.php:511
msgid "Max. Price"
msgstr ""

#: elementor/widgets/search-builder-old.php:531
#: elementor/widgets/search-builder-old.php:532
#: elementor/widgets/search-builder.php:518
#: elementor/widgets/search-builder.php:519
msgid "Min. Area"
msgstr ""

#: elementor/widgets/search-builder-old.php:539
#: elementor/widgets/search-builder-old.php:540
#: elementor/widgets/search-builder.php:526
#: elementor/widgets/search-builder.php:527
msgid "Max. Area"
msgstr ""

#: elementor/widgets/search-builder-old.php:583
#: elementor/widgets/search-builder.php:570
msgid "Button Size"
msgstr ""

#: elementor/widgets/search-builder-old.php:600
#: elementor/widgets/search-builder.php:587
#: elementor/widgets/single-agency/agency-call-btn.php:72
#: elementor/widgets/single-agency/agency-contact-btn.php:60
#: elementor/widgets/single-agency/agency-line-btn.php:60
#: elementor/widgets/single-agency/agency-telegram-btn.php:60
#: elementor/widgets/single-agency/agency-whatsapp-btn.php:60
#: elementor/widgets/single-agent/agent-call-btn.php:72
#: elementor/widgets/single-agent/agent-contact-btn.php:60
#: elementor/widgets/single-agent/agent-line-btn.php:60
#: elementor/widgets/single-agent/agent-telegram-btn.php:60
#: elementor/widgets/single-agent/agent-whatsapp-btn.php:60
msgid "Button Icon"
msgstr ""

#: elementor/widgets/search-builder-old.php:612
#: elementor/widgets/search-builder.php:599
msgid "Button Alignment"
msgstr ""

#: elementor/widgets/search-builder-old.php:670
#: elementor/widgets/search-builder.php:805
msgid "Show Tabs"
msgstr ""

#: elementor/widgets/search-builder-old.php:763
#: elementor/widgets/search-builder.php:898
msgid "Show All Text"
msgstr ""

#: elementor/widgets/search-builder-old.php:944
#: elementor/widgets/search-builder.php:1196
#: elementor/widgets/single-agency/agency-contact-form.php:149
#: elementor/widgets/single-agency/agency-review.php:51
#: elementor/widgets/single-agency/agency-search.php:196
#: elementor/widgets/single-agent/agent-contact-form.php:149
#: elementor/widgets/single-agent/agent-review.php:51
#: elementor/widgets/single-agent/agent-search.php:196
#: elementor/widgets/single-property/section-contact-2.php:588
#: elementor/widgets/single-property/section-contact-bottom.php:468
#: elementor/widgets/single-property/section-review.php:51
#: elementor/widgets/single-property/section-schedule-tour-v2.php:288
#: elementor/widgets/single-property/section-schedule-tour.php:132
msgid "Fields"
msgstr ""

#: elementor/widgets/search-builder-old.php:1035
#: elementor/widgets/search-builder.php:1290
msgid "Price Slider"
msgstr ""

#: elementor/widgets/search-builder.php:114
msgid "Clear Search Button"
msgstr ""

#: elementor/widgets/search-builder.php:123
msgid ""
"Search and Clear button settings are below under \"Search Button\" section"
msgstr ""

#: elementor/widgets/search-builder.php:641
msgid "Advanced Fields Collapse"
msgstr ""

#: elementor/widgets/search-builder.php:651
msgid ""
"Organize your search form by collapsing some fields into an expandable "
"\"Advanced\" section. This helps keep the main form clean while providing "
"access to additional search options."
msgstr ""

#: elementor/widgets/search-builder.php:659
msgid "Enable Collapse Fields"
msgstr ""

#: elementor/widgets/search-builder.php:665
msgid ""
"When enabled, fields after the specified number will be moved to a "
"collapsible \"Advanced\" section."
msgstr ""

#: elementor/widgets/search-builder.php:672
msgid "Collapse Fields After"
msgstr ""

#: elementor/widgets/search-builder.php:677
msgid ""
"Number of fields to show in the main form. Remaining fields will be moved to "
"the collapsible section."
msgstr ""

#: elementor/widgets/search-builder.php:687
msgid "Collapse Button Settings"
msgstr ""

#: elementor/widgets/search-builder.php:702
msgid "Text displayed on the button that toggles the collapsed fields."
msgstr ""

#: elementor/widgets/search-builder.php:712
msgid "Advanced Button Width"
msgstr ""

#: elementor/widgets/search-builder.php:742
msgid ""
"Set the width of the Advanced button that toggles the collapsed fields. "
"Responsive settings available."
msgstr ""

#: elementor/widgets/search-builder.php:752
msgid "Advanced Button Size"
msgstr ""

#: elementor/widgets/search-builder.php:762
msgid "Set the size of the Advanced button that toggles the collapsed fields."
msgstr ""

#: elementor/widgets/search-builder.php:772
msgid "Show Collapsed Fields Initially"
msgstr ""

#: elementor/widgets/search-builder.php:778
msgid ""
"When enabled, the collapsed fields section will be visible (expanded) by "
"default when the page loads."
msgstr ""

#: elementor/widgets/search-builder.php:1626
msgid "Advanced Button"
msgstr ""

#: elementor/widgets/search-builder.php:1798
msgid "Clear Button"
msgstr ""

#: elementor/widgets/search-builder.php:2322
msgid "Clear"
msgstr ""

#: elementor/widgets/section-title.php:96
msgid "Sub Title"
msgstr ""

#: elementor/widgets/section-title.php:135
msgid "Section Subtitle "
msgstr ""

#: elementor/widgets/section-title.php:171
msgid "Main Title"
msgstr ""

#: elementor/widgets/section-title.php:195
msgid "Main Title Margin Bottom"
msgstr ""

#: elementor/widgets/section-title.php:225
msgid "Subtitle Margin Bottom"
msgstr ""

#: elementor/widgets/section-title.php:255
msgid "Section Margin Bottom"
msgstr ""

#: elementor/widgets/sidebar/code-banner.php:16
#: elementor/widgets/sidebar/code-banner.php:37
#: elementor/widgets/sidebar/code-banner.php:65
msgid "Code Banner"
msgstr ""

#: elementor/widgets/sidebar/code-banner.php:45
msgid "Ads Code"
msgstr ""

#: elementor/widgets/sidebar/code-banner.php:54
msgid "JS or Google AdSense Code"
msgstr ""

#: elementor/widgets/sidebar/code-banner.php:56
msgid "Paste your code here..."
msgstr ""

#: elementor/widgets/single-agency/agency-about.php:19
msgid "Section Agency Bio"
msgstr ""

#: elementor/widgets/single-agency/agency-about.php:180
#: elementor/widgets/single-agency/agency-contact.php:244
#: elementor/widgets/single-agency/agency-search.php:166
#: elementor/widgets/single-agency/agency-stats.php:145
#: elementor/widgets/single-agent/agent-about.php:180
#: elementor/widgets/single-agent/agent-contact.php:244
#: elementor/widgets/single-agent/agent-search.php:166
#: elementor/widgets/single-agent/agent-stats.php:145
msgid "Margin Button"
msgstr ""

#: elementor/widgets/single-agency/agency-address.php:16
#: elementor/widgets/single-agency/agency-address.php:41
msgid "Agency Address"
msgstr ""

#: elementor/widgets/single-agency/agency-agents.php:19
msgid "Agency Agents"
msgstr ""

#: elementor/widgets/single-agency/agency-call-btn.php:17
msgid "Agency Call Button"
msgstr ""

#: elementor/widgets/single-agency/agency-call-btn.php:42
#: elementor/widgets/single-agency/agency-call-btn.php:165
#: elementor/widgets/single-agency/agency-profile-v1.php:410
#: elementor/widgets/single-agent/agent-call-btn.php:42
#: elementor/widgets/single-agent/agent-call-btn.php:165
#: elementor/widgets/single-agent/agent-profile-v1.php:422
#: elementor/widgets/single-property/section-contact-2.php:612
msgid "Call Button"
msgstr ""

#: elementor/widgets/single-agency/agency-call-btn.php:50
#: elementor/widgets/single-agency/agency-contact-btn.php:50
#: elementor/widgets/single-agency/agency-line-btn.php:50
#: elementor/widgets/single-agency/agency-telegram-btn.php:50
#: elementor/widgets/single-agency/agency-whatsapp-btn.php:50
#: elementor/widgets/single-agent/agent-call-btn.php:50
#: elementor/widgets/single-agent/agent-contact-btn.php:50
#: elementor/widgets/single-agent/agent-line-btn.php:50
#: elementor/widgets/single-agent/agent-telegram-btn.php:50
#: elementor/widgets/single-agent/agent-whatsapp-btn.php:50
msgid "label"
msgstr ""

#: elementor/widgets/single-agency/agency-call-btn.php:53
#: elementor/widgets/single-agency/agency-line-btn.php:53
#: elementor/widgets/single-agency/agency-telegram-btn.php:53
#: elementor/widgets/single-agency/agency-whatsapp-btn.php:53
#: elementor/widgets/single-agent/agent-call-btn.php:53
#: elementor/widgets/single-agent/agent-line-btn.php:53
#: elementor/widgets/single-agent/agent-telegram-btn.php:53
#: elementor/widgets/single-agent/agent-whatsapp-btn.php:53
msgid "Add label"
msgstr ""

#: elementor/widgets/single-agency/agency-call-btn.php:60
#: elementor/widgets/single-agent/agent-call-btn.php:60
msgid "Show Number as Text"
msgstr ""

#: elementor/widgets/single-agency/agency-call-btn.php:134
#: elementor/widgets/single-agency/agency-contact-btn.php:122
#: elementor/widgets/single-agency/agency-line-btn.php:122
#: elementor/widgets/single-agency/agency-telegram-btn.php:122
#: elementor/widgets/single-agency/agency-whatsapp-btn.php:122
#: elementor/widgets/single-agent/agent-call-btn.php:134
#: elementor/widgets/single-agent/agent-contact-btn.php:122
#: elementor/widgets/single-agent/agent-line-btn.php:122
#: elementor/widgets/single-agent/agent-telegram-btn.php:122
#: elementor/widgets/single-agent/agent-whatsapp-btn.php:122
msgid "Icon Spacing"
msgstr ""

#: elementor/widgets/single-agency/agency-contact-btn.php:17
msgid "Agency Contact Button"
msgstr ""

#: elementor/widgets/single-agency/agency-contact-btn.php:42
#: elementor/widgets/single-agency/agency-contact-btn.php:153
#: elementor/widgets/single-agent/agent-contact-btn.php:42
#: elementor/widgets/single-agent/agent-contact-btn.php:153
msgid "Contact Button"
msgstr ""

#: elementor/widgets/single-agency/agency-contact-btn.php:53
#: elementor/widgets/single-agent/agent-contact-btn.php:53
msgid "Add title"
msgstr ""

#: elementor/widgets/single-agency/agency-contact-form.php:21
msgid "Agency Contact Form"
msgstr ""

#: elementor/widgets/single-agency/agency-contact-form.php:55
msgid "Agency Detail"
msgstr ""

#: elementor/widgets/single-agency/agency-contact-form.php:122
#: elementor/widgets/single-agent/agent-contact-form.php:55
#: elementor/widgets/single-agent/agent-contact-form.php:122
#: elementor/widgets/single-property/section-contact-2.php:498
#: elementor/widgets/single-property/section-contact-bottom.php:365
msgid "Agent Detail"
msgstr ""

#: elementor/widgets/single-agency/agency-contact-form.php:173
#: elementor/widgets/single-agent/agent-contact-form.php:173
#: elementor/widgets/single-property/section-contact-2.php:636
#: elementor/widgets/single-property/section-contact-bottom.php:609
#: elementor/widgets/single-property/section-schedule-tour-v2.php:312
#: elementor/widgets/single-property/section-schedule-tour.php:250
msgid "Terms of use"
msgstr ""

#: elementor/widgets/single-agency/agency-contact.php:19
msgid "Agency Contact"
msgstr ""

#: elementor/widgets/single-agency/agency-contact.php:52
#: elementor/widgets/single-agent/agent-contact.php:52
#: elementor/widgets/single-property/property-title-area.php:204
#: elementor/widgets/single-property/section-toparea-v1.php:106
#: elementor/widgets/single-property/section-toparea-v2.php:129
#: elementor/widgets/single-property/section-toparea-v3.php:131
#: elementor/widgets/single-property/section-toparea-v5.php:106
#: elementor/widgets/single-property/section-toparea-v6.php:151
#: elementor/widgets/single-property/section-toparea-v7.php:126
msgid "Show Title"
msgstr ""

#: elementor/widgets/single-agency/agency-contact.php:64
#: elementor/widgets/single-agent/agent-contact.php:64
msgid "Map"
msgstr ""

#: elementor/widgets/single-agency/agency-contact.php:112
#: elementor/widgets/single-agent/agent-contact.php:112
msgid "Fax"
msgstr ""

#: elementor/widgets/single-agency/agency-contact.php:136
#: elementor/widgets/single-agency/agency-meta.php:62
#: elementor/widgets/single-agent/agent-contact.php:136
#: elementor/widgets/single-agent/agent-meta.php:64
#: elementor/widgets/single-post/author-box.php:122
msgid "Website"
msgstr ""

#: elementor/widgets/single-agency/agency-contact.php:148
#: elementor/widgets/single-agent/agent-contact.php:148
msgid "Social Links"
msgstr ""

#: elementor/widgets/single-agency/agency-contact.php:340
#: elementor/widgets/single-agent/agent-contact.php:340
msgid "Contact Info"
msgstr ""

#: elementor/widgets/single-agency/agency-contact.php:369
#: elementor/widgets/single-agent/agent-contact.php:369
msgid "Values Typography"
msgstr ""

#: elementor/widgets/single-agency/agency-contact.php:378
#: elementor/widgets/single-agent/agent-contact.php:378
#: elementor/widgets/single-property/section-address.php:92
#: elementor/widgets/single-property/section-details.php:93
#: elementor/widgets/single-property/section-details.php:187
#: elementor/widgets/single-property/section-energy.php:319
msgid "Line Height"
msgstr ""

#: elementor/widgets/single-agency/agency-contact.php:406
#: elementor/widgets/single-agent/agent-contact.php:406
msgid "Social"
msgstr ""

#: elementor/widgets/single-agency/agency-contact.php:452
#: elementor/widgets/single-agent/agent-contact.php:451
msgid "Contact"
msgstr ""

#: elementor/widgets/single-agency/agency-contact.php:470
#: elementor/widgets/single-agency/agency-map.php:76
#: elementor/widgets/single-agent/agent-contact.php:469
#: elementor/widgets/single-agent/agent-map.php:76
#: elementor/widgets/single-property/section-google-map.php:177
#: elementor/widgets/single-property/section-map.php:139
#: elementor/widgets/single-property/section-open-street-map.php:153
msgid "Map will show here on frontend"
msgstr ""

#: elementor/widgets/single-agency/agency-content.php:16
msgid "Agency Content"
msgstr ""

#: elementor/widgets/single-agency/agency-excerpt.php:16
msgid "Agency Excerpt"
msgstr ""

#: elementor/widgets/single-agency/agency-image.php:17
#: elementor/widgets/single-agency/agency-image.php:42
msgid "Agency Picture"
msgstr ""

#: elementor/widgets/single-agency/agency-line-btn.php:17
msgid "Agency LINE Button"
msgstr ""

#: elementor/widgets/single-agency/agency-line-btn.php:42
#: elementor/widgets/single-agency/agency-line-btn.php:153
#: elementor/widgets/single-agent/agent-line-btn.php:42
#: elementor/widgets/single-agent/agent-line-btn.php:153
msgid "Line Button"
msgstr ""

#: elementor/widgets/single-agency/agency-listings-review.php:21
msgid "Agency Listings & Reviews Tabs"
msgstr ""

#: elementor/widgets/single-agency/agency-listings-review.php:60
#: elementor/widgets/single-agency/agency-listings.php:60
#: elementor/widgets/single-agent/agent-listings-review.php:65
#: elementor/widgets/single-agent/agent-listings.php:65
#: elementor/widgets/single-property/section-similar.php:99
msgid "Listings Layout"
msgstr ""

#: elementor/widgets/single-agency/agency-listings-review.php:147
#: elementor/widgets/single-agency/agency-listings.php:147
#: elementor/widgets/single-agent/agent-listings-review.php:152
#: elementor/widgets/single-agent/agent-listings.php:152
msgid "Show Listing Tabs"
msgstr ""

#: elementor/widgets/single-agency/agency-listings-review.php:244
#: elementor/widgets/single-agent/agent-listings-review.php:249
msgid "Section Tabs"
msgstr ""

#: elementor/widgets/single-agency/agency-listings-review.php:434
#: elementor/widgets/single-agent/agent-listings-review.php:439
msgid "Listing Tabs"
msgstr ""

#: elementor/widgets/single-agency/agency-listings-review.php:543
#: elementor/widgets/single-agent/agent-listings-review.php:548
msgid "Tabs Typography"
msgstr ""

#: elementor/widgets/single-agency/agency-listings.php:21
msgid "Agency Listings"
msgstr ""

#: elementor/widgets/single-agency/agency-map.php:16
msgid "Agency Location Map"
msgstr ""

#: elementor/widgets/single-agency/agency-meta.php:16
#: elementor/widgets/single-agency/agency-meta.php:41
msgid "Agency Meta"
msgstr ""

#: elementor/widgets/single-agency/agency-meta.php:49
#: elementor/widgets/single-agent/agent-meta.php:49
msgid "Meta Field"
msgstr ""

#: elementor/widgets/single-agency/agency-meta.php:52
#: elementor/widgets/single-agent/agent-meta.php:52
msgid "Email Address"
msgstr ""

#: elementor/widgets/single-agency/agency-meta.php:53
#: elementor/widgets/single-agency/agency-profile-v1.php:89
#: elementor/widgets/single-agent/agent-meta.php:53
#: elementor/widgets/single-agent/agent-profile-v1.php:101
msgid "Service Areas"
msgstr ""

#: elementor/widgets/single-agency/agency-meta.php:54
#: elementor/widgets/single-agency/agency-profile-v1.php:101
#: elementor/widgets/single-agent/agent-meta.php:54
#: elementor/widgets/single-agent/agent-profile-v1.php:113
msgid "Specialties"
msgstr ""

#: elementor/widgets/single-agency/agency-meta.php:56
#: elementor/widgets/single-agency/agency-profile-v1.php:77
#: elementor/widgets/single-agent/agent-meta.php:58
#: elementor/widgets/single-agent/agent-profile-v1.php:89
msgid "Tax Number"
msgstr ""

#: elementor/widgets/single-agency/agency-meta.php:57
#: elementor/widgets/single-agent/agent-meta.php:59
msgid "Mobile Number"
msgstr ""

#: elementor/widgets/single-agency/agency-meta.php:58
msgid "Phone Number"
msgstr ""

#: elementor/widgets/single-agency/agency-meta.php:59
#: elementor/widgets/single-agent/agent-meta.php:61
msgid "Fax Number"
msgstr ""

#: elementor/widgets/single-agency/agency-meta.php:64
#: elementor/widgets/single-agent/agent-meta.php:66
msgid "LINE ID"
msgstr ""

#: elementor/widgets/single-agency/agency-meta.php:66
#: elementor/widgets/single-agent/agent-meta.php:68
msgid "Zillow"
msgstr ""

#: elementor/widgets/single-agency/agency-meta.php:67
#: elementor/widgets/single-agent/agent-meta.php:69
msgid "Realtor.com"
msgstr ""

#: elementor/widgets/single-agency/agency-meta.php:68
#: elementor/widgets/single-agent/agent-meta.php:70
msgid "Facebook"
msgstr ""

#: elementor/widgets/single-agency/agency-meta.php:69
#: elementor/widgets/single-agent/agent-meta.php:71
msgid "X"
msgstr ""

#: elementor/widgets/single-agency/agency-meta.php:70
#: elementor/widgets/single-agent/agent-meta.php:72
msgid "LinkedIn"
msgstr ""

#: elementor/widgets/single-agency/agency-meta.php:71
#: elementor/widgets/single-agent/agent-meta.php:73
msgid "Google"
msgstr ""

#: elementor/widgets/single-agency/agency-meta.php:72
#: elementor/widgets/single-agent/agent-meta.php:74
msgid "Tiktok"
msgstr ""

#: elementor/widgets/single-agency/agency-meta.php:73
#: elementor/widgets/single-agent/agent-meta.php:75
msgid "Instagram"
msgstr ""

#: elementor/widgets/single-agency/agency-meta.php:74
#: elementor/widgets/single-agent/agent-meta.php:76
msgid "Pinterest"
msgstr ""

#: elementor/widgets/single-agency/agency-meta.php:75
#: elementor/widgets/single-agent/agent-meta.php:77
msgid "Youtube"
msgstr ""

#: elementor/widgets/single-agency/agency-meta.php:76
#: elementor/widgets/single-agent/agent-meta.php:78
msgid "Vimeo"
msgstr ""

#: elementor/widgets/single-agency/agency-meta.php:77
#: elementor/widgets/single-agent/agent-meta.php:79
msgid "Shortcode"
msgstr ""

#: elementor/widgets/single-agency/agency-meta.php:86
#: elementor/widgets/single-agency/agency-meta.php:266
#: elementor/widgets/single-agent/agent-meta.php:88
#: elementor/widgets/single-agent/agent-meta.php:267
msgid "Meta Title"
msgstr ""

#: elementor/widgets/single-agency/agency-meta.php:120
#: elementor/widgets/single-agency/agency-name.php:50
#: elementor/widgets/single-agent/agent-meta.php:122
#: elementor/widgets/single-agent/agent-name.php:50
#: elementor/widgets/single-post/author-box.php:99
#: elementor/widgets/single-post/post-title.php:50
#: elementor/widgets/single-property/property-title.php:50
msgid "HTML Tag"
msgstr ""

#: elementor/widgets/single-agency/agency-name.php:17
#: elementor/widgets/single-agency/agency-name.php:42
msgid "Agency Name"
msgstr ""

#: elementor/widgets/single-agency/agency-name.php:96
msgid "Agency Name Typography"
msgstr ""

#: elementor/widgets/single-agency/agency-name.php:113
msgid "Agency Name Color"
msgstr ""

#: elementor/widgets/single-agency/agency-name.php:134
msgid "Agency Name Hover Color"
msgstr ""

#: elementor/widgets/single-agency/agency-profile-v1.php:19
msgid "Section Agency Profile v1"
msgstr ""

#: elementor/widgets/single-agency/agency-profile-v1.php:113
#: elementor/widgets/single-agent/agent-profile-v1.php:125
#: elementor/widgets/single-property/section-energy.php:282
msgid "Hide Border"
msgstr ""

#: elementor/widgets/single-agency/agency-profile-v1.php:278
#: elementor/widgets/single-agent/agent-profile-v1.php:290
#: elementor/widgets/single-property/section-contact-2.php:600
msgid "Send Email Button"
msgstr ""

#: elementor/widgets/single-agency/agency-profile-v2.php:19
msgid "Section Agency Profile v2"
msgstr ""

#: elementor/widgets/single-agency/agency-profile-v2.php:212
#: elementor/widgets/single-agent/agent-profile-v2.php:224
msgid "Ask a Question & Phone Number"
msgstr ""

#: elementor/widgets/single-agency/agency-rating.php:16
#: elementor/widgets/single-agency/agency-rating.php:41
#: elementor/widgets/single-agency/agency-rating.php:100
msgid "Agency Rating"
msgstr ""

#: elementor/widgets/single-agency/agency-rating.php:49
#: elementor/widgets/single-agent/agent-rating.php:49
msgid "Rating Count"
msgstr ""

#: elementor/widgets/single-agency/agency-rating.php:61
#: elementor/widgets/single-agent/agent-rating.php:61
msgid "Rating Stars"
msgstr ""

#: elementor/widgets/single-agency/agency-rating.php:73
#: elementor/widgets/single-agent/agent-rating.php:73
msgid "See All Review"
msgstr ""

#: elementor/widgets/single-agency/agency-rating.php:85
#: elementor/widgets/single-agent/agent-rating.php:85
msgid "See All Review Text"
msgstr ""

#: elementor/widgets/single-agency/agency-rating.php:108
#: elementor/widgets/single-agent/agent-rating.php:108
msgid "Stars Size"
msgstr ""

#: elementor/widgets/single-agency/agency-rating.php:162
#: elementor/widgets/single-agent/agent-rating.php:162
msgid "Rating Count Margin Right"
msgstr ""

#: elementor/widgets/single-agency/agency-rating.php:187
#: elementor/widgets/single-agent/agent-rating.php:187
msgid "Rating Count Typography"
msgstr ""

#: elementor/widgets/single-agency/agency-rating.php:196
#: elementor/widgets/single-agent/agent-rating.php:196
msgid "Review Text Typography"
msgstr ""

#: elementor/widgets/single-agency/agency-rating.php:204
#: elementor/widgets/single-agent/agent-rating.php:204
msgid "Rating Count Color"
msgstr ""

#: elementor/widgets/single-agency/agency-rating.php:215
#: elementor/widgets/single-agent/agent-rating.php:215
msgid "Review Text Color"
msgstr ""

#: elementor/widgets/single-agency/agency-rating.php:226
#: elementor/widgets/single-agent/agent-rating.php:226
msgid "Review Text Color Hover"
msgstr ""

#: elementor/widgets/single-agency/agency-review.php:21
msgid "Agency Reviews"
msgstr ""

#: elementor/widgets/single-agency/agency-review.php:87
#: elementor/widgets/single-agent/agent-review.php:87
#: elementor/widgets/single-property/section-review.php:87
msgid "Leave a Review Button"
msgstr ""

#: elementor/widgets/single-agency/agency-search.php:19
#: elementor/widgets/single-agency/agency-search.php:45
msgid "Agency Search"
msgstr ""

#: elementor/widgets/single-agency/agency-single-stats.php:19
msgid "Agency Taxonomy Stats"
msgstr ""

#: elementor/widgets/single-agency/agency-single-stats.php:45
#: elementor/widgets/single-agency/agency-stats.php:45
msgid "Agency Stats"
msgstr ""

#: elementor/widgets/single-agency/agency-single-stats.php:62
#: elementor/widgets/single-agent/agent-single-stats.php:62
#: elementor/widgets/single-post/post-info.php:163
msgid "Taxonomy"
msgstr ""

#: elementor/widgets/single-agency/agency-stats.php:19
msgid "Section Agency Stats"
msgstr ""

#: elementor/widgets/single-agency/agency-telegram-btn.php:17
msgid "Agency Telegram Button"
msgstr ""

#: elementor/widgets/single-agency/agency-telegram-btn.php:42
#: elementor/widgets/single-agency/agency-telegram-btn.php:153
#: elementor/widgets/single-agent/agent-telegram-btn.php:42
#: elementor/widgets/single-agent/agent-telegram-btn.php:153
msgid "Telegram Button"
msgstr ""

#: elementor/widgets/single-agency/agency-whatsapp-btn.php:17
msgid "Agency WhatsApp Button"
msgstr ""

#: elementor/widgets/single-agency/agency-whatsapp-btn.php:42
#: elementor/widgets/single-agency/agency-whatsapp-btn.php:153
#: elementor/widgets/single-agent/agent-whatsapp-btn.php:42
#: elementor/widgets/single-agent/agent-whatsapp-btn.php:153
#: elementor/widgets/single-property/section-contact-2.php:624
msgid "WhatsApp Button"
msgstr ""

#: elementor/widgets/single-agent/agent-about.php:19
msgid "Section Agent Bio"
msgstr ""

#: elementor/widgets/single-agent/agent-call-btn.php:17
msgid "Agent Call Button"
msgstr ""

#: elementor/widgets/single-agent/agent-contact-btn.php:17
msgid "Agent Contact Button"
msgstr ""

#: elementor/widgets/single-agent/agent-contact-form.php:21
msgid "Agent Contact Form"
msgstr ""

#: elementor/widgets/single-agent/agent-contact.php:19
msgid "Agent Contact"
msgstr ""

#: elementor/widgets/single-agent/agent-content.php:16
msgid "Agent Content"
msgstr ""

#: elementor/widgets/single-agent/agent-excerpt.php:16
msgid "Agent Excerpt"
msgstr ""

#: elementor/widgets/single-agent/agent-image.php:17
#: elementor/widgets/single-agent/agent-image.php:42
msgid "Agent Picture"
msgstr ""

#: elementor/widgets/single-agent/agent-line-btn.php:17
msgid "Agent LINE Button"
msgstr ""

#: elementor/widgets/single-agent/agent-listings-review.php:26
msgid "Agent Listings & Reviews Tabs"
msgstr ""

#: elementor/widgets/single-agent/agent-listings.php:26
msgid "Agent Listings"
msgstr ""

#: elementor/widgets/single-agent/agent-map.php:16
msgid "Agent Location Map"
msgstr ""

#: elementor/widgets/single-agent/agent-meta.php:16
#: elementor/widgets/single-agent/agent-meta.php:41
msgid "Agent Meta"
msgstr ""

#: elementor/widgets/single-agent/agent-meta.php:56
msgid "Company Name"
msgstr ""

#: elementor/widgets/single-agent/agent-meta.php:60
msgid "Office Number"
msgstr ""

#: elementor/widgets/single-agent/agent-name.php:96
msgid "Agent Name Typography"
msgstr ""

#: elementor/widgets/single-agent/agent-name.php:113
msgid "Agent Name Color"
msgstr ""

#: elementor/widgets/single-agent/agent-name.php:134
msgid "Agent Name Hover Color"
msgstr ""

#: elementor/widgets/single-agent/agent-position.php:16
#: elementor/widgets/single-agent/agent-position.php:41
#: elementor/widgets/single-agent/agent-profile-v2.php:65
msgid "Agent Position"
msgstr ""

#: elementor/widgets/single-agent/agent-position.php:87
msgid "Company Color"
msgstr ""

#: elementor/widgets/single-agent/agent-position.php:98
msgid "Company Color Hover"
msgstr ""

#: elementor/widgets/single-agent/agent-profile-v1.php:19
msgid "Section Agent Profile v1"
msgstr ""

#: elementor/widgets/single-agent/agent-profile-v1.php:53
msgid "Company Logo"
msgstr ""

#: elementor/widgets/single-agent/agent-profile-v2.php:19
msgid "Section Agent Profile v2"
msgstr ""

#: elementor/widgets/single-agent/agent-profile-v2.php:77
msgid "Agent Agency"
msgstr ""

#: elementor/widgets/single-agent/agent-rating.php:16
#: elementor/widgets/single-agent/agent-rating.php:41
#: elementor/widgets/single-agent/agent-rating.php:100
msgid "Agent Rating"
msgstr ""

#: elementor/widgets/single-agent/agent-review.php:21
msgid "Agent Reviews"
msgstr ""

#: elementor/widgets/single-agent/agent-search.php:19
#: elementor/widgets/single-agent/agent-search.php:45
msgid "Agent Search"
msgstr ""

#: elementor/widgets/single-agent/agent-single-stats.php:19
msgid "Agent Taxonomy Stats"
msgstr ""

#: elementor/widgets/single-agent/agent-single-stats.php:45
#: elementor/widgets/single-agent/agent-stats.php:45
msgid "Agent Stats"
msgstr ""

#: elementor/widgets/single-agent/agent-stats.php:19
msgid "Section Agent Stats"
msgstr ""

#: elementor/widgets/single-agent/agent-telegram-btn.php:17
msgid "Agent Telegram Button"
msgstr ""

#: elementor/widgets/single-agent/agent-whatsapp-btn.php:17
msgid "Agent WhatsApp Button"
msgstr ""

#: elementor/widgets/single-post/author-box.php:26
msgid "Author Box"
msgstr ""

#: elementor/widgets/single-post/author-box.php:51
msgid "Author Info"
msgstr ""

#: elementor/widgets/single-post/author-box.php:59
msgid "Profile Picture"
msgstr ""

#: elementor/widgets/single-post/author-box.php:73
msgid "Picture Size"
msgstr ""

#: elementor/widgets/single-post/author-box.php:85
msgid "Display Name"
msgstr ""

#: elementor/widgets/single-post/author-box.php:123
msgid "Posts Archive"
msgstr ""

#: elementor/widgets/single-post/author-box.php:125
msgid "Link for the Author Name and Image"
msgstr ""

#: elementor/widgets/single-post/author-box.php:132
#: elementor/widgets/single-post/author-box.php:567
msgid "Biography"
msgstr ""

#: elementor/widgets/single-post/author-box.php:146
msgid "Archive Button"
msgstr ""

#: elementor/widgets/single-post/author-box.php:160
msgid "Archive Text"
msgstr ""

#: elementor/widgets/single-post/author-box.php:162
msgid "All Posts"
msgstr ""

#: elementor/widgets/single-post/author-box.php:238
msgid "Vertical Align"
msgstr ""

#: elementor/widgets/single-post/author-box.php:246
msgid "Middle"
msgstr ""

#: elementor/widgets/single-post/author-box.php:939
#: elementor/widgets/single-post/post-info.php:944
#, php-format
msgid "Picture of %s"
msgstr ""

#: elementor/widgets/single-post/author-box.php:942
msgid "Author picture"
msgstr ""

#: elementor/widgets/single-post/post-comments.php:16
msgid "Post Comments"
msgstr ""

#: elementor/widgets/single-post/post-comments.php:39
#: elementor/widgets/single-post/post-info.php:78
#: elementor/widgets/single-post/post-info.php:241
msgid "Comments"
msgstr ""

#: elementor/widgets/single-post/post-comments.php:53
msgid "Skin"
msgstr ""

#: elementor/widgets/single-post/post-comments.php:56
msgid "Theme Comments"
msgstr ""

#: elementor/widgets/single-post/post-comments.php:58
msgid ""
"The Theme Comments skin uses the currently active theme comments design and "
"layout to display the comment form and comments."
msgstr ""

#: elementor/widgets/single-post/post-comments.php:79
msgid "Comments are closed."
msgstr ""

#: elementor/widgets/single-post/post-comments.php:82
msgid ""
"Switch on comments from either the discussion box on the WordPress post edit "
"screen or from the WordPress discussion settings."
msgstr ""

#: elementor/widgets/single-post/post-excerpt.php:16
msgid "Post Excerpt"
msgstr ""

#: elementor/widgets/single-post/post-image.php:17
msgid "Post Featured Image"
msgstr ""

#: elementor/widgets/single-post/post-info.php:16
msgid "Post Info"
msgstr ""

#: elementor/widgets/single-post/post-info.php:41
#: elementor/widgets/single-property/section-energy.php:244
#: elementor/widgets/single-property/section-overview-v2.php:339
#: elementor/widgets/single-property/section-overview.php:419
msgid "Meta Data"
msgstr ""

#: elementor/widgets/single-post/post-info.php:57
#: elementor/widgets/taxonomies-list.php:126
msgid "Inline"
msgstr ""

#: elementor/widgets/single-post/post-info.php:77
msgid "Time"
msgstr ""

#: elementor/widgets/single-post/post-info.php:79
msgid "Terms"
msgstr ""

#: elementor/widgets/single-post/post-info.php:88
msgid "Date Format"
msgstr ""

#: elementor/widgets/single-post/post-info.php:93
msgctxt "Date Format"
msgid "March 6, 2018 (F j, Y)"
msgstr ""

#: elementor/widgets/single-post/post-info.php:108
msgid "Custom Date Format"
msgstr ""

#: elementor/widgets/single-post/post-info.php:117
#: elementor/widgets/single-post/post-info.php:154
#, php-format
msgid "Use the letters: %s"
msgstr ""

#: elementor/widgets/single-post/post-info.php:126
msgid "Time Format"
msgstr ""

#: elementor/widgets/single-post/post-info.php:144
msgid "Custom Time Format"
msgstr ""

#: elementor/widgets/single-post/post-info.php:177
msgid "Avatar"
msgstr ""

#: elementor/widgets/single-post/post-info.php:203
msgid "Custom Format"
msgstr ""

#: elementor/widgets/single-post/post-info.php:215
#: elementor/widgets/single-post/post-info.php:217
#: elementor/widgets/single-post/post-info.php:774
msgid "No Comments"
msgstr ""

#: elementor/widgets/single-post/post-info.php:228
#: elementor/widgets/single-post/post-info.php:230
#: elementor/widgets/single-post/post-info.php:775
msgid "One Comment"
msgstr ""

#: elementor/widgets/single-post/post-info.php:243
#: elementor/widgets/single-post/post-info.php:776
#, php-format
msgid "%s Comments"
msgstr ""

#: elementor/widgets/single-post/post-info.php:312
msgid "Choose Icon"
msgstr ""

#: elementor/widgets/single-post/post-info.php:369
#: elementor/widgets/taxonomies-list.php:180
msgid "List"
msgstr ""

#: elementor/widgets/single-post/post-info.php:464
#: elementor/widgets/taxonomies-list.php:266
msgid "Weight"
msgstr ""

#: elementor/widgets/single-post/post-info.php:640
msgid "Indent"
msgstr ""

#: elementor/widgets/single-post/post-info.php:691
msgid "Choose"
msgstr ""

#: elementor/widgets/single-post/post-navigation.php:16
#: elementor/widgets/single-post/post-navigation.php:41
msgid "Post Navigation"
msgstr ""

#: elementor/widgets/single-post/post-navigation.php:60
msgid "Previous Label"
msgstr ""

#: elementor/widgets/single-post/post-navigation.php:78
msgid "Next Label"
msgstr ""

#: elementor/widgets/single-property/breadcrumb.php:19
msgid "Breadcrumbs"
msgstr ""

#: elementor/widgets/single-property/featured-image.php:17
msgid "Featured Image"
msgstr ""

#: elementor/widgets/single-property/featured-image.php:60
msgid "Enable Fallback"
msgstr ""

#: elementor/widgets/single-property/featured-image.php:73
msgid "Fallback Image"
msgstr ""

#: elementor/widgets/single-property/featured-label.php:18
msgid "Featured Label"
msgstr ""

#: elementor/widgets/single-property/images-gallery-v1.php:16
msgid "Property Images Gallery v1"
msgstr ""

#: elementor/widgets/single-property/images-gallery-v1.php:50
#: elementor/widgets/single-property/images-gallery-v2.php:66
#: elementor/widgets/single-property/images-gallery-v3.php:49
#: elementor/widgets/single-property/images-gallery-v4.php:59
#: elementor/widgets/single-property/images-gallery-v5.php:59
msgid "Popup Gallery Type"
msgstr ""

#: elementor/widgets/single-property/images-gallery-v1.php:53
#: elementor/widgets/single-property/images-gallery-v2.php:69
#: elementor/widgets/single-property/images-gallery-v3.php:52
#: elementor/widgets/single-property/images-gallery-v4.php:62
#: elementor/widgets/single-property/images-gallery-v5.php:62
msgid "Built In"
msgstr ""

#: elementor/widgets/single-property/images-gallery-v1.php:54
#: elementor/widgets/single-property/images-gallery-v2.php:70
#: elementor/widgets/single-property/images-gallery-v3.php:53
#: elementor/widgets/single-property/images-gallery-v4.php:63
#: elementor/widgets/single-property/images-gallery-v5.php:63
msgid "Photo Swipe"
msgstr ""

#: elementor/widgets/single-property/images-gallery-v1.php:86
#: elementor/widgets/single-property/images-gallery-v2.php:79
#: elementor/widgets/single-property/images-gallery-v3.php:62
#: elementor/widgets/single-property/section-toparea-v1.php:554
#: elementor/widgets/single-property/section-toparea-v2.php:576
#: elementor/widgets/single-property/section-toparea-v3.php:578
#: elementor/widgets/single-property/section-toparea-v5.php:553
msgid "Gallery Button"
msgstr ""

#: elementor/widgets/single-property/images-gallery-v1.php:98
#: elementor/widgets/single-property/images-gallery-v2.php:91
#: elementor/widgets/single-property/images-gallery-v3.php:74
msgid ""
"Gallery button can only be disabled if all other buttons (Video, 360° Tour, "
"Map, Street View) are disabled"
msgstr ""

#: elementor/widgets/single-property/images-gallery-v1.php:109
#: elementor/widgets/single-property/images-gallery-v2.php:102
#: elementor/widgets/single-property/images-gallery-v3.php:85
#: elementor/widgets/single-property/section-toparea-v1.php:566
#: elementor/widgets/single-property/section-toparea-v2.php:588
#: elementor/widgets/single-property/section-toparea-v3.php:590
#: elementor/widgets/single-property/section-toparea-v5.php:565
msgid "Video Button"
msgstr ""

#: elementor/widgets/single-property/images-gallery-v1.php:121
#: elementor/widgets/single-property/images-gallery-v2.php:114
#: elementor/widgets/single-property/images-gallery-v3.php:97
#: elementor/widgets/single-property/section-toparea-v1.php:578
msgid "360° Virtual Tour"
msgstr ""

#: elementor/widgets/single-property/images-gallery-v1.php:133
#: elementor/widgets/single-property/images-gallery-v2.php:126
#: elementor/widgets/single-property/images-gallery-v3.php:109
#: elementor/widgets/single-property/section-toparea-v1.php:590
#: elementor/widgets/single-property/section-toparea-v2.php:612
#: elementor/widgets/single-property/section-toparea-v3.php:614
#: elementor/widgets/single-property/section-toparea-v5.php:589
msgid "Map Button"
msgstr ""

#: elementor/widgets/single-property/images-gallery-v1.php:145
#: elementor/widgets/single-property/images-gallery-v2.php:138
#: elementor/widgets/single-property/images-gallery-v3.php:121
#: elementor/widgets/single-property/section-toparea-v1.php:602
#: elementor/widgets/single-property/section-toparea-v2.php:624
#: elementor/widgets/single-property/section-toparea-v3.php:626
#: elementor/widgets/single-property/section-toparea-v5.php:601
msgid "Map will only show if you have enabled when add/edit property"
msgstr ""

#: elementor/widgets/single-property/images-gallery-v1.php:156
#: elementor/widgets/single-property/images-gallery-v2.php:149
#: elementor/widgets/single-property/images-gallery-v3.php:132
#: elementor/widgets/single-property/section-toparea-v1.php:613
#: elementor/widgets/single-property/section-toparea-v2.php:635
#: elementor/widgets/single-property/section-toparea-v3.php:637
#: elementor/widgets/single-property/section-toparea-v5.php:612
msgid "Street View Button"
msgstr ""

#: elementor/widgets/single-property/images-gallery-v1.php:168
#: elementor/widgets/single-property/images-gallery-v2.php:161
#: elementor/widgets/single-property/images-gallery-v3.php:144
#: elementor/widgets/single-property/section-toparea-v1.php:625
#: elementor/widgets/single-property/section-toparea-v2.php:647
#: elementor/widgets/single-property/section-toparea-v3.php:649
#: elementor/widgets/single-property/section-toparea-v5.php:624
msgid ""
"Street view will only show if you have enabled when add/edit property and "
"map type set to Google"
msgstr ""

#: elementor/widgets/single-property/images-gallery-v2.php:23
msgid "Property Images Gallery v2"
msgstr ""

#: elementor/widgets/single-property/images-gallery-v3.php:16
msgid "Property Images Gallery v3"
msgstr ""

#: elementor/widgets/single-property/images-gallery-v4.php:16
msgid "Property Images Gallery v4"
msgstr ""

#: elementor/widgets/single-property/images-gallery-v4.php:105
#: elementor/widgets/single-property/images-gallery-v5.php:104
#: elementor/widgets/single-property/images-gallery-v5.php:108
#: elementor/widgets/single-property/section-toparea-v6.php:706
#: elementor/widgets/single-property/section-toparea-v7.php:679
msgid "More"
msgstr ""

#: elementor/widgets/single-property/images-gallery-v5.php:16
msgid "Property Images Gallery v5"
msgstr ""

#: elementor/widgets/single-property/item-tools.php:20
msgid "Share, Favorite"
msgstr ""

#: elementor/widgets/single-property/item-tools.php:52
msgid "Share Button"
msgstr ""

#: elementor/widgets/single-property/item-tools.php:64
msgid "Favorite Button"
msgstr ""

#: elementor/widgets/single-property/item-tools.php:76
msgid "Print Button"
msgstr ""

#: elementor/widgets/single-property/item-tools.php:158
#: elementor/widgets/single-property/property-title-area.php:629
#: elementor/widgets/single-property/section-toparea-v1.php:531
#: elementor/widgets/single-property/section-toparea-v2.php:553
#: elementor/widgets/single-property/section-toparea-v3.php:555
#: elementor/widgets/single-property/section-toparea-v5.php:530
#: elementor/widgets/single-property/section-toparea-v6.php:575
#: elementor/widgets/single-property/section-toparea-v7.php:550
msgid "Border Color Hover"
msgstr ""

#: elementor/widgets/single-property/property-address.php:45
#: elementor/widgets/single-property/property-title-area.php:48
#: elementor/widgets/single-property/section-toparea-v1.php:48
#: elementor/widgets/single-property/section-toparea-v2.php:47
#: elementor/widgets/single-property/section-toparea-v3.php:52
#: elementor/widgets/single-property/section-toparea-v5.php:47
#: elementor/widgets/single-property/section-toparea-v6.php:48
#: elementor/widgets/single-property/section-toparea-v7.php:47
msgid "Streat Address"
msgstr ""

#: elementor/widgets/single-property/property-address.php:49
#: elementor/widgets/single-property/property-title-area.php:52
#: elementor/widgets/single-property/section-toparea-v1.php:52
#: elementor/widgets/single-property/section-toparea-v2.php:51
#: elementor/widgets/single-property/section-toparea-v3.php:56
#: elementor/widgets/single-property/section-toparea-v5.php:51
#: elementor/widgets/single-property/section-toparea-v6.php:52
#: elementor/widgets/single-property/section-toparea-v7.php:51
msgid "area"
msgstr ""

#: elementor/widgets/single-property/property-address.php:93
#: elementor/widgets/single-property/property-title-area.php:351
#: elementor/widgets/single-property/section-floorplan.php:197
#: elementor/widgets/single-property/section-toparea-v1.php:253
#: elementor/widgets/single-property/section-toparea-v2.php:276
#: elementor/widgets/single-property/section-toparea-v3.php:278
#: elementor/widgets/single-property/section-toparea-v5.php:253
#: elementor/widgets/single-property/section-toparea-v6.php:298
#: elementor/widgets/single-property/section-toparea-v7.php:273
msgid "Hide Icon"
msgstr ""

#: elementor/widgets/single-property/property-address.php:177
#: elementor/widgets/single-property/property-price.php:163
#: elementor/widgets/single-property/property-price.php:238
#: elementor/widgets/single-property/property-title-area.php:236
#: elementor/widgets/single-property/property-title-area.php:386
#: elementor/widgets/single-property/property-title-area.php:477
#: elementor/widgets/single-property/property-title-area.php:514
#: elementor/widgets/single-property/section-toparea-v1.php:138
#: elementor/widgets/single-property/section-toparea-v1.php:288
#: elementor/widgets/single-property/section-toparea-v1.php:379
#: elementor/widgets/single-property/section-toparea-v1.php:416
#: elementor/widgets/single-property/section-toparea-v2.php:161
#: elementor/widgets/single-property/section-toparea-v2.php:311
#: elementor/widgets/single-property/section-toparea-v2.php:401
#: elementor/widgets/single-property/section-toparea-v2.php:438
#: elementor/widgets/single-property/section-toparea-v3.php:163
#: elementor/widgets/single-property/section-toparea-v3.php:313
#: elementor/widgets/single-property/section-toparea-v3.php:403
#: elementor/widgets/single-property/section-toparea-v3.php:440
#: elementor/widgets/single-property/section-toparea-v5.php:138
#: elementor/widgets/single-property/section-toparea-v5.php:288
#: elementor/widgets/single-property/section-toparea-v5.php:378
#: elementor/widgets/single-property/section-toparea-v5.php:415
#: elementor/widgets/single-property/section-toparea-v6.php:183
#: elementor/widgets/single-property/section-toparea-v6.php:333
#: elementor/widgets/single-property/section-toparea-v6.php:423
#: elementor/widgets/single-property/section-toparea-v6.php:460
#: elementor/widgets/single-property/section-toparea-v7.php:158
#: elementor/widgets/single-property/section-toparea-v7.php:308
#: elementor/widgets/single-property/section-toparea-v7.php:398
#: elementor/widgets/single-property/section-toparea-v7.php:435
msgid "Text Shadow"
msgstr ""

#: elementor/widgets/single-property/property-content.php:119
#: elementor/widgets/single-property/section-description.php:293
msgid "Link Hover Color"
msgstr ""

#: elementor/widgets/single-property/property-price.php:52
msgid "Hide Second Price"
msgstr ""

#: elementor/widgets/single-property/property-price.php:171
#: elementor/widgets/single-property/property-title-area.php:485
#: elementor/widgets/single-property/section-toparea-v1.php:387
#: elementor/widgets/single-property/section-toparea-v2.php:409
#: elementor/widgets/single-property/section-toparea-v3.php:411
#: elementor/widgets/single-property/section-toparea-v5.php:386
#: elementor/widgets/single-property/section-toparea-v6.php:431
#: elementor/widgets/single-property/section-toparea-v7.php:406
msgid "Second Price"
msgstr ""

#: elementor/widgets/single-property/property-title-area.php:20
msgid "Property Title Info"
msgstr ""

#: elementor/widgets/single-property/property-title-area.php:131
#: elementor/widgets/single-property/section-toparea-v1.php:75
#: elementor/widgets/single-property/section-toparea-v2.php:73
#: elementor/widgets/single-property/section-toparea-v3.php:98
#: elementor/widgets/single-property/section-toparea-v5.php:73
#: elementor/widgets/single-property/section-toparea-v6.php:118
#: elementor/widgets/single-property/section-toparea-v7.php:93
msgid "Breadcrumb"
msgstr ""

#: elementor/widgets/single-property/property-title-area.php:293
#: elementor/widgets/single-property/section-toparea-v1.php:195
#: elementor/widgets/single-property/section-toparea-v2.php:218
#: elementor/widgets/single-property/section-toparea-v3.php:220
#: elementor/widgets/single-property/section-toparea-v5.php:195
#: elementor/widgets/single-property/section-toparea-v6.php:240
#: elementor/widgets/single-property/section-toparea-v7.php:215
msgid "Property Labels"
msgstr ""

#: elementor/widgets/single-property/property-title-area.php:301
#: elementor/widgets/single-property/section-toparea-v1.php:203
#: elementor/widgets/single-property/section-toparea-v2.php:226
#: elementor/widgets/single-property/section-toparea-v3.php:228
#: elementor/widgets/single-property/section-toparea-v5.php:203
#: elementor/widgets/single-property/section-toparea-v6.php:248
#: elementor/widgets/single-property/section-toparea-v7.php:223
msgid "Show Labels"
msgstr ""

#: elementor/widgets/single-property/property-title-area.php:324
#: elementor/widgets/single-property/section-toparea-v1.php:226
#: elementor/widgets/single-property/section-toparea-v2.php:249
#: elementor/widgets/single-property/section-toparea-v3.php:251
#: elementor/widgets/single-property/section-toparea-v5.php:226
#: elementor/widgets/single-property/section-toparea-v6.php:271
#: elementor/widgets/single-property/section-toparea-v7.php:246
msgid "Show Address"
msgstr ""

#: elementor/widgets/single-property/property-title-area.php:406
#: elementor/widgets/single-property/section-toparea-v1.php:308
#: elementor/widgets/single-property/section-toparea-v2.php:331
#: elementor/widgets/single-property/section-toparea-v3.php:333
#: elementor/widgets/single-property/section-toparea-v5.php:308
#: elementor/widgets/single-property/section-toparea-v6.php:353
#: elementor/widgets/single-property/section-toparea-v7.php:328
msgid "Show Price"
msgstr ""

#: elementor/widgets/single-property/property-title-area.php:525
#: elementor/widgets/single-property/section-toparea-v1.php:427
#: elementor/widgets/single-property/section-toparea-v2.php:449
#: elementor/widgets/single-property/section-toparea-v3.php:451
#: elementor/widgets/single-property/section-toparea-v5.php:426
#: elementor/widgets/single-property/section-toparea-v6.php:471
#: elementor/widgets/single-property/section-toparea-v7.php:446
msgid "Tools"
msgstr ""

#: elementor/widgets/single-property/property-title-area.php:533
msgid "Show Favorite"
msgstr ""

#: elementor/widgets/single-property/property-title-area.php:545
msgid "Show Social"
msgstr ""

#: elementor/widgets/single-property/property-title-area.php:557
msgid "Show Print"
msgstr ""

#: elementor/widgets/single-property/property-title.php:42
msgid "Listing Title"
msgstr ""

#: elementor/widgets/single-property/property-title.php:82
msgid "Title Eclipse"
msgstr ""

#: elementor/widgets/single-property/section-360-virtual.php:21
msgid "Section 360 Virtual Tour"
msgstr ""

#: elementor/widgets/single-property/section-360-virtual.php:82
#: elementor/widgets/single-property/section-address.php:197
#: elementor/widgets/single-property/section-attachments.php:91
#: elementor/widgets/single-property/section-calculator.php:81
#: elementor/widgets/single-property/section-calendar.php:81
#: elementor/widgets/single-property/section-description.php:117
#: elementor/widgets/single-property/section-details.php:359
#: elementor/widgets/single-property/section-energy.php:167
#: elementor/widgets/single-property/section-features.php:95
#: elementor/widgets/single-property/section-floorplan-v2.php:132
#: elementor/widgets/single-property/section-floorplan.php:143
#: elementor/widgets/single-property/section-nearby.php:89
#: elementor/widgets/single-property/section-overview-v2.php:246
#: elementor/widgets/single-property/section-overview.php:264
#: elementor/widgets/single-property/section-schedule-tour.php:82
#: elementor/widgets/single-property/section-similar.php:198
#: elementor/widgets/single-property/section-sublistings.php:80
#: elementor/widgets/single-property/section-video.php:81
#: elementor/widgets/single-property/section-walkscore.php:89
msgid "Section Style"
msgstr ""

#: elementor/widgets/single-property/section-360-virtual.php:94
#: elementor/widgets/single-property/section-attachments.php:247
#: elementor/widgets/single-property/section-calculator.php:93
#: elementor/widgets/single-property/section-calendar.php:93
#: elementor/widgets/single-property/section-description.php:129
#: elementor/widgets/single-property/section-energy.php:179
#: elementor/widgets/single-property/section-features.php:107
#: elementor/widgets/single-property/section-nearby.php:101
#: elementor/widgets/single-property/section-overview-v2.php:258
#: elementor/widgets/single-property/section-overview.php:276
#: elementor/widgets/single-property/section-video.php:93
#: elementor/widgets/single-property/section-walkscore.php:101
msgid "Content Style"
msgstr ""

#: elementor/widgets/single-property/section-address.php:21
msgid "Section Address"
msgstr ""

#: elementor/widgets/single-property/section-address.php:78
#: elementor/widgets/single-property/section-details.php:79
#: elementor/widgets/single-property/section-details.php:173
#: elementor/widgets/single-property/section-features.php:77
msgid "Data Columns"
msgstr ""

#: elementor/widgets/single-property/section-address.php:82
#: elementor/widgets/single-property/section-details.php:83
#: elementor/widgets/single-property/section-details.php:177
#: elementor/widgets/single-property/section-features.php:81
msgid "1 Column"
msgstr ""

#: elementor/widgets/single-property/section-address.php:111
msgid "Address Data"
msgstr ""

#: elementor/widgets/single-property/section-address.php:119
#: elementor/widgets/single-property/section-details.php:120
msgid "Control Name"
msgstr ""

#: elementor/widgets/single-property/section-address.php:132
#: elementor/widgets/single-property/section-details.php:208
msgid "Titles"
msgstr ""

#: elementor/widgets/single-property/section-address.php:241
#: elementor/widgets/single-property/section-details.php:471
msgid "Meta Titles"
msgstr ""

#: elementor/widgets/single-property/section-address.php:271
#: elementor/widgets/single-property/section-details.php:501
msgid "Meta Values"
msgstr ""

#: elementor/widgets/single-property/section-attachments.php:20
msgid "Section Attachments"
msgstr ""

#: elementor/widgets/single-property/section-attachments.php:78
#: elementor/widgets/single-property/section-description.php:101
msgid "Download Text"
msgstr ""

#: elementor/widgets/single-property/section-attachments.php:109
#: elementor/widgets/single-property/section-contact-bottom.php:565
#: elementor/widgets/single-property/section-schedule-tour.php:206
#: elementor/widgets/single-property/section-sublistings.php:88
msgid "Hide Title Border"
msgstr ""

#: elementor/widgets/single-property/section-attachments.php:283
msgid "Documents"
msgstr ""

#: elementor/widgets/single-property/section-attachments.php:324
#: elementor/widgets/single-property/section-description.php:264
msgid "Link Typography"
msgstr ""

#: elementor/widgets/single-property/section-attachments.php:347
#: elementor/widgets/single-property/section-description.php:328
#: elementor/widgets/single-property/section-description.php:402
#: elementor/widgets/single-property/section-description.php:404
#: elementor/widgets/single-property/section-description.php:407
msgid "Download"
msgstr ""

#: elementor/widgets/single-property/section-block-gallery.php:16
msgid "Section Block Gallery"
msgstr ""

#: elementor/widgets/single-property/section-block-gallery.php:50
msgid "Visible Images"
msgstr ""

#: elementor/widgets/single-property/section-block-gallery.php:59
msgid "Images in a row"
msgstr ""

#: elementor/widgets/single-property/section-block-gallery.php:76
msgid "Popup Type"
msgstr ""

#: elementor/widgets/single-property/section-calculator.php:20
msgid "Section Mortgage Calculator"
msgstr ""

#: elementor/widgets/single-property/section-calculator.php:140
msgid "Border Style"
msgstr ""

#: elementor/widgets/single-property/section-calendar.php:20
msgid "Section Availability Calendar"
msgstr ""

#: elementor/widgets/single-property/section-contact-2.php:21
msgid "Section Agent Contact Form v2"
msgstr ""

#: elementor/widgets/single-property/section-contact-2.php:55
msgid "Schedule Tour Tab"
msgstr ""

#: elementor/widgets/single-property/section-contact-2.php:67
msgid "Schedule Tour Title"
msgstr ""

#: elementor/widgets/single-property/section-contact-2.php:79
msgid "Request Info Title"
msgstr ""

#: elementor/widgets/single-property/section-contact-2.php:91
msgid "Default Active Tab"
msgstr ""

#: elementor/widgets/single-property/section-contact-2.php:95
msgid "Schedule a Tour"
msgstr ""

#: elementor/widgets/single-property/section-contact-2.php:96
msgid "Request Info"
msgstr ""

#: elementor/widgets/single-property/section-contact-2.php:107
msgid "Agent Info"
msgstr ""

#: elementor/widgets/single-property/section-contact-2.php:119
msgid "View Listing Link"
msgstr ""

#: elementor/widgets/single-property/section-contact-2.php:186
msgid "Form Tabs"
msgstr ""

#: elementor/widgets/single-property/section-contact-2.php:316
#: elementor/widgets/single-property/section-schedule-tour-v2.php:142
msgid "Tour Tabs"
msgstr ""

#: elementor/widgets/single-property/section-contact-2.php:451
#: elementor/widgets/single-property/section-schedule-tour-v2.php:250
msgid "Active Border Color"
msgstr ""

#: elementor/widgets/single-property/section-contact-bottom.php:21
msgid "Section Agent Contact Form"
msgstr ""

#: elementor/widgets/single-property/section-contact-bottom.php:77
msgid "Agent Details"
msgstr ""

#: elementor/widgets/single-property/section-contact-bottom.php:217
msgid "View Listing Button"
msgstr ""

#: elementor/widgets/single-property/section-description.php:20
msgid "Section Description"
msgstr ""

#: elementor/widgets/single-property/section-description.php:78
msgid "Show Attachments"
msgstr ""

#: elementor/widgets/single-property/section-description.php:88
msgid "Attachment Title"
msgstr ""

#: elementor/widgets/single-property/section-description.php:223
msgid "Documents Title"
msgstr ""

#: elementor/widgets/single-property/section-details.php:21
msgid "Section Details"
msgstr ""

#: elementor/widgets/single-property/section-details.php:112
msgid "Details Data"
msgstr ""

#: elementor/widgets/single-property/section-details.php:132
msgid "Additional Details"
msgstr ""

#: elementor/widgets/single-property/section-details.php:140
msgid "Show Additional Details"
msgstr ""

#: elementor/widgets/single-property/section-details.php:150
msgid "Addtional Section Header"
msgstr ""

#: elementor/widgets/single-property/section-details.php:160
msgid "Additional Section Title"
msgstr ""

#: elementor/widgets/single-property/section-details.php:251
#: elementor/widgets/single-property/section-overview-v2.php:200
#: elementor/widgets/single-property/section-overview.php:199
#: statistics/houzez-statistics.php:589
msgid "Bedroom"
msgstr ""

#: elementor/widgets/single-property/section-details.php:268
msgid "Room"
msgstr ""

#: elementor/widgets/single-property/section-details.php:285
#: elementor/widgets/single-property/section-overview-v2.php:207
#: elementor/widgets/single-property/section-overview.php:206
#: statistics/houzez-statistics.php:590
msgid "Bathroom"
msgstr ""

#: elementor/widgets/single-property/section-details.php:319
msgid "Garage Size"
msgstr ""

#: elementor/widgets/single-property/section-details.php:373
msgid "Details Box Style"
msgstr ""

#: elementor/widgets/single-property/section-details.php:403
msgid "Hide Box Border"
msgstr ""

#: elementor/widgets/single-property/section-details.php:562
msgid "Meta Border Color"
msgstr ""

#: elementor/widgets/single-property/section-details.php:574
msgid "Additional Meta Border Color"
msgstr ""

#: elementor/widgets/single-property/section-energy.php:20
msgid "Section Energy Class"
msgstr ""

#: elementor/widgets/single-property/section-energy.php:87
msgid ""
"Add your custom labels, it will overwrite default labels which are "
"manageable in theme options -> translation"
msgstr ""

#: elementor/widgets/single-property/section-energy.php:96
msgid "Energetic class"
msgstr ""

#: elementor/widgets/single-property/section-energy.php:106
msgid "Global Energy Performance Index"
msgstr ""

#: elementor/widgets/single-property/section-energy.php:115
msgid "Renewable energy performance index"
msgstr ""

#: elementor/widgets/single-property/section-energy.php:125
msgid "Energy performance of the building"
msgstr ""

#: elementor/widgets/single-property/section-energy.php:135
msgid "EPC Current Rating"
msgstr ""

#: elementor/widgets/single-property/section-energy.php:145
msgid "EPC Potential Rating"
msgstr ""

#: elementor/widgets/single-property/section-energy.php:155
msgid "Energy class"
msgstr ""

#: elementor/widgets/single-property/section-features.php:20
msgid "Section Features"
msgstr ""

#: elementor/widgets/single-property/section-features.php:152
msgid "Icons Color"
msgstr ""

#: elementor/widgets/single-property/section-features.php:164
msgid "Icons Size"
msgstr ""

#: elementor/widgets/single-property/section-features.php:207
msgid "Text Typography"
msgstr ""

#: elementor/widgets/single-property/section-floorplan-v2.php:20
msgid "Section Floor Plan v2"
msgstr ""

#: elementor/widgets/single-property/section-floorplan-v2.php:77
#: elementor/widgets/single-property/section-floorplan.php:87
msgid "Hide Size"
msgstr ""

#: elementor/widgets/single-property/section-floorplan-v2.php:90
#: elementor/widgets/single-property/section-floorplan.php:100
msgid "Hide Beds"
msgstr ""

#: elementor/widgets/single-property/section-floorplan-v2.php:103
#: elementor/widgets/single-property/section-floorplan.php:113
msgid "Hide Bath"
msgstr ""

#: elementor/widgets/single-property/section-floorplan-v2.php:116
#: elementor/widgets/single-property/section-floorplan.php:126
msgid "Hide Price"
msgstr ""

#: elementor/widgets/single-property/section-floorplan-v2.php:236
#: elementor/widgets/single-property/section-floorplan.php:256
msgid "Floor Plan Title"
msgstr ""

#: elementor/widgets/single-property/section-floorplan.php:20
msgid "Section Floor Plan"
msgstr ""

#: elementor/widgets/single-property/section-floorplan.php:77
msgid "Default Open"
msgstr ""

#: elementor/widgets/single-property/section-floorplan.php:288
msgid "Meta"
msgstr ""

#: elementor/widgets/single-property/section-google-map.php:16
msgid "Section Google Map"
msgstr ""

#: elementor/widgets/single-property/section-google-map.php:57
#: elementor/widgets/single-property/section-open-street-map.php:57
msgid "Deprecated Widget"
msgstr ""

#: elementor/widgets/single-property/section-google-map.php:58
#: elementor/widgets/single-property/section-open-street-map.php:58
msgid ""
"This widget is deprecated. Please use \"Section Map\" widget instead. The "
"map will display according to the map system selected in Theme Options."
msgstr ""

#: elementor/widgets/single-property/section-google-map.php:68
#: elementor/widgets/single-property/section-map.php:51
#: elementor/widgets/single-property/section-open-street-map.php:68
msgid "Pin or Circle"
msgstr ""

#: elementor/widgets/single-property/section-google-map.php:72
#: elementor/widgets/single-property/section-map.php:55
#: elementor/widgets/single-property/section-open-street-map.php:72
msgid "Marker Pin"
msgstr ""

#: elementor/widgets/single-property/section-google-map.php:73
#: elementor/widgets/single-property/section-map.php:56
#: elementor/widgets/single-property/section-open-street-map.php:73
msgid "Circle"
msgstr ""

#: elementor/widgets/single-property/section-google-map.php:85
msgid "Road Map"
msgstr ""

#: elementor/widgets/single-property/section-google-map.php:96
#: elementor/widgets/single-property/section-map.php:64
#: elementor/widgets/single-property/section-open-street-map.php:81
msgid "Zoom"
msgstr ""

#: elementor/widgets/single-property/section-google-map.php:105
msgid "Style for Google Map"
msgstr ""

#: elementor/widgets/single-property/section-google-map.php:107
msgid "Use https://snazzymaps.com/ to create styles"
msgstr ""

#: elementor/widgets/single-property/section-map.php:16
msgid "Section Map"
msgstr ""

#: elementor/widgets/single-property/section-nearby.php:20
msgid "Section Yelp Nearby"
msgstr ""

#: elementor/widgets/single-property/section-nearby.php:77
msgid ""
"Make sure you have added Yelp API in Theme Options -> Property Details -> "
"Yelp Nearby Places"
msgstr ""

#: elementor/widgets/single-property/section-nearby.php:158
msgid "Powered by"
msgstr ""

#: elementor/widgets/single-property/section-nearby.php:158
msgid "Yelp"
msgstr ""

#: elementor/widgets/single-property/section-open-street-map.php:16
msgid "Section Open Street Map"
msgstr ""

#: elementor/widgets/single-property/section-overview-v2.php:23
msgid "Section Overview v2"
msgstr ""

#: elementor/widgets/single-property/section-overview-v2.php:177
#: elementor/widgets/single-property/section-overview.php:176
msgid "Pre Defined"
msgstr ""

#: elementor/widgets/single-property/section-overview-v2.php:266
#: elementor/widgets/single-property/section-overview.php:343
msgid "Text Align"
msgstr ""

#: elementor/widgets/single-property/section-overview-v2.php:368
#: elementor/widgets/single-property/section-overview.php:448
msgid "Meta Icons"
msgstr ""

#: elementor/widgets/single-property/section-overview-v2.php:389
#: elementor/widgets/single-property/section-overview.php:469
msgid "Icons Size(px)"
msgstr ""

#: elementor/widgets/single-property/section-overview.php:21
msgid "Section Overview"
msgstr ""

#: elementor/widgets/single-property/section-overview.php:246
msgid "6"
msgstr ""

#: elementor/widgets/single-property/section-overview.php:247
msgid "5"
msgstr ""

#: elementor/widgets/single-property/section-overview.php:248
msgid "4"
msgstr ""

#: elementor/widgets/single-property/section-overview.php:249
msgid "3"
msgstr ""

#: elementor/widgets/single-property/section-overview.php:250
msgid "2"
msgstr ""

#: elementor/widgets/single-property/section-overview.php:284
msgid "Separator"
msgstr ""

#: elementor/widgets/single-property/section-review.php:21
msgid "Section Reviews"
msgstr ""

#: elementor/widgets/single-property/section-schedule-tour-v2.php:21
msgid "Section Schedule Tour v2"
msgstr ""

#: elementor/widgets/single-property/section-schedule-tour.php:21
msgid "Section Schedule Tour"
msgstr ""

#: elementor/widgets/single-property/section-similar.php:21
msgid "Section Similar Listings"
msgstr ""

#: elementor/widgets/single-property/section-similar.php:78
msgid "Similar Properties Criteria"
msgstr ""

#: elementor/widgets/single-property/section-similar.php:84
msgid "Property Feature"
msgstr ""

#: elementor/widgets/single-property/section-similar.php:168
#: elementor/widgets/sort-by.php:149 elementor/widgets/sort-by.php:150
msgid "Default Order"
msgstr ""

#: elementor/widgets/single-property/section-similar.php:171
#: functions/functions.php:254
msgid "Date New to Old"
msgstr ""

#: elementor/widgets/single-property/section-similar.php:172
#: functions/functions.php:253
msgid "Date Old to New"
msgstr ""

#: elementor/widgets/single-property/section-similar.php:173
#: functions/functions.php:252
msgid "Price (High to Low)"
msgstr ""

#: elementor/widgets/single-property/section-similar.php:174
#: functions/functions.php:251
msgid "Price (Low to High)"
msgstr ""

#: elementor/widgets/single-property/section-similar.php:175
msgid "Show Featured Listings on Top"
msgstr ""

#: elementor/widgets/single-property/section-sublistings.php:19
msgid "Section Sub Listings"
msgstr ""

#: elementor/widgets/single-property/section-toparea-v1.php:20
msgid "Section Top Area v1"
msgstr ""

#: elementor/widgets/single-property/section-toparea-v1.php:435
#: elementor/widgets/single-property/section-toparea-v2.php:457
#: elementor/widgets/single-property/section-toparea-v3.php:459
#: elementor/widgets/single-property/section-toparea-v5.php:434
#: elementor/widgets/single-property/section-toparea-v6.php:479
#: elementor/widgets/single-property/section-toparea-v7.php:454
msgid "Favorite"
msgstr ""

#: elementor/widgets/single-property/section-toparea-v1.php:447
#: elementor/widgets/single-property/section-toparea-v2.php:469
#: elementor/widgets/single-property/section-toparea-v3.php:471
#: elementor/widgets/single-property/section-toparea-v5.php:446
#: elementor/widgets/single-property/section-toparea-v6.php:491
#: elementor/widgets/single-property/section-toparea-v7.php:466
msgid "Social Share"
msgstr ""

#: elementor/widgets/single-property/section-toparea-v1.php:459
#: elementor/widgets/single-property/section-toparea-v2.php:481
#: elementor/widgets/single-property/section-toparea-v3.php:483
#: elementor/widgets/single-property/section-toparea-v5.php:458
#: elementor/widgets/single-property/section-toparea-v6.php:503
#: elementor/widgets/single-property/section-toparea-v7.php:478
msgid "Print"
msgstr ""

#: elementor/widgets/single-property/section-toparea-v1.php:546
#: elementor/widgets/single-property/section-toparea-v2.php:568
#: elementor/widgets/single-property/section-toparea-v3.php:570
#: elementor/widgets/single-property/section-toparea-v5.php:545
msgid "Media Buttons"
msgstr ""

#: elementor/widgets/single-property/section-toparea-v1.php:639
msgid "Agent Form"
msgstr ""

#: elementor/widgets/single-property/section-toparea-v1.php:647
msgid "Show Agent Form"
msgstr ""

#: elementor/widgets/single-property/section-toparea-v2.php:19
msgid "Section Top Area v2"
msgstr ""

#: elementor/widgets/single-property/section-toparea-v2.php:600
#: elementor/widgets/single-property/section-toparea-v3.php:602
#: elementor/widgets/single-property/section-toparea-v5.php:577
msgid "360° Virtual Tour Button"
msgstr ""

#: elementor/widgets/single-property/section-toparea-v3.php:24
msgid "Section Top Area v3 and v4"
msgstr ""

#: elementor/widgets/single-property/section-toparea-v5.php:19
msgid "Section Top Area v5"
msgstr ""

#: elementor/widgets/single-property/section-toparea-v6.php:20
msgid "Section Top Area v6"
msgstr ""

#: elementor/widgets/single-property/section-toparea-v7.php:19
msgid "Section Top Area v7"
msgstr ""

#: elementor/widgets/single-property/section-video.php:20
msgid "Section Video"
msgstr ""

#: elementor/widgets/single-property/section-walkscore.php:20
msgid "Section Walkscore"
msgstr ""

#: elementor/widgets/single-property/section-walkscore.php:77
msgid ""
"Make sure you have added Walkscore API key in Theme Options -> Property "
"Details -> Walkscore"
msgstr ""

#: elementor/widgets/single-property/section-walkscore.php:169
msgid "Visit listing detail page for walkscore preview"
msgstr ""

#: elementor/widgets/sort-by.php:36
msgid "Listings Sort By"
msgstr ""

#: elementor/widgets/sort-by.php:89
msgid "Sort By:"
msgstr ""

#: elementor/widgets/sort-by.php:151
msgid "Price - Low to High"
msgstr ""

#: elementor/widgets/sort-by.php:152
msgid "Price - High to Low"
msgstr ""

#: elementor/widgets/sort-by.php:154
msgid "Featured Listings First"
msgstr ""

#: elementor/widgets/sort-by.php:156
msgid "Date - Old to New"
msgstr ""

#: elementor/widgets/sort-by.php:157
msgid "Date - New to Old"
msgstr ""

#: elementor/widgets/space.php:36
msgid "Empty Space"
msgstr ""

#: elementor/widgets/space.php:86
msgid "Space"
msgstr ""

#: elementor/widgets/taxonomies-cards-carousel.php:37
msgid "Taxonomies Cards Carousel"
msgstr ""

#: elementor/widgets/taxonomies-cards-carousel.php:87
#: elementor/widgets/taxonomies-cards.php:87
msgid "Choose Layout"
msgstr ""

#: elementor/widgets/taxonomies-cards-carousel.php:266
#: elementor/widgets/taxonomies-cards.php:171
#: elementor/widgets/taxonomies-grids-carousel.php:251
#: elementor/widgets/taxonomies-grids.php:164
msgid "Count Typography"
msgstr ""

#: elementor/widgets/taxonomies-cards-carousel.php:286
#: elementor/widgets/taxonomies-cards.php:191
#: elementor/widgets/taxonomies-grids-carousel.php:271
#: elementor/widgets/taxonomies-grids.php:184
msgid "Count Color"
msgstr ""

#: elementor/widgets/taxonomies-cards-carousel.php:310
#: elementor/widgets/taxonomies-cards.php:215
msgid "Gap Bottom"
msgstr ""

#: elementor/widgets/taxonomies-cards-carousel.php:327
#: elementor/widgets/taxonomies-cards.php:232
msgid "Gap Right"
msgstr ""

#: elementor/widgets/taxonomies-cards-carousel.php:376
#: elementor/widgets/taxonomies-cards.php:281
msgid "Image Padding"
msgstr ""

#: elementor/widgets/taxonomies-cards-carousel.php:389
#: elementor/widgets/taxonomies-cards.php:294
#: elementor/widgets/taxonomies-grids-carousel.php:331
#: elementor/widgets/taxonomies-grids.php:279
#: elementor/widgets/taxonomies-list.php:711
msgid "Shadow"
msgstr ""

#: elementor/widgets/taxonomies-cards.php:37
msgid "Taxonomies Cards"
msgstr ""

#: elementor/widgets/taxonomies-grids-carousel.php:37
msgid "Taxonomies Grids Carousel"
msgstr ""

#: elementor/widgets/taxonomies-grids-carousel.php:300
#: elementor/widgets/taxonomies-grids.php:248
msgid "Grid Padding"
msgstr ""

#: elementor/widgets/taxonomies-grids-carousel.php:365
#: elementor/widgets/taxonomies-grids.php:313
msgid "Opacity Color"
msgstr ""

#: elementor/widgets/taxonomies-grids.php:40
msgid "Taxonomies Grids"
msgstr ""

#: elementor/widgets/taxonomies-list.php:42
msgid "Taxonomies List"
msgstr ""

#: elementor/widgets/taxonomies-list.php:92
msgid "List Title"
msgstr ""

#: elementor/widgets/taxonomies-list.php:123
msgid "Count Position"
msgstr ""

#: elementor/widgets/taxonomies-list.php:127
msgid "Separated"
msgstr ""

#: elementor/widgets/taxonomies-list.php:491
msgid "Horizontal Alignment"
msgstr ""

#: elementor/widgets/taxonomies-list.php:522
msgid "Vertical Alignment"
msgstr ""

#: elementor/widgets/taxonomies-list.php:548
msgid "Adjust Vertical Position"
msgstr ""

#: elementor/widgets/team-member.php:36
msgid "Team"
msgstr ""

#: elementor/widgets/team-member.php:130
msgid "Facebook Profile Link"
msgstr ""

#: elementor/widgets/team-member.php:138
msgid "Facebook Target"
msgstr ""

#: elementor/widgets/team-member.php:151
msgid "X Profile Link"
msgstr ""

#: elementor/widgets/team-member.php:159
msgid "X Target"
msgstr ""

#: elementor/widgets/team-member.php:173
msgid "LinkedIn Profile Link"
msgstr ""

#: elementor/widgets/team-member.php:181
msgid "LinkedIn Target"
msgstr ""

#: elementor/widgets/team-member.php:194
msgid "Pinterest Profile Link"
msgstr ""

#: elementor/widgets/team-member.php:202
msgid "Pinterest Target"
msgstr ""

#: elementor/widgets/testimonials-v2.php:37
msgid "Testimonials v2"
msgstr ""

#: elementor/widgets/testimonials-v2.php:87
#: elementor/widgets/testimonials.php:87
msgid "Testimonials Type"
msgstr ""

#: elementor/widgets/testimonials-v2.php:175
#: elementor/widgets/testimonials.php:115
msgid "Image Resolution"
msgstr ""

#: elementor/widgets/testimonials-v3.php:37
msgid "Testimonials v3"
msgstr ""

#: elementor/widgets/testimonials.php:37
msgid "Testimonials v1"
msgstr ""

#: elementor/widgets/testimonials.php:90
msgid "Grid 4 Columns"
msgstr ""

#: elementor/widgets/wpml/advanced-search-wpml.php:22
msgid "Advanced Search: Search Title"
msgstr ""

#: elementor/widgets/wpml/contact-form-wpml.php:22
msgid "Contact Form: GDPR Label"
msgstr ""

#: elementor/widgets/wpml/contact-form-wpml.php:27
msgid "Contact Form: GDPR Validation Message"
msgstr ""

#: elementor/widgets/wpml/contact-form-wpml.php:32
msgid "Contact Form: GDPR Agreement Text"
msgstr ""

#: elementor/widgets/wpml/contact-form-wpml.php:37
msgid "Contact Form: Redirect Link"
msgstr ""

#: elementor/widgets/wpml/contact-form-wpml.php:63
msgid "Contact Form: Field Label"
msgstr ""

#: elementor/widgets/wpml/contact-form-wpml.php:66
msgid "Contact Form: Field Placeholder"
msgstr ""

#: elementor/widgets/wpml/contact-form-wpml.php:69
msgid "Contact Form: Field Validation Message"
msgstr ""

#: elementor/widgets/wpml/grid-builder-wpml.php:22
msgid "Grid Builder: Title"
msgstr ""

#: elementor/widgets/wpml/grid-builder-wpml.php:27
msgid "Grid Builder: Sub Title"
msgstr ""

#: elementor/widgets/wpml/grid-builder-wpml.php:32
msgid "Grid Builder: More Details Text"
msgstr ""

#: elementor/widgets/wpml/grid-builder-wpml.php:37
msgid "Grid Builder: Property Text"
msgstr ""

#: elementor/widgets/wpml/grid-builder-wpml.php:42
msgid "Grid Builder: Properties Text"
msgstr ""

#: elementor/widgets/wpml/grid-builder-wpml.php:47
msgid "Grid Builder: Link"
msgstr ""

#: elementor/widgets/wpml/icon-box-wpml.php:42
msgid "Icon Box: Title"
msgstr ""

#: elementor/widgets/wpml/icon-box-wpml.php:45
msgid "Icon Box: Text"
msgstr ""

#: elementor/widgets/wpml/icon-box-wpml.php:48
msgid "Icon Box: Read More Text"
msgstr ""

#: elementor/widgets/wpml/icon-box-wpml.php:51
msgid "Icon Box: Read More Link"
msgstr ""

#: elementor/widgets/wpml/inquiry-form-wpml.php:22
msgid "Inquiry Form: GDPR Label"
msgstr ""

#: elementor/widgets/wpml/inquiry-form-wpml.php:27
msgid "Inquiry Form: GDPR Validation Message"
msgstr ""

#: elementor/widgets/wpml/inquiry-form-wpml.php:32
msgid "Inquiry Form: GDPR Agreement Text"
msgstr ""

#: elementor/widgets/wpml/inquiry-form-wpml.php:37
msgid "Inquiry Form: Redirect Link"
msgstr ""

#: elementor/widgets/wpml/inquiry-form-wpml.php:63
msgid "Inquiry Form: Field Label"
msgstr ""

#: elementor/widgets/wpml/inquiry-form-wpml.php:66
msgid "Inquiry Form: Field Placeholder"
msgstr ""

#: elementor/widgets/wpml/inquiry-form-wpml.php:69
msgid "Inquiry Form: Field Validation Message"
msgstr ""

#: elementor/widgets/wpml/price-table-wpml.php:22
msgid "Price Table: Package Name"
msgstr ""

#: elementor/widgets/wpml/price-table-wpml.php:27
msgid "Price Table: Package Price"
msgstr ""

#: elementor/widgets/wpml/price-table-wpml.php:32
msgid "Price Table: Package Currency"
msgstr ""

#: elementor/widgets/wpml/price-table-wpml.php:37
msgid "Price Table: Package Content"
msgstr ""

#: elementor/widgets/wpml/price-table-wpml.php:42
msgid "Price Table: Package Button Text"
msgstr ""

#: elementor/widgets/wpml/property-carousel-v1-wpml.php:22
msgid "Properties Carousel v1: All - Button Text"
msgstr ""

#: elementor/widgets/wpml/property-carousel-v1-wpml.php:27
msgid "Properties Carousel v1: All - Button URL"
msgstr ""

#: elementor/widgets/wpml/property-carousel-v2-wpml.php:22
msgid "Properties Carousel v2: All - Button Text"
msgstr ""

#: elementor/widgets/wpml/property-carousel-v2-wpml.php:27
msgid "Properties Carousel v2: All - Button URL"
msgstr ""

#: elementor/widgets/wpml/property-carousel-v3-wpml.php:22
msgid "Properties Carousel v3: All - Button Text"
msgstr ""

#: elementor/widgets/wpml/property-carousel-v3-wpml.php:27
msgid "Properties Carousel v3: All - Button URL"
msgstr ""

#: elementor/widgets/wpml/property-carousel-v5-wpml.php:22
msgid "Properties Carousel v5: All - Button Text"
msgstr ""

#: elementor/widgets/wpml/property-carousel-v5-wpml.php:27
msgid "Properties Carousel v5: All - Button URL"
msgstr ""

#: elementor/widgets/wpml/property-carousel-v6-wpml.php:22
msgid "Properties Carousel v6: All - Button Text"
msgstr ""

#: elementor/widgets/wpml/property-carousel-v6-wpml.php:27
msgid "Properties Carousel v6: All - Button URL"
msgstr ""

#: elementor/widgets/wpml/search-builder-wpml.php:22
msgid "Search Builder: All Tab Text"
msgstr ""

#: elementor/widgets/wpml/search-builder-wpml.php:48
msgid "Search Builder: Field Label"
msgstr ""

#: elementor/widgets/wpml/search-builder-wpml.php:51
msgid "Search Builder: Field Placeholder"
msgstr ""

#: elementor/widgets/wpml/search-builder-wpml.php:54
msgid "Search Builder: Selected Count Text"
msgstr ""

#: elementor/widgets/wpml/section-title-wpml.php:22
msgid "Section Title: Main Title"
msgstr ""

#: elementor/widgets/wpml/section-title-wpml.php:27
msgid "Section Title: Sub Title"
msgstr ""

#: elementor/widgets/wpml/sort-by-wpml.php:22
msgid "Sort By: Title"
msgstr ""

#: elementor/widgets/wpml/team-member-wpml.php:22
msgid "Team Member: Name"
msgstr ""

#: elementor/widgets/wpml/team-member-wpml.php:27
msgid "Team Member: Position"
msgstr ""

#: elementor/widgets/wpml/team-member-wpml.php:32
msgid "Team Member: Description"
msgstr ""

#: elementor/widgets/wpml/team-member-wpml.php:37
msgid "Team Member: Profile Link"
msgstr ""

#: elementor/widgets/wpml/team-member-wpml.php:42
msgid "Team Member: Facebook Link"
msgstr ""

#: elementor/widgets/wpml/team-member-wpml.php:47
msgid "Team Member: Twitter Link"
msgstr ""

#: elementor/widgets/wpml/team-member-wpml.php:52
msgid "Team Member: LinkedIn Link"
msgstr ""

#: elementor/widgets/wpml/team-member-wpml.php:57
msgid "Team Member: Pinterest Link"
msgstr ""

#: elementor/widgets/wpml/team-member-wpml.php:62
msgid "Team Member: Google Link"
msgstr ""

#: extensions/favethemes-white-label/favethemes-white-label.php:50
#: extensions/favethemes-white-label/favethemes-white-label.php:51
msgid "White Label"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:10
msgid "White label settings updated successfully!"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:33
msgid "White Label Settings"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:42
#: extensions/favethemes-white-label/template/form.php:269
msgid "Reset Form"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:65
msgid "Fields Configured"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:75
msgid "Completion Rate"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:84
msgid "Hidden"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:84
msgid "Visible"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:85
msgid "Customizer Themes"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:95
#: extensions/favethemes-white-label/template/form.php:221
msgid "Branding Logo"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:105
msgid "Theme Branding Configuration"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:108
msgid "Well Configured"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:108
msgid "Needs Setup"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:113
msgid ""
"Customize the theme branding to match your agency or company identity. These "
"settings will replace the default Houzez branding throughout the admin area "
"and theme appearance."
msgstr ""

#: extensions/favethemes-white-label/template/form.php:125
msgid "Theme Branding"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:128
msgid "Enter your brand name"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:129
msgid "This replaces \"Houzez\" throughout the admin area"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:136
msgid "Theme Name"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:138
msgid "Custom theme name"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:139
msgid "Replaces the theme name in Appearance > Themes"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:146
msgid "Theme Author"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:148
msgid "Your company name"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:149
msgid "Replaces the theme author in Appearance > Themes"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:156
msgid "Author URL"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:158
msgid "https://yourwebsite.com"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:159
msgid "Your website URL that will be linked from the theme author"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:166
msgid "Theme Description"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:168
msgid "Enter a custom description for your theme..."
msgstr ""

#: extensions/favethemes-white-label/template/form.php:169
msgid "Custom description that appears in Appearance > Themes"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:178
msgid "Visual Branding"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:187
#: extensions/favethemes-white-label/template/form.php:192
msgid "Theme Screenshot"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:196
msgid "Screenshot URL"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:200
#: extensions/favethemes-white-label/template/form.php:229
msgid "Upload"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:204
#: extensions/favethemes-white-label/template/form.php:233
#: extensions/meta-box/addons/meta-box-group/group-field.php:140
#: extensions/meta-box/inc/fields/file-input.php:48
msgid "Remove"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:209
msgid "Custom screenshot for Appearance > Themes. Recommended size: 880x660px"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:216
msgid "Admin Logo"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:225
msgid "Logo URL"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:238
msgid "Logo displayed in the admin panel header. Recommended size: 127x24px"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:249
msgid "Advanced Settings"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:257
msgid "Hide Themes Section in Customizer"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:258
msgid "Prevents users from switching themes through the WordPress Customizer"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:288
msgid "How White Labeling Works"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:298
msgid "Theme Appearance"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:299
msgid ""
"Customize how your theme appears in the WordPress admin, including name, "
"author, and description in the Themes section."
msgstr ""

#: extensions/favethemes-white-label/template/form.php:308
msgid "Admin Branding"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:309
msgid ""
"Replace \"Houzez\" branding throughout the admin area with your own company "
"or agency branding for a professional look."
msgstr ""

#: extensions/favethemes-white-label/template/form.php:318
msgid "Client Protection"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:319
msgid ""
"Hide theme switching options and present a seamless, branded experience to "
"your clients without exposing the underlying theme."
msgstr ""

#: extensions/meta-box/addons/mb-term-meta/src/MetaBox.php:61
msgid "Term added."
msgstr ""

#: extensions/meta-box/addons/meta-box-group/group-field.php:63
msgid "Are you sure you want to remove this group?"
msgstr ""

#: extensions/meta-box/addons/meta-box-group/group-field.php:324
msgid "Entry {#}"
msgstr ""

#: extensions/meta-box/addons/meta-box-group/group-field.php:324
msgid "Entry"
msgstr ""

#: extensions/meta-box/inc/about/about.php:38
msgid "About"
msgstr ""

#: extensions/meta-box/inc/about/about.php:40
msgid "Go Pro"
msgstr ""

#: extensions/meta-box/inc/about/about.php:50
#: extensions/meta-box/inc/about/about.php:51
#: extensions/meta-box/src/Block/Register.php:12
#: extensions/meta-box/src/Bricks/Register.php:10
#: extensions/meta-box/src/Elementor/Register.php:13
#: extensions/meta-box/src/Oxygen/Register.php:14
msgid "Meta Box"
msgstr ""

#: extensions/meta-box/inc/about/about.php:63
msgid "Welcome to Meta Box"
msgstr ""

#: extensions/meta-box/inc/about/about.php:64
msgid "Dashboard"
msgstr ""

#: extensions/meta-box/inc/about/sections/extensions.php:5
msgid ""
"Extend custom fields in WordPress well beyond what others would ever "
"consider ordinary!"
msgstr ""

#: extensions/meta-box/inc/about/sections/extensions.php:6
msgid "Save over 80% with our extensions bundles."
msgstr ""

#: extensions/meta-box/inc/about/sections/extensions.php:6
msgid "View Bundles"
msgstr ""

#: extensions/meta-box/inc/about/sections/extensions.php:14
msgid ""
"Create and manage custom post types easily in WordPress with an easy-to-use "
"interface."
msgstr ""

#: extensions/meta-box/inc/about/sections/extensions.php:18
#: extensions/meta-box/inc/about/sections/extensions.php:114
msgid "Free Download"
msgstr ""

#: extensions/meta-box/inc/about/sections/extensions.php:26
msgid ""
"Drag and drop your custom fields into place without a single line of code."
msgstr ""

#: extensions/meta-box/inc/about/sections/extensions.php:30
#: extensions/meta-box/inc/about/sections/extensions.php:42
#: extensions/meta-box/inc/about/sections/extensions.php:54
#: extensions/meta-box/inc/about/sections/extensions.php:66
#: extensions/meta-box/inc/about/sections/extensions.php:78
#: extensions/meta-box/inc/about/sections/extensions.php:90
#: extensions/meta-box/inc/about/sections/extensions.php:102
msgid "Learn More"
msgstr ""

#: extensions/meta-box/inc/about/sections/extensions.php:38
msgid ""
"Create repeatable groups of custom fields for better appearance and "
"structure."
msgstr ""

#: extensions/meta-box/inc/about/sections/extensions.php:50
msgid "Create a powerful settings page for your theme, plugin or website."
msgstr ""

#: extensions/meta-box/inc/about/sections/extensions.php:62
msgid ""
"Control the visibility of meta boxes and fields or even HTML elements with "
"ease."
msgstr ""

#: extensions/meta-box/inc/about/sections/extensions.php:74
msgid ""
"Create register, login and edit user profile forms in the frontend. Embed "
"everywhere with shortcodes."
msgstr ""

#: extensions/meta-box/inc/about/sections/extensions.php:86
msgid ""
"Create frontend forms for users to submit custom content. Embed everywhere "
"with shortcode."
msgstr ""

#: extensions/meta-box/inc/about/sections/extensions.php:98
msgid ""
"Save custom fields data to custom table. Reduce database size and increase "
"performance."
msgstr ""

#: extensions/meta-box/inc/about/sections/extensions.php:110
msgid "Create many-to-many relationships between posts, terms and users."
msgstr ""

#: extensions/meta-box/inc/about/sections/extensions.php:118
msgid "View all extensions"
msgstr ""

#: extensions/meta-box/inc/about/sections/getting-started-pro.php:4
msgid ""
"Please follow this video tutorial to get started with Meta Box and "
"extensions:"
msgstr ""

#: extensions/meta-box/inc/about/sections/getting-started-pro.php:8
msgid "Tutorials"
msgstr ""

#: extensions/meta-box/inc/about/sections/getting-started-pro.php:9
msgid ""
"We've made bunches of tutorials that come with videos, let's take a look to "
"have detailed guides to create custom fields and apply them in real cases."
msgstr ""

#: extensions/meta-box/inc/about/sections/getting-started-pro.php:11
msgid "Beginners"
msgstr ""

#: extensions/meta-box/inc/about/sections/getting-started-pro.php:11
msgid "Let’s start with some basic practices with Meta Box."
msgstr ""

#: extensions/meta-box/inc/about/sections/getting-started-pro.php:12
msgid "Case Studies"
msgstr ""

#: extensions/meta-box/inc/about/sections/getting-started-pro.php:12
msgid ""
"See how to use Meta Box in the real case studies with comprehensive "
"tutorials."
msgstr ""

#: extensions/meta-box/inc/about/sections/getting-started-pro.php:13
msgid "General Guide"
msgstr ""

#: extensions/meta-box/inc/about/sections/getting-started-pro.php:13
msgid "See how to use Meta box in common tasks."
msgstr ""

#: extensions/meta-box/inc/about/sections/getting-started-pro.php:14
#: extensions/meta-box/inc/about/sections/getting-started.php:15
#: extensions/meta-box/inc/about/sections/tabs.php:6
msgid "Extensions"
msgstr ""

#: extensions/meta-box/inc/about/sections/getting-started-pro.php:14
msgid ""
"Learn about Meta Box extensions, what features they offer and how to use "
"them."
msgstr ""

#: extensions/meta-box/inc/about/sections/getting-started-pro.php:15
msgid "Page Builders"
msgstr ""

#: extensions/meta-box/inc/about/sections/getting-started-pro.php:15
msgid ""
"Tutorials on combining Meta Box and other builders or tools for real case "
"studies."
msgstr ""

#: extensions/meta-box/inc/about/sections/getting-started-pro.php:16
msgid "MB Views"
msgstr ""

#: extensions/meta-box/inc/about/sections/getting-started-pro.php:16
msgid ""
"Build front-end templates for WordPress without touching theme files. "
"Support Twig and all field types."
msgstr ""

#: extensions/meta-box/inc/about/sections/getting-started.php:6
msgid "Getting Started With Online Generator"
msgstr ""

#: extensions/meta-box/inc/about/sections/getting-started.php:7
msgid ""
"Online Generator is a free tool to help you create and set up custom fields "
"using a simple, friendly user interface. With it, you can add fields, set "
"options and generate needed code that's ready to copy and paste."
msgstr ""

#: extensions/meta-box/inc/about/sections/getting-started.php:9
msgid "online generator"
msgstr ""

#: extensions/meta-box/inc/about/sections/getting-started.php:11
msgid "Go to Online Generator"
msgstr ""

#: extensions/meta-box/inc/about/sections/getting-started.php:16
msgid ""
"Wanna see more features that transform your WordPress website into a "
"powerful CMS? Check out some extensions below:"
msgstr ""

#: extensions/meta-box/inc/about/sections/getting-started.php:18
msgid "Meta Box Builder"
msgstr ""

#: extensions/meta-box/inc/about/sections/getting-started.php:18
msgid "Build meta boxes and fields with UI."
msgstr ""

#: extensions/meta-box/inc/about/sections/getting-started.php:19
msgid "Meta Box Group"
msgstr ""

#: extensions/meta-box/inc/about/sections/getting-started.php:19
msgid "Organize fields into repeatable groups."
msgstr ""

#: extensions/meta-box/inc/about/sections/getting-started.php:20
msgid "Meta Box Conditional Logic"
msgstr ""

#: extensions/meta-box/inc/about/sections/getting-started.php:20
msgid "Control the visibility of fields."
msgstr ""

#: extensions/meta-box/inc/about/sections/getting-started.php:21
msgid "MB Settings Page"
msgstr ""

#: extensions/meta-box/inc/about/sections/getting-started.php:21
msgid "Create settings pages/Customizer options."
msgstr ""

#: extensions/meta-box/inc/about/sections/getting-started.php:27
msgid "More Extensions"
msgstr ""

#: extensions/meta-box/inc/about/sections/products.php:5
msgid "Our WordPress Products"
msgstr ""

#: extensions/meta-box/inc/about/sections/products.php:8
msgid "Like this plugin? Check out our other WordPress products:"
msgstr ""

#: extensions/meta-box/inc/about/sections/products.php:9
msgid "Automated & fast SEO plugin for WordPress"
msgstr ""

#: extensions/meta-box/inc/about/sections/products.php:10
msgid "The best schema plugin for WordPress"
msgstr ""

#: extensions/meta-box/inc/about/sections/products.php:11
msgid "Simple, elegant and clean WordPress themes"
msgstr ""

#: extensions/meta-box/inc/about/sections/review.php:5
msgid "Write a review for Meta Box"
msgstr ""

#: extensions/meta-box/inc/about/sections/review.php:8
msgid ""
"If you like Meta Box, please write a review on WordPress.org to help us "
"spread the word. We really appreciate that!"
msgstr ""

#: extensions/meta-box/inc/about/sections/review.php:9
msgid "Write a review"
msgstr ""

#: extensions/meta-box/inc/about/sections/support.php:12
#, php-format
msgid ""
"Still need help with Meta Box? We offer excellent support for you. But don't "
"forget to check our <a href=\"%s\">documentation</a> first."
msgstr ""

#: extensions/meta-box/inc/about/sections/support.php:17
msgid "Free Support"
msgstr ""

#: extensions/meta-box/inc/about/sections/support.php:18
msgid ""
"If you have any question about how to use the plugin, please open a new "
"topic on WordPress.org support forum or open a new issue on Github "
"(preferable). We will try to answer as soon as we can."
msgstr ""

#: extensions/meta-box/inc/about/sections/support.php:19
msgid "Go to Github"
msgstr ""

#: extensions/meta-box/inc/about/sections/support.php:20
msgid "Go to WordPress.org"
msgstr ""

#: extensions/meta-box/inc/about/sections/support.php:24
msgid "Premium Support"
msgstr ""

#: extensions/meta-box/inc/about/sections/support.php:25
msgid ""
"For users that have bought premium extensions, the support is provided in "
"the Meta Box Support forum. Any question will be answered with technical "
"details within 24 hours."
msgstr ""

#: extensions/meta-box/inc/about/sections/support.php:26
msgid "Go to support forum"
msgstr ""

#: extensions/meta-box/inc/about/sections/tabs.php:4
msgid "Getting Started"
msgstr ""

#: extensions/meta-box/inc/about/sections/tabs.php:7
#: extensions/meta-box/inc/about/sections/welcome.php:15
msgid "Support"
msgstr ""

#: extensions/meta-box/inc/about/sections/upgrade.php:4
msgid "Upgrade to Meta Box PRO"
msgstr ""

#: extensions/meta-box/inc/about/sections/upgrade.php:5
msgid "Please upgrade to the PRO plan to unlock more awesome features."
msgstr ""

#: extensions/meta-box/inc/about/sections/upgrade.php:7
msgid ""
"Create custom fields with drag-n-drop interface - no coding knowledge "
"required!"
msgstr ""

#: extensions/meta-box/inc/about/sections/upgrade.php:8
msgid "Add custom fields to taxonomies or user profile."
msgstr ""

#: extensions/meta-box/inc/about/sections/upgrade.php:9
msgid "Create custom settings pages."
msgstr ""

#: extensions/meta-box/inc/about/sections/upgrade.php:10
msgid "Create frontend submission forms."
msgstr ""

#: extensions/meta-box/inc/about/sections/upgrade.php:11
msgid "Save custom fields in custom tables."
msgstr ""

#: extensions/meta-box/inc/about/sections/upgrade.php:12
msgid "And much more!"
msgstr ""

#: extensions/meta-box/inc/about/sections/upgrade.php:14
msgid "Get Meta Box PRO now"
msgstr ""

#: extensions/meta-box/inc/about/sections/welcome.php:8
#, php-format
msgid "Welcome to %s"
msgstr ""

#: extensions/meta-box/inc/about/sections/welcome.php:11
msgid ""
"Meta Box is a free Gutenberg and GDPR-compatible WordPress custom fields "
"plugin and framework that makes quick work of customizing a website with—you "
"guessed it—meta boxes and custom fields in WordPress. Follow the instruction "
"below to get started!"
msgstr ""

#: extensions/meta-box/inc/about/sections/welcome.php:14
msgid "Documentation"
msgstr ""

#: extensions/meta-box/inc/about/sections/welcome.php:16
msgid "Facebook Group"
msgstr ""

#: extensions/meta-box/inc/about/sections/welcome.php:17
msgid "Youtube Channel"
msgstr ""

#: extensions/meta-box/inc/core.php:20
msgid "Docs"
msgstr ""

#: extensions/meta-box/inc/field.php:309
msgid "+ Add more"
msgstr ""

#: extensions/meta-box/inc/fields/background.php:61
msgid "Background Image"
msgstr ""

#: extensions/meta-box/inc/fields/background.php:73
msgid "-- Repeat --"
msgstr ""

#: extensions/meta-box/inc/fields/background.php:75
msgid "No Repeat"
msgstr ""

#: extensions/meta-box/inc/fields/background.php:76
msgid "Repeat All"
msgstr ""

#: extensions/meta-box/inc/fields/background.php:77
msgid "Repeat Horizontally"
msgstr ""

#: extensions/meta-box/inc/fields/background.php:78
msgid "Repeat Vertically"
msgstr ""

#: extensions/meta-box/inc/fields/background.php:79
#: extensions/meta-box/inc/fields/background.php:113
#: extensions/meta-box/inc/fields/background.php:125
msgid "Inherit"
msgstr ""

#: extensions/meta-box/inc/fields/background.php:89
msgid "-- Position --"
msgstr ""

#: extensions/meta-box/inc/fields/background.php:109
msgid "-- Attachment --"
msgstr ""

#: extensions/meta-box/inc/fields/background.php:111
msgid "Fixed"
msgstr ""

#: extensions/meta-box/inc/fields/background.php:112
msgid "Scroll"
msgstr ""

#: extensions/meta-box/inc/fields/background.php:123
msgid "-- Size --"
msgstr ""

#: extensions/meta-box/inc/fields/button.php:28
msgid "Click me"
msgstr ""

#: extensions/meta-box/inc/fields/file-input.php:17
msgid "Select File"
msgstr ""

#: extensions/meta-box/inc/fields/file.php:15
#, php-format
msgid "You may only upload maximum %d file"
msgstr ""

#: extensions/meta-box/inc/fields/file.php:17
#, php-format
msgid "You may only upload maximum %d files"
msgstr ""

#: extensions/meta-box/inc/fields/file.php:48
msgid "Error: Invalid file"
msgstr ""

#: extensions/meta-box/inc/fields/file.php:61
msgid "Error: Cannot delete file"
msgstr ""

#: extensions/meta-box/inc/fields/file.php:86
msgctxt "file upload"
msgid "+ Add new file"
msgstr ""

#: extensions/meta-box/inc/fields/file.php:166
msgctxt "file upload"
msgid "Delete"
msgstr ""

#: extensions/meta-box/inc/fields/file.php:167
msgctxt "file upload"
msgid "Edit"
msgstr ""

#: extensions/meta-box/inc/fields/icon.php:223
msgid "Select an icon"
msgstr ""

#: extensions/meta-box/inc/fields/input-list.php:83
msgid "Toggle All"
msgstr ""

#: extensions/meta-box/inc/fields/key-value.php:94
msgid "Key"
msgstr ""

#: extensions/meta-box/inc/fields/key-value.php:95
msgid "Value"
msgstr ""

#: extensions/meta-box/inc/fields/map.php:29
#: extensions/meta-box/inc/fields/osm.php:16
msgid "No results found"
msgstr ""

#: extensions/meta-box/inc/fields/media.php:20
msgctxt "media"
msgid "+ Add Media"
msgstr ""

#: extensions/meta-box/inc/fields/media.php:21
msgctxt "media"
msgid " file"
msgstr ""

#: extensions/meta-box/inc/fields/media.php:22
msgctxt "media"
msgid " files"
msgstr ""

#: extensions/meta-box/inc/fields/media.php:23
msgctxt "media"
msgid "Remove"
msgstr ""

#: extensions/meta-box/inc/fields/media.php:24
msgctxt "media"
msgid "Edit"
msgstr ""

#: extensions/meta-box/inc/fields/media.php:25
msgctxt "media"
msgid "View"
msgstr ""

#: extensions/meta-box/inc/fields/media.php:26
msgctxt "media"
msgid "No Title"
msgstr ""

#: extensions/meta-box/inc/fields/media.php:29
msgctxt "media"
msgid "Select Files"
msgstr ""

#: extensions/meta-box/inc/fields/media.php:30
msgctxt "media"
msgid "or"
msgstr ""

#: extensions/meta-box/inc/fields/media.php:31
msgctxt "media"
msgid "Drop files here to upload"
msgstr ""

#: extensions/meta-box/inc/fields/oembed.php:18
msgid "Embed HTML not available."
msgstr ""

#: extensions/meta-box/inc/fields/post.php:69
msgid "Select a post"
msgstr ""

#: extensions/meta-box/inc/fields/post.php:75
#: extensions/meta-box/inc/fields/taxonomy.php:88
#, php-format
msgid "Select a %s"
msgstr ""

#: extensions/meta-box/inc/fields/post.php:136
#: extensions/meta-box/inc/fields/taxonomy.php:134
#: extensions/meta-box/inc/fields/user.php:135
msgid "(No title)"
msgstr ""

#: extensions/meta-box/inc/fields/select-advanced.php:44
msgid "Select an item"
msgstr ""

#: extensions/meta-box/inc/fields/sidebar.php:10
msgid "Select a sidebar"
msgstr ""

#: extensions/meta-box/inc/fields/taxonomy.php:84
msgid "Select a term"
msgstr ""

#: extensions/meta-box/inc/fields/user.php:75
msgid "Select a user"
msgstr ""

#: extensions/meta-box/inc/fields/user.php:189
msgid "Add New User"
msgstr ""

#: extensions/meta-box/inc/meta-box.php:275
msgid "Meta Box Title"
msgstr ""

#: extensions/meta-box/inc/validation.php:66
msgid "Please correct the errors highlighted below and try again."
msgstr ""

#: extensions/meta-box/js/select2/select2.min.js:2
msgid ""
"<span class=\"select2-dropdown\"><span class=\"select2-results\"></span></"
"span>"
msgstr ""

#: extensions/meta-box/src/Updater/Checker.php:104
msgid ""
"UPDATE UNAVAILABLE! Please enter a valid license key to enable automatic "
"updates."
msgstr ""

#: extensions/meta-box/src/Updater/Notification.php:59
#, php-format
msgid ""
"You have not set your Meta Box license key yet, which means you are missing "
"out on automatic updates and support! Please <a href=\"%1$s\">enter your "
"license key</a> or <a href=\"%2$s\" target=\"_blank\">get a new one here</a>."
msgstr ""

#: extensions/meta-box/src/Updater/Notification.php:61
#: extensions/meta-box/src/Updater/Notification.php:63
#, php-format
msgid ""
"Your license key for Meta Box is <b>invalid</b>. Please <a "
"href=\"%1$s\">update your license key</a> or <a href=\"%2$s\" "
"target=\"_blank\">get a new one</a> to enable automatic updates."
msgstr ""

#: extensions/meta-box/src/Updater/Notification.php:65
#, php-format
msgid ""
"Your license key for Meta Box is <b>expired</b>. Please <a href=\"%3$s\" "
"target=\"_blank\">renew your license</a> to get automatic updates and "
"premium support."
msgstr ""

#: extensions/meta-box/src/Updater/Notification.php:89
#, php-format
msgid ""
"Please <a href=\"%1$s\">enter your license key</a> or <a href=\"%2$s\" "
"target=\"_blank\">get a new one here</a>."
msgstr ""

#: extensions/meta-box/src/Updater/Notification.php:91
#: extensions/meta-box/src/Updater/Notification.php:93
#, php-format
msgid ""
"Your license key is <b>invalid</b>. Please <a href=\"%1$s\">update your "
"license key</a> or <a href=\"%2$s\" target=\"_blank\">get a new one here</a>."
msgstr ""

#: extensions/meta-box/src/Updater/Notification.php:95
#, php-format
msgid ""
"Your license key is <b>expired</b>. Please <a href=\"%3$s\" "
"target=\"_blank\">renew your license</a>."
msgstr ""

#: extensions/meta-box/src/Updater/Notification.php:111
msgid "Activate License"
msgstr ""

#: extensions/meta-box/src/Updater/Notification.php:111
msgid "Update License"
msgstr ""

#: extensions/meta-box/src/Updater/Settings.php:39
#: extensions/meta-box/src/Updater/Settings.php:54
msgid "Meta Box License"
msgstr ""

#: extensions/meta-box/src/Updater/Settings.php:55
msgid ""
"Please enter your license key to enable automatic updates for Meta Box "
"extensions."
msgstr ""

#: extensions/meta-box/src/Updater/Settings.php:60
#, php-format
msgid ""
"To get the license key, visit the <a href=\"%1$s\" target=\"_blank\">My "
"Account</a> page on metabox.io website. If you have not purchased any "
"extension yet, please <a href=\"%2$s\" target=\"_blank\">get a new license "
"here</a>."
msgstr ""

#: extensions/meta-box/src/Updater/Settings.php:72
msgid "License Key"
msgstr ""

#: extensions/meta-box/src/Updater/Settings.php:76
#: extensions/meta-box/src/Updater/Settings.php:77
msgid "Your license key is <b style=\"color: #d63638\">invalid</b>."
msgstr ""

#: extensions/meta-box/src/Updater/Settings.php:78
msgid "Your license key is <b style=\"color: #d63638\">expired</b>."
msgstr ""

#: extensions/meta-box/src/Updater/Settings.php:79
msgid "Your license key is <b style=\"color: #00a32a\">active</b>."
msgstr ""

#: extensions/meta-box/src/Updater/Settings.php:123
msgid ""
"Something wrong with the connection to metabox.io. Please try again later."
msgstr ""

#: extensions/meta-box/src/Updater/Settings.php:125
msgid "Your license is activated."
msgstr ""

#: extensions/meta-box/src/Updater/Settings.php:128
#, php-format
msgid ""
"License expired. Please renew on the <a href=\"%s\" target=\"_blank\">My "
"Account</a> page on metabox.io website."
msgstr ""

#: extensions/meta-box/src/Updater/Settings.php:134
#, php-format
msgid ""
"Invalid license. Please <a href=\"%1$s\" target=\"_blank\">check again</a> "
"or <a href=\"%2$s\" target=\"_blank\">get a new license here</a>."
msgstr ""

#: functions/functions.php:236
msgid "Active & Sold"
msgstr ""

#: functions/functions.php:238
msgid "Sold"
msgstr ""

#: functions/functions.php:249
msgid "Title - ASC"
msgstr ""

#: functions/functions.php:250
msgid "Title - DESC"
msgstr ""

#: functions/functions.php:255
msgid "Featured on Top"
msgstr ""

#: functions/functions.php:256
msgid "Featured on Top - Randomly"
msgstr ""

#: functions/functions.php:274
msgid "Base currency to get rates not found in database"
msgstr ""

#: functions/functions.php:318
msgid "Currency was not exist or found in database."
msgstr ""

#: functions/functions.php:326
msgid "Amount to covert is not number, it must be number."
msgstr ""

#: functions/functions.php:341
msgid ""
"Look like your API is not valid, There was a problem to get currency data "
"from database."
msgstr ""

#: functions/functions.php:386
msgid ""
"Please pass valid currency code for argument and it must be a string of "
"three characters long"
msgstr ""

#: functions/functions.php:396
msgid "Currency could not be found"
msgstr ""

#: functions/functions.php:697 functions/functions.php:698
msgctxt "listing post status"
msgid "Draft"
msgstr ""

#: functions/functions.php:701 functions/functions.php:702
msgctxt "listing post status"
msgid "Active"
msgstr ""

#: functions/functions.php:705 functions/functions.php:734
msgctxt "listing post status"
msgid "Pending"
msgstr ""

#: functions/functions.php:706
msgctxt "listing post status"
msgid "Pending approval"
msgstr ""

#: functions/functions.php:709 functions/functions.php:710
msgctxt "listing post status"
msgid "Expired"
msgstr ""

#: functions/functions.php:713 functions/functions.php:714
msgctxt "listing post status"
msgid "Disapproved"
msgstr ""

#: functions/functions.php:717 functions/functions.php:718
msgctxt "listing post status"
msgid "On Hold"
msgstr ""

#: functions/functions.php:721 functions/functions.php:722
msgctxt "listing post status"
msgid "Sold"
msgstr ""

#: functions/functions.php:725 functions/functions.php:726
msgctxt "listing post status"
msgid "Preview"
msgstr ""

#: functions/functions.php:731
#, php-format
msgid "Preview <span class=\"count\">(%s)</span>"
msgid_plural "Preview <span class=\"count\">(%s)</span>"
msgstr[0] ""
msgstr[1] ""

#: functions/functions.php:735
msgctxt "listing post status"
msgid "Pending payment"
msgstr ""

#: functions/functions.php:740
#, php-format
msgid "Pending Payment <span class=\"count\">(%s)</span>"
msgid_plural "Pending Payment <span class=\"count\">(%s)</span>"
msgstr[0] ""
msgstr[1] ""

#: functions/functions.php:743 functions/functions.php:744
msgctxt "listing post status"
msgid "Trash"
msgstr ""

#: functions/helpers.php:440 functions/helpers.php:530
msgid "View All"
msgstr ""

#: shortcodes/price-table.php:113
msgid "Images"
msgstr ""

#: statistics/houzez-statistics.php:9
msgid "Houzez Statistics"
msgstr ""

#: statistics/houzez-statistics.php:10 statistics/houzez-statistics.php:108
msgid "Most Viewed Properties"
msgstr ""

#: statistics/houzez-statistics.php:10
msgid "Most Viewed"
msgstr ""

#: statistics/houzez-statistics.php:11 statistics/houzez-statistics.php:233
msgid "Most Favourite Properties"
msgstr ""

#: statistics/houzez-statistics.php:11
msgid "Most Favourite"
msgstr ""

#: statistics/houzez-statistics.php:12 statistics/houzez-statistics.php:348
msgid "Saved Searches"
msgstr ""

#: statistics/houzez-statistics.php:40 statistics/houzez-statistics.php:95
#: statistics/houzez-statistics.php:215 statistics/houzez-statistics.php:343
#: statistics/houzez-statistics.php:574
msgid "You do not have sufficient permissions to access this page."
msgstr ""

#: statistics/houzez-statistics.php:48
msgid "Houzez Statistic"
msgstr ""

#: statistics/houzez-statistics.php:52 statistics/houzez-statistics.php:78
msgid "Total Views Today"
msgstr ""

#: statistics/houzez-statistics.php:54
msgid "Users"
msgstr ""

#: statistics/houzez-statistics.php:59
msgid "Buyers"
msgstr ""

#: statistics/houzez-statistics.php:65
msgid "Sellers"
msgstr ""

#: statistics/houzez-statistics.php:68
msgid "Owners"
msgstr ""

#: statistics/houzez-statistics.php:71
msgid "Managers"
msgstr ""

#: statistics/houzez-statistics.php:121 statistics/houzez-statistics.php:198
msgid "Views Count"
msgstr ""

#: statistics/houzez-statistics.php:177 statistics/houzez-statistics.php:304
#: statistics/houzez-statistics.php:541
msgid "View"
msgstr ""

#: statistics/houzez-statistics.php:246
msgid "Favourite Count "
msgstr ""

#: statistics/houzez-statistics.php:326
msgid "Favourite Count"
msgstr ""

#: statistics/houzez-statistics.php:353 statistics/houzez-statistics.php:556
msgid "Search Query"
msgstr ""

#: statistics/houzez-statistics.php:402 statistics/houzez-statistics.php:582
msgid "Location"
msgstr ""

#: statistics/houzez-statistics.php:426
msgid "Neighborhood"
msgstr ""

#: statistics/houzez-statistics.php:548
msgid "No Search found"
msgstr ""

#: statistics/houzez-statistics.php:586
msgid "Feature"
msgstr ""

#: statistics/houzez-statistics.php:587 statistics/houzez-statistics.php:588
msgid "Max Size"
msgstr ""

#: statistics/houzez-statistics.php:596
msgid "Houzez Searches"
msgstr ""

#: statistics/houzez-statistics.php:601
msgid "Search Keyworks"
msgstr ""

#: templates/currency/currency-list.php:14
msgid "Total Currencies"
msgstr ""

#: templates/currency/currency-list.php:23
msgid "Before Position"
msgstr ""

#: templates/currency/currency-list.php:32
msgid "After Position"
msgstr ""

#: templates/currency/currency-list.php:57 templates/currency/form.php:82
msgid "Currency Name"
msgstr ""

#: templates/currency/currency-list.php:61
msgid "Code"
msgstr ""

#: templates/currency/currency-list.php:65
msgid "Symbol"
msgstr ""

#: templates/currency/currency-list.php:73
msgid "Decimals"
msgstr ""

#: templates/currency/currency-list.php:77
msgid "Separators"
msgstr ""

#: templates/currency/currency-list.php:102
#: templates/currency/currency-list.php:200
#: templates/fields-builder/index.php:90 templates/fields-builder/index.php:178
msgid "Copy to clipboard"
msgstr ""

#: templates/currency/currency-list.php:125
msgid "Decimal:"
msgstr ""

#: templates/currency/currency-list.php:128
msgid "Thousands:"
msgstr ""

#: templates/currency/currency-list.php:136
msgid "Edit currency"
msgstr ""

#: templates/currency/currency-list.php:140
msgid "Delete currency"
msgstr ""

#: templates/currency/currency-list.php:141
msgid "Are you sure you want to delete this currency?"
msgstr ""

#: templates/currency/currency-list.php:158
msgid "No Currencies Found"
msgstr ""

#: templates/currency/currency-list.php:159
msgid ""
"Start adding currencies to support multiple currency options for your "
"properties."
msgstr ""

#: templates/currency/currency-list.php:162
msgid "Add Your First Currency"
msgstr ""

#: templates/currency/currency-list.php:194
#: templates/fields-builder/index.php:172
msgid "Copied!"
msgstr ""

#: templates/currency/currency-list.php:209
#: templates/fields-builder/index.php:187
msgid "Please copy manually"
msgstr ""

#: templates/currency/form.php:5 templates/currency/form.php:6
msgid "Update Currency"
msgstr ""

#: templates/currency/form.php:9
msgid "Update the currency settings below and save your changes."
msgstr ""

#: templates/currency/form.php:11
msgid "Create New Currency"
msgstr ""

#: templates/currency/form.php:12
msgid "Create Currency"
msgstr ""

#: templates/currency/form.php:14
msgid "Fill in the details below to create a new currency for your properties."
msgstr ""

#: templates/currency/form.php:24
msgid "Currency has been added successfully!"
msgstr ""

#: templates/currency/form.php:27
msgid "Currency has been updated successfully!"
msgstr ""

#: templates/currency/form.php:30 templates/fields-builder/fields-form.php:30
msgid "There was an error processing your request. Please try again."
msgstr ""

#: templates/currency/form.php:55
msgid "Back to Currencies List"
msgstr ""

#: templates/currency/form.php:89
msgid "Enter currency name (e.g., United States Dollar)"
msgstr ""

#: templates/currency/form.php:93
msgid "The full name of the currency."
msgstr ""

#: templates/currency/form.php:101
msgid "Currency Code"
msgstr ""

#: templates/currency/form.php:108
msgid "Enter currency code (e.g., USD)"
msgstr ""

#: templates/currency/form.php:114
msgid "3-letter ISO currency code (e.g., USD, EUR, GBP)."
msgstr ""

#: templates/currency/form.php:122
msgid "Currency Symbol"
msgstr ""

#: templates/currency/form.php:129
msgid "Enter currency symbol (e.g., $)"
msgstr ""

#: templates/currency/form.php:133
msgid "The symbol used to represent this currency."
msgstr ""

#: templates/currency/form.php:141
msgid "Symbol Position"
msgstr ""

#: templates/currency/form.php:148
msgid "-- Choose position --"
msgstr ""

#: templates/currency/form.php:150
msgid "Before ($100)"
msgstr ""

#: templates/currency/form.php:153
msgid "After (100$)"
msgstr ""

#: templates/currency/form.php:157
msgid "Where to display the currency symbol relative to the amount."
msgstr ""

#: templates/currency/form.php:165
msgid "Decimal Places"
msgstr ""

#: templates/currency/form.php:174
msgid "decimal place"
msgstr ""

#: templates/currency/form.php:174
msgid "decimal places"
msgstr ""

#: templates/currency/form.php:179
msgid "Number of digits after the decimal point."
msgstr ""

#: templates/currency/form.php:187
msgid "Decimal Separator"
msgstr ""

#: templates/currency/form.php:194
msgid "Enter decimal separator (e.g., .)"
msgstr ""

#: templates/currency/form.php:199
msgid "Character used to separate decimal places (e.g., . or ,)."
msgstr ""

#: templates/currency/form.php:207
msgid "Thousands Separator"
msgstr ""

#: templates/currency/form.php:214
msgid "Enter thousands separator (e.g., ,)"
msgstr ""

#: templates/currency/form.php:219
msgid "Character used to separate thousands (e.g., , or space)."
msgstr ""

#: templates/currency/form.php:263 templates/fields-builder/fields-form.php:231
msgid "Processing..."
msgstr ""

#: templates/currency/form.php:269 templates/fields-builder/fields-form.php:237
msgid "Processing your request..."
msgstr ""

#: templates/fields-builder/fields-form.php:5
#: templates/fields-builder/fields-form.php:6
msgid "Update Field"
msgstr ""

#: templates/fields-builder/fields-form.php:9
msgid "Update the field settings below and save your changes."
msgstr ""

#: templates/fields-builder/fields-form.php:11
msgid "Create New Field"
msgstr ""

#: templates/fields-builder/fields-form.php:12
msgid "Create Field"
msgstr ""

#: templates/fields-builder/fields-form.php:14
msgid ""
"Fill in the details below to create a new custom field for your properties."
msgstr ""

#: templates/fields-builder/fields-form.php:24
msgid "Field has been added successfully!"
msgstr ""

#: templates/fields-builder/fields-form.php:27
msgid "Field has been updated successfully!"
msgstr ""

#: templates/fields-builder/fields-form.php:33
msgid "A field with this name already exists. Please choose a different name."
msgstr ""

#: templates/fields-builder/fields-form.php:58
msgid "Back to Fields List"
msgstr ""

#: templates/fields-builder/fields-form.php:85
#: templates/fields-builder/index.php:45
msgid "Field Name"
msgstr ""

#: templates/fields-builder/fields-form.php:92
msgid "Enter field name"
msgstr ""

#: templates/fields-builder/fields-form.php:96
msgid "This will be the display name for your custom field."
msgstr ""

#: templates/fields-builder/fields-form.php:104
msgid "Placeholder Text"
msgstr ""

#: templates/fields-builder/fields-form.php:110
msgid "Enter field placeholder"
msgstr ""

#: templates/fields-builder/fields-form.php:113
msgid "Optional placeholder text that appears inside the field."
msgstr ""

#: templates/fields-builder/fields-form.php:128
msgid "-- Choose field type --"
msgstr ""

#: templates/fields-builder/fields-form.php:136
msgid "Select the type of input field you want to create."
msgstr ""

#: templates/fields-builder/fields-form.php:144
msgid "Available for Search"
msgstr ""

#: templates/fields-builder/fields-form.php:159
msgid "Whether this field should be available in property search forms."
msgstr ""

#: templates/fields-builder/fields-form.php:167
msgid "Field Options"
msgstr ""

#: templates/fields-builder/fields-form.php:173
msgid "Please add comma separated options. Example: One, Two, Three"
msgstr ""

#: templates/fields-builder/fields-form.php:178
msgid "Enter comma-separated options for select, radio, or checkbox fields."
msgstr ""

#: templates/fields-builder/index.php:11
msgid "Custom Fields"
msgstr ""

#: templates/fields-builder/index.php:20
msgid "Search Fields"
msgstr ""

#: templates/fields-builder/index.php:30
msgid "Custom Fields Management"
msgstr ""

#: templates/fields-builder/index.php:49
msgid "Field ID"
msgstr ""

#: templates/fields-builder/index.php:114
msgid "Edit field"
msgstr ""

#: templates/fields-builder/index.php:118
msgid "Delete field"
msgstr ""

#: templates/fields-builder/index.php:119
msgid "Are you sure you want to delete this field?"
msgstr ""

#: templates/fields-builder/index.php:136
msgid "No Custom Fields Found"
msgstr ""

#: templates/fields-builder/index.php:137
msgid "Start building your custom fields to enhance your property listings."
msgstr ""

#: templates/fields-builder/index.php:140
msgid "Create Your First Field"
msgstr ""

#: templates/fields-builder/multiple.php:15
msgid "Enter Value"
msgstr ""

#: templates/locations/form.php:9
msgid "Locations imported successfully!"
msgstr ""

#: templates/locations/form.php:12
msgid "Import failed. Please check your CSV file and try again."
msgstr ""

#: templates/locations/form.php:35
msgid "Import Locations"
msgstr ""

#: templates/locations/form.php:40 templates/locations/form.php:149
msgid "Fetch CSV Headers"
msgstr ""

#: templates/locations/form.php:44
msgid "Refresh Page"
msgstr ""

#: templates/locations/form.php:67
msgid "Countries"
msgstr ""

#: templates/locations/form.php:77
msgid "States/Counties"
msgstr ""

#: templates/locations/form.php:97
msgid "Areas"
msgstr ""

#: templates/locations/form.php:107
msgid "CSV File Upload"
msgstr ""

#: templates/locations/form.php:110 templates/locations/form.php:287
msgid "No File Selected"
msgstr ""

#: templates/locations/form.php:115
msgid ""
"Upload a CSV file containing location data to import countries, states, "
"cities, and areas into your Houzez website. The system will automatically "
"create hierarchical relationships between locations."
msgstr ""

#: templates/locations/form.php:125
msgid "CSV File"
msgstr ""

#: templates/locations/form.php:129
msgid "Select a CSV file..."
msgstr ""

#: templates/locations/form.php:132
msgid "Choose File"
msgstr ""

#: templates/locations/form.php:135
msgid ""
"Select a CSV file containing location data. The file should include columns "
"for country, state, city, and area information."
msgstr ""

#: templates/locations/form.php:143
msgid "Clear Selection"
msgstr ""

#: templates/locations/form.php:162
msgid "Field Mapping"
msgstr ""

#: templates/locations/form.php:165
msgid "Configure Mapping"
msgstr ""

#: templates/locations/form.php:170
msgid ""
"Map the columns from your CSV file to the corresponding location fields. At "
"least one field mapping is required to proceed with the import."
msgstr ""

#: templates/locations/form.php:182
msgid "Import Results"
msgstr ""

#: templates/locations/form.php:196
msgid "CSV Format Requirements"
msgstr ""

#: templates/locations/form.php:206
msgid "CSV Structure"
msgstr ""

#: templates/locations/form.php:207
msgid ""
"Your CSV file should contain columns for location data. Common column names "
"include Country, State, City, and Area. The first row should contain column "
"headers."
msgstr ""

#: templates/locations/form.php:216
msgid "Hierarchical Import"
msgstr ""

#: templates/locations/form.php:217
msgid ""
"The system automatically creates hierarchical relationships: Areas belong to "
"Cities, Cities belong to States, and States belong to Countries."
msgstr ""

#: templates/locations/form.php:226
msgid "Smart Import"
msgstr ""

#: templates/locations/form.php:227
msgid ""
"Existing locations are automatically detected and updated. New locations are "
"created as needed. Duplicate entries are handled intelligently."
msgstr ""

#: templates/locations/form.php:234
msgid "Sample CSV Format:"
msgstr ""

#: templates/locations/form.php:283
msgid "File Selected"
msgstr ""
