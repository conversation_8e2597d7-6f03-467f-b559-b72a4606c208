msgid ""
msgstr ""
"Project-Id-Version: Ho<PERSON>z Theme Functionality Plugin\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-23 11:52+0500\n"
"PO-Revision-Date: \n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: ru\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && "
"n%10<=4 && (n%100<12 || n%100>14) ? 1 : 2);\n"
"X-Generator: Poedit 3.6\n"

#: classes/class-agency-post-type.php:36 classes/class-agency-post-type.php:42
#: classes/class-agency-post-type.php:48 classes/class-menu.php:62
#: classes/class-menu.php:63 classes/class-post-types.php:65
#: statistics/houzez-statistics.php:62
msgid "Agencies"
msgstr "агентства"

#: classes/class-agency-post-type.php:37 classes/class-image-sizes.php:145
#: classes/class-image-sizes.php:1825
msgid "Agency"
msgstr "Агентство"

#: classes/class-agency-post-type.php:38 classes/class-agency-post-type.php:39
msgid "Add New Agency"
msgstr "Добавить новое агентство"

#: classes/class-agency-post-type.php:40
msgid "Edit Agency"
msgstr "Редактировать агентство"

#: classes/class-agency-post-type.php:41
msgid "New Agency"
msgstr "Новое агентство"

#: classes/class-agency-post-type.php:43
msgid "View Agency"
msgstr "Просмотр агентства"

#: classes/class-agency-post-type.php:44
msgid "Search Agency"
msgstr "Поисковое агентство"

#: classes/class-agency-post-type.php:45
msgid "No agencies found"
msgstr "Агентства не найдены"

#: classes/class-agency-post-type.php:46
msgid "No agencies found in Trash"
msgstr "Агентств не найдено в корзине"

#: classes/class-agency-post-type.php:87
msgid "Agency ID"
msgstr "ID агентства"

#: classes/class-agency-post-type.php:88
#: classes/class-property-post-type.php:509
#: classes/class-reviews-post-type.php:102
#: classes/class-user-packages-post-type.php:85
#: elementor/traits/Houzez_Testimonials_Traits.php:38
#: elementor/traits/Houzez_Testimonials_Traits.php:162
#: elementor/widgets/advanced-search.php:87
#: elementor/widgets/agents-grid.php:168 elementor/widgets/agents.php:166
#: elementor/widgets/custom-carousel.php:101
#: elementor/widgets/custom-carousel.php:311
#: elementor/widgets/custom-carousel.php:794
#: elementor/widgets/custom-carousel.php:1164
#: elementor/widgets/grid-builder.php:116 elementor/widgets/icon-box.php:150
#: elementor/widgets/icon-box.php:389 elementor/widgets/listings-tabs.php:307
#: elementor/widgets/partners.php:93 elementor/widgets/section-title.php:87
#: elementor/widgets/single-agency/agency-about.php:53
#: elementor/widgets/single-agency/agency-listings-review.php:568
#: elementor/widgets/single-agency/agency-search.php:55
#: elementor/widgets/single-agency/agency-single-stats.php:53
#: elementor/widgets/single-agent/agent-about.php:53
#: elementor/widgets/single-agent/agent-listings-review.php:573
#: elementor/widgets/single-agent/agent-search.php:55
#: elementor/widgets/single-agent/agent-single-stats.php:53
#: elementor/widgets/single-post/post-navigation.php:195
#: elementor/widgets/sort-by.php:86 elementor/widgets/taxonomies-list.php:146
msgid "Title"
msgstr "заглавие"

#: classes/class-agency-post-type.php:89
#: elementor/widgets/single-agency/agency-meta.php:55
#: elementor/widgets/single-agency/agency-profile-v1.php:65
#: elementor/widgets/single-agent/agent-meta.php:57
#: elementor/widgets/single-agent/agent-profile-v1.php:77
#: extensions/meta-box/src/Updater/Settings.php:39
msgid "License"
msgstr "Лицензия"

#: classes/class-agency-post-type.php:90 statistics/houzez-statistics.php:115
#: statistics/houzez-statistics.php:192 statistics/houzez-statistics.php:240
#: statistics/houzez-statistics.php:320
msgid "Thumbnail"
msgstr "Thumbnail"

#: classes/class-agency-post-type.php:91 classes/class-agent-post-type.php:129
msgid "E-mail"
msgstr "Эл. почта"

#: classes/class-agency-post-type.php:92 classes/class-agent-post-type.php:130
msgid "Web"
msgstr "Web"

#: classes/class-agency-post-type.php:93
#: elementor/template-part/single-agency/contact-form.php:73
#: elementor/template-part/single-agent/contact-form.php:71
#: elementor/widgets/single-agency/agency-contact.php:88
#: elementor/widgets/single-agent/agent-contact.php:88
msgid "Phone"
msgstr "Телефон"

#: classes/class-agency-post-type.php:94 classes/class-agent-post-type.php:30
#: classes/class-houzez-init.php:523 classes/class-menu.php:72
#: classes/class-menu.php:73 classes/class-post-types.php:63
#: elementor/template-part/single-agency/agency-listings-review.php:128
#: elementor/widgets/agents.php:36 statistics/houzez-statistics.php:56
msgid "Agents"
msgstr "Агенты"

#: classes/class-agency-post-type.php:95
#: elementor/widgets/single-post/author-box.php:472
#: elementor/widgets/single-post/post-info.php:75
msgid "Author"
msgstr "автор"

#: classes/class-agent-post-type.php:31 classes/class-image-sizes.php:144
#: classes/class-image-sizes.php:1824
#: elementor/traits/Houzez_Property_Cards_Traits.php:1211
#: elementor/widgets/properties-recent-viewed.php:361
#: elementor/widgets/property-cards-v1.php:311
#: elementor/widgets/property-cards-v2.php:287
#: elementor/widgets/property-cards-v4.php:306
#: elementor/widgets/property-cards-v7.php:295
#: elementor/widgets/property-cards-v8.php:281
#: elementor/widgets/property-cards-v8.php:527
#: elementor/widgets/property-carousel-v1.php:286
#: elementor/widgets/property-carousel-v2.php:263
#: elementor/widgets/property-carousel-v7.php:271
msgid "Agent"
msgstr "агент"

#: classes/class-agent-post-type.php:32 classes/class-invoice-post-type.php:38
#: classes/class-partners-post-type.php:33
#: classes/class-property-post-type.php:213
#: classes/class-reviews-post-type.php:32
#: classes/class-testimonials-post-type.php:35
#: classes/class-user-packages-post-type.php:41
#: classes/class-user-packages-post-type.php:42
#: templates/currency/currency-list.php:4 templates/currency/form.php:17
#: templates/fields-builder/fields-form.php:16
#: templates/fields-builder/index.php:5
msgid "Add New"
msgstr "Добавить новое"

#: classes/class-agent-post-type.php:33
msgid "Add New Agent"
msgstr "Добавить новый агент"

#: classes/class-agent-post-type.php:34
msgid "Edit Agent"
msgstr "Изменить агент"

#: classes/class-agent-post-type.php:35
msgid "New Agent"
msgstr "Новый агент"

#: classes/class-agent-post-type.php:36
msgid "View Agent"
msgstr "Агент просмотра"

#: classes/class-agent-post-type.php:37
#: classes/class-testimonials-post-type.php:40
msgid "Search Agent"
msgstr "Агент поиска"

#: classes/class-agent-post-type.php:38
msgid "No Agent found"
msgstr "Агент не найден"

#: classes/class-agent-post-type.php:39
msgid "No Agent found in Trash"
msgstr "Агент не найден в корзине"

#: classes/class-agent-post-type.php:75 classes/class-houzez-init.php:529
msgid "Categories"
msgstr "категории"

#: classes/class-agent-post-type.php:76
msgid "Add New Category"
msgstr "Добавить новую категорию"

#: classes/class-agent-post-type.php:77
msgid "New Category"
msgstr "Новая категория"

#: classes/class-agent-post-type.php:97 classes/class-houzez-init.php:534
#: elementor/widgets/search-builder.php:470
msgid "Cities"
msgstr "Города"

#: classes/class-agent-post-type.php:98
#: classes/class-property-post-type.php:366
msgid "Add New City"
msgstr "Добавить новый город"

#: classes/class-agent-post-type.php:99
#: classes/class-property-post-type.php:367
msgid "New City"
msgstr "Новый город"

#: classes/class-agent-post-type.php:125
msgid "Agent ID"
msgstr "Агент ID"

#: classes/class-agent-post-type.php:126 elementor/widgets/agent-card.php:423
#: elementor/widgets/agents-grid.php:404 elementor/widgets/agents.php:254
#: elementor/widgets/single-agent/agent-name.php:17
#: elementor/widgets/single-agent/agent-name.php:42
msgid "Agent Name"
msgstr "Имя агента"

#: classes/class-agent-post-type.php:127
msgid "Picture"
msgstr "Картина"

#: classes/class-agent-post-type.php:128 elementor/widgets/agents-grid.php:120
#: elementor/widgets/agents.php:120
#: elementor/widgets/blog-posts-carousel.php:104
#: elementor/widgets/blog-posts-v2.php:98 elementor/widgets/blog-posts.php:112
msgid "Category"
msgstr "категория"

#: classes/class-agent-post-type.php:131 elementor/widgets/contact-form.php:89
#: elementor/widgets/inquiry-form.php:107
#: elementor/widgets/single-agency/agency-contact.php:100
#: elementor/widgets/single-agent/agent-contact.php:100
msgid "Mobile"
msgstr "мобильный"

#: classes/class-api-settings.php:60
#, php-format
msgctxt "openexchangerates.org link"
msgid ""
"Plugin get currency data from %1s and imports it into the WordPress "
"database. The exchange rates will be updated on a frequency that you can "
"specify below."
msgstr ""

#: classes/class-api-settings.php:67
msgid "Save"
msgstr ""

#: classes/class-api-settings.php:87
msgid "API Key"
msgstr ""

#: classes/class-api-settings.php:89
msgid "Update Interval"
msgstr ""

#: classes/class-api-settings.php:131
msgid "Enter the Open Exchange Rates API key"
msgstr ""

#: classes/class-api-settings.php:134
#, php-format
msgctxt "URL where to get the API key"
msgid "Get yours at: %1s"
msgstr ""

#: classes/class-api-settings.php:158
msgid "Hourly"
msgstr ""

#: classes/class-api-settings.php:159
msgid "Daily"
msgstr ""

#: classes/class-api-settings.php:160
msgid "Weekly"
msgstr ""

#: classes/class-api-settings.php:161
msgid "Biweekly"
msgstr ""

#: classes/class-api-settings.php:162
msgid "Monthly"
msgstr ""

#: classes/class-api-settings.php:167
msgid "Specify the frequency when to update currencies exchange rates"
msgstr ""

#: classes/class-cron.php:52
msgid "Once a Minute"
msgstr ""

#: classes/class-cron.php:56
msgid "Once Weekly"
msgstr ""

#: classes/class-cron.php:60
msgid "Once Biweekly"
msgstr ""

#: classes/class-cron.php:64
msgid "Once Monthly"
msgstr ""

#: classes/class-currencies.php:254
msgid "The currency has been added, excellent!"
msgstr ""

#: classes/class-currencies.php:261
msgid "The currency has been updated, excellent!"
msgstr ""

#: classes/class-currencies.php:268 classes/class-fields-builder.php:243
msgid "There has been an error. Bummer!"
msgstr ""

#: classes/class-fields-builder.php:119 elementor/widgets/banner-image.php:133
#: elementor/widgets/banner-image.php:451
#: elementor/widgets/contact-form.php:429
#: elementor/widgets/header-footer/menu.php:232
#: elementor/widgets/icon-box.php:157 elementor/widgets/inquiry-form.php:414
#: elementor/widgets/single-agent/agent-position.php:49
#: elementor/widgets/single-post/post-info.php:632
#: elementor/widgets/single-property/section-contact-bottom.php:373
#: elementor/widgets/single-property/section-description.php:165
#: elementor/widgets/taxonomies-list.php:576
msgid "Text"
msgstr ""

#: classes/class-fields-builder.php:120
#: elementor/widgets/single-agency/agency-listings-review.php:152
#: elementor/widgets/single-agency/agency-listings.php:152
#: elementor/widgets/single-agent/agent-listings-review.php:157
#: elementor/widgets/single-agent/agent-listings.php:157
#: functions/functions.php:225
msgid "Number"
msgstr ""

#: classes/class-fields-builder.php:121
msgid "URL/Link"
msgstr ""

#: classes/class-fields-builder.php:122
msgid "Text area"
msgstr ""

#: classes/class-fields-builder.php:123 classes/class-houzez-init.php:347
#: elementor/widgets/search-builder.php:283
#: extensions/meta-box/inc/fields/file-input.php:46
#: extensions/meta-box/inc/fields/select.php:80
msgid "Select"
msgstr ""

#: classes/class-fields-builder.php:124
msgid "Multi Select"
msgstr ""

#: classes/class-fields-builder.php:125
msgid "Checkbox List"
msgstr ""

#: classes/class-fields-builder.php:126
msgid "Radio"
msgstr ""

#: classes/class-fields-builder.php:229
msgid "The field has been added, excellent!"
msgstr ""

#: classes/class-fields-builder.php:236
msgid "The field has been updated, excellent!"
msgstr ""

#: classes/class-fields-builder.php:442
msgid "A field with the name provided already exists"
msgstr ""

#: classes/class-houzez-init.php:342
msgid "Processing, Please wait..."
msgstr ""

#: classes/class-houzez-init.php:343
msgid "Are you sure you want to do this?"
msgstr ""

#: classes/class-houzez-init.php:344 ⁨classes/class-image-sizes copy.php⁩:1197
#: classes/class-image-sizes.php:2072
#: extensions/meta-box/inc/fields/autocomplete.php:14
#: extensions/meta-box/inc/fields/autocomplete.php:79
#: extensions/meta-box/inc/fields/autocomplete.php:91
#: templates/currency/currency-list.php:18
#: templates/fields-builder/index.php:14
msgid "Delete"
msgstr ""

#: classes/class-houzez-init.php:345 ⁨classes/class-image-sizes copy.php⁩:449
#: ⁨classes/class-image-sizes copy.php⁩:1063 classes/class-image-sizes.php:968
#: classes/class-image-sizes.php:1039 classes/class-image-sizes.php:1205
#: classes/class-image-sizes.php:1247 classes/class-image-sizes.php:1342
#: classes/class-image-sizes.php:1916
msgid "Cancel"
msgstr ""

#: classes/class-houzez-init.php:346
msgid "Confirm"
msgstr ""

#: classes/class-houzez-init.php:348
msgid "Import"
msgstr ""

#: classes/class-houzez-init.php:349 classes/class-import-locations.php:47
msgid "Please map at least one field."
msgstr ""

#: classes/class-houzez-init.php:350
msgid "Error in Importing Data."
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:341
#: ⁨classes/class-image-sizes copy.php⁩:420 classes/class-image-sizes.php:1086
#: classes/class-image-sizes.php:1165
msgid "Please fill in all required fields."
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:411 classes/class-image-sizes.php:1156
#: extensions/favethemes-white-label/template/form.php:101
#: extensions/meta-box/src/Updater/Settings.php:92
#, fuzzy
#| msgid "Saved Searches"
msgid "Save Changes"
msgstr "Сохраненные поиски"

#: ⁨classes/class-image-sizes copy.php⁩:465 classes/class-image-sizes.php:1256
msgid "Are you sure you want to delete this image size? This cannot be undone."
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:557
#: ⁨classes/class-image-sizes copy.php⁩:558
#, fuzzy
#| msgid "Max Size"
msgid "Image Sizes"
msgstr "Максимальный размер"

#: ⁨classes/class-image-sizes copy.php⁩:673
msgid "Manage the dimensions and cropping for built-in Houzez image sizes."
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:686
#: ⁨classes/class-image-sizes copy.php⁩:852 classes/class-image-sizes.php:1553
msgid "Important:"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:687
#: ⁨classes/class-image-sizes copy.php⁩:853 classes/class-image-sizes.php:1554
msgid ""
"Changes to these settings will only apply to newly uploaded images. For "
"existing images, you will need to regenerate thumbnails."
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:689
#: ⁨classes/class-image-sizes copy.php⁩:855 classes/class-image-sizes.php:1556
msgid "Install Force Regenerate Thumbnails plugin"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:848
#, fuzzy
#| msgid "Houzez Invoices"
msgid "Houzez Image Sizes"
msgstr "Houzez Invoices"

#: ⁨classes/class-image-sizes copy.php⁩:862 classes/class-image-sizes.php:1711
msgid "Layout Image Assignments"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:865 classes/class-image-sizes.php:1714
msgid "Manage Image Sizes"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:873 classes/class-image-sizes.php:1722
msgid ""
"Assign specific image sizes to different areas of your website. This "
"controls which image dimensions are used in each layout element."
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:883 classes/class-image-sizes.php:1732
#, fuzzy
#| msgid "Properties Settings"
msgid "Property Listings"
msgstr "Настройки свойств"

#: ⁨classes/class-image-sizes copy.php⁩:885 classes/class-image-sizes.php:1734
msgid "Control image sizes for property listings across different views."
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:890 classes/class-image-sizes.php:124
#: classes/class-image-sizes.php:1739
msgid "Listing Grid v1"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:891 classes/class-image-sizes.php:125
#: classes/class-image-sizes.php:1740
msgid "Listing Grid v2"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:892 classes/class-image-sizes.php:126
#: classes/class-image-sizes.php:1741
msgid "Listing Grid v3"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:893 classes/class-image-sizes.php:127
#: classes/class-image-sizes.php:1742
msgid "Listing Grid v4"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:894 classes/class-image-sizes.php:128
#: classes/class-image-sizes.php:1743
msgid "Listing Grid v5"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:895 classes/class-image-sizes.php:129
#: classes/class-image-sizes.php:1744
msgid "Listing Grid v6"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:896 classes/class-image-sizes.php:130
#: classes/class-image-sizes.php:1745
msgid "Listing Grid v7"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:897 classes/class-image-sizes.php:131
#: classes/class-image-sizes.php:1746
msgid "Listing List v1"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:898 classes/class-image-sizes.php:132
#: classes/class-image-sizes.php:1747
msgid "Listing List v2"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:899 classes/class-image-sizes.php:133
#: classes/class-image-sizes.php:1748
msgid "Listing List v4"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:900 classes/class-image-sizes.php:134
#: classes/class-image-sizes.php:1749
msgid "Listing List v7"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:928 classes/class-image-sizes.php:1777
#, fuzzy
#| msgid "Property Title"
msgid "Property Detail Pages"
msgstr "Заголовок собственности"

#: ⁨classes/class-image-sizes copy.php⁩:930 classes/class-image-sizes.php:1779
msgid "Control image sizes for property detail pages and sliders."
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:935
#, fuzzy
#| msgid "Property Slug"
msgid "Property Slider"
msgstr "Недвижимость Slug"

#: ⁨classes/class-image-sizes copy.php⁩:936 classes/class-image-sizes.php:136
#: classes/class-image-sizes.php:1784
#, fuzzy
#| msgid "Property ID"
msgid "Property Detail v1"
msgstr "ИД объекта"

#: ⁨classes/class-image-sizes copy.php⁩:937 classes/class-image-sizes.php:137
#: classes/class-image-sizes.php:1785
#, fuzzy
#| msgid "Property ID"
msgid "Property Detail v2"
msgstr "ИД объекта"

#: ⁨classes/class-image-sizes copy.php⁩:938
#, fuzzy
#| msgid "Property ID"
msgid "Property Detail v3"
msgstr "ИД объекта"

#: ⁨classes/class-image-sizes copy.php⁩:939
#, fuzzy
#| msgid "Property ID"
msgid "Property Detail v4"
msgstr "ИД объекта"

#: ⁨classes/class-image-sizes copy.php⁩:940 classes/class-image-sizes.php:139
#: classes/class-image-sizes.php:1787
#, fuzzy
#| msgid "Property ID"
msgid "Property Detail v5"
msgstr "ИД объекта"

#: ⁨classes/class-image-sizes copy.php⁩:967
msgid "Profiles & Blog"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:969
msgid "Control image sizes for agent/agency profiles and blog posts."
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:974
#, fuzzy
#| msgid "Agent Name"
msgid "Agent Profile"
msgstr "Имя агента"

#: ⁨classes/class-image-sizes copy.php⁩:975
#, fuzzy
#| msgid "Agencies"
msgid "Agency Profile"
msgstr "агентства"

#: ⁨classes/class-image-sizes copy.php⁩:976 classes/class-image-sizes.php:146
#: classes/class-image-sizes.php:1826
msgid "Blog Post"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1000 classes/class-image-sizes.php:1851
msgid "Save Assignments"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1002 classes/class-image-sizes.php:1853
msgid ""
"Tip: You can create custom image sizes in the \"Manage Image Sizes\" tab if "
"you need specific dimensions."
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1018 classes/class-image-sizes.php:1869
#, fuzzy
#| msgid "Add New Invoice"
msgid "Add New Image Size"
msgstr "Добавить новый счет-фактуру"

#: ⁨classes/class-image-sizes copy.php⁩:1027 classes/class-image-sizes.php:1878
msgid "Create New Custom Image Size"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1030
#: ⁨classes/class-image-sizes copy.php⁩:1073
#: ⁨classes/class-image-sizes copy.php⁩:1231 classes/class-image-sizes.php:1881
#: classes/class-image-sizes.php:1926 classes/class-image-sizes.php:2100
#: classes/class-property-post-type.php:893
#: classes/class-property-post-type.php:932
#: classes/class-property-post-type.php:973
#: elementor/traits/Houzez_Filters_Traits.php:468
#: elementor/traits/Houzez_Testimonials_Traits.php:115
#: elementor/widgets/contact-form.php:270
#: elementor/widgets/contact-form.php:271 elementor/widgets/grids.php:278
#: elementor/widgets/single-agency/agency-about.php:62
#: elementor/widgets/single-agent/agent-about.php:62
#: elementor/widgets/single-post/author-box.php:480
#: elementor/widgets/team-member.php:95 elementor/widgets/team-member.php:261
#: templates/currency/currency-list.php:10
msgid "Name"
msgstr "имя"

#: ⁨classes/class-image-sizes copy.php⁩:1033 classes/class-image-sizes.php:1884
msgid "A descriptive name for this image size"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1037
#: ⁨classes/class-image-sizes copy.php⁩:1075
#: ⁨classes/class-image-sizes copy.php⁩:1237 classes/class-image-sizes.php:1888
#: classes/class-image-sizes.php:1928 classes/class-image-sizes.php:2106
#: elementor/traits/Houzez_Style_Traits.php:174
#: elementor/widgets/agent-card.php:343 elementor/widgets/banner-image.php:181
#: elementor/widgets/custom-carousel.php:466
#: elementor/widgets/header-footer/menu.php:648
#: elementor/widgets/header-footer/site-logo.php:173
#: elementor/widgets/single-post/post-info.php:494
#: elementor/widgets/single-property/featured-image.php:125
#: elementor/widgets/taxonomies-list.php:290
msgid "Width"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1040 classes/class-image-sizes.php:1891
msgid "Width in pixels"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1044
#: ⁨classes/class-image-sizes copy.php⁩:1076
#: ⁨classes/class-image-sizes copy.php⁩:1243 classes/class-image-sizes.php:1895
#: classes/class-image-sizes.php:1929 classes/class-image-sizes.php:2112
#: elementor/traits/Houzez_Style_Traits.php:244
#: elementor/widgets/agent-card.php:365 elementor/widgets/banner-image.php:251
#: elementor/widgets/custom-carousel.php:536
#: elementor/widgets/header-footer/menu.php:667
#: elementor/widgets/single-post/post-info.php:529
#: elementor/widgets/single-property/images-gallery-v1.php:63
#: elementor/widgets/taxonomies-grids.php:193
msgid "Height"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1047 classes/class-image-sizes.php:1898
msgid "Height in pixels"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1051
#: ⁨classes/class-image-sizes copy.php⁩:1249 classes/class-image-sizes.php:1902
#: classes/class-image-sizes.php:2118
msgid "Hard Crop?"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1054 classes/class-image-sizes.php:1906
msgid "Whether to crop the image to exact dimensions"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1060 classes/class-image-sizes.php:1913
msgid "Add Custom Image Size"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1074 classes/class-image-sizes.php:1927
#: classes/class-property-post-type.php:896
#: classes/class-property-post-type.php:935
#: classes/class-property-post-type.php:976
msgid "Slug"
msgstr "слизень"

#: ⁨classes/class-image-sizes copy.php⁩:1077 classes/class-image-sizes.php:1930
msgid "Crop"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1078 classes/class-image-sizes.php:1931
#: classes/class-post-types.php:128 classes/class-post-types.php:147
#: classes/class-post-types.php:165 classes/class-post-types.php:183
#: classes/class-post-types.php:201 classes/class-post-types.php:219
#: classes/class-post-types.php:237 classes/class-taxonomies.php:120
#: classes/class-taxonomies.php:138 classes/class-taxonomies.php:156
#: classes/class-taxonomies.php:174
msgid "Enabled"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1079 classes/class-image-sizes.php:1932
#: classes/class-property-post-type.php:516
#: classes/class-reviews-post-type.php:105 statistics/houzez-statistics.php:122
#: statistics/houzez-statistics.php:199 statistics/houzez-statistics.php:247
#: statistics/houzez-statistics.php:327 statistics/houzez-statistics.php:355
#: statistics/houzez-statistics.php:558
msgid "Actions"
msgstr "действия"

#: ⁨classes/class-image-sizes copy.php⁩:1086 classes/class-image-sizes.php:2009
msgid "WordPress Core Sizes"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1087 classes/class-image-sizes.php:2010
msgid ""
"These sizes are defined by WordPress core. You can disable generation but "
"dimensions must be changed in Media Settings."
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1096
#: ⁨classes/class-image-sizes copy.php⁩:1182 classes/class-image-sizes.php:2019
#: classes/class-image-sizes.php:2057 classes/class-property-post-type.php:628
#: classes/class-property-post-type.php:1587
#: elementor/traits/Houzez_Filters_Traits.php:427
#: elementor/traits/Houzez_Filters_Traits.php:441
#: elementor/traits/Houzez_Filters_Traits.php:455
#: elementor/traits/Houzez_Property_Cards_Traits.php:430
#: elementor/traits/Houzez_Property_Cards_Traits.php:444
#: elementor/traits/Houzez_Property_Cards_Traits.php:468
#: elementor/traits/Houzez_Property_Cards_Traits.php:482
#: elementor/traits/Houzez_Property_Cards_Traits.php:1318
#: elementor/traits/Houzez_Property_Cards_Traits.php:1353
#: elementor/traits/Houzez_Property_Cards_Traits.php:1368
#: elementor/traits/Houzez_Property_Cards_Traits.php:1383
#: elementor/traits/Houzez_Property_Cards_Traits.php:1398
#: elementor/traits/Houzez_Property_Cards_Traits.php:1413
#: elementor/traits/Houzez_Property_Cards_Traits.php:1428
#: elementor/traits/Houzez_Property_Cards_Traits.php:1443
#: elementor/traits/Houzez_Property_Cards_Traits.php:1475
#: elementor/traits/Houzez_Property_Cards_Traits.php:2126
#: elementor/traits/Houzez_Property_Cards_Traits.php:2142
#: elementor/traits/Houzez_Property_Cards_Traits.php:2158
#: elementor/traits/Houzez_Property_Cards_Traits.php:2174
#: elementor/traits/Houzez_Property_Cards_Traits.php:2190
#: elementor/traits/Houzez_Property_Cards_Traits.php:2206
#: elementor/traits/Houzez_Style_Traits.php:109
#: elementor/widgets/agent-card.php:160 elementor/widgets/agent-card.php:171
#: elementor/widgets/agent-card.php:182 elementor/widgets/agent-card.php:203
#: elementor/widgets/agent-card.php:213 elementor/widgets/agent-card.php:224
#: elementor/widgets/agent-card.php:235 elementor/widgets/agents-grid.php:207
#: elementor/widgets/agents-grid.php:222 elementor/widgets/agents-grid.php:237
#: elementor/widgets/agents-grid.php:252 elementor/widgets/agents-grid.php:267
#: elementor/widgets/agents-grid.php:357
#: elementor/widgets/blog-posts-carousel.php:206
#: elementor/widgets/blog-posts-carousel.php:220
#: elementor/widgets/blog-posts-carousel.php:244
#: elementor/widgets/blog-posts-carousel.php:258
#: elementor/widgets/contact-form.php:612
#: elementor/widgets/create-listing-btn.php:196
#: elementor/widgets/custom-carousel.php:197
#: elementor/widgets/custom-carousel.php:211
#: elementor/widgets/custom-carousel.php:235
#: elementor/widgets/custom-carousel.php:249
#: elementor/widgets/google-map.php:167 elementor/widgets/google-map.php:182
#: elementor/widgets/google-map.php:312 elementor/widgets/grid-builder.php:340
#: elementor/widgets/grids.php:237 elementor/widgets/grids.php:251
#: elementor/widgets/grids.php:265
#: elementor/widgets/header-footer/create-listing-btn.php:228
#: elementor/widgets/header-footer/login-modal.php:81
#: elementor/widgets/header-footer/login-modal.php:95
#: elementor/widgets/header-footer/site-logo.php:467
#: elementor/widgets/inquiry-form.php:596 elementor/widgets/login-modal.php:68
#: elementor/widgets/login-modal.php:82 elementor/widgets/mapbox.php:151
#: elementor/widgets/mapbox.php:166 elementor/widgets/mapbox.php:296
#: elementor/widgets/open-street-map.php:121
#: elementor/widgets/open-street-map.php:136
#: elementor/widgets/open-street-map.php:302
#: elementor/widgets/properties-grids.php:134
#: elementor/widgets/properties-recent-viewed.php:155
#: elementor/widgets/properties-recent-viewed.php:170
#: elementor/widgets/properties-recent-viewed.php:185
#: elementor/widgets/properties-recent-viewed.php:200
#: elementor/widgets/properties-recent-viewed.php:215
#: elementor/widgets/properties-recent-viewed.php:230
#: elementor/widgets/properties-recent-viewed.php:245
#: elementor/widgets/properties-recent-viewed.php:257
#: elementor/widgets/property-cards-v1.php:168
#: elementor/widgets/property-cards-v1.php:183
#: elementor/widgets/property-cards-v1.php:195
#: elementor/widgets/property-cards-v2.php:164
#: elementor/widgets/property-cards-v2.php:179
#: elementor/widgets/property-cards-v4.php:163
#: elementor/widgets/property-cards-v4.php:178
#: elementor/widgets/property-cards-v4.php:190
#: elementor/widgets/property-cards-v7.php:167
#: elementor/widgets/property-cards-v7.php:185
#: elementor/widgets/property-cards-v8.php:148
#: elementor/widgets/property-cards-v8.php:165
#: elementor/widgets/property-carousel-v1.php:143
#: elementor/widgets/property-carousel-v1.php:158
#: elementor/widgets/property-carousel-v1.php:170
#: elementor/widgets/property-carousel-v2.php:140
#: elementor/widgets/property-carousel-v2.php:155
#: elementor/widgets/property-carousel-v7.php:143
#: elementor/widgets/property-carousel-v7.php:161
#: elementor/widgets/single-agency/agency-call-btn.php:62
#: elementor/widgets/single-agency/agency-contact-form.php:57
#: elementor/widgets/single-agency/agency-meta.php:161
#: elementor/widgets/single-agency/agency-profile-v1.php:115
#: elementor/widgets/single-agent/agent-call-btn.php:62
#: elementor/widgets/single-agent/agent-contact-form.php:57
#: elementor/widgets/single-agent/agent-meta.php:163
#: elementor/widgets/single-agent/agent-profile-v1.php:127
#: elementor/widgets/single-property/featured-image.php:62
#: elementor/widgets/single-property/images-gallery-v1.php:88
#: elementor/widgets/single-property/images-gallery-v1.php:100
#: elementor/widgets/single-property/images-gallery-v1.php:112
#: elementor/widgets/single-property/images-gallery-v1.php:124
#: elementor/widgets/single-property/images-gallery-v1.php:147
#: elementor/widgets/single-property/images-gallery-v2.php:81
#: elementor/widgets/single-property/images-gallery-v2.php:93
#: elementor/widgets/single-property/images-gallery-v2.php:105
#: elementor/widgets/single-property/images-gallery-v2.php:117
#: elementor/widgets/single-property/images-gallery-v2.php:140
#: elementor/widgets/single-property/images-gallery-v3.php:64
#: elementor/widgets/single-property/images-gallery-v3.php:76
#: elementor/widgets/single-property/images-gallery-v3.php:88
#: elementor/widgets/single-property/images-gallery-v3.php:100
#: elementor/widgets/single-property/images-gallery-v3.php:123
#: elementor/widgets/single-property/item-tools.php:54
#: elementor/widgets/single-property/item-tools.php:66
#: elementor/widgets/single-property/item-tools.php:78
#: elementor/widgets/single-property/property-address.php:95
#: elementor/widgets/single-property/property-price.php:54
#: elementor/widgets/single-property/property-title-area.php:141
#: elementor/widgets/single-property/property-title-area.php:206
#: elementor/widgets/single-property/property-title-area.php:303
#: elementor/widgets/single-property/property-title-area.php:326
#: elementor/widgets/single-property/property-title-area.php:353
#: elementor/widgets/single-property/property-title-area.php:408
#: elementor/widgets/single-property/property-title-area.php:535
#: elementor/widgets/single-property/property-title.php:85
#: elementor/widgets/single-property/section-attachments.php:111
#: elementor/widgets/single-property/section-contact-2.php:57
#: elementor/widgets/single-property/section-contact-2.php:109
#: elementor/widgets/single-property/section-contact-2.php:121
#: elementor/widgets/single-property/section-contact-bottom.php:567
#: elementor/widgets/single-property/section-floorplan.php:199
#: elementor/widgets/single-property/section-overview.php:286
#: elementor/widgets/single-property/section-schedule-tour.php:208
#: elementor/widgets/single-property/section-sublistings.php:90
#: elementor/widgets/single-property/section-toparea-v1.php:85
#: elementor/widgets/single-property/section-toparea-v1.php:108
#: elementor/widgets/single-property/section-toparea-v1.php:205
#: elementor/widgets/single-property/section-toparea-v1.php:228
#: elementor/widgets/single-property/section-toparea-v1.php:255
#: elementor/widgets/single-property/section-toparea-v1.php:310
#: elementor/widgets/single-property/section-toparea-v1.php:437
#: elementor/widgets/single-property/section-toparea-v1.php:449
#: elementor/widgets/single-property/section-toparea-v1.php:461
#: elementor/widgets/single-property/section-toparea-v1.php:556
#: elementor/widgets/single-property/section-toparea-v1.php:568
#: elementor/widgets/single-property/section-toparea-v1.php:580
#: elementor/widgets/single-property/section-toparea-v1.php:592
#: elementor/widgets/single-property/section-toparea-v1.php:615
#: elementor/widgets/single-property/section-toparea-v1.php:649
#: elementor/widgets/single-property/section-toparea-v2.php:83
#: elementor/widgets/single-property/section-toparea-v2.php:108
#: elementor/widgets/single-property/section-toparea-v2.php:205
#: elementor/widgets/single-property/section-toparea-v2.php:228
#: elementor/widgets/single-property/section-toparea-v2.php:255
#: elementor/widgets/single-property/section-toparea-v2.php:310
#: elementor/widgets/single-property/section-toparea-v2.php:436
#: elementor/widgets/single-property/section-toparea-v2.php:448
#: elementor/widgets/single-property/section-toparea-v2.php:460
#: elementor/widgets/single-property/section-toparea-v2.php:555
#: elementor/widgets/single-property/section-toparea-v2.php:567
#: elementor/widgets/single-property/section-toparea-v2.php:579
#: elementor/widgets/single-property/section-toparea-v2.php:591
#: elementor/widgets/single-property/section-toparea-v2.php:614
#: elementor/widgets/single-property/section-toparea-v3.php:108
#: elementor/widgets/single-property/section-toparea-v3.php:133
#: elementor/widgets/single-property/section-toparea-v3.php:230
#: elementor/widgets/single-property/section-toparea-v3.php:253
#: elementor/widgets/single-property/section-toparea-v3.php:280
#: elementor/widgets/single-property/section-toparea-v3.php:335
#: elementor/widgets/single-property/section-toparea-v3.php:461
#: elementor/widgets/single-property/section-toparea-v3.php:473
#: elementor/widgets/single-property/section-toparea-v3.php:485
#: elementor/widgets/single-property/section-toparea-v3.php:580
#: elementor/widgets/single-property/section-toparea-v3.php:592
#: elementor/widgets/single-property/section-toparea-v3.php:604
#: elementor/widgets/single-property/section-toparea-v3.php:616
#: elementor/widgets/single-property/section-toparea-v3.php:639
#: elementor/widgets/single-property/section-toparea-v5.php:83
#: elementor/widgets/single-property/section-toparea-v5.php:108
#: elementor/widgets/single-property/section-toparea-v5.php:205
#: elementor/widgets/single-property/section-toparea-v5.php:228
#: elementor/widgets/single-property/section-toparea-v5.php:255
#: elementor/widgets/single-property/section-toparea-v5.php:310
#: elementor/widgets/single-property/section-toparea-v5.php:436
#: elementor/widgets/single-property/section-toparea-v5.php:448
#: elementor/widgets/single-property/section-toparea-v5.php:460
#: elementor/widgets/single-property/section-toparea-v5.php:555
#: elementor/widgets/single-property/section-toparea-v5.php:567
#: elementor/widgets/single-property/section-toparea-v5.php:579
#: elementor/widgets/single-property/section-toparea-v5.php:591
#: elementor/widgets/single-property/section-toparea-v5.php:614
#: elementor/widgets/single-property/section-toparea-v6.php:128
#: elementor/widgets/single-property/section-toparea-v6.php:153
#: elementor/widgets/single-property/section-toparea-v6.php:250
#: elementor/widgets/single-property/section-toparea-v6.php:273
#: elementor/widgets/single-property/section-toparea-v6.php:300
#: elementor/widgets/single-property/section-toparea-v6.php:355
#: elementor/widgets/single-property/section-toparea-v6.php:481
#: elementor/widgets/single-property/section-toparea-v6.php:493
#: elementor/widgets/single-property/section-toparea-v6.php:505
#: elementor/widgets/single-property/section-toparea-v7.php:103
#: elementor/widgets/single-property/section-toparea-v7.php:128
#: elementor/widgets/single-property/section-toparea-v7.php:225
#: elementor/widgets/single-property/section-toparea-v7.php:248
#: elementor/widgets/single-property/section-toparea-v7.php:275
#: elementor/widgets/single-property/section-toparea-v7.php:330
#: elementor/widgets/single-property/section-toparea-v7.php:456
#: elementor/widgets/single-property/section-toparea-v7.php:468
#: elementor/widgets/single-property/section-toparea-v7.php:480
#: elementor/widgets/taxonomies-cards-carousel.php:180
#: elementor/widgets/taxonomies-cards-carousel.php:194
#: elementor/widgets/taxonomies-cards-carousel.php:218
#: elementor/widgets/taxonomies-cards-carousel.php:232
#: elementor/widgets/taxonomies-grids-carousel.php:165
#: elementor/widgets/taxonomies-grids-carousel.php:179
#: elementor/widgets/taxonomies-grids-carousel.php:203
#: elementor/widgets/taxonomies-grids-carousel.php:217
#: extensions/meta-box/inc/fields/checkbox.php:43
#: templates/fields-builder/fields-form.php:62
msgid "Yes"
msgstr "да"

#: ⁨classes/class-image-sizes copy.php⁩:1096
#: ⁨classes/class-image-sizes copy.php⁩:1182 classes/class-image-sizes.php:2019
#: classes/class-image-sizes.php:2057 classes/class-property-post-type.php:626
#: elementor/traits/Houzez_Filters_Traits.php:426
#: elementor/traits/Houzez_Filters_Traits.php:440
#: elementor/traits/Houzez_Filters_Traits.php:454
#: elementor/traits/Houzez_Property_Cards_Traits.php:431
#: elementor/traits/Houzez_Property_Cards_Traits.php:443
#: elementor/traits/Houzez_Property_Cards_Traits.php:467
#: elementor/traits/Houzez_Property_Cards_Traits.php:481
#: elementor/traits/Houzez_Property_Cards_Traits.php:1319
#: elementor/traits/Houzez_Property_Cards_Traits.php:1354
#: elementor/traits/Houzez_Property_Cards_Traits.php:1369
#: elementor/traits/Houzez_Property_Cards_Traits.php:1384
#: elementor/traits/Houzez_Property_Cards_Traits.php:1399
#: elementor/traits/Houzez_Property_Cards_Traits.php:1414
#: elementor/traits/Houzez_Property_Cards_Traits.php:1429
#: elementor/traits/Houzez_Property_Cards_Traits.php:1444
#: elementor/traits/Houzez_Property_Cards_Traits.php:1476
#: elementor/traits/Houzez_Property_Cards_Traits.php:2127
#: elementor/traits/Houzez_Property_Cards_Traits.php:2143
#: elementor/traits/Houzez_Property_Cards_Traits.php:2159
#: elementor/traits/Houzez_Property_Cards_Traits.php:2175
#: elementor/traits/Houzez_Property_Cards_Traits.php:2191
#: elementor/traits/Houzez_Property_Cards_Traits.php:2207
#: elementor/traits/Houzez_Style_Traits.php:110
#: elementor/widgets/agent-card.php:161 elementor/widgets/agent-card.php:172
#: elementor/widgets/agent-card.php:183 elementor/widgets/agent-card.php:204
#: elementor/widgets/agent-card.php:214 elementor/widgets/agent-card.php:225
#: elementor/widgets/agent-card.php:236 elementor/widgets/agents-grid.php:208
#: elementor/widgets/agents-grid.php:223 elementor/widgets/agents-grid.php:238
#: elementor/widgets/agents-grid.php:253 elementor/widgets/agents-grid.php:268
#: elementor/widgets/agents-grid.php:358
#: elementor/widgets/blog-posts-carousel.php:207
#: elementor/widgets/blog-posts-carousel.php:219
#: elementor/widgets/blog-posts-carousel.php:243
#: elementor/widgets/blog-posts-carousel.php:257
#: elementor/widgets/contact-form.php:613
#: elementor/widgets/create-listing-btn.php:197
#: elementor/widgets/custom-carousel.php:198
#: elementor/widgets/custom-carousel.php:210
#: elementor/widgets/custom-carousel.php:234
#: elementor/widgets/custom-carousel.php:248
#: elementor/widgets/google-map.php:168 elementor/widgets/google-map.php:183
#: elementor/widgets/google-map.php:313 elementor/widgets/grid-builder.php:341
#: elementor/widgets/grids.php:236 elementor/widgets/grids.php:250
#: elementor/widgets/grids.php:264
#: elementor/widgets/header-footer/create-listing-btn.php:229
#: elementor/widgets/header-footer/login-modal.php:80
#: elementor/widgets/header-footer/login-modal.php:94
#: elementor/widgets/header-footer/site-logo.php:466
#: elementor/widgets/inquiry-form.php:597 elementor/widgets/login-modal.php:67
#: elementor/widgets/login-modal.php:81 elementor/widgets/mapbox.php:152
#: elementor/widgets/mapbox.php:167 elementor/widgets/mapbox.php:297
#: elementor/widgets/open-street-map.php:122
#: elementor/widgets/open-street-map.php:137
#: elementor/widgets/open-street-map.php:303
#: elementor/widgets/properties-grids.php:135
#: elementor/widgets/properties-recent-viewed.php:156
#: elementor/widgets/properties-recent-viewed.php:171
#: elementor/widgets/properties-recent-viewed.php:186
#: elementor/widgets/properties-recent-viewed.php:201
#: elementor/widgets/properties-recent-viewed.php:216
#: elementor/widgets/properties-recent-viewed.php:231
#: elementor/widgets/properties-recent-viewed.php:246
#: elementor/widgets/properties-recent-viewed.php:258
#: elementor/widgets/property-cards-v1.php:169
#: elementor/widgets/property-cards-v1.php:184
#: elementor/widgets/property-cards-v1.php:196
#: elementor/widgets/property-cards-v2.php:165
#: elementor/widgets/property-cards-v2.php:180
#: elementor/widgets/property-cards-v4.php:164
#: elementor/widgets/property-cards-v4.php:179
#: elementor/widgets/property-cards-v4.php:191
#: elementor/widgets/property-cards-v7.php:168
#: elementor/widgets/property-cards-v7.php:186
#: elementor/widgets/property-cards-v8.php:149
#: elementor/widgets/property-cards-v8.php:166
#: elementor/widgets/property-carousel-v1.php:144
#: elementor/widgets/property-carousel-v1.php:159
#: elementor/widgets/property-carousel-v1.php:171
#: elementor/widgets/property-carousel-v2.php:141
#: elementor/widgets/property-carousel-v2.php:156
#: elementor/widgets/property-carousel-v7.php:144
#: elementor/widgets/property-carousel-v7.php:162
#: elementor/widgets/single-agency/agency-call-btn.php:63
#: elementor/widgets/single-agency/agency-contact-form.php:58
#: elementor/widgets/single-agency/agency-meta.php:162
#: elementor/widgets/single-agency/agency-profile-v1.php:116
#: elementor/widgets/single-agent/agent-call-btn.php:63
#: elementor/widgets/single-agent/agent-contact-form.php:58
#: elementor/widgets/single-agent/agent-meta.php:164
#: elementor/widgets/single-agent/agent-profile-v1.php:128
#: elementor/widgets/single-property/featured-image.php:63
#: elementor/widgets/single-property/images-gallery-v1.php:89
#: elementor/widgets/single-property/images-gallery-v1.php:101
#: elementor/widgets/single-property/images-gallery-v1.php:113
#: elementor/widgets/single-property/images-gallery-v1.php:125
#: elementor/widgets/single-property/images-gallery-v1.php:148
#: elementor/widgets/single-property/images-gallery-v2.php:82
#: elementor/widgets/single-property/images-gallery-v2.php:94
#: elementor/widgets/single-property/images-gallery-v2.php:106
#: elementor/widgets/single-property/images-gallery-v2.php:118
#: elementor/widgets/single-property/images-gallery-v2.php:141
#: elementor/widgets/single-property/images-gallery-v3.php:65
#: elementor/widgets/single-property/images-gallery-v3.php:77
#: elementor/widgets/single-property/images-gallery-v3.php:89
#: elementor/widgets/single-property/images-gallery-v3.php:101
#: elementor/widgets/single-property/images-gallery-v3.php:124
#: elementor/widgets/single-property/item-tools.php:55
#: elementor/widgets/single-property/item-tools.php:67
#: elementor/widgets/single-property/item-tools.php:79
#: elementor/widgets/single-property/property-address.php:96
#: elementor/widgets/single-property/property-price.php:55
#: elementor/widgets/single-property/property-title-area.php:142
#: elementor/widgets/single-property/property-title-area.php:207
#: elementor/widgets/single-property/property-title-area.php:304
#: elementor/widgets/single-property/property-title-area.php:327
#: elementor/widgets/single-property/property-title-area.php:354
#: elementor/widgets/single-property/property-title-area.php:409
#: elementor/widgets/single-property/property-title-area.php:536
#: elementor/widgets/single-property/property-title.php:86
#: elementor/widgets/single-property/section-attachments.php:112
#: elementor/widgets/single-property/section-contact-2.php:58
#: elementor/widgets/single-property/section-contact-2.php:110
#: elementor/widgets/single-property/section-contact-2.php:122
#: elementor/widgets/single-property/section-contact-bottom.php:568
#: elementor/widgets/single-property/section-floorplan.php:200
#: elementor/widgets/single-property/section-overview.php:287
#: elementor/widgets/single-property/section-schedule-tour.php:209
#: elementor/widgets/single-property/section-sublistings.php:91
#: elementor/widgets/single-property/section-toparea-v1.php:86
#: elementor/widgets/single-property/section-toparea-v1.php:109
#: elementor/widgets/single-property/section-toparea-v1.php:206
#: elementor/widgets/single-property/section-toparea-v1.php:229
#: elementor/widgets/single-property/section-toparea-v1.php:256
#: elementor/widgets/single-property/section-toparea-v1.php:311
#: elementor/widgets/single-property/section-toparea-v1.php:438
#: elementor/widgets/single-property/section-toparea-v1.php:450
#: elementor/widgets/single-property/section-toparea-v1.php:462
#: elementor/widgets/single-property/section-toparea-v1.php:557
#: elementor/widgets/single-property/section-toparea-v1.php:569
#: elementor/widgets/single-property/section-toparea-v1.php:581
#: elementor/widgets/single-property/section-toparea-v1.php:593
#: elementor/widgets/single-property/section-toparea-v1.php:616
#: elementor/widgets/single-property/section-toparea-v1.php:650
#: elementor/widgets/single-property/section-toparea-v2.php:84
#: elementor/widgets/single-property/section-toparea-v2.php:109
#: elementor/widgets/single-property/section-toparea-v2.php:206
#: elementor/widgets/single-property/section-toparea-v2.php:229
#: elementor/widgets/single-property/section-toparea-v2.php:256
#: elementor/widgets/single-property/section-toparea-v2.php:311
#: elementor/widgets/single-property/section-toparea-v2.php:437
#: elementor/widgets/single-property/section-toparea-v2.php:449
#: elementor/widgets/single-property/section-toparea-v2.php:461
#: elementor/widgets/single-property/section-toparea-v2.php:556
#: elementor/widgets/single-property/section-toparea-v2.php:568
#: elementor/widgets/single-property/section-toparea-v2.php:580
#: elementor/widgets/single-property/section-toparea-v2.php:592
#: elementor/widgets/single-property/section-toparea-v2.php:615
#: elementor/widgets/single-property/section-toparea-v3.php:109
#: elementor/widgets/single-property/section-toparea-v3.php:134
#: elementor/widgets/single-property/section-toparea-v3.php:231
#: elementor/widgets/single-property/section-toparea-v3.php:254
#: elementor/widgets/single-property/section-toparea-v3.php:281
#: elementor/widgets/single-property/section-toparea-v3.php:336
#: elementor/widgets/single-property/section-toparea-v3.php:462
#: elementor/widgets/single-property/section-toparea-v3.php:474
#: elementor/widgets/single-property/section-toparea-v3.php:486
#: elementor/widgets/single-property/section-toparea-v3.php:581
#: elementor/widgets/single-property/section-toparea-v3.php:593
#: elementor/widgets/single-property/section-toparea-v3.php:605
#: elementor/widgets/single-property/section-toparea-v3.php:617
#: elementor/widgets/single-property/section-toparea-v3.php:640
#: elementor/widgets/single-property/section-toparea-v5.php:84
#: elementor/widgets/single-property/section-toparea-v5.php:109
#: elementor/widgets/single-property/section-toparea-v5.php:206
#: elementor/widgets/single-property/section-toparea-v5.php:229
#: elementor/widgets/single-property/section-toparea-v5.php:256
#: elementor/widgets/single-property/section-toparea-v5.php:311
#: elementor/widgets/single-property/section-toparea-v5.php:437
#: elementor/widgets/single-property/section-toparea-v5.php:449
#: elementor/widgets/single-property/section-toparea-v5.php:461
#: elementor/widgets/single-property/section-toparea-v5.php:556
#: elementor/widgets/single-property/section-toparea-v5.php:568
#: elementor/widgets/single-property/section-toparea-v5.php:580
#: elementor/widgets/single-property/section-toparea-v5.php:592
#: elementor/widgets/single-property/section-toparea-v5.php:615
#: elementor/widgets/single-property/section-toparea-v6.php:129
#: elementor/widgets/single-property/section-toparea-v6.php:154
#: elementor/widgets/single-property/section-toparea-v6.php:251
#: elementor/widgets/single-property/section-toparea-v6.php:274
#: elementor/widgets/single-property/section-toparea-v6.php:301
#: elementor/widgets/single-property/section-toparea-v6.php:356
#: elementor/widgets/single-property/section-toparea-v6.php:482
#: elementor/widgets/single-property/section-toparea-v6.php:494
#: elementor/widgets/single-property/section-toparea-v6.php:506
#: elementor/widgets/single-property/section-toparea-v7.php:104
#: elementor/widgets/single-property/section-toparea-v7.php:129
#: elementor/widgets/single-property/section-toparea-v7.php:226
#: elementor/widgets/single-property/section-toparea-v7.php:249
#: elementor/widgets/single-property/section-toparea-v7.php:276
#: elementor/widgets/single-property/section-toparea-v7.php:331
#: elementor/widgets/single-property/section-toparea-v7.php:457
#: elementor/widgets/single-property/section-toparea-v7.php:469
#: elementor/widgets/single-property/section-toparea-v7.php:481
#: elementor/widgets/taxonomies-cards-carousel.php:181
#: elementor/widgets/taxonomies-cards-carousel.php:193
#: elementor/widgets/taxonomies-cards-carousel.php:217
#: elementor/widgets/taxonomies-cards-carousel.php:231
#: elementor/widgets/taxonomies-grids-carousel.php:166
#: elementor/widgets/taxonomies-grids-carousel.php:178
#: elementor/widgets/taxonomies-grids-carousel.php:202
#: elementor/widgets/taxonomies-grids-carousel.php:216
#: extensions/meta-box/inc/fields/checkbox.php:43
#: templates/fields-builder/fields-form.php:62
msgid "No"
msgstr "нет"

#: ⁨classes/class-image-sizes copy.php⁩:1111 classes/class-image-sizes.php:2034
#, fuzzy
#| msgid "Update Settings"
msgid "Edit in Media Settings"
msgstr "Обновить настройки"

#: ⁨classes/class-image-sizes copy.php⁩:1120 classes/class-image-sizes.php:1939
#, fuzzy
#| msgid "Houzez Invoices"
msgid "Houzez Built-in Sizes"
msgstr "Houzez Invoices"

#: ⁨classes/class-image-sizes copy.php⁩:1121
msgid ""
"These sizes are used by the Houzez theme for property listings and galleries."
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1133
#: ⁨classes/class-image-sizes copy.php⁩:1141 classes/class-image-sizes.php:1968
#: classes/class-image-sizes.php:1976
#: elementor/traits/Houzez_Property_Cards_Traits.php:852
#: elementor/traits/Houzez_Style_Traits.php:272
#: elementor/traits/Houzez_Style_Traits.php:983
#: elementor/traits/Houzez_Style_Traits.php:1336
#: elementor/widgets/banner-image.php:279
#: elementor/widgets/contact-form.php:206
#: elementor/widgets/contact-form.php:458
#: elementor/widgets/custom-carousel.php:564
#: elementor/widgets/header-footer/menu.php:408
#: elementor/widgets/header-footer/site-logo.php:498
#: elementor/widgets/inquiry-form.php:216
#: elementor/widgets/inquiry-form.php:443
#: elementor/widgets/property-cards-v1.php:496
#: elementor/widgets/property-cards-v4.php:491
#: elementor/widgets/property-cards-v7.php:462
#: elementor/widgets/property-carousel-v1.php:471
#: elementor/widgets/property-carousel-v7.php:432
#: elementor/widgets/search-builder.php:235
#: elementor/widgets/single-agency/agency-call-btn.php:75
#: elementor/widgets/single-agency/agency-contact-btn.php:63
#: elementor/widgets/single-agency/agency-line-btn.php:63
#: elementor/widgets/single-agency/agency-telegram-btn.php:63
#: elementor/widgets/single-agency/agency-whatsapp-btn.php:63
#: elementor/widgets/single-agent/agent-call-btn.php:75
#: elementor/widgets/single-agent/agent-contact-btn.php:63
#: elementor/widgets/single-agent/agent-line-btn.php:63
#: elementor/widgets/single-agent/agent-telegram-btn.php:63
#: elementor/widgets/single-agent/agent-whatsapp-btn.php:63
#: elementor/widgets/single-post/post-info.php:53
#: elementor/widgets/single-post/post-info.php:299
#: elementor/widgets/single-property/section-address.php:313
#: elementor/widgets/single-property/section-calculator.php:144
#: elementor/widgets/single-property/section-contact-bottom.php:124
#: elementor/widgets/single-property/section-details.php:407
#: elementor/widgets/single-property/section-details.php:543
#: elementor/widgets/single-property/section-energy.php:286
#: elementor/widgets/single-property/section-overview-v2.php:269
#: elementor/widgets/single-property/section-overview.php:299
#: elementor/widgets/single-property/section-overview.php:346
#: elementor/widgets/single-property/section-similar.php:210
#: elementor/widgets/taxonomies-list.php:344 functions/functions.php:248
msgid "Default"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1162 classes/class-image-sizes.php:1997
msgid "Reset to Default"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1171 classes/class-image-sizes.php:2046
msgid "Custom Image Sizes"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1172 classes/class-image-sizes.php:2047
msgid "These are custom image sizes that you have created."
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1194 classes/class-image-sizes.php:2069
#: templates/currency/currency-list.php:17
#: templates/fields-builder/index.php:13
msgid "Edit"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1204 classes/class-image-sizes.php:2082
msgid "No custom image sizes defined."
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1210 classes/class-image-sizes.php:2088
msgid "Save All Changes"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1215
msgid "Warning:"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1216
msgid ""
"Disabling any image size will prevent WordPress from generating that "
"specific thumbnail size for new uploads. This may affect how images appear "
"in certain areas of your website."
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1219
msgid ""
"If you later decide to re-enable a previously disabled size, you will need "
"to regenerate thumbnails for existing images."
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1226 classes/class-image-sizes.php:2095
msgid "Edit Custom Image Size"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1255 classes/class-image-sizes.php:2124
msgid "Enabled?"
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1374
#: ⁨classes/class-image-sizes copy.php⁩:1416
#: ⁨classes/class-image-sizes copy.php⁩:1445
#: ⁨classes/class-image-sizes copy.php⁩:1526 classes/class-image-sizes.php:2249
#: classes/class-image-sizes.php:2291 classes/class-image-sizes.php:2320
#: classes/class-image-sizes.php:2401
msgid "Security check failed."
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1385
#: ⁨classes/class-image-sizes copy.php⁩:1485 classes/class-image-sizes.php:2260
#: classes/class-image-sizes.php:2360
msgid "Please provide all required fields."
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1393
#: ⁨classes/class-image-sizes copy.php⁩:1498 classes/class-image-sizes.php:2268
#: classes/class-image-sizes.php:2373
msgid "An image size with this name already exists."
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1424
#: ⁨classes/class-image-sizes copy.php⁩:1457
#: ⁨classes/class-image-sizes copy.php⁩:1534 classes/class-image-sizes.php:2299
#: classes/class-image-sizes.php:2332 classes/class-image-sizes.php:2409
#, fuzzy
#| msgid "Invalid license key"
msgid "Invalid image size name."
msgstr "Недействительный лицензионный ключ"

#: ⁨classes/class-image-sizes copy.php⁩:1432
#: ⁨classes/class-image-sizes copy.php⁩:1465
#: ⁨classes/class-image-sizes copy.php⁩:1542 classes/class-image-sizes.php:2307
#: classes/class-image-sizes.php:2340 classes/class-image-sizes.php:2417
msgid "Image size not found."
msgstr ""

#: ⁨classes/class-image-sizes copy.php⁩:1493 classes/class-image-sizes.php:2368
msgid "Original image size not found."
msgstr ""

#: classes/class-image-sizes.php:138 classes/class-image-sizes.php:1786
#, fuzzy
#| msgid "Property Title"
msgid "Property Detail v3-4"
msgstr "Заголовок собственности"

#: classes/class-image-sizes.php:140 classes/class-image-sizes.php:1788
#, fuzzy
#| msgid "Property ID"
msgid "Property Detail v6"
msgstr "ИД объекта"

#: classes/class-image-sizes.php:141 classes/class-image-sizes.php:1789
#, fuzzy
#| msgid "Property ID"
msgid "Property Detail v7"
msgstr "ИД объекта"

#: classes/class-image-sizes.php:142 classes/class-image-sizes.php:1790
msgid "Property Detail Block Gallery"
msgstr ""

#: classes/class-image-sizes.php:147 classes/class-image-sizes.php:1827
msgid "Blog Grid"
msgstr ""

#: classes/class-image-sizes.php:887
msgid "Image Size Assignments"
msgstr ""

#: classes/class-image-sizes.php:897 classes/class-image-sizes.php:957
#: classes/class-image-sizes.php:1028 classes/class-image-sizes.php:1236
#: classes/class-image-sizes.php:1331
msgid "This image size is currently assigned to the following layouts:"
msgstr ""

#: classes/class-image-sizes.php:899
msgid "This image size is not currently assigned to any layouts."
msgstr ""

#: classes/class-image-sizes.php:908 classes/class-image-sizes.php:965
#: classes/class-image-sizes.php:1036 classes/class-image-sizes.php:1244
#: classes/class-image-sizes.php:1339
msgid "Go to Layout Assignments"
msgstr ""

#: classes/class-image-sizes.php:911 elementor/widgets/agent-card.php:955
msgid "Close"
msgstr ""

#: classes/class-image-sizes.php:956 classes/class-image-sizes.php:1027
#: classes/class-image-sizes.php:1330
msgid "Cannot Disable Image Size"
msgstr ""

#: classes/class-image-sizes.php:959 classes/class-image-sizes.php:1030
#: classes/class-image-sizes.php:1333
msgid ""
"Please unassign this image size from these layouts in the Layout Image "
"Assignments tab before disabling it."
msgstr ""

#: classes/class-image-sizes.php:1175
msgid ""
"This image size is assigned to layouts and cannot be disabled. Please "
"unassign it first."
msgstr ""

#: classes/class-image-sizes.php:1235
msgid "Cannot Delete Image Size"
msgstr ""

#: classes/class-image-sizes.php:1238
msgid ""
"Please unassign this image size from these layouts in the Layout Image "
"Assignments tab before deleting it."
msgstr ""

#: classes/class-image-sizes.php:1424 classes/class-image-sizes.php:1425
#: classes/class-image-sizes.php:1704
#, fuzzy
#| msgid "Managers"
msgid "Media Manager"
msgstr "Менеджеры"

#: classes/class-image-sizes.php:1659
msgid "Full Size (Original Image)"
msgstr ""

#: classes/class-image-sizes.php:1706
msgid ""
"Control how images are displayed throughout your website by managing "
"dimensions, cropping, and layout assignments."
msgstr ""

#: classes/class-image-sizes.php:1817
msgid "Others"
msgstr ""

#: classes/class-image-sizes.php:1819
msgid "Control image sizes for agent/agency and blog posts."
msgstr ""

#: classes/class-image-sizes.php:1940
msgid ""
"These optimized image sizes are specifically designed for Houzez theme "
"property listings, galleries, and various layout components to ensure "
"optimal performance and visual quality."
msgstr ""

#: classes/class-image-sizes.php:2484
msgid "Global (Use Media Settings)"
msgstr ""

#: classes/class-image-sizes.php:2487
msgid "Full Size (Original)"
msgstr ""

#: classes/class-invoice-post-type.php:36 classes/class-menu.php:110
#: classes/class-menu.php:111
#, fuzzy
#| msgid "New Invoice"
msgid "Invoices"
msgstr "Новый счет-фактура"

#: classes/class-invoice-post-type.php:37
#, fuzzy
#| msgid "New Invoice"
msgid "Invoice"
msgstr "Новый счет-фактура"

#: classes/class-invoice-post-type.php:39
msgid "Add New Invoice"
msgstr "Добавить новый счет-фактуру"

#: classes/class-invoice-post-type.php:40
msgid "Edit Invoice"
msgstr "Изменить счет-фактуру"

#: classes/class-invoice-post-type.php:41
msgid "New Invoice"
msgstr "Новый счет-фактура"

#: classes/class-invoice-post-type.php:42
msgid "View Invoice"
msgstr "Посмотреть счет"

#: classes/class-invoice-post-type.php:43
msgid "Search Invoice"
msgstr "Счет-фактура"

#: classes/class-invoice-post-type.php:44
msgid "No Invoice found"
msgstr "Нет счета-фактуры"

#: classes/class-invoice-post-type.php:45
msgid "No Invoice found in Trash"
msgstr "В корзине нет счета-фактуры"

#: classes/class-invoice-post-type.php:87
msgid "Invoice Title"
msgstr "Название счета"

#: classes/class-invoice-post-type.php:88
#: classes/class-property-post-type.php:512
#: elementor/traits/Houzez_Property_Cards_Traits.php:652
#: elementor/traits/Houzez_Property_Cards_Traits.php:1083
#: elementor/traits/Houzez_Property_Cards_Traits.php:1521
#: elementor/widgets/properties-recent-viewed.php:316
#: elementor/widgets/properties-recent-viewed.php:553
#: elementor/widgets/property-cards-v1.php:266
#: elementor/widgets/property-cards-v2.php:250
#: elementor/widgets/property-cards-v3.php:194
#: elementor/widgets/property-cards-v4.php:261
#: elementor/widgets/property-cards-v5.php:193
#: elementor/widgets/property-cards-v6.php:199
#: elementor/widgets/property-cards-v7.php:250
#: elementor/widgets/property-cards-v8.php:236
#: elementor/widgets/property-carousel-v1.php:241
#: elementor/widgets/property-carousel-v2.php:226
#: elementor/widgets/property-carousel-v3.php:170
#: elementor/widgets/property-carousel-v5.php:170
#: elementor/widgets/property-carousel-v6.php:171
#: elementor/widgets/property-carousel-v7.php:226
#: elementor/widgets/single-property/property-price.php:96
#: elementor/widgets/single-property/section-details.php:224
#: statistics/houzez-statistics.php:119 statistics/houzez-statistics.php:196
#: statistics/houzez-statistics.php:244 statistics/houzez-statistics.php:324
#: statistics/houzez-statistics.php:506 statistics/houzez-statistics.php:509
msgid "Price"
msgstr "Цена"

#: classes/class-invoice-post-type.php:89
msgid "Payment Method"
msgstr "Способ оплаты"

#: classes/class-invoice-post-type.php:90
msgid "Buyer"
msgstr "Покупатель"

#: classes/class-invoice-post-type.php:91
msgid "Buyer Email"
msgstr "Электронная почта покупателя"

#: classes/class-invoice-post-type.php:92
msgid "Invoice Type"
msgstr "Тип счета-фактуры"

#: classes/class-invoice-post-type.php:93
msgid "Billion For"
msgstr "Миллиард для"

#: classes/class-invoice-post-type.php:94
#: classes/class-property-post-type.php:288
#: classes/class-property-post-type.php:515
#: classes/class-property-post-type.php:591
#: elementor/traits/Houzez_Filters_Traits.php:39
#: elementor/traits/Houzez_Filters_Traits.php:328
#: elementor/widgets/listings-tabs.php:96
#: elementor/widgets/search-builder.php:82
#: elementor/widgets/search-builder.php:685
#: elementor/widgets/single-agency/agency-listings-review.php:178
#: elementor/widgets/single-agency/agency-listings.php:178
#: elementor/widgets/single-agent/agent-listings-review.php:183
#: elementor/widgets/single-agent/agent-listings.php:183
#: statistics/houzez-statistics.php:118 statistics/houzez-statistics.php:195
#: statistics/houzez-statistics.php:243 statistics/houzez-statistics.php:323
#: statistics/houzez-statistics.php:418 statistics/houzez-statistics.php:580
msgid "Status"
msgstr "Положение дел"

#: classes/class-invoice-post-type.php:95
#: classes/class-reviews-post-type.php:106
#: elementor/traits/Houzez_Property_Cards_Traits.php:1241
#: elementor/traits/Houzez_Testimonials_Traits.php:39
#: elementor/widgets/agents-grid.php:169 elementor/widgets/agents.php:167
#: elementor/widgets/partners.php:94
#: elementor/widgets/properties-recent-viewed.php:370
#: elementor/widgets/property-cards-v1.php:320
#: elementor/widgets/property-cards-v2.php:296
#: elementor/widgets/property-cards-v4.php:315
#: elementor/widgets/property-cards-v7.php:307
#: elementor/widgets/property-carousel-v1.php:295
#: elementor/widgets/property-carousel-v2.php:272
#: elementor/widgets/property-carousel-v7.php:280
#: elementor/widgets/single-post/post-info.php:76
msgid "Date"
msgstr "Дата"

#: classes/class-invoice-post-type.php:126
msgid "Direct Bank Transfer"
msgstr "Прямой банковский перевод"

#: classes/class-invoice-post-type.php:140
msgid "Listing"
msgstr ""

#: classes/class-invoice-post-type.php:142
#, fuzzy
#| msgid "New Feature"
msgid "Upgrade to Featured"
msgstr "Новая функция"

#: classes/class-invoice-post-type.php:165
msgid "Not Paid"
msgstr "Не оплачен"

#: classes/class-invoice-post-type.php:167
msgid "Paid"
msgstr "оплаченный"

#: classes/class-membership-post-type.php:33
#: classes/class-membership-post-type.php:34 classes/class-menu.php:100
#: classes/class-menu.php:101 classes/class-user-packages-post-type.php:40
msgid "Packages"
msgstr "пакеты"

#: classes/class-membership-post-type.php:35
msgid "Add New Package"
msgstr "Добавить новый пакет"

#: classes/class-membership-post-type.php:36
msgid "Add Packages"
msgstr "Добавить пакеты"

#: classes/class-membership-post-type.php:37
msgid "Edit Packages"
msgstr "Редактировать пакеты"

#: classes/class-membership-post-type.php:38
msgid "Edit Package"
msgstr "Изменить пакет"

#: classes/class-membership-post-type.php:39
msgid "New Packages"
msgstr "Новые пакеты"

#: classes/class-membership-post-type.php:40
#: classes/class-membership-post-type.php:41
msgid "View Packages"
msgstr "Просмотр пакетов"

#: classes/class-membership-post-type.php:42
msgid "Search Packages"
msgstr "Пакеты поиска"

#: classes/class-membership-post-type.php:43
#: classes/class-membership-post-type.php:44
msgid "No Packages found"
msgstr "Пакеты найдены не найдены"

#: classes/class-membership-post-type.php:45
msgid "Parent Package"
msgstr "Родительский пакет"

#: classes/class-menu.php:28
msgid "Real Estate"
msgstr ""

#: classes/class-menu.php:41 classes/class-property-post-type.php:212
msgid "Add New Property"
msgstr "Добавить новое свойство"

#: classes/class-menu.php:42 classes/class-property-post-type.php:215
msgid "New Property"
msgstr "Новая недвижимость"

#: classes/class-menu.php:82 classes/class-menu.php:83
#: classes/class-partners-post-type.php:31 classes/class-post-types.php:71
#: elementor/widgets/partners.php:36
msgid "Partners"
msgstr "партнеры"

#: classes/class-menu.php:91 classes/class-menu.php:92
#: classes/class-reviews-post-type.php:30
#: elementor/template-part/single-agency/agency-listings-review.php:134
#: elementor/template-part/single-agency/agency-reviews.php:24
#: elementor/template-part/single-agent/agent-listings-review.php:68
#: elementor/template-part/single-agent/agent-reviews.php:24
msgid "Reviews"
msgstr ""

#: classes/class-menu.php:121 classes/class-menu.php:122
#, fuzzy
#| msgid "Users Package Info"
msgid "Packages Info"
msgstr "Информация о пакете"

#: classes/class-partners-post-type.php:32
msgid "Partner"
msgstr "партнер"

#: classes/class-partners-post-type.php:34
msgid "Add New Partner"
msgstr "Добавить новый партнер"

#: classes/class-partners-post-type.php:35
msgid "Edit Partner"
msgstr "Изменить партнера"

#: classes/class-partners-post-type.php:36
msgid "New Partner"
msgstr "Новый партнер"

#: classes/class-partners-post-type.php:37
msgid "View Partner"
msgstr "Просмотреть партнера"

#: classes/class-partners-post-type.php:38
msgid "Search Partner"
msgstr "Поиск партнера"

#: classes/class-partners-post-type.php:39
msgid "No Partner found"
msgstr "Партнер не нашел"

#: classes/class-partners-post-type.php:40
msgid "No Partner found in Trash"
msgstr "Партнер не найден в корзине"

#: classes/class-permalinks.php:47
#, fuzzy
#| msgid "Permalinks"
msgid "Update Permalinks"
msgstr "Permalinks"

#: classes/class-permalinks.php:61
msgid "Permalinks"
msgstr "Permalinks"

#: classes/class-permalinks.php:64
msgid "Property Slug"
msgstr "Недвижимость Slug"

#: classes/class-permalinks.php:66
msgid "Property Type Slug"
msgstr "Тип недвижимости Slug"

#: classes/class-permalinks.php:68
msgid "Property Feature Slug"
msgstr "Свойство свойств Slug"

#: classes/class-permalinks.php:70
msgid "Property Status Slug"
msgstr "Статус свойства Slug"

#: classes/class-permalinks.php:72
#, fuzzy
#| msgid "Property City Slug"
msgid "Property Country Slug"
msgstr "Недвижимость City Slug"

#: classes/class-permalinks.php:74
msgid "Property State Slug"
msgstr "Собственность State Slug"

#: classes/class-permalinks.php:76
msgid "Property City Slug"
msgstr "Недвижимость City Slug"

#: classes/class-permalinks.php:78
msgid "Property Area Slug"
msgstr "Область собственности Slug"

#: classes/class-permalinks.php:80
#, fuzzy
#| msgid "Property State Slug"
msgid "Property Label Slug"
msgstr "Собственность State Slug"

#: classes/class-permalinks.php:82
#, fuzzy
#| msgid "Agent ID"
msgid "Agent Slug"
msgstr "Агент ID"

#: classes/class-permalinks.php:84
#, fuzzy
#| msgid "Agency"
msgid "Agency Slug"
msgstr "Агентство"

#: classes/class-permalinks.php:126
msgid "Set up custom permalinks for the property section on your site."
msgstr ""
"Настройте пользовательские постоянные ссылки для раздела свойств вашего "
"сайта."

#: classes/class-post-types.php:46 classes/class-taxonomies.php:47
msgid "Update Settings"
msgstr "Обновить настройки"

#: classes/class-post-types.php:60
msgid "Custom Post Types"
msgstr ""

#: classes/class-post-types.php:67
msgid "Houzez Packages"
msgstr "Пакеты Houzez"

#: classes/class-post-types.php:69
msgid "Houzez Invoices"
msgstr "Houzez Invoices"

#: classes/class-post-types.php:73 classes/class-testimonials-post-type.php:33
msgid "Testimonials"
msgstr "Отзывы"

#: classes/class-post-types.php:75
#, fuzzy
#| msgid "Users Package Info"
msgid "User Packages Info"
msgstr "Информация о пакете"

#: classes/class-post-types.php:112
msgid ""
"Disable Custom Post Types which you do not want to show(if disabled then "
"these will not show on back-end and front-end)"
msgstr ""

#: classes/class-post-types.php:129 classes/class-post-types.php:148
#: classes/class-post-types.php:166 classes/class-post-types.php:184
#: classes/class-post-types.php:202 classes/class-post-types.php:220
#: classes/class-post-types.php:238 classes/class-taxonomies.php:121
#: classes/class-taxonomies.php:139 classes/class-taxonomies.php:157
#: classes/class-taxonomies.php:175
msgid "Disabled"
msgstr ""

#: classes/class-property-post-type.php:210
#: elementor/widgets/google-map.php:195
#: elementor/widgets/properties-ajax-tabs.php:548
#: elementor/widgets/properties.php:42
#: elementor/widgets/property-cards-v1.php:83
#: elementor/widgets/property-cards-v5.php:82
#: elementor/widgets/property-cards-v6.php:83
#: elementor/widgets/property-cards-v7.php:82
#: elementor/widgets/property-cards-v8.php:82
msgid "Properties"
msgstr "свойства"

#: classes/class-property-post-type.php:211
msgid "Property"
msgstr "Имущество"

#: classes/class-property-post-type.php:214
msgid "Edit Property"
msgstr "Изменить свойство"

#: classes/class-property-post-type.php:216
msgid "View Property"
msgstr "Просмотр недвижимости"

#: classes/class-property-post-type.php:217
msgid "Search Property"
msgstr "Поиск недвижимости"

#: classes/class-property-post-type.php:218
#: statistics/houzez-statistics.php:183 statistics/houzez-statistics.php:311
msgid "No Property found"
msgstr "Нет Найденное свойство"

#: classes/class-property-post-type.php:219
msgid "No Property found in Trash"
msgstr "Нет собственности, найденной в корзине"

#: classes/class-property-post-type.php:241
#, fuzzy
#| msgid "Properties"
msgid "properties"
msgstr "свойства"

#: classes/class-property-post-type.php:264
#: classes/class-property-post-type.php:582
#: elementor/traits/Houzez_Filters_Traits.php:26
#: elementor/traits/Houzez_Filters_Traits.php:312
#: elementor/widgets/listings-tabs.php:97
#: elementor/widgets/search-builder.php:81
#: elementor/widgets/search-builder.php:686
#: elementor/widgets/single-agency/agency-listings-review.php:179
#: elementor/widgets/single-agency/agency-listings.php:179
#: elementor/widgets/single-agent/agent-listings-review.php:184
#: elementor/widgets/single-agent/agent-listings.php:184
#: elementor/widgets/single-post/post-info.php:71
#: statistics/houzez-statistics.php:117 statistics/houzez-statistics.php:194
#: statistics/houzez-statistics.php:242 statistics/houzez-statistics.php:322
#: statistics/houzez-statistics.php:410 statistics/houzez-statistics.php:581
#: templates/fields-builder/fields-form.php:39
msgid "Type"
msgstr "Тип"

#: classes/class-property-post-type.php:265
msgid "Add New Type"
msgstr "Добавить новый тип"

#: classes/class-property-post-type.php:266
msgid "New Type"
msgstr "Новый тип"

#: classes/class-property-post-type.php:289
msgid "Add New Status"
msgstr "Добавить новый статус"

#: classes/class-property-post-type.php:290
msgid "New Status"
msgstr "Новый статус"

#: classes/class-property-post-type.php:313
#: elementor/widgets/search-builder.php:87
msgid "Features"
msgstr "Особенности"

#: classes/class-property-post-type.php:314
msgid "Add New Feature"
msgstr "Добавить новую функцию"

#: classes/class-property-post-type.php:315
msgid "New Feature"
msgstr "Новая функция"

#: classes/class-property-post-type.php:338
#: elementor/traits/Houzez_Filters_Traits.php:52
#: elementor/traits/Houzez_Filters_Traits.php:344
#: elementor/traits/Houzez_Property_Cards_Traits.php:724
#: elementor/traits/Houzez_Property_Cards_Traits.php:1613
#: elementor/widgets/contact-form.php:314
#: elementor/widgets/inquiry-form.php:301
#: elementor/widgets/properties-recent-viewed.php:662
#: elementor/widgets/search-builder.php:640
#: elementor/widgets/single-agency/agency-review.php:63
#: elementor/widgets/single-agent/agent-review.php:63
#: elementor/widgets/single-property/section-contact-bottom.php:480
#: elementor/widgets/single-property/section-energy.php:79
#: elementor/widgets/single-property/section-review.php:63
#: elementor/widgets/single-property/section-schedule-tour.php:144
msgid "Labels"
msgstr "Этикетки"

#: classes/class-property-post-type.php:339
msgid "Add New Label"
msgstr "Добавить новую метку"

#: classes/class-property-post-type.php:340
msgid "New Label"
msgstr "Новая метка"

#: classes/class-property-post-type.php:365
#: classes/class-property-post-type.php:573
#: classes/class-property-post-type.php:894 classes/class-taxonomies.php:67
#: elementor/traits/Houzez_Filters_Traits.php:95
#: elementor/traits/Houzez_Filters_Traits.php:391
#: elementor/widgets/agents-grid.php:132 elementor/widgets/agents.php:131
#: elementor/widgets/contact-form.php:96 elementor/widgets/google-map.php:283
#: elementor/widgets/grid-builder.php:291 elementor/widgets/grids.php:200
#: elementor/widgets/inquiry-form.php:98 elementor/widgets/listings-tabs.php:98
#: elementor/widgets/mapbox.php:267 elementor/widgets/open-street-map.php:273
#: elementor/widgets/properties-ajax-tabs.php:375
#: elementor/widgets/search-builder.php:89
#: elementor/widgets/search-builder.php:687
#: elementor/widgets/single-agency/agency-listings-review.php:180
#: elementor/widgets/single-agency/agency-listings.php:180
#: elementor/widgets/single-agent/agent-listings-review.php:185
#: elementor/widgets/single-agent/agent-listings.php:185
#: elementor/widgets/single-property/property-address.php:48
#: elementor/widgets/single-property/property-title-area.php:51
#: elementor/widgets/single-property/section-address.php:166
#: elementor/widgets/single-property/section-toparea-v1.php:51
#: elementor/widgets/single-property/section-toparea-v2.php:50
#: elementor/widgets/single-property/section-toparea-v3.php:55
#: elementor/widgets/single-property/section-toparea-v5.php:50
#: elementor/widgets/single-property/section-toparea-v6.php:51
#: elementor/widgets/single-property/section-toparea-v7.php:50
#: statistics/houzez-statistics.php:116 statistics/houzez-statistics.php:193
#: statistics/houzez-statistics.php:241 statistics/houzez-statistics.php:321
msgid "City"
msgstr "город"

#: classes/class-property-post-type.php:392 classes/class-taxonomies.php:69
#: elementor/traits/Houzez_Filters_Traits.php:110
#: elementor/traits/Houzez_Filters_Traits.php:407
#: elementor/widgets/google-map.php:296 elementor/widgets/grid-builder.php:308
#: elementor/widgets/grids.php:216
#: elementor/widgets/header-footer/area-switcher.php:165
#: elementor/widgets/inquiry-form.php:99 elementor/widgets/mapbox.php:280
#: elementor/widgets/open-street-map.php:286
#: elementor/widgets/properties-ajax-tabs.php:388
#: elementor/widgets/search-builder.php:90
#: elementor/widgets/single-property/section-address.php:175
#: statistics/houzez-statistics.php:585
msgid "Area"
msgstr "Площадь"

#: classes/class-property-post-type.php:393
#, fuzzy
#| msgid "Add New Agent"
msgid "Add New Area"
msgstr "Добавить новый агент"

#: classes/class-property-post-type.php:394
#, fuzzy
#| msgid "New Agent"
msgid "New Area"
msgstr "Новый агент"

#: classes/class-property-post-type.php:418
#: classes/class-property-post-type.php:974 classes/class-taxonomies.php:65
#: elementor/traits/Houzez_Filters_Traits.php:65
#: elementor/traits/Houzez_Filters_Traits.php:359
#: elementor/widgets/contact-form.php:95 elementor/widgets/google-map.php:257
#: elementor/widgets/grid-builder.php:257 elementor/widgets/grids.php:168
#: elementor/widgets/inquiry-form.php:97 elementor/widgets/mapbox.php:241
#: elementor/widgets/open-street-map.php:247
#: elementor/widgets/properties-ajax-tabs.php:349
#: elementor/widgets/search-builder.php:88
#: elementor/widgets/single-property/property-address.php:46
#: elementor/widgets/single-property/property-title-area.php:49
#: elementor/widgets/single-property/section-address.php:148
#: elementor/widgets/single-property/section-toparea-v1.php:49
#: elementor/widgets/single-property/section-toparea-v2.php:48
#: elementor/widgets/single-property/section-toparea-v3.php:53
#: elementor/widgets/single-property/section-toparea-v5.php:48
#: elementor/widgets/single-property/section-toparea-v6.php:49
#: elementor/widgets/single-property/section-toparea-v7.php:48
#: statistics/houzez-statistics.php:584
msgid "Country"
msgstr "Страна"

#: classes/class-property-post-type.php:419
#, fuzzy
#| msgid "Add New City"
msgid "Add New Country"
msgstr "Добавить новый город"

#: classes/class-property-post-type.php:420
#, fuzzy
#| msgid "Country"
msgid "New Country"
msgstr "Страна"

#: classes/class-property-post-type.php:444
#: elementor/traits/Houzez_Filters_Traits.php:80
#: elementor/traits/Houzez_Filters_Traits.php:375
#: elementor/widgets/contact-form.php:97 elementor/widgets/google-map.php:270
#: elementor/widgets/grid-builder.php:274 elementor/widgets/grids.php:184
#: elementor/widgets/inquiry-form.php:100 elementor/widgets/mapbox.php:254
#: elementor/widgets/open-street-map.php:260
#: elementor/widgets/properties-ajax-tabs.php:362
#: elementor/widgets/search-builder.php:91
#: elementor/widgets/single-property/property-address.php:47
#: elementor/widgets/single-property/property-title-area.php:50
#: elementor/widgets/single-property/section-address.php:157
#: elementor/widgets/single-property/section-toparea-v1.php:50
#: elementor/widgets/single-property/section-toparea-v2.php:49
#: elementor/widgets/single-property/section-toparea-v3.php:54
#: elementor/widgets/single-property/section-toparea-v5.php:49
#: elementor/widgets/single-property/section-toparea-v6.php:50
#: elementor/widgets/single-property/section-toparea-v7.php:49
#: statistics/houzez-statistics.php:434 statistics/houzez-statistics.php:583
msgid "State"
msgstr "состояние"

#: classes/class-property-post-type.php:445
#, fuzzy
#| msgid "Add New Status"
msgid "Add New State"
msgstr "Добавить новый статус"

#: classes/class-property-post-type.php:446
#, fuzzy
#| msgid "New Status"
msgid "New State"
msgstr "Новый статус"

#: classes/class-property-post-type.php:510
msgid "Info"
msgstr ""

#: classes/class-property-post-type.php:513
#: classes/class-property-post-type.php:1586
#: elementor/widgets/single-property/featured-label.php:207
msgid "Featured"
msgstr "Рекомендуемые"

#: classes/class-property-post-type.php:514
msgid "Posted"
msgstr "Сообщение"

#: classes/class-property-post-type.php:521
msgid "Translations"
msgstr ""

#: classes/class-property-post-type.php:563
#: classes/class-property-post-type.php:601
#: classes/class-property-post-type.php:667
#: classes/class-property-post-type.php:676
#: classes/class-property-post-type.php:685
#: statistics/houzez-statistics.php:169 statistics/houzez-statistics.php:296
msgid "NA"
msgstr "Не Доступно"

#: classes/class-property-post-type.php:598
#: classes/class-property-post-type.php:601
msgid "Listing ID"
msgstr ""

#: classes/class-property-post-type.php:610
msgid "Expires"
msgstr "Истекает"

#: classes/class-property-post-type.php:640
msgid "No Address Provided!"
msgstr "Никакой адрес не предоставлен!"

#: classes/class-property-post-type.php:700
#, php-format
msgid "by %s"
msgstr "% s"

#: classes/class-property-post-type.php:709
#, fuzzy
#| msgid "by a guest"
msgid "a guest"
msgstr "гостем"

#: classes/class-property-post-type.php:746
#: classes/class-reviews-post-type.php:163
msgid "Approve"
msgstr "Одобрить"

#: classes/class-property-post-type.php:758
#, fuzzy
#| msgid "Approve"
msgid "Disapprove"
msgstr "Одобрить"

#: classes/class-property-post-type.php:770
#, fuzzy
#| msgid "Featured"
msgid "Mark as Featured"
msgstr "Рекомендуемые"

#: classes/class-property-post-type.php:782
msgid "Remove from Featured"
msgstr ""

#: classes/class-property-post-type.php:794
msgid "Expire"
msgstr "истекать"

#: classes/class-property-post-type.php:807
msgid "Mark as Sold"
msgstr ""

#: classes/class-property-post-type.php:819
msgid "Put on Hold"
msgstr ""

#: classes/class-property-post-type.php:831
msgid "Go Live"
msgstr ""

#: classes/class-property-post-type.php:843
msgid "Duplicate"
msgstr ""

#: classes/class-property-post-type.php:855
msgid "Publish"
msgstr ""

#: classes/class-property-post-type.php:897
#: classes/class-property-post-type.php:936
#: classes/class-property-post-type.php:977
msgid "Posts"
msgstr "Сообщений"

#: classes/class-property-post-type.php:933
msgid "County/State"
msgstr "Область /"

#: classes/class-property-post-type.php:1446
#: elementor/widgets/search-builder.php:478
msgid "All Types"
msgstr "Все типы"

#: classes/class-property-post-type.php:1479
#: elementor/widgets/search-builder.php:489
msgid "All Status"
msgstr "Все статусы"

#: classes/class-property-post-type.php:1512
msgid "All Labels"
msgstr "Все ярлыки"

#: classes/class-property-post-type.php:1545
#: elementor/widgets/search-builder.php:471
msgid "All Cities"
msgstr "Все города"

#: classes/class-property-post-type.php:1583
#: elementor/controls/details-control.php:44
#: elementor/widgets/property-by-id.php:136
#: elementor/widgets/property-meta-data.php:55
#: elementor/widgets/search-builder.php:100
#: elementor/widgets/search-builder.php:547
#: elementor/widgets/search-builder.php:548
#: elementor/widgets/single-property/section-details.php:216
#: elementor/widgets/single-property/section-overview-v2.php:58
#: elementor/widgets/single-property/section-overview.php:57
#: statistics/houzez-statistics.php:120 statistics/houzez-statistics.php:197
#: statistics/houzez-statistics.php:245 statistics/houzez-statistics.php:325
msgid "Property ID"
msgstr "ИД объекта"

#: classes/class-rates.php:134
msgid ""
"There was a problem to update currencies and exchange rates, Please check "
"your API key is valid and you have usage quota."
msgstr ""

#: classes/class-reviews-post-type.php:31
#: elementor/template-part/single-agency/agency-reviews.php:26
#: elementor/template-part/single-agent/agent-reviews.php:26
msgid "Review"
msgstr ""

#: classes/class-reviews-post-type.php:33
#, fuzzy
#| msgid "Add New Invoice"
msgid "Add New Review"
msgstr "Добавить новый счет-фактуру"

#: classes/class-reviews-post-type.php:34
#, fuzzy
#| msgid "Edit Invoice"
msgid "Edit Review"
msgstr "Изменить счет-фактуру"

#: classes/class-reviews-post-type.php:35
#, fuzzy
#| msgid "New Invoice"
msgid "New Review"
msgstr "Новый счет-фактура"

#: classes/class-reviews-post-type.php:36
#, fuzzy
#| msgid "View Invoice"
msgid "View Review"
msgstr "Посмотреть счет"

#: classes/class-reviews-post-type.php:37
#, fuzzy
#| msgid "Search Invoice"
msgid "Search Review"
msgstr "Счет-фактура"

#: classes/class-reviews-post-type.php:38
#, fuzzy
#| msgid "No Invoice found"
msgid "No Review found"
msgstr "Нет счета-фактуры"

#: classes/class-reviews-post-type.php:39
#, fuzzy
#| msgid "No Invoice found in Trash"
msgid "No Review found in Trash"
msgstr "В корзине нет счета-фактуры"

#: classes/class-reviews-post-type.php:103
msgid "Stars"
msgstr ""

#: classes/class-reviews-post-type.php:104
msgid "Review On"
msgstr ""

#: classes/class-reviews-post-type.php:175
#, fuzzy
#| msgid "Approve"
msgid "Unapprove"
msgstr "Одобрить"

#: classes/class-taxonomies.php:61
msgid "Taxonomies"
msgstr ""

#: classes/class-taxonomies.php:71
msgid "County / State"
msgstr "Графство / Государство"

#: classes/class-taxonomies.php:105
msgid ""
"Disable Taxonomies which you do not want to show(if disabled then these will "
"not show on back-end and front-end)"
msgstr ""

#: classes/class-testimonials-post-type.php:34
msgid "Testimonial"
msgstr "свидетельство"

#: classes/class-testimonials-post-type.php:36
msgid "Add New Testimonial"
msgstr "Добавить новый отзыв"

#: classes/class-testimonials-post-type.php:37
msgid "Edit Testimonial"
msgstr "Изменить отзыв"

#: classes/class-testimonials-post-type.php:38
msgid "New Testimonial"
msgstr "Новый отзыв"

#: classes/class-testimonials-post-type.php:39
msgid "View Testimonial"
msgstr "Посмотреть отзыв"

#: classes/class-testimonials-post-type.php:41
msgid "No Testimonial found"
msgstr "Нет отзывов"

#: classes/class-testimonials-post-type.php:42
msgid "No Testimonial found in Trash"
msgstr "Нет отзывов, найденных в корзине"

#: classes/class-user-packages-post-type.php:39
msgid "Users Package Info"
msgstr "Информация о пакете"

#: classes/class-user-packages-post-type.php:86
msgid "Package Holder"
msgstr "Держатель для упаковки"

#: classes/class-user-packages-post-type.php:87
msgid "Package"
msgstr "пакет"

#: classes/class-user-packages-post-type.php:88
msgid "Available Listings"
msgstr "Доступные списки"

#: classes/class-user-packages-post-type.php:89
msgid "Featured Available"
msgstr "Избранные"

#: classes/class-user-packages-post-type.php:90
msgid "Activation"
msgstr "активация"

#: classes/class-user-packages-post-type.php:91
msgid "Expiry"
msgstr "истечение"

#: demo-data/demo-importer.php:3
msgid "Best if used on new WordPress install."
msgstr ""

#: demo-data/demo-importer.php:4
msgid "Images are for demo purpose only."
msgstr ""

#: demo-data/demo-importer.php:27
msgid "Demo Import"
msgstr ""

#: demo-data/demo-importer.php:28
msgid "Demo Importer"
msgstr ""

#: demo-data/demo-importer.php:53 demo-data/demo-importer.php:70
#: demo-data/demo-importer.php:87 demo-data/demo-importer.php:104
#: demo-data/demo-importer.php:121 demo-data/demo-importer.php:138
#: demo-data/demo-importer.php:155 demo-data/demo-importer.php:172
#: demo-data/demo-importer.php:189 demo-data/demo-importer.php:206
#: demo-data/demo-importer.php:223 demo-data/demo-importer.php:240
#: demo-data/demo-importer.php:257 demo-data/demo-importer.php:274
#: demo-data/demo-importer.php:291 demo-data/demo-importer.php:308
#: demo-data/demo-importer.php:325 demo-data/demo-importer.php:342
#: demo-data/demo-importer.php:359 demo-data/demo-importer.php:376
#: demo-data/demo-importer.php:393 demo-data/demo-importer.php:409
#: demo-data/demo-importer.php:425 demo-data/demo-importer.php:441
#: demo-data/demo-importer.php:457 demo-data/demo-importer.php:473
#: demo-data/demo-importer.php:489 demo-data/demo-importer.php:505
#: demo-data/demo-importer.php:521 demo-data/demo-importer.php:537
#: demo-data/demo-importer.php:553 demo-data/demo-importer.php:569
#: demo-data/demo-importer.php:585 demo-data/demo-importer.php:601
#: demo-data/demo-importer.php:617 demo-data/demo-importer.php:634
#: demo-data/demo-importer.php:651 demo-data/demo-importer.php:668
#: demo-data/demo-importer.php:685 demo-data/demo-importer.php:702
#: demo-data/demo-importer.php:719 demo-data/demo-importer.php:736
#: demo-data/demo-importer.php:753 demo-data/demo-importer.php:769
#: demo-data/demo-importer.php:785
msgid ""
"After you import this demo, you will have to setup the slider separately."
msgstr ""

#: elementor/controls/address-control.php:37
#: elementor/widgets/single-property/property-address.php:19
#: elementor/widgets/single-property/property-title-area.php:316
#: elementor/widgets/single-property/section-toparea-v1.php:218
#: elementor/widgets/single-property/section-toparea-v2.php:218
#: elementor/widgets/single-property/section-toparea-v3.php:243
#: elementor/widgets/single-property/section-toparea-v5.php:218
#: elementor/widgets/single-property/section-toparea-v6.php:263
#: elementor/widgets/single-property/section-toparea-v7.php:238
#, fuzzy
#| msgid "Property Area Slug"
msgid "Property Address"
msgstr "Область собственности Slug"

#: elementor/controls/address-control.php:38
#: elementor/widgets/property-meta-data.php:51
#: elementor/widgets/single-agency/agency-single-stats.php:69
#: elementor/widgets/single-agent/agent-single-stats.php:69
#: elementor/widgets/single-property/section-similar.php:85
#, fuzzy
#| msgid "No Property found"
msgid "Property Country"
msgstr "Нет Найденное свойство"

#: elementor/controls/address-control.php:39
#: elementor/widgets/property-meta-data.php:53
#: elementor/widgets/single-agency/agency-single-stats.php:68
#: elementor/widgets/single-agent/agent-single-stats.php:68
#: elementor/widgets/single-property/section-similar.php:86
#, fuzzy
#| msgid "Property State Slug"
msgid "Property State"
msgstr "Собственность State Slug"

#: elementor/controls/address-control.php:40
#: elementor/widgets/property-meta-data.php:52
#: elementor/widgets/single-agency/agency-single-stats.php:67
#: elementor/widgets/single-agent/agent-single-stats.php:67
#: elementor/widgets/single-property/section-similar.php:87
#, fuzzy
#| msgid "Property City Slug"
msgid "Property City"
msgstr "Недвижимость City Slug"

#: elementor/controls/address-control.php:41
#: elementor/widgets/property-meta-data.php:54
#: elementor/widgets/single-property/section-similar.php:88
#, fuzzy
#| msgid "Property Area Slug"
msgid "Property Area"
msgstr "Область собственности Slug"

#: elementor/controls/address-control.php:42
#, fuzzy
#| msgid "Property Title"
msgid "Property Zip/Postal Code"
msgstr "Заголовок собственности"

#: elementor/controls/autocomplete.php:50
#: elementor/widgets/search-builder.php:555
#, fuzzy
#| msgid "Search Agent"
msgid "Search"
msgstr "Агент поиска"

#: elementor/controls/details-control.php:45
#: elementor/widgets/single-property/property-price.php:20
#: elementor/widgets/single-property/property-title-area.php:398
#: elementor/widgets/single-property/section-toparea-v1.php:300
#: elementor/widgets/single-property/section-toparea-v2.php:300
#: elementor/widgets/single-property/section-toparea-v3.php:325
#: elementor/widgets/single-property/section-toparea-v5.php:300
#: elementor/widgets/single-property/section-toparea-v6.php:345
#: elementor/widgets/single-property/section-toparea-v7.php:320
#, fuzzy
#| msgid "Property Title"
msgid "Property Price"
msgstr "Заголовок собственности"

#: elementor/controls/details-control.php:46
#: elementor/widgets/single-property/section-details.php:233
#, fuzzy
#| msgid "Property Title"
msgid "Property Size"
msgstr "Заголовок собственности"

#: elementor/controls/details-control.php:47
#, fuzzy
#| msgid "Property Title"
msgid "Property Land Size"
msgstr "Заголовок собственности"

#: elementor/controls/details-control.php:48
#, fuzzy
#| msgid "Properties"
msgid "Property Bedrooms"
msgstr "свойства"

#: elementor/controls/details-control.php:49
#, fuzzy
#| msgid "Property"
msgid "Property Rooms"
msgstr "Имущество"

#: elementor/controls/details-control.php:50
#, fuzzy
#| msgid "Bathrooms"
msgid "Property Bathrooms"
msgstr "В ванных комнатах"

#: elementor/controls/details-control.php:51
#, fuzzy
#| msgid "Property Area Slug"
msgid "Property Garages"
msgstr "Область собственности Slug"

#: elementor/controls/details-control.php:52
#, fuzzy
#| msgid "Property Area Slug"
msgid "Property Garage Size"
msgstr "Область собственности Slug"

#: elementor/controls/details-control.php:53
#, fuzzy
#| msgid "Property Area Slug"
msgid "Property Year Built"
msgstr "Область собственности Slug"

#: elementor/controls/details-control.php:54
#: elementor/widgets/inquiry-form.php:86
#: elementor/widgets/properties-slider.php:115
#: elementor/widgets/property-meta-data.php:48
#: elementor/widgets/search-builder.php:488
#: elementor/widgets/single-agency/agency-single-stats.php:66
#: elementor/widgets/single-agent/agent-single-stats.php:66
#: elementor/widgets/single-property/section-details.php:346
#: elementor/widgets/single-property/section-overview-v2.php:57
#: elementor/widgets/single-property/section-overview.php:56
#: elementor/widgets/single-property/section-similar.php:82
#: elementor/widgets/single-property/status-label.php:19
#, fuzzy
#| msgid "Property Status Slug"
msgid "Property Status"
msgstr "Статус свойства Slug"

#: elementor/controls/details-control.php:55
#: elementor/traits/Houzez_Property_Cards_Traits.php:1121
#: elementor/widgets/inquiry-form.php:85
#: elementor/widgets/properties-recent-viewed.php:334
#: elementor/widgets/properties-slider.php:103
#: elementor/widgets/property-cards-v1.php:284
#: elementor/widgets/property-cards-v2.php:268
#: elementor/widgets/property-cards-v4.php:279
#: elementor/widgets/property-cards-v5.php:203
#: elementor/widgets/property-cards-v7.php:268
#: elementor/widgets/property-cards-v8.php:254
#: elementor/widgets/property-carousel-v1.php:259
#: elementor/widgets/property-carousel-v2.php:244
#: elementor/widgets/property-carousel-v5.php:180
#: elementor/widgets/property-carousel-v7.php:244
#: elementor/widgets/property-meta-data.php:49
#: elementor/widgets/search-builder.php:477
#: elementor/widgets/single-agency/agency-single-stats.php:65
#: elementor/widgets/single-agent/agent-single-stats.php:65
#: elementor/widgets/single-property/section-details.php:337
#: elementor/widgets/single-property/section-overview-v2.php:50
#: elementor/widgets/single-property/section-overview-v2.php:193
#: elementor/widgets/single-property/section-overview.php:48
#: elementor/widgets/single-property/section-overview.php:192
#: elementor/widgets/single-property/section-similar.php:81
#, fuzzy
#| msgid "Property Type Slug"
msgid "Property Type"
msgstr "Тип недвижимости Slug"

#: elementor/elementor.php:76
#, fuzzy
#| msgid "Houzez Payments"
msgid "Houzez Elements"
msgstr "Houzez Payments"

#: elementor/elementor.php:84 elementor/elementor.php:92
msgid "Houzez Header & Footer"
msgstr ""

#: elementor/elementor.php:100 elementor/elementor.php:108
#, fuzzy
#| msgid "View Property"
msgid "Houzez Single Property"
msgstr "Просмотр недвижимости"

#: elementor/elementor.php:116 elementor/elementor.php:124
msgid "Houzez Single Agent"
msgstr ""

#: elementor/elementor.php:132 elementor/elementor.php:140
msgid "Houzez Single Agency"
msgstr ""

#: elementor/elementor.php:148
#, fuzzy
#| msgid "Houzez Invoice"
msgid "Houzez Loop Builder"
msgstr "Houzez Invoice"

#: elementor/elementor.php:156 elementor/elementor.php:164
#, fuzzy
#| msgid "Houzez Statistic"
msgid "Houzez Single Post"
msgstr "Houzez Statistics"

#: elementor/elementor.php:172
msgid "Houzez Sidebar/Footer Widgets "
msgstr ""

#: elementor/tags/property-content.php:9
#: elementor/widgets/single-property/property-content.php:16
#, fuzzy
#| msgid "Property Title"
msgid "Property Content"
msgstr "Заголовок собственности"

#: elementor/tags/property-excerpt.php:9
#: elementor/widgets/single-property/property-excerpt.php:16
#, fuzzy
#| msgid "Property Title"
msgid "Property Excerpt"
msgstr "Заголовок собственности"

#: elementor/tags/property-title.php:9
#: elementor/traits/Houzez_Property_Cards_Traits.php:977
#: elementor/widgets/properties-recent-viewed.php:281
#: elementor/widgets/property-cards-v1.php:219
#: elementor/widgets/property-cards-v2.php:203
#: elementor/widgets/property-cards-v3.php:175
#: elementor/widgets/property-cards-v4.php:214
#: elementor/widgets/property-cards-v5.php:175
#: elementor/widgets/property-cards-v6.php:181
#: elementor/widgets/property-cards-v7.php:215
#: elementor/widgets/property-cards-v8.php:189
#: elementor/widgets/property-carousel-v1.php:194
#: elementor/widgets/property-carousel-v2.php:179
#: elementor/widgets/property-carousel-v3.php:151
#: elementor/widgets/property-carousel-v5.php:152
#: elementor/widgets/property-carousel-v6.php:153
#: elementor/widgets/property-carousel-v7.php:191
#: elementor/widgets/single-property/property-title-area.php:196
#: elementor/widgets/single-property/property-title.php:17
#: elementor/widgets/single-property/section-toparea-v1.php:98
#: elementor/widgets/single-property/section-toparea-v2.php:98
#: elementor/widgets/single-property/section-toparea-v3.php:123
#: elementor/widgets/single-property/section-toparea-v5.php:98
#: elementor/widgets/single-property/section-toparea-v6.php:143
#: elementor/widgets/single-property/section-toparea-v7.php:118
#: statistics/houzez-statistics.php:113 statistics/houzez-statistics.php:190
#: statistics/houzez-statistics.php:238 statistics/houzez-statistics.php:318
msgid "Property Title"
msgstr "Заголовок собственности"

#: elementor/tags/test.php:29
#, fuzzy
#| msgid "Featured Available"
msgid "Server Variable"
msgstr "Избранные"

#: elementor/tags/test.php:82
msgid "Param Name"
msgstr ""

#: elementor/template-part/listing-tabs.php:40
#: elementor/traits/Houzez_Filters_Traits.php:202
#: elementor/widgets/listings-tabs.php:356
#: elementor/widgets/properties-ajax-tabs.php:478
#: extensions/meta-box/inc/fields/select.php:80
msgid "All"
msgstr ""

#: elementor/template-part/single-agency/agency-listings-review.php:122
#: elementor/template-part/single-agent/agent-listings-review.php:62
#, fuzzy
#| msgid "Settings"
msgid "Listings"
msgstr "настройки"

#: elementor/template-part/single-agency/agency-profile-v1.php:59
#: elementor/template-part/single-agent/agent-profile-v1.php:75
#, fuzzy
#| msgid "Buyer Email"
msgid "Send Email"
msgstr "Электронная почта покупателя"

#: elementor/template-part/single-agency/agency-profile-v1.php:65
#: elementor/template-part/single-agent/agent-profile-v1.php:81
#: elementor/widgets/agent-card.php:896
msgid "Call"
msgstr ""

#: elementor/template-part/single-agency/agency-profile-v2.php:34
#: elementor/template-part/single-agent/agent-profile-v2.php:53
msgid "Ask a question"
msgstr ""

#: elementor/template-part/single-agency/agency-reviews.php:41
#: elementor/template-part/single-agent/agent-reviews.php:41
msgid "out of"
msgstr ""

#: elementor/template-part/single-agency/agency-reviews.php:49
#: elementor/template-part/single-agent/agent-reviews.php:49
msgid "Leave a Review"
msgstr ""

#: elementor/template-part/single-agency/agency-reviews.php:78
#: elementor/template-part/single-agent/agent-reviews.php:78
#: elementor/widgets/single-post/post-navigation.php:68
msgid "Previous"
msgstr ""

#: elementor/template-part/single-agency/agency-reviews.php:83
#: elementor/template-part/single-agent/agent-reviews.php:83
#: elementor/widgets/google-map.php:330 elementor/widgets/mapbox.php:351
#: elementor/widgets/open-street-map.php:323
#: elementor/widgets/single-post/post-navigation.php:80
#: functions/helpers.php:477 shortcodes/agents.php:159
#: shortcodes/blog-posts-carousel.php:110 shortcodes/partners.php:35
msgid "Next"
msgstr ""

#: elementor/template-part/single-agency/agency-single-stats.php:68
#: elementor/template-part/single-agent/agent-single-stats.php:55
#: elementor/widgets/single-property/section-energy.php:273
msgid "Other"
msgstr ""

#: elementor/template-part/single-agency/contact-form.php:67
#: elementor/template-part/single-agent/contact-form.php:65
#, fuzzy
#| msgid "Name"
msgid "Your Name"
msgstr "имя"

#: elementor/template-part/single-agency/contact-form.php:78
#: elementor/template-part/single-agent/contact-form.php:76
#: elementor/widgets/contact-form.php:88 elementor/widgets/contact-form.php:278
#: elementor/widgets/contact-form.php:279
#: elementor/widgets/contact-form.php:521
#: elementor/widgets/inquiry-form.php:106
#: elementor/widgets/inquiry-form.php:506
#: elementor/widgets/single-agency/agency-contact.php:124
#: elementor/widgets/single-agent/agent-contact.php:124
#, fuzzy
#| msgid "E-mail"
msgid "Email"
msgstr "Эл. почта"

#: elementor/template-part/single-agency/contact-form.php:83
#: elementor/template-part/single-agent/contact-form.php:81
#: elementor/widgets/contact-form.php:90 elementor/widgets/contact-form.php:285
#: elementor/widgets/contact-form.php:286
#: elementor/widgets/inquiry-form.php:109
msgid "Message"
msgstr ""

#: elementor/template-part/single-agency/contact-form.php:83
#: elementor/template-part/single-agent/contact-form.php:81
#, php-format
msgid ""
"Hi %s, I saw your profile on %s and wanted to see if i can get some help"
msgstr ""

#: elementor/template-part/single-agency/contact-form.php:126
#: elementor/template-part/single-agent/contact-form.php:124
#: elementor/widgets/contact-form.php:431
#: elementor/widgets/contact-form.php:432
#: elementor/widgets/inquiry-form.php:416
#: elementor/widgets/inquiry-form.php:417 templates/currency/form.php:10
#: templates/fields-builder/fields-form.php:10
msgid "Submit"
msgstr ""

#: elementor/traits/Houzez_Button_Traits.php:33
#: elementor/widgets/contact-form.php:300
#: elementor/widgets/contact-form.php:443
#: elementor/widgets/inquiry-form.php:287
#: elementor/widgets/inquiry-form.php:428
#: elementor/widgets/search-builder.php:569
#: elementor/widgets/search-builder.php:588
msgid "Extra Small"
msgstr ""

#: elementor/traits/Houzez_Button_Traits.php:34
#: elementor/widgets/contact-form.php:301
#: elementor/widgets/contact-form.php:444
#: elementor/widgets/inquiry-form.php:288
#: elementor/widgets/inquiry-form.php:429
#: elementor/widgets/search-builder.php:570
#: elementor/widgets/search-builder.php:589
msgid "Small"
msgstr ""

#: elementor/traits/Houzez_Button_Traits.php:35
#: elementor/widgets/contact-form.php:302
#: elementor/widgets/contact-form.php:445
#: elementor/widgets/inquiry-form.php:289
#: elementor/widgets/inquiry-form.php:430
#: elementor/widgets/search-builder.php:571
#: elementor/widgets/search-builder.php:590
msgid "Medium"
msgstr ""

#: elementor/traits/Houzez_Button_Traits.php:36
#: elementor/widgets/contact-form.php:303
#: elementor/widgets/contact-form.php:446
#: elementor/widgets/inquiry-form.php:290
#: elementor/widgets/inquiry-form.php:431
#: elementor/widgets/search-builder.php:572
#: elementor/widgets/search-builder.php:591
msgid "Large"
msgstr ""

#: elementor/traits/Houzez_Button_Traits.php:37
#: elementor/widgets/contact-form.php:304
#: elementor/widgets/contact-form.php:447
#: elementor/widgets/inquiry-form.php:291
#: elementor/widgets/inquiry-form.php:432
#: elementor/widgets/search-builder.php:573
#: elementor/widgets/search-builder.php:592
msgid "Extra Large"
msgstr ""

#: elementor/traits/Houzez_Button_Traits.php:55
#: elementor/widgets/agents-grid.php:413 elementor/widgets/agents.php:263
#: elementor/widgets/properties-ajax-tabs.php:116
#: elementor/widgets/single-agent/agent-meta.php:55
#: elementor/widgets/single-agent/agent-profile-v1.php:65
#: elementor/widgets/team-member.php:104 elementor/widgets/team-member.php:270
#: templates/currency/currency-list.php:13
#, fuzzy
#| msgid "Location"
msgid "Position"
msgstr "Место нахождения"

#: elementor/traits/Houzez_Button_Traits.php:59
#: elementor/traits/Houzez_Style_Traits.php:18
#: elementor/traits/Houzez_Style_Traits.php:153
#: elementor/traits/Houzez_Style_Traits.php:471
#: elementor/widgets/contact-form.php:481
#: elementor/widgets/create-listing-btn.php:82
#: elementor/widgets/header-footer/create-listing-btn.php:98
#: elementor/widgets/header-footer/login-modal.php:142
#: elementor/widgets/header-footer/login-modal.php:167
#: elementor/widgets/header-footer/menu.php:194
#: elementor/widgets/header-footer/menu.php:381
#: elementor/widgets/header-footer/site-logo.php:441
#: elementor/widgets/inquiry-form.php:466
#: elementor/widgets/listings-tabs.php:278
#: elementor/widgets/login-modal.php:129 elementor/widgets/login-modal.php:154
#: elementor/widgets/properties-ajax-tabs.php:121
#: elementor/widgets/search-builder.php:616
#: elementor/widgets/search-builder.php:1210
#: elementor/widgets/section-title.php:158
#: elementor/widgets/single-agency/agency-content.php:51
#: elementor/widgets/single-agency/agency-excerpt.php:51
#: elementor/widgets/single-agent/agent-content.php:51
#: elementor/widgets/single-agent/agent-excerpt.php:51
#: elementor/widgets/single-post/author-box.php:173
#: elementor/widgets/single-post/author-box.php:197
#: elementor/widgets/single-post/post-content.php:51
#: elementor/widgets/single-post/post-excerpt.php:51
#: elementor/widgets/single-property/featured-image.php:94
#: elementor/widgets/single-property/featured-label.php:56
#: elementor/widgets/single-property/item-label.php:56
#: elementor/widgets/single-property/item-tools.php:175
#: elementor/widgets/single-property/property-address.php:125
#: elementor/widgets/single-property/property-content.php:53
#: elementor/widgets/single-property/property-excerpt.php:52
#: elementor/widgets/single-property/property-price.php:72
#: elementor/widgets/single-property/section-description.php:198
#: elementor/widgets/single-property/section-sublistings.php:132
#: elementor/widgets/single-property/status-label.php:57
#: elementor/widgets/taxonomies-list.php:210
#: elementor/widgets/taxonomies-list.php:495
msgid "Left"
msgstr ""

#: elementor/traits/Houzez_Button_Traits.php:63
#: elementor/traits/Houzez_Button_Traits.php:95
#: elementor/traits/Houzez_Style_Traits.php:22
#: elementor/traits/Houzez_Style_Traits.php:157
#: elementor/traits/Houzez_Style_Traits.php:475
#: elementor/widgets/contact-form.php:485
#: elementor/widgets/create-listing-btn.php:86
#: elementor/widgets/header-footer/create-listing-btn.php:102
#: elementor/widgets/header-footer/login-modal.php:146
#: elementor/widgets/header-footer/menu.php:198
#: elementor/widgets/header-footer/menu.php:385
#: elementor/widgets/header-footer/site-logo.php:445
#: elementor/widgets/inquiry-form.php:470
#: elementor/widgets/listings-tabs.php:282
#: elementor/widgets/login-modal.php:133
#: elementor/widgets/properties-ajax-tabs.php:125
#: elementor/widgets/search-builder.php:620
#: elementor/widgets/search-builder.php:1214
#: elementor/widgets/section-title.php:162
#: elementor/widgets/single-agency/agency-content.php:55
#: elementor/widgets/single-agency/agency-excerpt.php:55
#: elementor/widgets/single-agent/agent-content.php:55
#: elementor/widgets/single-agent/agent-excerpt.php:55
#: elementor/widgets/single-post/author-box.php:201
#: elementor/widgets/single-post/post-content.php:55
#: elementor/widgets/single-post/post-excerpt.php:55
#: elementor/widgets/single-post/post-info.php:413
#: elementor/widgets/single-property/featured-image.php:98
#: elementor/widgets/single-property/featured-label.php:60
#: elementor/widgets/single-property/item-label.php:60
#: elementor/widgets/single-property/item-tools.php:179
#: elementor/widgets/single-property/property-address.php:129
#: elementor/widgets/single-property/property-content.php:57
#: elementor/widgets/single-property/property-excerpt.php:56
#: elementor/widgets/single-property/property-price.php:76
#: elementor/widgets/single-property/section-description.php:202
#: elementor/widgets/single-property/section-overview-v2.php:270
#: elementor/widgets/single-property/section-overview.php:347
#: elementor/widgets/single-property/section-sublistings.php:136
#: elementor/widgets/single-property/status-label.php:61
#: elementor/widgets/taxonomies-list.php:214
#: elementor/widgets/taxonomies-list.php:499
#: elementor/widgets/taxonomies-list.php:530
msgid "Center"
msgstr ""

#: elementor/traits/Houzez_Button_Traits.php:67
#: elementor/traits/Houzez_Style_Traits.php:26
#: elementor/traits/Houzez_Style_Traits.php:161
#: elementor/traits/Houzez_Style_Traits.php:479
#: elementor/widgets/contact-form.php:489
#: elementor/widgets/create-listing-btn.php:90
#: elementor/widgets/header-footer/create-listing-btn.php:106
#: elementor/widgets/header-footer/login-modal.php:150
#: elementor/widgets/header-footer/login-modal.php:171
#: elementor/widgets/header-footer/menu.php:202
#: elementor/widgets/header-footer/menu.php:389
#: elementor/widgets/header-footer/site-logo.php:449
#: elementor/widgets/inquiry-form.php:474 elementor/widgets/login-modal.php:137
#: elementor/widgets/login-modal.php:158
#: elementor/widgets/properties-ajax-tabs.php:129
#: elementor/widgets/search-builder.php:624
#: elementor/widgets/section-title.php:166
#: elementor/widgets/single-agency/agency-content.php:59
#: elementor/widgets/single-agency/agency-excerpt.php:59
#: elementor/widgets/single-agent/agent-content.php:59
#: elementor/widgets/single-agent/agent-excerpt.php:59
#: elementor/widgets/single-post/author-box.php:181
#: elementor/widgets/single-post/author-box.php:205
#: elementor/widgets/single-post/post-content.php:59
#: elementor/widgets/single-post/post-excerpt.php:59
#: elementor/widgets/single-property/featured-image.php:102
#: elementor/widgets/single-property/featured-label.php:64
#: elementor/widgets/single-property/item-label.php:64
#: elementor/widgets/single-property/item-tools.php:183
#: elementor/widgets/single-property/property-address.php:133
#: elementor/widgets/single-property/property-content.php:61
#: elementor/widgets/single-property/property-excerpt.php:60
#: elementor/widgets/single-property/property-price.php:80
#: elementor/widgets/single-property/section-description.php:206
#: elementor/widgets/single-property/section-sublistings.php:140
#: elementor/widgets/single-property/status-label.php:65
#: elementor/widgets/taxonomies-list.php:218
#: elementor/widgets/taxonomies-list.php:503
msgid "Right"
msgstr ""

#: elementor/traits/Houzez_Button_Traits.php:71
#: elementor/widgets/header-footer/menu.php:206
msgid "Stretch"
msgstr ""

#: elementor/traits/Houzez_Button_Traits.php:87
#: elementor/traits/Houzez_Style_Traits.php:14
#: elementor/traits/Houzez_Style_Traits.php:149
#: elementor/traits/Houzez_Style_Traits.php:467
#: elementor/widgets/contact-form.php:477
#: elementor/widgets/create-listing-btn.php:78
#: elementor/widgets/header-footer/create-listing-btn.php:94
#: elementor/widgets/header-footer/login-modal.php:138
#: elementor/widgets/header-footer/site-logo.php:437
#: elementor/widgets/inquiry-form.php:462
#: elementor/widgets/listings-tabs.php:274
#: elementor/widgets/login-modal.php:125
#: elementor/widgets/search-builder.php:1206
#: elementor/widgets/section-title.php:154
#: elementor/widgets/single-agency/agency-content.php:47
#: elementor/widgets/single-agency/agency-excerpt.php:47
#: elementor/widgets/single-agent/agent-content.php:47
#: elementor/widgets/single-agent/agent-excerpt.php:47
#: elementor/widgets/single-post/author-box.php:193
#: elementor/widgets/single-post/post-content.php:47
#: elementor/widgets/single-post/post-excerpt.php:47
#: elementor/widgets/single-post/post-info.php:405
#: elementor/widgets/single-property/featured-image.php:90
#: elementor/widgets/single-property/featured-label.php:52
#: elementor/widgets/single-property/item-label.php:52
#: elementor/widgets/single-property/item-tools.php:170
#: elementor/widgets/single-property/property-address.php:120
#: elementor/widgets/single-property/property-content.php:49
#: elementor/widgets/single-property/property-excerpt.php:48
#: elementor/widgets/single-property/property-price.php:67
#: elementor/widgets/single-property/section-description.php:194
#: elementor/widgets/single-property/section-sublistings.php:128
#: elementor/widgets/single-property/status-label.php:53
#: elementor/widgets/taxonomies-list.php:206
msgid "Alignment"
msgstr ""

#: elementor/traits/Houzez_Button_Traits.php:91
#: elementor/widgets/single-agency/agency-call-btn.php:108
#: elementor/widgets/single-agency/agency-contact-btn.php:96
#: elementor/widgets/single-agency/agency-line-btn.php:96
#: elementor/widgets/single-agency/agency-telegram-btn.php:96
#: elementor/widgets/single-agency/agency-whatsapp-btn.php:96
#: elementor/widgets/single-agent/agent-call-btn.php:108
#: elementor/widgets/single-agent/agent-contact-btn.php:96
#: elementor/widgets/single-agent/agent-line-btn.php:96
#: elementor/widgets/single-agent/agent-telegram-btn.php:96
#: elementor/widgets/single-agent/agent-whatsapp-btn.php:96
#: elementor/widgets/single-post/post-info.php:409
#: elementor/widgets/taxonomies-list.php:526
msgid "Start"
msgstr ""

#: elementor/traits/Houzez_Button_Traits.php:99
#: elementor/widgets/single-agency/agency-call-btn.php:112
#: elementor/widgets/single-agency/agency-contact-btn.php:100
#: elementor/widgets/single-agency/agency-line-btn.php:100
#: elementor/widgets/single-agency/agency-telegram-btn.php:100
#: elementor/widgets/single-agency/agency-whatsapp-btn.php:100
#: elementor/widgets/single-agent/agent-call-btn.php:112
#: elementor/widgets/single-agent/agent-contact-btn.php:100
#: elementor/widgets/single-agent/agent-line-btn.php:100
#: elementor/widgets/single-agent/agent-telegram-btn.php:100
#: elementor/widgets/single-agent/agent-whatsapp-btn.php:100
#: elementor/widgets/single-post/post-info.php:417
#: elementor/widgets/taxonomies-list.php:534
msgid "End"
msgstr ""

#: elementor/traits/Houzez_Button_Traits.php:103
msgid "Space between"
msgstr ""

#: elementor/traits/Houzez_Button_Traits.php:143
#: elementor/traits/Houzez_Button_Traits.php:346
#: elementor/traits/Houzez_Button_Traits.php:558
#: elementor/traits/Houzez_Button_Traits.php:761
#: elementor/traits/Houzez_Button_Traits.php:933
#: elementor/traits/Houzez_Form_Traits.php:338
#: elementor/traits/Houzez_Property_Cards_Traits.php:29
#: elementor/traits/Houzez_Property_Cards_Traits.php:158
#: elementor/traits/Houzez_Property_Cards_Traits.php:511
#: elementor/traits/Houzez_Property_Cards_Traits.php:669
#: elementor/traits/Houzez_Property_Cards_Traits.php:1538
#: elementor/traits/Houzez_Property_Cards_Traits.php:1810
#: elementor/traits/Houzez_Style_Traits.php:83
#: elementor/traits/Houzez_Style_Traits.php:324
#: elementor/widgets/advanced-search.php:242
#: elementor/widgets/agent-card.php:693 elementor/widgets/agents-grid.php:473
#: elementor/widgets/agents.php:305 elementor/widgets/banner-image.php:329
#: elementor/widgets/blog-posts-carousel.php:781
#: elementor/widgets/custom-carousel.php:616
#: elementor/widgets/custom-carousel.php:853
#: elementor/widgets/custom-carousel.php:1025
#: elementor/widgets/header-footer/area-switcher.php:188
#: elementor/widgets/header-footer/create-listing-btn.php:126
#: elementor/widgets/header-footer/currency.php:188
#: elementor/widgets/header-footer/login-modal.php:306
#: elementor/widgets/header-footer/menu.php:467
#: elementor/widgets/header-footer/menu.php:791
#: elementor/widgets/header-footer/menu.php:1071
#: elementor/widgets/header-footer/site-logo.php:244
#: elementor/widgets/properties-slider.php:137
#: elementor/widgets/property-cards-v8.php:557
#: elementor/widgets/search-builder.php:1241
#: elementor/widgets/single-agency/agency-listings-review.php:400
#: elementor/widgets/single-agency/agency-profile-v1.php:290
#: elementor/widgets/single-agency/agency-profile-v1.php:422
#: elementor/widgets/single-agency/agency-profile-v2.php:224
#: elementor/widgets/single-agency/agency-search.php:283
#: elementor/widgets/single-agent/agent-listings-review.php:405
#: elementor/widgets/single-agent/agent-profile-v1.php:302
#: elementor/widgets/single-agent/agent-profile-v1.php:434
#: elementor/widgets/single-agent/agent-profile-v2.php:236
#: elementor/widgets/single-agent/agent-search.php:283
#: elementor/widgets/single-post/author-box.php:669
#: elementor/widgets/single-post/post-navigation.php:123
#: elementor/widgets/single-post/post-navigation.php:208
#: elementor/widgets/single-property/featured-image.php:204
#: elementor/widgets/single-property/featured-label.php:131
#: elementor/widgets/single-property/item-label.php:131
#: elementor/widgets/single-property/section-contact-bottom.php:236
#: elementor/widgets/single-property/status-label.php:132
#: elementor/widgets/taxonomies-cards-carousel.php:415
#: elementor/widgets/taxonomies-cards.php:303
#: elementor/widgets/taxonomies-grids-carousel.php:340
#: elementor/widgets/taxonomies-grids-carousel.php:442
#: elementor/widgets/taxonomies-grids.php:285
#: elementor/widgets/taxonomies-list.php:373
#: elementor/widgets/taxonomies-list.php:605
#: elementor/widgets/taxonomies-list.php:721
#: elementor/widgets/testimonials-v3.php:122
msgid "Normal"
msgstr ""

#: elementor/traits/Houzez_Button_Traits.php:151
#: elementor/traits/Houzez_Button_Traits.php:192
#: elementor/traits/Houzez_Button_Traits.php:354
#: elementor/traits/Houzez_Button_Traits.php:407
#: elementor/traits/Houzez_Button_Traits.php:566
#: elementor/traits/Houzez_Button_Traits.php:607
#: elementor/traits/Houzez_Button_Traits.php:769
#: elementor/traits/Houzez_Button_Traits.php:810
#: elementor/traits/Houzez_Button_Traits.php:940
#: elementor/traits/Houzez_Button_Traits.php:989
#: elementor/traits/Houzez_Form_Traits.php:107
#: elementor/traits/Houzez_Form_Traits.php:205
#: elementor/traits/Houzez_Form_Traits.php:299
#: elementor/traits/Houzez_Form_Traits.php:357
#: elementor/traits/Houzez_Form_Traits.php:429
#: elementor/traits/Houzez_Property_Cards_Traits.php:36
#: elementor/traits/Houzez_Property_Cards_Traits.php:81
#: elementor/traits/Houzez_Property_Cards_Traits.php:1817
#: elementor/traits/Houzez_Property_Cards_Traits.php:1865
#: elementor/traits/Houzez_Style_Traits.php:44
#: elementor/traits/Houzez_Style_Traits.php:497
#: elementor/traits/Houzez_Style_Traits.php:578
#: elementor/traits/Houzez_Style_Traits.php:622
#: elementor/traits/Houzez_Style_Traits.php:733
#: elementor/traits/Houzez_Style_Traits.php:796
#: elementor/traits/Houzez_Style_Traits.php:842
#: elementor/traits/Houzez_Style_Traits.php:1441
#: elementor/traits/Houzez_Testimonials_Traits.php:74
#: elementor/traits/Houzez_Testimonials_Traits.php:123
#: elementor/traits/Houzez_Testimonials_Traits.php:170
#: elementor/widgets/advanced-search.php:170
#: elementor/widgets/advanced-search.php:261
#: elementor/widgets/advanced-search.php:333
#: elementor/widgets/agent-card.php:532 elementor/widgets/agents-grid.php:491
#: elementor/widgets/agents-grid.php:531 elementor/widgets/agents.php:323
#: elementor/widgets/agents.php:363 elementor/widgets/custom-carousel.php:319
#: elementor/widgets/custom-carousel.php:355
#: elementor/widgets/custom-carousel.php:767
#: elementor/widgets/custom-carousel.php:803
#: elementor/widgets/custom-carousel.php:861
#: elementor/widgets/custom-carousel.php:902
#: elementor/widgets/header-footer/menu.php:474
#: elementor/widgets/header-footer/menu.php:498
#: elementor/widgets/header-footer/menu.php:516
#: elementor/widgets/header-footer/menu.php:563
#: elementor/widgets/header-footer/menu.php:798
#: elementor/widgets/header-footer/menu.php:836
#: elementor/widgets/header-footer/menu.php:884
#: elementor/widgets/header-footer/site-logo.php:547
#: elementor/widgets/icon-box.php:414 elementor/widgets/icon-box.php:443
#: elementor/widgets/icon-box.php:473
#: elementor/widgets/property-cards-v8.php:564
#: elementor/widgets/search-builder.php:858
#: elementor/widgets/search-builder.php:952
#: elementor/widgets/search-builder.php:1043
#: elementor/widgets/single-agency/agency-about.php:108
#: elementor/widgets/single-agency/agency-contact.php:285
#: elementor/widgets/single-agency/agency-contact.php:318
#: elementor/widgets/single-agency/agency-contact.php:348
#: elementor/widgets/single-agency/agency-contact.php:417
#: elementor/widgets/single-agency/agency-content.php:76
#: elementor/widgets/single-agency/agency-excerpt.php:76
#: elementor/widgets/single-agency/agency-listings-review.php:345
#: elementor/widgets/single-agency/agency-listings-review.php:419
#: elementor/widgets/single-agency/agency-profile-v1.php:166
#: elementor/widgets/single-agency/agency-profile-v1.php:309
#: elementor/widgets/single-agency/agency-profile-v1.php:381
#: elementor/widgets/single-agency/agency-profile-v1.php:441
#: elementor/widgets/single-agency/agency-profile-v1.php:513
#: elementor/widgets/single-agency/agency-profile-v2.php:100
#: elementor/widgets/single-agency/agency-search.php:86
#: elementor/widgets/single-agency/agency-search.php:204
#: elementor/widgets/single-agency/agency-search.php:302
#: elementor/widgets/single-agency/agency-search.php:374
#: elementor/widgets/single-agency/agency-single-stats.php:101
#: elementor/widgets/single-agency/agency-stats.php:65
#: elementor/widgets/single-agent/agent-about.php:108
#: elementor/widgets/single-agent/agent-contact.php:285
#: elementor/widgets/single-agent/agent-contact.php:318
#: elementor/widgets/single-agent/agent-contact.php:348
#: elementor/widgets/single-agent/agent-contact.php:417
#: elementor/widgets/single-agent/agent-content.php:76
#: elementor/widgets/single-agent/agent-excerpt.php:76
#: elementor/widgets/single-agent/agent-listings-review.php:350
#: elementor/widgets/single-agent/agent-listings-review.php:424
#: elementor/widgets/single-agent/agent-profile-v1.php:178
#: elementor/widgets/single-agent/agent-profile-v1.php:321
#: elementor/widgets/single-agent/agent-profile-v1.php:393
#: elementor/widgets/single-agent/agent-profile-v1.php:453
#: elementor/widgets/single-agent/agent-profile-v1.php:525
#: elementor/widgets/single-agent/agent-profile-v2.php:112
#: elementor/widgets/single-agent/agent-search.php:86
#: elementor/widgets/single-agent/agent-search.php:204
#: elementor/widgets/single-agent/agent-search.php:302
#: elementor/widgets/single-agent/agent-search.php:374
#: elementor/widgets/single-agent/agent-single-stats.php:101
#: elementor/widgets/single-agent/agent-stats.php:65
#: elementor/widgets/single-post/author-box.php:679
#: elementor/widgets/single-post/post-content.php:76
#: elementor/widgets/single-post/post-excerpt.php:76
#: elementor/widgets/single-post/post-info.php:664
#: elementor/widgets/single-property/breadcrumb.php:51
#: elementor/widgets/single-property/property-content.php:78
#: elementor/widgets/single-property/property-excerpt.php:77
#: elementor/widgets/single-property/property-title-area.php:151
#: elementor/widgets/single-property/section-contact-2.php:327
#: elementor/widgets/single-property/section-contact-2.php:531
#: elementor/widgets/single-property/section-contact-bottom.php:243
#: elementor/widgets/single-property/section-contact-bottom.php:281
#: elementor/widgets/single-property/section-contact-bottom.php:500
#: elementor/widgets/single-property/section-features.php:194
#: elementor/widgets/single-property/section-schedule-tour-v2.php:150
#: elementor/widgets/single-property/section-schedule-tour.php:164
#: elementor/widgets/single-property/section-toparea-v1.php:268
#: elementor/widgets/single-property/section-toparea-v1.php:321
#: elementor/widgets/single-property/section-toparea-v1.php:396
#: elementor/widgets/single-property/section-toparea-v5.php:118
#: elementor/widgets/sort-by.php:114 elementor/widgets/testimonials-v2.php:113
#: elementor/widgets/testimonials-v2.php:133
msgid "Text Color"
msgstr ""

#: elementor/traits/Houzez_Button_Traits.php:184
#: elementor/traits/Houzez_Button_Traits.php:399
#: elementor/traits/Houzez_Button_Traits.php:599
#: elementor/traits/Houzez_Button_Traits.php:802
#: elementor/traits/Houzez_Button_Traits.php:982
#: elementor/traits/Houzez_Form_Traits.php:410
#: elementor/traits/Houzez_Property_Cards_Traits.php:74
#: elementor/traits/Houzez_Property_Cards_Traits.php:202
#: elementor/traits/Houzez_Property_Cards_Traits.php:562
#: elementor/traits/Houzez_Property_Cards_Traits.php:766
#: elementor/traits/Houzez_Property_Cards_Traits.php:1655
#: elementor/traits/Houzez_Property_Cards_Traits.php:1858
#: elementor/traits/Houzez_Style_Traits.php:358
#: elementor/widgets/advanced-search.php:314
#: elementor/widgets/agent-card.php:749 elementor/widgets/agents-grid.php:513
#: elementor/widgets/agents.php:345 elementor/widgets/banner-image.php:363
#: elementor/widgets/blog-posts-carousel.php:825
#: elementor/widgets/custom-carousel.php:650
#: elementor/widgets/custom-carousel.php:894
#: elementor/widgets/custom-carousel.php:1063
#: elementor/widgets/header-footer/area-switcher.php:208
#: elementor/widgets/header-footer/create-listing-btn.php:168
#: elementor/widgets/header-footer/currency.php:208
#: elementor/widgets/header-footer/login-modal.php:373
#: elementor/widgets/header-footer/menu.php:491
#: elementor/widgets/header-footer/menu.php:829
#: elementor/widgets/header-footer/menu.php:1103
#: elementor/widgets/header-footer/site-logo.php:278
#: elementor/widgets/properties-slider.php:178
#: elementor/widgets/property-cards-v8.php:603
#: elementor/widgets/search-builder.php:1313
#: elementor/widgets/single-agency/agency-profile-v1.php:362
#: elementor/widgets/single-agency/agency-profile-v1.php:494
#: elementor/widgets/single-agency/agency-profile-v2.php:253
#: elementor/widgets/single-agency/agency-search.php:355
#: elementor/widgets/single-agent/agent-profile-v1.php:374
#: elementor/widgets/single-agent/agent-profile-v1.php:506
#: elementor/widgets/single-agent/agent-profile-v2.php:265
#: elementor/widgets/single-agent/agent-search.php:355
#: elementor/widgets/single-post/author-box.php:721
#: elementor/widgets/single-post/post-navigation.php:145
#: elementor/widgets/single-post/post-navigation.php:228
#: elementor/widgets/single-property/featured-image.php:238
#: elementor/widgets/single-property/featured-label.php:162
#: elementor/widgets/single-property/item-label.php:162
#: elementor/widgets/single-property/section-contact-bottom.php:274
#: elementor/widgets/single-property/status-label.php:163
#: elementor/widgets/taxonomies-cards-carousel.php:459
#: elementor/widgets/taxonomies-cards.php:329
#: elementor/widgets/taxonomies-grids-carousel.php:378
#: elementor/widgets/taxonomies-grids-carousel.php:486
#: elementor/widgets/taxonomies-grids.php:323
#: elementor/widgets/taxonomies-list.php:398
#: elementor/widgets/taxonomies-list.php:629
#: elementor/widgets/taxonomies-list.php:745
#: elementor/widgets/testimonials-v3.php:160
msgid "Hover"
msgstr ""

#: elementor/traits/Houzez_Button_Traits.php:221
#: elementor/traits/Houzez_Button_Traits.php:385
#: elementor/traits/Houzez_Button_Traits.php:436
#: elementor/traits/Houzez_Button_Traits.php:636
#: elementor/traits/Houzez_Button_Traits.php:839
#: elementor/traits/Houzez_Button_Traits.php:969
#: elementor/traits/Houzez_Button_Traits.php:1016
#: elementor/traits/Houzez_Form_Traits.php:247
#: elementor/traits/Houzez_Form_Traits.php:440
#: elementor/traits/Houzez_Property_Cards_Traits.php:60
#: elementor/traits/Houzez_Property_Cards_Traits.php:105
#: elementor/traits/Houzez_Property_Cards_Traits.php:189
#: elementor/traits/Houzez_Property_Cards_Traits.php:234
#: elementor/traits/Houzez_Property_Cards_Traits.php:544
#: elementor/traits/Houzez_Property_Cards_Traits.php:595
#: elementor/traits/Houzez_Property_Cards_Traits.php:892
#: elementor/traits/Houzez_Property_Cards_Traits.php:1843
#: elementor/traits/Houzez_Property_Cards_Traits.php:1891
#: elementor/traits/Houzez_Style_Traits.php:1001
#: elementor/traits/Houzez_Style_Traits.php:1354
#: elementor/widgets/advanced-search.php:344
#: elementor/widgets/blog-posts-carousel.php:704
#: elementor/widgets/blog-posts-carousel.php:812
#: elementor/widgets/blog-posts-carousel.php:857
#: elementor/widgets/blog-posts.php:635
#: elementor/widgets/custom-carousel.php:931
#: elementor/widgets/custom-carousel.php:1096
#: elementor/widgets/properties-slider.php:166
#: elementor/widgets/properties-slider.php:208
#: elementor/widgets/property-cards-v7.php:481
#: elementor/widgets/property-cards-v8.php:588
#: elementor/widgets/property-cards-v8.php:634
#: elementor/widgets/property-carousel-v7.php:451
#: elementor/widgets/search-builder.php:998
#: elementor/widgets/search-builder.php:1343
#: elementor/widgets/single-agency/agency-listings-review.php:430
#: elementor/widgets/single-agency/agency-profile-v1.php:128
#: elementor/widgets/single-agency/agency-profile-v1.php:392
#: elementor/widgets/single-agency/agency-profile-v1.php:524
#: elementor/widgets/single-agency/agency-search.php:240
#: elementor/widgets/single-agency/agency-search.php:385
#: elementor/widgets/single-agent/agent-listings-review.php:435
#: elementor/widgets/single-agent/agent-profile-v1.php:140
#: elementor/widgets/single-agent/agent-profile-v1.php:404
#: elementor/widgets/single-agent/agent-profile-v1.php:536
#: elementor/widgets/single-agent/agent-search.php:240
#: elementor/widgets/single-agent/agent-search.php:385
#: elementor/widgets/single-post/author-box.php:362
#: elementor/widgets/single-property/item-tools.php:146
#: elementor/widgets/single-property/property-title-area.php:593
#: elementor/widgets/single-property/section-address.php:331
#: elementor/widgets/single-property/section-attachments.php:125
#: elementor/widgets/single-property/section-calculator.php:162
#: elementor/widgets/single-property/section-contact-2.php:436
#: elementor/widgets/single-property/section-contact-bottom.php:142
#: elementor/widgets/single-property/section-contact-bottom.php:308
#: elementor/widgets/single-property/section-contact-bottom.php:580
#: elementor/widgets/single-property/section-details.php:425
#: elementor/widgets/single-property/section-energy.php:304
#: elementor/widgets/single-property/section-schedule-tour-v2.php:238
#: elementor/widgets/single-property/section-schedule-tour.php:221
#: elementor/widgets/single-property/section-similar.php:228
#: elementor/widgets/single-property/section-sublistings.php:104
#: elementor/widgets/single-property/section-toparea-v1.php:519
#: elementor/widgets/single-property/section-toparea-v2.php:518
#: elementor/widgets/single-property/section-toparea-v3.php:543
#: elementor/widgets/single-property/section-toparea-v5.php:518
#: elementor/widgets/single-property/section-toparea-v6.php:563
#: elementor/widgets/single-property/section-toparea-v7.php:538
#: elementor/widgets/taxonomies-cards-carousel.php:446
#: elementor/widgets/taxonomies-cards-carousel.php:490
#: elementor/widgets/taxonomies-grids-carousel.php:473
#: elementor/widgets/taxonomies-grids-carousel.php:517
#: elementor/widgets/testimonials-v3.php:193
msgid "Border Color"
msgstr ""

#: elementor/traits/Houzez_Button_Traits.php:236
#: elementor/traits/Houzez_Button_Traits.php:448
#: elementor/traits/Houzez_Button_Traits.php:651
#: elementor/traits/Houzez_Button_Traits.php:854
#: elementor/traits/Houzez_Button_Traits.php:1027
#: elementor/traits/Houzez_Style_Traits.php:391
#: elementor/widgets/banner-image.php:396
#: elementor/widgets/custom-carousel.php:683
#: elementor/widgets/custom-carousel.php:946
#: elementor/widgets/custom-carousel.php:1110
#: elementor/widgets/header-footer/site-logo.php:311
#: elementor/widgets/single-post/author-box.php:759
#: elementor/widgets/single-post/post-navigation.php:164
#: elementor/widgets/single-post/post-navigation.php:246
#: elementor/widgets/single-property/featured-image.php:271
#: elementor/widgets/single-property/section-contact-bottom.php:322
#: elementor/widgets/taxonomies-cards.php:354
#: elementor/widgets/taxonomies-grids-carousel.php:403
#: elementor/widgets/taxonomies-grids.php:348
#: elementor/widgets/taxonomies-list.php:418
#: elementor/widgets/taxonomies-list.php:648
#: elementor/widgets/taxonomies-list.php:764
#: elementor/widgets/testimonials-v3.php:207
msgid "Transition Duration"
msgstr ""

#: elementor/traits/Houzez_Button_Traits.php:251
#: elementor/traits/Houzez_Button_Traits.php:463
#: elementor/traits/Houzez_Button_Traits.php:666
#: elementor/traits/Houzez_Button_Traits.php:869
#: elementor/traits/Houzez_Style_Traits.php:409
#: elementor/widgets/single-property/featured-image.php:288
#: elementor/widgets/testimonials-v3.php:222
msgid "Hover Animation"
msgstr ""

#: elementor/traits/Houzez_Button_Traits.php:274
#: elementor/traits/Houzez_Button_Traits.php:486
#: elementor/traits/Houzez_Button_Traits.php:689
#: elementor/traits/Houzez_Button_Traits.php:892
#: elementor/traits/Houzez_Button_Traits.php:1056
#: elementor/traits/Houzez_Form_Traits.php:276
#: elementor/traits/Houzez_Form_Traits.php:384
#: elementor/traits/Houzez_Property_Cards_Traits.php:130
#: elementor/traits/Houzez_Style_Traits.php:430
#: elementor/traits/Houzez_Style_Traits.php:663
#: elementor/traits/Houzez_Style_Traits.php:778
#: elementor/traits/Houzez_Testimonials_Traits.php:219
#: elementor/widgets/advanced-search.php:215
#: elementor/widgets/advanced-search.php:288
#: elementor/widgets/agent-card.php:303 elementor/widgets/agent-card.php:735
#: elementor/widgets/agent-card.php:787 elementor/widgets/agents-grid.php:335
#: elementor/widgets/agents.php:223 elementor/widgets/banner-image.php:426
#: elementor/widgets/custom-carousel.php:427
#: elementor/widgets/custom-carousel.php:714
#: elementor/widgets/custom-carousel.php:975
#: elementor/widgets/custom-carousel.php:1138
#: elementor/widgets/header-footer/menu.php:1174
#: elementor/widgets/header-footer/site-logo.php:341
#: elementor/widgets/icon-box.php:253 elementor/widgets/search-builder.php:1017
#: elementor/widgets/search-builder.php:1287
#: elementor/widgets/single-agency/agency-listings-review.php:372
#: elementor/widgets/single-agency/agency-profile-v1.php:336
#: elementor/widgets/single-agency/agency-profile-v1.php:468
#: elementor/widgets/single-agency/agency-search.php:257
#: elementor/widgets/single-agency/agency-search.php:329
#: elementor/widgets/single-agent/agent-listings-review.php:377
#: elementor/widgets/single-agent/agent-profile-v1.php:348
#: elementor/widgets/single-agent/agent-profile-v1.php:480
#: elementor/widgets/single-agent/agent-search.php:257
#: elementor/widgets/single-agent/agent-search.php:329
#: elementor/widgets/single-post/author-box.php:427
#: elementor/widgets/single-post/author-box.php:819
#: elementor/widgets/single-property/featured-image.php:309
#: elementor/widgets/single-property/section-contact-bottom.php:350
#: elementor/widgets/testimonials-v3.php:243
msgid "Border Radius"
msgstr ""

#: elementor/traits/Houzez_Button_Traits.php:296
#: elementor/traits/Houzez_Button_Traits.php:508
#: elementor/traits/Houzez_Button_Traits.php:711
#: elementor/traits/Houzez_Button_Traits.php:914
#: elementor/traits/Houzez_Form_Traits.php:141
#: elementor/traits/Houzez_Style_Traits.php:634
#: elementor/widgets/advanced-search.php:106
#: elementor/widgets/agent-card.php:274 elementor/widgets/agent-card.php:408
#: elementor/widgets/blog-posts-carousel.php:740
#: elementor/widgets/blog-posts-v2.php:223 elementor/widgets/blog-posts.php:671
#: elementor/widgets/create-listing-btn.php:48
#: elementor/widgets/custom-carousel.php:442
#: elementor/widgets/custom-carousel.php:747
#: elementor/widgets/custom-carousel.php:997 elementor/widgets/grids.php:453
#: elementor/widgets/header-footer/create-listing-btn.php:64
#: elementor/widgets/header-footer/login-modal.php:219
#: elementor/widgets/header-footer/site-logo.php:586
#: elementor/widgets/icon-box.php:226 elementor/widgets/listings-tabs.php:247
#: elementor/widgets/login-modal.php:172
#: elementor/widgets/search-builder.php:900
#: elementor/widgets/search-builder.php:1169
#: elementor/widgets/search-builder.php:1299
#: elementor/widgets/single-agency/agency-about.php:120
#: elementor/widgets/single-agency/agency-contact.php:182
#: elementor/widgets/single-agency/agency-listings-review.php:527
#: elementor/widgets/single-agency/agency-listings.php:349
#: elementor/widgets/single-agency/agency-profile-v1.php:190
#: elementor/widgets/single-agency/agency-profile-v2.php:124
#: elementor/widgets/single-agency/agency-search.php:98
#: elementor/widgets/single-agency/agency-single-stats.php:133
#: elementor/widgets/single-agency/agency-stats.php:97
#: elementor/widgets/single-agent/agent-about.php:120
#: elementor/widgets/single-agent/agent-contact.php:182
#: elementor/widgets/single-agent/agent-listings-review.php:532
#: elementor/widgets/single-agent/agent-listings.php:354
#: elementor/widgets/single-agent/agent-profile-v1.php:202
#: elementor/widgets/single-agent/agent-profile-v2.php:136
#: elementor/widgets/single-agent/agent-search.php:98
#: elementor/widgets/single-agent/agent-single-stats.php:133
#: elementor/widgets/single-agent/agent-stats.php:97
#: elementor/widgets/single-post/author-box.php:846
#: elementor/widgets/single-property/featured-label.php:90
#: elementor/widgets/single-property/item-label.php:90
#: elementor/widgets/single-property/section-contact-2.php:284
#: elementor/widgets/single-property/section-contact-2.php:466
#: elementor/widgets/single-property/section-details.php:391
#: elementor/widgets/single-property/section-floorplan-v2.php:221
#: elementor/widgets/single-property/section-schedule-tour-v2.php:262
#: elementor/widgets/single-property/status-label.php:91
msgid "Padding"
msgstr ""

#: elementor/traits/Houzez_Filters_Traits.php:125
#: elementor/widgets/properties-ajax-tabs.php:402
#, fuzzy
#| msgid "Properties Settings"
msgid "Properties by Agents"
msgstr "Настройки свойств"

#: elementor/traits/Houzez_Filters_Traits.php:136
#: elementor/widgets/properties-ajax-tabs.php:413
#, fuzzy
#| msgid "Properties Settings"
msgid "Properties by Agencies"
msgstr "Настройки свойств"

#: elementor/traits/Houzez_Filters_Traits.php:147
#: elementor/widgets/inquiry-form.php:89
#: elementor/widgets/properties-ajax-tabs.php:424
#, fuzzy
#| msgid "Min Price"
msgid "Minimum Price"
msgstr "Минимальная цена"

#: elementor/traits/Houzez_Filters_Traits.php:155
#: elementor/widgets/inquiry-form.php:90
#: elementor/widgets/properties-ajax-tabs.php:432
#, fuzzy
#| msgid "Max Price"
msgid "Maximum Price"
msgstr "Макс. Цена"

#: elementor/traits/Houzez_Filters_Traits.php:164
#: elementor/widgets/properties-ajax-tabs.php:441
msgid "Minimum Beds"
msgstr ""

#: elementor/traits/Houzez_Filters_Traits.php:172
#: elementor/widgets/properties-ajax-tabs.php:449
msgid "Maximum Beds"
msgstr ""

#: elementor/traits/Houzez_Filters_Traits.php:181
#: elementor/widgets/properties-ajax-tabs.php:458
msgid "Minimum Baths"
msgstr ""

#: elementor/traits/Houzez_Filters_Traits.php:189
#: elementor/widgets/properties-ajax-tabs.php:466
msgid "Maximum Baths"
msgstr ""

#: elementor/traits/Houzez_Filters_Traits.php:199
#: elementor/widgets/properties-ajax-tabs.php:475
msgid "User Role"
msgstr ""

#: elementor/traits/Houzez_Filters_Traits.php:217
#: elementor/widgets/google-map.php:309 elementor/widgets/mapbox.php:293
#: elementor/widgets/open-street-map.php:299
#: elementor/widgets/properties-ajax-tabs.php:493
#, fuzzy
#| msgid "Most Favourite Properties"
msgid "Featured Properties"
msgstr "Самые любимые свойства"

#: elementor/traits/Houzez_Filters_Traits.php:220
#: elementor/widgets/properties-ajax-tabs.php:496
msgid "- Any -"
msgstr ""

#: elementor/traits/Houzez_Filters_Traits.php:221
#: elementor/widgets/properties-ajax-tabs.php:497
#, fuzzy
#| msgid "Featured"
msgid "Without Featured"
msgstr "Рекомендуемые"

#: elementor/traits/Houzez_Filters_Traits.php:222
#: elementor/widgets/properties-ajax-tabs.php:498
#, fuzzy
#| msgid "Featured"
msgid "Only Featured"
msgstr "Рекомендуемые"

#: elementor/traits/Houzez_Filters_Traits.php:224
#: elementor/widgets/properties-ajax-tabs.php:500
msgid ""
"You can make a post featured by clicking featured properties checkbox while "
"add/edit post"
msgstr ""

#: elementor/traits/Houzez_Filters_Traits.php:232
#: elementor/widgets/property-by-ids.php:134
#, fuzzy
#| msgid "Properties"
msgid "Properties IDs"
msgstr "свойства"

#: elementor/traits/Houzez_Filters_Traits.php:234
#: elementor/widgets/property-by-ids.php:136
msgid "Enter properties ids comma separated. Ex 12,305,34"
msgstr ""

#: elementor/traits/Houzez_Filters_Traits.php:244
#: elementor/traits/Houzez_Property_Cards_Traits.php:384
#: elementor/widgets/blog-posts-v2.php:88 elementor/widgets/blog-posts.php:88
#: elementor/widgets/properties-ajax-tabs.php:610
#: elementor/widgets/properties-recent-viewed.php:129
#: elementor/widgets/single-agency/agency-image.php:50
#: elementor/widgets/single-agent/agent-image.php:50
#: elementor/widgets/single-post/post-image.php:50
#: elementor/widgets/single-property/featured-image.php:50
#, fuzzy
#| msgid "Thumbnail"
msgid "Thumbnail Size"
msgstr "Thumbnail"

#: elementor/traits/Houzez_Filters_Traits.php:254
#: elementor/widgets/properties-ajax-tabs.php:620
msgid "Sort By"
msgstr ""

#: elementor/traits/Houzez_Filters_Traits.php:265
#: elementor/widgets/google-map.php:203 elementor/widgets/mapbox.php:187
#: elementor/widgets/open-street-map.php:193
#: elementor/widgets/properties-ajax-tabs.php:631
#: elementor/widgets/properties-slider.php:93
#: elementor/widgets/single-agency/agency-listings-review.php:135
#: elementor/widgets/single-agency/agency-listings.php:135
#: elementor/widgets/single-agent/agent-listings-review.php:140
#: elementor/widgets/single-agent/agent-listings.php:140
#, fuzzy
#| msgid "Properties"
msgid "Number of properties"
msgstr "свойства"

#: elementor/traits/Houzez_Filters_Traits.php:286
#: elementor/widgets/properties-ajax-tabs.php:508
#, fuzzy
#| msgid "Status"
msgid "Post Status"
msgstr "Положение дел"

#: elementor/traits/Houzez_Filters_Traits.php:423
#: elementor/widgets/grids.php:233
msgid "Show Child"
msgstr ""

#: elementor/traits/Houzez_Filters_Traits.php:437
#: elementor/widgets/grids.php:247
msgid "Hide Empty"
msgstr ""

#: elementor/traits/Houzez_Filters_Traits.php:451
#: elementor/widgets/grids.php:261
#, fuzzy
#| msgid "Views Count"
msgid "Hide Count"
msgstr "Количество просмотров"

#: elementor/traits/Houzez_Filters_Traits.php:465
#: elementor/traits/Houzez_Testimonials_Traits.php:33
#: elementor/widgets/agents-grid.php:163 elementor/widgets/agents.php:161
#: elementor/widgets/grids.php:275 elementor/widgets/partners.php:88
msgid "Order By"
msgstr ""

#: elementor/traits/Houzez_Filters_Traits.php:469
#: elementor/widgets/grids.php:279 elementor/widgets/taxonomies-list.php:671
#, fuzzy
#| msgid "Country"
msgid "Count"
msgstr "Страна"

#: elementor/traits/Houzez_Filters_Traits.php:470
#: elementor/traits/Houzez_Testimonials_Traits.php:37
#: elementor/widgets/agents-grid.php:167 elementor/widgets/agents.php:165
#: elementor/widgets/grids.php:280 elementor/widgets/partners.php:92
msgid "ID"
msgstr ""

#: elementor/traits/Houzez_Filters_Traits.php:480
#: elementor/traits/Houzez_Testimonials_Traits.php:49
#: elementor/widgets/agents-grid.php:179 elementor/widgets/agents.php:177
#: elementor/widgets/grids.php:290 elementor/widgets/partners.php:106
msgid "Order"
msgstr ""

#: elementor/traits/Houzez_Filters_Traits.php:483
#: elementor/traits/Houzez_Testimonials_Traits.php:52
#: elementor/widgets/agents-grid.php:182 elementor/widgets/agents.php:180
#: elementor/widgets/grids.php:293 elementor/widgets/partners.php:109
msgid "ASC"
msgstr ""

#: elementor/traits/Houzez_Filters_Traits.php:484
#: elementor/traits/Houzez_Testimonials_Traits.php:53
#: elementor/widgets/agents-grid.php:183 elementor/widgets/agents.php:181
#: elementor/widgets/grids.php:294 elementor/widgets/partners.php:110
msgid "DESC"
msgstr ""

#: elementor/traits/Houzez_Filters_Traits.php:494
#: elementor/widgets/grids.php:304
msgid "Number of Items to Show"
msgstr ""

#: elementor/traits/Houzez_Form_Traits.php:26
#: elementor/widgets/advanced-search.php:98
#: elementor/widgets/search-builder.php:777
msgid "Form"
msgstr ""

#: elementor/traits/Houzez_Form_Traits.php:34
#: elementor/widgets/search-builder.php:785
msgid "Columns Gap"
msgstr ""

#: elementor/traits/Houzez_Form_Traits.php:55
#: elementor/widgets/search-builder.php:806
msgid "Rows Gap"
msgstr ""

#: elementor/traits/Houzez_Form_Traits.php:76
#: elementor/widgets/contact-form.php:121
#: elementor/widgets/inquiry-form.php:132
#: elementor/widgets/property-meta-data.php:102
#: elementor/widgets/property-meta-data.php:343
#: elementor/widgets/search-builder.php:83
#: elementor/widgets/search-builder.php:143
#: elementor/widgets/search-builder.php:827
#: elementor/widgets/single-post/post-navigation.php:49
#: elementor/widgets/single-post/post-navigation.php:110
#: elementor/widgets/single-property/section-overview-v2.php:83
#: elementor/widgets/single-property/section-overview.php:82
#, fuzzy
#| msgid "Labels"
msgid "Label"
msgstr "Этикетки"

#: elementor/traits/Houzez_Form_Traits.php:85
#: elementor/traits/Houzez_Style_Traits.php:536
#: elementor/widgets/header-footer/site-logo.php:597
#: elementor/widgets/icon-box.php:397 elementor/widgets/search-builder.php:836
#: elementor/widgets/single-agency/agency-rating.php:135
#: elementor/widgets/single-agent/agent-rating.php:135
msgid "Spacing"
msgstr ""

#: elementor/traits/Houzez_Form_Traits.php:118
msgid "Mark Color"
msgstr ""

#: elementor/traits/Houzez_Form_Traits.php:153
#: elementor/traits/Houzez_Form_Traits.php:224
#: elementor/traits/Houzez_Form_Traits.php:345
#: elementor/traits/Houzez_Form_Traits.php:417
#: elementor/traits/Houzez_Property_Cards_Traits.php:48
#: elementor/traits/Houzez_Property_Cards_Traits.php:93
#: elementor/traits/Houzez_Property_Cards_Traits.php:164
#: elementor/traits/Houzez_Property_Cards_Traits.php:209
#: elementor/traits/Houzez_Property_Cards_Traits.php:531
#: elementor/traits/Houzez_Property_Cards_Traits.php:582
#: elementor/traits/Houzez_Property_Cards_Traits.php:1830
#: elementor/traits/Houzez_Property_Cards_Traits.php:1878
#: elementor/traits/Houzez_Style_Traits.php:509
#: elementor/traits/Houzez_Style_Traits.php:610
#: elementor/traits/Houzez_Style_Traits.php:755
#: elementor/widgets/advanced-search.php:118
#: elementor/widgets/advanced-search.php:192
#: elementor/widgets/advanced-search.php:249
#: elementor/widgets/advanced-search.php:321
#: elementor/widgets/agents-grid.php:315 elementor/widgets/agents-grid.php:480
#: elementor/widgets/agents-grid.php:520 elementor/widgets/agents.php:203
#: elementor/widgets/agents.php:312 elementor/widgets/agents.php:352
#: elementor/widgets/blog-posts-carousel.php:292
#: elementor/widgets/blog-posts-carousel.php:752
#: elementor/widgets/blog-posts-carousel.php:787
#: elementor/widgets/blog-posts-carousel.php:832
#: elementor/widgets/blog-posts-v2.php:184 elementor/widgets/blog-posts.php:228
#: elementor/widgets/blog-posts.php:683
#: elementor/widgets/custom-carousel.php:407
#: elementor/widgets/header-footer/area-switcher.php:267
#: elementor/widgets/header-footer/currency.php:267
#: elementor/widgets/header-footer/menu.php:812
#: elementor/widgets/header-footer/menu.php:856
#: elementor/widgets/header-footer/menu.php:899
#: elementor/widgets/header-footer/menu.php:1090
#: elementor/widgets/header-footer/menu.php:1122
#: elementor/widgets/header-footer/menu.php:1208
#: elementor/widgets/header-footer/site-logo.php:559
#: elementor/widgets/properties-slider.php:143
#: elementor/widgets/properties-slider.php:185
#: elementor/widgets/property-cards-v8.php:576
#: elementor/widgets/search-builder.php:888
#: elementor/widgets/search-builder.php:974
#: elementor/widgets/search-builder.php:1248
#: elementor/widgets/search-builder.php:1320
#: elementor/widgets/single-agency/agency-about.php:96
#: elementor/widgets/single-agency/agency-contact.php:170
#: elementor/widgets/single-agency/agency-listings-review.php:333
#: elementor/widgets/single-agency/agency-listings-review.php:407
#: elementor/widgets/single-agency/agency-profile-v1.php:154
#: elementor/widgets/single-agency/agency-profile-v1.php:297
#: elementor/widgets/single-agency/agency-profile-v1.php:369
#: elementor/widgets/single-agency/agency-profile-v1.php:429
#: elementor/widgets/single-agency/agency-profile-v1.php:501
#: elementor/widgets/single-agency/agency-profile-v2.php:88
#: elementor/widgets/single-agency/agency-search.php:74
#: elementor/widgets/single-agency/agency-search.php:226
#: elementor/widgets/single-agency/agency-search.php:290
#: elementor/widgets/single-agency/agency-search.php:362
#: elementor/widgets/single-agency/agency-single-stats.php:89
#: elementor/widgets/single-agency/agency-stats.php:53
#: elementor/widgets/single-agent/agent-about.php:96
#: elementor/widgets/single-agent/agent-contact.php:170
#: elementor/widgets/single-agent/agent-listings-review.php:338
#: elementor/widgets/single-agent/agent-listings-review.php:412
#: elementor/widgets/single-agent/agent-profile-v1.php:166
#: elementor/widgets/single-agent/agent-profile-v1.php:309
#: elementor/widgets/single-agent/agent-profile-v1.php:381
#: elementor/widgets/single-agent/agent-profile-v1.php:441
#: elementor/widgets/single-agent/agent-profile-v1.php:513
#: elementor/widgets/single-agent/agent-profile-v2.php:100
#: elementor/widgets/single-agent/agent-search.php:74
#: elementor/widgets/single-agent/agent-search.php:226
#: elementor/widgets/single-agent/agent-search.php:290
#: elementor/widgets/single-agent/agent-search.php:362
#: elementor/widgets/single-agent/agent-single-stats.php:89
#: elementor/widgets/single-agent/agent-stats.php:53
#: elementor/widgets/single-post/author-box.php:694
#: elementor/widgets/single-post/author-box.php:745
#: elementor/widgets/single-property/featured-label.php:149
#: elementor/widgets/single-property/featured-label.php:180
#: elementor/widgets/single-property/item-label.php:149
#: elementor/widgets/single-property/item-label.php:180
#: elementor/widgets/single-property/item-tools.php:98
#: elementor/widgets/single-property/property-title-area.php:545
#: elementor/widgets/single-property/section-toparea-v1.php:471
#: elementor/widgets/single-property/section-toparea-v2.php:470
#: elementor/widgets/single-property/section-toparea-v3.php:495
#: elementor/widgets/single-property/section-toparea-v5.php:470
#: elementor/widgets/single-property/section-toparea-v6.php:515
#: elementor/widgets/single-property/section-toparea-v7.php:490
#: elementor/widgets/single-property/status-label.php:150
#: elementor/widgets/single-property/status-label.php:181
#: elementor/widgets/taxonomies-cards-carousel.php:298
#: elementor/widgets/taxonomies-cards-carousel.php:422
#: elementor/widgets/taxonomies-cards-carousel.php:466
#: elementor/widgets/taxonomies-cards.php:203
#: elementor/widgets/taxonomies-grids-carousel.php:449
#: elementor/widgets/taxonomies-grids-carousel.php:493
#: elementor/widgets/testimonials-v2.php:145
msgid "Background Color"
msgstr ""

#: elementor/traits/Houzez_Form_Traits.php:166
#: elementor/widgets/advanced-search.php:131
#: elementor/widgets/agent-card.php:295 elementor/widgets/agent-card.php:727
#: elementor/widgets/agent-card.php:779 elementor/widgets/grids.php:445
#: elementor/widgets/listings-tabs.php:227
#: elementor/widgets/properties-recent-viewed.php:541
#: elementor/widgets/property-cards-v8.php:497
#: elementor/widgets/search-builder.php:913
#: elementor/widgets/single-agency/agency-listings-review.php:507
#: elementor/widgets/single-agency/agency-listings.php:329
#: elementor/widgets/single-agent/agent-listings-review.php:512
#: elementor/widgets/single-agent/agent-listings.php:334
#: elementor/widgets/single-post/author-box.php:342
#: elementor/widgets/single-property/section-address.php:301
#: elementor/widgets/single-property/section-calculator.php:132
#: elementor/widgets/single-property/section-contact-2.php:258
#: elementor/widgets/single-property/section-contact-2.php:424
#: elementor/widgets/single-property/section-details.php:531
#: elementor/widgets/single-property/section-schedule-tour-v2.php:229
msgid "Border"
msgstr ""

#: elementor/traits/Houzez_Form_Traits.php:174
#: elementor/traits/Houzez_Property_Cards_Traits.php:2107
#: elementor/widgets/advanced-search.php:139
#: elementor/widgets/grid-builder.php:588 elementor/widgets/grids.php:465
#: elementor/widgets/listings-tabs.php:262
#: elementor/widgets/search-builder.php:104
#: elementor/widgets/search-builder.php:921
#: elementor/widgets/search-builder.php:1194
#: elementor/widgets/single-agency/agency-about.php:140
#: elementor/widgets/single-agency/agency-contact.php:202
#: elementor/widgets/single-agency/agency-listings-review.php:542
#: elementor/widgets/single-agency/agency-listings.php:364
#: elementor/widgets/single-agency/agency-profile-v1.php:210
#: elementor/widgets/single-agency/agency-profile-v2.php:144
#: elementor/widgets/single-agency/agency-search.php:126
#: elementor/widgets/single-agency/agency-single-stats.php:121
#: elementor/widgets/single-agency/agency-stats.php:85
#: elementor/widgets/single-agent/agent-about.php:140
#: elementor/widgets/single-agent/agent-contact.php:202
#: elementor/widgets/single-agent/agent-listings-review.php:547
#: elementor/widgets/single-agent/agent-listings.php:366
#: elementor/widgets/single-agent/agent-profile-v1.php:222
#: elementor/widgets/single-agent/agent-profile-v2.php:156
#: elementor/widgets/single-agent/agent-search.php:126
#: elementor/widgets/single-agent/agent-single-stats.php:121
#: elementor/widgets/single-agent/agent-stats.php:85
#: elementor/widgets/single-property/featured-label.php:102
#: elementor/widgets/single-property/item-label.php:102
#: elementor/widgets/single-property/section-contact-2.php:299
#: elementor/widgets/single-property/section-contact-2.php:481
#: elementor/widgets/single-property/section-schedule-tour-v2.php:274
#: elementor/widgets/single-property/status-label.php:103
#: elementor/widgets/taxonomies-grids-carousel.php:317
#: elementor/widgets/taxonomies-grids.php:262
#: elementor/widgets/team-member.php:230
msgid "Radius"
msgstr ""

#: elementor/traits/Houzez_Form_Traits.php:187
#: elementor/widgets/advanced-search.php:152
#: elementor/widgets/agent-card.php:324
#: elementor/widgets/header-footer/login-modal.php:547
#: elementor/widgets/login-modal.php:482
#: elementor/widgets/properties-recent-viewed.php:499
#: elementor/widgets/properties-recent-viewed.php:507
#: elementor/widgets/property-cards-v8.php:444
#: elementor/widgets/property-cards-v8.php:452
#: elementor/widgets/search-builder.php:934
msgid "Box Shadow"
msgstr ""

#: elementor/traits/Houzez_Form_Traits.php:197
#: elementor/widgets/advanced-search.php:162
#: elementor/widgets/contact-form.php:111
#: elementor/widgets/inquiry-form.php:122
#: elementor/widgets/search-builder.php:133
#: elementor/widgets/single-property/property-address.php:60
#: elementor/widgets/single-property/property-title-area.php:63
#: elementor/widgets/single-property/section-overview-v2.php:73
#: elementor/widgets/single-property/section-overview.php:72
#: elementor/widgets/single-property/section-toparea-v1.php:63
#: elementor/widgets/single-property/section-toparea-v2.php:62
#: elementor/widgets/single-property/section-toparea-v3.php:67
#: elementor/widgets/single-property/section-toparea-v5.php:62
#: elementor/widgets/single-property/section-toparea-v6.php:63
#: elementor/widgets/single-property/section-toparea-v7.php:62
msgid "Field"
msgstr ""

#: elementor/traits/Houzez_Form_Traits.php:262
#: elementor/widgets/header-footer/menu.php:998
#: elementor/widgets/header-footer/menu.php:1154
#: elementor/widgets/single-post/author-box.php:389
#: elementor/widgets/single-post/author-box.php:792
#: elementor/widgets/single-property/section-overview.php:321
msgid "Border Width"
msgstr ""

#: elementor/traits/Houzez_Form_Traits.php:291
msgid "GDPR"
msgstr ""

#: elementor/traits/Houzez_Form_Traits.php:328
#: elementor/traits/Houzez_Property_Cards_Traits.php:17
#: elementor/traits/Houzez_Property_Cards_Traits.php:1777
#: elementor/widgets/advanced-search.php:232
#: elementor/widgets/agents-grid.php:440 elementor/widgets/agents-grid.php:451
#: elementor/widgets/agents.php:282 elementor/widgets/agents.php:293
#: elementor/widgets/property-cards-v8.php:545
#: elementor/widgets/search-builder.php:1231
#: elementor/widgets/single-agency/agency-search.php:273
#: elementor/widgets/single-agent/agent-search.php:273
msgid "Button"
msgstr ""

#: elementor/traits/Houzez_Form_Traits.php:396
#: elementor/widgets/advanced-search.php:300
#: elementor/widgets/single-agency/agency-listings-review.php:385
#: elementor/widgets/single-agency/agency-profile-v1.php:348
#: elementor/widgets/single-agency/agency-profile-v1.php:480
#: elementor/widgets/single-agency/agency-search.php:341
#: elementor/widgets/single-agent/agent-listings-review.php:390
#: elementor/widgets/single-agent/agent-profile-v1.php:360
#: elementor/widgets/single-agent/agent-profile-v1.php:492
#: elementor/widgets/single-agent/agent-search.php:341
msgid "Text Padding"
msgstr ""

#: elementor/traits/Houzez_Form_Traits.php:454
#: elementor/widgets/header-footer/menu.php:244
#: elementor/widgets/header-footer/menu.php:265
#: elementor/widgets/header-footer/menu.php:284
#: elementor/widgets/header-footer/menu.php:307
#: elementor/widgets/search-builder.php:1357
#: elementor/widgets/single-post/author-box.php:777
#, fuzzy
#| msgid "Activation"
msgid "Animation"
msgstr "активация"

#: elementor/traits/Houzez_Property_Cards_Traits.php:149
#: elementor/widgets/blog-posts-carousel.php:772
#: elementor/widgets/properties-slider.php:128
#: elementor/widgets/taxonomies-cards-carousel.php:402
#: elementor/widgets/taxonomies-grids-carousel.php:429
msgid "Next/Prev buttons"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:176
#: elementor/traits/Houzez_Property_Cards_Traits.php:221
#: elementor/traits/Houzez_Property_Cards_Traits.php:352
#: elementor/traits/Houzez_Property_Cards_Traits.php:518
#: elementor/traits/Houzez_Property_Cards_Traits.php:569
#: elementor/traits/Houzez_Style_Traits.php:91
#: elementor/traits/Houzez_Style_Traits.php:887
#: elementor/traits/Houzez_Style_Traits.php:924
#: elementor/traits/Houzez_Style_Traits.php:1251
#: elementor/traits/Houzez_Style_Traits.php:1285
#: elementor/traits/Houzez_Style_Traits.php:1312
#: elementor/widgets/agent-card.php:439 elementor/widgets/agent-card.php:502
#: elementor/widgets/banner-image.php:468
#: elementor/widgets/blog-posts-carousel.php:421
#: elementor/widgets/blog-posts-carousel.php:488
#: elementor/widgets/blog-posts-carousel.php:579
#: elementor/widgets/blog-posts-carousel.php:662
#: elementor/widgets/blog-posts-carousel.php:799
#: elementor/widgets/blog-posts-carousel.php:844
#: elementor/widgets/blog-posts-carousel.php:972
#: elementor/widgets/blog-posts-v2.php:249
#: elementor/widgets/blog-posts-v2.php:313
#: elementor/widgets/blog-posts-v2.php:389
#: elementor/widgets/blog-posts-v2.php:466 elementor/widgets/blog-posts.php:352
#: elementor/widgets/blog-posts.php:419 elementor/widgets/blog-posts.php:510
#: elementor/widgets/blog-posts.php:593
#: elementor/widgets/custom-carousel.php:1032
#: elementor/widgets/custom-carousel.php:1070
#: elementor/widgets/header-footer/area-switcher.php:195
#: elementor/widgets/header-footer/area-switcher.php:215
#: elementor/widgets/header-footer/area-switcher.php:278
#: elementor/widgets/header-footer/currency.php:195
#: elementor/widgets/header-footer/currency.php:215
#: elementor/widgets/header-footer/currency.php:278
#: elementor/widgets/header-footer/lang.php:177
#: elementor/widgets/header-footer/menu.php:690
#: elementor/widgets/header-footer/menu.php:1078
#: elementor/widgets/header-footer/menu.php:1110
#: elementor/widgets/header-footer/menu.php:1197
#: elementor/widgets/properties-slider.php:154
#: elementor/widgets/properties-slider.php:196
#: elementor/widgets/property-meta-data.php:222
#: elementor/widgets/property-meta-data.php:329
#: elementor/widgets/property-meta-data.php:389
#: elementor/widgets/search-builder.php:1260
#: elementor/widgets/search-builder.php:1332
#: elementor/widgets/single-agency/agency-address.php:57
#: elementor/widgets/single-agency/agency-meta.php:299
#: elementor/widgets/single-agency/agency-meta.php:353
#: elementor/widgets/single-agency/agency-meta.php:406
#: elementor/widgets/single-agency/agency-profile-v2.php:231
#: elementor/widgets/single-agency/agency-profile-v2.php:260
#: elementor/widgets/single-agent/agent-meta.php:300
#: elementor/widgets/single-agent/agent-meta.php:354
#: elementor/widgets/single-agent/agent-meta.php:407
#: elementor/widgets/single-agent/agent-position.php:76
#: elementor/widgets/single-agent/agent-profile-v2.php:243
#: elementor/widgets/single-agent/agent-profile-v2.php:272
#: elementor/widgets/single-post/author-box.php:498
#: elementor/widgets/single-post/author-box.php:585
#: elementor/widgets/single-post/author-box.php:731
#: elementor/widgets/single-post/post-info.php:564
#: elementor/widgets/single-post/post-info.php:589
#: elementor/widgets/single-post/post-navigation.php:130
#: elementor/widgets/single-post/post-navigation.php:152
#: elementor/widgets/single-post/post-navigation.php:215
#: elementor/widgets/single-post/post-navigation.php:235
#: elementor/widgets/single-property/featured-label.php:138
#: elementor/widgets/single-property/featured-label.php:169
#: elementor/widgets/single-property/item-label.php:138
#: elementor/widgets/single-property/item-label.php:169
#: elementor/widgets/single-property/item-tools.php:122
#: elementor/widgets/single-property/property-address.php:157
#: elementor/widgets/single-property/property-price.php:105
#: elementor/widgets/single-property/property-price.php:180
#: elementor/widgets/single-property/property-title-area.php:216
#: elementor/widgets/single-property/property-title-area.php:366
#: elementor/widgets/single-property/property-title-area.php:419
#: elementor/widgets/single-property/property-title-area.php:494
#: elementor/widgets/single-property/property-title-area.php:569
#: elementor/widgets/single-property/section-360-virtual.php:110
#: elementor/widgets/single-property/section-address.php:217
#: elementor/widgets/single-property/section-address.php:248
#: elementor/widgets/single-property/section-address.php:278
#: elementor/widgets/single-property/section-attachments.php:263
#: elementor/widgets/single-property/section-attachments.php:292
#: elementor/widgets/single-property/section-calculator.php:109
#: elementor/widgets/single-property/section-calendar.php:109
#: elementor/widgets/single-property/section-contact-bottom.php:100
#: elementor/widgets/single-property/section-contact-bottom.php:381
#: elementor/widgets/single-property/section-description.php:145
#: elementor/widgets/single-property/section-description.php:174
#: elementor/widgets/single-property/section-description.php:232
#: elementor/widgets/single-property/section-details.php:447
#: elementor/widgets/single-property/section-details.php:478
#: elementor/widgets/single-property/section-details.php:508
#: elementor/widgets/single-property/section-energy.php:195
#: elementor/widgets/single-property/section-energy.php:224
#: elementor/widgets/single-property/section-energy.php:253
#: elementor/widgets/single-property/section-features.php:123
#: elementor/widgets/single-property/section-floorplan-v2.php:156
#: elementor/widgets/single-property/section-floorplan-v2.php:195
#: elementor/widgets/single-property/section-floorplan-v2.php:244
#: elementor/widgets/single-property/section-floorplan-v2.php:276
#: elementor/widgets/single-property/section-floorplan.php:164
#: elementor/widgets/single-property/section-floorplan.php:264
#: elementor/widgets/single-property/section-floorplan.php:296
#: elementor/widgets/single-property/section-nearby.php:117
#: elementor/widgets/single-property/section-overview-v2.php:290
#: elementor/widgets/single-property/section-overview-v2.php:319
#: elementor/widgets/single-property/section-overview-v2.php:348
#: elementor/widgets/single-property/section-overview-v2.php:377
#: elementor/widgets/single-property/section-overview.php:370
#: elementor/widgets/single-property/section-overview.php:399
#: elementor/widgets/single-property/section-overview.php:428
#: elementor/widgets/single-property/section-overview.php:457
#: elementor/widgets/single-property/section-schedule-tour-v2.php:120
#: elementor/widgets/single-property/section-schedule-tour.php:110
#: elementor/widgets/single-property/section-toparea-v1.php:118
#: elementor/widgets/single-property/section-toparea-v1.php:495
#: elementor/widgets/single-property/section-toparea-v2.php:118
#: elementor/widgets/single-property/section-toparea-v2.php:268
#: elementor/widgets/single-property/section-toparea-v2.php:320
#: elementor/widgets/single-property/section-toparea-v2.php:395
#: elementor/widgets/single-property/section-toparea-v2.php:494
#: elementor/widgets/single-property/section-toparea-v3.php:143
#: elementor/widgets/single-property/section-toparea-v3.php:293
#: elementor/widgets/single-property/section-toparea-v3.php:345
#: elementor/widgets/single-property/section-toparea-v3.php:420
#: elementor/widgets/single-property/section-toparea-v3.php:519
#: elementor/widgets/single-property/section-toparea-v5.php:268
#: elementor/widgets/single-property/section-toparea-v5.php:320
#: elementor/widgets/single-property/section-toparea-v5.php:395
#: elementor/widgets/single-property/section-toparea-v5.php:494
#: elementor/widgets/single-property/section-toparea-v6.php:163
#: elementor/widgets/single-property/section-toparea-v6.php:313
#: elementor/widgets/single-property/section-toparea-v6.php:365
#: elementor/widgets/single-property/section-toparea-v6.php:440
#: elementor/widgets/single-property/section-toparea-v6.php:539
#: elementor/widgets/single-property/section-toparea-v7.php:138
#: elementor/widgets/single-property/section-toparea-v7.php:288
#: elementor/widgets/single-property/section-toparea-v7.php:340
#: elementor/widgets/single-property/section-toparea-v7.php:415
#: elementor/widgets/single-property/section-toparea-v7.php:514
#: elementor/widgets/single-property/section-video.php:109
#: elementor/widgets/single-property/section-walkscore.php:117
#: elementor/widgets/single-property/status-label.php:139
#: elementor/widgets/single-property/status-label.php:170
#: elementor/widgets/taxonomies-cards-carousel.php:433
#: elementor/widgets/taxonomies-cards-carousel.php:477
#: elementor/widgets/taxonomies-cards-carousel.php:608
#: elementor/widgets/taxonomies-grids-carousel.php:460
#: elementor/widgets/taxonomies-grids-carousel.php:504
#: elementor/widgets/taxonomies-grids-carousel.php:635
#: elementor/widgets/taxonomies-list.php:166
#: elementor/widgets/taxonomies-list.php:308
#: elementor/widgets/taxonomies-list.php:380
#: elementor/widgets/taxonomies-list.php:405
#: elementor/widgets/taxonomies-list.php:612
#: elementor/widgets/taxonomies-list.php:636
#: elementor/widgets/taxonomies-list.php:728
#: elementor/widgets/taxonomies-list.php:752
#: elementor/widgets/testimonials-v3.php:129
#: elementor/widgets/testimonials-v3.php:167
msgid "Color"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:253
#: elementor/widgets/blog-posts-carousel.php:876
#: elementor/widgets/taxonomies-cards-carousel.php:509
#: elementor/widgets/taxonomies-grids-carousel.php:536
msgid "Carousel Dots"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:264
#: elementor/widgets/blog-posts-carousel.php:884
#: elementor/widgets/contact-form.php:439
#: elementor/widgets/header-footer/menu.php:1137
#: elementor/widgets/inquiry-form.php:424
#: elementor/widgets/property-meta-data.php:235
#: elementor/widgets/single-agency/agency-meta.php:428
#: elementor/widgets/single-agent/agent-meta.php:429
#: elementor/widgets/single-post/post-info.php:188
#: elementor/widgets/single-post/post-info.php:602
#: elementor/widgets/taxonomies-cards-carousel.php:520
#: elementor/widgets/taxonomies-grids-carousel.php:547
#: elementor/widgets/taxonomies-list.php:439
#: statistics/houzez-statistics.php:517 statistics/houzez-statistics.php:520
msgid "Size"
msgstr "Размер"

#: elementor/traits/Houzez_Property_Cards_Traits.php:281
#: elementor/widgets/blog-posts-carousel.php:901
#: elementor/widgets/custom-carousel.php:386
#: elementor/widgets/property-meta-data.php:190
#: elementor/widgets/single-agency/agency-meta.php:243
#: elementor/widgets/single-agent/agent-meta.php:244
#: elementor/widgets/single-post/post-info.php:377
#: elementor/widgets/taxonomies-cards-carousel.php:537
#: elementor/widgets/taxonomies-grids-carousel.php:564
#: elementor/widgets/taxonomies-list.php:188
msgid "Space Between"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:298
#: elementor/traits/Houzez_Style_Traits.php:1016
#: elementor/traits/Houzez_Style_Traits.php:1220
#: elementor/widgets/blog-posts-carousel.php:442
#: elementor/widgets/blog-posts-carousel.php:515
#: elementor/widgets/blog-posts-carousel.php:616
#: elementor/widgets/blog-posts-carousel.php:918
#: elementor/widgets/blog-posts-v2.php:270
#: elementor/widgets/blog-posts-v2.php:334
#: elementor/widgets/blog-posts-v2.php:422 elementor/widgets/blog-posts.php:373
#: elementor/widgets/blog-posts.php:446 elementor/widgets/blog-posts.php:547
#: elementor/widgets/search-builder.php:1063
#: elementor/widgets/single-agency/agency-about.php:152
#: elementor/widgets/single-agency/agency-contact-form.php:67
#: elementor/widgets/single-agency/agency-contact.php:216
#: elementor/widgets/single-agency/agency-listings-review.php:268
#: elementor/widgets/single-agency/agency-profile-v1.php:222
#: elementor/widgets/single-agency/agency-profile-v2.php:156
#: elementor/widgets/single-agency/agency-search.php:138
#: elementor/widgets/single-agency/agency-stats.php:117
#: elementor/widgets/single-agent/agent-about.php:152
#: elementor/widgets/single-agent/agent-contact-form.php:67
#: elementor/widgets/single-agent/agent-contact.php:216
#: elementor/widgets/single-agent/agent-listings-review.php:273
#: elementor/widgets/single-agent/agent-profile-v1.php:234
#: elementor/widgets/single-agent/agent-profile-v2.php:168
#: elementor/widgets/single-agent/agent-search.php:138
#: elementor/widgets/single-agent/agent-stats.php:117
#: elementor/widgets/single-property/property-price.php:116
#: elementor/widgets/single-property/property-price.php:191
#: elementor/widgets/single-property/property-title-area.php:244
#: elementor/widgets/single-property/property-title-area.php:430
#: elementor/widgets/single-property/section-attachments.php:140
#: elementor/widgets/single-property/section-block-gallery.php:101
#: elementor/widgets/single-property/section-contact-2.php:131
#: elementor/widgets/single-property/section-contact-bottom.php:422
#: elementor/widgets/single-property/section-contact-bottom.php:519
#: elementor/widgets/single-property/section-schedule-tour-v2.php:65
#: elementor/widgets/single-property/section-similar.php:243
#: elementor/widgets/single-property/section-sublistings.php:153
#: elementor/widgets/single-property/section-toparea-v1.php:146
#: elementor/widgets/single-property/section-toparea-v1.php:332
#: elementor/widgets/single-property/section-toparea-v2.php:146
#: elementor/widgets/single-property/section-toparea-v2.php:331
#: elementor/widgets/single-property/section-toparea-v3.php:171
#: elementor/widgets/single-property/section-toparea-v3.php:356
#: elementor/widgets/single-property/section-toparea-v5.php:146
#: elementor/widgets/single-property/section-toparea-v5.php:331
#: elementor/widgets/single-property/section-toparea-v6.php:191
#: elementor/widgets/single-property/section-toparea-v6.php:376
#: elementor/widgets/single-property/section-toparea-v7.php:166
#: elementor/widgets/single-property/section-toparea-v7.php:351
#: elementor/widgets/taxonomies-cards-carousel.php:554
#: elementor/widgets/taxonomies-grids-carousel.php:581
msgid "Margin Top"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:316
#: elementor/traits/Houzez_Style_Traits.php:331
#: elementor/traits/Houzez_Style_Traits.php:365
#: elementor/widgets/banner-image.php:336
#: elementor/widgets/banner-image.php:370
#: elementor/widgets/blog-posts-carousel.php:936
#: elementor/widgets/custom-carousel.php:623
#: elementor/widgets/custom-carousel.php:657
#: elementor/widgets/header-footer/site-logo.php:251
#: elementor/widgets/header-footer/site-logo.php:285
#: elementor/widgets/single-property/featured-image.php:211
#: elementor/widgets/single-property/featured-image.php:245
#: elementor/widgets/taxonomies-cards-carousel.php:572
#: elementor/widgets/taxonomies-cards.php:310
#: elementor/widgets/taxonomies-cards.php:336
#: elementor/widgets/taxonomies-grids-carousel.php:347
#: elementor/widgets/taxonomies-grids-carousel.php:385
#: elementor/widgets/taxonomies-grids-carousel.php:599
#: elementor/widgets/taxonomies-grids.php:292
#: elementor/widgets/taxonomies-grids.php:330
msgid "Opacity"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:334
#: elementor/widgets/blog-posts-carousel.php:954
#: elementor/widgets/taxonomies-cards-carousel.php:590
#: elementor/widgets/taxonomies-grids-carousel.php:617
msgid "Opacity Active"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:364
#: elementor/widgets/blog-posts-carousel.php:984
#: elementor/widgets/single-property/section-floorplan-v2.php:208
#: elementor/widgets/taxonomies-cards-carousel.php:620
#: elementor/widgets/taxonomies-grids-carousel.php:647
msgid "Active Color"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:393
#: elementor/widgets/blog-posts-carousel.php:189
#: elementor/widgets/custom-carousel.php:160
#: elementor/widgets/taxonomies-cards-carousel.php:143
#: elementor/widgets/taxonomies-grids-carousel.php:128
msgid "Slides To Show"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:410
#: elementor/widgets/custom-carousel.php:177
#: elementor/widgets/taxonomies-cards-carousel.php:160
#: elementor/widgets/taxonomies-grids-carousel.php:145
msgid "Slides To Scroll"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:427
#: elementor/widgets/blog-posts-carousel.php:203
#: elementor/widgets/custom-carousel.php:194
#: elementor/widgets/single-agency/agency-listings-review.php:153
#: elementor/widgets/single-agency/agency-listings.php:153
#: elementor/widgets/single-agent/agent-listings-review.php:158
#: elementor/widgets/single-agent/agent-listings.php:158
#: elementor/widgets/taxonomies-cards-carousel.php:177
#: elementor/widgets/taxonomies-grids-carousel.php:162
#: functions/functions.php:226
msgid "Infinite Scroll"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:440
#: elementor/widgets/blog-posts-carousel.php:216
#: elementor/widgets/custom-carousel.php:207
#: elementor/widgets/taxonomies-cards-carousel.php:190
#: elementor/widgets/taxonomies-grids-carousel.php:175
msgid "Auto Play"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:457
#: elementor/widgets/blog-posts-carousel.php:233
#: elementor/widgets/custom-carousel.php:224
#: elementor/widgets/taxonomies-cards-carousel.php:207
#: elementor/widgets/taxonomies-grids-carousel.php:192
msgid "Autoplay Speed in milliseconds. Default 3000"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:464
#: elementor/widgets/blog-posts-carousel.php:240
#: elementor/widgets/custom-carousel.php:231
#: elementor/widgets/taxonomies-cards-carousel.php:214
#: elementor/widgets/taxonomies-grids-carousel.php:199
msgid "Next/Prev Navigation"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:478
#: elementor/widgets/blog-posts-carousel.php:254
#: elementor/widgets/custom-carousel.php:245
#: elementor/widgets/taxonomies-cards-carousel.php:228
#: elementor/widgets/taxonomies-grids-carousel.php:213
msgid "Dots Nav"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:499
#: elementor/widgets/properties-ajax-tabs.php:652
#: elementor/widgets/properties.php:134
#: elementor/widgets/property-cards-v1.php:109
#: elementor/widgets/property-cards-v2.php:107
#: elementor/widgets/property-cards-v3.php:106
#: elementor/widgets/property-cards-v4.php:106
#: elementor/widgets/property-cards-v5.php:106
#: elementor/widgets/property-cards-v6.php:110
#: elementor/widgets/property-cards-v7.php:108
#: elementor/widgets/property-cards-v8.php:92
#: elementor/widgets/single-agency/agency-listings-review.php:147
#: elementor/widgets/single-agency/agency-listings.php:147
#: elementor/widgets/single-agent/agent-listings-review.php:152
#: elementor/widgets/single-agent/agent-listings.php:152
#, fuzzy
#| msgid "Activation"
msgid "Pagination"
msgstr "активация"

#: elementor/traits/Houzez_Property_Cards_Traits.php:614
msgid "Background Color Active"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:629
msgid "Color Active"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:676
#: elementor/traits/Houzez_Property_Cards_Traits.php:773
#: elementor/traits/Houzez_Property_Cards_Traits.php:1545
#: elementor/traits/Houzez_Property_Cards_Traits.php:1662
#: elementor/widgets/grid-builder.php:640 elementor/widgets/grids.php:408
#: elementor/widgets/properties-ajax-tabs.php:686
#: elementor/widgets/properties-recent-viewed.php:614
#: elementor/widgets/taxonomies-cards-carousel.php:274
#: elementor/widgets/taxonomies-cards.php:179
#: elementor/widgets/taxonomies-grids-carousel.php:259
#: elementor/widgets/taxonomies-grids.php:169
#, fuzzy
#| msgid "Title"
msgid "Title Color"
msgstr "заглавие"

#: elementor/traits/Houzez_Property_Cards_Traits.php:688
#: elementor/traits/Houzez_Property_Cards_Traits.php:1557
#: elementor/widgets/properties-recent-viewed.php:626
msgid "Address Color"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:700
#: elementor/traits/Houzez_Property_Cards_Traits.php:1589
#: elementor/widgets/properties-recent-viewed.php:638
msgid "Icons"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:712
#: elementor/traits/Houzez_Property_Cards_Traits.php:1601
#: elementor/widgets/properties-recent-viewed.php:650
msgid "Figure"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:740
#: elementor/traits/Houzez_Property_Cards_Traits.php:785
#: elementor/traits/Houzez_Property_Cards_Traits.php:1629
#: elementor/traits/Houzez_Property_Cards_Traits.php:1674
#: elementor/widgets/properties-recent-viewed.php:566
msgid "Item Tools Background Color"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:752
#: elementor/traits/Houzez_Property_Cards_Traits.php:797
#: elementor/traits/Houzez_Property_Cards_Traits.php:1641
#: elementor/traits/Houzez_Property_Cards_Traits.php:1686
#: elementor/widgets/properties-recent-viewed.php:590
msgid "Item Tools Color"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:818
#: elementor/widgets/property-cards-v1.php:482
#: elementor/widgets/property-cards-v2.php:458
#: elementor/widgets/property-cards-v4.php:477
#: elementor/widgets/property-cards-v5.php:340
#: elementor/widgets/property-cards-v6.php:298
#: elementor/widgets/property-cards-v7.php:439
#: elementor/widgets/property-carousel-v1.php:457
#: elementor/widgets/property-carousel-v2.php:434
#: elementor/widgets/property-carousel-v5.php:317
#: elementor/widgets/property-carousel-v6.php:270
#: elementor/widgets/property-carousel-v7.php:409
msgid "Card Box"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:848
#: elementor/widgets/property-cards-v1.php:492
#: elementor/widgets/property-cards-v4.php:487
#: elementor/widgets/property-carousel-v1.php:467
msgid "Box Footer Border Type"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:853
#: elementor/traits/Houzez_Style_Traits.php:984
#: elementor/traits/Houzez_Style_Traits.php:1337
#: elementor/traits/Houzez_Testimonials_Traits.php:36
#: elementor/widgets/agents-grid.php:166 elementor/widgets/agents.php:164
#: elementor/widgets/banner-image.php:147
#: elementor/widgets/header-footer/menu.php:226
#: elementor/widgets/header-footer/menu.php:332
#: elementor/widgets/header-footer/menu.php:366
#: elementor/widgets/header-footer/site-logo.php:499
#: elementor/widgets/partners.php:91 elementor/widgets/price-table.php:76
#: elementor/widgets/properties-ajax-tabs.php:250
#: elementor/widgets/properties-ajax-tabs.php:655
#: elementor/widgets/property-cards-v1.php:497
#: elementor/widgets/property-cards-v4.php:492
#: elementor/widgets/property-cards-v7.php:463
#: elementor/widgets/property-carousel-v1.php:472
#: elementor/widgets/property-carousel-v7.php:433
#: elementor/widgets/single-agency/agency-listings-review.php:150
#: elementor/widgets/single-agency/agency-listings.php:150
#: elementor/widgets/single-agent/agent-listings-review.php:155
#: elementor/widgets/single-agent/agent-listings.php:155
#: elementor/widgets/single-post/author-box.php:121
#: elementor/widgets/single-post/post-info.php:298
#: elementor/widgets/single-property/section-address.php:314
#: elementor/widgets/single-property/section-calculator.php:145
#: elementor/widgets/single-property/section-contact-bottom.php:125
#: elementor/widgets/single-property/section-details.php:408
#: elementor/widgets/single-property/section-details.php:544
#: elementor/widgets/single-property/section-energy.php:287
#: elementor/widgets/single-property/section-overview.php:300
#: elementor/widgets/single-property/section-similar.php:211
#: extensions/meta-box/inc/fields/select.php:80 functions/functions.php:223
msgid "None"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:854
#: elementor/traits/Houzez_Style_Traits.php:985
#: elementor/traits/Houzez_Style_Traits.php:1338
#: elementor/widgets/header-footer/menu.php:632
#: elementor/widgets/property-cards-v1.php:498
#: elementor/widgets/property-cards-v4.php:493
#: elementor/widgets/property-cards-v7.php:464
#: elementor/widgets/property-carousel-v1.php:473
#: elementor/widgets/property-carousel-v7.php:434
#: elementor/widgets/single-post/post-info.php:445
#: elementor/widgets/single-property/section-address.php:315
#: elementor/widgets/single-property/section-calculator.php:146
#: elementor/widgets/single-property/section-contact-bottom.php:126
#: elementor/widgets/single-property/section-details.php:409
#: elementor/widgets/single-property/section-details.php:545
#: elementor/widgets/single-property/section-energy.php:288
#: elementor/widgets/single-property/section-overview.php:301
#: elementor/widgets/single-property/section-similar.php:212
#: elementor/widgets/taxonomies-list.php:248
msgid "Solid"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:855
#: elementor/traits/Houzez_Style_Traits.php:986
#: elementor/traits/Houzez_Style_Traits.php:1339
#: elementor/widgets/header-footer/menu.php:635
#: elementor/widgets/property-cards-v1.php:499
#: elementor/widgets/property-cards-v4.php:494
#: elementor/widgets/property-cards-v7.php:465
#: elementor/widgets/property-carousel-v1.php:474
#: elementor/widgets/property-carousel-v7.php:435
#: elementor/widgets/single-post/post-info.php:448
#: elementor/widgets/single-property/section-address.php:316
#: elementor/widgets/single-property/section-calculator.php:147
#: elementor/widgets/single-property/section-contact-bottom.php:127
#: elementor/widgets/single-property/section-details.php:410
#: elementor/widgets/single-property/section-details.php:546
#: elementor/widgets/single-property/section-energy.php:289
#: elementor/widgets/single-property/section-overview.php:304
#: elementor/widgets/single-property/section-similar.php:213
#: elementor/widgets/taxonomies-list.php:251
msgid "Dashed"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:856
#: elementor/traits/Houzez_Style_Traits.php:987
#: elementor/traits/Houzez_Style_Traits.php:1340
#: elementor/widgets/header-footer/menu.php:634
#: elementor/widgets/property-cards-v1.php:500
#: elementor/widgets/property-cards-v4.php:495
#: elementor/widgets/property-cards-v7.php:466
#: elementor/widgets/property-carousel-v1.php:475
#: elementor/widgets/property-carousel-v7.php:436
#: elementor/widgets/single-post/post-info.php:447
#: elementor/widgets/single-property/section-address.php:317
#: elementor/widgets/single-property/section-calculator.php:148
#: elementor/widgets/single-property/section-contact-bottom.php:128
#: elementor/widgets/single-property/section-details.php:411
#: elementor/widgets/single-property/section-details.php:547
#: elementor/widgets/single-property/section-energy.php:290
#: elementor/widgets/single-property/section-overview.php:303
#: elementor/widgets/single-property/section-similar.php:214
#: elementor/widgets/taxonomies-list.php:250
msgid "Dotted"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:857
#: elementor/traits/Houzez_Style_Traits.php:988
#: elementor/traits/Houzez_Style_Traits.php:1341
#: elementor/widgets/property-cards-v1.php:501
#: elementor/widgets/property-cards-v4.php:496
#: elementor/widgets/property-cards-v7.php:467
#: elementor/widgets/property-carousel-v1.php:476
#: elementor/widgets/property-carousel-v7.php:437
#: elementor/widgets/single-property/section-address.php:318
#: elementor/widgets/single-property/section-calculator.php:149
#: elementor/widgets/single-property/section-contact-bottom.php:129
#: elementor/widgets/single-property/section-details.php:412
#: elementor/widgets/single-property/section-details.php:548
#: elementor/widgets/single-property/section-energy.php:291
#: elementor/widgets/single-property/section-overview.php:305
#: elementor/widgets/single-property/section-similar.php:215
msgid "Groove"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:858
#: elementor/traits/Houzez_Style_Traits.php:989
#: elementor/traits/Houzez_Style_Traits.php:1342
#: elementor/widgets/header-footer/menu.php:633
#: elementor/widgets/property-cards-v1.php:502
#: elementor/widgets/property-cards-v4.php:497
#: elementor/widgets/property-cards-v7.php:468
#: elementor/widgets/property-carousel-v1.php:477
#: elementor/widgets/property-carousel-v7.php:438
#: elementor/widgets/single-post/post-info.php:446
#: elementor/widgets/single-property/section-address.php:319
#: elementor/widgets/single-property/section-calculator.php:150
#: elementor/widgets/single-property/section-contact-bottom.php:130
#: elementor/widgets/single-property/section-details.php:413
#: elementor/widgets/single-property/section-details.php:549
#: elementor/widgets/single-property/section-energy.php:292
#: elementor/widgets/single-property/section-overview.php:302
#: elementor/widgets/single-property/section-similar.php:216
#: elementor/widgets/taxonomies-list.php:249
msgid "Double"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:859
#: elementor/traits/Houzez_Style_Traits.php:990
#: elementor/traits/Houzez_Style_Traits.php:1343
#: elementor/widgets/property-cards-v1.php:503
#: elementor/widgets/property-cards-v4.php:498
#: elementor/widgets/property-cards-v7.php:469
#: elementor/widgets/property-carousel-v1.php:478
#: elementor/widgets/property-carousel-v7.php:439
#: elementor/widgets/single-property/section-address.php:320
#: elementor/widgets/single-property/section-calculator.php:151
#: elementor/widgets/single-property/section-contact-bottom.php:131
#: elementor/widgets/single-property/section-details.php:414
#: elementor/widgets/single-property/section-details.php:550
#: elementor/widgets/single-property/section-energy.php:293
#: elementor/widgets/single-property/section-similar.php:217
msgid "Ridge"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:930
#: elementor/traits/Houzez_Style_Traits.php:1273
#: elementor/widgets/grids.php:347 elementor/widgets/property-cards-v1.php:534
#: elementor/widgets/property-cards-v2.php:473
#: elementor/widgets/property-cards-v4.php:529
#: elementor/widgets/property-cards-v5.php:355
#: elementor/widgets/property-cards-v6.php:313
#: elementor/widgets/property-cards-v7.php:499
#: elementor/widgets/property-carousel-v1.php:509
#: elementor/widgets/property-carousel-v2.php:449
#: elementor/widgets/property-carousel-v5.php:332
#: elementor/widgets/property-carousel-v6.php:285
#: elementor/widgets/property-carousel-v7.php:469
#: elementor/widgets/single-agency/agency-profile-v2.php:53
#: elementor/widgets/single-agent/agent-profile-v2.php:53
#: elementor/widgets/taxonomies-cards-carousel.php:364
#: elementor/widgets/taxonomies-cards.php:269
msgid "Image Radius"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:968
#: elementor/traits/Houzez_Style_Traits.php:902
#: elementor/traits/Houzez_Style_Traits.php:939
#: elementor/traits/Houzez_Style_Traits.php:1324
#: elementor/widgets/agents-grid.php:395 elementor/widgets/agents.php:245
#: elementor/widgets/banner-image.php:460
#: elementor/widgets/blog-posts-carousel.php:434
#: elementor/widgets/blog-posts-carousel.php:504
#: elementor/widgets/blog-posts-carousel.php:608
#: elementor/widgets/blog-posts-carousel.php:687
#: elementor/widgets/blog-posts-carousel.php:732
#: elementor/widgets/blog-posts-v2.php:262
#: elementor/widgets/blog-posts-v2.php:326
#: elementor/widgets/blog-posts-v2.php:414
#: elementor/widgets/blog-posts-v2.php:491 elementor/widgets/blog-posts.php:365
#: elementor/widgets/blog-posts.php:435 elementor/widgets/blog-posts.php:539
#: elementor/widgets/blog-posts.php:618 elementor/widgets/blog-posts.php:663
#: elementor/widgets/header-footer/login-modal.php:268
#: elementor/widgets/listings-tabs.php:298
#: elementor/widgets/login-modal.php:221
#: elementor/widgets/properties-recent-viewed.php:272
#: elementor/widgets/property-cards-v1.php:210
#: elementor/widgets/property-cards-v2.php:194
#: elementor/widgets/property-cards-v3.php:166
#: elementor/widgets/property-cards-v4.php:205
#: elementor/widgets/property-cards-v5.php:166
#: elementor/widgets/property-cards-v6.php:172
#: elementor/widgets/property-cards-v7.php:206
#: elementor/widgets/property-cards-v8.php:180
#: elementor/widgets/property-carousel-v1.php:185
#: elementor/widgets/property-carousel-v2.php:170
#: elementor/widgets/property-carousel-v3.php:142
#: elementor/widgets/property-carousel-v5.php:143
#: elementor/widgets/property-carousel-v6.php:144
#: elementor/widgets/property-carousel-v7.php:182
#: elementor/widgets/section-title.php:107
#: elementor/widgets/single-property/section-360-virtual.php:122
#: elementor/widgets/single-property/section-address.php:229
#: elementor/widgets/single-property/section-address.php:260
#: elementor/widgets/single-property/section-address.php:290
#: elementor/widgets/single-property/section-attachments.php:275
#: elementor/widgets/single-property/section-attachments.php:304
#: elementor/widgets/single-property/section-calculator.php:121
#: elementor/widgets/single-property/section-calendar.php:121
#: elementor/widgets/single-property/section-contact-bottom.php:112
#: elementor/widgets/single-property/section-description.php:157
#: elementor/widgets/single-property/section-description.php:186
#: elementor/widgets/single-property/section-description.php:244
#: elementor/widgets/single-property/section-details.php:459
#: elementor/widgets/single-property/section-details.php:490
#: elementor/widgets/single-property/section-details.php:520
#: elementor/widgets/single-property/section-energy.php:207
#: elementor/widgets/single-property/section-energy.php:236
#: elementor/widgets/single-property/section-energy.php:265
#: elementor/widgets/single-property/section-features.php:135
#: elementor/widgets/single-property/section-floorplan-v2.php:168
#: elementor/widgets/single-property/section-floorplan-v2.php:256
#: elementor/widgets/single-property/section-floorplan-v2.php:288
#: elementor/widgets/single-property/section-floorplan.php:176
#: elementor/widgets/single-property/section-floorplan.php:276
#: elementor/widgets/single-property/section-floorplan.php:308
#: elementor/widgets/single-property/section-nearby.php:129
#: elementor/widgets/single-property/section-overview-v2.php:302
#: elementor/widgets/single-property/section-overview-v2.php:331
#: elementor/widgets/single-property/section-overview-v2.php:360
#: elementor/widgets/single-property/section-overview.php:382
#: elementor/widgets/single-property/section-overview.php:411
#: elementor/widgets/single-property/section-overview.php:440
#: elementor/widgets/single-property/section-schedule-tour-v2.php:132
#: elementor/widgets/single-property/section-schedule-tour.php:122
#: elementor/widgets/single-property/section-sublistings.php:120
#: elementor/widgets/single-property/section-video.php:121
#: elementor/widgets/single-property/section-walkscore.php:129
#: elementor/widgets/taxonomies-list.php:158
#: elementor/widgets/team-member.php:252
msgid "Typography"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:986
#: elementor/widgets/contact-form.php:94
#: elementor/widgets/properties-recent-viewed.php:290
#: elementor/widgets/property-cards-v1.php:228
#: elementor/widgets/property-cards-v2.php:212
#: elementor/widgets/property-cards-v4.php:223
#: elementor/widgets/property-cards-v7.php:224
#: elementor/widgets/property-cards-v8.php:198
#: elementor/widgets/property-carousel-v1.php:203
#: elementor/widgets/property-carousel-v2.php:188
#: elementor/widgets/property-carousel-v7.php:200
#: elementor/widgets/single-agency/agency-contact.php:76
#: elementor/widgets/single-agency/agency-contact.php:307
#: elementor/widgets/single-agency/agency-meta.php:61
#: elementor/widgets/single-agency/agency-profile-v1.php:53
#: elementor/widgets/single-agency/agency-profile-v2.php:65
#: elementor/widgets/single-agent/agent-contact.php:76
#: elementor/widgets/single-agent/agent-contact.php:307
#: elementor/widgets/single-agent/agent-meta.php:63
#: elementor/widgets/single-property/property-address.php:44
#: elementor/widgets/single-property/property-title-area.php:47
#: elementor/widgets/single-property/section-address.php:140
#: elementor/widgets/single-property/section-toparea-v1.php:47
#: elementor/widgets/single-property/section-toparea-v2.php:46
#: elementor/widgets/single-property/section-toparea-v3.php:51
#: elementor/widgets/single-property/section-toparea-v5.php:46
#: elementor/widgets/single-property/section-toparea-v6.php:47
#: elementor/widgets/single-property/section-toparea-v7.php:46
msgid "Address"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:1016
#: elementor/widgets/property-cards-v1.php:237
#: elementor/widgets/property-cards-v2.php:221
#: elementor/widgets/property-cards-v4.php:232
#: elementor/widgets/property-cards-v8.php:207
#: elementor/widgets/property-carousel-v1.php:212
#: elementor/widgets/property-carousel-v2.php:197
msgid "Excerpt"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:1046
#: elementor/widgets/properties-recent-viewed.php:299
#: elementor/widgets/property-cards-v1.php:249
#: elementor/widgets/property-cards-v2.php:233
#: elementor/widgets/property-cards-v4.php:244
#: elementor/widgets/property-cards-v7.php:233
#: elementor/widgets/property-cards-v8.php:219
#: elementor/widgets/property-carousel-v1.php:224
#: elementor/widgets/property-carousel-v2.php:209
#: elementor/widgets/property-carousel-v7.php:209
#: elementor/widgets/single-property/section-energy.php:215
#: elementor/widgets/single-property/section-overview-v2.php:310
#: elementor/widgets/single-property/section-overview.php:390
#, fuzzy
#| msgid "Labels"
msgid "Meta Labels"
msgstr "Этикетки"

#: elementor/traits/Houzez_Property_Cards_Traits.php:1074
#: elementor/widgets/properties-recent-viewed.php:307
#: elementor/widgets/property-cards-v1.php:257
#: elementor/widgets/property-cards-v2.php:241
#: elementor/widgets/property-cards-v3.php:185
#: elementor/widgets/property-cards-v4.php:252
#: elementor/widgets/property-cards-v5.php:184
#: elementor/widgets/property-cards-v6.php:190
#: elementor/widgets/property-cards-v7.php:241
#: elementor/widgets/property-cards-v8.php:227
#: elementor/widgets/property-carousel-v1.php:232
#: elementor/widgets/property-carousel-v2.php:217
#: elementor/widgets/property-carousel-v3.php:161
#: elementor/widgets/property-carousel-v5.php:161
#: elementor/widgets/property-carousel-v6.php:162
#: elementor/widgets/property-carousel-v7.php:217
msgid "Meta Figure"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:1092
#: elementor/widgets/properties-recent-viewed.php:325
#: elementor/widgets/property-cards-v1.php:275
#: elementor/widgets/property-cards-v2.php:259
#: elementor/widgets/property-cards-v4.php:270
#: elementor/widgets/property-cards-v7.php:259
#: elementor/widgets/property-cards-v8.php:245
#: elementor/widgets/property-carousel-v1.php:250
#: elementor/widgets/property-carousel-v2.php:235
#: elementor/widgets/property-carousel-v7.php:235
#, fuzzy
#| msgid "Price"
msgid "Sub Price"
msgstr "Цена"

#: elementor/traits/Houzez_Property_Cards_Traits.php:1151
#: elementor/widgets/properties-recent-viewed.php:343
#: elementor/widgets/property-cards-v1.php:293
#: elementor/widgets/property-cards-v2.php:277
#: elementor/widgets/property-cards-v3.php:204
#: elementor/widgets/property-cards-v4.php:288
#: elementor/widgets/property-cards-v5.php:212
#: elementor/widgets/property-cards-v7.php:277
#: elementor/widgets/property-cards-v8.php:263
#: elementor/widgets/property-carousel-v1.php:268
#: elementor/widgets/property-carousel-v2.php:253
#: elementor/widgets/property-carousel-v3.php:180
#: elementor/widgets/property-carousel-v5.php:189
#: elementor/widgets/property-carousel-v7.php:253
msgid "Area Postfix"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:1181
#: elementor/widgets/properties-recent-viewed.php:352
#: elementor/widgets/property-cards-v1.php:302
#: elementor/widgets/property-cards-v4.php:297
#: elementor/widgets/property-carousel-v1.php:277
#: elementor/widgets/property-carousel-v7.php:262
msgid "Detail Button"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:1271
#: elementor/widgets/property-cards-v7.php:286
#: elementor/widgets/property-cards-v8.php:272
#: elementor/widgets/single-property/section-contact-2.php:354
#: elementor/widgets/single-property/section-schedule-tour-v2.php:171
#: elementor/widgets/testimonials-v3.php:110
msgid "Buttons"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:1308
#: elementor/widgets/properties-recent-viewed.php:145
#: elementor/widgets/property-cards-v1.php:156
#: elementor/widgets/property-cards-v2.php:152
#: elementor/widgets/property-cards-v3.php:151
#: elementor/widgets/property-cards-v4.php:151
#: elementor/widgets/property-cards-v5.php:151
#: elementor/widgets/property-cards-v6.php:157
#: elementor/widgets/property-cards-v7.php:155
#: elementor/widgets/property-cards-v8.php:138
#: elementor/widgets/property-carousel-v1.php:131
#: elementor/widgets/property-carousel-v2.php:128
#: elementor/widgets/property-carousel-v3.php:127
#: elementor/widgets/property-carousel-v5.php:128
#: elementor/widgets/property-carousel-v6.php:129
#: elementor/widgets/property-carousel-v7.php:131
msgid "Show/Hide Data"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:1316
msgid "Hide Description"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:1351
#: elementor/traits/Houzez_Property_Cards_Traits.php:2124
#: elementor/widgets/properties-recent-viewed.php:153
msgid "Hide Compare Button"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:1366
#: elementor/traits/Houzez_Property_Cards_Traits.php:2140
#: elementor/widgets/properties-recent-viewed.php:168
#, fuzzy
#| msgid "Favourite Count"
msgid "Hide Favorite Button"
msgstr "Любимый подсчет"

#: elementor/traits/Houzez_Property_Cards_Traits.php:1381
#: elementor/traits/Houzez_Property_Cards_Traits.php:2156
#: elementor/widgets/properties-recent-viewed.php:183
msgid "Hide Preview Button"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:1396
#: elementor/traits/Houzez_Property_Cards_Traits.php:2172
#: elementor/widgets/properties-recent-viewed.php:198
#, fuzzy
#| msgid "Featured Available"
msgid "Hide Featured Label"
msgstr "Избранные"

#: elementor/traits/Houzez_Property_Cards_Traits.php:1411
#: elementor/traits/Houzez_Property_Cards_Traits.php:2188
#: elementor/widgets/properties-recent-viewed.php:213
#, fuzzy
#| msgid "New Status"
msgid "Hide Status"
msgstr "Новый статус"

#: elementor/traits/Houzez_Property_Cards_Traits.php:1426
#: elementor/traits/Houzez_Property_Cards_Traits.php:2204
#: elementor/widgets/properties-recent-viewed.php:228
#, fuzzy
#| msgid "Labels"
msgid "Hide Labels"
msgstr "Этикетки"

#: elementor/traits/Houzez_Property_Cards_Traits.php:1441
#: elementor/widgets/properties-recent-viewed.php:243
#: elementor/widgets/property-cards-v1.php:181
#: elementor/widgets/property-cards-v4.php:176
#: elementor/widgets/property-carousel-v1.php:156
msgid "Hide Details Button"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:1473
#: elementor/widgets/properties-recent-viewed.php:255
#: elementor/widgets/property-cards-v1.php:193
#: elementor/widgets/property-cards-v2.php:177
#: elementor/widgets/property-cards-v4.php:188
#: elementor/widgets/property-carousel-v1.php:168
#: elementor/widgets/property-carousel-v2.php:153
#, fuzzy
#| msgid "Add New Agent"
msgid "Hide Date & Agent"
msgstr "Добавить новый агент"

#: elementor/traits/Houzez_Property_Cards_Traits.php:1514
#: elementor/widgets/header-footer/login-modal.php:298
#: elementor/widgets/login-modal.php:251
#: elementor/widgets/properties-recent-viewed.php:520
#: elementor/widgets/property-cards-v1.php:549
#: elementor/widgets/property-cards-v2.php:488
#: elementor/widgets/property-cards-v3.php:290
#: elementor/widgets/property-cards-v4.php:544
#: elementor/widgets/property-cards-v5.php:370
#: elementor/widgets/property-cards-v6.php:328
#: elementor/widgets/property-cards-v7.php:514
#: elementor/widgets/property-cards-v8.php:465
#: elementor/widgets/property-carousel-v1.php:524
#: elementor/widgets/property-carousel-v2.php:464
#: elementor/widgets/property-carousel-v3.php:266
#: elementor/widgets/property-carousel-v5.php:347
#: elementor/widgets/property-carousel-v6.php:300
#: elementor/widgets/property-carousel-v7.php:484
msgid "Colors"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:1702
#: elementor/widgets/property-cards-v1.php:559
#: elementor/widgets/property-cards-v2.php:498
#: elementor/widgets/property-cards-v4.php:554
#: elementor/widgets/property-cards-v8.php:511
#: elementor/widgets/property-carousel-v1.php:534
#: elementor/widgets/property-carousel-v2.php:474
msgid "Excerpt Color"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:1736
#: elementor/widgets/properties-recent-viewed.php:675
#: elementor/widgets/property-cards-v1.php:576
#: elementor/widgets/property-cards-v2.php:514
#: elementor/widgets/property-cards-v4.php:571
#: elementor/widgets/property-carousel-v1.php:551
#: elementor/widgets/property-carousel-v2.php:490
#, fuzzy
#| msgid "Agent Name"
msgid "Agent & Date"
msgstr "Имя агента"

#: elementor/traits/Houzez_Property_Cards_Traits.php:1908
#: elementor/widgets/property-cards-v8.php:650
msgid "360° Button Color"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:1942
#: elementor/widgets/property-cards-v8.php:663
msgid "360° Button Background Color"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:1975
#: elementor/widgets/property-cards-v8.php:675
msgid "Video Button Color"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:2008
#: elementor/widgets/property-cards-v8.php:687
msgid "Video Button Background Color"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:2049
#: elementor/traits/Houzez_Style_Traits.php:680
#: elementor/traits/Houzez_Style_Traits.php:954
#: elementor/traits/Houzez_Style_Traits.php:1135
#: elementor/widgets/agent-card.php:257 elementor/widgets/agent-card.php:712
#: elementor/widgets/agent-card.php:768
#: elementor/widgets/header-footer/menu.php:231
#: elementor/widgets/property-cards-v7.php:447
#: elementor/widgets/property-carousel-v7.php:417
#: elementor/widgets/search-builder.php:879
#: elementor/widgets/single-property/section-attachments.php:100
#: elementor/widgets/single-property/section-contact-2.php:522
#: elementor/widgets/single-property/section-contact-bottom.php:404
#: elementor/widgets/single-property/section-details.php:382
#: elementor/widgets/single-property/section-schedule-tour-v2.php:56
msgid "Background"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:2060
#: elementor/traits/Houzez_Style_Traits.php:689
#: elementor/traits/Houzez_Style_Traits.php:1097
#: elementor/traits/Houzez_Style_Traits.php:1144
#: elementor/widgets/blog-posts.php:243
#: elementor/widgets/single-property/section-attachments.php:221
#: elementor/widgets/single-property/section-contact-2.php:545
#: elementor/widgets/single-property/section-contact-bottom.php:442
#: elementor/widgets/single-property/section-schedule-tour-v2.php:86
msgid "Box Padding"
msgstr ""

#: elementor/traits/Houzez_Property_Cards_Traits.php:2081
#: elementor/traits/Houzez_Style_Traits.php:710
#: elementor/traits/Houzez_Style_Traits.php:1109
#: elementor/traits/Houzez_Style_Traits.php:1165
#: elementor/widgets/blog-posts.php:264
#: elementor/widgets/single-property/section-attachments.php:233
#: elementor/widgets/single-property/section-contact-2.php:566
#: elementor/widgets/single-property/section-contact-bottom.php:454
#: elementor/widgets/single-property/section-schedule-tour-v2.php:98
#: elementor/widgets/taxonomies-cards-carousel.php:352
#: elementor/widgets/taxonomies-cards.php:257
msgid "Box Radius"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:30
#: elementor/traits/Houzez_Style_Traits.php:483
#: elementor/widgets/contact-form.php:493
#: elementor/widgets/inquiry-form.php:478
#: elementor/widgets/search-builder.php:628
#: elementor/widgets/single-agency/agency-content.php:63
#: elementor/widgets/single-agency/agency-excerpt.php:63
#: elementor/widgets/single-agent/agent-content.php:63
#: elementor/widgets/single-agent/agent-excerpt.php:63
#: elementor/widgets/single-post/post-content.php:63
#: elementor/widgets/single-post/post-excerpt.php:63
#: elementor/widgets/single-property/property-content.php:65
#: elementor/widgets/single-property/property-excerpt.php:64
#: elementor/widgets/single-property/section-description.php:210
msgid "Justified"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:80
msgid "Blend Mode"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:84
msgid "Multiply"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:85
msgid "Screen"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:86
msgid "Overlay"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:87
msgid "Darken"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:88
msgid "Lighten"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:89
msgid "Color Dodge"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:90
#, fuzzy
#| msgid "Activation"
msgid "Saturation"
msgstr "активация"

#: elementor/traits/Houzez_Style_Traits.php:92
msgid "Difference"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:93
msgid "Exclusion"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:94
msgid "Hue"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:95
msgid "Luminosity"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:107
#, fuzzy
#| msgid "Permalinks"
msgid "Permalink"
msgstr "Permalinks"

#: elementor/traits/Houzez_Style_Traits.php:119
#: elementor/widgets/blog-posts-carousel.php:674
#: elementor/widgets/blog-posts-v2.php:478 elementor/widgets/blog-posts.php:605
msgid "Hover Color"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:141
#: elementor/widgets/banner-image.php:173
#: elementor/widgets/custom-carousel.php:458
#: elementor/widgets/custom-carousel.php:1190
#: elementor/widgets/grid-builder.php:376
#: elementor/widgets/properties-ajax-tabs.php:258
#: elementor/widgets/properties-ajax-tabs.php:280
#: elementor/widgets/single-post/author-box.php:221
#: elementor/widgets/single-property/featured-image.php:117
#: elementor/widgets/single-property/section-toparea-v3.php:77
#: elementor/widgets/single-property/section-toparea-v7.php:73
#: elementor/widgets/team-member.php:86
#: elementor/widgets/testimonials-v2.php:167
#: elementor/widgets/testimonials-v3.php:93
#: elementor/widgets/testimonials.php:107
msgid "Image"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:209
#: elementor/widgets/banner-image.php:216
#: elementor/widgets/custom-carousel.php:295
#: elementor/widgets/custom-carousel.php:501
#: elementor/widgets/header-footer/site-logo.php:208
#: elementor/widgets/single-property/featured-image.php:160
msgid "Max Width"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:266
#: elementor/widgets/banner-image.php:273
#: elementor/widgets/custom-carousel.php:558
msgid "Object Fit"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:273
#: elementor/widgets/banner-image.php:280
#: elementor/widgets/custom-carousel.php:565
msgid "Fill"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:274
#: elementor/widgets/banner-image.php:281
#: elementor/widgets/custom-carousel.php:566
#: extensions/meta-box/inc/fields/background.php:126
msgid "Cover"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:275
#: elementor/widgets/banner-image.php:282
#: elementor/widgets/custom-carousel.php:567
#: extensions/meta-box/inc/fields/background.php:127
msgid "Contain"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:276
#: elementor/widgets/custom-carousel.php:568
msgid "Scale Down"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:288
#: elementor/widgets/banner-image.php:294
#: elementor/widgets/custom-carousel.php:580
msgid "Object Position"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:291
#: elementor/widgets/banner-image.php:297
#: elementor/widgets/custom-carousel.php:583
#: extensions/meta-box/inc/fields/background.php:95
msgid "Center Center"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:292
#: elementor/widgets/banner-image.php:298
#: elementor/widgets/custom-carousel.php:584
#: extensions/meta-box/inc/fields/background.php:94
msgid "Center Left"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:293
#: elementor/widgets/banner-image.php:299
#: elementor/widgets/custom-carousel.php:585
#: extensions/meta-box/inc/fields/background.php:96
msgid "Center Right"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:294
#: elementor/widgets/banner-image.php:300
#: elementor/widgets/custom-carousel.php:586
#: extensions/meta-box/inc/fields/background.php:92
msgid "Top Center"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:295
#: elementor/widgets/banner-image.php:301
#: elementor/widgets/custom-carousel.php:587
#: extensions/meta-box/inc/fields/background.php:91
msgid "Top Left"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:296
#: elementor/widgets/banner-image.php:302
#: elementor/widgets/custom-carousel.php:588
#: extensions/meta-box/inc/fields/background.php:93
msgid "Top Right"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:297
#: elementor/widgets/banner-image.php:303
#: elementor/widgets/custom-carousel.php:589
#: extensions/meta-box/inc/fields/background.php:98
msgid "Bottom Center"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:298
#: elementor/widgets/banner-image.php:304
#: elementor/widgets/custom-carousel.php:590
#: extensions/meta-box/inc/fields/background.php:97
msgid "Bottom Left"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:299
#: elementor/widgets/banner-image.php:305
#: elementor/widgets/custom-carousel.php:591
#: extensions/meta-box/inc/fields/background.php:99
msgid "Bottom Right"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:455
#: elementor/widgets/header-footer/site-logo.php:463
#: elementor/widgets/header-footer/site-logo.php:536
#, fuzzy
#| msgid "Location"
msgid "Caption"
msgstr "Место нахождения"

#: elementor/traits/Houzez_Style_Traits.php:569
#, fuzzy
#| msgid "Payment Title"
msgid "Widget Title"
msgstr "Название платежа"

#: elementor/traits/Houzez_Style_Traits.php:601
msgid "Widget Box"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:815
#: elementor/widgets/blog-posts-carousel.php:330
#: elementor/widgets/single-property/property-title-area.php:106
#: elementor/widgets/single-property/section-contact-bottom.php:157
#: elementor/widgets/single-property/section-contact-bottom.php:542
#: elementor/widgets/single-property/section-schedule-tour.php:183
#: elementor/widgets/single-property/section-toparea-v6.php:91
msgid "Padding Bottom"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:853
#: elementor/widgets/single-agency/agency-profile-v1.php:178
#: elementor/widgets/single-agency/agency-profile-v2.php:112
#: elementor/widgets/single-agent/agent-profile-v1.php:190
#: elementor/widgets/single-agent/agent-profile-v2.php:124
msgid "Anchor Color"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:876
#: elementor/traits/Houzez_Style_Traits.php:1207
#: elementor/widgets/section-title.php:37
#: elementor/widgets/section-title.php:116
#: elementor/widgets/single-agency/agency-contact.php:274
#: elementor/widgets/single-agent/agent-contact.php:274
#: elementor/widgets/single-property/section-360-virtual.php:65
#: elementor/widgets/single-property/section-360-virtual.php:102
#: elementor/widgets/single-property/section-address.php:65
#: elementor/widgets/single-property/section-address.php:210
#: elementor/widgets/single-property/section-attachments.php:64
#: elementor/widgets/single-property/section-attachments.php:255
#: elementor/widgets/single-property/section-calculator.php:64
#: elementor/widgets/single-property/section-calculator.php:101
#: elementor/widgets/single-property/section-calendar.php:64
#: elementor/widgets/single-property/section-calendar.php:101
#: elementor/widgets/single-property/section-contact-bottom.php:64
#: elementor/widgets/single-property/section-description.php:64
#: elementor/widgets/single-property/section-description.php:137
#: elementor/widgets/single-property/section-details.php:65
#: elementor/widgets/single-property/section-details.php:440
#: elementor/widgets/single-property/section-energy.php:64
#: elementor/widgets/single-property/section-energy.php:187
#: elementor/widgets/single-property/section-features.php:64
#: elementor/widgets/single-property/section-features.php:115
#: elementor/widgets/single-property/section-floorplan-v2.php:64
#: elementor/widgets/single-property/section-floorplan-v2.php:145
#: elementor/widgets/single-property/section-floorplan.php:64
#: elementor/widgets/single-property/section-floorplan.php:156
#: elementor/widgets/single-property/section-nearby.php:64
#: elementor/widgets/single-property/section-nearby.php:109
#: elementor/widgets/single-property/section-overview-v2.php:161
#: elementor/widgets/single-property/section-overview-v2.php:282
#: elementor/widgets/single-property/section-overview.php:160
#: elementor/widgets/single-property/section-overview.php:362
#: elementor/widgets/single-property/section-schedule-tour.php:65
#: elementor/widgets/single-property/section-schedule-tour.php:94
#: elementor/widgets/single-property/section-schedule-tour.php:102
#: elementor/widgets/single-property/section-similar.php:65
#: elementor/widgets/single-property/section-sublistings.php:63
#: elementor/widgets/single-property/section-video.php:64
#: elementor/widgets/single-property/section-video.php:101
#: elementor/widgets/single-property/section-walkscore.php:64
#: elementor/widgets/single-property/section-walkscore.php:109
#, fuzzy
#| msgid "Payment Title"
msgid "Section Title"
msgstr "Название платежа"

#: elementor/traits/Houzez_Style_Traits.php:913
#, fuzzy
#| msgid "Available Listings"
msgid "View Listing"
msgstr "Доступные списки"

#: elementor/traits/Houzez_Style_Traits.php:979
#: elementor/traits/Houzez_Style_Traits.php:1332
#: elementor/widgets/property-cards-v7.php:458
#: elementor/widgets/property-carousel-v7.php:428
#: elementor/widgets/single-property/section-address.php:309
#: elementor/widgets/single-property/section-contact-bottom.php:120
#: elementor/widgets/single-property/section-details.php:539
#: elementor/widgets/single-property/section-overview.php:296
#: elementor/widgets/single-property/section-similar.php:206
#, fuzzy
#| msgid "New Type"
msgid "Border Type"
msgstr "Новый тип"

#: elementor/traits/Houzez_Style_Traits.php:1036
#: elementor/traits/Houzez_Style_Traits.php:1369
#: elementor/widgets/single-agency/agency-contact-form.php:87
#: elementor/widgets/single-agent/agent-contact-form.php:87
#: elementor/widgets/single-property/section-attachments.php:160
#: elementor/widgets/single-property/section-contact-2.php:151
#: elementor/widgets/single-property/section-similar.php:263
#: elementor/widgets/single-property/section-sublistings.php:173
msgid "Title Padding Bottom"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:1066
#: elementor/traits/Houzez_Style_Traits.php:1399
#: elementor/widgets/single-property/section-attachments.php:190
#: elementor/widgets/single-property/section-similar.php:293
#: elementor/widgets/single-property/section-sublistings.php:203
msgid "Title Margin Bottom"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:1126
msgid "Form Box"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:1189
#: elementor/traits/Houzez_Style_Traits.php:1243
#: elementor/traits/Houzez_Testimonials_Traits.php:66
#: elementor/widgets/advanced-search.php:79 elementor/widgets/agent-card.php:84
#: elementor/widgets/agents-grid.php:84 elementor/widgets/agents.php:84
#: elementor/widgets/agents.php:272 elementor/widgets/banner-image.php:98
#: elementor/widgets/blog-posts-carousel.php:82
#: elementor/widgets/blog-posts-v2.php:80 elementor/widgets/blog-posts.php:80
#: elementor/widgets/custom-carousel.php:739
#: elementor/widgets/custom-carousel.php:1179
#: elementor/widgets/grid-builder.php:80 elementor/widgets/grids.php:79
#: elementor/widgets/header-footer/area-switcher.php:114
#: elementor/widgets/header-footer/currency.php:114
#: elementor/widgets/header-footer/lang.php:114
#: elementor/widgets/header-footer/menu.php:142
#: elementor/widgets/icon-box.php:88 elementor/widgets/listings-tabs.php:85
#: elementor/widgets/page-title.php:80 elementor/widgets/page-title.php:102
#: elementor/widgets/partners.php:79 elementor/widgets/price-table.php:87
#: elementor/widgets/price-table.php:176
#: elementor/widgets/properties-grids.php:82
#: elementor/widgets/properties-recent-viewed.php:80
#: elementor/widgets/properties-slider.php:84
#: elementor/widgets/properties.php:85 elementor/widgets/property-by-id.php:80
#: elementor/widgets/property-by-ids.php:80
#: elementor/widgets/property-cards-v2.php:82
#: elementor/widgets/property-cards-v3.php:82
#: elementor/widgets/property-cards-v4.php:82
#: elementor/widgets/property-meta-data.php:179
#: elementor/widgets/section-title.php:79
#: elementor/widgets/single-agency/agency-about.php:45
#: elementor/widgets/single-agency/agency-about.php:88
#: elementor/widgets/single-agency/agency-agents.php:45
#: elementor/widgets/single-agency/agency-contact-form.php:47
#: elementor/widgets/single-agency/agency-contact.php:44
#: elementor/widgets/single-agency/agency-listings-review.php:52
#: elementor/widgets/single-agency/agency-listings.php:52
#: elementor/widgets/single-agency/agency-map.php:41
#: elementor/widgets/single-agency/agency-meta.php:208
#: elementor/widgets/single-agency/agency-name.php:72
#: elementor/widgets/single-agency/agency-profile-v1.php:45
#: elementor/widgets/single-agency/agency-profile-v1.php:146
#: elementor/widgets/single-agency/agency-profile-v2.php:45
#: elementor/widgets/single-agency/agency-profile-v2.php:80
#: elementor/widgets/single-agent/agent-about.php:45
#: elementor/widgets/single-agent/agent-about.php:88
#: elementor/widgets/single-agent/agent-contact-form.php:47
#: elementor/widgets/single-agent/agent-contact.php:44
#: elementor/widgets/single-agent/agent-listings-review.php:57
#: elementor/widgets/single-agent/agent-listings.php:57
#: elementor/widgets/single-agent/agent-map.php:41
#: elementor/widgets/single-agent/agent-meta.php:210
#: elementor/widgets/single-agent/agent-name.php:72
#: elementor/widgets/single-agent/agent-profile-v1.php:45
#: elementor/widgets/single-agent/agent-profile-v2.php:45
#: elementor/widgets/single-agent/agent-profile-v2.php:92
#: elementor/widgets/single-post/post-image.php:42
#: elementor/widgets/single-post/post-title.php:72
#: elementor/widgets/single-property/featured-image.php:42
#: elementor/widgets/single-property/images-gallery-v1.php:42
#: elementor/widgets/single-property/images-gallery-v2.php:48
#: elementor/widgets/single-property/images-gallery-v3.php:41
#: elementor/widgets/single-property/images-gallery-v4.php:41
#: elementor/widgets/single-property/images-gallery-v5.php:41
#: elementor/widgets/single-property/item-tools.php:44
#: elementor/widgets/single-property/item-tools.php:90
#: elementor/widgets/single-property/property-address.php:70
#: elementor/widgets/single-property/property-excerpt.php:40
#: elementor/widgets/single-property/property-price.php:44
#: elementor/widgets/single-property/property-title-area.php:75
#: elementor/widgets/single-property/property-title.php:72
#: elementor/widgets/single-property/section-360-virtual.php:47
#: elementor/widgets/single-property/section-address.php:47
#: elementor/widgets/single-property/section-attachments.php:46
#: elementor/widgets/single-property/section-block-gallery.php:42
#: elementor/widgets/single-property/section-calculator.php:46
#: elementor/widgets/single-property/section-calendar.php:46
#: elementor/widgets/single-property/section-contact-2.php:47
#: elementor/widgets/single-property/section-contact-bottom.php:46
#: elementor/widgets/single-property/section-description.php:46
#: elementor/widgets/single-property/section-details.php:47
#: elementor/widgets/single-property/section-energy.php:46
#: elementor/widgets/single-property/section-features.php:46
#: elementor/widgets/single-property/section-features.php:143
#: elementor/widgets/single-property/section-floorplan-v2.php:46
#: elementor/widgets/single-property/section-floorplan-v2.php:268
#: elementor/widgets/single-property/section-floorplan.php:46
#: elementor/widgets/single-property/section-google-map.php:42
#: elementor/widgets/single-property/section-map.php:42
#: elementor/widgets/single-property/section-nearby.php:46
#: elementor/widgets/single-property/section-open-street-map.php:42
#: elementor/widgets/single-property/section-overview-v2.php:143
#: elementor/widgets/single-property/section-overview.php:142
#: elementor/widgets/single-property/section-schedule-tour-v2.php:47
#: elementor/widgets/single-property/section-schedule-tour.php:47
#: elementor/widgets/single-property/section-similar.php:47
#: elementor/widgets/single-property/section-sublistings.php:45
#: elementor/widgets/single-property/section-toparea-v6.php:73
#: elementor/widgets/single-property/section-video.php:46
#: elementor/widgets/single-property/section-walkscore.php:46
#: elementor/widgets/sort-by.php:78 elementor/widgets/space.php:78
#: elementor/widgets/taxonomies-cards-carousel.php:79
#: elementor/widgets/taxonomies-cards.php:79
#: elementor/widgets/taxonomies-grids-carousel.php:79
#: elementor/widgets/taxonomies-grids-carousel.php:233
#: elementor/widgets/taxonomies-grids.php:82
#: elementor/widgets/taxonomies-grids.php:143
#: elementor/widgets/taxonomies-list.php:84
#: elementor/widgets/team-member.php:78 elementor/widgets/team-member.php:279
#: elementor/widgets/testimonials-v2.php:79
#: elementor/widgets/testimonials-v2.php:105
#: elementor/widgets/testimonials-v3.php:79
#: elementor/widgets/testimonials.php:79
msgid "Content"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:1197
#: elementor/widgets/single-property/section-360-virtual.php:55
#: elementor/widgets/single-property/section-address.php:55
#: elementor/widgets/single-property/section-attachments.php:54
#: elementor/widgets/single-property/section-calculator.php:54
#: elementor/widgets/single-property/section-calendar.php:54
#: elementor/widgets/single-property/section-contact-bottom.php:54
#: elementor/widgets/single-property/section-contact-bottom.php:89
#: elementor/widgets/single-property/section-description.php:54
#: elementor/widgets/single-property/section-details.php:55
#: elementor/widgets/single-property/section-energy.php:54
#: elementor/widgets/single-property/section-features.php:54
#: elementor/widgets/single-property/section-floorplan-v2.php:54
#: elementor/widgets/single-property/section-floorplan.php:54
#: elementor/widgets/single-property/section-nearby.php:54
#: elementor/widgets/single-property/section-overview-v2.php:151
#: elementor/widgets/single-property/section-overview.php:150
#: elementor/widgets/single-property/section-schedule-tour.php:55
#: elementor/widgets/single-property/section-similar.php:55
#: elementor/widgets/single-property/section-sublistings.php:53
#: elementor/widgets/single-property/section-video.php:54
#: elementor/widgets/single-property/section-walkscore.php:54
msgid "Section Header"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:1265
msgid "Review List"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:1301
#: elementor/widgets/single-property/section-contact-bottom.php:492
#: elementor/widgets/single-property/section-schedule-tour-v2.php:112
#: elementor/widgets/single-property/section-schedule-tour.php:156
#, fuzzy
#| msgid "Property Title"
msgid "Form Title"
msgstr "Заголовок собственности"

#: elementor/traits/Houzez_Style_Traits.php:1434
#: elementor/widgets/blog-posts-carousel.php:272
#: elementor/widgets/blog-posts-v2.php:167 elementor/widgets/blog-posts.php:208
#: elementor/widgets/header-footer/menu.php:629
#: elementor/widgets/single-agency/agency-content.php:39
#: elementor/widgets/single-agency/agency-excerpt.php:39
#: elementor/widgets/single-agency/agency-search.php:66
#: elementor/widgets/single-agency/agency-single-stats.php:81
#: elementor/widgets/single-agent/agent-content.php:39
#: elementor/widgets/single-agent/agent-excerpt.php:39
#: elementor/widgets/single-agent/agent-position.php:60
#: elementor/widgets/single-agent/agent-profile-v1.php:158
#: elementor/widgets/single-agent/agent-search.php:66
#: elementor/widgets/single-agent/agent-single-stats.php:81
#: elementor/widgets/single-post/post-content.php:39
#: elementor/widgets/single-post/post-excerpt.php:39
#: elementor/widgets/single-post/post-info.php:442
#: elementor/widgets/single-property/breadcrumb.php:43
#: elementor/widgets/single-property/featured-label.php:44
#: elementor/widgets/single-property/item-label.php:44
#: elementor/widgets/single-property/property-address.php:148
#: elementor/widgets/single-property/property-content.php:41
#: elementor/widgets/single-property/status-label.php:45
#: elementor/widgets/sort-by.php:98
#: elementor/widgets/taxonomies-cards-carousel.php:248
#: elementor/widgets/taxonomies-cards.php:153
#: elementor/widgets/taxonomies-list.php:245
msgid "Style"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:1452
#: elementor/widgets/single-property/breadcrumb.php:62
#: elementor/widgets/single-property/property-title-area.php:162
#: elementor/widgets/single-property/section-attachments.php:312
#: elementor/widgets/single-property/section-description.php:252
msgid "Link Color"
msgstr ""

#: elementor/traits/Houzez_Style_Traits.php:1463
#: elementor/widgets/agents-grid.php:371
#: elementor/widgets/single-property/property-title-area.php:173
msgid "Separator Color"
msgstr ""

#: elementor/traits/Houzez_Testimonials_Traits.php:16
#: elementor/widgets/partners.php:128
#: elementor/widgets/single-property/section-similar.php:185
msgid "Limit"
msgstr ""

#: elementor/traits/Houzez_Testimonials_Traits.php:18
#: elementor/widgets/partners.php:130
#, fuzzy
#| msgid "No Testimonial found in Trash"
msgid "Number of testimonials to show."
msgstr "Нет отзывов, найденных в корзине"

#: elementor/traits/Houzez_Testimonials_Traits.php:25
msgid "Offset posts"
msgstr ""

#: elementor/traits/Houzez_Testimonials_Traits.php:40
#: elementor/widgets/agents-grid.php:170 elementor/widgets/agents.php:168
#: elementor/widgets/partners.php:95
#: elementor/widgets/single-property/section-similar.php:176
#: functions/functions.php:257
msgid "Random"
msgstr ""

#: elementor/traits/Houzez_Testimonials_Traits.php:41
#: elementor/widgets/agents-grid.php:171 elementor/widgets/agents.php:169
#: elementor/widgets/partners.php:96
msgid "Menu Order"
msgstr ""

#: elementor/widgets/advanced-search.php:37
#, fuzzy
#| msgid "Saved Searches"
msgid "Advanced Search"
msgstr "Сохраненные поиски"

#: elementor/widgets/advanced-search.php:89
msgid "Enter search title"
msgstr ""

#: elementor/widgets/agent-card.php:37
#, fuzzy
#| msgid "Agent Name"
msgid "Agent Card"
msgstr "Имя агента"

#: elementor/widgets/agent-card.php:91 elementor/widgets/agents-grid.php:92
#: elementor/widgets/properties-ajax-tabs.php:592
#: elementor/widgets/properties.php:117
#: elementor/widgets/property-cards-v1.php:91
#: elementor/widgets/property-cards-v2.php:90
#: elementor/widgets/property-cards-v3.php:90
#: elementor/widgets/property-cards-v4.php:90
#: elementor/widgets/property-cards-v5.php:90
#: elementor/widgets/property-cards-v6.php:91
#: elementor/widgets/property-cards-v7.php:90
#: elementor/widgets/single-post/author-box.php:169
#: elementor/widgets/single-post/post-info.php:48
msgid "Layout"
msgstr ""

#: elementor/widgets/agent-card.php:95
msgid "Variation 1"
msgstr ""

#: elementor/widgets/agent-card.php:96
msgid "Variation 2"
msgstr ""

#: elementor/widgets/agent-card.php:97
msgid "Variation 3"
msgstr ""

#: elementor/widgets/agent-card.php:105
#, fuzzy
#| msgid "Agent Name"
msgid "Agent Source"
msgstr "Имя агента"

#: elementor/widgets/agent-card.php:109
msgid "Auto"
msgstr ""

#: elementor/widgets/agent-card.php:110
#, fuzzy
#| msgid "Search Agent"
msgid "Specific Agent"
msgstr "Агент поиска"

#: elementor/widgets/agent-card.php:112
msgid ""
"Select \"Auto\" to display the agent associated with the current property "
"(for property detail pages). Select \"Specific Agent\" to choose an agent."
msgstr ""

#: elementor/widgets/agent-card.php:119
#, fuzzy
#| msgid "Search Agent"
msgid "Select Agent"
msgstr "Агент поиска"

#: elementor/widgets/agent-card.php:133
#, fuzzy
#| msgid "Available Listings"
msgid "View Listings Text"
msgstr "Доступные списки"

#: elementor/widgets/agent-card.php:135 elementor/widgets/agent-card.php:136
msgid "View my listings"
msgstr ""

#: elementor/widgets/agent-card.php:137
msgid "Customize the \"View my listings\" link text"
msgstr ""

#: elementor/widgets/agent-card.php:157
msgid "Show Phone Number"
msgstr ""

#: elementor/widgets/agent-card.php:168
msgid "Show Mobile Number"
msgstr ""

#: elementor/widgets/agent-card.php:179
msgid "Click to Reveal"
msgstr ""

#: elementor/widgets/agent-card.php:184
msgid "Enable to hide phone numbers until clicked"
msgstr ""

#: elementor/widgets/agent-card.php:191 elementor/widgets/agent-card.php:641
#, fuzzy
#| msgid "Actions"
msgid "Action Buttons"
msgstr "действия"

#: elementor/widgets/agent-card.php:200
msgid "Show Call Button"
msgstr ""

#: elementor/widgets/agent-card.php:210
msgid "Show WhatsApp Button"
msgstr ""

#: elementor/widgets/agent-card.php:221
msgid "Show Telegram Button"
msgstr ""

#: elementor/widgets/agent-card.php:232
msgid "Show Line Button"
msgstr ""

#: elementor/widgets/agent-card.php:248
msgid "Card Styling"
msgstr ""

#: elementor/widgets/agent-card.php:335
#, fuzzy
#| msgid "Agent Name"
msgid "Agent Image"
msgstr "Имя агента"

#: elementor/widgets/agent-card.php:386
msgid "Image Border Radius"
msgstr ""

#: elementor/widgets/agent-card.php:392
#, no-php-format
msgid ""
"Override card border radius for the image. For Variation 2, use 50% for a "
"circle."
msgstr ""

#: elementor/widgets/agent-card.php:400
#, fuzzy
#| msgid "Package Holder"
msgid "Image Border"
msgstr "Держатель для упаковки"

#: elementor/widgets/agent-card.php:451
#: elementor/widgets/single-agency/agency-meta.php:417
#: elementor/widgets/single-agent/agent-meta.php:418
#: elementor/widgets/single-property/item-tools.php:134
#: elementor/widgets/single-property/property-title-area.php:581
#: elementor/widgets/single-property/section-toparea-v1.php:507
#: elementor/widgets/single-property/section-toparea-v2.php:506
#: elementor/widgets/single-property/section-toparea-v3.php:531
#: elementor/widgets/single-property/section-toparea-v5.php:506
#: elementor/widgets/single-property/section-toparea-v6.php:551
#: elementor/widgets/single-property/section-toparea-v7.php:526
msgid "Color Hover"
msgstr ""

#: elementor/widgets/agent-card.php:462
msgid "Bottom Spacing"
msgstr ""

#: elementor/widgets/agent-card.php:483
msgid "View Listing Text"
msgstr ""

#: elementor/widgets/agent-card.php:516
msgid "Contact Info Text"
msgstr ""

#: elementor/widgets/agent-card.php:543
#: elementor/widgets/property-cards-v8.php:610
msgid "Text Color Hover"
msgstr ""

#: elementor/widgets/agent-card.php:554
msgid "Item Bottom Spacing"
msgstr ""

#: elementor/widgets/agent-card.php:579
msgid "Contact Icons"
msgstr ""

#: elementor/widgets/agent-card.php:587 elementor/widgets/agent-card.php:700
#: elementor/widgets/agent-card.php:756
#: elementor/widgets/header-footer/login-modal.php:312
#: elementor/widgets/header-footer/login-modal.php:379
#: elementor/widgets/icon-box.php:324 elementor/widgets/login-modal.php:259
#: elementor/widgets/single-property/section-floorplan.php:212
msgid "Icon Color"
msgstr ""

#: elementor/widgets/agent-card.php:598 elementor/widgets/icon-box.php:336
#: elementor/widgets/single-property/section-floorplan.php:223
#, fuzzy
#| msgid "Size"
msgid "Icon Size"
msgstr "Размер"

#: elementor/widgets/agent-card.php:616
msgid "Icon Right Spacing"
msgstr ""

#: elementor/widgets/agent-card.php:649
msgid "Buttons Top Spacing"
msgstr ""

#: elementor/widgets/agent-card.php:667
msgid "Button Icon Size"
msgstr ""

#: elementor/widgets/agent-card.php:679
msgid "Button Padding"
msgstr ""

#: elementor/widgets/agent-card.php:814
#, php-format
msgid "Profile photo of agent %s"
msgstr ""

#: elementor/widgets/agent-card.php:907
#: elementor/widgets/single-agency/agency-meta.php:63
#: elementor/widgets/single-agent/agent-meta.php:65
msgid "WhatsApp"
msgstr ""

#: elementor/widgets/agent-card.php:918
#: elementor/widgets/single-agency/agency-meta.php:65
#: elementor/widgets/single-agent/agent-meta.php:67
msgid "Telegram"
msgstr ""

#: elementor/widgets/agent-card.php:929
msgid "Line"
msgstr ""

#: elementor/widgets/agent-card.php:954
msgid "Contact us"
msgstr ""

#: elementor/widgets/agent-card.php:961
msgid "Please quote property reference"
msgstr ""

#: elementor/widgets/agent-card.php:1136
msgid ""
"Please select an agent or ensure the widget is on a property page with an "
"assigned agent."
msgstr ""

#: elementor/widgets/agents-grid.php:36
#, fuzzy
#| msgid "Agents"
msgid "Agents Grid"
msgstr "Агенты"

#: elementor/widgets/agents-grid.php:95 elementor/widgets/properties.php:106
msgid "Version 1"
msgstr ""

#: elementor/widgets/agents-grid.php:96 elementor/widgets/properties.php:107
msgid "Version 2"
msgstr ""

#: elementor/widgets/agents-grid.php:106 elementor/widgets/agents.php:106
#: elementor/widgets/properties-recent-viewed.php:106
#: elementor/widgets/property-by-ids.php:143
#: elementor/widgets/single-agency/agency-listings-review.php:101
#: elementor/widgets/single-agency/agency-listings.php:101
#: elementor/widgets/single-agent/agent-listings-review.php:106
#: elementor/widgets/single-agent/agent-listings.php:106
#: elementor/widgets/single-property/section-overview.php:243
#: elementor/widgets/single-property/section-similar.php:139
#: elementor/widgets/taxonomies-cards.php:111
#: elementor/widgets/taxonomies-grids.php:90
msgid "Columns"
msgstr ""

#: elementor/widgets/agents-grid.php:109 elementor/widgets/agents.php:109
#: elementor/widgets/properties-ajax-tabs.php:596
msgid "4 Columns"
msgstr ""

#: elementor/widgets/agents-grid.php:110 elementor/widgets/agents.php:110
#: elementor/widgets/properties-ajax-tabs.php:595
#: elementor/widgets/properties-recent-viewed.php:109
#: elementor/widgets/property-by-ids.php:146
#: elementor/widgets/single-agency/agency-listings-review.php:123
#: elementor/widgets/single-agency/agency-listings.php:123
#: elementor/widgets/single-agent/agent-listings-review.php:128
#: elementor/widgets/single-agent/agent-listings.php:128
#: elementor/widgets/single-property/section-address.php:84
#: elementor/widgets/single-property/section-details.php:85
#: elementor/widgets/single-property/section-details.php:179
#: elementor/widgets/single-property/section-features.php:83
msgid "3 Columns"
msgstr ""

#: elementor/widgets/agents-grid.php:144 elementor/widgets/agents.php:142
#, fuzzy
#| msgid "New Agent"
msgid "Number of Agents"
msgstr "Новый агент"

#: elementor/widgets/agents-grid.php:197
#: elementor/widgets/blog-posts-carousel.php:137
#: elementor/widgets/blog-posts-v2.php:133 elementor/widgets/blog-posts.php:163
msgid "Show/Hide"
msgstr ""

#: elementor/widgets/agents-grid.php:205
msgid "Hide Agent Position"
msgstr ""

#: elementor/widgets/agents-grid.php:220
#, fuzzy
#| msgid "Views Count"
msgid "Hide Listings Count"
msgstr "Количество просмотров"

#: elementor/widgets/agents-grid.php:235
msgid "Hide Languages"
msgstr ""

#: elementor/widgets/agents-grid.php:250
msgid "Hide Button"
msgstr ""

#: elementor/widgets/agents-grid.php:265
msgid "Hide Empty Bottom"
msgstr ""

#: elementor/widgets/agents-grid.php:307 elementor/widgets/agents.php:195
#: elementor/widgets/single-agency/agency-contact-form.php:137
#: elementor/widgets/single-agency/agency-contact.php:162
#: elementor/widgets/single-agent/agent-contact-form.php:137
#: elementor/widgets/single-agent/agent-contact.php:162
#: elementor/widgets/single-property/section-contact-2.php:513
#: elementor/widgets/single-property/section-contact-bottom.php:395
#: elementor/widgets/team-member.php:221
msgid "Box"
msgstr ""

#: elementor/widgets/agents-grid.php:355
msgid "Hide Separator"
msgstr ""

#: elementor/widgets/agents-grid.php:422
#, fuzzy
#| msgid "Views Count"
msgid "Listings Count"
msgstr "Количество просмотров"

#: elementor/widgets/agents-grid.php:431
#: elementor/widgets/single-agency/agency-about.php:74
#: elementor/widgets/single-agent/agent-about.php:74
msgid "Languages"
msgstr ""

#: elementor/widgets/agents.php:92
#, fuzzy
#| msgid "New Type"
msgid "Module Type"
msgstr "Новый тип"

#: elementor/widgets/agents.php:95
msgid "Grid"
msgstr ""

#: elementor/widgets/agents.php:96
msgid "Carousel"
msgstr ""

#: elementor/widgets/banner-image.php:42
msgid "Image Banner"
msgstr ""

#: elementor/widgets/banner-image.php:106
#: elementor/widgets/grid-builder.php:102
#: elementor/widgets/grid-builder.php:405
#: elementor/widgets/header-footer/site-logo.php:413
msgid "Choose Image"
msgstr ""

#: elementor/widgets/banner-image.php:143
#: elementor/widgets/banner-image.php:156
#: elementor/widgets/create-listing-btn.php:206
#: elementor/widgets/grid-builder.php:150
#: elementor/widgets/header-footer/create-listing-btn.php:238
#: elementor/widgets/header-footer/site-logo.php:494
#: elementor/widgets/header-footer/site-logo.php:511
#: elementor/widgets/single-post/author-box.php:118
#: elementor/widgets/single-post/post-info.php:269
msgid "Link"
msgstr ""

#: elementor/widgets/banner-image.php:148
#: elementor/widgets/header-footer/site-logo.php:500
#: elementor/widgets/single-post/post-info.php:281
msgid "Custom URL"
msgstr ""

#: elementor/widgets/blog-posts-carousel.php:36
msgid "Blog Posts Carousel"
msgstr ""

#: elementor/widgets/blog-posts-carousel.php:90
#: elementor/widgets/blog-posts.php:98
msgid "Grid Version"
msgstr ""

#: elementor/widgets/blog-posts-carousel.php:116
#: elementor/widgets/blog-posts.php:140
msgid "Number of posts to show"
msgstr ""

#: elementor/widgets/blog-posts-carousel.php:144
#: elementor/widgets/blog-posts.php:170
#, fuzzy
#| msgid "Author"
msgid "Post Author"
msgstr "автор"

#: elementor/widgets/blog-posts-carousel.php:146
#: elementor/widgets/blog-posts-carousel.php:157
#: elementor/widgets/blog-posts-carousel.php:169
#: elementor/widgets/blog-posts-v2.php:142
#: elementor/widgets/blog-posts-v2.php:154 elementor/widgets/blog-posts.php:172
#: elementor/widgets/blog-posts.php:183 elementor/widgets/blog-posts.php:195
#: elementor/widgets/contact-form.php:316
#: elementor/widgets/contact-form.php:329
#: elementor/widgets/contact-form.php:343
#: elementor/widgets/contact-form.php:356
#: elementor/widgets/inquiry-form.php:303
#: elementor/widgets/inquiry-form.php:316
#: elementor/widgets/inquiry-form.php:330
#: elementor/widgets/inquiry-form.php:343
#: elementor/widgets/listings-tabs.php:157 elementor/widgets/page-title.php:90
#: elementor/widgets/search-builder.php:602
#: elementor/widgets/search-builder.php:642
#: elementor/widgets/search-builder.php:672
#: elementor/widgets/search-builder.php:753
#: elementor/widgets/single-agency/agency-about.php:64
#: elementor/widgets/single-agency/agency-about.php:76
#: elementor/widgets/single-agency/agency-contact.php:54
#: elementor/widgets/single-agency/agency-contact.php:66
#: elementor/widgets/single-agency/agency-contact.php:78
#: elementor/widgets/single-agency/agency-contact.php:90
#: elementor/widgets/single-agency/agency-contact.php:102
#: elementor/widgets/single-agency/agency-contact.php:114
#: elementor/widgets/single-agency/agency-contact.php:126
#: elementor/widgets/single-agency/agency-contact.php:138
#: elementor/widgets/single-agency/agency-contact.php:150
#: elementor/widgets/single-agency/agency-listings-review.php:165
#: elementor/widgets/single-agency/agency-listings-review.php:245
#: elementor/widgets/single-agency/agency-listings.php:165
#: elementor/widgets/single-agency/agency-listings.php:245
#: elementor/widgets/single-agency/agency-profile-v1.php:55
#: elementor/widgets/single-agency/agency-profile-v1.php:67
#: elementor/widgets/single-agency/agency-profile-v1.php:79
#: elementor/widgets/single-agency/agency-profile-v1.php:91
#: elementor/widgets/single-agency/agency-profile-v1.php:103
#: elementor/widgets/single-agency/agency-profile-v2.php:67
#: elementor/widgets/single-agency/agency-rating.php:51
#: elementor/widgets/single-agency/agency-rating.php:63
#: elementor/widgets/single-agency/agency-rating.php:75
#: elementor/widgets/single-agent/agent-about.php:64
#: elementor/widgets/single-agent/agent-about.php:76
#: elementor/widgets/single-agent/agent-contact.php:54
#: elementor/widgets/single-agent/agent-contact.php:66
#: elementor/widgets/single-agent/agent-contact.php:78
#: elementor/widgets/single-agent/agent-contact.php:90
#: elementor/widgets/single-agent/agent-contact.php:102
#: elementor/widgets/single-agent/agent-contact.php:114
#: elementor/widgets/single-agent/agent-contact.php:126
#: elementor/widgets/single-agent/agent-contact.php:138
#: elementor/widgets/single-agent/agent-contact.php:150
#: elementor/widgets/single-agent/agent-listings-review.php:170
#: elementor/widgets/single-agent/agent-listings-review.php:250
#: elementor/widgets/single-agent/agent-listings.php:170
#: elementor/widgets/single-agent/agent-listings.php:250
#: elementor/widgets/single-agent/agent-profile-v1.php:55
#: elementor/widgets/single-agent/agent-profile-v1.php:67
#: elementor/widgets/single-agent/agent-profile-v1.php:79
#: elementor/widgets/single-agent/agent-profile-v1.php:91
#: elementor/widgets/single-agent/agent-profile-v1.php:103
#: elementor/widgets/single-agent/agent-profile-v1.php:115
#: elementor/widgets/single-agent/agent-profile-v2.php:67
#: elementor/widgets/single-agent/agent-profile-v2.php:79
#: elementor/widgets/single-agent/agent-rating.php:51
#: elementor/widgets/single-agent/agent-rating.php:63
#: elementor/widgets/single-agent/agent-rating.php:75
#: elementor/widgets/single-post/author-box.php:62
#: elementor/widgets/single-post/author-box.php:88
#: elementor/widgets/single-post/author-box.php:135
#: elementor/widgets/single-post/author-box.php:149
#: elementor/widgets/single-post/post-navigation.php:51
#: elementor/widgets/single-post/post-navigation.php:98
msgid "Show"
msgstr ""

#: elementor/widgets/blog-posts-carousel.php:147
#: elementor/widgets/blog-posts-carousel.php:158
#: elementor/widgets/blog-posts-carousel.php:170
#: elementor/widgets/blog-posts-v2.php:143
#: elementor/widgets/blog-posts-v2.php:155 elementor/widgets/blog-posts.php:173
#: elementor/widgets/blog-posts.php:184 elementor/widgets/blog-posts.php:196
#: elementor/widgets/contact-form.php:317
#: elementor/widgets/contact-form.php:330
#: elementor/widgets/contact-form.php:344
#: elementor/widgets/contact-form.php:357
#: elementor/widgets/inquiry-form.php:304
#: elementor/widgets/inquiry-form.php:317
#: elementor/widgets/inquiry-form.php:331
#: elementor/widgets/inquiry-form.php:344
#: elementor/widgets/listings-tabs.php:158 elementor/widgets/page-title.php:91
#: elementor/widgets/search-builder.php:603
#: elementor/widgets/search-builder.php:643
#: elementor/widgets/search-builder.php:673
#: elementor/widgets/search-builder.php:754
#: elementor/widgets/single-agency/agency-about.php:65
#: elementor/widgets/single-agency/agency-about.php:77
#: elementor/widgets/single-agency/agency-contact.php:55
#: elementor/widgets/single-agency/agency-contact.php:67
#: elementor/widgets/single-agency/agency-contact.php:79
#: elementor/widgets/single-agency/agency-contact.php:91
#: elementor/widgets/single-agency/agency-contact.php:103
#: elementor/widgets/single-agency/agency-contact.php:115
#: elementor/widgets/single-agency/agency-contact.php:127
#: elementor/widgets/single-agency/agency-contact.php:139
#: elementor/widgets/single-agency/agency-contact.php:151
#: elementor/widgets/single-agency/agency-listings-review.php:166
#: elementor/widgets/single-agency/agency-listings-review.php:246
#: elementor/widgets/single-agency/agency-listings.php:166
#: elementor/widgets/single-agency/agency-listings.php:246
#: elementor/widgets/single-agency/agency-profile-v1.php:56
#: elementor/widgets/single-agency/agency-profile-v1.php:68
#: elementor/widgets/single-agency/agency-profile-v1.php:80
#: elementor/widgets/single-agency/agency-profile-v1.php:92
#: elementor/widgets/single-agency/agency-profile-v1.php:104
#: elementor/widgets/single-agency/agency-profile-v2.php:68
#: elementor/widgets/single-agency/agency-rating.php:52
#: elementor/widgets/single-agency/agency-rating.php:64
#: elementor/widgets/single-agency/agency-rating.php:76
#: elementor/widgets/single-agent/agent-about.php:65
#: elementor/widgets/single-agent/agent-about.php:77
#: elementor/widgets/single-agent/agent-contact.php:55
#: elementor/widgets/single-agent/agent-contact.php:67
#: elementor/widgets/single-agent/agent-contact.php:79
#: elementor/widgets/single-agent/agent-contact.php:91
#: elementor/widgets/single-agent/agent-contact.php:103
#: elementor/widgets/single-agent/agent-contact.php:115
#: elementor/widgets/single-agent/agent-contact.php:127
#: elementor/widgets/single-agent/agent-contact.php:139
#: elementor/widgets/single-agent/agent-contact.php:151
#: elementor/widgets/single-agent/agent-listings-review.php:171
#: elementor/widgets/single-agent/agent-listings-review.php:251
#: elementor/widgets/single-agent/agent-listings.php:171
#: elementor/widgets/single-agent/agent-listings.php:251
#: elementor/widgets/single-agent/agent-profile-v1.php:56
#: elementor/widgets/single-agent/agent-profile-v1.php:68
#: elementor/widgets/single-agent/agent-profile-v1.php:80
#: elementor/widgets/single-agent/agent-profile-v1.php:92
#: elementor/widgets/single-agent/agent-profile-v1.php:104
#: elementor/widgets/single-agent/agent-profile-v1.php:116
#: elementor/widgets/single-agent/agent-profile-v2.php:68
#: elementor/widgets/single-agent/agent-profile-v2.php:80
#: elementor/widgets/single-agent/agent-rating.php:52
#: elementor/widgets/single-agent/agent-rating.php:64
#: elementor/widgets/single-agent/agent-rating.php:76
#: elementor/widgets/single-post/author-box.php:63
#: elementor/widgets/single-post/author-box.php:89
#: elementor/widgets/single-post/author-box.php:136
#: elementor/widgets/single-post/author-box.php:150
#: elementor/widgets/single-post/post-navigation.php:52
#: elementor/widgets/single-post/post-navigation.php:99
msgid "Hide"
msgstr ""

#: elementor/widgets/blog-posts-carousel.php:155
#: elementor/widgets/blog-posts-v2.php:140 elementor/widgets/blog-posts.php:181
#, fuzzy
#| msgid "Posted"
msgid "Post Date"
msgstr "Сообщение"

#: elementor/widgets/blog-posts-carousel.php:167
#: elementor/widgets/blog-posts-v2.php:152 elementor/widgets/blog-posts.php:193
#, fuzzy
#| msgid "Category"
msgid "Post Category"
msgstr "категория"

#: elementor/widgets/blog-posts-carousel.php:182
#: elementor/widgets/custom-carousel.php:152
#: elementor/widgets/property-carousel-v1.php:82
#: elementor/widgets/property-carousel-v2.php:82
#: elementor/widgets/property-carousel-v3.php:81
#: elementor/widgets/property-carousel-v5.php:82
#: elementor/widgets/property-carousel-v6.php:83
#: elementor/widgets/property-carousel-v7.php:82
#: elementor/widgets/taxonomies-cards-carousel.php:136
#: elementor/widgets/taxonomies-grids-carousel.php:121
#, fuzzy
#| msgid "Update Settings"
msgid "Carousel Settings"
msgstr "Обновить настройки"

#: elementor/widgets/blog-posts-carousel.php:280
#: elementor/widgets/blog-posts-v2.php:175 elementor/widgets/blog-posts.php:216
#, fuzzy
#| msgid "Posts"
msgid "Post Box"
msgstr "Сообщений"

#: elementor/widgets/blog-posts-carousel.php:307
#: elementor/widgets/single-property/property-title-area.php:83
msgid "Padding Top"
msgstr ""

#: elementor/widgets/blog-posts-carousel.php:353
#: elementor/widgets/blog-posts-carousel.php:459
#: elementor/widgets/blog-posts-carousel.php:535
#: elementor/widgets/blog-posts-carousel.php:636
#: elementor/widgets/blog-posts-v2.php:196
#: elementor/widgets/blog-posts-v2.php:287
#: elementor/widgets/blog-posts-v2.php:351
#: elementor/widgets/blog-posts-v2.php:439 elementor/widgets/blog-posts.php:284
#: elementor/widgets/blog-posts.php:390 elementor/widgets/blog-posts.php:466
#: elementor/widgets/blog-posts.php:567
#: elementor/widgets/search-builder.php:1083
#: elementor/widgets/single-agency/agency-listings-review.php:296
#: elementor/widgets/single-agency/agency-profile-v1.php:250
#: elementor/widgets/single-agency/agency-profile-v2.php:184
#: elementor/widgets/single-agent/agent-listings-review.php:301
#: elementor/widgets/single-agent/agent-profile-v1.php:262
#: elementor/widgets/single-agent/agent-profile-v2.php:196
#: elementor/widgets/single-property/property-price.php:135
#: elementor/widgets/single-property/property-price.php:210
#: elementor/widgets/single-property/property-title-area.php:267
#: elementor/widgets/single-property/property-title-area.php:449
#: elementor/widgets/single-property/section-contact-bottom.php:187
#: elementor/widgets/single-property/section-toparea-v1.php:169
#: elementor/widgets/single-property/section-toparea-v1.php:351
#: elementor/widgets/single-property/section-toparea-v2.php:169
#: elementor/widgets/single-property/section-toparea-v2.php:350
#: elementor/widgets/single-property/section-toparea-v3.php:194
#: elementor/widgets/single-property/section-toparea-v3.php:375
#: elementor/widgets/single-property/section-toparea-v5.php:169
#: elementor/widgets/single-property/section-toparea-v5.php:350
#: elementor/widgets/single-property/section-toparea-v6.php:214
#: elementor/widgets/single-property/section-toparea-v6.php:395
#: elementor/widgets/single-property/section-toparea-v7.php:189
#: elementor/widgets/single-property/section-toparea-v7.php:370
msgid "Margin Bottom"
msgstr ""

#: elementor/widgets/blog-posts-carousel.php:377
#: elementor/widgets/blog-posts-v2.php:214 elementor/widgets/blog-posts.php:308
msgid "Image Style"
msgstr ""

#: elementor/widgets/blog-posts-carousel.php:389
#: elementor/widgets/blog-posts.php:320
msgid "Margin left & right"
msgstr ""

#: elementor/widgets/blog-posts-carousel.php:412
#: elementor/widgets/blog-posts-v2.php:240 elementor/widgets/blog-posts.php:343
#: elementor/widgets/single-post/post-navigation.php:96
#: elementor/widgets/single-post/post-title.php:17
#: elementor/widgets/single-post/post-title.php:42
#, fuzzy
#| msgid "Property Title"
msgid "Post Title"
msgstr "Заголовок собственности"

#: elementor/widgets/blog-posts-carousel.php:476
#: elementor/widgets/blog-posts-v2.php:304 elementor/widgets/blog-posts.php:407
#: elementor/widgets/single-post/post-content.php:16
msgid "Post Content"
msgstr ""

#: elementor/widgets/blog-posts-carousel.php:555
#: elementor/widgets/blog-posts-v2.php:368 elementor/widgets/blog-posts.php:486
msgid "Content Padding"
msgstr ""

#: elementor/widgets/blog-posts-carousel.php:570
#: elementor/widgets/blog-posts-v2.php:380 elementor/widgets/blog-posts.php:501
#, fuzzy
#| msgid "Posted"
msgid "Post Meta"
msgstr "Сообщение"

#: elementor/widgets/blog-posts-carousel.php:592
#: elementor/widgets/blog-posts-v2.php:401 elementor/widgets/blog-posts.php:523
#, fuzzy
#| msgid "Category"
msgid "Category Color"
msgstr "категория"

#: elementor/widgets/blog-posts-carousel.php:653
#: elementor/widgets/blog-posts.php:584
msgid "Continue Link"
msgstr ""

#: elementor/widgets/blog-posts-carousel.php:695
#: elementor/widgets/blog-posts.php:626
msgid "Footer"
msgstr ""

#: elementor/widgets/blog-posts-carousel.php:719
#: elementor/widgets/blog-posts.php:650
msgid "Author Text Color"
msgstr ""

#: elementor/widgets/blog-posts-v2.php:34
msgid "Blog Posts Grid v2"
msgstr ""

#: elementor/widgets/blog-posts-v2.php:457
msgid "Continue Reading"
msgstr ""

#: elementor/widgets/blog-posts.php:34
msgid "Blog Posts Grid"
msgstr ""

#: elementor/widgets/blog-posts.php:124
msgid "Posts in Row"
msgstr ""

#: elementor/widgets/contact-form.php:43
msgid "Contact Form"
msgstr ""

#: elementor/widgets/contact-form.php:85 elementor/widgets/inquiry-form.php:103
#, fuzzy
#| msgid "Name"
msgid "Full Name"
msgstr "имя"

#: elementor/widgets/contact-form.php:86 elementor/widgets/inquiry-form.php:104
#, fuzzy
#| msgid "Agent Name"
msgid "First Name"
msgstr "Имя агента"

#: elementor/widgets/contact-form.php:87 elementor/widgets/inquiry-form.php:105
#, fuzzy
#| msgid "Agent Name"
msgid "Last Name"
msgstr "Имя агента"

#: elementor/widgets/contact-form.php:91
#, fuzzy
#| msgid "Phone"
msgid "Home Phone"
msgstr "Телефон"

#: elementor/widgets/contact-form.php:92
#, fuzzy
#| msgid "Phone"
msgid "Work Phone"
msgstr "Телефон"

#: elementor/widgets/contact-form.php:93 elementor/widgets/inquiry-form.php:108
#, fuzzy
#| msgid "New Type"
msgid "User Type"
msgstr "Новый тип"

#: elementor/widgets/contact-form.php:98 elementor/widgets/inquiry-form.php:101
#: elementor/widgets/single-property/section-address.php:184
msgid "Zip/Postal Code"
msgstr ""

#: elementor/widgets/contact-form.php:130
#: elementor/widgets/inquiry-form.php:141
#: elementor/widgets/search-builder.php:152
#: templates/fields-builder/fields-form.php:33
#, fuzzy
#| msgid "Package Holder"
msgid "Placeholder"
msgstr "Держатель для упаковки"

#: elementor/widgets/contact-form.php:149
#: elementor/widgets/contact-form.php:381
#: elementor/widgets/inquiry-form.php:159
#: elementor/widgets/inquiry-form.php:366
msgid "Required"
msgstr ""

#: elementor/widgets/contact-form.php:169
#: elementor/widgets/contact-form.php:394
#: elementor/widgets/inquiry-form.php:179
#: elementor/widgets/inquiry-form.php:379
msgid "Validation Message"
msgstr ""

#: elementor/widgets/contact-form.php:181
#: elementor/widgets/inquiry-form.php:191
#: templates/fields-builder/fields-form.php:48
#, fuzzy
#| msgid "Actions"
msgid "Options"
msgstr "действия"

#: elementor/widgets/contact-form.php:184
#: elementor/widgets/inquiry-form.php:194
msgid ""
"Enter each option in a separate line. To differentiate between label and "
"value, separate them with a pipe char (\"|\"). For example: First Name|f_name"
msgstr ""

#: elementor/widgets/contact-form.php:203
#: elementor/widgets/contact-form.php:455
#: elementor/widgets/inquiry-form.php:213
#: elementor/widgets/inquiry-form.php:440
#: elementor/widgets/search-builder.php:232
msgid "Column Width"
msgstr ""

#: elementor/widgets/contact-form.php:225
#: elementor/widgets/inquiry-form.php:235
msgid "Rows"
msgstr ""

#: elementor/widgets/contact-form.php:247
#: elementor/widgets/inquiry-form.php:257
#: elementor/widgets/search-builder.php:457
msgid "Form Fields"
msgstr ""

#: elementor/widgets/contact-form.php:254
#: elementor/widgets/contact-form.php:257
#: elementor/widgets/inquiry-form.php:264
#: elementor/widgets/inquiry-form.php:267
#, fuzzy
#| msgid "Name"
msgid "Form Name"
msgstr "имя"

#: elementor/widgets/contact-form.php:256
#: elementor/widgets/inquiry-form.php:266
#, fuzzy
#| msgid "New Feature"
msgid "New Form"
msgstr "Новая функция"

#: elementor/widgets/contact-form.php:297
#: elementor/widgets/inquiry-form.php:284
#: elementor/widgets/search-builder.php:566
msgid "Input Size"
msgstr ""

#: elementor/widgets/contact-form.php:327
#: elementor/widgets/inquiry-form.php:314
msgid "Required Mark"
msgstr ""

#: elementor/widgets/contact-form.php:341
#: elementor/widgets/inquiry-form.php:328
msgid "Google reCaptcha"
msgstr ""

#: elementor/widgets/contact-form.php:346
#: elementor/widgets/inquiry-form.php:333
msgid ""
"Please make sure you have enabled google reCaptcha in Theme Options -> "
"Google reCaptcha and have add reCaptcha API keys"
msgstr ""

#: elementor/widgets/contact-form.php:354
#: elementor/widgets/inquiry-form.php:341
msgid "GDPR Agreement"
msgstr ""

#: elementor/widgets/contact-form.php:367
#: elementor/widgets/inquiry-form.php:352
msgid "Hide checkbox"
msgstr ""

#: elementor/widgets/contact-form.php:368
#: elementor/widgets/inquiry-form.php:353
msgid "If you want to hide the checkbox, you can enable this option."
msgstr ""

#: elementor/widgets/contact-form.php:406
#: elementor/widgets/inquiry-form.php:391
msgid "GDPR Agreement Text"
msgstr ""

#: elementor/widgets/contact-form.php:422
#: elementor/widgets/inquiry-form.php:407
#: elementor/widgets/single-agency/agency-contact-form.php:161
#: elementor/widgets/single-agency/agency-review.php:75
#: elementor/widgets/single-agent/agent-contact-form.php:161
#: elementor/widgets/single-agent/agent-review.php:75
#: elementor/widgets/single-property/section-contact-bottom.php:597
#: elementor/widgets/single-property/section-review.php:75
#: elementor/widgets/single-property/section-schedule-tour-v2.php:300
#: elementor/widgets/single-property/section-schedule-tour.php:238
msgid "Submit Button"
msgstr ""

#: elementor/widgets/contact-form.php:505
#: elementor/widgets/inquiry-form.php:490
msgid "Button ID"
msgstr ""

#: elementor/widgets/contact-form.php:508
#: elementor/widgets/inquiry-form.php:493
msgid "Add your custom id WITHOUT the Pound key. e.g: my-id"
msgstr ""

#: elementor/widgets/contact-form.php:510
#: elementor/widgets/inquiry-form.php:495
msgid ""
"Please make sure the ID is unique and not used elsewhere on the page this "
"form is displayed. This field allows <code>A-z 0-9</code> & underscore chars "
"without spaces."
msgstr ""

#: elementor/widgets/contact-form.php:528
#: elementor/widgets/inquiry-form.php:513
#: elementor/widgets/search-builder.php:184
#: elementor/widgets/search-builder.php:220
msgid "To"
msgstr ""

#: elementor/widgets/contact-form.php:533
#: elementor/widgets/contact-form.php:558
#: elementor/widgets/contact-form.php:569
#: elementor/widgets/inquiry-form.php:518
#: elementor/widgets/inquiry-form.php:543
#: elementor/widgets/inquiry-form.php:554
msgid "Separate emails with commas"
msgstr ""

#: elementor/widgets/contact-form.php:538
#: elementor/widgets/inquiry-form.php:523
#, php-format
msgid "New message from \"%s\""
msgstr ""

#: elementor/widgets/contact-form.php:543
#: elementor/widgets/inquiry-form.php:528
msgid "Subject"
msgstr ""

#: elementor/widgets/contact-form.php:555
#: elementor/widgets/inquiry-form.php:540
msgid "Cc"
msgstr ""

#: elementor/widgets/contact-form.php:566
#: elementor/widgets/inquiry-form.php:551
msgid "Bcc"
msgstr ""

#: elementor/widgets/contact-form.php:580
#: elementor/widgets/inquiry-form.php:564
msgid "Redirect"
msgstr ""

#: elementor/widgets/contact-form.php:587
#: elementor/widgets/inquiry-form.php:571
msgid "Redirect To"
msgstr ""

#: elementor/widgets/contact-form.php:589
#: elementor/widgets/create-listing-btn.php:208
#: elementor/widgets/header-footer/create-listing-btn.php:240
#: elementor/widgets/header-footer/site-logo.php:516
#: elementor/widgets/inquiry-form.php:573
msgid "https://your-link.com"
msgstr ""

#: elementor/widgets/contact-form.php:603
#: elementor/widgets/contact-form.php:610
#: elementor/widgets/inquiry-form.php:587
#: elementor/widgets/inquiry-form.php:594
msgid "Webhook"
msgstr ""

#: elementor/widgets/contact-form.php:622
#: elementor/widgets/inquiry-form.php:606
msgid "Webhook URL"
msgstr ""

#: elementor/widgets/contact-form.php:624
#: elementor/widgets/inquiry-form.php:608
msgid "https://your-webhook-url.com"
msgstr ""

#: elementor/widgets/contact-form.php:630
#: elementor/widgets/inquiry-form.php:614
msgid ""
"Enter the integration URL (like Zapier) that will receive the form's "
"submitted data."
msgstr ""

#: elementor/widgets/create-listing-btn.php:19
#: elementor/widgets/create-listing-btn.php:40
#: elementor/widgets/header-footer/create-listing-btn.php:21
#: elementor/widgets/header-footer/create-listing-btn.php:56
msgid "Create Listing Button"
msgstr ""

#: elementor/widgets/create-listing-btn.php:66
#: elementor/widgets/header-footer/create-listing-btn.php:82
#: elementor/widgets/listings-tabs.php:235
#: elementor/widgets/search-builder.php:1182
#: elementor/widgets/single-agency/agency-listings-review.php:515
#: elementor/widgets/single-agency/agency-listings.php:337
#: elementor/widgets/single-agent/agent-listings-review.php:520
#: elementor/widgets/single-agent/agent-listings.php:342
#: elementor/widgets/single-property/featured-label.php:78
#: elementor/widgets/single-property/item-label.php:78
#: elementor/widgets/single-property/property-address.php:108
#: elementor/widgets/single-property/section-contact-2.php:269
#: elementor/widgets/single-property/status-label.php:79
msgid "Margin"
msgstr ""

#: elementor/widgets/create-listing-btn.php:110
#: elementor/widgets/header-footer/create-listing-btn.php:132
#: elementor/widgets/header-footer/create-listing-btn.php:174
msgid "Button Color"
msgstr ""

#: elementor/widgets/create-listing-btn.php:122
msgid "Button Color Hover"
msgstr ""

#: elementor/widgets/create-listing-btn.php:135
#: elementor/widgets/header-footer/create-listing-btn.php:144
#: elementor/widgets/header-footer/create-listing-btn.php:187
msgid "Button Background Color"
msgstr ""

#: elementor/widgets/create-listing-btn.php:147
msgid "Button Background Color Hover"
msgstr ""

#: elementor/widgets/create-listing-btn.php:159
#: elementor/widgets/header-footer/create-listing-btn.php:156
#: elementor/widgets/header-footer/create-listing-btn.php:199
msgid "Button Border Color"
msgstr ""

#: elementor/widgets/create-listing-btn.php:171
msgid "Button Border Color Hover"
msgstr ""

#: elementor/widgets/create-listing-btn.php:186
#: elementor/widgets/header-footer/create-listing-btn.php:218
msgid "Button Link"
msgstr ""

#: elementor/widgets/create-listing-btn.php:194
#: elementor/widgets/header-footer/create-listing-btn.php:226
#: elementor/widgets/team-member.php:120
msgid "Custom Link"
msgstr ""

#: elementor/widgets/custom-carousel.php:44
msgid "Custom Carousel"
msgstr ""

#: elementor/widgets/custom-carousel.php:93
#: elementor/widgets/custom-carousel.php:123
#: elementor/widgets/custom-carousel.php:377
#: elementor/widgets/testimonials-v2.php:92
#: elementor/widgets/testimonials.php:92
msgid "Slides"
msgstr ""

#: elementor/widgets/custom-carousel.php:110
#: elementor/widgets/custom-carousel.php:346
msgid "Tagline"
msgstr ""

#: elementor/widgets/custom-carousel.php:135
msgid "Nav Position"
msgstr ""

#: elementor/widgets/custom-carousel.php:139
msgid "On Top"
msgstr ""

#: elementor/widgets/custom-carousel.php:140
msgid "Inside"
msgstr ""

#: elementor/widgets/custom-carousel.php:262
#, fuzzy
#| msgid "Property Title"
msgid "Carousel Title"
msgstr "Заголовок собственности"

#: elementor/widgets/custom-carousel.php:824
msgid "Read More Button"
msgstr ""

#: elementor/widgets/custom-carousel.php:1013
msgid "Navigation Buttons"
msgstr ""

#: elementor/widgets/custom-carousel.php:1166
#: elementor/widgets/custom-carousel.php:1238
#: elementor/widgets/custom-carousel.php:1247
#: elementor/widgets/custom-carousel.php:1256
#, fuzzy
#| msgid "Invoice Title"
msgid "Slide Title"
msgstr "Название счета"

#: elementor/widgets/custom-carousel.php:1201
msgid "More Button"
msgstr ""

#: elementor/widgets/custom-carousel.php:1211
#: elementor/widgets/icon-box.php:164
#, fuzzy
#| msgid "Load More"
msgid "Read More Text"
msgstr "Загрузи больше"

#: elementor/widgets/custom-carousel.php:1222
#: elementor/widgets/icon-box.php:171
#, fuzzy
#| msgid "Load More"
msgid "Read More Link"
msgstr "Загрузи больше"

#: elementor/widgets/custom-carousel.php:1237
#: elementor/widgets/custom-carousel.php:1246
#: elementor/widgets/custom-carousel.php:1255
msgid ""
"Lorem ipsum, dolor sit amet, consectetur adipisicing elit. Debitis, sequi "
"sed tempora qui ad accusantium eius, ab quo adipisci beatae illo, distinctio "
"numquam veritatis autem obcaecati blanditiis consectetur consequatur "
"perspiciatis.."
msgstr ""

#: elementor/widgets/google-map.php:41
#, fuzzy
#| msgid "Properties"
msgid "Properties Google Map"
msgstr "свойства"

#: elementor/widgets/google-map.php:93 elementor/widgets/mapbox.php:94
#: elementor/widgets/open-street-map.php:97
msgid "Map Options"
msgstr ""

#: elementor/widgets/google-map.php:101 elementor/widgets/mapbox.php:306
#: elementor/widgets/open-street-map.php:148
#: elementor/widgets/single-property/section-google-map.php:115
#: elementor/widgets/single-property/section-map.php:73
#: elementor/widgets/single-property/section-open-street-map.php:91
msgid "Map Height (px)"
msgstr ""

#: elementor/widgets/google-map.php:136
#: elementor/widgets/open-street-map.php:105
msgid "Default Zoom"
msgstr ""

#: elementor/widgets/google-map.php:142
#: elementor/widgets/open-street-map.php:111
msgid "Set the default zoom level for the map"
msgstr ""

#: elementor/widgets/google-map.php:149
#: elementor/widgets/single-property/section-google-map.php:81
#, fuzzy
#| msgid "Type"
msgid "Map Type"
msgstr "Тип"

#: elementor/widgets/google-map.php:152
msgid "RoadMap"
msgstr ""

#: elementor/widgets/google-map.php:153 elementor/widgets/mapbox.php:110
#: elementor/widgets/single-property/section-google-map.php:86
msgid "Satellite"
msgstr ""

#: elementor/widgets/google-map.php:154
#: elementor/widgets/single-property/section-google-map.php:87
msgid "Hybrid"
msgstr ""

#: elementor/widgets/google-map.php:155
#: elementor/widgets/single-property/section-google-map.php:88
msgid "Terrain"
msgstr ""

#: elementor/widgets/google-map.php:164 elementor/widgets/mapbox.php:163
#: elementor/widgets/open-street-map.php:118
msgid "Hide Zoom Control"
msgstr ""

#: elementor/widgets/google-map.php:179 elementor/widgets/mapbox.php:148
#: elementor/widgets/open-street-map.php:133
msgid "Hide Next/Previous Control"
msgstr ""

#: elementor/widgets/google-map.php:311 elementor/widgets/mapbox.php:295
#: elementor/widgets/open-street-map.php:301
msgid ""
"You can make a property featured by clicking featured properties checkbox "
"while add/edit property"
msgstr ""

#: elementor/widgets/google-map.php:329 elementor/widgets/mapbox.php:350
#: elementor/widgets/open-street-map.php:322 functions/helpers.php:476
#: shortcodes/agents.php:158 shortcodes/blog-posts-carousel.php:109
#: shortcodes/partners.php:34
msgid "Prev"
msgstr ""

#: elementor/widgets/grid-builder.php:37
msgid "Grids Builder"
msgstr ""

#: elementor/widgets/grid-builder.php:88
msgid "Data"
msgstr ""

#: elementor/widgets/grid-builder.php:92 elementor/widgets/grid-builder.php:379
#: elementor/widgets/single-post/post-info.php:80
#: elementor/widgets/single-post/post-info.php:97
#: elementor/widgets/single-post/post-info.php:134
#: elementor/widgets/single-post/post-info.php:254
#: elementor/widgets/single-post/post-info.php:300
#: elementor/widgets/single-property/section-overview-v2.php:178
#: elementor/widgets/single-property/section-overview.php:177
msgid "Custom"
msgstr ""

#: elementor/widgets/grid-builder.php:93 elementor/widgets/grid-builder.php:378
msgid "Dynamic"
msgstr ""

#: elementor/widgets/grid-builder.php:127
#: elementor/widgets/section-title.php:192
#, fuzzy
#| msgid "Title"
msgid "Subtitle"
msgstr "заглавие"

#: elementor/widgets/grid-builder.php:138
#: elementor/widgets/grid-builder.php:326
msgid "More Details Text"
msgstr ""

#: elementor/widgets/grid-builder.php:168 elementor/widgets/grids.php:103
#: elementor/widgets/taxonomies-cards-carousel.php:112
#: elementor/widgets/taxonomies-cards.php:125
#: elementor/widgets/taxonomies-grids-carousel.php:97
#: elementor/widgets/taxonomies-grids.php:117
#: elementor/widgets/taxonomies-list.php:102
msgid "Choose Taxonomy"
msgstr ""

#: elementor/widgets/grid-builder.php:338
#, fuzzy
#| msgid "Views Count"
msgid "Listing Count"
msgstr "Количество просмотров"

#: elementor/widgets/grid-builder.php:353
#, fuzzy
#| msgid "Properties"
msgid "Properties Text"
msgstr "свойства"

#: elementor/widgets/grid-builder.php:364
#, fuzzy
#| msgid "Property Title"
msgid "Property Text"
msgstr "Заголовок собственности"

#: elementor/widgets/grid-builder.php:391
#: elementor/widgets/header-footer/site-logo.php:427
#: elementor/widgets/single-post/author-box.php:272
#: elementor/widgets/single-property/images-gallery-v2.php:56
#: elementor/widgets/single-property/images-gallery-v4.php:49
#: elementor/widgets/single-property/images-gallery-v5.php:49
#: elementor/widgets/single-property/section-toparea-v3.php:85
#: elementor/widgets/single-property/section-toparea-v6.php:81
#: elementor/widgets/single-property/section-toparea-v7.php:81
#: elementor/widgets/taxonomies-cards-carousel.php:102
#: elementor/widgets/taxonomies-cards.php:101
#: elementor/widgets/taxonomies-grids-carousel.php:87
#: elementor/widgets/taxonomies-grids.php:107
#, fuzzy
#| msgid "Max Size"
msgid "Image Size"
msgstr "Максимальный размер"

#: elementor/widgets/grid-builder.php:423
msgid "Grid Styling"
msgstr ""

#: elementor/widgets/grid-builder.php:431
#, fuzzy
#| msgid "Type"
msgid "Grid Type"
msgstr "Тип"

#: elementor/widgets/grid-builder.php:434
msgid "Landscape"
msgstr ""

#: elementor/widgets/grid-builder.php:435
msgid "Square"
msgstr ""

#: elementor/widgets/grid-builder.php:436
msgid "Portrait"
msgstr ""

#: elementor/widgets/grid-builder.php:445
#: elementor/widgets/grid-builder.php:488
#: elementor/widgets/grid-builder.php:531
msgid "Height(%)"
msgstr ""

#: elementor/widgets/grid-builder.php:574
msgid "Padding(px)"
msgstr ""

#: elementor/widgets/grid-builder.php:601 elementor/widgets/grids.php:369
#: elementor/widgets/single-agency/agency-contact.php:360
#: elementor/widgets/single-agent/agent-contact.php:360
#: elementor/widgets/taxonomies-cards-carousel.php:257
#: elementor/widgets/taxonomies-cards.php:162
#: elementor/widgets/taxonomies-grids-carousel.php:242
#: elementor/widgets/taxonomies-grids.php:152
msgid "Title Typography"
msgstr ""

#: elementor/widgets/grid-builder.php:621 elementor/widgets/grids.php:389
msgid "Subtitle Typography"
msgstr ""

#: elementor/widgets/grid-builder.php:652 elementor/widgets/grids.php:420
msgid "Subtitle Color"
msgstr ""

#: elementor/widgets/grids.php:36
#, fuzzy
#| msgid "Houzez Invoices"
msgid "Houzez Grids"
msgstr "Houzez Invoices"

#: elementor/widgets/grids.php:87
msgid "Choose Grid"
msgstr ""

#: elementor/widgets/grids.php:90
msgid "Grid v1"
msgstr ""

#: elementor/widgets/grids.php:91
msgid "Grid v2"
msgstr ""

#: elementor/widgets/grids.php:92
msgid "Grid v3"
msgstr ""

#: elementor/widgets/grids.php:93
msgid "Grid v4"
msgstr ""

#: elementor/widgets/grids.php:320 elementor/widgets/icon-box.php:307
msgid "Styling"
msgstr ""

#: elementor/widgets/grids.php:328
#: elementor/widgets/taxonomies-grids-carousel.php:283
#: elementor/widgets/taxonomies-grids.php:228
msgid "Grid Gap"
msgstr ""

#: elementor/widgets/grids.php:359
msgid "Title & Subtitle"
msgstr ""

#: elementor/widgets/grids.php:432
msgid "Title Background Color"
msgstr ""

#: elementor/widgets/header-footer/area-switcher.php:43
#: elementor/widgets/header-footer/area-switcher.php:321
msgid "Area Switcher"
msgstr ""

#: elementor/widgets/header-footer/area-switcher.php:122
#: elementor/widgets/header-footer/area-switcher.php:324
msgid "You need enable it under Theme Options -> Top Bar -> Area Switcher"
msgstr ""

#: elementor/widgets/header-footer/area-switcher.php:130
#: elementor/widgets/header-footer/currency.php:130
#: elementor/widgets/header-footer/lang.php:130
#: elementor/widgets/header-footer/menu.php:753
#: elementor/widgets/header-footer/menu.php:950
#: elementor/widgets/header-footer/menu.php:1052
#: elementor/widgets/header-footer/site-logo.php:129
msgid "Vertical Padding"
msgstr ""

#: elementor/widgets/header-footer/area-switcher.php:147
#: elementor/widgets/header-footer/currency.php:147
#: elementor/widgets/header-footer/lang.php:147
#: elementor/widgets/header-footer/menu.php:736
#: elementor/widgets/header-footer/menu.php:932
#: elementor/widgets/header-footer/menu.php:1038
msgid "Horizontal Padding"
msgstr ""

#: elementor/widgets/header-footer/area-switcher.php:230
#: elementor/widgets/header-footer/currency.php:230
msgid "Currency Dropdown"
msgstr ""

#: elementor/widgets/header-footer/area-switcher.php:239
#: elementor/widgets/header-footer/currency.php:239
#: elementor/widgets/header-footer/login-modal.php:515
#: elementor/widgets/login-modal.php:450
msgid "Position from Top"
msgstr ""

#: elementor/widgets/header-footer/area-switcher.php:289
#: elementor/widgets/header-footer/currency.php:289
#: elementor/widgets/header-footer/lang.php:188
msgid "Color :hover"
msgstr ""

#: elementor/widgets/header-footer/currency.php:43
msgid "Currency Switcher"
msgstr ""

#: elementor/widgets/header-footer/currency.php:122
#: elementor/widgets/header-footer/currency.php:345
msgid "You need enable it under Theme Options -> Top Bar -> Currency Switcher"
msgstr ""

#: elementor/widgets/header-footer/currency.php:165
#: elementor/widgets/header-footer/currency.php:342
#: elementor/widgets/search-builder.php:99
msgid "Currency"
msgstr ""

#: elementor/widgets/header-footer/lang.php:43
#: elementor/widgets/single-agency/agency-meta.php:60
#: elementor/widgets/single-agent/agent-meta.php:62
msgid "Language"
msgstr ""

#: elementor/widgets/header-footer/lang.php:122
msgid "You need Polylang or WPML plugin for this to work"
msgstr ""

#: elementor/widgets/header-footer/login-modal.php:19
#: elementor/widgets/header-footer/login-modal.php:54
#: elementor/widgets/login-modal.php:19 elementor/widgets/login-modal.php:41
msgid "Login Modal"
msgstr ""

#: elementor/widgets/header-footer/login-modal.php:63
#: elementor/widgets/login-modal.php:50
msgid "Login, register type"
msgstr ""

#: elementor/widgets/header-footer/login-modal.php:66
#: elementor/widgets/login-modal.php:53
msgid "Show as Icon"
msgstr ""

#: elementor/widgets/header-footer/login-modal.php:67
#: elementor/widgets/login-modal.php:54
msgid "Show as Text"
msgstr ""

#: elementor/widgets/header-footer/login-modal.php:77
#: elementor/widgets/login-modal.php:64
msgid "Show LoggedIn View"
msgstr ""

#: elementor/widgets/header-footer/login-modal.php:83
#: elementor/widgets/header-footer/login-modal.php:97
#: elementor/widgets/login-modal.php:70 elementor/widgets/login-modal.php:84
msgid "Only for design purpose"
msgstr ""

#: elementor/widgets/header-footer/login-modal.php:91
#: elementor/widgets/login-modal.php:78
msgid "Show Drop Down"
msgstr ""

#: elementor/widgets/header-footer/login-modal.php:105
#: elementor/widgets/login-modal.php:92
msgid "Icon Size(px)"
msgstr ""

#: elementor/widgets/header-footer/login-modal.php:163
#: elementor/widgets/login-modal.php:150
msgid "LoggedIn Position"
msgstr ""

#: elementor/widgets/header-footer/login-modal.php:185
msgid "LoggedIn Horizontal Padding"
msgstr ""

#: elementor/widgets/header-footer/login-modal.php:202
msgid "LoggedIn Vertical Padding"
msgstr ""

#: elementor/widgets/header-footer/login-modal.php:234
#: elementor/widgets/login-modal.php:187
msgid "Login Text Padding"
msgstr ""

#: elementor/widgets/header-footer/login-modal.php:249
#: elementor/widgets/login-modal.php:202
msgid "Register Text Padding"
msgstr ""

#: elementor/widgets/header-footer/login-modal.php:279
#: elementor/widgets/login-modal.php:232
msgid "Nav Links"
msgstr ""

#: elementor/widgets/header-footer/login-modal.php:288
#: elementor/widgets/header-footer/login-modal.php:324
#: elementor/widgets/header-footer/login-modal.php:391
#: elementor/widgets/login-modal.php:241 elementor/widgets/login-modal.php:282
msgid "Login/Register Text"
msgstr ""

#: elementor/widgets/header-footer/login-modal.php:337
#: elementor/widgets/header-footer/login-modal.php:404
#: elementor/widgets/login-modal.php:308
msgid "Drop Down Background"
msgstr ""

#: elementor/widgets/header-footer/login-modal.php:349
#: elementor/widgets/header-footer/login-modal.php:416
#: elementor/widgets/login-modal.php:320
msgid "Drop Down border"
msgstr ""

#: elementor/widgets/header-footer/login-modal.php:361
#: elementor/widgets/header-footer/login-modal.php:428
#: elementor/widgets/login-modal.php:332
msgid "User Nav Links"
msgstr ""

#: elementor/widgets/header-footer/login-modal.php:447
#: elementor/widgets/login-modal.php:382
msgid "Sizes & Spacing"
msgstr ""

#: elementor/widgets/header-footer/login-modal.php:455
#: elementor/widgets/login-modal.php:390
msgid "Avatar Border Radius(px)"
msgstr ""

#: elementor/widgets/header-footer/login-modal.php:485
#: elementor/widgets/login-modal.php:420
msgid "Drop Down Size(px)"
msgstr ""

#: elementor/widgets/header-footer/login-modal.php:556
#: elementor/widgets/login-modal.php:491
msgid "Drop Down Box Shadow"
msgstr ""

#: elementor/widgets/header-footer/login-modal.php:580
#: elementor/widgets/header-footer/login-modal.php:615
#: elementor/widgets/login-modal.php:514 elementor/widgets/login-modal.php:549
msgid "Login"
msgstr ""

#: elementor/widgets/header-footer/login-modal.php:586
#: elementor/widgets/header-footer/login-modal.php:622
#: elementor/widgets/login-modal.php:520 elementor/widgets/login-modal.php:554
msgid "Register"
msgstr ""

#: elementor/widgets/header-footer/menu.php:49
#, fuzzy
#| msgid "Activation"
msgid "Navigation"
msgstr "активация"

#: elementor/widgets/header-footer/menu.php:152
#: elementor/widgets/header-footer/menu.php:1360
msgid "Menu"
msgstr ""

#: elementor/widgets/header-footer/menu.php:156
#, php-format
msgid ""
"Go to the <a href=\"%s\" target=\"_blank\">Menus screen</a> to manage your "
"menus."
msgstr ""

#: elementor/widgets/header-footer/menu.php:165
#, php-format
msgid ""
"<strong>There are no menus in your site.</strong><br>Go to the <a "
"href=\"%s\" target=\"_blank\">Menus screen</a> to create one."
msgstr ""

#: elementor/widgets/header-footer/menu.php:174
msgid "layout"
msgstr ""

#: elementor/widgets/header-footer/menu.php:177
msgid "Horizontal"
msgstr ""

#: elementor/widgets/header-footer/menu.php:178
#: elementor/widgets/header-footer/menu.php:772
msgid "Dropdown"
msgstr ""

#: elementor/widgets/header-footer/menu.php:190
msgid "Align"
msgstr ""

#: elementor/widgets/header-footer/menu.php:222
msgid "Pointer"
msgstr ""

#: elementor/widgets/header-footer/menu.php:227
msgid "Underline"
msgstr ""

#: elementor/widgets/header-footer/menu.php:228
msgid "Overline"
msgstr ""

#: elementor/widgets/header-footer/menu.php:229
msgid "Double Line"
msgstr ""

#: elementor/widgets/header-footer/menu.php:230
msgid "Framed"
msgstr ""

#: elementor/widgets/header-footer/menu.php:329
msgid "Submenu Indicator"
msgstr ""

#: elementor/widgets/header-footer/menu.php:333
msgid "Angle"
msgstr ""

#: elementor/widgets/header-footer/menu.php:334
msgid "Caret"
msgstr ""

#: elementor/widgets/header-footer/menu.php:335
msgid "Plus"
msgstr ""

#: elementor/widgets/header-footer/menu.php:348
msgid "Mobile Dropdown"
msgstr ""

#: elementor/widgets/header-footer/menu.php:360
msgid "Breakpoint"
msgstr ""

#: elementor/widgets/header-footer/menu.php:364
msgid "Tablet Portrait & Less"
msgstr ""

#: elementor/widgets/header-footer/menu.php:365
msgid "Mobile Portrait & Less"
msgstr ""

#: elementor/widgets/header-footer/menu.php:377
msgid "Toggle Align"
msgstr ""

#: elementor/widgets/header-footer/menu.php:400
#: elementor/widgets/icon-box.php:112 elementor/widgets/icon-box.php:316
#: elementor/widgets/properties-ajax-tabs.php:254
#: elementor/widgets/properties-ajax-tabs.php:268
#: elementor/widgets/property-meta-data.php:212
#: elementor/widgets/single-agency/agency-call-btn.php:86
#: elementor/widgets/single-agency/agency-contact-btn.php:74
#: elementor/widgets/single-agency/agency-line-btn.php:74
#: elementor/widgets/single-agency/agency-meta.php:374
#: elementor/widgets/single-agency/agency-telegram-btn.php:74
#: elementor/widgets/single-agency/agency-whatsapp-btn.php:74
#: elementor/widgets/single-agent/agent-call-btn.php:86
#: elementor/widgets/single-agent/agent-contact-btn.php:74
#: elementor/widgets/single-agent/agent-line-btn.php:74
#: elementor/widgets/single-agent/agent-meta.php:375
#: elementor/widgets/single-agent/agent-telegram-btn.php:74
#: elementor/widgets/single-agent/agent-whatsapp-btn.php:74
#: elementor/widgets/single-post/post-info.php:295
#: elementor/widgets/single-post/post-info.php:581
#: elementor/widgets/single-property/section-floorplan.php:189
#: elementor/widgets/taxonomies-list.php:328
#: elementor/widgets/taxonomies-list.php:336
msgid "Icon"
msgstr ""

#: elementor/widgets/header-footer/menu.php:442
msgid "Main Menu"
msgstr ""

#: elementor/widgets/header-footer/menu.php:532
#: elementor/widgets/header-footer/menu.php:576
msgid "Pointer Color"
msgstr ""

#: elementor/widgets/header-footer/menu.php:556
#: elementor/widgets/header-footer/menu.php:877
#: elementor/widgets/single-agency/agency-listings-review.php:326
#: elementor/widgets/single-agent/agent-listings-review.php:331
#: functions/functions.php:237
#, fuzzy
#| msgid "Activation"
msgid "Active"
msgstr "активация"

#: elementor/widgets/header-footer/menu.php:611
#: elementor/widgets/header-footer/menu.php:971
#: elementor/widgets/single-post/post-info.php:428
#: elementor/widgets/taxonomies-list.php:231
msgid "Divider"
msgstr ""

#: elementor/widgets/header-footer/menu.php:613
#: elementor/widgets/single-post/post-info.php:430
#: elementor/widgets/taxonomies-list.php:233
#: extensions/meta-box/inc/fields/switch.php:85
msgid "Off"
msgstr ""

#: elementor/widgets/header-footer/menu.php:614
#: elementor/widgets/single-post/post-info.php:431
#: elementor/widgets/taxonomies-list.php:234
#: extensions/meta-box/inc/fields/switch.php:84
msgid "On"
msgstr ""

#: elementor/widgets/header-footer/menu.php:713
msgid "Pointer Width"
msgstr ""

#: elementor/widgets/header-footer/menu.php:780
msgid ""
"On desktop, this will affect the submenu. On mobile, this will affect the "
"entire menu."
msgstr ""

#: elementor/widgets/header-footer/menu.php:1027
msgid "Toggle Button"
msgstr ""

#: elementor/widgets/header-footer/menu.php:1188
msgid "Offcanvas Header"
msgstr ""

#: elementor/widgets/header-footer/site-logo.php:51
msgid "Site Logo"
msgstr ""

#: elementor/widgets/header-footer/site-logo.php:121
msgid "Site logo"
msgstr ""

#: elementor/widgets/header-footer/site-logo.php:146
#: elementor/widgets/single-post/author-box.php:242
msgid "Top"
msgstr ""

#: elementor/widgets/header-footer/site-logo.php:375
msgid "Logo"
msgstr ""

#: elementor/widgets/header-footer/site-logo.php:382
msgid "Logo Source"
msgstr ""

#: elementor/widgets/header-footer/site-logo.php:385
msgid "Theme Options"
msgstr ""

#: elementor/widgets/header-footer/site-logo.php:386
msgid "Custom Logo"
msgstr ""

#: elementor/widgets/header-footer/site-logo.php:397
#, php-format
msgid ""
"Please select or upload your <strong>Logo</strong> in the <a "
"target=\"_blank\" href=\"%1$s\"><em>Theme Options</em></a>."
msgstr ""

#: elementor/widgets/header-footer/site-logo.php:479
msgid "Custom Caption"
msgstr ""

#: elementor/widgets/header-footer/site-logo.php:482
msgid "Enter caption"
msgstr ""

#: elementor/widgets/icon-box.php:43 elementor/widgets/icon-box.php:179
msgid "Icon Box"
msgstr ""

#: elementor/widgets/icon-box.php:98
#: elementor/widgets/properties-ajax-tabs.php:245
#, fuzzy
#| msgid "Invoice Type"
msgid "Icon Type"
msgstr "Тип счета-фактуры"

#: elementor/widgets/icon-box.php:128
msgid "Fontawesome Icon"
msgstr ""

#: elementor/widgets/icon-box.php:139
#: elementor/widgets/property-meta-data.php:152
#: elementor/widgets/single-agency/agency-call-btn.php:76
#: elementor/widgets/single-agency/agency-contact-btn.php:64
#: elementor/widgets/single-agency/agency-line-btn.php:64
#: elementor/widgets/single-agency/agency-meta.php:159
#: elementor/widgets/single-agency/agency-telegram-btn.php:64
#: elementor/widgets/single-agency/agency-whatsapp-btn.php:64
#: elementor/widgets/single-agent/agent-call-btn.php:76
#: elementor/widgets/single-agent/agent-contact-btn.php:64
#: elementor/widgets/single-agent/agent-line-btn.php:64
#: elementor/widgets/single-agent/agent-meta.php:161
#: elementor/widgets/single-agent/agent-telegram-btn.php:64
#: elementor/widgets/single-agent/agent-whatsapp-btn.php:64
#: elementor/widgets/single-property/section-overview-v2.php:121
#: elementor/widgets/single-property/section-overview.php:120
msgid "Custom Icon"
msgstr ""

#: elementor/widgets/icon-box.php:191
msgid "Icon Boxes Style"
msgstr ""

#: elementor/widgets/icon-box.php:287
msgid "Background Overlay"
msgstr ""

#: elementor/widgets/icon-box.php:353
msgid "Icon Padding"
msgstr ""

#: elementor/widgets/icon-box.php:373
msgid "Icon Rotate"
msgstr ""

#: elementor/widgets/icon-box.php:435 elementor/widgets/team-member.php:112
msgid "Description"
msgstr ""

#: elementor/widgets/icon-box.php:465
#, fuzzy
#| msgid "Load More"
msgid "Read More"
msgstr "Загрузи больше"

#: elementor/widgets/inquiry-form.php:42
msgid "Inquiry Form"
msgstr ""

#: elementor/widgets/inquiry-form.php:84
#, fuzzy
#| msgid "Invoice Type"
msgid "Inquiry Type"
msgstr "Тип счета-фактуры"

#: elementor/widgets/inquiry-form.php:87
#: elementor/widgets/property-meta-data.php:50
#: elementor/widgets/single-agency/agency-single-stats.php:70
#: elementor/widgets/single-agent/agent-single-stats.php:70
#: elementor/widgets/single-property/item-label.php:18
#: elementor/widgets/single-property/section-similar.php:83
#, fuzzy
#| msgid "Property Slug"
msgid "Property Label"
msgstr "Недвижимость Slug"

#: elementor/widgets/inquiry-form.php:91
#, fuzzy
#| msgid "Bedrooms"
msgid "Minimum Bedrooms"
msgstr "спальни"

#: elementor/widgets/inquiry-form.php:92
#, fuzzy
#| msgid "Bedrooms"
msgid "Maximum Bedrooms"
msgstr "спальни"

#: elementor/widgets/inquiry-form.php:93
#, fuzzy
#| msgid "Bathrooms"
msgid "Minimum Bathrooms"
msgstr "В ванных комнатах"

#: elementor/widgets/inquiry-form.php:94
#, fuzzy
#| msgid "Bathrooms"
msgid "Maximum Bathrooms"
msgstr "В ванных комнатах"

#: elementor/widgets/inquiry-form.php:95
msgid "Minimum Area Size"
msgstr ""

#: elementor/widgets/inquiry-form.php:96
#, fuzzy
#| msgid "Max Size"
msgid "Maximum Area Size"
msgstr "Максимальный размер"

#: elementor/widgets/inquiry-form.php:102
msgid "Street Address"
msgstr ""

#: elementor/widgets/inquiry-form.php:652
msgid "Please install and activate Houzez CRM plugin."
msgstr ""

#: elementor/widgets/inquiry-form.php:1171
msgid "Purchase, Rent, Sell, Miss, Evaluation, Mortgage"
msgstr ""

#: elementor/widgets/listings-tabs.php:36
msgid "Listings Tabs"
msgstr ""

#: elementor/widgets/listings-tabs.php:93
#: elementor/widgets/search-builder.php:682
#: elementor/widgets/single-agency/agency-listings-review.php:175
#: elementor/widgets/single-agency/agency-listings.php:175
#: elementor/widgets/single-agent/agent-listings-review.php:180
#: elementor/widgets/single-agent/agent-listings.php:180
#, fuzzy
#| msgid "Type"
msgid "Tab Type"
msgstr "Тип"

#: elementor/widgets/listings-tabs.php:108
#: elementor/widgets/search-builder.php:700
#: elementor/widgets/single-agency/agency-listings-review.php:193
#: elementor/widgets/single-agency/agency-listings.php:193
#: elementor/widgets/single-agent/agent-listings-review.php:198
#: elementor/widgets/single-agent/agent-listings.php:198
#, fuzzy
#| msgid "All Types"
msgid "Select Types"
msgstr "Все типы"

#: elementor/widgets/listings-tabs.php:123
#: elementor/widgets/search-builder.php:717
#: elementor/widgets/single-agency/agency-listings-review.php:209
#: elementor/widgets/single-agency/agency-listings.php:209
#: elementor/widgets/single-agent/agent-listings-review.php:214
#: elementor/widgets/single-agent/agent-listings.php:214
#, fuzzy
#| msgid "New Status"
msgid "Select Statuses"
msgstr "Новый статус"

#: elementor/widgets/listings-tabs.php:139
#: elementor/widgets/search-builder.php:734
#: elementor/widgets/single-agency/agency-listings-review.php:226
#: elementor/widgets/single-agency/agency-listings.php:226
#: elementor/widgets/single-agent/agent-listings-review.php:231
#: elementor/widgets/single-agent/agent-listings.php:231
#, fuzzy
#| msgid "All Cities"
msgid "Select Cities"
msgstr "Все города"

#: elementor/widgets/listings-tabs.php:155
#: elementor/widgets/search-builder.php:751
#: elementor/widgets/single-agency/agency-listings-review.php:243
#: elementor/widgets/single-agency/agency-listings.php:243
#: elementor/widgets/single-agent/agent-listings-review.php:248
#: elementor/widgets/single-agent/agent-listings.php:248
msgid "Show All Tab"
msgstr ""

#: elementor/widgets/listings-tabs.php:170
#: elementor/widgets/properties-ajax-tabs.php:99
#: elementor/widgets/search-builder.php:1105
#: elementor/widgets/single-agency/agency-listings.php:261
#: elementor/widgets/single-agent/agent-listings.php:266
msgid "Tabs Style"
msgstr ""

#: elementor/widgets/listings-tabs.php:178
#: elementor/widgets/search-builder.php:1121
#: elementor/widgets/single-agency/agency-listings-review.php:458
#: elementor/widgets/single-agency/agency-listings.php:280
#: elementor/widgets/single-agent/agent-listings-review.php:463
#: elementor/widgets/single-agent/agent-listings.php:285
#: elementor/widgets/single-property/section-contact-2.php:197
#: elementor/widgets/single-property/section-contact-2.php:363
#: elementor/widgets/single-property/section-schedule-tour-v2.php:180
msgid "Tabs Color"
msgstr ""

#: elementor/widgets/listings-tabs.php:190
#: elementor/widgets/search-builder.php:1133
#: elementor/widgets/single-agency/agency-listings-review.php:470
#: elementor/widgets/single-agency/agency-listings.php:292
#: elementor/widgets/single-agent/agent-listings-review.php:475
#: elementor/widgets/single-agent/agent-listings.php:297
#: elementor/widgets/single-property/section-contact-2.php:212
#: elementor/widgets/single-property/section-contact-2.php:378
#: elementor/widgets/single-property/section-schedule-tour-v2.php:192
msgid "Tabs Active Color"
msgstr ""

#: elementor/widgets/listings-tabs.php:202
#: elementor/widgets/search-builder.php:1145
#: elementor/widgets/single-agency/agency-listings-review.php:482
#: elementor/widgets/single-agency/agency-listings.php:304
#: elementor/widgets/single-agent/agent-listings-review.php:487
#: elementor/widgets/single-agent/agent-listings.php:309
#: elementor/widgets/single-property/section-contact-2.php:227
#: elementor/widgets/single-property/section-contact-2.php:393
#: elementor/widgets/single-property/section-schedule-tour-v2.php:204
msgid "Tabs Background Color"
msgstr ""

#: elementor/widgets/listings-tabs.php:214
#: elementor/widgets/search-builder.php:1157
#: elementor/widgets/single-agency/agency-listings-review.php:494
#: elementor/widgets/single-agency/agency-listings.php:316
#: elementor/widgets/single-agent/agent-listings-review.php:499
#: elementor/widgets/single-agent/agent-listings.php:321
#: elementor/widgets/single-property/section-contact-2.php:242
#: elementor/widgets/single-property/section-contact-2.php:408
#: elementor/widgets/single-property/section-schedule-tour-v2.php:216
msgid "Active Tabs Background Color"
msgstr ""

#: elementor/widgets/login-modal.php:270
msgid "Icon Color Hover"
msgstr ""

#: elementor/widgets/login-modal.php:295
msgid "Login/Register Text Hover"
msgstr ""

#: elementor/widgets/login-modal.php:344
msgid "Drop Down Hover Background"
msgstr ""

#: elementor/widgets/login-modal.php:356
msgid "Drop Down border hover"
msgstr ""

#: elementor/widgets/login-modal.php:368
msgid "User Nav Links Hover"
msgstr ""

#: elementor/widgets/mapbox.php:42
#, fuzzy
#| msgid "Properties"
msgid "Properties Mapbox"
msgstr "свойства"

#: elementor/widgets/mapbox.php:102
msgid "Map Style"
msgstr ""

#: elementor/widgets/mapbox.php:105
msgid "Global (Use Theme Options)"
msgstr ""

#: elementor/widgets/mapbox.php:106
msgid "Streets"
msgstr ""

#: elementor/widgets/mapbox.php:107
msgid "Outdoors"
msgstr ""

#: elementor/widgets/mapbox.php:108
msgid "Light"
msgstr ""

#: elementor/widgets/mapbox.php:109
msgid "Dark"
msgstr ""

#: elementor/widgets/mapbox.php:111
msgid "Satellite Streets"
msgstr ""

#: elementor/widgets/mapbox.php:112 elementor/widgets/mapbox.php:121
msgid "Custom Style URL"
msgstr ""

#: elementor/widgets/mapbox.php:134
#, php-format
msgid ""
"To use a custom Mapbox style URL, select \"Custom Style URL\" above and "
"enter your Mapbox style URL. You can create custom styles in the %1$sMapbox "
"Studio%2$s."
msgstr ""

#: elementor/widgets/mapbox.php:179 elementor/widgets/open-street-map.php:185
#, fuzzy
#| msgid "Properties"
msgid "Properties Filters"
msgstr "свойства"

#: elementor/widgets/open-street-map.php:45
#, fuzzy
#| msgid "Properties Settings"
msgid "Properties Open Street Map"
msgstr "Настройки свойств"

#: elementor/widgets/page-title.php:38
#, fuzzy
#| msgid "Payment Title"
msgid "Page Title"
msgstr "Название платежа"

#: elementor/widgets/page-title.php:88
#: elementor/widgets/single-property/property-title-area.php:139
#: elementor/widgets/single-property/section-toparea-v1.php:83
#: elementor/widgets/single-property/section-toparea-v2.php:81
#: elementor/widgets/single-property/section-toparea-v3.php:106
#: elementor/widgets/single-property/section-toparea-v5.php:81
#: elementor/widgets/single-property/section-toparea-v6.php:126
#: elementor/widgets/single-property/section-toparea-v7.php:101
msgid "Show Breadcrumb"
msgstr ""

#: elementor/widgets/price-table.php:37
#, fuzzy
#| msgid "Price"
msgid "Price Table"
msgstr "Цена"

#: elementor/widgets/price-table.php:95
#, fuzzy
#| msgid "Search Packages"
msgid "Select Package"
msgstr "Пакеты поиска"

#: elementor/widgets/price-table.php:106
#: elementor/widgets/single-property/section-overview-v2.php:174
#: elementor/widgets/single-property/section-overview.php:173
#, fuzzy
#| msgid "Payment Type"
msgid "Data Type"
msgstr "Способ оплаты"

#: elementor/widgets/price-table.php:120
msgid "Popular?"
msgstr ""

#: elementor/widgets/price-table.php:139
#, fuzzy
#| msgid "Package"
msgid "Package Name"
msgstr "пакет"

#: elementor/widgets/price-table.php:151
#, fuzzy
#| msgid "Package"
msgid "Package Price"
msgstr "пакет"

#: elementor/widgets/price-table.php:163
#, fuzzy
#| msgid "Package"
msgid "Package Currency"
msgstr "пакет"

#: elementor/widgets/price-table.php:193
msgid "Button Text"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:47
#, fuzzy
#| msgid "Properties"
msgid "Properties Ajax Tabs"
msgstr "свойства"

#: elementor/widgets/properties-ajax-tabs.php:92
msgid "General"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:103
msgid "Tabs v1"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:104
msgid "Tabs v2"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:105
msgid "Tabs v3"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:106
msgid "Tabs v4"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:107
msgid "Tabs v5"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:140
msgid "Tab Spacer"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:159
msgid "Bottom Spacer"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:178
msgid "Enable Icon"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:188
#: elementor/widgets/single-agency/agency-call-btn.php:103
#: elementor/widgets/single-agency/agency-contact-btn.php:91
#: elementor/widgets/single-agency/agency-line-btn.php:91
#: elementor/widgets/single-agency/agency-telegram-btn.php:91
#: elementor/widgets/single-agency/agency-whatsapp-btn.php:91
#: elementor/widgets/single-agent/agent-call-btn.php:103
#: elementor/widgets/single-agent/agent-contact-btn.php:91
#: elementor/widgets/single-agent/agent-line-btn.php:91
#: elementor/widgets/single-agent/agent-telegram-btn.php:91
#: elementor/widgets/single-agent/agent-whatsapp-btn.php:91
msgid "Icon Position"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:193
#: elementor/widgets/single-post/author-box.php:177
msgid "Above"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:194
msgid "Before title"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:203
msgid "Show title"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:218
#: elementor/widgets/properties-ajax-tabs.php:678
#: elementor/widgets/search-builder.php:662
#: elementor/widgets/single-property/section-floorplan-v2.php:179
msgid "Tabs"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:231
msgid "Tab"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:237
#: elementor/widgets/properties-ajax-tabs.php:239
#, fuzzy
#| msgid "Title"
msgid "Tab Title"
msgstr "заглавие"

#: elementor/widgets/properties-ajax-tabs.php:297
msgid "Query"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:556
msgid "Cards Style"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:574
#: elementor/widgets/properties-ajax-tabs.php:583
#: elementor/widgets/property-by-id.php:118
#: elementor/widgets/property-by-id.php:127
#: elementor/widgets/property-by-ids.php:116
#: elementor/widgets/property-by-ids.php:125
#: elementor/widgets/single-agency/agency-listings-review.php:83
#: elementor/widgets/single-agency/agency-listings-review.php:92
#: elementor/widgets/single-agency/agency-listings.php:83
#: elementor/widgets/single-agency/agency-listings.php:92
#: elementor/widgets/single-agent/agent-listings-review.php:88
#: elementor/widgets/single-agent/agent-listings-review.php:97
#: elementor/widgets/single-agent/agent-listings.php:88
#: elementor/widgets/single-agent/agent-listings.php:97
#: elementor/widgets/single-property/section-similar.php:121
#: elementor/widgets/single-property/section-similar.php:130
msgid "only for hack"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:597
#: elementor/widgets/properties-recent-viewed.php:110
#: elementor/widgets/property-by-ids.php:147
#: elementor/widgets/single-agency/agency-listings-review.php:122
#: elementor/widgets/single-agency/agency-listings.php:122
#: elementor/widgets/single-agent/agent-listings-review.php:127
#: elementor/widgets/single-agent/agent-listings.php:127
#: elementor/widgets/single-property/section-address.php:83
#: elementor/widgets/single-property/section-details.php:84
#: elementor/widgets/single-property/section-details.php:178
#: elementor/widgets/single-property/section-features.php:82
msgid "2 Columns"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:656
#: elementor/widgets/single-agency/agency-listings-review.php:151
#: elementor/widgets/single-agency/agency-listings.php:151
#: elementor/widgets/single-agent/agent-listings-review.php:156
#: elementor/widgets/single-agent/agent-listings.php:156
#: functions/functions.php:224 functions/helpers.php:302
#: functions/helpers.php:363 shortcodes/properties.php:117
msgid "Load More"
msgstr "Загрузи больше"

#: elementor/widgets/properties-ajax-tabs.php:697
msgid "Title Color Hover"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:708
msgid "Active tab border color"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:722
msgid "Active tab border after color"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:765
#: elementor/widgets/properties-recent-viewed.php:384
#: elementor/widgets/property-by-id.php:163
#: elementor/widgets/property-by-ids.php:177
#: elementor/widgets/property-cards-v1.php:334
#: elementor/widgets/property-cards-v2.php:310
#: elementor/widgets/property-cards-v3.php:217
#: elementor/widgets/property-cards-v4.php:329
#: elementor/widgets/property-cards-v5.php:225
#: elementor/widgets/property-cards-v6.php:212
#: elementor/widgets/property-cards-v7.php:324
#: elementor/widgets/property-cards-v8.php:296
#: elementor/widgets/property-carousel-v1.php:309
#: elementor/widgets/property-carousel-v2.php:286
#: elementor/widgets/property-carousel-v3.php:193
#: elementor/widgets/property-carousel-v5.php:202
#: elementor/widgets/property-carousel-v6.php:184
#: elementor/widgets/property-carousel-v7.php:294
#: elementor/widgets/single-agency/agency-listings-review.php:593
#: elementor/widgets/single-agency/agency-listings.php:393
#: elementor/widgets/single-agent/agent-listings-review.php:598
#: elementor/widgets/single-agent/agent-listings.php:395
#: elementor/widgets/single-property/section-similar.php:340
msgid "Spaces & Sizes"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:773
#: elementor/widgets/properties-recent-viewed.php:392
#: elementor/widgets/property-by-id.php:171
#: elementor/widgets/property-by-ids.php:185
#: elementor/widgets/property-cards-v1.php:342
#: elementor/widgets/property-cards-v2.php:318
#: elementor/widgets/property-cards-v3.php:225
#: elementor/widgets/property-cards-v4.php:337
#: elementor/widgets/property-cards-v5.php:233
#: elementor/widgets/property-cards-v6.php:220
#: elementor/widgets/property-cards-v7.php:332
#: elementor/widgets/property-cards-v8.php:304
#: elementor/widgets/property-carousel-v1.php:317
#: elementor/widgets/property-carousel-v2.php:294
#: elementor/widgets/property-carousel-v3.php:201
#: elementor/widgets/property-carousel-v5.php:210
#: elementor/widgets/property-carousel-v6.php:192
#: elementor/widgets/property-carousel-v7.php:302
#: elementor/widgets/single-agency/agency-listings-review.php:601
#: elementor/widgets/single-agency/agency-listings.php:401
#: elementor/widgets/single-agent/agent-listings-review.php:606
#: elementor/widgets/single-agent/agent-listings.php:403
#: elementor/widgets/single-property/section-similar.php:348
msgid "Title Margin Bottom(px)"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:803
#: elementor/widgets/property-cards-v3.php:255
#: elementor/widgets/property-carousel-v3.php:231
msgid "Labels Margin Bottom(px)"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:824
#: elementor/widgets/properties-recent-viewed.php:422
#: elementor/widgets/property-by-id.php:201
#: elementor/widgets/property-by-ids.php:215
#: elementor/widgets/property-cards-v1.php:372
#: elementor/widgets/property-cards-v2.php:348
#: elementor/widgets/property-cards-v4.php:367
#: elementor/widgets/property-cards-v7.php:362
#: elementor/widgets/property-cards-v8.php:334
#: elementor/widgets/property-carousel-v1.php:347
#: elementor/widgets/property-carousel-v2.php:324
#: elementor/widgets/property-carousel-v7.php:332
#: elementor/widgets/single-agency/agency-listings-review.php:631
#: elementor/widgets/single-agency/agency-listings.php:431
#: elementor/widgets/single-agent/agent-listings-review.php:636
#: elementor/widgets/single-agent/agent-listings.php:433
#: elementor/widgets/single-property/section-similar.php:378
msgid "Address Margin Bottom(px)"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:854
#: elementor/widgets/property-cards-v1.php:402
#: elementor/widgets/property-cards-v2.php:378
#: elementor/widgets/property-cards-v4.php:397
#: elementor/widgets/property-cards-v8.php:364
#: elementor/widgets/property-carousel-v1.php:377
#: elementor/widgets/property-carousel-v2.php:354
msgid "Excerpt Margin Bottom(px)"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:887
#: elementor/widgets/properties-recent-viewed.php:452
#: elementor/widgets/property-by-id.php:231
#: elementor/widgets/property-by-ids.php:245
#: elementor/widgets/property-cards-v1.php:435
#: elementor/widgets/property-cards-v2.php:411
#: elementor/widgets/property-cards-v4.php:430
#: elementor/widgets/property-cards-v5.php:293
#: elementor/widgets/property-cards-v6.php:251
#: elementor/widgets/property-cards-v7.php:392
#: elementor/widgets/property-cards-v8.php:397
#: elementor/widgets/property-carousel-v1.php:410
#: elementor/widgets/property-carousel-v2.php:387
#: elementor/widgets/property-carousel-v5.php:270
#: elementor/widgets/property-carousel-v6.php:223
#: elementor/widgets/property-carousel-v7.php:362
#: elementor/widgets/single-agency/agency-listings-review.php:661
#: elementor/widgets/single-agency/agency-listings.php:461
#: elementor/widgets/single-agent/agent-listings-review.php:666
#: elementor/widgets/single-agent/agent-listings.php:463
#: elementor/widgets/single-property/section-similar.php:408
msgid "Meta Icons Size(px)"
msgstr ""

#: elementor/widgets/properties-ajax-tabs.php:917
#: elementor/widgets/properties-recent-viewed.php:482
#: elementor/widgets/property-by-id.php:261
#: elementor/widgets/property-by-ids.php:275
#: elementor/widgets/property-cards-v1.php:465
#: elementor/widgets/property-cards-v2.php:441
#: elementor/widgets/property-cards-v4.php:460
#: elementor/widgets/property-cards-v5.php:323
#: elementor/widgets/property-cards-v6.php:281
#: elementor/widgets/property-cards-v7.php:422
#: elementor/widgets/property-cards-v8.php:427
#: elementor/widgets/property-carousel-v1.php:440
#: elementor/widgets/property-carousel-v2.php:417
#: elementor/widgets/property-carousel-v5.php:300
#: elementor/widgets/property-carousel-v6.php:253
#: elementor/widgets/property-carousel-v7.php:392
#: elementor/widgets/single-agency/agency-listings-review.php:691
#: elementor/widgets/single-agency/agency-listings.php:491
#: elementor/widgets/single-agent/agent-listings-review.php:696
#: elementor/widgets/single-agent/agent-listings.php:493
#: elementor/widgets/single-property/section-similar.php:438
msgid "Content Area Padding"
msgstr ""

#: elementor/widgets/properties-grids.php:39
#, fuzzy
#| msgid "Properties"
msgid "Property Grids"
msgstr "свойства"

#: elementor/widgets/properties-grids.php:90
#: elementor/widgets/properties-recent-viewed.php:88
#: elementor/widgets/property-by-id.php:88
#: elementor/widgets/property-by-ids.php:88
msgid "Grid Style"
msgstr ""

#: elementor/widgets/properties-grids.php:112
#: elementor/widgets/properties.php:148
#: elementor/widgets/property-cards-v1.php:140
#: elementor/widgets/property-cards-v2.php:137
#: elementor/widgets/property-cards-v3.php:136
#: elementor/widgets/property-cards-v4.php:136
#: elementor/widgets/property-cards-v5.php:136
#: elementor/widgets/property-cards-v6.php:140
#: elementor/widgets/property-cards-v7.php:139
#: elementor/widgets/property-cards-v8.php:122
#: elementor/widgets/property-carousel-v1.php:97
#: elementor/widgets/property-carousel-v2.php:94
#: elementor/widgets/property-carousel-v3.php:93
#: elementor/widgets/property-carousel-v5.php:94
#: elementor/widgets/property-carousel-v6.php:95
#: elementor/widgets/property-carousel-v7.php:97
msgid "Filters"
msgstr ""

#: elementor/widgets/properties-grids.php:124
msgid "Settings"
msgstr "настройки"

#: elementor/widgets/properties-grids.php:132
msgid "Hide Tools"
msgstr ""

#: elementor/widgets/properties-recent-viewed.php:38
#, fuzzy
#| msgid "Most Viewed Properties"
msgid "Recently Viewed Properties"
msgstr "Самые просматриваемые свойства"

#: elementor/widgets/properties-recent-viewed.php:91
#: elementor/widgets/property-by-id.php:91
#: elementor/widgets/property-by-ids.php:91
#, fuzzy
#| msgid "Property ID"
msgid "Property Card v1"
msgstr "ИД объекта"

#: elementor/widgets/properties-recent-viewed.php:92
#: elementor/widgets/property-by-id.php:92
#: elementor/widgets/property-by-ids.php:92
#, fuzzy
#| msgid "Property ID"
msgid "Property Card v2"
msgstr "ИД объекта"

#: elementor/widgets/properties-recent-viewed.php:93
#: elementor/widgets/property-by-id.php:93
#: elementor/widgets/property-by-ids.php:93
#, fuzzy
#| msgid "Property ID"
msgid "Property Card v3"
msgstr "ИД объекта"

#: elementor/widgets/properties-recent-viewed.php:94
#: elementor/widgets/property-by-id.php:95
#: elementor/widgets/property-by-ids.php:94
#, fuzzy
#| msgid "Property ID"
msgid "Property Card v5"
msgstr "ИД объекта"

#: elementor/widgets/properties-recent-viewed.php:95
#: elementor/widgets/property-by-id.php:96
#: elementor/widgets/property-by-ids.php:95
#, fuzzy
#| msgid "Property ID"
msgid "Property Card v6"
msgstr "ИД объекта"

#: elementor/widgets/properties-recent-viewed.php:96
#: elementor/widgets/property-by-id.php:97
#: elementor/widgets/property-by-ids.php:96
#, fuzzy
#| msgid "Property ID"
msgid "Property Card v7"
msgstr "ИД объекта"

#: elementor/widgets/properties-recent-viewed.php:98
#: elementor/widgets/property-by-id.php:100
#: elementor/widgets/property-by-ids.php:99
msgid "Choose grid style, default will be propety card v1"
msgstr ""

#: elementor/widgets/properties-recent-viewed.php:119
#, fuzzy
#| msgid "Properties"
msgid "Number of Properties"
msgstr "свойства"

#: elementor/widgets/properties-recent-viewed.php:528
#: elementor/widgets/property-cards-v8.php:473
msgid "Grid Background"
msgstr ""

#: elementor/widgets/properties-recent-viewed.php:578
msgid "Item Tools Background Color Hover"
msgstr ""

#: elementor/widgets/properties-recent-viewed.php:602
msgid "Item Tools Color Hover"
msgstr ""

#: elementor/widgets/properties-recent-viewed.php:689
msgid "Details Button Background Color"
msgstr ""

#: elementor/widgets/properties-recent-viewed.php:701
msgid "Details Button Background Color Hover"
msgstr ""

#: elementor/widgets/properties-recent-viewed.php:713
msgid "Detail Button Color"
msgstr ""

#: elementor/widgets/properties-recent-viewed.php:725
msgid "Detail Button Color Hover"
msgstr ""

#: elementor/widgets/properties-slider.php:37
#, fuzzy
#| msgid "Properties"
msgid "Properties Slider"
msgstr "свойства"

#: elementor/widgets/properties.php:95
msgid ""
"<span style=\"color: red; font-weight: bold;\">DEPRECATED:</span> This "
"widget is deprecated. Please use Property Cards V1 or Property Cards V2 "
"instead."
msgstr ""

#: elementor/widgets/properties.php:103
msgid "Grid/List Style"
msgstr ""

#: elementor/widgets/properties.php:109
msgid "Choose grid/list style, default will be version 1"
msgstr ""

#: elementor/widgets/properties.php:120
#: elementor/widgets/testimonials-v2.php:91
#: elementor/widgets/testimonials.php:91
msgid "Grid 3 Columns"
msgstr ""

#: elementor/widgets/properties.php:121
#: elementor/widgets/testimonials-v2.php:90
msgid "Grid 2 Columns"
msgstr ""

#: elementor/widgets/properties.php:122
#: elementor/widgets/property-cards-v1.php:97
#: elementor/widgets/property-cards-v2.php:95
#: elementor/widgets/property-cards-v7.php:96
#, fuzzy
#| msgid "Most Viewed"
msgid "List View"
msgstr "Наиболее просматриваемые"

#: elementor/widgets/property-by-id.php:38
#, fuzzy
#| msgid "Property ID"
msgid "Property by ID"
msgstr "ИД объекта"

#: elementor/widgets/property-by-id.php:94
#, fuzzy
#| msgid "Property ID"
msgid "Property Card v4"
msgstr "ИД объекта"

#: elementor/widgets/property-by-id.php:98
#: elementor/widgets/property-by-ids.php:97
#, fuzzy
#| msgid "Property ID"
msgid "Property Card v8"
msgstr "ИД объекта"

#: elementor/widgets/property-by-id.php:138
msgid "Enter property ID. Ex 305"
msgstr ""

#: elementor/widgets/property-by-ids.php:38
#, fuzzy
#| msgid "Property ID"
msgid "Property by IDs"
msgstr "ИД объекта"

#: elementor/widgets/property-by-ids.php:149
msgid "Note: Property card v4 will show only 2 columns"
msgstr ""

#: elementor/widgets/property-cards-v1.php:40
#, fuzzy
#| msgid "Property Status Slug"
msgid "Property Cards v1"
msgstr "Статус свойства Slug"

#: elementor/widgets/property-cards-v1.php:94
#: elementor/widgets/property-cards-v2.php:93
#: elementor/widgets/property-cards-v3.php:93
#: elementor/widgets/property-cards-v4.php:94
#: elementor/widgets/property-cards-v5.php:93
#: elementor/widgets/property-cards-v6.php:95
#: elementor/widgets/property-cards-v7.php:94
#: elementor/widgets/single-agency/agency-listings-review.php:105
#: elementor/widgets/single-agency/agency-listings.php:105
#: elementor/widgets/single-agent/agent-listings-review.php:110
#: elementor/widgets/single-agent/agent-listings.php:110
#, fuzzy
#| msgid "Views Count"
msgid "Grid View 3 Columns"
msgstr "Количество просмотров"

#: elementor/widgets/property-cards-v1.php:95
#: elementor/widgets/property-cards-v6.php:96
#: elementor/widgets/property-cards-v7.php:95
#: elementor/widgets/single-agency/agency-listings-review.php:106
#: elementor/widgets/single-agency/agency-listings.php:106
#: elementor/widgets/single-agent/agent-listings-review.php:111
#: elementor/widgets/single-agent/agent-listings.php:111
#, fuzzy
#| msgid "Views Count"
msgid "Grid View 4 Columns"
msgstr "Количество просмотров"

#: elementor/widgets/property-cards-v1.php:96
#: elementor/widgets/property-cards-v2.php:94
#: elementor/widgets/property-cards-v3.php:94
#: elementor/widgets/property-cards-v4.php:93
#: elementor/widgets/property-cards-v5.php:94
#: elementor/widgets/property-cards-v6.php:94
#: elementor/widgets/property-cards-v7.php:93
#: elementor/widgets/single-agency/agency-listings-review.php:104
#: elementor/widgets/single-agency/agency-listings.php:104
#: elementor/widgets/single-agent/agent-listings-review.php:109
#: elementor/widgets/single-agent/agent-listings.php:109
#, fuzzy
#| msgid "Views Count"
msgid "Grid View 2 Columns"
msgstr "Количество просмотров"

#: elementor/widgets/property-cards-v1.php:123
#: elementor/widgets/property-cards-v2.php:121
#: elementor/widgets/property-cards-v3.php:120
#: elementor/widgets/property-cards-v4.php:120
#: elementor/widgets/property-cards-v5.php:120
#: elementor/widgets/property-cards-v6.php:124
#: elementor/widgets/property-cards-v7.php:122
#: elementor/widgets/property-cards-v8.php:106
msgid "Warning"
msgstr ""

#: elementor/widgets/property-cards-v1.php:124
#: elementor/widgets/property-cards-v2.php:122
#: elementor/widgets/property-cards-v3.php:121
#: elementor/widgets/property-cards-v4.php:121
#: elementor/widgets/property-cards-v5.php:121
#: elementor/widgets/property-cards-v6.php:125
#: elementor/widgets/property-cards-v7.php:123
#: elementor/widgets/property-cards-v8.php:107
msgid ""
"Infinite Scroll works with only one widget per page; enabling it for "
"multiple widgets on the same page will cause issues."
msgstr ""

#: elementor/widgets/property-cards-v1.php:166
#: elementor/widgets/property-cards-v2.php:162
#: elementor/widgets/property-cards-v4.php:161
#: elementor/widgets/property-cards-v8.php:146
#: elementor/widgets/property-carousel-v1.php:141
#: elementor/widgets/property-carousel-v2.php:138
msgid "Hide Excerpt"
msgstr ""

#: elementor/widgets/property-cards-v1.php:514
msgid "Box Footer Border Color"
msgstr ""

#: elementor/widgets/property-cards-v2.php:40
#, fuzzy
#| msgid "Property Status Slug"
msgid "Property Cards v2"
msgstr "Статус свойства Slug"

#: elementor/widgets/property-cards-v3.php:39
#, fuzzy
#| msgid "Property Status Slug"
msgid "Property Cards v3"
msgstr "Статус свойства Slug"

#: elementor/widgets/property-cards-v4.php:39
#, fuzzy
#| msgid "Property Status Slug"
msgid "Property Cards v4"
msgstr "Статус свойства Slug"

#: elementor/widgets/property-cards-v4.php:509
#: elementor/widgets/property-carousel-v1.php:489
msgid "Bos Footer Border Color"
msgstr ""

#: elementor/widgets/property-cards-v5.php:39
#, fuzzy
#| msgid "Property Status Slug"
msgid "Property Cards v5"
msgstr "Статус свойства Slug"

#: elementor/widgets/property-cards-v5.php:263
#: elementor/widgets/property-carousel-v5.php:240
msgid "Price Margin Bottom(px)"
msgstr ""

#: elementor/widgets/property-cards-v6.php:40
#, fuzzy
#| msgid "Property Status Slug"
msgid "Property Cards v6"
msgstr "Статус свойства Slug"

#: elementor/widgets/property-cards-v7.php:39
#, fuzzy
#| msgid "Property Status Slug"
msgid "Property Cards v7"
msgstr "Статус свойства Slug"

#: elementor/widgets/property-cards-v7.php:165
#: elementor/widgets/property-carousel-v7.php:141
#, fuzzy
#| msgid "Date"
msgid "Hide Date"
msgstr "Дата"

#: elementor/widgets/property-cards-v7.php:183
#: elementor/widgets/property-cards-v8.php:163
#: elementor/widgets/property-carousel-v7.php:159
#, fuzzy
#| msgid "Author"
msgid "Hide Author"
msgstr "автор"

#: elementor/widgets/property-cards-v8.php:39
#, fuzzy
#| msgid "Property Status Slug"
msgid "Property Cards v8"
msgstr "Статус свойства Slug"

#: elementor/widgets/property-cards-v8.php:485
msgid "Grid Footer Background"
msgstr ""

#: elementor/widgets/property-cards-v8.php:622
#: elementor/widgets/single-property/item-tools.php:110
#: elementor/widgets/single-property/property-title-area.php:557
#: elementor/widgets/single-property/section-toparea-v1.php:483
#: elementor/widgets/single-property/section-toparea-v2.php:482
#: elementor/widgets/single-property/section-toparea-v3.php:507
#: elementor/widgets/single-property/section-toparea-v5.php:482
#: elementor/widgets/single-property/section-toparea-v6.php:527
#: elementor/widgets/single-property/section-toparea-v7.php:502
msgid "Background Color Hover"
msgstr ""

#: elementor/widgets/property-carousel-v1.php:39
msgid "Property Cards Carousel v1"
msgstr ""

#: elementor/widgets/property-carousel-v1.php:109
#: elementor/widgets/property-carousel-v2.php:106
#: elementor/widgets/property-carousel-v3.php:105
#: elementor/widgets/property-carousel-v5.php:106
#: elementor/widgets/property-carousel-v6.php:107
#: elementor/widgets/property-carousel-v7.php:109
msgid "All - Button Text"
msgstr ""

#: elementor/widgets/property-carousel-v1.php:117
#: elementor/widgets/property-carousel-v2.php:114
#: elementor/widgets/property-carousel-v3.php:113
#: elementor/widgets/property-carousel-v5.php:114
#: elementor/widgets/property-carousel-v6.php:115
#: elementor/widgets/property-carousel-v7.php:117
msgid "All - Button URL"
msgstr ""

#: elementor/widgets/property-carousel-v2.php:40
msgid "Property Cards Carousel v2"
msgstr ""

#: elementor/widgets/property-carousel-v3.php:39
msgid "Property Cards Carousel v3"
msgstr ""

#: elementor/widgets/property-carousel-v5.php:39
msgid "Property Cards Carousel v5"
msgstr ""

#: elementor/widgets/property-carousel-v6.php:39
msgid "Property Cards Carousel v6"
msgstr ""

#: elementor/widgets/property-carousel-v7.php:39
msgid "Property Cards Carousel v7"
msgstr ""

#: elementor/widgets/property-meta-data.php:16
#, fuzzy
#| msgid "Property ID"
msgid "Property Meta Data"
msgstr "ИД объекта"

#: elementor/widgets/property-meta-data.php:41
#: elementor/widgets/search-builder.php:85
#: elementor/widgets/single-property/section-details.php:276
#: elementor/widgets/single-property/section-overview.php:50
#, fuzzy
#| msgid "Bedrooms"
msgid "Rooms"
msgstr "спальни"

#: elementor/widgets/property-meta-data.php:42
#: elementor/widgets/search-builder.php:84
#: elementor/widgets/search-builder.php:499
#: elementor/widgets/search-builder.php:500
#: elementor/widgets/single-property/section-details.php:259
#: elementor/widgets/single-property/section-overview-v2.php:51
#: elementor/widgets/single-property/section-overview-v2.php:201
#: elementor/widgets/single-property/section-overview.php:49
#: elementor/widgets/single-property/section-overview.php:200
#: statistics/houzez-statistics.php:494
msgid "Bedrooms"
msgstr "спальни"

#: elementor/widgets/property-meta-data.php:43
#: elementor/widgets/search-builder.php:86
#: elementor/widgets/search-builder.php:507
#: elementor/widgets/search-builder.php:508
#: elementor/widgets/single-property/section-details.php:293
#: elementor/widgets/single-property/section-overview-v2.php:52
#: elementor/widgets/single-property/section-overview-v2.php:208
#: elementor/widgets/single-property/section-overview.php:51
#: elementor/widgets/single-property/section-overview.php:207
#: statistics/houzez-statistics.php:499
msgid "Bathrooms"
msgstr "В ванных комнатах"

#: elementor/widgets/property-meta-data.php:44
#: elementor/widgets/single-property/section-overview-v2.php:53
#: elementor/widgets/single-property/section-overview-v2.php:221
#: elementor/widgets/single-property/section-overview.php:52
#: elementor/widgets/single-property/section-overview.php:220
#, fuzzy
#| msgid "Max Size"
msgid "Area Size"
msgstr "Максимальный размер"

#: elementor/widgets/property-meta-data.php:45
#: elementor/widgets/single-property/section-details.php:242
#: elementor/widgets/single-property/section-overview-v2.php:54
#: elementor/widgets/single-property/section-overview.php:53
#, fuzzy
#| msgid "Area"
msgid "Land Area"
msgstr "Площадь"

#: elementor/widgets/property-meta-data.php:46
#: elementor/widgets/single-property/section-details.php:310
#: elementor/widgets/single-property/section-overview-v2.php:55
#: elementor/widgets/single-property/section-overview-v2.php:215
#: elementor/widgets/single-property/section-overview.php:54
#: elementor/widgets/single-property/section-overview.php:214
#, fuzzy
#| msgid "Packages"
msgid "Garages"
msgstr "пакеты"

#: elementor/widgets/property-meta-data.php:47
#: elementor/widgets/single-property/section-overview-v2.php:56
#: elementor/widgets/single-property/section-overview.php:55
msgid "Built Year"
msgstr ""

#: elementor/widgets/property-meta-data.php:69
#, fuzzy
#| msgid "Property"
msgid "Property Meta"
msgstr "Имущество"

#: elementor/widgets/property-meta-data.php:77
msgid "Meta Layout"
msgstr ""

#: elementor/widgets/property-meta-data.php:80
msgid "Layout v1"
msgstr ""

#: elementor/widgets/property-meta-data.php:81
msgid "Layout v2"
msgstr ""

#: elementor/widgets/property-meta-data.php:82
msgid "Layout v3"
msgstr ""

#: elementor/widgets/property-meta-data.php:83
msgid "Layout v4"
msgstr ""

#: elementor/widgets/property-meta-data.php:92
msgid "Select Field"
msgstr ""

#: elementor/widgets/property-meta-data.php:123
#: elementor/widgets/single-property/section-overview-v2.php:92
#: elementor/widgets/single-property/section-overview.php:91
msgid "Label Plural"
msgstr ""

#: elementor/widgets/property-meta-data.php:148
#: elementor/widgets/single-property/section-overview-v2.php:117
#: elementor/widgets/single-property/section-overview.php:116
msgid "Icons From"
msgstr ""

#: elementor/widgets/property-meta-data.php:151
#: elementor/widgets/single-property/section-overview-v2.php:120
#: elementor/widgets/single-property/section-overview.php:119
msgid "Theme Options "
msgstr ""

#: elementor/widgets/property-meta-data.php:153
#: elementor/widgets/single-agency/agency-call-btn.php:77
#: elementor/widgets/single-agency/agency-contact-btn.php:65
#: elementor/widgets/single-agency/agency-line-btn.php:65
#: elementor/widgets/single-agency/agency-telegram-btn.php:65
#: elementor/widgets/single-agency/agency-whatsapp-btn.php:65
#: elementor/widgets/single-agent/agent-call-btn.php:77
#: elementor/widgets/single-agent/agent-contact-btn.php:65
#: elementor/widgets/single-agent/agent-line-btn.php:65
#: elementor/widgets/single-agent/agent-telegram-btn.php:65
#: elementor/widgets/single-agent/agent-whatsapp-btn.php:65
#: elementor/widgets/single-property/section-overview-v2.php:122
#: elementor/widgets/single-property/section-overview.php:121
#, fuzzy
#| msgid "No Invoice found"
msgid "No Icon"
msgstr "Нет счета-фактуры"

#: elementor/widgets/property-meta-data.php:165
#: elementor/widgets/single-agency/agency-meta.php:195
#: elementor/widgets/single-agent/agent-meta.php:197
#: elementor/widgets/single-property/section-overview-v2.php:131
#: elementor/widgets/single-property/section-overview.php:130
msgid "upload Icon"
msgstr ""

#: elementor/widgets/property-meta-data.php:263
#: elementor/widgets/property-meta-data.php:291
#: elementor/widgets/property-meta-data.php:351
#: elementor/widgets/single-post/author-box.php:304
#: elementor/widgets/single-post/author-box.php:535
#: elementor/widgets/single-post/author-box.php:622
#: elementor/widgets/single-property/section-block-gallery.php:89
#: elementor/widgets/taxonomies-list.php:468
msgid "Gap"
msgstr ""

#: elementor/widgets/property-meta-data.php:283
#: elementor/widgets/single-agency/agency-meta.php:320
#: elementor/widgets/single-agent/agent-meta.php:321
msgid "Meta Value"
msgstr ""

#: elementor/widgets/search-builder.php:37
#, fuzzy
#| msgid "Search Query"
msgid "Search Builder"
msgstr "Поисковый запрос"

#: elementor/widgets/search-builder.php:80
msgid "Keyword"
msgstr ""

#: elementor/widgets/search-builder.php:92
#, fuzzy
#| msgid "Min Price"
msgid "Min Area"
msgstr "Минимальная цена"

#: elementor/widgets/search-builder.php:93
#, fuzzy
#| msgid "Max Price"
msgid "Max Area"
msgstr "Макс. Цена"

#: elementor/widgets/search-builder.php:94
msgid "Min Land Area"
msgstr ""

#: elementor/widgets/search-builder.php:95
msgid "Max Land Area"
msgstr ""

#: elementor/widgets/search-builder.php:96 statistics/houzez-statistics.php:578
msgid "Min Price"
msgstr "Минимальная цена"

#: elementor/widgets/search-builder.php:97 statistics/houzez-statistics.php:579
msgid "Max Price"
msgstr "Макс. Цена"

#: elementor/widgets/search-builder.php:98
msgid "Price Range Slider"
msgstr ""

#: elementor/widgets/search-builder.php:101
#: elementor/widgets/single-property/section-details.php:302
#: elementor/widgets/single-property/section-overview-v2.php:214
#: elementor/widgets/single-property/section-overview.php:213
msgid "Garage"
msgstr ""

#: elementor/widgets/search-builder.php:102
#: elementor/widgets/single-property/section-details.php:328
#: elementor/widgets/single-property/section-overview-v2.php:228
#: elementor/widgets/single-property/section-overview.php:227
msgid "Year Built"
msgstr ""

#: elementor/widgets/search-builder.php:103
#, fuzzy
#| msgid "Location"
msgid "Geo Location"
msgstr "Место нахождения"

#: elementor/widgets/search-builder.php:114
#: elementor/widgets/search-builder.php:554
#, fuzzy
#| msgid "Search Partner"
msgid "Search Button"
msgstr "Поиск партнера"

#: elementor/widgets/search-builder.php:122
msgid "Search button settings are below under \"Search Button\" section"
msgstr ""

#: elementor/widgets/search-builder.php:172
#: elementor/widgets/search-builder.php:208
msgid "From"
msgstr ""

#: elementor/widgets/search-builder.php:196
msgid "Default Radius"
msgstr ""

#: elementor/widgets/search-builder.php:279
#, fuzzy
#| msgid "New Type"
msgid "Field Type"
msgstr "Новый тип"

#: elementor/widgets/search-builder.php:284
msgid "Input"
msgstr ""

#: elementor/widgets/search-builder.php:304
#, fuzzy
#| msgid "Saved Searches"
msgid "Data Live Search"
msgstr "Сохраненные поиски"

#: elementor/widgets/search-builder.php:334
msgid "Slider Width(%)"
msgstr ""

#: elementor/widgets/search-builder.php:357
msgid "Multi Selection"
msgstr ""

#: elementor/widgets/search-builder.php:383
msgid "Select/Deselect All"
msgstr ""

#: elementor/widgets/search-builder.php:396
msgid "Selected Items Text"
msgstr ""

#: elementor/widgets/search-builder.php:408
msgid "Responsive"
msgstr ""

#: elementor/widgets/search-builder.php:419
msgid ""
"Responsive visibility will take effect only on preview or live page, and not "
"while editing in Elementor."
msgstr ""

#: elementor/widgets/search-builder.php:427
msgid "Hide On Desktop"
msgstr ""

#: elementor/widgets/search-builder.php:437
msgid "Hide On Tablet"
msgstr ""

#: elementor/widgets/search-builder.php:447
#, fuzzy
#| msgid "Mobile"
msgid "Hide On Mobile"
msgstr "мобильный"

#: elementor/widgets/search-builder.php:515
#: elementor/widgets/search-builder.php:516
#, fuzzy
#| msgid "Min Price"
msgid "Min. Price"
msgstr "Минимальная цена"

#: elementor/widgets/search-builder.php:523
#: elementor/widgets/search-builder.php:524
#, fuzzy
#| msgid "Max Price"
msgid "Max. Price"
msgstr "Макс. Цена"

#: elementor/widgets/search-builder.php:531
#: elementor/widgets/search-builder.php:532
#, fuzzy
#| msgid "Area"
msgid "Min. Area"
msgstr "Площадь"

#: elementor/widgets/search-builder.php:539
#: elementor/widgets/search-builder.php:540
#, fuzzy
#| msgid "Area"
msgid "Max. Area"
msgstr "Площадь"

#: elementor/widgets/search-builder.php:583
msgid "Button Size"
msgstr ""

#: elementor/widgets/search-builder.php:600
#: elementor/widgets/single-agency/agency-call-btn.php:72
#: elementor/widgets/single-agency/agency-contact-btn.php:60
#: elementor/widgets/single-agency/agency-line-btn.php:60
#: elementor/widgets/single-agency/agency-telegram-btn.php:60
#: elementor/widgets/single-agency/agency-whatsapp-btn.php:60
#: elementor/widgets/single-agent/agent-call-btn.php:72
#: elementor/widgets/single-agent/agent-contact-btn.php:60
#: elementor/widgets/single-agent/agent-line-btn.php:60
#: elementor/widgets/single-agent/agent-telegram-btn.php:60
#: elementor/widgets/single-agent/agent-whatsapp-btn.php:60
msgid "Button Icon"
msgstr ""

#: elementor/widgets/search-builder.php:612
msgid "Button Alignment"
msgstr ""

#: elementor/widgets/search-builder.php:670
msgid "Show Tabs"
msgstr ""

#: elementor/widgets/search-builder.php:763
msgid "Show All Text"
msgstr ""

#: elementor/widgets/search-builder.php:944
#: elementor/widgets/single-agency/agency-contact-form.php:149
#: elementor/widgets/single-agency/agency-review.php:51
#: elementor/widgets/single-agency/agency-search.php:196
#: elementor/widgets/single-agent/agent-contact-form.php:149
#: elementor/widgets/single-agent/agent-review.php:51
#: elementor/widgets/single-agent/agent-search.php:196
#: elementor/widgets/single-property/section-contact-2.php:588
#: elementor/widgets/single-property/section-contact-bottom.php:468
#: elementor/widgets/single-property/section-review.php:51
#: elementor/widgets/single-property/section-schedule-tour-v2.php:288
#: elementor/widgets/single-property/section-schedule-tour.php:132
msgid "Fields"
msgstr ""

#: elementor/widgets/search-builder.php:1035
msgid "Price Slider"
msgstr ""

#: elementor/widgets/section-title.php:96
#, fuzzy
#| msgid "Title"
msgid "Sub Title"
msgstr "заглавие"

#: elementor/widgets/section-title.php:135
msgid "Section Subtitle "
msgstr ""

#: elementor/widgets/section-title.php:181
#, fuzzy
#| msgid "Payment Title"
msgid "Main Title"
msgstr "Название платежа"

#: elementor/widgets/section-title.php:205
msgid "Main Title Margin Bottom"
msgstr ""

#: elementor/widgets/section-title.php:235
msgid "Subtitle Margin Bottom"
msgstr ""

#: elementor/widgets/section-title.php:265
msgid "Section Margin Bottom"
msgstr ""

#: elementor/widgets/sidebar/code-banner.php:16
#: elementor/widgets/sidebar/code-banner.php:37
#: elementor/widgets/sidebar/code-banner.php:65
msgid "Code Banner"
msgstr ""

#: elementor/widgets/sidebar/code-banner.php:45
msgid "Ads Code"
msgstr ""

#: elementor/widgets/sidebar/code-banner.php:54
msgid "JS or Google AdSense Code"
msgstr ""

#: elementor/widgets/sidebar/code-banner.php:56
msgid "Paste your code here..."
msgstr ""

#: elementor/widgets/single-agency/agency-about.php:19
#, fuzzy
#| msgid "Search Agency"
msgid "Section Agency Bio"
msgstr "Поисковое агентство"

#: elementor/widgets/single-agency/agency-about.php:180
#: elementor/widgets/single-agency/agency-contact.php:244
#: elementor/widgets/single-agency/agency-search.php:166
#: elementor/widgets/single-agency/agency-stats.php:145
#: elementor/widgets/single-agent/agent-about.php:180
#: elementor/widgets/single-agent/agent-contact.php:244
#: elementor/widgets/single-agent/agent-search.php:166
#: elementor/widgets/single-agent/agent-stats.php:145
msgid "Margin Button"
msgstr ""

#: elementor/widgets/single-agency/agency-address.php:16
#: elementor/widgets/single-agency/agency-address.php:41
#, fuzzy
#| msgid "Agencies"
msgid "Agency Address"
msgstr "агентства"

#: elementor/widgets/single-agency/agency-agents.php:19
#, fuzzy
#| msgid "Agencies"
msgid "Agency Agents"
msgstr "агентства"

#: elementor/widgets/single-agency/agency-call-btn.php:17
msgid "Agency Call Button"
msgstr ""

#: elementor/widgets/single-agency/agency-call-btn.php:42
#: elementor/widgets/single-agency/agency-call-btn.php:165
#: elementor/widgets/single-agency/agency-profile-v1.php:410
#: elementor/widgets/single-agent/agent-call-btn.php:42
#: elementor/widgets/single-agent/agent-call-btn.php:165
#: elementor/widgets/single-agent/agent-profile-v1.php:422
#: elementor/widgets/single-property/section-contact-2.php:612
msgid "Call Button"
msgstr ""

#: elementor/widgets/single-agency/agency-call-btn.php:50
#: elementor/widgets/single-agency/agency-contact-btn.php:50
#: elementor/widgets/single-agency/agency-line-btn.php:50
#: elementor/widgets/single-agency/agency-telegram-btn.php:50
#: elementor/widgets/single-agency/agency-whatsapp-btn.php:50
#: elementor/widgets/single-agent/agent-call-btn.php:50
#: elementor/widgets/single-agent/agent-contact-btn.php:50
#: elementor/widgets/single-agent/agent-line-btn.php:50
#: elementor/widgets/single-agent/agent-telegram-btn.php:50
#: elementor/widgets/single-agent/agent-whatsapp-btn.php:50
#, fuzzy
#| msgid "Labels"
msgid "label"
msgstr "Этикетки"

#: elementor/widgets/single-agency/agency-call-btn.php:53
#: elementor/widgets/single-agency/agency-line-btn.php:53
#: elementor/widgets/single-agency/agency-telegram-btn.php:53
#: elementor/widgets/single-agency/agency-whatsapp-btn.php:53
#: elementor/widgets/single-agent/agent-call-btn.php:53
#: elementor/widgets/single-agent/agent-line-btn.php:53
#: elementor/widgets/single-agent/agent-telegram-btn.php:53
#: elementor/widgets/single-agent/agent-whatsapp-btn.php:53
#, fuzzy
#| msgid "Add New Label"
msgid "Add label"
msgstr "Добавить новую метку"

#: elementor/widgets/single-agency/agency-call-btn.php:60
#: elementor/widgets/single-agent/agent-call-btn.php:60
msgid "Show Number as Text"
msgstr ""

#: elementor/widgets/single-agency/agency-call-btn.php:134
#: elementor/widgets/single-agency/agency-contact-btn.php:122
#: elementor/widgets/single-agency/agency-line-btn.php:122
#: elementor/widgets/single-agency/agency-telegram-btn.php:122
#: elementor/widgets/single-agency/agency-whatsapp-btn.php:122
#: elementor/widgets/single-agent/agent-call-btn.php:134
#: elementor/widgets/single-agent/agent-contact-btn.php:122
#: elementor/widgets/single-agent/agent-line-btn.php:122
#: elementor/widgets/single-agent/agent-telegram-btn.php:122
#: elementor/widgets/single-agent/agent-whatsapp-btn.php:122
msgid "Icon Spacing"
msgstr ""

#: elementor/widgets/single-agency/agency-contact-btn.php:17
msgid "Agency Contact Button"
msgstr ""

#: elementor/widgets/single-agency/agency-contact-btn.php:42
#: elementor/widgets/single-agency/agency-contact-btn.php:153
#: elementor/widgets/single-agent/agent-contact-btn.php:42
#: elementor/widgets/single-agent/agent-contact-btn.php:153
msgid "Contact Button"
msgstr ""

#: elementor/widgets/single-agency/agency-contact-btn.php:53
#: elementor/widgets/single-agent/agent-contact-btn.php:53
#, fuzzy
#| msgid "Add New"
msgid "Add title"
msgstr "Добавить новое"

#: elementor/widgets/single-agency/agency-contact-form.php:21
msgid "Agency Contact Form"
msgstr ""

#: elementor/widgets/single-agency/agency-contact-form.php:55
#, fuzzy
#| msgid "Agency ID"
msgid "Agency Detail"
msgstr "ID агентства"

#: elementor/widgets/single-agency/agency-contact-form.php:122
#: elementor/widgets/single-agent/agent-contact-form.php:55
#: elementor/widgets/single-agent/agent-contact-form.php:122
#: elementor/widgets/single-property/section-contact-2.php:498
#: elementor/widgets/single-property/section-contact-bottom.php:365
#, fuzzy
#| msgid "Agent ID"
msgid "Agent Detail"
msgstr "Агент ID"

#: elementor/widgets/single-agency/agency-contact-form.php:173
#: elementor/widgets/single-agent/agent-contact-form.php:173
#: elementor/widgets/single-property/section-contact-2.php:636
#: elementor/widgets/single-property/section-contact-bottom.php:609
#: elementor/widgets/single-property/section-schedule-tour-v2.php:312
#: elementor/widgets/single-property/section-schedule-tour.php:250
msgid "Terms of use"
msgstr ""

#: elementor/widgets/single-agency/agency-contact.php:19
#, fuzzy
#| msgid "Agency ID"
msgid "Agency Contact"
msgstr "ID агентства"

#: elementor/widgets/single-agency/agency-contact.php:52
#: elementor/widgets/single-agent/agent-contact.php:52
#: elementor/widgets/single-property/property-title-area.php:204
#: elementor/widgets/single-property/section-toparea-v1.php:106
#: elementor/widgets/single-property/section-toparea-v2.php:106
#: elementor/widgets/single-property/section-toparea-v3.php:131
#: elementor/widgets/single-property/section-toparea-v5.php:106
#: elementor/widgets/single-property/section-toparea-v6.php:151
#: elementor/widgets/single-property/section-toparea-v7.php:126
#, fuzzy
#| msgid "Title"
msgid "Show Title"
msgstr "заглавие"

#: elementor/widgets/single-agency/agency-contact.php:64
#: elementor/widgets/single-agent/agent-contact.php:64
msgid "Map"
msgstr ""

#: elementor/widgets/single-agency/agency-contact.php:112
#: elementor/widgets/single-agent/agent-contact.php:112
msgid "Fax"
msgstr ""

#: elementor/widgets/single-agency/agency-contact.php:136
#: elementor/widgets/single-agency/agency-meta.php:62
#: elementor/widgets/single-agent/agent-contact.php:136
#: elementor/widgets/single-agent/agent-meta.php:64
#: elementor/widgets/single-post/author-box.php:122
msgid "Website"
msgstr ""

#: elementor/widgets/single-agency/agency-contact.php:148
#: elementor/widgets/single-agent/agent-contact.php:148
msgid "Social Links"
msgstr ""

#: elementor/widgets/single-agency/agency-contact.php:340
#: elementor/widgets/single-agent/agent-contact.php:340
msgid "Contact Info"
msgstr ""

#: elementor/widgets/single-agency/agency-contact.php:369
#: elementor/widgets/single-agent/agent-contact.php:369
msgid "Values Typography"
msgstr ""

#: elementor/widgets/single-agency/agency-contact.php:378
#: elementor/widgets/single-agent/agent-contact.php:378
#: elementor/widgets/single-property/section-address.php:92
#: elementor/widgets/single-property/section-details.php:93
#: elementor/widgets/single-property/section-details.php:187
#: elementor/widgets/single-property/section-energy.php:319
msgid "Line Height"
msgstr ""

#: elementor/widgets/single-agency/agency-contact.php:406
#: elementor/widgets/single-agent/agent-contact.php:406
msgid "Social"
msgstr ""

#: elementor/widgets/single-agency/agency-contact.php:452
#: elementor/widgets/single-agent/agent-contact.php:451
msgid "Contact"
msgstr ""

#: elementor/widgets/single-agency/agency-contact.php:470
#: elementor/widgets/single-agency/agency-map.php:76
#: elementor/widgets/single-agent/agent-contact.php:469
#: elementor/widgets/single-agent/agent-map.php:76
#: elementor/widgets/single-property/section-google-map.php:177
#: elementor/widgets/single-property/section-map.php:139
#: elementor/widgets/single-property/section-open-street-map.php:153
msgid "Map will show here on frontend"
msgstr ""

#: elementor/widgets/single-agency/agency-content.php:16
#, fuzzy
#| msgid "Agency ID"
msgid "Agency Content"
msgstr "ID агентства"

#: elementor/widgets/single-agency/agency-excerpt.php:16
#, fuzzy
#| msgid "Agency ID"
msgid "Agency Excerpt"
msgstr "ID агентства"

#: elementor/widgets/single-agency/agency-image.php:17
#: elementor/widgets/single-agency/agency-image.php:42
#, fuzzy
#| msgid "Picture"
msgid "Agency Picture"
msgstr "Картина"

#: elementor/widgets/single-agency/agency-line-btn.php:17
msgid "Agency LINE Button"
msgstr ""

#: elementor/widgets/single-agency/agency-line-btn.php:42
#: elementor/widgets/single-agency/agency-line-btn.php:153
#: elementor/widgets/single-agent/agent-line-btn.php:42
#: elementor/widgets/single-agent/agent-line-btn.php:153
msgid "Line Button"
msgstr ""

#: elementor/widgets/single-agency/agency-listings-review.php:21
msgid "Agency Listings & Reviews Tabs"
msgstr ""

#: elementor/widgets/single-agency/agency-listings-review.php:60
#: elementor/widgets/single-agency/agency-listings.php:60
#: elementor/widgets/single-agent/agent-listings-review.php:65
#: elementor/widgets/single-agent/agent-listings.php:65
#: elementor/widgets/single-property/section-similar.php:99
msgid "Listings Layout"
msgstr ""

#: elementor/widgets/single-agency/agency-listings-review.php:119
#: elementor/widgets/single-agency/agency-listings.php:119
#: elementor/widgets/single-agent/agent-listings-review.php:124
#: elementor/widgets/single-agent/agent-listings.php:124
msgid "Grid Columns"
msgstr ""

#: elementor/widgets/single-agency/agency-listings-review.php:163
#: elementor/widgets/single-agency/agency-listings.php:163
#: elementor/widgets/single-agent/agent-listings-review.php:168
#: elementor/widgets/single-agent/agent-listings.php:168
msgid "Show Listing Tabs"
msgstr ""

#: elementor/widgets/single-agency/agency-listings-review.php:260
#: elementor/widgets/single-agent/agent-listings-review.php:265
#, fuzzy
#| msgid "Actions"
msgid "Section Tabs"
msgstr "действия"

#: elementor/widgets/single-agency/agency-listings-review.php:450
#: elementor/widgets/single-agent/agent-listings-review.php:455
msgid "Listing Tabs"
msgstr ""

#: elementor/widgets/single-agency/agency-listings-review.php:559
#: elementor/widgets/single-agent/agent-listings-review.php:564
msgid "Tabs Typography"
msgstr ""

#: elementor/widgets/single-agency/agency-listings.php:21
#, fuzzy
#| msgid "Available Listings"
msgid "Agency Listings"
msgstr "Доступные списки"

#: elementor/widgets/single-agency/agency-map.php:16
msgid "Agency Location Map"
msgstr ""

#: elementor/widgets/single-agency/agency-meta.php:16
#: elementor/widgets/single-agency/agency-meta.php:41
#, fuzzy
#| msgid "Agency"
msgid "Agency Meta"
msgstr "Агентство"

#: elementor/widgets/single-agency/agency-meta.php:49
#: elementor/widgets/single-agent/agent-meta.php:49
msgid "Meta Field"
msgstr ""

#: elementor/widgets/single-agency/agency-meta.php:52
#: elementor/widgets/single-agent/agent-meta.php:52
msgid "Email Address"
msgstr ""

#: elementor/widgets/single-agency/agency-meta.php:53
#: elementor/widgets/single-agency/agency-profile-v1.php:89
#: elementor/widgets/single-agent/agent-meta.php:53
#: elementor/widgets/single-agent/agent-profile-v1.php:101
msgid "Service Areas"
msgstr ""

#: elementor/widgets/single-agency/agency-meta.php:54
#: elementor/widgets/single-agency/agency-profile-v1.php:101
#: elementor/widgets/single-agent/agent-meta.php:54
#: elementor/widgets/single-agent/agent-profile-v1.php:113
msgid "Specialties"
msgstr ""

#: elementor/widgets/single-agency/agency-meta.php:56
#: elementor/widgets/single-agency/agency-profile-v1.php:77
#: elementor/widgets/single-agent/agent-meta.php:58
#: elementor/widgets/single-agent/agent-profile-v1.php:89
msgid "Tax Number"
msgstr ""

#: elementor/widgets/single-agency/agency-meta.php:57
#: elementor/widgets/single-agent/agent-meta.php:59
#, fuzzy
#| msgid "Mobile"
msgid "Mobile Number"
msgstr "мобильный"

#: elementor/widgets/single-agency/agency-meta.php:58
msgid "Phone Number"
msgstr ""

#: elementor/widgets/single-agency/agency-meta.php:59
#: elementor/widgets/single-agent/agent-meta.php:61
msgid "Fax Number"
msgstr ""

#: elementor/widgets/single-agency/agency-meta.php:64
#: elementor/widgets/single-agent/agent-meta.php:66
msgid "LINE ID"
msgstr ""

#: elementor/widgets/single-agency/agency-meta.php:66
#: elementor/widgets/single-agent/agent-meta.php:68
msgid "Zillow"
msgstr ""

#: elementor/widgets/single-agency/agency-meta.php:67
#: elementor/widgets/single-agent/agent-meta.php:69
msgid "Realtor.com"
msgstr ""

#: elementor/widgets/single-agency/agency-meta.php:68
#: elementor/widgets/single-agent/agent-meta.php:70
msgid "Facebook"
msgstr ""

#: elementor/widgets/single-agency/agency-meta.php:69
#: elementor/widgets/single-agent/agent-meta.php:71
msgid "X"
msgstr ""

#: elementor/widgets/single-agency/agency-meta.php:70
#: elementor/widgets/single-agent/agent-meta.php:72
msgid "LinkedIn"
msgstr ""

#: elementor/widgets/single-agency/agency-meta.php:71
#: elementor/widgets/single-agent/agent-meta.php:73
msgid "Google"
msgstr ""

#: elementor/widgets/single-agency/agency-meta.php:72
#: elementor/widgets/single-agent/agent-meta.php:74
msgid "Tiktok"
msgstr ""

#: elementor/widgets/single-agency/agency-meta.php:73
#: elementor/widgets/single-agent/agent-meta.php:75
msgid "Instagram"
msgstr ""

#: elementor/widgets/single-agency/agency-meta.php:74
#: elementor/widgets/single-agent/agent-meta.php:76
msgid "Pinterest"
msgstr ""

#: elementor/widgets/single-agency/agency-meta.php:75
#: elementor/widgets/single-agent/agent-meta.php:77
msgid "Youtube"
msgstr ""

#: elementor/widgets/single-agency/agency-meta.php:76
#: elementor/widgets/single-agent/agent-meta.php:78
msgid "Vimeo"
msgstr ""

#: elementor/widgets/single-agency/agency-meta.php:77
#: elementor/widgets/single-agent/agent-meta.php:79
msgid "Shortcode"
msgstr ""

#: elementor/widgets/single-agency/agency-meta.php:86
#: elementor/widgets/single-agency/agency-meta.php:266
#: elementor/widgets/single-agent/agent-meta.php:88
#: elementor/widgets/single-agent/agent-meta.php:267
#, fuzzy
#| msgid "Payment Title"
msgid "Meta Title"
msgstr "Название платежа"

#: elementor/widgets/single-agency/agency-meta.php:120
#: elementor/widgets/single-agency/agency-name.php:50
#: elementor/widgets/single-agent/agent-meta.php:122
#: elementor/widgets/single-agent/agent-name.php:50
#: elementor/widgets/single-post/author-box.php:99
#: elementor/widgets/single-post/post-title.php:50
#: elementor/widgets/single-property/property-title.php:50
msgid "HTML Tag"
msgstr ""

#: elementor/widgets/single-agency/agency-name.php:17
#: elementor/widgets/single-agency/agency-name.php:42
#, fuzzy
#| msgid "Agent Name"
msgid "Agency Name"
msgstr "Имя агента"

#: elementor/widgets/single-agency/agency-profile-v1.php:19
msgid "Section Agency Profile v1"
msgstr ""

#: elementor/widgets/single-agency/agency-profile-v1.php:113
#: elementor/widgets/single-agent/agent-profile-v1.php:125
#: elementor/widgets/single-property/section-energy.php:282
msgid "Hide Border"
msgstr ""

#: elementor/widgets/single-agency/agency-profile-v1.php:278
#: elementor/widgets/single-agent/agent-profile-v1.php:290
#: elementor/widgets/single-property/section-contact-2.php:600
msgid "Send Email Button"
msgstr ""

#: elementor/widgets/single-agency/agency-profile-v2.php:19
#: elementor/widgets/single-agent/agent-profile-v2.php:19
msgid "Section Agent Profile v2"
msgstr ""

#: elementor/widgets/single-agency/agency-profile-v2.php:212
#: elementor/widgets/single-agent/agent-profile-v2.php:224
msgid "Ask a Question & Phone Number"
msgstr ""

#: elementor/widgets/single-agency/agency-rating.php:16
#: elementor/widgets/single-agency/agency-rating.php:41
#: elementor/widgets/single-agency/agency-rating.php:100
#, fuzzy
#| msgid "Agency ID"
msgid "Agency Rating"
msgstr "ID агентства"

#: elementor/widgets/single-agency/agency-rating.php:49
#: elementor/widgets/single-agent/agent-rating.php:49
#, fuzzy
#| msgid "Views Count"
msgid "Rating Count"
msgstr "Количество просмотров"

#: elementor/widgets/single-agency/agency-rating.php:61
#: elementor/widgets/single-agent/agent-rating.php:61
msgid "Rating Stars"
msgstr ""

#: elementor/widgets/single-agency/agency-rating.php:73
#: elementor/widgets/single-agent/agent-rating.php:73
msgid "See All Review"
msgstr ""

#: elementor/widgets/single-agency/agency-rating.php:85
#: elementor/widgets/single-agent/agent-rating.php:85
msgid "See All Review Text"
msgstr ""

#: elementor/widgets/single-agency/agency-rating.php:108
#: elementor/widgets/single-agent/agent-rating.php:108
#, fuzzy
#| msgid "Max Size"
msgid "Stars Size"
msgstr "Максимальный размер"

#: elementor/widgets/single-agency/agency-rating.php:162
#: elementor/widgets/single-agent/agent-rating.php:162
msgid "Rating Count Margin Right"
msgstr ""

#: elementor/widgets/single-agency/agency-rating.php:187
#: elementor/widgets/single-agent/agent-rating.php:187
msgid "Rating Count Typography"
msgstr ""

#: elementor/widgets/single-agency/agency-rating.php:196
#: elementor/widgets/single-agent/agent-rating.php:196
msgid "Review Text Typography"
msgstr ""

#: elementor/widgets/single-agency/agency-rating.php:204
#: elementor/widgets/single-agent/agent-rating.php:204
msgid "Rating Count Color"
msgstr ""

#: elementor/widgets/single-agency/agency-rating.php:215
#: elementor/widgets/single-agent/agent-rating.php:215
msgid "Review Text Color"
msgstr ""

#: elementor/widgets/single-agency/agency-rating.php:226
#: elementor/widgets/single-agent/agent-rating.php:226
msgid "Review Text Color Hover"
msgstr ""

#: elementor/widgets/single-agency/agency-review.php:21
#, fuzzy
#| msgid "Agencies"
msgid "Agency Reviews"
msgstr "агентства"

#: elementor/widgets/single-agency/agency-review.php:87
#: elementor/widgets/single-agent/agent-review.php:87
#: elementor/widgets/single-property/section-review.php:87
msgid "Leave a Review Button"
msgstr ""

#: elementor/widgets/single-agency/agency-search.php:19
#: elementor/widgets/single-agency/agency-search.php:45
#, fuzzy
#| msgid "Agency ID"
msgid "Agency Search"
msgstr "ID агентства"

#: elementor/widgets/single-agency/agency-single-stats.php:19
msgid "Agency Taxonomy Stats"
msgstr ""

#: elementor/widgets/single-agency/agency-single-stats.php:45
#: elementor/widgets/single-agency/agency-stats.php:45
#, fuzzy
#| msgid "Agency ID"
msgid "Agency Stats"
msgstr "ID агентства"

#: elementor/widgets/single-agency/agency-single-stats.php:62
#: elementor/widgets/single-agent/agent-single-stats.php:62
#: elementor/widgets/single-post/post-info.php:163
msgid "Taxonomy"
msgstr ""

#: elementor/widgets/single-agency/agency-stats.php:19
#, fuzzy
#| msgid "Search Agency"
msgid "Section Agency Stats"
msgstr "Поисковое агентство"

#: elementor/widgets/single-agency/agency-telegram-btn.php:17
msgid "Agency Telegram Button"
msgstr ""

#: elementor/widgets/single-agency/agency-telegram-btn.php:42
#: elementor/widgets/single-agency/agency-telegram-btn.php:153
#: elementor/widgets/single-agent/agent-telegram-btn.php:42
#: elementor/widgets/single-agent/agent-telegram-btn.php:153
msgid "Telegram Button"
msgstr ""

#: elementor/widgets/single-agency/agency-whatsapp-btn.php:17
msgid "Agency WhatsApp Button"
msgstr ""

#: elementor/widgets/single-agency/agency-whatsapp-btn.php:42
#: elementor/widgets/single-agency/agency-whatsapp-btn.php:153
#: elementor/widgets/single-agent/agent-whatsapp-btn.php:42
#: elementor/widgets/single-agent/agent-whatsapp-btn.php:153
#: elementor/widgets/single-property/section-contact-2.php:624
msgid "WhatsApp Button"
msgstr ""

#: elementor/widgets/single-agent/agent-about.php:19
#, fuzzy
#| msgid "Search Agent"
msgid "Section Agent Bio"
msgstr "Агент поиска"

#: elementor/widgets/single-agent/agent-call-btn.php:17
msgid "Agent Call Button"
msgstr ""

#: elementor/widgets/single-agent/agent-contact-btn.php:17
msgid "Agent Contact Button"
msgstr ""

#: elementor/widgets/single-agent/agent-contact-form.php:21
msgid "Agent Contact Form"
msgstr ""

#: elementor/widgets/single-agent/agent-contact.php:19
#, fuzzy
#| msgid "Agent Name"
msgid "Agent Contact"
msgstr "Имя агента"

#: elementor/widgets/single-agent/agent-content.php:16
#, fuzzy
#| msgid "Agent Name"
msgid "Agent Content"
msgstr "Имя агента"

#: elementor/widgets/single-agent/agent-excerpt.php:16
#, fuzzy
#| msgid "Agent Name"
msgid "Agent Excerpt"
msgstr "Имя агента"

#: elementor/widgets/single-agent/agent-image.php:17
#: elementor/widgets/single-agent/agent-image.php:42
#, fuzzy
#| msgid "Picture"
msgid "Agent Picture"
msgstr "Картина"

#: elementor/widgets/single-agent/agent-line-btn.php:17
msgid "Agent LINE Button"
msgstr ""

#: elementor/widgets/single-agent/agent-listings-review.php:26
msgid "Agent Listings & Reviews Tabs"
msgstr ""

#: elementor/widgets/single-agent/agent-listings.php:26
#, fuzzy
#| msgid "Available Listings"
msgid "Agent Listings"
msgstr "Доступные списки"

#: elementor/widgets/single-agent/agent-map.php:16
#, fuzzy
#| msgid "Location"
msgid "Agent Location Map"
msgstr "Место нахождения"

#: elementor/widgets/single-agent/agent-meta.php:16
#: elementor/widgets/single-agent/agent-meta.php:41
#, fuzzy
#| msgid "Agent Name"
msgid "Agent Meta"
msgstr "Имя агента"

#: elementor/widgets/single-agent/agent-meta.php:56
msgid "Company Name"
msgstr ""

#: elementor/widgets/single-agent/agent-meta.php:60
msgid "Office Number"
msgstr ""

#: elementor/widgets/single-agent/agent-position.php:16
#: elementor/widgets/single-agent/agent-position.php:41
#: elementor/widgets/single-agent/agent-profile-v2.php:65
#, fuzzy
#| msgid "Agents"
msgid "Agent Position"
msgstr "Агенты"

#: elementor/widgets/single-agent/agent-position.php:87
msgid "Company Color"
msgstr ""

#: elementor/widgets/single-agent/agent-position.php:98
msgid "Company Color Hover"
msgstr ""

#: elementor/widgets/single-agent/agent-profile-v1.php:19
msgid "Section Agent Profile v1"
msgstr ""

#: elementor/widgets/single-agent/agent-profile-v1.php:53
msgid "Company Logo"
msgstr ""

#: elementor/widgets/single-agent/agent-profile-v2.php:77
#, fuzzy
#| msgid "New Agency"
msgid "Agent Agency"
msgstr "Новое агентство"

#: elementor/widgets/single-agent/agent-rating.php:16
#: elementor/widgets/single-agent/agent-rating.php:41
#: elementor/widgets/single-agent/agent-rating.php:100
#, fuzzy
#| msgid "Agent Name"
msgid "Agent Rating"
msgstr "Имя агента"

#: elementor/widgets/single-agent/agent-review.php:21
#, fuzzy
#| msgid "Agencies"
msgid "Agent Reviews"
msgstr "агентства"

#: elementor/widgets/single-agent/agent-search.php:19
#: elementor/widgets/single-agent/agent-search.php:45
#, fuzzy
#| msgid "Agent Name"
msgid "Agent Search"
msgstr "Имя агента"

#: elementor/widgets/single-agent/agent-single-stats.php:19
msgid "Agent Taxonomy Stats"
msgstr ""

#: elementor/widgets/single-agent/agent-single-stats.php:45
#: elementor/widgets/single-agent/agent-stats.php:45
#, fuzzy
#| msgid "Agents"
msgid "Agent Stats"
msgstr "Агенты"

#: elementor/widgets/single-agent/agent-stats.php:19
msgid "Section Agent Stats"
msgstr ""

#: elementor/widgets/single-agent/agent-telegram-btn.php:17
msgid "Agent Telegram Button"
msgstr ""

#: elementor/widgets/single-agent/agent-whatsapp-btn.php:17
msgid "Agent WhatsApp Button"
msgstr ""

#: elementor/widgets/single-post/author-box.php:26
#, fuzzy
#| msgid "Author"
msgid "Author Box"
msgstr "автор"

#: elementor/widgets/single-post/author-box.php:51
#, fuzzy
#| msgid "Author"
msgid "Author Info"
msgstr "автор"

#: elementor/widgets/single-post/author-box.php:59
#, fuzzy
#| msgid "Picture"
msgid "Profile Picture"
msgstr "Картина"

#: elementor/widgets/single-post/author-box.php:73
#, fuzzy
#| msgid "Picture"
msgid "Picture Size"
msgstr "Картина"

#: elementor/widgets/single-post/author-box.php:85
msgid "Display Name"
msgstr ""

#: elementor/widgets/single-post/author-box.php:123
msgid "Posts Archive"
msgstr ""

#: elementor/widgets/single-post/author-box.php:125
msgid "Link for the Author Name and Image"
msgstr ""

#: elementor/widgets/single-post/author-box.php:132
#: elementor/widgets/single-post/author-box.php:567
msgid "Biography"
msgstr ""

#: elementor/widgets/single-post/author-box.php:146
msgid "Archive Button"
msgstr ""

#: elementor/widgets/single-post/author-box.php:160
msgid "Archive Text"
msgstr ""

#: elementor/widgets/single-post/author-box.php:162
#, fuzzy
#| msgid "Posts"
msgid "All Posts"
msgstr "Сообщений"

#: elementor/widgets/single-post/author-box.php:238
msgid "Vertical Align"
msgstr ""

#: elementor/widgets/single-post/author-box.php:246
msgid "Middle"
msgstr ""

#: elementor/widgets/single-post/author-box.php:939
#: elementor/widgets/single-post/post-info.php:944
#, fuzzy, php-format
#| msgid "Picture"
msgid "Picture of %s"
msgstr "Картина"

#: elementor/widgets/single-post/author-box.php:942
#, fuzzy
#| msgid "Author"
msgid "Author picture"
msgstr "автор"

#: elementor/widgets/single-post/post-comments.php:16
msgid "Post Comments"
msgstr ""

#: elementor/widgets/single-post/post-comments.php:39
#: elementor/widgets/single-post/post-info.php:78
#: elementor/widgets/single-post/post-info.php:241
msgid "Comments"
msgstr ""

#: elementor/widgets/single-post/post-comments.php:53
msgid "Skin"
msgstr ""

#: elementor/widgets/single-post/post-comments.php:56
msgid "Theme Comments"
msgstr ""

#: elementor/widgets/single-post/post-comments.php:58
msgid ""
"The Theme Comments skin uses the currently active theme comments design and "
"layout to display the comment form and comments."
msgstr ""

#: elementor/widgets/single-post/post-comments.php:79
msgid "Comments are closed."
msgstr ""

#: elementor/widgets/single-post/post-comments.php:82
msgid ""
"Switch on comments from either the discussion box on the WordPress post edit "
"screen or from the WordPress discussion settings."
msgstr ""

#: elementor/widgets/single-post/post-excerpt.php:16
msgid "Post Excerpt"
msgstr ""

#: elementor/widgets/single-post/post-image.php:17
#, fuzzy
#| msgid "Property Feature Slug"
msgid "Post Featured Image"
msgstr "Свойство свойств Slug"

#: elementor/widgets/single-post/post-info.php:16
msgid "Post Info"
msgstr ""

#: elementor/widgets/single-post/post-info.php:41
#: elementor/widgets/single-property/section-energy.php:244
#: elementor/widgets/single-property/section-overview-v2.php:339
#: elementor/widgets/single-property/section-overview.php:419
msgid "Meta Data"
msgstr ""

#: elementor/widgets/single-post/post-info.php:57
#: elementor/widgets/taxonomies-list.php:126
msgid "Inline"
msgstr ""

#: elementor/widgets/single-post/post-info.php:77
#, fuzzy
#| msgid "One Time"
msgid "Time"
msgstr "Один раз"

#: elementor/widgets/single-post/post-info.php:79
msgid "Terms"
msgstr ""

#: elementor/widgets/single-post/post-info.php:88
msgid "Date Format"
msgstr ""

#: elementor/widgets/single-post/post-info.php:93
msgctxt "Date Format"
msgid "March 6, 2018 (F j, Y)"
msgstr ""

#: elementor/widgets/single-post/post-info.php:108
msgid "Custom Date Format"
msgstr ""

#: elementor/widgets/single-post/post-info.php:117
#: elementor/widgets/single-post/post-info.php:154
#, php-format
msgid "Use the letters: %s"
msgstr ""

#: elementor/widgets/single-post/post-info.php:126
msgid "Time Format"
msgstr ""

#: elementor/widgets/single-post/post-info.php:144
msgid "Custom Time Format"
msgstr ""

#: elementor/widgets/single-post/post-info.php:177
msgid "Avatar"
msgstr ""

#: elementor/widgets/single-post/post-info.php:203
msgid "Custom Format"
msgstr ""

#: elementor/widgets/single-post/post-info.php:215
#: elementor/widgets/single-post/post-info.php:217
#: elementor/widgets/single-post/post-info.php:774
msgid "No Comments"
msgstr ""

#: elementor/widgets/single-post/post-info.php:228
#: elementor/widgets/single-post/post-info.php:230
#: elementor/widgets/single-post/post-info.php:775
#, fuzzy
#| msgid "One Time"
msgid "One Comment"
msgstr "Один раз"

#: elementor/widgets/single-post/post-info.php:243
#: elementor/widgets/single-post/post-info.php:776
#, php-format
msgid "%s Comments"
msgstr ""

#: elementor/widgets/single-post/post-info.php:312
msgid "Choose Icon"
msgstr ""

#: elementor/widgets/single-post/post-info.php:369
#: elementor/widgets/taxonomies-list.php:180
msgid "List"
msgstr ""

#: elementor/widgets/single-post/post-info.php:464
#: elementor/widgets/taxonomies-list.php:266
msgid "Weight"
msgstr ""

#: elementor/widgets/single-post/post-info.php:640
msgid "Indent"
msgstr ""

#: elementor/widgets/single-post/post-info.php:691
msgid "Choose"
msgstr ""

#: elementor/widgets/single-post/post-navigation.php:16
#: elementor/widgets/single-post/post-navigation.php:41
msgid "Post Navigation"
msgstr ""

#: elementor/widgets/single-post/post-navigation.php:60
#, fuzzy
#| msgid "New Label"
msgid "Previous Label"
msgstr "Новая метка"

#: elementor/widgets/single-post/post-navigation.php:78
#, fuzzy
#| msgid "New Label"
msgid "Next Label"
msgstr "Новая метка"

#: elementor/widgets/single-property/breadcrumb.php:19
msgid "Breadcrumbs"
msgstr ""

#: elementor/widgets/single-property/featured-image.php:17
#, fuzzy
#| msgid "Featured"
msgid "Featured Image"
msgstr "Рекомендуемые"

#: elementor/widgets/single-property/featured-image.php:60
msgid "Enable Fallback"
msgstr ""

#: elementor/widgets/single-property/featured-image.php:73
msgid "Fallback Image"
msgstr ""

#: elementor/widgets/single-property/featured-label.php:18
#, fuzzy
#| msgid "Featured Available"
msgid "Featured Label"
msgstr "Избранные"

#: elementor/widgets/single-property/images-gallery-v1.php:16
msgid "Property Images Gallery v1"
msgstr ""

#: elementor/widgets/single-property/images-gallery-v1.php:50
#: elementor/widgets/single-property/images-gallery-v2.php:66
#: elementor/widgets/single-property/images-gallery-v3.php:49
#: elementor/widgets/single-property/images-gallery-v4.php:59
#: elementor/widgets/single-property/images-gallery-v5.php:59
#, fuzzy
#| msgid "Property Type Slug"
msgid "Popup Gallery Type"
msgstr "Тип недвижимости Slug"

#: elementor/widgets/single-property/images-gallery-v1.php:53
#: elementor/widgets/single-property/images-gallery-v2.php:69
#: elementor/widgets/single-property/images-gallery-v3.php:52
#: elementor/widgets/single-property/images-gallery-v4.php:62
#: elementor/widgets/single-property/images-gallery-v5.php:62
msgid "Built In"
msgstr ""

#: elementor/widgets/single-property/images-gallery-v1.php:54
#: elementor/widgets/single-property/images-gallery-v2.php:70
#: elementor/widgets/single-property/images-gallery-v3.php:53
#: elementor/widgets/single-property/images-gallery-v4.php:63
#: elementor/widgets/single-property/images-gallery-v5.php:63
msgid "Photo Swipe"
msgstr ""

#: elementor/widgets/single-property/images-gallery-v1.php:86
#: elementor/widgets/single-property/images-gallery-v2.php:79
#: elementor/widgets/single-property/images-gallery-v3.php:62
#: elementor/widgets/single-property/section-toparea-v1.php:554
#: elementor/widgets/single-property/section-toparea-v2.php:553
#: elementor/widgets/single-property/section-toparea-v3.php:578
#: elementor/widgets/single-property/section-toparea-v5.php:553
msgid "Gallery Button"
msgstr ""

#: elementor/widgets/single-property/images-gallery-v1.php:98
#: elementor/widgets/single-property/images-gallery-v2.php:91
#: elementor/widgets/single-property/images-gallery-v3.php:74
#: elementor/widgets/single-property/section-toparea-v1.php:566
#: elementor/widgets/single-property/section-toparea-v2.php:565
#: elementor/widgets/single-property/section-toparea-v3.php:590
#: elementor/widgets/single-property/section-toparea-v5.php:565
msgid "Video Button"
msgstr ""

#: elementor/widgets/single-property/images-gallery-v1.php:110
#: elementor/widgets/single-property/images-gallery-v2.php:103
#: elementor/widgets/single-property/images-gallery-v3.php:86
#: elementor/widgets/single-property/section-toparea-v1.php:578
msgid "360° Virtual Tour"
msgstr ""

#: elementor/widgets/single-property/images-gallery-v1.php:122
#: elementor/widgets/single-property/images-gallery-v2.php:115
#: elementor/widgets/single-property/images-gallery-v3.php:98
#: elementor/widgets/single-property/section-toparea-v1.php:590
#: elementor/widgets/single-property/section-toparea-v2.php:589
#: elementor/widgets/single-property/section-toparea-v3.php:614
#: elementor/widgets/single-property/section-toparea-v5.php:589
msgid "Map Button"
msgstr ""

#: elementor/widgets/single-property/images-gallery-v1.php:134
#: elementor/widgets/single-property/images-gallery-v2.php:127
#: elementor/widgets/single-property/images-gallery-v3.php:110
#: elementor/widgets/single-property/section-toparea-v1.php:602
#: elementor/widgets/single-property/section-toparea-v2.php:601
#: elementor/widgets/single-property/section-toparea-v3.php:626
#: elementor/widgets/single-property/section-toparea-v5.php:601
msgid "Map will only show if you have enabled when add/edit property"
msgstr ""

#: elementor/widgets/single-property/images-gallery-v1.php:145
#: elementor/widgets/single-property/images-gallery-v2.php:138
#: elementor/widgets/single-property/images-gallery-v3.php:121
#: elementor/widgets/single-property/section-toparea-v1.php:613
#: elementor/widgets/single-property/section-toparea-v2.php:612
#: elementor/widgets/single-property/section-toparea-v3.php:637
#: elementor/widgets/single-property/section-toparea-v5.php:612
msgid "Street View Button"
msgstr ""

#: elementor/widgets/single-property/images-gallery-v1.php:157
#: elementor/widgets/single-property/images-gallery-v2.php:150
#: elementor/widgets/single-property/images-gallery-v3.php:133
#: elementor/widgets/single-property/section-toparea-v1.php:625
#: elementor/widgets/single-property/section-toparea-v2.php:624
#: elementor/widgets/single-property/section-toparea-v3.php:649
#: elementor/widgets/single-property/section-toparea-v5.php:624
msgid ""
"Street view will only show if you have enabled when add/edit property and "
"map type set to Google"
msgstr ""

#: elementor/widgets/single-property/images-gallery-v2.php:23
msgid "Property Images Gallery v2"
msgstr ""

#: elementor/widgets/single-property/images-gallery-v3.php:16
msgid "Property Images Gallery v3"
msgstr ""

#: elementor/widgets/single-property/images-gallery-v4.php:16
msgid "Property Images Gallery v4"
msgstr ""

#: elementor/widgets/single-property/images-gallery-v4.php:105
#: elementor/widgets/single-property/images-gallery-v5.php:104
#: elementor/widgets/single-property/images-gallery-v5.php:108
#: elementor/widgets/single-property/section-toparea-v6.php:706
#: elementor/widgets/single-property/section-toparea-v7.php:679
#, fuzzy
#| msgid "Load More"
msgid "More"
msgstr "Загрузи больше"

#: elementor/widgets/single-property/images-gallery-v5.php:16
msgid "Property Images Gallery v5"
msgstr ""

#: elementor/widgets/single-property/item-tools.php:20
#, fuzzy
#| msgid "Most Favourite"
msgid "Share, Favorite"
msgstr "Самые любимые"

#: elementor/widgets/single-property/item-tools.php:52
msgid "Share Button"
msgstr ""

#: elementor/widgets/single-property/item-tools.php:64
#, fuzzy
#| msgid "Favourite Count"
msgid "Favorite Button"
msgstr "Любимый подсчет"

#: elementor/widgets/single-property/item-tools.php:76
msgid "Print Button"
msgstr ""

#: elementor/widgets/single-property/item-tools.php:158
#: elementor/widgets/single-property/property-title-area.php:605
#: elementor/widgets/single-property/section-toparea-v1.php:531
#: elementor/widgets/single-property/section-toparea-v2.php:530
#: elementor/widgets/single-property/section-toparea-v3.php:555
#: elementor/widgets/single-property/section-toparea-v5.php:530
#: elementor/widgets/single-property/section-toparea-v6.php:575
#: elementor/widgets/single-property/section-toparea-v7.php:550
msgid "Border Color Hover"
msgstr ""

#: elementor/widgets/single-property/property-address.php:45
#: elementor/widgets/single-property/property-title-area.php:48
#: elementor/widgets/single-property/section-toparea-v1.php:48
#: elementor/widgets/single-property/section-toparea-v2.php:47
#: elementor/widgets/single-property/section-toparea-v3.php:52
#: elementor/widgets/single-property/section-toparea-v5.php:47
#: elementor/widgets/single-property/section-toparea-v6.php:48
#: elementor/widgets/single-property/section-toparea-v7.php:47
msgid "Streat Address"
msgstr ""

#: elementor/widgets/single-property/property-address.php:49
#: elementor/widgets/single-property/property-title-area.php:52
#: elementor/widgets/single-property/section-toparea-v1.php:52
#: elementor/widgets/single-property/section-toparea-v2.php:51
#: elementor/widgets/single-property/section-toparea-v3.php:56
#: elementor/widgets/single-property/section-toparea-v5.php:51
#: elementor/widgets/single-property/section-toparea-v6.php:52
#: elementor/widgets/single-property/section-toparea-v7.php:51
msgid "area"
msgstr ""

#: elementor/widgets/single-property/property-address.php:93
#: elementor/widgets/single-property/property-title-area.php:351
#: elementor/widgets/single-property/section-floorplan.php:197
#: elementor/widgets/single-property/section-toparea-v1.php:253
#: elementor/widgets/single-property/section-toparea-v2.php:253
#: elementor/widgets/single-property/section-toparea-v3.php:278
#: elementor/widgets/single-property/section-toparea-v5.php:253
#: elementor/widgets/single-property/section-toparea-v6.php:298
#: elementor/widgets/single-property/section-toparea-v7.php:273
msgid "Hide Icon"
msgstr ""

#: elementor/widgets/single-property/property-address.php:177
#: elementor/widgets/single-property/property-price.php:163
#: elementor/widgets/single-property/property-price.php:238
#: elementor/widgets/single-property/property-title-area.php:236
#: elementor/widgets/single-property/property-title-area.php:386
#: elementor/widgets/single-property/property-title-area.php:477
#: elementor/widgets/single-property/property-title-area.php:514
#: elementor/widgets/single-property/section-toparea-v1.php:138
#: elementor/widgets/single-property/section-toparea-v1.php:288
#: elementor/widgets/single-property/section-toparea-v1.php:379
#: elementor/widgets/single-property/section-toparea-v1.php:416
#: elementor/widgets/single-property/section-toparea-v2.php:138
#: elementor/widgets/single-property/section-toparea-v2.php:288
#: elementor/widgets/single-property/section-toparea-v2.php:378
#: elementor/widgets/single-property/section-toparea-v2.php:415
#: elementor/widgets/single-property/section-toparea-v3.php:163
#: elementor/widgets/single-property/section-toparea-v3.php:313
#: elementor/widgets/single-property/section-toparea-v3.php:403
#: elementor/widgets/single-property/section-toparea-v3.php:440
#: elementor/widgets/single-property/section-toparea-v5.php:138
#: elementor/widgets/single-property/section-toparea-v5.php:288
#: elementor/widgets/single-property/section-toparea-v5.php:378
#: elementor/widgets/single-property/section-toparea-v5.php:415
#: elementor/widgets/single-property/section-toparea-v6.php:183
#: elementor/widgets/single-property/section-toparea-v6.php:333
#: elementor/widgets/single-property/section-toparea-v6.php:423
#: elementor/widgets/single-property/section-toparea-v6.php:460
#: elementor/widgets/single-property/section-toparea-v7.php:158
#: elementor/widgets/single-property/section-toparea-v7.php:308
#: elementor/widgets/single-property/section-toparea-v7.php:398
#: elementor/widgets/single-property/section-toparea-v7.php:435
msgid "Text Shadow"
msgstr ""

#: elementor/widgets/single-property/property-price.php:52
#, fuzzy
#| msgid "Min Price"
msgid "Hide Second Price"
msgstr "Минимальная цена"

#: elementor/widgets/single-property/property-price.php:171
#: elementor/widgets/single-property/property-title-area.php:485
#: elementor/widgets/single-property/section-toparea-v1.php:387
#: elementor/widgets/single-property/section-toparea-v2.php:386
#: elementor/widgets/single-property/section-toparea-v3.php:411
#: elementor/widgets/single-property/section-toparea-v5.php:386
#: elementor/widgets/single-property/section-toparea-v6.php:431
#: elementor/widgets/single-property/section-toparea-v7.php:406
#, fuzzy
#| msgid "Min Price"
msgid "Second Price"
msgstr "Минимальная цена"

#: elementor/widgets/single-property/property-title-area.php:20
#, fuzzy
#| msgid "Property Title"
msgid "Property Title Info"
msgstr "Заголовок собственности"

#: elementor/widgets/single-property/property-title-area.php:131
#: elementor/widgets/single-property/section-toparea-v1.php:75
#: elementor/widgets/single-property/section-toparea-v2.php:73
#: elementor/widgets/single-property/section-toparea-v3.php:98
#: elementor/widgets/single-property/section-toparea-v5.php:73
#: elementor/widgets/single-property/section-toparea-v6.php:118
#: elementor/widgets/single-property/section-toparea-v7.php:93
msgid "Breadcrumb"
msgstr ""

#: elementor/widgets/single-property/property-title-area.php:293
#: elementor/widgets/single-property/section-toparea-v1.php:195
#: elementor/widgets/single-property/section-toparea-v2.php:195
#: elementor/widgets/single-property/section-toparea-v3.php:220
#: elementor/widgets/single-property/section-toparea-v5.php:195
#: elementor/widgets/single-property/section-toparea-v6.php:240
#: elementor/widgets/single-property/section-toparea-v7.php:215
#, fuzzy
#| msgid "Properties"
msgid "Property Labels"
msgstr "свойства"

#: elementor/widgets/single-property/property-title-area.php:301
#: elementor/widgets/single-property/section-toparea-v1.php:203
#: elementor/widgets/single-property/section-toparea-v2.php:203
#: elementor/widgets/single-property/section-toparea-v3.php:228
#: elementor/widgets/single-property/section-toparea-v5.php:203
#: elementor/widgets/single-property/section-toparea-v6.php:248
#: elementor/widgets/single-property/section-toparea-v7.php:223
#, fuzzy
#| msgid "Labels"
msgid "Show Labels"
msgstr "Этикетки"

#: elementor/widgets/single-property/property-title-area.php:324
#: elementor/widgets/single-property/section-toparea-v1.php:226
#: elementor/widgets/single-property/section-toparea-v2.php:226
#: elementor/widgets/single-property/section-toparea-v3.php:251
#: elementor/widgets/single-property/section-toparea-v5.php:226
#: elementor/widgets/single-property/section-toparea-v6.php:271
#: elementor/widgets/single-property/section-toparea-v7.php:246
msgid "Show Address"
msgstr ""

#: elementor/widgets/single-property/property-title-area.php:406
#: elementor/widgets/single-property/section-toparea-v1.php:308
#: elementor/widgets/single-property/section-toparea-v2.php:308
#: elementor/widgets/single-property/section-toparea-v3.php:333
#: elementor/widgets/single-property/section-toparea-v5.php:308
#: elementor/widgets/single-property/section-toparea-v6.php:353
#: elementor/widgets/single-property/section-toparea-v7.php:328
#, fuzzy
#| msgid "Price"
msgid "Show Price"
msgstr "Цена"

#: elementor/widgets/single-property/property-title-area.php:525
#: elementor/widgets/single-property/section-toparea-v1.php:427
#: elementor/widgets/single-property/section-toparea-v2.php:426
#: elementor/widgets/single-property/section-toparea-v3.php:451
#: elementor/widgets/single-property/section-toparea-v5.php:426
#: elementor/widgets/single-property/section-toparea-v6.php:471
#: elementor/widgets/single-property/section-toparea-v7.php:446
msgid "Tools"
msgstr ""

#: elementor/widgets/single-property/property-title-area.php:533
msgid "Show Tools"
msgstr ""

#: elementor/widgets/single-property/property-title.php:42
msgid "Listing Title"
msgstr ""

#: elementor/widgets/single-property/property-title.php:82
msgid "Title Eclipse"
msgstr ""

#: elementor/widgets/single-property/section-360-virtual.php:21
msgid "Section 360 Virtual Tour"
msgstr ""

#: elementor/widgets/single-property/section-360-virtual.php:82
#: elementor/widgets/single-property/section-address.php:197
#: elementor/widgets/single-property/section-attachments.php:91
#: elementor/widgets/single-property/section-calculator.php:81
#: elementor/widgets/single-property/section-calendar.php:81
#: elementor/widgets/single-property/section-description.php:117
#: elementor/widgets/single-property/section-details.php:359
#: elementor/widgets/single-property/section-energy.php:167
#: elementor/widgets/single-property/section-features.php:95
#: elementor/widgets/single-property/section-floorplan-v2.php:132
#: elementor/widgets/single-property/section-floorplan.php:143
#: elementor/widgets/single-property/section-nearby.php:89
#: elementor/widgets/single-property/section-overview-v2.php:246
#: elementor/widgets/single-property/section-overview.php:264
#: elementor/widgets/single-property/section-schedule-tour.php:82
#: elementor/widgets/single-property/section-similar.php:198
#: elementor/widgets/single-property/section-sublistings.php:80
#: elementor/widgets/single-property/section-video.php:81
#: elementor/widgets/single-property/section-walkscore.php:89
msgid "Section Style"
msgstr ""

#: elementor/widgets/single-property/section-360-virtual.php:94
#: elementor/widgets/single-property/section-attachments.php:247
#: elementor/widgets/single-property/section-calculator.php:93
#: elementor/widgets/single-property/section-calendar.php:93
#: elementor/widgets/single-property/section-description.php:129
#: elementor/widgets/single-property/section-energy.php:179
#: elementor/widgets/single-property/section-features.php:107
#: elementor/widgets/single-property/section-nearby.php:101
#: elementor/widgets/single-property/section-overview-v2.php:258
#: elementor/widgets/single-property/section-overview.php:276
#: elementor/widgets/single-property/section-video.php:93
#: elementor/widgets/single-property/section-walkscore.php:101
msgid "Content Style"
msgstr ""

#: elementor/widgets/single-property/section-address.php:21
msgid "Section Address"
msgstr ""

#: elementor/widgets/single-property/section-address.php:78
#: elementor/widgets/single-property/section-details.php:79
#: elementor/widgets/single-property/section-details.php:173
#: elementor/widgets/single-property/section-features.php:77
msgid "Data Columns"
msgstr ""

#: elementor/widgets/single-property/section-address.php:82
#: elementor/widgets/single-property/section-details.php:83
#: elementor/widgets/single-property/section-details.php:177
#: elementor/widgets/single-property/section-features.php:81
msgid "1 Column"
msgstr ""

#: elementor/widgets/single-property/section-address.php:111
msgid "Address Data"
msgstr ""

#: elementor/widgets/single-property/section-address.php:119
#: elementor/widgets/single-property/section-details.php:120
#, fuzzy
#| msgid "Agent Name"
msgid "Control Name"
msgstr "Имя агента"

#: elementor/widgets/single-property/section-address.php:132
#: elementor/widgets/single-property/section-details.php:208
#, fuzzy
#| msgid "Title"
msgid "Titles"
msgstr "заглавие"

#: elementor/widgets/single-property/section-address.php:241
#: elementor/widgets/single-property/section-details.php:471
#, fuzzy
#| msgid "Payment Title"
msgid "Meta Titles"
msgstr "Название платежа"

#: elementor/widgets/single-property/section-address.php:271
#: elementor/widgets/single-property/section-details.php:501
msgid "Meta Values"
msgstr ""

#: elementor/widgets/single-property/section-attachments.php:20
msgid "Section Attachments"
msgstr ""

#: elementor/widgets/single-property/section-attachments.php:78
#: elementor/widgets/single-property/section-description.php:101
msgid "Download Text"
msgstr ""

#: elementor/widgets/single-property/section-attachments.php:109
#: elementor/widgets/single-property/section-contact-bottom.php:565
#: elementor/widgets/single-property/section-schedule-tour.php:206
#: elementor/widgets/single-property/section-sublistings.php:88
msgid "Hide Title Border"
msgstr ""

#: elementor/widgets/single-property/section-attachments.php:283
msgid "Documents"
msgstr ""

#: elementor/widgets/single-property/section-attachments.php:324
#: elementor/widgets/single-property/section-description.php:264
msgid "Link Typography"
msgstr ""

#: elementor/widgets/single-property/section-attachments.php:347
#: elementor/widgets/single-property/section-description.php:289
#: elementor/widgets/single-property/section-description.php:333
#: elementor/widgets/single-property/section-description.php:335
#: elementor/widgets/single-property/section-description.php:338
msgid "Download"
msgstr ""

#: elementor/widgets/single-property/section-block-gallery.php:16
msgid "Section Block Gallery"
msgstr ""

#: elementor/widgets/single-property/section-block-gallery.php:50
msgid "Visible Images"
msgstr ""

#: elementor/widgets/single-property/section-block-gallery.php:59
msgid "Images in a row"
msgstr ""

#: elementor/widgets/single-property/section-block-gallery.php:76
msgid "Popup Type"
msgstr ""

#: elementor/widgets/single-property/section-calculator.php:20
msgid "Section Mortgage Calculator"
msgstr ""

#: elementor/widgets/single-property/section-calculator.php:140
msgid "Border Style"
msgstr ""

#: elementor/widgets/single-property/section-calendar.php:20
msgid "Section Availability Calendar"
msgstr ""

#: elementor/widgets/single-property/section-contact-2.php:21
msgid "Section Agent Contact Form v2"
msgstr ""

#: elementor/widgets/single-property/section-contact-2.php:55
msgid "Schedule Tour Tab"
msgstr ""

#: elementor/widgets/single-property/section-contact-2.php:67
msgid "Schedule Tour Title"
msgstr ""

#: elementor/widgets/single-property/section-contact-2.php:79
msgid "Request Info Title"
msgstr ""

#: elementor/widgets/single-property/section-contact-2.php:91
msgid "Default Active Tab"
msgstr ""

#: elementor/widgets/single-property/section-contact-2.php:95
msgid "Schedule a Tour"
msgstr ""

#: elementor/widgets/single-property/section-contact-2.php:96
msgid "Request Info"
msgstr ""

#: elementor/widgets/single-property/section-contact-2.php:107
#, fuzzy
#| msgid "Agent ID"
msgid "Agent Info"
msgstr "Агент ID"

#: elementor/widgets/single-property/section-contact-2.php:119
#, fuzzy
#| msgid "View Testimonial"
msgid "View Listing Link"
msgstr "Посмотреть отзыв"

#: elementor/widgets/single-property/section-contact-2.php:186
msgid "Form Tabs"
msgstr ""

#: elementor/widgets/single-property/section-contact-2.php:316
#: elementor/widgets/single-property/section-schedule-tour-v2.php:142
msgid "Tour Tabs"
msgstr ""

#: elementor/widgets/single-property/section-contact-2.php:451
#: elementor/widgets/single-property/section-schedule-tour-v2.php:250
msgid "Active Border Color"
msgstr ""

#: elementor/widgets/single-property/section-contact-bottom.php:21
msgid "Section Agent Contact Form"
msgstr ""

#: elementor/widgets/single-property/section-contact-bottom.php:77
#, fuzzy
#| msgid "Agent ID"
msgid "Agent Details"
msgstr "Агент ID"

#: elementor/widgets/single-property/section-contact-bottom.php:217
msgid "View Listing Button"
msgstr ""

#: elementor/widgets/single-property/section-description.php:20
msgid "Section Description"
msgstr ""

#: elementor/widgets/single-property/section-description.php:78
msgid "Show Attachments"
msgstr ""

#: elementor/widgets/single-property/section-description.php:88
#, fuzzy
#| msgid "Payment Title"
msgid "Attachment Title"
msgstr "Название платежа"

#: elementor/widgets/single-property/section-description.php:223
#, fuzzy
#| msgid "Payment Title"
msgid "Documents Title"
msgstr "Название платежа"

#: elementor/widgets/single-property/section-details.php:21
msgid "Section Details"
msgstr ""

#: elementor/widgets/single-property/section-details.php:112
msgid "Details Data"
msgstr ""

#: elementor/widgets/single-property/section-details.php:132
msgid "Additional Details"
msgstr ""

#: elementor/widgets/single-property/section-details.php:140
msgid "Show Additional Details"
msgstr ""

#: elementor/widgets/single-property/section-details.php:150
msgid "Addtional Section Header"
msgstr ""

#: elementor/widgets/single-property/section-details.php:160
msgid "Additional Section Title"
msgstr ""

#: elementor/widgets/single-property/section-details.php:251
#: elementor/widgets/single-property/section-overview-v2.php:200
#: elementor/widgets/single-property/section-overview.php:199
#: statistics/houzez-statistics.php:589
msgid "Bedroom"
msgstr "Спальня"

#: elementor/widgets/single-property/section-details.php:268
msgid "Room"
msgstr ""

#: elementor/widgets/single-property/section-details.php:285
#: elementor/widgets/single-property/section-overview-v2.php:207
#: elementor/widgets/single-property/section-overview.php:206
#: statistics/houzez-statistics.php:590
msgid "Bathroom"
msgstr "ванная комната"

#: elementor/widgets/single-property/section-details.php:319
#, fuzzy
#| msgid "Max Size"
msgid "Garage Size"
msgstr "Максимальный размер"

#: elementor/widgets/single-property/section-details.php:373
msgid "Details Box Style"
msgstr ""

#: elementor/widgets/single-property/section-details.php:403
msgid "Hide Box Border"
msgstr ""

#: elementor/widgets/single-property/section-details.php:562
msgid "Meta Border Color"
msgstr ""

#: elementor/widgets/single-property/section-details.php:574
msgid "Additional Meta Border Color"
msgstr ""

#: elementor/widgets/single-property/section-energy.php:20
msgid "Section Energy Class"
msgstr ""

#: elementor/widgets/single-property/section-energy.php:87
msgid ""
"Add your custom labels, it will overwrite default labels which are "
"manageable in theme options -> translation"
msgstr ""

#: elementor/widgets/single-property/section-energy.php:96
msgid "Energetic class"
msgstr ""

#: elementor/widgets/single-property/section-energy.php:106
msgid "Global Energy Performance Index"
msgstr ""

#: elementor/widgets/single-property/section-energy.php:115
msgid "Renewable energy performance index"
msgstr ""

#: elementor/widgets/single-property/section-energy.php:125
msgid "Energy performance of the building"
msgstr ""

#: elementor/widgets/single-property/section-energy.php:135
msgid "EPC Current Rating"
msgstr ""

#: elementor/widgets/single-property/section-energy.php:145
msgid "EPC Potential Rating"
msgstr ""

#: elementor/widgets/single-property/section-energy.php:155
msgid "Energy class"
msgstr ""

#: elementor/widgets/single-property/section-features.php:20
#, fuzzy
#| msgid "Features"
msgid "Section Features"
msgstr "Особенности"

#: elementor/widgets/single-property/section-features.php:152
msgid "Icons Color"
msgstr ""

#: elementor/widgets/single-property/section-features.php:164
msgid "Icons Size"
msgstr ""

#: elementor/widgets/single-property/section-features.php:207
msgid "Text Typography"
msgstr ""

#: elementor/widgets/single-property/section-floorplan-v2.php:20
msgid "Section Floor Plan v2"
msgstr ""

#: elementor/widgets/single-property/section-floorplan-v2.php:77
#: elementor/widgets/single-property/section-floorplan.php:87
#, fuzzy
#| msgid "Size"
msgid "Hide Size"
msgstr "Размер"

#: elementor/widgets/single-property/section-floorplan-v2.php:90
#: elementor/widgets/single-property/section-floorplan.php:100
msgid "Hide Beds"
msgstr ""

#: elementor/widgets/single-property/section-floorplan-v2.php:103
#: elementor/widgets/single-property/section-floorplan.php:113
msgid "Hide Bath"
msgstr ""

#: elementor/widgets/single-property/section-floorplan-v2.php:116
#: elementor/widgets/single-property/section-floorplan.php:126
#, fuzzy
#| msgid "Min Price"
msgid "Hide Price"
msgstr "Минимальная цена"

#: elementor/widgets/single-property/section-floorplan-v2.php:236
#: elementor/widgets/single-property/section-floorplan.php:256
#, fuzzy
#| msgid "Payment Title"
msgid "Floor Plan Title"
msgstr "Название платежа"

#: elementor/widgets/single-property/section-floorplan.php:20
msgid "Section Floor Plan"
msgstr ""

#: elementor/widgets/single-property/section-floorplan.php:77
msgid "Default Open"
msgstr ""

#: elementor/widgets/single-property/section-floorplan.php:288
msgid "Meta"
msgstr ""

#: elementor/widgets/single-property/section-google-map.php:16
msgid "Section Google Map"
msgstr ""

#: elementor/widgets/single-property/section-google-map.php:57
#: elementor/widgets/single-property/section-open-street-map.php:57
msgid "Deprecated Widget"
msgstr ""

#: elementor/widgets/single-property/section-google-map.php:58
#: elementor/widgets/single-property/section-open-street-map.php:58
msgid ""
"This widget is deprecated. Please use \"Section Map\" widget instead. The "
"map will display according to the map system selected in Theme Options."
msgstr ""

#: elementor/widgets/single-property/section-google-map.php:68
#: elementor/widgets/single-property/section-map.php:51
#: elementor/widgets/single-property/section-open-street-map.php:68
msgid "Pin or Circle"
msgstr ""

#: elementor/widgets/single-property/section-google-map.php:72
#: elementor/widgets/single-property/section-map.php:55
#: elementor/widgets/single-property/section-open-street-map.php:72
msgid "Marker Pin"
msgstr ""

#: elementor/widgets/single-property/section-google-map.php:73
#: elementor/widgets/single-property/section-map.php:56
#: elementor/widgets/single-property/section-open-street-map.php:73
msgid "Circle"
msgstr ""

#: elementor/widgets/single-property/section-google-map.php:85
msgid "Road Map"
msgstr ""

#: elementor/widgets/single-property/section-google-map.php:96
#: elementor/widgets/single-property/section-map.php:64
#: elementor/widgets/single-property/section-open-street-map.php:81
msgid "Zoom"
msgstr ""

#: elementor/widgets/single-property/section-google-map.php:105
msgid "Style for Google Map"
msgstr ""

#: elementor/widgets/single-property/section-google-map.php:107
msgid "Use https://snazzymaps.com/ to create styles"
msgstr ""

#: elementor/widgets/single-property/section-map.php:16
msgid "Section Map"
msgstr ""

#: elementor/widgets/single-property/section-nearby.php:20
msgid "Section Yelp Nearby"
msgstr ""

#: elementor/widgets/single-property/section-nearby.php:77
msgid ""
"Make sure you have added Yelp API in Theme Options -> Property Details -> "
"Yelp Nearby Places"
msgstr ""

#: elementor/widgets/single-property/section-nearby.php:158
msgid "Powered by"
msgstr ""

#: elementor/widgets/single-property/section-nearby.php:158
msgid "Yelp"
msgstr ""

#: elementor/widgets/single-property/section-open-street-map.php:16
msgid "Section Open Street Map"
msgstr ""

#: elementor/widgets/single-property/section-overview-v2.php:23
msgid "Section Overview v2"
msgstr ""

#: elementor/widgets/single-property/section-overview-v2.php:177
#: elementor/widgets/single-property/section-overview.php:176
msgid "Pre Defined"
msgstr ""

#: elementor/widgets/single-property/section-overview-v2.php:266
#: elementor/widgets/single-property/section-overview.php:343
msgid "Text Align"
msgstr ""

#: elementor/widgets/single-property/section-overview-v2.php:368
#: elementor/widgets/single-property/section-overview.php:448
msgid "Meta Icons"
msgstr ""

#: elementor/widgets/single-property/section-overview-v2.php:389
#: elementor/widgets/single-property/section-overview.php:469
msgid "Icons Size(px)"
msgstr ""

#: elementor/widgets/single-property/section-overview-v2.php:457
#: elementor/widgets/single-property/section-overview-v2.php:528
msgid "Map will show on listing detail page."
msgstr ""

#: elementor/widgets/single-property/section-overview.php:21
msgid "Section Overview"
msgstr ""

#: elementor/widgets/single-property/section-overview.php:246
msgid "6"
msgstr ""

#: elementor/widgets/single-property/section-overview.php:247
msgid "5"
msgstr ""

#: elementor/widgets/single-property/section-overview.php:248
msgid "4"
msgstr ""

#: elementor/widgets/single-property/section-overview.php:249
msgid "3"
msgstr ""

#: elementor/widgets/single-property/section-overview.php:250
msgid "2"
msgstr ""

#: elementor/widgets/single-property/section-overview.php:284
msgid "Separator"
msgstr ""

#: elementor/widgets/single-property/section-review.php:21
msgid "Section Reviews"
msgstr ""

#: elementor/widgets/single-property/section-schedule-tour-v2.php:21
msgid "Section Schedule Tour v2"
msgstr ""

#: elementor/widgets/single-property/section-schedule-tour.php:21
msgid "Section Schedule Tour"
msgstr ""

#: elementor/widgets/single-property/section-similar.php:21
msgid "Section Similar Listings"
msgstr ""

#: elementor/widgets/single-property/section-similar.php:78
msgid "Similar Properties Criteria"
msgstr ""

#: elementor/widgets/single-property/section-similar.php:84
#, fuzzy
#| msgid "Property Feature Slug"
msgid "Property Feature"
msgstr "Свойство свойств Slug"

#: elementor/widgets/single-property/section-similar.php:168
#: elementor/widgets/sort-by.php:149 elementor/widgets/sort-by.php:150
msgid "Default Order"
msgstr ""

#: elementor/widgets/single-property/section-similar.php:171
#: functions/functions.php:254
msgid "Date New to Old"
msgstr ""

#: elementor/widgets/single-property/section-similar.php:172
#: functions/functions.php:253
msgid "Date Old to New"
msgstr ""

#: elementor/widgets/single-property/section-similar.php:173
#: functions/functions.php:252
msgid "Price (High to Low)"
msgstr ""

#: elementor/widgets/single-property/section-similar.php:174
#: functions/functions.php:251
msgid "Price (Low to High)"
msgstr ""

#: elementor/widgets/single-property/section-similar.php:175
msgid "Show Featured Listings on Top"
msgstr ""

#: elementor/widgets/single-property/section-sublistings.php:19
msgid "Section Sub Listings"
msgstr ""

#: elementor/widgets/single-property/section-toparea-v1.php:20
msgid "Section Top Area v1"
msgstr ""

#: elementor/widgets/single-property/section-toparea-v1.php:435
#: elementor/widgets/single-property/section-toparea-v2.php:434
#: elementor/widgets/single-property/section-toparea-v3.php:459
#: elementor/widgets/single-property/section-toparea-v5.php:434
#: elementor/widgets/single-property/section-toparea-v6.php:479
#: elementor/widgets/single-property/section-toparea-v7.php:454
#, fuzzy
#| msgid "Most Favourite"
msgid "Favorite"
msgstr "Самые любимые"

#: elementor/widgets/single-property/section-toparea-v1.php:447
#: elementor/widgets/single-property/section-toparea-v2.php:446
#: elementor/widgets/single-property/section-toparea-v3.php:471
#: elementor/widgets/single-property/section-toparea-v5.php:446
#: elementor/widgets/single-property/section-toparea-v6.php:491
#: elementor/widgets/single-property/section-toparea-v7.php:466
msgid "Social Share"
msgstr ""

#: elementor/widgets/single-property/section-toparea-v1.php:459
#: elementor/widgets/single-property/section-toparea-v2.php:458
#: elementor/widgets/single-property/section-toparea-v3.php:483
#: elementor/widgets/single-property/section-toparea-v5.php:458
#: elementor/widgets/single-property/section-toparea-v6.php:503
#: elementor/widgets/single-property/section-toparea-v7.php:478
msgid "Print"
msgstr ""

#: elementor/widgets/single-property/section-toparea-v1.php:546
#: elementor/widgets/single-property/section-toparea-v2.php:545
#: elementor/widgets/single-property/section-toparea-v3.php:570
#: elementor/widgets/single-property/section-toparea-v5.php:545
msgid "Media Buttons"
msgstr ""

#: elementor/widgets/single-property/section-toparea-v1.php:639
#, fuzzy
#| msgid "Agent Name"
msgid "Agent Form"
msgstr "Имя агента"

#: elementor/widgets/single-property/section-toparea-v1.php:647
#, fuzzy
#| msgid "No Agent found"
msgid "Show Agent Form"
msgstr "Агент не найден"

#: elementor/widgets/single-property/section-toparea-v2.php:19
msgid "Section Top Area v2"
msgstr ""

#: elementor/widgets/single-property/section-toparea-v2.php:577
#: elementor/widgets/single-property/section-toparea-v3.php:602
#: elementor/widgets/single-property/section-toparea-v5.php:577
msgid "360° Virtual Tour Button"
msgstr ""

#: elementor/widgets/single-property/section-toparea-v3.php:24
msgid "Section Top Area v3 and v4"
msgstr ""

#: elementor/widgets/single-property/section-toparea-v5.php:19
msgid "Section Top Area v5"
msgstr ""

#: elementor/widgets/single-property/section-toparea-v6.php:20
msgid "Section Top Area v6"
msgstr ""

#: elementor/widgets/single-property/section-toparea-v7.php:19
msgid "Section Top Area v7"
msgstr ""

#: elementor/widgets/single-property/section-video.php:20
msgid "Section Video"
msgstr ""

#: elementor/widgets/single-property/section-walkscore.php:20
msgid "Section Walkscore"
msgstr ""

#: elementor/widgets/single-property/section-walkscore.php:77
msgid ""
"Make sure you have added Walkscore API key in Theme Options -> Property "
"Details -> Walkscore"
msgstr ""

#: elementor/widgets/single-property/section-walkscore.php:169
msgid "Visit listing detail page for walkscore preview"
msgstr ""

#: elementor/widgets/sort-by.php:36
msgid "Listings Sort By"
msgstr ""

#: elementor/widgets/sort-by.php:89
msgid "Sort By:"
msgstr ""

#: elementor/widgets/sort-by.php:151
msgid "Price - Low to High"
msgstr ""

#: elementor/widgets/sort-by.php:152
msgid "Price - High to Low"
msgstr ""

#: elementor/widgets/sort-by.php:154
msgid "Featured Listings First"
msgstr ""

#: elementor/widgets/sort-by.php:156
msgid "Date - Old to New"
msgstr ""

#: elementor/widgets/sort-by.php:157
msgid "Date - New to Old"
msgstr ""

#: elementor/widgets/space.php:36
msgid "Empty Space"
msgstr ""

#: elementor/widgets/space.php:86
msgid "Space"
msgstr ""

#: elementor/widgets/taxonomies-cards-carousel.php:37
msgid "Taxonomies Cards Carousel"
msgstr ""

#: elementor/widgets/taxonomies-cards-carousel.php:87
#: elementor/widgets/taxonomies-cards.php:87
msgid "Choose Layout"
msgstr ""

#: elementor/widgets/taxonomies-cards-carousel.php:266
#: elementor/widgets/taxonomies-cards.php:171
#: elementor/widgets/taxonomies-grids-carousel.php:251
#: elementor/widgets/taxonomies-grids.php:161
#, fuzzy
#| msgid "Country"
msgid "Count Typography"
msgstr "Страна"

#: elementor/widgets/taxonomies-cards-carousel.php:286
#: elementor/widgets/taxonomies-cards.php:191
#: elementor/widgets/taxonomies-grids-carousel.php:271
#: elementor/widgets/taxonomies-grids.php:181
#, fuzzy
#| msgid "Country"
msgid "Count Color"
msgstr "Страна"

#: elementor/widgets/taxonomies-cards-carousel.php:310
#: elementor/widgets/taxonomies-cards.php:215
msgid "Gap Bottom"
msgstr ""

#: elementor/widgets/taxonomies-cards-carousel.php:327
#: elementor/widgets/taxonomies-cards.php:232
msgid "Gap Right"
msgstr ""

#: elementor/widgets/taxonomies-cards-carousel.php:376
#: elementor/widgets/taxonomies-cards.php:281
msgid "Image Padding"
msgstr ""

#: elementor/widgets/taxonomies-cards-carousel.php:389
#: elementor/widgets/taxonomies-cards.php:294
#: elementor/widgets/taxonomies-grids-carousel.php:331
#: elementor/widgets/taxonomies-grids.php:276
#: elementor/widgets/taxonomies-list.php:711
msgid "Shadow"
msgstr ""

#: elementor/widgets/taxonomies-cards.php:37
msgid "Taxonomies Cards"
msgstr ""

#: elementor/widgets/taxonomies-grids-carousel.php:37
msgid "Taxonomies Grids Carousel"
msgstr ""

#: elementor/widgets/taxonomies-grids-carousel.php:300
#: elementor/widgets/taxonomies-grids.php:245
msgid "Grid Padding"
msgstr ""

#: elementor/widgets/taxonomies-grids-carousel.php:365
#: elementor/widgets/taxonomies-grids.php:310
msgid "Opacity Color"
msgstr ""

#: elementor/widgets/taxonomies-grids.php:40
msgid "Taxonomies Grids"
msgstr ""

#: elementor/widgets/taxonomies-list.php:42
msgid "Taxonomies List"
msgstr ""

#: elementor/widgets/taxonomies-list.php:92
#, fuzzy
#| msgid "Title"
msgid "List Title"
msgstr "заглавие"

#: elementor/widgets/taxonomies-list.php:123
msgid "Count Position"
msgstr ""

#: elementor/widgets/taxonomies-list.php:127
msgid "Separated"
msgstr ""

#: elementor/widgets/taxonomies-list.php:491
msgid "Horizontal Alignment"
msgstr ""

#: elementor/widgets/taxonomies-list.php:522
msgid "Vertical Alignment"
msgstr ""

#: elementor/widgets/taxonomies-list.php:548
msgid "Adjust Vertical Position"
msgstr ""

#: elementor/widgets/team-member.php:36
msgid "Team"
msgstr ""

#: elementor/widgets/team-member.php:130
msgid "Facebook Profile Link"
msgstr ""

#: elementor/widgets/team-member.php:138
msgid "Facebook Target"
msgstr ""

#: elementor/widgets/team-member.php:151
msgid "X Profile Link"
msgstr ""

#: elementor/widgets/team-member.php:159
msgid "X Target"
msgstr ""

#: elementor/widgets/team-member.php:173
msgid "LinkedIn Profile Link"
msgstr ""

#: elementor/widgets/team-member.php:181
msgid "LinkedIn Target"
msgstr ""

#: elementor/widgets/team-member.php:194
msgid "Pinterest Profile Link"
msgstr ""

#: elementor/widgets/team-member.php:202
msgid "Pinterest Target"
msgstr ""

#: elementor/widgets/testimonials-v2.php:37
#, fuzzy
#| msgid "Testimonials"
msgid "Testimonials v2"
msgstr "Отзывы"

#: elementor/widgets/testimonials-v2.php:87
#: elementor/widgets/testimonials.php:87
#, fuzzy
#| msgid "Testimonials"
msgid "Testimonials Type"
msgstr "Отзывы"

#: elementor/widgets/testimonials-v2.php:175
#: elementor/widgets/testimonials.php:115
msgid "Image Resolution"
msgstr ""

#: elementor/widgets/testimonials-v3.php:37
#, fuzzy
#| msgid "Testimonials"
msgid "Testimonials v3"
msgstr "Отзывы"

#: elementor/widgets/testimonials.php:37
#, fuzzy
#| msgid "Testimonials"
msgid "Testimonials v1"
msgstr "Отзывы"

#: elementor/widgets/testimonials.php:90
msgid "Grid 4 Columns"
msgstr ""

#: elementor/widgets/wpml/advanced-search-wpml.php:22
msgid "Advanced Search: Search Title"
msgstr ""

#: elementor/widgets/wpml/contact-form-wpml.php:22
msgid "Contact Form: GDPR Label"
msgstr ""

#: elementor/widgets/wpml/contact-form-wpml.php:27
msgid "Contact Form: GDPR Validation Message"
msgstr ""

#: elementor/widgets/wpml/contact-form-wpml.php:32
msgid "Contact Form: GDPR Agreement Text"
msgstr ""

#: elementor/widgets/wpml/contact-form-wpml.php:37
msgid "Contact Form: Redirect Link"
msgstr ""

#: elementor/widgets/wpml/contact-form-wpml.php:63
msgid "Contact Form: Field Label"
msgstr ""

#: elementor/widgets/wpml/contact-form-wpml.php:66
msgid "Contact Form: Field Placeholder"
msgstr ""

#: elementor/widgets/wpml/contact-form-wpml.php:69
msgid "Contact Form: Field Validation Message"
msgstr ""

#: elementor/widgets/wpml/grid-builder-wpml.php:22
msgid "Grid Builder: Title"
msgstr ""

#: elementor/widgets/wpml/grid-builder-wpml.php:27
msgid "Grid Builder: Sub Title"
msgstr ""

#: elementor/widgets/wpml/grid-builder-wpml.php:32
msgid "Grid Builder: More Details Text"
msgstr ""

#: elementor/widgets/wpml/grid-builder-wpml.php:37
msgid "Grid Builder: Property Text"
msgstr ""

#: elementor/widgets/wpml/grid-builder-wpml.php:42
msgid "Grid Builder: Properties Text"
msgstr ""

#: elementor/widgets/wpml/grid-builder-wpml.php:47
msgid "Grid Builder: Link"
msgstr ""

#: elementor/widgets/wpml/icon-box-wpml.php:42
#, fuzzy
#| msgid "Invoice Title"
msgid "Icon Box: Title"
msgstr "Название счета"

#: elementor/widgets/wpml/icon-box-wpml.php:45
msgid "Icon Box: Text"
msgstr ""

#: elementor/widgets/wpml/icon-box-wpml.php:48
msgid "Icon Box: Read More Text"
msgstr ""

#: elementor/widgets/wpml/icon-box-wpml.php:51
msgid "Icon Box: Read More Link"
msgstr ""

#: elementor/widgets/wpml/inquiry-form-wpml.php:22
msgid "Inquiry Form: GDPR Label"
msgstr ""

#: elementor/widgets/wpml/inquiry-form-wpml.php:27
msgid "Inquiry Form: GDPR Validation Message"
msgstr ""

#: elementor/widgets/wpml/inquiry-form-wpml.php:32
msgid "Inquiry Form: GDPR Agreement Text"
msgstr ""

#: elementor/widgets/wpml/inquiry-form-wpml.php:37
msgid "Inquiry Form: Redirect Link"
msgstr ""

#: elementor/widgets/wpml/inquiry-form-wpml.php:63
msgid "Inquiry Form: Field Label"
msgstr ""

#: elementor/widgets/wpml/inquiry-form-wpml.php:66
msgid "Inquiry Form: Field Placeholder"
msgstr ""

#: elementor/widgets/wpml/inquiry-form-wpml.php:69
msgid "Inquiry Form: Field Validation Message"
msgstr ""

#: elementor/widgets/wpml/price-table-wpml.php:22
msgid "Price Table: Package Name"
msgstr ""

#: elementor/widgets/wpml/price-table-wpml.php:27
msgid "Price Table: Package Price"
msgstr ""

#: elementor/widgets/wpml/price-table-wpml.php:32
msgid "Price Table: Package Currency"
msgstr ""

#: elementor/widgets/wpml/price-table-wpml.php:37
msgid "Price Table: Package Content"
msgstr ""

#: elementor/widgets/wpml/price-table-wpml.php:42
msgid "Price Table: Package Button Text"
msgstr ""

#: elementor/widgets/wpml/property-carousel-v1-wpml.php:22
msgid "Properties Carousel v1: All - Button Text"
msgstr ""

#: elementor/widgets/wpml/property-carousel-v1-wpml.php:27
msgid "Properties Carousel v1: All - Button URL"
msgstr ""

#: elementor/widgets/wpml/property-carousel-v2-wpml.php:22
msgid "Properties Carousel v2: All - Button Text"
msgstr ""

#: elementor/widgets/wpml/property-carousel-v2-wpml.php:27
msgid "Properties Carousel v2: All - Button URL"
msgstr ""

#: elementor/widgets/wpml/property-carousel-v3-wpml.php:22
msgid "Properties Carousel v3: All - Button Text"
msgstr ""

#: elementor/widgets/wpml/property-carousel-v3-wpml.php:27
msgid "Properties Carousel v3: All - Button URL"
msgstr ""

#: elementor/widgets/wpml/property-carousel-v5-wpml.php:22
msgid "Properties Carousel v5: All - Button Text"
msgstr ""

#: elementor/widgets/wpml/property-carousel-v5-wpml.php:27
msgid "Properties Carousel v5: All - Button URL"
msgstr ""

#: elementor/widgets/wpml/property-carousel-v6-wpml.php:22
msgid "Properties Carousel v6: All - Button Text"
msgstr ""

#: elementor/widgets/wpml/property-carousel-v6-wpml.php:27
msgid "Properties Carousel v6: All - Button URL"
msgstr ""

#: elementor/widgets/wpml/search-builder-wpml.php:22
msgid "Search Builder: All Tab Text"
msgstr ""

#: elementor/widgets/wpml/search-builder-wpml.php:48
msgid "Search Builder: Field Label"
msgstr ""

#: elementor/widgets/wpml/search-builder-wpml.php:51
msgid "Search Builder: Field Placeholder"
msgstr ""

#: elementor/widgets/wpml/search-builder-wpml.php:54
msgid "Search Builder: Selected Count Text"
msgstr ""

#: elementor/widgets/wpml/section-title-wpml.php:22
msgid "Section Title: Main Title"
msgstr ""

#: elementor/widgets/wpml/section-title-wpml.php:27
msgid "Section Title: Sub Title"
msgstr ""

#: elementor/widgets/wpml/sort-by-wpml.php:22
#, fuzzy
#| msgid "Property Title"
msgid "Sort By: Title"
msgstr "Заголовок собственности"

#: elementor/widgets/wpml/team-member-wpml.php:22
msgid "Team Member: Name"
msgstr ""

#: elementor/widgets/wpml/team-member-wpml.php:27
msgid "Team Member: Position"
msgstr ""

#: elementor/widgets/wpml/team-member-wpml.php:32
msgid "Team Member: Description"
msgstr ""

#: elementor/widgets/wpml/team-member-wpml.php:37
msgid "Team Member: Profile Link"
msgstr ""

#: elementor/widgets/wpml/team-member-wpml.php:42
msgid "Team Member: Facebook Link"
msgstr ""

#: elementor/widgets/wpml/team-member-wpml.php:47
msgid "Team Member: Twitter Link"
msgstr ""

#: elementor/widgets/wpml/team-member-wpml.php:52
msgid "Team Member: LinkedIn Link"
msgstr ""

#: elementor/widgets/wpml/team-member-wpml.php:57
msgid "Team Member: Pinterest Link"
msgstr ""

#: elementor/widgets/wpml/team-member-wpml.php:62
msgid "Team Member: Google Link"
msgstr ""

#: extensions/favethemes-white-label/favethemes-white-label.php:50
#: extensions/favethemes-white-label/favethemes-white-label.php:51
#: extensions/favethemes-white-label/template/form.php:14
#, fuzzy
#| msgid "New Label"
msgid "White Label"
msgstr "Новая метка"

#: extensions/favethemes-white-label/template/form.php:25
msgid "Theme Branding:"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:27
msgid "This option replace Houzez in the admin"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:31
msgid "Theme Name:"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:33
msgid "This option replace the theme name in Appearance > Themes."
msgstr ""

#: extensions/favethemes-white-label/template/form.php:37
#, fuzzy
#| msgid "Author"
msgid "Theme Author:"
msgstr "автор"

#: extensions/favethemes-white-label/template/form.php:39
msgid "This option replace the theme author in Appearance > Themes."
msgstr ""

#: extensions/favethemes-white-label/template/form.php:43
msgid "Theme Author URL:"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:45
msgid "This option replace the theme autohr url in Appearance > Themes."
msgstr ""

#: extensions/favethemes-white-label/template/form.php:49
msgid "Theme Description:"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:51
msgid "This option replace the theme description in Appearance > Themes."
msgstr ""

#: extensions/favethemes-white-label/template/form.php:55
msgid "Theme Screenshot URL:"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:60
msgid "Preview Image"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:65
#: extensions/favethemes-white-label/template/form.php:82
msgid "Upload"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:66
#: extensions/favethemes-white-label/template/form.php:83
msgid "Remove Image"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:67
msgid ""
"This option replace the theme screenshot in Appearance > Themes. Recommended "
"size: 880x660px"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:72
msgid "Branding Logo URL:"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:77
msgid "Branding Logo"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:84
msgid ""
"This option replace the branding logo in admin panel. Recommended size: "
"127x24"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:91
msgid "Hide The Themes Section in the Customizer"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:95
msgid "Hide White Label Page"
msgstr ""

#: extensions/favethemes-white-label/template/form.php:97
msgid ""
"Check this option to hide this page. Re-activate Houzez Theme Functionality "
"to display this page again."
msgstr ""

#: extensions/meta-box/addons/mb-term-meta/src/MetaBox.php:61
msgid "Term added."
msgstr ""

#: extensions/meta-box/addons/meta-box-group/group-field.php:63
msgid "Are you sure you want to remove this group?"
msgstr ""

#: extensions/meta-box/addons/meta-box-group/group-field.php:140
#: extensions/meta-box/inc/fields/file-input.php:48
msgid "Remove"
msgstr ""

#: extensions/meta-box/addons/meta-box-group/group-field.php:324
msgid "Entry {#}"
msgstr ""

#: extensions/meta-box/addons/meta-box-group/group-field.php:324
#, fuzzy
#| msgid "Country"
msgid "Entry"
msgstr "Страна"

#: extensions/meta-box/inc/about/about.php:38
msgid "About"
msgstr ""

#: extensions/meta-box/inc/about/about.php:40
msgid "Go Pro"
msgstr ""

#: extensions/meta-box/inc/about/about.php:50
#: extensions/meta-box/inc/about/about.php:51
#: extensions/meta-box/src/Block/Register.php:12
#: extensions/meta-box/src/Bricks/Register.php:10
#: extensions/meta-box/src/Elementor/Register.php:13
#: extensions/meta-box/src/Oxygen/Register.php:14
msgid "Meta Box"
msgstr ""

#: extensions/meta-box/inc/about/about.php:63
msgid "Welcome to Meta Box"
msgstr ""

#: extensions/meta-box/inc/about/about.php:64
msgid "Dashboard"
msgstr ""

#: extensions/meta-box/inc/about/sections/extensions.php:5
msgid ""
"Extend custom fields in WordPress well beyond what others would ever "
"consider ordinary!"
msgstr ""

#: extensions/meta-box/inc/about/sections/extensions.php:6
msgid "Save over 80% with our extensions bundles."
msgstr ""

#: extensions/meta-box/inc/about/sections/extensions.php:6
#, fuzzy
#| msgid "Views Count"
msgid "View Bundles"
msgstr "Количество просмотров"

#: extensions/meta-box/inc/about/sections/extensions.php:14
msgid ""
"Create and manage custom post types easily in WordPress with an easy-to-use "
"interface."
msgstr ""

#: extensions/meta-box/inc/about/sections/extensions.php:18
#: extensions/meta-box/inc/about/sections/extensions.php:114
msgid "Free Download"
msgstr ""

#: extensions/meta-box/inc/about/sections/extensions.php:26
msgid ""
"Drag and drop your custom fields into place without a single line of code."
msgstr ""

#: extensions/meta-box/inc/about/sections/extensions.php:30
#: extensions/meta-box/inc/about/sections/extensions.php:42
#: extensions/meta-box/inc/about/sections/extensions.php:54
#: extensions/meta-box/inc/about/sections/extensions.php:66
#: extensions/meta-box/inc/about/sections/extensions.php:78
#: extensions/meta-box/inc/about/sections/extensions.php:90
#: extensions/meta-box/inc/about/sections/extensions.php:102
#, fuzzy
#| msgid "Load More"
msgid "Learn More"
msgstr "Загрузи больше"

#: extensions/meta-box/inc/about/sections/extensions.php:38
msgid ""
"Create repeatable groups of custom fields for better appearance and "
"structure."
msgstr ""

#: extensions/meta-box/inc/about/sections/extensions.php:50
msgid "Create a powerful settings page for your theme, plugin or website."
msgstr ""

#: extensions/meta-box/inc/about/sections/extensions.php:62
msgid ""
"Control the visibility of meta boxes and fields or even HTML elements with "
"ease."
msgstr ""

#: extensions/meta-box/inc/about/sections/extensions.php:74
msgid ""
"Create register, login and edit user profile forms in the frontend. Embed "
"everywhere with shortcodes."
msgstr ""

#: extensions/meta-box/inc/about/sections/extensions.php:86
msgid ""
"Create frontend forms for users to submit custom content. Embed everywhere "
"with shortcode."
msgstr ""

#: extensions/meta-box/inc/about/sections/extensions.php:98
msgid ""
"Save custom fields data to custom table. Reduce database size and increase "
"performance."
msgstr ""

#: extensions/meta-box/inc/about/sections/extensions.php:110
msgid "Create many-to-many relationships between posts, terms and users."
msgstr ""

#: extensions/meta-box/inc/about/sections/extensions.php:118
msgid "View all extensions"
msgstr ""

#: extensions/meta-box/inc/about/sections/getting-started-pro.php:4
msgid ""
"Please follow this video tutorial to get started with Meta Box and "
"extensions:"
msgstr ""

#: extensions/meta-box/inc/about/sections/getting-started-pro.php:8
#, fuzzy
#| msgid "Testimonials"
msgid "Tutorials"
msgstr "Отзывы"

#: extensions/meta-box/inc/about/sections/getting-started-pro.php:9
msgid ""
"We've made bunches of tutorials that come with videos, let's take a look to "
"have detailed guides to create custom fields and apply them in real cases."
msgstr ""

#: extensions/meta-box/inc/about/sections/getting-started-pro.php:11
msgid "Beginners"
msgstr ""

#: extensions/meta-box/inc/about/sections/getting-started-pro.php:11
msgid "Let’s start with some basic practices with Meta Box."
msgstr ""

#: extensions/meta-box/inc/about/sections/getting-started-pro.php:12
msgid "Case Studies"
msgstr ""

#: extensions/meta-box/inc/about/sections/getting-started-pro.php:12
msgid ""
"See how to use Meta Box in the real case studies with comprehensive "
"tutorials."
msgstr ""

#: extensions/meta-box/inc/about/sections/getting-started-pro.php:13
msgid "General Guide"
msgstr ""

#: extensions/meta-box/inc/about/sections/getting-started-pro.php:13
msgid "See how to use Meta box in common tasks."
msgstr ""

#: extensions/meta-box/inc/about/sections/getting-started-pro.php:14
#: extensions/meta-box/inc/about/sections/getting-started.php:15
#: extensions/meta-box/inc/about/sections/tabs.php:6
msgid "Extensions"
msgstr ""

#: extensions/meta-box/inc/about/sections/getting-started-pro.php:14
msgid ""
"Learn about Meta Box extensions, what features they offer and how to use "
"them."
msgstr ""

#: extensions/meta-box/inc/about/sections/getting-started-pro.php:15
#, fuzzy
#| msgid "Package Holder"
msgid "Page Builders"
msgstr "Держатель для упаковки"

#: extensions/meta-box/inc/about/sections/getting-started-pro.php:15
msgid ""
"Tutorials on combining Meta Box and other builders or tools for real case "
"studies."
msgstr ""

#: extensions/meta-box/inc/about/sections/getting-started-pro.php:16
#, fuzzy
#| msgid "View"
msgid "MB Views"
msgstr "Посмотреть"

#: extensions/meta-box/inc/about/sections/getting-started-pro.php:16
msgid ""
"Build front-end templates for WordPress without touching theme files. "
"Support Twig and all field types."
msgstr ""

#: extensions/meta-box/inc/about/sections/getting-started.php:6
msgid "Getting Started With Online Generator"
msgstr ""

#: extensions/meta-box/inc/about/sections/getting-started.php:7
msgid ""
"Online Generator is a free tool to help you create and set up custom fields "
"using a simple, friendly user interface. With it, you can add fields, set "
"options and generate needed code that's ready to copy and paste."
msgstr ""

#: extensions/meta-box/inc/about/sections/getting-started.php:9
msgid "online generator"
msgstr ""

#: extensions/meta-box/inc/about/sections/getting-started.php:11
msgid "Go to Online Generator"
msgstr ""

#: extensions/meta-box/inc/about/sections/getting-started.php:16
msgid ""
"Wanna see more features that transform your WordPress website into a "
"powerful CMS? Check out some extensions below:"
msgstr ""

#: extensions/meta-box/inc/about/sections/getting-started.php:18
msgid "Meta Box Builder"
msgstr ""

#: extensions/meta-box/inc/about/sections/getting-started.php:18
msgid "Build meta boxes and fields with UI."
msgstr ""

#: extensions/meta-box/inc/about/sections/getting-started.php:19
msgid "Meta Box Group"
msgstr ""

#: extensions/meta-box/inc/about/sections/getting-started.php:19
msgid "Organize fields into repeatable groups."
msgstr ""

#: extensions/meta-box/inc/about/sections/getting-started.php:20
msgid "Meta Box Conditional Logic"
msgstr ""

#: extensions/meta-box/inc/about/sections/getting-started.php:20
msgid "Control the visibility of fields."
msgstr ""

#: extensions/meta-box/inc/about/sections/getting-started.php:21
#, fuzzy
#| msgid "Settings"
msgid "MB Settings Page"
msgstr "настройки"

#: extensions/meta-box/inc/about/sections/getting-started.php:21
msgid "Create settings pages/Customizer options."
msgstr ""

#: extensions/meta-box/inc/about/sections/getting-started.php:27
msgid "More Extensions"
msgstr ""

#: extensions/meta-box/inc/about/sections/products.php:5
msgid "Our WordPress Products"
msgstr ""

#: extensions/meta-box/inc/about/sections/products.php:8
msgid "Like this plugin? Check out our other WordPress products:"
msgstr ""

#: extensions/meta-box/inc/about/sections/products.php:9
msgid "Automated & fast SEO plugin for WordPress"
msgstr ""

#: extensions/meta-box/inc/about/sections/products.php:10
msgid "The best schema plugin for WordPress"
msgstr ""

#: extensions/meta-box/inc/about/sections/products.php:11
msgid "Simple, elegant and clean WordPress themes"
msgstr ""

#: extensions/meta-box/inc/about/sections/review.php:5
msgid "Write a review for Meta Box"
msgstr ""

#: extensions/meta-box/inc/about/sections/review.php:8
msgid ""
"If you like Meta Box, please write a review on WordPress.org to help us "
"spread the word. We really appreciate that!"
msgstr ""

#: extensions/meta-box/inc/about/sections/review.php:9
msgid "Write a review"
msgstr ""

#: extensions/meta-box/inc/about/sections/support.php:12
#, php-format
msgid ""
"Still need help with Meta Box? We offer excellent support for you. But don't "
"forget to check our <a href=\"%s\">documentation</a> first."
msgstr ""

#: extensions/meta-box/inc/about/sections/support.php:17
msgid "Free Support"
msgstr ""

#: extensions/meta-box/inc/about/sections/support.php:18
msgid ""
"If you have any question about how to use the plugin, please open a new "
"topic on WordPress.org support forum or open a new issue on Github "
"(preferable). We will try to answer as soon as we can."
msgstr ""

#: extensions/meta-box/inc/about/sections/support.php:19
msgid "Go to Github"
msgstr ""

#: extensions/meta-box/inc/about/sections/support.php:20
msgid "Go to WordPress.org"
msgstr ""

#: extensions/meta-box/inc/about/sections/support.php:24
msgid "Premium Support"
msgstr ""

#: extensions/meta-box/inc/about/sections/support.php:25
msgid ""
"For users that have bought premium extensions, the support is provided in "
"the Meta Box Support forum. Any question will be answered with technical "
"details within 24 hours."
msgstr ""

#: extensions/meta-box/inc/about/sections/support.php:26
msgid "Go to support forum"
msgstr ""

#: extensions/meta-box/inc/about/sections/tabs.php:4
msgid "Getting Started"
msgstr ""

#: extensions/meta-box/inc/about/sections/tabs.php:7
#: extensions/meta-box/inc/about/sections/welcome.php:15
msgid "Support"
msgstr ""

#: extensions/meta-box/inc/about/sections/upgrade.php:4
msgid "Upgrade to Meta Box PRO"
msgstr ""

#: extensions/meta-box/inc/about/sections/upgrade.php:5
msgid "Please upgrade to the PRO plan to unlock more awesome features."
msgstr ""

#: extensions/meta-box/inc/about/sections/upgrade.php:7
msgid ""
"Create custom fields with drag-n-drop interface - no coding knowledge "
"required!"
msgstr ""

#: extensions/meta-box/inc/about/sections/upgrade.php:8
msgid "Add custom fields to taxonomies or user profile."
msgstr ""

#: extensions/meta-box/inc/about/sections/upgrade.php:9
msgid "Create custom settings pages."
msgstr ""

#: extensions/meta-box/inc/about/sections/upgrade.php:10
msgid "Create frontend submission forms."
msgstr ""

#: extensions/meta-box/inc/about/sections/upgrade.php:11
msgid "Save custom fields in custom tables."
msgstr ""

#: extensions/meta-box/inc/about/sections/upgrade.php:12
msgid "And much more!"
msgstr ""

#: extensions/meta-box/inc/about/sections/upgrade.php:14
msgid "Get Meta Box PRO now"
msgstr ""

#: extensions/meta-box/inc/about/sections/welcome.php:8
#, php-format
msgid "Welcome to %s"
msgstr ""

#: extensions/meta-box/inc/about/sections/welcome.php:11
msgid ""
"Meta Box is a free Gutenberg and GDPR-compatible WordPress custom fields "
"plugin and framework that makes quick work of customizing a website with—you "
"guessed it—meta boxes and custom fields in WordPress. Follow the instruction "
"below to get started!"
msgstr ""

#: extensions/meta-box/inc/about/sections/welcome.php:14
#, fuzzy
#| msgid "Location"
msgid "Documentation"
msgstr "Место нахождения"

#: extensions/meta-box/inc/about/sections/welcome.php:16
msgid "Facebook Group"
msgstr ""

#: extensions/meta-box/inc/about/sections/welcome.php:17
msgid "Youtube Channel"
msgstr ""

#: extensions/meta-box/inc/core.php:20
msgid "Docs"
msgstr ""

#: extensions/meta-box/inc/field.php:309
msgid "+ Add more"
msgstr ""

#: extensions/meta-box/inc/fields/background.php:61
msgid "Background Image"
msgstr ""

#: extensions/meta-box/inc/fields/background.php:73
msgid "-- Repeat --"
msgstr ""

#: extensions/meta-box/inc/fields/background.php:75
msgid "No Repeat"
msgstr ""

#: extensions/meta-box/inc/fields/background.php:76
msgid "Repeat All"
msgstr ""

#: extensions/meta-box/inc/fields/background.php:77
msgid "Repeat Horizontally"
msgstr ""

#: extensions/meta-box/inc/fields/background.php:78
msgid "Repeat Vertically"
msgstr ""

#: extensions/meta-box/inc/fields/background.php:79
#: extensions/meta-box/inc/fields/background.php:113
#: extensions/meta-box/inc/fields/background.php:125
msgid "Inherit"
msgstr ""

#: extensions/meta-box/inc/fields/background.php:89
msgid "-- Position --"
msgstr ""

#: extensions/meta-box/inc/fields/background.php:109
msgid "-- Attachment --"
msgstr ""

#: extensions/meta-box/inc/fields/background.php:111
msgid "Fixed"
msgstr ""

#: extensions/meta-box/inc/fields/background.php:112
msgid "Scroll"
msgstr ""

#: extensions/meta-box/inc/fields/background.php:123
msgid "-- Size --"
msgstr ""

#: extensions/meta-box/inc/fields/button.php:28
msgid "Click me"
msgstr ""

#: extensions/meta-box/inc/fields/file-input.php:17
msgid "Select File"
msgstr ""

#: extensions/meta-box/inc/fields/file.php:15
#, php-format
msgid "You may only upload maximum %d file"
msgstr ""

#: extensions/meta-box/inc/fields/file.php:17
#, php-format
msgid "You may only upload maximum %d files"
msgstr ""

#: extensions/meta-box/inc/fields/file.php:48
msgid "Error: Invalid file"
msgstr ""

#: extensions/meta-box/inc/fields/file.php:61
msgid "Error: Cannot delete file"
msgstr ""

#: extensions/meta-box/inc/fields/file.php:86
#, fuzzy
#| msgid "Add New Invoice"
msgctxt "file upload"
msgid "+ Add new file"
msgstr "Добавить новый счет-фактуру"

#: extensions/meta-box/inc/fields/file.php:166
msgctxt "file upload"
msgid "Delete"
msgstr ""

#: extensions/meta-box/inc/fields/file.php:167
msgctxt "file upload"
msgid "Edit"
msgstr ""

#: extensions/meta-box/inc/fields/icon.php:223
msgid "Select an icon"
msgstr ""

#: extensions/meta-box/inc/fields/input-list.php:83
msgid "Toggle All"
msgstr ""

#: extensions/meta-box/inc/fields/key-value.php:94
msgid "Key"
msgstr ""

#: extensions/meta-box/inc/fields/key-value.php:95
msgid "Value"
msgstr ""

#: extensions/meta-box/inc/fields/map.php:29
#: extensions/meta-box/inc/fields/osm.php:16
#, fuzzy
#| msgid "No Agent found"
msgid "No results found"
msgstr "Агент не найден"

#: extensions/meta-box/inc/fields/media.php:20
msgctxt "media"
msgid "+ Add Media"
msgstr ""

#: extensions/meta-box/inc/fields/media.php:21
msgctxt "media"
msgid " file"
msgstr ""

#: extensions/meta-box/inc/fields/media.php:22
msgctxt "media"
msgid " files"
msgstr ""

#: extensions/meta-box/inc/fields/media.php:23
msgctxt "media"
msgid "Remove"
msgstr ""

#: extensions/meta-box/inc/fields/media.php:24
msgctxt "media"
msgid "Edit"
msgstr ""

#: extensions/meta-box/inc/fields/media.php:25
#, fuzzy
#| msgid "View"
msgctxt "media"
msgid "View"
msgstr "Посмотреть"

#: extensions/meta-box/inc/fields/media.php:26
#, fuzzy
#| msgid "Title"
msgctxt "media"
msgid "No Title"
msgstr "заглавие"

#: extensions/meta-box/inc/fields/media.php:29
msgctxt "media"
msgid "Select Files"
msgstr ""

#: extensions/meta-box/inc/fields/media.php:30
msgctxt "media"
msgid "or"
msgstr ""

#: extensions/meta-box/inc/fields/media.php:31
msgctxt "media"
msgid "Drop files here to upload"
msgstr ""

#: extensions/meta-box/inc/fields/oembed.php:18
msgid "Embed HTML not available."
msgstr ""

#: extensions/meta-box/inc/fields/post.php:69
msgid "Select a post"
msgstr ""

#: extensions/meta-box/inc/fields/post.php:75
#: extensions/meta-box/inc/fields/taxonomy.php:88
#, php-format
msgid "Select a %s"
msgstr ""

#: extensions/meta-box/inc/fields/post.php:136
#: extensions/meta-box/inc/fields/taxonomy.php:134
#: extensions/meta-box/inc/fields/user.php:135
msgid "(No title)"
msgstr ""

#: extensions/meta-box/inc/fields/select-advanced.php:44
msgid "Select an item"
msgstr ""

#: extensions/meta-box/inc/fields/sidebar.php:10
msgid "Select a sidebar"
msgstr ""

#: extensions/meta-box/inc/fields/taxonomy.php:84
msgid "Select a term"
msgstr ""

#: extensions/meta-box/inc/fields/user.php:75
msgid "Select a user"
msgstr ""

#: extensions/meta-box/inc/fields/user.php:189
#, fuzzy
#| msgid "Add New Type"
msgid "Add New User"
msgstr "Добавить новый тип"

#: extensions/meta-box/inc/meta-box.php:275
msgid "Meta Box Title"
msgstr ""

#: extensions/meta-box/inc/validation.php:66
msgid "Please correct the errors highlighted below and try again."
msgstr ""

#: extensions/meta-box/js/select2/select2.min.js:2
msgid ""
"<span class=\"select2-dropdown\"><span class=\"select2-results\"></span></"
"span>"
msgstr ""

#: extensions/meta-box/src/Updater/Checker.php:104
msgid ""
"UPDATE UNAVAILABLE! Please enter a valid license key to enable automatic "
"updates."
msgstr ""

#: extensions/meta-box/src/Updater/Notification.php:59
#, php-format
msgid ""
"You have not set your Meta Box license key yet, which means you are missing "
"out on automatic updates and support! Please <a href=\"%1$s\">enter your "
"license key</a> or <a href=\"%2$s\" target=\"_blank\">get a new one here</a>."
msgstr ""

#: extensions/meta-box/src/Updater/Notification.php:61
#: extensions/meta-box/src/Updater/Notification.php:63
#, php-format
msgid ""
"Your license key for Meta Box is <b>invalid</b>. Please <a "
"href=\"%1$s\">update your license key</a> or <a href=\"%2$s\" "
"target=\"_blank\">get a new one</a> to enable automatic updates."
msgstr ""

#: extensions/meta-box/src/Updater/Notification.php:65
#, php-format
msgid ""
"Your license key for Meta Box is <b>expired</b>. Please <a href=\"%3$s\" "
"target=\"_blank\">renew your license</a> to get automatic updates and "
"premium support."
msgstr ""

#: extensions/meta-box/src/Updater/Notification.php:89
#, php-format
msgid ""
"Please <a href=\"%1$s\">enter your license key</a> or <a href=\"%2$s\" "
"target=\"_blank\">get a new one here</a>."
msgstr ""

#: extensions/meta-box/src/Updater/Notification.php:91
#: extensions/meta-box/src/Updater/Notification.php:93
#, php-format
msgid ""
"Your license key is <b>invalid</b>. Please <a href=\"%1$s\">update your "
"license key</a> or <a href=\"%2$s\" target=\"_blank\">get a new one here</a>."
msgstr ""

#: extensions/meta-box/src/Updater/Notification.php:95
#, php-format
msgid ""
"Your license key is <b>expired</b>. Please <a href=\"%3$s\" "
"target=\"_blank\">renew your license</a>."
msgstr ""

#: extensions/meta-box/src/Updater/Notification.php:111
#, fuzzy
#| msgid "Activation"
msgid "Activate License"
msgstr "активация"

#: extensions/meta-box/src/Updater/Notification.php:111
#, fuzzy
#| msgid "Update Settings"
msgid "Update License"
msgstr "Обновить настройки"

#: extensions/meta-box/src/Updater/Settings.php:39
#: extensions/meta-box/src/Updater/Settings.php:54
#, fuzzy
#| msgid "License"
msgid "Meta Box License"
msgstr "Лицензия"

#: extensions/meta-box/src/Updater/Settings.php:55
msgid ""
"Please enter your license key to enable automatic updates for Meta Box "
"extensions."
msgstr ""

#: extensions/meta-box/src/Updater/Settings.php:60
#, php-format
msgid ""
"To get the license key, visit the <a href=\"%1$s\" target=\"_blank\">My "
"Account</a> page on metabox.io website. If you have not purchased any "
"extension yet, please <a href=\"%2$s\" target=\"_blank\">get a new license "
"here</a>."
msgstr ""

#: extensions/meta-box/src/Updater/Settings.php:72
#, fuzzy
#| msgid "License"
msgid "License Key"
msgstr "Лицензия"

#: extensions/meta-box/src/Updater/Settings.php:76
#: extensions/meta-box/src/Updater/Settings.php:77
msgid "Your license key is <b style=\"color: #d63638\">invalid</b>."
msgstr ""

#: extensions/meta-box/src/Updater/Settings.php:78
msgid "Your license key is <b style=\"color: #d63638\">expired</b>."
msgstr ""

#: extensions/meta-box/src/Updater/Settings.php:79
msgid "Your license key is <b style=\"color: #00a32a\">active</b>."
msgstr ""

#: extensions/meta-box/src/Updater/Settings.php:123
msgid ""
"Something wrong with the connection to metabox.io. Please try again later."
msgstr ""

#: extensions/meta-box/src/Updater/Settings.php:125
msgid "Your license is activated."
msgstr ""

#: extensions/meta-box/src/Updater/Settings.php:128
#, php-format
msgid ""
"License expired. Please renew on the <a href=\"%s\" target=\"_blank\">My "
"Account</a> page on metabox.io website."
msgstr ""

#: extensions/meta-box/src/Updater/Settings.php:134
#, php-format
msgid ""
"Invalid license. Please <a href=\"%1$s\" target=\"_blank\">check again</a> "
"or <a href=\"%2$s\" target=\"_blank\">get a new license here</a>."
msgstr ""

#: functions/functions.php:236
msgid "Active & Sold"
msgstr ""

#: functions/functions.php:238
msgid "Sold"
msgstr ""

#: functions/functions.php:249
#, fuzzy
#| msgid "Title"
msgid "Title - ASC"
msgstr "заглавие"

#: functions/functions.php:250
msgid "Title - DESC"
msgstr ""

#: functions/functions.php:255
#, fuzzy
#| msgid "Featured"
msgid "Featured on Top"
msgstr "Рекомендуемые"

#: functions/functions.php:256
msgid "Featured on Top - Randomly"
msgstr ""

#: functions/functions.php:274
msgid "Base currency to get rates not found in database"
msgstr ""

#: functions/functions.php:318
msgid "Currency was not exist or found in database."
msgstr ""

#: functions/functions.php:326
msgid "Amount to covert is not number, it must be number."
msgstr ""

#: functions/functions.php:341
msgid ""
"Look like your API is not valid, There was a problem to get currency data "
"from database."
msgstr ""

#: functions/functions.php:386
msgid ""
"Please pass valid currency code for argument and it must be a string of "
"three characters long"
msgstr ""

#: functions/functions.php:396
msgid "Currency could not be found"
msgstr ""

#: functions/functions.php:697 functions/functions.php:698
msgctxt "listing post status"
msgid "Draft"
msgstr ""

#: functions/functions.php:701 functions/functions.php:702
#, fuzzy
#| msgid "Activation"
msgctxt "listing post status"
msgid "Active"
msgstr "активация"

#: functions/functions.php:705 functions/functions.php:734
msgctxt "listing post status"
msgid "Pending"
msgstr ""

#: functions/functions.php:706
msgctxt "listing post status"
msgid "Pending approval"
msgstr ""

#: functions/functions.php:709 functions/functions.php:710
#, fuzzy
#| msgid "Expire"
msgctxt "listing post status"
msgid "Expired"
msgstr "истекать"

#: functions/functions.php:713 functions/functions.php:714
#, fuzzy
#| msgid "Approve"
msgctxt "listing post status"
msgid "Disapproved"
msgstr "Одобрить"

#: functions/functions.php:717 functions/functions.php:718
msgctxt "listing post status"
msgid "On Hold"
msgstr ""

#: functions/functions.php:721 functions/functions.php:722
msgctxt "listing post status"
msgid "Sold"
msgstr ""

#: functions/functions.php:725 functions/functions.php:726
msgctxt "listing post status"
msgid "Preview"
msgstr ""

#: functions/functions.php:731
#, php-format
msgid "Preview <span class=\"count\">(%s)</span>"
msgid_plural "Preview <span class=\"count\">(%s)</span>"
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: functions/functions.php:735
#, fuzzy
#| msgid "Payment"
msgctxt "listing post status"
msgid "Pending payment"
msgstr "Оплата"

#: functions/functions.php:740
#, php-format
msgid "Pending Payment <span class=\"count\">(%s)</span>"
msgid_plural "Pending Payment <span class=\"count\">(%s)</span>"
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: functions/functions.php:743 functions/functions.php:744
msgctxt "listing post status"
msgid "Trash"
msgstr ""

#: functions/helpers.php:440 functions/helpers.php:530
#, fuzzy
#| msgid "View Agent"
msgid "View All"
msgstr "Агент просмотра"

#: shortcodes/price-table.php:113
msgid "Images"
msgstr ""

#: statistics/houzez-statistics.php:9
msgid "Houzez Statistics"
msgstr "Статистика Houzez"

#: statistics/houzez-statistics.php:10 statistics/houzez-statistics.php:108
msgid "Most Viewed Properties"
msgstr "Самые просматриваемые свойства"

#: statistics/houzez-statistics.php:10
msgid "Most Viewed"
msgstr "Наиболее просматриваемые"

#: statistics/houzez-statistics.php:11 statistics/houzez-statistics.php:233
msgid "Most Favourite Properties"
msgstr "Самые любимые свойства"

#: statistics/houzez-statistics.php:11
msgid "Most Favourite"
msgstr "Самые любимые"

#: statistics/houzez-statistics.php:12 statistics/houzez-statistics.php:348
msgid "Saved Searches"
msgstr "Сохраненные поиски"

#: statistics/houzez-statistics.php:40 statistics/houzez-statistics.php:95
#: statistics/houzez-statistics.php:215 statistics/houzez-statistics.php:343
#: statistics/houzez-statistics.php:574
msgid "You do not have sufficient permissions to access this page."
msgstr "У вас недостаточно прав для доступа к этой странице."

#: statistics/houzez-statistics.php:48
msgid "Houzez Statistic"
msgstr "Houzez Statistics"

#: statistics/houzez-statistics.php:52 statistics/houzez-statistics.php:78
msgid "Total Views Today"
msgstr "Просмотров сегодня"

#: statistics/houzez-statistics.php:54
msgid "Users"
msgstr "пользователей"

#: statistics/houzez-statistics.php:59
msgid "Buyers"
msgstr "Покупатели"

#: statistics/houzez-statistics.php:65
msgid "Sellers"
msgstr "Продавцы"

#: statistics/houzez-statistics.php:68
msgid "Owners"
msgstr "Владельцы"

#: statistics/houzez-statistics.php:71
msgid "Managers"
msgstr "Менеджеры"

#: statistics/houzez-statistics.php:121 statistics/houzez-statistics.php:198
msgid "Views Count"
msgstr "Количество просмотров"

#: statistics/houzez-statistics.php:177 statistics/houzez-statistics.php:304
#: statistics/houzez-statistics.php:541
msgid "View"
msgstr "Посмотреть"

#: statistics/houzez-statistics.php:246
msgid "Favourite Count "
msgstr "Любимый подсчет"

#: statistics/houzez-statistics.php:326
msgid "Favourite Count"
msgstr "Любимый подсчет"

#: statistics/houzez-statistics.php:353 statistics/houzez-statistics.php:556
msgid "Search Query"
msgstr "Поисковый запрос"

#: statistics/houzez-statistics.php:402 statistics/houzez-statistics.php:582
msgid "Location"
msgstr "Место нахождения"

#: statistics/houzez-statistics.php:426
msgid "Neighborhood"
msgstr "окрестности"

#: statistics/houzez-statistics.php:548
msgid "No Search found"
msgstr "Поиск не найдено"

#: statistics/houzez-statistics.php:586
msgid "Feature"
msgstr "Особенность"

#: statistics/houzez-statistics.php:587 statistics/houzez-statistics.php:588
msgid "Max Size"
msgstr "Максимальный размер"

#: statistics/houzez-statistics.php:596
msgid "Houzez Searches"
msgstr "Houzez Searches"

#: statistics/houzez-statistics.php:601
msgid "Search Keyworks"
msgstr "Поиск по ключевым словам"

#: templates/currency/currency-list.php:3 templates/currency/form.php:16
#, fuzzy
#| msgid "Agencies"
msgid "Currencies"
msgstr "агентства"

#: templates/currency/currency-list.php:11
msgid "Code"
msgstr ""

#: templates/currency/currency-list.php:12
msgid "Symbol"
msgstr ""

#: templates/currency/currency-list.php:14
msgid "N. of Decimal Point"
msgstr ""

#: templates/currency/currency-list.php:15
msgid "Decimal Point Separator"
msgstr ""

#: templates/currency/currency-list.php:16
msgid "Thousands Separator"
msgstr ""

#: templates/currency/currency-list.php:41
#: templates/fields-builder/index.php:39
#, fuzzy
#| msgid "Edit Invoice"
msgid "Edit field"
msgstr "Изменить счет-фактуру"

#: templates/currency/currency-list.php:46
#: templates/fields-builder/index.php:44
msgid "Delete field"
msgstr ""

#: templates/currency/form.php:5
#, fuzzy
#| msgid "Update Settings"
msgid "Update Currency"
msgstr "Обновить настройки"

#: templates/currency/form.php:6 templates/fields-builder/fields-form.php:6
msgid "Update"
msgstr ""

#: templates/currency/form.php:9
msgid "Create Currency"
msgstr ""

#: templates/currency/form.php:26
#, fuzzy
#| msgid "Agent Name"
msgid "Currency Name"
msgstr "Имя агента"

#: templates/currency/form.php:28
msgid "Enter currency name (ie: United States Dollar)"
msgstr ""

#: templates/currency/form.php:33
msgid "Currency Code"
msgstr ""

#: templates/currency/form.php:35
msgid "Enter currency code (ie: USD)"
msgstr ""

#: templates/currency/form.php:39
msgid "Currency Symbol"
msgstr ""

#: templates/currency/form.php:41
msgid "Enter currency symbol (ie: $)"
msgstr ""

#: templates/currency/form.php:45
msgid "Currency Position"
msgstr ""

#: templates/currency/form.php:46
msgid "Before"
msgstr ""

#: templates/currency/form.php:46
msgid "After"
msgstr ""

#: templates/currency/form.php:51
msgid "Number of decimal points?"
msgstr ""

#: templates/currency/form.php:69
msgid "Decimal Point Separator(eg: .)"
msgstr ""

#: templates/currency/form.php:74
msgid "Thousands Separator(eg: ,)"
msgstr ""

#: templates/fields-builder/fields-form.php:5
msgid "Update field"
msgstr ""

#: templates/fields-builder/fields-form.php:9
msgid "Create field"
msgstr ""

#: templates/fields-builder/fields-form.php:15
#: templates/fields-builder/index.php:4
msgid "Fields Builder"
msgstr ""

#: templates/fields-builder/fields-form.php:26
#: templates/fields-builder/index.php:11
#, fuzzy
#| msgid "Agent Name"
msgid "Field Name"
msgstr "Имя агента"

#: templates/fields-builder/fields-form.php:29
msgid "Enter field name"
msgstr ""

#: templates/fields-builder/fields-form.php:35
msgid "Enter field placeholder"
msgstr ""

#: templates/fields-builder/fields-form.php:41
msgid "-- Choose field type --"
msgstr ""

#: templates/fields-builder/fields-form.php:50
msgid "Please add comma separated options. Example: One, Two, Three"
msgstr ""

#: templates/fields-builder/fields-form.php:61
msgid "Make available for searches?"
msgstr ""

#: templates/fields-builder/index.php:12
msgid "Field ID"
msgstr ""

#: templates/fields-builder/multiple.php:9
msgid "Enter Value"
msgstr ""

#: templates/locations/form.php:14
#, fuzzy
#| msgid "Location"
msgid "Import Locations"
msgstr "Место нахождения"

#: templates/locations/form.php:27
msgid "Choose CSV File"
msgstr ""

#: templates/locations/form.php:36
msgid "Fetch CSV"
msgstr ""

#~ msgid "Portfolio Settings"
#~ msgstr "Настройки портфолио"

#~ msgid "Activated Successfully, reload page!"
#~ msgstr "Активировано успешно, перезагрузите страницу!"

#~ msgid "Payment For"
#~ msgstr "Платеж за"

#~ msgid "Recurring"
#~ msgstr "Периодическая"

#~ msgid "Add Property Neighborhood"
#~ msgstr "Добавить соседство собственности"

#~ msgid "New Property Neighborhood"
#~ msgstr "Новая недвижимость"

#~ msgid "Add Property County / State"
#~ msgstr "Добавить собственность"

#~ msgid "New Property County / State"
#~ msgstr "New Property County / State"
