/* Main Page Container */
.mls-otf-main-page-container {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    overflow-x: hidden;
}

/* Content Wrapper */
.mls-otf-main-page-content-wrapper {
    display: flex;
    justify-content: center;
    padding: 1.5rem;
    flex: 1;
}

.mls-otf-main-page-center-content {
    flex: 1;
}

/* Top Section Styling */
.mls-otf-main-page-top-section {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.mls-otf-main-page-half-section {
    flex: 1;
    padding: 2rem;
    border-radius: 0.75rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    text-align: center;
}

/* Titles & Descriptions */
.mls-otf-main-page-title {
    font-size: 2rem;
    font-weight: bold;
    color: #fff;
    margin-bottom: 1rem;
}

.mls-otf-main-page-description {
    font-size: 1rem;
    color: #fff;
    margin-bottom: 1.5rem;
}

/* Primary & Secondary Buttons */
.mls-otf-main-page-btn-primary {
    background-color: #8d42f7;
    color: #faf8fc;
    padding: 0.75rem 1.25rem;
    border-radius: 0.75rem;
    font-size: 0.875rem;
    border: none;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.mls-otf-main-page-btn-primary:hover {
    background-color: #6a32c2;
}

.mls-otf-main-page-btn-secondary {
    background-color: #ece7f4;
    color: #130d1c;
    padding: 0.75rem 1.25rem;
    border-radius: 0.75rem;
    border: none;
    font-size: 0.875rem;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.mls-otf-main-page-btn-secondary:hover {
    background-color: #d1cde7;
}

/* Section Title */
.mls-otf-main-page-section-title {
    font-size: 1.375rem;
    font-weight: bold;
    color: #130d1c;
    margin-top: 2rem;
}

/* Grid Container for Images */
.mls-otf-main-page-grid-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(158px, 1fr));
    gap: 1rem;
}

.mls-otf-main-page-grid-item {
    width: 100%;
    min-height: 250px;
    background-position: center;
    background-size: cover;
    border-radius: 0.75rem;
}

/* Documentation Item */
.mls-otf-main-page-doc-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.2rem;
    background-color: #faf8fc;
    border-radius: 0.75rem;
    margin-top: 0.2rem;
}

.mls-otf-main-page-doc-content {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.mls-otf-main-page-doc-text {
    display: flex;
    flex-direction: column;
}

.mls-otf-main-page-doc-title {
    font-size: 1rem;
    font-weight: bold;
    color: #130d1c;
}

/* Demo and Real Data Sections */
.mls-otf-main-page-demo-section,
.mls-otf-main-page-real-section {
    position: relative;
    background-position-y: bottom;
    background-size: cover;
    padding: 6rem;
    border-radius: 0.75rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    text-align: center;
    overflow: hidden;
}

.mls-otf-main-page-demo-section {
    background-image: url('../image/demo-data.webp');
}

.mls-otf-main-page-real-section {
    background-image: url('../image/real-data.webp');
}

.mls-otf-main-page-demo-section::before,
.mls-otf-main-page-real-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1;
}

.mls-otf-main-page-title,
.mls-otf-main-page-description,
.mls-otf-main-page-btn-primary,
.mls-otf-main-page-btn-secondary {
    position: relative;
    z-index: 2;
}

/* Flag Icons */
.us-flag {
    background-image: url("../image/us.webp");
}

.canada-flag {
    background-image: url("../image/canada.webp");
}

.mexico-flag {
    background-image: url("../image/mexico.webp");
}


.ecuador-flag {
    background-image: url("../image/ecuador.webp");
}

/* Document Icon */
.mls-otf-main-page-icon {
    background-image: url(../image/document.png);
    width: 40px;
    height: 40px;
    background-size: contain;
    background-repeat: no-repeat;
}

/* Remove Underline for Links */
a.mls-otf-main-page-btn-secondary {
    text-decoration: none;
}

/* Centered Heading */
.mls-otf-main-page-heading {
    text-align: center;
    color: #8d42f7;
}

/* Multi-Step Wizard Container */
.mls-otf-wizard-container {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 2rem;
    box-sizing: border-box;
}

.mls-otf-wizard-step {
    display: none;
    width: 100%;
    padding: 1.5rem;
    border-radius: 0.75rem;
}

.mls-otf-wizard-step.active {
    display: block;
}

/* Progress Bar */
.progress-bar {
    display: flex;
    justify-content: center;
    margin-bottom: 2rem;
}

.progress-bar .step {
    width: 25px;
    height: 25px;
    border-radius: 50%;
    background-color: #ececec;
    margin: 0 1rem;
    position: relative;
}

.progress-bar .step.active {
    background-color: #8d42f7;
}

.progress-bar .step::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    background-color: #8d42f7;
    transform: translate(-50%, -50%) scale(0);
    border-radius: 50%;
    transition: transform 0.3s ease;
}

.progress-bar .step.active::before {
    transform: translate(-50%, -50%) scale(1);
}

/* Wizard Buttons */
.mls-otf-button-group {
    gap: 20px;
    display: flex;
    justify-content: right;
    margin-bottom: 20px;
}

.mls-otf-wizard-btn {
    padding: 10px 20px;
    font-size: 16px;
    border: none;
    background-color: #007bff;
    color: white;
    cursor: pointer;
    border-radius: 5px;
}

.mls-otf-wizard-btn.btn-close, .mls-otf-wizard-btn.realdata-btn-close {
    background-color: white;
    border: 2px solid #dc3545;
    color: #dc3545;
}

.mls-otf-wizard-btn:hover {
    opacity: 0.9;
}

.btn-prev, .realdata-btn-prev {
    background-color: white;
    border: 2px solid #8d42f7;
    color: #8d42f7;
}


.btn-next, .realdata-btn-next {
    background-color: #8d42f7;
    border: 2px solid #8d42f7;
    color: white;
}

.submit-keys-btn {
    background-color: #8d42f7;
    border: 2px solid #8d42f7;
    color: white;
}


/* Data Tracking Message */
.mls-otf-data-tracking-message {
    margin-top: 20px;
    padding: 15px;
    background-color: #f9f9f9;
    border-top: 1px solid #ddd;
    font-size: 14px;
    text-align: center;
    color: #555;
}

#error-message {
    color: red;
    padding: 10px 0 0 10px;
    border-left: 10px solid red;
    font-size: 18px !important;
}

#support-guide {
    color: black;
    padding: 10px 0 0 10px;
    margin-bottom: 20px;
    border-left: 10px solid #8d42f7;
}

#support-guide * {
    font-size: 18px !important;
    margin: 0 !important;
}

.mls-otf-all-set-content {
    text-align: center;
    padding: 20px;
}

.mls-otf-all-set-content h2 {
    font-size: 2rem;
    font-weight: bold;
    color: #8d42f7; /* Optional: Customize as needed */
    margin-bottom: 20px;
}

.mls-otf-all-set-content p {
    font-size: 1.1rem;
    line-height: 1.6;
    color: #555; /* Optional: Customize as needed */
    margin-bottom: 20px;
}

.mls-otf-all-set-content strong {
    font-weight: bold;
    color: #007bff; /* Optional: Customize as needed */
}


.properties-notes-wrapper {
    margin-top: 30px;
}

.properties-notes-list {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.property-note-card {
    display: flex;
    flex-wrap: wrap;
    gap: 40px;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background-color: #fff;
    box-shadow: 0 1px 4px rgba(0,0,0,0.06);
}

.property-note-left {
    flex: 1;
    min-width: 250px;
}

.property-note-left img {
    max-width: 100%;
    max-height: 180px;
    object-fit: cover;
    border-radius: 4px;
    margin-bottom: 10px;
}

.property-note-left p {
    margin: 6px 0;
}

.property-note-right {
    flex: 1;
    min-width: 250px;
}

.property-notes-table {
    width: 100%;
    border-collapse: collapse;
    background-color: #f9f9f9;
}

.property-notes-table th,
.property-notes-table td {
    padding: 10px;
    border: 1px solid #ccc;
    text-align: left;
}

.property-notes-table th {
    background-color: #f1f1f1;
    font-weight: bold;
}

.edit-notes-btn {
    margin-top: 15px;
}

.properties-pagination {
    margin-top: 30px;
    text-align: center;
}

.properties-pagination .page-numbers {
    display: inline-block;
    padding: 8px 12px;
    margin: 0 4px;
    border: 1px solid #ccc;
    border-radius: 4px;
    text-decoration: none;
    color: #333;
    background: #f1f1f1;
    transition: background 0.2s ease;
}

.properties-pagination .page-numbers:hover {
    background: #e1e1e1;
}

.properties-pagination .current {
    background: #0073aa;
    color: #fff;
    border-color: #0073aa;
    font-weight: bold;
}


.edit-notes-modal-wrapper {
    position: fixed;
    top: 0; left: 0; right: 0; bottom: 0;
    z-index: 9999;
}

.edit-notes-modal {
    position: relative;
    max-width: 600px;
    margin: 80px auto;
    background: #fff;
    padding: 20px 30px;
    border-radius: 8px;
    z-index: 10001;
    box-shadow: 0 5px 25px rgba(0,0,0,0.2);
}

.modal-backdrop {
    position: fixed;
    top: 0; left: 0; right: 0; bottom: 0;
    background: rgba(0,0,0,0.4);
    z-index: 10000;
}

.note-row {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
}

.note-row input[type="text"] {
    flex: 1;
}

.note-row .remove-note-row {
    background: #dc3545;
    color: #fff;
    border: none;
    padding: 4px 10px;
    font-size: 16px;
    cursor: pointer;
    border-radius: 3px;
}

.edit-notes-btn, .delete-notes-btn{
    margin-top: 10px !important;
}