/* Reset some default styling */
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f4f4f4;
}

/* Dashboard Styles Begin */
.realtyna-base-plugin-dashboard-wrapper {
    width: calc(100% + 20px);
    margin: -20px;
    padding-top: 100px;
    position: relative;
    font-size: 14px;
    font-weight: 500;
    line-height: 24px;
    font-family: "Montserrat", sans-serif;
    font-optical-sizing: auto;
    font-style: normal;
    color: #1D2327;
}

.realtyna-base-plugin-dashboard-wrapper .realtyna-base-plugin-dashboard-header-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: end;
    background: linear-gradient(92.11deg, #3C2B7D 17.71%, #5F43CB 88.87%);
    padding: 80px 20px 200px 20px;
    position: absolute;
    width: calc(100% - 40px);
    z-index: -1;
    top: 0;
}

.realtyna-base-plugin-dashboard-wrapper .realtyna-base-plugin-dashboard-header-wrapper .realtyna-base-plugin-dashboard-header-sub-title {
    color: #fff;
    font-size: 14px;
    font-weight: 400;
}

.realtyna-base-plugin-dashboard-wrapper .realtyna-base-plugin-dashboard-header-wrapper h2 {
    font-size: 32px;
    color: #fff;
    font-weight: 700;
    margin: 0;
    line-height: 1.3;
}

.realtyna-base-plugin-dashboard-wrapper .realtyna-base-plugin-dashboard-content-wrapper {
    position: relative;
    margin: 50px 20px 20px 20px;
    background-color: #fff;
    padding: 20px;
    border-radius: 32px;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
    overflow-x: auto;
    overflow-y: hidden;
    min-height: 85vh;
}

.realtyna-base-plugin-dashboard-wrapper .realtyna-base-plugin-dashboard-content-wrapper h3 {
    margin-bottom: 20px;
}

.realtyna-base-plugin-dashboard-wrapper #realtyna-base-plugin-update-settings {
    position: absolute;
    top: 15px;
    right: 15px;
}

.realtyna-base-plugin-dashboard-wrapper .realtyna-base-plugin-tab-headers {
    max-width: calc(100% - 120px);
}

/* Dashboard Styles End */

/* Tab styles Begin */
.realtyna-base-plugin-tabs-wrapper .realtyna-base-plugin-tab-headers {
    display: inline-flex;
    gap: 20px;
}

.realtyna-base-plugin-tabs-wrapper .realtyna-base-plugin-tab-headers a {
    text-decoration: none;
    color: #1D2327;
    padding-top: 10px;
    padding-bottom: 10px;
    font-weight: 700;
    font-size: 14px;
}

.realtyna-base-plugin-tabs-wrapper .realtyna-base-plugin-tab-headers a:focus,
.realtyna-base-plugin-tabs-wrapper .realtyna-base-plugin-tab-headers a:focus-within,
.realtyna-base-plugin-tabs-wrapper .realtyna-base-plugin-tab-headers a:focus-visible {
    box-shadow: none;
}

.realtyna-base-plugin-tabs-wrapper .realtyna-base-plugin-tab-headers a.active:focus,
.realtyna-base-plugin-tabs-wrapper .realtyna-base-plugin-tab-headers a.active:focus-within,
.realtyna-base-plugin-tabs-wrapper .realtyna-base-plugin-tab-headers a.active:focus-visible,
.realtyna-base-plugin-tabs-wrapper .realtyna-base-plugin-tab-headers a.active {
    color: #8E42F7;
    border-bottom: 2px solid;
}

.realtyna-base-plugin-tabs-wrapper .realtyna-base-plugin-tab-contents-wrapper {
    padding-top: 40px;
}

.realtyna-base-plugin-tabs-wrapper .realtyna-base-plugin-tab-contents-wrapper .realtyna-base-plugin-tab-content:not(.active) {
    display: none;
}

/* Tab styles End */

/* Fields styles Begin */
.realtyna-core-field-wrapper {
    position: relative;
    margin-bottom: 30px;
}

.realtyna-core-field-wrapper .realtyna-base-plugin-input-wrapper,
.realtyna-core-field-wrapper .select2,
.realtyna-core-field-wrapper.realtyna-base-plugin-filter-group-title-wrapper,
.realtyna-base-plugin-input-wrapper {
    position: relative;
    width: 100% !important;
    max-width: 540px;
}

.realtyna-core-field-wrapper .realtyna-core-field-label {
    display: block;
    font-size: 14px;
    line-height: 20px;
    font-weight: 500;
    margin-bottom: 5px;
}

.realtyna-core-field-wrapper input[type="text"],
.realtyna-core-field-wrapper input[type="email"],
.realtyna-core-field-wrapper input[type="password"],
.realtyna-core-field-wrapper input[type="number"],
.realtyna-core-field-wrapper input[type="date"],
.realtyna-core-field-wrapper input[type="tel"] {
    display: block;
    position: relative;
    width: 100%;
    max-width: 540px;
    border-radius: 8px;
    border-width: 1px;
    padding: 10px 14px;
    border-color: #C6C6C6;
    color: #8D8D8D;
    line-height: 24px;
    font-size: 16px;
}

.realtyna-core-field-wrapper .realtyna-core-field-description {
    font-size: 14px;
    line-height: 20px;
    font-style: italic;
    font-weight: 500;
}

.realtyna-core-field-wrapper input[type="radio"] {
    width: 20px;
    height: 20px;
    margin-right: 10px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
}

.realtyna-core-field-wrapper input[type="radio"]:checked::before {
    width: 14px;
    height: 14px;
    margin: 0;
    padding: 0;
    background-color: #644BC4;
}

.realtyna-base-plugin-dashboard-wrapper input[type="checkbox"]:checked:before,
.realtyna-core-field-wrapper input[type="checkbox"]:checked:before {
    margin: 3.5px 0 0 3px;
    height: 10px;
    width: 10px;
    content: url(data:image/svg+xml;base64,PHN2ZyB2aWV3Qm94PSIwIDAgNyA1IiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8cGF0aCBkPSJNNi4xMzgwNyAxLjQ3MTUzQzYuMzk4NDEgMS4yMTExOCA2LjM5ODQxIDAuNzg5MDYgNi4xMzgwNyAwLjUyODcxNkM1Ljg3NzczIDAuMjY4MzcyIDUuNDU1NiAwLjI2ODM3MiA1LjE5NTI2IDAuNTI4NzE2TDIuMzMzMzIgMy4zOTA2NUwxLjEzODA3IDIuMTk1MzdDMC44Nzc3MjYgMS45MzUwMyAwLjQ1NTYwMiAxLjkzNTAzIDAuMTk1MjU4IDIuMTk1MzdDLTAuMDY1MDg1OSAyLjQ1NTcyIC0wLjA2NTA4NTkgMi44Nzc4NCAwLjE5NTI1OCAzLjEzODE4TDEuODYxOTUgNC44MDQ4NEMyLjEyMjI5IDUuMDY1MTggMi41NDQ0MSA1LjA2NTE4IDIuODA0NzYgNC44MDQ4NEw2LjEzODA3IDEuNDcxNTNaIiBmaWxsPSIjMjAyMDIwIi8+Cjwvc3ZnPgo=);
}

.realtyna-core-field-wrapper select:active,
.realtyna-core-field-wrapper select {
    padding: 10px 30px 10px 14px;
    border: 1px solid #C6C6C6;
    border-radius: 8px;
    line-height: 24px;
    color: #525252;
}

.realtyna-core-field-wrapper .select2-container .select2-selection--single {
    width: 100%;
    height: auto;
    padding: 10px 30px 10px 14px;
    border-radius: 8px;
}

.realtyna-core-field-wrapper .select2-container .select2-selection--single .select2-selection__arrow {
    top: 12px;
}

.select2-container .select2-results__options {
    padding-top: 20px;
    padding-bottom: 20px;
    color: #161616;
}

.select2-container .select2-results__options .select2-results__option {
    padding: 5px 10px;
}

.select2-container .select2-results__options .select2-results__option--selected,
.select2-container .select2-results__options .select2-results__option--highlighted {
    color: #433FFF;
    background: linear-gradient(248.26deg, rgba(99, 2, 221, 0.07) 0.82%, rgba(67, 63, 255, 0.07) 74.81%);
}

.realtyna-core-field-wrapper .select2-container ul.list li.disabled {
    display: none;
}

/* Fields styles End */

/* Input Actions Begin */
.realtyna-core-field-wrapper .realtyna-base-plugin-input-open-actions {
    content: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNCIgaGVpZ2h0PSIxNiIgdmlld0JveD0iMCAwIDQgMTYiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0yIDRDMy4xIDQgNCAzLjEgNCAyQzQgMC45IDMuMSAwIDIgMEMwLjkgMCAwIDAuOSAwIDJDMCAzLjEgMC45IDQgMiA0Wk0yIDZDMC45IDYgMCA2LjkgMCA4QzAgOS4xIDAuOSAxMCAyIDEwQzMuMSAxMCA0IDkuMSA0IDhDNCA2LjkgMy4xIDYgMiA2Wk0yIDEyQzAuOSAxMiAwIDEyLjkgMCAxNEMwIDE1LjEgMC45IDE2IDIgMTZDMy4xIDE2IDQgMTUuMSA0IDE0QzQgMTIuOSAzLjEgMTIgMiAxMloiIGZpbGw9ImJsYWNrIi8+Cjwvc3ZnPgo=");
    position: absolute;
    top: 10px;
    right: 10px;
    cursor: pointer;
    padding: 5px;
}

.realtyna-core-field-wrapper .realtyna-base-plugin-item-actions {
    display: none;
    position: absolute;
    top: 35px;
    right: 10px;
    padding-top: 10px;
    padding-bottom: 10px;
    background: #fff;
    box-shadow: 0px 4px 12px 0px #00000033;
    border-radius: 8px;
    z-index: 10;
}

.realtyna-core-field-wrapper.realtyna-base-plugin-open-actions .realtyna-base-plugin-item-actions {
    display: block;
}

.realtyna-core-field-wrapper .realtyna-base-plugin-item-actions ul li {
    padding: 5px 15px;
    margin: 0;
    cursor: pointer;
}

.realtyna-core-field-wrapper .realtyna-base-plugin-item-actions ul li:hover {
    background: linear-gradient(248.26deg, rgba(99, 2, 221, 0.07) 0.82%, rgba(67, 63, 255, 0.07) 74.81%);
    color: #6302DD;
    fill: #6302DD;
}

/* Input Actions End */

/* Button Styles Begin */
.mls-on-the-fly-btn {
    padding: 5px 15px;
    cursor: pointer;
    border-radius: 8px;
    line-height: 24px;
    margin-bottom: 10px;
    text-decoration: none;
}

.mls-on-the-fly-btn:hover {
    opacity: .7;
}

.mls-on-the-fly-btn.mls-on-the-fly-btn-primary {
    background-color: #6302DD;
    border: 1px solid #6302DD;
    color: #fff;
}

.mls-on-the-fly-btn.mls-on-the-fly-btn-primary:hover {
    background-color: #6302DD;
}

.mls-on-the-fly-btn.mls-on-the-fly-btn-transparent {
    background-color: transparent;
    border: 1px solid #fff;
    color: #fff;
}

.mls-on-the-fly-btn.mls-on-the-fly-btn-transparent:hover {
    background-color: #fff;
    color: #1D2327;
}

.mls-on-the-fly-btn.mls-on-the-fly-btn-success {
    background-color: #28a745;
    border: 1px solid #28a745;
    color: #fff;
}

.mls-on-the-fly-btn.mls-on-the-fly-btn-success:hover {
    background-color: #28a745;
}

/* Button Styles End */

/* Taxonomy Styles Begin */
.taxonomy-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
}

.taxonomy-box {
    display: flex;
    gap: 5px;
    justify-content: space-between;
    width: calc(33% - 20px);
    border: 1px solid #ccc;
    padding: 20px;
    box-sizing: border-box;
    background-color: #fff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    color: #fff;
    background: linear-gradient(270.52deg, #8E41F8 24.72%, #3C2B7D 94.16%);
}

.taxonomy-box > div {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.taxonomy-box .box-1 {

}

.taxonomy-box .box-2 {
    min-width: 80px;
    text-align: right;
    font-size: 12px;
    font-weight: 500;
}

.taxonomy-box .box-2 .total-cache {
    font-size: 36px;
    font-weight: 600;
}

.taxonomy-box .box-2 .last-updated label {
    display: block;
    text-shadow: 0px 2px #1E5E5699;
}

.clearfix::after {
    content: "";
    clear: both;
    display: table;
}

.taxonomy-box strong {
    display: block;
    margin-bottom: 10px;
    font-size: 16px;
}

.taxonomy-box p {
    margin: 10px 0;
}

.taxonomy-box form {
    /* display: flex;
    flex-direction: column;
    align-items: center; */
}

/* Taxonomy Styles End */

/*GLOBAL FILTER STYLE START*/

#realtyna-base-plugin-mls-on-the-fly-global-filters textarea {
    width: 100%;
}

#realtyna-base-plugin-mls-on-the-fly-global-filters input {
    font-size: 14px;
    padding: 5px 15px;
    background-color: #6302DD;
    border: 1px solid #6302DD;
    color: #fff;
    cursor: pointer;
    border-radius: 8px;
}

/*GLOBAL FILTER STYLE END*/

/* Modal Styles Begin */
.realtyna-base-plugin-modal:not(.show) {
    display: none;
}

.realtyna-base-plugin-modal .modal-content {
    background-color: #fff;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
}

.realtyna-base-plugin-modal .modal-content .modal-footer button.realtyna-base-plugin-close-modal-button {
    background: #fff;
    color: #161616;
    border-color: #C6C6C6;
}

.realtyna-base-plugin-modal .modal-header .realtyna-base-plugin-close-modal-button {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    border-width: 0;
    border-radius: 50%;
}

.realtyna-base-plugin-modal .modal-header .realtyna-base-plugin-close-modal-button:hover {
    color: #000;
}

.realtyna-base-plugin-modal .modal-footer .realtyna-base-plugin-close-modal-button {

}

.realtyna-base-plugin-modal .modal-dialog {
    top: 50%;
}

.realtyna-base-plugin-modal .modal-content .modal-footer {
    border-top: 0;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
}

.realtyna-base-plugin-modal .modal-content .modal-footer button {
    width: calc(50% - 10px);
    padding: 10px 5px;
    border-radius: 8px;
    border-width: 1px;
    font-weight: 600;
    line-height: 24px;
}

/* Modal Styles End */

.realtyna-base-plugin-filter-buttons {
    padding: 0px 5px 10px 5px;
}

.realtyna-base-plugin-filter-buttons a {
    text-decoration: none;
    color: #1D2327;
    fill: #1D2327;
}

.realtyna-base-plugin-filter-buttons a:hover {
    opacity: .7;
}

#realtyna-base-plugin-message .notice {
    margin: unset;
    border-radius: 15px;
    border-left-color: #644BC4;
    border-right-width: 4px;
    border-right-color: #644BC4;
}

/* Change separator color */
#adminmenu li.wp-menu-separator.realtyna-realtyna-base-plugin {
    border-top: 3px solid #48358e !important;
    background-color: #48358e !important;
    margin-bottom: 0px;
}

/* Change MLS On The Fly<sup>&reg;</sup> menu background */
#toplevel_page_realtyna-realtyna-base-plugin .wp-menu-name, #toplevel_page_realtyna_rf_shell_social_impact .wp-menu-name,
#toplevel_page_realtyna-realtyna-base-plugin .wp-submenu, #toplevel_page_realtyna_rf_shell_social_impact .wp-submenu {
    background-color: #644BC4 !important;
    color: #ffffff !important;
}

.mls-on-the-fly-important-text{
    font-weight: bold;
    color: #644BC4;
}

#realtyna-base-plugin-mls-on-the-fly .realtyna-base-plugin-dashboard-header-wrapper h2 {
    display: none;
}

#realtyna-base-plugin-mls-on-the-fly{
    padding-top: 5px !important;
}

.mls-on-the-fly-lead-page-header h1{
    font-size: 2.5em;
}

.mls-on-the-fly-lead-page-header p{
    font-size: 18px;
}

#realtyna-base-plugin-main .spinner {
    float: unset;
    margin-bottom: 20px;
}

#mls-on-the-fly-lead-page {
    padding: 20px;
}

.mls-on-the-fly-lead-page-header {
    text-align: center;
    margin-bottom: 20px;
}

.mls-on-the-fly-lead-page-header h1 {
    color: #8E42F7;
}

.mls-on-the-fly-lead-page-content {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
}

.mls-on-the-fly-lead-page-left,
.mls-on-the-fly-lead-page-right {
    flex: 1;
    width: 50%;
    box-sizing: border-box;
    padding: 20px;
}

.animated-character img {
    width: 100%;
    transition: transform 0.5s;
}

.mls-on-the-fly-lead-generator-form {
    background-color: #fff;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.mls-on-the-fly-lead-generator-form label {
    font-weight: bold;
}

.mls-on-the-fly-lead-generator-form input,
.mls-on-the-fly-lead-generator-form textarea {
    width: 100%;
    padding: 10px;
    margin-bottom: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
}

.mls-on-the-fly-lead-generator-form button {
    width: 100%;
    padding: 10px;
    background-color: #8E42F7;
    color: #fff;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

.mls-on-the-fly-lead-generator-form button:hover {
    background-color: #722FC0;
}

.mls-on-the-fly-lead-generator-success,
.mls-on-the-fly-lead-generator-error {
    margin-top: 20px;
    padding: 10px;
    border-radius: 5px;
}

.mls-on-the-fly-lead-generator-success {
    background-color: #d4edda;
    color: #155724;
}

.mls-on-the-fly-lead-generator-error {
    background-color: #f8d7da;
    color: #721c24;
}

.mls-on-the-fly-lead-generator-links-wrapper {
    text-align: center;
    margin-top: 20px;
}

.mls-on-the-fly-lead-generator-links-title {
    color: #8E42F7;
    margin-bottom: 10px;
}

.mls-on-the-fly-lead-generator-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.mls-on-the-fly-lead-generator-links li {
    margin-bottom: 10px;
}

.mls-on-the-fly-lead-generator-links a {
    color: #8E42F7;
    text-decoration: none;
}

.mls-on-the-fly-lead-generator-links a:hover {
    text-decoration: underline;
}
