<?php declare(strict_types=1);

namespace Php<PERSON><PERSON><PERSON>\Parser;

use Php<PERSON><PERSON><PERSON>\Error;
use Php<PERSON>arser\Modifiers;
use Php<PERSON>arser\Node;
use PhpParser\Node\Expr;
use Php<PERSON>arser\Node\Name;
use Php<PERSON><PERSON>er\Node\Scalar;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Node\Stmt;

/* This is an automatically GENERATED file, which should not be manually edited.
 * Instead edit one of the following:
 *  * the grammar file grammar/php.y
 *  * the skeleton file grammar/parser.template
 *  * the preprocessing script grammar/rebuildParsers.php
 */
class Php8 extends \PhpParser\ParserAbstract
{
    public const YYERRTOK = 256;
    public const T_THROW = 257;
    public const T_INCLUDE = 258;
    public const T_INCLUDE_ONCE = 259;
    public const T_EVAL = 260;
    public const T_REQUIRE = 261;
    public const T_REQUIRE_ONCE = 262;
    public const T_LOGICAL_OR = 263;
    public const T_LOGICAL_XOR = 264;
    public const T_LOGICAL_AND = 265;
    public const T_PRINT = 266;
    public const T_YIELD = 267;
    public const T_DOUBLE_ARROW = 268;
    public const T_YIELD_FROM = 269;
    public const T_PLUS_EQUAL = 270;
    public const T_MINUS_EQUAL = 271;
    public const T_MUL_EQUAL = 272;
    public const T_DIV_EQUAL = 273;
    public const T_CONCAT_EQUAL = 274;
    public const T_MOD_EQUAL = 275;
    public const T_AND_EQUAL = 276;
    public const T_OR_EQUAL = 277;
    public const T_XOR_EQUAL = 278;
    public const T_SL_EQUAL = 279;
    public const T_SR_EQUAL = 280;
    public const T_POW_EQUAL = 281;
    public const T_COALESCE_EQUAL = 282;
    public const T_COALESCE = 283;
    public const T_BOOLEAN_OR = 284;
    public const T_BOOLEAN_AND = 285;
    public const T_AMPERSAND_NOT_FOLLOWED_BY_VAR_OR_VARARG = 286;
    public const T_AMPERSAND_FOLLOWED_BY_VAR_OR_VARARG = 287;
    public const T_IS_EQUAL = 288;
    public const T_IS_NOT_EQUAL = 289;
    public const T_IS_IDENTICAL = 290;
    public const T_IS_NOT_IDENTICAL = 291;
    public const T_SPACESHIP = 292;
    public const T_IS_SMALLER_OR_EQUAL = 293;
    public const T_IS_GREATER_OR_EQUAL = 294;
    public const T_SL = 295;
    public const T_SR = 296;
    public const T_INSTANCEOF = 297;
    public const T_INC = 298;
    public const T_DEC = 299;
    public const T_INT_CAST = 300;
    public const T_DOUBLE_CAST = 301;
    public const T_STRING_CAST = 302;
    public const T_ARRAY_CAST = 303;
    public const T_OBJECT_CAST = 304;
    public const T_BOOL_CAST = 305;
    public const T_UNSET_CAST = 306;
    public const T_POW = 307;
    public const T_NEW = 308;
    public const T_CLONE = 309;
    public const T_EXIT = 310;
    public const T_IF = 311;
    public const T_ELSEIF = 312;
    public const T_ELSE = 313;
    public const T_ENDIF = 314;
    public const T_LNUMBER = 315;
    public const T_DNUMBER = 316;
    public const T_STRING = 317;
    public const T_STRING_VARNAME = 318;
    public const T_VARIABLE = 319;
    public const T_NUM_STRING = 320;
    public const T_INLINE_HTML = 321;
    public const T_ENCAPSED_AND_WHITESPACE = 322;
    public const T_CONSTANT_ENCAPSED_STRING = 323;
    public const T_ECHO = 324;
    public const T_DO = 325;
    public const T_WHILE = 326;
    public const T_ENDWHILE = 327;
    public const T_FOR = 328;
    public const T_ENDFOR = 329;
    public const T_FOREACH = 330;
    public const T_ENDFOREACH = 331;
    public const T_DECLARE = 332;
    public const T_ENDDECLARE = 333;
    public const T_AS = 334;
    public const T_SWITCH = 335;
    public const T_MATCH = 336;
    public const T_ENDSWITCH = 337;
    public const T_CASE = 338;
    public const T_DEFAULT = 339;
    public const T_BREAK = 340;
    public const T_CONTINUE = 341;
    public const T_GOTO = 342;
    public const T_FUNCTION = 343;
    public const T_FN = 344;
    public const T_CONST = 345;
    public const T_RETURN = 346;
    public const T_TRY = 347;
    public const T_CATCH = 348;
    public const T_FINALLY = 349;
    public const T_USE = 350;
    public const T_INSTEADOF = 351;
    public const T_GLOBAL = 352;
    public const T_STATIC = 353;
    public const T_ABSTRACT = 354;
    public const T_FINAL = 355;
    public const T_PRIVATE = 356;
    public const T_PROTECTED = 357;
    public const T_PUBLIC = 358;
    public const T_READONLY = 359;
    public const T_PUBLIC_SET = 360;
    public const T_PROTECTED_SET = 361;
    public const T_PRIVATE_SET = 362;
    public const T_VAR = 363;
    public const T_UNSET = 364;
    public const T_ISSET = 365;
    public const T_EMPTY = 366;
    public const T_HALT_COMPILER = 367;
    public const T_CLASS = 368;
    public const T_TRAIT = 369;
    public const T_INTERFACE = 370;
    public const T_ENUM = 371;
    public const T_EXTENDS = 372;
    public const T_IMPLEMENTS = 373;
    public const T_OBJECT_OPERATOR = 374;
    public const T_NULLSAFE_OBJECT_OPERATOR = 375;
    public const T_LIST = 376;
    public const T_ARRAY = 377;
    public const T_CALLABLE = 378;
    public const T_CLASS_C = 379;
    public const T_TRAIT_C = 380;
    public const T_METHOD_C = 381;
    public const T_FUNC_C = 382;
    public const T_PROPERTY_C = 383;
    public const T_LINE = 384;
    public const T_FILE = 385;
    public const T_START_HEREDOC = 386;
    public const T_END_HEREDOC = 387;
    public const T_DOLLAR_OPEN_CURLY_BRACES = 388;
    public const T_CURLY_OPEN = 389;
    public const T_PAAMAYIM_NEKUDOTAYIM = 390;
    public const T_NAMESPACE = 391;
    public const T_NS_C = 392;
    public const T_DIR = 393;
    public const T_NS_SEPARATOR = 394;
    public const T_ELLIPSIS = 395;
    public const T_NAME_FULLY_QUALIFIED = 396;
    public const T_NAME_QUALIFIED = 397;
    public const T_NAME_RELATIVE = 398;
    public const T_ATTRIBUTE = 399;

    protected int $tokenToSymbolMapSize = 400;
    protected int $actionTableSize = 1289;
    protected int $gotoTableSize = 608;

    protected int $invalidSymbol = 172;
    protected int $errorSymbol = 1;
    protected int $defaultAction = -32766;
    protected int $unexpectedTokenRule = 32767;

    protected int $YY2TBLSTATE = 442;
    protected int $numNonLeafStates = 753;

    protected array $symbolToName = array(
        "EOF",
        "error",
        "T_THROW",
        "T_INCLUDE",
        "T_INCLUDE_ONCE",
        "T_EVAL",
        "T_REQUIRE",
        "T_REQUIRE_ONCE",
        "','",
        "T_LOGICAL_OR",
        "T_LOGICAL_XOR",
        "T_LOGICAL_AND",
        "T_PRINT",
        "T_YIELD",
        "T_DOUBLE_ARROW",
        "T_YIELD_FROM",
        "'='",
        "T_PLUS_EQUAL",
        "T_MINUS_EQUAL",
        "T_MUL_EQUAL",
        "T_DIV_EQUAL",
        "T_CONCAT_EQUAL",
        "T_MOD_EQUAL",
        "T_AND_EQUAL",
        "T_OR_EQUAL",
        "T_XOR_EQUAL",
        "T_SL_EQUAL",
        "T_SR_EQUAL",
        "T_POW_EQUAL",
        "T_COALESCE_EQUAL",
        "'?'",
        "':'",
        "T_COALESCE",
        "T_BOOLEAN_OR",
        "T_BOOLEAN_AND",
        "'|'",
        "'^'",
        "T_AMPERSAND_NOT_FOLLOWED_BY_VAR_OR_VARARG",
        "T_AMPERSAND_FOLLOWED_BY_VAR_OR_VARARG",
        "T_IS_EQUAL",
        "T_IS_NOT_EQUAL",
        "T_IS_IDENTICAL",
        "T_IS_NOT_IDENTICAL",
        "T_SPACESHIP",
        "'<'",
        "T_IS_SMALLER_OR_EQUAL",
        "'>'",
        "T_IS_GREATER_OR_EQUAL",
        "'.'",
        "T_SL",
        "T_SR",
        "'+'",
        "'-'",
        "'*'",
        "'/'",
        "'%'",
        "'!'",
        "T_INSTANCEOF",
        "'~'",
        "T_INC",
        "T_DEC",
        "T_INT_CAST",
        "T_DOUBLE_CAST",
        "T_STRING_CAST",
        "T_ARRAY_CAST",
        "T_OBJECT_CAST",
        "T_BOOL_CAST",
        "T_UNSET_CAST",
        "'@'",
        "T_POW",
        "'['",
        "T_NEW",
        "T_CLONE",
        "T_EXIT",
        "T_IF",
        "T_ELSEIF",
        "T_ELSE",
        "T_ENDIF",
        "T_LNUMBER",
        "T_DNUMBER",
        "T_STRING",
        "T_STRING_VARNAME",
        "T_VARIABLE",
        "T_NUM_STRING",
        "T_INLINE_HTML",
        "T_ENCAPSED_AND_WHITESPACE",
        "T_CONSTANT_ENCAPSED_STRING",
        "T_ECHO",
        "T_DO",
        "T_WHILE",
        "T_ENDWHILE",
        "T_FOR",
        "T_ENDFOR",
        "T_FOREACH",
        "T_ENDFOREACH",
        "T_DECLARE",
        "T_ENDDECLARE",
        "T_AS",
        "T_SWITCH",
        "T_MATCH",
        "T_ENDSWITCH",
        "T_CASE",
        "T_DEFAULT",
        "T_BREAK",
        "T_CONTINUE",
        "T_GOTO",
        "T_FUNCTION",
        "T_FN",
        "T_CONST",
        "T_RETURN",
        "T_TRY",
        "T_CATCH",
        "T_FINALLY",
        "T_USE",
        "T_INSTEADOF",
        "T_GLOBAL",
        "T_STATIC",
        "T_ABSTRACT",
        "T_FINAL",
        "T_PRIVATE",
        "T_PROTECTED",
        "T_PUBLIC",
        "T_READONLY",
        "T_PUBLIC_SET",
        "T_PROTECTED_SET",
        "T_PRIVATE_SET",
        "T_VAR",
        "T_UNSET",
        "T_ISSET",
        "T_EMPTY",
        "T_HALT_COMPILER",
        "T_CLASS",
        "T_TRAIT",
        "T_INTERFACE",
        "T_ENUM",
        "T_EXTENDS",
        "T_IMPLEMENTS",
        "T_OBJECT_OPERATOR",
        "T_NULLSAFE_OBJECT_OPERATOR",
        "T_LIST",
        "T_ARRAY",
        "T_CALLABLE",
        "T_CLASS_C",
        "T_TRAIT_C",
        "T_METHOD_C",
        "T_FUNC_C",
        "T_PROPERTY_C",
        "T_LINE",
        "T_FILE",
        "T_START_HEREDOC",
        "T_END_HEREDOC",
        "T_DOLLAR_OPEN_CURLY_BRACES",
        "T_CURLY_OPEN",
        "T_PAAMAYIM_NEKUDOTAYIM",
        "T_NAMESPACE",
        "T_NS_C",
        "T_DIR",
        "T_NS_SEPARATOR",
        "T_ELLIPSIS",
        "T_NAME_FULLY_QUALIFIED",
        "T_NAME_QUALIFIED",
        "T_NAME_RELATIVE",
        "T_ATTRIBUTE",
        "';'",
        "']'",
        "'('",
        "')'",
        "'{'",
        "'}'",
        "'`'",
        "'\"'",
        "'$'"
    );

    protected array $tokenToSymbol = array(
            0,  172,  172,  172,  172,  172,  172,  172,  172,  172,
          172,  172,  172,  172,  172,  172,  172,  172,  172,  172,
          172,  172,  172,  172,  172,  172,  172,  172,  172,  172,
          172,  172,  172,   56,  170,  172,  171,   55,  172,  172,
          165,  166,   53,   51,    8,   52,   48,   54,  172,  172,
          172,  172,  172,  172,  172,  172,  172,  172,   31,  163,
           44,   16,   46,   30,   68,  172,  172,  172,  172,  172,
          172,  172,  172,  172,  172,  172,  172,  172,  172,  172,
          172,  172,  172,  172,  172,  172,  172,  172,  172,  172,
          172,   70,  172,  164,   36,  172,  169,  172,  172,  172,
          172,  172,  172,  172,  172,  172,  172,  172,  172,  172,
          172,  172,  172,  172,  172,  172,  172,  172,  172,  172,
          172,  172,  172,  167,   35,  168,   58,  172,  172,  172,
          172,  172,  172,  172,  172,  172,  172,  172,  172,  172,
          172,  172,  172,  172,  172,  172,  172,  172,  172,  172,
          172,  172,  172,  172,  172,  172,  172,  172,  172,  172,
          172,  172,  172,  172,  172,  172,  172,  172,  172,  172,
          172,  172,  172,  172,  172,  172,  172,  172,  172,  172,
          172,  172,  172,  172,  172,  172,  172,  172,  172,  172,
          172,  172,  172,  172,  172,  172,  172,  172,  172,  172,
          172,  172,  172,  172,  172,  172,  172,  172,  172,  172,
          172,  172,  172,  172,  172,  172,  172,  172,  172,  172,
          172,  172,  172,  172,  172,  172,  172,  172,  172,  172,
          172,  172,  172,  172,  172,  172,  172,  172,  172,  172,
          172,  172,  172,  172,  172,  172,  172,  172,  172,  172,
          172,  172,  172,  172,  172,  172,    1,    2,    3,    4,
            5,    6,    7,    9,   10,   11,   12,   13,   14,   15,
           17,   18,   19,   20,   21,   22,   23,   24,   25,   26,
           27,   28,   29,   32,   33,   34,   37,   38,   39,   40,
           41,   42,   43,   45,   47,   49,   50,   57,   59,   60,
           61,   62,   63,   64,   65,   66,   67,   69,   71,   72,
           73,   74,   75,   76,   77,   78,   79,   80,   81,   82,
           83,   84,   85,   86,   87,   88,   89,   90,   91,   92,
           93,   94,   95,   96,   97,   98,   99,  100,  101,  102,
          103,  104,  105,  106,  107,  108,  109,  110,  111,  112,
          113,  114,  115,  116,  117,  118,  119,  120,  121,  122,
          123,  124,  125,  126,  127,  128,  129,  130,  131,  132,
          133,  134,  135,  136,  137,  138,  139,  140,  141,  142,
          143,  144,  145,  146,  147,  148,  149,  150,  151,  152,
          153,  154,  155,  156,  157,  158,  159,  160,  161,  162
    );

    protected array $action = array(
          126,  127,  128,  570,  129,  130,  955,  765,  766,  767,
          131,   38,  849,  -85,-32766, 1376,-32766,-32766,-32766,    0,
          840, 1134, 1135, 1136, 1130, 1129, 1128, 1137, 1131, 1132,
         1133,-32766,-32766,-32766,  851,  759,  758,-32766,-32766,-32766,
        -32766,-32766,-32766,-32766,-32766,-32766,-32767,-32767,-32767,-32767,
        -32767, 1005,-32766, 1045, -570,  768, 1134, 1135, 1136, 1130,
         1129, 1128, 1137, 1131, 1132, 1133,  388,  387,  842,  263,
          132,  389,  772,  773,  774,  775,  430,  845,  431,  -85,
            2,   36,  246,   47,  291,  829,  776,  777,  778,  779,
          780,  781,  782,  783,  784,  785,  805,  571,  806,  807,
          808,  809,  797,  798,  344,  345,  800,  801,  786,  787,
          788,  790,  791,  792,  359,  832,  833,  834,  835,  836,
          572, -570, -570, -332,  793,  794,  573,  574,  236,  817,
          815,  816,  828,  812,  813,   26, -194,  575,  576,  811,
          577,  578,  579,  580,  323,  581,  582,  876,  844,  877,
          297,  298,  814,  583,  584,  722,  133,  846,  126,  127,
          128,  570,  129,  130, 1078,  765,  766,  767,  131,   38,
        -32766,   35,  735, 1038, 1037, 1036, 1042, 1039, 1040, 1041,
        -32766,-32766,-32766, 1006,  104,  105,  106,  107,  108, -372,
          275, -372,-32766,  759,  758, 1054,  850,-32766,-32766,-32766,
          848,-32766,  109,-32766,-32766,-32766,-32766,-32766,-32766,-32766,
          134,  476,  477,  768,-32766,-32766,-32766, 1054,-32766,  290,
        -32766,-32766,-32766,-32766,-32766,  616,  143,  263,  132,  389,
          772,  773,  774,  775,  249,-32766,  431,-32766,-32766,-32766,
        -32766,  290,  307,  829,  776,  777,  778,  779,  780,  781,
          782,  783,  784,  785,  805,  571,  806,  807,  808,  809,
          797,  798,  344,  345,  800,  801,  786,  787,  788,  790,
          791,  792,  359,  832,  833,  834,  835,  836,  572,  958,
         -273, -332,  793,  794,  573,  574,  840,  817,  815,  816,
          828,  812,  813, 1301, -194,  575,  576,  811,  577,  578,
          579,  580,  566,  581,  582, 1108,   82,   83,   84,  748,
          814,  583,  584,  309,  146,  789,  760,  761,  762,  763,
          764,  235,  765,  766,  767,  802,  803,   37,  957,   85,
           86,   87,   88,   89,   90,   91,   92,   93,   94,   95,
           96,   97,   98,   99,  100,  101,  102,  103,  104,  105,
          106,  107,  108,  157,  275,-32766,-32766,-32766,-32767,-32767,
        -32767,-32767,  101,  102,  103,-32766,  109, 1313,  622,  318,
          768,-32766,-32766,-32766,  849, 1361,-32766, 1107,-32766,-32766,
        -32766,  340, 1360, 1357,  769,  770,  771,  772,  773,  774,
          775,  341,-32766,  838,-32766,-32766, 1386,  374, 1281, 1387,
          829,  776,  777,  778,  779,  780,  781,  782,  783,  784,
          785,  805,  827,  806,  807,  808,  809,  797,  798,  799,
          826,  800,  801,  786,  787,  788,  790,  791,  792,  831,
          832,  833,  834,  835,  836,  837, 1077,  431, -567,  793,
          794,  795,  796,  148,  817,  815,  816,  828,  812,  813,
          380, -193,  804,  810,  811,  818,  819,  821,  820,  138,
          822,  823,  840,  321,  396,  285,   24,  814,  825,  824,
           49,   50,   51,  522,   52,   53,  398, -110,    7,  849,
           54,   55, -110,   56, -110,-32766,-32766,-32766, 1342,  303,
          125, 1123, -110, -110, -110, -110, -110, -110, -110, -110,
         -110, -110, -110,  161,  750, -567, -567,  291,  974,  975,
        -32766,-32766,-32766,  976,  448,  285, 1276, 1275, 1277,   57,
           58, -567,-32766,-32766,   59, 1109,   60,  243,  244,   61,
           62,   63,   64,   65,   66,   67,   68,-32766,   28,  265,
           69,  446,  523,  490, -346,  449, 1307, 1308,  524,  139,
          849, 1051,  450,  321, 1305,   42,   20,  525,  934,  526,
          934,  527,   74,  528, -568,  698,  529,  530,  321,  386,
          387,   44,   45,  452,  383,  382, 1054,   46,  531,  430,
          974,  975,  451,  372,  339,  976, 1281,  855,  725,  934,
         1267,  759,  758,-32766,  970,  533,  534,  535,  149,  934,
          281,  699,  -78, -566, 1274,  102,  103,  537,  538, -193,
         1293, 1294, 1295, 1296, 1298, 1290, 1291,  295, 1054,  726,
          466,  467,  468, 1297, 1292,  700,  701, 1276, 1275, 1277,
          296, -568, -568,   70, -153, -153, -153,  316,  317,  321,
         1272,  924,  290,  924, 1276, 1275, 1277, -568, 1051, -153,
          281, -153, 1150, -153,   81, -153,  740,  151,  321, -574,
          152,  759,  758,-32766, 1053,  381,  876,  849,  877,  153,
         -566, -566,  924, 1054, 1051,  155,  974,  975, -606,  491,
         -606,  532,  924, 1276, 1275, 1277, -566,   33, 1054,  910,
          970, -110, -110, -110,   28,  266,  -58,  281, -573, 1054,
        -32766,-32766, -110, -110,  665,   21,  849, -110,  -57, -564,
         1305,  684,  685,  147,  413,  123, -110,  384,  385,  124,
          936,  135,  936,  136,  720,-32766,  720, -153,  142,   48,
           32,  110,  111,  112,  113,  114,  115,  116,  117,  118,
          119,  120,  121,  122,  390,  391, 1267,  296,  759,  758,
           74,  936,  156,  934,  158,  720,  321,   -4,  934,  159,
          934,  936,  160,  537,  538,  720, 1293, 1294, 1295, 1296,
         1298, 1290, 1291, 1183, 1185,  934, -564, -564, -565, 1297,
         1292,  759,  758,  727, -564,-32766,  656,  657, -306,   72,
          730, 1274, -564,  -87,  317,  321,  299,  300,-32766,-32766,
        -32766,  -84,-32766,  -78,-32766,  737,-32766,  -73,  -72,-32766,
          -71,  -70,  379,  -69,-32766,-32766,-32766,  -68,-32766,  -67,
        -32766,-32766,  -66,  -65, 1274,  -46,-32766,  427,   28,  265,
          -18,-32766,-32766,-32766,  140,-32766,  924,-32766,-32766,-32766,
          849,  924,-32766,  924, 1305, -565, -565,-32766,-32766,-32766,
          274, -564, -564,-32766,-32766,  282,  736,  739,  924,-32766,
          427, -565,  933,  381,  145,  443,  286, -564,  951,   73,
          294,-32766, -302, -572,  974,  975,  279,  280,  283,  532,
         1267,   28,  266,  284,  329,  275,  109,  536,  970, -110,
         -110, -110,  287,  849,  292,  293,  840, 1305,  538,  694,
         1293, 1294, 1295, 1296, 1298, 1290, 1291,  709,  144,  587,
          711,   11,   10, 1297, 1292,  991,  849, 1141,  473,  720,
          936,-32766,  936,   72,  720,   -4,  720, 1388,  317,  321,
          -50,  970,  672, 1267,  687,  666,  501,  936,  971,  301,
          308,  720,  671, 1312,  302, 1314,-32766,  688,  953, -530,
         -520,  538,   40, 1293, 1294, 1295, 1296, 1298, 1290, 1291,
          848,   41,    8,  137,  654,   27, 1297, 1292,  304,   34,
          593,  620,  296,-32766,    0,    0,   72,    0,    0, 1274,
            0,  317,  321,    0,    0,    0,-32766,-32766,-32766, -276,
        -32766,    0,-32766,    0,-32766,    0,    0,-32766,    0,    0,
            0,    0,-32766,-32766,-32766,  934,-32766,    0,-32766,-32766,
            0,    0, 1274,  378,-32766,  427,  745, -600,  412,-32766,
        -32766,-32766,  746,-32766,  868,-32766,-32766,-32766,  934,  915,
        -32766, 1015,  992,  999,  989,-32766,-32766,-32766, 1000,-32766,
          913,-32766,-32766,  987, 1112, 1274, 1115,-32766,  427, 1116,
         1113, 1152,-32766,-32766,-32766, 1114,-32766, 1120,-32766,-32766,
        -32766, 1302,  860,-32766, 1329, 1346, 1379,  496,-32766,-32766,
        -32766,  659,-32766, -599,-32766,-32766, -598, -574, 1274,  600,
        -32766,  427, -573, -572, -571,-32766,-32766,-32766,  924,-32766,
         -514,-32766,-32766,-32766,    1,   29,-32766, -274,   30,   39,
           43,-32766,-32766,-32766, -251, -251, -251,-32766,-32766,   71,
          381,  924,   75,-32766,  427,   76,   77,   78, 1281,   79,
           80,  974,  975,  141,  150,-32766,  532, -250, -250, -250,
         -273,  154,  241,  381,  910,  970, -110, -110, -110,  325,
          360,  361,  362,  363,  974,  975,  364,  365,  -16,  532,
          366,  367,  368,  369,  370,  373,  444,  910,  970, -110,
         -110, -110,-32766,   13,  565,  371, 1306,  936, 1274,   14,
          416,  720, -251,   15,   16,-32766,-32766,-32766,   18,-32766,
          354,-32766,  411,-32766,  492,  493,-32766,  500,  503,  504,
          936,-32766,-32766,-32766,  720, -250,  505,-32766,-32766,  849,
          506,  510,  511,-32766,  427,  512,  519,  598,  704, 1080,
         1223, 1303, 1079, 1060, 1262,-32766, 1056, -278, -102,   12,
           17,   22,  312,  410,  612,  617,  645,  710, 1227, 1280,
         1224, 1358,    0,  315, -110, -110,  375,  721,  724, -110,
          728,  729,  731,  732,  733,  734,  738,  750, -110,  723,
          751,    0,  742,  911, 1383, 1385,    0,-32766,  871,  870,
          964, 1007, 1384,  963,  961,  962,  965, 1255,  944,  954,
          942, 1151, 1147, 1101,  997,  998,  643, 1382, 1340,  296,
         1355,    0,   74, 1240,  321,    0,    0,    0,  321
    );

    protected array $actionCheck = array(
            2,    3,    4,    5,    6,    7,    1,    9,   10,   11,
           12,   13,   82,   31,  116,   85,    9,   10,   11,    0,
           80,  116,  117,  118,  119,  120,  121,  122,  123,  124,
          125,    9,   10,   11,    1,   37,   38,   30,  140,   32,
           33,   34,   35,   36,   37,   38,   39,   40,   41,   42,
           43,   31,   30,    1,   70,   57,  116,  117,  118,  119,
          120,  121,  122,  123,  124,  125,  106,  107,   80,   71,
           72,   73,   74,   75,   76,   77,  116,   80,   80,   97,
            8,  151,  152,   70,   30,   87,   88,   89,   90,   91,
           92,   93,   94,   95,   96,   97,   98,   99,  100,  101,
          102,  103,  104,  105,  106,  107,  108,  109,  110,  111,
          112,  113,  114,  115,  116,  117,  118,  119,  120,  121,
          122,  137,  138,    8,  126,  127,  128,  129,   14,  131,
          132,  133,  134,  135,  136,    8,    8,  139,  140,  141,
          142,  143,  144,  145,   70,  147,  148,  106,  160,  108,
          137,  138,  154,  155,  156,  167,  158,  160,    2,    3,
            4,    5,    6,    7,  166,    9,   10,   11,   12,   13,
          116,    8,  167,  119,  120,  121,  122,  123,  124,  125,
            9,   10,   11,  163,   51,   52,   53,   54,   55,  106,
           57,  108,  116,   37,   38,  141,  163,    9,   10,   11,
          159,   30,   69,   32,   33,   34,   35,   36,   37,   38,
            8,  137,  138,   57,    9,   10,   11,  141,   30,  165,
           32,   33,   34,   35,   36,    1,    8,   71,   72,   73,
           74,   75,   76,   77,    8,   30,   80,   32,   33,   34,
           35,  165,    8,   87,   88,   89,   90,   91,   92,   93,
           94,   95,   96,   97,   98,   99,  100,  101,  102,  103,
          104,  105,  106,  107,  108,  109,  110,  111,  112,  113,
          114,  115,  116,  117,  118,  119,  120,  121,  122,   73,
          166,  166,  126,  127,  128,  129,   80,  131,  132,  133,
          134,  135,  136,    1,  166,  139,  140,  141,  142,  143,
          144,  145,   85,  147,  148,  163,    9,   10,   11,  167,
          154,  155,  156,    8,  158,    2,    3,    4,    5,    6,
            7,   97,    9,   10,   11,   12,   13,   30,  122,   32,
           33,   34,   35,   36,   37,   38,   39,   40,   41,   42,
           43,   44,   45,   46,   47,   48,   49,   50,   51,   52,
           53,   54,   55,   16,   57,    9,   10,   11,   44,   45,
           46,   47,   48,   49,   50,    9,   69,  150,   52,    8,
           57,    9,   10,   11,   82,    1,   30,    1,   32,   33,
           34,    8,    8,    1,   71,   72,   73,   74,   75,   76,
           77,    8,   30,   80,   32,   33,   80,    8,    1,   83,
           87,   88,   89,   90,   91,   92,   93,   94,   95,   96,
           97,   98,   99,  100,  101,  102,  103,  104,  105,  106,
          107,  108,  109,  110,  111,  112,  113,  114,  115,  116,
          117,  118,  119,  120,  121,  122,    1,   80,   70,  126,
          127,  128,  129,   14,  131,  132,  133,  134,  135,  136,
            8,    8,  139,  140,  141,  142,  143,  144,  145,  167,
          147,  148,   80,  171,    8,   30,  101,  154,  155,  156,
            2,    3,    4,    5,    6,    7,  106,  101,  108,   82,
           12,   13,  106,   15,  108,    9,   10,   11,    1,  113,
           14,  126,  116,  117,  118,  119,  120,  121,  122,  123,
          124,  125,  126,   14,  167,  137,  138,   30,  117,  118,
            9,   10,   11,  122,    8,   30,  159,  160,  161,   51,
           52,  153,    9,   10,   56,  168,   58,   59,   60,   61,
           62,   63,   64,   65,   66,   67,   68,  140,   70,   71,
           72,   73,   74,   31,  168,    8,   78,   79,   80,  167,
           82,  116,    8,  171,   86,   87,   88,   89,    1,   91,
            1,   93,  165,   95,   70,   80,   98,   99,  171,  106,
          107,  103,  104,  105,  106,  107,  141,  109,  110,  116,
          117,  118,    8,  115,  116,  122,    1,    8,   31,    1,
          122,   37,   38,  116,  131,  127,  128,  129,   14,    1,
          165,  116,   16,   70,   80,   49,   50,  139,  140,  166,
          142,  143,  144,  145,  146,  147,  148,  149,  141,   31,
          132,  133,  134,  155,  156,  140,  141,  159,  160,  161,
          162,  137,  138,  165,   75,   76,   77,  169,  170,  171,
          116,   84,  165,   84,  159,  160,  161,  153,  116,   90,
          165,   92,  163,   94,  167,   96,  167,   14,  171,  165,
           14,   37,   38,  116,  140,  106,  106,   82,  108,   14,
          137,  138,   84,  141,  116,   14,  117,  118,  164,  167,
          166,  122,   84,  159,  160,  161,  153,   14,  141,  130,
          131,  132,  133,  134,   70,   71,   16,  165,  165,  141,
           51,   52,  117,  118,   75,   76,   82,  122,   16,   70,
           86,   75,   76,  101,  102,   16,  131,  106,  107,   16,
          163,   16,  163,   16,  167,  140,  167,  168,   16,   70,
           16,   17,   18,   19,   20,   21,   22,   23,   24,   25,
           26,   27,   28,   29,  106,  107,  122,  162,   37,   38,
          165,  163,   16,    1,   16,  167,  171,    0,    1,   16,
            1,  163,   16,  139,  140,  167,  142,  143,  144,  145,
          146,  147,  148,   59,   60,    1,  137,  138,   70,  155,
          156,   37,   38,   31,   70,   74,  111,  112,   35,  165,
           31,   80,  153,   31,  170,  171,  137,  138,   87,   88,
           89,   31,   91,   31,   93,   31,   95,   31,   31,   98,
           31,   31,  153,   31,  103,  104,  105,   31,   74,   31,
          109,  110,   31,   31,   80,   31,  115,  116,   70,   71,
           31,   87,   88,   89,   31,   91,   84,   93,  127,   95,
           82,   84,   98,   84,   86,  137,  138,  103,  104,  105,
           31,  137,  138,  109,  110,   31,   31,   31,   84,  115,
          116,  153,   31,  106,   31,  108,   37,  153,   38,  158,
          113,  127,   35,  165,  117,  118,   35,   35,   35,  122,
          122,   70,   71,   35,   35,   57,   69,  130,  131,  132,
          133,  134,   37,   82,   37,   37,   80,   86,  140,   77,
          142,  143,  144,  145,  146,  147,  148,   80,   70,   89,
           92,  154,   97,  155,  156,  163,   82,   82,   97,  167,
          163,   85,  163,  165,  167,  168,  167,   83,  170,  171,
           31,  131,  100,  122,   94,   90,   97,  163,  131,  135,
          135,  167,   96,  150,  136,  150,  140,  100,  158,  153,
          153,  140,  163,  142,  143,  144,  145,  146,  147,  148,
          159,  163,  153,   31,  113,  153,  155,  156,  114,  167,
          157,  157,  162,   74,   -1,   -1,  165,   -1,   -1,   80,
           -1,  170,  171,   -1,   -1,   -1,   87,   88,   89,  166,
           91,   -1,   93,   -1,   95,   -1,   -1,   98,   -1,   -1,
           -1,   -1,  103,  104,  105,    1,   74,   -1,  109,  110,
           -1,   -1,   80,  153,  115,  116,  163,  165,  168,   87,
           88,   89,  163,   91,  163,   93,  127,   95,    1,  163,
           98,  163,  163,  163,  163,  103,  104,  105,  163,   74,
          163,  109,  110,  163,  163,   80,  163,  115,  116,  163,
          163,  163,   87,   88,   89,  163,   91,  163,   93,  127,
           95,  164,  164,   98,  164,  164,  164,  102,  103,  104,
          105,  164,   74,  165,  109,  110,  165,  165,   80,   81,
          115,  116,  165,  165,  165,   87,   88,   89,   84,   91,
          165,   93,  127,   95,  165,  165,   98,  166,  165,  165,
          165,  103,  104,  105,  100,  101,  102,  109,  110,  165,
          106,   84,  165,  115,  116,  165,  165,  165,    1,  165,
          165,  117,  118,  165,  165,  127,  122,  100,  101,  102,
          166,  165,  165,  106,  130,  131,  132,  133,  134,  165,
          165,  165,  165,  165,  117,  118,  165,  165,   31,  122,
          165,  165,  165,  165,  165,  165,  165,  130,  131,  132,
          133,  134,   74,  166,  165,  165,  170,  163,   80,  166,
          168,  167,  168,  166,  166,   87,   88,   89,  166,   91,
          166,   93,  166,   95,  166,  166,   98,  166,  166,  166,
          163,  103,  104,  105,  167,  168,  166,  109,  110,   82,
          166,  166,  166,  115,  116,  166,  166,  166,  166,  166,
          166,  166,  166,  166,  166,  127,  166,  166,  166,  166,
          166,  166,  166,  166,  166,  166,  166,  166,  166,  166,
          166,  166,   -1,  167,  117,  118,  167,  167,  167,  122,
          167,  167,  167,  167,  167,  167,  167,  167,  131,  167,
          167,   -1,  168,  168,  168,  168,   -1,  140,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  162,
          168,   -1,  165,  169,  171,   -1,   -1,   -1,  171
    );

    protected array $actionBase = array(
            0,   -2,  156,  559,  757, 1004, 1027,  485,  292,  357,
          -60,  -12,  588,  759,  759,  774,  759,  557,  752,  892,
          598,  598,  598,  827,  313,  313,  827,  313,  711,  711,
          711,  711,  744,  744,  965,  965,  998,  932,  899, 1088,
         1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088,
         1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088,
         1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088,
         1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088,
         1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088,
         1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088,
         1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088,
         1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088,
         1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088,
         1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088,
         1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088,
         1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088,
         1088, 1088,   33,   20,  224, 1080,  673, 1056, 1062, 1058,
         1063, 1054, 1053, 1057, 1059, 1064, 1109, 1110,  833, 1108,
         1112, 1060,  907, 1055, 1061,  888,  297,  297,  297,  297,
          297,  297,  297,  297,  297,  297,  297,  297,  297,  297,
          297,  297,  297,  297,  297,  297,  297,  297,  297,  297,
          297,  297,  356,  476,  513,  501,  501,  501,  501,  501,
          501,  501,  501,  501,  501,  501,  501,  501,  501,  501,
          501,  501,  501,  501,  501,  624,  624,   22,   22,   22,
          362,  811,  758,  811,  811,  811,  811,  811,  811,  811,
          811,  346,  205,  188,  714,  171,  171,    7,    7,    7,
            7,    7,  376, 1117,   54,  585,  585,  314,  314,  314,
          314,  365,  554,   83,  435,  397,  556,  477,  463,  532,
          532,  558,  558,   76,   76,  558,  558,  558,  133,  133,
          547,  547,  547,  547,   41,  217,  806,  382,  382,  382,
          382,  806,  806,  806,  806,  795,  996,  806,  806,  806,
          494,  533,  708,  649,  649,  560,  -70,  -70,  560,  800,
          -70,  487,  975,  316,  982, -102,  807,  -40,  514, -102,
         1000,  368,  639,  639,  659,  639,  639,  639,  801,  611,
          801, 1052,  836,  836,  794,  776,  894, 1082, 1065,  832,
         1106,  847, 1107, 1083,  489,  488,  -16,   13,   74,  772,
         1051, 1051, 1051, 1051, 1051, 1051, 1051, 1051, 1051, 1051,
         1051, 1051, 1113,  554, 1052,   -3, 1104, 1105, 1113, 1113,
         1113,  554,  554,  554,  554,  554,  554,  554,  554,  799,
          554,  554,  675,   -3,  629,  636,   -3,  849,  554,  797,
           33,   33,   33,   33,   33,   33,   33,   33,   33,   33,
          512,   33,   33,   20,    5,    5,   33,  142,   52,    5,
            5,    5,  337,    5,   33,   33,   33,  611,  828,  813,
          638,  -18,  814,  443,  828,  828,  828,  115,  114,  128,
          753,  837,  370,  816,  816,  835,  929,  929,  816,  834,
          816,  835,  816,  816,  929,  929,  810,  929,  202,  506,
          373,  442,  537,  929,  234,  816,  816,  816,  816,  805,
          929,   72,  544,  816,  226,  218,  816,  816,  805,  804,
          824,  808,  929,  929,  929,  805,  389,  808,  808,  808,
          853,  859,  851,  819,  361,  305,  579,  163,  830,  819,
          819,  816,  456,  851,  819,  851,  819,  790,  819,  819,
          819,  851,  819,  834,  383,  819,  736,  574,  127,  819,
          816,   19,  944,  947,  762,  950,  934,  951,  991,  952,
          954, 1070,  925,  967,  935,  955,  999,  933,  930,  831,
          699,  703,  809,  796,  919,  817,  817,  817,  912,  917,
          817,  817,  817,  817,  817,  817,  817,  817,  699,  897,
          860,  820,  976,  705,  707, 1041,  793, 1085, 1114,  975,
          944,  954,  770,  935,  955,  933,  930,  792,  791,  786,
          788,  782,  780,  777,  779,  803, 1043,  958,  789,  712,
         1012,  977, 1084, 1066,  978,  981, 1016, 1044,  861, 1045,
         1086,  838, 1087, 1090,  898,  985, 1071,  817,  911,  852,
          900,  982,  918,  699,  901, 1046,  997,  802, 1018, 1019,
         1069,  821,  844,  902, 1091,  986,  987,  988, 1073, 1074,
          798, 1003,  823, 1021,  839,  850, 1022, 1023, 1030, 1034,
         1075, 1092, 1076,  908, 1077,  866,  845,  931,  846, 1093,
          429,  843,  848,  858,  990,  584,  974, 1078, 1002, 1094,
         1035, 1036, 1039, 1095, 1096,  959,  868, 1007,  840, 1008,
          964,  869,  870,  643,  857, 1047,  841,  842,  855,  646,
          655, 1097, 1098, 1099,  966,  825,  822,  871,  875, 1048,
          829, 1050, 1100,  661,  877, 1101, 1042,  738,  743,  586,
          692,  680,  746,  818, 1079,  812,  854,  815,  989,  743,
          826,  880, 1102,  881,  883,  886, 1040,  887, 1014, 1103,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,  468,  468,  468,  468,  468,  468,
          313,  313,  313,  313,  313,  468,  468,  468,  468,  468,
          468,  468,  313,  468,  468,  468,  313,    0,    0,  313,
            0,  468,  468,  468,  468,  468,  468,  468,  468,  468,
          468,  468,  468,  468,  468,  468,  468,  468,  468,  468,
          468,  468,  468,  468,  468,  468,  468,  468,  468,  468,
          468,  468,  468,  468,  468,  468,  468,  468,  468,  468,
          468,  468,  468,  468,  468,  468,  468,  468,  468,  468,
          468,  468,  468,  468,  468,  468,  468,  468,  468,  468,
          468,  468,  468,  468,  468,  468,  468,  468,  468,  468,
          468,  468,  468,  468,  468,  468,  468,  468,  468,  468,
          468,  468,  468,  468,  468,  468,  468,  468,  468,  468,
          468,  468,  468,  468,  468,  468,  468,  468,  468,  468,
          468,  468,  468,  468,  468,  468,  468,  468,  468,  468,
          468,  468,  468,  468,  468,  468,  468,  468,  468,  468,
          468,  468,  468,  468,  468,  468,  468,  468,  468,  468,
          468,  468,  468,  468,  468,  297,  297,  297,  297,  297,
          297,  297,  297,  297,  297,  297,  297,  297,  297,  297,
          297,  297,  297,  297,  297,  297,  297,  297,  297,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,  297,  297,  297,  297,  297,
          297,  297,  297,  297,  297,  297,  297,  297,  297,  297,
          297,  297,  297,  297,  297,  297,  297,  297,  524,  524,
          297,  297,  297,  297,  524,  524,  524,  524,  524,  524,
          524,  524,  524,  524,  297,  297,  297,    0,  297,  297,
          297,  297,  297,  297,  297,  810,  524,  524,  524,  524,
          133,  133,  133,  133,  -95,  -95,  -95,  524,  524,  133,
          524,  810,  524,  524,  524,  524,  524,  524,  524,  524,
          524,    0,    0,  524,  524,  524,  524,   -3,  -70,  524,
          834,  834,  834,  834,  524,  524,  524,  524,  -70,  -70,
          524,  524,  524,    0,    0,    0,  133,  133,   -3,    0,
            0,   -3,  391,    0,  834,  206,  834,  206,  524,  391,
          810,  374,  524,  489,    0,    0,    0,    0,    0,    0,
            0,   -3,  834,   -3,  554,  -70,  -70,  554,  554,    5,
           33,  374,  612,  612,  612,  612,   33,    0,    0,    0,
            0,    0,  611,  810,  810,  810,  810,  810,  810,  810,
          810,  810,  810,  810,  810,  834,    0,  810,    0,  810,
          810,  834,  834,  834,    0,    0,    0,    0,    0,    0,
            0,    0,  929,    0,    0,    0,    0,    0,    0,    0,
          834,    0,  929,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,  834,    0,    0,    0,    0,    0,    0,    0,    0,
            0,  817,  821,    0,    0,  821,    0,  817,  817,  817,
            0,    0,    0,  857,  829
    );

    protected array $actionDefault = array(
            3,32767,  102,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,  100,32767,  618,  618,
          618,  618,32767,32767,  255,  102,32767,32767,  489,  406,
          406,  406,32767,32767,  562,  562,  562,  562,  562,32767,
        32767,32767,32767,32767,32767,  489,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,   36,    7,    8,   10,
           11,   49,   17,  328,  100,32767,32767,32767,32767,32767,
        32767,32767,32767,  102,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,  393,  611,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,  493,  472,  473,  475,
          476,  405,  563,  617,  331,  614,  333,  404,  145,  343,
          334,  243,  259,  494,  260,  495,  498,  499,  216,  390,
          149,  150,  436,  490,  438,  488,  492,  437,  411,  417,
          418,  419,  420,  421,  422,  423,  424,  425,  426,  427,
          428,  429,  409,  410,  491,32767,32767,  469,  468,  467,
          434,32767,32767,32767,32767,32767,32767,32767,32767,  102,
        32767,  435,  439,  442,  408,  440,  441,  458,  459,  456,
          457,  460,32767,32767,  320,32767,32767,  461,  462,  463,
          464,  371,  195,  369,32767,32767,  443,  320,  111,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,  449,  450,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,  102,32767,  100,
          506,  556,  466,  444,  445,32767,  531,32767,  102,32767,
          533,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,  558,  431,  433,  526,  612,  412,  615,32767,  519,
          100,  195,32767,  532,  195,  195,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,  557,32767,  625,  519,
          110,  110,  110,  110,  110,  110,  110,  110,  110,  110,
          110,  110,32767,  195,  110,32767,  110,  110,32767,32767,
          100,  195,  195,  195,  195,  195,  195,  195,  195,  534,
          195,  195,  190,32767,  269,  271,  102,  580,  195,  536,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,  393,32767,32767,32767,32767,  519,  454,  138,
        32767,  521,  138,  564,  446,  447,  448,  564,  564,  564,
          316,  293,32767,32767,32767,32767,  534,  534,  100,  100,
          100,  100,32767,32767,32767,32767,  111,  505,   99,   99,
           99,   99,   99,  103,  101,32767,32767,32767,32767,  224,
        32767,  101,   99,32767,  101,  101,32767,32767,  224,  226,
          213,  228,32767,  584,  585,  224,  101,  228,  228,  228,
          248,  248,  508,  322,  101,   99,  101,  101,  197,  322,
          322,32767,  101,  508,  322,  508,  322,  199,  322,  322,
          322,  508,  322,32767,  101,  322,  215,   99,   99,  322,
        32767,32767,32767,32767,  521,32767,32767,32767,32767,32767,
        32767,32767,  223,32767,32767,32767,32767,32767,32767,32767,
        32767,  551,32767,  569,  582,  452,  453,  455,  568,  566,
          477,  478,  479,  480,  481,  482,  483,  485,  613,32767,
          525,32767,32767,32767,  342,32767,  623,32767,32767,32767,
            9,   74,  514,   42,   43,   51,   57,  540,  541,  542,
          543,  537,  538,  544,  539,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
          624,32767,  564,32767,32767,32767,32767,  451,  546,  590,
        32767,32767,  565,  616,32767,32767,32767,32767,32767,32767,
        32767,  138,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,  551,32767,  136,32767,32767,32767,32767,32767,
        32767,32767,32767,  547,32767,32767,32767,  564,32767,32767,
        32767,32767,  318,  315,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
          564,32767,32767,32767,32767,32767,  295,32767,  312,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,  389,  521,  298,
          300,  301,32767,32767,32767,32767,  365,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
          152,  152,    3,    3,  345,  152,  152,  152,  345,  345,
          152,  345,  345,  345,  152,  152,  152,  152,  152,  152,
          152,  281,  185,  263,  266,  248,  248,  152,  357,  152,
          391,  391,  400
    );

    protected array $goto = array(
          194,  194, 1052,  487,  705,  278,  278,  278,  278,  990,
          489,  548,  548,  907,  865,  907,  907,  548,  714,  548,
          548,  548,  548,  548,  548,  548,  548,  166,  166,  166,
          166,  218,  195,  191,  191,  176,  178,  213,  191,  191,
          191,  191,  191,  192,  192,  192,  192,  192,  186,  187,
          188,  189,  190,  215,  213,  216,  545,  546,  428,  547,
          550,  551,  552,  553,  554,  555,  556,  557, 1169,  167,
          168,  169,  193,  170,  171,  172,  164,  173,  174,  175,
          177,  212,  214,  217,  237,  240,  251,  252,  253,  255,
          256,  257,  258,  259,  260,  261,  267,  268,  269,  270,
          276,  288,  289,  313,  314,  434,  435,  436,  607,  219,
          220,  221,  222,  223,  224,  225,  226,  227,  228,  229,
          230,  231,  232,  233,  234,  186,  187,  188,  189,  190,
          215, 1169,  196,  197,  198,  199,  238,  179,  180,  200,
          181,  201,  197,  182,  239,  196,  163,  202,  203,  183,
          204,  205,  206,  184,  207,  208,  165,  209,  210,  211,
          185,  869,  560, 1083,  560,  560,  592, 1100,  475,  475,
          744,  646,  648,  609,  560,  668,  432,  475,  621,  692,
          695, 1025,  703,  712, 1021,  719,  558,  558,  558,  558,
          470,  613,  866,  663,  664,  463,  681,  682,  683, 1218,
          984,  984,  984,  984,  247,  247,  463,  978,  985,  355,
          355,  355,  355,  867,  923,  918,  919,  932,  875,  920,
          872,  921,  922,  873,  350,  926,  879, 1126, 1154, 1127,
          878,  245,  245,  245,  245,  242,  248,  841, 1106, 1102,
         1103,  438,  670,  402,  405,  610,  614,  433,  336,  332,
          333,  335,  602,  437,  337,  439,  647,  426, 1273, 1052,
         1273, 1273,  342,  900,  456,  456,  348,  456,  456, 1052,
         1273,  882, 1052,  520, 1052, 1052, 1052, 1052, 1052, 1052,
         1052, 1052, 1052,  343,  342, 1052, 1052, 1052, 1052,  894,
          465, 1273,  881,  508,  599,  509, 1273, 1273, 1273, 1273,
          358,  515, 1273, 1273, 1273, 1354, 1354, 1354, 1354,  862,
          358,  358, 1372, 1372,  630,  667,  895,  883, 1088, 1092,
          940,  358,  358, 1362,  941,  358, 1011, 1372, 1389,  993,
          956,  447,  956,  619,  633,  636,  637,  638,  639,  660,
          661,  662,  716,  718,  564,  569,  562,  358,  358, 1375,
         1375,  400,  983, 1055, 1055,  690,  967,  597,  862, 1047,
         1063, 1064,  456,  456,  456,  456,  456,  456,  456,  456,
          456,  456,  456,  456, 1138,  899,  456,  669,  456,  456,
         1058, 1057,  322,  562,  569,  594,  595,  324,  605,  611,
         1166,  626,  627, 1028, 1028, 1061, 1062,  632,  632,   25,
          320,  306, 1334, 1304, 1304, 1304, 1304, 1304, 1304, 1304,
         1304, 1304, 1304,  702, 1349, 1350, 1014,  843,    5,  986,
            6,  743,  445,  422,  561, 1023, 1018, 1076, 1345,  702,
         1345, 1345,  702,  603,  624, 1323, 1323,  691,  250,  250,
         1345, 1323, 1323, 1323, 1323, 1323, 1323, 1323, 1323, 1323,
         1323,  563,  589,  927,  564,  928,  563,  675,  589,  859,
          403,  469, 1356, 1356, 1356, 1356,  338,  887,  271,  319,
          625,  319,  319,  478,  606,  479,  480,  973,  351,  352,
          409,  892, 1320, 1320, 1380, 1381, 1341,  862, 1320, 1320,
         1320, 1320, 1320, 1320, 1320, 1320, 1320, 1320,  982,  417,
          713, 1268, 1264,  414,  415, 1033,  884,  440,  679,  890,
          680, 1149,  419,  420,  421, 1089,  693,  847, 1266,  423,
          440,  747, 1043,  346,  485, 1093, 1059, 1059,  330,  484,
         1347, 1348, 1140,  674, 1070, 1066, 1067, 1091,  896,  995,
          549,  549,  377, 1343, 1343, 1091,  549,  549,  549,  549,
          549,  549,  549,  549,  549,  549, 1269, 1270,    0, 1256,
            0,  847,    0,  847,  615,  857,    0,  945, 1156,  640,
          642,  644, 1256,    0,    0,    0,    0,  608, 1119, 1030,
            0,    0,  752,  752, 1271, 1331, 1332,  886,  717,  673,
         1009,    0,    0,  516,  708,  880, 1117, 1249,  959,    0,
            0,    0, 1250, 1253,  960,    0, 1254, 1263
    );

    protected array $gotoCheck = array(
           42,   42,   73,   84,   73,   23,   23,   23,   23,   49,
           84,  162,  162,   25,   25,   25,   25,  162,    9,  162,
          162,  162,  162,  162,  162,  162,  162,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   15,   19,  128,   19,   19,   48,   15,  154,  154,
           48,   48,   48,  131,   19,   48,   13,  154,   13,   48,
           48,   48,   48,   48,   48,   48,  107,  107,  107,  107,
          156,  107,   26,   86,   86,   19,   86,   86,   86,  156,
           19,   19,   19,   19,    5,    5,   19,   19,   19,   24,
           24,   24,   24,   27,   15,   15,   15,   15,   15,   15,
           15,   15,   15,   15,   97,   15,   15,  146,  146,  146,
           15,    5,    5,    5,    5,    5,    5,    6,   15,   15,
           15,   66,   66,   59,   59,   59,   59,   66,   66,   66,
           66,   66,   66,   66,   66,   66,   66,   43,   73,   73,
           73,   73,  174,   45,   23,   23,  185,   23,   23,   73,
           73,   35,   73,   76,   73,   73,   73,   73,   73,   73,
           73,   73,   73,  174,  174,   73,   73,   73,   73,   35,
           83,   73,   35,  160,  178,  160,   73,   73,   73,   73,
           14,  160,   73,   73,   73,    9,    9,    9,    9,   22,
           14,   14,  188,  188,   56,   56,   16,   16,   16,   16,
           73,   14,   14,  187,   73,   14,  103,  188,   14,   16,
            9,   83,    9,   81,   81,   81,   81,   81,   81,   81,
           81,   81,   81,   81,   14,   76,   76,   14,   14,  188,
          188,   62,   16,   89,   89,   89,   89,  104,   22,   89,
           89,   89,   23,   23,   23,   23,   23,   23,   23,   23,
           23,   23,   23,   23,   16,   16,   23,   64,   23,   23,
          119,  119,   76,   76,   76,   76,   76,   76,   76,   76,
          155,   76,   76,  107,  107,  120,  120,  108,  108,   76,
          175,  175,   14,  108,  108,  108,  108,  108,  108,  108,
          108,  108,  108,    7,  184,  184,   50,    7,   46,   50,
           46,   50,  113,   14,   50,   50,   50,  115,  131,    7,
          131,  131,    7,    2,    2,  176,  176,  117,    5,    5,
          131,  176,  176,  176,  176,  176,  176,  176,  176,  176,
          176,    9,    9,   65,   14,   65,    9,  121,    9,   18,
            9,    9,  131,  131,  131,  131,   29,   39,   24,   24,
           80,   24,   24,    9,    9,    9,    9,   92,   97,   97,
           28,    9,  177,  177,    9,    9,  131,   22,  177,  177,
          177,  177,  177,  177,  177,  177,  177,  177,   93,   93,
           93,   20,  166,   82,   82,  110,   37,  118,   82,    9,
           82,  153,   82,   82,   82,  130,   82,   12,   14,   82,
          118,   99,  114,   82,  157,  133,  118,  118,    9,  182,
          182,  182,  149,  118,  118,  118,  118,  131,   41,   96,
          179,  179,  138,  131,  131,  131,  179,  179,  179,  179,
          179,  179,  179,  179,  179,  179,   20,   20,   -1,   20,
           -1,   12,   -1,   12,   17,   20,   -1,   17,   17,   85,
           85,   85,   20,   -1,   -1,   -1,   -1,    8,    8,   17,
           -1,   -1,   24,   24,   20,   20,   20,   17,    8,   17,
           17,   -1,   -1,    8,    8,   17,    8,   79,   79,   -1,
           -1,   -1,   79,   79,   79,   -1,   79,   17
    );

    protected array $gotoBase = array(
            0,    0, -289,    0,    0,  203,  227,  406,  569,    8,
            0,    0,  223, -162,    5, -186, -143,   93,  152, -101,
          102,    0,   31,    2,  206,   10,  188,  209,  142,  172,
            0,    0,    0,    0,    0, -104,    0,  166,    0,  149,
            0,   90,   -1,  234,    0,  237, -329,    0, -555,   -9,
          404,    0,    0,    0,    0,    0,  274,    0,    0,  198,
            0,    0,  309,    0,  141,  439,    6,    0,    0,    0,
            0,    0,    0,   -5,    0,    0,    1,    0,    0,  183,
          146,  -28,    4,   12, -475,   82, -535,    0,    0,   74,
            0,    0,  151,  196,    0,    0,   89, -267,    0,  108,
            0,    0,    0,  291,  314,    0,    0,  158,  162,    0,
          131,    0,    0,  145,  100,  153,    0,  156,  243,  101,
          112,  167,    0,    0,    0,    0,    0,    0,  161,    0,
          135,  165,    0,   76,    0,    0,    0,    0, -209,    0,
            0,    0,    0,    0,    0,    0,  -44,    0,    0,   81,
            0,    0,    0,  157,  134,  148,  -76,   77,    0,    0,
         -210,    0, -224,    0,    0,    0,  129,    0,    0,    0,
            0,    0,    0,    0,  -33,   84,  200,  247,  265,  305,
            0,    0,  231,    0,   36,  236,    0,  292,    7,    0,
            0
    );

    protected array $gotoDefault = array(
        -32768,  521,  754,    4,  755,  949,  830,  839,  585,  539,
          715,  347,  634,  429, 1339,  925, 1155,  604,  858, 1282,
         1288,  464,  861,  327,  741,  937,  908,  909,  406,  393,
          874,  404,  658,  635,  502,  893,  460,  885,  494,  888,
          459,  897,  162,  425,  518,  901,    3,  904,  567,  935,
          988,  394,  912,  395,  686,  914,  588,  916,  917,  401,
          407,  408, 1160,  596,  631,  929,  254,  590,  930,  392,
          931,  939,  397,  399,  696,  474,  513,  507,  418, 1121,
          591,  618,  655,  453,  481,  629,  641,  628,  488,  441,
          424,  326,  972,  980,  495,  472,  994,  349, 1002,  749,
         1168,  649,  497, 1010,  650, 1017, 1020,  540,  541,  486,
         1032,  264, 1035,  498, 1044,   23,  676, 1049, 1050,  677,
          651, 1072,  652,  678,  653, 1074,  471,  586, 1082,  461,
         1090, 1328,  462, 1094,  262, 1097,  277,  353,  376,  442,
         1104, 1105,    9, 1111,  706,  707,   19,  273,  517, 1139,
          697, 1145,  272, 1148,  458, 1167,  457, 1237, 1239,  568,
          499, 1257,  310, 1260,  689,  514, 1265,  454, 1330,  455,
          542,  482,  334,  543, 1373,  305,  356,  331,  559,  311,
          357,  544,  483, 1336, 1344,  328,   31, 1363, 1374,  601,
          623
    );

    protected array $ruleToNonTerminal = array(
            0,    1,    3,    3,    2,    5,    5,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    7,    7,    7,
            7,    7,    7,    7,    7,    8,    8,    9,   10,   11,
           11,   11,   12,   12,   13,   13,   14,   15,   15,   16,
           16,   17,   17,   18,   18,   21,   21,   22,   23,   23,
           24,   24,    4,    4,    4,    4,    4,    4,    4,    4,
            4,    4,    4,   29,   29,   30,   30,   32,   34,   34,
           28,   36,   36,   33,   38,   38,   35,   35,   37,   37,
           39,   39,   31,   40,   40,   41,   43,   44,   44,   45,
           45,   46,   46,   48,   47,   47,   47,   47,   49,   49,
           49,   49,   49,   49,   49,   49,   49,   49,   49,   49,
           49,   49,   49,   49,   49,   49,   49,   49,   49,   49,
           49,   49,   25,   25,   50,   69,   69,   72,   72,   71,
           70,   70,   63,   75,   75,   76,   76,   77,   77,   78,
           78,   79,   79,   80,   80,   80,   26,   26,   27,   27,
           27,   27,   27,   88,   88,   90,   90,   83,   83,   91,
           91,   92,   92,   92,   84,   84,   87,   87,   85,   85,
           93,   94,   94,   57,   57,   65,   65,   68,   68,   68,
           67,   95,   95,   96,   58,   58,   58,   58,   97,   97,
           98,   98,   99,   99,  100,  101,  101,  102,  102,  103,
          103,   55,   55,   51,   51,  105,   53,   53,  106,   52,
           52,   54,   54,   64,   64,   64,   64,   81,   81,  109,
          109,  111,  111,  112,  112,  112,  112,  112,  112,  112,
          110,  110,  110,  115,  115,  115,  115,   89,   89,  118,
          118,  118,  119,  119,  116,  116,  120,  120,  122,  122,
          123,  123,  117,  124,  124,  121,  125,  125,  125,  125,
          113,  113,   82,   82,   82,   20,   20,   20,  127,  126,
          126,  128,  128,  128,  128,   60,  129,  129,  130,   61,
          132,  132,  133,  133,  134,  134,   86,  135,  135,  135,
          135,  135,  135,  135,  135,  141,  141,  142,  142,  143,
          143,  143,  143,  143,  144,  145,  145,  140,  140,  136,
          136,  139,  139,  147,  147,  146,  146,  146,  146,  146,
          146,  146,  146,  146,  146,  137,  148,  148,  150,  149,
          149,  138,  138,  114,  114,  151,  151,  153,  153,  153,
          152,  152,   62,  104,  154,  154,   56,   56,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,  161,  162,  162,  163,  155,  155,  160,  160,
          164,  165,  165,  166,  167,  168,  168,  168,  168,   19,
           19,   73,   73,   73,   73,  156,  156,  156,  156,  170,
          170,  159,  159,  159,  157,  157,  176,  176,  176,  176,
          176,  176,  176,  176,  176,  176,  177,  177,  177,  108,
          179,  179,  179,  179,  158,  158,  158,  158,  158,  158,
          158,  158,   59,   59,  173,  173,  173,  173,  173,  180,
          180,  169,  169,  169,  169,  181,  181,  181,  181,  181,
           74,   74,   66,   66,   66,   66,  131,  131,  131,  131,
          184,  183,  172,  172,  172,  172,  172,  172,  171,  171,
          171,  182,  182,  182,  182,  107,  178,  186,  186,  185,
          185,  187,  187,  187,  187,  187,  187,  187,  187,  175,
          175,  175,  175,  174,  189,  188,  188,  188,  188,  188,
          188,  188,  188,  190,  190,  190,  190
    );

    protected array $ruleToLength = array(
            1,    1,    2,    0,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    0,
            1,    0,    1,    1,    2,    1,    3,    4,    1,    2,
            0,    1,    1,    1,    1,    4,    3,    5,    4,    3,
            4,    1,    3,    1,    1,    8,    7,    2,    3,    1,
            2,    3,    1,    2,    3,    1,    1,    3,    1,    3,
            1,    2,    2,    3,    1,    3,    2,    3,    1,    3,
            3,    2,    0,    1,    1,    1,    1,    1,    3,    7,
           10,    5,    7,    9,    5,    3,    3,    3,    3,    3,
            3,    1,    2,    5,    7,    9,    6,    5,    6,    3,
            2,    1,    1,    1,    1,    0,    2,    1,    3,    8,
            0,    4,    2,    1,    3,    0,    1,    0,    1,    0,
            1,    3,    1,    1,    1,    1,    8,    9,    7,    8,
            7,    6,    8,    0,    2,    0,    2,    1,    2,    1,
            2,    1,    1,    1,    0,    2,    0,    2,    0,    2,
            2,    1,    3,    1,    4,    1,    4,    1,    1,    4,
            2,    1,    3,    3,    3,    4,    4,    5,    0,    2,
            4,    3,    1,    1,    7,    0,    2,    1,    3,    3,
            4,    1,    4,    0,    2,    5,    0,    2,    6,    0,
            2,    0,    3,    1,    2,    1,    1,    2,    0,    1,
            3,    0,    2,    1,    1,    1,    1,    1,    1,    1,
            7,    9,    6,    1,    2,    1,    1,    1,    1,    1,
            1,    1,    1,    3,    3,    3,    1,    3,    3,    3,
            3,    3,    1,    3,    3,    1,    1,    2,    1,    1,
            0,    1,    0,    2,    2,    2,    4,    3,    1,    1,
            3,    1,    2,    2,    3,    2,    3,    1,    1,    2,
            3,    1,    1,    3,    2,    0,    1,    5,    7,    5,
            6,   10,    3,    5,    1,    1,    3,    0,    2,    4,
            5,    4,    4,    4,    3,    1,    1,    1,    1,    1,
            1,    0,    1,    1,    2,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    2,    1,    3,    1,    1,
            3,    0,    2,    0,    3,    5,    8,    1,    3,    3,
            0,    2,    2,    2,    3,    1,    0,    1,    1,    3,
            3,    3,    4,    4,    1,    1,    2,    3,    3,    3,
            3,    3,    3,    3,    3,    3,    3,    3,    3,    3,
            2,    2,    2,    2,    3,    3,    3,    3,    3,    3,
            3,    3,    3,    3,    3,    3,    3,    3,    3,    3,
            3,    3,    2,    2,    2,    2,    3,    3,    3,    3,
            3,    3,    3,    3,    3,    3,    3,    5,    4,    3,
            4,    4,    2,    2,    4,    2,    2,    2,    2,    2,
            2,    2,    2,    2,    2,    2,    1,    3,    2,    1,
            2,    4,    2,    2,    8,    9,    8,    9,    9,   10,
            9,   10,    8,    3,    2,    2,    1,    1,    0,    4,
            2,    1,    3,    2,    1,    2,    2,    2,    4,    1,
            1,    1,    1,    1,    1,    1,    1,    3,    1,    1,
            1,    0,    1,    1,    0,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    3,    5,    3,    3,
            4,    1,    1,    3,    1,    1,    1,    1,    1,    3,
            2,    3,    0,    1,    1,    3,    1,    1,    1,    1,
            1,    1,    3,    1,    1,    1,    4,    1,    4,    4,
            0,    1,    1,    1,    3,    3,    1,    4,    2,    2,
            1,    3,    1,    4,    3,    3,    3,    3,    1,    3,
            1,    1,    3,    1,    1,    4,    1,    1,    1,    3,
            1,    1,    2,    1,    3,    4,    3,    2,    0,    2,
            2,    1,    2,    1,    1,    1,    4,    3,    3,    3,
            3,    6,    3,    1,    1,    2,    1
    );

    protected function initReduceCallbacks(): void {
        $this->reduceCallbacks = [
            0 => null,
            1 => static function ($self, $stackPos) {
                 $self->semValue = $self->handleNamespaces($self->semStack[$stackPos-(1-1)]);
            },
            2 => static function ($self, $stackPos) {
                 if ($self->semStack[$stackPos-(2-2)] !== null) { $self->semStack[$stackPos-(2-1)][] = $self->semStack[$stackPos-(2-2)]; } $self->semValue = $self->semStack[$stackPos-(2-1)];;
            },
            3 => static function ($self, $stackPos) {
                 $self->semValue = array();
            },
            4 => static function ($self, $stackPos) {
                 $nop = $self->maybeCreateZeroLengthNop($self->tokenPos);;
            if ($nop !== null) { $self->semStack[$stackPos-(1-1)][] = $nop; } $self->semValue = $self->semStack[$stackPos-(1-1)];
            },
            5 => null,
            6 => null,
            7 => null,
            8 => null,
            9 => null,
            10 => null,
            11 => null,
            12 => null,
            13 => null,
            14 => null,
            15 => null,
            16 => null,
            17 => null,
            18 => null,
            19 => null,
            20 => null,
            21 => null,
            22 => null,
            23 => null,
            24 => null,
            25 => null,
            26 => null,
            27 => null,
            28 => null,
            29 => null,
            30 => null,
            31 => null,
            32 => null,
            33 => null,
            34 => null,
            35 => null,
            36 => null,
            37 => null,
            38 => null,
            39 => null,
            40 => null,
            41 => null,
            42 => null,
            43 => null,
            44 => null,
            45 => null,
            46 => null,
            47 => null,
            48 => null,
            49 => null,
            50 => null,
            51 => null,
            52 => null,
            53 => null,
            54 => null,
            55 => null,
            56 => null,
            57 => null,
            58 => null,
            59 => null,
            60 => null,
            61 => null,
            62 => null,
            63 => null,
            64 => null,
            65 => null,
            66 => null,
            67 => null,
            68 => null,
            69 => null,
            70 => null,
            71 => null,
            72 => null,
            73 => null,
            74 => null,
            75 => null,
            76 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(1-1)]; if ($self->semValue === "<?=") $self->emitError(new Error('Cannot use "<?=" as an identifier', $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos])));
            },
            77 => null,
            78 => null,
            79 => null,
            80 => null,
            81 => null,
            82 => null,
            83 => null,
            84 => null,
            85 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Identifier($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            86 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Identifier($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            87 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Identifier($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            88 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Identifier($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            89 => static function ($self, $stackPos) {
                 $self->semValue = new Name($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            90 => static function ($self, $stackPos) {
                 $self->semValue = new Name($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            91 => static function ($self, $stackPos) {
                 $self->semValue = new Name($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            92 => static function ($self, $stackPos) {
                 $self->semValue = new Name($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            93 => static function ($self, $stackPos) {
                 $self->semValue = new Name($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            94 => null,
            95 => static function ($self, $stackPos) {
                 $self->semValue = new Name(substr($self->semStack[$stackPos-(1-1)], 1), $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            96 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Variable(substr($self->semStack[$stackPos-(1-1)], 1), $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            97 => static function ($self, $stackPos) {
                 /* nothing */
            },
            98 => static function ($self, $stackPos) {
                 /* nothing */
            },
            99 => static function ($self, $stackPos) {
                 /* nothing */
            },
            100 => static function ($self, $stackPos) {
                 $self->emitError(new Error('A trailing comma is not allowed here', $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos])));
            },
            101 => null,
            102 => null,
            103 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Attribute($self->semStack[$stackPos-(1-1)], [], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            104 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Attribute($self->semStack[$stackPos-(2-1)], $self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            105 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            106 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            107 => static function ($self, $stackPos) {
                 $self->semValue = new Node\AttributeGroup($self->semStack[$stackPos-(4-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            108 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            109 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(2-1)][] = $self->semStack[$stackPos-(2-2)]; $self->semValue = $self->semStack[$stackPos-(2-1)];
            },
            110 => static function ($self, $stackPos) {
                 $self->semValue = [];
            },
            111 => null,
            112 => null,
            113 => null,
            114 => null,
            115 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\HaltCompiler($self->handleHaltCompiler(), $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            116 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Namespace_($self->semStack[$stackPos-(3-2)], null, $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            $self->semValue->setAttribute('kind', Stmt\Namespace_::KIND_SEMICOLON);
            $self->checkNamespace($self->semValue);
            },
            117 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Namespace_($self->semStack[$stackPos-(5-2)], $self->semStack[$stackPos-(5-4)], $self->getAttributes($self->tokenStartStack[$stackPos-(5-1)], $self->tokenEndStack[$stackPos]));
            $self->semValue->setAttribute('kind', Stmt\Namespace_::KIND_BRACED);
            $self->checkNamespace($self->semValue);
            },
            118 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Namespace_(null, $self->semStack[$stackPos-(4-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            $self->semValue->setAttribute('kind', Stmt\Namespace_::KIND_BRACED);
            $self->checkNamespace($self->semValue);
            },
            119 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Use_($self->semStack[$stackPos-(3-2)], Stmt\Use_::TYPE_NORMAL, $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            120 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Use_($self->semStack[$stackPos-(4-3)], $self->semStack[$stackPos-(4-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            121 => null,
            122 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Const_($self->semStack[$stackPos-(3-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            123 => static function ($self, $stackPos) {
                 $self->semValue = Stmt\Use_::TYPE_FUNCTION;
            },
            124 => static function ($self, $stackPos) {
                 $self->semValue = Stmt\Use_::TYPE_CONSTANT;
            },
            125 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\GroupUse($self->semStack[$stackPos-(8-3)], $self->semStack[$stackPos-(8-6)], $self->semStack[$stackPos-(8-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(8-1)], $self->tokenEndStack[$stackPos]));
            },
            126 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\GroupUse($self->semStack[$stackPos-(7-2)], $self->semStack[$stackPos-(7-5)], Stmt\Use_::TYPE_UNKNOWN, $self->getAttributes($self->tokenStartStack[$stackPos-(7-1)], $self->tokenEndStack[$stackPos]));
            },
            127 => null,
            128 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            129 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            130 => null,
            131 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            132 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            133 => null,
            134 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            135 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            136 => static function ($self, $stackPos) {
                 $self->semValue = new Node\UseItem($self->semStack[$stackPos-(1-1)], null, Stmt\Use_::TYPE_UNKNOWN, $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos])); $self->checkUseUse($self->semValue, $stackPos-(1-1));
            },
            137 => static function ($self, $stackPos) {
                 $self->semValue = new Node\UseItem($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], Stmt\Use_::TYPE_UNKNOWN, $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos])); $self->checkUseUse($self->semValue, $stackPos-(3-3));
            },
            138 => static function ($self, $stackPos) {
                 $self->semValue = new Node\UseItem($self->semStack[$stackPos-(1-1)], null, Stmt\Use_::TYPE_UNKNOWN, $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos])); $self->checkUseUse($self->semValue, $stackPos-(1-1));
            },
            139 => static function ($self, $stackPos) {
                 $self->semValue = new Node\UseItem($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], Stmt\Use_::TYPE_UNKNOWN, $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos])); $self->checkUseUse($self->semValue, $stackPos-(3-3));
            },
            140 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(1-1)]; $self->semValue->type = Stmt\Use_::TYPE_NORMAL;
            },
            141 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(2-2)]; $self->semValue->type = $self->semStack[$stackPos-(2-1)];
            },
            142 => null,
            143 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            144 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            145 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Const_($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            146 => null,
            147 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            148 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            149 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Const_(new Node\Identifier($self->semStack[$stackPos-(3-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)],  $self->tokenEndStack[$stackPos-(3-1)])), $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            150 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Const_(new Node\Identifier($self->semStack[$stackPos-(3-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)],  $self->tokenEndStack[$stackPos-(3-1)])), $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            151 => static function ($self, $stackPos) {
                 if ($self->semStack[$stackPos-(2-2)] !== null) { $self->semStack[$stackPos-(2-1)][] = $self->semStack[$stackPos-(2-2)]; } $self->semValue = $self->semStack[$stackPos-(2-1)];;
            },
            152 => static function ($self, $stackPos) {
                 $self->semValue = array();
            },
            153 => static function ($self, $stackPos) {
                 $nop = $self->maybeCreateZeroLengthNop($self->tokenPos);;
            if ($nop !== null) { $self->semStack[$stackPos-(1-1)][] = $nop; } $self->semValue = $self->semStack[$stackPos-(1-1)];
            },
            154 => null,
            155 => null,
            156 => null,
            157 => static function ($self, $stackPos) {
                 throw new Error('__HALT_COMPILER() can only be used from the outermost scope', $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            158 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Block($self->semStack[$stackPos-(3-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            159 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\If_($self->semStack[$stackPos-(7-3)], ['stmts' => $self->semStack[$stackPos-(7-5)], 'elseifs' => $self->semStack[$stackPos-(7-6)], 'else' => $self->semStack[$stackPos-(7-7)]], $self->getAttributes($self->tokenStartStack[$stackPos-(7-1)], $self->tokenEndStack[$stackPos]));
            },
            160 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\If_($self->semStack[$stackPos-(10-3)], ['stmts' => $self->semStack[$stackPos-(10-6)], 'elseifs' => $self->semStack[$stackPos-(10-7)], 'else' => $self->semStack[$stackPos-(10-8)]], $self->getAttributes($self->tokenStartStack[$stackPos-(10-1)], $self->tokenEndStack[$stackPos]));
            },
            161 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\While_($self->semStack[$stackPos-(5-3)], $self->semStack[$stackPos-(5-5)], $self->getAttributes($self->tokenStartStack[$stackPos-(5-1)], $self->tokenEndStack[$stackPos]));
            },
            162 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Do_($self->semStack[$stackPos-(7-5)], $self->semStack[$stackPos-(7-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(7-1)], $self->tokenEndStack[$stackPos]));
            },
            163 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\For_(['init' => $self->semStack[$stackPos-(9-3)], 'cond' => $self->semStack[$stackPos-(9-5)], 'loop' => $self->semStack[$stackPos-(9-7)], 'stmts' => $self->semStack[$stackPos-(9-9)]], $self->getAttributes($self->tokenStartStack[$stackPos-(9-1)], $self->tokenEndStack[$stackPos]));
            },
            164 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Switch_($self->semStack[$stackPos-(5-3)], $self->semStack[$stackPos-(5-5)], $self->getAttributes($self->tokenStartStack[$stackPos-(5-1)], $self->tokenEndStack[$stackPos]));
            },
            165 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Break_($self->semStack[$stackPos-(3-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            166 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Continue_($self->semStack[$stackPos-(3-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            167 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Return_($self->semStack[$stackPos-(3-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            168 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Global_($self->semStack[$stackPos-(3-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            169 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Static_($self->semStack[$stackPos-(3-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            170 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Echo_($self->semStack[$stackPos-(3-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            171 => static function ($self, $stackPos) {

        $self->semValue = new Stmt\InlineHTML($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
        $self->semValue->setAttribute('hasLeadingNewline', $self->inlineHtmlHasLeadingNewline($stackPos-(1-1)));

            },
            172 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Expression($self->semStack[$stackPos-(2-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            173 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Unset_($self->semStack[$stackPos-(5-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(5-1)], $self->tokenEndStack[$stackPos]));
            },
            174 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Foreach_($self->semStack[$stackPos-(7-3)], $self->semStack[$stackPos-(7-5)][0], ['keyVar' => null, 'byRef' => $self->semStack[$stackPos-(7-5)][1], 'stmts' => $self->semStack[$stackPos-(7-7)]], $self->getAttributes($self->tokenStartStack[$stackPos-(7-1)], $self->tokenEndStack[$stackPos]));
            },
            175 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Foreach_($self->semStack[$stackPos-(9-3)], $self->semStack[$stackPos-(9-7)][0], ['keyVar' => $self->semStack[$stackPos-(9-5)], 'byRef' => $self->semStack[$stackPos-(9-7)][1], 'stmts' => $self->semStack[$stackPos-(9-9)]], $self->getAttributes($self->tokenStartStack[$stackPos-(9-1)], $self->tokenEndStack[$stackPos]));
            },
            176 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Foreach_($self->semStack[$stackPos-(6-3)], new Expr\Error($self->getAttributes($self->tokenStartStack[$stackPos-(6-4)],  $self->tokenEndStack[$stackPos-(6-4)])), ['stmts' => $self->semStack[$stackPos-(6-6)]], $self->getAttributes($self->tokenStartStack[$stackPos-(6-1)], $self->tokenEndStack[$stackPos]));
            },
            177 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Declare_($self->semStack[$stackPos-(5-3)], $self->semStack[$stackPos-(5-5)], $self->getAttributes($self->tokenStartStack[$stackPos-(5-1)], $self->tokenEndStack[$stackPos]));
            },
            178 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\TryCatch($self->semStack[$stackPos-(6-3)], $self->semStack[$stackPos-(6-5)], $self->semStack[$stackPos-(6-6)], $self->getAttributes($self->tokenStartStack[$stackPos-(6-1)], $self->tokenEndStack[$stackPos])); $self->checkTryCatch($self->semValue);
            },
            179 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Goto_($self->semStack[$stackPos-(3-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            180 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Label($self->semStack[$stackPos-(2-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            181 => static function ($self, $stackPos) {
                 $self->semValue = null; /* means: no statement */
            },
            182 => null,
            183 => static function ($self, $stackPos) {
                 $self->semValue = $self->maybeCreateNop($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]);
            },
            184 => static function ($self, $stackPos) {
                 if ($self->semStack[$stackPos-(1-1)] instanceof Stmt\Block) { $self->semValue = $self->semStack[$stackPos-(1-1)]->stmts; } else if ($self->semStack[$stackPos-(1-1)] === null) { $self->semValue = []; } else { $self->semValue = [$self->semStack[$stackPos-(1-1)]]; };
            },
            185 => static function ($self, $stackPos) {
                 $self->semValue = array();
            },
            186 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(2-1)][] = $self->semStack[$stackPos-(2-2)]; $self->semValue = $self->semStack[$stackPos-(2-1)];
            },
            187 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            188 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            189 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Catch_($self->semStack[$stackPos-(8-3)], $self->semStack[$stackPos-(8-4)], $self->semStack[$stackPos-(8-7)], $self->getAttributes($self->tokenStartStack[$stackPos-(8-1)], $self->tokenEndStack[$stackPos]));
            },
            190 => static function ($self, $stackPos) {
                 $self->semValue = null;
            },
            191 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Finally_($self->semStack[$stackPos-(4-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            192 => null,
            193 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            194 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            195 => static function ($self, $stackPos) {
                 $self->semValue = false;
            },
            196 => static function ($self, $stackPos) {
                 $self->semValue = true;
            },
            197 => static function ($self, $stackPos) {
                 $self->semValue = false;
            },
            198 => static function ($self, $stackPos) {
                 $self->semValue = true;
            },
            199 => static function ($self, $stackPos) {
                 $self->semValue = false;
            },
            200 => static function ($self, $stackPos) {
                 $self->semValue = true;
            },
            201 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(3-2)];
            },
            202 => static function ($self, $stackPos) {
                 $self->semValue = [];
            },
            203 => null,
            204 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Identifier($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            205 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Identifier($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            206 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Function_($self->semStack[$stackPos-(8-3)], ['byRef' => $self->semStack[$stackPos-(8-2)], 'params' => $self->semStack[$stackPos-(8-5)], 'returnType' => $self->semStack[$stackPos-(8-7)], 'stmts' => $self->semStack[$stackPos-(8-8)], 'attrGroups' => []], $self->getAttributes($self->tokenStartStack[$stackPos-(8-1)], $self->tokenEndStack[$stackPos]));
            },
            207 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Function_($self->semStack[$stackPos-(9-4)], ['byRef' => $self->semStack[$stackPos-(9-3)], 'params' => $self->semStack[$stackPos-(9-6)], 'returnType' => $self->semStack[$stackPos-(9-8)], 'stmts' => $self->semStack[$stackPos-(9-9)], 'attrGroups' => $self->semStack[$stackPos-(9-1)]], $self->getAttributes($self->tokenStartStack[$stackPos-(9-1)], $self->tokenEndStack[$stackPos]));
            },
            208 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Class_($self->semStack[$stackPos-(7-2)], ['type' => $self->semStack[$stackPos-(7-1)], 'extends' => $self->semStack[$stackPos-(7-3)], 'implements' => $self->semStack[$stackPos-(7-4)], 'stmts' => $self->semStack[$stackPos-(7-6)], 'attrGroups' => []], $self->getAttributes($self->tokenStartStack[$stackPos-(7-1)], $self->tokenEndStack[$stackPos]));
            $self->checkClass($self->semValue, $stackPos-(7-2));
            },
            209 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Class_($self->semStack[$stackPos-(8-3)], ['type' => $self->semStack[$stackPos-(8-2)], 'extends' => $self->semStack[$stackPos-(8-4)], 'implements' => $self->semStack[$stackPos-(8-5)], 'stmts' => $self->semStack[$stackPos-(8-7)], 'attrGroups' => $self->semStack[$stackPos-(8-1)]], $self->getAttributes($self->tokenStartStack[$stackPos-(8-1)], $self->tokenEndStack[$stackPos]));
            $self->checkClass($self->semValue, $stackPos-(8-3));
            },
            210 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Interface_($self->semStack[$stackPos-(7-3)], ['extends' => $self->semStack[$stackPos-(7-4)], 'stmts' => $self->semStack[$stackPos-(7-6)], 'attrGroups' => $self->semStack[$stackPos-(7-1)]], $self->getAttributes($self->tokenStartStack[$stackPos-(7-1)], $self->tokenEndStack[$stackPos]));
            $self->checkInterface($self->semValue, $stackPos-(7-3));
            },
            211 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Trait_($self->semStack[$stackPos-(6-3)], ['stmts' => $self->semStack[$stackPos-(6-5)], 'attrGroups' => $self->semStack[$stackPos-(6-1)]], $self->getAttributes($self->tokenStartStack[$stackPos-(6-1)], $self->tokenEndStack[$stackPos]));
            },
            212 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Enum_($self->semStack[$stackPos-(8-3)], ['scalarType' => $self->semStack[$stackPos-(8-4)], 'implements' => $self->semStack[$stackPos-(8-5)], 'stmts' => $self->semStack[$stackPos-(8-7)], 'attrGroups' => $self->semStack[$stackPos-(8-1)]], $self->getAttributes($self->tokenStartStack[$stackPos-(8-1)], $self->tokenEndStack[$stackPos]));
            $self->checkEnum($self->semValue, $stackPos-(8-3));
            },
            213 => static function ($self, $stackPos) {
                 $self->semValue = null;
            },
            214 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(2-2)];
            },
            215 => static function ($self, $stackPos) {
                 $self->semValue = null;
            },
            216 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(2-2)];
            },
            217 => static function ($self, $stackPos) {
                 $self->semValue = 0;
            },
            218 => null,
            219 => null,
            220 => static function ($self, $stackPos) {
                 $self->checkClassModifier($self->semStack[$stackPos-(2-1)], $self->semStack[$stackPos-(2-2)], $stackPos-(2-2)); $self->semValue = $self->semStack[$stackPos-(2-1)] | $self->semStack[$stackPos-(2-2)];
            },
            221 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::ABSTRACT;
            },
            222 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::FINAL;
            },
            223 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::READONLY;
            },
            224 => static function ($self, $stackPos) {
                 $self->semValue = null;
            },
            225 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(2-2)];
            },
            226 => static function ($self, $stackPos) {
                 $self->semValue = array();
            },
            227 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(2-2)];
            },
            228 => static function ($self, $stackPos) {
                 $self->semValue = array();
            },
            229 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(2-2)];
            },
            230 => null,
            231 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            232 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            233 => null,
            234 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(4-2)];
            },
            235 => null,
            236 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(4-2)];
            },
            237 => static function ($self, $stackPos) {
                 if ($self->semStack[$stackPos-(1-1)] instanceof Stmt\Block) { $self->semValue = $self->semStack[$stackPos-(1-1)]->stmts; } else if ($self->semStack[$stackPos-(1-1)] === null) { $self->semValue = []; } else { $self->semValue = [$self->semStack[$stackPos-(1-1)]]; };
            },
            238 => static function ($self, $stackPos) {
                 $self->semValue = null;
            },
            239 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(4-2)];
            },
            240 => null,
            241 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            242 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            243 => static function ($self, $stackPos) {
                 $self->semValue = new Node\DeclareItem($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            244 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(3-2)];
            },
            245 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(4-3)];
            },
            246 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(4-2)];
            },
            247 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(5-3)];
            },
            248 => static function ($self, $stackPos) {
                 $self->semValue = array();
            },
            249 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(2-1)][] = $self->semStack[$stackPos-(2-2)]; $self->semValue = $self->semStack[$stackPos-(2-1)];
            },
            250 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Case_($self->semStack[$stackPos-(4-2)], $self->semStack[$stackPos-(4-4)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            251 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Case_(null, $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            252 => null,
            253 => null,
            254 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Match_($self->semStack[$stackPos-(7-3)], $self->semStack[$stackPos-(7-6)], $self->getAttributes($self->tokenStartStack[$stackPos-(7-1)], $self->tokenEndStack[$stackPos]));
            },
            255 => static function ($self, $stackPos) {
                 $self->semValue = [];
            },
            256 => null,
            257 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            258 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            259 => static function ($self, $stackPos) {
                 $self->semValue = new Node\MatchArm($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            260 => static function ($self, $stackPos) {
                 $self->semValue = new Node\MatchArm(null, $self->semStack[$stackPos-(4-4)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            261 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(1-1)];
            },
            262 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(4-2)];
            },
            263 => static function ($self, $stackPos) {
                 $self->semValue = array();
            },
            264 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(2-1)][] = $self->semStack[$stackPos-(2-2)]; $self->semValue = $self->semStack[$stackPos-(2-1)];
            },
            265 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\ElseIf_($self->semStack[$stackPos-(5-3)], $self->semStack[$stackPos-(5-5)], $self->getAttributes($self->tokenStartStack[$stackPos-(5-1)], $self->tokenEndStack[$stackPos]));
            },
            266 => static function ($self, $stackPos) {
                 $self->semValue = array();
            },
            267 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(2-1)][] = $self->semStack[$stackPos-(2-2)]; $self->semValue = $self->semStack[$stackPos-(2-1)];
            },
            268 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\ElseIf_($self->semStack[$stackPos-(6-3)], $self->semStack[$stackPos-(6-6)], $self->getAttributes($self->tokenStartStack[$stackPos-(6-1)], $self->tokenEndStack[$stackPos])); $self->fixupAlternativeElse($self->semValue);
            },
            269 => static function ($self, $stackPos) {
                 $self->semValue = null;
            },
            270 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Else_($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            271 => static function ($self, $stackPos) {
                 $self->semValue = null;
            },
            272 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Else_($self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos])); $self->fixupAlternativeElse($self->semValue);
            },
            273 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)], false);
            },
            274 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(2-2)], true);
            },
            275 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)], false);
            },
            276 => static function ($self, $stackPos) {
                 $self->semValue = array($self->fixupArrayDestructuring($self->semStack[$stackPos-(1-1)]), false);
            },
            277 => null,
            278 => static function ($self, $stackPos) {
                 $self->semValue = array();
            },
            279 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            280 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            281 => static function ($self, $stackPos) {
                 $self->semValue = 0;
            },
            282 => static function ($self, $stackPos) {
                 $self->checkModifier($self->semStack[$stackPos-(2-1)], $self->semStack[$stackPos-(2-2)], $stackPos-(2-2)); $self->semValue = $self->semStack[$stackPos-(2-1)] | $self->semStack[$stackPos-(2-2)];
            },
            283 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::PUBLIC;
            },
            284 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::PROTECTED;
            },
            285 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::PRIVATE;
            },
            286 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::PUBLIC_SET;
            },
            287 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::PROTECTED_SET;
            },
            288 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::PRIVATE_SET;
            },
            289 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::READONLY;
            },
            290 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Param($self->semStack[$stackPos-(7-6)], null, $self->semStack[$stackPos-(7-3)], $self->semStack[$stackPos-(7-4)], $self->semStack[$stackPos-(7-5)], $self->getAttributes($self->tokenStartStack[$stackPos-(7-1)], $self->tokenEndStack[$stackPos]), $self->semStack[$stackPos-(7-2)], $self->semStack[$stackPos-(7-1)], $self->semStack[$stackPos-(7-7)]);
            $self->checkParam($self->semValue);
            $self->addPropertyNameToHooks($self->semValue);
            },
            291 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Param($self->semStack[$stackPos-(9-6)], $self->semStack[$stackPos-(9-8)], $self->semStack[$stackPos-(9-3)], $self->semStack[$stackPos-(9-4)], $self->semStack[$stackPos-(9-5)], $self->getAttributes($self->tokenStartStack[$stackPos-(9-1)], $self->tokenEndStack[$stackPos]), $self->semStack[$stackPos-(9-2)], $self->semStack[$stackPos-(9-1)], $self->semStack[$stackPos-(9-9)]);
            $self->checkParam($self->semValue);
            $self->addPropertyNameToHooks($self->semValue);
            },
            292 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Param(new Expr\Error($self->getAttributes($self->tokenStartStack[$stackPos-(6-1)], $self->tokenEndStack[$stackPos])), null, $self->semStack[$stackPos-(6-3)], $self->semStack[$stackPos-(6-4)], $self->semStack[$stackPos-(6-5)], $self->getAttributes($self->tokenStartStack[$stackPos-(6-1)], $self->tokenEndStack[$stackPos]), $self->semStack[$stackPos-(6-2)], $self->semStack[$stackPos-(6-1)]);
            },
            293 => null,
            294 => static function ($self, $stackPos) {
                 $self->semValue = new Node\NullableType($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            295 => static function ($self, $stackPos) {
                 $self->semValue = new Node\UnionType($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            296 => null,
            297 => null,
            298 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Name('static', $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            299 => static function ($self, $stackPos) {
                 $self->semValue = $self->handleBuiltinTypes($self->semStack[$stackPos-(1-1)]);
            },
            300 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Identifier('array', $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            301 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Identifier('callable', $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            302 => null,
            303 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(3-2)];
            },
            304 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)]);
            },
            305 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            306 => null,
            307 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(3-2)];
            },
            308 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)]);
            },
            309 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            310 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)]);
            },
            311 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            312 => static function ($self, $stackPos) {
                 $self->semValue = new Node\IntersectionType($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            313 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)]);
            },
            314 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            315 => static function ($self, $stackPos) {
                 $self->semValue = new Node\IntersectionType($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            316 => null,
            317 => static function ($self, $stackPos) {
                 $self->semValue = new Node\NullableType($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            318 => static function ($self, $stackPos) {
                 $self->semValue = new Node\UnionType($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            319 => null,
            320 => static function ($self, $stackPos) {
                 $self->semValue = null;
            },
            321 => null,
            322 => static function ($self, $stackPos) {
                 $self->semValue = null;
            },
            323 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(2-2)];
            },
            324 => static function ($self, $stackPos) {
                 $self->semValue = null;
            },
            325 => static function ($self, $stackPos) {
                 $self->semValue = array();
            },
            326 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(4-2)];
            },
            327 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(3-2)]);
            },
            328 => static function ($self, $stackPos) {
                 $self->semValue = new Node\VariadicPlaceholder($self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            329 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            330 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            331 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Arg($self->semStack[$stackPos-(1-1)], false, false, $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            332 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Arg($self->semStack[$stackPos-(2-2)], true, false, $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            333 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Arg($self->semStack[$stackPos-(2-2)], false, true, $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            334 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Arg($self->semStack[$stackPos-(3-3)], false, false, $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]), $self->semStack[$stackPos-(3-1)]);
            },
            335 => null,
            336 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            337 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            338 => null,
            339 => null,
            340 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            341 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            342 => static function ($self, $stackPos) {
                 $self->semValue = new Node\StaticVar($self->semStack[$stackPos-(1-1)], null, $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            343 => static function ($self, $stackPos) {
                 $self->semValue = new Node\StaticVar($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            344 => static function ($self, $stackPos) {
                 if ($self->semStack[$stackPos-(2-2)] !== null) { $self->semStack[$stackPos-(2-1)][] = $self->semStack[$stackPos-(2-2)]; $self->semValue = $self->semStack[$stackPos-(2-1)]; } else { $self->semValue = $self->semStack[$stackPos-(2-1)]; }
            },
            345 => static function ($self, $stackPos) {
                 $self->semValue = array();
            },
            346 => static function ($self, $stackPos) {
                 $nop = $self->maybeCreateZeroLengthNop($self->tokenPos);;
            if ($nop !== null) { $self->semStack[$stackPos-(1-1)][] = $nop; } $self->semValue = $self->semStack[$stackPos-(1-1)];
            },
            347 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Property($self->semStack[$stackPos-(5-2)], $self->semStack[$stackPos-(5-4)], $self->getAttributes($self->tokenStartStack[$stackPos-(5-1)], $self->tokenEndStack[$stackPos]), $self->semStack[$stackPos-(5-3)], $self->semStack[$stackPos-(5-1)]);
            },
            348 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Property($self->semStack[$stackPos-(7-2)], $self->semStack[$stackPos-(7-4)], $self->getAttributes($self->tokenStartStack[$stackPos-(7-1)], $self->tokenEndStack[$stackPos]), $self->semStack[$stackPos-(7-3)], $self->semStack[$stackPos-(7-1)], $self->semStack[$stackPos-(7-6)]);
            $self->checkPropertyHooksForMultiProperty($self->semValue, $stackPos-(7-5));
            $self->checkEmptyPropertyHookList($self->semStack[$stackPos-(7-6)], $stackPos-(7-5));
            $self->addPropertyNameToHooks($self->semValue);
            },
            349 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\ClassConst($self->semStack[$stackPos-(5-4)], $self->semStack[$stackPos-(5-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(5-1)], $self->tokenEndStack[$stackPos]), $self->semStack[$stackPos-(5-1)]);
            $self->checkClassConst($self->semValue, $stackPos-(5-2));
            },
            350 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\ClassConst($self->semStack[$stackPos-(6-5)], $self->semStack[$stackPos-(6-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(6-1)], $self->tokenEndStack[$stackPos]), $self->semStack[$stackPos-(6-1)], $self->semStack[$stackPos-(6-4)]);
            $self->checkClassConst($self->semValue, $stackPos-(6-2));
            },
            351 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\ClassMethod($self->semStack[$stackPos-(10-5)], ['type' => $self->semStack[$stackPos-(10-2)], 'byRef' => $self->semStack[$stackPos-(10-4)], 'params' => $self->semStack[$stackPos-(10-7)], 'returnType' => $self->semStack[$stackPos-(10-9)], 'stmts' => $self->semStack[$stackPos-(10-10)], 'attrGroups' => $self->semStack[$stackPos-(10-1)]], $self->getAttributes($self->tokenStartStack[$stackPos-(10-1)], $self->tokenEndStack[$stackPos]));
            $self->checkClassMethod($self->semValue, $stackPos-(10-2));
            },
            352 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\TraitUse($self->semStack[$stackPos-(3-2)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            353 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\EnumCase($self->semStack[$stackPos-(5-3)], $self->semStack[$stackPos-(5-4)], $self->semStack[$stackPos-(5-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(5-1)], $self->tokenEndStack[$stackPos]));
            },
            354 => static function ($self, $stackPos) {
                 $self->semValue = null; /* will be skipped */
            },
            355 => static function ($self, $stackPos) {
                 $self->semValue = array();
            },
            356 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(3-2)];
            },
            357 => static function ($self, $stackPos) {
                 $self->semValue = array();
            },
            358 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(2-1)][] = $self->semStack[$stackPos-(2-2)]; $self->semValue = $self->semStack[$stackPos-(2-1)];
            },
            359 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\TraitUseAdaptation\Precedence($self->semStack[$stackPos-(4-1)][0], $self->semStack[$stackPos-(4-1)][1], $self->semStack[$stackPos-(4-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            360 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\TraitUseAdaptation\Alias($self->semStack[$stackPos-(5-1)][0], $self->semStack[$stackPos-(5-1)][1], $self->semStack[$stackPos-(5-3)], $self->semStack[$stackPos-(5-4)], $self->getAttributes($self->tokenStartStack[$stackPos-(5-1)], $self->tokenEndStack[$stackPos]));
            },
            361 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\TraitUseAdaptation\Alias($self->semStack[$stackPos-(4-1)][0], $self->semStack[$stackPos-(4-1)][1], $self->semStack[$stackPos-(4-3)], null, $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            362 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\TraitUseAdaptation\Alias($self->semStack[$stackPos-(4-1)][0], $self->semStack[$stackPos-(4-1)][1], null, $self->semStack[$stackPos-(4-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            363 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\TraitUseAdaptation\Alias($self->semStack[$stackPos-(4-1)][0], $self->semStack[$stackPos-(4-1)][1], null, $self->semStack[$stackPos-(4-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            364 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)]);
            },
            365 => null,
            366 => static function ($self, $stackPos) {
                 $self->semValue = array(null, $self->semStack[$stackPos-(1-1)]);
            },
            367 => static function ($self, $stackPos) {
                 $self->semValue = null;
            },
            368 => null,
            369 => null,
            370 => static function ($self, $stackPos) {
                 $self->semValue = 0;
            },
            371 => static function ($self, $stackPos) {
                 $self->semValue = 0;
            },
            372 => null,
            373 => null,
            374 => static function ($self, $stackPos) {
                 $self->checkModifier($self->semStack[$stackPos-(2-1)], $self->semStack[$stackPos-(2-2)], $stackPos-(2-2)); $self->semValue = $self->semStack[$stackPos-(2-1)] | $self->semStack[$stackPos-(2-2)];
            },
            375 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::PUBLIC;
            },
            376 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::PROTECTED;
            },
            377 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::PRIVATE;
            },
            378 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::PUBLIC_SET;
            },
            379 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::PROTECTED_SET;
            },
            380 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::PRIVATE_SET;
            },
            381 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::STATIC;
            },
            382 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::ABSTRACT;
            },
            383 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::FINAL;
            },
            384 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::READONLY;
            },
            385 => null,
            386 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            387 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            388 => static function ($self, $stackPos) {
                 $self->semValue = new Node\VarLikeIdentifier(substr($self->semStack[$stackPos-(1-1)], 1), $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            389 => static function ($self, $stackPos) {
                 $self->semValue = new Node\PropertyItem($self->semStack[$stackPos-(1-1)], null, $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            390 => static function ($self, $stackPos) {
                 $self->semValue = new Node\PropertyItem($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            391 => static function ($self, $stackPos) {
                 $self->semValue = [];
            },
            392 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(2-1)][] = $self->semStack[$stackPos-(2-2)]; $self->semValue = $self->semStack[$stackPos-(2-1)];
            },
            393 => static function ($self, $stackPos) {
                 $self->semValue = [];
            },
            394 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(3-2)]; $self->checkEmptyPropertyHookList($self->semStack[$stackPos-(3-2)], $stackPos-(3-1));
            },
            395 => static function ($self, $stackPos) {
                 $self->semValue = new Node\PropertyHook($self->semStack[$stackPos-(5-4)], $self->semStack[$stackPos-(5-5)], ['flags' => $self->semStack[$stackPos-(5-2)], 'byRef' => $self->semStack[$stackPos-(5-3)], 'params' => [], 'attrGroups' => $self->semStack[$stackPos-(5-1)]], $self->getAttributes($self->tokenStartStack[$stackPos-(5-1)], $self->tokenEndStack[$stackPos]));
            $self->checkPropertyHook($self->semValue, null);
            },
            396 => static function ($self, $stackPos) {
                 $self->semValue = new Node\PropertyHook($self->semStack[$stackPos-(8-4)], $self->semStack[$stackPos-(8-8)], ['flags' => $self->semStack[$stackPos-(8-2)], 'byRef' => $self->semStack[$stackPos-(8-3)], 'params' => $self->semStack[$stackPos-(8-6)], 'attrGroups' => $self->semStack[$stackPos-(8-1)]], $self->getAttributes($self->tokenStartStack[$stackPos-(8-1)], $self->tokenEndStack[$stackPos]));
            $self->checkPropertyHook($self->semValue, $stackPos-(8-5));
            },
            397 => static function ($self, $stackPos) {
                 $self->semValue = null;
            },
            398 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(3-2)];
            },
            399 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(3-2)];
            },
            400 => static function ($self, $stackPos) {
                 $self->semValue = 0;
            },
            401 => static function ($self, $stackPos) {
                 $self->checkPropertyHookModifiers($self->semStack[$stackPos-(2-1)], $self->semStack[$stackPos-(2-2)], $stackPos-(2-2)); $self->semValue = $self->semStack[$stackPos-(2-1)] | $self->semStack[$stackPos-(2-2)];
            },
            402 => null,
            403 => null,
            404 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            405 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            406 => static function ($self, $stackPos) {
                 $self->semValue = array();
            },
            407 => null,
            408 => null,
            409 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Assign($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            410 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Assign($self->fixupArrayDestructuring($self->semStack[$stackPos-(3-1)]), $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            411 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Assign($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            412 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\AssignRef($self->semStack[$stackPos-(4-1)], $self->semStack[$stackPos-(4-4)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            413 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\AssignRef($self->semStack[$stackPos-(4-1)], $self->semStack[$stackPos-(4-4)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            if (!$self->phpVersion->allowsAssignNewByReference()) {
                $self->emitError(new Error('Cannot assign new by reference', $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos])));
            }

            },
            414 => null,
            415 => null,
            416 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Clone_($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            417 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\AssignOp\Plus($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            418 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\AssignOp\Minus($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            419 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\AssignOp\Mul($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            420 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\AssignOp\Div($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            421 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\AssignOp\Concat($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            422 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\AssignOp\Mod($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            423 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\AssignOp\BitwiseAnd($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            424 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\AssignOp\BitwiseOr($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            425 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\AssignOp\BitwiseXor($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            426 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\AssignOp\ShiftLeft($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            427 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\AssignOp\ShiftRight($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            428 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\AssignOp\Pow($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            429 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\AssignOp\Coalesce($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            430 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\PostInc($self->semStack[$stackPos-(2-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            431 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\PreInc($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            432 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\PostDec($self->semStack[$stackPos-(2-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            433 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\PreDec($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            434 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\BooleanOr($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            435 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\BooleanAnd($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            436 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\LogicalOr($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            437 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\LogicalAnd($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            438 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\LogicalXor($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            439 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\BitwiseOr($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            440 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\BitwiseAnd($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            441 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\BitwiseAnd($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            442 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\BitwiseXor($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            443 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\Concat($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            444 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\Plus($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            445 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\Minus($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            446 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\Mul($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            447 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\Div($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            448 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\Mod($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            449 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\ShiftLeft($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            450 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\ShiftRight($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            451 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\Pow($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            452 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\UnaryPlus($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            453 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\UnaryMinus($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            454 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BooleanNot($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            455 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BitwiseNot($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            456 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\Identical($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            457 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\NotIdentical($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            458 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\Equal($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            459 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\NotEqual($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            460 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\Spaceship($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            461 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\Smaller($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            462 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\SmallerOrEqual($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            463 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\Greater($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            464 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\GreaterOrEqual($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            465 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Instanceof_($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            466 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(3-2)];
            },
            467 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Ternary($self->semStack[$stackPos-(5-1)], $self->semStack[$stackPos-(5-3)], $self->semStack[$stackPos-(5-5)], $self->getAttributes($self->tokenStartStack[$stackPos-(5-1)], $self->tokenEndStack[$stackPos]));
            },
            468 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Ternary($self->semStack[$stackPos-(4-1)], null, $self->semStack[$stackPos-(4-4)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            469 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\Coalesce($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            470 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Isset_($self->semStack[$stackPos-(4-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            471 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Empty_($self->semStack[$stackPos-(4-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            472 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Include_($self->semStack[$stackPos-(2-2)], Expr\Include_::TYPE_INCLUDE, $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            473 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Include_($self->semStack[$stackPos-(2-2)], Expr\Include_::TYPE_INCLUDE_ONCE, $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            474 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Eval_($self->semStack[$stackPos-(4-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            475 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Include_($self->semStack[$stackPos-(2-2)], Expr\Include_::TYPE_REQUIRE, $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            476 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Include_($self->semStack[$stackPos-(2-2)], Expr\Include_::TYPE_REQUIRE_ONCE, $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            477 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Cast\Int_($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            478 => static function ($self, $stackPos) {
                 $attrs = $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]);
            $attrs['kind'] = $self->getFloatCastKind($self->semStack[$stackPos-(2-1)]);
            $self->semValue = new Expr\Cast\Double($self->semStack[$stackPos-(2-2)], $attrs);
            },
            479 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Cast\String_($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            480 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Cast\Array_($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            481 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Cast\Object_($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            482 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Cast\Bool_($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            483 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Cast\Unset_($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            484 => static function ($self, $stackPos) {
                 $self->semValue = $self->createExitExpr($self->semStack[$stackPos-(2-1)], $stackPos-(2-1), $self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            485 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\ErrorSuppress($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            486 => null,
            487 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\ShellExec($self->semStack[$stackPos-(3-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            488 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Print_($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            489 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Yield_(null, null, $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            490 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Yield_($self->semStack[$stackPos-(2-2)], null, $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            491 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Yield_($self->semStack[$stackPos-(4-4)], $self->semStack[$stackPos-(4-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            492 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\YieldFrom($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            493 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Throw_($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            494 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\ArrowFunction(['static' => false, 'byRef' => $self->semStack[$stackPos-(8-2)], 'params' => $self->semStack[$stackPos-(8-4)], 'returnType' => $self->semStack[$stackPos-(8-6)], 'expr' => $self->semStack[$stackPos-(8-8)], 'attrGroups' => []], $self->getAttributes($self->tokenStartStack[$stackPos-(8-1)], $self->tokenEndStack[$stackPos]));
            },
            495 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\ArrowFunction(['static' => true, 'byRef' => $self->semStack[$stackPos-(9-3)], 'params' => $self->semStack[$stackPos-(9-5)], 'returnType' => $self->semStack[$stackPos-(9-7)], 'expr' => $self->semStack[$stackPos-(9-9)], 'attrGroups' => []], $self->getAttributes($self->tokenStartStack[$stackPos-(9-1)], $self->tokenEndStack[$stackPos]));
            },
            496 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Closure(['static' => false, 'byRef' => $self->semStack[$stackPos-(8-2)], 'params' => $self->semStack[$stackPos-(8-4)], 'uses' => $self->semStack[$stackPos-(8-6)], 'returnType' => $self->semStack[$stackPos-(8-7)], 'stmts' => $self->semStack[$stackPos-(8-8)], 'attrGroups' => []], $self->getAttributes($self->tokenStartStack[$stackPos-(8-1)], $self->tokenEndStack[$stackPos]));
            },
            497 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Closure(['static' => true, 'byRef' => $self->semStack[$stackPos-(9-3)], 'params' => $self->semStack[$stackPos-(9-5)], 'uses' => $self->semStack[$stackPos-(9-7)], 'returnType' => $self->semStack[$stackPos-(9-8)], 'stmts' => $self->semStack[$stackPos-(9-9)], 'attrGroups' => []], $self->getAttributes($self->tokenStartStack[$stackPos-(9-1)], $self->tokenEndStack[$stackPos]));
            },
            498 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\ArrowFunction(['static' => false, 'byRef' => $self->semStack[$stackPos-(9-3)], 'params' => $self->semStack[$stackPos-(9-5)], 'returnType' => $self->semStack[$stackPos-(9-7)], 'expr' => $self->semStack[$stackPos-(9-9)], 'attrGroups' => $self->semStack[$stackPos-(9-1)]], $self->getAttributes($self->tokenStartStack[$stackPos-(9-1)], $self->tokenEndStack[$stackPos]));
            },
            499 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\ArrowFunction(['static' => true, 'byRef' => $self->semStack[$stackPos-(10-4)], 'params' => $self->semStack[$stackPos-(10-6)], 'returnType' => $self->semStack[$stackPos-(10-8)], 'expr' => $self->semStack[$stackPos-(10-10)], 'attrGroups' => $self->semStack[$stackPos-(10-1)]], $self->getAttributes($self->tokenStartStack[$stackPos-(10-1)], $self->tokenEndStack[$stackPos]));
            },
            500 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Closure(['static' => false, 'byRef' => $self->semStack[$stackPos-(9-3)], 'params' => $self->semStack[$stackPos-(9-5)], 'uses' => $self->semStack[$stackPos-(9-7)], 'returnType' => $self->semStack[$stackPos-(9-8)], 'stmts' => $self->semStack[$stackPos-(9-9)], 'attrGroups' => $self->semStack[$stackPos-(9-1)]], $self->getAttributes($self->tokenStartStack[$stackPos-(9-1)], $self->tokenEndStack[$stackPos]));
            },
            501 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Closure(['static' => true, 'byRef' => $self->semStack[$stackPos-(10-4)], 'params' => $self->semStack[$stackPos-(10-6)], 'uses' => $self->semStack[$stackPos-(10-8)], 'returnType' => $self->semStack[$stackPos-(10-9)], 'stmts' => $self->semStack[$stackPos-(10-10)], 'attrGroups' => $self->semStack[$stackPos-(10-1)]], $self->getAttributes($self->tokenStartStack[$stackPos-(10-1)], $self->tokenEndStack[$stackPos]));
            },
            502 => static function ($self, $stackPos) {
                 $self->semValue = array(new Stmt\Class_(null, ['type' => $self->semStack[$stackPos-(8-2)], 'extends' => $self->semStack[$stackPos-(8-4)], 'implements' => $self->semStack[$stackPos-(8-5)], 'stmts' => $self->semStack[$stackPos-(8-7)], 'attrGroups' => $self->semStack[$stackPos-(8-1)]], $self->getAttributes($self->tokenStartStack[$stackPos-(8-1)], $self->tokenEndStack[$stackPos])), $self->semStack[$stackPos-(8-3)]);
            $self->checkClass($self->semValue[0], -1);
            },
            503 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\New_($self->semStack[$stackPos-(3-2)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            504 => static function ($self, $stackPos) {
                 list($class, $ctorArgs) = $self->semStack[$stackPos-(2-2)]; $self->semValue = new Expr\New_($class, $ctorArgs, $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            505 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\New_($self->semStack[$stackPos-(2-2)], [], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            506 => null,
            507 => null,
            508 => static function ($self, $stackPos) {
                 $self->semValue = array();
            },
            509 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(4-3)];
            },
            510 => null,
            511 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            512 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            513 => static function ($self, $stackPos) {
                 $self->semValue = new Node\ClosureUse($self->semStack[$stackPos-(2-2)], $self->semStack[$stackPos-(2-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            514 => static function ($self, $stackPos) {
                 $self->semValue = new Name($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            515 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\FuncCall($self->semStack[$stackPos-(2-1)], $self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            516 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\FuncCall($self->semStack[$stackPos-(2-1)], $self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            517 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\FuncCall($self->semStack[$stackPos-(2-1)], $self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            518 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\StaticCall($self->semStack[$stackPos-(4-1)], $self->semStack[$stackPos-(4-3)], $self->semStack[$stackPos-(4-4)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            519 => static function ($self, $stackPos) {
                 $self->semValue = new Name($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            520 => null,
            521 => static function ($self, $stackPos) {
                 $self->semValue = new Name($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            522 => static function ($self, $stackPos) {
                 $self->semValue = new Name($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            523 => static function ($self, $stackPos) {
                 $self->semValue = new Name\FullyQualified(substr($self->semStack[$stackPos-(1-1)], 1), $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            524 => static function ($self, $stackPos) {
                 $self->semValue = new Name\Relative(substr($self->semStack[$stackPos-(1-1)], 10), $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            525 => null,
            526 => null,
            527 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(3-2)];
            },
            528 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Error($self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos])); $self->errorState = 2;
            },
            529 => null,
            530 => null,
            531 => static function ($self, $stackPos) {
                 $self->semValue = array();
            },
            532 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]); foreach ($self->semValue as $s) { if ($s instanceof Node\InterpolatedStringPart) { $s->value = Node\Scalar\String_::parseEscapeSequences($s->value, '`', $self->phpVersion->supportsUnicodeEscapes()); } };
            },
            533 => static function ($self, $stackPos) {
                 foreach ($self->semStack[$stackPos-(1-1)] as $s) { if ($s instanceof Node\InterpolatedStringPart) { $s->value = Node\Scalar\String_::parseEscapeSequences($s->value, '`', $self->phpVersion->supportsUnicodeEscapes()); } }; $self->semValue = $self->semStack[$stackPos-(1-1)];
            },
            534 => static function ($self, $stackPos) {
                 $self->semValue = array();
            },
            535 => null,
            536 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\ConstFetch($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            537 => static function ($self, $stackPos) {
                 $self->semValue = new Scalar\MagicConst\Line($self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            538 => static function ($self, $stackPos) {
                 $self->semValue = new Scalar\MagicConst\File($self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            539 => static function ($self, $stackPos) {
                 $self->semValue = new Scalar\MagicConst\Dir($self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            540 => static function ($self, $stackPos) {
                 $self->semValue = new Scalar\MagicConst\Class_($self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            541 => static function ($self, $stackPos) {
                 $self->semValue = new Scalar\MagicConst\Trait_($self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            542 => static function ($self, $stackPos) {
                 $self->semValue = new Scalar\MagicConst\Method($self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            543 => static function ($self, $stackPos) {
                 $self->semValue = new Scalar\MagicConst\Function_($self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            544 => static function ($self, $stackPos) {
                 $self->semValue = new Scalar\MagicConst\Namespace_($self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            545 => static function ($self, $stackPos) {
                 $self->semValue = new Scalar\MagicConst\Property($self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            546 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\ClassConstFetch($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            547 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\ClassConstFetch($self->semStack[$stackPos-(5-1)], $self->semStack[$stackPos-(5-4)], $self->getAttributes($self->tokenStartStack[$stackPos-(5-1)], $self->tokenEndStack[$stackPos]));
            },
            548 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\ClassConstFetch($self->semStack[$stackPos-(3-1)], new Expr\Error($self->getAttributes($self->tokenStartStack[$stackPos-(3-3)],  $self->tokenEndStack[$stackPos-(3-3)])), $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos])); $self->errorState = 2;
            },
            549 => static function ($self, $stackPos) {
                 $attrs = $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]); $attrs['kind'] = Expr\Array_::KIND_SHORT;
            $self->semValue = new Expr\Array_($self->semStack[$stackPos-(3-2)], $attrs);
            },
            550 => static function ($self, $stackPos) {
                 $attrs = $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]); $attrs['kind'] = Expr\Array_::KIND_LONG;
            $self->semValue = new Expr\Array_($self->semStack[$stackPos-(4-3)], $attrs);
            $self->createdArrays->attach($self->semValue);
            },
            551 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(1-1)]; $self->createdArrays->attach($self->semValue);
            },
            552 => static function ($self, $stackPos) {
                 $self->semValue = Scalar\String_::fromString($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]), $self->phpVersion->supportsUnicodeEscapes());
            },
            553 => static function ($self, $stackPos) {
                 $attrs = $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]); $attrs['kind'] = Scalar\String_::KIND_DOUBLE_QUOTED;
            foreach ($self->semStack[$stackPos-(3-2)] as $s) { if ($s instanceof Node\InterpolatedStringPart) { $s->value = Node\Scalar\String_::parseEscapeSequences($s->value, '"', $self->phpVersion->supportsUnicodeEscapes()); } }; $self->semValue = new Scalar\InterpolatedString($self->semStack[$stackPos-(3-2)], $attrs);
            },
            554 => static function ($self, $stackPos) {
                 $self->semValue = $self->parseLNumber($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]), $self->phpVersion->allowsInvalidOctals());
            },
            555 => static function ($self, $stackPos) {
                 $self->semValue = Scalar\Float_::fromString($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            556 => null,
            557 => null,
            558 => null,
            559 => static function ($self, $stackPos) {
                 $self->semValue = $self->parseDocString($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-2)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]), $self->getAttributes($self->tokenStartStack[$stackPos-(3-3)],  $self->tokenEndStack[$stackPos-(3-3)]), true);
            },
            560 => static function ($self, $stackPos) {
                 $self->semValue = $self->parseDocString($self->semStack[$stackPos-(2-1)], '', $self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]), $self->getAttributes($self->tokenStartStack[$stackPos-(2-2)],  $self->tokenEndStack[$stackPos-(2-2)]), true);
            },
            561 => static function ($self, $stackPos) {
                 $self->semValue = $self->parseDocString($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-2)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]), $self->getAttributes($self->tokenStartStack[$stackPos-(3-3)],  $self->tokenEndStack[$stackPos-(3-3)]), true);
            },
            562 => static function ($self, $stackPos) {
                 $self->semValue = null;
            },
            563 => null,
            564 => null,
            565 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(3-2)];
            },
            566 => null,
            567 => null,
            568 => null,
            569 => null,
            570 => null,
            571 => null,
            572 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(3-2)];
            },
            573 => null,
            574 => null,
            575 => null,
            576 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\ArrayDimFetch($self->semStack[$stackPos-(4-1)], $self->semStack[$stackPos-(4-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            577 => null,
            578 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\MethodCall($self->semStack[$stackPos-(4-1)], $self->semStack[$stackPos-(4-3)], $self->semStack[$stackPos-(4-4)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            579 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\NullsafeMethodCall($self->semStack[$stackPos-(4-1)], $self->semStack[$stackPos-(4-3)], $self->semStack[$stackPos-(4-4)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            580 => static function ($self, $stackPos) {
                 $self->semValue = null;
            },
            581 => null,
            582 => null,
            583 => null,
            584 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\PropertyFetch($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            585 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\NullsafePropertyFetch($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            586 => null,
            587 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Variable($self->semStack[$stackPos-(4-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            588 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Variable($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            589 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Variable(new Expr\Error($self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos])), $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos])); $self->errorState = 2;
            },
            590 => static function ($self, $stackPos) {
                 $var = $self->semStack[$stackPos-(1-1)]->name; $self->semValue = \is_string($var) ? new Node\VarLikeIdentifier($var, $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos])) : $var;
            },
            591 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\StaticPropertyFetch($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            592 => null,
            593 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\ArrayDimFetch($self->semStack[$stackPos-(4-1)], $self->semStack[$stackPos-(4-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            594 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\PropertyFetch($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            595 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\NullsafePropertyFetch($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            596 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\StaticPropertyFetch($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            597 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\StaticPropertyFetch($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            598 => null,
            599 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(3-2)];
            },
            600 => null,
            601 => null,
            602 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(3-2)];
            },
            603 => null,
            604 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Error($self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos])); $self->errorState = 2;
            },
            605 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\List_($self->semStack[$stackPos-(4-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos])); $self->semValue->setAttribute('kind', Expr\List_::KIND_LIST);
            $self->postprocessList($self->semValue);
            },
            606 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(1-1)]; $end = count($self->semValue)-1; if ($self->semValue[$end]->value instanceof Expr\Error) array_pop($self->semValue);
            },
            607 => null,
            608 => static function ($self, $stackPos) {
                 /* do nothing -- prevent default action of $$=$self->semStack[$1]. See $551. */
            },
            609 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            610 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            611 => static function ($self, $stackPos) {
                 $self->semValue = new Node\ArrayItem($self->semStack[$stackPos-(1-1)], null, false, $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            612 => static function ($self, $stackPos) {
                 $self->semValue = new Node\ArrayItem($self->semStack[$stackPos-(2-2)], null, true, $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            613 => static function ($self, $stackPos) {
                 $self->semValue = new Node\ArrayItem($self->semStack[$stackPos-(1-1)], null, false, $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            614 => static function ($self, $stackPos) {
                 $self->semValue = new Node\ArrayItem($self->semStack[$stackPos-(3-3)], $self->semStack[$stackPos-(3-1)], false, $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            615 => static function ($self, $stackPos) {
                 $self->semValue = new Node\ArrayItem($self->semStack[$stackPos-(4-4)], $self->semStack[$stackPos-(4-1)], true, $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            616 => static function ($self, $stackPos) {
                 $self->semValue = new Node\ArrayItem($self->semStack[$stackPos-(3-3)], $self->semStack[$stackPos-(3-1)], false, $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            617 => static function ($self, $stackPos) {
                 $self->semValue = new Node\ArrayItem($self->semStack[$stackPos-(2-2)], null, false, $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]), true);
            },
            618 => static function ($self, $stackPos) {
                 /* Create an Error node now to remember the position. We'll later either report an error,
             or convert this into a null element, depending on whether this is a creation or destructuring context. */
          $attrs = $self->createEmptyElemAttributes($self->tokenPos);
          $self->semValue = new Node\ArrayItem(new Expr\Error($attrs), null, false, $attrs);
            },
            619 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(2-1)][] = $self->semStack[$stackPos-(2-2)]; $self->semValue = $self->semStack[$stackPos-(2-1)];
            },
            620 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(2-1)][] = $self->semStack[$stackPos-(2-2)]; $self->semValue = $self->semStack[$stackPos-(2-1)];
            },
            621 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            622 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(2-1)], $self->semStack[$stackPos-(2-2)]);
            },
            623 => static function ($self, $stackPos) {
                 $attrs = $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]); $attrs['rawValue'] = $self->semStack[$stackPos-(1-1)]; $self->semValue = new Node\InterpolatedStringPart($self->semStack[$stackPos-(1-1)], $attrs);
            },
            624 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Variable($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            625 => null,
            626 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\ArrayDimFetch($self->semStack[$stackPos-(4-1)], $self->semStack[$stackPos-(4-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            627 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\PropertyFetch($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            628 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\NullsafePropertyFetch($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            629 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Variable($self->semStack[$stackPos-(3-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            630 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Variable($self->semStack[$stackPos-(3-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            631 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\ArrayDimFetch($self->semStack[$stackPos-(6-2)], $self->semStack[$stackPos-(6-4)], $self->getAttributes($self->tokenStartStack[$stackPos-(6-1)], $self->tokenEndStack[$stackPos]));
            },
            632 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(3-2)];
            },
            633 => static function ($self, $stackPos) {
                 $self->semValue = new Scalar\String_($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            634 => static function ($self, $stackPos) {
                 $self->semValue = $self->parseNumString($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            635 => static function ($self, $stackPos) {
                 $self->semValue = $self->parseNumString('-' . $self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            636 => null,
        ];
    }
}
