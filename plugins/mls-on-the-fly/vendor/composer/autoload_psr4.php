<?php

// autoload_psr4.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'Symfony\\Polyfill\\Mbstring\\' => array($vendorDir . '/symfony/polyfill-mbstring'),
    'Symfony\\Component\\VarDumper\\' => array($vendorDir . '/symfony/var-dumper'),
    'Realtyna\\Test\\' => array($vendorDir . '/realtyna/wp-plugin-framework/tests'),
    'Realtyna\\OData\\' => array($vendorDir . '/realtyna/odata-php-client/src'),
    'Realtyna\\MlsOnTheFly\\' => array($baseDir . '/src'),
    'Realtyna\\Dev\\' => array($baseDir . '/dev'),
    'Realtyna\\Core\\' => array($vendorDir . '/realtyna/wp-plugin-framework/src'),
    'Php<PERSON>arser\\' => array($vendorDir . '/nikic/php-parser/lib/PhpParser'),
    'Doctrine\\Instantiator\\' => array($vendorDir . '/doctrine/instantiator/src/Doctrine/Instantiator'),
    'DeepCopy\\' => array($vendorDir . '/myclabs/deep-copy/src/DeepCopy'),
);
