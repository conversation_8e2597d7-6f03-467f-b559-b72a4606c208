{"name": "phpunit/php-code-coverage", "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "type": "library", "keywords": ["coverage", "testing", "xunit"], "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "license": "BSD-3-<PERSON><PERSON>", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy"}, "config": {"platform": {"php": "7.3.0"}, "optimize-autoloader": true, "sort-packages": true}, "prefer-stable": true, "require": {"php": ">=7.3", "ext-dom": "*", "ext-libxml": "*", "ext-xmlwriter": "*", "nikic/php-parser": "^4.19.1 || ^5.1.0", "phpunit/php-file-iterator": "^3.0.6", "phpunit/php-text-template": "^2.0.4", "sebastian/code-unit-reverse-lookup": "^2.0.3", "sebastian/complexity": "^2.0.3", "sebastian/environment": "^5.1.5", "sebastian/lines-of-code": "^1.0.4", "sebastian/version": "^3.0.2", "theseer/tokenizer": "^1.2.3"}, "require-dev": {"phpunit/phpunit": "^9.6"}, "suggest": {"ext-pcov": "PHP extension that provides line coverage", "ext-xdebug": "PHP extension that provides line coverage as well as branch and path coverage"}, "autoload": {"classmap": ["src/"]}, "autoload-dev": {"files": ["tests/TestCase.php", "tests/_files/BankAccountTest.php"]}, "extra": {"branch-alias": {"dev-main": "9.2.x-dev"}}}