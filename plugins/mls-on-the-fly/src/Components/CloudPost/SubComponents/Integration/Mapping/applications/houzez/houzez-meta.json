{"fave_currency": {"mapping": "USD"}, "fave_property_price": {"mapping": "{ListPrice}"}, "fave_property_sec_price": {"mapping": "{PreviousListPrice}"}, "fave_property_price_prefix": {"mapping": ""}, "fave_property_price_postfix": {"mapping": ""}, "fave_property_size": {"mapping": "{BuildingAreaTotal}"}, "fave_property_size_prefix": {"mapping": "Sqft"}, "fave_living_size": {"mapping": "{LivingArea}"}, "fave_living_size_prefix": {"mapping": "{LivingAreaUnits}"}, "fave_property_land": {"mapping": "{LotSizeAcres}"}, "fave_property_land_postfix": {"mapping": "<PERSON><PERSON>s"}, "fave_property_bedrooms": {"mapping": "{BedroomsTotal}"}, "fave_property_rooms": {"mapping": "{RoomsTotal}"}, "fave_property_bathrooms": {"mapping": "{BathroomsTotalInteger}"}, "fave_property_garage": {"mapping": "{GarageSpaces}"}, "fave_property_garage_size": {"mapping": "{GarageWidth} x {GarageLength} x {GarageHeight}"}, "fave_property_year": {"mapping": "{YearBuilt}"}, "fave_property_id": {"mapping": "{ListingId}"}, "fave_property_map": {"mapping": ""}, "fave_property_location": {"mapping": "IF('{Latitude}' != '','{Latitude},{Longitude}', '')"}, "houzez_geolocation_lat": {"mapping": "IF('{Latitude}' != '','{Latitude}', '')"}, "houzez_geolocation_long": {"mapping": "IF('{Longitude}' != '','{Longitude}', '')"}, "fave_featured": {"mapping": ""}, "fave_loggedintoview": {"mapping": ""}, "fave_property_map_address": {"mapping": "{StreetNumber} {StreetDirPrefix} {StreetName} {StreetDirSuffix} {StreetSuffix}, {City}, {StateOrProvince} {PostalCode}"}, "fave_property_address": {"mapping": "{StreetNumber} {StreetDirPrefix} {StreetName} {StreetDirSuffix} {StreetSuffix} "}, "fave_property_zip": {"mapping": "{PostalCode}"}, "fave_property_country": {"mapping": "US"}, "property_country": {"default": "United States"}, "fave_mls_id": {"mapping": "{ListingId}"}, "fave_office_name": {"mapping": "{ListOfficeName}"}, "fave_agent_name": {"mapping": "{ListAgentFullName}"}, "fave_mls_status": {"mapping": "{MlsStatus}"}, "property_type": {"mapping": "{PropertySubType}"}, "property_status": {"mapping": "{StandardStatus}"}, "property_state": {"mapping": "{StateOrProvince}"}, "property_city": {"mapping": "{City}"}, "property_area": {"mapping": "{SubdivisionName}"}, "fave_additional_feature_title": {"default": "Listing Terms|Association Fee|School District|Roof|Utilities|Sewer|Cooling|Heating|Flooring|County|Property Type|Pool|Parking|Elementary School|Middle School|High School|Community Features|Waterfront|Architectural Style"}, "fave_additional_feature_value": {"mapping": "{ListingTerms}|{AssociationFee}|{SchoolDistrict}|{Roof}|{Utilities}|{Sewer}|{Cooling}|{Heating}|{Flooring}|{CountyOrParish}|{PropertyType}|{PoolFeatures}|{ParkingFeatures}|{ElementarySchool}|{MiddleOrJuniorSchool}|{HighSchool}|{CommunityFeatures}|{WaterfrontFeatures}|{ArchitecturalStyle}"}, "fave_video_url": {"mapping": "{VirtualTourURLBranded}"}, "fave_virtual_tour": {"mapping": "IF('{VirtualTourURLUnbranded}' != '', '<iframe src={VirtualTourURLUnbranded}></iframe>', '')"}, "additional_features": {"mapping": ""}, "property_exterior_features": {"mapping": "{ExteriorFeatures}"}, "property_interior_features": {"mapping": "{InteriorFeatures}"}, "property_appliances": {"mapping": "{Appliances}"}, "fave_mlsid": {"mapping": "{ListingId}"}}