{"key_mappings": {"date": "ModificationTimestamp", "listing_posted": "ModificationTimestamp", "fave_property_id": "ListingId", "fave_property_price": "ListPrice", "price": "ListPrice", "fave_property_unique_id": "ListingKey", "fave_property_bedrooms": "BedroomsTotal", "fave_property_bathrooms": "BathroomsTotalInteger", "fave_property_carport": "CarportSpaces", "fave_property_garage": "GarageSpaces", "fave_property_status": "StandardStatus", "fave_property_address": "UnparsedAddress", "fave_agents": "ListAgentMlsId"}, "post_metas": {"fave_property_id": {"method": "where", "rf_field": "ListingId", "compare": "eq", "value": false, "boolean": "or"}, "fave_property_unique_id": {"method": "where", "rf_field": "ListingKey", "compare": "eq", "value": false, "boolean": "or"}, "fave_property_zip": {"method": "where", "rf_field": "PostalCode", "compare": "eq", "value": false, "boolean": "or"}, "fave_property_price": {"method": "where", "rf_field": "ListPrice", "value": false}, "fave_property_bedrooms": {"method": "where", "rf_field": "BedroomsTotal", "value": false}, "fave_property_bathrooms": {"method": "where", "rf_field": "BathroomsTotalInteger", "value": false}, "fave_property_carport": {"method": "where", "rf_field": "CarportSpaces", "value": false}, "fave_property_garage": {"method": "where", "rf_field": "GarageSpaces", "value": false}, "fave_property_status": {"method": "whereIn", "rf_field": "StandardStatus", "value": false}, "fave_property_size": {"method": "where", "rf_field": "BuildingAreaTotal", "value": false}, "fave_property_address": {"method": "contains", "rf_field": "UnparsedAddress", "value": false}, "property_state": {"method": "where", "rf_field": "StateOrProvince", "value": false}, "property_city": {"method": "where", "rf_field": "City", "value": false}, "fave_agents": {"method": "where", "rf_field": "ListAgentMlsId", "value": false}, "fave_property_agency": {"method": "where", "rf_field": "ListOfficeMlsId", "value": false}}, "post_types": {"fave_property": {"rf_field": "PropertyType", "method": "contains", "value": "Residential", "boolean": "or"}, "rental": {"rf_field": "PropertyType", "method": "contains", "value": "Lease", "boolean": "or"}, "land": {"rf_field": "PropertyType", "method": "contains", "value": "Land", "boolean": "or"}, "commercial": {"rf_field": "PropertyType", "method": "contains", "value": "Commercial", "boolean": "or"}, "rural": {"rf_field": "PropertyType", "method": "contains", "value": "Farm", "boolean": "or"}, "business": {"rf_field": "PropertyType", "method": "contains", "value": "BusinessOpportunity", "boolean": "or"}}, "taxonomies": {"location": {"mapping": "City"}, "property_feature": {"mapping": ["ExteriorFeatures", "InteriorFeatures", "Appliances"], "method": "contains", "separator": ","}, "property_type": {"mapping": "PropertyType", "child": "PropertySubType"}, "property-type": {"mapping": "PropertyType"}, "property-state": {"mapping": "StateOrProvince"}, "property-city": {"mapping": "City"}, "property_state": {"mapping": "StateOrProvince"}, "property_city": {"mapping": "City"}, "property_status": {"mapping": "StandardStatus"}, "property-status": {"mapping": "StandardStatus"}, "property_country": {"mapping": "Country"}, "property_area": {"mapping": "SubdivisionName"}, "property_label": {"mapping": "PropertyCondition"}}}