<?php
/**
 * Placements Types Buddypress.
 *
 * @package AdvancedAds\Pro
 * <AUTHOR> Ads <<EMAIL>>
 * @since   2.26.0
 */

namespace AdvancedAds\Pro\Placements\Types;

use AdvancedAds\Interfaces\Placement_Type;
use AdvancedAds\Abstracts\Placement_Type as Base;
use AdvancedAds\Pro\Modules\BuddyPress\BuddyPress as BuddyPress_Main;
use AdvancedAds\Pro\Placements\Placement_Buddypress;

defined( 'ABSPATH' ) || exit;

/**
 * Buddypress.
 */
class Buddypress extends Base implements Placement_Type {

	/**
	 * Get the unique identifier (ID) of the placement type.
	 *
	 * @return string The unique ID of the placement type.
	 */
	public function get_id(): string {
		return 'buddypress';
	}

	/**
	 * Get the class name of the object as a string.
	 *
	 * @return string
	 */
	public function get_classname(): string {
		return Placement_Buddypress::class;
	}

	/**
	 * Get the title or name of the placement type.
	 *
	 * @return string The title of the placement type.
	 */
	public function get_title(): string {
		if ( BuddyPress_Main::is_buddyboss() ) {
			return __( 'BuddyBoss Content', 'advanced-ads-pro' );
		}
		return __( 'BuddyPress Content', 'advanced-ads-pro' );
	}

	/**
	 * Get a description of the placement type.
	 *
	 * @return string The description of the placement type.
	 */
	public function get_description(): string {
		if ( BuddyPress_Main::is_buddyboss() ) {
			return __( 'Display ads on BuddyBoss related pages.', 'advanced-ads-pro' );
		}
		return __( 'Display ads on BuddyPress related pages.', 'advanced-ads-pro' );
	}

	/**
	 * Check if this placement type requires premium.
	 *
	 * @return bool True if premium is required; otherwise, false.
	 */
	public function is_premium(): bool {
		return false;
	}

	/**
	 * Get the URL for upgrading to this placement type.
	 *
	 * @return string The upgrade URL for the placement type.
	 */
	public function get_image(): string {
		if ( BuddyPress_Main::is_buddyboss() ) {
			return AA_PRO_BASE_URL . 'modules/buddypress/assets/img/buddyboss.png';
		}
		return AA_PRO_BASE_URL . 'modules/buddypress/assets/img/buddypress-icon.png';
	}

	/**
	 * Get order number for this placement type.
	 *
	 * @return int The order number.
	 */
	public function get_order(): int {
		return 31;
	}

	/**
	 * Get options for this placement type.
	 *
	 * @return array The options array.
	 */
	public function get_options(): array {
		return $this->apply_filter_on_options( [ 'placement-display-conditions' => [ 'request_uri', 'buddypress_group' ] ] );
	}
}
