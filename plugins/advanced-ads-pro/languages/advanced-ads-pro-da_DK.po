msgid ""
msgstr ""
"Project-Id-Version: Advanced Ads Pro\n"
"Report-Msgid-Bugs-To: \n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2025-03-25T23:54:42+00:00\n"
"PO-Revision-Date: 2025-02-22 10:13:19+0000\n"
"Language: da_DK\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/4.0.1\n"

#: views/admin/placements/bulk-edit.php:21
msgid "Off"
msgstr ""

#: views/admin/placements/bulk-edit.php:18
#: views/admin/placements/bulk-edit.php:29
#: views/admin/placements/bulk-edit.php:39
msgid "No Change"
msgstr ""

#: modules/cache-busting/cache-busting.class.php:253
#: views/admin/placements/bulk-edit.php:37
msgid "Hide when empty"
msgstr ""

#: views/admin/placements/bulk-edit.php:31
#: views/admin/placements/bulk-edit.php:41
msgid "Disabled"
msgstr ""

#: views/admin/placements/bulk-edit.php:19
msgid "Auto"
msgstr ""

#. Author of the plugin
#: advanced-ads-pro.php
msgid "Advanced Ads"
msgstr ""

#. translators: %s: Plugin name
#: includes/class-bootstrap.php:155
msgid "Your versions of the Advanced Ads addons listed below are incompatible with <strong>Advanced Ads %s</strong> and have been deactivated. Please update the plugin to the latest version."
msgstr ""

#. translators: %s: Plugin name
#: includes/class-bootstrap.php:109
msgid "Your version of <strong>Advanced Ads - Pro</strong> is incompatible with <strong>Advanced Ads %s</strong> and has been deactivated. Please update the plugin to the latest version."
msgstr ""

#. translators: 1: is a link to a support document. 2: closing link
#: includes/class-autoloader.php:113
msgid "Your installation of Advanced Ads is incomplete. If you installed Advanced Ads from GitHub, %1$s please refer to this document%2$s to set up your development environment."
msgstr ""

#: modules/geo/admin/views/setting-download.php:22
msgid "You are currently using filter hooks to load custom database files."
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:17
msgid "When to show overlay again?"
msgstr ""

#: modules/placement_conditions/admin.class.php:70
msgid "Visitor Conditions"
msgstr ""

#: modules/ads-by-hours/src/class-admin.php:135
msgid "viewer time zone"
msgstr ""

#: modules/advanced-visitor-conditions/main.class.php:218
msgid "User IP Address"
msgstr ""

#: modules/ads-by-hours/views/publish-metabox-inputs.php:54
msgid "To"
msgstr ""

#. translators: 1 opening link tag. 2 closing tag
#: modules/cache-busting/views/settings.php:53
msgid "This option does not work with the current active theme and %1$sblock themes%2$s in general."
msgstr ""

#. translators: %s: dashicon class
#: modules/lazy-load/views/settings.php:22
msgid "This module requires: <br> <span class=\"dashicons %s\"></span> Cache Busting"
msgstr ""

#: modules/ads-for-adblockers/views/settings.php:25
msgid "This module requires:"
msgstr ""

#: views/admin/tables/ads/adsense-fallback.php:47
#: views/admin/tables/ads/adsense-fallback.php:66
msgid "The selected item will be displayed when an AdSense ad is unavailable, ensuring your ad space remains filled."
msgstr ""

#: includes/class-bootstrap.php:180
msgid "The following addons are affected:"
msgstr ""

#. translators: group or ad title.
#: views/admin/tables/ads/adsense-fallback.php:58
msgid "The default fallback is \"%s\". You can change this in the AdSense settings."
msgstr ""

#: views/admin/tables/ads/adsense-fallback.php:75
msgid "The AdSense fallback feature requires the ad to be assigned to a placement with enabled Cache Busting."
msgstr ""

#. translators: %s: WP_ENVIRONMENT_TYPES
#: includes/class-autoloader.php:157
msgid "The %s constant is no longer supported."
msgstr ""

#: modules/weekdays/class-advanced-ads-pro-weekdays.php:177
msgid "Shows up"
msgstr ""

#: modules/ads-by-hours/src/class-admin.php:79
msgid "Showing ads depending on the visitor's time requires enabling Cache Busting."
msgstr ""

#: modules/extended-adblocker/views/setting-activate-extended.php:26
msgid "Show a custom overlay to users with an ad blocker enabled, prompting them to turn it off on your website."
msgstr ""

#: modules/ads-for-adblockers/admin.class.php:89
#: modules/lazy-load/admin.class.php:58
msgid "Settings"
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:20
msgid "Set the timing for the overlay to reappear after being dismissed."
msgstr ""

#: modules/ads-by-hours/views/publish-metabox-inputs.php:21
msgid "Set specific hours"
msgstr ""

#: modules/cache-busting/cache-busting.class.php:257
#: views/admin/placements/bulk-edit.php:45
msgid "Remove the placeholder if unfilled."
msgstr ""

#: modules/extended-adblocker/views/setting-activate-extended.php:64
msgid "Redirect URL"
msgstr ""

#: modules/extended-adblocker/views/setting-activate-extended.php:37
msgid "Redirect"
msgstr ""

#: modules/lazy-load/admin.class.php:68
msgid "Prevent ads from getting loaded before they appear in the visitor’s visible area."
msgstr ""

#: modules/extended-adblocker/views/setting-activate-extended.php:22
msgid "Overlay"
msgstr ""

#: views/admin/tables/ads/adsense-fallback.php:27
msgid "None"
msgstr ""

#: views/admin/tables/ads/adsense-fallback.php:52
msgid "No default fallback ad selected. Choose one in the AdSense settings."
msgstr ""

#: modules/extended-adblocker/views/setting-activate-extended.php:17
msgid "No additional actions"
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:43
msgid "Never"
msgstr ""

#: modules/extended-adblocker/views/setting-styling.php:19
msgid "Insert CSS to customize the overlay container layout."
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:84
msgid "Insert CSS to customize the dismiss button layout."
msgstr ""

#: modules/extended-adblocker/views/setting-styling.php:36
msgid "Insert CSS to customize the background of the overlay."
msgstr ""

#. translators: %s: index input field
#: modules/bbpress/class-admin.php:106
msgid "Inject after %s post"
msgstr ""

#: includes/class-bootstrap.php:129
#: includes/class-bootstrap.php:152
msgid "Important Notice"
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:55
msgid "Hide dismiss button"
msgstr ""

#: modules/ads-for-adblockers/views/placement-item.php:18
msgid "Groups"
msgstr ""

#. translators: %s: first field, %s: last field
#: modules/advanced-display-conditions/main.class.php:571
msgid "from %1$s to %2$s"
msgstr ""

#: modules/ads-by-hours/views/publish-metabox-inputs.php:35
msgid "From"
msgstr ""

#: includes/admin/class-adsense.php:38
msgid "Fallback ad/group"
msgstr ""

#: views/admin/tables/ads/adsense-fallback.php:20
msgid "Fallback"
msgstr ""

#: modules/extended-adblocker/views/setting-exclude.php:13
msgid "Exclude"
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:28
msgid "Everytime"
msgstr ""

#. translators: 1: The download URL, 2: The HTTP error code, 3: The HTTP header
#. title, 4: The response body
#: modules/geo/classes/admin.php:545
msgid "Error downloading database from: %1$s - %2$s %3$s - %4$s"
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:68
msgid "Enter the text that you want to appear on the dismiss button."
msgstr ""

#: modules/extended-adblocker/views/setting-activate-extended.php:68
msgid "Enter a specific page on your domain to which users with activated AdBlocker should be automatically redirected."
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:77
#: modules/extended-adblocker/views/setting-dismissible.php:94
#: modules/extended-adblocker/views/setting-styling.php:29
#: modules/extended-adblocker/views/setting-styling.php:46
msgid "Empty and save to revert to defaults."
msgstr ""

#: modules/advanced-visitor-conditions/main.class.php:211
msgid "Display or hide ad when user use adblocker."
msgstr ""

#: includes/admin/class-ad-list-table.php:42
#: includes/admin/class-ad-list-table.php:86
msgid "Display Once"
msgstr ""

#: modules/placement_conditions/admin.class.php:52
msgid "Display Conditions"
msgstr ""

#: modules/advanced-visitor-conditions/main.class.php:219
msgid "Display ads based on the user IP address. Enter one IP address per line."
msgstr ""

#: modules/buddypress/class-buddypress.php:324
msgid "Display ads based on existing BuddyPress groups."
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:65
msgid "Dismiss button text"
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:81
msgid "Dismiss button styling"
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:48
msgid "Dismiss button"
msgstr ""

#: modules/extended-adblocker/main.class.php:83
#: modules/extended-adblocker/views/setting-dismissible.php:75
msgid "Dismiss"
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:58
msgid "Disabling the dismiss button significantly limits site interaction."
msgstr ""

#: modules/cache-busting/cache-busting.class.php:261
msgid "Deleting an empty placement might lead to a layout shift."
msgstr ""

#: views/admin/tables/ads/adsense-fallback.php:25
msgid "Default"
msgstr ""

#: includes/admin/class-duplicate-placement.php:101
msgid "Create a copy of this placement"
msgstr ""

#: includes/admin/class-group-duplication.php:77
msgid "Create a copy of this group"
msgstr ""

#: modules/extended-adblocker/views/setting-overlay-content.php:11
msgid "Content"
msgstr ""

#: modules/extended-adblocker/views/setting-styling.php:16
msgid "Container styling"
msgstr ""

#: modules/extended-adblocker/views/setting-exclude.php:16
msgid "Choose which user roles to exclude from this ad blocker countermeasure."
msgstr ""

#: modules/lazy-load/admin.class.php:56
msgid "Cache Busting needs to be enabled"
msgstr ""

#: modules/ads-for-adblockers/admin.class.php:87
msgid "Cache Busting and Ad blocker disguise need to be enabled"
msgstr ""

#: modules/buddypress/class-buddypress.php:323
msgid "BuddyPress group"
msgstr ""

#: modules/extended-adblocker/views/setting-styling.php:33
msgid "Background styling"
msgstr ""

#: modules/extended-adblocker/views/setting-activate-extended.php:41
msgid "Automatically redirect users with ad blockers enabled to an internal page. Content access is granted after turning off the ad blocker."
msgstr ""

#: includes/class-bootstrap.php:71
#: includes/class-bootstrap.php:78
msgid "Advanced Ads - Pro"
msgstr ""

#. translators: ad title.
#: includes/class-adsense.php:52
msgid "AdSense fallback was loaded for empty AdSense ad \"%s\""
msgstr ""

#: includes/class-bootstrap.php:130
msgid "Addons listed below requires the <strong><a href=\"https://wpadvancedads.com/?utm_source=advanced-ads&utm_medium=link&utm_campaign=activate-advanced-ads-pro\" target=\"_blank\">Advanced Ads</a></strong> plugin to be installed and activated on your site."
msgstr ""

#: modules/advanced-visitor-conditions/main.class.php:210
msgid "Adblocker"
msgstr ""

#: views/admin/tables/ads/adsense-fallback.php:29
msgid "Ad groups"
msgstr ""

#: modules/extended-adblocker/admin.class.php:49
msgid "Ad blocker countermeasures"
msgstr ""

#: modules/ads-for-adblockers/views/settings.php:29
msgid "Ad block disguise"
msgstr ""

#: modules/extended-adblocker/views/setting-activate-extended.php:51
msgid "Activate the ad blocker disguise above to display the overlay."
msgstr ""

#: modules/ads-by-hours/src/class-admin.php:70
msgid "A cache plugin has been detected. It is recommended to enable Cache Busting and check the visitor's time when displaying the ad on the frontend."
msgstr ""

#: includes/class-bootstrap.php:95
msgid "<strong>Advanced Ads - Pro</strong> requires the <strong>Advanced Ads free</strong> plugin to be installed and activated on your site."
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:37
msgid "1 week"
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:40
msgid "1 month"
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:31
msgid "1 hour"
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:34
msgid "1 day"
msgstr ""

#: modules/ads-for-adblockers/views/placement-item.php:15
msgid "--not selected--"
msgstr ""

#. translators: comma separated list of days.
#: modules/weekdays/class-advanced-ads-pro-weekdays.php:181
msgid " on: %s"
msgstr ""

#. translators: 1. localized time 2. localized time 3. timezone name.
#: modules/weekdays/class-advanced-ads-pro-weekdays.php:187
msgid " between %1$s and %2$s %3$s"
msgstr ""

#: modules/responsive-ads/views/setting_reload_ads.php:19
msgid "You need to enable cache-busting in order to use this feature."
msgstr "Du skal aktivere cache-busting for at bruge denne funktion."

#. translators: 1 is the opening link to the Advanced Ads website, 2 the
#. closing link
#: classes/advanced-ads-pro.php:338
msgid "We have renamed the Responsive Ads add-on to ‘Advanced Ads AMP Ads’. With this change, the Browser Width visitor condition moved from that add-on into Advanced Ads Pro. You can deactivate ‘Advanced Ads AMP Ads’ if you don’t utilize AMP ads or the custom sizes feature for responsive AdSense ad units. %1$sRead more%2$s."
msgstr "Vi har omdøbt Responsive Ads-tilføjelsen til 'Advanced Ads AMP Ads'. Med denne ændring flyttede browserbredden besøgendes betingelse fra denne tilføjelse til Advanced Ads Pro. Du kan deaktivere 'Avancerede AMP-annoncer', hvis du ikke bruger AMP-annoncer eller funktionen tilpassede størrelser til responsive AdSense-annonceenheder. %1$sLæs mere%2$s."

#: modules/responsive-ads/includes/class-admin.php:45
msgid "Responsive Image Ads"
msgstr "Responsive billedannoncer"

#: modules/responsive-ads/includes/class-admin.php:38
msgid "Responsive Ads"
msgstr "Responsive Ads"

#: modules/responsive-ads/views/setting_reload_ads.php:16
msgid "Reload ads when the screen resizes."
msgstr "Genindlæs annoncer, når skærmen ændrer størrelse."

#: modules/responsive-ads/includes/class-admin.php:53
msgid "Reload ads on resize"
msgstr "Genindlæs annoncer ved ændring af størrelse"

#: modules/responsive-ads/includes/class-admin.php:61
msgid "Fallback width"
msgstr "Fallback bredde"

#: modules/responsive-ads/includes/class-common.php:40
msgid "Display ads based on the browser width."
msgstr "Vis annoncer baseret på browserens bredde."

#: modules/cache-busting/admin-ui.class.php:51
msgid "Couldn't find the placement."
msgstr "Kunne ikke finde placeringen."

#: modules/responsive-ads/views/setting_responsive_images.php:15
msgid "Check this option if the size of image ads is not adjusted responsively by your theme."
msgstr "Marker denne mulighed, hvis størrelsen på billedannoncer ikke justeres responsivt af dit tema."

#: modules/cache-busting/admin-ui.class.php:48
msgid "Cache busting has been successfully enabled for the assigned placement."
msgstr "Cachebusting er blevet aktiveret for den tildelte placering."

#: modules/responsive-ads/includes/class-common.php:39
msgid "browser width"
msgstr "browser bredde"

#: modules/cache-busting/admin-ui.class.php:88
#: modules/cache-busting/views/settings.php:75
msgid "off"
msgstr "slukket"

#: modules/cache-busting/views/settings.php:18
msgid "Choose which method to use when a placement needs cache busting and the option is set to “auto”."
msgstr "Vælg, hvilken metode der skal bruges, når en placering har brug for cache-busting, og indstillingen er sat til \"auto\"."

#: modules/cache-busting/admin-ui.class.php:86
msgid "auto"
msgstr "auto"

#: modules/cache-busting/admin-ui.class.php:39
#: modules/geo/classes/admin.php:435
msgid "You are not allowed to do this."
msgstr "Du har ikke tilladelse til dette."

#: modules/geo/classes/admin.php:447
msgid "Please provide a MaxMind license key"
msgstr "Angiv venligst en MaxMind licensnøgle"

#. translators: Link to parallax manual
#: modules/parallax-ads/views/placement-options-after.php:24
msgid "Optimized for image ads. %s"
msgstr "Optimeret for billedannoncer. %s"

#: modules/parallax-ads/views/placement-options-after.php:19
msgid "Enable the parallax effect."
msgstr "Aktiver parallakse-effekten."

#: modules/rest-api/classes/Admin_UI.php:33
msgid "REST API"
msgstr "REST API"

#: modules/parallax-ads/classes/Advanced_Ads_Pro_Module_Parallax_Admin_UI.php:71
msgid "Parallax Ads"
msgstr "Parallax Ads"

#: modules/parallax-ads/views/placement-options-after.php:37
msgctxt "Parallax Ad placement height unit"
msgid "Unit"
msgstr "Enhed"

#: modules/parallax-ads/views/placement-options-after.php:31
msgctxt "Parallax Ad placement height"
msgid "Height"
msgstr "Højde"

#: modules/rest-api/classes/Advanced_Ads_Group.php:27
msgid "Invalid group ID."
msgstr "Ugyldigt gruppe ID."

#: modules/parallax-ads/views/placement-options-after.php:40
msgid "Choose the height of the cutout, either in pixels or relative to the viewport."
msgstr "Vælg højden på udskæringen, enten i pixels eller i forhold til visningsporten."

#: modules/geo/admin/views/visitor-profile.php:20
msgid "Reset Visitor Profile"
msgstr "Nulstil besøgsprofil"

#: modules/geo/admin/views/visitor-profile.php:9
msgid "Location based on your visitor profile cookie:"
msgstr "Placering baseret på din besøgendes profil-cookie:"

#: modules/geo/classes/admin.php:405
msgid "Location based on your IP address"
msgstr "Placering baseret på din IP addresse"

#: modules/advanced-visitor-conditions/main.class.php:203
msgid "Display or hide ads for new visitors."
msgstr "Vis eller skjul annoncer for nye besøgende."

#: modules/advanced-visitor-conditions/main.class.php:181
msgid "Display ads based on the value of a cookie. Set the operator to “matches/does not match” and leave the value empty to check only the existence of the cookie."
msgstr "Vis annoncer baseret på værdien af en cookie. Indstil operatøren til \"matcher/matcher ikke\" og lad værdien være tom for kun at kontrollere eksistensen af cookien."

#: modules/advanced-display-conditions/main.class.php:88
msgid "Display ads based on the page language set by the WPML plugin."
msgstr "Vis annoncer baseret på sidesproget, der er indstillet af WPML-pluginnet."

#: modules/buddypress/class-buddypress.php:335
#: modules/buddypress/class-buddypress.php:376
msgid "Display ads based on existing BuddyBoss groups."
msgstr "Vis annoncer baseret på eksisterende BuddyBoss-grupper."

#: modules/geo/classes/admin.php:367
msgid "miles (mi)"
msgstr "miles (mi)"

#: modules/geo/classes/admin.php:726
msgid "There was an error connecting to the search service."
msgstr "Der opstod en fejl under forbindelse til søgetjenesten."

#: modules/geo/classes/admin.php:725
msgid "Your search did not return any results."
msgstr "Din søgning gav ikke nogen resultater."

#. translators: 1: A link to the geo location service.
#: modules/geo/classes/admin.php:728
msgid "You can search for the geo coordinates manually at %1$s."
msgstr "Du kan søge efter geokoordinaterne manuelt på %1$s."

#. translators: 1: The number of search results.
#: modules/geo/classes/admin.php:724
msgid "Found %1$d results. Please pick the one, you want to use."
msgstr "Fandt %1$d resultater. Vælg venligst den, du vil bruge."

#: modules/geo/classes/admin.php:366
msgid "kilometers (km)"
msgstr "kilometer (km)"

#. translators: 1 is the opening link to the plugins page, 2 the closing link
#: modules/geo/classes/Advanced_Ads_Geo_Version_Check.php:51
msgid "The geo-targeting visitor condition moved into Advanced Ads Pro. You can remove Geo Targeting %1$shere%2$s."
msgstr "Betingelsen for geografisk målretning af besøgende blev flyttet ind i Advanced Ads Pro. Du kan fjerne Geo Targeting %1$shere%2$s."

#: modules/geo/classes/api.php:389
msgid "Zimbabwe"
msgstr "Zimbabwe"

#: modules/geo/classes/api.php:388
msgid "Zambia"
msgstr "Zambia"

#: modules/geo/classes/admin.php:245
msgid "Your IP address format is incorrect"
msgstr "Dit IP-adresseformat er forkert"

#. translators: %s is a URL.
#: modules/cache-busting/admin-ui.class.php:196
msgid "You need a placement to deliver this group using cache busting. <a href=\"%s\" target=\"_blank\">Create a placement now.</a>"
msgstr "Du har brug for en placering for at levere denne gruppe ved hjælp af cache-busting. <a href=\"%s\" target=\"_blank\">Opret en placering nu.</a>"

#: modules/geo/classes/api.php:387
msgid "Yemen"
msgstr "Yemen"

#: modules/geo/classes/api.php:386
msgid "Western Samoa"
msgstr "Vestlige Samoa"

#: modules/geo/classes/api.php:385
msgid "Western Sahara"
msgstr "Vestlige Sahara"

#: modules/geo/classes/api.php:384
msgid "Wallis and Futuna Islands"
msgstr "Wallis og Futuna Islands"

#: modules/geo/classes/api.php:383
msgid "Virgin Islands (USA)"
msgstr "Virgin Islands (USA)"

#: modules/geo/classes/api.php:382
msgid "Virgin Islands (British)"
msgstr "Virgin Islands (British)"

#: modules/geo/classes/api.php:381
msgid "Vietnam"
msgstr "Vietnam"

#: modules/geo/classes/api.php:380
msgid "Venezuela"
msgstr "Venezuela"

#: modules/geo/classes/api.php:379
msgid "Vanuatu"
msgstr "Vanuatu"

#: modules/geo/classes/api.php:378
msgid "Uzbekistan"
msgstr "Uzbekistan"

#: modules/geo/classes/api.php:377
msgid "US Minor Outlying Islands"
msgstr "US mindre afsidesliggende øer"

#: modules/geo/classes/api.php:376
msgid "Uruguay"
msgstr "Uruguay"

#: modules/geo/admin/views/setting-download.php:57
msgid "Update geo location databases"
msgstr "Opdater geo placerings-databaser"

#: modules/geo/classes/api.php:129
msgid "United States"
msgstr "Forenede Stater"

#: modules/geo/classes/api.php:130
msgid "United Kingdom"
msgstr "Storbritannien"

#: modules/geo/classes/api.php:375
msgid "United Arab Emirates"
msgstr "Forenede Arabiske Emirater"

#: modules/geo/classes/api.php:374
msgid "Ukraine"
msgstr "Ukraine"

#: modules/geo/classes/api.php:373
msgid "Uganda"
msgstr "Uganda"

#: modules/geo/classes/api.php:372
msgid "Tuvalu"
msgstr "Tuvalu"

#: modules/geo/classes/api.php:371
msgid "Turks and Caicos Islands"
msgstr "Turks og Caicos Islands"

#: modules/geo/classes/api.php:370
msgid "Turkmenistan"
msgstr "Turkmenistan"

#: modules/geo/classes/api.php:369
msgid "Turkey"
msgstr "Tyrkiet"

#: modules/geo/classes/api.php:368
msgid "Tunisia"
msgstr "Tunesien"

#: modules/geo/classes/api.php:367
msgid "Trinidad and Tobago"
msgstr "Trinidad og Tobago"

#: modules/geo/classes/api.php:366
msgid "Tonga"
msgstr "Tonga"

#: modules/geo/classes/api.php:365
msgid "Tokelau"
msgstr "Tokelau"

#: modules/geo/classes/api.php:364
msgid "Togo"
msgstr "Togo"

#: modules/geo/classes/api.php:363
msgid "Timor-Leste"
msgstr "Timor-Leste"

#: modules/geo/classes/admin.php:351
msgid "This option can’t be used if your site is using sucuri.net."
msgstr "Denne indstilling kan ikke bruges, hvis dit websted bruger sucuri.net."

#: modules/geo/classes/admin.php:441
msgid "The upload dir is not available"
msgstr "Upload placeringen er ikke tilgængelig"

#: modules/geo/admin/views/setting-download.php:31
msgid "The MaxMind license key is missing."
msgstr "MaxMind-licensnøglen mangler."

#: modules/geo/admin/views/setting-download.php:91
msgid "The databases are updated on the first Tuesday (midnight, GMT) of each month."
msgstr "Databaserne bliver opdateret den første tirsdag (midnat, GMT) i hver måned."

#: modules/geo/classes/api.php:362
msgid "Thailand"
msgstr "Thailand"

#: modules/geo/classes/api.php:361
msgid "Tanzania"
msgstr "Tanzania"

#: modules/geo/classes/api.php:360
msgid "Tajikistan"
msgstr "Tadsjikistan"

#: modules/geo/classes/api.php:359
msgid "Taiwan"
msgstr "Taiwan"

#: modules/geo/classes/api.php:358
msgid "Syrian Arab Republic"
msgstr "Syriske Arabiske Republik"

#: modules/geo/classes/api.php:357
msgid "Switzerland"
msgstr "Schweiz"

#: modules/geo/classes/api.php:356
msgid "Sweden"
msgstr "Sverige"

#: modules/geo/classes/api.php:355
msgid "Swaziland"
msgstr "Swaziland"

#: modules/geo/classes/api.php:354
msgid "Svalbard and Jan Mayen Islands"
msgstr "Svalbard og Jan Mayen Øerne"

#: modules/geo/classes/api.php:353
msgid "Suriname"
msgstr "Surinam"

#: modules/geo/classes/api.php:352
msgid "Sudan"
msgstr "Sudan"

#: modules/geo/classes/plugin.php:130
msgid "Sucuri Header (country only)"
msgstr "Sucuri Header (kun land)"

#: modules/geo/classes/admin.php:333
msgid "State/Region"
msgstr "Kommune/Region"

#: modules/geo/classes/api.php:351
msgid "Sri Lanka"
msgstr "Sri Lanka"

#: modules/geo/classes/api.php:350
msgid "Spain"
msgstr "Spanien"

#: modules/geo/classes/api.php:349
msgid "South Sudan"
msgstr "Syd Sudan"

#: modules/geo/classes/api.php:348
msgid "South Korea"
msgstr "Sydkorea"

#: modules/geo/classes/api.php:347
msgid "South Georgia"
msgstr "Syd Georgia"

#: modules/geo/classes/api.php:135
msgid "South America"
msgstr "Sydamerika"

#: modules/geo/classes/api.php:346
msgid "South Africa"
msgstr "Sydafrika"

#: modules/geo/classes/api.php:345
msgid "Somalia"
msgstr "Somalia"

#: modules/geo/classes/api.php:344
msgid "Solomon Islands"
msgstr "Salomon Øerne"

#: modules/geo/classes/api.php:343
msgid "Slovenia"
msgstr "Slovenien"

#: modules/geo/classes/api.php:342
msgid "Slovak Republic"
msgstr "Slovakiske Republik"

#: modules/geo/classes/api.php:341
msgid "Singapore"
msgstr "Singapore"

#: modules/geo/classes/api.php:340
msgid "Sierra Leone"
msgstr "Sierra Leone"

#: modules/geo/classes/api.php:339
msgid "Seychelles"
msgstr "Seychelles"

#: modules/geo/classes/api.php:338
msgid "Serbia"
msgstr "Serbien"

#: modules/geo/classes/api.php:337
msgid "Senegal"
msgstr "Senegal"

#: modules/geo/classes/admin.php:390
msgid "Search"
msgstr "Søg"

#: modules/geo/classes/api.php:336
msgid "Saudi Arabia"
msgstr "Saudi Arabien"

#: modules/geo/classes/api.php:334
msgid "San Marino"
msgstr "San Marino"

#: modules/geo/classes/api.php:333
msgid "Saint Vincent and the Grenadines"
msgstr "St. Vincent og Grenadinerne"

#: modules/geo/classes/api.php:332
msgid "Saint Pierre and Miquelon"
msgstr "Saint Pierre og Miquelon"

#: modules/geo/classes/api.php:330
msgid "Saint Martin (French)"
msgstr "St. Martin (fransk)"

#: modules/geo/classes/api.php:331
msgid "Saint Martin (Dutch)"
msgstr "Sankt Martin (Hollandsk)"

#: modules/geo/classes/api.php:329
msgid "Saint Lucia"
msgstr "St. Lucia"

#: modules/geo/classes/api.php:328
msgid "Saint Kitts and Nevis"
msgstr "Saint Kitts og Nevis"

#: modules/geo/classes/api.php:327
msgid "Saint Helena"
msgstr "Sankt Helena"

#: modules/geo/classes/api.php:326
msgid "Saint Barth&eacute;lemy"
msgstr "Saint Barth&eacute;lemy"

#: modules/geo/classes/api.php:335
msgid "S&atilde;o Tom&eacute; and Pr&iacute;ncipe"
msgstr "S&atilde;o Tom&eacute; and Pr&iacute;ncipe"

#: modules/geo/classes/api.php:325
msgid "Rwanda"
msgstr "Rwanda"

#: modules/geo/classes/api.php:324
msgid "Russian Federation"
msgstr "Russiske Føderation"

#: modules/geo/classes/api.php:323
msgid "Romania"
msgstr "Rumænien"

#: modules/geo/classes/api.php:322
msgid "Reunion Island"
msgstr "Reunion Island"

#: modules/geo/classes/api.php:321
msgid "Republic of Kosovo"
msgstr "Kosovo-republikken"

#: modules/geo/classes/api.php:320
msgid "Qatar"
msgstr "Qatar"

#: modules/geo/classes/api.php:319
msgid "Puerto Rico"
msgstr "Puerto Rico"

#: modules/geo/classes/api.php:318
msgid "Portugal"
msgstr "Portugal"

#: modules/geo/classes/api.php:317
msgid "Poland"
msgstr "Polen"

#. translators: 1: opening <a>-tag to Advanced Ads manual, 2: closing <a>-tag
#: modules/geo/admin/views/setting-download.php:36
#: modules/geo/classes/admin.php:234
msgid "Please read the %1$sinstallation instructions%2$s."
msgstr "Læs venligst %1$sinstallationsinstruktionerne%2$s."

#: modules/geo/classes/api.php:316
msgid "Pitcairn Island"
msgstr "Pitcairn Island"

#: modules/geo/classes/api.php:315
msgid "Phillipines"
msgstr "Filippinerne"

#: modules/geo/classes/api.php:314
msgid "Peru"
msgstr "Peru"

#: modules/geo/classes/api.php:313
msgid "Paraguay"
msgstr "Paraguay"

#: modules/geo/classes/api.php:312
msgid "Papua New Guinea"
msgstr "Papua New Guinea"

#: modules/geo/classes/api.php:311
msgid "Panama"
msgstr "Panama"

#: modules/geo/classes/api.php:310
msgid "Palestinian Territories"
msgstr "Palæstinensiske områder"

#: modules/geo/classes/api.php:309
msgid "Palau"
msgstr "Palau"

#: modules/geo/classes/api.php:308
msgid "Pakistan"
msgstr "Pakistan"

#: modules/geo/classes/admin.php:332
#: modules/geo/classes/admin.php:334
msgid "or"
msgstr "eller"

#: modules/geo/classes/api.php:307
msgid "Oman"
msgstr "Oman"

#: modules/geo/classes/api.php:139
msgid "Oceania"
msgstr "Oceania"

#: modules/geo/classes/api.php:306
msgid "Norway"
msgstr "Norge"

#: modules/geo/classes/api.php:305
msgid "Northern Mariana Islands"
msgstr "Northern Mariana Islands"

#: modules/geo/classes/api.php:304
msgid "North Korea"
msgstr "Nordkorea"

#: modules/geo/classes/api.php:134
msgid "North America"
msgstr "Nordamerika"

#: modules/geo/classes/api.php:303
msgid "Norfolk Island"
msgstr "Norfolk Island"

#: modules/geo/classes/api.php:302
msgid "Niue"
msgstr "Niue"

#: modules/geo/classes/api.php:301
msgid "Nigeria"
msgstr "Nigeria"

#: modules/geo/classes/api.php:300
msgid "Niger"
msgstr "Niger"

#: modules/geo/classes/api.php:299
msgid "Nicaragua"
msgstr "Nicaragua"

#. translators: Timestamp in the localized date_format
#: modules/geo/admin/views/setting-download.php:84
msgid "Next possible update on %s."
msgstr "Næste mulige opdatering på %s."

#: modules/geo/classes/api.php:298
msgid "New Zealand"
msgstr "New Zealand"

#: modules/geo/classes/api.php:297
msgid "New Caledonia"
msgstr "New Caledonia"

#: modules/geo/classes/api.php:296
msgid "Netherlands Antilles"
msgstr "Hollandske Antiller"

#: modules/geo/classes/api.php:295
msgid "Netherlands"
msgstr "Holland"

#: modules/geo/classes/api.php:294
msgid "Nepal"
msgstr "Nepal"

#: modules/geo/classes/api.php:293
msgid "Nauru"
msgstr "Nauru"

#: modules/geo/classes/api.php:292
msgid "Namibia"
msgstr "Namibia"

#: modules/geo/classes/api.php:291
msgid "Myanmar"
msgstr "Myanmar"

#: modules/geo/classes/api.php:290
msgid "Mozambique"
msgstr "Mozambique"

#: modules/geo/classes/api.php:289
msgid "Morocco"
msgstr "Marokko"

#: modules/geo/classes/api.php:288
msgid "Montserrat"
msgstr "Montserrat"

#: modules/geo/classes/api.php:287
msgid "Montenegro"
msgstr "Montenegro"

#: modules/geo/classes/api.php:286
msgid "Mongolia"
msgstr "Mongoliet"

#: modules/geo/classes/api.php:285
msgid "Monaco"
msgstr "Monaco"

#: modules/geo/classes/api.php:284
msgid "Moldova, Republic of"
msgstr "Moldova, Republikken"

#: modules/geo/classes/api.php:283
msgid "Micronesia"
msgstr "Mikronesien"

#: modules/geo/classes/api.php:282
msgid "Mexico"
msgstr "Mexico"

#: modules/geo/classes/admin.php:76
msgid "Method"
msgstr "Metode"

#: modules/geo/classes/api.php:281
msgid "Mayotte"
msgstr "Mayotte"

#: modules/geo/classes/admin.php:241
msgid "Maybe you are working on a local or secured environment."
msgstr "Måske arbejder du på et lokalt eller sikkert miljø."

#: modules/geo/classes/admin.php:57
msgid "MaxMind license key"
msgstr "MaxMind licensnøgle"

#: modules/geo/classes/plugin.php:124
msgid "MaxMind database (default)"
msgstr "MaxMind database (standard)"

#: modules/geo/classes/admin.php:66
msgid "MaxMind Database"
msgstr "MaxMind database"

#: modules/geo/classes/api.php:280
msgid "Mauritius"
msgstr "Mauritius"

#: modules/geo/classes/api.php:279
msgid "Mauritania"
msgstr "Mauretanien"

#: modules/geo/classes/api.php:278
msgid "Martinique"
msgstr "Martinique"

#: modules/geo/classes/api.php:277
msgid "Marshall Islands"
msgstr "Marshall Islands"

#: modules/geo/classes/api.php:276
msgid "Malta"
msgstr "Malta"

#: modules/geo/classes/api.php:275
msgid "Mali"
msgstr "Mali"

#: modules/geo/classes/api.php:274
msgid "Maldives"
msgstr "Maldiverne"

#: modules/geo/classes/api.php:273
msgid "Malaysia"
msgstr "Malaysia"

#: modules/geo/classes/api.php:272
msgid "Malawi"
msgstr "Malawi"

#: modules/geo/classes/api.php:271
msgid "Madagascar"
msgstr "Madagaskar"

#: modules/geo/classes/api.php:270
msgid "Macedonia"
msgstr "Makedonien"

#: modules/geo/classes/api.php:269
msgid "Macau"
msgstr "Macau"

#: modules/geo/classes/api.php:268
msgid "Luxembourg"
msgstr "Luxembourg"

#: modules/geo/classes/admin.php:373
msgid "Longitude"
msgstr "Længdegrad"

#: modules/geo/classes/api.php:267
msgid "Lithuania"
msgstr "Litauen"

#: modules/geo/classes/api.php:266
msgid "Liechtenstein"
msgstr "Liechtenstein"

#: modules/geo/classes/api.php:265
msgid "Libyan Arab Jamahiriya"
msgstr "Libyens arabiske Jamahiriya"

#: modules/geo/classes/api.php:264
msgid "Liberia"
msgstr "Liberia"

#: modules/geo/classes/api.php:263
msgid "Lesotho"
msgstr "Lesotho"

#: modules/geo/classes/api.php:262
msgid "Lebanon"
msgstr "Libanon"

#: modules/geo/classes/api.php:261
msgid "Latvia"
msgstr "Letland"

#: modules/geo/classes/admin.php:372
msgid "Latitude"
msgstr "Breddegrad"

#. translators: Timestamp in the localized date_format
#: modules/geo/admin/views/setting-download.php:73
msgid "Last update: %s"
msgstr "Sidst opdateret: %s"

#: modules/geo/classes/api.php:260
msgid "Lao People's Democratic Republic"
msgstr "Laos Demokratiske Folkerepublik"

#: modules/geo/classes/admin.php:86
msgid "Language of names"
msgstr "Sprog for navne"

#: modules/geo/classes/api.php:259
msgid "Kyrgyzstan"
msgstr "Kirgisistan"

#: modules/geo/classes/api.php:258
msgid "Kuwait"
msgstr "Kuwait"

#: modules/geo/classes/api.php:257
msgid "Kiribati"
msgstr "Kiribati"

#: modules/geo/classes/api.php:256
msgid "Kenya"
msgstr "Kenya"

#: modules/geo/classes/api.php:255
msgid "Kazakhstan"
msgstr "Kasakhstan"

#: modules/geo/classes/api.php:254
msgid "Jordan"
msgstr "Jordan"

#: modules/geo/classes/api.php:253
msgid "Jersey"
msgstr "Jersey"

#: modules/geo/classes/api.php:252
msgid "Japan"
msgstr "Japan"

#: modules/geo/classes/api.php:251
msgid "Jamaica"
msgstr "Jamaica"

#: modules/geo/classes/api.php:250
msgid "Italy"
msgstr "Italien"

#. translators: %s is a URL.
#: modules/cache-busting/admin-ui.class.php:218
msgid "It seems that a caching plugin is activated. Your ads might not rotate properly, while cache busting is disabled for the placement your group is using. <a href=\"#\" data-placement=\"%s\" class=\"js-placement-activate-cb\">Activate cache busting for this placement.</a>"
msgstr ""

#. translators: %s is a URL.
#: modules/cache-busting/admin-ui.class.php:173
msgid "It seems that a caching plugin is activated. Your ads might not rotate properly while cache busting is disabled. <a href=\"%s\" target=\"_blank\">Activate cache busting.</a>"
msgstr "Det lader til, at et caching-plugin er aktiveret. Dine annoncer roterer muligvis ikke ordentligt, mens cache-busting er deaktiveret. <a href=\"%s\" target=\"_blank\">Aktivér cache-busting.</a>"

#: modules/geo/classes/api.php:249
msgid "Israel"
msgstr "Israel"

#: modules/geo/classes/api.php:248
msgid "Isle of Man"
msgstr "Isle of Man"

#: modules/geo/classes/api.php:247
msgid "Ireland"
msgstr "Irland"

#: modules/geo/classes/api.php:246
msgid "Iraq"
msgstr "Irak"

#: modules/geo/classes/api.php:245
msgid "Iran"
msgstr "Iran"

#: modules/geo/classes/api.php:244
msgid "Indonesia"
msgstr "Indonesien"

#: modules/geo/classes/api.php:243
msgid "India"
msgstr "Indien"

#: modules/geo/admin/views/setting-download.php:50
msgid "In order to use Geo Targeting, please download the geo location databases by clicking on the button below."
msgstr "For at bruge Geo Targeting, venligst download databaserne for geografisk placering ved at klikke på knappen nedenfor."

#: modules/click-fraud-protection/click-fraud-protection.class.php:156
msgid "Ignore global Click Fraud Protection"
msgstr "Ignorer global beskyttelse mod kliksvindel"

#: modules/geo/classes/api.php:242
msgid "Iceland"
msgstr "Island"

#: modules/geo/classes/api.php:241
msgid "Hungary"
msgstr "Ungarn"

#: modules/geo/classes/api.php:240
msgid "Hong Kong"
msgstr "Hong Kong"

#: modules/geo/classes/api.php:239
msgid "Honduras"
msgstr "Honduras"

#: modules/geo/classes/api.php:238
msgid "Holy See (City Vatican State)"
msgstr "Holy See (Vatikanstaten)"

#: modules/geo/classes/api.php:237
msgid "Heard and McDonald Islands"
msgstr "Heard og McDonald Islands"

#: modules/geo/classes/api.php:236
msgid "Haiti"
msgstr "Haiti"

#: modules/geo/classes/api.php:235
msgid "Guyana"
msgstr "Guyana"

#: modules/geo/classes/api.php:234
msgid "Guinea-Bissau"
msgstr "Guinea-Bissau"

#: modules/geo/classes/api.php:233
msgid "Guinea"
msgstr "Guinea"

#: modules/geo/classes/api.php:232
msgid "Guernsey"
msgstr "Guernsey"

#: modules/geo/classes/api.php:231
msgid "Guatemala"
msgstr "Guatemala"

#: modules/geo/classes/api.php:230
msgid "Guam"
msgstr "Guam"

#: modules/geo/classes/api.php:229
msgid "Guadeloupe"
msgstr "Guadeloupe"

#: modules/geo/classes/api.php:228
msgid "Grenada"
msgstr "Grenada"

#: modules/geo/classes/api.php:227
msgid "Greenland"
msgstr "Grønland"

#: modules/geo/classes/api.php:224
msgid "Greece"
msgstr "Grækenland"

#: modules/geo/classes/api.php:226
msgid "Gibraltar"
msgstr "Gibraltar"

#: modules/geo/classes/api.php:225
msgid "Ghana"
msgstr "Ghana"

#: modules/geo/classes/admin.php:370
msgid "get coordinates"
msgstr "få koordinater"

#: modules/geo/classes/api.php:132
msgid "Germany"
msgstr "Tyskland"

#: modules/geo/classes/api.php:223
msgid "Georgia"
msgstr "Georgien"

#: modules/geo/classes/admin.php:49
msgid "Geo Targeting"
msgstr "Geo-målretning"

#: modules/geo/classes/plugin.php:105
msgid "geo location"
msgstr "geografisk placering"

#: modules/geo/admin/views/setting-download.php:47
#: modules/geo/classes/admin.php:231
msgid "Geo Databases not found."
msgstr "Geo databaser ikke fundet."

#: modules/geo/classes/public.php:222
msgid "Geo Databases not found"
msgstr "Geo databaser ikke fundet"

#: modules/geo/admin/views/setting-download.php:16
msgid "Geo Databases found."
msgstr "Geo databaser fundet."

#: modules/geo/classes/api.php:222
msgid "Gambia"
msgstr "Gambia"

#: modules/geo/classes/api.php:221
msgid "Gabon"
msgstr "Gabon"

#: modules/geo/classes/admin.php:368
msgid "from"
msgstr "fra"

#: modules/geo/classes/api.php:220
msgid "French Southern Territories"
msgstr "Franske sydlige områder"

#: modules/geo/classes/api.php:219
msgid "French Polynesia"
msgstr "Fransk Polynesien"

#: modules/geo/classes/api.php:218
msgid "French Guiana"
msgstr "Fransk Guyana"

#: modules/geo/classes/api.php:217
msgid "France"
msgstr "Frankrig"

#: modules/geo/classes/api.php:216
msgid "Finland"
msgstr "Finland"

#: modules/geo/classes/api.php:215
msgid "Fiji"
msgstr "Fiji"

#: modules/geo/classes/api.php:214
msgid "Faroe Islands"
msgstr "Færøerne"

#: modules/geo/classes/api.php:213
msgid "Falkland Islands"
msgstr "Falklandsøerne"

#: modules/geo/classes/api.php:131
msgid "European Union"
msgstr "Den Europæiske Union"

#: modules/geo/classes/api.php:136
msgid "Europe"
msgstr "Europa"

#: modules/geo/classes/api.php:212
msgid "Ethiopia"
msgstr "Etiopien"

#: modules/geo/classes/api.php:211
msgid "Estonia"
msgstr "Estland"

#: modules/geo/classes/api.php:210
msgid "Eritrea"
msgstr "Eritrea"

#: modules/geo/classes/api.php:208
msgid "Equatorial Guinea"
msgstr "Ækvatorial Guinea"

#: modules/geo/classes/admin.php:393
msgid "Enter the name of the city, click the search button and pick one of the results to set the coordinates of the center."
msgstr "Indtast navnet på byen, klik på søgeknappen og vælg et af resultaterne for at indstille koordinaterne for centrum."

#: modules/geo/classes/api.php:209
msgid "El Salvador"
msgstr "El Salvador"

#: modules/geo/classes/api.php:207
msgid "Egypt"
msgstr "Egypten"

#: modules/geo/classes/api.php:206
msgid "Ecuador"
msgstr "Ecuador"

#: modules/geo/classes/api.php:205
msgid "East Timor"
msgstr "Øst Timor"

#: modules/geo/classes/api.php:204
msgid "Dominican Republic"
msgstr "Dominikanske republik"

#: modules/geo/classes/api.php:203
msgid "Dominica"
msgstr "Dominica"

#: modules/geo/classes/api.php:202
msgid "Djibouti"
msgstr "Djibouti"

#: modules/geo/classes/admin.php:378
msgid "Distance to center"
msgstr "Afstand til center"

#: modules/geo/classes/admin.php:361
msgid "Distance is less than or equal to"
msgstr "Afstanden er mindre eller lig med"

#: modules/geo/classes/admin.php:362
msgid "Distance is greater than"
msgstr "Afstanden er større end"

#: modules/geo/classes/admin.php:364
msgid "Distance"
msgstr "Afstand"

#: modules/geo/classes/plugin.php:106
msgid "Display ads based on geo location."
msgstr "Vis annoncer baseret på geografisk placering."

#: modules/geo/classes/api.php:201
msgid "Denmark"
msgstr "Danmark"

#: modules/geo/classes/admin.php:518
msgid "Database updated successfully!"
msgstr "Databasen blev opdateret med succes!"

#: modules/geo/classes/admin.php:536
msgid "Database update failed"
msgstr "Databaseopdatering mislykkedes"

#: modules/geo/classes/api.php:200
msgid "Czech Republic"
msgstr "Tjekkiet"

#: modules/geo/classes/api.php:199
msgid "Cyprus"
msgstr "Cypern"

#: modules/geo/classes/api.php:198
msgid "Cura&Ccedil;ao"
msgstr "Cura&Ccedil;ao"

#: modules/geo/classes/api.php:197
msgid "Cuba"
msgstr "Cuba"

#: modules/geo/classes/api.php:196
msgid "Croatia/Hrvatska"
msgstr "Kroatien/Hrvatska"

#. translators: an error mesage.
#: modules/geo/classes/admin.php:573
msgid "Could not open downloaded database for reading: %s"
msgstr "Kunne ikke åbne download-databasen til læsning: %s"

#. translators: MaxMind database file name.
#: modules/geo/classes/admin.php:602
msgid "Could not open database for writing %s"
msgstr "Kunne ikke åbne databasen for at skrive %s"

#: modules/geo/classes/admin.php:589
msgid "Could not access filesystem"
msgstr "Kunne ikke tilgå filsystemet"

#: modules/geo/classes/api.php:195
msgid "Cote d'Ivoire"
msgstr "Cote d'Ivoire"

#: modules/geo/classes/api.php:194
msgid "Costa Rica"
msgstr "Costa Rica"

#: modules/geo/admin/views/visitor-profile.php:15
#: modules/geo/classes/admin.php:252
msgid "Coordinates"
msgstr "Koordinater"

#: modules/geo/classes/api.php:193
msgid "Cook Islands"
msgstr "Cookøerne"

#: modules/geo/classes/api.php:192
msgid "Congo, Republic of"
msgstr "Congo, Republikken af"

#: modules/geo/classes/api.php:191
msgid "Congo, Democratic People's Republic"
msgstr "Congo, Den Demokratiske Folkerepublik"

#: modules/geo/classes/api.php:190
msgid "Comoros"
msgstr "Comorerne"

#: modules/geo/classes/api.php:189
msgid "Colombia"
msgstr "Colombia"

#: modules/geo/classes/api.php:188
msgid "Cocos Islands"
msgstr "Cocosøerne"

#: modules/geo/classes/admin.php:335
#: modules/geo/classes/admin.php:388
msgid "City"
msgstr "By"

#: modules/geo/classes/api.php:187
msgid "Christmas Island"
msgstr "Juleøen"

#: modules/geo/admin/views/setting-locale.php:15
msgid "Choose the language of the state/region or city entered. If the language is not available in the geo location database, it will check against the English version."
msgstr "Vælg sproget for den angivne stat/region eller by. Hvis sproget ikke er tilgængeligt i databasen med geografiske placeringer, tjekkes det mod den engelske version."

#: modules/geo/classes/api.php:186
msgid "China"
msgstr "Kina"

#: modules/geo/classes/api.php:185
msgid "Chile"
msgstr "Chile"

#: modules/geo/classes/api.php:184
msgid "Chad"
msgstr "Tchad"

#: modules/geo/classes/api.php:183
msgid "Central African Republic"
msgstr "Central Afrikanske Republik"

#: modules/geo/classes/api.php:182
msgid "Cayman Islands"
msgstr "Caymanøerne"

#: modules/geo/classes/api.php:181
msgid "Cape Verde"
msgstr "Kap Verde"

#: modules/geo/classes/admin.php:391
msgid "Cancel"
msgstr "Annullerer"

#: modules/geo/classes/api.php:180
msgid "Canada"
msgstr "Canada"

#: modules/geo/classes/api.php:179
msgid "Cameroon"
msgstr "Cameroun"

#: modules/geo/classes/api.php:178
msgid "Cambodia"
msgstr "Cambodja"

#: modules/geo/classes/admin.php:288
msgid "by specific location"
msgstr "efter bestemt sted"

#: modules/geo/classes/admin.php:294
msgid "by radius"
msgstr "efter radius"

#: modules/geo/classes/api.php:177
msgid "Burundi"
msgstr "Burundi"

#: modules/geo/classes/api.php:176
msgid "Burkina Faso"
msgstr "Burkina Faso"

#: modules/geo/classes/api.php:175
msgid "Bulgaria"
msgstr "Bulgarien"

#: modules/geo/classes/api.php:174
msgid "Brunei Darrussalam"
msgstr "Brunei Darussalam"

#: modules/geo/classes/api.php:173
msgid "British Indian Ocean Territory"
msgstr "Det britiske territorium i det Indiske Ocean"

#: modules/geo/classes/api.php:172
msgid "Brazil"
msgstr "Brasilien"

#: modules/geo/classes/api.php:171
msgid "Bouvet Island"
msgstr "Bouvetøen"

#: modules/geo/classes/api.php:170
msgid "Botswana"
msgstr "Botswana"

#: modules/geo/classes/api.php:169
msgid "Bosnia and Herzegovina"
msgstr "Bosnien-Hercegovina"

#: modules/geo/classes/api.php:168
msgid "Bonaire, Saint Eustatius and Saba"
msgstr "Bonaire, Saint Eustatius og Saba"

#: modules/geo/classes/api.php:167
msgid "Bolivia"
msgstr "Bolivia"

#: modules/geo/classes/api.php:166
msgid "Bhutan"
msgstr "Bhutan"

#: modules/geo/classes/api.php:165
msgid "Bermuda"
msgstr "Bermuda"

#: modules/geo/classes/api.php:164
msgid "Benin"
msgstr "Benin"

#: modules/geo/classes/api.php:163
msgid "Belize"
msgstr "Belize"

#: modules/geo/classes/api.php:162
msgid "Belgium"
msgstr "Belgien"

#: modules/geo/classes/api.php:161
msgid "Belarus"
msgstr "Hviderusland"

#: modules/geo/classes/api.php:160
msgid "Barbados"
msgstr "Barbados"

#: modules/geo/classes/api.php:159
msgid "Bangladesh"
msgstr "Bangladesh"

#: modules/geo/classes/api.php:158
msgid "Bahrain"
msgstr "Bahrain"

#: modules/geo/classes/api.php:157
msgid "Bahamas"
msgstr "Bahamas"

#: modules/geo/classes/api.php:156
msgid "Azerbaijan"
msgstr "Aserbajdsjan"

#: modules/geo/classes/api.php:155
msgid "Austria"
msgstr "Østrig"

#: modules/geo/classes/api.php:154
msgid "Australia"
msgstr "Australien"

#: modules/geo/classes/api.php:138
msgid "Asia"
msgstr "Asien"

#: modules/geo/classes/api.php:153
msgid "Aruba"
msgstr "Aruba"

#: modules/geo/classes/api.php:152
msgid "Armenia"
msgstr "Armenien"

#: modules/geo/classes/api.php:151
msgid "Argentina"
msgstr "Argentina"

#: modules/geo/classes/api.php:150
msgid "Antigua and Barbuda"
msgstr "Antigua og Barbuda"

#: modules/geo/classes/api.php:149
msgid "Antarctica"
msgstr "Antarktis"

#: modules/geo/classes/api.php:148
msgid "Anguilla"
msgstr "Anguilla"

#: modules/geo/classes/api.php:147
msgid "Angola"
msgstr "Angola"

#: modules/geo/classes/api.php:146
msgid "Andorra"
msgstr "Andorra"

#: modules/geo/classes/api.php:145
msgid "American Samoa"
msgstr "Amerikansk Samoa"

#: modules/geo/classes/api.php:144
msgid "Algeria"
msgstr "Algeriet"

#: modules/geo/classes/api.php:143
msgid "Albania"
msgstr "Albanien"

#: modules/geo/classes/api.php:137
msgid "Africa"
msgstr "Afrika"

#: modules/geo/classes/api.php:141
msgid "Afghanistan"
msgstr "Afghanistan"

#. translators: an error message.
#: modules/geo/classes/public.php:227
msgid "Address not found: %s"
msgstr "Adresse ikke fundet: %s"

#: modules/group-refresh/views/settings_group_refresh.php:21
#: views/admin/tables/ads/adsense-fallback.php:77
msgid "Activate now"
msgstr "Aktiver nu"

#: modules/geo/classes/admin.php:219
#: modules/geo/classes/public.php:208
#: modules/geo/classes/public.php:372
msgid "(unknown region)"
msgstr "(ukendt region)"

#: modules/geo/classes/admin.php:208
#: modules/geo/classes/public.php:200
#: modules/geo/classes/public.php:368
msgid "(unknown city)"
msgstr "(ukendt by)"

#: modules/geo/classes/api.php:142
msgid "&#197;land Islands"
msgstr "&#197;land øer"

#: modules/lazy-load/admin.class.php:23
#: modules/lazy-load/admin.class.php:64
#: views/admin/placements/bulk-edit.php:27
msgid "Lazy Loading"
msgstr "Lazy Loading"

#: views/setting-placement-positioning.php:29
msgid "This method also works on AMP pages and causes fewer conflicts with website optimization features. However, it can cause critical issues with a few other plugins that use a similar technique (i.e., output buffering). We recommend less technical users test it carefully."
msgstr "Denne metode fungerer også på AMP-sider og forårsager færre konflikter med funktioner til webstedsoptimering. Det kan dog forårsage kritiske problemer med nogle få andre plugins, der bruger en lignende teknik (dvs. outputbuffering). Vi anbefaler, at mindre tekniske brugere tester den omhyggeligt."

#: classes/advanced-ads-pro-admin.php:148
msgid "Placement positioning"
msgstr "Placerings-positionering"

#. translators: %s: Names of placement types
#: views/setting-placement-positioning.php:13
msgid "Choose when Advanced Ads will add the following placement types: %s"
msgstr "Vælg, hvornår Avancerede annoncer tilføjer følgende placeringstyper: %s"

#: views/setting-placement-positioning.php:24
msgid "Before page load using PHP."
msgstr "Før sideindlæsning med PHP."

#: views/setting-placement-positioning.php:19
msgid "After page load using JavaScript"
msgstr "Efter sideindlæsning med JavaScript"

#: modules/advanced-display-conditions/main.class.php:116
msgid "post content"
msgstr "post indhold"

#: modules/advanced-display-conditions/main.class.php:117
msgid "Display ads based on words and phrases within the post or page content. Dynamically added text might not be considered."
msgstr "Vis annoncer baseret på ord og sætninger inden for indlægget eller sidens indhold. Dynamisk tilføjet tekst kommer muligvis ikke i betragtning."

#: modules/gamipress/views/visitor-condition-rank.php:28
msgid "Choose rank"
msgstr "Vælg rang"

#: modules/gamipress/views/visitor-condition-points.php:17
msgid "Choose Points Type"
msgstr "Vælg punkttype"

#: modules/gamipress/views/visitor-condition-achievement.php:21
msgid "Choose Achievement Type"
msgstr "Vælg præstationstype"

#: modules/gamipress/admin.class.php:34
msgid "Display ads based on GamiPress user achievements."
msgstr "Vis annoncer baseret på GamiPress-brugerpræstationer."

#: modules/gamipress/admin.class.php:33
msgid "GamiPress Achievement"
msgstr "GamiPress præstation"

#: modules/gamipress/admin.class.php:28
msgid "Display ads based on GamiPress user ranks."
msgstr "Vis annoncer baseret på GamiPress-brugerrangeringer."

#: modules/gamipress/admin.class.php:27
msgid "GamiPress Rank"
msgstr "GamiPress-rangering"

#: modules/gamipress/admin.class.php:22
msgid "Display ads based on GamiPress user points."
msgstr "Vis annoncer baseret på GamiPress bruger point."

#: modules/gamipress/admin.class.php:21
msgid "GamiPress Points"
msgstr "GamiPress point"

#: views/privacy-policy-content.php:10
msgid "You can use the text below as a template for your own privacy policy."
msgstr "Du kan bruge teksten nedenfor som skabelon til din egen privatlivspolitik."

#: views/privacy-policy-content.php:13
msgid "This website uses Advanced Ads Pro to place advertisements. The WordPress plugin may use multiple first-party cookies to ensure the correct integration of ads. These cookies store technical information but not IP addresses. Their use is linked to specific features and options when embedding ads."
msgstr "Denne hjemmeside bruger Advanced Ads Pro til at placere annoncer. WordPress-plugin'et kan bruge flere førstepartscookies for at sikre den korrekte integration af annoncer. Disse cookies gemmer tekniske oplysninger, men ikke IP-adresser. Deres brug er knyttet til specifikke funktioner og muligheder ved indlejring af annoncer."

#: views/privacy-policy-content.php:12
msgid "Suggested Text:"
msgstr "Foreslået tekst:"

#. translators: %1$s is an opening a tag, %2$s is the corresponding closing one
#: views/privacy-policy-content.php:19
msgid "Please, see the %1$sAdvanced Ads cookie information%2$s for more details."
msgstr "Se venligst %1$sAdvanced Ads-cookie-informationen%2$s for flere detaljer."

#: views/privacy-policy-content.php:7
msgid "Depending on the setup, Advanced Ads Pro and other add-ons use cookies to control which user sees which ad. They also help to reduce expensive server requests."
msgstr "Afhængigt af opsætningen bruger Advanced Ads Pro og andre tilføjelser cookies til at styre, hvilken bruger der ser hvilken annonce. De hjælper også med at reducere dyre serveranmodninger."

#: classes/advanced-ads-pro-admin.php:531
msgid "Select a Sidebar placement to enable cache-busting."
msgstr "Vælg en sidepanel-placering for at aktivere cache-busting."

#: modules/buddypress/views/position-option.php:56
msgid "member timeline"
msgstr "medlems-tidslinje"

#: classes/advanced-ads-pro-admin.php:533
msgid "Learn more"
msgstr "Lær mere"

#: modules/buddypress/views/position-option.php:55
msgid "group feed"
msgstr "Gruppe feed"

#: modules/buddypress/class-buddypress.php:334
msgid "BuddyBoss group"
msgstr "BuddyBoss gruppe"

#: modules/buddypress/views/position-option.php:53
msgid "any"
msgstr "enhver"

#: modules/buddypress/views/position-option.php:54
msgid "activity stream"
msgstr "aktivitetsstrøm"

#: modules/cache-busting/views/settings.php:38
msgid "Force passive cache busting"
msgstr "Tving passiv cache-busting"

#: modules/cache-busting/views/settings.php:61
msgid "Fallback option"
msgstr "Fallback indstillinger"

#: modules/cache-busting/views/settings.php:17
msgid "Default option"
msgstr "Standard indstillinger"

#: modules/buddypress/class-buddypress.php:131
msgid "Display ads based on BuddyBoss profile fields."
msgstr "Vis annoncer baseret på BuddyBoss profil felter."

#: modules/buddypress/class-buddypress.php:130
msgid "BuddyBoss profile field"
msgstr "BuddyBoss profil felter"

#: modules/buddypress/views/position-option.php:51
msgid "Stream"
msgstr "Stream"

#: includes/placements/types/class-buddypress.php:61
msgid "Display ads on BuddyBoss related pages."
msgstr "Vis annoncer på BuddyBoss relaterede sider."

#: includes/placements/types/class-buddypress.php:49
msgid "BuddyBoss Content"
msgstr "BuddyBoss indhold"

#: modules/cache-busting/views/settings.php:42
msgid "By default, cache busting only works through placements."
msgstr "Som standard, virker cache-busting kun gennem placeringer."

#: modules/cache-busting/views/settings.php:90
msgid "Update visitor profile"
msgstr "Opdater besøgs profil"

#: modules/ad-server/views/placement-usage.php:14
msgid "Direct URL"
msgstr "Direkte URL"

#: modules/ad-server/views/placement-settings.php:11
msgid "The name of the placement that appears in the URL and injection code."
msgstr "Navnet på den placering, der vises i URL'en og injektionskoden."

#: modules/ad-server/views/placement-settings.php:10
msgid "Save the page to update the usage code below."
msgstr "Gem siden for at opdatere koden brugt nedenfor."

#: modules/ad-server/views/module-settings.php:30
msgid "Prevent direct access to the placement URL."
msgstr "Forhindre direkte adgang til placerings-URL'en."

#: modules/ad-server/views/module-settings.php:24
msgid "Please don’t enter subdirectories."
msgstr "Indtast venligst ikke undermapper."

#: modules/ad-server/views/module-settings.php:18
msgid "Separate multiple values with a comma."
msgstr "Adskil flere værdier med et komma."

#: modules/ad-server/views/module-settings.php:18
msgid "Top level domains on which the ads will be loaded."
msgstr "Top-level domæner, som annoncerne vil blive indlæst på."

#: modules/ad-server/views/module-settings.php:13
#: modules/ads-for-adblockers/views/settings.php:18
#: modules/advanced-visitor-conditions/views/settings.php:6
#: modules/cache-busting/views/settings.php:12
#: modules/click-fraud-protection/views/settings.php:10
#: modules/lazy-load/views/settings.php:8
#: modules/rest-api/views/module-enable.php:12
msgid "Activate module."
msgstr "Aktiver modul."

#: modules/ad-server/admin.class.php:92
msgid "Usage"
msgstr "Anvendelse"

#: modules/ad-server/admin.class.php:78
msgid "Public string"
msgstr "Offentlig streng"

#: includes/placements/types/class-server.php:56
msgid "Display ads on external websites."
msgstr "Vis annoncer på eksterne websteder."

#: includes/placements/types/class-server.php:47
#: modules/ad-server/admin.class.php:37
msgid "Ad Server"
msgstr "Annonceserver"

#: modules/advanced-visitor-conditions/main.class.php:149
msgid " Display ads based on the user’s capabilities."
msgstr "Vis annoncer baseret på brugerenes rettigheder."

#: modules/advanced-visitor-conditions/main.class.php:141
msgid "Display ads based on the user agent."
msgstr "Vis annoncer baseret på brugeragent. "

#: modules/inject-content/admin.class.php:255
msgid "Words between ads"
msgstr "Ord mellem annoncer"

#: views/setting_words_between_ads.php:6
msgid "Words are counted within paragraphs, headlines and any other element."
msgstr "Ord tælles indenfor afsnit, overskrifter og ethvert andet element."

#: views/setting_words_between_ads.php:5
msgid "A minimum amount of words between automatically injected ads."
msgstr "Et minimum antal af ord, mellem automatisk injicerede annoncer."

#: includes/admin/class-duplicate-placement.php:64
#: includes/admin/class-group-duplication.php:115
#: modules/duplicate-ads/admin.class.php:138
msgctxt "noun"
msgid "copy"
msgstr "kopi"

#: modules/inject-content/admin.class.php:124
msgid "Minimum length of content (in words) before automatically injected ads are allowed in them. Set to zero or leave empty to always display ads, regardless of how long the content is."
msgstr "Minimumslængde af indhold (i ord) før automatisk injicerede annoncer er tilladt i dem. Indstil til nul, eller lad tom, for altid at vise annoncer, uanset hvor langt indholdet er."

#: includes/admin/class-duplicate-placement.php:102
#: includes/admin/class-group-duplication.php:78
#: modules/duplicate-ads/admin.class.php:88
msgid "Duplicate"
msgstr "Duplikere"

#: modules/duplicate-ads/admin.class.php:88
msgid "Create a copy of this ad"
msgstr "Opret en kopi af denne annonce"

#: modules/inject-content/views/setting_post_meta_box.php:4
msgid "Disable automatic ad injection into the content"
msgstr "Deaktiver automatisk annonce injektion i indholdet"

#: modules/cache-busting/views/settings.php:102
msgid "Update visitor profile when user logs in or out"
msgstr "Opdater besøgsprofil, når brugeren logger ind eller ud"

#: modules/cache-busting/views/settings.php:92
msgid "You might need to update the page cache if you are using one."
msgstr "Du skal muligvis opdatere sidens cache, hvis du bruger en."

#: modules/cache-busting/views/settings.php:86
msgid "Advanced Ads stores some user information in the user’s browser to limit the number of AJAX requests for cache busting. Manual"
msgstr "Advanced Ads gemmer nogle brugeroplysninger i brugerens browser, for at begrænse antallet af AJAX-anmodninger om cache-busting. Manuelt"

#: modules/cache-busting/views/settings.php:83
msgid "Visitor profile"
msgstr "Besøgsprofil"

#. translators: %s: URL
#: modules/advanced-display-conditions/main.class.php:41
msgid "Display ads based on the current URL parameters (everything following %s), except values following #."
msgstr "Vis annoncer baseret på de aktuelle URL parametre (alt efter %s), undtagen værdier efter #."

#: classes/advanced-ads-pro-admin.php:156
msgid "Disable ads for post types"
msgstr "Deaktiver annoncer for indholdstyperne"

#: modules/cache-busting/views/settings.php:94
msgid "An error occurred"
msgstr "En fejl opstod"

#: modules/cache-busting/views/settings.php:91
msgid "Updated"
msgstr "Opdateret"

#. translators: %s is a placeholder for the URL.
#: modules/advanced-visitor-conditions/main.class.php:163
msgid "Display ads based on the user's roles. See <a href=\"%s\" target=\"_blank\">List of roles in WordPress</a>."
msgstr ""

#: modules/advanced-visitor-conditions/main.class.php:160
msgid "user role"
msgstr "brugerrolle"

#. translators: %s is a list of supported optimizer plugins
#: views/setting_autoptimize.php:7
msgid "Advanced Ads Pro disables optimizers ( %s ) for displaying ads per default. Enable this option to allow optimizers to change the ad code. Especially JavaScript ads might stop working then."
msgstr "Advanced Ads Pro deaktiverer optimeringsværktøjer ( %s ) til visning af annoncer som standard. Aktivér denne mulighed for at tillade optimeringsværktøjer at ændre annoncekoden. Især JavaScript-annoncer stopper muligvis med at fungere."

#: classes/advanced-ads-pro-admin.php:139
msgid "Allow optimizers to modify ad codes"
msgstr "Tillad optimeringsværktøjer at ændre annoncekoder"

#. translators: %s: input field
#: modules/lazy-load/views/settings.php:34
msgid "Start loading the ads %s pixels before they are visible on the screen."
msgstr "Start indlæsning af annoncer %s pixels, før de er synlige på skærmen."

#: modules/grids/admin.class.php:61
msgid "Set to 0 to disable this feature."
msgstr "Indstil til 0 for at deaktivere denne funktion."

#: modules/grids/admin.class.php:60
msgid "On browsers smaller than this, the columns show in full width below each other."
msgstr "I browsere mindre end dette, vises kolonnerne i fuld bredde under hinanden."

#: modules/grids/admin.class.php:58
msgid "Full width"
msgstr "Fuld bredde"

#. translators: "profile fields" relates to BuddyPress profile fields
#: modules/buddypress/views/xprofile-condition.php:58
msgid "No profile fields found"
msgstr "Ingen profilfelter fundet"

#: modules/buddypress/class-buddypress.php:118
msgid "Display ads based on BuddyPress profile fields."
msgstr "Vis annoncer baseret på BuddyPress profilfelter"

#: modules/buddypress/class-buddypress.php:117
msgid "BuddyPress profile field"
msgstr "BuddyPress profilfelter"

#: modules/buddypress/views/position-option.php:44
#: views/setting_repeat.php:4
msgid "repeat the position"
msgstr "gentag positionen"

#: views/setting_custom_code.php:34
msgid "Displayed after the ad content"
msgstr "Vises efter annoncen indhold"

#: views/setting_custom_code.php:30
msgid "place your own code below the ad"
msgstr "placer din egen kode under annoncen"

#: views/setting_custom_code.php:27
msgid "custom code"
msgstr "brugerdefineret kode"

#. Author URI of the plugin
#: advanced-ads-pro.php
msgid "https://wpadvancedads.com"
msgstr "https://wpadvancedads.com"

#. Plugin URI of the plugin
#: advanced-ads-pro.php
msgid "https://wpadvancedads.com/add-ons/advanced-ads-pro/"
msgstr "https://wpadvancedads.com/add-ons/advanced-ads-pro/"

#. Description of the plugin
#: advanced-ads-pro.php
msgid "Advanced features to boost your ad revenue."
msgstr "Avancerede funktioner til at øge din annonceindtægter."

#. Plugin Name of the plugin
#: advanced-ads-pro.php
msgid "Advanced Ads Pro"
msgstr "Advanced Ads Pro"

#: modules/weekdays/views/ad-submitbox-meta.php:19
msgid "Set specific days"
msgstr "Indstil specifikke dage"

#. translators: 1: month, 2: day, 3: year, 4: hour, 5: minute
#: modules/placement-tests/views/settings_test_expiry_date.php:31
msgctxt "order of expiry date fields 1: month, 2: day, 3: year, 4: hour, 5: minute"
msgid "%1$s %2$s, %3$s @ %4$s %5$s"
msgstr "%1$s %2$s, %3$s @ %4$s %5$s"

#: modules/placement-tests/views/settings_test_expiry_date.php:25
msgid "Minute"
msgstr "Minut"

#: modules/placement-tests/views/settings_test_expiry_date.php:24
msgid "Hour"
msgstr "Time"

#: modules/placement-tests/views/settings_test_expiry_date.php:23
msgid "Year"
msgstr "År"

#: modules/placement-tests/views/settings_test_expiry_date.php:22
msgid "Day"
msgstr "Dag"

#. translators: 1: month number (01, 02, etc.), 2: month abbreviation
#: modules/placement-tests/views/settings_test_expiry_date.php:17
msgctxt "1: month number (01, 02, etc.), 2: month abbreviation"
msgid "%1$s-%2$s"
msgstr "%1$s-%2$s"

#: modules/placement-tests/views/settings_test_expiry_date.php:12
msgid "Month"
msgstr "Måned"

#: modules/placement-tests/views/settings_test_expiry_date.php:5
msgctxt "placement tests"
msgid "Send email after"
msgstr "Send e-mail efter"

#: modules/placement-tests/placement-tests.class.php:237
#: modules/placement-tests/views/setting_placement_test_weight.php:17
msgid "Test weight"
msgstr "Test vægtning"

#: modules/placement-tests/views/setting_placement_test_weight.php:15
msgctxt "placement tests"
msgid "Testing"
msgstr "Testning"

#: modules/placement-tests/views/placement-tests.php:66
msgid "Save Tests"
msgstr "Gem test"

#: modules/placement-tests/views/placement-tests.php:59
msgctxt "checkbox to remove placement test"
msgid "delete"
msgstr "slet"

#: modules/placement-tests/views/placement-tests.php:53
msgctxt "placement tests"
msgid "empty"
msgstr "tom"

#: modules/placement-tests/views/placement-tests.php:18
msgid "Placements"
msgstr "Placeringer"

#: modules/placement-tests/views/placement-tests.php:17
msgid "Expiry date"
msgstr "Udløbsdato"

#: modules/placement-tests/views/placement-tests.php:16
msgid "Author"
msgstr "Forfatter"

#: modules/placement-tests/views/placement-tests.php:11
msgid "Placement tests"
msgstr "Placeringstest"

#: modules/lazy-load/views/setting_lazy_load.php:6
#: modules/lazy-load/views/setting_lazy_load.php:8
msgid "disabled"
msgstr "deaktiveret"

#: modules/lazy-load/views/setting_lazy_load.php:2
#: modules/lazy-load/views/setting_lazy_load.php:4
msgid "enabled"
msgstr "aktiveret"

#: modules/group-refresh/views/settings_group_refresh.php:27
msgid "Please use a placement to deliver this group using cache-busting."
msgstr "Brug venligst en placering, for at levere denne gruppe ved hjælp cache-busting."

#: modules/group-refresh/views/settings_group_refresh.php:19
msgid "Refresh ads on the same spot. Works when cache-busting is used."
msgstr "Opdater annoncer på samme sted. Virker når cache-busting bruges."

#: modules/group-refresh/views/settings_group_refresh.php:16
msgid "milliseconds"
msgstr "millisekunder"

#: modules/group-refresh/views/settings_group_refresh.php:14
#: views/admin/placements/bulk-edit.php:30
#: views/admin/placements/bulk-edit.php:40
#: views/admin/tables/ads/column-displayonce.php:10
msgid "Enabled"
msgstr "Aktiveret"

#: modules/grids/views/grid-option-size.php:20
msgid "rows"
msgstr "rækker"

#: modules/grids/views/grid-option-size.php:16
msgid "columns"
msgstr "Kolonner"

#: modules/click-fraud-protection/views/settings.php:23
msgid "Period for which to hide the ad ( in days )"
msgstr "Periode, hvor annoncen skal skjules (i dage)"

#: modules/click-fraud-protection/views/settings.php:20
msgid "Period in which the click limit should be reached ( in hours )"
msgstr "Periode, i hvilken klikgrænsen skal nås (i timer)"

#: modules/click-fraud-protection/views/settings.php:17
msgid "Allowed clicks on a single ad before it is removed"
msgstr "Tilladte klik på en enkelt annonce, før den fjernes"

#. translators: Link to parallax manual
#: modules/ad-server/views/module-settings.php:15
#: modules/ads-for-adblockers/views/settings.php:20
#: modules/advanced-display-conditions/main.class.php:508
#: modules/advanced-display-conditions/views/metabox-string.php:12
#: modules/advanced-visitor-conditions/main.class.php:309
#: modules/advanced-visitor-conditions/main.class.php:408
#: modules/advanced-visitor-conditions/main.class.php:465
#: modules/advanced-visitor-conditions/main.class.php:503
#: modules/buddypress/class-buddypress.php:378
#: modules/buddypress/views/xprofile-condition.php:74
#: modules/cache-busting/cache-busting.class.php:263
#: modules/cache-busting/views/settings.php:14
#: modules/click-fraud-protection/click-fraud-protection.class.php:150
#: modules/click-fraud-protection/click-fraud-protection.class.php:158
#: modules/click-fraud-protection/views/settings.php:12
#: modules/gamipress/views/visitor-condition-achievement.php:34
#: modules/gamipress/views/visitor-condition-points.php:41
#: modules/gamipress/views/visitor-condition-rank.php:41
#: modules/geo/admin/views/setting-maxmind-license-key.php:22
#: modules/geo/classes/admin.php:400
#: modules/group-refresh/views/settings_group_refresh.php:23
#: modules/lazy-load/views/settings.php:10
#: modules/paid-memberships-pro/main.class.php:140
#: modules/paid-memberships-pro/main.class.php:259
#: modules/parallax-ads/views/placement-options-after.php:24
#: modules/responsive-ads/views/setting_fallback_width.php:16
#: modules/rest-api/views/module-enable.php:15
msgid "Manual"
msgstr "Manual"

#. translators: %s link to manual
#: modules/cache-busting/views/settings_check_ad.php:16
msgid "The code of this ad might not work properly with activated cache-busting. <a href=\"%s\" target=\"_blank\">Manual</a>"
msgstr "Koden til denne annonce fungerer muligvis ikke korrekt med aktiveret cache-busting.<a href=\"%s\" target=\"_blank\">Manual</a>"

#: modules/cache-busting/views/settings.php:44
msgid "Enable passive cache busting for all ads and groups which are not delivered through a placement, if possible."
msgstr "Aktiver passiv cache-busting for alle annoncer og grupper, som ikke leveres gennem en placering, hvis det er muligt."

#: modules/cache-busting/views/settings.php:62
msgid "Choose the fallback if “passive“ cache busting is not possible."
msgstr "Vælg fallback, hvis \"passiv\" cache-busting ikke er mulig."

#: modules/cache-busting/admin-ui.class.php:87
#: modules/cache-busting/views/settings.php:31
#: modules/cache-busting/views/settings.php:68
#: views/admin/placements/bulk-edit.php:20
msgid "AJAX"
msgstr "AJAX"

#: modules/advanced-display-conditions/views/referrer-url-metabox.php:15
#: modules/advanced-visitor-conditions/views/referrer-url-metabox.php:15
msgid "url of the referrer"
msgstr "url til henvisningen"

#: modules/advanced-display-conditions/views/referrer-url-metabox.php:5
#: modules/advanced-visitor-conditions/views/referrer-url-metabox.php:5
msgid "URL"
msgstr "URL"

#: modules/advanced-display-conditions/views/referrer-url-metabox.php:3
#: modules/advanced-visitor-conditions/views/referrer-url-metabox.php:3
msgid "Display ad depending on the external url the user comes from."
msgstr "Vis annonce afhængig af den eksterne url, brugeren kommer fra."

#: modules/advanced-display-conditions/views/referrer-url-metabox.php:1
#: modules/advanced-visitor-conditions/views/referrer-url-metabox.php:1
msgid "Display by referrer url"
msgstr "Vis efter henvisnings-url"

#: modules/ads-for-adblockers/views/placement-item.php:46
#: modules/lazy-load/views/setting_lazy_load.php:11
msgid "Works only with cache-busting enabled"
msgstr "Virker kun med cache-busting aktiveret"

#: modules/weekdays/class-advanced-ads-pro-weekdays.php:171
msgid "Never shows up"
msgstr "Viser sig aldrig"

#: modules/weekdays/class-advanced-ads-pro-weekdays.php:90
#: modules/weekdays/class-advanced-ads-pro-weekdays.php:107
msgid "specific days"
msgstr "Specifikke dage"

#. translators: %s: placement test name
#: modules/placement-tests/placement-tests.class.php:702
msgid "Placement test <em>%s</em> created"
msgstr "Placeringstest <em>%s</em> oprettet"

#: modules/placement-tests/placement-tests.class.php:564
msgctxt "placement tests"
msgid "Placement page"
msgstr "Placeringsside"

#: modules/placement-tests/placement-tests.class.php:558
#: modules/placement-tests/placement-tests.class.php:700
#: modules/placement-tests/views/placement-tests.php:50
msgctxt "placement tests"
msgid " vs "
msgstr "vs"

#: modules/placement-tests/placement-tests.class.php:530
msgctxt "placement tests"
msgid "Expired placement tests"
msgstr "Udløbne placerings-teste"

#: modules/placement-tests/views/setting_placement_test_weight.php:28
msgid "Save new test"
msgstr ""

#: modules/paid-memberships-pro/main.class.php:157
msgid "Display ads only to users with a specific membership level set with PaidMembershipsPro."
msgstr "Vis kun annoncer til brugere med et specifikt medlemsniveau, indstillet med PaidMembershipsPro."

#: modules/paid-memberships-pro/main.class.php:156
msgid "PMP user level"
msgstr "PMP brugerniveau"

#: modules/paid-memberships-pro/main.class.php:133
#: modules/paid-memberships-pro/main.class.php:252
msgid "No membership levels set up yet."
msgstr "Ingen medlems niveauer opsat endnu."

#: modules/paid-memberships-pro/main.class.php:39
msgid "Display ads only on pages that require a specific membership level set of PaidMembershipsPro."
msgstr "Vis kun annoncer på sider, der kræver et specifikt medlemsniveau, indstillede med PaidMembershipsPro."

#: modules/paid-memberships-pro/main.class.php:38
msgid "PMP page level"
msgstr "PMP sideniveau"

#: includes/placements/types/class-archive-pages.php:56
msgid "Display the ad between posts on post lists, e.g. home, archives, search etc."
msgstr "Vis annoncen mellem indholdet af sidevisningen på fx hjem/forside, arkiver, søgning osv."

#: includes/placements/types/class-archive-pages.php:47
msgid "Post Lists"
msgstr "Indholdslister"

#: includes/placements/types/class-custom-position.php:57
msgid "Attach the ad to any element in the frontend."
msgstr "Fastgør annoncen til et vilkårligt element i frontend."

#: includes/placements/types/class-custom-position.php:48
msgid "Custom Position"
msgstr "Brugerdefineret position"

#: includes/placements/types/class-content-middle.php:56
msgid "In the middle of the main content based on the number of paragraphs."
msgstr "I midten af hovedindholdet, baseret på antallet af afsnit."

#: includes/placements/types/class-content-middle.php:47
msgid "Content Middle"
msgstr "Indholdets midte"

#: includes/placements/types/class-above-headline.php:57
msgid "Above the main headline on the page (&lt;h1&gt;)."
msgstr "Over hovedoverskriften på siden (&lt;h1&gt;)."

#: includes/placements/types/class-above-headline.php:48
msgid "Above Headline"
msgstr "Over overskriften"

#: includes/placements/types/class-content-random.php:56
msgid "After a random paragraph in the main content."
msgstr "Efter et tilfældigt afsnit i hovedindholdet."

#: includes/placements/types/class-content-random.php:47
msgid "Random Paragraph"
msgstr "Tilfældig afsnit"

#: modules/inject-content/admin.class.php:155
msgid "secondary loops"
msgstr "sekundære loops"

#: modules/inject-content/admin.class.php:151
msgid "Only enable this option if you are sure what you are doing!"
msgstr "Aktiver kun denne mulighed, hvis du er sikker på, hvad du laver!"

#: modules/inject-content/admin.class.php:150
msgid "Allow injection into any custom and secondary queries."
msgstr "Tillad indsprøjtning i alle brugerdefinerede og sekundære forespørgsler."

#: modules/inject-content/admin.class.php:128
msgid "minimum content length"
msgstr "mindste indholdslængde"

#: modules/inject-content/admin.class.php:93
msgid "Before which post to inject the ad on post lists."
msgstr "Før hvilket indlæg skal annoncen indsættes på indlægs-lister."

#. translators: %s: index of the post
#: modules/inject-content/admin.class.php:91
msgid "Inject before %s. post"
msgstr "Injicer før %s. indlæg"

#: modules/inject-content/admin.class.php:69
msgid "Place the following element where the ad should be displayed."
msgstr "Placer følgende element, hvor annoncen skal vises."

#: modules/inject-content/admin.class.php:68
msgid "by new element"
msgstr "ved nyt element"

#: modules/bbpress/class-admin.php:64
#: modules/bbpress/class-admin.php:87
#: modules/bbpress/class-admin.php:130
#: modules/inject-content/admin.class.php:58
msgid "Position"
msgstr "Position"

#: modules/inject-content/admin.class.php:57
msgid "Uses <a href=\"https://api.jquery.com/category/selectors/\" target=\"_blank\">jQuery selectors</a>, e.g. #container_id, .container_class"
msgstr "Bruger <a href=\"https://api.jquery.com/category/selectors/\" target=\"_blank\">jQuery-vælgere</a>, e.g. #container_id, .container_class"

#: modules/inject-content/admin.class.php:54
msgid "or enter manually"
msgstr "eller indtast manuelt"

#: modules/inject-content/admin.class.php:56
msgid "select position"
msgstr "vælg position"

#: modules/inject-content/admin.class.php:55
msgctxt "frontend picker"
msgid "stop selection"
msgstr "stop udvælgelse"

#: modules/inject-content/admin.class.php:51
msgid "Place ads in relation to an existing element in the frontend."
msgstr "Placer annoncer i relation til et eksisterende element i frontend."

#: modules/inject-content/admin.class.php:49
msgid "by existing element"
msgstr "af eksisterende element"

#: modules/inject-content/admin.class.php:40
msgid "below"
msgstr "nedenfor"

#: modules/inject-content/admin.class.php:39
msgid "inside, after other content"
msgstr "indenfor, efter andet indhold"

#: modules/inject-content/admin.class.php:38
msgid "inside, before other content"
msgstr "indenfor, før andet indhold"

#: modules/inject-content/admin.class.php:37
msgid "above"
msgstr "ovenfor"

#: modules/group-refresh/admin.class.php:60
msgid "Refresh interval"
msgstr "Opdateringsinterval"

#: modules/grids/admin.class.php:73
msgid "Random order"
msgstr "Tilfældig rækkefølge"

#: modules/grids/admin.class.php:53
msgid "Minimum width of a column in the grid."
msgstr "Minimumsbredde af en kolonne i gitteret."

#: modules/grids/admin.class.php:51
msgid "Min. width"
msgstr "Min. bredde"

#: modules/grids/admin.class.php:45
msgid "Inner margin"
msgstr "Indre margen"

#: modules/grids/admin.class.php:36
msgid "Size"
msgstr "Størrelse"

#: modules/click-fraud-protection/click-fraud-protection.class.php:320
msgid "Click Fraud Protection enabled"
msgstr "Beskyttelse mod klik bedrageri, aktiveret"

#: modules/click-fraud-protection/click-fraud-protection.class.php:145
msgid "hours"
msgstr "timer"

#: modules/click-fraud-protection/click-fraud-protection.class.php:143
msgid "within"
msgstr "indenfor"

#: modules/click-fraud-protection/click-fraud-protection.class.php:111
msgid "Display ad only if a click limit has not been reached."
msgstr "Vis kun annonce, hvis en klikgrænse ikke er nået."

#: modules/click-fraud-protection/click-fraud-protection.class.php:110
msgid "max. ad clicks"
msgstr "max. annonce klik"

#: modules/click-fraud-protection/admin.class.php:29
msgid "Click Fraud Protection"
msgstr "Beskyttelse mod klik svindel"

#: modules/cache-busting/cache-busting.class.php:1913
msgctxt "placement admin label"
msgid "Cache-busting:"
msgstr "Cache-busting:"

#: modules/cache-busting/cache-busting.class.php:1907
msgid "The ad is not displayed on the page"
msgstr "Annoncen vises ikke på siden"

#: modules/cache-busting/cache-busting.class.php:1906
msgid "The ad is displayed on the page"
msgstr "Annoncen vises på siden"

#: modules/cache-busting/cache-busting.class.php:1903
#: modules/cache-busting/views/settings.php:24
msgid "passive"
msgstr "passiv"

#: modules/cache-busting/cache-busting.class.php:1897
msgid "The ad can work with passive cache-busting"
msgstr "Annoncen kan fungere med passiv cache-busting"

#: modules/cache-busting/cache-busting.class.php:1895
msgid "The ad can not work with passive cache-busting"
msgstr "Annoncen kan ikke fungere med passiv cache-busting"

#: modules/ads-for-adblockers/views/settings.php:27
#: modules/cache-busting/admin.class.php:29
#: views/admin/placements/bulk-edit.php:16
msgid "Cache Busting"
msgstr "Cache Busting"

#: modules/cache-busting/admin-ui.class.php:150
msgid "The <em>Ad Group</em> ad type can only use AJAX or no cache-busting, but not passive cache-busting."
msgstr "<em>Annoncegruppe</em> Annoncetype kan kun bruge AJAX eller ingen cache-busting, men ikke passiv cache-busting."

#: modules/cache-busting/admin-ui.class.php:107
msgctxt "placement admin label"
msgid "Cache-busting"
msgstr "Cache-busting"

#: modules/cache-busting/cache-busting.class.php:1910
msgctxt "setting label"
msgid "off"
msgstr "fra"

#: modules/cache-busting/cache-busting.class.php:1901
msgctxt "setting label"
msgid "ajax"
msgstr "ajax"

#: modules/buddypress/class-admin.php:82
msgid "Member List"
msgstr "Medlemsliste"

#: modules/buddypress/class-admin.php:79
msgid "Group List"
msgstr "Gruppeliste"

#: modules/buddypress/class-admin.php:63
#: modules/buddypress/class-admin.php:71
msgid "Activity Entry"
msgstr "Aktivitets indgang"

#. translators: %s is an HTML input element.
#: modules/buddypress/views/position-option.php:37
msgid "Inject after %s. entry"
msgstr "Injicer efter %s. indgang"

#: includes/placements/types/class-buddypress.php:63
msgid "Display ads on BuddyPress related pages."
msgstr "Vis annoncer på BuddyPress-relaterede sider."

#: includes/placements/types/class-buddypress.php:51
msgid "BuddyPress Content"
msgstr "BuddyPress indhold"

#: modules/bbpress/class-admin.php:150
msgid "forums page"
msgstr "forum side"

#: modules/bbpress/class-admin.php:146
msgid "single forum page"
msgstr "enkelt forumside"

#: modules/bbpress/class-admin.php:142
#: modules/bbpress/class-admin.php:164
msgid "forum topic page"
msgstr "forumets emneside"

#: modules/buddypress/views/position-option.php:20
#: modules/inject-content/admin.class.php:80
#: modules/inject-content/admin.class.php:96
msgid "position"
msgstr "position "

#: includes/placements/types/class-bbpress-comment.php:56
msgid "Display ads in bbPress replies."
msgstr "Vis annoncer i bbPress-svar."

#: includes/placements/types/class-bbpress-comment.php:47
msgid "bbPress Reply Content"
msgstr "bbPress besvars-indhold"

#: includes/placements/types/class-bbpress-static.php:56
msgid "Display ads on bbPress related pages."
msgstr "Vis annoncer på bbPress-relaterede sider."

#: includes/placements/types/class-bbpress-static.php:47
msgid "bbPress Static Content"
msgstr "bbPress Statisk indhold"

#: modules/background-ads/admin.class.php:33
msgid "background"
msgstr "baggrund"

#: modules/background-ads/admin.class.php:30
msgid "Select a background color in case the background image is not high enough to cover the whole screen."
msgstr "Vælg en baggrundsfarve i tilfælde af, at baggrundsbilledet ikke er højt nok til at dække hele skærmen."

#: includes/placements/types/class-background-ad.php:56
msgid "Background of the website behind the main wrapper."
msgstr "Baggrunden på websted, bag den vigtigste \"main wrapper\"."

#: includes/placements/types/class-background-ad.php:47
msgid "Background Ad"
msgstr "Baggrunds-annonce"

#. translators: %s is a placeholder for the impressions field
#: modules/advanced-visitor-conditions/main.class.php:499
msgid "within %s seconds"
msgstr "inden for %s sekunder"

#: modules/advanced-visitor-conditions/main.class.php:449
msgid "Cookie Value"
msgstr "Cookie værdi"

#: modules/advanced-visitor-conditions/main.class.php:448
msgid "Cookie Name"
msgstr "Cookie navn"

#: modules/advanced-visitor-conditions/main.class.php:301
#: modules/advanced-visitor-conditions/main.class.php:349
#: modules/advanced-visitor-conditions/main.class.php:398
msgid "-- choose one --"
msgstr "-- Vælg en --"

#: modules/advanced-visitor-conditions/main.class.php:298
msgid "can not"
msgstr "kan ikke"

#: modules/advanced-visitor-conditions/main.class.php:297
msgid "can"
msgstr "kan"

#: modules/advanced-visitor-conditions/main.class.php:202
msgid "new visitor"
msgstr "ny besøgende"

#: modules/advanced-visitor-conditions/main.class.php:196
msgid "Display the ad only for a few impressions in a given period per user."
msgstr "Vis kun annoncen for nogle få visninger, i en given periode pr. bruger."

#: modules/advanced-visitor-conditions/main.class.php:195
msgid "max. ad impressions"
msgstr "max. annonce visninger"

#: modules/advanced-visitor-conditions/main.class.php:188
msgid "Display ads based on the number of page impressions the user already made before the current one."
msgstr "Vis annoncer baseret på antallet af sidevisninger, brugeren allerede har foretaget før den aktuelle."

#: modules/advanced-visitor-conditions/main.class.php:187
msgid "page impressions"
msgstr "side indtryk "

#: modules/advanced-visitor-conditions/main.class.php:180
msgid "cookie"
msgstr "cookie"

#: modules/advanced-visitor-conditions/main.class.php:174
msgid "Display ads based on the visitor's browser language."
msgstr "Vis annoncer baseret på den besøgendes browsersprog."

#: modules/advanced-visitor-conditions/main.class.php:173
msgid "browser language"
msgstr "browser sprog"

#: modules/advanced-visitor-conditions/main.class.php:148
msgid "user can (capabilities)"
msgstr "bruger kan (tilladelser)"

#: modules/advanced-visitor-conditions/main.class.php:140
msgid "user agent"
msgstr "brugeragent"

#: modules/advanced-visitor-conditions/main.class.php:133
msgid "Display ads based on the referrer URL."
msgstr "Vis annoncer baseret på henvisnings-url."

#: modules/advanced-visitor-conditions/main.class.php:132
msgid "referrer url"
msgstr "henvisnings url "

#: modules/advanced-visitor-conditions/admin.class.php:29
msgid "Advanced visitor conditions"
msgstr "Avancerede besøgsbetingelser"

#: modules/advanced-display-conditions/main.class.php:574
msgid "count from end"
msgstr "tælle fra ende"

#: modules/advanced-display-conditions/main.class.php:540
msgid "meta key"
msgstr "meta nøgle"

#: modules/advanced-display-conditions/main.class.php:539
msgid "all of"
msgstr "alle af"

#: modules/advanced-display-conditions/main.class.php:538
msgid "any of"
msgstr "hvilken som helst af"

#: modules/advanced-display-conditions/main.class.php:502
msgid "no languages set up in WPML"
msgstr "ingen sprog opsat i WPML"

#: modules/advanced-display-conditions/main.class.php:431
#: modules/advanced-display-conditions/main.class.php:486
#: modules/advanced-visitor-conditions/main.class.php:346
#: modules/advanced-visitor-conditions/main.class.php:395
#: modules/advanced-visitor-conditions/main.class.php:538
#: modules/gamipress/views/visitor-condition-achievement.php:17
#: modules/geo/classes/admin.php:308
#: modules/paid-memberships-pro/main.class.php:118
#: modules/paid-memberships-pro/main.class.php:237
msgid "is not"
msgstr "er ikke"

#: modules/advanced-display-conditions/main.class.php:430
#: modules/advanced-display-conditions/main.class.php:485
#: modules/advanced-visitor-conditions/main.class.php:345
#: modules/advanced-visitor-conditions/main.class.php:394
#: modules/advanced-visitor-conditions/main.class.php:536
#: modules/gamipress/views/visitor-condition-achievement.php:16
#: modules/geo/classes/admin.php:307
#: modules/paid-memberships-pro/main.class.php:117
#: modules/paid-memberships-pro/main.class.php:236
msgid "is"
msgstr "er"

#: modules/advanced-display-conditions/main.class.php:110
msgid "Display ads based on the index of a split page"
msgstr "Vis annoncer baseret på indekset af en opdelt side"

#: modules/advanced-display-conditions/main.class.php:109
msgid "pagination"
msgstr "sideopdeling"

#: modules/advanced-display-conditions/main.class.php:103
msgid "Display ads based on post meta."
msgstr "Vis annoncer baseret på indlægs-meta."

#: modules/advanced-display-conditions/main.class.php:102
msgid "post meta"
msgstr "indholds meta"

#: modules/advanced-display-conditions/main.class.php:96
msgid "Display ads based on parent page."
msgstr "Vis annoncer baseret på overordnet side."

#: modules/advanced-display-conditions/main.class.php:95
msgid "parent page"
msgstr "Overordnet side"

#: modules/advanced-display-conditions/main.class.php:87
msgid "WPML language"
msgstr "WPML sprog"

#. translators: %s: post type
#: modules/advanced-display-conditions/main.class.php:57
#: modules/advanced-display-conditions/main.class.php:76
msgid "Display ads based on the template of the %s post type."
msgstr "Vis annoncer baseret på skabelonen for %s indlægstypen."

#. translators: %s: page
#: modules/advanced-display-conditions/main.class.php:55
#: modules/advanced-display-conditions/main.class.php:75
msgid "%s template"
msgstr "%s skabelon"

#: modules/advanced-display-conditions/main.class.php:39
msgid "url parameters"
msgstr "url parametre"

#: modules/ads-for-adblockers/admin.class.php:148
msgid "The chosen ad contains a reference to an external .js file"
msgstr "Den valgte annonce indeholder en reference til en ekstern .js-fil"

#: modules/ads-for-adblockers/admin.class.php:86
msgid "Displayed to visitors with an ad blocker"
msgstr "Vises for besøgende med annonceblokering"

#: modules/ads-for-adblockers/admin.class.php:94
msgid "Ad blocker item"
msgstr "Annonce-blokeringselement"

#: modules/ads-for-adblockers/admin.class.php:42
msgid "Ads for ad blockers"
msgstr "Annoncer til annonceblokering"

#: modules/admin-bar/admin-bar.class.php:104
msgid "placement"
msgstr "placering"

#: modules/admin-bar/admin-bar.class.php:101
msgid "group"
msgstr "gruppe"

#: modules/admin-bar/admin-bar.class.php:98
msgid "ad"
msgstr "annonce"

#: modules/admin-bar/admin-bar.class.php:56
msgid "No Ads found"
msgstr "Ingen annoncer fundet"

#: modules/admin-bar/admin-bar.class.php:48
#: modules/ads-for-adblockers/views/placement-item.php:27
#: views/admin/tables/ads/adsense-fallback.php:37
msgid "Ads"
msgstr "Annoncer"

#: views/setting_output_once.php:6
msgid "Display the ad only once per page"
msgstr "Vis kun annoncen én gang pr. side"

#: views/setting_output_once.php:2
msgid "Display only once"
msgstr "Vis kun én gang"

#: classes/advanced-ads-pro.php:332
msgid "Please note, the “Ad Admin“ and the “Ad Manager“ roles have the “upload_files“ and the “unfiltered_html“ capabilities"
msgstr "Bemærk venligst, at rollerne \"Annonce administrator\" og \"Annonce manager\" har \"upload_files\" og \"unfiltred_html\" rettigheder"

#: includes/class-bootstrap.php:225
msgid "Install Now"
msgstr "Installer nu"

#: includes/class-bootstrap.php:233
msgid "Activate Now"
msgstr "Aktiver nu"

#. translators: %s: URL to the settings page
#: classes/advanced-ads-pro-admin.php:390
msgid "Enable the Advanced Visitor Conditions <a href=\"%s\" target=\"_blank\">in the settings</a>."
msgstr "Aktiver de avancerede besøgsbetingelser <a href=\"%s\" target=\"_blank\">i indstillingerne</a>."

#: classes/advanced-ads-pro-admin.php:336
msgid "Please note, with the last update, the “Ad Admin“ and “Ad Manager“ roles have the “upload_files“ and the “unfiltered_html“ capabilities."
msgstr "Bemærk venligst, at med den seneste opdatering har rollerne \"Annonce administrator\" og \"Annonce manager\" \"upload_files\" og \"unfiltred_html\" rettigheder."

#: classes/advanced-ads-pro-admin.php:326
msgid "Ad User Role"
msgstr "Annonce-brugerrolle"

#: classes/advanced-ads-pro-admin.php:323
msgid "Advanced Ads User Role"
msgstr "Advanced Ads bruger rolle"

#: classes/advanced-ads-pro-admin.php:46
msgid "--no role--"
msgstr "--ingen rolle--"

#: classes/advanced-ads-pro-admin.php:45
#: includes/installation/class-install.php:159
msgid "Ad User"
msgstr "Annonce bruger"

#: classes/advanced-ads-pro-admin.php:44
#: includes/installation/class-install.php:146
msgid "Ad Manager"
msgstr "Annonce manager"

#: classes/advanced-ads-pro-admin.php:43
#: includes/installation/class-install.php:132
msgid "Ad Admin"
msgstr "Annonce admin"
