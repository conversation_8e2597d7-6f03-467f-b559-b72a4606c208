msgid ""
msgstr ""
"Project-Id-Version: Advanced Ads Pro\n"
"Report-Msgid-Bugs-To: \n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2025-03-25T23:54:42+00:00\n"
"PO-Revision-Date: 2022-10-05 18:32:57+0000\n"
"Language: es_VE\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/4.0.1\n"

#: views/admin/placements/bulk-edit.php:21
msgid "Off"
msgstr ""

#: views/admin/placements/bulk-edit.php:18
#: views/admin/placements/bulk-edit.php:29
#: views/admin/placements/bulk-edit.php:39
msgid "No Change"
msgstr ""

#: modules/cache-busting/cache-busting.class.php:253
#: views/admin/placements/bulk-edit.php:37
msgid "Hide when empty"
msgstr ""

#: views/admin/placements/bulk-edit.php:31
#: views/admin/placements/bulk-edit.php:41
msgid "Disabled"
msgstr ""

#: views/admin/placements/bulk-edit.php:19
msgid "Auto"
msgstr ""

#. Author of the plugin
#: advanced-ads-pro.php
msgid "Advanced Ads"
msgstr ""

#. translators: %s: Plugin name
#: includes/class-bootstrap.php:155
msgid "Your versions of the Advanced Ads addons listed below are incompatible with <strong>Advanced Ads %s</strong> and have been deactivated. Please update the plugin to the latest version."
msgstr ""

#. translators: %s: Plugin name
#: includes/class-bootstrap.php:109
msgid "Your version of <strong>Advanced Ads - Pro</strong> is incompatible with <strong>Advanced Ads %s</strong> and has been deactivated. Please update the plugin to the latest version."
msgstr ""

#. translators: 1: is a link to a support document. 2: closing link
#: includes/class-autoloader.php:113
msgid "Your installation of Advanced Ads is incomplete. If you installed Advanced Ads from GitHub, %1$s please refer to this document%2$s to set up your development environment."
msgstr ""

#: modules/geo/admin/views/setting-download.php:22
msgid "You are currently using filter hooks to load custom database files."
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:17
msgid "When to show overlay again?"
msgstr ""

#: modules/placement_conditions/admin.class.php:70
msgid "Visitor Conditions"
msgstr ""

#: modules/ads-by-hours/src/class-admin.php:135
msgid "viewer time zone"
msgstr ""

#: modules/advanced-visitor-conditions/main.class.php:218
msgid "User IP Address"
msgstr ""

#: modules/ads-by-hours/views/publish-metabox-inputs.php:54
msgid "To"
msgstr ""

#. translators: 1 opening link tag. 2 closing tag
#: modules/cache-busting/views/settings.php:53
msgid "This option does not work with the current active theme and %1$sblock themes%2$s in general."
msgstr ""

#. translators: %s: dashicon class
#: modules/lazy-load/views/settings.php:22
msgid "This module requires: <br> <span class=\"dashicons %s\"></span> Cache Busting"
msgstr ""

#: modules/ads-for-adblockers/views/settings.php:25
msgid "This module requires:"
msgstr ""

#: views/admin/tables/ads/adsense-fallback.php:47
#: views/admin/tables/ads/adsense-fallback.php:66
msgid "The selected item will be displayed when an AdSense ad is unavailable, ensuring your ad space remains filled."
msgstr ""

#: includes/class-bootstrap.php:180
msgid "The following addons are affected:"
msgstr ""

#. translators: group or ad title.
#: views/admin/tables/ads/adsense-fallback.php:58
msgid "The default fallback is \"%s\". You can change this in the AdSense settings."
msgstr ""

#: views/admin/tables/ads/adsense-fallback.php:75
msgid "The AdSense fallback feature requires the ad to be assigned to a placement with enabled Cache Busting."
msgstr ""

#. translators: %s: WP_ENVIRONMENT_TYPES
#: includes/class-autoloader.php:157
msgid "The %s constant is no longer supported."
msgstr ""

#: modules/weekdays/class-advanced-ads-pro-weekdays.php:177
msgid "Shows up"
msgstr ""

#: modules/ads-by-hours/src/class-admin.php:79
msgid "Showing ads depending on the visitor's time requires enabling Cache Busting."
msgstr ""

#: modules/extended-adblocker/views/setting-activate-extended.php:26
msgid "Show a custom overlay to users with an ad blocker enabled, prompting them to turn it off on your website."
msgstr ""

#: modules/ads-for-adblockers/admin.class.php:89
#: modules/lazy-load/admin.class.php:58
msgid "Settings"
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:20
msgid "Set the timing for the overlay to reappear after being dismissed."
msgstr ""

#: modules/ads-by-hours/views/publish-metabox-inputs.php:21
msgid "Set specific hours"
msgstr ""

#: modules/cache-busting/cache-busting.class.php:257
#: views/admin/placements/bulk-edit.php:45
msgid "Remove the placeholder if unfilled."
msgstr ""

#: modules/extended-adblocker/views/setting-activate-extended.php:64
msgid "Redirect URL"
msgstr ""

#: modules/extended-adblocker/views/setting-activate-extended.php:37
msgid "Redirect"
msgstr ""

#: modules/lazy-load/admin.class.php:68
msgid "Prevent ads from getting loaded before they appear in the visitor’s visible area."
msgstr ""

#: modules/extended-adblocker/views/setting-activate-extended.php:22
msgid "Overlay"
msgstr ""

#: views/admin/tables/ads/adsense-fallback.php:27
msgid "None"
msgstr ""

#: views/admin/tables/ads/adsense-fallback.php:52
msgid "No default fallback ad selected. Choose one in the AdSense settings."
msgstr ""

#: modules/extended-adblocker/views/setting-activate-extended.php:17
msgid "No additional actions"
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:43
msgid "Never"
msgstr ""

#: modules/extended-adblocker/views/setting-styling.php:19
msgid "Insert CSS to customize the overlay container layout."
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:84
msgid "Insert CSS to customize the dismiss button layout."
msgstr ""

#: modules/extended-adblocker/views/setting-styling.php:36
msgid "Insert CSS to customize the background of the overlay."
msgstr ""

#. translators: %s: index input field
#: modules/bbpress/class-admin.php:106
msgid "Inject after %s post"
msgstr ""

#: includes/class-bootstrap.php:129
#: includes/class-bootstrap.php:152
msgid "Important Notice"
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:55
msgid "Hide dismiss button"
msgstr ""

#: modules/ads-for-adblockers/views/placement-item.php:18
msgid "Groups"
msgstr ""

#. translators: %s: first field, %s: last field
#: modules/advanced-display-conditions/main.class.php:571
msgid "from %1$s to %2$s"
msgstr ""

#: modules/ads-by-hours/views/publish-metabox-inputs.php:35
msgid "From"
msgstr ""

#: includes/admin/class-adsense.php:38
msgid "Fallback ad/group"
msgstr ""

#: views/admin/tables/ads/adsense-fallback.php:20
msgid "Fallback"
msgstr ""

#: modules/extended-adblocker/views/setting-exclude.php:13
msgid "Exclude"
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:28
msgid "Everytime"
msgstr ""

#. translators: 1: The download URL, 2: The HTTP error code, 3: The HTTP header
#. title, 4: The response body
#: modules/geo/classes/admin.php:545
msgid "Error downloading database from: %1$s - %2$s %3$s - %4$s"
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:68
msgid "Enter the text that you want to appear on the dismiss button."
msgstr ""

#: modules/extended-adblocker/views/setting-activate-extended.php:68
msgid "Enter a specific page on your domain to which users with activated AdBlocker should be automatically redirected."
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:77
#: modules/extended-adblocker/views/setting-dismissible.php:94
#: modules/extended-adblocker/views/setting-styling.php:29
#: modules/extended-adblocker/views/setting-styling.php:46
msgid "Empty and save to revert to defaults."
msgstr ""

#: modules/advanced-visitor-conditions/main.class.php:211
msgid "Display or hide ad when user use adblocker."
msgstr ""

#: includes/admin/class-ad-list-table.php:42
#: includes/admin/class-ad-list-table.php:86
msgid "Display Once"
msgstr ""

#: modules/placement_conditions/admin.class.php:52
msgid "Display Conditions"
msgstr ""

#: modules/advanced-visitor-conditions/main.class.php:219
msgid "Display ads based on the user IP address. Enter one IP address per line."
msgstr ""

#: modules/buddypress/class-buddypress.php:324
msgid "Display ads based on existing BuddyPress groups."
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:65
msgid "Dismiss button text"
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:81
msgid "Dismiss button styling"
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:48
msgid "Dismiss button"
msgstr ""

#: modules/extended-adblocker/main.class.php:83
#: modules/extended-adblocker/views/setting-dismissible.php:75
msgid "Dismiss"
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:58
msgid "Disabling the dismiss button significantly limits site interaction."
msgstr ""

#: modules/cache-busting/cache-busting.class.php:261
msgid "Deleting an empty placement might lead to a layout shift."
msgstr ""

#: views/admin/tables/ads/adsense-fallback.php:25
msgid "Default"
msgstr ""

#: includes/admin/class-duplicate-placement.php:101
msgid "Create a copy of this placement"
msgstr ""

#: includes/admin/class-group-duplication.php:77
msgid "Create a copy of this group"
msgstr ""

#: modules/extended-adblocker/views/setting-overlay-content.php:11
msgid "Content"
msgstr ""

#: modules/extended-adblocker/views/setting-styling.php:16
msgid "Container styling"
msgstr ""

#: modules/extended-adblocker/views/setting-exclude.php:16
msgid "Choose which user roles to exclude from this ad blocker countermeasure."
msgstr ""

#: modules/lazy-load/admin.class.php:56
msgid "Cache Busting needs to be enabled"
msgstr ""

#: modules/ads-for-adblockers/admin.class.php:87
msgid "Cache Busting and Ad blocker disguise need to be enabled"
msgstr ""

#: modules/buddypress/class-buddypress.php:323
msgid "BuddyPress group"
msgstr ""

#: modules/extended-adblocker/views/setting-styling.php:33
msgid "Background styling"
msgstr ""

#: modules/extended-adblocker/views/setting-activate-extended.php:41
msgid "Automatically redirect users with ad blockers enabled to an internal page. Content access is granted after turning off the ad blocker."
msgstr ""

#: includes/class-bootstrap.php:71
#: includes/class-bootstrap.php:78
msgid "Advanced Ads - Pro"
msgstr ""

#. translators: ad title.
#: includes/class-adsense.php:52
msgid "AdSense fallback was loaded for empty AdSense ad \"%s\""
msgstr ""

#: includes/class-bootstrap.php:130
msgid "Addons listed below requires the <strong><a href=\"https://wpadvancedads.com/?utm_source=advanced-ads&utm_medium=link&utm_campaign=activate-advanced-ads-pro\" target=\"_blank\">Advanced Ads</a></strong> plugin to be installed and activated on your site."
msgstr ""

#: modules/advanced-visitor-conditions/main.class.php:210
msgid "Adblocker"
msgstr ""

#: views/admin/tables/ads/adsense-fallback.php:29
msgid "Ad groups"
msgstr ""

#: modules/extended-adblocker/admin.class.php:49
msgid "Ad blocker countermeasures"
msgstr ""

#: modules/ads-for-adblockers/views/settings.php:29
msgid "Ad block disguise"
msgstr ""

#: modules/extended-adblocker/views/setting-activate-extended.php:51
msgid "Activate the ad blocker disguise above to display the overlay."
msgstr ""

#: modules/ads-by-hours/src/class-admin.php:70
msgid "A cache plugin has been detected. It is recommended to enable Cache Busting and check the visitor's time when displaying the ad on the frontend."
msgstr ""

#: includes/class-bootstrap.php:95
msgid "<strong>Advanced Ads - Pro</strong> requires the <strong>Advanced Ads free</strong> plugin to be installed and activated on your site."
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:37
msgid "1 week"
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:40
msgid "1 month"
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:31
msgid "1 hour"
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:34
msgid "1 day"
msgstr ""

#: modules/ads-for-adblockers/views/placement-item.php:15
msgid "--not selected--"
msgstr ""

#. translators: comma separated list of days.
#: modules/weekdays/class-advanced-ads-pro-weekdays.php:181
msgid " on: %s"
msgstr ""

#. translators: 1. localized time 2. localized time 3. timezone name.
#: modules/weekdays/class-advanced-ads-pro-weekdays.php:187
msgid " between %1$s and %2$s %3$s"
msgstr ""

#: modules/responsive-ads/views/setting_reload_ads.php:19
msgid "You need to enable cache-busting in order to use this feature."
msgstr ""

#. translators: 1 is the opening link to the Advanced Ads website, 2 the
#. closing link
#: classes/advanced-ads-pro.php:338
msgid "We have renamed the Responsive Ads add-on to ‘Advanced Ads AMP Ads’. With this change, the Browser Width visitor condition moved from that add-on into Advanced Ads Pro. You can deactivate ‘Advanced Ads AMP Ads’ if you don’t utilize AMP ads or the custom sizes feature for responsive AdSense ad units. %1$sRead more%2$s."
msgstr ""

#: modules/responsive-ads/includes/class-admin.php:45
msgid "Responsive Image Ads"
msgstr ""

#: modules/responsive-ads/includes/class-admin.php:38
msgid "Responsive Ads"
msgstr ""

#: modules/responsive-ads/views/setting_reload_ads.php:16
msgid "Reload ads when the screen resizes."
msgstr ""

#: modules/responsive-ads/includes/class-admin.php:53
msgid "Reload ads on resize"
msgstr ""

#: modules/responsive-ads/includes/class-admin.php:61
msgid "Fallback width"
msgstr ""

#: modules/responsive-ads/includes/class-common.php:40
msgid "Display ads based on the browser width."
msgstr ""

#: modules/cache-busting/admin-ui.class.php:51
msgid "Couldn't find the placement."
msgstr ""

#: modules/responsive-ads/views/setting_responsive_images.php:15
msgid "Check this option if the size of image ads is not adjusted responsively by your theme."
msgstr ""

#: modules/cache-busting/admin-ui.class.php:48
msgid "Cache busting has been successfully enabled for the assigned placement."
msgstr ""

#: modules/responsive-ads/includes/class-common.php:39
msgid "browser width"
msgstr ""

#: modules/cache-busting/admin-ui.class.php:88
#: modules/cache-busting/views/settings.php:75
msgid "off"
msgstr ""

#: modules/cache-busting/views/settings.php:18
msgid "Choose which method to use when a placement needs cache busting and the option is set to “auto”."
msgstr ""

#: modules/cache-busting/admin-ui.class.php:86
msgid "auto"
msgstr ""

#: modules/cache-busting/admin-ui.class.php:39
#: modules/geo/classes/admin.php:435
msgid "You are not allowed to do this."
msgstr ""

#: modules/geo/classes/admin.php:447
msgid "Please provide a MaxMind license key"
msgstr ""

#. translators: Link to parallax manual
#: modules/parallax-ads/views/placement-options-after.php:24
msgid "Optimized for image ads. %s"
msgstr ""

#: modules/parallax-ads/views/placement-options-after.php:19
msgid "Enable the parallax effect."
msgstr ""

#: modules/rest-api/classes/Admin_UI.php:33
msgid "REST API"
msgstr ""

#: modules/parallax-ads/classes/Advanced_Ads_Pro_Module_Parallax_Admin_UI.php:71
msgid "Parallax Ads"
msgstr ""

#: modules/parallax-ads/views/placement-options-after.php:37
msgctxt "Parallax Ad placement height unit"
msgid "Unit"
msgstr ""

#: modules/parallax-ads/views/placement-options-after.php:31
msgctxt "Parallax Ad placement height"
msgid "Height"
msgstr ""

#: modules/rest-api/classes/Advanced_Ads_Group.php:27
msgid "Invalid group ID."
msgstr ""

#: modules/parallax-ads/views/placement-options-after.php:40
msgid "Choose the height of the cutout, either in pixels or relative to the viewport."
msgstr ""

#: modules/geo/admin/views/visitor-profile.php:20
msgid "Reset Visitor Profile"
msgstr ""

#: modules/geo/admin/views/visitor-profile.php:9
msgid "Location based on your visitor profile cookie:"
msgstr ""

#: modules/geo/classes/admin.php:405
msgid "Location based on your IP address"
msgstr ""

#: modules/advanced-visitor-conditions/main.class.php:203
msgid "Display or hide ads for new visitors."
msgstr ""

#: modules/advanced-visitor-conditions/main.class.php:181
msgid "Display ads based on the value of a cookie. Set the operator to “matches/does not match” and leave the value empty to check only the existence of the cookie."
msgstr ""

#: modules/advanced-display-conditions/main.class.php:88
msgid "Display ads based on the page language set by the WPML plugin."
msgstr ""

#: modules/buddypress/class-buddypress.php:335
#: modules/buddypress/class-buddypress.php:376
msgid "Display ads based on existing BuddyBoss groups."
msgstr ""

#: modules/geo/classes/admin.php:367
msgid "miles (mi)"
msgstr ""

#: modules/geo/classes/admin.php:726
msgid "There was an error connecting to the search service."
msgstr ""

#: modules/geo/classes/admin.php:725
msgid "Your search did not return any results."
msgstr ""

#. translators: 1: A link to the geo location service.
#: modules/geo/classes/admin.php:728
msgid "You can search for the geo coordinates manually at %1$s."
msgstr ""

#. translators: 1: The number of search results.
#: modules/geo/classes/admin.php:724
msgid "Found %1$d results. Please pick the one, you want to use."
msgstr ""

#: modules/geo/classes/admin.php:366
msgid "kilometers (km)"
msgstr ""

#. translators: 1 is the opening link to the plugins page, 2 the closing link
#: modules/geo/classes/Advanced_Ads_Geo_Version_Check.php:51
msgid "The geo-targeting visitor condition moved into Advanced Ads Pro. You can remove Geo Targeting %1$shere%2$s."
msgstr ""

#: modules/geo/classes/api.php:389
msgid "Zimbabwe"
msgstr "Zimbabue"

#: modules/geo/classes/api.php:388
msgid "Zambia"
msgstr "Zambia"

#: modules/geo/classes/admin.php:245
msgid "Your IP address format is incorrect"
msgstr "El formato de tu dirección IP es incorrecto"

#. translators: %s is a URL.
#: modules/cache-busting/admin-ui.class.php:196
msgid "You need a placement to deliver this group using cache busting. <a href=\"%s\" target=\"_blank\">Create a placement now.</a>"
msgstr ""

#: modules/geo/classes/api.php:387
msgid "Yemen"
msgstr "Yemen"

#: modules/geo/classes/api.php:386
msgid "Western Samoa"
msgstr "Samoa Occidental"

#: modules/geo/classes/api.php:385
msgid "Western Sahara"
msgstr "Sahara Occidental"

#: modules/geo/classes/api.php:384
msgid "Wallis and Futuna Islands"
msgstr "Islas Wallis y Futuna"

#: modules/geo/classes/api.php:383
msgid "Virgin Islands (USA)"
msgstr "Islas Vírgenes (USA)"

#: modules/geo/classes/api.php:382
msgid "Virgin Islands (British)"
msgstr "Islas Vírgenes (Británicas)"

#: modules/geo/classes/api.php:381
msgid "Vietnam"
msgstr "Vietnam"

#: modules/geo/classes/api.php:380
msgid "Venezuela"
msgstr "Venezuela"

#: modules/geo/classes/api.php:379
msgid "Vanuatu"
msgstr "Vanuatu"

#: modules/geo/classes/api.php:378
msgid "Uzbekistan"
msgstr "Uzbekistán"

#: modules/geo/classes/api.php:377
msgid "US Minor Outlying Islands"
msgstr "Isla Periférica Menor de los Estados Unidos"

#: modules/geo/classes/api.php:376
msgid "Uruguay"
msgstr "Uruguay"

#: modules/geo/admin/views/setting-download.php:57
msgid "Update geo location databases"
msgstr ""

#: modules/geo/classes/api.php:129
msgid "United States"
msgstr "Estados Unidos"

#: modules/geo/classes/api.php:130
msgid "United Kingdom"
msgstr "Reino Unido"

#: modules/geo/classes/api.php:375
msgid "United Arab Emirates"
msgstr "Emiratos Árabes Unidos"

#: modules/geo/classes/api.php:374
msgid "Ukraine"
msgstr "Ucrania"

#: modules/geo/classes/api.php:373
msgid "Uganda"
msgstr "Uganda"

#: modules/geo/classes/api.php:372
msgid "Tuvalu"
msgstr "Tuvalu"

#: modules/geo/classes/api.php:371
msgid "Turks and Caicos Islands"
msgstr "Islas Turcas y Caicos"

#: modules/geo/classes/api.php:370
msgid "Turkmenistan"
msgstr "Turkmenistán"

#: modules/geo/classes/api.php:369
msgid "Turkey"
msgstr "Turquía"

#: modules/geo/classes/api.php:368
msgid "Tunisia"
msgstr "Túnez"

#: modules/geo/classes/api.php:367
msgid "Trinidad and Tobago"
msgstr "Trinidad y Tobago"

#: modules/geo/classes/api.php:366
msgid "Tonga"
msgstr "Tonga"

#: modules/geo/classes/api.php:365
msgid "Tokelau"
msgstr "Tokelau"

#: modules/geo/classes/api.php:364
msgid "Togo"
msgstr "Togo"

#: modules/geo/classes/api.php:363
msgid "Timor-Leste"
msgstr "Timor-Leste"

#: modules/geo/classes/admin.php:351
msgid "This option can’t be used if your site is using sucuri.net."
msgstr "Esta opción no puede ser usada si tu sitio está usando sucuri.net."

#: modules/geo/classes/admin.php:441
msgid "The upload dir is not available"
msgstr "El directorio de subida no está disponible"

#: modules/geo/admin/views/setting-download.php:31
msgid "The MaxMind license key is missing."
msgstr "Hace falta la clave de la licencia de MaxMind."

#: modules/geo/admin/views/setting-download.php:91
msgid "The databases are updated on the first Tuesday (midnight, GMT) of each month."
msgstr "Las bases de datos son actualizadas el primer martes (medianoche, GMT) de cada mes."

#: modules/geo/classes/api.php:362
msgid "Thailand"
msgstr "Tailandia"

#: modules/geo/classes/api.php:361
msgid "Tanzania"
msgstr "Tanzania"

#: modules/geo/classes/api.php:360
msgid "Tajikistan"
msgstr "Tajikistan"

#: modules/geo/classes/api.php:359
msgid "Taiwan"
msgstr "Taiwán"

#: modules/geo/classes/api.php:358
msgid "Syrian Arab Republic"
msgstr "República Árabe Siria"

#: modules/geo/classes/api.php:357
msgid "Switzerland"
msgstr "Suiza"

#: modules/geo/classes/api.php:356
msgid "Sweden"
msgstr "Suecia"

#: modules/geo/classes/api.php:355
msgid "Swaziland"
msgstr "Swazilandia"

#: modules/geo/classes/api.php:354
msgid "Svalbard and Jan Mayen Islands"
msgstr "Svalbard y Jan Mayen"

#: modules/geo/classes/api.php:353
msgid "Suriname"
msgstr "Surinam"

#: modules/geo/classes/api.php:352
msgid "Sudan"
msgstr "Sudán"

#: modules/geo/classes/plugin.php:130
msgid "Sucuri Header (country only)"
msgstr "Cabecera de Sucuri (solo para el país)"

#: modules/geo/classes/admin.php:333
msgid "State/Region"
msgstr "Estado/Región"

#: modules/geo/classes/api.php:351
msgid "Sri Lanka"
msgstr "Sri Lanka"

#: modules/geo/classes/api.php:350
msgid "Spain"
msgstr "España"

#: modules/geo/classes/api.php:349
msgid "South Sudan"
msgstr "Sudán del Sur"

#: modules/geo/classes/api.php:348
msgid "South Korea"
msgstr "Corea del Sur"

#: modules/geo/classes/api.php:347
msgid "South Georgia"
msgstr "Georgia del Sur"

#: modules/geo/classes/api.php:135
msgid "South America"
msgstr "Sudamérica"

#: modules/geo/classes/api.php:346
msgid "South Africa"
msgstr "Suráfrica"

#: modules/geo/classes/api.php:345
msgid "Somalia"
msgstr "Somalia"

#: modules/geo/classes/api.php:344
msgid "Solomon Islands"
msgstr "Islas Salomón"

#: modules/geo/classes/api.php:343
msgid "Slovenia"
msgstr "Eslovenia"

#: modules/geo/classes/api.php:342
msgid "Slovak Republic"
msgstr "República Eslovaca"

#: modules/geo/classes/api.php:341
msgid "Singapore"
msgstr "Singapur"

#: modules/geo/classes/api.php:340
msgid "Sierra Leone"
msgstr "Sierra Leona"

#: modules/geo/classes/api.php:339
msgid "Seychelles"
msgstr "Seychelles"

#: modules/geo/classes/api.php:338
msgid "Serbia"
msgstr "Serbia"

#: modules/geo/classes/api.php:337
msgid "Senegal"
msgstr "Senegal"

#: modules/geo/classes/admin.php:390
msgid "Search"
msgstr "Buscar"

#: modules/geo/classes/api.php:336
msgid "Saudi Arabia"
msgstr "Arabia Saudita"

#: modules/geo/classes/api.php:334
msgid "San Marino"
msgstr "San Marino"

#: modules/geo/classes/api.php:333
msgid "Saint Vincent and the Grenadines"
msgstr "San Vicente y las Granadinas"

#: modules/geo/classes/api.php:332
msgid "Saint Pierre and Miquelon"
msgstr "San Pedro y Miquelón"

#: modules/geo/classes/api.php:330
msgid "Saint Martin (French)"
msgstr "San Martín (Francesa)"

#: modules/geo/classes/api.php:331
msgid "Saint Martin (Dutch)"
msgstr "San Martín (Holandesa)"

#: modules/geo/classes/api.php:329
msgid "Saint Lucia"
msgstr "Santa Lucía"

#: modules/geo/classes/api.php:328
msgid "Saint Kitts and Nevis"
msgstr "San Cristóbal y Nieves"

#: modules/geo/classes/api.php:327
msgid "Saint Helena"
msgstr "Santa Helena"

#: modules/geo/classes/api.php:326
msgid "Saint Barth&eacute;lemy"
msgstr "San Bartolomé"

#: modules/geo/classes/api.php:335
msgid "S&atilde;o Tom&eacute; and Pr&iacute;ncipe"
msgstr "Santo Tomé y Príncipe"

#: modules/geo/classes/api.php:325
msgid "Rwanda"
msgstr "Ruanda"

#: modules/geo/classes/api.php:324
msgid "Russian Federation"
msgstr "Rusia"

#: modules/geo/classes/api.php:323
msgid "Romania"
msgstr "Rumania"

#: modules/geo/classes/api.php:322
msgid "Reunion Island"
msgstr "Isla Reunión"

#: modules/geo/classes/api.php:321
msgid "Republic of Kosovo"
msgstr "República de Kosovo"

#: modules/geo/classes/api.php:320
msgid "Qatar"
msgstr "Qatar"

#: modules/geo/classes/api.php:319
msgid "Puerto Rico"
msgstr "Puerto Rico"

#: modules/geo/classes/api.php:318
msgid "Portugal"
msgstr "Portugal"

#: modules/geo/classes/api.php:317
msgid "Poland"
msgstr "Polonia"

#. translators: 1: opening <a>-tag to Advanced Ads manual, 2: closing <a>-tag
#: modules/geo/admin/views/setting-download.php:36
#: modules/geo/classes/admin.php:234
msgid "Please read the %1$sinstallation instructions%2$s."
msgstr "Por favor, lee las %1$sintrucciones de instalación%2$s."

#: modules/geo/classes/api.php:316
msgid "Pitcairn Island"
msgstr "Isla Pitcairn"

#: modules/geo/classes/api.php:315
msgid "Phillipines"
msgstr "Filipinas"

#: modules/geo/classes/api.php:314
msgid "Peru"
msgstr "Perú"

#: modules/geo/classes/api.php:313
msgid "Paraguay"
msgstr "Paraguay"

#: modules/geo/classes/api.php:312
msgid "Papua New Guinea"
msgstr "Papúa Nueva Guinea"

#: modules/geo/classes/api.php:311
msgid "Panama"
msgstr "Panamá"

#: modules/geo/classes/api.php:310
msgid "Palestinian Territories"
msgstr "Territorios Palestinos"

#: modules/geo/classes/api.php:309
msgid "Palau"
msgstr "Palau"

#: modules/geo/classes/api.php:308
msgid "Pakistan"
msgstr "Pakistán"

#: modules/geo/classes/admin.php:332
#: modules/geo/classes/admin.php:334
msgid "or"
msgstr "o"

#: modules/geo/classes/api.php:307
msgid "Oman"
msgstr "Omán"

#: modules/geo/classes/api.php:139
msgid "Oceania"
msgstr "Oceania"

#: modules/geo/classes/api.php:306
msgid "Norway"
msgstr "Noruega"

#: modules/geo/classes/api.php:305
msgid "Northern Mariana Islands"
msgstr "Islas Marianas del Norte"

#: modules/geo/classes/api.php:304
msgid "North Korea"
msgstr "Corea del Norte"

#: modules/geo/classes/api.php:134
msgid "North America"
msgstr "Norteamérica"

#: modules/geo/classes/api.php:303
msgid "Norfolk Island"
msgstr "Isla Norfolk"

#: modules/geo/classes/api.php:302
msgid "Niue"
msgstr "Niue"

#: modules/geo/classes/api.php:301
msgid "Nigeria"
msgstr "Nigeria"

#: modules/geo/classes/api.php:300
msgid "Niger"
msgstr "Niger"

#: modules/geo/classes/api.php:299
msgid "Nicaragua"
msgstr "Nicaragua"

#. translators: Timestamp in the localized date_format
#: modules/geo/admin/views/setting-download.php:84
msgid "Next possible update on %s."
msgstr "La próxima actualización será posible en %s."

#: modules/geo/classes/api.php:298
msgid "New Zealand"
msgstr "Nueva Zelanda"

#: modules/geo/classes/api.php:297
msgid "New Caledonia"
msgstr "Nueva Caledonia"

#: modules/geo/classes/api.php:296
msgid "Netherlands Antilles"
msgstr "Antillas Holandesas"

#: modules/geo/classes/api.php:295
msgid "Netherlands"
msgstr "Holanda"

#: modules/geo/classes/api.php:294
msgid "Nepal"
msgstr "Nepal"

#: modules/geo/classes/api.php:293
msgid "Nauru"
msgstr "Nauru"

#: modules/geo/classes/api.php:292
msgid "Namibia"
msgstr "Namibia"

#: modules/geo/classes/api.php:291
msgid "Myanmar"
msgstr "Myanmar"

#: modules/geo/classes/api.php:290
msgid "Mozambique"
msgstr "Mozambique"

#: modules/geo/classes/api.php:289
msgid "Morocco"
msgstr "Marruecos"

#: modules/geo/classes/api.php:288
msgid "Montserrat"
msgstr "Montserrat"

#: modules/geo/classes/api.php:287
msgid "Montenegro"
msgstr "Montenegro"

#: modules/geo/classes/api.php:286
msgid "Mongolia"
msgstr "Mongolia"

#: modules/geo/classes/api.php:285
msgid "Monaco"
msgstr "Mónaco"

#: modules/geo/classes/api.php:284
msgid "Moldova, Republic of"
msgstr "República de Moldavia"

#: modules/geo/classes/api.php:283
msgid "Micronesia"
msgstr "Micronesia"

#: modules/geo/classes/api.php:282
msgid "Mexico"
msgstr "México"

#: modules/geo/classes/admin.php:76
msgid "Method"
msgstr "Método"

#: modules/geo/classes/api.php:281
msgid "Mayotte"
msgstr "Mayotte"

#: modules/geo/classes/admin.php:241
msgid "Maybe you are working on a local or secured environment."
msgstr "Tal vez estás trabajando en un entorno seguro o local."

#: modules/geo/classes/admin.php:57
msgid "MaxMind license key"
msgstr "Clave de licencia de MaxMind"

#: modules/geo/classes/plugin.php:124
msgid "MaxMind database (default)"
msgstr "Base de datos MaxMind (por defecto)"

#: modules/geo/classes/admin.php:66
msgid "MaxMind Database"
msgstr "Base de datos de MaxMind"

#: modules/geo/classes/api.php:280
msgid "Mauritius"
msgstr "Mauricio"

#: modules/geo/classes/api.php:279
msgid "Mauritania"
msgstr "Mauritania"

#: modules/geo/classes/api.php:278
msgid "Martinique"
msgstr "Martinica"

#: modules/geo/classes/api.php:277
msgid "Marshall Islands"
msgstr "Islas Marshall"

#: modules/geo/classes/api.php:276
msgid "Malta"
msgstr "Malta"

#: modules/geo/classes/api.php:275
msgid "Mali"
msgstr "Mali"

#: modules/geo/classes/api.php:274
msgid "Maldives"
msgstr "Maldivas"

#: modules/geo/classes/api.php:273
msgid "Malaysia"
msgstr "Malasia"

#: modules/geo/classes/api.php:272
msgid "Malawi"
msgstr "Malawi"

#: modules/geo/classes/api.php:271
msgid "Madagascar"
msgstr "Madagascar"

#: modules/geo/classes/api.php:270
msgid "Macedonia"
msgstr "Macedonia"

#: modules/geo/classes/api.php:269
msgid "Macau"
msgstr "Macau"

#: modules/geo/classes/api.php:268
msgid "Luxembourg"
msgstr "Luxemburgo"

#: modules/geo/classes/admin.php:373
msgid "Longitude"
msgstr "Longitud"

#: modules/geo/classes/api.php:267
msgid "Lithuania"
msgstr "Lituania"

#: modules/geo/classes/api.php:266
msgid "Liechtenstein"
msgstr "Lichtenstein"

#: modules/geo/classes/api.php:265
msgid "Libyan Arab Jamahiriya"
msgstr "Jamahiriya Árabe Libia"

#: modules/geo/classes/api.php:264
msgid "Liberia"
msgstr "Liberia"

#: modules/geo/classes/api.php:263
msgid "Lesotho"
msgstr "Lesoto"

#: modules/geo/classes/api.php:262
msgid "Lebanon"
msgstr "Líbano"

#: modules/geo/classes/api.php:261
msgid "Latvia"
msgstr "Letonia"

#: modules/geo/classes/admin.php:372
msgid "Latitude"
msgstr "Latitud"

#. translators: Timestamp in the localized date_format
#: modules/geo/admin/views/setting-download.php:73
msgid "Last update: %s"
msgstr "Última actualización: %s"

#: modules/geo/classes/api.php:260
msgid "Lao People's Democratic Republic"
msgstr "República Democrática de Laos"

#: modules/geo/classes/admin.php:86
msgid "Language of names"
msgstr "Idioma de los nombres"

#: modules/geo/classes/api.php:259
msgid "Kyrgyzstan"
msgstr "Kirguistán"

#: modules/geo/classes/api.php:258
msgid "Kuwait"
msgstr "Kuwait"

#: modules/geo/classes/api.php:257
msgid "Kiribati"
msgstr "Kiribati"

#: modules/geo/classes/api.php:256
msgid "Kenya"
msgstr "Kenia"

#: modules/geo/classes/api.php:255
msgid "Kazakhstan"
msgstr "Kazajstán"

#: modules/geo/classes/api.php:254
msgid "Jordan"
msgstr "Jordania"

#: modules/geo/classes/api.php:253
msgid "Jersey"
msgstr "Jersey"

#: modules/geo/classes/api.php:252
msgid "Japan"
msgstr "Japón"

#: modules/geo/classes/api.php:251
msgid "Jamaica"
msgstr "Jamaica"

#: modules/geo/classes/api.php:250
msgid "Italy"
msgstr "Italia"

#. translators: %s is a URL.
#: modules/cache-busting/admin-ui.class.php:218
msgid "It seems that a caching plugin is activated. Your ads might not rotate properly, while cache busting is disabled for the placement your group is using. <a href=\"#\" data-placement=\"%s\" class=\"js-placement-activate-cb\">Activate cache busting for this placement.</a>"
msgstr ""

#. translators: %s is a URL.
#: modules/cache-busting/admin-ui.class.php:173
msgid "It seems that a caching plugin is activated. Your ads might not rotate properly while cache busting is disabled. <a href=\"%s\" target=\"_blank\">Activate cache busting.</a>"
msgstr ""

#: modules/geo/classes/api.php:249
msgid "Israel"
msgstr "Israel"

#: modules/geo/classes/api.php:248
msgid "Isle of Man"
msgstr "Isla de Pascua"

#: modules/geo/classes/api.php:247
msgid "Ireland"
msgstr "Irlanda"

#: modules/geo/classes/api.php:246
msgid "Iraq"
msgstr "Irak"

#: modules/geo/classes/api.php:245
msgid "Iran"
msgstr "Irán"

#: modules/geo/classes/api.php:244
msgid "Indonesia"
msgstr "Indonesia"

#: modules/geo/classes/api.php:243
msgid "India"
msgstr "India"

#: modules/geo/admin/views/setting-download.php:50
msgid "In order to use Geo Targeting, please download the geo location databases by clicking on the button below."
msgstr ""

#: modules/click-fraud-protection/click-fraud-protection.class.php:156
msgid "Ignore global Click Fraud Protection"
msgstr ""

#: modules/geo/classes/api.php:242
msgid "Iceland"
msgstr "Islandia"

#: modules/geo/classes/api.php:241
msgid "Hungary"
msgstr "Hungría"

#: modules/geo/classes/api.php:240
msgid "Hong Kong"
msgstr "Hong Kong"

#: modules/geo/classes/api.php:239
msgid "Honduras"
msgstr "Honduras"

#: modules/geo/classes/api.php:238
msgid "Holy See (City Vatican State)"
msgstr "Ciudad del Vaticano"

#: modules/geo/classes/api.php:237
msgid "Heard and McDonald Islands"
msgstr "Islas Heard y McDonald"

#: modules/geo/classes/api.php:236
msgid "Haiti"
msgstr "Haití"

#: modules/geo/classes/api.php:235
msgid "Guyana"
msgstr "Guyana"

#: modules/geo/classes/api.php:234
msgid "Guinea-Bissau"
msgstr "Guinea-Bissau"

#: modules/geo/classes/api.php:233
msgid "Guinea"
msgstr "Guinea"

#: modules/geo/classes/api.php:232
msgid "Guernsey"
msgstr "Guernsey"

#: modules/geo/classes/api.php:231
msgid "Guatemala"
msgstr "Guatemala"

#: modules/geo/classes/api.php:230
msgid "Guam"
msgstr "Guam"

#: modules/geo/classes/api.php:229
msgid "Guadeloupe"
msgstr "Guadalupe"

#: modules/geo/classes/api.php:228
msgid "Grenada"
msgstr "Granada"

#: modules/geo/classes/api.php:227
msgid "Greenland"
msgstr "Groenlandia"

#: modules/geo/classes/api.php:224
msgid "Greece"
msgstr "Grecia"

#: modules/geo/classes/api.php:226
msgid "Gibraltar"
msgstr "Gibraltar"

#: modules/geo/classes/api.php:225
msgid "Ghana"
msgstr "Ghana"

#: modules/geo/classes/admin.php:370
msgid "get coordinates"
msgstr "obtener las coordenadas"

#: modules/geo/classes/api.php:132
msgid "Germany"
msgstr "Alemania"

#: modules/geo/classes/api.php:223
msgid "Georgia"
msgstr "Georgia"

#: modules/geo/classes/admin.php:49
msgid "Geo Targeting"
msgstr "Geo Targeting"

#: modules/geo/classes/plugin.php:105
msgid "geo location"
msgstr "ubicación geográfica"

#: modules/geo/admin/views/setting-download.php:47
#: modules/geo/classes/admin.php:231
msgid "Geo Databases not found."
msgstr ""

#: modules/geo/classes/public.php:222
msgid "Geo Databases not found"
msgstr ""

#: modules/geo/admin/views/setting-download.php:16
msgid "Geo Databases found."
msgstr ""

#: modules/geo/classes/api.php:222
msgid "Gambia"
msgstr "Gambia"

#: modules/geo/classes/api.php:221
msgid "Gabon"
msgstr "Gabón"

#: modules/geo/classes/admin.php:368
msgid "from"
msgstr "desde"

#: modules/geo/classes/api.php:220
msgid "French Southern Territories"
msgstr "Territorios Sureños Franceses"

#: modules/geo/classes/api.php:219
msgid "French Polynesia"
msgstr "Polinesia Francesa"

#: modules/geo/classes/api.php:218
msgid "French Guiana"
msgstr "Guyana Francesa"

#: modules/geo/classes/api.php:217
msgid "France"
msgstr "Francia"

#: modules/geo/classes/api.php:216
msgid "Finland"
msgstr "Finlandia"

#: modules/geo/classes/api.php:215
msgid "Fiji"
msgstr "Fiji"

#: modules/geo/classes/api.php:214
msgid "Faroe Islands"
msgstr "Islas Faroe"

#: modules/geo/classes/api.php:213
msgid "Falkland Islands"
msgstr "Islas Malvinas"

#: modules/geo/classes/api.php:131
msgid "European Union"
msgstr "Unión Europea"

#: modules/geo/classes/api.php:136
msgid "Europe"
msgstr "Europa"

#: modules/geo/classes/api.php:212
msgid "Ethiopia"
msgstr "Etiopía"

#: modules/geo/classes/api.php:211
msgid "Estonia"
msgstr "Estonia"

#: modules/geo/classes/api.php:210
msgid "Eritrea"
msgstr "Eritrea"

#: modules/geo/classes/api.php:208
msgid "Equatorial Guinea"
msgstr "Guinea Ecuatorial"

#: modules/geo/classes/admin.php:393
msgid "Enter the name of the city, click the search button and pick one of the results to set the coordinates of the center."
msgstr "Introduce el nombre de la ciudad, haz clic en el botón de búsqueda y elige uno de los resultados para establecer las coordenadas del centro."

#: modules/geo/classes/api.php:209
msgid "El Salvador"
msgstr "El Salvador"

#: modules/geo/classes/api.php:207
msgid "Egypt"
msgstr "Egipto"

#: modules/geo/classes/api.php:206
msgid "Ecuador"
msgstr "Ecuador"

#: modules/geo/classes/api.php:205
msgid "East Timor"
msgstr "Timor Oriental"

#: modules/geo/classes/api.php:204
msgid "Dominican Republic"
msgstr "República Dominicana"

#: modules/geo/classes/api.php:203
msgid "Dominica"
msgstr "Dominica"

#: modules/geo/classes/api.php:202
msgid "Djibouti"
msgstr "Yibuti"

#: modules/geo/classes/admin.php:378
msgid "Distance to center"
msgstr "Distancia al centro"

#: modules/geo/classes/admin.php:361
msgid "Distance is less than or equal to"
msgstr "La distancia es menor o igual a"

#: modules/geo/classes/admin.php:362
msgid "Distance is greater than"
msgstr "La distancia es mayor que"

#: modules/geo/classes/admin.php:364
msgid "Distance"
msgstr "Distancia"

#: modules/geo/classes/plugin.php:106
msgid "Display ads based on geo location."
msgstr "Mostrar anuncios basados en la Geo Locación."

#: modules/geo/classes/api.php:201
msgid "Denmark"
msgstr "Dinamarca"

#: modules/geo/classes/admin.php:518
msgid "Database updated successfully!"
msgstr "¡La base de datos se ha actualizado correctamente!"

#: modules/geo/classes/admin.php:536
msgid "Database update failed"
msgstr "La actualización de la base de datos ha fallado"

#: modules/geo/classes/api.php:200
msgid "Czech Republic"
msgstr "República Checa"

#: modules/geo/classes/api.php:199
msgid "Cyprus"
msgstr "Chipre"

#: modules/geo/classes/api.php:198
msgid "Cura&Ccedil;ao"
msgstr "Curazao"

#: modules/geo/classes/api.php:197
msgid "Cuba"
msgstr "Cuba"

#: modules/geo/classes/api.php:196
msgid "Croatia/Hrvatska"
msgstr "Croacia/Hrvatska"

#. translators: an error mesage.
#: modules/geo/classes/admin.php:573
msgid "Could not open downloaded database for reading: %s"
msgstr "No se ha podido abrir la base de datos descargada para su lectura: %s"

#. translators: MaxMind database file name.
#: modules/geo/classes/admin.php:602
msgid "Could not open database for writing %s"
msgstr "No se ha podido abrir la base de datos para su escritura %s"

#: modules/geo/classes/admin.php:589
msgid "Could not access filesystem"
msgstr "No se ha podido acceder al sistema de archivos."

#: modules/geo/classes/api.php:195
msgid "Cote d'Ivoire"
msgstr "Costa de Marfil"

#: modules/geo/classes/api.php:194
msgid "Costa Rica"
msgstr "Costa Rica"

#: modules/geo/admin/views/visitor-profile.php:15
#: modules/geo/classes/admin.php:252
msgid "Coordinates"
msgstr "Coordenadas"

#: modules/geo/classes/api.php:193
msgid "Cook Islands"
msgstr "Islas Cook"

#: modules/geo/classes/api.php:192
msgid "Congo, Republic of"
msgstr "República del Congo"

#: modules/geo/classes/api.php:191
msgid "Congo, Democratic People's Republic"
msgstr "República Democrática del Congo"

#: modules/geo/classes/api.php:190
msgid "Comoros"
msgstr "Comoras"

#: modules/geo/classes/api.php:189
msgid "Colombia"
msgstr "Colombia"

#: modules/geo/classes/api.php:188
msgid "Cocos Islands"
msgstr "Islas Cocos"

#: modules/geo/classes/admin.php:335
#: modules/geo/classes/admin.php:388
msgid "City"
msgstr "Ciudad"

#: modules/geo/classes/api.php:187
msgid "Christmas Island"
msgstr "Isla de Navidad"

#: modules/geo/admin/views/setting-locale.php:15
msgid "Choose the language of the state/region or city entered. If the language is not available in the geo location database, it will check against the English version."
msgstr "Elige el lenguaje para el estado/región o ciudad introducida. Si el lenguaje no está disponible en la base de datos geográficos, entonces lo comparará con la versión en inglés."

#: modules/geo/classes/api.php:186
msgid "China"
msgstr "China"

#: modules/geo/classes/api.php:185
msgid "Chile"
msgstr "Chile"

#: modules/geo/classes/api.php:184
msgid "Chad"
msgstr "Chad"

#: modules/geo/classes/api.php:183
msgid "Central African Republic"
msgstr "República Central Africana"

#: modules/geo/classes/api.php:182
msgid "Cayman Islands"
msgstr "Islas Caimán"

#: modules/geo/classes/api.php:181
msgid "Cape Verde"
msgstr "Cabo Verde"

#: modules/geo/classes/admin.php:391
msgid "Cancel"
msgstr "Cancelar"

#: modules/geo/classes/api.php:180
msgid "Canada"
msgstr "Canadá"

#: modules/geo/classes/api.php:179
msgid "Cameroon"
msgstr "Camerún"

#: modules/geo/classes/api.php:178
msgid "Cambodia"
msgstr "Camboya"

#: modules/geo/classes/admin.php:288
msgid "by specific location"
msgstr "por ubicación específica"

#: modules/geo/classes/admin.php:294
msgid "by radius"
msgstr "por radio"

#: modules/geo/classes/api.php:177
msgid "Burundi"
msgstr "Burundi"

#: modules/geo/classes/api.php:176
msgid "Burkina Faso"
msgstr "Burkina Faso"

#: modules/geo/classes/api.php:175
msgid "Bulgaria"
msgstr "Bulgaria"

#: modules/geo/classes/api.php:174
msgid "Brunei Darrussalam"
msgstr "Brunei Darrussalam"

#: modules/geo/classes/api.php:173
msgid "British Indian Ocean Territory"
msgstr "Territorio Británico del Océano Índico"

#: modules/geo/classes/api.php:172
msgid "Brazil"
msgstr "Brasil"

#: modules/geo/classes/api.php:171
msgid "Bouvet Island"
msgstr "Isla Bouvet"

#: modules/geo/classes/api.php:170
msgid "Botswana"
msgstr "Botswana"

#: modules/geo/classes/api.php:169
msgid "Bosnia and Herzegovina"
msgstr "Bosnia-Herzegovina"

#: modules/geo/classes/api.php:168
msgid "Bonaire, Saint Eustatius and Saba"
msgstr "Bonaire, Saint Eustatius y Saba"

#: modules/geo/classes/api.php:167
msgid "Bolivia"
msgstr "Bolivia"

#: modules/geo/classes/api.php:166
msgid "Bhutan"
msgstr "Bhután"

#: modules/geo/classes/api.php:165
msgid "Bermuda"
msgstr "Bermuda"

#: modules/geo/classes/api.php:164
msgid "Benin"
msgstr "Benin"

#: modules/geo/classes/api.php:163
msgid "Belize"
msgstr "Belice"

#: modules/geo/classes/api.php:162
msgid "Belgium"
msgstr "Bélgica"

#: modules/geo/classes/api.php:161
msgid "Belarus"
msgstr "Bielorrusia"

#: modules/geo/classes/api.php:160
msgid "Barbados"
msgstr "Barbados"

#: modules/geo/classes/api.php:159
msgid "Bangladesh"
msgstr "Bangladesh"

#: modules/geo/classes/api.php:158
msgid "Bahrain"
msgstr "Bahrein"

#: modules/geo/classes/api.php:157
msgid "Bahamas"
msgstr "Bahamas"

#: modules/geo/classes/api.php:156
msgid "Azerbaijan"
msgstr "Azerbaijan"

#: modules/geo/classes/api.php:155
msgid "Austria"
msgstr "Austria"

#: modules/geo/classes/api.php:154
msgid "Australia"
msgstr "Australia"

#: modules/geo/classes/api.php:138
msgid "Asia"
msgstr "Asia"

#: modules/geo/classes/api.php:153
msgid "Aruba"
msgstr "Aruba"

#: modules/geo/classes/api.php:152
msgid "Armenia"
msgstr "Armenia"

#: modules/geo/classes/api.php:151
msgid "Argentina"
msgstr "Argentina"

#: modules/geo/classes/api.php:150
msgid "Antigua and Barbuda"
msgstr "Antigua y Barbados"

#: modules/geo/classes/api.php:149
msgid "Antarctica"
msgstr "Antártica"

#: modules/geo/classes/api.php:148
msgid "Anguilla"
msgstr "Anguila"

#: modules/geo/classes/api.php:147
msgid "Angola"
msgstr "Angola"

#: modules/geo/classes/api.php:146
msgid "Andorra"
msgstr "Andorra"

#: modules/geo/classes/api.php:145
msgid "American Samoa"
msgstr "Samoa Americana"

#: modules/geo/classes/api.php:144
msgid "Algeria"
msgstr "Argelia"

#: modules/geo/classes/api.php:143
msgid "Albania"
msgstr "Albania"

#: modules/geo/classes/api.php:137
msgid "Africa"
msgstr "Africa"

#: modules/geo/classes/api.php:141
msgid "Afghanistan"
msgstr "Afganistán"

#. translators: an error message.
#: modules/geo/classes/public.php:227
msgid "Address not found: %s"
msgstr "No se ha encontrado la dirección: %s"

#: modules/group-refresh/views/settings_group_refresh.php:21
#: views/admin/tables/ads/adsense-fallback.php:77
msgid "Activate now"
msgstr ""

#: modules/geo/classes/admin.php:219
#: modules/geo/classes/public.php:208
#: modules/geo/classes/public.php:372
msgid "(unknown region)"
msgstr "(región desconocida)"

#: modules/geo/classes/admin.php:208
#: modules/geo/classes/public.php:200
#: modules/geo/classes/public.php:368
msgid "(unknown city)"
msgstr "(ciudad desconocida)"

#: modules/geo/classes/api.php:142
msgid "&#197;land Islands"
msgstr "Islas Åland"

#: modules/lazy-load/admin.class.php:23
#: modules/lazy-load/admin.class.php:64
#: views/admin/placements/bulk-edit.php:27
msgid "Lazy Loading"
msgstr ""

#: views/setting-placement-positioning.php:29
msgid "This method also works on AMP pages and causes fewer conflicts with website optimization features. However, it can cause critical issues with a few other plugins that use a similar technique (i.e., output buffering). We recommend less technical users test it carefully."
msgstr ""

#: classes/advanced-ads-pro-admin.php:148
msgid "Placement positioning"
msgstr ""

#. translators: %s: Names of placement types
#: views/setting-placement-positioning.php:13
msgid "Choose when Advanced Ads will add the following placement types: %s"
msgstr ""

#: views/setting-placement-positioning.php:24
msgid "Before page load using PHP."
msgstr ""

#: views/setting-placement-positioning.php:19
msgid "After page load using JavaScript"
msgstr ""

#: modules/advanced-display-conditions/main.class.php:116
msgid "post content"
msgstr ""

#: modules/advanced-display-conditions/main.class.php:117
msgid "Display ads based on words and phrases within the post or page content. Dynamically added text might not be considered."
msgstr ""

#: modules/gamipress/views/visitor-condition-rank.php:28
msgid "Choose rank"
msgstr ""

#: modules/gamipress/views/visitor-condition-points.php:17
msgid "Choose Points Type"
msgstr ""

#: modules/gamipress/views/visitor-condition-achievement.php:21
msgid "Choose Achievement Type"
msgstr ""

#: modules/gamipress/admin.class.php:34
msgid "Display ads based on GamiPress user achievements."
msgstr ""

#: modules/gamipress/admin.class.php:33
msgid "GamiPress Achievement"
msgstr ""

#: modules/gamipress/admin.class.php:28
msgid "Display ads based on GamiPress user ranks."
msgstr ""

#: modules/gamipress/admin.class.php:27
msgid "GamiPress Rank"
msgstr ""

#: modules/gamipress/admin.class.php:22
msgid "Display ads based on GamiPress user points."
msgstr ""

#: modules/gamipress/admin.class.php:21
msgid "GamiPress Points"
msgstr ""

#: views/privacy-policy-content.php:10
msgid "You can use the text below as a template for your own privacy policy."
msgstr ""

#: views/privacy-policy-content.php:13
msgid "This website uses Advanced Ads Pro to place advertisements. The WordPress plugin may use multiple first-party cookies to ensure the correct integration of ads. These cookies store technical information but not IP addresses. Their use is linked to specific features and options when embedding ads."
msgstr ""

#: views/privacy-policy-content.php:12
msgid "Suggested Text:"
msgstr ""

#. translators: %1$s is an opening a tag, %2$s is the corresponding closing one
#: views/privacy-policy-content.php:19
msgid "Please, see the %1$sAdvanced Ads cookie information%2$s for more details."
msgstr ""

#: views/privacy-policy-content.php:7
msgid "Depending on the setup, Advanced Ads Pro and other add-ons use cookies to control which user sees which ad. They also help to reduce expensive server requests."
msgstr ""

#: classes/advanced-ads-pro-admin.php:531
msgid "Select a Sidebar placement to enable cache-busting."
msgstr ""

#: modules/buddypress/views/position-option.php:56
msgid "member timeline"
msgstr ""

#: classes/advanced-ads-pro-admin.php:533
msgid "Learn more"
msgstr ""

#: modules/buddypress/views/position-option.php:55
msgid "group feed"
msgstr ""

#: modules/buddypress/class-buddypress.php:334
msgid "BuddyBoss group"
msgstr ""

#: modules/buddypress/views/position-option.php:53
msgid "any"
msgstr ""

#: modules/buddypress/views/position-option.php:54
msgid "activity stream"
msgstr ""

#: modules/cache-busting/views/settings.php:38
msgid "Force passive cache busting"
msgstr ""

#: modules/cache-busting/views/settings.php:61
msgid "Fallback option"
msgstr ""

#: modules/cache-busting/views/settings.php:17
msgid "Default option"
msgstr ""

#: modules/buddypress/class-buddypress.php:131
msgid "Display ads based on BuddyBoss profile fields."
msgstr ""

#: modules/buddypress/class-buddypress.php:130
msgid "BuddyBoss profile field"
msgstr "Campo de perfil de BuddyBoss"

#: modules/buddypress/views/position-option.php:51
msgid "Stream"
msgstr "Cronología de actividad"

#: includes/placements/types/class-buddypress.php:61
msgid "Display ads on BuddyBoss related pages."
msgstr "Mostrar anuncios en páginas relacionadas con BuddyBoss."

#: includes/placements/types/class-buddypress.php:49
msgid "BuddyBoss Content"
msgstr "Contenido de BuddyBoss"

#: modules/cache-busting/views/settings.php:42
msgid "By default, cache busting only works through placements."
msgstr ""

#: modules/cache-busting/views/settings.php:90
msgid "Update visitor profile"
msgstr "Actualizar el perfil del visitante"

#: modules/ad-server/views/placement-usage.php:14
msgid "Direct URL"
msgstr "URL directa"

#: modules/ad-server/views/placement-settings.php:11
msgid "The name of the placement that appears in the URL and injection code."
msgstr "El nombre de la ubicación que aparece en la URL y el código de inyección."

#: modules/ad-server/views/placement-settings.php:10
msgid "Save the page to update the usage code below."
msgstr "Guarda la página para actualizar el código de uso de abajo."

#: modules/ad-server/views/module-settings.php:30
msgid "Prevent direct access to the placement URL."
msgstr "Impedir el acceso directo a la URL de la ubicación."

#: modules/ad-server/views/module-settings.php:24
msgid "Please don’t enter subdirectories."
msgstr "Por favor, no introduzcas subdirectorios."

#: modules/ad-server/views/module-settings.php:18
msgid "Separate multiple values with a comma."
msgstr "Separa los valores múltiples con una coma."

#: modules/ad-server/views/module-settings.php:18
msgid "Top level domains on which the ads will be loaded."
msgstr "Dominios de primer nivel en los que se cargarán los anuncios."

#: modules/ad-server/views/module-settings.php:13
#: modules/ads-for-adblockers/views/settings.php:18
#: modules/advanced-visitor-conditions/views/settings.php:6
#: modules/cache-busting/views/settings.php:12
#: modules/click-fraud-protection/views/settings.php:10
#: modules/lazy-load/views/settings.php:8
#: modules/rest-api/views/module-enable.php:12
msgid "Activate module."
msgstr "Activar el módulo."

#: modules/ad-server/admin.class.php:92
msgid "Usage"
msgstr "Uso"

#: modules/ad-server/admin.class.php:78
msgid "Public string"
msgstr "Cadena pública"

#: includes/placements/types/class-server.php:56
msgid "Display ads on external websites."
msgstr "Mostrar anuncios en sitios web externos."

#: includes/placements/types/class-server.php:47
#: modules/ad-server/admin.class.php:37
msgid "Ad Server"
msgstr "Servidor de anuncios"

#: modules/advanced-visitor-conditions/main.class.php:149
msgid " Display ads based on the user’s capabilities."
msgstr ""

#: modules/advanced-visitor-conditions/main.class.php:141
msgid "Display ads based on the user agent."
msgstr "Mostrar anuncios basados en el agente de usuario."

#: modules/inject-content/admin.class.php:255
msgid "Words between ads"
msgstr "Palabras entre anuncios"

#: views/setting_words_between_ads.php:6
msgid "Words are counted within paragraphs, headlines and any other element."
msgstr "Las palabras se cuentan dentro de los párrafos, titulares y cualquier otro elemento."

#: views/setting_words_between_ads.php:5
msgid "A minimum amount of words between automatically injected ads."
msgstr "Una cantidad mínima de palabras entre anuncios inyectados automáticamente."

#: includes/admin/class-duplicate-placement.php:64
#: includes/admin/class-group-duplication.php:115
#: modules/duplicate-ads/admin.class.php:138
msgctxt "noun"
msgid "copy"
msgstr "copia"

#: modules/inject-content/admin.class.php:124
msgid "Minimum length of content (in words) before automatically injected ads are allowed in them. Set to zero or leave empty to always display ads, regardless of how long the content is."
msgstr "Se permite una longitud mínima de contenido (en palabras) antes de que se inyecten automáticamente los anuncios en ellos. Establécelo en cero o déjalo vacío para mostrar siempre los anuncios, independientemente de la longitud del contenido."

#: includes/admin/class-duplicate-placement.php:102
#: includes/admin/class-group-duplication.php:78
#: modules/duplicate-ads/admin.class.php:88
msgid "Duplicate"
msgstr "Duplicar"

#: modules/duplicate-ads/admin.class.php:88
msgid "Create a copy of this ad"
msgstr "Crear una copia de este anuncio"

#: modules/inject-content/views/setting_post_meta_box.php:4
msgid "Disable automatic ad injection into the content"
msgstr "Desactivar la inyección automática de anuncios en el contenido"

#: modules/cache-busting/views/settings.php:102
msgid "Update visitor profile when user logs in or out"
msgstr "Actualizar el perfil del visitante cuando el usuario entra o sale"

#: modules/cache-busting/views/settings.php:92
msgid "You might need to update the page cache if you are using one."
msgstr "Es posible que tengas que actualizar la memoria caché de la página si está usando una."

#: modules/cache-busting/views/settings.php:86
msgid "Advanced Ads stores some user information in the user’s browser to limit the number of AJAX requests for cache busting. Manual"
msgstr ""

#: modules/cache-busting/views/settings.php:83
msgid "Visitor profile"
msgstr "Perfil del visitante"

#. translators: %s: URL
#: modules/advanced-display-conditions/main.class.php:41
msgid "Display ads based on the current URL parameters (everything following %s), except values following #."
msgstr "Mostrar anuncios basados en los parámetros actuales de la URL (todo lo que sigue a %s), excepto los valores que siguen a #."

#: classes/advanced-ads-pro-admin.php:156
msgid "Disable ads for post types"
msgstr "Desactivar anuncios para tipos de contenido"

#: modules/cache-busting/views/settings.php:94
msgid "An error occurred"
msgstr ""

#: modules/cache-busting/views/settings.php:91
msgid "Updated"
msgstr "Actualizado"

#. translators: %s is a placeholder for the URL.
#: modules/advanced-visitor-conditions/main.class.php:163
msgid "Display ads based on the user's roles. See <a href=\"%s\" target=\"_blank\">List of roles in WordPress</a>."
msgstr ""

#: modules/advanced-visitor-conditions/main.class.php:160
msgid "user role"
msgstr "perfil de usuario"

#. translators: %s is a list of supported optimizer plugins
#: views/setting_autoptimize.php:7
msgid "Advanced Ads Pro disables optimizers ( %s ) for displaying ads per default. Enable this option to allow optimizers to change the ad code. Especially JavaScript ads might stop working then."
msgstr "Advanced Ads Pro desactiva los optimizadores (%s) para mostrar los anuncios por defecto. Activa esta opción para permitir que los optimizadores cambien el código del anuncio. Los anuncios en JavaScript en especial podrían dejar de funcionar."

#: classes/advanced-ads-pro-admin.php:139
msgid "Allow optimizers to modify ad codes"
msgstr "Permitir que los optimizadores modifiquen los códigos de anuncios"

#. translators: %s: input field
#: modules/lazy-load/views/settings.php:34
msgid "Start loading the ads %s pixels before they are visible on the screen."
msgstr "Empieza a cargar los anuncios %s píxeles antes de que sean visibles en la pantalla."

#: modules/grids/admin.class.php:61
msgid "Set to 0 to disable this feature."
msgstr "Establece 0 para desactivar esta característica."

#: modules/grids/admin.class.php:60
msgid "On browsers smaller than this, the columns show in full width below each other."
msgstr "En los navegadores más pequeños, las columnas se muestran a todo lo ancho, una debajo de la otra."

#: modules/grids/admin.class.php:58
msgid "Full width"
msgstr "Ancho completo"

#. translators: "profile fields" relates to BuddyPress profile fields
#: modules/buddypress/views/xprofile-condition.php:58
msgid "No profile fields found"
msgstr "No se han encontrado campos de perfil"

#: modules/buddypress/class-buddypress.php:118
msgid "Display ads based on BuddyPress profile fields."
msgstr ""

#: modules/buddypress/class-buddypress.php:117
msgid "BuddyPress profile field"
msgstr "Campo de perfil de BuddyPress"

#: modules/buddypress/views/position-option.php:44
#: views/setting_repeat.php:4
msgid "repeat the position"
msgstr "repetir la posición"

#: views/setting_custom_code.php:34
msgid "Displayed after the ad content"
msgstr "Se muestra después del contenido del anuncio"

#: views/setting_custom_code.php:30
msgid "place your own code below the ad"
msgstr "coloca tu código personalizado debajo del anuncio"

#: views/setting_custom_code.php:27
msgid "custom code"
msgstr "codigo personalizado"

#. Author URI of the plugin
#: advanced-ads-pro.php
msgid "https://wpadvancedads.com"
msgstr "https://wpadvancedads.com"

#. Plugin URI of the plugin
#: advanced-ads-pro.php
msgid "https://wpadvancedads.com/add-ons/advanced-ads-pro/"
msgstr "https://wpadvancedads.com/add-ons/advanced-ads-pro/"

#. Description of the plugin
#: advanced-ads-pro.php
msgid "Advanced features to boost your ad revenue."
msgstr "Características avanzadas para incrementar las ganancias de tus anuncios."

#. Plugin Name of the plugin
#: advanced-ads-pro.php
msgid "Advanced Ads Pro"
msgstr "Advanced Ads Pro"

#: modules/weekdays/views/ad-submitbox-meta.php:19
msgid "Set specific days"
msgstr "Establecer días específicos"

#. translators: 1: month, 2: day, 3: year, 4: hour, 5: minute
#: modules/placement-tests/views/settings_test_expiry_date.php:31
msgctxt "order of expiry date fields 1: month, 2: day, 3: year, 4: hour, 5: minute"
msgid "%1$s %2$s, %3$s @ %4$s %5$s"
msgstr "%1$s %2$s, %3$s @ %4$s %5$s"

#: modules/placement-tests/views/settings_test_expiry_date.php:25
msgid "Minute"
msgstr "Minuto"

#: modules/placement-tests/views/settings_test_expiry_date.php:24
msgid "Hour"
msgstr "Hora"

#: modules/placement-tests/views/settings_test_expiry_date.php:23
msgid "Year"
msgstr "Año"

#: modules/placement-tests/views/settings_test_expiry_date.php:22
msgid "Day"
msgstr "Día"

#. translators: 1: month number (01, 02, etc.), 2: month abbreviation
#: modules/placement-tests/views/settings_test_expiry_date.php:17
msgctxt "1: month number (01, 02, etc.), 2: month abbreviation"
msgid "%1$s-%2$s"
msgstr "%1$s-%2$s"

#: modules/placement-tests/views/settings_test_expiry_date.php:12
msgid "Month"
msgstr "Mes"

#: modules/placement-tests/views/settings_test_expiry_date.php:5
msgctxt "placement tests"
msgid "Send email after"
msgstr "Enviar correo después de"

#: modules/placement-tests/placement-tests.class.php:237
#: modules/placement-tests/views/setting_placement_test_weight.php:17
msgid "Test weight"
msgstr "Peso de la prueba"

#: modules/placement-tests/views/setting_placement_test_weight.php:15
msgctxt "placement tests"
msgid "Testing"
msgstr "Probando"

#: modules/placement-tests/views/placement-tests.php:66
msgid "Save Tests"
msgstr "Guardar pruebas"

#: modules/placement-tests/views/placement-tests.php:59
msgctxt "checkbox to remove placement test"
msgid "delete"
msgstr "borrar"

#: modules/placement-tests/views/placement-tests.php:53
msgctxt "placement tests"
msgid "empty"
msgstr "vacías"

#: modules/placement-tests/views/placement-tests.php:18
msgid "Placements"
msgstr "Ubicaciones"

#: modules/placement-tests/views/placement-tests.php:17
msgid "Expiry date"
msgstr "Fecha de expiración"

#: modules/placement-tests/views/placement-tests.php:16
msgid "Author"
msgstr "Autor"

#: modules/placement-tests/views/placement-tests.php:11
msgid "Placement tests"
msgstr "Pruebas de ubicación"

#: modules/lazy-load/views/setting_lazy_load.php:6
#: modules/lazy-load/views/setting_lazy_load.php:8
msgid "disabled"
msgstr "desactivado"

#: modules/lazy-load/views/setting_lazy_load.php:2
#: modules/lazy-load/views/setting_lazy_load.php:4
msgid "enabled"
msgstr "activado"

#: modules/group-refresh/views/settings_group_refresh.php:27
msgid "Please use a placement to deliver this group using cache-busting."
msgstr "Por favor, utiliza una ubicación para lanzar este grupo usando la eliminación de caché."

#: modules/group-refresh/views/settings_group_refresh.php:19
msgid "Refresh ads on the same spot. Works when cache-busting is used."
msgstr "Actualizar los anuncios en el mismo sitio. Solo funciona cuando se usa la eliminación de caché."

#: modules/group-refresh/views/settings_group_refresh.php:16
msgid "milliseconds"
msgstr "milisegundos"

#: modules/group-refresh/views/settings_group_refresh.php:14
#: views/admin/placements/bulk-edit.php:30
#: views/admin/placements/bulk-edit.php:40
#: views/admin/tables/ads/column-displayonce.php:10
msgid "Enabled"
msgstr "Activado"

#: modules/grids/views/grid-option-size.php:20
msgid "rows"
msgstr "filas"

#: modules/grids/views/grid-option-size.php:16
msgid "columns"
msgstr "columnas"

#: modules/click-fraud-protection/views/settings.php:23
msgid "Period for which to hide the ad ( in days )"
msgstr "Período en el cual ocultar el anuncio (en día)"

#: modules/click-fraud-protection/views/settings.php:20
msgid "Period in which the click limit should be reached ( in hours )"
msgstr "Período en el que el límite de clics debe ser alcanzado (en horas)"

#: modules/click-fraud-protection/views/settings.php:17
msgid "Allowed clicks on a single ad before it is removed"
msgstr "Permitir clics en un anuncio individual antes de ser removido"

#. translators: Link to parallax manual
#: modules/ad-server/views/module-settings.php:15
#: modules/ads-for-adblockers/views/settings.php:20
#: modules/advanced-display-conditions/main.class.php:508
#: modules/advanced-display-conditions/views/metabox-string.php:12
#: modules/advanced-visitor-conditions/main.class.php:309
#: modules/advanced-visitor-conditions/main.class.php:408
#: modules/advanced-visitor-conditions/main.class.php:465
#: modules/advanced-visitor-conditions/main.class.php:503
#: modules/buddypress/class-buddypress.php:378
#: modules/buddypress/views/xprofile-condition.php:74
#: modules/cache-busting/cache-busting.class.php:263
#: modules/cache-busting/views/settings.php:14
#: modules/click-fraud-protection/click-fraud-protection.class.php:150
#: modules/click-fraud-protection/click-fraud-protection.class.php:158
#: modules/click-fraud-protection/views/settings.php:12
#: modules/gamipress/views/visitor-condition-achievement.php:34
#: modules/gamipress/views/visitor-condition-points.php:41
#: modules/gamipress/views/visitor-condition-rank.php:41
#: modules/geo/admin/views/setting-maxmind-license-key.php:22
#: modules/geo/classes/admin.php:400
#: modules/group-refresh/views/settings_group_refresh.php:23
#: modules/lazy-load/views/settings.php:10
#: modules/paid-memberships-pro/main.class.php:140
#: modules/paid-memberships-pro/main.class.php:259
#: modules/parallax-ads/views/placement-options-after.php:24
#: modules/responsive-ads/views/setting_fallback_width.php:16
#: modules/rest-api/views/module-enable.php:15
msgid "Manual"
msgstr "Manual"

#. translators: %s link to manual
#: modules/cache-busting/views/settings_check_ad.php:16
msgid "The code of this ad might not work properly with activated cache-busting. <a href=\"%s\" target=\"_blank\">Manual</a>"
msgstr "El código de este anuncio puede no funcionar correctamente con la eliminación de caché activada. <a href=\"%s\" target=\"_blank\">Manual</a>"

#: modules/cache-busting/views/settings.php:44
msgid "Enable passive cache busting for all ads and groups which are not delivered through a placement, if possible."
msgstr ""

#: modules/cache-busting/views/settings.php:62
msgid "Choose the fallback if “passive“ cache busting is not possible."
msgstr ""

#: modules/cache-busting/admin-ui.class.php:87
#: modules/cache-busting/views/settings.php:31
#: modules/cache-busting/views/settings.php:68
#: views/admin/placements/bulk-edit.php:20
msgid "AJAX"
msgstr "AJAX"

#: modules/advanced-display-conditions/views/referrer-url-metabox.php:15
#: modules/advanced-visitor-conditions/views/referrer-url-metabox.php:15
msgid "url of the referrer"
msgstr "URL de procedencia"

#: modules/advanced-display-conditions/views/referrer-url-metabox.php:5
#: modules/advanced-visitor-conditions/views/referrer-url-metabox.php:5
msgid "URL"
msgstr "URL"

#: modules/advanced-display-conditions/views/referrer-url-metabox.php:3
#: modules/advanced-visitor-conditions/views/referrer-url-metabox.php:3
msgid "Display ad depending on the external url the user comes from."
msgstr "Mostrar el anuncio dependiendo de la URL externa de la cual proviene el usuario."

#: modules/advanced-display-conditions/views/referrer-url-metabox.php:1
#: modules/advanced-visitor-conditions/views/referrer-url-metabox.php:1
msgid "Display by referrer url"
msgstr "Mostrar por URL de procedencia"

#: modules/ads-for-adblockers/views/placement-item.php:46
#: modules/lazy-load/views/setting_lazy_load.php:11
msgid "Works only with cache-busting enabled"
msgstr "Funciona solamente con la eliminación de caché activada"

#: modules/weekdays/class-advanced-ads-pro-weekdays.php:171
msgid "Never shows up"
msgstr "Nunca se muestra"

#: modules/weekdays/class-advanced-ads-pro-weekdays.php:90
#: modules/weekdays/class-advanced-ads-pro-weekdays.php:107
msgid "specific days"
msgstr "días específicos"

#. translators: %s: placement test name
#: modules/placement-tests/placement-tests.class.php:702
msgid "Placement test <em>%s</em> created"
msgstr "Prueba de ubicación <em>%s</em> creada"

#: modules/placement-tests/placement-tests.class.php:564
msgctxt "placement tests"
msgid "Placement page"
msgstr "Página de ubicación"

#: modules/placement-tests/placement-tests.class.php:558
#: modules/placement-tests/placement-tests.class.php:700
#: modules/placement-tests/views/placement-tests.php:50
msgctxt "placement tests"
msgid " vs "
msgstr " vs "

#: modules/placement-tests/placement-tests.class.php:530
msgctxt "placement tests"
msgid "Expired placement tests"
msgstr "Pruebas de ubicaciones caducadas"

#: modules/placement-tests/views/setting_placement_test_weight.php:28
msgid "Save new test"
msgstr ""

#: modules/paid-memberships-pro/main.class.php:157
msgid "Display ads only to users with a specific membership level set with PaidMembershipsPro."
msgstr "Mostrar anuncios solamente a usuarios con niveles específicos de membresía con PaidMembershipsPro."

#: modules/paid-memberships-pro/main.class.php:156
msgid "PMP user level"
msgstr "Nivel de usuario PMP"

#: modules/paid-memberships-pro/main.class.php:133
#: modules/paid-memberships-pro/main.class.php:252
msgid "No membership levels set up yet."
msgstr "No se han configurado niveles de membresía todavía."

#: modules/paid-memberships-pro/main.class.php:39
msgid "Display ads only on pages that require a specific membership level set of PaidMembershipsPro."
msgstr ""

#: modules/paid-memberships-pro/main.class.php:38
msgid "PMP page level"
msgstr "Nivel de página PMP"

#: includes/placements/types/class-archive-pages.php:56
msgid "Display the ad between posts on post lists, e.g. home, archives, search etc."
msgstr "Mostrar el anuncio entre entradas en las listas de entradas, p. ej. inicio, archivos, búsquedas, etcétera."

#: includes/placements/types/class-archive-pages.php:47
msgid "Post Lists"
msgstr "Lista de entradas"

#: includes/placements/types/class-custom-position.php:57
msgid "Attach the ad to any element in the frontend."
msgstr "Adjuntar el anuncio a cualquier elemento en el frente del sitio."

#: includes/placements/types/class-custom-position.php:48
msgid "Custom Position"
msgstr "Posición personalizada"

#: includes/placements/types/class-content-middle.php:56
msgid "In the middle of the main content based on the number of paragraphs."
msgstr "En la mitad del contenido principal pasado en el número de párrafos."

#: includes/placements/types/class-content-middle.php:47
msgid "Content Middle"
msgstr "En la mitad del contenido"

#: includes/placements/types/class-above-headline.php:57
msgid "Above the main headline on the page (&lt;h1&gt;)."
msgstr "Arriba del encabezado principal en la página (&lt;h1&gt;)."

#: includes/placements/types/class-above-headline.php:48
msgid "Above Headline"
msgstr "Arriba del encabezado"

#: includes/placements/types/class-content-random.php:56
msgid "After a random paragraph in the main content."
msgstr "Después de un párrafo aleatorio en el contenido principal."

#: includes/placements/types/class-content-random.php:47
msgid "Random Paragraph"
msgstr "Párrafo aleatorio"

#: modules/inject-content/admin.class.php:155
msgid "secondary loops"
msgstr "bucles secundarios"

#: modules/inject-content/admin.class.php:151
msgid "Only enable this option if you are sure what you are doing!"
msgstr "¡Solo activa esta opción si estás seguro de lo que estás haciendo!"

#: modules/inject-content/admin.class.php:150
msgid "Allow injection into any custom and secondary queries."
msgstr "Permite la inyección en cualquier consulta personalizada y secundaria."

#: modules/inject-content/admin.class.php:128
msgid "minimum content length"
msgstr "longitud mínima del contenido"

#: modules/inject-content/admin.class.php:93
msgid "Before which post to inject the ad on post lists."
msgstr "Antes de que entrada se debe inyectar el anuncio en las listas de entradas."

#. translators: %s: index of the post
#: modules/inject-content/admin.class.php:91
msgid "Inject before %s. post"
msgstr "Inyectar antes de la %s. entrada"

#: modules/inject-content/admin.class.php:69
msgid "Place the following element where the ad should be displayed."
msgstr "Posicionar el siguiente elemento en donde el anuncio debe ser mostrado."

#: modules/inject-content/admin.class.php:68
msgid "by new element"
msgstr "por nuevo elemento"

#: modules/bbpress/class-admin.php:64
#: modules/bbpress/class-admin.php:87
#: modules/bbpress/class-admin.php:130
#: modules/inject-content/admin.class.php:58
msgid "Position"
msgstr "Posición"

#: modules/inject-content/admin.class.php:57
msgid "Uses <a href=\"https://api.jquery.com/category/selectors/\" target=\"_blank\">jQuery selectors</a>, e.g. #container_id, .container_class"
msgstr "Utilizar <a href=\"https://api.jquery.com/category/selectors/\" target=\"_blank\">selectores de jQuery</a>, ej. #container_id, .container_class"

#: modules/inject-content/admin.class.php:54
msgid "or enter manually"
msgstr "o introducir manualmente"

#: modules/inject-content/admin.class.php:56
msgid "select position"
msgstr "seleccionar posición"

#: modules/inject-content/admin.class.php:55
msgctxt "frontend picker"
msgid "stop selection"
msgstr "detener la selección"

#: modules/inject-content/admin.class.php:51
msgid "Place ads in relation to an existing element in the frontend."
msgstr "Posicionar anuncios en relación a un elemento existente en el frente el sitio."

#: modules/inject-content/admin.class.php:49
msgid "by existing element"
msgstr "por elemento existente"

#: modules/inject-content/admin.class.php:40
msgid "below"
msgstr "abajo"

#: modules/inject-content/admin.class.php:39
msgid "inside, after other content"
msgstr "adentro, después de otro contenido"

#: modules/inject-content/admin.class.php:38
msgid "inside, before other content"
msgstr "adentro, antes de otro contenido"

#: modules/inject-content/admin.class.php:37
msgid "above"
msgstr "arriba"

#: modules/group-refresh/admin.class.php:60
msgid "Refresh interval"
msgstr "Intervalo de actualización"

#: modules/grids/admin.class.php:73
msgid "Random order"
msgstr "Orden aleatorio"

#: modules/grids/admin.class.php:53
msgid "Minimum width of a column in the grid."
msgstr "ancho mínimo de una columna en la cuadrícula"

#: modules/grids/admin.class.php:51
msgid "Min. width"
msgstr "Ancho mínimo"

#: modules/grids/admin.class.php:45
msgid "Inner margin"
msgstr "Margen interior"

#: modules/grids/admin.class.php:36
msgid "Size"
msgstr "Tamaño"

#: modules/click-fraud-protection/click-fraud-protection.class.php:320
msgid "Click Fraud Protection enabled"
msgstr "Protección contra el fraude de clics activado"

#: modules/click-fraud-protection/click-fraud-protection.class.php:145
msgid "hours"
msgstr "horas"

#: modules/click-fraud-protection/click-fraud-protection.class.php:143
msgid "within"
msgstr "dentro"

#: modules/click-fraud-protection/click-fraud-protection.class.php:111
msgid "Display ad only if a click limit has not been reached."
msgstr ""

#: modules/click-fraud-protection/click-fraud-protection.class.php:110
msgid "max. ad clicks"
msgstr "clips máximos del anuncio"

#: modules/click-fraud-protection/admin.class.php:29
msgid "Click Fraud Protection"
msgstr "Protección contra el fraude de clics"

#: modules/cache-busting/cache-busting.class.php:1913
msgctxt "placement admin label"
msgid "Cache-busting:"
msgstr "Eliminación de caché:"

#: modules/cache-busting/cache-busting.class.php:1907
msgid "The ad is not displayed on the page"
msgstr "El anuncio no se muestra en esta página"

#: modules/cache-busting/cache-busting.class.php:1906
msgid "The ad is displayed on the page"
msgstr "El anuncio se muestra en esta página"

#: modules/cache-busting/cache-busting.class.php:1903
#: modules/cache-busting/views/settings.php:24
msgid "passive"
msgstr "pasiva"

#: modules/cache-busting/cache-busting.class.php:1897
msgid "The ad can work with passive cache-busting"
msgstr "El anuncio puede funcionar con la eliminación de caché pasiva"

#: modules/cache-busting/cache-busting.class.php:1895
msgid "The ad can not work with passive cache-busting"
msgstr "El anuncio no puede funcionar con la eliminación de caché pasiva"

#: modules/ads-for-adblockers/views/settings.php:27
#: modules/cache-busting/admin.class.php:29
#: views/admin/placements/bulk-edit.php:16
msgid "Cache Busting"
msgstr "Eliminación de caché"

#: modules/cache-busting/admin-ui.class.php:150
msgid "The <em>Ad Group</em> ad type can only use AJAX or no cache-busting, but not passive cache-busting."
msgstr "El tipo de anuncio del <em>Grupo de anuncio</em> solamente puede utilizar AJAX o eliminación de caché, pero no puede utilizar la eliminación de caché pasiva."

#: modules/cache-busting/admin-ui.class.php:107
msgctxt "placement admin label"
msgid "Cache-busting"
msgstr "Eliminación de caché"

#: modules/cache-busting/cache-busting.class.php:1910
msgctxt "setting label"
msgid "off"
msgstr "apagado"

#: modules/cache-busting/cache-busting.class.php:1901
msgctxt "setting label"
msgid "ajax"
msgstr "ajax"

#: modules/buddypress/class-admin.php:82
msgid "Member List"
msgstr "Lista de miembros"

#: modules/buddypress/class-admin.php:79
msgid "Group List"
msgstr "Lista de grupos"

#: modules/buddypress/class-admin.php:63
#: modules/buddypress/class-admin.php:71
msgid "Activity Entry"
msgstr "Rubro de Actividad"

#. translators: %s is an HTML input element.
#: modules/buddypress/views/position-option.php:37
msgid "Inject after %s. entry"
msgstr ""

#: includes/placements/types/class-buddypress.php:63
msgid "Display ads on BuddyPress related pages."
msgstr "mostrar anuncios en las páginas relacionadas de BuddyPress"

#: includes/placements/types/class-buddypress.php:51
msgid "BuddyPress Content"
msgstr "Contenido BuddyPress"

#: modules/bbpress/class-admin.php:150
msgid "forums page"
msgstr "página de foros"

#: modules/bbpress/class-admin.php:146
msgid "single forum page"
msgstr "página individual del foro"

#: modules/bbpress/class-admin.php:142
#: modules/bbpress/class-admin.php:164
msgid "forum topic page"
msgstr "página de tópico del foro"

#: modules/buddypress/views/position-option.php:20
#: modules/inject-content/admin.class.php:80
#: modules/inject-content/admin.class.php:96
msgid "position"
msgstr "posición"

#: includes/placements/types/class-bbpress-comment.php:56
msgid "Display ads in bbPress replies."
msgstr "Mostrar anuncios en las respuestas de bbPress."

#: includes/placements/types/class-bbpress-comment.php:47
msgid "bbPress Reply Content"
msgstr "Contenido de Respuesta de bbPress"

#: includes/placements/types/class-bbpress-static.php:56
msgid "Display ads on bbPress related pages."
msgstr "Mostrar anuncios en páginas relacionadas de bbPress."

#: includes/placements/types/class-bbpress-static.php:47
msgid "bbPress Static Content"
msgstr "Contenido Estático bbPress"

#: modules/background-ads/admin.class.php:33
msgid "background"
msgstr "fondo"

#: modules/background-ads/admin.class.php:30
msgid "Select a background color in case the background image is not high enough to cover the whole screen."
msgstr "Seleccionar un color de fondo, en caso que la imagen de fondo no sea lo suficientemente alta para cubrir toda la pantalla."

#: includes/placements/types/class-background-ad.php:56
msgid "Background of the website behind the main wrapper."
msgstr "Fondo del sitio web detrás del contenedor principal."

#: includes/placements/types/class-background-ad.php:47
msgid "Background Ad"
msgstr "Anuncio de fondo"

#. translators: %s is a placeholder for the impressions field
#: modules/advanced-visitor-conditions/main.class.php:499
msgid "within %s seconds"
msgstr "dentro de %s segundos"

#: modules/advanced-visitor-conditions/main.class.php:449
msgid "Cookie Value"
msgstr "Valor de la cookie"

#: modules/advanced-visitor-conditions/main.class.php:448
msgid "Cookie Name"
msgstr "Nombre de la cookie"

#: modules/advanced-visitor-conditions/main.class.php:301
#: modules/advanced-visitor-conditions/main.class.php:349
#: modules/advanced-visitor-conditions/main.class.php:398
msgid "-- choose one --"
msgstr "-- elige uno --"

#: modules/advanced-visitor-conditions/main.class.php:298
msgid "can not"
msgstr "no puede"

#: modules/advanced-visitor-conditions/main.class.php:297
msgid "can"
msgstr "puede"

#: modules/advanced-visitor-conditions/main.class.php:202
msgid "new visitor"
msgstr "nuevo visitante"

#: modules/advanced-visitor-conditions/main.class.php:196
msgid "Display the ad only for a few impressions in a given period per user."
msgstr "Mostrar el anuncio, por usuario, solamente por unas pocas impresiones en un periodo de tiempo establecido."

#: modules/advanced-visitor-conditions/main.class.php:195
msgid "max. ad impressions"
msgstr "máximas impresiones de anuncio"

#: modules/advanced-visitor-conditions/main.class.php:188
msgid "Display ads based on the number of page impressions the user already made before the current one."
msgstr ""

#: modules/advanced-visitor-conditions/main.class.php:187
msgid "page impressions"
msgstr "impresiones de páginas"

#: modules/advanced-visitor-conditions/main.class.php:180
msgid "cookie"
msgstr "cookie"

#: modules/advanced-visitor-conditions/main.class.php:174
msgid "Display ads based on the visitor's browser language."
msgstr ""

#: modules/advanced-visitor-conditions/main.class.php:173
msgid "browser language"
msgstr "idioma del navegador"

#: modules/advanced-visitor-conditions/main.class.php:148
msgid "user can (capabilities)"
msgstr "el usuario puede (capacidades)"

#: modules/advanced-visitor-conditions/main.class.php:140
msgid "user agent"
msgstr "agente de usuario"

#: modules/advanced-visitor-conditions/main.class.php:133
msgid "Display ads based on the referrer URL."
msgstr ""

#: modules/advanced-visitor-conditions/main.class.php:132
msgid "referrer url"
msgstr "URL de procedencia"

#: modules/advanced-visitor-conditions/admin.class.php:29
msgid "Advanced visitor conditions"
msgstr "Condiciones avanzadas del visitante"

#: modules/advanced-display-conditions/main.class.php:574
msgid "count from end"
msgstr "contar desde el final"

#: modules/advanced-display-conditions/main.class.php:540
msgid "meta key"
msgstr "clave meta"

#: modules/advanced-display-conditions/main.class.php:539
msgid "all of"
msgstr "todas de"

#: modules/advanced-display-conditions/main.class.php:538
msgid "any of"
msgstr "cualquiera de"

#: modules/advanced-display-conditions/main.class.php:502
msgid "no languages set up in WPML"
msgstr "no hay idiomas configurados en WPML"

#: modules/advanced-display-conditions/main.class.php:431
#: modules/advanced-display-conditions/main.class.php:486
#: modules/advanced-visitor-conditions/main.class.php:346
#: modules/advanced-visitor-conditions/main.class.php:395
#: modules/advanced-visitor-conditions/main.class.php:538
#: modules/gamipress/views/visitor-condition-achievement.php:17
#: modules/geo/classes/admin.php:308
#: modules/paid-memberships-pro/main.class.php:118
#: modules/paid-memberships-pro/main.class.php:237
msgid "is not"
msgstr "no es"

#: modules/advanced-display-conditions/main.class.php:430
#: modules/advanced-display-conditions/main.class.php:485
#: modules/advanced-visitor-conditions/main.class.php:345
#: modules/advanced-visitor-conditions/main.class.php:394
#: modules/advanced-visitor-conditions/main.class.php:536
#: modules/gamipress/views/visitor-condition-achievement.php:16
#: modules/geo/classes/admin.php:307
#: modules/paid-memberships-pro/main.class.php:117
#: modules/paid-memberships-pro/main.class.php:236
msgid "is"
msgstr "es"

#: modules/advanced-display-conditions/main.class.php:110
msgid "Display ads based on the index of a split page"
msgstr "Mostrar anuncios basados en el índice de una página dividida"

#: modules/advanced-display-conditions/main.class.php:109
msgid "pagination"
msgstr "paginación"

#: modules/advanced-display-conditions/main.class.php:103
msgid "Display ads based on post meta."
msgstr "Mostrar anuncios basados en el meta de la entrada."

#: modules/advanced-display-conditions/main.class.php:102
msgid "post meta"
msgstr "meta de la entrada"

#: modules/advanced-display-conditions/main.class.php:96
msgid "Display ads based on parent page."
msgstr "Mostrar anuncios basado en la página madre."

#: modules/advanced-display-conditions/main.class.php:95
msgid "parent page"
msgstr "página madre"

#: modules/advanced-display-conditions/main.class.php:87
msgid "WPML language"
msgstr "Lenguaje WPML"

#. translators: %s: post type
#: modules/advanced-display-conditions/main.class.php:57
#: modules/advanced-display-conditions/main.class.php:76
msgid "Display ads based on the template of the %s post type."
msgstr "Mostrar anuncios basados en la plantilla del %s tipo de entrada."

#. translators: %s: page
#: modules/advanced-display-conditions/main.class.php:55
#: modules/advanced-display-conditions/main.class.php:75
msgid "%s template"
msgstr "%s plantilla"

#: modules/advanced-display-conditions/main.class.php:39
msgid "url parameters"
msgstr "parámetros de la URL"

#: modules/ads-for-adblockers/admin.class.php:148
msgid "The chosen ad contains a reference to an external .js file"
msgstr "El anuncio elegido contiene una referencia a un archivo externo .js"

#: modules/ads-for-adblockers/admin.class.php:86
msgid "Displayed to visitors with an ad blocker"
msgstr "Mostrado a los visitantes con un bloqueador de anuncios"

#: modules/ads-for-adblockers/admin.class.php:94
msgid "Ad blocker item"
msgstr "Ítem de bloqueador de anuncio"

#: modules/ads-for-adblockers/admin.class.php:42
msgid "Ads for ad blockers"
msgstr "Anuncios para bloqueadores de anuncios"

#: modules/admin-bar/admin-bar.class.php:104
msgid "placement"
msgstr "ubicación"

#: modules/admin-bar/admin-bar.class.php:101
msgid "group"
msgstr "grupo"

#: modules/admin-bar/admin-bar.class.php:98
msgid "ad"
msgstr "anuncio"

#: modules/admin-bar/admin-bar.class.php:56
msgid "No Ads found"
msgstr "No se han encontrado anuncios"

#: modules/admin-bar/admin-bar.class.php:48
#: modules/ads-for-adblockers/views/placement-item.php:27
#: views/admin/tables/ads/adsense-fallback.php:37
msgid "Ads"
msgstr "Anuncios"

#: views/setting_output_once.php:6
msgid "Display the ad only once per page"
msgstr "Mostrar el anuncio solamente una vez por página"

#: views/setting_output_once.php:2
msgid "Display only once"
msgstr "Mostrar solamente una vez"

#: classes/advanced-ads-pro.php:332
msgid "Please note, the “Ad Admin“ and the “Ad Manager“ roles have the “upload_files“ and the “unfiltered_html“ capabilities"
msgstr "Por favor, ten presente que los perfiles «Súper administrador de anuncios» y «Administrador de anuncios» tienen las capacidades de «upload_files» y «unfiltered_html»"

#: includes/class-bootstrap.php:225
msgid "Install Now"
msgstr "Instalar ahora"

#: includes/class-bootstrap.php:233
msgid "Activate Now"
msgstr "Activar ahora"

#. translators: %s: URL to the settings page
#: classes/advanced-ads-pro-admin.php:390
msgid "Enable the Advanced Visitor Conditions <a href=\"%s\" target=\"_blank\">in the settings</a>."
msgstr "Activa las Condiciones avanzadas del visitante <a href=\"%s\" target=\"_blank\">en los ajustes</a>."

#: classes/advanced-ads-pro-admin.php:336
msgid "Please note, with the last update, the “Ad Admin“ and “Ad Manager“ roles have the “upload_files“ and the “unfiltered_html“ capabilities."
msgstr "Por favor, ten presente que con la última actualización, los perfiles «Súper administrador de anuncios» y el «Administrador de anuncios» tienen las capacidades «upload_files» y «unfiltered_html»."

#: classes/advanced-ads-pro-admin.php:326
msgid "Ad User Role"
msgstr "Perfil de usuario de anuncios"

#: classes/advanced-ads-pro-admin.php:323
msgid "Advanced Ads User Role"
msgstr "Perfil de usuario de anuncios avanzado"

#: classes/advanced-ads-pro-admin.php:46
msgid "--no role--"
msgstr "--sin perfil--"

#: classes/advanced-ads-pro-admin.php:45
#: includes/installation/class-install.php:159
msgid "Ad User"
msgstr "Usuario de anuncios"

#: classes/advanced-ads-pro-admin.php:44
#: includes/installation/class-install.php:146
msgid "Ad Manager"
msgstr "Administrador de anuncios"

#: classes/advanced-ads-pro-admin.php:43
#: includes/installation/class-install.php:132
msgid "Ad Admin"
msgstr "Súper administrador de anuncios"
