msgid ""
msgstr ""
"Project-Id-Version: Advanced Ads Pro\n"
"Report-Msgid-Bugs-To: \n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2025-04-09T03:52:05+00:00\n"
"PO-Revision-Date: 2025-03-28 07:25:30+0000\n"
"Language: sl_SI\n"
"Plural-Forms: nplurals=4; plural=(n % 100 == 1) ? 0 : ((n % 100 == 2) ? 1 : ((n % 100 == 3 || n % 100 == 4) ? 2 : 3));\n"
"X-Generator: GlotPress/4.0.1\n"

#: views/admin/placements/bulk-edit.php:21
msgid "Off"
msgstr ""

#: views/admin/placements/bulk-edit.php:18
#: views/admin/placements/bulk-edit.php:29
#: views/admin/placements/bulk-edit.php:39
msgid "No Change"
msgstr ""

#: modules/cache-busting/cache-busting.class.php:253
#: views/admin/placements/bulk-edit.php:37
msgid "Hide when empty"
msgstr ""

#: views/admin/placements/bulk-edit.php:31
#: views/admin/placements/bulk-edit.php:41
msgid "Disabled"
msgstr ""

#: views/admin/placements/bulk-edit.php:19
msgid "Auto"
msgstr ""

#. Author of the plugin
#: advanced-ads-pro.php
msgid "Advanced Ads"
msgstr ""

#. translators: %s: Plugin name
#: includes/class-bootstrap.php:155
msgid "Your versions of the Advanced Ads addons listed below are incompatible with <strong>Advanced Ads %s</strong> and have been deactivated. Please update the plugin to the latest version."
msgstr ""

#. translators: %s: Plugin name
#: includes/class-bootstrap.php:109
msgid "Your version of <strong>Advanced Ads - Pro</strong> is incompatible with <strong>Advanced Ads %s</strong> and has been deactivated. Please update the plugin to the latest version."
msgstr ""

#. translators: 1: is a link to a support document. 2: closing link
#: includes/class-autoloader.php:113
msgid "Your installation of Advanced Ads is incomplete. If you installed Advanced Ads from GitHub, %1$s please refer to this document%2$s to set up your development environment."
msgstr ""

#: modules/geo/admin/views/setting-download.php:22
msgid "You are currently using filter hooks to load custom database files."
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:17
msgid "When to show overlay again?"
msgstr ""

#: modules/placement_conditions/admin.class.php:70
msgid "Visitor Conditions"
msgstr ""

#: modules/ads-by-hours/src/class-admin.php:135
msgid "viewer time zone"
msgstr ""

#: modules/advanced-visitor-conditions/main.class.php:218
msgid "User IP Address"
msgstr ""

#: modules/ads-by-hours/views/publish-metabox-inputs.php:54
msgid "To"
msgstr ""

#. translators: 1 opening link tag. 2 closing tag
#: modules/cache-busting/views/settings.php:53
msgid "This option does not work with the current active theme and %1$sblock themes%2$s in general."
msgstr ""

#. translators: %s: dashicon class
#: modules/lazy-load/views/settings.php:22
msgid "This module requires: <br> <span class=\"dashicons %s\"></span> Cache Busting"
msgstr ""

#: modules/ads-for-adblockers/views/settings.php:25
msgid "This module requires:"
msgstr ""

#: views/admin/tables/ads/adsense-fallback.php:47
#: views/admin/tables/ads/adsense-fallback.php:66
msgid "The selected item will be displayed when an AdSense ad is unavailable, ensuring your ad space remains filled."
msgstr ""

#: includes/class-bootstrap.php:180
msgid "The following addons are affected:"
msgstr ""

#. translators: group or ad title.
#: views/admin/tables/ads/adsense-fallback.php:58
msgid "The default fallback is \"%s\". You can change this in the AdSense settings."
msgstr ""

#: views/admin/tables/ads/adsense-fallback.php:75
msgid "The AdSense fallback feature requires the ad to be assigned to a placement with enabled Cache Busting."
msgstr ""

#. translators: %s: WP_ENVIRONMENT_TYPES
#: includes/class-autoloader.php:157
msgid "The %s constant is no longer supported."
msgstr ""

#: modules/weekdays/class-advanced-ads-pro-weekdays.php:177
msgid "Shows up"
msgstr ""

#: modules/ads-by-hours/src/class-admin.php:79
msgid "Showing ads depending on the visitor's time requires enabling Cache Busting."
msgstr ""

#: modules/extended-adblocker/views/setting-activate-extended.php:26
msgid "Show a custom overlay to users with an ad blocker enabled, prompting them to turn it off on your website."
msgstr ""

#: modules/ads-for-adblockers/admin.class.php:89
#: modules/lazy-load/admin.class.php:58
msgid "Settings"
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:20
msgid "Set the timing for the overlay to reappear after being dismissed."
msgstr ""

#: modules/ads-by-hours/views/publish-metabox-inputs.php:21
msgid "Set specific hours"
msgstr ""

#: modules/cache-busting/cache-busting.class.php:257
#: views/admin/placements/bulk-edit.php:45
msgid "Remove the placeholder if unfilled."
msgstr ""

#: modules/extended-adblocker/views/setting-activate-extended.php:64
msgid "Redirect URL"
msgstr ""

#: modules/extended-adblocker/views/setting-activate-extended.php:37
msgid "Redirect"
msgstr ""

#: modules/lazy-load/admin.class.php:68
msgid "Prevent ads from getting loaded before they appear in the visitor’s visible area."
msgstr ""

#: modules/extended-adblocker/views/setting-activate-extended.php:22
msgid "Overlay"
msgstr ""

#: views/admin/tables/ads/adsense-fallback.php:27
msgid "None"
msgstr ""

#: views/admin/tables/ads/adsense-fallback.php:52
msgid "No default fallback ad selected. Choose one in the AdSense settings."
msgstr ""

#: modules/extended-adblocker/views/setting-activate-extended.php:17
msgid "No additional actions"
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:43
msgid "Never"
msgstr ""

#: modules/extended-adblocker/views/setting-styling.php:19
msgid "Insert CSS to customize the overlay container layout."
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:84
msgid "Insert CSS to customize the dismiss button layout."
msgstr ""

#: modules/extended-adblocker/views/setting-styling.php:36
msgid "Insert CSS to customize the background of the overlay."
msgstr ""

#. translators: %s: index input field
#: modules/bbpress/class-admin.php:106
msgid "Inject after %s post"
msgstr ""

#: includes/class-bootstrap.php:129
#: includes/class-bootstrap.php:152
msgid "Important Notice"
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:55
msgid "Hide dismiss button"
msgstr ""

#: modules/ads-for-adblockers/views/placement-item.php:18
msgid "Groups"
msgstr ""

#. translators: %s: first field, %s: last field
#: modules/advanced-display-conditions/main.class.php:571
msgid "from %1$s to %2$s"
msgstr ""

#: modules/ads-by-hours/views/publish-metabox-inputs.php:35
msgid "From"
msgstr ""

#: includes/admin/class-adsense.php:38
msgid "Fallback ad/group"
msgstr ""

#: views/admin/tables/ads/adsense-fallback.php:20
msgid "Fallback"
msgstr ""

#: modules/extended-adblocker/views/setting-exclude.php:13
msgid "Exclude"
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:28
msgid "Everytime"
msgstr ""

#. translators: 1: The download URL, 2: The HTTP error code, 3: The HTTP header
#. title, 4: The response body
#: modules/geo/classes/admin.php:545
msgid "Error downloading database from: %1$s - %2$s %3$s - %4$s"
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:68
msgid "Enter the text that you want to appear on the dismiss button."
msgstr ""

#: modules/extended-adblocker/views/setting-activate-extended.php:68
msgid "Enter a specific page on your domain to which users with activated AdBlocker should be automatically redirected."
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:77
#: modules/extended-adblocker/views/setting-dismissible.php:94
#: modules/extended-adblocker/views/setting-styling.php:29
#: modules/extended-adblocker/views/setting-styling.php:46
msgid "Empty and save to revert to defaults."
msgstr ""

#: modules/advanced-visitor-conditions/main.class.php:211
msgid "Display or hide ad when user use adblocker."
msgstr ""

#: includes/admin/class-ad-list-table.php:42
#: includes/admin/class-ad-list-table.php:86
msgid "Display Once"
msgstr ""

#: modules/placement_conditions/admin.class.php:52
msgid "Display Conditions"
msgstr ""

#: modules/advanced-visitor-conditions/main.class.php:219
msgid "Display ads based on the user IP address. Enter one IP address per line."
msgstr ""

#: modules/buddypress/class-buddypress.php:324
msgid "Display ads based on existing BuddyPress groups."
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:65
msgid "Dismiss button text"
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:81
msgid "Dismiss button styling"
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:48
msgid "Dismiss button"
msgstr ""

#: modules/extended-adblocker/main.class.php:83
#: modules/extended-adblocker/views/setting-dismissible.php:75
msgid "Dismiss"
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:58
msgid "Disabling the dismiss button significantly limits site interaction."
msgstr ""

#: modules/cache-busting/cache-busting.class.php:261
msgid "Deleting an empty placement might lead to a layout shift."
msgstr ""

#: views/admin/tables/ads/adsense-fallback.php:25
msgid "Default"
msgstr ""

#: includes/admin/class-duplicate-placement.php:101
msgid "Create a copy of this placement"
msgstr ""

#: includes/admin/class-group-duplication.php:77
msgid "Create a copy of this group"
msgstr ""

#: modules/extended-adblocker/views/setting-overlay-content.php:11
msgid "Content"
msgstr ""

#: modules/extended-adblocker/views/setting-styling.php:16
msgid "Container styling"
msgstr ""

#: modules/extended-adblocker/views/setting-exclude.php:16
msgid "Choose which user roles to exclude from this ad blocker countermeasure."
msgstr ""

#: modules/lazy-load/admin.class.php:56
msgid "Cache Busting needs to be enabled"
msgstr ""

#: modules/ads-for-adblockers/admin.class.php:87
msgid "Cache Busting and Ad blocker disguise need to be enabled"
msgstr ""

#: modules/buddypress/class-buddypress.php:323
msgid "BuddyPress group"
msgstr ""

#: modules/extended-adblocker/views/setting-styling.php:33
msgid "Background styling"
msgstr ""

#: modules/extended-adblocker/views/setting-activate-extended.php:41
msgid "Automatically redirect users with ad blockers enabled to an internal page. Content access is granted after turning off the ad blocker."
msgstr ""

#: includes/class-bootstrap.php:71
#: includes/class-bootstrap.php:78
msgid "Advanced Ads - Pro"
msgstr ""

#. translators: ad title.
#: includes/class-adsense.php:52
msgid "AdSense fallback was loaded for empty AdSense ad \"%s\""
msgstr ""

#: includes/class-bootstrap.php:130
msgid "Addons listed below requires the <strong><a href=\"https://wpadvancedads.com/?utm_source=advanced-ads&utm_medium=link&utm_campaign=activate-advanced-ads-pro\" target=\"_blank\">Advanced Ads</a></strong> plugin to be installed and activated on your site."
msgstr ""

#: modules/advanced-visitor-conditions/main.class.php:210
msgid "Adblocker"
msgstr ""

#: views/admin/tables/ads/adsense-fallback.php:29
msgid "Ad groups"
msgstr ""

#: modules/extended-adblocker/admin.class.php:49
msgid "Ad blocker countermeasures"
msgstr ""

#: modules/ads-for-adblockers/views/settings.php:29
msgid "Ad block disguise"
msgstr ""

#: modules/extended-adblocker/views/setting-activate-extended.php:51
msgid "Activate the ad blocker disguise above to display the overlay."
msgstr ""

#: modules/ads-by-hours/src/class-admin.php:70
msgid "A cache plugin has been detected. It is recommended to enable Cache Busting and check the visitor's time when displaying the ad on the frontend."
msgstr ""

#: includes/class-bootstrap.php:95
msgid "<strong>Advanced Ads - Pro</strong> requires the <strong>Advanced Ads free</strong> plugin to be installed and activated on your site."
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:37
msgid "1 week"
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:40
msgid "1 month"
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:31
msgid "1 hour"
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:34
msgid "1 day"
msgstr ""

#: modules/ads-for-adblockers/views/placement-item.php:15
msgid "--not selected--"
msgstr ""

#. translators: comma separated list of days.
#: modules/weekdays/class-advanced-ads-pro-weekdays.php:181
msgid " on: %s"
msgstr ""

#. translators: 1. localized time 2. localized time 3. timezone name.
#: modules/weekdays/class-advanced-ads-pro-weekdays.php:187
msgid " between %1$s and %2$s %3$s"
msgstr ""

#: modules/responsive-ads/views/setting_reload_ads.php:19
msgid "You need to enable cache-busting in order to use this feature."
msgstr "Za uporabo te zmožnosti moraš omogočiti cache-busting."

#. translators: 1 is the opening link to the Advanced Ads website, 2 the
#. closing link
#: classes/advanced-ads-pro.php:338
msgid "We have renamed the Responsive Ads add-on to ‘Advanced Ads AMP Ads’. With this change, the Browser Width visitor condition moved from that add-on into Advanced Ads Pro. You can deactivate ‘Advanced Ads AMP Ads’ if you don’t utilize AMP ads or the custom sizes feature for responsive AdSense ad units. %1$sRead more%2$s."
msgstr "Dodatek Odzivni oglasi (Responsive Ads) smo preimenovali v ‘Advanced Ads oglasi AMP’. So to spremembo se je pogoj obiskovalcev Širina brskalnika prestavil iz tega dodatka v Advanced Ads Pro. ‘Advanced Ads oglasi AMP’ lahko onemogočiš, če ne uporabljaš oglasov AMP ali zmožnosti poljubnih velikosti oglasnih enot AdSense. %1$sPreberi več%2$s."

#: modules/responsive-ads/includes/class-admin.php:45
msgid "Responsive Image Ads"
msgstr "Odzivni slikovni oglasi"

#: modules/responsive-ads/includes/class-admin.php:38
msgid "Responsive Ads"
msgstr "Odzivni oglasi"

#: modules/responsive-ads/views/setting_reload_ads.php:16
msgid "Reload ads when the screen resizes."
msgstr "Ponovno naloži oglase, ko se spremeni velikost zaslona."

#: modules/responsive-ads/includes/class-admin.php:53
msgid "Reload ads on resize"
msgstr "Ponovno naloži oglase ob spremembi velikosti"

#: modules/responsive-ads/includes/class-admin.php:61
msgid "Fallback width"
msgstr "Nadomestna širina"

#: modules/responsive-ads/includes/class-common.php:40
msgid "Display ads based on the browser width."
msgstr "Prikaži oglase glede na širino brskalnika."

#: modules/cache-busting/admin-ui.class.php:51
msgid "Couldn't find the placement."
msgstr "Ne najdem postavitve."

#: modules/responsive-ads/views/setting_responsive_images.php:15
msgid "Check this option if the size of image ads is not adjusted responsively by your theme."
msgstr "Označi to možnost, če tvoja tema ne prilagodi velikosti slikovnih oglasov odzivno."

#: modules/cache-busting/admin-ui.class.php:48
msgid "Cache busting has been successfully enabled for the assigned placement."
msgstr "Cache busting je bilo uspešno omogočeno za dodeljeno postavitev."

#: modules/responsive-ads/includes/class-common.php:39
msgid "browser width"
msgstr "širina brskalnika"

#: modules/cache-busting/admin-ui.class.php:88
#: modules/cache-busting/views/settings.php:75
msgid "off"
msgstr "izklopljeno"

#: modules/cache-busting/views/settings.php:18
msgid "Choose which method to use when a placement needs cache busting and the option is set to “auto”."
msgstr "Izberi, katero metodo naj uporabim, ko rabi postavitev cache busting in je nastavljeno na \"avtomatsko\"."

#: modules/cache-busting/admin-ui.class.php:86
msgid "auto"
msgstr "avtomatsko"

#: modules/cache-busting/admin-ui.class.php:39
#: modules/geo/classes/admin.php:435
msgid "You are not allowed to do this."
msgstr "To ti ni dovoljeno."

#: modules/geo/classes/admin.php:447
msgid "Please provide a MaxMind license key"
msgstr "Prosim zagotovi licenčni ključ MaxMind"

#. translators: Link to parallax manual
#: modules/parallax-ads/views/placement-options-after.php:24
msgid "Optimized for image ads. %s"
msgstr "Optimizirano za slikovne oglase. %s"

#: modules/parallax-ads/views/placement-options-after.php:19
msgid "Enable the parallax effect."
msgstr "Omogoči učinek parallax."

#: modules/rest-api/classes/Admin_UI.php:33
msgid "REST API"
msgstr "REST API"

#: modules/parallax-ads/classes/Advanced_Ads_Pro_Module_Parallax_Admin_UI.php:71
msgid "Parallax Ads"
msgstr "Oglasi parallax"

#: modules/parallax-ads/views/placement-options-after.php:37
msgctxt "Parallax Ad placement height unit"
msgid "Unit"
msgstr "Enota"

#: modules/parallax-ads/views/placement-options-after.php:31
msgctxt "Parallax Ad placement height"
msgid "Height"
msgstr "Višina"

#: modules/rest-api/classes/Advanced_Ads_Group.php:27
msgid "Invalid group ID."
msgstr "Neveljavni ID skupine."

#: modules/parallax-ads/views/placement-options-after.php:40
msgid "Choose the height of the cutout, either in pixels or relative to the viewport."
msgstr "Izberi višino izreza, v pikah ali relativno na okno (viewport)."

#: modules/geo/admin/views/visitor-profile.php:20
msgid "Reset Visitor Profile"
msgstr "Ponastavi profil obiskovalca"

#: modules/geo/admin/views/visitor-profile.php:9
msgid "Location based on your visitor profile cookie:"
msgstr "Lokacija, glede na profil uporabnikovega piškotka"

#: modules/geo/classes/admin.php:405
msgid "Location based on your IP address"
msgstr "Lokacija, glede na tvoj naslov IP"

#: modules/advanced-visitor-conditions/main.class.php:203
msgid "Display or hide ads for new visitors."
msgstr "Prikaži ali skrij oglase novim uporabnikom."

#: modules/advanced-visitor-conditions/main.class.php:181
msgid "Display ads based on the value of a cookie. Set the operator to “matches/does not match” and leave the value empty to check only the existence of the cookie."
msgstr "Prikaži oglase glede na vrednost piškotka. Nastavi operator na \"se ujema/se ne ujema\" in pusti vrednost prazno, da samo preveriš obstajanje piškotka."

#: modules/advanced-display-conditions/main.class.php:88
msgid "Display ads based on the page language set by the WPML plugin."
msgstr "Prikaži oglase, glede na jezik strani, nastavljen v vtičniku WPML."

#: modules/buddypress/class-buddypress.php:335
#: modules/buddypress/class-buddypress.php:376
msgid "Display ads based on existing BuddyBoss groups."
msgstr "Prikaži oglase, glede na obstoječe skupine BuddyBoss."

#: modules/geo/classes/admin.php:367
msgid "miles (mi)"
msgstr "milj (mi)"

#: modules/geo/classes/admin.php:726
msgid "There was an error connecting to the search service."
msgstr "Prišlo je do napake pri povezovanju do storitve iskanja."

#: modules/geo/classes/admin.php:725
msgid "Your search did not return any results."
msgstr "Tvoje  iskanje ni vrnilo rezultatov."

#. translators: 1: A link to the geo location service.
#: modules/geo/classes/admin.php:728
msgid "You can search for the geo coordinates manually at %1$s."
msgstr "Geo koordinate lahko iščeš ročno na %1$s."

#. translators: 1: The number of search results.
#: modules/geo/classes/admin.php:724
msgid "Found %1$d results. Please pick the one, you want to use."
msgstr "Najdeno %1$d rezultatov. Prosim izberi tistega, ki ga želiš uporabiti."

#: modules/geo/classes/admin.php:366
msgid "kilometers (km)"
msgstr "kilometrov (km)"

#. translators: 1 is the opening link to the plugins page, 2 the closing link
#: modules/geo/classes/Advanced_Ads_Geo_Version_Check.php:51
msgid "The geo-targeting visitor condition moved into Advanced Ads Pro. You can remove Geo Targeting %1$shere%2$s."
msgstr "Pogoj geo-ciljanja uporabnikov se je prestavil v Advanced Ads Pro. Geo ciljanje lahko odstraniš %1$stukaj%2$s."

#: modules/geo/classes/api.php:389
msgid "Zimbabwe"
msgstr "Zimbabve"

#: modules/geo/classes/api.php:388
msgid "Zambia"
msgstr "Zambija"

#: modules/geo/classes/admin.php:245
msgid "Your IP address format is incorrect"
msgstr "Format tvojega naslova IP je nepravilen"

#. translators: %s is a URL.
#: modules/cache-busting/admin-ui.class.php:196
msgid "You need a placement to deliver this group using cache busting. <a href=\"%s\" target=\"_blank\">Create a placement now.</a>"
msgstr "Potrebuješ postavitev, da dostaviš to skupino z uporabo cache busting. <a href=\"%s\" target=\"_blank\">Ustvari postavitev zdaj.</a>"

#: modules/geo/classes/api.php:387
msgid "Yemen"
msgstr "Jemen"

#: modules/geo/classes/api.php:386
msgid "Western Samoa"
msgstr "Zahodna Samoa"

#: modules/geo/classes/api.php:385
msgid "Western Sahara"
msgstr "Zahodna Sahara"

#: modules/geo/classes/api.php:384
msgid "Wallis and Futuna Islands"
msgstr "Otoki Wallis in Futuna"

#: modules/geo/classes/api.php:383
msgid "Virgin Islands (USA)"
msgstr "Deviški otoki (ZDA)"

#: modules/geo/classes/api.php:382
msgid "Virgin Islands (British)"
msgstr "Deviški otoki (britanski)"

#: modules/geo/classes/api.php:381
msgid "Vietnam"
msgstr "Vietnam"

#: modules/geo/classes/api.php:380
msgid "Venezuela"
msgstr "Venezuela"

#: modules/geo/classes/api.php:379
msgid "Vanuatu"
msgstr "Vanuatu"

#: modules/geo/classes/api.php:378
msgid "Uzbekistan"
msgstr "Uzbekistan"

#: modules/geo/classes/api.php:377
msgid "US Minor Outlying Islands"
msgstr "Mali oddaljeni otoki ZDA"

#: modules/geo/classes/api.php:376
msgid "Uruguay"
msgstr "Urugvaj"

#: modules/geo/admin/views/setting-download.php:57
msgid "Update geo location databases"
msgstr "Posodobi podatkovne baze geo lokacij"

#: modules/geo/classes/api.php:129
msgid "United States"
msgstr "Združene države Amerike"

#: modules/geo/classes/api.php:130
msgid "United Kingdom"
msgstr "Združeno kraljestvo"

#: modules/geo/classes/api.php:375
msgid "United Arab Emirates"
msgstr "Združeni arabski emirati"

#: modules/geo/classes/api.php:374
msgid "Ukraine"
msgstr "Ukrajina"

#: modules/geo/classes/api.php:373
msgid "Uganda"
msgstr "Uganda"

#: modules/geo/classes/api.php:372
msgid "Tuvalu"
msgstr "Tuvalu"

#: modules/geo/classes/api.php:371
msgid "Turks and Caicos Islands"
msgstr "Otoki Turks in Caicos"

#: modules/geo/classes/api.php:370
msgid "Turkmenistan"
msgstr "Turkmenistan"

#: modules/geo/classes/api.php:369
msgid "Turkey"
msgstr "Turčija"

#: modules/geo/classes/api.php:368
msgid "Tunisia"
msgstr "Tunizija"

#: modules/geo/classes/api.php:367
msgid "Trinidad and Tobago"
msgstr "Trinidad in Tobago"

#: modules/geo/classes/api.php:366
msgid "Tonga"
msgstr "Tonga"

#: modules/geo/classes/api.php:365
msgid "Tokelau"
msgstr "Tokelau"

#: modules/geo/classes/api.php:364
msgid "Togo"
msgstr "Togo"

#: modules/geo/classes/api.php:363
msgid "Timor-Leste"
msgstr "Vzhodni Timor"

#: modules/geo/classes/admin.php:351
msgid "This option can’t be used if your site is using sucuri.net."
msgstr "Ta možnost se ne more uporabiti, če tvoja stran uporablja sucuri.net."

#: modules/geo/classes/admin.php:441
msgid "The upload dir is not available"
msgstr "Mapa za nalaganje (upload) ni na voljo"

#: modules/geo/admin/views/setting-download.php:31
msgid "The MaxMind license key is missing."
msgstr "Licenčni ključ za MaxMind manjka."

#: modules/geo/admin/views/setting-download.php:91
msgid "The databases are updated on the first Tuesday (midnight, GMT) of each month."
msgstr "Podatkovne baze se posodobijo prvi ponedeljek (opolnoči GMT) v vsakem mesecu."

#: modules/geo/classes/api.php:362
msgid "Thailand"
msgstr "Tajska"

#: modules/geo/classes/api.php:361
msgid "Tanzania"
msgstr "Tanzanija"

#: modules/geo/classes/api.php:360
msgid "Tajikistan"
msgstr "Tadžikistan"

#: modules/geo/classes/api.php:359
msgid "Taiwan"
msgstr "Tajvan"

#: modules/geo/classes/api.php:358
msgid "Syrian Arab Republic"
msgstr "Sirska arabska republika"

#: modules/geo/classes/api.php:357
msgid "Switzerland"
msgstr "Švica"

#: modules/geo/classes/api.php:356
msgid "Sweden"
msgstr "Švedska"

#: modules/geo/classes/api.php:355
msgid "Swaziland"
msgstr "Svazi"

#: modules/geo/classes/api.php:354
msgid "Svalbard and Jan Mayen Islands"
msgstr "Otoki Svalbard in Jan Mayen"

#: modules/geo/classes/api.php:353
msgid "Suriname"
msgstr "Surinam"

#: modules/geo/classes/api.php:352
msgid "Sudan"
msgstr "Sudan"

#: modules/geo/classes/plugin.php:130
msgid "Sucuri Header (country only)"
msgstr "Glava Sucuri (samo država)"

#: modules/geo/classes/admin.php:333
msgid "State/Region"
msgstr "Zvezna država/regija"

#: modules/geo/classes/api.php:351
msgid "Sri Lanka"
msgstr "Šrilanka"

#: modules/geo/classes/api.php:350
msgid "Spain"
msgstr "Španija"

#: modules/geo/classes/api.php:349
msgid "South Sudan"
msgstr "Južni Sudan"

#: modules/geo/classes/api.php:348
msgid "South Korea"
msgstr "Južna Koreja"

#: modules/geo/classes/api.php:347
msgid "South Georgia"
msgstr "Južna Gruzija / Južna Georgia"

#: modules/geo/classes/api.php:135
msgid "South America"
msgstr "Južna Amerika"

#: modules/geo/classes/api.php:346
msgid "South Africa"
msgstr "Južna Afrika"

#: modules/geo/classes/api.php:345
msgid "Somalia"
msgstr "Somalija"

#: modules/geo/classes/api.php:344
msgid "Solomon Islands"
msgstr "Salomonovi otoki"

#: modules/geo/classes/api.php:343
msgid "Slovenia"
msgstr "Slovenija"

#: modules/geo/classes/api.php:342
msgid "Slovak Republic"
msgstr "Slovaška"

#: modules/geo/classes/api.php:341
msgid "Singapore"
msgstr "Singapor"

#: modules/geo/classes/api.php:340
msgid "Sierra Leone"
msgstr "Sierra Leone"

#: modules/geo/classes/api.php:339
msgid "Seychelles"
msgstr "Sejšeli"

#: modules/geo/classes/api.php:338
msgid "Serbia"
msgstr "Srbija"

#: modules/geo/classes/api.php:337
msgid "Senegal"
msgstr "Senegal"

#: modules/geo/classes/admin.php:390
msgid "Search"
msgstr "Iskanje"

#: modules/geo/classes/api.php:336
msgid "Saudi Arabia"
msgstr "Savdska Arabija"

#: modules/geo/classes/api.php:334
msgid "San Marino"
msgstr "San Marino"

#: modules/geo/classes/api.php:333
msgid "Saint Vincent and the Grenadines"
msgstr "Sveti Vincent in Grenadini"

#: modules/geo/classes/api.php:332
msgid "Saint Pierre and Miquelon"
msgstr "Sveti Pierre in Mikelon / Saint Pierre in Miquelon"

#: modules/geo/classes/api.php:330
msgid "Saint Martin (French)"
msgstr "Sveti Martin (francoski)"

#: modules/geo/classes/api.php:331
msgid "Saint Martin (Dutch)"
msgstr "Sveti Martin (nizozemski)"

#: modules/geo/classes/api.php:329
msgid "Saint Lucia"
msgstr "Sveta Lucija"

#: modules/geo/classes/api.php:328
msgid "Saint Kitts and Nevis"
msgstr "Saint Kitts in Nevis"

#: modules/geo/classes/api.php:327
msgid "Saint Helena"
msgstr "Sveta Helena"

#: modules/geo/classes/api.php:326
msgid "Saint Barth&eacute;lemy"
msgstr "Sveti Bartolomej / Saint Barthélemy"

#: modules/geo/classes/api.php:335
msgid "S&atilde;o Tom&eacute; and Pr&iacute;ncipe"
msgstr "S&atilde;o Tom&eacute; in Pr&iacute;ncipe"

#: modules/geo/classes/api.php:325
msgid "Rwanda"
msgstr "Ruanda"

#: modules/geo/classes/api.php:324
msgid "Russian Federation"
msgstr "Rusija"

#: modules/geo/classes/api.php:323
msgid "Romania"
msgstr "Romunija"

#: modules/geo/classes/api.php:322
msgid "Reunion Island"
msgstr "Otok Reunion"

#: modules/geo/classes/api.php:321
msgid "Republic of Kosovo"
msgstr "Kosovo"

#: modules/geo/classes/api.php:320
msgid "Qatar"
msgstr "Katar"

#: modules/geo/classes/api.php:319
msgid "Puerto Rico"
msgstr "Portoriko"

#: modules/geo/classes/api.php:318
msgid "Portugal"
msgstr "Portugalska"

#: modules/geo/classes/api.php:317
msgid "Poland"
msgstr "Poljska"

#. translators: 1: opening <a>-tag to Advanced Ads manual, 2: closing <a>-tag
#: modules/geo/admin/views/setting-download.php:36
#: modules/geo/classes/admin.php:234
msgid "Please read the %1$sinstallation instructions%2$s."
msgstr "Prosim preberi %1$snavodila namestitve%2$s."

#: modules/geo/classes/api.php:316
msgid "Pitcairn Island"
msgstr "Otok Pitcairn"

#: modules/geo/classes/api.php:315
msgid "Phillipines"
msgstr "Filipini"

#: modules/geo/classes/api.php:314
msgid "Peru"
msgstr "Peru"

#: modules/geo/classes/api.php:313
msgid "Paraguay"
msgstr "Paragvaj"

#: modules/geo/classes/api.php:312
msgid "Papua New Guinea"
msgstr "Papua Nova Gvineja"

#: modules/geo/classes/api.php:311
msgid "Panama"
msgstr "Panama"

#: modules/geo/classes/api.php:310
msgid "Palestinian Territories"
msgstr "Palestinska ozemlja"

#: modules/geo/classes/api.php:309
msgid "Palau"
msgstr "Palau"

#: modules/geo/classes/api.php:308
msgid "Pakistan"
msgstr "Pakistan"

#: modules/geo/classes/admin.php:332
#: modules/geo/classes/admin.php:334
msgid "or"
msgstr "ali"

#: modules/geo/classes/api.php:307
msgid "Oman"
msgstr "Oman"

#: modules/geo/classes/api.php:139
msgid "Oceania"
msgstr "Oceanija"

#: modules/geo/classes/api.php:306
msgid "Norway"
msgstr "Norveška"

#: modules/geo/classes/api.php:305
msgid "Northern Mariana Islands"
msgstr "Severni Marianski otoki"

#: modules/geo/classes/api.php:304
msgid "North Korea"
msgstr "Severna Koreja"

#: modules/geo/classes/api.php:134
msgid "North America"
msgstr "Severna Amerika"

#: modules/geo/classes/api.php:303
msgid "Norfolk Island"
msgstr "Otok Norfolk"

#: modules/geo/classes/api.php:302
msgid "Niue"
msgstr "Niue"

#: modules/geo/classes/api.php:301
msgid "Nigeria"
msgstr "Nigerija"

#: modules/geo/classes/api.php:300
msgid "Niger"
msgstr "Niger"

#: modules/geo/classes/api.php:299
msgid "Nicaragua"
msgstr "Nikaragva"

#. translators: Timestamp in the localized date_format
#: modules/geo/admin/views/setting-download.php:84
msgid "Next possible update on %s."
msgstr "Naslednja možna posodobitev na %s."

#: modules/geo/classes/api.php:298
msgid "New Zealand"
msgstr "Nova Zelandija"

#: modules/geo/classes/api.php:297
msgid "New Caledonia"
msgstr "Nova Kaledonija"

#: modules/geo/classes/api.php:296
msgid "Netherlands Antilles"
msgstr "Nizozemski Antili"

#: modules/geo/classes/api.php:295
msgid "Netherlands"
msgstr "Nizozemska"

#: modules/geo/classes/api.php:294
msgid "Nepal"
msgstr "Nepal"

#: modules/geo/classes/api.php:293
msgid "Nauru"
msgstr "Nauru"

#: modules/geo/classes/api.php:292
msgid "Namibia"
msgstr "Namibija"

#: modules/geo/classes/api.php:291
msgid "Myanmar"
msgstr "Mjanmar"

#: modules/geo/classes/api.php:290
msgid "Mozambique"
msgstr "Mozambik"

#: modules/geo/classes/api.php:289
msgid "Morocco"
msgstr "Maroko"

#: modules/geo/classes/api.php:288
msgid "Montserrat"
msgstr "Montserrat"

#: modules/geo/classes/api.php:287
msgid "Montenegro"
msgstr "Črna gora"

#: modules/geo/classes/api.php:286
msgid "Mongolia"
msgstr "Mongolija"

#: modules/geo/classes/api.php:285
msgid "Monaco"
msgstr "Monako"

#: modules/geo/classes/api.php:284
msgid "Moldova, Republic of"
msgstr "Moldavija"

#: modules/geo/classes/api.php:283
msgid "Micronesia"
msgstr "Mikronezija"

#: modules/geo/classes/api.php:282
msgid "Mexico"
msgstr "Mehika"

#: modules/geo/classes/admin.php:76
msgid "Method"
msgstr "Metoda"

#: modules/geo/classes/api.php:281
msgid "Mayotte"
msgstr "Mayotte"

#: modules/geo/classes/admin.php:241
msgid "Maybe you are working on a local or secured environment."
msgstr "Morda delaš nalokalnem ali zavarovanem okolju."

#: modules/geo/classes/admin.php:57
msgid "MaxMind license key"
msgstr "Licenčni ključ MaxMind"

#: modules/geo/classes/plugin.php:124
msgid "MaxMind database (default)"
msgstr "Podatkovna baza MaxMind (privzeto)"

#: modules/geo/classes/admin.php:66
msgid "MaxMind Database"
msgstr "Podatkovna baza MaxMind"

#: modules/geo/classes/api.php:280
msgid "Mauritius"
msgstr "Mavricij"

#: modules/geo/classes/api.php:279
msgid "Mauritania"
msgstr "Mavretanija"

#: modules/geo/classes/api.php:278
msgid "Martinique"
msgstr "Martinik"

#: modules/geo/classes/api.php:277
msgid "Marshall Islands"
msgstr "Marshallovi otoki"

#: modules/geo/classes/api.php:276
msgid "Malta"
msgstr "Malta"

#: modules/geo/classes/api.php:275
msgid "Mali"
msgstr "Mali"

#: modules/geo/classes/api.php:274
msgid "Maldives"
msgstr "Maldivi"

#: modules/geo/classes/api.php:273
msgid "Malaysia"
msgstr "Malazija"

#: modules/geo/classes/api.php:272
msgid "Malawi"
msgstr "Malavi"

#: modules/geo/classes/api.php:271
msgid "Madagascar"
msgstr "Madagaskar"

#: modules/geo/classes/api.php:270
msgid "Macedonia"
msgstr "Makedonija"

#: modules/geo/classes/api.php:269
msgid "Macau"
msgstr "Macau"

#: modules/geo/classes/api.php:268
msgid "Luxembourg"
msgstr "Luksemburg"

#: modules/geo/classes/admin.php:373
msgid "Longitude"
msgstr "Zemljepisna dolžina"

#: modules/geo/classes/api.php:267
msgid "Lithuania"
msgstr "Litva"

#: modules/geo/classes/api.php:266
msgid "Liechtenstein"
msgstr "Lihtenštajn"

#: modules/geo/classes/api.php:265
msgid "Libyan Arab Jamahiriya"
msgstr "Libijska Arabska Džamahirija"

#: modules/geo/classes/api.php:264
msgid "Liberia"
msgstr "Liberija"

#: modules/geo/classes/api.php:263
msgid "Lesotho"
msgstr "Lesoto"

#: modules/geo/classes/api.php:262
msgid "Lebanon"
msgstr "Libanon"

#: modules/geo/classes/api.php:261
msgid "Latvia"
msgstr "Latvija"

#: modules/geo/classes/admin.php:372
msgid "Latitude"
msgstr "Zemljepisna širina"

#. translators: Timestamp in the localized date_format
#: modules/geo/admin/views/setting-download.php:73
msgid "Last update: %s"
msgstr "Zadnja posodobitev: %s"

#: modules/geo/classes/api.php:260
msgid "Lao People's Democratic Republic"
msgstr "Laoška ljudska demokratična republika"

#: modules/geo/classes/admin.php:86
msgid "Language of names"
msgstr "Jezik imen"

#: modules/geo/classes/api.php:259
msgid "Kyrgyzstan"
msgstr "Kirgizistan"

#: modules/geo/classes/api.php:258
msgid "Kuwait"
msgstr "Kuvajt"

#: modules/geo/classes/api.php:257
msgid "Kiribati"
msgstr "Kiribati"

#: modules/geo/classes/api.php:256
msgid "Kenya"
msgstr "Kenija"

#: modules/geo/classes/api.php:255
msgid "Kazakhstan"
msgstr "Kazahstan"

#: modules/geo/classes/api.php:254
msgid "Jordan"
msgstr "Jordanija"

#: modules/geo/classes/api.php:253
msgid "Jersey"
msgstr "Jersey"

#: modules/geo/classes/api.php:252
msgid "Japan"
msgstr "Japonska"

#: modules/geo/classes/api.php:251
msgid "Jamaica"
msgstr "Jamajka"

#: modules/geo/classes/api.php:250
msgid "Italy"
msgstr "Italija"

#. translators: %s is a URL.
#: modules/cache-busting/admin-ui.class.php:218
msgid "It seems that a caching plugin is activated. Your ads might not rotate properly, while cache busting is disabled for the placement your group is using. <a href=\"#\" data-placement=\"%s\" class=\"js-placement-activate-cb\">Activate cache busting for this placement.</a>"
msgstr "Izgleda, da je aktiven vtičnik za predpomnjenje (caching). Tvoji oglasi se morda na menjujejo pravilno, medtem ko je cache busting izklopljeno za postavitev, ki jo uporablja tvoja skupina.<a href=\"#\" data-placement=\"%s\" class=\"js-placement-activate-cb\">Aktiviraj cache busting za to postavitev.</a>"

#. translators: %s is a URL.
#: modules/cache-busting/admin-ui.class.php:173
msgid "It seems that a caching plugin is activated. Your ads might not rotate properly while cache busting is disabled. <a href=\"%s\" target=\"_blank\">Activate cache busting.</a>"
msgstr "gleda, da je aktiven vtičnik za predpomnjenje (caching). Tvoji oglasi se morda na menjujejo pravilno, medtem ko je cache busting izklopljen. <a href=\"%s\" target=\"_blank\">Aktiviraj cache busting.</a>"

#: modules/geo/classes/api.php:249
msgid "Israel"
msgstr "Izrael"

#: modules/geo/classes/api.php:248
msgid "Isle of Man"
msgstr "Otok Man"

#: modules/geo/classes/api.php:247
msgid "Ireland"
msgstr "Irska"

#: modules/geo/classes/api.php:246
msgid "Iraq"
msgstr "Irak"

#: modules/geo/classes/api.php:245
msgid "Iran"
msgstr "Iran"

#: modules/geo/classes/api.php:244
msgid "Indonesia"
msgstr "Indonezija"

#: modules/geo/classes/api.php:243
msgid "India"
msgstr "Indija"

#: modules/geo/admin/views/setting-download.php:50
msgid "In order to use Geo Targeting, please download the geo location databases by clicking on the button below."
msgstr "Da lahko uporabiš geo ciljanje, prosim prenesi podatkovne baze geo lokacij s klikom na spodnji gumb."

#: modules/click-fraud-protection/click-fraud-protection.class.php:156
msgid "Ignore global Click Fraud Protection"
msgstr "Ignoriraj globalno zaščito goljufije klikov (Click Fraud Protection)"

#: modules/geo/classes/api.php:242
msgid "Iceland"
msgstr "Islandija"

#: modules/geo/classes/api.php:241
msgid "Hungary"
msgstr "Madžarska"

#: modules/geo/classes/api.php:240
msgid "Hong Kong"
msgstr "Hong Kong"

#: modules/geo/classes/api.php:239
msgid "Honduras"
msgstr "Honduras"

#: modules/geo/classes/api.php:238
msgid "Holy See (City Vatican State)"
msgstr "Sveti sedež (mesto Vatikan)"

#: modules/geo/classes/api.php:237
msgid "Heard and McDonald Islands"
msgstr "Otoki Heard in McDonald"

#: modules/geo/classes/api.php:236
msgid "Haiti"
msgstr "Haiti"

#: modules/geo/classes/api.php:235
msgid "Guyana"
msgstr "Gvajana"

#: modules/geo/classes/api.php:234
msgid "Guinea-Bissau"
msgstr "Gvineja Bissau"

#: modules/geo/classes/api.php:233
msgid "Guinea"
msgstr "Gvineja"

#: modules/geo/classes/api.php:232
msgid "Guernsey"
msgstr "Guernsey"

#: modules/geo/classes/api.php:231
msgid "Guatemala"
msgstr "Gvatemala"

#: modules/geo/classes/api.php:230
msgid "Guam"
msgstr "Guam"

#: modules/geo/classes/api.php:229
msgid "Guadeloupe"
msgstr "Guadeloupe"

#: modules/geo/classes/api.php:228
msgid "Grenada"
msgstr "Grenada"

#: modules/geo/classes/api.php:227
msgid "Greenland"
msgstr "Grenlandija"

#: modules/geo/classes/api.php:224
msgid "Greece"
msgstr "Grčija"

#: modules/geo/classes/api.php:226
msgid "Gibraltar"
msgstr "Gibraltar"

#: modules/geo/classes/api.php:225
msgid "Ghana"
msgstr "Gana"

#: modules/geo/classes/admin.php:370
msgid "get coordinates"
msgstr "pridobi koordinate"

#: modules/geo/classes/api.php:132
msgid "Germany"
msgstr "Nemčija"

#: modules/geo/classes/api.php:223
msgid "Georgia"
msgstr "Gruzija"

#: modules/geo/classes/admin.php:49
msgid "Geo Targeting"
msgstr "Geo ciljanje"

#: modules/geo/classes/plugin.php:105
msgid "geo location"
msgstr "geolokacija"

#: modules/geo/admin/views/setting-download.php:47
#: modules/geo/classes/admin.php:231
msgid "Geo Databases not found."
msgstr "Ne najdem geo podatkovne baze."

#: modules/geo/classes/public.php:222
msgid "Geo Databases not found"
msgstr "Ne najdem geo podatkovne baze"

#: modules/geo/admin/views/setting-download.php:16
msgid "Geo Databases found."
msgstr "Geo podatkovne baze so bile najdene."

#: modules/geo/classes/api.php:222
msgid "Gambia"
msgstr "Gambija"

#: modules/geo/classes/api.php:221
msgid "Gabon"
msgstr "Gabon"

#: modules/geo/classes/admin.php:368
msgid "from"
msgstr "iz"

#: modules/geo/classes/api.php:220
msgid "French Southern Territories"
msgstr "Francoska južna ozemlja"

#: modules/geo/classes/api.php:219
msgid "French Polynesia"
msgstr "Francoska Polinezija"

#: modules/geo/classes/api.php:218
msgid "French Guiana"
msgstr "Francoska Gvajana"

#: modules/geo/classes/api.php:217
msgid "France"
msgstr "Francija"

#: modules/geo/classes/api.php:216
msgid "Finland"
msgstr "Finska"

#: modules/geo/classes/api.php:215
msgid "Fiji"
msgstr "Fidži"

#: modules/geo/classes/api.php:214
msgid "Faroe Islands"
msgstr "Ferski otoki"

#: modules/geo/classes/api.php:213
msgid "Falkland Islands"
msgstr "Falklandski otoki"

#: modules/geo/classes/api.php:131
msgid "European Union"
msgstr "Evropska unija"

#: modules/geo/classes/api.php:136
msgid "Europe"
msgstr "Evropa"

#: modules/geo/classes/api.php:212
msgid "Ethiopia"
msgstr "Etiopija"

#: modules/geo/classes/api.php:211
msgid "Estonia"
msgstr "Estonija"

#: modules/geo/classes/api.php:210
msgid "Eritrea"
msgstr "Eritreja"

#: modules/geo/classes/api.php:208
msgid "Equatorial Guinea"
msgstr "Ekvatorialna Gvineja"

#: modules/geo/classes/admin.php:393
msgid "Enter the name of the city, click the search button and pick one of the results to set the coordinates of the center."
msgstr "Vpiši ime mesta, klikni gumb za iskanje in izberi enega izmed rezultatov, da nastaviš koordinate centra."

#: modules/geo/classes/api.php:209
msgid "El Salvador"
msgstr "El Salvador"

#: modules/geo/classes/api.php:207
msgid "Egypt"
msgstr "Egipt"

#: modules/geo/classes/api.php:206
msgid "Ecuador"
msgstr "Ekvador"

#: modules/geo/classes/api.php:205
msgid "East Timor"
msgstr "Vzhodni Timor"

#: modules/geo/classes/api.php:204
msgid "Dominican Republic"
msgstr "Dominikanska republika"

#: modules/geo/classes/api.php:203
msgid "Dominica"
msgstr "Dominika"

#: modules/geo/classes/api.php:202
msgid "Djibouti"
msgstr "Džibuti"

#: modules/geo/classes/admin.php:378
msgid "Distance to center"
msgstr "Razdalja do centra"

#: modules/geo/classes/admin.php:361
msgid "Distance is less than or equal to"
msgstr "Razdalja je manj kot ali enaka"

#: modules/geo/classes/admin.php:362
msgid "Distance is greater than"
msgstr "Razdalja je več kot"

#: modules/geo/classes/admin.php:364
msgid "Distance"
msgstr "Razdalja"

#: modules/geo/classes/plugin.php:106
msgid "Display ads based on geo location."
msgstr "Prikaži oglase glede na geolokacijo."

#: modules/geo/classes/api.php:201
msgid "Denmark"
msgstr "Danska"

#: modules/geo/classes/admin.php:518
msgid "Database updated successfully!"
msgstr "Podatkovna baza je bila uspešno posodobljena!"

#: modules/geo/classes/admin.php:536
msgid "Database update failed"
msgstr "Posodobitev podatkovne baze ni uspela"

#: modules/geo/classes/api.php:200
msgid "Czech Republic"
msgstr "Češka"

#: modules/geo/classes/api.php:199
msgid "Cyprus"
msgstr "Ciper"

#: modules/geo/classes/api.php:198
msgid "Cura&Ccedil;ao"
msgstr "Cura&Ccedil;ao"

#: modules/geo/classes/api.php:197
msgid "Cuba"
msgstr "Kuba"

#: modules/geo/classes/api.php:196
msgid "Croatia/Hrvatska"
msgstr "Hrvaška"

#. translators: an error mesage.
#: modules/geo/classes/admin.php:573
msgid "Could not open downloaded database for reading: %s"
msgstr "Ni bilo možno odpreti prenesene podatkovne baze za branje: %s"

#. translators: MaxMind database file name.
#: modules/geo/classes/admin.php:602
msgid "Could not open database for writing %s"
msgstr "Ni bilo mogoče odpreti podatkovne baze za pisanje %s"

#: modules/geo/classes/admin.php:589
msgid "Could not access filesystem"
msgstr "Ni bilo mogoče dostopati do podatkovnega sistema"

#: modules/geo/classes/api.php:195
msgid "Cote d'Ivoire"
msgstr "Slonokoščena obala"

#: modules/geo/classes/api.php:194
msgid "Costa Rica"
msgstr "Kostarika"

#: modules/geo/admin/views/visitor-profile.php:15
#: modules/geo/classes/admin.php:252
msgid "Coordinates"
msgstr "Koordinate"

#: modules/geo/classes/api.php:193
msgid "Cook Islands"
msgstr "Cookovi otoki"

#: modules/geo/classes/api.php:192
msgid "Congo, Republic of"
msgstr "Republika Kongo"

#: modules/geo/classes/api.php:191
msgid "Congo, Democratic People's Republic"
msgstr "Kongo, Demokratična ljudska republika"

#: modules/geo/classes/api.php:190
msgid "Comoros"
msgstr "Komori"

#: modules/geo/classes/api.php:189
msgid "Colombia"
msgstr "Kolumbija"

#: modules/geo/classes/api.php:188
msgid "Cocos Islands"
msgstr "Kokosovi otoki"

#: modules/geo/classes/admin.php:335
#: modules/geo/classes/admin.php:388
msgid "City"
msgstr "Mesto"

#: modules/geo/classes/api.php:187
msgid "Christmas Island"
msgstr "Božični otok"

#: modules/geo/admin/views/setting-locale.php:15
msgid "Choose the language of the state/region or city entered. If the language is not available in the geo location database, it will check against the English version."
msgstr "Izberi jezik zvezne države/regije ali vneseno mesto. Če jezik ni na voljo v podatkovni bazi geo lokacije, bo preverilo proti angleški verziji."

#: modules/geo/classes/api.php:186
msgid "China"
msgstr "Kitajska"

#: modules/geo/classes/api.php:185
msgid "Chile"
msgstr "Čile"

#: modules/geo/classes/api.php:184
msgid "Chad"
msgstr "Čad"

#: modules/geo/classes/api.php:183
msgid "Central African Republic"
msgstr "Centralna afriška republika"

#: modules/geo/classes/api.php:182
msgid "Cayman Islands"
msgstr "Kajmanski otoki"

#: modules/geo/classes/api.php:181
msgid "Cape Verde"
msgstr "Zelenortski otoki"

#: modules/geo/classes/admin.php:391
msgid "Cancel"
msgstr "Prekliči"

#: modules/geo/classes/api.php:180
msgid "Canada"
msgstr "Kanada"

#: modules/geo/classes/api.php:179
msgid "Cameroon"
msgstr "Kamerun"

#: modules/geo/classes/api.php:178
msgid "Cambodia"
msgstr "Kambodža"

#: modules/geo/classes/admin.php:288
msgid "by specific location"
msgstr "po določeni lokaciji"

#: modules/geo/classes/admin.php:294
msgid "by radius"
msgstr "po radiju"

#: modules/geo/classes/api.php:177
msgid "Burundi"
msgstr "Burundi"

#: modules/geo/classes/api.php:176
msgid "Burkina Faso"
msgstr "Burkina Faso"

#: modules/geo/classes/api.php:175
msgid "Bulgaria"
msgstr "Bulgarija"

#: modules/geo/classes/api.php:174
msgid "Brunei Darrussalam"
msgstr "Brunej Darusalam"

#: modules/geo/classes/api.php:173
msgid "British Indian Ocean Territory"
msgstr "Britansko ozemlje Indijskega oceana"

#: modules/geo/classes/api.php:172
msgid "Brazil"
msgstr "Brazilija"

#: modules/geo/classes/api.php:171
msgid "Bouvet Island"
msgstr "Otok Bouvet"

#: modules/geo/classes/api.php:170
msgid "Botswana"
msgstr "Bocvana"

#: modules/geo/classes/api.php:169
msgid "Bosnia and Herzegovina"
msgstr "Bosna in Hercegovina"

#: modules/geo/classes/api.php:168
msgid "Bonaire, Saint Eustatius and Saba"
msgstr "Bonaire, Sveti Evstatij in Saba"

#: modules/geo/classes/api.php:167
msgid "Bolivia"
msgstr "Bolivija"

#: modules/geo/classes/api.php:166
msgid "Bhutan"
msgstr "Butan"

#: modules/geo/classes/api.php:165
msgid "Bermuda"
msgstr "Bermudi"

#: modules/geo/classes/api.php:164
msgid "Benin"
msgstr "Benin"

#: modules/geo/classes/api.php:163
msgid "Belize"
msgstr "Belize"

#: modules/geo/classes/api.php:162
msgid "Belgium"
msgstr "Belgija"

#: modules/geo/classes/api.php:161
msgid "Belarus"
msgstr "Belorusija"

#: modules/geo/classes/api.php:160
msgid "Barbados"
msgstr "Barbados"

#: modules/geo/classes/api.php:159
msgid "Bangladesh"
msgstr "Bangladeš"

#: modules/geo/classes/api.php:158
msgid "Bahrain"
msgstr "Bahrajn"

#: modules/geo/classes/api.php:157
msgid "Bahamas"
msgstr "Bahami"

#: modules/geo/classes/api.php:156
msgid "Azerbaijan"
msgstr "Azerbajdžan"

#: modules/geo/classes/api.php:155
msgid "Austria"
msgstr "Avstrija"

#: modules/geo/classes/api.php:154
msgid "Australia"
msgstr "Avstralija"

#: modules/geo/classes/api.php:138
msgid "Asia"
msgstr "Azija"

#: modules/geo/classes/api.php:153
msgid "Aruba"
msgstr "Aruba"

#: modules/geo/classes/api.php:152
msgid "Armenia"
msgstr "Armenija"

#: modules/geo/classes/api.php:151
msgid "Argentina"
msgstr "Argentina"

#: modules/geo/classes/api.php:150
msgid "Antigua and Barbuda"
msgstr "Antigva in Barbuda"

#: modules/geo/classes/api.php:149
msgid "Antarctica"
msgstr "Antarktika"

#: modules/geo/classes/api.php:148
msgid "Anguilla"
msgstr "Angvila"

#: modules/geo/classes/api.php:147
msgid "Angola"
msgstr "Angola"

#: modules/geo/classes/api.php:146
msgid "Andorra"
msgstr "Andora"

#: modules/geo/classes/api.php:145
msgid "American Samoa"
msgstr "Ameriška Samoa"

#: modules/geo/classes/api.php:144
msgid "Algeria"
msgstr "Alžirija"

#: modules/geo/classes/api.php:143
msgid "Albania"
msgstr "Albanija"

#: modules/geo/classes/api.php:137
msgid "Africa"
msgstr "Afrika"

#: modules/geo/classes/api.php:141
msgid "Afghanistan"
msgstr "Afganistan"

#. translators: an error message.
#: modules/geo/classes/public.php:227
msgid "Address not found: %s"
msgstr "Naslov ni bil najden: %s"

#: modules/group-refresh/views/settings_group_refresh.php:21
#: views/admin/tables/ads/adsense-fallback.php:77
msgid "Activate now"
msgstr "Aktiviraj zdaj"

#: modules/geo/classes/admin.php:219
#: modules/geo/classes/public.php:208
#: modules/geo/classes/public.php:372
msgid "(unknown region)"
msgstr "(neznana regija)"

#: modules/geo/classes/admin.php:208
#: modules/geo/classes/public.php:200
#: modules/geo/classes/public.php:368
msgid "(unknown city)"
msgstr "(neznano mesto)"

#: modules/geo/classes/api.php:142
msgid "&#197;land Islands"
msgstr "&#197;landski otoki"

#: modules/lazy-load/admin.class.php:23
#: modules/lazy-load/admin.class.php:64
#: views/admin/placements/bulk-edit.php:27
msgid "Lazy Loading"
msgstr "Zakasnelo nalaganje (Lazy loading)"

#: views/setting-placement-positioning.php:29
msgid "This method also works on AMP pages and causes fewer conflicts with website optimization features. However, it can cause critical issues with a few other plugins that use a similar technique (i.e., output buffering). We recommend less technical users test it carefully."
msgstr "Ta metoda deluje tudi na straneh AMP in povzroča manj konfliktov s funkcijami optimizacije spletne strani. Toda lahko povzroči kritične težave s par drugimi vtičniki, ki uporabijo enako tehniko (npr. output buffering). Manj tehničnim uporabnikom priporočamo previdno testiranje."

#: classes/advanced-ads-pro-admin.php:148
msgid "Placement positioning"
msgstr "Pozicioniranje postavitev"

#. translators: %s: Names of placement types
#: views/setting-placement-positioning.php:13
msgid "Choose when Advanced Ads will add the following placement types: %s"
msgstr "Izberi, kdaj naj Advanced Ads doda naslednje vrste postavitev: %s"

#: views/setting-placement-positioning.php:24
msgid "Before page load using PHP."
msgstr "Pred naloženjem strani s pomočjo PHP."

#: views/setting-placement-positioning.php:19
msgid "After page load using JavaScript"
msgstr "Po naloženju strani z uporabo JavaScripta"

#: modules/advanced-display-conditions/main.class.php:116
msgid "post content"
msgstr "vsebina objave"

#: modules/advanced-display-conditions/main.class.php:117
msgid "Display ads based on words and phrases within the post or page content. Dynamically added text might not be considered."
msgstr "Prikaži oglase glede na besede ali fraze znotraj prispevka ali vsebine strani. Dinamično dodano besedilo se morda ne bo upoštevalo."

#: modules/gamipress/views/visitor-condition-rank.php:28
msgid "Choose rank"
msgstr "Izberi nivo"

#: modules/gamipress/views/visitor-condition-points.php:17
msgid "Choose Points Type"
msgstr "Izberi vrsto točk"

#: modules/gamipress/views/visitor-condition-achievement.php:21
msgid "Choose Achievement Type"
msgstr "Izberi vrsto dosežka"

#: modules/gamipress/admin.class.php:34
msgid "Display ads based on GamiPress user achievements."
msgstr "Prikaži oglase glede na GamiPress dosežke uporabnikov."

#: modules/gamipress/admin.class.php:33
msgid "GamiPress Achievement"
msgstr "Dosežki GamiPress"

#: modules/gamipress/admin.class.php:28
msgid "Display ads based on GamiPress user ranks."
msgstr "Prikaži oglase glede na GamiPress nivoje uporabnikov."

#: modules/gamipress/admin.class.php:27
msgid "GamiPress Rank"
msgstr "Nivo GamiPress"

#: modules/gamipress/admin.class.php:22
msgid "Display ads based on GamiPress user points."
msgstr "Prikaži oglase glede na GamiPress točke uporabnikov."

#: modules/gamipress/admin.class.php:21
msgid "GamiPress Points"
msgstr "Točke GamiPress"

#: views/privacy-policy-content.php:10
msgid "You can use the text below as a template for your own privacy policy."
msgstr "Spodnje besedilo lahko uporabiš kot vzorec za oblikovanje lastne politike zasebnosti."

#: views/privacy-policy-content.php:13
msgid "This website uses Advanced Ads Pro to place advertisements. The WordPress plugin may use multiple first-party cookies to ensure the correct integration of ads. These cookies store technical information but not IP addresses. Their use is linked to specific features and options when embedding ads."
msgstr "Ta spletna stran uporablja Advanced Ads Pro za postavitev oglasov. Ta WordPress vtičnik lahko uporablja številne lastne piškotke za zagotavljanje pravilne integracije oglasov. Ti piškotki hranijo tehnične podatke brez naslova IP. Njihova uporaba je povezana s posameznimi funkcijami in možnostmi vdelave oglasov."

#: views/privacy-policy-content.php:12
msgid "Suggested Text:"
msgstr "Predlagano besedilo:"

#. translators: %1$s is an opening a tag, %2$s is the corresponding closing one
#: views/privacy-policy-content.php:19
msgid "Please, see the %1$sAdvanced Ads cookie information%2$s for more details."
msgstr "Prosim poglej %1$sAdvanced Ads informacije o piškotkih%2$s za več podrobnosti."

#: views/privacy-policy-content.php:7
msgid "Depending on the setup, Advanced Ads Pro and other add-ons use cookies to control which user sees which ad. They also help to reduce expensive server requests."
msgstr "Glede na nastavitve uporabljajo Advanced Ads Pro in drugi dodatni piškotke za nadzor, kateri obiskovalci vidijo kateri oglas. Prav tako pomagajo zmanjšati velike zahteve za strežnik."

#: classes/advanced-ads-pro-admin.php:531
msgid "Select a Sidebar placement to enable cache-busting."
msgstr "Izberi postavitev stranske vrstice, da omogočiš cache-busting."

#: modules/buddypress/views/position-option.php:56
msgid "member timeline"
msgstr "časovnica članov"

#: classes/advanced-ads-pro-admin.php:533
msgid "Learn more"
msgstr "Preberi več"

#: modules/buddypress/views/position-option.php:55
msgid "group feed"
msgstr "vir (feed) skupine"

#: modules/buddypress/class-buddypress.php:334
msgid "BuddyBoss group"
msgstr "skupina BuddyBoss"

#: modules/buddypress/views/position-option.php:53
msgid "any"
msgstr "katerikoli"

#: modules/buddypress/views/position-option.php:54
msgid "activity stream"
msgstr "tok aktivnosti (activity stream)"

#: modules/cache-busting/views/settings.php:38
msgid "Force passive cache busting"
msgstr "Prisili pasivni cache-busting"

#: modules/cache-busting/views/settings.php:61
msgid "Fallback option"
msgstr "Povratna možnost"

#: modules/cache-busting/views/settings.php:17
msgid "Default option"
msgstr "Privzeta možnost"

#: modules/buddypress/class-buddypress.php:131
msgid "Display ads based on BuddyBoss profile fields."
msgstr "Prikaži oglase glede na BuddyBoss polja profila."

#: modules/buddypress/class-buddypress.php:130
msgid "BuddyBoss profile field"
msgstr "BuddyBoss polja profila"

#: modules/buddypress/views/position-option.php:51
msgid "Stream"
msgstr "Tok (stream)"

#: includes/placements/types/class-buddypress.php:61
msgid "Display ads on BuddyBoss related pages."
msgstr "Prikaži oglase na straneh, povezanih z BuddyBoss"

#: includes/placements/types/class-buddypress.php:49
msgid "BuddyBoss Content"
msgstr "Vsebina BuddyBoss"

#: modules/cache-busting/views/settings.php:42
msgid "By default, cache busting only works through placements."
msgstr "Privzeto deluje cache busting le skozi postavitve."

#: modules/cache-busting/views/settings.php:90
msgid "Update visitor profile"
msgstr "Posodobi profil uporabnika"

#: modules/ad-server/views/placement-usage.php:14
msgid "Direct URL"
msgstr "Direktni URL"

#: modules/ad-server/views/placement-settings.php:11
msgid "The name of the placement that appears in the URL and injection code."
msgstr "Ime postavitve, ki se pokaže v naslovu URL in kodi za vstavitev."

#: modules/ad-server/views/placement-settings.php:10
msgid "Save the page to update the usage code below."
msgstr "Shrani strani, da posodobiš spodnjo kodo uporabe."

#: modules/ad-server/views/module-settings.php:30
msgid "Prevent direct access to the placement URL."
msgstr "Prepreči neposreden dostop do URL-ja postavitve."

#: modules/ad-server/views/module-settings.php:24
msgid "Please don’t enter subdirectories."
msgstr "Prosim ne vpisuj pod-map."

#: modules/ad-server/views/module-settings.php:18
msgid "Separate multiple values with a comma."
msgstr "Več vrednosti loči z vejico."

#: modules/ad-server/views/module-settings.php:18
msgid "Top level domains on which the ads will be loaded."
msgstr "Vrhnje (top level) domene, na katerih bodo naloženi oglasi."

#: modules/ad-server/views/module-settings.php:13
#: modules/ads-for-adblockers/views/settings.php:18
#: modules/advanced-visitor-conditions/views/settings.php:6
#: modules/cache-busting/views/settings.php:12
#: modules/click-fraud-protection/views/settings.php:10
#: modules/lazy-load/views/settings.php:8
#: modules/rest-api/views/module-enable.php:12
msgid "Activate module."
msgstr "Aktiviraj modul"

#: modules/ad-server/admin.class.php:92
msgid "Usage"
msgstr "Uporaba"

#: modules/ad-server/admin.class.php:78
msgid "Public string"
msgstr "Javni ključ"

#: includes/placements/types/class-server.php:56
msgid "Display ads on external websites."
msgstr "Prikaži oglase na zunanjih spletnih straneh."

#: includes/placements/types/class-server.php:47
#: modules/ad-server/admin.class.php:37
msgid "Ad Server"
msgstr "Strežnik oglasov"

#: modules/advanced-visitor-conditions/main.class.php:149
msgid " Display ads based on the user’s capabilities."
msgstr "Prikaži oglase glede na pravice uporabnika."

#: modules/advanced-visitor-conditions/main.class.php:141
msgid "Display ads based on the user agent."
msgstr "Prikaži oglase glede na uporabnikov agent."

#: modules/inject-content/admin.class.php:255
msgid "Words between ads"
msgstr "Besed med oglasi"

#: views/setting_words_between_ads.php:6
msgid "Words are counted within paragraphs, headlines and any other element."
msgstr "Besede se štejejo znotraj odstavkov, naslovov in katerihkoli drugih elementov."

#: views/setting_words_between_ads.php:5
msgid "A minimum amount of words between automatically injected ads."
msgstr "Minimalna količina besed med avtomatsko vstavljenimi oglasi."

#: includes/admin/class-duplicate-placement.php:64
#: includes/admin/class-group-duplication.php:115
#: modules/duplicate-ads/admin.class.php:138
msgctxt "noun"
msgid "copy"
msgstr "kopiraj"

#: modules/inject-content/admin.class.php:124
msgid "Minimum length of content (in words) before automatically injected ads are allowed in them. Set to zero or leave empty to always display ads, regardless of how long the content is."
msgstr "Minimalna dolžila vsebine (v besedah) preden so v njej dovoljeni avtomatsko vstavljeni oglasi. Nastavi na ničlo, da vedno prikažeš oglase, ne glede na dolžino vsebine."

#: includes/admin/class-duplicate-placement.php:102
#: includes/admin/class-group-duplication.php:78
#: modules/duplicate-ads/admin.class.php:88
msgid "Duplicate"
msgstr "Podvoji"

#: modules/duplicate-ads/admin.class.php:88
msgid "Create a copy of this ad"
msgstr "Ustvari kopijo tega oglasa"

#: modules/inject-content/views/setting_post_meta_box.php:4
msgid "Disable automatic ad injection into the content"
msgstr "Onemogoči avtomatsko vstavljanje oglasa v vsebino"

#: modules/cache-busting/views/settings.php:102
msgid "Update visitor profile when user logs in or out"
msgstr "Posodobi uporabnikov profil, ko se prijavi ali odjavi"

#: modules/cache-busting/views/settings.php:92
msgid "You might need to update the page cache if you are using one."
msgstr "Morda boš moral posodobiti predpomnilnik (cache) strani, če ga uporabljaš."

#: modules/cache-busting/views/settings.php:86
msgid "Advanced Ads stores some user information in the user’s browser to limit the number of AJAX requests for cache busting. Manual"
msgstr "Advanced Ads hrani nekaj podatkov o uporabniku v njegovem brskalniku, da omeji število AJAX zahtev za cache busting. Priročnik"

#: modules/cache-busting/views/settings.php:83
msgid "Visitor profile"
msgstr "Profil uporabnika"

#. translators: %s: URL
#: modules/advanced-display-conditions/main.class.php:41
msgid "Display ads based on the current URL parameters (everything following %s), except values following #."
msgstr "Prikaži oglase glede na parametre trenutnega URL (vse, kar sledi %s), razen vrednosti, ki sledijo #."

#: classes/advanced-ads-pro-admin.php:156
msgid "Disable ads for post types"
msgstr "Onemogoči oglase za vrste objav"

#: modules/cache-busting/views/settings.php:94
msgid "An error occurred"
msgstr "Prišlo je do napake"

#: modules/cache-busting/views/settings.php:91
msgid "Updated"
msgstr "Posodobljeno"

#. translators: %s is a placeholder for the URL.
#: modules/advanced-visitor-conditions/main.class.php:163
msgid "Display ads based on the user's roles. See <a href=\"%s\" target=\"_blank\">List of roles in WordPress</a>."
msgstr "Prikaži oglase glede na uporabnikove vloge. Glej <a href=\"%s\" target=\"_blank\">Seznam vlog v WordPressu</a>."

#: modules/advanced-visitor-conditions/main.class.php:160
msgid "user role"
msgstr "vloga uporabnika"

#. translators: %s is a list of supported optimizer plugins
#: views/setting_autoptimize.php:7
msgid "Advanced Ads Pro disables optimizers ( %s ) for displaying ads per default. Enable this option to allow optimizers to change the ad code. Especially JavaScript ads might stop working then."
msgstr "Advanced Ads Pro privzeto onemogoči optimizatorje ( %s ) za prikaz oglasov. Omogoči to možnost, da spremeniš kodo oglasa. Predvsem JavaScript oglasi lahko potem nehajo delovati."

#: classes/advanced-ads-pro-admin.php:139
msgid "Allow optimizers to modify ad codes"
msgstr "Omogoči, da optimizatorji spremenijo kode oglasa"

#. translators: %s: input field
#: modules/lazy-load/views/settings.php:34
msgid "Start loading the ads %s pixels before they are visible on the screen."
msgstr "Začni nalagati oglase %s pik, preden so vidni na ekranu."

#: modules/grids/admin.class.php:61
msgid "Set to 0 to disable this feature."
msgstr "Nastavi na 0, da onemogočiš to možnost."

#: modules/grids/admin.class.php:60
msgid "On browsers smaller than this, the columns show in full width below each other."
msgstr "Na brskalnikih, manjših od tega, se stolpci pokažejo v polni širini, eden pod drugim."

#: modules/grids/admin.class.php:58
msgid "Full width"
msgstr "Polna širina"

#. translators: "profile fields" relates to BuddyPress profile fields
#: modules/buddypress/views/xprofile-condition.php:58
msgid "No profile fields found"
msgstr "Ne najdem polj profila"

#: modules/buddypress/class-buddypress.php:118
msgid "Display ads based on BuddyPress profile fields."
msgstr "Prikaži oglase glede na polja profila BuddyPress."

#: modules/buddypress/class-buddypress.php:117
msgid "BuddyPress profile field"
msgstr "BuddyPress polja profila"

#: modules/buddypress/views/position-option.php:44
#: views/setting_repeat.php:4
msgid "repeat the position"
msgstr "ponovi položaj"

#: views/setting_custom_code.php:34
msgid "Displayed after the ad content"
msgstr "Prikazano po vsebini oglasa"

#: views/setting_custom_code.php:30
msgid "place your own code below the ad"
msgstr "vstavi lastno kodo pod oglas"

#: views/setting_custom_code.php:27
msgid "custom code"
msgstr "koda po meri"

#. Author URI of the plugin
#: advanced-ads-pro.php
msgid "https://wpadvancedads.com"
msgstr "https://wpadvancedads.com"

#. Plugin URI of the plugin
#: advanced-ads-pro.php
msgid "https://wpadvancedads.com/add-ons/advanced-ads-pro/"
msgstr "https://wpadvancedads.com/add-ons/advanced-ads-pro/"

#. Description of the plugin
#: advanced-ads-pro.php
msgid "Advanced features to boost your ad revenue."
msgstr "Napredne možnosti za večanje prihodkov oglasov."

#. Plugin Name of the plugin
#: advanced-ads-pro.php
msgid "Advanced Ads Pro"
msgstr "Advanced Ads Pro"

#: modules/weekdays/views/ad-submitbox-meta.php:19
msgid "Set specific days"
msgstr "Nastavi točno določene datume"

#. translators: 1: month, 2: day, 3: year, 4: hour, 5: minute
#: modules/placement-tests/views/settings_test_expiry_date.php:31
msgctxt "order of expiry date fields 1: month, 2: day, 3: year, 4: hour, 5: minute"
msgid "%1$s %2$s, %3$s @ %4$s %5$s"
msgstr "%2$s. %1$s. %3$s @ %4$s. %5$s"

#: modules/placement-tests/views/settings_test_expiry_date.php:25
msgid "Minute"
msgstr "Minuta"

#: modules/placement-tests/views/settings_test_expiry_date.php:24
msgid "Hour"
msgstr "Ura"

#: modules/placement-tests/views/settings_test_expiry_date.php:23
msgid "Year"
msgstr "Leto"

#: modules/placement-tests/views/settings_test_expiry_date.php:22
msgid "Day"
msgstr "Dan"

#. translators: 1: month number (01, 02, etc.), 2: month abbreviation
#: modules/placement-tests/views/settings_test_expiry_date.php:17
msgctxt "1: month number (01, 02, etc.), 2: month abbreviation"
msgid "%1$s-%2$s"
msgstr "%1$s-%2$s"

#: modules/placement-tests/views/settings_test_expiry_date.php:12
msgid "Month"
msgstr "Mesec"

#: modules/placement-tests/views/settings_test_expiry_date.php:5
msgctxt "placement tests"
msgid "Send email after"
msgstr "Pošlji e-pošto po"

#: modules/placement-tests/placement-tests.class.php:237
#: modules/placement-tests/views/setting_placement_test_weight.php:17
msgid "Test weight"
msgstr "Testna utež"

#: modules/placement-tests/views/setting_placement_test_weight.php:15
msgctxt "placement tests"
msgid "Testing"
msgstr "Testiram"

#: modules/placement-tests/views/placement-tests.php:66
msgid "Save Tests"
msgstr "Shrani teste"

#: modules/placement-tests/views/placement-tests.php:59
msgctxt "checkbox to remove placement test"
msgid "delete"
msgstr "izbriši"

#: modules/placement-tests/views/placement-tests.php:53
msgctxt "placement tests"
msgid "empty"
msgstr "prazen"

#: modules/placement-tests/views/placement-tests.php:18
msgid "Placements"
msgstr "Postavitve"

#: modules/placement-tests/views/placement-tests.php:17
msgid "Expiry date"
msgstr "Datum poteka"

#: modules/placement-tests/views/placement-tests.php:16
msgid "Author"
msgstr "Avtor"

#: modules/placement-tests/views/placement-tests.php:11
msgid "Placement tests"
msgstr "Testi postavitev"

#: modules/lazy-load/views/setting_lazy_load.php:6
#: modules/lazy-load/views/setting_lazy_load.php:8
msgid "disabled"
msgstr "onemogočeno"

#: modules/lazy-load/views/setting_lazy_load.php:2
#: modules/lazy-load/views/setting_lazy_load.php:4
msgid "enabled"
msgstr "omogočeno"

#: modules/group-refresh/views/settings_group_refresh.php:27
msgid "Please use a placement to deliver this group using cache-busting."
msgstr "Prosim izberi postavitev, da dostaviš to skupino oglasov z cache-busting."

#: modules/group-refresh/views/settings_group_refresh.php:19
msgid "Refresh ads on the same spot. Works when cache-busting is used."
msgstr "Osveži oglase na istem mestu. Deluje le, ko je uporabljen cache-busting."

#: modules/group-refresh/views/settings_group_refresh.php:16
msgid "milliseconds"
msgstr "milisekund"

#: modules/group-refresh/views/settings_group_refresh.php:14
#: views/admin/placements/bulk-edit.php:30
#: views/admin/placements/bulk-edit.php:40
#: views/admin/tables/ads/column-displayonce.php:10
msgid "Enabled"
msgstr "Omogočeno"

#: modules/grids/views/grid-option-size.php:20
msgid "rows"
msgstr "vrstic"

#: modules/grids/views/grid-option-size.php:16
msgid "columns"
msgstr "stolpcev"

#: modules/click-fraud-protection/views/settings.php:23
msgid "Period for which to hide the ad ( in days )"
msgstr "Obdobje, za katerega naj skrijem oglas (v dneh)"

#: modules/click-fraud-protection/views/settings.php:20
msgid "Period in which the click limit should be reached ( in hours )"
msgstr "Obdobje, v katerem naj se doseže limit klikov (v urah)"

#: modules/click-fraud-protection/views/settings.php:17
msgid "Allowed clicks on a single ad before it is removed"
msgstr "Dovoljeni kliki na enem oglasu, preden je odstranjen"

#. translators: Link to parallax manual
#: modules/ad-server/views/module-settings.php:15
#: modules/ads-for-adblockers/views/settings.php:20
#: modules/advanced-display-conditions/main.class.php:508
#: modules/advanced-display-conditions/views/metabox-string.php:12
#: modules/advanced-visitor-conditions/main.class.php:309
#: modules/advanced-visitor-conditions/main.class.php:408
#: modules/advanced-visitor-conditions/main.class.php:465
#: modules/advanced-visitor-conditions/main.class.php:503
#: modules/buddypress/class-buddypress.php:378
#: modules/buddypress/views/xprofile-condition.php:74
#: modules/cache-busting/cache-busting.class.php:263
#: modules/cache-busting/views/settings.php:14
#: modules/click-fraud-protection/click-fraud-protection.class.php:150
#: modules/click-fraud-protection/click-fraud-protection.class.php:158
#: modules/click-fraud-protection/views/settings.php:12
#: modules/gamipress/views/visitor-condition-achievement.php:34
#: modules/gamipress/views/visitor-condition-points.php:41
#: modules/gamipress/views/visitor-condition-rank.php:41
#: modules/geo/admin/views/setting-maxmind-license-key.php:22
#: modules/geo/classes/admin.php:400
#: modules/group-refresh/views/settings_group_refresh.php:23
#: modules/lazy-load/views/settings.php:10
#: modules/paid-memberships-pro/main.class.php:140
#: modules/paid-memberships-pro/main.class.php:259
#: modules/parallax-ads/views/placement-options-after.php:24
#: modules/responsive-ads/views/setting_fallback_width.php:16
#: modules/rest-api/views/module-enable.php:15
msgid "Manual"
msgstr "Priročnik"

#. translators: %s link to manual
#: modules/cache-busting/views/settings_check_ad.php:16
msgid "The code of this ad might not work properly with activated cache-busting. <a href=\"%s\" target=\"_blank\">Manual</a>"
msgstr "Koda tega oglasa morda ne bo delovala pravilno z aktiviranim cache-busting.<a href=\"%s\" target=\"_blank\">Priročnik</a>"

#: modules/cache-busting/views/settings.php:44
msgid "Enable passive cache busting for all ads and groups which are not delivered through a placement, if possible."
msgstr "Omogoči pasivni cache-busting za vse oglase in skupine, ki niso dostavljene skozi postavitev, če je to možno."

#: modules/cache-busting/views/settings.php:62
msgid "Choose the fallback if “passive“ cache busting is not possible."
msgstr "Izberi povratno možnost, če pasivni cache-busting ni možen."

#: modules/cache-busting/admin-ui.class.php:87
#: modules/cache-busting/views/settings.php:31
#: modules/cache-busting/views/settings.php:68
#: views/admin/placements/bulk-edit.php:20
msgid "AJAX"
msgstr "AJAX"

#: modules/advanced-display-conditions/views/referrer-url-metabox.php:15
#: modules/advanced-visitor-conditions/views/referrer-url-metabox.php:15
msgid "url of the referrer"
msgstr "url pošiljatelja (referrer)"

#: modules/advanced-display-conditions/views/referrer-url-metabox.php:5
#: modules/advanced-visitor-conditions/views/referrer-url-metabox.php:5
msgid "URL"
msgstr "URL"

#: modules/advanced-display-conditions/views/referrer-url-metabox.php:3
#: modules/advanced-visitor-conditions/views/referrer-url-metabox.php:3
msgid "Display ad depending on the external url the user comes from."
msgstr "Prikaži oglas glede na zunanji url, iz katerega pride uporabnik."

#: modules/advanced-display-conditions/views/referrer-url-metabox.php:1
#: modules/advanced-visitor-conditions/views/referrer-url-metabox.php:1
msgid "Display by referrer url"
msgstr "Prikaži po url pošiljatelja"

#: modules/ads-for-adblockers/views/placement-item.php:46
#: modules/lazy-load/views/setting_lazy_load.php:11
msgid "Works only with cache-busting enabled"
msgstr "Deluje le z omogočenim cache-busting"

#: modules/weekdays/class-advanced-ads-pro-weekdays.php:171
msgid "Never shows up"
msgstr "Nikoli se ne prikaže"

#: modules/weekdays/class-advanced-ads-pro-weekdays.php:90
#: modules/weekdays/class-advanced-ads-pro-weekdays.php:107
msgid "specific days"
msgstr "določeni dnevi"

#. translators: %s: placement test name
#: modules/placement-tests/placement-tests.class.php:702
msgid "Placement test <em>%s</em> created"
msgstr "Test postavitve <em>%s</em> je bil ustvarjen"

#: modules/placement-tests/placement-tests.class.php:564
msgctxt "placement tests"
msgid "Placement page"
msgstr "Stran postavitve"

#: modules/placement-tests/placement-tests.class.php:558
#: modules/placement-tests/placement-tests.class.php:700
#: modules/placement-tests/views/placement-tests.php:50
msgctxt "placement tests"
msgid " vs "
msgstr "proti"

#: modules/placement-tests/placement-tests.class.php:530
msgctxt "placement tests"
msgid "Expired placement tests"
msgstr "Potekli testi postavitev"

#: modules/placement-tests/views/setting_placement_test_weight.php:28
msgid "Save new test"
msgstr "Shrani novi test"

#: modules/paid-memberships-pro/main.class.php:157
msgid "Display ads only to users with a specific membership level set with PaidMembershipsPro."
msgstr "Prikaži oglase le uporabnikom z določenim nivojem članstva, nastavljenim v PaidMembershipsPro."

#: modules/paid-memberships-pro/main.class.php:156
msgid "PMP user level"
msgstr "PMP nivo uporabnika"

#: modules/paid-memberships-pro/main.class.php:133
#: modules/paid-memberships-pro/main.class.php:252
msgid "No membership levels set up yet."
msgstr "Ni še nastavljenih nivojev članstev."

#: modules/paid-memberships-pro/main.class.php:39
msgid "Display ads only on pages that require a specific membership level set of PaidMembershipsPro."
msgstr "Prikaži oglase samo na straneh, ki zahtevajo določen nivo članstva, nastavljen v PaidMembershipsPro."

#: modules/paid-memberships-pro/main.class.php:38
msgid "PMP page level"
msgstr "PMP nivo strani"

#: includes/placements/types/class-archive-pages.php:56
msgid "Display the ad between posts on post lists, e.g. home, archives, search etc."
msgstr "Prikaži oglase med objavami na seznamu prispevkov, npr. domov, arhivi, rezultati iskanj itd."

#: includes/placements/types/class-archive-pages.php:47
msgid "Post Lists"
msgstr "Seznam prispevkov"

#: includes/placements/types/class-custom-position.php:57
msgid "Attach the ad to any element in the frontend."
msgstr "Pripni oglas h kateremukoli elementu v ospredju."

#: includes/placements/types/class-custom-position.php:48
msgid "Custom Position"
msgstr "Položaj po meri"

#: includes/placements/types/class-content-middle.php:56
msgid "In the middle of the main content based on the number of paragraphs."
msgstr "V sredini glavne vsebine, glede na število odstavkov."

#: includes/placements/types/class-content-middle.php:47
msgid "Content Middle"
msgstr "Sredi vsebine"

#: includes/placements/types/class-above-headline.php:57
msgid "Above the main headline on the page (&lt;h1&gt;)."
msgstr "Nad glavnim naslovom strani (&lt;h1&gt;)."

#: includes/placements/types/class-above-headline.php:48
msgid "Above Headline"
msgstr "Nad naslovom"

#: includes/placements/types/class-content-random.php:56
msgid "After a random paragraph in the main content."
msgstr "Po naključnem odstavku v glavni vsebini."

#: includes/placements/types/class-content-random.php:47
msgid "Random Paragraph"
msgstr "Naključni odstavek"

#: modules/inject-content/admin.class.php:155
msgid "secondary loops"
msgstr "sekundarne poizvedbe"

#: modules/inject-content/admin.class.php:151
msgid "Only enable this option if you are sure what you are doing!"
msgstr "To možnost omogoči, le če veš, kaj počneš!"

#: modules/inject-content/admin.class.php:150
msgid "Allow injection into any custom and secondary queries."
msgstr "Dovoli vstavitev v katerekoli sekundarne poizvedbe in poizvedbe po meri."

#: modules/inject-content/admin.class.php:128
msgid "minimum content length"
msgstr "minimalna dolžina vsebine"

#: modules/inject-content/admin.class.php:93
msgid "Before which post to inject the ad on post lists."
msgstr "Pred katerim prispevkom naj vstavim oglas na seznamu prispevkov."

#. translators: %s: index of the post
#: modules/inject-content/admin.class.php:91
msgid "Inject before %s. post"
msgstr "Vstavi pred %s. prispevkom"

#: modules/inject-content/admin.class.php:69
msgid "Place the following element where the ad should be displayed."
msgstr "Vstavi naslednji element, kjer naj se prikaže oglas."

#: modules/inject-content/admin.class.php:68
msgid "by new element"
msgstr "glede na nov element"

#: modules/bbpress/class-admin.php:64
#: modules/bbpress/class-admin.php:87
#: modules/bbpress/class-admin.php:130
#: modules/inject-content/admin.class.php:58
msgid "Position"
msgstr "Položaj"

#: modules/inject-content/admin.class.php:57
msgid "Uses <a href=\"https://api.jquery.com/category/selectors/\" target=\"_blank\">jQuery selectors</a>, e.g. #container_id, .container_class"
msgstr "Uporalbja <a href=\"https://api.jquery.com/category/selectors/\" target=\"_blank\">jQuery selektorje</a>, npr. #container_id, .container_class"

#: modules/inject-content/admin.class.php:54
msgid "or enter manually"
msgstr "ali vstavi ročno"

#: modules/inject-content/admin.class.php:56
msgid "select position"
msgstr "izberi položaj"

#: modules/inject-content/admin.class.php:55
msgctxt "frontend picker"
msgid "stop selection"
msgstr "ustavi izbor"

#: modules/inject-content/admin.class.php:51
msgid "Place ads in relation to an existing element in the frontend."
msgstr "Vstavi oglase glede na obstoječi element v ospredju."

#: modules/inject-content/admin.class.php:49
msgid "by existing element"
msgstr "glede na obstoječi element"

#: modules/inject-content/admin.class.php:40
msgid "below"
msgstr "pod"

#: modules/inject-content/admin.class.php:39
msgid "inside, after other content"
msgstr "znotraj, po drugi vsebini"

#: modules/inject-content/admin.class.php:38
msgid "inside, before other content"
msgstr "znotraj, pred drugimi vsebinami"

#: modules/inject-content/admin.class.php:37
msgid "above"
msgstr "nad"

#: modules/group-refresh/admin.class.php:60
msgid "Refresh interval"
msgstr "Interval osvežitve"

#: modules/grids/admin.class.php:73
msgid "Random order"
msgstr "Naključni vrstni red"

#: modules/grids/admin.class.php:53
msgid "Minimum width of a column in the grid."
msgstr "Minimalna širina stolpca v mreži."

#: modules/grids/admin.class.php:51
msgid "Min. width"
msgstr "Min. širina"

#: modules/grids/admin.class.php:45
msgid "Inner margin"
msgstr "Notranji odmik"

#: modules/grids/admin.class.php:36
msgid "Size"
msgstr "Velikost"

#: modules/click-fraud-protection/click-fraud-protection.class.php:320
msgid "Click Fraud Protection enabled"
msgstr "Zaščita pred goljufijami klikov je omogočena"

#: modules/click-fraud-protection/click-fraud-protection.class.php:145
msgid "hours"
msgstr "ur"

#: modules/click-fraud-protection/click-fraud-protection.class.php:143
msgid "within"
msgstr "znotraj"

#: modules/click-fraud-protection/click-fraud-protection.class.php:111
msgid "Display ad only if a click limit has not been reached."
msgstr "Prikaži oglas le, če še ni bil dosežen limit klikov."

#: modules/click-fraud-protection/click-fraud-protection.class.php:110
msgid "max. ad clicks"
msgstr "max. klikov oglasa"

#: modules/click-fraud-protection/admin.class.php:29
msgid "Click Fraud Protection"
msgstr "Zaščita pred goljufijami klikov"

#: modules/cache-busting/cache-busting.class.php:1913
msgctxt "placement admin label"
msgid "Cache-busting:"
msgstr "Cache-busting:"

#: modules/cache-busting/cache-busting.class.php:1907
msgid "The ad is not displayed on the page"
msgstr "Oglas ni prikazan na strani"

#: modules/cache-busting/cache-busting.class.php:1906
msgid "The ad is displayed on the page"
msgstr "Oglas je prikazan na strani"

#: modules/cache-busting/cache-busting.class.php:1903
#: modules/cache-busting/views/settings.php:24
msgid "passive"
msgstr "pasivno"

#: modules/cache-busting/cache-busting.class.php:1897
msgid "The ad can work with passive cache-busting"
msgstr "Oglas lahko deluje s pasivnim cache-busting"

#: modules/cache-busting/cache-busting.class.php:1895
msgid "The ad can not work with passive cache-busting"
msgstr "Oglas ne more delovati s pasivnim cache-busting"

#: modules/ads-for-adblockers/views/settings.php:27
#: modules/cache-busting/admin.class.php:29
#: views/admin/placements/bulk-edit.php:16
msgid "Cache Busting"
msgstr "Cache Busting"

#: modules/cache-busting/admin-ui.class.php:150
msgid "The <em>Ad Group</em> ad type can only use AJAX or no cache-busting, but not passive cache-busting."
msgstr "Oglasna vrsta <em>skupina oglasov</em> lahko uporabi le AJAX ali brez cache-busting, ne pa tudi pasivni cache-busting."

#: modules/cache-busting/admin-ui.class.php:107
msgctxt "placement admin label"
msgid "Cache-busting"
msgstr "Cache-busting"

#: modules/cache-busting/cache-busting.class.php:1910
msgctxt "setting label"
msgid "off"
msgstr "izklopljeno"

#: modules/cache-busting/cache-busting.class.php:1901
msgctxt "setting label"
msgid "ajax"
msgstr "ajax"

#: modules/buddypress/class-admin.php:82
msgid "Member List"
msgstr "Seznam članov"

#: modules/buddypress/class-admin.php:79
msgid "Group List"
msgstr "Seznam skupin"

#: modules/buddypress/class-admin.php:63
#: modules/buddypress/class-admin.php:71
msgid "Activity Entry"
msgstr "Aktivnost vnosa"

#. translators: %s is an HTML input element.
#: modules/buddypress/views/position-option.php:37
msgid "Inject after %s. entry"
msgstr "Vstavi po %s. vnosu"

#: includes/placements/types/class-buddypress.php:63
msgid "Display ads on BuddyPress related pages."
msgstr "Prikaži oglase na straneh, povezanih z BuddyPress. "

#: includes/placements/types/class-buddypress.php:51
msgid "BuddyPress Content"
msgstr "BuddyPress vsebine"

#: modules/bbpress/class-admin.php:150
msgid "forums page"
msgstr "stran forumov"

#: modules/bbpress/class-admin.php:146
msgid "single forum page"
msgstr "enojna stran foruma"

#: modules/bbpress/class-admin.php:142
#: modules/bbpress/class-admin.php:164
msgid "forum topic page"
msgstr "stran teme foruma"

#: modules/buddypress/views/position-option.php:20
#: modules/inject-content/admin.class.php:80
#: modules/inject-content/admin.class.php:96
msgid "position"
msgstr "položaj"

#: includes/placements/types/class-bbpress-comment.php:56
msgid "Display ads in bbPress replies."
msgstr "Prikaži oglase v odgovorih bbPress."

#: includes/placements/types/class-bbpress-comment.php:47
msgid "bbPress Reply Content"
msgstr "bbPress vsebina odgovora"

#: includes/placements/types/class-bbpress-static.php:56
msgid "Display ads on bbPress related pages."
msgstr "Prikaži oglase na straneh, povezanih z bbPress."

#: includes/placements/types/class-bbpress-static.php:47
msgid "bbPress Static Content"
msgstr "bbPress statična vsebina"

#: modules/background-ads/admin.class.php:33
msgid "background"
msgstr "ozadje"

#: modules/background-ads/admin.class.php:30
msgid "Select a background color in case the background image is not high enough to cover the whole screen."
msgstr "Izberi barvo ozadja za primer, če slika ozadja ne zapolni celoten ekran."

#: includes/placements/types/class-background-ad.php:56
msgid "Background of the website behind the main wrapper."
msgstr "Ozadje spletne strani za glavnim okvirjem."

#: includes/placements/types/class-background-ad.php:47
msgid "Background Ad"
msgstr "Oglas ozadje"

#. translators: %s is a placeholder for the impressions field
#: modules/advanced-visitor-conditions/main.class.php:499
msgid "within %s seconds"
msgstr "v %s sekundah"

#: modules/advanced-visitor-conditions/main.class.php:449
msgid "Cookie Value"
msgstr "Vrednost piškotka"

#: modules/advanced-visitor-conditions/main.class.php:448
msgid "Cookie Name"
msgstr "Ime piškotka"

#: modules/advanced-visitor-conditions/main.class.php:301
#: modules/advanced-visitor-conditions/main.class.php:349
#: modules/advanced-visitor-conditions/main.class.php:398
msgid "-- choose one --"
msgstr "-- izberi eno --"

#: modules/advanced-visitor-conditions/main.class.php:298
msgid "can not"
msgstr "ne more"

#: modules/advanced-visitor-conditions/main.class.php:297
msgid "can"
msgstr "lahko"

#: modules/advanced-visitor-conditions/main.class.php:202
msgid "new visitor"
msgstr "nov obiskovalec"

#: modules/advanced-visitor-conditions/main.class.php:196
msgid "Display the ad only for a few impressions in a given period per user."
msgstr "Prikaži oglas le za par prikazov v določenem obdobju na uporabnika."

#: modules/advanced-visitor-conditions/main.class.php:195
msgid "max. ad impressions"
msgstr "max. prikazov oglasa"

#: modules/advanced-visitor-conditions/main.class.php:188
msgid "Display ads based on the number of page impressions the user already made before the current one."
msgstr "Prikaži oglase glede na število ogledov strani, ki jih je uporabnik opravil pred trenutnim."

#: modules/advanced-visitor-conditions/main.class.php:187
msgid "page impressions"
msgstr "prikazi strani"

#: modules/advanced-visitor-conditions/main.class.php:180
msgid "cookie"
msgstr "piškotek"

#: modules/advanced-visitor-conditions/main.class.php:174
msgid "Display ads based on the visitor's browser language."
msgstr "Prikaži oglase glede na obiskovalčev jezik brskalnika."

#: modules/advanced-visitor-conditions/main.class.php:173
msgid "browser language"
msgstr "jezik brskalnika"

#: modules/advanced-visitor-conditions/main.class.php:148
msgid "user can (capabilities)"
msgstr "uporabnik lahko (zmogljivosti)"

#: modules/advanced-visitor-conditions/main.class.php:140
msgid "user agent"
msgstr "uporabniški agent"

#: modules/advanced-visitor-conditions/main.class.php:133
msgid "Display ads based on the referrer URL."
msgstr "Prikaži oglase glede na URL napotitelja."

#: modules/advanced-visitor-conditions/main.class.php:132
msgid "referrer url"
msgstr "napotitveni URL"

#: modules/advanced-visitor-conditions/admin.class.php:29
msgid "Advanced visitor conditions"
msgstr "Napredni pogoji obiskovalcev"

#: modules/advanced-display-conditions/main.class.php:574
msgid "count from end"
msgstr "štej od konca"

#: modules/advanced-display-conditions/main.class.php:540
msgid "meta key"
msgstr "meta ključ"

#: modules/advanced-display-conditions/main.class.php:539
msgid "all of"
msgstr "vse izmed"

#: modules/advanced-display-conditions/main.class.php:538
msgid "any of"
msgstr "katerikoli izmed"

#: modules/advanced-display-conditions/main.class.php:502
msgid "no languages set up in WPML"
msgstr "v WPML ni nič nastavljenih jezikov"

#: modules/advanced-display-conditions/main.class.php:431
#: modules/advanced-display-conditions/main.class.php:486
#: modules/advanced-visitor-conditions/main.class.php:346
#: modules/advanced-visitor-conditions/main.class.php:395
#: modules/advanced-visitor-conditions/main.class.php:538
#: modules/gamipress/views/visitor-condition-achievement.php:17
#: modules/geo/classes/admin.php:308
#: modules/paid-memberships-pro/main.class.php:118
#: modules/paid-memberships-pro/main.class.php:237
msgid "is not"
msgstr "ni"

#: modules/advanced-display-conditions/main.class.php:430
#: modules/advanced-display-conditions/main.class.php:485
#: modules/advanced-visitor-conditions/main.class.php:345
#: modules/advanced-visitor-conditions/main.class.php:394
#: modules/advanced-visitor-conditions/main.class.php:536
#: modules/gamipress/views/visitor-condition-achievement.php:16
#: modules/geo/classes/admin.php:307
#: modules/paid-memberships-pro/main.class.php:117
#: modules/paid-memberships-pro/main.class.php:236
msgid "is"
msgstr "je"

#: modules/advanced-display-conditions/main.class.php:110
msgid "Display ads based on the index of a split page"
msgstr "Prikaži oglase glede na indeks razpolovljene strani"

#: modules/advanced-display-conditions/main.class.php:109
msgid "pagination"
msgstr "paginacija"

#: modules/advanced-display-conditions/main.class.php:103
msgid "Display ads based on post meta."
msgstr "Prikaži oglase glede na metapodatke objav."

#: modules/advanced-display-conditions/main.class.php:102
msgid "post meta"
msgstr "meta objave (post meta)"

#: modules/advanced-display-conditions/main.class.php:96
msgid "Display ads based on parent page."
msgstr "Prikaži oglase glede na nadrejeno stran."

#: modules/advanced-display-conditions/main.class.php:95
msgid "parent page"
msgstr "nadrejena stran"

#: modules/advanced-display-conditions/main.class.php:87
msgid "WPML language"
msgstr "jezik WPML"

#. translators: %s: post type
#: modules/advanced-display-conditions/main.class.php:57
#: modules/advanced-display-conditions/main.class.php:76
msgid "Display ads based on the template of the %s post type."
msgstr "Prikaži oglase glede na predlogo vrste objav %s."

#. translators: %s: page
#: modules/advanced-display-conditions/main.class.php:55
#: modules/advanced-display-conditions/main.class.php:75
msgid "%s template"
msgstr "predloga %s"

#: modules/advanced-display-conditions/main.class.php:39
msgid "url parameters"
msgstr "parametri url"

#: modules/ads-for-adblockers/admin.class.php:148
msgid "The chosen ad contains a reference to an external .js file"
msgstr "Izbran oglas vsebuje sklicevanje na zunanjo datoteko .js"

#: modules/ads-for-adblockers/admin.class.php:86
msgid "Displayed to visitors with an ad blocker"
msgstr "Prikazano obiskovalcem z ad blockerjem"

#: modules/ads-for-adblockers/admin.class.php:94
msgid "Ad blocker item"
msgstr "Predmet ad blocker"

#: modules/ads-for-adblockers/admin.class.php:42
msgid "Ads for ad blockers"
msgstr "Oglasi za ad blockerje"

#: modules/admin-bar/admin-bar.class.php:104
msgid "placement"
msgstr "postavitev"

#: modules/admin-bar/admin-bar.class.php:101
msgid "group"
msgstr "skupina"

#: modules/admin-bar/admin-bar.class.php:98
msgid "ad"
msgstr "oglas"

#: modules/admin-bar/admin-bar.class.php:56
msgid "No Ads found"
msgstr "Ne najdem oglasov"

#: modules/admin-bar/admin-bar.class.php:48
#: modules/ads-for-adblockers/views/placement-item.php:27
#: views/admin/tables/ads/adsense-fallback.php:37
msgid "Ads"
msgstr "Oglasi"

#: views/setting_output_once.php:6
msgid "Display the ad only once per page"
msgstr "Prikaži oglas le enkrat na stran"

#: views/setting_output_once.php:2
msgid "Display only once"
msgstr "Prikaži samo enkrat"

#: classes/advanced-ads-pro.php:332
msgid "Please note, the “Ad Admin“ and the “Ad Manager“ roles have the “upload_files“ and the “unfiltered_html“ capabilities"
msgstr "Prosim pomni, da imata vlogi “Ad Admin“ in “Ad Manager dovoljenja “upload_files“ in “unfiltered_html“"

#: includes/class-bootstrap.php:225
msgid "Install Now"
msgstr "Namesti zdaj"

#: includes/class-bootstrap.php:233
msgid "Activate Now"
msgstr "Aktiviraj zdaj"

#. translators: %s: URL to the settings page
#: classes/advanced-ads-pro-admin.php:390
msgid "Enable the Advanced Visitor Conditions <a href=\"%s\" target=\"_blank\">in the settings</a>."
msgstr "Omogoči Napredne pogoje obiskovalcev <a href=\"%s\" target=\"_blank\">v nastavitvah</a>."

#: classes/advanced-ads-pro-admin.php:336
msgid "Please note, with the last update, the “Ad Admin“ and “Ad Manager“ roles have the “upload_files“ and the “unfiltered_html“ capabilities."
msgstr "Pozor, z zadnjo posodobitvijo imata vlogi “Ad Admin“ in “Ad Manager“ pravice “upload_files“ in “unfiltered_html“."

#: classes/advanced-ads-pro-admin.php:326
msgid "Ad User Role"
msgstr "Dodaj uporabniško vlogo"

#: classes/advanced-ads-pro-admin.php:323
msgid "Advanced Ads User Role"
msgstr "Uporabniška vloga Advanced Ads"

#: classes/advanced-ads-pro-admin.php:46
msgid "--no role--"
msgstr "--ni vloge--"

#: classes/advanced-ads-pro-admin.php:45
#: includes/installation/class-install.php:159
msgid "Ad User"
msgstr "Dodaj uporabnika"

#: classes/advanced-ads-pro-admin.php:44
#: includes/installation/class-install.php:146
msgid "Ad Manager"
msgstr "Dodaj skrbnika"

#: classes/advanced-ads-pro-admin.php:43
#: includes/installation/class-install.php:132
msgid "Ad Admin"
msgstr "Dodaj administratorja"
