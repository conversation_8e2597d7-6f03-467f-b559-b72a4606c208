msgid ""
msgstr ""
"Project-Id-Version: Advanced Ads Pro\n"
"Report-Msgid-Bugs-To: \n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2025-04-09T03:52:05+00:00\n"
"PO-Revision-Date: 2025-03-08 10:52:29+0000\n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=(n == 0) ? 0 : ((n == 1) ? 1 : ((n == 2) ? 2 : ((n % 100 >= 3 && n % 100 <= 10) ? 3 : ((n % 100 >= 11 && n % 100 <= 99) ? 4 : 5))));\n"
"X-Generator: GlotPress/4.0.1\n"

#: views/admin/placements/bulk-edit.php:21
msgid "Off"
msgstr ""

#: views/admin/placements/bulk-edit.php:18
#: views/admin/placements/bulk-edit.php:29
#: views/admin/placements/bulk-edit.php:39
msgid "No Change"
msgstr ""

#: modules/cache-busting/cache-busting.class.php:253
#: views/admin/placements/bulk-edit.php:37
msgid "Hide when empty"
msgstr ""

#: views/admin/placements/bulk-edit.php:31
#: views/admin/placements/bulk-edit.php:41
msgid "Disabled"
msgstr ""

#: views/admin/placements/bulk-edit.php:19
msgid "Auto"
msgstr ""

#. Author of the plugin
#: advanced-ads-pro.php
msgid "Advanced Ads"
msgstr ""

#. translators: %s: Plugin name
#: includes/class-bootstrap.php:155
msgid "Your versions of the Advanced Ads addons listed below are incompatible with <strong>Advanced Ads %s</strong> and have been deactivated. Please update the plugin to the latest version."
msgstr "إصداراتك من إضافات Advanced Ads المدرجة أدناه غير متوافقة مع <strong>Advanced Ads %s</strong> وقد تم تعطيلها. يُرجى تحديث الإضافة إلى أحدث إصدار."

#. translators: %s: Plugin name
#: includes/class-bootstrap.php:109
msgid "Your version of <strong>Advanced Ads - Pro</strong> is incompatible with <strong>Advanced Ads %s</strong> and has been deactivated. Please update the plugin to the latest version."
msgstr "إصدارك من <strong>Advanced Ads - Pro</strong> غير متوافق مع <strong>Advanced Ads %s</strong> وقد تم تعطيله. يُرجى تحديث الإضافة إلى أحدث إصدار."

#. translators: 1: is a link to a support document. 2: closing link
#: includes/class-autoloader.php:113
msgid "Your installation of Advanced Ads is incomplete. If you installed Advanced Ads from GitHub, %1$s please refer to this document%2$s to set up your development environment."
msgstr "تثبيت Advanced Ads لديك غير مكتمل. إذا قمت بتثبيت Advanced Ads من GitHub، %1$s يُرجى الرجوع إلى هذا المستند%2$s لإعداد بيئة التطوير الخاصة بك."

#: modules/geo/admin/views/setting-download.php:22
msgid "You are currently using filter hooks to load custom database files."
msgstr "أنت تستخدم حاليًا خطافات التصفية لتحميل ملفات قاعدة بيانات مخصصة."

#: modules/extended-adblocker/views/setting-dismissible.php:17
msgid "When to show overlay again?"
msgstr "متى يتم عرض التراكب (أوفرلاي) مرة أخرى؟"

#: modules/placement_conditions/admin.class.php:70
msgid "Visitor Conditions"
msgstr "شروط الزائر"

#: modules/ads-by-hours/src/class-admin.php:135
msgid "viewer time zone"
msgstr "المنطقة الزمنية للمشاهد"

#: modules/advanced-visitor-conditions/main.class.php:218
msgid "User IP Address"
msgstr "عنوان بروتوكول الإنترنت IP للمستخدم"

#: modules/ads-by-hours/views/publish-metabox-inputs.php:54
msgid "To"
msgstr "إلى"

#. translators: 1 opening link tag. 2 closing tag
#: modules/cache-busting/views/settings.php:53
msgid "This option does not work with the current active theme and %1$sblock themes%2$s in general."
msgstr "هذا الخيار لا يعمل مع القالب النشط حاليًا و%1$sقوالب الكتل%2$s بشكل عام."

#. translators: %s: dashicon class
#: modules/lazy-load/views/settings.php:22
msgid "This module requires: <br> <span class=\"dashicons %s\"></span> Cache Busting"
msgstr "هذه الوحدة تتطلب: <br> <span class=\"dashicons %s\"></span> إلغاء تخزين مؤقت (Cache Busting)"

#: modules/ads-for-adblockers/views/settings.php:25
msgid "This module requires:"
msgstr "هذه الوحدة تتطلب:"

#: views/admin/tables/ads/adsense-fallback.php:47
#: views/admin/tables/ads/adsense-fallback.php:66
msgid "The selected item will be displayed when an AdSense ad is unavailable, ensuring your ad space remains filled."
msgstr "سيتم عرض العنصر المحدد عندما يكون إعلان AdSense غير متاح، مما يضمن بقاء مساحة إعلانك مشغولة."

#: includes/class-bootstrap.php:180
msgid "The following addons are affected:"
msgstr "الإضافات التالية متأثرة:"

#. translators: group or ad title.
#: views/admin/tables/ads/adsense-fallback.php:58
msgid "The default fallback is \"%s\". You can change this in the AdSense settings."
msgstr "الإعداد الافتراضي للطريقة الاحتياطية هو \"%s\". يمكنك تغييره في إعدادات AdSense."

#: views/admin/tables/ads/adsense-fallback.php:75
msgid "The AdSense fallback feature requires the ad to be assigned to a placement with enabled Cache Busting."
msgstr "تتطلب ميزة الإعلان الاحتياطي في AdSense تعيين الإعلان إلى موضع تم تفعيل إلغاء التخزين المؤقت (Cache Busting) فيه."

#. translators: %s: WP_ENVIRONMENT_TYPES
#: includes/class-autoloader.php:157
msgid "The %s constant is no longer supported."
msgstr "الثابت %s لم يعد مدعومًا."

#: modules/weekdays/class-advanced-ads-pro-weekdays.php:177
msgid "Shows up"
msgstr "يظهر"

#: modules/ads-by-hours/src/class-admin.php:79
msgid "Showing ads depending on the visitor's time requires enabling Cache Busting."
msgstr "يتطلب عرض الإعلانات وفقًا لوقت الزائر تفعيل إلغاء التخزين المؤقت (Cache Busting)."

#: modules/extended-adblocker/views/setting-activate-extended.php:26
msgid "Show a custom overlay to users with an ad blocker enabled, prompting them to turn it off on your website."
msgstr "عرض تراكب مخصص للمستخدمين الذين لديهم مانع إعلانات مفعّل، لحثهم على تعطيله في موقعك."

#: modules/ads-for-adblockers/admin.class.php:89
#: modules/lazy-load/admin.class.php:58
msgid "Settings"
msgstr "الإعدادات"

#: modules/extended-adblocker/views/setting-dismissible.php:20
msgid "Set the timing for the overlay to reappear after being dismissed."
msgstr "حدد التوقيت الذي سيظهر فيه التراكب مرة أخرى بعد إغلاقه."

#: modules/ads-by-hours/views/publish-metabox-inputs.php:21
msgid "Set specific hours"
msgstr "قم بتحديد ساعات محددة"

#: modules/cache-busting/cache-busting.class.php:257
#: views/admin/placements/bulk-edit.php:45
msgid "Remove the placeholder if unfilled."
msgstr "قم بإزالة العنصر النائب إذا كان فارغًا."

#: modules/extended-adblocker/views/setting-activate-extended.php:64
msgid "Redirect URL"
msgstr "عنوان URL لإعادة التوجيه"

#: modules/extended-adblocker/views/setting-activate-extended.php:37
msgid "Redirect"
msgstr "إعادة توجيه"

#: modules/lazy-load/admin.class.php:68
msgid "Prevent ads from getting loaded before they appear in the visitor’s visible area."
msgstr "منع تحميل الإعلانات قبل أن تظهر في المنطقة المرئية للزائر."

#: modules/extended-adblocker/views/setting-activate-extended.php:22
msgid "Overlay"
msgstr "تراكب (أوفرلاي)"

#: views/admin/tables/ads/adsense-fallback.php:27
msgid "None"
msgstr "لا شيء"

#: views/admin/tables/ads/adsense-fallback.php:52
msgid "No default fallback ad selected. Choose one in the AdSense settings."
msgstr "لم يتم تحديد إعلان احتياطي افتراضي. اختر واحدًا في إعدادات AdSense."

#: modules/extended-adblocker/views/setting-activate-extended.php:17
msgid "No additional actions"
msgstr "لا توجد إجراءات إضافية"

#: modules/extended-adblocker/views/setting-dismissible.php:43
msgid "Never"
msgstr "أبدًا"

#: modules/extended-adblocker/views/setting-styling.php:19
msgid "Insert CSS to customize the overlay container layout."
msgstr "أضف CSS لتخصيص تخطيط حاوية التراكب."

#: modules/extended-adblocker/views/setting-dismissible.php:84
msgid "Insert CSS to customize the dismiss button layout."
msgstr "أضف CSS لتخصيص تخطيط زر الإغلاق."

#: modules/extended-adblocker/views/setting-styling.php:36
msgid "Insert CSS to customize the background of the overlay."
msgstr "أضف CSS لتخصيص خلفية التراكب."

#. translators: %s: index input field
#: modules/bbpress/class-admin.php:106
msgid "Inject after %s post"
msgstr "إدراج بعد %s مقالة"

#: includes/class-bootstrap.php:129
#: includes/class-bootstrap.php:152
msgid "Important Notice"
msgstr "إشعار هام"

#: modules/extended-adblocker/views/setting-dismissible.php:55
msgid "Hide dismiss button"
msgstr "إخفاء زر الإغلاق"

#: modules/ads-for-adblockers/views/placement-item.php:18
msgid "Groups"
msgstr "المجموعات"

#. translators: %s: first field, %s: last field
#: modules/advanced-display-conditions/main.class.php:571
msgid "from %1$s to %2$s"
msgstr "من %1$s إلى %2$s"

#: modules/ads-by-hours/views/publish-metabox-inputs.php:35
msgid "From"
msgstr "من"

#: includes/admin/class-adsense.php:38
msgid "Fallback ad/group"
msgstr "إعلان/مجموعة احتياطية"

#: views/admin/tables/ads/adsense-fallback.php:20
msgid "Fallback"
msgstr "عنصر احتياطي"

#: modules/extended-adblocker/views/setting-exclude.php:13
msgid "Exclude"
msgstr "استثناء"

#: modules/extended-adblocker/views/setting-dismissible.php:28
msgid "Everytime"
msgstr "في كل مرة"

#. translators: 1: The download URL, 2: The HTTP error code, 3: The HTTP header
#. title, 4: The response body
#: modules/geo/classes/admin.php:545
msgid "Error downloading database from: %1$s - %2$s %3$s - %4$s"
msgstr "خطأ في تنزيل قاعدة البيانات من: %1$s - %2$s %3$s - %4$s"

#: modules/extended-adblocker/views/setting-dismissible.php:68
msgid "Enter the text that you want to appear on the dismiss button."
msgstr "أدخل النص الذي تريد أن يظهر على زر الإغلاق."

#: modules/extended-adblocker/views/setting-activate-extended.php:68
msgid "Enter a specific page on your domain to which users with activated AdBlocker should be automatically redirected."
msgstr "أدخل صفحة محددة على نطاقك لإعادة توجيه المستخدمين الذين لديهم مانع إعلانات مفعّل إليها تلقائيًا."

#: modules/extended-adblocker/views/setting-dismissible.php:77
#: modules/extended-adblocker/views/setting-dismissible.php:94
#: modules/extended-adblocker/views/setting-styling.php:29
#: modules/extended-adblocker/views/setting-styling.php:46
msgid "Empty and save to revert to defaults."
msgstr "اتركه فارغًا ثم احفظ لاستعادة الإعدادات الافتراضية"

#: modules/advanced-visitor-conditions/main.class.php:211
msgid "Display or hide ad when user use adblocker."
msgstr "عرض أو إخفاء الإعلان عند استخدام المستخدم لمانع الإعلانات"

#: includes/admin/class-ad-list-table.php:42
#: includes/admin/class-ad-list-table.php:86
msgid "Display Once"
msgstr "عرض مرة واحدة"

#: modules/placement_conditions/admin.class.php:52
msgid "Display Conditions"
msgstr "شروط العرض"

#: modules/advanced-visitor-conditions/main.class.php:219
msgid "Display ads based on the user IP address. Enter one IP address per line."
msgstr "عرض الإعلانات بناءً على عنوان IP الخاص بالمستخدم. أدخل عنوان IP واحد في كل سطر."

#: modules/buddypress/class-buddypress.php:324
msgid "Display ads based on existing BuddyPress groups."
msgstr "عرض الإعلانات بناءً على المجموعات الموجودة في BuddyPress."

#: modules/extended-adblocker/views/setting-dismissible.php:65
msgid "Dismiss button text"
msgstr "نص زر الإغلاق"

#: modules/extended-adblocker/views/setting-dismissible.php:81
msgid "Dismiss button styling"
msgstr "تنسيق زر الإغلاق"

#: modules/extended-adblocker/views/setting-dismissible.php:48
msgid "Dismiss button"
msgstr "زر الإغلاق"

#: modules/extended-adblocker/main.class.php:83
#: modules/extended-adblocker/views/setting-dismissible.php:75
msgid "Dismiss"
msgstr "إغلاق"

#: modules/extended-adblocker/views/setting-dismissible.php:58
msgid "Disabling the dismiss button significantly limits site interaction."
msgstr "تعطيل زر الإغلاق يحد بشكل كبير من تفاعل المستخدم مع الموقع."

#: modules/cache-busting/cache-busting.class.php:261
msgid "Deleting an empty placement might lead to a layout shift."
msgstr "حذف موضع فارغ قد يؤدي إلى تغيير في تخطيط الصفحة."

#: views/admin/tables/ads/adsense-fallback.php:25
msgid "Default"
msgstr "افتراضي"

#: includes/admin/class-duplicate-placement.php:101
msgid "Create a copy of this placement"
msgstr "إنشاء نسخة من هذا الموضع"

#: includes/admin/class-group-duplication.php:77
msgid "Create a copy of this group"
msgstr "إنشاء نسخة من هذه المجموعة"

#: modules/extended-adblocker/views/setting-overlay-content.php:11
msgid "Content"
msgstr "المحتوى"

#: modules/extended-adblocker/views/setting-styling.php:16
msgid "Container styling"
msgstr "تنسيق الحاوية"

#: modules/extended-adblocker/views/setting-exclude.php:16
msgid "Choose which user roles to exclude from this ad blocker countermeasure."
msgstr "اختر أدوار المستخدمين التي تريد استثنائها من إجراء مواجهة مانع الإعلانات."

#: modules/lazy-load/admin.class.php:56
msgid "Cache Busting needs to be enabled"
msgstr "يجب تفعيل إلغاء التخزين المؤقت (Cache Busting)."

#: modules/ads-for-adblockers/admin.class.php:87
msgid "Cache Busting and Ad blocker disguise need to be enabled"
msgstr "يجب تفعيل إلغاء التخزين المؤقت (Cache Busting) وتنكر مانع الإعلانات."

#: modules/buddypress/class-buddypress.php:323
msgid "BuddyPress group"
msgstr "مجموعة BuddyPress"

#: modules/extended-adblocker/views/setting-styling.php:33
msgid "Background styling"
msgstr "تنسيق الخلفية"

#: modules/extended-adblocker/views/setting-activate-extended.php:41
msgid "Automatically redirect users with ad blockers enabled to an internal page. Content access is granted after turning off the ad blocker."
msgstr "إعادة توجيه المستخدمين الذين لديهم مانع إعلانات مفعّل تلقائيًا إلى صفحة داخلية. يتم منح الوصول إلى المحتوى بعد إيقاف تشغيل مانع الإعلانات."

#: includes/class-bootstrap.php:71
#: includes/class-bootstrap.php:78
msgid "Advanced Ads - Pro"
msgstr "Advanced Ads - Pro"

#. translators: ad title.
#: includes/class-adsense.php:52
msgid "AdSense fallback was loaded for empty AdSense ad \"%s\""
msgstr "تم تحميل إعلان الاحتياطي لإعلان AdSense الفارغ \"%s\""

#: includes/class-bootstrap.php:130
msgid "Addons listed below requires the <strong><a href=\"https://wpadvancedads.com/?utm_source=advanced-ads&utm_medium=link&utm_campaign=activate-advanced-ads-pro\" target=\"_blank\">Advanced Ads</a></strong> plugin to be installed and activated on your site."
msgstr "الإضافات المدرجة أدناه تتطلب تثبيت وتفعيل إضافة <strong><a href=\"https://wpadvancedads.com/?utm_source=advanced-ads&utm_medium=link&utm_campaign=activate-advanced-ads-pro\" target=\"_blank\">Advanced Ads</a></strong> على موقعك."

#: modules/advanced-visitor-conditions/main.class.php:210
msgid "Adblocker"
msgstr "مانع الإعلانات"

#: views/admin/tables/ads/adsense-fallback.php:29
msgid "Ad groups"
msgstr "مجموعات الإعلانات"

#: modules/extended-adblocker/admin.class.php:49
msgid "Ad blocker countermeasures"
msgstr "إجراءات مواجهة مانع الإعلانات"

#: modules/ads-for-adblockers/views/settings.php:29
msgid "Ad block disguise"
msgstr "تنكر مانع الإعلانات"

#: modules/extended-adblocker/views/setting-activate-extended.php:51
msgid "Activate the ad blocker disguise above to display the overlay."
msgstr "قم بتفعيل تنكر مانع الإعلانات أعلاه لعرض التراكب."

#: modules/ads-by-hours/src/class-admin.php:70
msgid "A cache plugin has been detected. It is recommended to enable Cache Busting and check the visitor's time when displaying the ad on the frontend."
msgstr "تم اكتشاف إضافة للتخزين المؤقت. يُوصى بتفعيل إلغاء التخزين المؤقت (Cache Busting) والتحقق من وقت الزائر عند عرض الإعلان في الواجهة الأمامية."

#: includes/class-bootstrap.php:95
msgid "<strong>Advanced Ads - Pro</strong> requires the <strong>Advanced Ads free</strong> plugin to be installed and activated on your site."
msgstr "يتطلب <strong>Advanced Ads - Pro</strong> تثبيت وتفعيل إضافة <strong>Advanced Ads المجانية</strong> على موقعك."

#: modules/extended-adblocker/views/setting-dismissible.php:37
msgid "1 week"
msgstr "أسبوع واحد"

#: modules/extended-adblocker/views/setting-dismissible.php:40
msgid "1 month"
msgstr "شهر واحد"

#: modules/extended-adblocker/views/setting-dismissible.php:31
msgid "1 hour"
msgstr "ساعة واحدة"

#: modules/extended-adblocker/views/setting-dismissible.php:34
msgid "1 day"
msgstr "يوم واحد"

#: modules/ads-for-adblockers/views/placement-item.php:15
msgid "--not selected--"
msgstr "--غير محدد--"

#. translators: comma separated list of days.
#: modules/weekdays/class-advanced-ads-pro-weekdays.php:181
msgid " on: %s"
msgstr " على: %s"

#. translators: 1. localized time 2. localized time 3. timezone name.
#: modules/weekdays/class-advanced-ads-pro-weekdays.php:187
msgid " between %1$s and %2$s %3$s"
msgstr ""

#: modules/responsive-ads/views/setting_reload_ads.php:19
msgid "You need to enable cache-busting in order to use this feature."
msgstr "يجب تمكين تفادي التخزين المؤقت لاستخدام هذه الميزة"

#. translators: 1 is the opening link to the Advanced Ads website, 2 the
#. closing link
#: classes/advanced-ads-pro.php:338
msgid "We have renamed the Responsive Ads add-on to ‘Advanced Ads AMP Ads’. With this change, the Browser Width visitor condition moved from that add-on into Advanced Ads Pro. You can deactivate ‘Advanced Ads AMP Ads’ if you don’t utilize AMP ads or the custom sizes feature for responsive AdSense ad units. %1$sRead more%2$s."
msgstr "لقد قمنا بإعادة تسمية الإضافة المتعلقة بالإعلانات المتجاوبة إلى 'إعلانات AMP'. مع هذا التغيير، انتقل شرط زائر عرض المتصفح من تلك الإضافة إلى الإعلانات المتقدمة Pro. يمكنك إيقاف 'إعلانات AMP' إذا لم تستخدم إعلانات AMP أو ميزة الأحجام المخصصة لوحدات إعلانات AdSense المتجاوبة. %1$sاقرأ المزيد%2$s."

#: modules/responsive-ads/includes/class-admin.php:45
msgid "Responsive Image Ads"
msgstr "الإعلانات صورية المتجاوبة"

#: modules/responsive-ads/includes/class-admin.php:38
msgid "Responsive Ads"
msgstr "الإعلانات متجاوبة"

#: modules/responsive-ads/views/setting_reload_ads.php:16
msgid "Reload ads when the screen resizes."
msgstr "إعادة تحميل الإعلانات عند تغيير حجم الشاشة"

#: modules/responsive-ads/includes/class-admin.php:53
msgid "Reload ads on resize"
msgstr "إعادة تحميل الإعلانات عند التغيير في حجم المتصفح"

#: modules/responsive-ads/includes/class-admin.php:61
msgid "Fallback width"
msgstr "العرض الاحتياطي للعنصر"

#: modules/responsive-ads/includes/class-common.php:40
msgid "Display ads based on the browser width."
msgstr "عرض الإعلانات بناءً على عرض المتصفح."

#: modules/cache-busting/admin-ui.class.php:51
msgid "Couldn't find the placement."
msgstr "تعذر العثور على الموضع."

#: modules/responsive-ads/views/setting_responsive_images.php:15
msgid "Check this option if the size of image ads is not adjusted responsively by your theme."
msgstr "حدد هذا الخيار إذا لم يتم ضبط حجم صور الإعلانات بشكل مستجيب حسب قالب ووردبريس المستخدم"

#: modules/cache-busting/admin-ui.class.php:48
msgid "Cache busting has been successfully enabled for the assigned placement."
msgstr "تم تفعيل تفادي التخزين المؤقت للموضع المخصص بنجاح."

#: modules/responsive-ads/includes/class-common.php:39
msgid "browser width"
msgstr "عرض المتصفح"

#: modules/cache-busting/admin-ui.class.php:88
#: modules/cache-busting/views/settings.php:75
msgid "off"
msgstr "إيقاف"

#: modules/cache-busting/views/settings.php:18
msgid "Choose which method to use when a placement needs cache busting and the option is set to “auto”."
msgstr "اختر الطريقة التي تُستخدم عندما يكون هناك حاجة لتفادي التخزين المؤقت للموقع و الخيار مُعيَّنًا على 'تلقائي'"

#: modules/cache-busting/admin-ui.class.php:86
msgid "auto"
msgstr "تلقائيًا"

#: modules/cache-busting/admin-ui.class.php:39
#: modules/geo/classes/admin.php:435
msgid "You are not allowed to do this."
msgstr "ليس لديك الإذن للقيام بذلك."

#: modules/geo/classes/admin.php:447
msgid "Please provide a MaxMind license key"
msgstr "يرجى تقديم مفتاح ترخيص MaxMind."

#. translators: Link to parallax manual
#: modules/parallax-ads/views/placement-options-after.php:24
msgid "Optimized for image ads. %s"
msgstr "مُحسّنة للإعلانات الصورية. %s"

#: modules/parallax-ads/views/placement-options-after.php:19
msgid "Enable the parallax effect."
msgstr "تفعيل تأثير البارالاكس."

#: modules/rest-api/classes/Admin_UI.php:33
msgid "REST API"
msgstr "واجهة برمجة تطبيقات REST"

#: modules/parallax-ads/classes/Advanced_Ads_Pro_Module_Parallax_Admin_UI.php:71
msgid "Parallax Ads"
msgstr "إعلانات الانتقالية الأفقية (إعلانات بارالاكس)"

#: modules/parallax-ads/views/placement-options-after.php:37
msgctxt "Parallax Ad placement height unit"
msgid "Unit"
msgstr "وحدة"

#: modules/parallax-ads/views/placement-options-after.php:31
msgctxt "Parallax Ad placement height"
msgid "Height"
msgstr "إرتفاع"

#: modules/rest-api/classes/Advanced_Ads_Group.php:27
msgid "Invalid group ID."
msgstr "معرف المجموعة غير صالح."

#: modules/parallax-ads/views/placement-options-after.php:40
msgid "Choose the height of the cutout, either in pixels or relative to the viewport."
msgstr "اختر ارتفاع الفصل ، إما بالبكسل أو بالنسبة لإطار العرض."

#: modules/geo/admin/views/visitor-profile.php:20
msgid "Reset Visitor Profile"
msgstr "إعادة تعيين ملف تعريف الزائر"

#: modules/geo/admin/views/visitor-profile.php:9
msgid "Location based on your visitor profile cookie:"
msgstr "الموقع بناءً على ملف تعريف الزائر الخاص بك:"

#: modules/geo/classes/admin.php:405
msgid "Location based on your IP address"
msgstr "الموقع على أساس عنوان IP الخاص بك"

#: modules/advanced-visitor-conditions/main.class.php:203
msgid "Display or hide ads for new visitors."
msgstr "عرض أو إخفاء الإعلانات للزوار الجدد."

#: modules/advanced-visitor-conditions/main.class.php:181
msgid "Display ads based on the value of a cookie. Set the operator to “matches/does not match” and leave the value empty to check only the existence of the cookie."
msgstr "عرض الإعلانات على أساس قيمة ملف تعريف الارتباط. اضبط عامل التشغيل على \"يتطابق / لا يتطابق\" واترك القيمة فارغة للتحقق فقط من وجود ملف تعريف الارتباط."

#: modules/advanced-display-conditions/main.class.php:88
msgid "Display ads based on the page language set by the WPML plugin."
msgstr "اعرض الإعلانات بناءً على لغة الصفحة التي تم تعيينها بواسطة المكون الإضافي WPML."

#: modules/buddypress/class-buddypress.php:335
#: modules/buddypress/class-buddypress.php:376
msgid "Display ads based on existing BuddyBoss groups."
msgstr "عرض الإعلانات على أساس مجموعات BuddyBoss الحالية."

#: modules/geo/classes/admin.php:367
msgid "miles (mi)"
msgstr "ميل"

#: modules/geo/classes/admin.php:726
msgid "There was an error connecting to the search service."
msgstr "حدث خطأ في الاتصال بخدمة البحث."

#: modules/geo/classes/admin.php:725
msgid "Your search did not return any results."
msgstr "ان بحثك لم يعد باي نتائج."

#. translators: 1: A link to the geo location service.
#: modules/geo/classes/admin.php:728
msgid "You can search for the geo coordinates manually at %1$s."
msgstr "مكنك البحث عن الإحداثيات الجغرافية يدويًا على %1$s."

#. translators: 1: The number of search results.
#: modules/geo/classes/admin.php:724
msgid "Found %1$d results. Please pick the one, you want to use."
msgstr "تم العثور على %1$d نتيجة. الرجاء اختيار واحد تريد استخدامه."

#: modules/geo/classes/admin.php:366
msgid "kilometers (km)"
msgstr "كيلومترات (كم)"

#. translators: 1 is the opening link to the plugins page, 2 the closing link
#: modules/geo/classes/Advanced_Ads_Geo_Version_Check.php:51
msgid "The geo-targeting visitor condition moved into Advanced Ads Pro. You can remove Geo Targeting %1$shere%2$s."
msgstr "تم نقل شرط استهداف الموقع الجغرافي إلى إعلانات متقدمة برو. يمكنك إزالة استهداف الموقع الجغرافي %1$sهنا%2$s"

#: modules/geo/classes/api.php:389
msgid "Zimbabwe"
msgstr "زيمبابوي"

#: modules/geo/classes/api.php:388
msgid "Zambia"
msgstr "زامبيا"

#: modules/geo/classes/admin.php:245
msgid "Your IP address format is incorrect"
msgstr "تنسيق عنوان IP الخاص بك غير صحيح"

#. translators: %s is a URL.
#: modules/cache-busting/admin-ui.class.php:196
msgid "You need a placement to deliver this group using cache busting. <a href=\"%s\" target=\"_blank\">Create a placement now.</a>"
msgstr "تحتاج إلى موضع لتسليم هذه المجموعة باستخدام تفادي التخزين المؤقت. <a href=\"%s\" target=\"_blank\">أنشئ موضعًا الآن.</a>"

#: modules/geo/classes/api.php:387
msgid "Yemen"
msgstr "اليمن"

#: modules/geo/classes/api.php:386
msgid "Western Samoa"
msgstr "ساموا الغربية"

#: modules/geo/classes/api.php:385
msgid "Western Sahara"
msgstr "الصحراء الغربية"

#: modules/geo/classes/api.php:384
msgid "Wallis and Futuna Islands"
msgstr "جزر واليس وفوتونا"

#: modules/geo/classes/api.php:383
msgid "Virgin Islands (USA)"
msgstr "جزر فيرجن (الولايات المتحدة الأمريكية)"

#: modules/geo/classes/api.php:382
msgid "Virgin Islands (British)"
msgstr "جزر العذراء البريطانية"

#: modules/geo/classes/api.php:381
msgid "Vietnam"
msgstr "فيتنام"

#: modules/geo/classes/api.php:380
msgid "Venezuela"
msgstr "فنزويلا"

#: modules/geo/classes/api.php:379
msgid "Vanuatu"
msgstr "فانواتو\t"

#: modules/geo/classes/api.php:378
msgid "Uzbekistan"
msgstr "أوزبكستان\t"

#: modules/geo/classes/api.php:377
msgid "US Minor Outlying Islands"
msgstr "جزر الولايات المتحدة البعيدة الصغيرة"

#: modules/geo/classes/api.php:376
msgid "Uruguay"
msgstr "أوروغواي"

#: modules/geo/admin/views/setting-download.php:57
msgid "Update geo location databases"
msgstr "تحديث قاعدة بيانات الموقع الجغرافي"

#: modules/geo/classes/api.php:129
msgid "United States"
msgstr "الولايات المتحدة"

#: modules/geo/classes/api.php:130
msgid "United Kingdom"
msgstr "المملكة المتحدة"

#: modules/geo/classes/api.php:375
msgid "United Arab Emirates"
msgstr "الإمارات العربية المتحدة\t"

#: modules/geo/classes/api.php:374
msgid "Ukraine"
msgstr "أوكرانيا"

#: modules/geo/classes/api.php:373
msgid "Uganda"
msgstr "أوغندا"

#: modules/geo/classes/api.php:372
msgid "Tuvalu"
msgstr "توفالو"

#: modules/geo/classes/api.php:371
msgid "Turks and Caicos Islands"
msgstr "جزر تركس وكايكوس"

#: modules/geo/classes/api.php:370
msgid "Turkmenistan"
msgstr "تركمانستان"

#: modules/geo/classes/api.php:369
msgid "Turkey"
msgstr "تركيا"

#: modules/geo/classes/api.php:368
msgid "Tunisia"
msgstr "تونس"

#: modules/geo/classes/api.php:367
msgid "Trinidad and Tobago"
msgstr "ترينيداد وتوباغو"

#: modules/geo/classes/api.php:366
msgid "Tonga"
msgstr "تونغا"

#: modules/geo/classes/api.php:365
msgid "Tokelau"
msgstr "توكيلاو"

#: modules/geo/classes/api.php:364
msgid "Togo"
msgstr "توغو"

#: modules/geo/classes/api.php:363
msgid "Timor-Leste"
msgstr "تيمور الشرقية"

#: modules/geo/classes/admin.php:351
msgid "This option can’t be used if your site is using sucuri.net."
msgstr "لا يمكن استخدام هذا الخيار إذا كان موقعك يستخدم Sucuri.net."

#: modules/geo/classes/admin.php:441
msgid "The upload dir is not available"
msgstr "دليل التحميل غير متوفر"

#: modules/geo/admin/views/setting-download.php:31
msgid "The MaxMind license key is missing."
msgstr "مفتاح ترخيص MaxMind مفقود."

#: modules/geo/admin/views/setting-download.php:91
msgid "The databases are updated on the first Tuesday (midnight, GMT) of each month."
msgstr "يتم تحديث قواعد البيانات في أول يوم ثلاثاء (منتصف الليل بتوقيت جرينتش) من كل شهر."

#: modules/geo/classes/api.php:362
msgid "Thailand"
msgstr "تايلاند"

#: modules/geo/classes/api.php:361
msgid "Tanzania"
msgstr "تنزانيا"

#: modules/geo/classes/api.php:360
msgid "Tajikistan"
msgstr "طاجيكستان"

#: modules/geo/classes/api.php:359
msgid "Taiwan"
msgstr "تايوان"

#: modules/geo/classes/api.php:358
msgid "Syrian Arab Republic"
msgstr "الجمهورية العربية السورية"

#: modules/geo/classes/api.php:357
msgid "Switzerland"
msgstr "سويسرا"

#: modules/geo/classes/api.php:356
msgid "Sweden"
msgstr "السويد"

#: modules/geo/classes/api.php:355
msgid "Swaziland"
msgstr "سوازيلاند"

#: modules/geo/classes/api.php:354
msgid "Svalbard and Jan Mayen Islands"
msgstr "جزر سفالبارد وجان ماين"

#: modules/geo/classes/api.php:353
msgid "Suriname"
msgstr "سورينام"

#: modules/geo/classes/api.php:352
msgid "Sudan"
msgstr "السودان"

#: modules/geo/classes/plugin.php:130
msgid "Sucuri Header (country only)"
msgstr "رأس Sucuri (الدولة فقط)"

#: modules/geo/classes/admin.php:333
msgid "State/Region"
msgstr "الولاية / المنطقة"

#: modules/geo/classes/api.php:351
msgid "Sri Lanka"
msgstr "سريلانكا"

#: modules/geo/classes/api.php:350
msgid "Spain"
msgstr "إسبانيا"

#: modules/geo/classes/api.php:349
msgid "South Sudan"
msgstr "جنوب السودان"

#: modules/geo/classes/api.php:348
msgid "South Korea"
msgstr "كوريا الجنوبية"

#: modules/geo/classes/api.php:347
msgid "South Georgia"
msgstr "جورجيا الجنوبية"

#: modules/geo/classes/api.php:135
msgid "South America"
msgstr "امريكا الجنوبية"

#: modules/geo/classes/api.php:346
msgid "South Africa"
msgstr "جنوب أفريقيا"

#: modules/geo/classes/api.php:345
msgid "Somalia"
msgstr "الصومال"

#: modules/geo/classes/api.php:344
msgid "Solomon Islands"
msgstr "جزر سليمان"

#: modules/geo/classes/api.php:343
msgid "Slovenia"
msgstr "سلوفينيا\t"

#: modules/geo/classes/api.php:342
msgid "Slovak Republic"
msgstr "سلوفاكيا\t"

#: modules/geo/classes/api.php:341
msgid "Singapore"
msgstr "سنغافورة"

#: modules/geo/classes/api.php:340
msgid "Sierra Leone"
msgstr "سيراليون"

#: modules/geo/classes/api.php:339
msgid "Seychelles"
msgstr "سيشل"

#: modules/geo/classes/api.php:338
msgid "Serbia"
msgstr "صربيا\t"

#: modules/geo/classes/api.php:337
msgid "Senegal"
msgstr "السنغال\t"

#: modules/geo/classes/admin.php:390
msgid "Search"
msgstr "بحث"

#: modules/geo/classes/api.php:336
msgid "Saudi Arabia"
msgstr "المملكة العربية السعودية"

#: modules/geo/classes/api.php:334
msgid "San Marino"
msgstr "سان مارينو\t"

#: modules/geo/classes/api.php:333
msgid "Saint Vincent and the Grenadines"
msgstr "سانت فينسنت والغرينادين\t"

#: modules/geo/classes/api.php:332
msgid "Saint Pierre and Miquelon"
msgstr "سانت بيير وميكلون"

#: modules/geo/classes/api.php:330
msgid "Saint Martin (French)"
msgstr "سانت مارتن (الفرنسية)"

#: modules/geo/classes/api.php:331
msgid "Saint Martin (Dutch)"
msgstr "سانت مارتن (الهولندية)"

#: modules/geo/classes/api.php:329
msgid "Saint Lucia"
msgstr "سانت لوسيا\t"

#: modules/geo/classes/api.php:328
msgid "Saint Kitts and Nevis"
msgstr "سانت كيتس ونيفيس\t"

#: modules/geo/classes/api.php:327
msgid "Saint Helena"
msgstr "سانت هيلانة"

#: modules/geo/classes/api.php:326
msgid "Saint Barth&eacute;lemy"
msgstr "سانت بارتيليمي"

#: modules/geo/classes/api.php:335
msgid "S&atilde;o Tom&eacute; and Pr&iacute;ncipe"
msgstr "ساو تومي وبرينسيبي"

#: modules/geo/classes/api.php:325
msgid "Rwanda"
msgstr "رواندا\t"

#: modules/geo/classes/api.php:324
msgid "Russian Federation"
msgstr "روسيا\t"

#: modules/geo/classes/api.php:323
msgid "Romania"
msgstr "رومانيا\t"

#: modules/geo/classes/api.php:322
msgid "Reunion Island"
msgstr "ريونيون"

#: modules/geo/classes/api.php:321
msgid "Republic of Kosovo"
msgstr "جمهورية كوسوفو"

#: modules/geo/classes/api.php:320
msgid "Qatar"
msgstr "قطر\t"

#: modules/geo/classes/api.php:319
msgid "Puerto Rico"
msgstr "بورتوريكو\t"

#: modules/geo/classes/api.php:318
msgid "Portugal"
msgstr "البرتغال\t"

#: modules/geo/classes/api.php:317
msgid "Poland"
msgstr "بولندا"

#. translators: 1: opening <a>-tag to Advanced Ads manual, 2: closing <a>-tag
#: modules/geo/admin/views/setting-download.php:36
#: modules/geo/classes/admin.php:234
msgid "Please read the %1$sinstallation instructions%2$s."
msgstr "الرجاء قراءة %1$sإرشادات التثبيت%2$s"

#: modules/geo/classes/api.php:316
msgid "Pitcairn Island"
msgstr "جزيرة بيتكيرن"

#: modules/geo/classes/api.php:315
msgid "Phillipines"
msgstr "الفلبين"

#: modules/geo/classes/api.php:314
msgid "Peru"
msgstr "بيرو"

#: modules/geo/classes/api.php:313
msgid "Paraguay"
msgstr "باراغواي"

#: modules/geo/classes/api.php:312
msgid "Papua New Guinea"
msgstr "بابوا غينيا الجديدة\t"

#: modules/geo/classes/api.php:311
msgid "Panama"
msgstr "بنما\t"

#: modules/geo/classes/api.php:310
msgid "Palestinian Territories"
msgstr "دولة فلسطين"

#: modules/geo/classes/api.php:309
msgid "Palau"
msgstr "بالاو"

#: modules/geo/classes/api.php:308
msgid "Pakistan"
msgstr "باكستان\t"

#: modules/geo/classes/admin.php:332
#: modules/geo/classes/admin.php:334
msgid "or"
msgstr "أو"

#: modules/geo/classes/api.php:307
msgid "Oman"
msgstr "عُمان"

#: modules/geo/classes/api.php:139
msgid "Oceania"
msgstr "أوقيانوسيا"

#: modules/geo/classes/api.php:306
msgid "Norway"
msgstr "النرويج"

#: modules/geo/classes/api.php:305
msgid "Northern Mariana Islands"
msgstr "جزر ماريانا الشمالية"

#: modules/geo/classes/api.php:304
msgid "North Korea"
msgstr "كوريا الشمالية"

#: modules/geo/classes/api.php:134
msgid "North America"
msgstr "أمريكا الشمالية"

#: modules/geo/classes/api.php:303
msgid "Norfolk Island"
msgstr "جزيرة نورفولك"

#: modules/geo/classes/api.php:302
msgid "Niue"
msgstr "نيوي"

#: modules/geo/classes/api.php:301
msgid "Nigeria"
msgstr "نيجيريا"

#: modules/geo/classes/api.php:300
msgid "Niger"
msgstr "النيجر"

#: modules/geo/classes/api.php:299
msgid "Nicaragua"
msgstr "نيكاراجوا\t"

#. translators: Timestamp in the localized date_format
#: modules/geo/admin/views/setting-download.php:84
msgid "Next possible update on %s."
msgstr "التحديث التالي المحتمل : %s"

#: modules/geo/classes/api.php:298
msgid "New Zealand"
msgstr "نيوزيلندا\t"

#: modules/geo/classes/api.php:297
msgid "New Caledonia"
msgstr "كاليدونيا الجديدة\t"

#: modules/geo/classes/api.php:296
msgid "Netherlands Antilles"
msgstr "جزر الأنتيل الهولندية"

#: modules/geo/classes/api.php:295
msgid "Netherlands"
msgstr "هولندا\t"

#: modules/geo/classes/api.php:294
msgid "Nepal"
msgstr "نيبال"

#: modules/geo/classes/api.php:293
msgid "Nauru"
msgstr "ناورو"

#: modules/geo/classes/api.php:292
msgid "Namibia"
msgstr "ناميبيا"

#: modules/geo/classes/api.php:291
msgid "Myanmar"
msgstr "ميانمار"

#: modules/geo/classes/api.php:290
msgid "Mozambique"
msgstr "موزمبيق\t"

#: modules/geo/classes/api.php:289
msgid "Morocco"
msgstr "المغرب"

#: modules/geo/classes/api.php:288
msgid "Montserrat"
msgstr "مونتسرات"

#: modules/geo/classes/api.php:287
msgid "Montenegro"
msgstr "الجبل الأسو"

#: modules/geo/classes/api.php:286
msgid "Mongolia"
msgstr "منغوليا"

#: modules/geo/classes/api.php:285
msgid "Monaco"
msgstr "موناكو\t"

#: modules/geo/classes/api.php:284
msgid "Moldova, Republic of"
msgstr "مولدوفا\t"

#: modules/geo/classes/api.php:283
msgid "Micronesia"
msgstr "ميكرونيزيا"

#: modules/geo/classes/api.php:282
msgid "Mexico"
msgstr "المكسيك"

#: modules/geo/classes/admin.php:76
msgid "Method"
msgstr "طريقة"

#: modules/geo/classes/api.php:281
msgid "Mayotte"
msgstr "مايوت"

#: modules/geo/classes/admin.php:241
msgid "Maybe you are working on a local or secured environment."
msgstr "ربما تعمل في بيئة محلية أو آمنة."

#: modules/geo/classes/admin.php:57
msgid "MaxMind license key"
msgstr "مفتاح ترخيص MaxMind"

#: modules/geo/classes/plugin.php:124
msgid "MaxMind database (default)"
msgstr "قاعدة بيانات MaxMind (افتراضي)"

#: modules/geo/classes/admin.php:66
msgid "MaxMind Database"
msgstr "قاعدة بيانات MaxMind"

#: modules/geo/classes/api.php:280
msgid "Mauritius"
msgstr "موريشيوس"

#: modules/geo/classes/api.php:279
msgid "Mauritania"
msgstr "موريتانيا\t"

#: modules/geo/classes/api.php:278
msgid "Martinique"
msgstr "مارتينيك"

#: modules/geo/classes/api.php:277
msgid "Marshall Islands"
msgstr "جزر مارشال\t"

#: modules/geo/classes/api.php:276
msgid "Malta"
msgstr "مالطا"

#: modules/geo/classes/api.php:275
msgid "Mali"
msgstr "مالي"

#: modules/geo/classes/api.php:274
msgid "Maldives"
msgstr "المالديف"

#: modules/geo/classes/api.php:273
msgid "Malaysia"
msgstr "ماليزيا"

#: modules/geo/classes/api.php:272
msgid "Malawi"
msgstr "مالاوي"

#: modules/geo/classes/api.php:271
msgid "Madagascar"
msgstr "مدغشقر"

#: modules/geo/classes/api.php:270
msgid "Macedonia"
msgstr "مقدونيا الشمالية\t"

#: modules/geo/classes/api.php:269
msgid "Macau"
msgstr "ماكاو"

#: modules/geo/classes/api.php:268
msgid "Luxembourg"
msgstr "لوكسمبورغ"

#: modules/geo/classes/admin.php:373
msgid "Longitude"
msgstr "خط الطول"

#: modules/geo/classes/api.php:267
msgid "Lithuania"
msgstr "ليتوانيا\t"

#: modules/geo/classes/api.php:266
msgid "Liechtenstein"
msgstr "ليختنشتاين\t"

#: modules/geo/classes/api.php:265
msgid "Libyan Arab Jamahiriya"
msgstr "الجماهيرية العربية الليبية"

#: modules/geo/classes/api.php:264
msgid "Liberia"
msgstr "ليبيريا\t"

#: modules/geo/classes/api.php:263
msgid "Lesotho"
msgstr "ليسوتو"

#: modules/geo/classes/api.php:262
msgid "Lebanon"
msgstr "لبنان"

#: modules/geo/classes/api.php:261
msgid "Latvia"
msgstr "لاتفيا"

#: modules/geo/classes/admin.php:372
msgid "Latitude"
msgstr "خط العرض"

#. translators: Timestamp in the localized date_format
#: modules/geo/admin/views/setting-download.php:73
msgid "Last update: %s"
msgstr "التحديث الأخير: %s"

#: modules/geo/classes/api.php:260
msgid "Lao People's Democratic Republic"
msgstr "جمهورية لاو الديمقراطية الشعبية"

#: modules/geo/classes/admin.php:86
msgid "Language of names"
msgstr "لغة الأسماء"

#: modules/geo/classes/api.php:259
msgid "Kyrgyzstan"
msgstr "قيرغيزستان"

#: modules/geo/classes/api.php:258
msgid "Kuwait"
msgstr "الكويت\t"

#: modules/geo/classes/api.php:257
msgid "Kiribati"
msgstr "كيريباتي"

#: modules/geo/classes/api.php:256
msgid "Kenya"
msgstr "كينيا"

#: modules/geo/classes/api.php:255
msgid "Kazakhstan"
msgstr "كازاخستان\t"

#: modules/geo/classes/api.php:254
msgid "Jordan"
msgstr "الأردن"

#: modules/geo/classes/api.php:253
msgid "Jersey"
msgstr "جيرسي"

#: modules/geo/classes/api.php:252
msgid "Japan"
msgstr "اليابان"

#: modules/geo/classes/api.php:251
msgid "Jamaica"
msgstr "جامايكا"

#: modules/geo/classes/api.php:250
msgid "Italy"
msgstr "إيطاليا\t"

#. translators: %s is a URL.
#: modules/cache-busting/admin-ui.class.php:218
msgid "It seems that a caching plugin is activated. Your ads might not rotate properly, while cache busting is disabled for the placement your group is using. <a href=\"#\" data-placement=\"%s\" class=\"js-placement-activate-cb\">Activate cache busting for this placement.</a>"
msgstr "يبدو أنه تم تفعيل إضافة التخزين المؤقت (الكاش). ربما لا تتناوب إعلاناتك بشكل صحيح، مالم يتم تفعيل تفادي التخزين المؤقت للموضع الذي يستخدمه مجموعتك. <a href=\"#\" data-placement=\"%s\" class=\"js-placement-activate-cb\">تفعيل إزالة التخزين المؤقت لهذا الموضع.</a>"

#. translators: %s is a URL.
#: modules/cache-busting/admin-ui.class.php:173
msgid "It seems that a caching plugin is activated. Your ads might not rotate properly while cache busting is disabled. <a href=\"%s\" target=\"_blank\">Activate cache busting.</a>"
msgstr "يبدو أنه تم تفعيل إضافة التخزين المؤقت (الكاش). ربما لا تتناوب إعلاناتك بشكل صحيح مالم يتم تفعيل تفادي التخزين المؤقت. <a href=\"%s\" target=\"_blank\">تفعيل إزالة التخزين المؤقت (Cache busting).</a>"

#: modules/geo/classes/api.php:249
msgid "Israel"
msgstr "إسرائيل"

#: modules/geo/classes/api.php:248
msgid "Isle of Man"
msgstr "جزيرة آيل أوف مان"

#: modules/geo/classes/api.php:247
msgid "Ireland"
msgstr "أيرلندا"

#: modules/geo/classes/api.php:246
msgid "Iraq"
msgstr "العراق"

#: modules/geo/classes/api.php:245
msgid "Iran"
msgstr "إيران"

#: modules/geo/classes/api.php:244
msgid "Indonesia"
msgstr "إندونيسيا"

#: modules/geo/classes/api.php:243
msgid "India"
msgstr "الهند"

#: modules/geo/admin/views/setting-download.php:50
msgid "In order to use Geo Targeting, please download the geo location databases by clicking on the button below."
msgstr "من أجل استخدام الاستهداف الجغرافي ، يرجى تنزيل قاعدة بيانات الموقع الجغرافي بالنقر فوق الزر أدناه."

#: modules/click-fraud-protection/click-fraud-protection.class.php:156
msgid "Ignore global Click Fraud Protection"
msgstr "تجاهل حماية النقرات الاحتيالية الشامل"

#: modules/geo/classes/api.php:242
msgid "Iceland"
msgstr "أيسلندا"

#: modules/geo/classes/api.php:241
msgid "Hungary"
msgstr "المجر"

#: modules/geo/classes/api.php:240
msgid "Hong Kong"
msgstr "هونغ كونغ\t"

#: modules/geo/classes/api.php:239
msgid "Honduras"
msgstr "هندوراس"

#: modules/geo/classes/api.php:238
msgid "Holy See (City Vatican State)"
msgstr "الكرسي الرسولي (دولة الفاتيكان)"

#: modules/geo/classes/api.php:237
msgid "Heard and McDonald Islands"
msgstr "جزر هيرد وماكدونالد"

#: modules/geo/classes/api.php:236
msgid "Haiti"
msgstr "هايتي"

#: modules/geo/classes/api.php:235
msgid "Guyana"
msgstr "غيانا"

#: modules/geo/classes/api.php:234
msgid "Guinea-Bissau"
msgstr "غينيا بيساو\t"

#: modules/geo/classes/api.php:233
msgid "Guinea"
msgstr "غينيا"

#: modules/geo/classes/api.php:232
msgid "Guernsey"
msgstr "غيرنسي"

#: modules/geo/classes/api.php:231
msgid "Guatemala"
msgstr "غواتيمال"

#: modules/geo/classes/api.php:230
msgid "Guam"
msgstr "غوام"

#: modules/geo/classes/api.php:229
msgid "Guadeloupe"
msgstr "غوادلوب"

#: modules/geo/classes/api.php:228
msgid "Grenada"
msgstr "غرينادا"

#: modules/geo/classes/api.php:227
msgid "Greenland"
msgstr "جرينلاند"

#: modules/geo/classes/api.php:224
msgid "Greece"
msgstr "اليونان"

#: modules/geo/classes/api.php:226
msgid "Gibraltar"
msgstr "جبل طارق"

#: modules/geo/classes/api.php:225
msgid "Ghana"
msgstr "غانا"

#: modules/geo/classes/admin.php:370
msgid "get coordinates"
msgstr "الحصول على الإحداثيات"

#: modules/geo/classes/api.php:132
msgid "Germany"
msgstr "ألمانيا"

#: modules/geo/classes/api.php:223
msgid "Georgia"
msgstr "جورجيا"

#: modules/geo/classes/admin.php:49
msgid "Geo Targeting"
msgstr "استهداف جغرافي"

#: modules/geo/classes/plugin.php:105
msgid "geo location"
msgstr "الموقع الجغرافي"

#: modules/geo/admin/views/setting-download.php:47
#: modules/geo/classes/admin.php:231
msgid "Geo Databases not found."
msgstr "لم يتم العثور على قاعدة البيانات الجغرافية."

#: modules/geo/classes/public.php:222
msgid "Geo Databases not found"
msgstr "لم يتم العثور على قاعدة البيانات الجغرافية"

#: modules/geo/admin/views/setting-download.php:16
msgid "Geo Databases found."
msgstr "تم العثور على قاعدة البيانات الجغرافية."

#: modules/geo/classes/api.php:222
msgid "Gambia"
msgstr "غامبيا"

#: modules/geo/classes/api.php:221
msgid "Gabon"
msgstr "الجابون"

#: modules/geo/classes/admin.php:368
msgid "from"
msgstr "من"

#: modules/geo/classes/api.php:220
msgid "French Southern Territories"
msgstr "المناطق الجنوبية لفرنسا"

#: modules/geo/classes/api.php:219
msgid "French Polynesia"
msgstr "بولينيزيا الفرنسية"

#: modules/geo/classes/api.php:218
msgid "French Guiana"
msgstr "غينيا الفرنسية\t"

#: modules/geo/classes/api.php:217
msgid "France"
msgstr "فرنسا"

#: modules/geo/classes/api.php:216
msgid "Finland"
msgstr "فنلندا"

#: modules/geo/classes/api.php:215
msgid "Fiji"
msgstr "فيجي"

#: modules/geo/classes/api.php:214
msgid "Faroe Islands"
msgstr "جزر فارو"

#: modules/geo/classes/api.php:213
msgid "Falkland Islands"
msgstr "جزر فوكلاند\t"

#: modules/geo/classes/api.php:131
msgid "European Union"
msgstr "الاتحاد الأوروبي"

#: modules/geo/classes/api.php:136
msgid "Europe"
msgstr "أوروبا"

#: modules/geo/classes/api.php:212
msgid "Ethiopia"
msgstr "أثيوبيا"

#: modules/geo/classes/api.php:211
msgid "Estonia"
msgstr "إستونيا"

#: modules/geo/classes/api.php:210
msgid "Eritrea"
msgstr "إريتريا"

#: modules/geo/classes/api.php:208
msgid "Equatorial Guinea"
msgstr "غينيا الإستوائية"

#: modules/geo/classes/admin.php:393
msgid "Enter the name of the city, click the search button and pick one of the results to set the coordinates of the center."
msgstr "أدخل اسم المدينة ، وانقر فوق زر البحث واختر إحدى النتائج لتعيين إحداثيات المركز."

#: modules/geo/classes/api.php:209
msgid "El Salvador"
msgstr "السلفادور"

#: modules/geo/classes/api.php:207
msgid "Egypt"
msgstr "مصر"

#: modules/geo/classes/api.php:206
msgid "Ecuador"
msgstr "الاكوادور"

#: modules/geo/classes/api.php:205
msgid "East Timor"
msgstr "تيمور الشرقية"

#: modules/geo/classes/api.php:204
msgid "Dominican Republic"
msgstr "جمهورية الدومينيكان"

#: modules/geo/classes/api.php:203
msgid "Dominica"
msgstr "دومينيكا"

#: modules/geo/classes/api.php:202
msgid "Djibouti"
msgstr "جيبوتي"

#: modules/geo/classes/admin.php:378
msgid "Distance to center"
msgstr "المسافة إلى المركز"

#: modules/geo/classes/admin.php:361
msgid "Distance is less than or equal to"
msgstr "المسافة أقل من أو تساوي"

#: modules/geo/classes/admin.php:362
msgid "Distance is greater than"
msgstr "المسافة أكبر من"

#: modules/geo/classes/admin.php:364
msgid "Distance"
msgstr "المسافة"

#: modules/geo/classes/plugin.php:106
msgid "Display ads based on geo location."
msgstr "عرض الإعلانات على أساس الموقع الجغرافي."

#: modules/geo/classes/api.php:201
msgid "Denmark"
msgstr "الدنمارك"

#: modules/geo/classes/admin.php:518
msgid "Database updated successfully!"
msgstr "تم تحديث قاعدة البيانات بنجاح!"

#: modules/geo/classes/admin.php:536
msgid "Database update failed"
msgstr "فشل تحديث قاعدة البيانات"

#: modules/geo/classes/api.php:200
msgid "Czech Republic"
msgstr "الجمهورية التشيكية"

#: modules/geo/classes/api.php:199
msgid "Cyprus"
msgstr "قبرص"

#: modules/geo/classes/api.php:198
msgid "Cura&Ccedil;ao"
msgstr "كوراساو"

#: modules/geo/classes/api.php:197
msgid "Cuba"
msgstr "كوبا"

#: modules/geo/classes/api.php:196
msgid "Croatia/Hrvatska"
msgstr "كرواتيا / هرفاتسكا"

#. translators: an error mesage.
#: modules/geo/classes/admin.php:573
msgid "Could not open downloaded database for reading: %s"
msgstr "تعذر فتح قاعدة البيانات التي تم تنزيلها لقراءة: %s"

#. translators: MaxMind database file name.
#: modules/geo/classes/admin.php:602
msgid "Could not open database for writing %s"
msgstr "تعذر فتح قاعدة البيانات لكتابة %s"

#: modules/geo/classes/admin.php:589
msgid "Could not access filesystem"
msgstr "تعذر الوصول إلى نظام الملفات"

#: modules/geo/classes/api.php:195
msgid "Cote d'Ivoire"
msgstr "كوت ديفوار"

#: modules/geo/classes/api.php:194
msgid "Costa Rica"
msgstr "كوستا ريكا"

#: modules/geo/admin/views/visitor-profile.php:15
#: modules/geo/classes/admin.php:252
msgid "Coordinates"
msgstr "إحداثيات"

#: modules/geo/classes/api.php:193
msgid "Cook Islands"
msgstr "جزر كوك"

#: modules/geo/classes/api.php:192
msgid "Congo, Republic of"
msgstr "جمهورية الكونغو"

#: modules/geo/classes/api.php:191
msgid "Congo, Democratic People's Republic"
msgstr "جمهورية الكونغو الديمقراطية الشعبية"

#: modules/geo/classes/api.php:190
msgid "Comoros"
msgstr "جزر القمر"

#: modules/geo/classes/api.php:189
msgid "Colombia"
msgstr "كولومبيا"

#: modules/geo/classes/api.php:188
msgid "Cocos Islands"
msgstr "جزر كوكوس"

#: modules/geo/classes/admin.php:335
#: modules/geo/classes/admin.php:388
msgid "City"
msgstr "مدينة"

#: modules/geo/classes/api.php:187
msgid "Christmas Island"
msgstr "جزيرة الكريسماس"

#: modules/geo/admin/views/setting-locale.php:15
msgid "Choose the language of the state/region or city entered. If the language is not available in the geo location database, it will check against the English version."
msgstr "اختر لغة الولاية / المنطقة أو المدينة التي تم إدخالها. إذا لم تكن اللغة متاحة في قاعدة بيانات الموقع الجغرافي ، فستتحقق من النسخة الإنجليزية."

#: modules/geo/classes/api.php:186
msgid "China"
msgstr "الصين"

#: modules/geo/classes/api.php:185
msgid "Chile"
msgstr "تشيلي"

#: modules/geo/classes/api.php:184
msgid "Chad"
msgstr "تشاد"

#: modules/geo/classes/api.php:183
msgid "Central African Republic"
msgstr "جمهورية افريقيا الوسطى"

#: modules/geo/classes/api.php:182
msgid "Cayman Islands"
msgstr "جزر كايمان"

#: modules/geo/classes/api.php:181
msgid "Cape Verde"
msgstr "الرأس الأخضر"

#: modules/geo/classes/admin.php:391
msgid "Cancel"
msgstr "إلغاء"

#: modules/geo/classes/api.php:180
msgid "Canada"
msgstr "كندا"

#: modules/geo/classes/api.php:179
msgid "Cameroon"
msgstr "الكاميرون"

#: modules/geo/classes/api.php:178
msgid "Cambodia"
msgstr "كمبوديا"

#: modules/geo/classes/admin.php:288
msgid "by specific location"
msgstr "حسب موقع معين"

#: modules/geo/classes/admin.php:294
msgid "by radius"
msgstr "حسب نصف القطر"

#: modules/geo/classes/api.php:177
msgid "Burundi"
msgstr "بوروندي"

#: modules/geo/classes/api.php:176
msgid "Burkina Faso"
msgstr "بوركينا فاسو"

#: modules/geo/classes/api.php:175
msgid "Bulgaria"
msgstr "بلغاريا"

#: modules/geo/classes/api.php:174
msgid "Brunei Darrussalam"
msgstr "بروناي دار السلام"

#: modules/geo/classes/api.php:173
msgid "British Indian Ocean Territory"
msgstr "إقليم المحيط البريطاني الهندي"

#: modules/geo/classes/api.php:172
msgid "Brazil"
msgstr "البرازيل"

#: modules/geo/classes/api.php:171
msgid "Bouvet Island"
msgstr "جزيرة بوفيت"

#: modules/geo/classes/api.php:170
msgid "Botswana"
msgstr "بوتسوانا"

#: modules/geo/classes/api.php:169
msgid "Bosnia and Herzegovina"
msgstr "البوسنة والهرسك"

#: modules/geo/classes/api.php:168
msgid "Bonaire, Saint Eustatius and Saba"
msgstr "بونير وسانت أوستاتيوس وسابا"

#: modules/geo/classes/api.php:167
msgid "Bolivia"
msgstr "بوليفيا"

#: modules/geo/classes/api.php:166
msgid "Bhutan"
msgstr "بوتان"

#: modules/geo/classes/api.php:165
msgid "Bermuda"
msgstr "برمودا"

#: modules/geo/classes/api.php:164
msgid "Benin"
msgstr "بنين"

#: modules/geo/classes/api.php:163
msgid "Belize"
msgstr "بليز"

#: modules/geo/classes/api.php:162
msgid "Belgium"
msgstr "بلجيكا"

#: modules/geo/classes/api.php:161
msgid "Belarus"
msgstr "بيلاروسيا"

#: modules/geo/classes/api.php:160
msgid "Barbados"
msgstr "بربادوس"

#: modules/geo/classes/api.php:159
msgid "Bangladesh"
msgstr "بنغلاديش"

#: modules/geo/classes/api.php:158
msgid "Bahrain"
msgstr "البحرين"

#: modules/geo/classes/api.php:157
msgid "Bahamas"
msgstr "جزر البهاما"

#: modules/geo/classes/api.php:156
msgid "Azerbaijan"
msgstr "أذربيجان"

#: modules/geo/classes/api.php:155
msgid "Austria"
msgstr "النمسا"

#: modules/geo/classes/api.php:154
msgid "Australia"
msgstr "أستراليا"

#: modules/geo/classes/api.php:138
msgid "Asia"
msgstr "آسيا"

#: modules/geo/classes/api.php:153
msgid "Aruba"
msgstr "أروبا"

#: modules/geo/classes/api.php:152
msgid "Armenia"
msgstr "أرمينيا"

#: modules/geo/classes/api.php:151
msgid "Argentina"
msgstr "الأرجنتين"

#: modules/geo/classes/api.php:150
msgid "Antigua and Barbuda"
msgstr "أنتيغوا وبربودا"

#: modules/geo/classes/api.php:149
msgid "Antarctica"
msgstr "القارة القطبية الجنوبية"

#: modules/geo/classes/api.php:148
msgid "Anguilla"
msgstr "أنغيلا"

#: modules/geo/classes/api.php:147
msgid "Angola"
msgstr "أنغولا"

#: modules/geo/classes/api.php:146
msgid "Andorra"
msgstr "أندورا"

#: modules/geo/classes/api.php:145
msgid "American Samoa"
msgstr "ساموا الأمريكية"

#: modules/geo/classes/api.php:144
msgid "Algeria"
msgstr "الجزائر"

#: modules/geo/classes/api.php:143
msgid "Albania"
msgstr "ألبانيا"

#: modules/geo/classes/api.php:137
msgid "Africa"
msgstr "أفريقيا"

#: modules/geo/classes/api.php:141
msgid "Afghanistan"
msgstr "أفغانستان"

#. translators: an error message.
#: modules/geo/classes/public.php:227
msgid "Address not found: %s"
msgstr "لم يتم العثور على العنوان: %s"

#: modules/group-refresh/views/settings_group_refresh.php:21
#: views/admin/tables/ads/adsense-fallback.php:77
msgid "Activate now"
msgstr "تفعيل الآن"

#: modules/geo/classes/admin.php:219
#: modules/geo/classes/public.php:208
#: modules/geo/classes/public.php:372
msgid "(unknown region)"
msgstr "(منطقة غير معروفة)"

#: modules/geo/classes/admin.php:208
#: modules/geo/classes/public.php:200
#: modules/geo/classes/public.php:368
msgid "(unknown city)"
msgstr "(مدينة غير معروفة)"

#: modules/geo/classes/api.php:142
msgid "&#197;land Islands"
msgstr "جزر آلاند"

#: modules/lazy-load/admin.class.php:23
#: modules/lazy-load/admin.class.php:64
#: views/admin/placements/bulk-edit.php:27
msgid "Lazy Loading"
msgstr "تحميل مؤجل"

#: views/setting-placement-positioning.php:29
msgid "This method also works on AMP pages and causes fewer conflicts with website optimization features. However, it can cause critical issues with a few other plugins that use a similar technique (i.e., output buffering). We recommend less technical users test it carefully."
msgstr "تعمل هذه الطريقة أيضًا على صفحات AMP وتسبب تعارض أقل مع ميزات تحسين موقع الويب. ومع ذلك ، يمكن أن يتسبب ذلك في مشاكل حرجة مع عدد قليل من المكونات الإضافية الأخرى التي تستخدم أسلوبًا مشابهًا (مثل التخزين المؤقت للإخراج). نوصي المستخدمين الأقل تقنيًا باختباره بعناية."

#: classes/advanced-ads-pro-admin.php:148
msgid "Placement positioning"
msgstr "تحديد المواقع"

#. translators: %s: Names of placement types
#: views/setting-placement-positioning.php:13
msgid "Choose when Advanced Ads will add the following placement types: %s"
msgstr "اختر متى ستضيف \"الإعلانات المتقدمة\" أنواع المواضع التالية: %s"

#: views/setting-placement-positioning.php:24
msgid "Before page load using PHP."
msgstr "قبل تحميل الصفحة باستخدام PHP."

#: views/setting-placement-positioning.php:19
msgid "After page load using JavaScript"
msgstr "بعد تحميل الصفحة باستخدام JavaScript"

#: modules/advanced-display-conditions/main.class.php:116
msgid "post content"
msgstr "محتوى المنشور"

#: modules/advanced-display-conditions/main.class.php:117
msgid "Display ads based on words and phrases within the post or page content. Dynamically added text might not be considered."
msgstr "عرض الإعلانات بناءً على الكلمات والعبارات داخل المنشور أو محتوى الصفحة. قد لا يتم اعتبار النص المضاف ديناميكيًا."

#: modules/gamipress/views/visitor-condition-rank.php:28
msgid "Choose rank"
msgstr "اختر الرتبة"

#: modules/gamipress/views/visitor-condition-points.php:17
msgid "Choose Points Type"
msgstr "اختر نوع النقاط"

#: modules/gamipress/views/visitor-condition-achievement.php:21
msgid "Choose Achievement Type"
msgstr "اختر نوع الإنجاز"

#: modules/gamipress/admin.class.php:34
msgid "Display ads based on GamiPress user achievements."
msgstr "عرض الإعلانات على حسب إنجازات مستخدم GamiPress"

#: modules/gamipress/admin.class.php:33
msgid "GamiPress Achievement"
msgstr "إنجاز GamiPress"

#: modules/gamipress/admin.class.php:28
msgid "Display ads based on GamiPress user ranks."
msgstr "عرض الإعلانات على حسب رتبة مستخدم GamiPress"

#: modules/gamipress/admin.class.php:27
msgid "GamiPress Rank"
msgstr "رتبة GamiPress"

#: modules/gamipress/admin.class.php:22
msgid "Display ads based on GamiPress user points."
msgstr "عرض الإعلانات على أساس نقاط مستخدم GamiPress"

#: modules/gamipress/admin.class.php:21
msgid "GamiPress Points"
msgstr "نقاط GamiPress"

#: views/privacy-policy-content.php:10
msgid "You can use the text below as a template for your own privacy policy."
msgstr "يمكنك استخدام النص أدناه كنموذج لسياسة الخصوصية الخاصة بك."

#: views/privacy-policy-content.php:13
msgid "This website uses Advanced Ads Pro to place advertisements. The WordPress plugin may use multiple first-party cookies to ensure the correct integration of ads. These cookies store technical information but not IP addresses. Their use is linked to specific features and options when embedding ads."
msgstr "يستخدم هذا الموقع برنامج Advanced Ads Pro لوضع الإعلانات. قد يستخدم المكون الإضافي لـ WordPress ملفات تعريف ارتباط متعددة للطرف الأول لضمان التكامل الصحيح للإعلانات. تخزن ملفات تعريف الارتباط هذه المعلومات الفنية ولكن ليس عناوين IP. يرتبط استخدامها بميزات وخيارات محددة عند تضمين الإعلانات."

#: views/privacy-policy-content.php:12
msgid "Suggested Text:"
msgstr "النص المقترح:"

#. translators: %1$s is an opening a tag, %2$s is the corresponding closing one
#: views/privacy-policy-content.php:19
msgid "Please, see the %1$sAdvanced Ads cookie information%2$s for more details."
msgstr "من فضلك ، راجع  %2$sمعلومات ملف تعريف ارتباط الإعلانات المتقدمة%1$s لمزيد من التفاصيل."

#: views/privacy-policy-content.php:7
msgid "Depending on the setup, Advanced Ads Pro and other add-ons use cookies to control which user sees which ad. They also help to reduce expensive server requests."
msgstr "اعتمادًا على الإعداد ، تستخدم Advanced Ads Pro والوظائف الإضافية الأخرى ملفات تعريف الارتباط للتحكم في تحديد المستخدم الذي يرى الإعلان. كما أنها تساعد في تقليل طلبات الخادم باهظة الثمن."

#: classes/advanced-ads-pro-admin.php:531
msgid "Select a Sidebar placement to enable cache-busting."
msgstr "حدد موضع الشريط الجانبي لتمكين كسر ذاكرة التخزين المؤقت."

#: modules/buddypress/views/position-option.php:56
msgid "member timeline"
msgstr "الجدول الزمني للعضو"

#: classes/advanced-ads-pro-admin.php:533
msgid "Learn more"
msgstr "لمعرفة المزيد"

#: modules/buddypress/views/position-option.php:55
msgid "group feed"
msgstr "تغذية المجموعة"

#: modules/buddypress/class-buddypress.php:334
msgid "BuddyBoss group"
msgstr "مجموعة BuddyBoss"

#: modules/buddypress/views/position-option.php:53
msgid "any"
msgstr "أي"

#: modules/buddypress/views/position-option.php:54
msgid "activity stream"
msgstr "تيار النشاط"

#: modules/cache-busting/views/settings.php:38
msgid "Force passive cache busting"
msgstr "فرض التفريغ السلبي لذاكرة التخزين المؤقت"

#: modules/cache-busting/views/settings.php:61
msgid "Fallback option"
msgstr "الخيار الاحتياطي"

#: modules/cache-busting/views/settings.php:17
msgid "Default option"
msgstr "الخيار الافتراضي"

#: modules/buddypress/class-buddypress.php:131
msgid "Display ads based on BuddyBoss profile fields."
msgstr "عرض الإعلانات على أساس حقول الملف الشخصي BuddyBoss"

#: modules/buddypress/class-buddypress.php:130
msgid "BuddyBoss profile field"
msgstr "حقل ملف تعريف BuddyBoss"

#: modules/buddypress/views/position-option.php:51
msgid "Stream"
msgstr "Stream"

#: includes/placements/types/class-buddypress.php:61
msgid "Display ads on BuddyBoss related pages."
msgstr "عرض الإعلانات على صفحات ذات صلة BuddyBoss."

#: includes/placements/types/class-buddypress.php:49
msgid "BuddyBoss Content"
msgstr "محتوى BuddyBoss"

#: modules/cache-busting/views/settings.php:42
msgid "By default, cache busting only works through placements."
msgstr "بشكل افتراضي ، لا تعمل عملية تفادي التخزين المؤقت المؤقت إلا من خلال المواضع."

#: modules/cache-busting/views/settings.php:90
msgid "Update visitor profile"
msgstr "تحديث ملف الزائر"

#: modules/ad-server/views/placement-usage.php:14
msgid "Direct URL"
msgstr "URL مباشر"

#: modules/ad-server/views/placement-settings.php:11
msgid "The name of the placement that appears in the URL and injection code."
msgstr "اسم الموضع الذي يظهر في عنوان URL وشفرة الإدخال."

#: modules/ad-server/views/placement-settings.php:10
msgid "Save the page to update the usage code below."
msgstr "احفظ الصفحة لتحديث رمز الاستخدام أدناه."

#: modules/ad-server/views/module-settings.php:30
msgid "Prevent direct access to the placement URL."
msgstr "منع الوصول المباشر إلى عنوان URL للموضع."

#: modules/ad-server/views/module-settings.php:24
msgid "Please don’t enter subdirectories."
msgstr "من فضلك لا تدخل المجلدات الفرعية."

#: modules/ad-server/views/module-settings.php:18
msgid "Separate multiple values with a comma."
msgstr "افصل بين القيم المتعددة بفاصلة."

#: modules/ad-server/views/module-settings.php:18
msgid "Top level domains on which the ads will be loaded."
msgstr "نطاقات المستوى الأعلى التي سيتم تحميل الإعلانات عليها."

#: modules/ad-server/views/module-settings.php:13
#: modules/ads-for-adblockers/views/settings.php:18
#: modules/advanced-visitor-conditions/views/settings.php:6
#: modules/cache-busting/views/settings.php:12
#: modules/click-fraud-protection/views/settings.php:10
#: modules/lazy-load/views/settings.php:8
#: modules/rest-api/views/module-enable.php:12
msgid "Activate module."
msgstr "تنشيط الوحدة."

#: modules/ad-server/admin.class.php:92
msgid "Usage"
msgstr "إستعمال"

#: modules/ad-server/admin.class.php:78
msgid "Public string"
msgstr "سلسلة عامة"

#: includes/placements/types/class-server.php:56
msgid "Display ads on external websites."
msgstr "عرض الإعلانات على مواقع الويب الخارجية."

#: includes/placements/types/class-server.php:47
#: modules/ad-server/admin.class.php:37
msgid "Ad Server"
msgstr "خادم الإعلانات"

#: modules/advanced-visitor-conditions/main.class.php:149
msgid " Display ads based on the user’s capabilities."
msgstr "عرض الإعلانات على أساس قدرات المستخدم."

#: modules/advanced-visitor-conditions/main.class.php:141
msgid "Display ads based on the user agent."
msgstr "عرض الإعلانات على أساس وكيل المستخدم."

#: modules/inject-content/admin.class.php:255
msgid "Words between ads"
msgstr "كلمات بين الإعلانات"

#: views/setting_words_between_ads.php:6
msgid "Words are counted within paragraphs, headlines and any other element."
msgstr "يتم عد الكلمات داخل الفقرات والعناوين وأي عنصر آخر."

#: views/setting_words_between_ads.php:5
msgid "A minimum amount of words between automatically injected ads."
msgstr "حد أدنى من الكلمات بين الإعلانات التي يتم حقنها تلقائيًا."

#: includes/admin/class-duplicate-placement.php:64
#: includes/admin/class-group-duplication.php:115
#: modules/duplicate-ads/admin.class.php:138
msgctxt "noun"
msgid "copy"
msgstr "نسخ"

#: modules/inject-content/admin.class.php:124
msgid "Minimum length of content (in words) before automatically injected ads are allowed in them. Set to zero or leave empty to always display ads, regardless of how long the content is."
msgstr "الحد الأدنى لطول المحتوى (بالكلمات) قبل السماح بالإعلانات المحقونة تلقائيًا فيها. عيِّن على صفر أو اتركه فارغًا لعرض الإعلانات دائمًا ، بصرف النظر عن طول المحتوى."

#: includes/admin/class-duplicate-placement.php:102
#: includes/admin/class-group-duplication.php:78
#: modules/duplicate-ads/admin.class.php:88
msgid "Duplicate"
msgstr "ينسخ"

#: modules/duplicate-ads/admin.class.php:88
msgid "Create a copy of this ad"
msgstr "قم بإنشاء نسخة من هذا الإعلان"

#: modules/inject-content/views/setting_post_meta_box.php:4
msgid "Disable automatic ad injection into the content"
msgstr "تعطيل الحقن التلقائي للإعلان في المحتوى"

#: modules/cache-busting/views/settings.php:102
msgid "Update visitor profile when user logs in or out"
msgstr "تحديث ملف تعريف الزائر عندما يقوم المستخدم بتسجيل الدخول أو الخروج"

#: modules/cache-busting/views/settings.php:92
msgid "You might need to update the page cache if you are using one."
msgstr "قد تحتاج إلى تحديث ذاكرة التخزين المؤقت للصفحة إذا كنت تستخدم واحدة."

#: modules/cache-busting/views/settings.php:86
msgid "Advanced Ads stores some user information in the user’s browser to limit the number of AJAX requests for cache busting. Manual"
msgstr "تخزن الإعلانات المتقدمة بعض معلومات المستخدم في متصفح المستخدم للحد من عدد طلبات AJAX لتفادي التخزين المؤقت."

#: modules/cache-busting/views/settings.php:83
msgid "Visitor profile"
msgstr "ملف تعريف الزائر"

#. translators: %s: URL
#: modules/advanced-display-conditions/main.class.php:41
msgid "Display ads based on the current URL parameters (everything following %s), except values following #."
msgstr "عرض الإعلانات استنادًا إلى معلمات عنوان URL الحالية (كل ما يلي %s) ، باستثناء القيم التالية #."

#: classes/advanced-ads-pro-admin.php:156
msgid "Disable ads for post types"
msgstr "تعطيل الإعلانات لأنواع المنشورات"

#: modules/cache-busting/views/settings.php:94
msgid "An error occurred"
msgstr "حدث خطأ"

#: modules/cache-busting/views/settings.php:91
msgid "Updated"
msgstr "محدث"

#. translators: %s is a placeholder for the URL.
#: modules/advanced-visitor-conditions/main.class.php:163
msgid "Display ads based on the user's roles. See <a href=\"%s\" target=\"_blank\">List of roles in WordPress</a>."
msgstr "عرض الإعلانات على أساس أدوار المستخدم. راجع <a href=\"%s\" target=\"_blank\"> قائمة الأدوار في WordPress </a>."

#: modules/advanced-visitor-conditions/main.class.php:160
msgid "user role"
msgstr "دور المستخدم"

#. translators: %s is a list of supported optimizer plugins
#: views/setting_autoptimize.php:7
msgid "Advanced Ads Pro disables optimizers ( %s ) for displaying ads per default. Enable this option to allow optimizers to change the ad code. Especially JavaScript ads might stop working then."
msgstr "يقوم Advanced Ads Pro بتعطيل المُحسِنين (%s) لعرض الإعلانات بشكل افتراضي. قم بتمكين هذا الخيار للسماح للمحسّنين بتغيير رمز الإعلان. قد تتوقف إعلانات جافا سكريبت بشكل خاص عن العمل بعد ذلك."

#: classes/advanced-ads-pro-admin.php:139
msgid "Allow optimizers to modify ad codes"
msgstr "السماح للمحسّنين بتعديل شفرات الإعلانات"

#. translators: %s: input field
#: modules/lazy-load/views/settings.php:34
msgid "Start loading the ads %s pixels before they are visible on the screen."
msgstr "ابدأ في تحميل الإعلانات %s بكسل قبل ظهورها على الشاشة."

#: modules/grids/admin.class.php:61
msgid "Set to 0 to disable this feature."
msgstr "اضبط على 0 لتعطيل هذه الميزة."

#: modules/grids/admin.class.php:60
msgid "On browsers smaller than this, the columns show in full width below each other."
msgstr "في المتصفحات الأصغر من ذلك ، تظهر الأعمدة بعرض كامل أسفل بعضها البعض."

#: modules/grids/admin.class.php:58
msgid "Full width"
msgstr "العرض الكامل"

#. translators: "profile fields" relates to BuddyPress profile fields
#: modules/buddypress/views/xprofile-condition.php:58
msgid "No profile fields found"
msgstr "لم يتم العثور على حقول الملف الشخصي"

#: modules/buddypress/class-buddypress.php:118
msgid "Display ads based on BuddyPress profile fields."
msgstr "عرض الإعلانات على أساس حقول الملف الشخصي BuddyPress"

#: modules/buddypress/class-buddypress.php:117
msgid "BuddyPress profile field"
msgstr "حقل ملف تعريف BuddyPress"

#: modules/buddypress/views/position-option.php:44
#: views/setting_repeat.php:4
msgid "repeat the position"
msgstr "تكرار الموضع"

#: views/setting_custom_code.php:34
msgid "Displayed after the ad content"
msgstr "تُعرض بعد محتوى الإعلان"

#: views/setting_custom_code.php:30
msgid "place your own code below the ad"
msgstr "ضع الشفرة الخاصة بك أسفل الإعلان"

#: views/setting_custom_code.php:27
msgid "custom code"
msgstr "التعليمات البرمجية المخصصة"

#. Author URI of the plugin
#: advanced-ads-pro.php
msgid "https://wpadvancedads.com"
msgstr "https://wpadvancedads.com"

#. Plugin URI of the plugin
#: advanced-ads-pro.php
msgid "https://wpadvancedads.com/add-ons/advanced-ads-pro/"
msgstr "https://wpadvancedads.com/add-ons/advanced-ads-pro/"

#. Description of the plugin
#: advanced-ads-pro.php
msgid "Advanced features to boost your ad revenue."
msgstr "ميزات متقدمة لزيادة عائدات إعلانك."

#. Plugin Name of the plugin
#: advanced-ads-pro.php
msgid "Advanced Ads Pro"
msgstr "Advanced Ads Pro"

#: modules/weekdays/views/ad-submitbox-meta.php:19
msgid "Set specific days"
msgstr "اختر أيامًا محددة"

#. translators: 1: month, 2: day, 3: year, 4: hour, 5: minute
#: modules/placement-tests/views/settings_test_expiry_date.php:31
msgctxt "order of expiry date fields 1: month, 2: day, 3: year, 4: hour, 5: minute"
msgid "%1$s %2$s, %3$s @ %4$s %5$s"
msgstr "%1$s %2$s, %3$s @ %4$s %5$s"

#: modules/placement-tests/views/settings_test_expiry_date.php:25
msgid "Minute"
msgstr "دقيقة"

#: modules/placement-tests/views/settings_test_expiry_date.php:24
msgid "Hour"
msgstr "ساعة"

#: modules/placement-tests/views/settings_test_expiry_date.php:23
msgid "Year"
msgstr "سنة"

#: modules/placement-tests/views/settings_test_expiry_date.php:22
msgid "Day"
msgstr "يوم"

#. translators: 1: month number (01, 02, etc.), 2: month abbreviation
#: modules/placement-tests/views/settings_test_expiry_date.php:17
msgctxt "1: month number (01, 02, etc.), 2: month abbreviation"
msgid "%1$s-%2$s"
msgstr "%1$s-%2$s"

#: modules/placement-tests/views/settings_test_expiry_date.php:12
msgid "Month"
msgstr "شهر"

#: modules/placement-tests/views/settings_test_expiry_date.php:5
msgctxt "placement tests"
msgid "Send email after"
msgstr "أرسل بريدًا إلكترونيًا بعد"

#: modules/placement-tests/placement-tests.class.php:237
#: modules/placement-tests/views/setting_placement_test_weight.php:17
msgid "Test weight"
msgstr "اختبار الوزن"

#: modules/placement-tests/views/setting_placement_test_weight.php:15
msgctxt "placement tests"
msgid "Testing"
msgstr "اختبار"

#: modules/placement-tests/views/placement-tests.php:66
msgid "Save Tests"
msgstr "حفظ الاختبارات"

#: modules/placement-tests/views/placement-tests.php:59
msgctxt "checkbox to remove placement test"
msgid "delete"
msgstr "حذف"

#: modules/placement-tests/views/placement-tests.php:53
msgctxt "placement tests"
msgid "empty"
msgstr "فارغ"

#: modules/placement-tests/views/placement-tests.php:18
msgid "Placements"
msgstr " المواضع"

#: modules/placement-tests/views/placement-tests.php:17
msgid "Expiry date"
msgstr "تاريخ انتهاء الصلاحية"

#: modules/placement-tests/views/placement-tests.php:16
msgid "Author"
msgstr "مؤلف"

#: modules/placement-tests/views/placement-tests.php:11
msgid "Placement tests"
msgstr "اختبارات الموضع"

#: modules/lazy-load/views/setting_lazy_load.php:6
#: modules/lazy-load/views/setting_lazy_load.php:8
msgid "disabled"
msgstr "معطل"

#: modules/lazy-load/views/setting_lazy_load.php:2
#: modules/lazy-load/views/setting_lazy_load.php:4
msgid "enabled"
msgstr "ممكّن"

#: modules/group-refresh/views/settings_group_refresh.php:27
msgid "Please use a placement to deliver this group using cache-busting."
msgstr "الرجاء استخدام موضع لتسليم هذه المجموعة باستخدام تفريغ ذاكرة التخزين المؤقت."

#: modules/group-refresh/views/settings_group_refresh.php:19
msgid "Refresh ads on the same spot. Works when cache-busting is used."
msgstr "تحديث الإعلانات في نفس المكان. يعمل عند استخدام ذاكرة التخزين المؤقت."

#: modules/group-refresh/views/settings_group_refresh.php:16
msgid "milliseconds"
msgstr "مللي ثانية"

#: modules/group-refresh/views/settings_group_refresh.php:14
#: views/admin/placements/bulk-edit.php:30
#: views/admin/placements/bulk-edit.php:40
#: views/admin/tables/ads/column-displayonce.php:10
msgid "Enabled"
msgstr "ممكّن"

#: modules/grids/views/grid-option-size.php:20
msgid "rows"
msgstr "صفوف"

#: modules/grids/views/grid-option-size.php:16
msgid "columns"
msgstr "الأعمدة"

#: modules/click-fraud-protection/views/settings.php:23
msgid "Period for which to hide the ad ( in days )"
msgstr "الفترة التي يتم فيها إخفاء الإعلان (بالأيام)"

#: modules/click-fraud-protection/views/settings.php:20
msgid "Period in which the click limit should be reached ( in hours )"
msgstr "الفترة التي يجب خلالها الوصول إلى حد النقرات (بالساعات)"

#: modules/click-fraud-protection/views/settings.php:17
msgid "Allowed clicks on a single ad before it is removed"
msgstr "النقرات المسموح بها على إعلان واحد قبل إزالته"

#. translators: Link to parallax manual
#: modules/ad-server/views/module-settings.php:15
#: modules/ads-for-adblockers/views/settings.php:20
#: modules/advanced-display-conditions/main.class.php:508
#: modules/advanced-display-conditions/views/metabox-string.php:12
#: modules/advanced-visitor-conditions/main.class.php:309
#: modules/advanced-visitor-conditions/main.class.php:408
#: modules/advanced-visitor-conditions/main.class.php:465
#: modules/advanced-visitor-conditions/main.class.php:503
#: modules/buddypress/class-buddypress.php:378
#: modules/buddypress/views/xprofile-condition.php:74
#: modules/cache-busting/cache-busting.class.php:263
#: modules/cache-busting/views/settings.php:14
#: modules/click-fraud-protection/click-fraud-protection.class.php:150
#: modules/click-fraud-protection/click-fraud-protection.class.php:158
#: modules/click-fraud-protection/views/settings.php:12
#: modules/gamipress/views/visitor-condition-achievement.php:34
#: modules/gamipress/views/visitor-condition-points.php:41
#: modules/gamipress/views/visitor-condition-rank.php:41
#: modules/geo/admin/views/setting-maxmind-license-key.php:22
#: modules/geo/classes/admin.php:400
#: modules/group-refresh/views/settings_group_refresh.php:23
#: modules/lazy-load/views/settings.php:10
#: modules/paid-memberships-pro/main.class.php:140
#: modules/paid-memberships-pro/main.class.php:259
#: modules/parallax-ads/views/placement-options-after.php:24
#: modules/responsive-ads/views/setting_fallback_width.php:16
#: modules/rest-api/views/module-enable.php:15
msgid "Manual"
msgstr "الدليل"

#. translators: %s link to manual
#: modules/cache-busting/views/settings_check_ad.php:16
msgid "The code of this ad might not work properly with activated cache-busting. <a href=\"%s\" target=\"_blank\">Manual</a>"
msgstr "قد لا تعمل شفرة هذا الإعلان بشكل صحيح مع تنشيط ذاكرة التخزين المؤقت. <a href=\"%s\" target=\"_blank\">الدليل</a>"

#: modules/cache-busting/views/settings.php:44
msgid "Enable passive cache busting for all ads and groups which are not delivered through a placement, if possible."
msgstr "قم بتمكين الخرق السلبي لذاكرة التخزين المؤقت لجميع الإعلانات والمجموعات التي لا يتم تسليمها من خلال أحد المواضع ، إن أمكن."

#: modules/cache-busting/views/settings.php:62
msgid "Choose the fallback if “passive“ cache busting is not possible."
msgstr "اختر الخيار الاحتياطي إذا لم يكن التحكُّم \"السلبي\" في ذاكرة التخزين المؤقت ممكنًا."

#: modules/cache-busting/admin-ui.class.php:87
#: modules/cache-busting/views/settings.php:31
#: modules/cache-busting/views/settings.php:68
#: views/admin/placements/bulk-edit.php:20
msgid "AJAX"
msgstr "AJAX"

#: modules/advanced-display-conditions/views/referrer-url-metabox.php:15
#: modules/advanced-visitor-conditions/views/referrer-url-metabox.php:15
msgid "url of the referrer"
msgstr "رابط المحيل"

#: modules/advanced-display-conditions/views/referrer-url-metabox.php:5
#: modules/advanced-visitor-conditions/views/referrer-url-metabox.php:5
msgid "URL"
msgstr "URL"

#: modules/advanced-display-conditions/views/referrer-url-metabox.php:3
#: modules/advanced-visitor-conditions/views/referrer-url-metabox.php:3
msgid "Display ad depending on the external url the user comes from."
msgstr "عرض الإعلان اعتمادًا على عنوان url الخارجي الذي يأتي منه المستخدم."

#: modules/advanced-display-conditions/views/referrer-url-metabox.php:1
#: modules/advanced-visitor-conditions/views/referrer-url-metabox.php:1
msgid "Display by referrer url"
msgstr "عرض حسب عنوان url المحيل"

#: modules/ads-for-adblockers/views/placement-item.php:46
#: modules/lazy-load/views/setting_lazy_load.php:11
msgid "Works only with cache-busting enabled"
msgstr "يعمل فقط مع تمكين تفريغ ذاكرة التخزين المؤقت"

#: modules/weekdays/class-advanced-ads-pro-weekdays.php:171
msgid "Never shows up"
msgstr "لا يظهر ابدا"

#: modules/weekdays/class-advanced-ads-pro-weekdays.php:90
#: modules/weekdays/class-advanced-ads-pro-weekdays.php:107
msgid "specific days"
msgstr "أيام محددة"

#. translators: %s: placement test name
#: modules/placement-tests/placement-tests.class.php:702
msgid "Placement test <em>%s</em> created"
msgstr "تم إنشاء اختبار الموضع <em>%s</em>"

#: modules/placement-tests/placement-tests.class.php:564
msgctxt "placement tests"
msgid "Placement page"
msgstr "صفحة الموضع"

#: modules/placement-tests/placement-tests.class.php:558
#: modules/placement-tests/placement-tests.class.php:700
#: modules/placement-tests/views/placement-tests.php:50
msgctxt "placement tests"
msgid " vs "
msgstr "ضد"

#: modules/placement-tests/placement-tests.class.php:530
msgctxt "placement tests"
msgid "Expired placement tests"
msgstr "اختبارات الموضع منتهية الصلاحية"

#: modules/placement-tests/views/setting_placement_test_weight.php:28
msgid "Save new test"
msgstr "حفظ الاختبار الجديد"

#: modules/paid-memberships-pro/main.class.php:157
msgid "Display ads only to users with a specific membership level set with PaidMembershipsPro."
msgstr "عرض الإعلانات فقط للمستخدمين الذين لديهم مستوى عضوية محدد تم تعيينه باستخدام PaidMembershipsPro."

#: modules/paid-memberships-pro/main.class.php:156
msgid "PMP user level"
msgstr "مستوى المستخدم PMP"

#: modules/paid-memberships-pro/main.class.php:133
#: modules/paid-memberships-pro/main.class.php:252
msgid "No membership levels set up yet."
msgstr "لم يتم إعداد مستويات العضوية حتى الآن."

#: modules/paid-memberships-pro/main.class.php:39
msgid "Display ads only on pages that require a specific membership level set of PaidMembershipsPro."
msgstr "عرض الإعلانات فقط على الصفحات التي تحتاج إلى مستوى عضوية محدد تم تعيينه مع PaidMembershipsPro."

#: modules/paid-memberships-pro/main.class.php:38
msgid "PMP page level"
msgstr "مستوى الصفحة PMP"

#: includes/placements/types/class-archive-pages.php:56
msgid "Display the ad between posts on post lists, e.g. home, archives, search etc."
msgstr "عرض الإعلان بين المشاركات في قوائم المشاركات ، على سبيل المثال الصفحة الرئيسية والمحفوظات والبحث وما إلى ذلك."

#: includes/placements/types/class-archive-pages.php:47
msgid "Post Lists"
msgstr "قائمة المقالات"

#: includes/placements/types/class-custom-position.php:57
msgid "Attach the ad to any element in the frontend."
msgstr "إرفاق الإعلان بأي عنصر في الواجهة الأمامية."

#: includes/placements/types/class-custom-position.php:48
msgid "Custom Position"
msgstr "موقف مخصص"

#: includes/placements/types/class-content-middle.php:56
msgid "In the middle of the main content based on the number of paragraphs."
msgstr "في منتصف المحتوى الرئيسي بناءً على عدد الفقرات."

#: includes/placements/types/class-content-middle.php:47
msgid "Content Middle"
msgstr "وسط المحتوى"

#: includes/placements/types/class-above-headline.php:57
msgid "Above the main headline on the page (&lt;h1&gt;)."
msgstr "فوق العنوان الرئيسي على الصفحة (&lt;h1&gt;)."

#: includes/placements/types/class-above-headline.php:48
msgid "Above Headline"
msgstr "فوق العنوان"

#: includes/placements/types/class-content-random.php:56
msgid "After a random paragraph in the main content."
msgstr "بعد فقرة عشوائية في المحتوى الرئيسي."

#: includes/placements/types/class-content-random.php:47
msgid "Random Paragraph"
msgstr "فقرة عشوائية"

#: modules/inject-content/admin.class.php:155
msgid "secondary loops"
msgstr "حلقات ثانوية"

#: modules/inject-content/admin.class.php:151
msgid "Only enable this option if you are sure what you are doing!"
msgstr "قم بتمكين هذا الخيار فقط إذا كنت متأكدًا مما تفعله!"

#: modules/inject-content/admin.class.php:150
msgid "Allow injection into any custom and secondary queries."
msgstr "السماح بإدخال أي استفسارات مخصصة وثانوية."

#: modules/inject-content/admin.class.php:128
msgid "minimum content length"
msgstr "الحد الأدنى لطول المحتوى"

#: modules/inject-content/admin.class.php:93
msgid "Before which post to inject the ad on post lists."
msgstr "قبل أي وظيفة لإدخال الإعلان في قوائم المشاركات."

#. translators: %s: index of the post
#: modules/inject-content/admin.class.php:91
msgid "Inject before %s. post"
msgstr "احقن قبل %s. مقال"

#: modules/inject-content/admin.class.php:69
msgid "Place the following element where the ad should be displayed."
msgstr "ضع العنصر التالي حيث يجب عرض الإعلان."

#: modules/inject-content/admin.class.php:68
msgid "by new element"
msgstr "بواسطة عنصر جديد"

#: modules/bbpress/class-admin.php:64
#: modules/bbpress/class-admin.php:87
#: modules/bbpress/class-admin.php:130
#: modules/inject-content/admin.class.php:58
msgid "Position"
msgstr "موضع"

#: modules/inject-content/admin.class.php:57
msgid "Uses <a href=\"https://api.jquery.com/category/selectors/\" target=\"_blank\">jQuery selectors</a>, e.g. #container_id, .container_class"
msgstr ""
"يستخدم <a href=\"https://api.jquery.com/category/selectors/\" target=\"_blank\">محددات jQuery</a> مثال: #container_id, .container_class\n"
" "

#: modules/inject-content/admin.class.php:54
msgid "or enter manually"
msgstr "أو أدخل يدويًا"

#: modules/inject-content/admin.class.php:56
msgid "select position"
msgstr "تحديد الموضع"

#: modules/inject-content/admin.class.php:55
msgctxt "frontend picker"
msgid "stop selection"
msgstr "وقف الاختيار"

#: modules/inject-content/admin.class.php:51
msgid "Place ads in relation to an existing element in the frontend."
msgstr "ضع الإعلانات فيما يتعلق بعنصر موجود في الواجهة الأمامية."

#: modules/inject-content/admin.class.php:49
msgid "by existing element"
msgstr "بواسطة العنصر الموجود"

#: modules/inject-content/admin.class.php:40
msgid "below"
msgstr "أسفل"

#: modules/inject-content/admin.class.php:39
msgid "inside, after other content"
msgstr "في الداخل ، بعد المحتويات الأخرى"

#: modules/inject-content/admin.class.php:38
msgid "inside, before other content"
msgstr "في الداخل قبل المحتويات الأخرى"

#: modules/inject-content/admin.class.php:37
msgid "above"
msgstr "فوق"

#: modules/group-refresh/admin.class.php:60
msgid "Refresh interval"
msgstr "الفاصل الزمني للتحديث"

#: modules/grids/admin.class.php:73
msgid "Random order"
msgstr "ترتيب عشوائي"

#: modules/grids/admin.class.php:53
msgid "Minimum width of a column in the grid."
msgstr "الحد الأدنى لعرض العمود في الشبكة."

#: modules/grids/admin.class.php:51
msgid "Min. width"
msgstr "أدنى عرض"

#: modules/grids/admin.class.php:45
msgid "Inner margin"
msgstr "الهامش الداخلي"

#: modules/grids/admin.class.php:36
msgid "Size"
msgstr "مقاس"

#: modules/click-fraud-protection/click-fraud-protection.class.php:320
msgid "Click Fraud Protection enabled"
msgstr "انقر فوق تمكين الحماية من الاحتيال"

#: modules/click-fraud-protection/click-fraud-protection.class.php:145
msgid "hours"
msgstr "ساعات"

#: modules/click-fraud-protection/click-fraud-protection.class.php:143
msgid "within"
msgstr "خلال"

#: modules/click-fraud-protection/click-fraud-protection.class.php:111
msgid "Display ad only if a click limit has not been reached."
msgstr "عرض الإعلان فقط إذا لم يتم الوصول إلى حد النقرات"

#: modules/click-fraud-protection/click-fraud-protection.class.php:110
msgid "max. ad clicks"
msgstr "الحد الأقصى لعدد نقرات الإعلان"

#: modules/click-fraud-protection/admin.class.php:29
msgid "Click Fraud Protection"
msgstr "الحماية من الاحتيال"

#: modules/cache-busting/cache-busting.class.php:1913
msgctxt "placement admin label"
msgid "Cache-busting:"
msgstr "تفريغ ذاكرة التخزين المؤقت:"

#: modules/cache-busting/cache-busting.class.php:1907
msgid "The ad is not displayed on the page"
msgstr "لا يتم عرض الإعلان على الصفحة"

#: modules/cache-busting/cache-busting.class.php:1906
msgid "The ad is displayed on the page"
msgstr "يتم عرض الإعلان على الصفحة"

#: modules/cache-busting/cache-busting.class.php:1903
#: modules/cache-busting/views/settings.php:24
msgid "passive"
msgstr "سلبي"

#: modules/cache-busting/cache-busting.class.php:1897
msgid "The ad can work with passive cache-busting"
msgstr "يمكن أن يعمل الإعلان مع التفريغ السلبي لذاكرة التخزين المؤقت"

#: modules/cache-busting/cache-busting.class.php:1895
msgid "The ad can not work with passive cache-busting"
msgstr "لا يمكن أن يعمل الإعلان مع التفريغ السلبي لذاكرة التخزين المؤقت"

#: modules/ads-for-adblockers/views/settings.php:27
#: modules/cache-busting/admin.class.php:29
#: views/admin/placements/bulk-edit.php:16
msgid "Cache Busting"
msgstr "تفادي التخزين المؤقت"

#: modules/cache-busting/admin-ui.class.php:150
msgid "The <em>Ad Group</em> ad type can only use AJAX or no cache-busting, but not passive cache-busting."
msgstr "يمكن لنوع إعلان <em> المجموعة الإعلانية </em> فقط استخدام  AJAX أو عدم استخدام تفريغ ذاكرة التخزين المؤقت ، ولكن لا يمكنه استخدام التفريغ السلبي لذاكرة التخزين المؤقت."

#: modules/cache-busting/admin-ui.class.php:107
msgctxt "placement admin label"
msgid "Cache-busting"
msgstr "تفريغ ذاكرة التخزين المؤقت:"

#: modules/cache-busting/cache-busting.class.php:1910
msgctxt "setting label"
msgid "off"
msgstr "إيقاف"

#: modules/cache-busting/cache-busting.class.php:1901
msgctxt "setting label"
msgid "ajax"
msgstr "ajax"

#: modules/buddypress/class-admin.php:82
msgid "Member List"
msgstr "قائمة الأعضاء"

#: modules/buddypress/class-admin.php:79
msgid "Group List"
msgstr "قائمة المجموعة"

#: modules/buddypress/class-admin.php:63
#: modules/buddypress/class-admin.php:71
msgid "Activity Entry"
msgstr "دخول النشاط"

#. translators: %s is an HTML input element.
#: modules/buddypress/views/position-option.php:37
msgid "Inject after %s. entry"
msgstr "احقن بعد %s. مدخل"

#: includes/placements/types/class-buddypress.php:63
msgid "Display ads on BuddyPress related pages."
msgstr "عرض الإعلانات على صفحات ذات صلة BuddyPress."

#: includes/placements/types/class-buddypress.php:51
msgid "BuddyPress Content"
msgstr "محتوى BuddyPress"

#: modules/bbpress/class-admin.php:150
msgid "forums page"
msgstr "صفحة المنتديات"

#: modules/bbpress/class-admin.php:146
msgid "single forum page"
msgstr "صفحة منتدى واحدة"

#: modules/bbpress/class-admin.php:142
#: modules/bbpress/class-admin.php:164
msgid "forum topic page"
msgstr "صفحة موضوع المنتدى"

#: modules/buddypress/views/position-option.php:20
#: modules/inject-content/admin.class.php:80
#: modules/inject-content/admin.class.php:96
msgid "position"
msgstr "موضع"

#: includes/placements/types/class-bbpress-comment.php:56
msgid "Display ads in bbPress replies."
msgstr "عرض الإعلانات في ردود bbPress."

#: includes/placements/types/class-bbpress-comment.php:47
msgid "bbPress Reply Content"
msgstr "bbPress رد المحتوى"

#: includes/placements/types/class-bbpress-static.php:56
msgid "Display ads on bbPress related pages."
msgstr "عرض الإعلانات على صفحات bbPress ذات الصلة."

#: includes/placements/types/class-bbpress-static.php:47
msgid "bbPress Static Content"
msgstr "المحتوى الثابت bbPress"

#: modules/background-ads/admin.class.php:33
msgid "background"
msgstr "خلفية"

#: modules/background-ads/admin.class.php:30
msgid "Select a background color in case the background image is not high enough to cover the whole screen."
msgstr "حدد لون الخلفية في حالة عدم ارتفاع صورة الخلفية بدرجة كافية لتغطية الشاشة بأكملها."

#: includes/placements/types/class-background-ad.php:56
msgid "Background of the website behind the main wrapper."
msgstr "خلفية الموقع خلف الغلاف الرئيسي."

#: includes/placements/types/class-background-ad.php:47
msgid "Background Ad"
msgstr "خلفية الإعلان"

#. translators: %s is a placeholder for the impressions field
#: modules/advanced-visitor-conditions/main.class.php:499
msgid "within %s seconds"
msgstr "خلال %s ثانية"

#: modules/advanced-visitor-conditions/main.class.php:449
msgid "Cookie Value"
msgstr "قيمة ملف تعريف الارتباط"

#: modules/advanced-visitor-conditions/main.class.php:448
msgid "Cookie Name"
msgstr "اسم ملف تعريف الارتباط"

#: modules/advanced-visitor-conditions/main.class.php:301
#: modules/advanced-visitor-conditions/main.class.php:349
#: modules/advanced-visitor-conditions/main.class.php:398
msgid "-- choose one --"
msgstr "- اختر عنصرًا واحدًا--"

#: modules/advanced-visitor-conditions/main.class.php:298
msgid "can not"
msgstr "لا تستطيع"

#: modules/advanced-visitor-conditions/main.class.php:297
msgid "can"
msgstr "تمكن"

#: modules/advanced-visitor-conditions/main.class.php:202
msgid "new visitor"
msgstr "زائر جديد"

#: modules/advanced-visitor-conditions/main.class.php:196
msgid "Display the ad only for a few impressions in a given period per user."
msgstr "عرض الإعلان فقط لعدد قليل من مرات الظهور في فترة معينة لكل مستخدم."

#: modules/advanced-visitor-conditions/main.class.php:195
msgid "max. ad impressions"
msgstr "الحد الأقصى لعدد مرات ظهور الإعلان"

#: modules/advanced-visitor-conditions/main.class.php:188
msgid "Display ads based on the number of page impressions the user already made before the current one."
msgstr "عرض الإعلانات استنادًا إلى عدد مرات ظهور الصفحة التي حققها المستخدم بالفعل (قبل اليوم الحالي)."

#: modules/advanced-visitor-conditions/main.class.php:187
msgid "page impressions"
msgstr "مرات ظهور الصفحة"

#: modules/advanced-visitor-conditions/main.class.php:180
msgid "cookie"
msgstr "ملف تعريف الارتباط"

#: modules/advanced-visitor-conditions/main.class.php:174
msgid "Display ads based on the visitor's browser language."
msgstr "عرض الإعلانات بناءً على لغة متصفح الزوار."

#: modules/advanced-visitor-conditions/main.class.php:173
msgid "browser language"
msgstr "لغة المتصفح"

#: modules/advanced-visitor-conditions/main.class.php:148
msgid "user can (capabilities)"
msgstr "يمكن للمستخدم (القدرات)"

#: modules/advanced-visitor-conditions/main.class.php:140
msgid "user agent"
msgstr "وكيل المستخدم"

#: modules/advanced-visitor-conditions/main.class.php:133
msgid "Display ads based on the referrer URL."
msgstr "عرض الإعلانات على أساس عنوان url المحيل."

#: modules/advanced-visitor-conditions/main.class.php:132
msgid "referrer url"
msgstr "عنوان url المُحيل"

#: modules/advanced-visitor-conditions/admin.class.php:29
msgid "Advanced visitor conditions"
msgstr "شروط الزائر المتقدمة"

#: modules/advanced-display-conditions/main.class.php:574
msgid "count from end"
msgstr "العد من النهاية"

#: modules/advanced-display-conditions/main.class.php:540
msgid "meta key"
msgstr "مفتاح التعريف"

#: modules/advanced-display-conditions/main.class.php:539
msgid "all of"
msgstr "كل"

#: modules/advanced-display-conditions/main.class.php:538
msgid "any of"
msgstr "اي من"

#: modules/advanced-display-conditions/main.class.php:502
msgid "no languages set up in WPML"
msgstr "لا توجد لغات معدة في WPML"

#: modules/advanced-display-conditions/main.class.php:431
#: modules/advanced-display-conditions/main.class.php:486
#: modules/advanced-visitor-conditions/main.class.php:346
#: modules/advanced-visitor-conditions/main.class.php:395
#: modules/advanced-visitor-conditions/main.class.php:538
#: modules/gamipress/views/visitor-condition-achievement.php:17
#: modules/geo/classes/admin.php:308
#: modules/paid-memberships-pro/main.class.php:118
#: modules/paid-memberships-pro/main.class.php:237
msgid "is not"
msgstr "ليس"

#: modules/advanced-display-conditions/main.class.php:430
#: modules/advanced-display-conditions/main.class.php:485
#: modules/advanced-visitor-conditions/main.class.php:345
#: modules/advanced-visitor-conditions/main.class.php:394
#: modules/advanced-visitor-conditions/main.class.php:536
#: modules/gamipress/views/visitor-condition-achievement.php:16
#: modules/geo/classes/admin.php:307
#: modules/paid-memberships-pro/main.class.php:117
#: modules/paid-memberships-pro/main.class.php:236
msgid "is"
msgstr "يكون"

#: modules/advanced-display-conditions/main.class.php:110
msgid "Display ads based on the index of a split page"
msgstr "عرض الإعلانات على أساس فهرس صفحة مقسمة"

#: modules/advanced-display-conditions/main.class.php:109
msgid "pagination"
msgstr "ترقيم الصفحات"

#: modules/advanced-display-conditions/main.class.php:103
msgid "Display ads based on post meta."
msgstr "عرض الإعلانات على أساس ما بعد التعريف."

#: modules/advanced-display-conditions/main.class.php:102
msgid "post meta"
msgstr "بيانات تعريف المقال (المنشور)"

#: modules/advanced-display-conditions/main.class.php:96
msgid "Display ads based on parent page."
msgstr "عرض الإعلانات على أساس الصفحة الرئيسية."

#: modules/advanced-display-conditions/main.class.php:95
msgid "parent page"
msgstr "الصفحة الأم"

#: modules/advanced-display-conditions/main.class.php:87
msgid "WPML language"
msgstr "WPML language"

#. translators: %s: post type
#: modules/advanced-display-conditions/main.class.php:57
#: modules/advanced-display-conditions/main.class.php:76
msgid "Display ads based on the template of the %s post type."
msgstr "عرض الإعلانات بناءً على قالب من نوع المشاركة %s."

#. translators: %s: page
#: modules/advanced-display-conditions/main.class.php:55
#: modules/advanced-display-conditions/main.class.php:75
msgid "%s template"
msgstr "قالب %s"

#: modules/advanced-display-conditions/main.class.php:39
msgid "url parameters"
msgstr "معلمات URL"

#: modules/ads-for-adblockers/admin.class.php:148
msgid "The chosen ad contains a reference to an external .js file"
msgstr "يحتوي الإعلان المختار على مرجع إلى ملف .js خارجي"

#: modules/ads-for-adblockers/admin.class.php:86
msgid "Displayed to visitors with an ad blocker"
msgstr "يتم عرضها للزوار مع مانع الإعلانات"

#: modules/ads-for-adblockers/admin.class.php:94
msgid "Ad blocker item"
msgstr "عنصر مانع الإعلانات"

#: modules/ads-for-adblockers/admin.class.php:42
msgid "Ads for ad blockers"
msgstr "إعلانات لأداة منع الإعلانات"

#: modules/admin-bar/admin-bar.class.php:104
msgid "placement"
msgstr "الموضع"

#: modules/admin-bar/admin-bar.class.php:101
msgid "group"
msgstr "مجموعة"

#: modules/admin-bar/admin-bar.class.php:98
msgid "ad"
msgstr "إعلان"

#: modules/admin-bar/admin-bar.class.php:56
msgid "No Ads found"
msgstr "لا توجد إعلانات"

#: modules/admin-bar/admin-bar.class.php:48
#: modules/ads-for-adblockers/views/placement-item.php:27
#: views/admin/tables/ads/adsense-fallback.php:37
msgid "Ads"
msgstr "إعلانات"

#: views/setting_output_once.php:6
msgid "Display the ad only once per page"
msgstr "عرض الإعلان مرة واحدة فقط في كل صفحة"

#: views/setting_output_once.php:2
msgid "Display only once"
msgstr "عرض مرة واحدة فقط"

#: classes/advanced-ads-pro.php:332
msgid "Please note, the “Ad Admin“ and the “Ad Manager“ roles have the “upload_files“ and the “unfiltered_html“ capabilities"
msgstr "يرجى ملاحظة أن أدوار \"مسؤول الإعلانات\" و \"مدير الإعلانات\" تتمتعان بإمكانيات \"upload_files\" و \"uniltered_html\""

#: includes/class-bootstrap.php:225
msgid "Install Now"
msgstr "تثبيت الآن"

#: includes/class-bootstrap.php:233
msgid "Activate Now"
msgstr " التفعيل الآن"

#. translators: %s: URL to the settings page
#: classes/advanced-ads-pro-admin.php:390
msgid "Enable the Advanced Visitor Conditions <a href=\"%s\" target=\"_blank\">in the settings</a>."
msgstr "قم بتمكين شروط الزائر المتقدمة <a href=\"%s\" target=\"_blank\"> في الإعدادات </a>."

#: classes/advanced-ads-pro-admin.php:336
msgid "Please note, with the last update, the “Ad Admin“ and “Ad Manager“ roles have the “upload_files“ and the “unfiltered_html“ capabilities."
msgstr "يرجى ملاحظة أنه مع التحديث الأخير ، فإن أدوار \"مدير الإعلانات\" و \"مدير الإعلانات\" تتمتعان بإمكانيات \"تحميل_الملفات\" و \"html_ uniltered\"."

#: classes/advanced-ads-pro-admin.php:326
msgid "Ad User Role"
msgstr "دور مستخدم الإعلان"

#: classes/advanced-ads-pro-admin.php:323
msgid "Advanced Ads User Role"
msgstr "دور مستخدم الإعلانات المتقدم"

#: classes/advanced-ads-pro-admin.php:46
msgid "--no role--"
msgstr "--لا دور--"

#: classes/advanced-ads-pro-admin.php:45
#: includes/installation/class-install.php:159
msgid "Ad User"
msgstr "مستخدم إعلان"

#: classes/advanced-ads-pro-admin.php:44
#: includes/installation/class-install.php:146
msgid "Ad Manager"
msgstr "مدير الإعلانات"

#: classes/advanced-ads-pro-admin.php:43
#: includes/installation/class-install.php:132
msgid "Ad Admin"
msgstr "مشرف الإعلان"
