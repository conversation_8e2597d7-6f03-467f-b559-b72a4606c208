msgid ""
msgstr ""
"Project-Id-Version: Advanced Ads Pro\n"
"Report-Msgid-Bugs-To: \n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2025-03-25T23:54:42+00:00\n"
"PO-Revision-Date: 2023-11-30 09:17:00+0000\n"
"Language: hu\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/4.0.1\n"

#: views/admin/placements/bulk-edit.php:21
msgid "Off"
msgstr ""

#: views/admin/placements/bulk-edit.php:18
#: views/admin/placements/bulk-edit.php:29
#: views/admin/placements/bulk-edit.php:39
msgid "No Change"
msgstr ""

#: modules/cache-busting/cache-busting.class.php:253
#: views/admin/placements/bulk-edit.php:37
msgid "Hide when empty"
msgstr ""

#: views/admin/placements/bulk-edit.php:31
#: views/admin/placements/bulk-edit.php:41
msgid "Disabled"
msgstr ""

#: views/admin/placements/bulk-edit.php:19
msgid "Auto"
msgstr ""

#. Author of the plugin
#: advanced-ads-pro.php
msgid "Advanced Ads"
msgstr ""

#. translators: %s: Plugin name
#: includes/class-bootstrap.php:155
msgid "Your versions of the Advanced Ads addons listed below are incompatible with <strong>Advanced Ads %s</strong> and have been deactivated. Please update the plugin to the latest version."
msgstr ""

#. translators: %s: Plugin name
#: includes/class-bootstrap.php:109
msgid "Your version of <strong>Advanced Ads - Pro</strong> is incompatible with <strong>Advanced Ads %s</strong> and has been deactivated. Please update the plugin to the latest version."
msgstr ""

#. translators: 1: is a link to a support document. 2: closing link
#: includes/class-autoloader.php:113
msgid "Your installation of Advanced Ads is incomplete. If you installed Advanced Ads from GitHub, %1$s please refer to this document%2$s to set up your development environment."
msgstr ""

#: modules/geo/admin/views/setting-download.php:22
msgid "You are currently using filter hooks to load custom database files."
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:17
msgid "When to show overlay again?"
msgstr ""

#: modules/placement_conditions/admin.class.php:70
msgid "Visitor Conditions"
msgstr ""

#: modules/ads-by-hours/src/class-admin.php:135
msgid "viewer time zone"
msgstr ""

#: modules/advanced-visitor-conditions/main.class.php:218
msgid "User IP Address"
msgstr ""

#: modules/ads-by-hours/views/publish-metabox-inputs.php:54
msgid "To"
msgstr ""

#. translators: 1 opening link tag. 2 closing tag
#: modules/cache-busting/views/settings.php:53
msgid "This option does not work with the current active theme and %1$sblock themes%2$s in general."
msgstr ""

#. translators: %s: dashicon class
#: modules/lazy-load/views/settings.php:22
msgid "This module requires: <br> <span class=\"dashicons %s\"></span> Cache Busting"
msgstr ""

#: modules/ads-for-adblockers/views/settings.php:25
msgid "This module requires:"
msgstr ""

#: views/admin/tables/ads/adsense-fallback.php:47
#: views/admin/tables/ads/adsense-fallback.php:66
msgid "The selected item will be displayed when an AdSense ad is unavailable, ensuring your ad space remains filled."
msgstr ""

#: includes/class-bootstrap.php:180
msgid "The following addons are affected:"
msgstr ""

#. translators: group or ad title.
#: views/admin/tables/ads/adsense-fallback.php:58
msgid "The default fallback is \"%s\". You can change this in the AdSense settings."
msgstr ""

#: views/admin/tables/ads/adsense-fallback.php:75
msgid "The AdSense fallback feature requires the ad to be assigned to a placement with enabled Cache Busting."
msgstr ""

#. translators: %s: WP_ENVIRONMENT_TYPES
#: includes/class-autoloader.php:157
msgid "The %s constant is no longer supported."
msgstr ""

#: modules/weekdays/class-advanced-ads-pro-weekdays.php:177
msgid "Shows up"
msgstr ""

#: modules/ads-by-hours/src/class-admin.php:79
msgid "Showing ads depending on the visitor's time requires enabling Cache Busting."
msgstr ""

#: modules/extended-adblocker/views/setting-activate-extended.php:26
msgid "Show a custom overlay to users with an ad blocker enabled, prompting them to turn it off on your website."
msgstr ""

#: modules/ads-for-adblockers/admin.class.php:89
#: modules/lazy-load/admin.class.php:58
msgid "Settings"
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:20
msgid "Set the timing for the overlay to reappear after being dismissed."
msgstr ""

#: modules/ads-by-hours/views/publish-metabox-inputs.php:21
msgid "Set specific hours"
msgstr ""

#: modules/cache-busting/cache-busting.class.php:257
#: views/admin/placements/bulk-edit.php:45
msgid "Remove the placeholder if unfilled."
msgstr ""

#: modules/extended-adblocker/views/setting-activate-extended.php:64
msgid "Redirect URL"
msgstr ""

#: modules/extended-adblocker/views/setting-activate-extended.php:37
msgid "Redirect"
msgstr ""

#: modules/lazy-load/admin.class.php:68
msgid "Prevent ads from getting loaded before they appear in the visitor’s visible area."
msgstr ""

#: modules/extended-adblocker/views/setting-activate-extended.php:22
msgid "Overlay"
msgstr ""

#: views/admin/tables/ads/adsense-fallback.php:27
msgid "None"
msgstr ""

#: views/admin/tables/ads/adsense-fallback.php:52
msgid "No default fallback ad selected. Choose one in the AdSense settings."
msgstr ""

#: modules/extended-adblocker/views/setting-activate-extended.php:17
msgid "No additional actions"
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:43
msgid "Never"
msgstr ""

#: modules/extended-adblocker/views/setting-styling.php:19
msgid "Insert CSS to customize the overlay container layout."
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:84
msgid "Insert CSS to customize the dismiss button layout."
msgstr ""

#: modules/extended-adblocker/views/setting-styling.php:36
msgid "Insert CSS to customize the background of the overlay."
msgstr ""

#. translators: %s: index input field
#: modules/bbpress/class-admin.php:106
msgid "Inject after %s post"
msgstr ""

#: includes/class-bootstrap.php:129
#: includes/class-bootstrap.php:152
msgid "Important Notice"
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:55
msgid "Hide dismiss button"
msgstr ""

#: modules/ads-for-adblockers/views/placement-item.php:18
msgid "Groups"
msgstr ""

#. translators: %s: first field, %s: last field
#: modules/advanced-display-conditions/main.class.php:571
msgid "from %1$s to %2$s"
msgstr ""

#: modules/ads-by-hours/views/publish-metabox-inputs.php:35
msgid "From"
msgstr ""

#: includes/admin/class-adsense.php:38
msgid "Fallback ad/group"
msgstr ""

#: views/admin/tables/ads/adsense-fallback.php:20
msgid "Fallback"
msgstr ""

#: modules/extended-adblocker/views/setting-exclude.php:13
msgid "Exclude"
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:28
msgid "Everytime"
msgstr ""

#. translators: 1: The download URL, 2: The HTTP error code, 3: The HTTP header
#. title, 4: The response body
#: modules/geo/classes/admin.php:545
msgid "Error downloading database from: %1$s - %2$s %3$s - %4$s"
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:68
msgid "Enter the text that you want to appear on the dismiss button."
msgstr ""

#: modules/extended-adblocker/views/setting-activate-extended.php:68
msgid "Enter a specific page on your domain to which users with activated AdBlocker should be automatically redirected."
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:77
#: modules/extended-adblocker/views/setting-dismissible.php:94
#: modules/extended-adblocker/views/setting-styling.php:29
#: modules/extended-adblocker/views/setting-styling.php:46
msgid "Empty and save to revert to defaults."
msgstr ""

#: modules/advanced-visitor-conditions/main.class.php:211
msgid "Display or hide ad when user use adblocker."
msgstr ""

#: includes/admin/class-ad-list-table.php:42
#: includes/admin/class-ad-list-table.php:86
msgid "Display Once"
msgstr ""

#: modules/placement_conditions/admin.class.php:52
msgid "Display Conditions"
msgstr ""

#: modules/advanced-visitor-conditions/main.class.php:219
msgid "Display ads based on the user IP address. Enter one IP address per line."
msgstr ""

#: modules/buddypress/class-buddypress.php:324
msgid "Display ads based on existing BuddyPress groups."
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:65
msgid "Dismiss button text"
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:81
msgid "Dismiss button styling"
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:48
msgid "Dismiss button"
msgstr ""

#: modules/extended-adblocker/main.class.php:83
#: modules/extended-adblocker/views/setting-dismissible.php:75
msgid "Dismiss"
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:58
msgid "Disabling the dismiss button significantly limits site interaction."
msgstr ""

#: modules/cache-busting/cache-busting.class.php:261
msgid "Deleting an empty placement might lead to a layout shift."
msgstr ""

#: views/admin/tables/ads/adsense-fallback.php:25
msgid "Default"
msgstr ""

#: includes/admin/class-duplicate-placement.php:101
msgid "Create a copy of this placement"
msgstr ""

#: includes/admin/class-group-duplication.php:77
msgid "Create a copy of this group"
msgstr ""

#: modules/extended-adblocker/views/setting-overlay-content.php:11
msgid "Content"
msgstr ""

#: modules/extended-adblocker/views/setting-styling.php:16
msgid "Container styling"
msgstr ""

#: modules/extended-adblocker/views/setting-exclude.php:16
msgid "Choose which user roles to exclude from this ad blocker countermeasure."
msgstr ""

#: modules/lazy-load/admin.class.php:56
msgid "Cache Busting needs to be enabled"
msgstr ""

#: modules/ads-for-adblockers/admin.class.php:87
msgid "Cache Busting and Ad blocker disguise need to be enabled"
msgstr ""

#: modules/buddypress/class-buddypress.php:323
msgid "BuddyPress group"
msgstr ""

#: modules/extended-adblocker/views/setting-styling.php:33
msgid "Background styling"
msgstr ""

#: modules/extended-adblocker/views/setting-activate-extended.php:41
msgid "Automatically redirect users with ad blockers enabled to an internal page. Content access is granted after turning off the ad blocker."
msgstr ""

#: includes/class-bootstrap.php:71
#: includes/class-bootstrap.php:78
msgid "Advanced Ads - Pro"
msgstr ""

#. translators: ad title.
#: includes/class-adsense.php:52
msgid "AdSense fallback was loaded for empty AdSense ad \"%s\""
msgstr ""

#: includes/class-bootstrap.php:130
msgid "Addons listed below requires the <strong><a href=\"https://wpadvancedads.com/?utm_source=advanced-ads&utm_medium=link&utm_campaign=activate-advanced-ads-pro\" target=\"_blank\">Advanced Ads</a></strong> plugin to be installed and activated on your site."
msgstr ""

#: modules/advanced-visitor-conditions/main.class.php:210
msgid "Adblocker"
msgstr ""

#: views/admin/tables/ads/adsense-fallback.php:29
msgid "Ad groups"
msgstr ""

#: modules/extended-adblocker/admin.class.php:49
msgid "Ad blocker countermeasures"
msgstr ""

#: modules/ads-for-adblockers/views/settings.php:29
msgid "Ad block disguise"
msgstr ""

#: modules/extended-adblocker/views/setting-activate-extended.php:51
msgid "Activate the ad blocker disguise above to display the overlay."
msgstr ""

#: modules/ads-by-hours/src/class-admin.php:70
msgid "A cache plugin has been detected. It is recommended to enable Cache Busting and check the visitor's time when displaying the ad on the frontend."
msgstr ""

#: includes/class-bootstrap.php:95
msgid "<strong>Advanced Ads - Pro</strong> requires the <strong>Advanced Ads free</strong> plugin to be installed and activated on your site."
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:37
msgid "1 week"
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:40
msgid "1 month"
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:31
msgid "1 hour"
msgstr ""

#: modules/extended-adblocker/views/setting-dismissible.php:34
msgid "1 day"
msgstr ""

#: modules/ads-for-adblockers/views/placement-item.php:15
msgid "--not selected--"
msgstr ""

#. translators: comma separated list of days.
#: modules/weekdays/class-advanced-ads-pro-weekdays.php:181
msgid " on: %s"
msgstr ""

#. translators: 1. localized time 2. localized time 3. timezone name.
#: modules/weekdays/class-advanced-ads-pro-weekdays.php:187
msgid " between %1$s and %2$s %3$s"
msgstr ""

#: modules/responsive-ads/views/setting_reload_ads.php:19
msgid "You need to enable cache-busting in order to use this feature."
msgstr "A funkció használatához engedélyeznie kell a gyorsítótár-busting használatát."

#. translators: 1 is the opening link to the Advanced Ads website, 2 the
#. closing link
#: classes/advanced-ads-pro.php:338
msgid "We have renamed the Responsive Ads add-on to ‘Advanced Ads AMP Ads’. With this change, the Browser Width visitor condition moved from that add-on into Advanced Ads Pro. You can deactivate ‘Advanced Ads AMP Ads’ if you don’t utilize AMP ads or the custom sizes feature for responsive AdSense ad units. %1$sRead more%2$s."
msgstr "Átneveztük a Reszponzív Hirdetések hozzáadását 'Advanced Ads AMP Ads'-re Ezzel a változtatással a Böngésző szélesség látogatói feltétel a bővítményből átkerült az Advanced Ads Pro rendszerbe. Deaktiválhatja a „Speciális hirdetések AMP-hirdetéseit”, ha nem használ AMP-hirdetéseket vagy az egyéni méretek funkciót az adaptív AdSense hirdetési egységekhez. %1$sTovábbi információ%2$s."

#: modules/responsive-ads/includes/class-admin.php:45
msgid "Responsive Image Ads"
msgstr "Reszponzív képi hirdetések"

#: modules/responsive-ads/includes/class-admin.php:38
msgid "Responsive Ads"
msgstr "Reszpozív hirdetések"

#: modules/responsive-ads/views/setting_reload_ads.php:16
msgid "Reload ads when the screen resizes."
msgstr "Hirdetések újratöltése a képernyő átméretezésekor"

#: modules/responsive-ads/includes/class-admin.php:53
msgid "Reload ads on resize"
msgstr "Hirdetések újratöltése átméretezéskor"

#: modules/responsive-ads/includes/class-admin.php:61
msgid "Fallback width"
msgstr "Tartalék szélesség"

#: modules/responsive-ads/includes/class-common.php:40
msgid "Display ads based on the browser width."
msgstr "Hirdetések megjelenítése a böngésző szélessége alapján."

#: modules/cache-busting/admin-ui.class.php:51
msgid "Couldn't find the placement."
msgstr "Nem található az elhelyezés."

#: modules/responsive-ads/views/setting_responsive_images.php:15
msgid "Check this option if the size of image ads is not adjusted responsively by your theme."
msgstr "Jelölje be ezt a lehetőséget, ha a képes hirdetések méretét nem igazítja megfelelően a téma."

#: modules/cache-busting/admin-ui.class.php:48
msgid "Cache busting has been successfully enabled for the assigned placement."
msgstr "A gyorsítótár-törlés sikeresen engedélyezve lett a hozzárendelt elhelyezéshez."

#: modules/responsive-ads/includes/class-common.php:39
msgid "browser width"
msgstr "böngésző szélesség"

#: modules/cache-busting/admin-ui.class.php:88
#: modules/cache-busting/views/settings.php:75
msgid "off"
msgstr "ki"

#: modules/cache-busting/views/settings.php:18
msgid "Choose which method to use when a placement needs cache busting and the option is set to “auto”."
msgstr "Válassza ki, hogy melyik módszert használja, ha egy elhelyezéshez gyorsítótár-lefoglalásra van szükség, és az opció „automatikus”-ra van állítva."

#: modules/cache-busting/admin-ui.class.php:86
msgid "auto"
msgstr "auto"

#: modules/cache-busting/admin-ui.class.php:39
#: modules/geo/classes/admin.php:435
msgid "You are not allowed to do this."
msgstr "Ehhez nincs jogodultsága"

#: modules/geo/classes/admin.php:447
msgid "Please provide a MaxMind license key"
msgstr "Kérem adjon meg egy MaxMind licenckulcsot"

#. translators: Link to parallax manual
#: modules/parallax-ads/views/placement-options-after.php:24
msgid "Optimized for image ads. %s"
msgstr "Képes hirdetésekhez optimalizálva. %s"

#: modules/parallax-ads/views/placement-options-after.php:19
msgid "Enable the parallax effect."
msgstr "Parallax hatás engedélyezése"

#: modules/rest-api/classes/Admin_UI.php:33
msgid "REST API"
msgstr "REST API"

#: modules/parallax-ads/classes/Advanced_Ads_Pro_Module_Parallax_Admin_UI.php:71
msgid "Parallax Ads"
msgstr "Parallax Hirdetések"

#: modules/parallax-ads/views/placement-options-after.php:37
msgctxt "Parallax Ad placement height unit"
msgid "Unit"
msgstr "Egység"

#: modules/parallax-ads/views/placement-options-after.php:31
msgctxt "Parallax Ad placement height"
msgid "Height"
msgstr "Magasság"

#: modules/rest-api/classes/Advanced_Ads_Group.php:27
msgid "Invalid group ID."
msgstr "Érvénytelen csoportazonosító."

#: modules/parallax-ads/views/placement-options-after.php:40
msgid "Choose the height of the cutout, either in pixels or relative to the viewport."
msgstr "Válassza ki a kivágás magasságát képpontokban vagy a nézetablakhoz viszonyítva."

#: modules/geo/admin/views/visitor-profile.php:20
msgid "Reset Visitor Profile"
msgstr "Látogatói profil visszaállítása"

#: modules/geo/admin/views/visitor-profile.php:9
msgid "Location based on your visitor profile cookie:"
msgstr "Helyszín a látogatói profil cookie-ja alapján:"

#: modules/geo/classes/admin.php:405
msgid "Location based on your IP address"
msgstr "Helyszín az Ön IP-címe alapján"

#: modules/advanced-visitor-conditions/main.class.php:203
msgid "Display or hide ads for new visitors."
msgstr "Hirdetések megjelenítése vagy elrejtése új látogatók számára."

#: modules/advanced-visitor-conditions/main.class.php:181
msgid "Display ads based on the value of a cookie. Set the operator to “matches/does not match” and leave the value empty to check only the existence of the cookie."
msgstr "Hirdetések megjelenítése a cookie értéke alapján. Állítsa az operátort „egyezik/nem egyezik” értékre, és hagyja üresen az értéket, hogy csak a cookie meglétét ellenőrizze."

#: modules/advanced-display-conditions/main.class.php:88
msgid "Display ads based on the page language set by the WPML plugin."
msgstr "Hirdetések megjelenítése a WPML beépülő modul által beállított oldalnyelv alapján."

#: modules/buddypress/class-buddypress.php:335
#: modules/buddypress/class-buddypress.php:376
msgid "Display ads based on existing BuddyBoss groups."
msgstr "Hirdetések megjelenítése a meglévő BuddyBoss-csoportok alapján."

#: modules/geo/classes/admin.php:367
msgid "miles (mi)"
msgstr "mérföld (mi)"

#: modules/geo/classes/admin.php:726
msgid "There was an error connecting to the search service."
msgstr "Hiba történt a kereső szolgáltatáshoz való kapcsolódáskor"

#: modules/geo/classes/admin.php:725
msgid "Your search did not return any results."
msgstr "A keresés nem hozott eredményt."

#. translators: 1: A link to the geo location service.
#: modules/geo/classes/admin.php:728
msgid "You can search for the geo coordinates manually at %1$s."
msgstr "A földrajzi koordinátákat manuálisan is megkeresheti a következő itt: %1$s."

#. translators: 1: The number of search results.
#: modules/geo/classes/admin.php:724
msgid "Found %1$d results. Please pick the one, you want to use."
msgstr "%1$d eredményt találtunk. Kérjük, válassza ki azt, amelyiket használni szeretné."

#: modules/geo/classes/admin.php:366
msgid "kilometers (km)"
msgstr "kilometer (km)"

#. translators: 1 is the opening link to the plugins page, 2 the closing link
#: modules/geo/classes/Advanced_Ads_Geo_Version_Check.php:51
msgid "The geo-targeting visitor condition moved into Advanced Ads Pro. You can remove Geo Targeting %1$shere%2$s."
msgstr "A földrajzi célzás látogatói feltétele átkerült az Advanced Ads Pro szolgáltatásba. A földrajzi célzást eltávolíthatja: %1$sitt%2$s."

#: modules/geo/classes/api.php:389
msgid "Zimbabwe"
msgstr "Zimbabwe"

#: modules/geo/classes/api.php:388
msgid "Zambia"
msgstr "Zambia"

#: modules/geo/classes/admin.php:245
msgid "Your IP address format is incorrect"
msgstr "Az ön IP-cím formátuma helytelen"

#. translators: %s is a URL.
#: modules/cache-busting/admin-ui.class.php:196
msgid "You need a placement to deliver this group using cache busting. <a href=\"%s\" target=\"_blank\">Create a placement now.</a>"
msgstr "Szüksége van egy elhelyezésre a csoport gyorsítótár-törléssel történő megjelenítéséhez. <a href=\"%s\" target=\"_blank\">Hozzon létre egy elhelyezést most.</a>"

#: modules/geo/classes/api.php:387
msgid "Yemen"
msgstr "Jemen"

#: modules/geo/classes/api.php:386
msgid "Western Samoa"
msgstr "Nyugat Szamoa"

#: modules/geo/classes/api.php:385
msgid "Western Sahara"
msgstr "Nyugat Szahara"

#: modules/geo/classes/api.php:384
msgid "Wallis and Futuna Islands"
msgstr "Wallis és Futuna Szigetek"

#: modules/geo/classes/api.php:383
msgid "Virgin Islands (USA)"
msgstr "Virgin Szigetek (USA)"

#: modules/geo/classes/api.php:382
msgid "Virgin Islands (British)"
msgstr "Virgin Szigetek (Britt)"

#: modules/geo/classes/api.php:381
msgid "Vietnam"
msgstr "Vietnam"

#: modules/geo/classes/api.php:380
msgid "Venezuela"
msgstr "Venezuela"

#: modules/geo/classes/api.php:379
msgid "Vanuatu"
msgstr "Vanuatu"

#: modules/geo/classes/api.php:378
msgid "Uzbekistan"
msgstr "Üzbegisztán"

#: modules/geo/classes/api.php:377
msgid "US Minor Outlying Islands"
msgstr "Egyesült Államok külső szigetei"

#: modules/geo/classes/api.php:376
msgid "Uruguay"
msgstr "Uruguay"

#: modules/geo/admin/views/setting-download.php:57
msgid "Update geo location databases"
msgstr "Földrajzi helyadatbázisok frissítése"

#: modules/geo/classes/api.php:129
msgid "United States"
msgstr "Egyesült Államok"

#: modules/geo/classes/api.php:130
msgid "United Kingdom"
msgstr "Egyesült Királyság"

#: modules/geo/classes/api.php:375
msgid "United Arab Emirates"
msgstr "Egyesült Arab Emirátusok"

#: modules/geo/classes/api.php:374
msgid "Ukraine"
msgstr "Ukrajna"

#: modules/geo/classes/api.php:373
msgid "Uganda"
msgstr "Uganda"

#: modules/geo/classes/api.php:372
msgid "Tuvalu"
msgstr "Tuvalu"

#: modules/geo/classes/api.php:371
msgid "Turks and Caicos Islands"
msgstr "Turks-és Caicos-szigetek"

#: modules/geo/classes/api.php:370
msgid "Turkmenistan"
msgstr "Türkmenisztán"

#: modules/geo/classes/api.php:369
msgid "Turkey"
msgstr "Törökország"

#: modules/geo/classes/api.php:368
msgid "Tunisia"
msgstr "Tunézia"

#: modules/geo/classes/api.php:367
msgid "Trinidad and Tobago"
msgstr "Trinidad és Tobago"

#: modules/geo/classes/api.php:366
msgid "Tonga"
msgstr "Tonga"

#: modules/geo/classes/api.php:365
msgid "Tokelau"
msgstr "Tokelau"

#: modules/geo/classes/api.php:364
msgid "Togo"
msgstr "Togo"

#: modules/geo/classes/api.php:363
msgid "Timor-Leste"
msgstr "Kelet-Timor"

#: modules/geo/classes/admin.php:351
msgid "This option can’t be used if your site is using sucuri.net."
msgstr "Ez a beállítás nem használható, ha webhelye a sucuri.net-et használja."

#: modules/geo/classes/admin.php:441
msgid "The upload dir is not available"
msgstr "A feltöltési könyvtár nem érhető el"

#: modules/geo/admin/views/setting-download.php:31
msgid "The MaxMind license key is missing."
msgstr "A MaxMind licenckulcs hiányzik."

#: modules/geo/admin/views/setting-download.php:91
msgid "The databases are updated on the first Tuesday (midnight, GMT) of each month."
msgstr "Az adatbázisok frissítése minden hónap első keddje (éjfél, GMT)."

#: modules/geo/classes/api.php:362
msgid "Thailand"
msgstr "Thaiföld"

#: modules/geo/classes/api.php:361
msgid "Tanzania"
msgstr "Tanzánia"

#: modules/geo/classes/api.php:360
msgid "Tajikistan"
msgstr "Tadzsikisztán"

#: modules/geo/classes/api.php:359
msgid "Taiwan"
msgstr "Taiwan"

#: modules/geo/classes/api.php:358
msgid "Syrian Arab Republic"
msgstr "Szíriai Arab Köztársaság"

#: modules/geo/classes/api.php:357
msgid "Switzerland"
msgstr "Svájc"

#: modules/geo/classes/api.php:356
msgid "Sweden"
msgstr "Svédország"

#: modules/geo/classes/api.php:355
msgid "Swaziland"
msgstr "Szváziföld"

#: modules/geo/classes/api.php:354
msgid "Svalbard and Jan Mayen Islands"
msgstr "Svalbard és Jan Mayen-Szigetek"

#: modules/geo/classes/api.php:353
msgid "Suriname"
msgstr "Szurinám"

#: modules/geo/classes/api.php:352
msgid "Sudan"
msgstr "Szudán"

#: modules/geo/classes/plugin.php:130
msgid "Sucuri Header (country only)"
msgstr "Sucuri fejléc (csak ország)"

#: modules/geo/classes/admin.php:333
msgid "State/Region"
msgstr "Állam/Régió"

#: modules/geo/classes/api.php:351
msgid "Sri Lanka"
msgstr "Sri Lanka"

#: modules/geo/classes/api.php:350
msgid "Spain"
msgstr "Spanyolország"

#: modules/geo/classes/api.php:349
msgid "South Sudan"
msgstr "Dél Szudán"

#: modules/geo/classes/api.php:348
msgid "South Korea"
msgstr "Dél Korea"

#: modules/geo/classes/api.php:347
msgid "South Georgia"
msgstr "Dél Georgia"

#: modules/geo/classes/api.php:135
msgid "South America"
msgstr "Dél Amerika"

#: modules/geo/classes/api.php:346
msgid "South Africa"
msgstr "Dél Afrika"

#: modules/geo/classes/api.php:345
msgid "Somalia"
msgstr "Szomália"

#: modules/geo/classes/api.php:344
msgid "Solomon Islands"
msgstr "Salamon Szigetek"

#: modules/geo/classes/api.php:343
msgid "Slovenia"
msgstr "Sílovénia"

#: modules/geo/classes/api.php:342
msgid "Slovak Republic"
msgstr "Sílovák Köztársaság"

#: modules/geo/classes/api.php:341
msgid "Singapore"
msgstr "Szingapúr"

#: modules/geo/classes/api.php:340
msgid "Sierra Leone"
msgstr "Szierra Leone"

#: modules/geo/classes/api.php:339
msgid "Seychelles"
msgstr "Seychelles"

#: modules/geo/classes/api.php:338
msgid "Serbia"
msgstr "Szerbia"

#: modules/geo/classes/api.php:337
msgid "Senegal"
msgstr "Szenegál"

#: modules/geo/classes/admin.php:390
msgid "Search"
msgstr "Keresés"

#: modules/geo/classes/api.php:336
msgid "Saudi Arabia"
msgstr "Szaudi Arábia"

#: modules/geo/classes/api.php:334
msgid "San Marino"
msgstr "San Marino"

#: modules/geo/classes/api.php:333
msgid "Saint Vincent and the Grenadines"
msgstr "Saint Vincent és a Grenadine-Szigetek"

#: modules/geo/classes/api.php:332
msgid "Saint Pierre and Miquelon"
msgstr "Saint Pierre és Miquelon"

#: modules/geo/classes/api.php:330
msgid "Saint Martin (French)"
msgstr "Saint Martin (Francia)"

#: modules/geo/classes/api.php:331
msgid "Saint Martin (Dutch)"
msgstr "Saint Martin (Holland)"

#: modules/geo/classes/api.php:329
msgid "Saint Lucia"
msgstr "Szent Lucia"

#: modules/geo/classes/api.php:328
msgid "Saint Kitts and Nevis"
msgstr "Saint Kitts és Nevis"

#: modules/geo/classes/api.php:327
msgid "Saint Helena"
msgstr "Szent Ilona"

#: modules/geo/classes/api.php:326
msgid "Saint Barth&eacute;lemy"
msgstr "Szent Bartolómeo"

#: modules/geo/classes/api.php:335
msgid "S&atilde;o Tom&eacute; and Pr&iacute;ncipe"
msgstr "S&atilde;o Tom&eacute; and Pr&iacute;ncipe"

#: modules/geo/classes/api.php:325
msgid "Rwanda"
msgstr "Ruanda"

#: modules/geo/classes/api.php:324
msgid "Russian Federation"
msgstr "Orosz Föderáció"

#: modules/geo/classes/api.php:323
msgid "Romania"
msgstr "Románia"

#: modules/geo/classes/api.php:322
msgid "Reunion Island"
msgstr "Réunion Sziget"

#: modules/geo/classes/api.php:321
msgid "Republic of Kosovo"
msgstr "Koszovói Köztársaság"

#: modules/geo/classes/api.php:320
msgid "Qatar"
msgstr "Katar"

#: modules/geo/classes/api.php:319
msgid "Puerto Rico"
msgstr "Puerto Rico"

#: modules/geo/classes/api.php:318
msgid "Portugal"
msgstr "Portugália"

#: modules/geo/classes/api.php:317
msgid "Poland"
msgstr "Lengyelország"

#. translators: 1: opening <a>-tag to Advanced Ads manual, 2: closing <a>-tag
#: modules/geo/admin/views/setting-download.php:36
#: modules/geo/classes/admin.php:234
msgid "Please read the %1$sinstallation instructions%2$s."
msgstr "Kérjük, olvassa el a %1$stelepítési útmutatót%2$s."

#: modules/geo/classes/api.php:316
msgid "Pitcairn Island"
msgstr "Pitcairn Sziget"

#: modules/geo/classes/api.php:315
msgid "Phillipines"
msgstr "Fülöp-szigetek"

#: modules/geo/classes/api.php:314
msgid "Peru"
msgstr "Peru"

#: modules/geo/classes/api.php:313
msgid "Paraguay"
msgstr "Paraguay"

#: modules/geo/classes/api.php:312
msgid "Papua New Guinea"
msgstr "Pápua Új-Guinea"

#: modules/geo/classes/api.php:311
msgid "Panama"
msgstr "Panama"

#: modules/geo/classes/api.php:310
msgid "Palestinian Territories"
msgstr "Palesztin területek"

#: modules/geo/classes/api.php:309
msgid "Palau"
msgstr "Palau"

#: modules/geo/classes/api.php:308
msgid "Pakistan"
msgstr "Pakisztán"

#: modules/geo/classes/admin.php:332
#: modules/geo/classes/admin.php:334
msgid "or"
msgstr "vagy"

#: modules/geo/classes/api.php:307
msgid "Oman"
msgstr "Omán"

#: modules/geo/classes/api.php:139
msgid "Oceania"
msgstr "Óceánia"

#: modules/geo/classes/api.php:306
msgid "Norway"
msgstr "Norvégia"

#: modules/geo/classes/api.php:305
msgid "Northern Mariana Islands"
msgstr "Észak Mariana szigetek"

#: modules/geo/classes/api.php:304
msgid "North Korea"
msgstr "Észak Korea"

#: modules/geo/classes/api.php:134
msgid "North America"
msgstr "Észak Amerika"

#: modules/geo/classes/api.php:303
msgid "Norfolk Island"
msgstr "Norfolk-sziget"

#: modules/geo/classes/api.php:302
msgid "Niue"
msgstr "Niue"

#: modules/geo/classes/api.php:301
msgid "Nigeria"
msgstr "Nigéria"

#: modules/geo/classes/api.php:300
msgid "Niger"
msgstr "Niger"

#: modules/geo/classes/api.php:299
msgid "Nicaragua"
msgstr "Nicaragua"

#. translators: Timestamp in the localized date_format
#: modules/geo/admin/views/setting-download.php:84
msgid "Next possible update on %s."
msgstr "Következő lehetséges frissítés: %s."

#: modules/geo/classes/api.php:298
msgid "New Zealand"
msgstr "Új Zéland"

#: modules/geo/classes/api.php:297
msgid "New Caledonia"
msgstr "Új-Kaledónia"

#: modules/geo/classes/api.php:296
msgid "Netherlands Antilles"
msgstr "Holland Antillák"

#: modules/geo/classes/api.php:295
msgid "Netherlands"
msgstr "Hollandia"

#: modules/geo/classes/api.php:294
msgid "Nepal"
msgstr "Nepál"

#: modules/geo/classes/api.php:293
msgid "Nauru"
msgstr "Nauru"

#: modules/geo/classes/api.php:292
msgid "Namibia"
msgstr "Namíbia"

#: modules/geo/classes/api.php:291
msgid "Myanmar"
msgstr "Mianmar"

#: modules/geo/classes/api.php:290
msgid "Mozambique"
msgstr "Mozambik"

#: modules/geo/classes/api.php:289
msgid "Morocco"
msgstr "Marokkó"

#: modules/geo/classes/api.php:288
msgid "Montserrat"
msgstr "Montserrat"

#: modules/geo/classes/api.php:287
msgid "Montenegro"
msgstr "Montenegro"

#: modules/geo/classes/api.php:286
msgid "Mongolia"
msgstr "Mongólia"

#: modules/geo/classes/api.php:285
msgid "Monaco"
msgstr "Monaco"

#: modules/geo/classes/api.php:284
msgid "Moldova, Republic of"
msgstr "Moldovai Köztársaság,"

#: modules/geo/classes/api.php:283
msgid "Micronesia"
msgstr "Mikronézia"

#: modules/geo/classes/api.php:282
msgid "Mexico"
msgstr "Mexikó"

#: modules/geo/classes/admin.php:76
msgid "Method"
msgstr "Módszer"

#: modules/geo/classes/api.php:281
msgid "Mayotte"
msgstr "Mayotte"

#: modules/geo/classes/admin.php:241
msgid "Maybe you are working on a local or secured environment."
msgstr "Lehet, hogy helyi vagy biztonságos környezetben dolgozik."

#: modules/geo/classes/admin.php:57
msgid "MaxMind license key"
msgstr "MaxMind licensz kulcs"

#: modules/geo/classes/plugin.php:124
msgid "MaxMind database (default)"
msgstr "MaxMind adatbázis (alapértelmezett)"

#: modules/geo/classes/admin.php:66
msgid "MaxMind Database"
msgstr "MaxMind adatbázis"

#: modules/geo/classes/api.php:280
msgid "Mauritius"
msgstr "Mauritius"

#: modules/geo/classes/api.php:279
msgid "Mauritania"
msgstr "Mauritánia"

#: modules/geo/classes/api.php:278
msgid "Martinique"
msgstr "Martinique"

#: modules/geo/classes/api.php:277
msgid "Marshall Islands"
msgstr "Marshall-szigetek"

#: modules/geo/classes/api.php:276
msgid "Malta"
msgstr "Málta"

#: modules/geo/classes/api.php:275
msgid "Mali"
msgstr "Mali"

#: modules/geo/classes/api.php:274
msgid "Maldives"
msgstr "Maldív"

#: modules/geo/classes/api.php:273
msgid "Malaysia"
msgstr "Malajzia"

#: modules/geo/classes/api.php:272
msgid "Malawi"
msgstr "Malawi"

#: modules/geo/classes/api.php:271
msgid "Madagascar"
msgstr "Madagaszkár"

#: modules/geo/classes/api.php:270
msgid "Macedonia"
msgstr "Makedónia"

#: modules/geo/classes/api.php:269
msgid "Macau"
msgstr "Makaó"

#: modules/geo/classes/api.php:268
msgid "Luxembourg"
msgstr "Luxembourg"

#: modules/geo/classes/admin.php:373
msgid "Longitude"
msgstr "Hosszúság"

#: modules/geo/classes/api.php:267
msgid "Lithuania"
msgstr "Litvánia"

#: modules/geo/classes/api.php:266
msgid "Liechtenstein"
msgstr "Liechtenstein"

#: modules/geo/classes/api.php:265
msgid "Libyan Arab Jamahiriya"
msgstr "Libia Arab Jamahiriya"

#: modules/geo/classes/api.php:264
msgid "Liberia"
msgstr "Libéria"

#: modules/geo/classes/api.php:263
msgid "Lesotho"
msgstr "Lesotho"

#: modules/geo/classes/api.php:262
msgid "Lebanon"
msgstr "Libanon"

#: modules/geo/classes/api.php:261
msgid "Latvia"
msgstr "Lettország"

#: modules/geo/classes/admin.php:372
msgid "Latitude"
msgstr "Szélesség"

#. translators: Timestamp in the localized date_format
#: modules/geo/admin/views/setting-download.php:73
msgid "Last update: %s"
msgstr "Utolsó frissítés: %s"

#: modules/geo/classes/api.php:260
msgid "Lao People's Democratic Republic"
msgstr "Laoszi Népi Demokratikus Köztársaság"

#: modules/geo/classes/admin.php:86
msgid "Language of names"
msgstr "A nevek nyelve"

#: modules/geo/classes/api.php:259
msgid "Kyrgyzstan"
msgstr "Kirgizisztán"

#: modules/geo/classes/api.php:258
msgid "Kuwait"
msgstr "Kuvait"

#: modules/geo/classes/api.php:257
msgid "Kiribati"
msgstr "Kiribati"

#: modules/geo/classes/api.php:256
msgid "Kenya"
msgstr "Kenya"

#: modules/geo/classes/api.php:255
msgid "Kazakhstan"
msgstr "Kazahsztán"

#: modules/geo/classes/api.php:254
msgid "Jordan"
msgstr "Jordánia"

#: modules/geo/classes/api.php:253
msgid "Jersey"
msgstr "Jersey"

#: modules/geo/classes/api.php:252
msgid "Japan"
msgstr "Japán"

#: modules/geo/classes/api.php:251
msgid "Jamaica"
msgstr "Jamaika"

#: modules/geo/classes/api.php:250
msgid "Italy"
msgstr "Olaszország"

#. translators: %s is a URL.
#: modules/cache-busting/admin-ui.class.php:218
msgid "It seems that a caching plugin is activated. Your ads might not rotate properly, while cache busting is disabled for the placement your group is using. <a href=\"#\" data-placement=\"%s\" class=\"js-placement-activate-cb\">Activate cache busting for this placement.</a>"
msgstr "Úgy tűnik, hogy egy gyorsítótárazó bővítmény aktiválva van. Előfordulhat, hogy hirdetései nem forognak megfelelően, miközben a gyorsítótár-törléssel le van tiltva a csoportja által használt elhelyezés. <a href=\"#\" data-placement=\"%s\" class=\"js-placement-activate-cb\">A gyorsítótár-lefoglalás aktiválása ennél az elhelyezésnél.</a>"

#. translators: %s is a URL.
#: modules/cache-busting/admin-ui.class.php:173
msgid "It seems that a caching plugin is activated. Your ads might not rotate properly while cache busting is disabled. <a href=\"%s\" target=\"_blank\">Activate cache busting.</a>"
msgstr "Úgy tűnik, hogy egy gyorsítótárazó bővítmény aktiválva van. Előfordulhat, hogy hirdetései nem forognak megfelelően, ha a gyorsítótár-törlés ki van kapcsolva. <a href=\"%s\" target=\"_blank\">A gyorsítótár-ltörlés aktiválása.</a>"

#: modules/geo/classes/api.php:249
msgid "Israel"
msgstr "Izrael"

#: modules/geo/classes/api.php:248
msgid "Isle of Man"
msgstr "Men Sziget"

#: modules/geo/classes/api.php:247
msgid "Ireland"
msgstr "Írország"

#: modules/geo/classes/api.php:246
msgid "Iraq"
msgstr "Irak"

#: modules/geo/classes/api.php:245
msgid "Iran"
msgstr "Irán"

#: modules/geo/classes/api.php:244
msgid "Indonesia"
msgstr "Indonézia"

#: modules/geo/classes/api.php:243
msgid "India"
msgstr "India"

#: modules/geo/admin/views/setting-download.php:50
msgid "In order to use Geo Targeting, please download the geo location databases by clicking on the button below."
msgstr "A földrajzi célzás használatához kérjük, töltse le a földrajzi helyadatbázisokat az alábbi gombra kattintva."

#: modules/click-fraud-protection/click-fraud-protection.class.php:156
msgid "Ignore global Click Fraud Protection"
msgstr "A globális kattintáscsalás elleni védelem figyelmen kívül hagyása"

#: modules/geo/classes/api.php:242
msgid "Iceland"
msgstr "Izland"

#: modules/geo/classes/api.php:241
msgid "Hungary"
msgstr "Magyarország"

#: modules/geo/classes/api.php:240
msgid "Hong Kong"
msgstr "Hong Kong"

#: modules/geo/classes/api.php:239
msgid "Honduras"
msgstr "Hondurasz"

#: modules/geo/classes/api.php:238
msgid "Holy See (City Vatican State)"
msgstr "Szentszék (Vatikáni Állam)"

#: modules/geo/classes/api.php:237
msgid "Heard and McDonald Islands"
msgstr "Heard és McDonald-szigetek"

#: modules/geo/classes/api.php:236
msgid "Haiti"
msgstr "Haiti"

#: modules/geo/classes/api.php:235
msgid "Guyana"
msgstr "Guyana"

#: modules/geo/classes/api.php:234
msgid "Guinea-Bissau"
msgstr "Bissau-Guinea"

#: modules/geo/classes/api.php:233
msgid "Guinea"
msgstr "Guinea"

#: modules/geo/classes/api.php:232
msgid "Guernsey"
msgstr "Guernsey"

#: modules/geo/classes/api.php:231
msgid "Guatemala"
msgstr "Guatemala"

#: modules/geo/classes/api.php:230
msgid "Guam"
msgstr "Guám"

#: modules/geo/classes/api.php:229
msgid "Guadeloupe"
msgstr "Guadeloupe"

#: modules/geo/classes/api.php:228
msgid "Grenada"
msgstr "Grenada"

#: modules/geo/classes/api.php:227
msgid "Greenland"
msgstr "Grönland"

#: modules/geo/classes/api.php:224
msgid "Greece"
msgstr "Görögország"

#: modules/geo/classes/api.php:226
msgid "Gibraltar"
msgstr "Gibraltár"

#: modules/geo/classes/api.php:225
msgid "Ghana"
msgstr "Gána"

#: modules/geo/classes/admin.php:370
msgid "get coordinates"
msgstr "koordináták megszerzése"

#: modules/geo/classes/api.php:132
msgid "Germany"
msgstr "Németország"

#: modules/geo/classes/api.php:223
msgid "Georgia"
msgstr "Grúzia"

#: modules/geo/classes/admin.php:49
msgid "Geo Targeting"
msgstr "Földrajzi célzás"

#: modules/geo/classes/plugin.php:105
msgid "geo location"
msgstr "Földrajzi hely"

#: modules/geo/admin/views/setting-download.php:47
#: modules/geo/classes/admin.php:231
msgid "Geo Databases not found."
msgstr "A földrajzi adatbázisok nem találhatók."

#: modules/geo/classes/public.php:222
msgid "Geo Databases not found"
msgstr "A földrajzi adatbázisok nem találhatók."

#: modules/geo/admin/views/setting-download.php:16
msgid "Geo Databases found."
msgstr "A földrajzi adatbázis megtalálva."

#: modules/geo/classes/api.php:222
msgid "Gambia"
msgstr "Gambia"

#: modules/geo/classes/api.php:221
msgid "Gabon"
msgstr "Gabon"

#: modules/geo/classes/admin.php:368
msgid "from"
msgstr "től"

#: modules/geo/classes/api.php:220
msgid "French Southern Territories"
msgstr "Francia déli területek"

#: modules/geo/classes/api.php:219
msgid "French Polynesia"
msgstr "Francia Polinézia"

#: modules/geo/classes/api.php:218
msgid "French Guiana"
msgstr "Francia Guyana"

#: modules/geo/classes/api.php:217
msgid "France"
msgstr "Franciaország"

#: modules/geo/classes/api.php:216
msgid "Finland"
msgstr "Finnország"

#: modules/geo/classes/api.php:215
msgid "Fiji"
msgstr "Fiji"

#: modules/geo/classes/api.php:214
msgid "Faroe Islands"
msgstr "Feröer szigetek"

#: modules/geo/classes/api.php:213
msgid "Falkland Islands"
msgstr "Falkland Szigetek"

#: modules/geo/classes/api.php:131
msgid "European Union"
msgstr "Európai Unió"

#: modules/geo/classes/api.php:136
msgid "Europe"
msgstr "Európa"

#: modules/geo/classes/api.php:212
msgid "Ethiopia"
msgstr "Etiópia"

#: modules/geo/classes/api.php:211
msgid "Estonia"
msgstr "Észtország"

#: modules/geo/classes/api.php:210
msgid "Eritrea"
msgstr "Eritreja"

#: modules/geo/classes/api.php:208
msgid "Equatorial Guinea"
msgstr "Egyenlítői Guinea"

#: modules/geo/classes/admin.php:393
msgid "Enter the name of the city, click the search button and pick one of the results to set the coordinates of the center."
msgstr "Írja be a város nevét, kattintson a keresés gombra, és válassza ki az egyik találatot a központ koordinátáinak beállításához."

#: modules/geo/classes/api.php:209
msgid "El Salvador"
msgstr "El Szalvador"

#: modules/geo/classes/api.php:207
msgid "Egypt"
msgstr "Egyiptom"

#: modules/geo/classes/api.php:206
msgid "Ecuador"
msgstr "Equador"

#: modules/geo/classes/api.php:205
msgid "East Timor"
msgstr "Kelet Timor"

#: modules/geo/classes/api.php:204
msgid "Dominican Republic"
msgstr "Dominikai Köztársaság"

#: modules/geo/classes/api.php:203
msgid "Dominica"
msgstr "Dominika"

#: modules/geo/classes/api.php:202
msgid "Djibouti"
msgstr "Dzsibuti"

#: modules/geo/classes/admin.php:378
msgid "Distance to center"
msgstr "Távolság a központig"

#: modules/geo/classes/admin.php:361
msgid "Distance is less than or equal to"
msgstr "A távolság kisebb, vagy egyenlő, mint"

#: modules/geo/classes/admin.php:362
msgid "Distance is greater than"
msgstr "A távolság nagyobb"

#: modules/geo/classes/admin.php:364
msgid "Distance"
msgstr "Távolság"

#: modules/geo/classes/plugin.php:106
msgid "Display ads based on geo location."
msgstr "Földrajzi hely alapján mutasd a hirdetéseket."

#: modules/geo/classes/api.php:201
msgid "Denmark"
msgstr "Dánia"

#: modules/geo/classes/admin.php:518
msgid "Database updated successfully!"
msgstr "Az adatbázis sikeresen frissítve."

#: modules/geo/classes/admin.php:536
msgid "Database update failed"
msgstr "Az adatbázis frissítés sikertelen."

#: modules/geo/classes/api.php:200
msgid "Czech Republic"
msgstr "Csehország"

#: modules/geo/classes/api.php:199
msgid "Cyprus"
msgstr "Ciprus"

#: modules/geo/classes/api.php:198
msgid "Cura&Ccedil;ao"
msgstr "Cura&Ccedil;ao"

#: modules/geo/classes/api.php:197
msgid "Cuba"
msgstr "Kuba"

#: modules/geo/classes/api.php:196
msgid "Croatia/Hrvatska"
msgstr "Horvátország"

#. translators: an error mesage.
#: modules/geo/classes/admin.php:573
msgid "Could not open downloaded database for reading: %s"
msgstr "A letöltött adatbázis nem nyitható meg olvasásra: %s"

#. translators: MaxMind database file name.
#: modules/geo/classes/admin.php:602
msgid "Could not open database for writing %s"
msgstr "Az adatbázis nem nyitható meg írásra %s"

#: modules/geo/classes/admin.php:589
msgid "Could not access filesystem"
msgstr "A fájlrendszer nem érhető el"

#: modules/geo/classes/api.php:195
msgid "Cote d'Ivoire"
msgstr "Elefántcsont Part"

#: modules/geo/classes/api.php:194
msgid "Costa Rica"
msgstr "Costa Rica"

#: modules/geo/admin/views/visitor-profile.php:15
#: modules/geo/classes/admin.php:252
msgid "Coordinates"
msgstr "Koordináták"

#: modules/geo/classes/api.php:193
msgid "Cook Islands"
msgstr "Cook Szigetek"

#: modules/geo/classes/api.php:192
msgid "Congo, Republic of"
msgstr "Kongói Köztársaság"

#: modules/geo/classes/api.php:191
msgid "Congo, Democratic People's Republic"
msgstr "Kongói Népi Demokratikus Köztársaság"

#: modules/geo/classes/api.php:190
msgid "Comoros"
msgstr "Komoró"

#: modules/geo/classes/api.php:189
msgid "Colombia"
msgstr "Kolumbia"

#: modules/geo/classes/api.php:188
msgid "Cocos Islands"
msgstr "Kókusz Szigetek"

#: modules/geo/classes/admin.php:335
#: modules/geo/classes/admin.php:388
msgid "City"
msgstr "Város"

#: modules/geo/classes/api.php:187
msgid "Christmas Island"
msgstr "Karácsony Sziget"

#: modules/geo/admin/views/setting-locale.php:15
msgid "Choose the language of the state/region or city entered. If the language is not available in the geo location database, it will check against the English version."
msgstr "Válassza ki a megadott állam/régió vagy város nyelvét. Ha a nyelv nem érhető el a földrajzi helyadatbázisban, akkor az angol verzióval fogja ellenőrizni."

#: modules/geo/classes/api.php:186
msgid "China"
msgstr "Kína"

#: modules/geo/classes/api.php:185
msgid "Chile"
msgstr "Csíle"

#: modules/geo/classes/api.php:184
msgid "Chad"
msgstr "Csád"

#: modules/geo/classes/api.php:183
msgid "Central African Republic"
msgstr "Közép Afrikai Köztársaság"

#: modules/geo/classes/api.php:182
msgid "Cayman Islands"
msgstr "Kajmán Szigetek"

#: modules/geo/classes/api.php:181
msgid "Cape Verde"
msgstr "Zöld Foki Szigetek"

#: modules/geo/classes/admin.php:391
msgid "Cancel"
msgstr "Töröl"

#: modules/geo/classes/api.php:180
msgid "Canada"
msgstr "Kanada"

#: modules/geo/classes/api.php:179
msgid "Cameroon"
msgstr "Kamerún"

#: modules/geo/classes/api.php:178
msgid "Cambodia"
msgstr "Kambodzsa"

#: modules/geo/classes/admin.php:288
msgid "by specific location"
msgstr "adott hely szerint"

#: modules/geo/classes/admin.php:294
msgid "by radius"
msgstr "sugár alapján"

#: modules/geo/classes/api.php:177
msgid "Burundi"
msgstr "Burundi"

#: modules/geo/classes/api.php:176
msgid "Burkina Faso"
msgstr "Burkina Fasszo"

#: modules/geo/classes/api.php:175
msgid "Bulgaria"
msgstr "Bulgária"

#: modules/geo/classes/api.php:174
msgid "Brunei Darrussalam"
msgstr "Bruneji Szultánság"

#: modules/geo/classes/api.php:173
msgid "British Indian Ocean Territory"
msgstr "Britt Indiai Óceáni Terület"

#: modules/geo/classes/api.php:172
msgid "Brazil"
msgstr "Brazília"

#: modules/geo/classes/api.php:171
msgid "Bouvet Island"
msgstr "Bouvet Sziget"

#: modules/geo/classes/api.php:170
msgid "Botswana"
msgstr "Botszvána"

#: modules/geo/classes/api.php:169
msgid "Bosnia and Herzegovina"
msgstr "Bosznia and Hercegovina"

#: modules/geo/classes/api.php:168
msgid "Bonaire, Saint Eustatius and Saba"
msgstr "Bonaire, Szent Eustatius és Szába"

#: modules/geo/classes/api.php:167
msgid "Bolivia"
msgstr "Bolívia"

#: modules/geo/classes/api.php:166
msgid "Bhutan"
msgstr "Bután"

#: modules/geo/classes/api.php:165
msgid "Bermuda"
msgstr "Bermuda"

#: modules/geo/classes/api.php:164
msgid "Benin"
msgstr "Benin"

#: modules/geo/classes/api.php:163
msgid "Belize"
msgstr "Belíz"

#: modules/geo/classes/api.php:162
msgid "Belgium"
msgstr "Belgium"

#: modules/geo/classes/api.php:161
msgid "Belarus"
msgstr "Fehéroroszország"

#: modules/geo/classes/api.php:160
msgid "Barbados"
msgstr "Barbadosz"

#: modules/geo/classes/api.php:159
msgid "Bangladesh"
msgstr "Banglades"

#: modules/geo/classes/api.php:158
msgid "Bahrain"
msgstr "Bahrein"

#: modules/geo/classes/api.php:157
msgid "Bahamas"
msgstr "Bahamák"

#: modules/geo/classes/api.php:156
msgid "Azerbaijan"
msgstr "Azerbajdzsán"

#: modules/geo/classes/api.php:155
msgid "Austria"
msgstr "Ausztria"

#: modules/geo/classes/api.php:154
msgid "Australia"
msgstr "Ausztrália"

#: modules/geo/classes/api.php:138
msgid "Asia"
msgstr "Ázsia"

#: modules/geo/classes/api.php:153
msgid "Aruba"
msgstr "Aruba"

#: modules/geo/classes/api.php:152
msgid "Armenia"
msgstr "Örményország"

#: modules/geo/classes/api.php:151
msgid "Argentina"
msgstr "Argentína"

#: modules/geo/classes/api.php:150
msgid "Antigua and Barbuda"
msgstr "Antigua és Barbuda"

#: modules/geo/classes/api.php:149
msgid "Antarctica"
msgstr "Antarktisz"

#: modules/geo/classes/api.php:148
msgid "Anguilla"
msgstr "Anguilla"

#: modules/geo/classes/api.php:147
msgid "Angola"
msgstr "Angóla"

#: modules/geo/classes/api.php:146
msgid "Andorra"
msgstr "Andorra"

#: modules/geo/classes/api.php:145
msgid "American Samoa"
msgstr "Amerikai Szamoa"

#: modules/geo/classes/api.php:144
msgid "Algeria"
msgstr "Algéria"

#: modules/geo/classes/api.php:143
msgid "Albania"
msgstr "Albánia"

#: modules/geo/classes/api.php:137
msgid "Africa"
msgstr "Afrika"

#: modules/geo/classes/api.php:141
msgid "Afghanistan"
msgstr "Afganisztán"

#. translators: an error message.
#: modules/geo/classes/public.php:227
msgid "Address not found: %s"
msgstr "A cím nem található: %s"

#: modules/group-refresh/views/settings_group_refresh.php:21
#: views/admin/tables/ads/adsense-fallback.php:77
msgid "Activate now"
msgstr "Aktiválás most"

#: modules/geo/classes/admin.php:219
#: modules/geo/classes/public.php:208
#: modules/geo/classes/public.php:372
msgid "(unknown region)"
msgstr "(ismeretlen régió)"

#: modules/geo/classes/admin.php:208
#: modules/geo/classes/public.php:200
#: modules/geo/classes/public.php:368
msgid "(unknown city)"
msgstr "(ismeretlen város)"

#: modules/geo/classes/api.php:142
msgid "&#197;land Islands"
msgstr "&#197;land Islands"

#: modules/lazy-load/admin.class.php:23
#: modules/lazy-load/admin.class.php:64
#: views/admin/placements/bulk-edit.php:27
msgid "Lazy Loading"
msgstr "Lusta töltés"

#: views/setting-placement-positioning.php:29
msgid "This method also works on AMP pages and causes fewer conflicts with website optimization features. However, it can cause critical issues with a few other plugins that use a similar technique (i.e., output buffering). We recommend less technical users test it carefully."
msgstr "Ez a módszer az AMP-oldalakon is működik, és kevesebb ütközést okoz a webhelyoptimalizálási funkciókkal. Ez azonban kritikus problémákat okozhat néhány más beépülő modulnál, amelyek hasonló technikát (azaz kimeneti pufferelést) használnak. A kevésbé műszaki felhasználóknak azt javasoljuk, hogy alaposan teszteljék."

#: classes/advanced-ads-pro-admin.php:148
msgid "Placement positioning"
msgstr "Elhelyezés pozicionálás"

#. translators: %s: Names of placement types
#: views/setting-placement-positioning.php:13
msgid "Choose when Advanced Ads will add the following placement types: %s"
msgstr "Válassza ki, hogy az Advanced Ads mikor adja hozzá a következő elhelyezéstípusokat: %s"

#: views/setting-placement-positioning.php:24
msgid "Before page load using PHP."
msgstr "Az oldal betöltése előtt PHP-vel"

#: views/setting-placement-positioning.php:19
msgid "After page load using JavaScript"
msgstr "Az oldal betöltése után JavaScript-tel"

#: modules/advanced-display-conditions/main.class.php:116
msgid "post content"
msgstr "tartalom közzététele"

#: modules/advanced-display-conditions/main.class.php:117
msgid "Display ads based on words and phrases within the post or page content. Dynamically added text might not be considered."
msgstr "Hirdetések megjelenítése a bejegyzésben vagy az oldal tartalmában szereplő szavak és kifejezések alapján. Előfordulhat, hogy a dinamikusan hozzáadott szöveget a rendszer nem veszi figyelembe."

#: modules/gamipress/views/visitor-condition-rank.php:28
msgid "Choose rank"
msgstr "Válasszon rangot"

#: modules/gamipress/views/visitor-condition-points.php:17
msgid "Choose Points Type"
msgstr "Válassza a Pontok típusát"

#: modules/gamipress/views/visitor-condition-achievement.php:21
msgid "Choose Achievement Type"
msgstr "Válasszon eredmény típust"

#: modules/gamipress/admin.class.php:34
msgid "Display ads based on GamiPress user achievements."
msgstr "Hirdetések megjelenítése a GamiPress felhasználói elért eredményei alapján."

#: modules/gamipress/admin.class.php:33
msgid "GamiPress Achievement"
msgstr "GamiPress Eredmény"

#: modules/gamipress/admin.class.php:28
msgid "Display ads based on GamiPress user ranks."
msgstr "Hirdetések megjelenítése a GamiPress felhasználói rangok alapján."

#: modules/gamipress/admin.class.php:27
msgid "GamiPress Rank"
msgstr "GamiPress Rang"

#: modules/gamipress/admin.class.php:22
msgid "Display ads based on GamiPress user points."
msgstr "Hirdetések megjelenítése a GamiPress felhasználói pontok alapján."

#: modules/gamipress/admin.class.php:21
msgid "GamiPress Points"
msgstr "GamiPress Pontok"

#: views/privacy-policy-content.php:10
msgid "You can use the text below as a template for your own privacy policy."
msgstr "Az alábbi szöveget sablonként használhatja saját adatvédelmi szabályzatához."

#: views/privacy-policy-content.php:13
msgid "This website uses Advanced Ads Pro to place advertisements. The WordPress plugin may use multiple first-party cookies to ensure the correct integration of ads. These cookies store technical information but not IP addresses. Their use is linked to specific features and options when embedding ads."
msgstr "Ez a webhely Advanced Ads Pro programot használ hirdetések elhelyezésére. A WordPress beépülő modul több belső cookie-t is használhat a hirdetések megfelelő integrációjának biztosítása érdekében. Ezek a sütik technikai információkat tárolnak, IP-címeket nem. Használatuk meghatározott funkciókhoz és lehetőségekhez kapcsolódik a hirdetések beágyazásakor."

#: views/privacy-policy-content.php:12
msgid "Suggested Text:"
msgstr "Javasolt szöveg:"

#. translators: %1$s is an opening a tag, %2$s is the corresponding closing one
#: views/privacy-policy-content.php:19
msgid "Please, see the %1$sAdvanced Ads cookie information%2$s for more details."
msgstr "További részletekért tekintse meg a %1$sAdvanced Ads cookie-információit%2$s."

#: views/privacy-policy-content.php:7
msgid "Depending on the setup, Advanced Ads Pro and other add-ons use cookies to control which user sees which ad. They also help to reduce expensive server requests."
msgstr "A beállítástól függően az Advanced Ads Pro és más kiegészítők cookie-kat használnak annak szabályozására, hogy melyik felhasználó melyik hirdetést lássa. Segítenek a drága szerverkérések csökkentésében is."

#: classes/advanced-ads-pro-admin.php:531
msgid "Select a Sidebar placement to enable cache-busting."
msgstr "Válasszon egy oldalsáv elhelyezést a gyorsítótár-törlés engedélyezéséhez."

#: modules/buddypress/views/position-option.php:56
msgid "member timeline"
msgstr "tag idővonala"

#: classes/advanced-ads-pro-admin.php:533
msgid "Learn more"
msgstr "Tudj meg többet"

#: modules/buddypress/views/position-option.php:55
msgid "group feed"
msgstr "csoport feed"

#: modules/buddypress/class-buddypress.php:334
msgid "BuddyBoss group"
msgstr "BuddyBoss csoport"

#: modules/buddypress/views/position-option.php:53
msgid "any"
msgstr "bármelyik"

#: modules/buddypress/views/position-option.php:54
msgid "activity stream"
msgstr "tevékenységfolyam"

#: modules/cache-busting/views/settings.php:38
msgid "Force passive cache busting"
msgstr "Passzív gyorsítótár-letörés kényszerítése"

#: modules/cache-busting/views/settings.php:61
msgid "Fallback option"
msgstr "Tartalék lehetőség"

#: modules/cache-busting/views/settings.php:17
msgid "Default option"
msgstr "alapértelmezett beállítás"

#: modules/buddypress/class-buddypress.php:131
msgid "Display ads based on BuddyBoss profile fields."
msgstr "Hirdetések megjelenítése a BuddyBoss profilmezők alapján."

#: modules/buddypress/class-buddypress.php:130
msgid "BuddyBoss profile field"
msgstr "BuddyBoss profilmező"

#: modules/buddypress/views/position-option.php:51
msgid "Stream"
msgstr "Folyam"

#: includes/placements/types/class-buddypress.php:61
msgid "Display ads on BuddyBoss related pages."
msgstr "Hirdetések megjelenítése a BuddyBosshoz kapcsolódó oldalakon."

#: includes/placements/types/class-buddypress.php:49
msgid "BuddyBoss Content"
msgstr "BuddyBoss Tartalom"

#: modules/cache-busting/views/settings.php:42
msgid "By default, cache busting only works through placements."
msgstr "Alapértelmezés szerint a gyorsítótár-törlés csak elhelyezéseken keresztül működik."

#: modules/cache-busting/views/settings.php:90
msgid "Update visitor profile"
msgstr "Látogatói profil frissítése"

#: modules/ad-server/views/placement-usage.php:14
msgid "Direct URL"
msgstr "Közvetlen URL"

#: modules/ad-server/views/placement-settings.php:11
msgid "The name of the placement that appears in the URL and injection code."
msgstr "Az URL-ben és a beillesztési kódban megjelenő elhelyezés neve."

#: modules/ad-server/views/placement-settings.php:10
msgid "Save the page to update the usage code below."
msgstr "Mentse el az oldalt az alábbi használati kód frissítéséhez."

#: modules/ad-server/views/module-settings.php:30
msgid "Prevent direct access to the placement URL."
msgstr "Akadályozza meg az elhelyezés URL-jéhez való közvetlen hozzáférést."

#: modules/ad-server/views/module-settings.php:24
msgid "Please don’t enter subdirectories."
msgstr "Kérjük, ne írjon be alkönyvtárakat."

#: modules/ad-server/views/module-settings.php:18
msgid "Separate multiple values with a comma."
msgstr "A több értéket vesszővel válassza el."

#: modules/ad-server/views/module-settings.php:18
msgid "Top level domains on which the ads will be loaded."
msgstr "Legfelső szintű domainek, amelyekre a hirdetések betöltődnek."

#: modules/ad-server/views/module-settings.php:13
#: modules/ads-for-adblockers/views/settings.php:18
#: modules/advanced-visitor-conditions/views/settings.php:6
#: modules/cache-busting/views/settings.php:12
#: modules/click-fraud-protection/views/settings.php:10
#: modules/lazy-load/views/settings.php:8
#: modules/rest-api/views/module-enable.php:12
msgid "Activate module."
msgstr "Modul aktiválása."

#: modules/ad-server/admin.class.php:92
msgid "Usage"
msgstr "Használat"

#: modules/ad-server/admin.class.php:78
msgid "Public string"
msgstr "Nyilvános karakterlánc"

#: includes/placements/types/class-server.php:56
msgid "Display ads on external websites."
msgstr "Hirdetések megjelenítése külső webhelyeken."

#: includes/placements/types/class-server.php:47
#: modules/ad-server/admin.class.php:37
msgid "Ad Server"
msgstr "Hirdetés szerver"

#: modules/advanced-visitor-conditions/main.class.php:149
msgid " Display ads based on the user’s capabilities."
msgstr "Hirdetések megjelenítése a felhasználó lehetőségei alapján."

#: modules/advanced-visitor-conditions/main.class.php:141
msgid "Display ads based on the user agent."
msgstr "Hirdetések megjelenítése a felhasználói ügynök alapján."

#: modules/inject-content/admin.class.php:255
msgid "Words between ads"
msgstr "Szavak a hirdetések között"

#: views/setting_words_between_ads.php:6
msgid "Words are counted within paragraphs, headlines and any other element."
msgstr "A szavak a bekezdéseken, címsorokon és bármely más elemen belül számítanak."

#: views/setting_words_between_ads.php:5
msgid "A minimum amount of words between automatically injected ads."
msgstr "Minimális szómennyiség az automatikusan beszúrt hirdetések között."

#: includes/admin/class-duplicate-placement.php:64
#: includes/admin/class-group-duplication.php:115
#: modules/duplicate-ads/admin.class.php:138
msgctxt "noun"
msgid "copy"
msgstr "másolat"

#: modules/inject-content/admin.class.php:124
msgid "Minimum length of content (in words) before automatically injected ads are allowed in them. Set to zero or leave empty to always display ads, regardless of how long the content is."
msgstr "A tartalom minimális hossza (szavakban) az automatikusan beszúrt hirdetések előtt bennük van engedélyezve. Állítsa nullára, vagy hagyja üresen, hogy a tartalom hosszúságától függetlenül, mindig megjelenjenek a hirdetések,."

#: includes/admin/class-duplicate-placement.php:102
#: includes/admin/class-group-duplication.php:78
#: modules/duplicate-ads/admin.class.php:88
msgid "Duplicate"
msgstr "Másolat"

#: modules/duplicate-ads/admin.class.php:88
msgid "Create a copy of this ad"
msgstr "Készítsen másolatot erről a hirdetésről"

#: modules/inject-content/views/setting_post_meta_box.php:4
msgid "Disable automatic ad injection into the content"
msgstr "Kapcsolja ki az automatikus hirdetésbeszúrást a tartalomba"

#: modules/cache-busting/views/settings.php:102
msgid "Update visitor profile when user logs in or out"
msgstr "Frissítse a látogatói profilt, amikor a felhasználó be-, vagy kijelentkezik"

#: modules/cache-busting/views/settings.php:92
msgid "You might need to update the page cache if you are using one."
msgstr "Lehetséges, hogy frissítenie kell az oldal gyorsítótárát, ha használ ilyet ."

#: modules/cache-busting/views/settings.php:86
msgid "Advanced Ads stores some user information in the user’s browser to limit the number of AJAX requests for cache busting. Manual"
msgstr "Az Advanced Ads bizonyos felhasználói információkat tárol a felhasználó böngészőjében, hogy korlátozza a gyorsítótár-leállításra vonatkozó AJAX-kérések számát. Kézikönyv"

#: modules/cache-busting/views/settings.php:83
msgid "Visitor profile"
msgstr "Látogatói profil"

#. translators: %s: URL
#: modules/advanced-display-conditions/main.class.php:41
msgid "Display ads based on the current URL parameters (everything following %s), except values following #."
msgstr "Hirdetések megjelenítése az aktuális URL-paraméterek alapján (minden, ami a %s után következik), kivéve a # utáni értékeket."

#: classes/advanced-ads-pro-admin.php:156
msgid "Disable ads for post types"
msgstr "Hirdetések letiltása bejegyzéstípusoknál"

#: modules/cache-busting/views/settings.php:94
msgid "An error occurred"
msgstr "Hiba történt"

#: modules/cache-busting/views/settings.php:91
msgid "Updated"
msgstr "Frissítve"

#. translators: %s is a placeholder for the URL.
#: modules/advanced-visitor-conditions/main.class.php:163
msgid "Display ads based on the user's roles. See <a href=\"%s\" target=\"_blank\">List of roles in WordPress</a>."
msgstr ""

#: modules/advanced-visitor-conditions/main.class.php:160
msgid "user role"
msgstr "felhasználói szerepkör"

#. translators: %s is a list of supported optimizer plugins
#: views/setting_autoptimize.php:7
msgid "Advanced Ads Pro disables optimizers ( %s ) for displaying ads per default. Enable this option to allow optimizers to change the ad code. Especially JavaScript ads might stop working then."
msgstr "Az Advanced Ads Pro letiltja az optimalizálókat ( %s ) a hirdetések alapértelmezett megjelenítéséhez. Engedélyezze ezt a lehetőséget, hogy az optimalizálók módosítsák a hirdetési kódot. Főleg a JavaScript-hirdetések állhatnak le ekkor."

#: classes/advanced-ads-pro-admin.php:139
msgid "Allow optimizers to modify ad codes"
msgstr "Engedélyezze az optimalizálóknak a hirdetési kódok módosítását"

#. translators: %s: input field
#: modules/lazy-load/views/settings.php:34
msgid "Start loading the ads %s pixels before they are visible on the screen."
msgstr "Kezdje el betölteni a hirdetéseket %s pixeleket, mielőtt azok megjelennének a képernyőn."

#: modules/grids/admin.class.php:61
msgid "Set to 0 to disable this feature."
msgstr "Állítsa 0-ra a funkció letiltásához."

#: modules/grids/admin.class.php:60
msgid "On browsers smaller than this, the columns show in full width below each other."
msgstr "Az ennél kisebb böngészőkben az oszlopok teljes szélességben jelennek meg egymás alatt."

#: modules/grids/admin.class.php:58
msgid "Full width"
msgstr "Teljes szélesség"

#. translators: "profile fields" relates to BuddyPress profile fields
#: modules/buddypress/views/xprofile-condition.php:58
msgid "No profile fields found"
msgstr "Nem található profilmező"

#: modules/buddypress/class-buddypress.php:118
msgid "Display ads based on BuddyPress profile fields."
msgstr "Hirdetések megjelenítése a BuddyPress profilmezők alapján."

#: modules/buddypress/class-buddypress.php:117
msgid "BuddyPress profile field"
msgstr "BuddyPress profilmező"

#: modules/buddypress/views/position-option.php:44
#: views/setting_repeat.php:4
msgid "repeat the position"
msgstr "ismételje meg a pozíciót"

#: views/setting_custom_code.php:34
msgid "Displayed after the ad content"
msgstr "A hirdetés tartalma után jelenik meg"

#: views/setting_custom_code.php:30
msgid "place your own code below the ad"
msgstr "helyezze el saját kódját a hirdetés alá"

#: views/setting_custom_code.php:27
msgid "custom code"
msgstr "egyéni kód"

#. Author URI of the plugin
#: advanced-ads-pro.php
msgid "https://wpadvancedads.com"
msgstr "https://wpadvancedads.com"

#. Plugin URI of the plugin
#: advanced-ads-pro.php
msgid "https://wpadvancedads.com/add-ons/advanced-ads-pro/"
msgstr "https://wpadvancedads.com/add-ons/advanced-ads-pro/"

#. Description of the plugin
#: advanced-ads-pro.php
msgid "Advanced features to boost your ad revenue."
msgstr "Speciális funkciók a hirdetési bevételek növeléséhez."

#. Plugin Name of the plugin
#: advanced-ads-pro.php
msgid "Advanced Ads Pro"
msgstr "Advanced Ads Pro"

#: modules/weekdays/views/ad-submitbox-meta.php:19
msgid "Set specific days"
msgstr "Különleges napok beállítása"

#. translators: 1: month, 2: day, 3: year, 4: hour, 5: minute
#: modules/placement-tests/views/settings_test_expiry_date.php:31
msgctxt "order of expiry date fields 1: month, 2: day, 3: year, 4: hour, 5: minute"
msgid "%1$s %2$s, %3$s @ %4$s %5$s"
msgstr "%1$s %2$s, %3$s @ %4$s %5$s"

#: modules/placement-tests/views/settings_test_expiry_date.php:25
msgid "Minute"
msgstr "Perc"

#: modules/placement-tests/views/settings_test_expiry_date.php:24
msgid "Hour"
msgstr "Óra"

#: modules/placement-tests/views/settings_test_expiry_date.php:23
msgid "Year"
msgstr "Év"

#: modules/placement-tests/views/settings_test_expiry_date.php:22
msgid "Day"
msgstr "Nap"

#. translators: 1: month number (01, 02, etc.), 2: month abbreviation
#: modules/placement-tests/views/settings_test_expiry_date.php:17
msgctxt "1: month number (01, 02, etc.), 2: month abbreviation"
msgid "%1$s-%2$s"
msgstr "%1$s-%2$s"

#: modules/placement-tests/views/settings_test_expiry_date.php:12
msgid "Month"
msgstr "Hónap"

#: modules/placement-tests/views/settings_test_expiry_date.php:5
msgctxt "placement tests"
msgid "Send email after"
msgstr "E-mail küldése utána"

#: modules/placement-tests/placement-tests.class.php:237
#: modules/placement-tests/views/setting_placement_test_weight.php:17
msgid "Test weight"
msgstr "Súly teszt"

#: modules/placement-tests/views/setting_placement_test_weight.php:15
msgctxt "placement tests"
msgid "Testing"
msgstr "Tesztelés"

#: modules/placement-tests/views/placement-tests.php:66
msgid "Save Tests"
msgstr "Tesztek mentése"

#: modules/placement-tests/views/placement-tests.php:59
msgctxt "checkbox to remove placement test"
msgid "delete"
msgstr "törlés"

#: modules/placement-tests/views/placement-tests.php:53
msgctxt "placement tests"
msgid "empty"
msgstr "üres"

#: modules/placement-tests/views/placement-tests.php:18
msgid "Placements"
msgstr "Elhelyezések"

#: modules/placement-tests/views/placement-tests.php:17
msgid "Expiry date"
msgstr "Lejárati dátum"

#: modules/placement-tests/views/placement-tests.php:16
msgid "Author"
msgstr "Szerző"

#: modules/placement-tests/views/placement-tests.php:11
msgid "Placement tests"
msgstr "Elhelyezés tesztek"

#: modules/lazy-load/views/setting_lazy_load.php:6
#: modules/lazy-load/views/setting_lazy_load.php:8
msgid "disabled"
msgstr "tiltva"

#: modules/lazy-load/views/setting_lazy_load.php:2
#: modules/lazy-load/views/setting_lazy_load.php:4
msgid "enabled"
msgstr "engedélyezve"

#: modules/group-refresh/views/settings_group_refresh.php:27
msgid "Please use a placement to deliver this group using cache-busting."
msgstr "Kérjük, használjon elhelyezést ennek a csoportnak a gyorsítótár-törlés használatával történő megjelenítéséhez."

#: modules/group-refresh/views/settings_group_refresh.php:19
msgid "Refresh ads on the same spot. Works when cache-busting is used."
msgstr "Frissítse a hirdetéseket ugyanazon a helyen. Gyorsítótár-törlés használata esetén működik."

#: modules/group-refresh/views/settings_group_refresh.php:16
msgid "milliseconds"
msgstr "milliszekundum"

#: modules/group-refresh/views/settings_group_refresh.php:14
#: views/admin/placements/bulk-edit.php:30
#: views/admin/placements/bulk-edit.php:40
#: views/admin/tables/ads/column-displayonce.php:10
msgid "Enabled"
msgstr "Engedélyezve"

#: modules/grids/views/grid-option-size.php:20
msgid "rows"
msgstr "sorok"

#: modules/grids/views/grid-option-size.php:16
msgid "columns"
msgstr "oszlopok"

#: modules/click-fraud-protection/views/settings.php:23
msgid "Period for which to hide the ad ( in days )"
msgstr "A hirdetés elrejtésének időtartama ( napokban )"

#: modules/click-fraud-protection/views/settings.php:20
msgid "Period in which the click limit should be reached ( in hours )"
msgstr "Időszak, amely alatt a kattintási korlátot el kell érni ( órákban )"

#: modules/click-fraud-protection/views/settings.php:17
msgid "Allowed clicks on a single ad before it is removed"
msgstr "Engedélyezett kattintások egyetlen hirdetésre az eltávolítás előtt"

#. translators: Link to parallax manual
#: modules/ad-server/views/module-settings.php:15
#: modules/ads-for-adblockers/views/settings.php:20
#: modules/advanced-display-conditions/main.class.php:508
#: modules/advanced-display-conditions/views/metabox-string.php:12
#: modules/advanced-visitor-conditions/main.class.php:309
#: modules/advanced-visitor-conditions/main.class.php:408
#: modules/advanced-visitor-conditions/main.class.php:465
#: modules/advanced-visitor-conditions/main.class.php:503
#: modules/buddypress/class-buddypress.php:378
#: modules/buddypress/views/xprofile-condition.php:74
#: modules/cache-busting/cache-busting.class.php:263
#: modules/cache-busting/views/settings.php:14
#: modules/click-fraud-protection/click-fraud-protection.class.php:150
#: modules/click-fraud-protection/click-fraud-protection.class.php:158
#: modules/click-fraud-protection/views/settings.php:12
#: modules/gamipress/views/visitor-condition-achievement.php:34
#: modules/gamipress/views/visitor-condition-points.php:41
#: modules/gamipress/views/visitor-condition-rank.php:41
#: modules/geo/admin/views/setting-maxmind-license-key.php:22
#: modules/geo/classes/admin.php:400
#: modules/group-refresh/views/settings_group_refresh.php:23
#: modules/lazy-load/views/settings.php:10
#: modules/paid-memberships-pro/main.class.php:140
#: modules/paid-memberships-pro/main.class.php:259
#: modules/parallax-ads/views/placement-options-after.php:24
#: modules/responsive-ads/views/setting_fallback_width.php:16
#: modules/rest-api/views/module-enable.php:15
msgid "Manual"
msgstr "Kézikönyv"

#. translators: %s link to manual
#: modules/cache-busting/views/settings_check_ad.php:16
msgid "The code of this ad might not work properly with activated cache-busting. <a href=\"%s\" target=\"_blank\">Manual</a>"
msgstr "Előfordulhat, hogy ennek a hirdetésnek a kódja nem működik megfelelően, ha a gyorsítótár-törlés aktív. <a href=\"%s\" target=\"_blank\">Kézikönyv</a>"

#: modules/cache-busting/views/settings.php:44
msgid "Enable passive cache busting for all ads and groups which are not delivered through a placement, if possible."
msgstr "Ha lehetséges, engedélyezze a passzív gyorsítótár törlést minden olyan hirdetésnél és csoportnál, amely nem egy elhelyezésen keresztül jelenik meg."

#: modules/cache-busting/views/settings.php:62
msgid "Choose the fallback if “passive“ cache busting is not possible."
msgstr "Válassza a tartalékot, ha a „passzív” gyorsítótár törlés nem lehetséges."

#: modules/cache-busting/admin-ui.class.php:87
#: modules/cache-busting/views/settings.php:31
#: modules/cache-busting/views/settings.php:68
#: views/admin/placements/bulk-edit.php:20
msgid "AJAX"
msgstr "AJAX"

#: modules/advanced-display-conditions/views/referrer-url-metabox.php:15
#: modules/advanced-visitor-conditions/views/referrer-url-metabox.php:15
msgid "url of the referrer"
msgstr "a hivatkozó url-je"

#: modules/advanced-display-conditions/views/referrer-url-metabox.php:5
#: modules/advanced-visitor-conditions/views/referrer-url-metabox.php:5
msgid "URL"
msgstr "URL"

#: modules/advanced-display-conditions/views/referrer-url-metabox.php:3
#: modules/advanced-visitor-conditions/views/referrer-url-metabox.php:3
msgid "Display ad depending on the external url the user comes from."
msgstr "Hirdetés megjelenítése attól függően, hogy a felhasználó milyen külső URL-címről érkezik."

#: modules/advanced-display-conditions/views/referrer-url-metabox.php:1
#: modules/advanced-visitor-conditions/views/referrer-url-metabox.php:1
msgid "Display by referrer url"
msgstr "Megjelenítés hivatkozó url-je alapján"

#: modules/ads-for-adblockers/views/placement-item.php:46
#: modules/lazy-load/views/setting_lazy_load.php:11
msgid "Works only with cache-busting enabled"
msgstr "Csak akkor működik, ha a gyorsítótár törlés engedélyezve van"

#: modules/weekdays/class-advanced-ads-pro-weekdays.php:171
msgid "Never shows up"
msgstr "Soha nem jelenik meg"

#: modules/weekdays/class-advanced-ads-pro-weekdays.php:90
#: modules/weekdays/class-advanced-ads-pro-weekdays.php:107
msgid "specific days"
msgstr "meghatározott napok"

#. translators: %s: placement test name
#: modules/placement-tests/placement-tests.class.php:702
msgid "Placement test <em>%s</em> created"
msgstr "<em>%s</em> elhelyezési teszt létrehozva"

#: modules/placement-tests/placement-tests.class.php:564
msgctxt "placement tests"
msgid "Placement page"
msgstr "Elhelyezés oldal"

#: modules/placement-tests/placement-tests.class.php:558
#: modules/placement-tests/placement-tests.class.php:700
#: modules/placement-tests/views/placement-tests.php:50
msgctxt "placement tests"
msgid " vs "
msgstr " vs "

#: modules/placement-tests/placement-tests.class.php:530
msgctxt "placement tests"
msgid "Expired placement tests"
msgstr "Lejárt elhelyezési tesztek"

#: modules/placement-tests/views/setting_placement_test_weight.php:28
msgid "Save new test"
msgstr ""

#: modules/paid-memberships-pro/main.class.php:157
msgid "Display ads only to users with a specific membership level set with PaidMembershipsPro."
msgstr "Csak a PaidMembershipsPro által beállított tagsági szinttel rendelkező felhasználók számára jelenítsen meg hirdetéseket."

#: modules/paid-memberships-pro/main.class.php:156
msgid "PMP user level"
msgstr "PMP felhasználói szint"

#: modules/paid-memberships-pro/main.class.php:133
#: modules/paid-memberships-pro/main.class.php:252
msgid "No membership levels set up yet."
msgstr "Még nincsenek beállítva tagsági szintek."

#: modules/paid-memberships-pro/main.class.php:39
msgid "Display ads only on pages that require a specific membership level set of PaidMembershipsPro."
msgstr "Csak azokon az oldalakon jelenítsen meg hirdetéseket, amelyekhez a PaidMembershipsPro meghatározott tagsági szintje szükséges."

#: modules/paid-memberships-pro/main.class.php:38
msgid "PMP page level"
msgstr "PMP oldal szint"

#: includes/placements/types/class-archive-pages.php:56
msgid "Display the ad between posts on post lists, e.g. home, archives, search etc."
msgstr "Jelenítse meg a hirdetést a bejegyzések között a bejegyzéslistákon, pl. otthon, archívum, keresés stb."

#: includes/placements/types/class-archive-pages.php:47
msgid "Post Lists"
msgstr "Hozzászólási listák"

#: includes/placements/types/class-custom-position.php:57
msgid "Attach the ad to any element in the frontend."
msgstr "Csatolja a hirdetést a kezelőfelület bármely eleméhez."

#: includes/placements/types/class-custom-position.php:48
msgid "Custom Position"
msgstr "Egyedi pozíció"

#: includes/placements/types/class-content-middle.php:56
msgid "In the middle of the main content based on the number of paragraphs."
msgstr "A fő tartalom közepén a bekezdések száma alapján."

#: includes/placements/types/class-content-middle.php:47
msgid "Content Middle"
msgstr "Tartalom középre"

#: includes/placements/types/class-above-headline.php:57
msgid "Above the main headline on the page (&lt;h1&gt;)."
msgstr "Az oldal fő címsora felett (&lt;h1&gt;)."

#: includes/placements/types/class-above-headline.php:48
msgid "Above Headline"
msgstr "A címsor felett"

#: includes/placements/types/class-content-random.php:56
msgid "After a random paragraph in the main content."
msgstr "A fő tartalom véletlenszerű bekezdése után."

#: includes/placements/types/class-content-random.php:47
msgid "Random Paragraph"
msgstr "Véletlen bekezdés"

#: modules/inject-content/admin.class.php:155
msgid "secondary loops"
msgstr "másodlagos ismétlések"

#: modules/inject-content/admin.class.php:151
msgid "Only enable this option if you are sure what you are doing!"
msgstr "Csak akkor engedélyezze ezt az opciót, ha biztos abban, hogy mit csinál!"

#: modules/inject-content/admin.class.php:150
msgid "Allow injection into any custom and secondary queries."
msgstr "Engedélyezze az beillesztést bármilyen egyéni és másodlagos lekérdezésbe."

#: modules/inject-content/admin.class.php:128
msgid "minimum content length"
msgstr "minimális tartalomhossz"

#: modules/inject-content/admin.class.php:93
msgid "Before which post to inject the ad on post lists."
msgstr "Melyik bejegyzés előtt helyezze el a hirdetést a bejegyzési listákon."

#. translators: %s: index of the post
#: modules/inject-content/admin.class.php:91
msgid "Inject before %s. post"
msgstr "Beillesztés a %s hozzászólás előtt."

#: modules/inject-content/admin.class.php:69
msgid "Place the following element where the ad should be displayed."
msgstr "Helyezze a következő elemet oda, ahol a hirdetésnek meg kell jelennie."

#: modules/inject-content/admin.class.php:68
msgid "by new element"
msgstr "új elem által"

#: modules/bbpress/class-admin.php:64
#: modules/bbpress/class-admin.php:87
#: modules/bbpress/class-admin.php:130
#: modules/inject-content/admin.class.php:58
msgid "Position"
msgstr "Pozíció"

#: modules/inject-content/admin.class.php:57
msgid "Uses <a href=\"https://api.jquery.com/category/selectors/\" target=\"_blank\">jQuery selectors</a>, e.g. #container_id, .container_class"
msgstr "<a href=\"https://api.jquery.com/category/selectors/\" target=\"_blank\">jQuery-választókat</a> használ, pl. #container_id, .container_class"

#: modules/inject-content/admin.class.php:54
msgid "or enter manually"
msgstr "vagy írja be kézzel"

#: modules/inject-content/admin.class.php:56
msgid "select position"
msgstr "pozíció kiválasztása"

#: modules/inject-content/admin.class.php:55
msgctxt "frontend picker"
msgid "stop selection"
msgstr "állítsa le a kiválasztást"

#: modules/inject-content/admin.class.php:51
msgid "Place ads in relation to an existing element in the frontend."
msgstr "Hirdetések elhelyezése egy a kezelőfelületen meglévő elemhez kapcsolva."

#: modules/inject-content/admin.class.php:49
msgid "by existing element"
msgstr "meglévő elem alapján"

#: modules/inject-content/admin.class.php:40
msgid "below"
msgstr "alá"

#: modules/inject-content/admin.class.php:39
msgid "inside, after other content"
msgstr "belül, más tartalom után"

#: modules/inject-content/admin.class.php:38
msgid "inside, before other content"
msgstr "belül, más tartalom előtt"

#: modules/inject-content/admin.class.php:37
msgid "above"
msgstr "fölé"

#: modules/group-refresh/admin.class.php:60
msgid "Refresh interval"
msgstr "Frissítési intervallum"

#: modules/grids/admin.class.php:73
msgid "Random order"
msgstr "Véletlen sorrend"

#: modules/grids/admin.class.php:53
msgid "Minimum width of a column in the grid."
msgstr "Egy oszlop minimális szélessége a rácsban."

#: modules/grids/admin.class.php:51
msgid "Min. width"
msgstr "Min. szélesség"

#: modules/grids/admin.class.php:45
msgid "Inner margin"
msgstr "Belső margó"

#: modules/grids/admin.class.php:36
msgid "Size"
msgstr "Méret"

#: modules/click-fraud-protection/click-fraud-protection.class.php:320
msgid "Click Fraud Protection enabled"
msgstr "Kattintás-csalás védelem engedélyezve"

#: modules/click-fraud-protection/click-fraud-protection.class.php:145
msgid "hours"
msgstr "óra"

#: modules/click-fraud-protection/click-fraud-protection.class.php:143
msgid "within"
msgstr "belül"

#: modules/click-fraud-protection/click-fraud-protection.class.php:111
msgid "Display ad only if a click limit has not been reached."
msgstr "Csak akkor jelenítsen meg egy hirdetést, ha az a kattintási korlátot nem érte el."

#: modules/click-fraud-protection/click-fraud-protection.class.php:110
msgid "max. ad clicks"
msgstr "max. hirdetés kattintás"

#: modules/click-fraud-protection/admin.class.php:29
msgid "Click Fraud Protection"
msgstr "Kattintás csalás védelem"

#: modules/cache-busting/cache-busting.class.php:1913
msgctxt "placement admin label"
msgid "Cache-busting:"
msgstr "Gyrosítótár törlés:"

#: modules/cache-busting/cache-busting.class.php:1907
msgid "The ad is not displayed on the page"
msgstr "A hirdetés nem jelenik meg az oldalon"

#: modules/cache-busting/cache-busting.class.php:1906
msgid "The ad is displayed on the page"
msgstr "A hirdetés megjelenik az oldalon"

#: modules/cache-busting/cache-busting.class.php:1903
#: modules/cache-busting/views/settings.php:24
msgid "passive"
msgstr "passzív"

#: modules/cache-busting/cache-busting.class.php:1897
msgid "The ad can work with passive cache-busting"
msgstr "A hirdetés működhet passzív gyorsítótár-törlés mellett"

#: modules/cache-busting/cache-busting.class.php:1895
msgid "The ad can not work with passive cache-busting"
msgstr "A hirdetés nem működik passzív gyorsítótár-törlés mellett"

#: modules/ads-for-adblockers/views/settings.php:27
#: modules/cache-busting/admin.class.php:29
#: views/admin/placements/bulk-edit.php:16
msgid "Cache Busting"
msgstr "Gyorsítótá törlés"

#: modules/cache-busting/admin-ui.class.php:150
msgid "The <em>Ad Group</em> ad type can only use AJAX or no cache-busting, but not passive cache-busting."
msgstr "A <em>Hirdetéscsoport</em> hirdetéstípus csak AJAX-ot használhat, vagy nem használhat gyorsítótár-törlést, de nem passzív gyorsítótár-törlést."

#: modules/cache-busting/admin-ui.class.php:107
msgctxt "placement admin label"
msgid "Cache-busting"
msgstr "Gyorsítótár törlés"

#: modules/cache-busting/cache-busting.class.php:1910
msgctxt "setting label"
msgid "off"
msgstr "ki"

#: modules/cache-busting/cache-busting.class.php:1901
msgctxt "setting label"
msgid "ajax"
msgstr "ajax"

#: modules/buddypress/class-admin.php:82
msgid "Member List"
msgstr "Tag lista"

#: modules/buddypress/class-admin.php:79
msgid "Group List"
msgstr "Csoport lista"

#: modules/buddypress/class-admin.php:63
#: modules/buddypress/class-admin.php:71
msgid "Activity Entry"
msgstr "Tevékenység bejegyzés"

#. translators: %s is an HTML input element.
#: modules/buddypress/views/position-option.php:37
msgid "Inject after %s. entry"
msgstr "Beillesztés a %s bejegyzés után."

#: includes/placements/types/class-buddypress.php:63
msgid "Display ads on BuddyPress related pages."
msgstr "Hirdetések megjelenítése a BuddyPress-hez kapcsolódó oldalakon."

#: includes/placements/types/class-buddypress.php:51
msgid "BuddyPress Content"
msgstr "BuddyPress tartalom"

#: modules/bbpress/class-admin.php:150
msgid "forums page"
msgstr "fórum oldal"

#: modules/bbpress/class-admin.php:146
msgid "single forum page"
msgstr "egyetlen fórumoldal"

#: modules/bbpress/class-admin.php:142
#: modules/bbpress/class-admin.php:164
msgid "forum topic page"
msgstr "fórum témaoldal"

#: modules/buddypress/views/position-option.php:20
#: modules/inject-content/admin.class.php:80
#: modules/inject-content/admin.class.php:96
msgid "position"
msgstr "pozíció"

#: includes/placements/types/class-bbpress-comment.php:56
msgid "Display ads in bbPress replies."
msgstr "Hirdetések megjelenítése a bbPress válaszokban."

#: includes/placements/types/class-bbpress-comment.php:47
msgid "bbPress Reply Content"
msgstr "bbPress Válasz tartalom"

#: includes/placements/types/class-bbpress-static.php:56
msgid "Display ads on bbPress related pages."
msgstr "Hirdetések megjelenítése a bbPress-hez kapcsolódó oldalakon."

#: includes/placements/types/class-bbpress-static.php:47
msgid "bbPress Static Content"
msgstr "bbPress Statikus tartalom"

#: modules/background-ads/admin.class.php:33
msgid "background"
msgstr "háttér"

#: modules/background-ads/admin.class.php:30
msgid "Select a background color in case the background image is not high enough to cover the whole screen."
msgstr "Válasszon egy háttérszínt arra az esetre, ha a háttérkép nem elég magas ahhoz, hogy lefedje a teljes képernyőt."

#: includes/placements/types/class-background-ad.php:56
msgid "Background of the website behind the main wrapper."
msgstr "A webhely háttere a fő befoglaló mögött."

#: includes/placements/types/class-background-ad.php:47
msgid "Background Ad"
msgstr "Háttér hirdetés"

#. translators: %s is a placeholder for the impressions field
#: modules/advanced-visitor-conditions/main.class.php:499
msgid "within %s seconds"
msgstr "%s másodpercen belül"

#: modules/advanced-visitor-conditions/main.class.php:449
msgid "Cookie Value"
msgstr "Cookie-érték"

#: modules/advanced-visitor-conditions/main.class.php:448
msgid "Cookie Name"
msgstr "Cookie-név"

#: modules/advanced-visitor-conditions/main.class.php:301
#: modules/advanced-visitor-conditions/main.class.php:349
#: modules/advanced-visitor-conditions/main.class.php:398
msgid "-- choose one --"
msgstr "-- válasszon --"

#: modules/advanced-visitor-conditions/main.class.php:298
msgid "can not"
msgstr "nem lehet"

#: modules/advanced-visitor-conditions/main.class.php:297
msgid "can"
msgstr "lehetséges"

#: modules/advanced-visitor-conditions/main.class.php:202
msgid "new visitor"
msgstr "új látogató"

#: modules/advanced-visitor-conditions/main.class.php:196
msgid "Display the ad only for a few impressions in a given period per user."
msgstr "A hirdetést felhasználónként csak néhány megjelenítésre jelenítse meg egy adott időszakban."

#: modules/advanced-visitor-conditions/main.class.php:195
msgid "max. ad impressions"
msgstr "max. hirdetésmegjelenítés"

#: modules/advanced-visitor-conditions/main.class.php:188
msgid "Display ads based on the number of page impressions the user already made before the current one."
msgstr "Hirdetések megjelenítése a felhasználó által az aktuális megjelenítés előtt már elért oldalmegjelenítések száma alapján."

#: modules/advanced-visitor-conditions/main.class.php:187
msgid "page impressions"
msgstr "oldalmegjelenítések"

#: modules/advanced-visitor-conditions/main.class.php:180
msgid "cookie"
msgstr "cookie"

#: modules/advanced-visitor-conditions/main.class.php:174
msgid "Display ads based on the visitor's browser language."
msgstr "Hirdetések megjelenítése a látogató böngésző nyelve alapján."

#: modules/advanced-visitor-conditions/main.class.php:173
msgid "browser language"
msgstr "böngésző nyelve"

#: modules/advanced-visitor-conditions/main.class.php:148
msgid "user can (capabilities)"
msgstr "a felhasználó lehetőségei"

#: modules/advanced-visitor-conditions/main.class.php:140
msgid "user agent"
msgstr "user agent"

#: modules/advanced-visitor-conditions/main.class.php:133
msgid "Display ads based on the referrer URL."
msgstr "Hirdetések megjelenítése a hivatkozó URL alapján."

#: modules/advanced-visitor-conditions/main.class.php:132
msgid "referrer url"
msgstr "hivatkozó URL"

#: modules/advanced-visitor-conditions/admin.class.php:29
msgid "Advanced visitor conditions"
msgstr "Speciális látogatói feltételek"

#: modules/advanced-display-conditions/main.class.php:574
msgid "count from end"
msgstr "végétől számítani"

#: modules/advanced-display-conditions/main.class.php:540
msgid "meta key"
msgstr "meta key"

#: modules/advanced-display-conditions/main.class.php:539
msgid "all of"
msgstr "az összes"

#: modules/advanced-display-conditions/main.class.php:538
msgid "any of"
msgstr "bármelyik"

#: modules/advanced-display-conditions/main.class.php:502
msgid "no languages set up in WPML"
msgstr "nincs nyelv beállítva a WPML-ben"

#: modules/advanced-display-conditions/main.class.php:431
#: modules/advanced-display-conditions/main.class.php:486
#: modules/advanced-visitor-conditions/main.class.php:346
#: modules/advanced-visitor-conditions/main.class.php:395
#: modules/advanced-visitor-conditions/main.class.php:538
#: modules/gamipress/views/visitor-condition-achievement.php:17
#: modules/geo/classes/admin.php:308
#: modules/paid-memberships-pro/main.class.php:118
#: modules/paid-memberships-pro/main.class.php:237
msgid "is not"
msgstr "nem"

#: modules/advanced-display-conditions/main.class.php:430
#: modules/advanced-display-conditions/main.class.php:485
#: modules/advanced-visitor-conditions/main.class.php:345
#: modules/advanced-visitor-conditions/main.class.php:394
#: modules/advanced-visitor-conditions/main.class.php:536
#: modules/gamipress/views/visitor-condition-achievement.php:16
#: modules/geo/classes/admin.php:307
#: modules/paid-memberships-pro/main.class.php:117
#: modules/paid-memberships-pro/main.class.php:236
msgid "is"
msgstr "van"

#: modules/advanced-display-conditions/main.class.php:110
msgid "Display ads based on the index of a split page"
msgstr "Hirdetések megjelenítése egy felosztott oldal indexe alapján"

#: modules/advanced-display-conditions/main.class.php:109
msgid "pagination"
msgstr "lapszámozás"

#: modules/advanced-display-conditions/main.class.php:103
msgid "Display ads based on post meta."
msgstr "Hirdetések megjelenítése a bejegyzés meta-ja alapján."

#: modules/advanced-display-conditions/main.class.php:102
msgid "post meta"
msgstr "bejegyzés meta"

#: modules/advanced-display-conditions/main.class.php:96
msgid "Display ads based on parent page."
msgstr "Hirdetések megjelenítése a szülőoldal alapján."

#: modules/advanced-display-conditions/main.class.php:95
msgid "parent page"
msgstr "szülőoldal"

#: modules/advanced-display-conditions/main.class.php:87
msgid "WPML language"
msgstr "WPML nyelv"

#. translators: %s: post type
#: modules/advanced-display-conditions/main.class.php:57
#: modules/advanced-display-conditions/main.class.php:76
msgid "Display ads based on the template of the %s post type."
msgstr "Hirdetések megjelenítése a %s bejegyzéstípus sablonja alapján."

#. translators: %s: page
#: modules/advanced-display-conditions/main.class.php:55
#: modules/advanced-display-conditions/main.class.php:75
msgid "%s template"
msgstr "%s sablon"

#: modules/advanced-display-conditions/main.class.php:39
msgid "url parameters"
msgstr "url paraméterek"

#: modules/ads-for-adblockers/admin.class.php:148
msgid "The chosen ad contains a reference to an external .js file"
msgstr "A kiválasztott hirdetés hivatkozást tartalmaz egy külső .js fájlra"

#: modules/ads-for-adblockers/admin.class.php:86
msgid "Displayed to visitors with an ad blocker"
msgstr "Üzenet a bekapcsolt hirdetésblokkolós látogatók számára "

#: modules/ads-for-adblockers/admin.class.php:94
msgid "Ad blocker item"
msgstr "Hirdetésblokkoló elem"

#: modules/ads-for-adblockers/admin.class.php:42
msgid "Ads for ad blockers"
msgstr "Hirdetések reklámblokkolók számára"

#: modules/admin-bar/admin-bar.class.php:104
msgid "placement"
msgstr "elhelyezés"

#: modules/admin-bar/admin-bar.class.php:101
msgid "group"
msgstr "csoport"

#: modules/admin-bar/admin-bar.class.php:98
msgid "ad"
msgstr "hirdetés"

#: modules/admin-bar/admin-bar.class.php:56
msgid "No Ads found"
msgstr "Nem találhatók hirdetések"

#: modules/admin-bar/admin-bar.class.php:48
#: modules/ads-for-adblockers/views/placement-item.php:27
#: views/admin/tables/ads/adsense-fallback.php:37
msgid "Ads"
msgstr "Hirdetések"

#: views/setting_output_once.php:6
msgid "Display the ad only once per page"
msgstr "Oldalanként csak egyszer jelenítse meg a hirdetést"

#: views/setting_output_once.php:2
msgid "Display only once"
msgstr "Csak egyszer jelenjen meg"

#: classes/advanced-ads-pro.php:332
msgid "Please note, the “Ad Admin“ and the “Ad Manager“ roles have the “upload_files“ and the “unfiltered_html“ capabilities"
msgstr "Felhívjuk figyelmét, hogy az „Ad Admin” és az „Ad Manager” szerepkör rendelkezik a „upload_files” és a „unfiltered_html” képességekkel."

#: includes/class-bootstrap.php:225
msgid "Install Now"
msgstr "Telepítés most"

#: includes/class-bootstrap.php:233
msgid "Activate Now"
msgstr "Aktiváld most"

#. translators: %s: URL to the settings page
#: classes/advanced-ads-pro-admin.php:390
msgid "Enable the Advanced Visitor Conditions <a href=\"%s\" target=\"_blank\">in the settings</a>."
msgstr "Engedélyezze a Speciális látogatói feltételeket <a href=\"%s\" target=\"_blank\">a beállításokban</a>."

#: classes/advanced-ads-pro-admin.php:336
msgid "Please note, with the last update, the “Ad Admin“ and “Ad Manager“ roles have the “upload_files“ and the “unfiltered_html“ capabilities."
msgstr "Kérjük, vegye figyelembe, hogy a legutóbbi frissítéssel az „Ad Adminr” és az „Ad Manager” szerepkör rendelkezik a „upload_files” és a „unfiltered_html” lehetőséggel."

#: classes/advanced-ads-pro-admin.php:326
msgid "Ad User Role"
msgstr "Hirdetésfelhasználói szerep"

#: classes/advanced-ads-pro-admin.php:323
msgid "Advanced Ads User Role"
msgstr "Advanced Ads felhasználói szerepkör"

#: classes/advanced-ads-pro-admin.php:46
msgid "--no role--"
msgstr "-- nincs szerep --"

#: classes/advanced-ads-pro-admin.php:45
#: includes/installation/class-install.php:159
msgid "Ad User"
msgstr "Hirdetés felhasználója"

#: classes/advanced-ads-pro-admin.php:44
#: includes/installation/class-install.php:146
msgid "Ad Manager"
msgstr "Ad Manager"

#: classes/advanced-ads-pro-admin.php:43
#: includes/installation/class-install.php:132
msgid "Ad Admin"
msgstr "Ad Admin"
