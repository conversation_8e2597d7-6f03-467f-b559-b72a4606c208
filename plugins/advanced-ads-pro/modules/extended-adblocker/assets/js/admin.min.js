!function(e){const a=e(".advads-eab-overlay-notice"),d=e("#advanced-ads-adblocker-overlay-options"),o=e("#advanced-ads-adblocker-redirect-options"),c=e("#advanced-ads-adblocker-option-exclude"),s=e("#advanced-ads-use-adblocker").prop("checked");e(".advanced-ads-adblocker-eab-method").on("change",(function(){switch(a.hide(),this.value){case"nothing":d.hide(),o.hide(),c.hide();break;case"overlay":d.show(),o.hide(),c.show(),!s&&a.show();break;case"redirect":d.hide(),o.show(),c.show()}}));const n=e("#advanced-ads-adblocker-dismiss-button-input");n.on("change",(function(){e("#advanced-ads-adblocker-dismiss-options").toggle(!this.checked)})),n.change()}(j<PERSON><PERSON>y);
