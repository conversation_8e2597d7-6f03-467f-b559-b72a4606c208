(()=>{let e;const t={},s={};let i=!1;try{const e=Object.defineProperty({},"passive",{get:()=>{i={passive:!0}}});window.addEventListener("testPassive",null,e),window.removeEventListener("testPassive",null,e)}catch(e){}const n=window.advads_parallax_placement_options,a=(t,s)=>{s.style.maxWidth=t.offsetWidth+"px",s.style.visibility="visible",e=window.innerHeight},d=(e,s)=>{for(const i in s){const s=document.getElementById(n.classes.prefix+n.classes.container+i);if(null===s)continue;const a=s.getElementsByClassName(n.classes.prefix+n.classes.inner)[0];s&&a&&(t[i]=!0,e(s,a,n.placements[i]))}},o=(t,i,n="")=>{const a=i.querySelector("img");s[a.src]||(s[a.src]={image:new Image,isLoaded:!1});const d=t.getBoundingClientRect(),o=s=>{if(s.naturalHeight/s.naturalWidth>e/t.clientWidth&&(a.style.objectFit="contain"),i.style.left=d.width/2+d.left+"px",s.naturalHeight>=e&&s.naturalWidth<=t.clientWidth)return a.style.height="100vh",void(i.style.transform="translateX(-50%)");i.getBoundingClientRect().height<d.height&&(i.style.height=d.height+"px",a.style.objectFit="cover")},l=()=>{let t;t=d.bottom>=e&&d.top<=e?e-i.getBoundingClientRect().height:d.top<=0&&d.bottom>0?0:((e-i.getBoundingClientRect().height)/(e-d.height)*d.top).toFixed(2),i.style.transform="translate3d(-50%,"+t+"px, 0)"};if(s[a.src].isLoaded)return"scroll"!==n&&o(s[a.src].image),void l();s[a.src].image.addEventListener("load",(e=>{s[a.src].isLoaded=e.target.complete&&0!==e.target.naturalHeight,o(e.target),l()})),s[a.src].image.src=a.src},l=()=>{const e=Object.assign({},n.placements);for(const s in t)delete e[s];Object.keys(e).length&&d(((e,t)=>{e.style.visibility="hidden",a(e,t),o(e,t),e.style.visibility="visible"}),e)};l(),document.addEventListener("DOMContentLoaded",l,i),window.addEventListener("resize",(()=>d(((e,t)=>{a(e,t),o(e,t,"resize")}),n.placements)),i);const r=()=>d(((e,t)=>{o(e,t,"scroll")}),n.placements);window.addEventListener("scroll",r,i),window.addEventListener("touchmove",r,i),"undefined"!=typeof advanced_ads_pro&&void 0!==advanced_ads_pro.observers&&advanced_ads_pro.observers.add((e=>{-1===["inject_passive_ads","inject_ajax_ads"].indexOf(e.event)||e.ad_ids&&!Object.keys(e.ad_ids).length||l()}))})();