if(!advanced_ads_pro)var advanced_ads_pro={observers:jQuery.Callbacks()};document.addEventListener("advanced_ads_privacy",(function(e){if(("unknown"===e.detail.previousState||"rejected"===e.detail.previousState&&"accepted"===e.detail.state)&&("accepted"===e.detail.state||"not_needed"===e.detail.state)){var a={};document.querySelectorAll('script[type="text/plain"][data-tcf="waiting-for-consent"]').forEach((function(e){!function(e){void 0!==e.dataset.noTrack&&"impressions"===e.dataset.noTrack||(a.hasOwnProperty(e.dataset.bid)||(a[e.dataset.bid]=[]),a[e.dataset.bid].push(parseInt(e.dataset.id,10))),advads.privacy.decode_ad(e)}(e)})),Object.keys(a).length&&advanced_ads_pro.observers.fire({event:"advanced_ads_decode_inserted_ads",ad_ids:a})}}));
