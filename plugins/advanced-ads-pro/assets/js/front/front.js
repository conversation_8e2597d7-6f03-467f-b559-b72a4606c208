(()=>{"use strict";var e={520:(e,t,a)=>{a.d(t,{K:()=>_});var i=a(669),n=a.n(i);function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}function s(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var a=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=a){var i,n,r,s,o=[],d=!0,_=!1;try{if(r=(a=a.call(e)).next,0===t){if(Object(a)!==a)return;d=!1}else for(;!(d=(i=r.call(a)).done)&&(o.push(i.value),o.length!==t);d=!0);}catch(e){_=!0,n=e}finally{try{if(!d&&null!=a.return&&(s=a.return(),Object(s)!==s))return}finally{if(_)throw n}}return o}}(e,t)||function(e,t){if(e){if("string"==typeof e)return o(e,t);var a={}.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?o(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function o(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,i=Array(t);a<t;a++)i[a]=e[a];return i}e=a.hmd(e);var d={REFERRER_COOKIE_NAME:"advanced_ads_pro_visitor_referrer",PAGE_IMPRESSIONS_COOKIE_NAME:"advanced_ads_page_impressions",AD_IMPRESSIONS_COOKIE_NAME:"advanced_ads_ad_impressions",VISITOR_INFO_COOKIE_NAME:"advanced_ads_visitor",conditions:{mobile:"check_mobile",referrer_url:"check_referrer_url",user_agent:"check_user_agent",request_uri:"check_request_uri",browser_lang:"check_browser_lang",cookie:"check_cookie",page_impressions:"check_page_impressions",ad_impressions:"check_ad_impressions",new_visitor:"check_new_visitor",adblocker:"check_adblocker",device_width:"check_browser_width",tablet:"check_tablet",loggedin:"check_loggedin",capability:"check_capability",role:"check_role",geo_targeting:"check_geo_targeting",buddypress_profile_field:"check_buddypress_profile_field"},init:function(){n()(document).trigger("advads-passive-cb-conditions",[this]),this.init=function(){}},frontend_check:function(e,t){var a=this.conditions[e.type];return"string"==typeof a&&(a=advads_pro_utils.bind(this[a],this)),!a||!!a(e,t)},check_mobile:function(t){if(function(t){var i=/iPhone/i,n=/iPod/i,r=/iPad/i,s=/(?=.*\bAndroid\b)(?=.*\bMobile\b)/i,o=/Android/i,d=/(?=.*\bAndroid\b)(?=.*\bSD4930UR\b)/i,_=/(?=.*\bAndroid\b)(?=.*\b(?:KFOT|KFTT|KFJWI|KFJWA|KFSOWI|KFTHWI|KFTHWA|KFAPWI|KFAPWA|KFARWI|KFASWI|KFSAWI|KFSAWA)\b)/i,c=/IEMobile/i,l=/(?=.*\bWindows\b)(?=.*\bARM\b)/i,u=/BlackBerry/i,p=/BB10/i,b=/Opera Mini/i,T=/(CriOS|Chrome)(?=.*\bMobile\b)/i,v=/(?=.*\bFirefox\b)(?=.*\bMobile\b)/i,f=new RegExp("(?:Nexus 7|BNTV250|Kindle Fire|Silk|GT-P1000)","i"),h=function(e,t){return e.test(t)},A=function(e){var t=e||navigator.userAgent,a=t.split("[FBAN");return void 0!==a[1]&&(t=a[0]),this.apple={phone:h(i,t),ipod:h(n,t),tablet:!h(i,t)&&h(r,t),device:h(i,t)||h(n,t)||h(r,t)},this.amazon={phone:h(d,t),tablet:!h(d,t)&&h(_,t),device:h(d,t)||h(_,t)},this.android={phone:h(d,t)||h(s,t),tablet:!h(d,t)&&!h(s,t)&&(h(_,t)||h(o,t)),device:h(d,t)||h(_,t)||h(s,t)||h(o,t)},this.windows={phone:h(c,t),tablet:h(l,t),device:h(c,t)||h(l,t)},this.other={blackberry:h(u,t),blackberry10:h(p,t),opera:h(b,t),firefox:h(v,t),chrome:h(T,t),device:h(u,t)||h(p,t)||h(b,t)||h(v,t)||h(T,t)},this.seven_inch=h(f,t),this.any=this.apple.device||this.android.device||this.windows.device||this.other.device||this.seven_inch,this.phone=this.apple.phone||this.android.phone||this.windows.phone,this.tablet=this.apple.tablet||this.android.tablet||this.windows.tablet,"undefined"==typeof window?this:void 0},y=function(){var e=new A;return e.Class=A,e};e.exports&&"undefined"==typeof window?e.exports=A:e.exports&&"undefined"!=typeof window?e.exports=y():"function"==typeof define&&a.amdO?define("isMobile",[],t.isMobile=y()):t.isMobile=y()}(this),!advads_pro_utils.isset(t.value)){if(!advads_pro_utils.isset(t.operator))return!0;var i=this.isMobile.any;return"is_not"===t.operator?!i:i}var n=this.check_tablet({operator:"is"});return Object.keys(Object.fromEntries(Object.entries({mobile:this.isMobile.any&&!n,tablet:n,desktop:!this.isMobile.any&&!n}).filter((function(e){var a=s(e,2),i=a[0],n=a[1];return t.value.includes(i)&&n})))).length>0},check_referrer_url:function(e){var t=advads_pro_utils.extract_cookie_data(advads.get_cookie(this.REFERRER_COOKIE_NAME))||"";return this.helper_check_string(t,e)},check_user_agent:function(e){var t="object"===("undefined"==typeof navigator?"undefined":r(navigator))?navigator.userAgent:"";return this.helper_check_string(t,e)},check_browser_lang:function(e){var t=e.value;if(!t)return!0;var a="object"===("undefined"==typeof navigator?"undefined":r(navigator))?navigator.languages?navigator.languages.join(","):navigator.language||navigator.userLanguage:"";if(!a)return!0;try{var i=new RegExp("\\b"+t+"\\b","i"),n=-1!==a.search(i)}catch(e){return!0}return"is_not"===e.operator?!n:n},check_request_uri:function(e){var t="object"===("undefined"==typeof location?"undefined":r(location))?location.href:"";return this.helper_check_string(t,e)},check_cookie:function(e){e.operator&&(e.operator=this.maybe_replace_cookie_operator(e.operator));var t=!advads_pro_utils.isset(e.operator)||"match_not"!==e.operator;if(!advads_pro_utils.isset(e.cookie)||""===e.cookie)return t;var a=advads.get_cookie(e.cookie);return advads_pro_utils.isset(a)?advads_pro_utils.isset(e.value)&&""!==e.value?(e.value=unescape(e.value.replace(/\\(.)/gm,"$1")),this.helper_check_string(a,e)):t:!t},check_page_impressions:function(e){if(!advads_pro_utils.isset(e.operator)||!advads_pro_utils.isset(e.value))return!0;var t=0,a=advads_pro_utils.extract_cookie_data(advads.get_cookie(this.PAGE_IMPRESSIONS_COOKIE_NAME));if(!advads_pro_utils.isset(a))return!0;t=parseInt(a,10)||0;var i=parseInt(e.value)||0;switch(e.operator){case"is_equal":if(i!==t)return!1;break;case"is_higher":if(i>t)return!1;break;case"is_lower":if(i<t)return!1}return!0},check_ad_impressions:function(e,t){if(!advads_pro_utils.isset(e.value)||!advads_pro_utils.isset(e.timeout)||!advads_pro_utils.isset(t.id))return!0;var a=parseInt(e.value)||0,i=this.AD_IMPRESSIONS_COOKIE_NAME+"_"+t.id,n=i+"_timeout";return!(advads_pro_utils.isset(advads.get_cookie(i))&&advads_pro_utils.isset(advads.get_cookie(n))&&a<=(parseInt(advads.get_cookie(i))||0))},check_new_visitor:function(e){if(!advads_pro_utils.isset(e.operator))return!0;var t=advads.cookie_exists(this.PAGE_IMPRESSIONS_COOKIE_NAME)?advads_pro_utils.extract_cookie_data(advads.get_cookie(this.PAGE_IMPRESSIONS_COOKIE_NAME)):0;return"is"===e.operator?1===t:1<t},check_adblocker:function(e){if(!advads_pro_utils.isset(e.operator))return!0;var t;switch(advanced_ads_check_adblocker((function(e){t=e})),e.operator){case"is":return t;case"is_not":return!t}return!0},check_browser_width:function(e){if(!advads_pro_utils.isset(e.operator)||!advads_pro_utils.isset(e.value))return!0;var t=n()(window).width(),a=parseInt(e.value)||0;switch(e.operator){case"is_equal":if(a!==t)return!1;break;case"is_higher":if(a>t)return!1;break;case"is_lower":if(a<t)return!1}return!0},check_tablet:function(e){if(!advads_pro_utils.isset(e.operator))return!0;var t={iPad:"iPad|iPad.*Mobile",NexusTablet:"Android.*Nexus[\\s]+(7|9|10)",SamsungTablet:"SAMSUNG.*Tablet|Galaxy.*Tab|SC-01C|GT-P1000|GT-P1003|GT-P1010|GT-P3105|GT-P6210|GT-P6800|GT-P6810|GT-P7100|GT-P7300|GT-P7310|GT-P7500|GT-P7510|SCH-I800|SCH-I815|SCH-I905|SGH-I957|SGH-I987|SGH-T849|SGH-T859|SGH-T869|SPH-P100|GT-P3100|GT-P3108|GT-P3110|GT-P5100|GT-P5110|GT-P6200|GT-P7320|GT-P7511|GT-N8000|GT-P8510|SGH-I497|SPH-P500|SGH-T779|SCH-I705|SCH-I915|GT-N8013|GT-P3113|GT-P5113|GT-P8110|GT-N8010|GT-N8005|GT-N8020|GT-P1013|GT-P6201|GT-P7501|GT-N5100|GT-N5105|GT-N5110|SHV-E140K|SHV-E140L|SHV-E140S|SHV-E150S|SHV-E230K|SHV-E230L|SHV-E230S|SHW-M180K|SHW-M180L|SHW-M180S|SHW-M180W|SHW-M300W|SHW-M305W|SHW-M380K|SHW-M380S|SHW-M380W|SHW-M430W|SHW-M480K|SHW-M480S|SHW-M480W|SHW-M485W|SHW-M486W|SHW-M500W|GT-I9228|SCH-P739|SCH-I925|GT-I9200|GT-P5200|GT-P5210|GT-P5210X|SM-T311|SM-T310|SM-T310X|SM-T210|SM-T210R|SM-T211|SM-P600|SM-P601|SM-P605|SM-P900|SM-P901|SM-T217|SM-T217A|SM-T217S|SM-P6000|SM-T3100|SGH-I467|XE500|SM-T110|GT-P5220|GT-I9200X|GT-N5110X|GT-N5120|SM-P905|SM-T111|SM-T2105|SM-T315|SM-T320|SM-T320X|SM-T321|SM-T520|SM-T525|SM-T530NU|SM-T230NU|SM-T330NU|SM-T900|XE500T1C|SM-P605V|SM-P905V|SM-T337V|SM-T537V|SM-T707V|SM-T807V|SM-P600X|SM-P900X|SM-T210X|SM-T230|SM-T230X|SM-T325|GT-P7503|SM-T531|SM-T330|SM-T530|SM-T705|SM-T705C|SM-T535|SM-T331|SM-T800|SM-T700|SM-T537|SM-T807|SM-P907A|SM-T337A|SM-T537A|SM-T707A|SM-T807A|SM-T237|SM-T807P|SM-P607T|SM-T217T|SM-T337T|SM-T807T|SM-T116NQ|SM-P550|SM-T350|SM-T550|SM-T9000|SM-P9000|SM-T705Y|SM-T805|GT-P3113|SM-T710|SM-T810|SM-T815|SM-T360|SM-T533|SM-T113|SM-T335|SM-T715|SM-T560|SM-T670|SM-T677|SM-T377|SM-T567|SM-T357T|SM-T555|SM-T561",Kindle:"Kindle|Silk.*Accelerated|Android.*\\b(KFOT|KFTT|KFJWI|KFJWA|KFOTE|KFSOWI|KFTHWI|KFTHWA|KFAPWI|KFAPWA|WFJWAE|KFSAWA|KFSAWI|KFASWI|KFARWI)\\b",SurfaceTablet:"Windows NT [0-9.]+; ARM;.*(Tablet|ARMBJS)",HPTablet:"HP Slate (7|8|10)|HP ElitePad 900|hp-tablet|EliteBook.*Touch|HP 8|Slate 21|HP SlateBook 10",AsusTablet:"^.*PadFone((?!Mobile).)*$|Transformer|TF101|TF101G|TF300T|TF300TG|TF300TL|TF700T|TF700KL|TF701T|TF810C|ME171|ME301T|ME302C|ME371MG|ME370T|ME372MG|ME172V|ME173X|ME400C|Slider SL101|\\bK00F\\b|\\bK00C\\b|\\bK00E\\b|\\bK00L\\b|TX201LA|ME176C|ME102A|\\bM80TA\\b|ME372CL|ME560CG|ME372CG|ME302KL| K010 | K017 |ME572C|ME103K|ME170C|ME171C|\\bME70C\\b|ME581C|ME581CL|ME8510C|ME181C|P01Y|PO1MA",BlackBerryTablet:"PlayBook|RIM Tablet",HTCtablet:"HTC_Flyer_P512|HTC Flyer|HTC Jetstream|HTC-P715a|HTC EVO View 4G|PG41200|PG09410",MotorolaTablet:"xoom|sholest|MZ615|MZ605|MZ505|MZ601|MZ602|MZ603|MZ604|MZ606|MZ607|MZ608|MZ609|MZ615|MZ616|MZ617",NookTablet:"Android.*Nook|NookColor|nook browser|BNRV200|BNRV200A|BNTV250|BNTV250A|BNTV400|BNTV600|LogicPD Zoom2",AcerTablet:"Android.*; \\b(A100|A101|A110|A200|A210|A211|A500|A501|A510|A511|A700|A701|W500|W500P|W501|W501P|W510|W511|W700|G100|G100W|B1-A71|B1-710|B1-711|A1-810|A1-811|A1-830)\\b|W3-810|\\bA3-A10\\b|\\bA3-A11\\b|\\bA3-A20",ToshibaTablet:"Android.*(AT100|AT105|AT200|AT205|AT270|AT275|AT300|AT305|AT1S5|AT500|AT570|AT700|AT830)|TOSHIBA.*FOLIO",LGTablet:"\\bL-06C|LG-V909|LG-V900|LG-V700|LG-V510|LG-V500|LG-V410|LG-V400|LG-VK810\\b",FujitsuTablet:"Android.*\\b(F-01D|F-02F|F-05E|F-10D|M532|Q572)\\b",PrestigioTablet:"PMP3170B|PMP3270B|PMP3470B|PMP7170B|PMP3370B|PMP3570C|PMP5870C|PMP3670B|PMP5570C|PMP5770D|PMP3970B|PMP3870C|PMP5580C|PMP5880D|PMP5780D|PMP5588C|PMP7280C|PMP7280C3G|PMP7280|PMP7880D|PMP5597D|PMP5597|PMP7100D|PER3464|PER3274|PER3574|PER3884|PER5274|PER5474|PMP5097CPRO|PMP5097|PMP7380D|PMP5297C|PMP5297C_QUAD|PMP812E|PMP812E3G|PMP812F|PMP810E|PMP880TD|PMT3017|PMT3037|PMT3047|PMT3057|PMT7008|PMT5887|PMT5001|PMT5002",LenovoTablet:"Lenovo TAB|Idea(Tab|Pad)( A1|A10| K1|)|ThinkPad([ ]+)?Tablet|YT3-X90L|YT3-X90F|YT3-X90X|Lenovo.*(S2109|S2110|S5000|S6000|K3011|A3000|A3500|A1000|A2107|A2109|A1107|A5500|A7600|B6000|B8000|B8080)(-|)(FL|F|HV|H|)",DellTablet:"Venue 11|Venue 8|Venue 7|Dell Streak 10|Dell Streak 7",YarvikTablet:"Android.*\\b(TAB210|TAB211|TAB224|TAB250|TAB260|TAB264|TAB310|TAB360|TAB364|TAB410|TAB411|TAB420|TAB424|TAB450|TAB460|TAB461|TAB464|TAB465|TAB467|TAB468|TAB07-100|TAB07-101|TAB07-150|TAB07-151|TAB07-152|TAB07-200|TAB07-201-3G|TAB07-210|TAB07-211|TAB07-212|TAB07-214|TAB07-220|TAB07-400|TAB07-485|TAB08-150|TAB08-200|TAB08-201-3G|TAB08-201-30|TAB09-100|TAB09-211|TAB09-410|TAB10-150|TAB10-201|TAB10-211|TAB10-400|TAB10-410|TAB13-201|TAB274EUK|TAB275EUK|TAB374EUK|TAB462EUK|TAB474EUK|TAB9-200)\\b",MedionTablet:"Android.*\\bOYO\\b|LIFE.*(P9212|P9514|P9516|S9512)|LIFETAB",ArnovaTablet:"AN10G2|AN7bG3|AN7fG3|AN8G3|AN8cG3|AN7G3|AN9G3|AN7dG3|AN7dG3ST|AN7dG3ChildPad|AN10bG3|AN10bG3DT|AN9G2",IntensoTablet:"INM8002KP|INM1010FP|INM805ND|Intenso Tab|TAB1004",IRUTablet:"M702pro",MegafonTablet:"MegaFon V9|\\bZTE V9\\b|Android.*\\bMT7A\\b",EbodaTablet:"E-Boda (Supreme|Impresspeed|Izzycomm|Essential)",AllViewTablet:"Allview.*(Viva|Alldro|City|Speed|All TV|Frenzy|Quasar|Shine|TX1|AX1|AX2)",ArchosTablet:"\\b(101G9|80G9|A101IT)\\b|Qilive 97R|Archos5|\\bARCHOS (70|79|80|90|97|101|FAMILYPAD|)(b|)(G10| Cobalt| TITANIUM(HD|)| Xenon| Neon|XSK| 2| XS 2| PLATINUM| CARBON|GAMEPAD)\\b",AinolTablet:"NOVO7|NOVO8|NOVO10|Novo7Aurora|Novo7Basic|NOVO7PALADIN|novo9-Spark",NokiaLumiaTablet:"Lumia 2520",SonyTablet:"Sony.*Tablet|Xperia Tablet|Sony Tablet S|SO-03E|SGPT12|SGPT13|SGPT114|SGPT121|SGPT122|SGPT123|SGPT111|SGPT112|SGPT113|SGPT131|SGPT132|SGPT133|SGPT211|SGPT212|SGPT213|SGP311|SGP312|SGP321|EBRD1101|EBRD1102|EBRD1201|SGP351|SGP341|SGP511|SGP512|SGP521|SGP541|SGP551|SGP621|SGP612|SOT31",PhilipsTablet:"\\b(PI2010|PI3000|PI3100|PI3105|PI3110|PI3205|PI3210|PI3900|PI4010|PI7000|PI7100)\\b",CubeTablet:"Android.*(K8GT|U9GT|U10GT|U16GT|U17GT|U18GT|U19GT|U20GT|U23GT|U30GT)|CUBE U8GT",CobyTablet:"MID1042|MID1045|MID1125|MID1126|MID7012|MID7014|MID7015|MID7034|MID7035|MID7036|MID7042|MID7048|MID7127|MID8042|MID8048|MID8127|MID9042|MID9740|MID9742|MID7022|MID7010",MIDTablet:"M9701|M9000|M9100|M806|M1052|M806|T703|MID701|MID713|MID710|MID727|MID760|MID830|MID728|MID933|MID125|MID810|MID732|MID120|MID930|MID800|MID731|MID900|MID100|MID820|MID735|MID980|MID130|MID833|MID737|MID960|MID135|MID860|MID736|MID140|MID930|MID835|MID733|MID4X10",MSITablet:"MSI \\b(Primo 73K|Primo 73L|Primo 81L|Primo 77|Primo 93|Primo 75|Primo 76|Primo 73|Primo 81|Primo 91|Primo 90|Enjoy 71|Enjoy 7|Enjoy 10)\\b",SMiTTablet:"Android.*(\\bMID\\b|MID-560|MTV-T1200|MTV-PND531|MTV-P1101|MTV-PND530)",RockChipTablet:"Android.*(RK2818|RK2808A|RK2918|RK3066)|RK2738|RK2808A",FlyTablet:"IQ310|Fly Vision",bqTablet:"Android.*(bq)?.*(Elcano|Curie|Edison|Maxwell|Kepler|Pascal|Tesla|Hypatia|Platon|Newton|Livingstone|Cervantes|Avant|Aquaris E10)|Maxwell.*Lite|Maxwell.*Plus",HuaweiTablet:"MediaPad|MediaPad 7 Youth|IDEOS S7|S7-201c|S7-202u|S7-101|S7-103|S7-104|S7-105|S7-106|S7-201|S7-Slim",NecTablet:"\\bN-06D|\\bN-08D",PantechTablet:"Pantech.*P4100",BronchoTablet:"Broncho.*(N701|N708|N802|a710)",VersusTablet:"TOUCHPAD.*[78910]|\\bTOUCHTAB\\b",ZyncTablet:"z1000|Z99 2G|z99|z930|z999|z990|z909|Z919|z900",PositivoTablet:"TB07STA|TB10STA|TB07FTA|TB10FTA",NabiTablet:"Android.*\\bNabi",KoboTablet:"Kobo Touch|\\bK080\\b|\\bVox\\b Build|\\bArc\\b Build",DanewTablet:"DSlide.*\\b(700|701R|702|703R|704|802|970|971|972|973|974|1010|1012)\\b",TexetTablet:"NaviPad|TB-772A|TM-7045|TM-7055|TM-9750|TM-7016|TM-7024|TM-7026|TM-7041|TM-7043|TM-7047|TM-8041|TM-9741|TM-9747|TM-9748|TM-9751|TM-7022|TM-7021|TM-7020|TM-7011|TM-7010|TM-7023|TM-7025|TM-7037W|TM-7038W|TM-7027W|TM-9720|TM-9725|TM-9737W|TM-1020|TM-9738W|TM-9740|TM-9743W|TB-807A|TB-771A|TB-727A|TB-725A|TB-719A|TB-823A|TB-805A|TB-723A|TB-715A|TB-707A|TB-705A|TB-709A|TB-711A|TB-890HD|TB-880HD|TB-790HD|TB-780HD|TB-770HD|TB-721HD|TB-710HD|TB-434HD|TB-860HD|TB-840HD|TB-760HD|TB-750HD|TB-740HD|TB-730HD|TB-722HD|TB-720HD|TB-700HD|TB-500HD|TB-470HD|TB-431HD|TB-430HD|TB-506|TB-504|TB-446|TB-436|TB-416|TB-146SE|TB-126SE",PlaystationTablet:"Playstation.*(Portable|Vita)",TrekstorTablet:"ST10416-1|VT10416-1|ST70408-1|ST702xx-1|ST702xx-2|ST80208|ST97216|ST70104-2|VT10416-2|ST10216-2A|SurfTab",PyleAudioTablet:"\\b(PTBL10CEU|PTBL10C|PTBL72BC|PTBL72BCEU|PTBL7CEU|PTBL7C|PTBL92BC|PTBL92BCEU|PTBL9CEU|PTBL9CUK|PTBL9C)\\b",AdvanTablet:"Android.* \\b(E3A|T3X|T5C|T5B|T3E|T3C|T3B|T1J|T1F|T2A|T1H|T1i|E1C|T1-E|T5-A|T4|E1-B|T2Ci|T1-B|T1-D|O1-A|E1-A|T1-A|T3A|T4i)\\b ",DanyTechTablet:"Genius Tab G3|Genius Tab S2|Genius Tab Q3|Genius Tab G4|Genius Tab Q4|Genius Tab G-II|Genius TAB GII|Genius TAB GIII|Genius Tab S1",GalapadTablet:"Android.*\\bG1\\b",MicromaxTablet:"Funbook|Micromax.*\\b(P250|P560|P360|P362|P600|P300|P350|P500|P275)\\b",KarbonnTablet:"Android.*\\b(A39|A37|A34|ST8|ST10|ST7|Smart Tab3|Smart Tab2)\\b",AllFineTablet:"Fine7 Genius|Fine7 Shine|Fine7 Air|Fine8 Style|Fine9 More|Fine10 Joy|Fine11 Wide",PROSCANTablet:"\\b(PEM63|PLT1023G|PLT1041|PLT1044|PLT1044G|PLT1091|PLT4311|PLT4311PL|PLT4315|PLT7030|PLT7033|PLT7033D|PLT7035|PLT7035D|PLT7044K|PLT7045K|PLT7045KB|PLT7071KG|PLT7072|PLT7223G|PLT7225G|PLT7777G|PLT7810K|PLT7849G|PLT7851G|PLT7852G|PLT8015|PLT8031|PLT8034|PLT8036|PLT8080K|PLT8082|PLT8088|PLT8223G|PLT8234G|PLT8235G|PLT8816K|PLT9011|PLT9045K|PLT9233G|PLT9735|PLT9760G|PLT9770G)\\b",YONESTablet:"BQ1078|BC1003|BC1077|RK9702|BC9730|BC9001|IT9001|BC7008|BC7010|BC708|BC728|BC7012|BC7030|BC7027|BC7026",ChangJiaTablet:"TPC7102|TPC7103|TPC7105|TPC7106|TPC7107|TPC7201|TPC7203|TPC7205|TPC7210|TPC7708|TPC7709|TPC7712|TPC7110|TPC8101|TPC8103|TPC8105|TPC8106|TPC8203|TPC8205|TPC8503|TPC9106|TPC9701|TPC97101|TPC97103|TPC97105|TPC97106|TPC97111|TPC97113|TPC97203|TPC97603|TPC97809|TPC97205|TPC10101|TPC10103|TPC10106|TPC10111|TPC10203|TPC10205|TPC10503",GUTablet:"TX-A1301|TX-M9002|Q702|kf026",PointOfViewTablet:"TAB-P506|TAB-navi-7-3G-M|TAB-P517|TAB-P-527|TAB-P701|TAB-P703|TAB-P721|TAB-P731N|TAB-P741|TAB-P825|TAB-P905|TAB-P925|TAB-PR945|TAB-PL1015|TAB-P1025|TAB-PI1045|TAB-P1325|TAB-PROTAB[0-9]+|TAB-PROTAB25|TAB-PROTAB26|TAB-PROTAB27|TAB-PROTAB26XL|TAB-PROTAB2-IPS9|TAB-PROTAB30-IPS9|TAB-PROTAB25XXL|TAB-PROTAB26-IPS10|TAB-PROTAB30-IPS10",OvermaxTablet:"OV-(SteelCore|NewBase|Basecore|Baseone|Exellen|Quattor|EduTab|Solution|ACTION|BasicTab|TeddyTab|MagicTab|Stream|TB-08|TB-09)",HCLTablet:"HCL.*Tablet|Connect-3G-2.0|Connect-2G-2.0|ME Tablet U1|ME Tablet U2|ME Tablet G1|ME Tablet X1|ME Tablet Y2|ME Tablet Sync",DPSTablet:"DPS Dream 9|DPS Dual 7",VistureTablet:"V97 HD|i75 3G|Visture V4( HD)?|Visture V5( HD)?|Visture V10",CrestaTablet:"CTP(-)?810|CTP(-)?818|CTP(-)?828|CTP(-)?838|CTP(-)?888|CTP(-)?978|CTP(-)?980|CTP(-)?987|CTP(-)?988|CTP(-)?989",MediatekTablet:"\\bMT8125|MT8389|MT8135|MT8377\\b",ConcordeTablet:"Concorde([ ]+)?Tab|ConCorde ReadMan",GoCleverTablet:"GOCLEVER TAB|A7GOCLEVER|M1042|M7841|M742|R1042BK|R1041|TAB A975|TAB A7842|TAB A741|TAB A741L|TAB M723G|TAB M721|TAB A1021|TAB I921|TAB R721|TAB I720|TAB T76|TAB R70|TAB R76.2|TAB R106|TAB R83.2|TAB M813G|TAB I721|GCTA722|TAB I70|TAB I71|TAB S73|TAB R73|TAB R74|TAB R93|TAB R75|TAB R76.1|TAB A73|TAB A93|TAB A93.2|TAB T72|TAB R83|TAB R974|TAB R973|TAB A101|TAB A103|TAB A104|TAB A104.2|R105BK|M713G|A972BK|TAB A971|TAB R974.2|TAB R104|TAB R83.3|TAB A1042",ModecomTablet:"FreeTAB 9000|FreeTAB 7.4|FreeTAB 7004|FreeTAB 7800|FreeTAB 2096|FreeTAB 7.5|FreeTAB 1014|FreeTAB 1001 |FreeTAB 8001|FreeTAB 9706|FreeTAB 9702|FreeTAB 7003|FreeTAB 7002|FreeTAB 1002|FreeTAB 7801|FreeTAB 1331|FreeTAB 1004|FreeTAB 8002|FreeTAB 8014|FreeTAB 9704|FreeTAB 1003",VoninoTablet:"\\b(Argus[ _]?S|Diamond[ _]?79HD|Emerald[ _]?78E|Luna[ _]?70C|Onyx[ _]?S|Onyx[ _]?Z|Orin[ _]?HD|Orin[ _]?S|Otis[ _]?S|SpeedStar[ _]?S|Magnet[ _]?M9|Primus[ _]?94[ _]?3G|Primus[ _]?94HD|Primus[ _]?QS|Android.*\\bQ8\\b|Sirius[ _]?EVO[ _]?QS|Sirius[ _]?QS|Spirit[ _]?S)\\b",ECSTablet:"V07OT2|TM105A|S10OT1|TR10CS1",StorexTablet:"eZee[_']?(Tab|Go)[0-9]+|TabLC7|Looney Tunes Tab",VodafoneTablet:"SmartTab([ ]+)?[0-9]+|SmartTabII10|SmartTabII7",EssentielBTablet:"Smart[ ']?TAB[ ]+?[0-9]+|Family[ ']?TAB2",RossMoorTablet:"RM-790|RM-997|RMD-878G|RMD-974R|RMT-705A|RMT-701|RME-601|RMT-501|RMT-711",iMobileTablet:"i-mobile i-note",TolinoTablet:"tolino tab [0-9.]+|tolino shine",AudioSonicTablet:"\\bC-22Q|T7-QC|T-17B|T-17P\\b",AMPETablet:"Android.* A78 ",SkkTablet:"Android.* (SKYPAD|PHOENIX|CYCLOPS)",TecnoTablet:"TECNO P9",JXDTablet:"Android.* \\b(F3000|A3300|JXD5000|JXD3000|JXD2000|JXD300B|JXD300|S5800|S7800|S602b|S5110b|S7300|S5300|S602|S603|S5100|S5110|S601|S7100a|P3000F|P3000s|P101|P200s|P1000m|P200m|P9100|P1000s|S6600b|S908|P1000|P300|S18|S6600|S9100)\\b",iJoyTablet:"Tablet (Spirit 7|Essentia|Galatea|Fusion|Onix 7|Landa|Titan|Scooby|Deox|Stella|Themis|Argon|Unique 7|Sygnus|Hexen|Finity 7|Cream|Cream X2|Jade|Neon 7|Neron 7|Kandy|Scape|Saphyr 7|Rebel|Biox|Rebel|Rebel 8GB|Myst|Draco 7|Myst|Tab7-004|Myst|Tadeo Jones|Tablet Boing|Arrow|Draco Dual Cam|Aurix|Mint|Amity|Revolution|Finity 9|Neon 9|T9w|Amity 4GB Dual Cam|Stone 4GB|Stone 8GB|Andromeda|Silken|X2|Andromeda II|Halley|Flame|Saphyr 9,7|Touch 8|Planet|Triton|Unique 10|Hexen 10|Memphis 4GB|Memphis 8GB|Onix 10)",FX2Tablet:"FX2 PAD7|FX2 PAD10",XoroTablet:"KidsPAD 701|PAD[ ]?712|PAD[ ]?714|PAD[ ]?716|PAD[ ]?717|PAD[ ]?718|PAD[ ]?720|PAD[ ]?721|PAD[ ]?722|PAD[ ]?790|PAD[ ]?792|PAD[ ]?900|PAD[ ]?9715D|PAD[ ]?9716DR|PAD[ ]?9718DR|PAD[ ]?9719QR|PAD[ ]?9720QR|TelePAD1030|Telepad1032|TelePAD730|TelePAD731|TelePAD732|TelePAD735Q|TelePAD830|TelePAD9730|TelePAD795|MegaPAD 1331|MegaPAD 1851|MegaPAD 2151",ViewsonicTablet:"ViewPad 10pi|ViewPad 10e|ViewPad 10s|ViewPad E72|ViewPad7|ViewPad E100|ViewPad 7e|ViewSonic VB733|VB100a",OdysTablet:"LOOX|XENO10|ODYS[ -](Space|EVO|Xpress|NOON)|\\bXELIO\\b|Xelio10Pro|XELIO7PHONETAB|XELIO10EXTREME|XELIOPT2|NEO_QUAD10",CaptivaTablet:"CAPTIVA PAD",IconbitTablet:"NetTAB|NT-3702|NT-3702S|NT-3702S|NT-3603P|NT-3603P|NT-0704S|NT-0704S|NT-3805C|NT-3805C|NT-0806C|NT-0806C|NT-0909T|NT-0909T|NT-0907S|NT-0907S|NT-0902S|NT-0902S",TeclastTablet:"T98 4G|\\bP80\\b|\\bX90HD\\b|X98 Air|X98 Air 3G|\\bX89\\b|P80 3G|\\bX80h\\b|P98 Air|\\bX89HD\\b|P98 3G|\\bP90HD\\b|P89 3G|X98 3G|\\bP70h\\b|P79HD 3G|G18d 3G|\\bP79HD\\b|\\bP89s\\b|\\bA88\\b|\\bP10HD\\b|\\bP19HD\\b|G18 3G|\\bP78HD\\b|\\bA78\\b|\\bP75\\b|G17s 3G|G17h 3G|\\bP85t\\b|\\bP90\\b|\\bP11\\b|\\bP98t\\b|\\bP98HD\\b|\\bG18d\\b|\\bP85s\\b|\\bP11HD\\b|\\bP88s\\b|\\bA80HD\\b|\\bA80se\\b|\\bA10h\\b|\\bP89\\b|\\bP78s\\b|\\bG18\\b|\\bP85\\b|\\bA70h\\b|\\bA70\\b|\\bG17\\b|\\bP18\\b|\\bA80s\\b|\\bA11s\\b|\\bP88HD\\b|\\bA80h\\b|\\bP76s\\b|\\bP76h\\b|\\bP98\\b|\\bA10HD\\b|\\bP78\\b|\\bP88\\b|\\bA11\\b|\\bA10t\\b|\\bP76a\\b|\\bP76t\\b|\\bP76e\\b|\\bP85HD\\b|\\bP85a\\b|\\bP86\\b|\\bP75HD\\b|\\bP76v\\b|\\bA12\\b|\\bP75a\\b|\\bA15\\b|\\bP76Ti\\b|\\bP81HD\\b|\\bA10\\b|\\bT760VE\\b|\\bT720HD\\b|\\bP76\\b|\\bP73\\b|\\bP71\\b|\\bP72\\b|\\bT720SE\\b|\\bC520Ti\\b|\\bT760\\b|\\bT720VE\\b|T720-3GE|T720-WiFi",OndaTablet:"\\b(V975i|Vi30|VX530|V701|Vi60|V701s|Vi50|V801s|V719|Vx610w|VX610W|V819i|Vi10|VX580W|Vi10|V711s|V813|V811|V820w|V820|Vi20|V711|VI30W|V712|V891w|V972|V819w|V820w|Vi60|V820w|V711|V813s|V801|V819|V975s|V801|V819|V819|V818|V811|V712|V975m|V101w|V961w|V812|V818|V971|V971s|V919|V989|V116w|V102w|V973|Vi40)\\b[\\s]+",JaytechTablet:"TPC-PA762",BlaupunktTablet:"Endeavour 800NG|Endeavour 1010",DigmaTablet:"\\b(iDx10|iDx9|iDx8|iDx7|iDxD7|iDxD8|iDsQ8|iDsQ7|iDsQ8|iDsD10|iDnD7|3TS804H|iDsQ11|iDj7|iDs10)\\b",EvolioTablet:"ARIA_Mini_wifi|Aria[ _]Mini|Evolio X10|Evolio X7|Evolio X8|\\bEvotab\\b|\\bNeura\\b",LavaTablet:"QPAD E704|\\bIvoryS\\b|E-TAB IVORY|\\bE-TAB\\b",AocTablet:"MW0811|MW0812|MW0922|MTK8382|MW1031|MW0831|MW0821|MW0931|MW0712",MpmanTablet:"MP11 OCTA|MP10 OCTA|MPQC1114|MPQC1004|MPQC994|MPQC974|MPQC973|MPQC804|MPQC784|MPQC780|\\bMPG7\\b|MPDCG75|MPDCG71|MPDC1006|MP101DC|MPDC9000|MPDC905|MPDC706HD|MPDC706|MPDC705|MPDC110|MPDC100|MPDC99|MPDC97|MPDC88|MPDC8|MPDC77|MP709|MID701|MID711|MID170|MPDC703|MPQC1010",CelkonTablet:"CT695|CT888|CT[\\s]?910|CT7 Tab|CT9 Tab|CT3 Tab|CT2 Tab|CT1 Tab|C820|C720|\\bCT-1\\b",WolderTablet:"miTab \\b(DIAMOND|SPACE|BROOKLYN|NEO|FLY|MANHATTAN|FUNK|EVOLUTION|SKY|GOCAR|IRON|GENIUS|POP|MINT|EPSILON|BROADWAY|JUMP|HOP|LEGEND|NEW AGE|LINE|ADVANCE|FEEL|FOLLOW|LIKE|LINK|LIVE|THINK|FREEDOM|CHICAGO|CLEVELAND|BALTIMORE-GH|IOWA|BOSTON|SEATTLE|PHOENIX|DALLAS|IN 101|MasterChef)\\b",MiTablet:"\\bMI PAD\\b|\\bHM NOTE 1W\\b",NibiruTablet:"Nibiru M1|Nibiru Jupiter One",NexoTablet:"NEXO NOVA|NEXO 10|NEXO AVIO|NEXO FREE|NEXO GO|NEXO EVO|NEXO 3G|NEXO SMART|NEXO KIDDO|NEXO MOBI",LeaderTablet:"TBLT10Q|TBLT10I|TBL-10WDKB|TBL-10WDKBO2013|TBL-W230V2|TBL-W450|TBL-W500|SV572|TBLT7I|TBA-AC7-8G|TBLT79|TBL-8W16|TBL-10W32|TBL-10WKB|TBL-W100",UbislateTablet:"UbiSlate[\\s]?7C",PocketBookTablet:"Pocketbook",KocasoTablet:"\\b(TB-1207)\\b",Hudl:"Hudl HT7S3|Hudl 2",TelstraTablet:"T-Hub2",GenericTablet:"Android.*\\b97D\\b|Tablet(?!.*PC)|BNTV250A|MID-WCDMA|LogicPD Zoom2|\\bA7EB\\b|CatNova8|A1_07|CT704|CT1002|\\bM721\\b|rk30sdk|\\bEVOTAB\\b|M758A|ET904|ALUMIUM10|Smartfren Tab|Endeavour 1010|Tablet-PC-4|Tagi Tab|\\bM6pro\\b|CT1020W|arc 10HD|\\bJolla\\b|\\bTP750\\b"},a="object"===("undefined"==typeof navigator?"undefined":r(navigator))?navigator.userAgent:"",i="";for(var n in t){var s=new RegExp(t[n],"i");if(s.test(a)){i=s;break}}switch(e.operator){case"is":return""!==i;case"is_not":return""===i}return!0},check_loggedin:function(e){if(!advads_pro_utils.isset(e.operator)||!advads_pro_utils.isset(e.type))return!0;var t=this.check_stored(e,(function(e,t){return!0===t}));switch(e.operator){case"is":return!0===t;case"is_not":return!1===t}return!0},check_capability:function(e){if(!advads_pro_utils.isset(e.operator)||!advads_pro_utils.isset(e.value)||!advads_pro_utils.isset(e.type))return!0;var t=this.check_stored(e,(function(e,t){return t===e.value}));switch(e.operator){case"can":return!0===t;case"can_not":return!1===t}return!0},check_role:function(e){if(!advads_pro_utils.isset(e.operator)||!advads_pro_utils.isset(e.value)||!advads_pro_utils.isset(e.type))return!0;var t=this.check_stored(e,(function(e,t){return t===e.value}));switch(e.operator){case"is":return!0===t;case"is_not":return!1===t}return!0},check_geo_targeting:function(e){if(!advads_pro_utils.isset(e.type)||!advads_pro_utils.isset(e.operator))return!0;var t=this.check_stored(e,(function(e,t){return"object"!==r(t)||(t.is_sucuri?this.check_geo_sucuri(e,t):this.check_geo_default(e,t))}),this);return t},check_geo_default:function(e,t){var a=e.city?e.city.trim().toLowerCase():"",i=e.region?e.region.trim().toLowerCase():"",n=e.country?e.country.trim():"",r=(""+t.visitor_city).toLowerCase(),s=(""+t.visitor_region).toLowerCase(),o=""+t.continent_code,d=t.country_code;if(0===n.indexOf("CONT_")&&(d="CONT_"+o),"latlon"===e.geo_mode){if(this.check_for_valid_lat_lon_options(e)){var _=advads_pro_utils.calculate_distance(parseFloat(t.current_lat),parseFloat(t.current_lon),parseFloat(e.lat),parseFloat(e.lon),e.distance_unit);return"gt"===e.distance_condition?_>e.distance:_<=e.distance}return!0}return"is_not"===e.operator?a?a!==r:i?i!==s:"EU"===n?!t.is_eu_state:n!==d:a?a===r:i?i===s:"EU"===n?t.is_eu_state:n===d},check_for_valid_lat_lon_options:function(e){return advads_pro_utils.is_numeric(e.lat)&&advads_pro_utils.is_numeric(e.lon)&&""!==e.distance_condition&&advads_pro_utils.is_numeric(e.distance)&&""!==e.distance_unit},check_geo_sucuri:function(e,t){e.operator&&e.operator;var a=e.country?e.country.trim():"";return"is_not"===e.operator?"EU"===a?!t.is_eu_state:a!==country_code:"EU"===a?t.is_eu_state:a===country_code},check_buddypress_profile_field:function(e){if(!(advads_pro_utils.isset(e.operator)&&advads_pro_utils.isset(e.value)&&advads_pro_utils.isset(e.type)&&advads_pro_utils.isset(e.field)))return!0;var t=this.check_stored(e,(function(e,t){if("object"!==r(t))return!0;if(t.field!==e.field)return!1;var a=t.data;if(Array.isArray(a)){var i=-1!==["contain","start","end","match","regex"].indexOf(e.operator);return a.length?i?a.some((function(t){return Advads_passive_cb_Conditions.helper_check_string(t,e)})):a.every((function(t){return Advads_passive_cb_Conditions.helper_check_string(t,e)})):!i}return Advads_passive_cb_Conditions.helper_check_string(t.data,e)}),this);return t},check_stored:function(e,t,a){var i=Advads_passive_cb_Conditions.get_stored_info()[e.type];if("object"!==r(i))return!0;for(var n in i)if(i.hasOwnProperty(n)){var s=i[n];if("object"===r(s)&&void 0!==s.data)if(t.call(a,e,s.data))return!0}return!1},helper_check_string:function(e,t){var a=t.operator,i=t.value;if("string"!=typeof i||""===i)return!0;var n=!0;switch(a){case"contain":n=-1!==e.indexOf(i);break;case"contain_not":n=-1===e.indexOf(i);break;case"start":n=0===e.lastIndexOf(i,0);break;case"start_not":n=0!==e.lastIndexOf(i,0);break;case"end":n=e.slice(-i.length)===i;break;case"end_not":n=e.slice(-i.length)!==i;break;case"match":n=e===i;break;case"match_not":n=e!==i;break;case"regex":case"regex_not":try{var r=new RegExp(i);n=!!e.match(r),"regex_not"===a&&(n=!n)}catch(e){advads_pro_utils.log('regular expression"'+i+'" in visitor condition is broken')}}return n},get_stored_info:function(){try{var e=JSON.parse(advads.get_cookie(this.VISITOR_INFO_COOKIE_NAME))}catch(e){}return"object"!==r(e)||"object"!==r(e.conditions)?{}:e.conditions},maybe_replace_cookie_operator:function(e){var t={show:"match",hide:"match_not"};return t[e]?t[e]:e}},_=function(){window.Advads_passive_cb_Conditions=d}},669:e=>{e.exports=jQuery}},t={};function i(a){var n=t[a];if(void 0!==n)return n.exports;var r=t[a]={id:a,loaded:!1,exports:{}};return e[a](r,r.exports,i),r.loaded=!0,r.exports}i.amdO={},i.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return i.d(t,{a:t}),t},i.d=(e,t)=>{for(var a in t)i.o(t,a)&&!i.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})},i.hmd=e=>((e=Object.create(e)).children||(e.children=[]),Object.defineProperty(e,"exports",{enumerable:!0,set:()=>{throw new Error("ES Modules may not assign module.exports or exports.*, Use ESM export syntax, instead: "+e.id)}}),e),i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);var n=i(669),r=i.n(n);function s(e,t){var a="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!a){if(Array.isArray(e)||(a=function(e,t){if(e){if("string"==typeof e)return o(e,t);var a={}.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?o(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){a&&(e=a);var i=0,n=function(){};return{s:n,n:function(){return i>=e.length?{done:!0}:{done:!1,value:e[i++]}},e:function(e){throw e},f:n}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var r,s=!0,d=!1;return{s:function(){a=a.call(e)},n:function(){var e=a.next();return s=e.done,e},e:function(e){d=!0,r=e},f:function(){try{s||null==a.return||a.return()}finally{if(d)throw r}}}}function o(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,i=Array(t);a<t;a++)i[a]=e[a];return i}function d(e){return d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},d(e)}function _(e,t){for(var a=0;a<t.length;a++){var i=t[a];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,c(i.key),i)}}function c(e){var t=function(e,t){if("object"!=d(e)||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var i=a.call(e,t||"default");if("object"!=d(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==d(t)?t:t+""}var l=function(){return e=function e(t,a){if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),"object"!==d(t)||!advads_pro_utils.isset(t.id)||!advads_pro_utils.isset(t.title)||!advads_pro_utils.isset(t.content))throw new SyntaxError("Can not create Advads_passive_cb_Ad obj");this.id=t.id,this.title=t.title,this.content=t.content?t.content:"",this.type=t.type,this.expiry_date=parseInt(t.expiry_date)||0,this.visitors=t.visitors,this.once_per_page=t.once_per_page,this.elementid=a||null,this.day_indexes=t.day_indexes?t.day_indexes:null,this.debugmode=t.debugmode,this.tracking_enabled=void 0===t.tracking_enabled||!0===t.tracking_enabled,this.blog_id=t.blog_id?t.blog_id:"",this.privacy=t.privacy?t.privacy:{},this.position=t.position?t.position:"",document.dispatchEvent(new CustomEvent("advanced-ads-passive-cb-ad-info",{detail:{ad:this,adInfo:t}}))},(t=[{key:"output",value:function(e){if(e=e||{},this.debugmode){var t=this.can_display({ignore_debugmode:!0})?"displayed":"hidden",a=r()(this.content).find(".advads-passive-cb-debug").data(t);this.content=this.content.replace("##advanced_ads_passive_cb_debug##",a)}if(e.do_has_ad&&advanced_ads_pro.hasAd(this.id,"ad",this.title,"passive"),e.track&&this.tracking_enabled&&(advanced_ads_pro.passive_ads[this.blog_id]||(advanced_ads_pro.passive_ads[this.blog_id]=[]),advanced_ads_pro.passive_ads[this.blog_id].push(this.id)),advads_pro_utils.log("output passive ad",this.id,this.elementid,this.content),!e.inject)return this.content;advanced_ads_pro.inject(this.elementid,this.content)}},{key:"can_display",value:function(e){if(e=e||{},this.debugmode&&!e.ignore_debugmode)return!0;if(""===this.content.trim())return advads_pro_utils.log("passive ad id",this.id,"cannot be displayed: empty content"),!1;if(!this.can_display_by_visitor())return advads_pro_utils.log("passive ad id",this.id,"cannot be displayed: by_visitor"),!1;if(!this.can_display_by_expiry_date())return advads_pro_utils.log("passive ad id",this.id,"cannot be displayed: by_expiry_date"),!1;if(!this.can_display_by_timeout())return advads_pro_utils.log("passive ad id",this.id,"cannot be displayed: by_timeout"),!1;if(!this.can_display_by_display_limit())return advads_pro_utils.log("passive ad id",this.id,"cannot be displayed: by_display_limit"),!1;if(!this.can_display_by_weekday())return advads_pro_utils.log("passive ad id",this.id,"cannot be displayed: by_weekday"),!1;if(!this.can_display_by_cfp())return advads_pro_utils.log("passive ad id",this.id,"cannot be displayed: by_cfp"),!1;if(!this.can_display_by_consent())return advads_pro_utils.log("passive ad id",this.id,"cannot be displayed: by_consent"),!1;var t={display:!0};return document.dispatchEvent(new CustomEvent("advanced-ads-passive-cb-can-display",{detail:{canDisplay:t,checkOptions:e,adInfo:this}})),t.display}},{key:"can_display_by_visitor",value:function(){if(!Array.isArray(this.visitors)||0===this.visitors.length)return!0;window.Advads_passive_cb_Conditions.init();for(var e,t=0,a=!1,i=0;i<this.visitors.length;++i)if(e=this.visitors[t],a&&"or"===e.connector)t++;else if(a=window.Advads_passive_cb_Conditions.frontend_check(e,this))t++;else if(t++,!this.visitors[t]||"or"!==this.visitors[t].connector)return!1;return!0}},{key:"can_display_by_expiry_date",value:function(){return this.expiry_date<=0||this.expiry_date>~~((new Date).getTime()/1e3)}},{key:"can_display_by_weekday",value:function(){if(!this.day_indexes)return!0;var e=new Date,t=window.advanced_ads_pro_ajax_object.wp_timezone_offset/60,a=t/60>=0?Math.floor(t/60):Math.ceil(t/60);return(t=e.getUTCMinutes()+t%60)>60&&(a++,t%=60),e.setHours(e.getUTCHours()+a),e.setMinutes(t),r().inArray(e.getDay(),this.day_indexes)>=0}},{key:"can_display_by_timeout",value:function(){return!advads_pro_utils.isset(advads.get_cookie("timeout_"+this.id))}},{key:"can_display_by_display_limit",value:function(){if(this.once_per_page)for(var e=advanced_ads_pro.ads.length,t=0;t<e;t++)if("ad"===advanced_ads_pro.ads[t].type&&parseInt(advanced_ads_pro.ads[t].id,10)===this.id)return!1;return!0}},{key:"can_display_by_cfp",value:function(){var e,t=s(this.visitors);try{for(t.s();!(e=t.n()).done;){var a=e.value;if("ad_clicks"===a.type&&a["exclude-from-global"])return!0}}catch(e){t.e(e)}finally{t.f()}return!advads.get_cookie("advads_pro_cfp_ban")}},{key:"can_display_by_consent",value:function(){if(!advads.privacy||this.privacy.ignore||"adsense"===this.type&&advads.privacy.is_adsense_npa_enabled()||("image"===this.type||"dummy"===this.type)&&!this.privacy.needs_consent)return!0;var e=advads.privacy.get_state();return"accepted"===e||"not_needed"===e}}])&&_(e.prototype,t),a&&_(e,a),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,a}();function u(e){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},u(e)}function p(e,t){for(var a=0;a<t.length;a++){var i=t[a];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,T(i.key),i)}}function T(e){var t=function(e,t){if("object"!=u(e)||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var i=a.call(e,t||"default");if("object"!=u(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==u(t)?t:t+""}var v=function(){return e=function e(t,a){if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),!("object"===u(t)&&t.hasOwnProperty("id")&&t.hasOwnProperty("type")&&t.hasOwnProperty("ads")&&t.hasOwnProperty("placement_info")&&"object"===u(t.ads)))throw new SyntaxError("Can not create Advads_passive_cb_Placement obj");this.id=t.id,this.ajax_query=t.ajax_query,this.type=t.type,this.element_id=a,this.ads=t.ads,this.ads_for_ab=t.ads_for_ab,this.groups_for_ab=t.groups_for_ab,this.placement_info=t.placement_info,this.placement_id=advads_pro_utils.isset_nested(this.placement_info,"id")?parseInt(this.placement_info.id,10):null,this.group_info=t.group_info,this.group_wrap=t.group_wrap,this.server_info_duration=parseInt(t.server_info_duration,10)||0,this.server_conditions=t.server_conditions,t.inject_before&&advanced_ads_pro.inject_before.push({elementId:this.element_id,data:t.inject_before})},(t=[{key:"can_display",value:function(){if(advads_pro_utils.isset_nested(this.placement_info,"test_id")&&r().inArray(this.placement_info.slug,advanced_ads_pro.get_random_placements())<0)return!1;if(advads_pro_utils.isset_nested(this.placement_info,"layer_placement","close","enabled")&&this.placement_info.layer_placement.close.enabled&&advads_pro_utils.isset_nested(this.placement_info,"layer_placement","close","timeout_enabled")&&this.placement_info.layer_placement.close.timeout_enabled&&advads_pro_utils.isset(advads.get_cookie("timeout_placement_"+this.placement_info.slug)))return!1;if(advads_pro_utils.isset_nested(this.placement_info,"close","enabled")&&this.placement_info.close.enabled&&advads_pro_utils.isset_nested(this.placement_info,"close","timeout_enabled")&&this.placement_info.close.timeout_enabled&&advads_pro_utils.isset(advads.get_cookie("timeout_placement_"+this.placement_info.slug)))return!1;if(advads_pro_utils.isset_nested(this.placement_info,"options")&&"object"===u(this.placement_info.options)){var e=this.placement_info.options;if(!advads_pro_utils.selector_exists(e))return!1}return!0}},{key:"can_use_passive_cb",value:function(){if(!this.ajax_query)return!0;var e=window.Advads_passive_cb_Conditions.get_stored_info(),t=~~((new Date).getTime()/1e3);for(var a in this.server_conditions)if(this.server_conditions.hasOwnProperty(a)){var i=e[this.server_conditions[a].type];if("object"!==u(i))return!1;var n=i[a];if("object"!==u(n))return!1;if((parseInt(n.time,10)||0)+this.server_info_duration<t)return!1}return!0}},{key:"output",value:function(){var e=!0,t=this.get_group_for_adblocker();if(this.can_display())switch(this.type){case"ad":if(!this.can_display())break;var a=new Advads_passive_cb_Ad(this.ads[this.id],this.element_id);if(!a.can_display())break;var i=this.get_ad_for_adblocker();if(t){this.swap_group_info(t);var n=new Advads_passive_cb_Group(this,this.element_id);n.output(),e=n.is_empty;break}i&&i.can_display()&&(a=i),a.output({track:!advads.privacy||"unknown"!==advads.privacy.get_state(),inject:!0,do_has_ad:!0}),e=!1;break;case"group":if("object"===u(this.group_info)){t&&this.swap_group_info(t);var r=new Advads_passive_cb_Group(this,this.element_id);r.output(),e=r.is_empty}}advanced_ads_pro.dispatchWrapperCBEvent(this.element_id,e,"passive",{emptyCbOption:Boolean(this.placement_info.cache_busting_empty)}),advanced_ads_pro.observers.fire({event:"inject_placement",id:this.placement_id,is_empty:e,cb_type:"passive"}),advanced_ads_pro.hasAd(this.placement_id,"placement",this.placement_info.title,"passive")}},{key:"swap_group_info",value:function(e){this.id=e.id,this.type="group",this.group_info=e}},{key:"get_ad_for_adblocker",value:function(){return!(!advanced_ads_pro.adblocker_active||!this.ads_for_ab)&&new Advads_passive_cb_Ad(this.ads_for_ab[Object.keys(this.ads_for_ab)[0]],this.element_id)}},{key:"get_group_for_adblocker",value:function(){return!(!advanced_ads_pro.adblocker_active||!this.groups_for_ab)&&this.groups_for_ab}}])&&p(e.prototype,t),a&&p(e,a),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,a}();function f(e,t){var a="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!a){if(Array.isArray(e)||(a=function(e,t){if(e){if("string"==typeof e)return h(e,t);var a={}.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?h(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){a&&(e=a);var i=0,n=function(){};return{s:n,n:function(){return i>=e.length?{done:!0}:{done:!1,value:e[i++]}},e:function(e){throw e},f:n}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var r,s=!0,o=!1;return{s:function(){a=a.call(e)},n:function(){var e=a.next();return s=e.done,e},e:function(e){o=!0,r=e},f:function(){try{s||null==a.return||a.return()}finally{if(o)throw r}}}}function h(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,i=Array(t);a<t;a++)i[a]=e[a];return i}function A(e){return A="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},A(e)}var y={ads:[],passive_ads:{},deferedAds:[],blockme:!1,blockmeQueue:[],observers:r().Callbacks(),postscribeObservers:r().Callbacks(),random_placements:!1,iterations:0,adblocker_active:!1,injected:0,injected_done:0,options:{action:"advads_ad_select"},inject_before:[],ajaxAdArgsByElementId:{},dispatchWrapperCBEvent:function(e,t,a,i){void 0===i&&(i={}),document.dispatchEvent(new CustomEvent("advads_pro_cache_busting_done",{detail:{elementId:e,isEmpty:t,type:a,extra:i}}))},isBusy:!1,set busy(e){this.isBusy=e,document.dispatchEvent(new CustomEvent("advanced_ads_pro."+(e?"busy":"idle")))},get busy(){return this.isBusy},load:function(e){var t,a,i,n,s,o;t=e.hasOwnProperty("id")?e.id:null,a=e.hasOwnProperty("method")?e.method:null,i=e.hasOwnProperty("params")&&"object"===A(e.params)?this.add_general_ajax_args(e.params):{},n=e.hasOwnProperty("elementid")?e.elementid:null,s=advanced_ads_pro_ajax_object.lazy_load_module_enabled&&i&&"enabled"===i.lazy_load,o=e.hasOwnProperty("blog_id")?e.blog_id:"";var d=e.hasOwnProperty("server_conditions")?e.server_conditions:"";if(n&&this.iterations>1&&r()("."+n).empty(),i&&"object"===A(i)){if(!window.advads_pro_utils.selector_exists(i))return;if(i.test_id&&r().inArray(i.slug,this.get_random_placements())<0)return;i.adblocker_active=this.adblocker_active,i=JSON.stringify(i)}var _={ad_id:t,ad_method:a,ad_args:i,elementId:n,blog_id:o,server_conditions:d};s?advanced_ads_pro.lazy_load.add(n,"ajax",_):this.deferedAds[this.deferedAds.length]=_},add_general_ajax_args:function(e){return window.advads_pro_utils.isset(e.post)&&advads_ajax_queries_args[e.post]&&(e.post=advads_ajax_queries_args[e.post]),e},hasAd:function(e,t,a,i,n){var r={id:e,type:t,title:a,cb_type:i,elementId:n};this.ads.push(r),this.observers.fire({event:"hasAd",ad:r})},injectBlocked:function(){var e,t,a=this.blockmeQueue,i=a.length;for(this.blockmeQueue=[],t=0;t<i;t+=1)e=a[t],this.inject(e[0],e[1])},_inject_before:function(e,t){return e&&window.advads_pro_utils.each(advanced_ads_pro.inject_before,(function(a){a.elementId===e&&(window.advads_pro_utils.each(a.data,(function(e){t.append(e)})),t=r()("."+e),a.data=[])})),t},inject:function(e,t){var a,i,n=this;if(this.blockme)this.blockmeQueue.push([e,t]);else{this.injected++;for(var s=new RegExp(/^([\s\S]*?)<script[^>]+?data-tcf="waiting-for-consent"[^>]+>(.+?)<\/script>([\s\S]*)$/i),o=s.exec(t);null!==o;){var d=document.createElement("script");d.setAttribute("type","text/plain"),d.textContent=o[2],t=o[1]+advads.privacy.decode_ad(d,!1)+o[3],o=s.exec(t)}try{if(a=t.match(/<script[^>]+src/)&&-1===t.indexOf(" async"),null===e)i=r()("head ");else if(!(i=r()("."+e)).length)return void this.injected--;if(a)this.blockme=!0,(i=n._inject_before(e,i)).each((function(){var e=r()(this);advads_postscribe(e,t,{beforeWriteToken:n.beforeWriteToken,afterAsync:function(){n.blockme=!1,n.injectBlocked()},done:function(){n.postscribeObservers.fire({event:"postscribe_done",ref:i,ad:t})},error:function(e){console.error(e),advanced_ads_pro.injected--}})}));else{if(i=n._inject_before(e,i),-1!==t.indexOf("gform.initializeOnLoaded")){var _=document.createElement("DIV");_.innerHTML=t;var c=_.querySelector('input[type="submit"]');if(c){var l=c.getAttribute("onclick"),u=c.getAttribute("onkeypress"),p=c.id,b=document.createElement("SCRIPT");c.removeAttribute("onclick"),c.removeAttribute("onkeypress"),b.innerHTML=["click","keypress"].map((function(e){return'document.body.addEventListener("'.concat(e,'", function(event){if (event.target && event.target.id === "').concat(this.id,'"){').concat(this[e],"}})")}),{id:p,click:l,keypress:u}).join(";"),_.append(b)}var T=_.querySelector("form"),v=T.getAttribute("action");v.includes("#gf")&&T.setAttribute("action","".concat(window.location.href.split("#")[0],"#").concat(v.split("#")[1])),t=_.innerHTML}i.each((function(){var e=r()(this);advads_postscribe(e,t,{beforeWriteToken:n.beforeWriteToken,done:function(){n.postscribeObservers.fire({event:"postscribe_done",ref:i,ad:t})},error:function(e){console.error(e),advanced_ads_pro.injected--}})}))}}catch(e){console.error(e),this.injected--}}},beforeWriteToken:function(e){if("startTag"===e.type)for(var t in e.attrs){var a=e.attrs[t];'{"'===a.substring(0,2)&&(a=a.replace(/\"/g,"&quot;"),e.attrs[t]=a)}return"atomicTag"===e.type&&e.src&&(e.src=e.src.replace(/&amp;/g,"&")),e},loadAjaxAds:function(){if(!this.deferedAds.length)return advanced_ads_pro.observers.fire({event:"inject_ajax_ads",ad_ids:[]}),void advanced_ads_pro.return_to_idle_injections_done();new Date;var e={action:"advads_ad_select",ad_ids:this.ads,deferedAds:this.deferedAds,consent:"undefined"==typeof advads?"not_needed":advads.privacy.get_state(),theId:window.advanced_ads_pro_ajax_object.the_id,isSingular:advanced_ads_pro_ajax_object.is_singular};document.dispatchEvent(new CustomEvent("advanced-ads-ajax-cb-payload",{detail:{payload:e}}));var t,a=f(this.deferedAds);try{for(a.s();!(t=a.n()).done;){var i=t.value;this.ajaxAdArgsByElementId[i.elementId]=JSON.parse(i.ad_args)}}catch(e){a.e(e)}finally{a.f()}document.dispatchEvent(new CustomEvent("advads_ajax_ad_select",{detail:e})),this.deferedAds=[];var n=this;r().ajax({url:advanced_ads_pro_ajax_object.ajax_url,method:"POST",data:e,dataType:"json"}).done((function(e){var t={};if(Array.isArray(e)){advanced_ads_pro.observe_injections();for(var a=0;a<e.length;a++){var i=e[a];if(i.hasOwnProperty("status")&&"success"===i.status&&i.hasOwnProperty("item")&&i.item&&(i.inject_before&&advanced_ads_pro.inject_before.push({elementId:i.elementId,data:i.inject_before}),advanced_ads_pro.inject(i.elementId,i.item,!0),i.hasOwnProperty("ads")&&Array.isArray(i.ads)))for(var r=0;r<i.ads.length;r++)if(advanced_ads_pro.hasAd(i.ads[r].id,i.ads[r].type,i.ads[r].title,"ajax",i.elementId),"ad"===i.ads[r].type&&i.ads[r].tracking_enabled){var s=i.blog_id?i.blog_id:1;void 0===t[s]&&(t[s]=[]),t[s].push(i.ads[r].id)}i.status&&advanced_ads_pro.dispatchWrapperCBEvent(i.elementId,"error"===i.status,"ajax",{emptyCbOption:Boolean(n.ajaxAdArgsByElementId[i.elementId].cache_busting_empty)}),i.hasOwnProperty("method")&&"placement"===i.method&&advanced_ads_pro.observers.fire({event:"inject_placement",id:i.id,is_empty:!!i.item,cb_type:"ajax"})}advanced_ads_pro.observers.fire({event:"inject_ajax_ads",ad_ids:t}),window.advads_pro_utils.log("AJAX CB response\n",e),document.body.dispatchEvent(new CustomEvent("advads_ajax_cb_response",{detail:{response:e}})),advanced_ads_pro.return_to_idle_injections_done()}})).fail((function(){advanced_ads_pro.return_to_idle_injections_done()}))},get_random_placements:function(e){return!1!==this.random_placements||(this.random_placements=[],window.advads_pro_utils.each_key(e,(function(e,t){if("object"===A(t)){var a=window.advads_pro_utils.get_random_el_by_weight(t.placements);a&&this.random_placements.push(a)}}),this)),this.random_placements},create_non_existent_arrays:function(){0===this.iterations&&(window.advads_pro_utils.each(["advads_passive_ads","advads_passive_groups","advads_passive_placements"],(function(e){window.advads_pro_utils.isset(window[e])||(window[e]={})})),window.advads_pro_utils.each(["advads_placement_tests","advads_ajax_queries","advads_has_ads","advads_js_items"],(function(e){window.advads_pro_utils.isset(window[e])||(window[e]=[])})))},process_passive_cb:function(){var e=this;e.create_non_existent_arrays(),window.advads_pro_utils.print_debug_arrays();var t=function(t){e.busy=!0,e.iterations++,e.lazy_load.clear(),e.adblocker_active=t,e.observe_injections(),window.advads_pro_utils.each(advads_has_ads,(function(e){advanced_ads_pro.hasAd.apply(advanced_ads_pro,e)})),e.get_random_placements(advads_placement_tests),window.advads_pro_utils.each_key(window.advads_passive_ads,(function(e,t){var a=(e+"").indexOf("_");-1!==a&&(e=e.slice(0,a)),window.advads_pro_utils.each(t.elementid,(function(a){advanced_ads_pro.iterations>1&&r()("."+a).empty();var i=new Advads_passive_cb_Ad(t.ads[e],a);i.can_display()&&i.output({track:!0,inject:!0,do_has_ad:!0})}))})),window.advads_pro_utils.each_key(window.advads_passive_groups,(function(e,t){window.advads_pro_utils.each(t.elementid,(function(e){advanced_ads_pro.iterations>1&&r()("."+e).empty(),new Advads_passive_cb_Group(t,e).output()}))})),window.advads_pro_utils.each_key(window.advads_passive_placements,(function(e,t){window.advads_pro_utils.each(t.elementid,(function(a){advanced_ads_pro.iterations>1&&r()("."+a).empty();var i=new Advads_passive_cb_Placement(t,a);i.can_use_passive_cb()?advanced_ads_pro_ajax_object.lazy_load_module_enabled&&t.placement_info.lazy_load&&"enabled"===t.placement_info.lazy_load?advanced_ads_pro.lazy_load.add(a,"passive",{key:e,placement_id:t.placement_info.id}):i.output():advanced_ads_pro.load(i.ajax_query)}))})),window.advads_pro_utils.isset(window.advads_js_items)&&window.advads_pro_utils.each_key(advads_js_items,(function(e,t){advanced_ads_pro.iterations>1||window.advads_pro_utils.selector_exists(t.args)&&(t.inject_before&&advanced_ads_pro.inject_before.push({elementId:t.elementid,data:t.inject_before}),advanced_ads_pro.inject(t.elementid,t.output,!0),window.advads_pro_utils.each(t.has_js_items,(function(e){advanced_ads_pro.hasAd(e.id,e.type,e.title),"ad"===e.type&&(advanced_ads_pro.passive_ads[e.blog_id]||(advanced_ads_pro.passive_ads[e.blog_id]=[]),advanced_ads_pro.passive_ads[e.blog_id].push(e.id))})))})),e.observers.fire({event:"inject_passive_ads",ad_ids:e.passive_ads}),e.passive_ads={},e.process_ajax_ads(advads_ajax_queries),e.lazy_load.enable()};"function"==typeof advanced_ads_check_adblocker?advanced_ads_check_adblocker((function(e){t(e)})):t(!1)},observe_injections:function(){advanced_ads_pro.injected_done>0||advanced_ads_pro.postscribeObservers.add((function(e){"postscribe_done"===e.event&&advanced_ads_pro.injected_done++}))},return_to_idle_injections_done:function(){var e=1e3,t=setInterval((function(){e-=10,(advanced_ads_pro.injected_done>=advanced_ads_pro.injected||e<0)&&(advanced_ads_pro.injected=0,advanced_ads_pro.injected_done=0,advanced_ads_pro.busy=!1,clearInterval(t))}),10)},process_ajax_ads:function(e){Array.isArray(e)&&window.advads_pro_utils.each(e,(function(e){advanced_ads_pro.load(e)})),this.loadAjaxAds()},lazy_load:{lazy_map:{},did_init:!1,add:function(e,t,a){var i,n=document.getElementById(e);n&&(a.placement_id?i=a.placement_id:"placement"===a.ad_method&&(i=a.ad_id),this.lazy_map[e]={node:n,type:t,data:a,offset:this.get_offset(i)})},get_offset:function(e){var t=0;return advanced_ads_pro_ajax_object.lazy_load&&(t=advanced_ads_pro_ajax_object.lazy_load.offsets[e]?parseInt(advanced_ads_pro_ajax_object.lazy_load.offsets[e],10):parseInt(advanced_ads_pro_ajax_object.lazy_load.default_offset,10)),t},clear:function(){this.lazy_map={}},enable:function(){var e=this;e.did_init?r()(window).trigger("scroll"):(e._create_scroll_handler(),e.did_init=!0)},_create_scroll_handler:function(){var e=this,t=!0;function a(){var a=r()(window).height();window.advads_pro_utils.each_key(e.lazy_map,(function(t,i){var n=i.node.getBoundingClientRect(),r=i.offset;n.top+r>=0&&n.bottom-r<=a&&e._display(t)})),t=!1}function i(e){var t=window.requestAnimationFrame||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame||function(e){return setTimeout(e,16)};t.call(window,e)}r()(window).on("scroll",(function(){t||(t=!0,i(a))})),i(a)},_display:function(e){var t=this.lazy_map[e];if(t){if(delete this.lazy_map[e],"ajax"===t.type)advanced_ads_pro.deferedAds.push(t.data),advanced_ads_pro.process_ajax_ads();else{var a=window.advads_passive_placements[t.data.key];new Advads_passive_cb_Placement(a,e).output(),advanced_ads_pro.observers.fire({event:"inject_passive_ads",ad_ids:advanced_ads_pro.passive_ads}),advanced_ads_pro.passive_ads={}}advanced_ads_pro.busy=!1}}}},P=i(520);function m(e){return m="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},m(e)}function M(e,t){for(var a=0;a<t.length;a++){var i=t[a];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,g(i.key),i)}}function g(e){var t=function(e,t){if("object"!=m(e)||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var i=a.call(e,t||"default");if("object"!=m(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==m(t)?t:t+""}var S=function(){return e=function e(t,a){if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),!(advads_pro_utils.isset(t.group_info.id)&&advads_pro_utils.isset(t.group_info.type)&&advads_pro_utils.isset(t.group_info.weights)&&advads_pro_utils.isset(t.group_info.ordered_ad_ids)&&advads_pro_utils.isset(t.group_info.ad_count)&&advads_pro_utils.isset(t.ads)))throw new SyntaxError("Can not create Advads_passive_cb_Group obj");this.id=t.group_info.id,this.name=t.group_info.name?t.group_info.name:this.id,this.type=t.group_info.type,this.weights=t.group_info.weights,this.ordered_ad_ids=t.group_info.ordered_ad_ids,this.ad_count=t.group_info.ad_count,this.elementid=a||null,this.slider_options=!!advads_pro_utils.isset(t.group_info.slider_options)&&t.group_info.slider_options,this.refresh_enabled=advads_pro_utils.isset(t.group_info.refresh_enabled),advads_pro_utils.isset(t.group_info.refresh_interval_for_ads)?this.refresh_interval=t.group_info.refresh_interval_for_ads:advads_pro_utils.isset(t.group_info.refresh_interval)?this.refresh_interval=t.group_info.refresh_interval:this.refresh_interval=2e3,this.placement=t instanceof Advads_passive_cb_Placement&&t,this.random=t.group_info.random,this.ads=t.ads,this.group_wrap=t.group_wrap,this.is_empty=!0},(t=[{key:"output",value:function(){var e=this.placement&&this.placement.get_ad_for_adblocker();if(advanced_ads_pro.hasAd(this.id,"group",this.name,"passive"),e||!this.refresh_enabled){var t,a=0,i=[],n=this.placement&&this.placement.get_group_for_adblocker();switch(n&&(this.ads=n.ads),this.type){case"ordered":case"slider":t=this.shuffle_ordered_ads(this.ordered_ad_ids,this.weights);break;case"grid":t=this.random?this.shuffle_ads():this.shuffle_ordered_ads(this.ordered_ad_ids,this.weights);break;default:t=this.shuffle_ads()}if(Array.isArray(t)&&r().isPlainObject(this.ads)){for(var s=0;s<t.length;s++)if(this.ads.hasOwnProperty(t[s])){var o=this.ads[t[s]];if("object"===m(o)){var d=new Advads_passive_cb_Ad(o,this.elementid);if(d.can_display()){e&&(d=e);var _=!advads.privacy||"unknown"!==advads.privacy.get_state();"slider"===this.type&&this.slider_options||this.group_wrap?i.push(d.output({track:_,inject:!1,do_has_ad:!0})):d.output({track:_,inject:!0,do_has_ad:!0}),a++,this.is_empty=!1}}if(a===this.ad_count)break;if(!this.is_empty&&e)break}i.length&&("slider"===this.type&&this.slider_options&&(i=this.output_slider(i)),advanced_ads_pro.inject(this.elementid,this.add_group_wrap(i,a)))}}else this.output_refresh()}},{key:"output_refresh",value:function(){var e=this.ordered_ad_ids,t=[],a=this,i=!1,n=[],s=0,o=(this.refresh_interval,r()("."+a.elementid));function d(e){if(r().inArray(e.id,n)<0&&e.tracking_enabled){var t={};t[e.blog_id]=[e.id],advanced_ads_pro.observers.fire({event:"inject_passive_ads",ad_ids:t})}}function _(e){return"object"!==m(a.refresh_interval)?parseInt(a.refresh_interval,10)||2e3:parseInt(a.refresh_interval[e],10)||2e3}function c(e){var t="";return advads_pro_utils.isset_nested(a.placement,"placement_info","options","placement_position")&&(t=a.placement.placement_info.options.placement_position),-1===["left","right"].indexOf(t)&&(t=e.position),t}o=advanced_ads_pro._inject_before(this.elementid,o),Array.isArray(e)&&r().isPlainObject(this.ads)&&function l(){for(var u=function(){var t;if("ordered"===a.type)if(-1!==(t=e.indexOf(i)))var n=e.slice(t+1).concat(e.slice(0,t));else n=e;else-1!==(t=(n=a.shuffle_ads()).indexOf(i))&&n.splice(t,1);return n}(),p=u.length,b=0;b<p;b++){var T=u[b],v=a.ads[T];if("object"===m(v)){var f=new Advads_passive_cb_Ad(v,a.elementid);if(f.can_display()){if(0===s)t=[f.output({track:!0,inject:!1,do_has_ad:!0})],advanced_ads_group_refresh.prepare_wrapper(o,c(f),!0);else{var h=r().inArray(T,n)<0;t=[f.output({track:!1,inject:!1,do_has_ad:h})],d(f),advanced_ads_group_refresh.prepare_wrapper(o,c(f),!1)}n.push(f.id),advanced_ads_pro.inject(a.elementid,a.add_group_wrap(t,1)),a.is_empty=!1,s++,setTimeout((function(){a.placement&&!a.placement.can_display()||l()}),_(T)),i=f.id;break}}}}()}},{key:"add_group_wrap",value:function(e,t){if(!e.length)return"";var a="",i="";if(this.group_wrap)for(var n=0;n<this.group_wrap.length;n++){var r=this.group_wrap[n];if(r.min_ads=r.min_ads||1,!("object"!==m(r)||r.min_ads>t))if(r.before&&(a=r.before+a),r.after&&(i+=r.after),"string"==typeof r.each)for(var s=0;s<e.length;s++)e[s]=r.each.replace("%s",e[s]);else if("object"===m(r.each)){var o=r.each;for(s=0;s<e.length;s++){for(var d in o){var _=!1;if(o.hasOwnProperty(d)&&"all"!==d&&(1+s)%parseInt(d,10)==0){e[s]=o[d].replace("%s",e[s]),_=!0;break}}!_&&o.all&&(e[s]=o.all.replace("%s",e[s]))}}}return a+e.join("")+i}},{key:"output_slider",value:function(e){var t;return e.length>1&&"function"==typeof r().fn.unslider&&(t=e.join("</li><li>"),(e=[]).push('<div id="'+this.slider_options.slider_id+'" class="'+this.slider_options.init_class+" "+this.slider_options.prefix+'slider"><ul><li>'),e.push(t),e.push("</li></ul></div>"),e.push("<script>jQuery(function() { jQuery('."+this.slider_options.init_class+"').unslider({ "+this.slider_options.settings+" }); });<\/script>")),e}},{key:"shuffle_ordered_ads",value:function(e,t){for(var a=[],i=0;i<e.length;i++){var n=t[e[i]];if(!n)return e;a.push(n)}var r=a.length,s=0;for(i=1;i<=r;i++)if(i==r||a[i]!==a[i-1]){var o=i-s;if(1!==o){var d=advads_pro_utils.shuffle_array(e.slice(s,s+o)),_=[s,o].concat(d);Array.prototype.splice.apply(e,_)}s=i}return e}},{key:"shuffle_ads",value:function(){for(var e=[],t=r().extend({},this.weights),a=advads_pro_utils.get_random_el_by_weight(t);null!==a;)delete t[a],e.push(parseInt(a,10)),a=advads_pro_utils.get_random_el_by_weight(t);return e}}])&&M(e.prototype,t),a&&M(e,a),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,a}();function w(e){return w="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},w(e)}var B,C={debug:window.location&&window.location.hash&&-1!==window.location.hash.indexOf("#debug=true"),each:function(e,t,a){var i,n=e&&e.length||0;for(i=0;i<n;i++)t.call(a,e[i],i)},each_key:function(e,t,a){var i;if("object"===w(e))for(i in e)e.hasOwnProperty(i)&&t.call(a,i,e[i])},log:function(){if(this.debug&&this.isset(window.console)){var e=Array.prototype.slice.call(arguments);e.unshift("Advanced Ads CB:"),window.console.log.apply(window.console,e)}},print_debug_arrays:function(){0===advanced_ads_pro.iterations&&(this.log("passive_ads\n",window.advads_passive_ads),this.log("passive_groups\n",window.advads_passive_groups),this.log("passive_placements\n",window.advads_passive_placements),this.log("ajax_queries\n",window.advads_ajax_queries),this.log(window.Advads_passive_cb_Conditions.VISITOR_INFO_COOKIE_NAME+"\n",window.Advads_passive_cb_Conditions.get_stored_info()))},isset:function(e){return void 0!==e},isset_nested:function(e){for(var t=arguments.length,a=1;a<t;a++){if(!e||!e.hasOwnProperty(arguments[a]))return!1;e=e[arguments[a]]}return!0},is_numeric:function(e){return!isNaN(parseFloat(e))&&isFinite(e)},get_random_number:function(e,t){var a=e-.5+Math.random()*(t-e+1);return Math.round(a)},get_random_el_by_weight:function(e,t){var a,i=0;if(t=void 0!==t&&t,"object"===w(e)){for(var n in e)n!==t&&e.hasOwnProperty(n)&&(i+=parseInt(e[n])||0);if(i<1)return null;for(var n in a=advads_pro_utils.get_random_number(1,i),e)if(n!==t&&e.hasOwnProperty(n)&&(a-=e[n])<=0)return n}},bind:function(e,t){return function(){return e.apply(t,arguments)}},shuffle_array:function(e){var t,a,i=e.length;if(!i)return e;for(;--i;)a=~~(Math.random()*(i+1)),t=e[i],e[i]=e[a],e[a]=t;return e},selector_exists:function(e){var t=e[e.inject_by&&"pro_custom_element"!==e.inject_by?"container_id":"pro_custom_element"];if(!t)return!0;var a=r()(t);return a.length?!(!advanced_ads_pro_ajax_object.moveintohidden&&!a.filter(":visible").length)||(advads_pro_utils.log("selector is hidden",t),!1):(advads_pro_utils.log("selector does not exist",t),!1)},deg2rad:function(e){return e*Math.PI/180},calculate_distance:function(e,t,i,n,r){r=r||"km",e=this.deg2rad(e),t=this.deg2rad(t),i=this.deg2rad(i);var s=(n=this.deg2rad(n))-t;a=Math.pow(Math.cos(i)*Math.sin(s),2)+Math.pow(Math.cos(e)*Math.sin(i)-Math.sin(e)*Math.cos(i)*Math.cos(s),2),b=Math.sin(e)*Math.sin(i)+Math.cos(e)*Math.cos(i)*Math.cos(s);var o=Math.atan2(Math.sqrt(a),b);return"mi"===r?3958.755865744*o:6371*o},extract_cookie_data:function(e){try{var t=JSON.parse(e)}catch(t){return e}return"object"!==w(t)?e:t.data}},E={element_ids:{},passiveRefresh:{},collectPassiveRefreshData:function(e){E.passiveRefresh[e.cb_id]=e},switchToPassive:function(e){var t=E.passiveRefresh[e];setTimeout((function(){r()(".".concat(t.cb_id)).empty(),E.launchRefresh(t)}),parseInt(t.default_interval,10))},launchRefresh:function(e){new v({id:e.placement_info.id,type:e.type,ads:e.ads,placement_info:e.placement_info,group_info:e.group_info,group_wrap:e.group_wrap},e.cb_id).output()},add_query:(B=[],function(e,t){e.elementid;var a=(new Date).getTime()+t;B[a]=B[a]||[],B[a].push(e),setTimeout((function(){var e=(new Date).getTime(),t=[];for(var a in B)if(B.hasOwnProperty(a)&&e>a-1e3){for(var i=B[a],n=i.length,r=0;r<n;r++)t.push(i[r]);delete B[a]}advanced_ads_pro.process_ajax_ads(t)}),t)}),find_float:function(e){var t=!1;return e.find("div").each((function(e,a){if("left"===this.style.float||"right"===this.style.float)return t=this.style.float,!1})),t},prepare_wrapper:function(e,t,a){a||(this.maybe_increase_sizes(e),e.empty()),this.set_float(e,t)},maybe_increase_sizes:function(e){var t=e.css("float");-1===["left","right"].indexOf(t)&&(t=!1);var a={};if(t){var i=parseInt(e.css("min-width"),10)||0,n=e.prop("scrollWidth")||0;n>i&&(a["min-width"]=n)}var r=parseInt(e.css("min-height"),10)||0,s=e.prop("scrollHeight")||0;s>r&&(a["min-height"]=s),(a["min-height"]||a["min-width"])&&e.css(a)},set_float:function(e,t){-1===["left","right"].indexOf(t)&&(t=!1),t!==(e.data("prev_float")||!1)&&(e.data("prev_float",t),t?e.css({"min-width":"","min-height":"",float:t}):e.css({"min-width":"","min-height":"",float:""}))}};function I(e,t){var a="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!a){if(Array.isArray(e)||(a=function(e,t){if(e){if("string"==typeof e)return D(e,t);var a={}.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?D(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){a&&(e=a);var i=0,n=function(){};return{s:n,n:function(){return i>=e.length?{done:!0}:{done:!1,value:e[i++]}},e:function(e){throw e},f:n}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var r,s=!0,o=!1;return{s:function(){a=a.call(e)},n:function(){var e=a.next();return s=e.done,e},e:function(e){o=!0,r=e},f:function(){try{s||null==a.return||a.return()}finally{if(o)throw r}}}}function D(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,i=Array(t);a<t;a++)i[a]=e[a];return i}const k=function(){var e={},t={},a=null,i=0,n=function(e){var t={};return e.classList.forEach((function(e){if(e.startsWith("gas_fallback-")){var a=e.split("-");t.ad=a[1],t.fallback=a[2]}})),t},r=function(e){var t=o(e.cbId)||d(e.cbId);t&&(!function(e,t){var a=e.fallback.split("_");if("ad"===a[0]){var i=_(t,a[1]);if(!i)return;var n=new window.Advads_passive_cb_Ad(i,e.cbId);e.wrapper.remove(),n.output({track:!0,inject:!0,do_has_ad:!0})}if("group"===a[0]){t.id=parseInt(a[1],10),t.type="group",t.group_info=t.adsense_fallback_group_info;var r=new window.Advads_passive_cb_Group(t,e.cbId);e.wrapper.remove(),r.output()}}(e,t),s(t))},s=function(e){if(advancedAds.adHealthNotice.enabled&&document.getElementById("wpadminbar")){var t=document.getElementById("wp-admin-bar-advanced_ads_ad_health_fine");null==t||t.remove();var a=document.getElementById("wp-admin-bar-advanced_ads_ad_health").querySelector("ul"),i=document.createElement("li");i.role="group",i.id="wp-admin-bar-advanced_ads_gads_fallback";var n=document.createElement("div");n.className="ab-item ab-empty-item",n.role="menuitem",n.textContent=window.advancedAds.adHealthNotice.pattern.replace("[ad_title]",e.ads[parseInt(e.placement_info.item.split("_")[1],10)].title),a.appendChild(i),i.appendChild(n)}},o=function(e){return Object.values(window.advads_passive_placements).find((function(t){return 0===t.elementid.indexOf(e)}))||!1},d=function(e){return void 0!==t[e]&&t[e]},_=function(e,t){return e.ads[t]||!1};document.addEventListener("advanced_ads_pro_adsense_unfilled",(function(t){0===--i&&a.disconnect();var s=n(t.detail.ad.closest('[class^="gas_fallback-"]'));e[s.ad]&&r(e[s.ad])})),document.body.addEventListener("advads_ajax_cb_response",(function(e){e.detail.response.forEach((function(e){t[e.elementId]=e}))})),window.advanced_ads_ready((function(){window.advanced_ads_pro&&window.advanced_ads_pro.postscribeObservers.add((function(t){if("postscribe_done"===t.event&&-1!==t.ad.indexOf("gas_fallback-")){var r,s=t.ref[0],o=s.querySelector('[class^="gas_fallback-"]'),d=n(o);e[d.ad]={fallback:d.fallback,wrapper:o,cbId:s.id},r=o.querySelector("ins"),null===a&&(a=new window.MutationObserver((function(e){var t,a=I(e);try{for(a.s();!(t=a.n()).done;){var i=t.value;if("unfilled"!==i.target.getAttribute("data-ad-status"))return;document.dispatchEvent(new CustomEvent("advanced_ads_pro_adsense_unfilled",{detail:{ad:i.target}}))}}catch(e){a.e(e)}finally{a.f()}}))),a.observe(r,{attributeFilter:["data-ad-status"]}),i++}}))}))};window.advanced_ads_pro=y,(0,P.K)(),window.Advads_passive_cb_Placement=v,window.Advads_passive_cb_Ad=l,window.Advads_passive_cb_Group=S,window.advads_pro_utils=C,window.advanced_ads_group_refresh=E,r()((function(){k()})),"undefined"!=typeof advads&&void 0!==advads.privacy.dispatch_event?document.addEventListener("advanced_ads_privacy",(function(e){if("unknown"===e.detail.previousState||"rejected"===e.detail.previousState&&"accepted"===e.detail.state){if(C.log("reload ads! transition from "+e.detail.previousState+" to "+e.detail.state),"accepted"===e.detail.state||"not_needed"===e.detail.state){var t='script[type="text/plain"][data-tcf="waiting-for-consent"]';document.querySelectorAll(t).forEach((function(e){y.passive_ads.hasOwnProperty(e.dataset.bid)||(y.passive_ads[e.dataset.bid]=[]),y.passive_ads[e.dataset.bid].push(parseInt(e.dataset.id,10)),advads.privacy.decode_ad(e)})),new MutationObserver((function(e){var a={},i=function(e){void 0!==e.dataset.noTrack&&"impressions"===e.dataset.noTrack||(a.hasOwnProperty(e.dataset.bid)||(a[e.dataset.bid]=[]),a[e.dataset.bid].push(parseInt(e.dataset.id,10))),advads.privacy.decode_ad(e)};e.forEach((function(e){e.addedNodes.forEach((function(e){void 0===e.tagName||void 0===e.dataset||"script"!==e.tagName.toLowerCase()||"waiting-for-consent"!==e.dataset.tcf?void 0!==e.dataset&&"waiting-for-consent"===e.dataset.tcf||document.querySelectorAll(t).forEach(i):i(e)}))})),Object.keys(a).length&&y.observers.fire({event:"advanced_ads_decode_inserted_ads",ad_ids:a})})).observe(document,{subtree:!0,childList:!0})}y.busy?document.addEventListener("advanced_ads_pro.idle",y.process_passive_cb,{once:!0}):y.process_passive_cb()}else C.log("no action! transition from "+e.detail.previousState+" to "+e.detail.state)})):(window.advanced_ads_ready||r()(document).ready).call(null,(function(){y.process_passive_cb()})),r()(document).on("advanced-ads-resize-window",(function(){var e=function(){for(var e=y.ads.length;e--;)"off"!==y.ads.cb_method&&y.ads.splice(e,1);y.process_passive_cb()};y.busy?document.addEventListener("advanced_ads_pro.idle",e,{once:!0}):e()})),document.addEventListener("advads_pro_cache_busting_done",(function(e){if(e.detail.isEmpty&&e.detail.extra.emptyCbOption){var t=document.getElementById(e.detail.elementId);t&&(t.parentNode&&t.parentNode.classList.contains("widget")&&(t=t.parentNode),t.remove())}}));y.observers.add((function(e){var t;-1===["inject_passive_ads","inject_ajax_ads"].indexOf(e.event)||Array.isArray(e.ad_ids)&&!e.ad_ids.length||(t=document.getElementById("wp-admin-bar-advanced_ads_ad_health_highlight_ads"))&&(t.querySelector(".highlighted_ads_count").innerText=document.querySelectorAll(".".concat(window.advancedAds.frontendPrefix,"highlight-wrapper")).length)}))})();