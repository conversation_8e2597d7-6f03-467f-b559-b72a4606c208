<?php return array(
    'root' => array(
        'name' => 'advanced-ads/advanced-ads-pro',
        'pretty_version' => '2.26.0',
        'version' => '2.26.0.0',
        'reference' => null,
        'type' => 'wordpress-plugin',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => false,
    ),
    'versions' => array(
        'advanced-ads/advanced-ads-pro' => array(
            'pretty_version' => '2.26.0',
            'version' => '2.26.0.0',
            'reference' => null,
            'type' => 'wordpress-plugin',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'composer/ca-bundle' => array(
            'pretty_version' => '1.5.3',
            'version' => '*******',
            'reference' => '3b1fc3f0be055baa7c6258b1467849c3e8204eb2',
            'type' => 'library',
            'install_path' => __DIR__ . '/./ca-bundle',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'geoip2/geoip2' => array(
            'pretty_version' => 'v2.10.0',
            'version' => '********',
            'reference' => '419557cd21d9fe039721a83490701a58c8ce784a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../geoip2/geoip2',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'maxmind-db/reader' => array(
            'pretty_version' => 'v1.12.0',
            'version' => '********',
            'reference' => '5b2d7a721dedfaef9dc20822c5fe7d26f9f8eb90',
            'type' => 'library',
            'install_path' => __DIR__ . '/../maxmind-db/reader',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'maxmind/web-service-common' => array(
            'pretty_version' => 'v0.6.0',
            'version' => '*******',
            'reference' => '40c928bb0194c45088b369a17f9baef9c3fc7460',
            'type' => 'library',
            'install_path' => __DIR__ . '/../maxmind/web-service-common',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/css-selector' => array(
            'pretty_version' => 'v4.4.44',
            'version' => '********',
            'reference' => 'bd0a6737e48de45b4b0b7b6fc98c78404ddceaed',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/css-selector',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php80' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => '60328e362d4c2c802a54fcbf04f9d3fb892b4cf8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php80',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
