<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'AdvancedAds\\Pro\\Admin\\Ad_List_Table' => $baseDir . '/includes/admin/class-ad-list-table.php',
    'AdvancedAds\\Pro\\Admin\\Adsense' => $baseDir . '/includes/admin/class-adsense.php',
    'AdvancedAds\\Pro\\Admin\\Duplicate_Placement' => $baseDir . '/includes/admin/class-duplicate-placement.php',
    'AdvancedAds\\Pro\\Admin\\Group_Duplication' => $baseDir . '/includes/admin/class-group-duplication.php',
    'AdvancedAds\\Pro\\Admin\\Placements\\Bulk_Edit' => $baseDir . '/includes/admin/placements/class-bulk-edit.php',
    'AdvancedAds\\Pro\\Adsense' => $baseDir . '/includes/class-adsense.php',
    'AdvancedAds\\Pro\\Assets_Manager' => $baseDir . '/includes/class-assets-manager.php',
    'AdvancedAds\\Pro\\Autoloader' => $baseDir . '/includes/class-autoloader.php',
    'AdvancedAds\\Pro\\Bootstrap' => $baseDir . '/includes/class-bootstrap.php',
    'AdvancedAds\\Pro\\Installation\\Install' => $baseDir . '/includes/installation/class-install.php',
    'AdvancedAds\\Pro\\Modules\\BuddyPress\\Admin' => $baseDir . '/modules/buddypress/class-admin.php',
    'AdvancedAds\\Pro\\Modules\\BuddyPress\\BuddyPress' => $baseDir . '/modules/buddypress/class-buddypress.php',
    'AdvancedAds\\Pro\\Modules\\Grids\\Group_Grid' => $baseDir . '/modules/grids/class-group-grid.php',
    'AdvancedAds\\Pro\\Modules\\Grids\\Type_Grid' => $baseDir . '/modules/grids/class-type-grid.php',
    'AdvancedAds\\Pro\\Modules\\bbPress\\Admin\\Admin' => $baseDir . '/modules/bbpress/class-admin.php',
    'AdvancedAds\\Pro\\Modules\\bbPress\\BBPress' => $baseDir . '/modules/bbpress/class-bbpress.php',
    'AdvancedAds\\Pro\\Placements\\Placement_Above_Headline' => $baseDir . '/includes/placements/class-placement-above-headline.php',
    'AdvancedAds\\Pro\\Placements\\Placement_Archive_Page' => $baseDir . '/includes/placements/class-placement-archive-page.php',
    'AdvancedAds\\Pro\\Placements\\Placement_Background_Ad' => $baseDir . '/includes/placements/class-placement-background-ad.php',
    'AdvancedAds\\Pro\\Placements\\Placement_Bbpress_Comment' => $baseDir . '/includes/placements/class-placement-bbpress-comment.php',
    'AdvancedAds\\Pro\\Placements\\Placement_Bbpress_Static' => $baseDir . '/includes/placements/class-placement-bbpress-static.php',
    'AdvancedAds\\Pro\\Placements\\Placement_Buddypress' => $baseDir . '/includes/placements/class-placement-buddypress.php',
    'AdvancedAds\\Pro\\Placements\\Placement_Content_Middle' => $baseDir . '/includes/placements/class-placement-content-middle.php',
    'AdvancedAds\\Pro\\Placements\\Placement_Content_Random' => $baseDir . '/includes/placements/class-placement-content-random.php',
    'AdvancedAds\\Pro\\Placements\\Placement_Custom_Position' => $baseDir . '/includes/placements/class-placement-custom-position.php',
    'AdvancedAds\\Pro\\Placements\\Placement_Server' => $baseDir . '/includes/placements/class-placement-server.php',
    'AdvancedAds\\Pro\\Placements\\Placement_Types' => $baseDir . '/includes/placements/class-placement-types.php',
    'AdvancedAds\\Pro\\Placements\\Types\\Above_Headline' => $baseDir . '/includes/placements/types/class-above-headline.php',
    'AdvancedAds\\Pro\\Placements\\Types\\Archive_Pages' => $baseDir . '/includes/placements/types/class-archive-pages.php',
    'AdvancedAds\\Pro\\Placements\\Types\\Background_Ad' => $baseDir . '/includes/placements/types/class-background-ad.php',
    'AdvancedAds\\Pro\\Placements\\Types\\Bbpress_Comment' => $baseDir . '/includes/placements/types/class-bbpress-comment.php',
    'AdvancedAds\\Pro\\Placements\\Types\\Bbpress_Static' => $baseDir . '/includes/placements/types/class-bbpress-static.php',
    'AdvancedAds\\Pro\\Placements\\Types\\Buddypress' => $baseDir . '/includes/placements/types/class-buddypress.php',
    'AdvancedAds\\Pro\\Placements\\Types\\Content_Middle' => $baseDir . '/includes/placements/types/class-content-middle.php',
    'AdvancedAds\\Pro\\Placements\\Types\\Content_Random' => $baseDir . '/includes/placements/types/class-content-random.php',
    'AdvancedAds\\Pro\\Placements\\Types\\Custom_Position' => $baseDir . '/includes/placements/types/class-custom-position.php',
    'AdvancedAds\\Pro\\Placements\\Types\\Server' => $baseDir . '/includes/placements/types/class-server.php',
    'AdvancedAds\\Pro\\Plugin' => $baseDir . '/includes/class-plugin.php',
    'AdvancedAds\\Pro\\Upgrades' => $baseDir . '/includes/class-upgrades.php',
    'Advanced_Ads_Geo' => $baseDir . '/modules/geo/classes/public.php',
    'Advanced_Ads_Geo_Admin' => $baseDir . '/modules/geo/classes/admin.php',
    'Advanced_Ads_Geo_Api' => $baseDir . '/modules/geo/classes/api.php',
    'Advanced_Ads_Geo_Plugin' => $baseDir . '/modules/geo/classes/plugin.php',
    'Advanced_Ads_Geo_Version_Check' => $baseDir . '/modules/geo/classes/Advanced_Ads_Geo_Version_Check.php',
    'Advanced_Ads_Geo_Visitor_Profile' => $baseDir . '/modules/geo/classes/Advanced_Ads_Geo_Visitor_Profile.php',
    'Advanced_Ads_Pro' => $baseDir . '/classes/advanced-ads-pro.php',
    'Advanced_Ads_Pro\\Ads_By_Hours\\Admin' => $baseDir . '/modules/ads-by-hours/src/class-admin.php',
    'Advanced_Ads_Pro\\Ads_By_Hours\\Module' => $baseDir . '/modules/ads-by-hours/src/class-module.php',
    'Advanced_Ads_Pro\\Module\\Responsive_Ads\\Admin' => $baseDir . '/modules/responsive-ads/includes/class-admin.php',
    'Advanced_Ads_Pro\\Module\\Responsive_Ads\\Common' => $baseDir . '/modules/responsive-ads/includes/class-common.php',
    'Advanced_Ads_Pro\\Module\\Responsive_Ads\\Frontend' => $baseDir . '/modules/responsive-ads/includes/class-frontend.php',
    'Advanced_Ads_Pro\\Rest_Api\\Ad' => $baseDir . '/modules/rest-api/classes/Advanced_Ads_Ad.php',
    'Advanced_Ads_Pro\\Rest_Api\\Admin_UI' => $baseDir . '/modules/rest-api/classes/Admin_UI.php',
    'Advanced_Ads_Pro\\Rest_Api\\Group' => $baseDir . '/modules/rest-api/classes/Advanced_Ads_Group.php',
    'Advanced_Ads_Pro\\Rest_Api\\Rest_Ads_Query' => $baseDir . '/modules/rest-api/classes/Rest_Ads_Query.php',
    'Advanced_Ads_Pro\\Rest_Api\\Rest_Api' => $baseDir . '/modules/rest-api/classes/Rest_Api.php',
    'Advanced_Ads_Pro\\Rest_Api\\Rest_Exception' => $baseDir . '/modules/rest-api/classes/Rest_Exception.php',
    'Advanced_Ads_Pro\\Rest_Api\\Rest_Groups_Query' => $baseDir . '/modules/rest-api/classes/Rest_Groups_Query.php',
    'Advanced_Ads_Pro\\Rest_Api\\Rest_Query_Params_Helper' => $baseDir . '/modules/rest-api/classes/Rest_Query_Params_Helper.php',
    'Advanced_Ads_Pro_AdSense_Public' => $baseDir . '/modules/gadsense/public.php',
    'Advanced_Ads_Pro_Admin' => $baseDir . '/classes/advanced-ads-pro-admin.php',
    'Advanced_Ads_Pro_Cache_Busting_Server_Info' => $baseDir . '/modules/cache-busting/server-info.class.php',
    'Advanced_Ads_Pro_Cache_Busting_Visitor_Info_Cookie' => $baseDir . '/modules/cache-busting/server-info.class.php',
    'Advanced_Ads_Pro_Compatibility' => $baseDir . '/classes/compatibility.php',
    'Advanced_Ads_Pro_Group_Refresh' => $baseDir . '/modules/group-refresh/group-refresh.class.php',
    'Advanced_Ads_Pro_Group_Refresh_Admin' => $baseDir . '/modules/group-refresh/admin.class.php',
    'Advanced_Ads_Pro_Module_Ad_Server' => $baseDir . '/modules/ad-server/main.class.php',
    'Advanced_Ads_Pro_Module_Ad_Server_Admin' => $baseDir . '/modules/ad-server/admin.class.php',
    'Advanced_Ads_Pro_Module_Admin_Bar' => $baseDir . '/modules/admin-bar/admin-bar.class.php',
    'Advanced_Ads_Pro_Module_Ads_For_Adblockers' => $baseDir . '/modules/ads-for-adblockers/main.class.php',
    'Advanced_Ads_Pro_Module_Ads_For_Adblockers_Admin' => $baseDir . '/modules/ads-for-adblockers/admin.class.php',
    'Advanced_Ads_Pro_Module_Advanced_Display_Conditions' => $baseDir . '/modules/advanced-display-conditions/main.class.php',
    'Advanced_Ads_Pro_Module_Advanced_Display_Conditions_Admin' => $baseDir . '/modules/advanced-display-conditions/admin.class.php',
    'Advanced_Ads_Pro_Module_Advanced_Visitor_Conditions' => $baseDir . '/modules/advanced-visitor-conditions/main.class.php',
    'Advanced_Ads_Pro_Module_Advanced_Visitor_Conditions_Admin' => $baseDir . '/modules/advanced-visitor-conditions/admin.class.php',
    'Advanced_Ads_Pro_Module_Background_Ads' => $baseDir . '/modules/background-ads/background.class.php',
    'Advanced_Ads_Pro_Module_Background_Ads_Admin' => $baseDir . '/modules/background-ads/admin.class.php',
    'Advanced_Ads_Pro_Module_CFP' => $baseDir . '/modules/click-fraud-protection/click-fraud-protection.class.php',
    'Advanced_Ads_Pro_Module_CFP_Admin' => $baseDir . '/modules/click-fraud-protection/admin.class.php',
    'Advanced_Ads_Pro_Module_Cache_Busting' => $baseDir . '/modules/cache-busting/cache-busting.class.php',
    'Advanced_Ads_Pro_Module_Cache_Busting_Admin' => $baseDir . '/modules/cache-busting/admin.class.php',
    'Advanced_Ads_Pro_Module_Cache_Busting_Admin_UI' => $baseDir . '/modules/cache-busting/admin-ui.class.php',
    'Advanced_Ads_Pro_Module_Duplicate_Ads' => $baseDir . '/modules/duplicate-ads/duplicate.class.php',
    'Advanced_Ads_Pro_Module_Duplicate_Ads_Admin' => $baseDir . '/modules/duplicate-ads/admin.class.php',
    'Advanced_Ads_Pro_Module_Extended_Adblocker' => $baseDir . '/modules/extended-adblocker/main.class.php',
    'Advanced_Ads_Pro_Module_Extended_Adblocker_Admin' => $baseDir . '/modules/extended-adblocker/admin.class.php',
    'Advanced_Ads_Pro_Module_GamiPress' => $baseDir . '/modules/gamipress/gamipress.class.php',
    'Advanced_Ads_Pro_Module_GamiPress_Admin' => $baseDir . '/modules/gamipress/admin.class.php',
    'Advanced_Ads_Pro_Module_Grids' => $baseDir . '/modules/grids/grids.class.php',
    'Advanced_Ads_Pro_Module_Grids_Admin' => $baseDir . '/modules/grids/admin.class.php',
    'Advanced_Ads_Pro_Module_Inject_Content' => $baseDir . '/modules/inject-content/inject-content.class.php',
    'Advanced_Ads_Pro_Module_Inject_Content_Admin' => $baseDir . '/modules/inject-content/admin.class.php',
    'Advanced_Ads_Pro_Module_Inject_Content_Custom_Position' => $baseDir . '/modules/inject-content/inject-content-custom-position.class.php',
    'Advanced_Ads_Pro_Module_Lazy_Load_Admin' => $baseDir . '/modules/lazy-load/admin.class.php',
    'Advanced_Ads_Pro_Module_PaidMembershipsPro' => $baseDir . '/modules/paid-memberships-pro/main.class.php',
    'Advanced_Ads_Pro_Module_PaidMembershipsPro_Admin' => $baseDir . '/modules/paid-memberships-pro/admin.class.php',
    'Advanced_Ads_Pro_Module_Parallax' => $baseDir . '/modules/parallax-ads/classes/Advanced_Ads_Pro_Module_Parallax.php',
    'Advanced_Ads_Pro_Module_Parallax_Admin_UI' => $baseDir . '/modules/parallax-ads/classes/Advanced_Ads_Pro_Module_Parallax_Admin_UI.php',
    'Advanced_Ads_Pro_Module_Parallax_Frontend' => $baseDir . '/modules/parallax-ads/classes/Advanced_Ads_Pro_Module_Parallax_Frontend.php',
    'Advanced_Ads_Pro_Module_Placement_Conditions' => $baseDir . '/modules/placement_conditions/main.class.php',
    'Advanced_Ads_Pro_Module_Placement_Conditions_Admin' => $baseDir . '/modules/placement_conditions/admin.class.php',
    'Advanced_Ads_Pro_Offset_Shifter' => $baseDir . '/classes/class-advanced-ads-pro-offset-shifter.php',
    'Advanced_Ads_Pro_Placement_Tests' => $baseDir . '/modules/placement-tests/placement-tests.class.php',
    'Advanced_Ads_Pro_Utils' => $baseDir . '/classes/utils.php',
    'Advanced_Ads_Pro_Weekdays' => $baseDir . '/modules/weekdays/class-advanced-ads-pro-weekdays.php',
    'Attribute' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/Attribute.php',
    'Composer\\CaBundle\\CaBundle' => $vendorDir . '/composer/ca-bundle/src/CaBundle.php',
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'GeoIp2\\Database\\Reader' => $vendorDir . '/geoip2/geoip2/src/Database/Reader.php',
    'GeoIp2\\Exception\\AddressNotFoundException' => $vendorDir . '/geoip2/geoip2/src/Exception/AddressNotFoundException.php',
    'GeoIp2\\Exception\\AuthenticationException' => $vendorDir . '/geoip2/geoip2/src/Exception/AuthenticationException.php',
    'GeoIp2\\Exception\\GeoIp2Exception' => $vendorDir . '/geoip2/geoip2/src/Exception/GeoIp2Exception.php',
    'GeoIp2\\Exception\\HttpException' => $vendorDir . '/geoip2/geoip2/src/Exception/HttpException.php',
    'GeoIp2\\Exception\\InvalidRequestException' => $vendorDir . '/geoip2/geoip2/src/Exception/InvalidRequestException.php',
    'GeoIp2\\Exception\\OutOfQueriesException' => $vendorDir . '/geoip2/geoip2/src/Exception/OutOfQueriesException.php',
    'GeoIp2\\Model\\AbstractModel' => $vendorDir . '/geoip2/geoip2/src/Model/AbstractModel.php',
    'GeoIp2\\Model\\AnonymousIp' => $vendorDir . '/geoip2/geoip2/src/Model/AnonymousIp.php',
    'GeoIp2\\Model\\Asn' => $vendorDir . '/geoip2/geoip2/src/Model/Asn.php',
    'GeoIp2\\Model\\City' => $vendorDir . '/geoip2/geoip2/src/Model/City.php',
    'GeoIp2\\Model\\ConnectionType' => $vendorDir . '/geoip2/geoip2/src/Model/ConnectionType.php',
    'GeoIp2\\Model\\Country' => $vendorDir . '/geoip2/geoip2/src/Model/Country.php',
    'GeoIp2\\Model\\Domain' => $vendorDir . '/geoip2/geoip2/src/Model/Domain.php',
    'GeoIp2\\Model\\Enterprise' => $vendorDir . '/geoip2/geoip2/src/Model/Enterprise.php',
    'GeoIp2\\Model\\Insights' => $vendorDir . '/geoip2/geoip2/src/Model/Insights.php',
    'GeoIp2\\Model\\Isp' => $vendorDir . '/geoip2/geoip2/src/Model/Isp.php',
    'GeoIp2\\ProviderInterface' => $vendorDir . '/geoip2/geoip2/src/ProviderInterface.php',
    'GeoIp2\\Record\\AbstractPlaceRecord' => $vendorDir . '/geoip2/geoip2/src/Record/AbstractPlaceRecord.php',
    'GeoIp2\\Record\\AbstractRecord' => $vendorDir . '/geoip2/geoip2/src/Record/AbstractRecord.php',
    'GeoIp2\\Record\\City' => $vendorDir . '/geoip2/geoip2/src/Record/City.php',
    'GeoIp2\\Record\\Continent' => $vendorDir . '/geoip2/geoip2/src/Record/Continent.php',
    'GeoIp2\\Record\\Country' => $vendorDir . '/geoip2/geoip2/src/Record/Country.php',
    'GeoIp2\\Record\\Location' => $vendorDir . '/geoip2/geoip2/src/Record/Location.php',
    'GeoIp2\\Record\\MaxMind' => $vendorDir . '/geoip2/geoip2/src/Record/MaxMind.php',
    'GeoIp2\\Record\\Postal' => $vendorDir . '/geoip2/geoip2/src/Record/Postal.php',
    'GeoIp2\\Record\\RepresentedCountry' => $vendorDir . '/geoip2/geoip2/src/Record/RepresentedCountry.php',
    'GeoIp2\\Record\\Subdivision' => $vendorDir . '/geoip2/geoip2/src/Record/Subdivision.php',
    'GeoIp2\\Record\\Traits' => $vendorDir . '/geoip2/geoip2/src/Record/Traits.php',
    'GeoIp2\\Util' => $vendorDir . '/geoip2/geoip2/src/Util.php',
    'GeoIp2\\WebService\\Client' => $vendorDir . '/geoip2/geoip2/src/WebService/Client.php',
    'MaxMind\\Db\\Reader' => $vendorDir . '/maxmind-db/reader/src/MaxMind/Db/Reader.php',
    'MaxMind\\Db\\Reader\\Decoder' => $vendorDir . '/maxmind-db/reader/src/MaxMind/Db/Reader/Decoder.php',
    'MaxMind\\Db\\Reader\\InvalidDatabaseException' => $vendorDir . '/maxmind-db/reader/src/MaxMind/Db/Reader/InvalidDatabaseException.php',
    'MaxMind\\Db\\Reader\\Metadata' => $vendorDir . '/maxmind-db/reader/src/MaxMind/Db/Reader/Metadata.php',
    'MaxMind\\Db\\Reader\\Util' => $vendorDir . '/maxmind-db/reader/src/MaxMind/Db/Reader/Util.php',
    'MaxMind\\Exception\\AuthenticationException' => $vendorDir . '/maxmind/web-service-common/src/Exception/AuthenticationException.php',
    'MaxMind\\Exception\\HttpException' => $vendorDir . '/maxmind/web-service-common/src/Exception/HttpException.php',
    'MaxMind\\Exception\\InsufficientFundsException' => $vendorDir . '/maxmind/web-service-common/src/Exception/InsufficientFundsException.php',
    'MaxMind\\Exception\\InvalidInputException' => $vendorDir . '/maxmind/web-service-common/src/Exception/InvalidInputException.php',
    'MaxMind\\Exception\\InvalidRequestException' => $vendorDir . '/maxmind/web-service-common/src/Exception/InvalidRequestException.php',
    'MaxMind\\Exception\\IpAddressNotFoundException' => $vendorDir . '/maxmind/web-service-common/src/Exception/IpAddressNotFoundException.php',
    'MaxMind\\Exception\\PermissionRequiredException' => $vendorDir . '/maxmind/web-service-common/src/Exception/PermissionRequiredException.php',
    'MaxMind\\Exception\\WebServiceException' => $vendorDir . '/maxmind/web-service-common/src/Exception/WebServiceException.php',
    'MaxMind\\WebService\\Client' => $vendorDir . '/maxmind/web-service-common/src/WebService/Client.php',
    'MaxMind\\WebService\\Http\\CurlRequest' => $vendorDir . '/maxmind/web-service-common/src/WebService/Http/CurlRequest.php',
    'MaxMind\\WebService\\Http\\Request' => $vendorDir . '/maxmind/web-service-common/src/WebService/Http/Request.php',
    'MaxMind\\WebService\\Http\\RequestFactory' => $vendorDir . '/maxmind/web-service-common/src/WebService/Http/RequestFactory.php',
    'PhpToken' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/PhpToken.php',
    'Stringable' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/Stringable.php',
    'Symfony\\Component\\CssSelector\\CssSelectorConverter' => $vendorDir . '/symfony/css-selector/CssSelectorConverter.php',
    'Symfony\\Component\\CssSelector\\Exception\\ExceptionInterface' => $vendorDir . '/symfony/css-selector/Exception/ExceptionInterface.php',
    'Symfony\\Component\\CssSelector\\Exception\\ExpressionErrorException' => $vendorDir . '/symfony/css-selector/Exception/ExpressionErrorException.php',
    'Symfony\\Component\\CssSelector\\Exception\\InternalErrorException' => $vendorDir . '/symfony/css-selector/Exception/InternalErrorException.php',
    'Symfony\\Component\\CssSelector\\Exception\\ParseException' => $vendorDir . '/symfony/css-selector/Exception/ParseException.php',
    'Symfony\\Component\\CssSelector\\Exception\\SyntaxErrorException' => $vendorDir . '/symfony/css-selector/Exception/SyntaxErrorException.php',
    'Symfony\\Component\\CssSelector\\Node\\AbstractNode' => $vendorDir . '/symfony/css-selector/Node/AbstractNode.php',
    'Symfony\\Component\\CssSelector\\Node\\AttributeNode' => $vendorDir . '/symfony/css-selector/Node/AttributeNode.php',
    'Symfony\\Component\\CssSelector\\Node\\ClassNode' => $vendorDir . '/symfony/css-selector/Node/ClassNode.php',
    'Symfony\\Component\\CssSelector\\Node\\CombinedSelectorNode' => $vendorDir . '/symfony/css-selector/Node/CombinedSelectorNode.php',
    'Symfony\\Component\\CssSelector\\Node\\ElementNode' => $vendorDir . '/symfony/css-selector/Node/ElementNode.php',
    'Symfony\\Component\\CssSelector\\Node\\FunctionNode' => $vendorDir . '/symfony/css-selector/Node/FunctionNode.php',
    'Symfony\\Component\\CssSelector\\Node\\HashNode' => $vendorDir . '/symfony/css-selector/Node/HashNode.php',
    'Symfony\\Component\\CssSelector\\Node\\NegationNode' => $vendorDir . '/symfony/css-selector/Node/NegationNode.php',
    'Symfony\\Component\\CssSelector\\Node\\NodeInterface' => $vendorDir . '/symfony/css-selector/Node/NodeInterface.php',
    'Symfony\\Component\\CssSelector\\Node\\PseudoNode' => $vendorDir . '/symfony/css-selector/Node/PseudoNode.php',
    'Symfony\\Component\\CssSelector\\Node\\SelectorNode' => $vendorDir . '/symfony/css-selector/Node/SelectorNode.php',
    'Symfony\\Component\\CssSelector\\Node\\Specificity' => $vendorDir . '/symfony/css-selector/Node/Specificity.php',
    'Symfony\\Component\\CssSelector\\Parser\\Handler\\CommentHandler' => $vendorDir . '/symfony/css-selector/Parser/Handler/CommentHandler.php',
    'Symfony\\Component\\CssSelector\\Parser\\Handler\\HandlerInterface' => $vendorDir . '/symfony/css-selector/Parser/Handler/HandlerInterface.php',
    'Symfony\\Component\\CssSelector\\Parser\\Handler\\HashHandler' => $vendorDir . '/symfony/css-selector/Parser/Handler/HashHandler.php',
    'Symfony\\Component\\CssSelector\\Parser\\Handler\\IdentifierHandler' => $vendorDir . '/symfony/css-selector/Parser/Handler/IdentifierHandler.php',
    'Symfony\\Component\\CssSelector\\Parser\\Handler\\NumberHandler' => $vendorDir . '/symfony/css-selector/Parser/Handler/NumberHandler.php',
    'Symfony\\Component\\CssSelector\\Parser\\Handler\\StringHandler' => $vendorDir . '/symfony/css-selector/Parser/Handler/StringHandler.php',
    'Symfony\\Component\\CssSelector\\Parser\\Handler\\WhitespaceHandler' => $vendorDir . '/symfony/css-selector/Parser/Handler/WhitespaceHandler.php',
    'Symfony\\Component\\CssSelector\\Parser\\Parser' => $vendorDir . '/symfony/css-selector/Parser/Parser.php',
    'Symfony\\Component\\CssSelector\\Parser\\ParserInterface' => $vendorDir . '/symfony/css-selector/Parser/ParserInterface.php',
    'Symfony\\Component\\CssSelector\\Parser\\Reader' => $vendorDir . '/symfony/css-selector/Parser/Reader.php',
    'Symfony\\Component\\CssSelector\\Parser\\Shortcut\\ClassParser' => $vendorDir . '/symfony/css-selector/Parser/Shortcut/ClassParser.php',
    'Symfony\\Component\\CssSelector\\Parser\\Shortcut\\ElementParser' => $vendorDir . '/symfony/css-selector/Parser/Shortcut/ElementParser.php',
    'Symfony\\Component\\CssSelector\\Parser\\Shortcut\\EmptyStringParser' => $vendorDir . '/symfony/css-selector/Parser/Shortcut/EmptyStringParser.php',
    'Symfony\\Component\\CssSelector\\Parser\\Shortcut\\HashParser' => $vendorDir . '/symfony/css-selector/Parser/Shortcut/HashParser.php',
    'Symfony\\Component\\CssSelector\\Parser\\Token' => $vendorDir . '/symfony/css-selector/Parser/Token.php',
    'Symfony\\Component\\CssSelector\\Parser\\TokenStream' => $vendorDir . '/symfony/css-selector/Parser/TokenStream.php',
    'Symfony\\Component\\CssSelector\\Parser\\Tokenizer\\Tokenizer' => $vendorDir . '/symfony/css-selector/Parser/Tokenizer/Tokenizer.php',
    'Symfony\\Component\\CssSelector\\Parser\\Tokenizer\\TokenizerEscaping' => $vendorDir . '/symfony/css-selector/Parser/Tokenizer/TokenizerEscaping.php',
    'Symfony\\Component\\CssSelector\\Parser\\Tokenizer\\TokenizerPatterns' => $vendorDir . '/symfony/css-selector/Parser/Tokenizer/TokenizerPatterns.php',
    'Symfony\\Component\\CssSelector\\XPath\\Extension\\AbstractExtension' => $vendorDir . '/symfony/css-selector/XPath/Extension/AbstractExtension.php',
    'Symfony\\Component\\CssSelector\\XPath\\Extension\\AttributeMatchingExtension' => $vendorDir . '/symfony/css-selector/XPath/Extension/AttributeMatchingExtension.php',
    'Symfony\\Component\\CssSelector\\XPath\\Extension\\CombinationExtension' => $vendorDir . '/symfony/css-selector/XPath/Extension/CombinationExtension.php',
    'Symfony\\Component\\CssSelector\\XPath\\Extension\\ExtensionInterface' => $vendorDir . '/symfony/css-selector/XPath/Extension/ExtensionInterface.php',
    'Symfony\\Component\\CssSelector\\XPath\\Extension\\FunctionExtension' => $vendorDir . '/symfony/css-selector/XPath/Extension/FunctionExtension.php',
    'Symfony\\Component\\CssSelector\\XPath\\Extension\\HtmlExtension' => $vendorDir . '/symfony/css-selector/XPath/Extension/HtmlExtension.php',
    'Symfony\\Component\\CssSelector\\XPath\\Extension\\NodeExtension' => $vendorDir . '/symfony/css-selector/XPath/Extension/NodeExtension.php',
    'Symfony\\Component\\CssSelector\\XPath\\Extension\\PseudoClassExtension' => $vendorDir . '/symfony/css-selector/XPath/Extension/PseudoClassExtension.php',
    'Symfony\\Component\\CssSelector\\XPath\\Translator' => $vendorDir . '/symfony/css-selector/XPath/Translator.php',
    'Symfony\\Component\\CssSelector\\XPath\\TranslatorInterface' => $vendorDir . '/symfony/css-selector/XPath/TranslatorInterface.php',
    'Symfony\\Component\\CssSelector\\XPath\\XPathExpr' => $vendorDir . '/symfony/css-selector/XPath/XPathExpr.php',
    'Symfony\\Polyfill\\Php80\\Php80' => $vendorDir . '/symfony/polyfill-php80/Php80.php',
    'Symfony\\Polyfill\\Php80\\PhpToken' => $vendorDir . '/symfony/polyfill-php80/PhpToken.php',
    'UnhandledMatchError' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/UnhandledMatchError.php',
    'ValueError' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/ValueError.php',
);
