<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit_advanced_ads_pro
{
    public static $files = array (
        'a4a119a56e50fbb293281d9a48007e0e' => __DIR__ . '/..' . '/symfony/polyfill-php80/bootstrap.php',
    );

    public static $prefixLengthsPsr4 = array (
        'S' => 
        array (
            'Symfony\\Polyfill\\Php80\\' => 23,
            'Symfony\\Component\\CssSelector\\' => 30,
        ),
        'M' => 
        array (
            'MaxMind\\WebService\\' => 19,
            'MaxMind\\Exception\\' => 18,
            'MaxMind\\Db\\' => 11,
        ),
        'G' => 
        array (
            'GeoIp2\\' => 7,
        ),
        'C' => 
        array (
            'Composer\\CaBundle\\' => 18,
        ),
        'A' => 
        array (
            'Advanced_Ads_Pro\\Rest_Api\\' => 26,
            'Advanced_Ads_Pro\\Ads_By_Hours\\' => 30,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'Symfony\\Polyfill\\Php80\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/polyfill-php80',
        ),
        'Symfony\\Component\\CssSelector\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/css-selector',
        ),
        'MaxMind\\WebService\\' => 
        array (
            0 => __DIR__ . '/..' . '/maxmind/web-service-common/src/WebService',
        ),
        'MaxMind\\Exception\\' => 
        array (
            0 => __DIR__ . '/..' . '/maxmind/web-service-common/src/Exception',
        ),
        'MaxMind\\Db\\' => 
        array (
            0 => __DIR__ . '/..' . '/maxmind-db/reader/src/MaxMind/Db',
        ),
        'GeoIp2\\' => 
        array (
            0 => __DIR__ . '/..' . '/geoip2/geoip2/src',
        ),
        'Composer\\CaBundle\\' => 
        array (
            0 => __DIR__ . '/..' . '/composer/ca-bundle/src',
        ),
        'Advanced_Ads_Pro\\Rest_Api\\' => 
        array (
            0 => __DIR__ . '/../..' . '/modules/rest-api/classes',
        ),
        'Advanced_Ads_Pro\\Ads_By_Hours\\' => 
        array (
            0 => __DIR__ . '/../..' . '/modules/ads-by-hours/src',
        ),
    );

    public static $classMap = array (
        'AdvancedAds\\Pro\\Admin\\Ad_List_Table' => __DIR__ . '/../..' . '/includes/admin/class-ad-list-table.php',
        'AdvancedAds\\Pro\\Admin\\Adsense' => __DIR__ . '/../..' . '/includes/admin/class-adsense.php',
        'AdvancedAds\\Pro\\Admin\\Duplicate_Placement' => __DIR__ . '/../..' . '/includes/admin/class-duplicate-placement.php',
        'AdvancedAds\\Pro\\Admin\\Group_Duplication' => __DIR__ . '/../..' . '/includes/admin/class-group-duplication.php',
        'AdvancedAds\\Pro\\Admin\\Placements\\Bulk_Edit' => __DIR__ . '/../..' . '/includes/admin/placements/class-bulk-edit.php',
        'AdvancedAds\\Pro\\Adsense' => __DIR__ . '/../..' . '/includes/class-adsense.php',
        'AdvancedAds\\Pro\\Assets_Manager' => __DIR__ . '/../..' . '/includes/class-assets-manager.php',
        'AdvancedAds\\Pro\\Autoloader' => __DIR__ . '/../..' . '/includes/class-autoloader.php',
        'AdvancedAds\\Pro\\Bootstrap' => __DIR__ . '/../..' . '/includes/class-bootstrap.php',
        'AdvancedAds\\Pro\\Installation\\Install' => __DIR__ . '/../..' . '/includes/installation/class-install.php',
        'AdvancedAds\\Pro\\Modules\\BuddyPress\\Admin' => __DIR__ . '/../..' . '/modules/buddypress/class-admin.php',
        'AdvancedAds\\Pro\\Modules\\BuddyPress\\BuddyPress' => __DIR__ . '/../..' . '/modules/buddypress/class-buddypress.php',
        'AdvancedAds\\Pro\\Modules\\Grids\\Group_Grid' => __DIR__ . '/../..' . '/modules/grids/class-group-grid.php',
        'AdvancedAds\\Pro\\Modules\\Grids\\Type_Grid' => __DIR__ . '/../..' . '/modules/grids/class-type-grid.php',
        'AdvancedAds\\Pro\\Modules\\bbPress\\Admin\\Admin' => __DIR__ . '/../..' . '/modules/bbpress/class-admin.php',
        'AdvancedAds\\Pro\\Modules\\bbPress\\BBPress' => __DIR__ . '/../..' . '/modules/bbpress/class-bbpress.php',
        'AdvancedAds\\Pro\\Placements\\Placement_Above_Headline' => __DIR__ . '/../..' . '/includes/placements/class-placement-above-headline.php',
        'AdvancedAds\\Pro\\Placements\\Placement_Archive_Page' => __DIR__ . '/../..' . '/includes/placements/class-placement-archive-page.php',
        'AdvancedAds\\Pro\\Placements\\Placement_Background_Ad' => __DIR__ . '/../..' . '/includes/placements/class-placement-background-ad.php',
        'AdvancedAds\\Pro\\Placements\\Placement_Bbpress_Comment' => __DIR__ . '/../..' . '/includes/placements/class-placement-bbpress-comment.php',
        'AdvancedAds\\Pro\\Placements\\Placement_Bbpress_Static' => __DIR__ . '/../..' . '/includes/placements/class-placement-bbpress-static.php',
        'AdvancedAds\\Pro\\Placements\\Placement_Buddypress' => __DIR__ . '/../..' . '/includes/placements/class-placement-buddypress.php',
        'AdvancedAds\\Pro\\Placements\\Placement_Content_Middle' => __DIR__ . '/../..' . '/includes/placements/class-placement-content-middle.php',
        'AdvancedAds\\Pro\\Placements\\Placement_Content_Random' => __DIR__ . '/../..' . '/includes/placements/class-placement-content-random.php',
        'AdvancedAds\\Pro\\Placements\\Placement_Custom_Position' => __DIR__ . '/../..' . '/includes/placements/class-placement-custom-position.php',
        'AdvancedAds\\Pro\\Placements\\Placement_Server' => __DIR__ . '/../..' . '/includes/placements/class-placement-server.php',
        'AdvancedAds\\Pro\\Placements\\Placement_Types' => __DIR__ . '/../..' . '/includes/placements/class-placement-types.php',
        'AdvancedAds\\Pro\\Placements\\Types\\Above_Headline' => __DIR__ . '/../..' . '/includes/placements/types/class-above-headline.php',
        'AdvancedAds\\Pro\\Placements\\Types\\Archive_Pages' => __DIR__ . '/../..' . '/includes/placements/types/class-archive-pages.php',
        'AdvancedAds\\Pro\\Placements\\Types\\Background_Ad' => __DIR__ . '/../..' . '/includes/placements/types/class-background-ad.php',
        'AdvancedAds\\Pro\\Placements\\Types\\Bbpress_Comment' => __DIR__ . '/../..' . '/includes/placements/types/class-bbpress-comment.php',
        'AdvancedAds\\Pro\\Placements\\Types\\Bbpress_Static' => __DIR__ . '/../..' . '/includes/placements/types/class-bbpress-static.php',
        'AdvancedAds\\Pro\\Placements\\Types\\Buddypress' => __DIR__ . '/../..' . '/includes/placements/types/class-buddypress.php',
        'AdvancedAds\\Pro\\Placements\\Types\\Content_Middle' => __DIR__ . '/../..' . '/includes/placements/types/class-content-middle.php',
        'AdvancedAds\\Pro\\Placements\\Types\\Content_Random' => __DIR__ . '/../..' . '/includes/placements/types/class-content-random.php',
        'AdvancedAds\\Pro\\Placements\\Types\\Custom_Position' => __DIR__ . '/../..' . '/includes/placements/types/class-custom-position.php',
        'AdvancedAds\\Pro\\Placements\\Types\\Server' => __DIR__ . '/../..' . '/includes/placements/types/class-server.php',
        'AdvancedAds\\Pro\\Plugin' => __DIR__ . '/../..' . '/includes/class-plugin.php',
        'AdvancedAds\\Pro\\Upgrades' => __DIR__ . '/../..' . '/includes/class-upgrades.php',
        'Advanced_Ads_Geo' => __DIR__ . '/../..' . '/modules/geo/classes/public.php',
        'Advanced_Ads_Geo_Admin' => __DIR__ . '/../..' . '/modules/geo/classes/admin.php',
        'Advanced_Ads_Geo_Api' => __DIR__ . '/../..' . '/modules/geo/classes/api.php',
        'Advanced_Ads_Geo_Plugin' => __DIR__ . '/../..' . '/modules/geo/classes/plugin.php',
        'Advanced_Ads_Geo_Version_Check' => __DIR__ . '/../..' . '/modules/geo/classes/Advanced_Ads_Geo_Version_Check.php',
        'Advanced_Ads_Geo_Visitor_Profile' => __DIR__ . '/../..' . '/modules/geo/classes/Advanced_Ads_Geo_Visitor_Profile.php',
        'Advanced_Ads_Pro' => __DIR__ . '/../..' . '/classes/advanced-ads-pro.php',
        'Advanced_Ads_Pro\\Ads_By_Hours\\Admin' => __DIR__ . '/../..' . '/modules/ads-by-hours/src/class-admin.php',
        'Advanced_Ads_Pro\\Ads_By_Hours\\Module' => __DIR__ . '/../..' . '/modules/ads-by-hours/src/class-module.php',
        'Advanced_Ads_Pro\\Module\\Responsive_Ads\\Admin' => __DIR__ . '/../..' . '/modules/responsive-ads/includes/class-admin.php',
        'Advanced_Ads_Pro\\Module\\Responsive_Ads\\Common' => __DIR__ . '/../..' . '/modules/responsive-ads/includes/class-common.php',
        'Advanced_Ads_Pro\\Module\\Responsive_Ads\\Frontend' => __DIR__ . '/../..' . '/modules/responsive-ads/includes/class-frontend.php',
        'Advanced_Ads_Pro\\Rest_Api\\Ad' => __DIR__ . '/../..' . '/modules/rest-api/classes/Advanced_Ads_Ad.php',
        'Advanced_Ads_Pro\\Rest_Api\\Admin_UI' => __DIR__ . '/../..' . '/modules/rest-api/classes/Admin_UI.php',
        'Advanced_Ads_Pro\\Rest_Api\\Group' => __DIR__ . '/../..' . '/modules/rest-api/classes/Advanced_Ads_Group.php',
        'Advanced_Ads_Pro\\Rest_Api\\Rest_Ads_Query' => __DIR__ . '/../..' . '/modules/rest-api/classes/Rest_Ads_Query.php',
        'Advanced_Ads_Pro\\Rest_Api\\Rest_Api' => __DIR__ . '/../..' . '/modules/rest-api/classes/Rest_Api.php',
        'Advanced_Ads_Pro\\Rest_Api\\Rest_Exception' => __DIR__ . '/../..' . '/modules/rest-api/classes/Rest_Exception.php',
        'Advanced_Ads_Pro\\Rest_Api\\Rest_Groups_Query' => __DIR__ . '/../..' . '/modules/rest-api/classes/Rest_Groups_Query.php',
        'Advanced_Ads_Pro\\Rest_Api\\Rest_Query_Params_Helper' => __DIR__ . '/../..' . '/modules/rest-api/classes/Rest_Query_Params_Helper.php',
        'Advanced_Ads_Pro_AdSense_Public' => __DIR__ . '/../..' . '/modules/gadsense/public.php',
        'Advanced_Ads_Pro_Admin' => __DIR__ . '/../..' . '/classes/advanced-ads-pro-admin.php',
        'Advanced_Ads_Pro_Cache_Busting_Server_Info' => __DIR__ . '/../..' . '/modules/cache-busting/server-info.class.php',
        'Advanced_Ads_Pro_Cache_Busting_Visitor_Info_Cookie' => __DIR__ . '/../..' . '/modules/cache-busting/server-info.class.php',
        'Advanced_Ads_Pro_Compatibility' => __DIR__ . '/../..' . '/classes/compatibility.php',
        'Advanced_Ads_Pro_Group_Refresh' => __DIR__ . '/../..' . '/modules/group-refresh/group-refresh.class.php',
        'Advanced_Ads_Pro_Group_Refresh_Admin' => __DIR__ . '/../..' . '/modules/group-refresh/admin.class.php',
        'Advanced_Ads_Pro_Module_Ad_Server' => __DIR__ . '/../..' . '/modules/ad-server/main.class.php',
        'Advanced_Ads_Pro_Module_Ad_Server_Admin' => __DIR__ . '/../..' . '/modules/ad-server/admin.class.php',
        'Advanced_Ads_Pro_Module_Admin_Bar' => __DIR__ . '/../..' . '/modules/admin-bar/admin-bar.class.php',
        'Advanced_Ads_Pro_Module_Ads_For_Adblockers' => __DIR__ . '/../..' . '/modules/ads-for-adblockers/main.class.php',
        'Advanced_Ads_Pro_Module_Ads_For_Adblockers_Admin' => __DIR__ . '/../..' . '/modules/ads-for-adblockers/admin.class.php',
        'Advanced_Ads_Pro_Module_Advanced_Display_Conditions' => __DIR__ . '/../..' . '/modules/advanced-display-conditions/main.class.php',
        'Advanced_Ads_Pro_Module_Advanced_Display_Conditions_Admin' => __DIR__ . '/../..' . '/modules/advanced-display-conditions/admin.class.php',
        'Advanced_Ads_Pro_Module_Advanced_Visitor_Conditions' => __DIR__ . '/../..' . '/modules/advanced-visitor-conditions/main.class.php',
        'Advanced_Ads_Pro_Module_Advanced_Visitor_Conditions_Admin' => __DIR__ . '/../..' . '/modules/advanced-visitor-conditions/admin.class.php',
        'Advanced_Ads_Pro_Module_Background_Ads' => __DIR__ . '/../..' . '/modules/background-ads/background.class.php',
        'Advanced_Ads_Pro_Module_Background_Ads_Admin' => __DIR__ . '/../..' . '/modules/background-ads/admin.class.php',
        'Advanced_Ads_Pro_Module_CFP' => __DIR__ . '/../..' . '/modules/click-fraud-protection/click-fraud-protection.class.php',
        'Advanced_Ads_Pro_Module_CFP_Admin' => __DIR__ . '/../..' . '/modules/click-fraud-protection/admin.class.php',
        'Advanced_Ads_Pro_Module_Cache_Busting' => __DIR__ . '/../..' . '/modules/cache-busting/cache-busting.class.php',
        'Advanced_Ads_Pro_Module_Cache_Busting_Admin' => __DIR__ . '/../..' . '/modules/cache-busting/admin.class.php',
        'Advanced_Ads_Pro_Module_Cache_Busting_Admin_UI' => __DIR__ . '/../..' . '/modules/cache-busting/admin-ui.class.php',
        'Advanced_Ads_Pro_Module_Duplicate_Ads' => __DIR__ . '/../..' . '/modules/duplicate-ads/duplicate.class.php',
        'Advanced_Ads_Pro_Module_Duplicate_Ads_Admin' => __DIR__ . '/../..' . '/modules/duplicate-ads/admin.class.php',
        'Advanced_Ads_Pro_Module_Extended_Adblocker' => __DIR__ . '/../..' . '/modules/extended-adblocker/main.class.php',
        'Advanced_Ads_Pro_Module_Extended_Adblocker_Admin' => __DIR__ . '/../..' . '/modules/extended-adblocker/admin.class.php',
        'Advanced_Ads_Pro_Module_GamiPress' => __DIR__ . '/../..' . '/modules/gamipress/gamipress.class.php',
        'Advanced_Ads_Pro_Module_GamiPress_Admin' => __DIR__ . '/../..' . '/modules/gamipress/admin.class.php',
        'Advanced_Ads_Pro_Module_Grids' => __DIR__ . '/../..' . '/modules/grids/grids.class.php',
        'Advanced_Ads_Pro_Module_Grids_Admin' => __DIR__ . '/../..' . '/modules/grids/admin.class.php',
        'Advanced_Ads_Pro_Module_Inject_Content' => __DIR__ . '/../..' . '/modules/inject-content/inject-content.class.php',
        'Advanced_Ads_Pro_Module_Inject_Content_Admin' => __DIR__ . '/../..' . '/modules/inject-content/admin.class.php',
        'Advanced_Ads_Pro_Module_Inject_Content_Custom_Position' => __DIR__ . '/../..' . '/modules/inject-content/inject-content-custom-position.class.php',
        'Advanced_Ads_Pro_Module_Lazy_Load_Admin' => __DIR__ . '/../..' . '/modules/lazy-load/admin.class.php',
        'Advanced_Ads_Pro_Module_PaidMembershipsPro' => __DIR__ . '/../..' . '/modules/paid-memberships-pro/main.class.php',
        'Advanced_Ads_Pro_Module_PaidMembershipsPro_Admin' => __DIR__ . '/../..' . '/modules/paid-memberships-pro/admin.class.php',
        'Advanced_Ads_Pro_Module_Parallax' => __DIR__ . '/../..' . '/modules/parallax-ads/classes/Advanced_Ads_Pro_Module_Parallax.php',
        'Advanced_Ads_Pro_Module_Parallax_Admin_UI' => __DIR__ . '/../..' . '/modules/parallax-ads/classes/Advanced_Ads_Pro_Module_Parallax_Admin_UI.php',
        'Advanced_Ads_Pro_Module_Parallax_Frontend' => __DIR__ . '/../..' . '/modules/parallax-ads/classes/Advanced_Ads_Pro_Module_Parallax_Frontend.php',
        'Advanced_Ads_Pro_Module_Placement_Conditions' => __DIR__ . '/../..' . '/modules/placement_conditions/main.class.php',
        'Advanced_Ads_Pro_Module_Placement_Conditions_Admin' => __DIR__ . '/../..' . '/modules/placement_conditions/admin.class.php',
        'Advanced_Ads_Pro_Offset_Shifter' => __DIR__ . '/../..' . '/classes/class-advanced-ads-pro-offset-shifter.php',
        'Advanced_Ads_Pro_Placement_Tests' => __DIR__ . '/../..' . '/modules/placement-tests/placement-tests.class.php',
        'Advanced_Ads_Pro_Utils' => __DIR__ . '/../..' . '/classes/utils.php',
        'Advanced_Ads_Pro_Weekdays' => __DIR__ . '/../..' . '/modules/weekdays/class-advanced-ads-pro-weekdays.php',
        'Attribute' => __DIR__ . '/..' . '/symfony/polyfill-php80/Resources/stubs/Attribute.php',
        'Composer\\CaBundle\\CaBundle' => __DIR__ . '/..' . '/composer/ca-bundle/src/CaBundle.php',
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
        'GeoIp2\\Database\\Reader' => __DIR__ . '/..' . '/geoip2/geoip2/src/Database/Reader.php',
        'GeoIp2\\Exception\\AddressNotFoundException' => __DIR__ . '/..' . '/geoip2/geoip2/src/Exception/AddressNotFoundException.php',
        'GeoIp2\\Exception\\AuthenticationException' => __DIR__ . '/..' . '/geoip2/geoip2/src/Exception/AuthenticationException.php',
        'GeoIp2\\Exception\\GeoIp2Exception' => __DIR__ . '/..' . '/geoip2/geoip2/src/Exception/GeoIp2Exception.php',
        'GeoIp2\\Exception\\HttpException' => __DIR__ . '/..' . '/geoip2/geoip2/src/Exception/HttpException.php',
        'GeoIp2\\Exception\\InvalidRequestException' => __DIR__ . '/..' . '/geoip2/geoip2/src/Exception/InvalidRequestException.php',
        'GeoIp2\\Exception\\OutOfQueriesException' => __DIR__ . '/..' . '/geoip2/geoip2/src/Exception/OutOfQueriesException.php',
        'GeoIp2\\Model\\AbstractModel' => __DIR__ . '/..' . '/geoip2/geoip2/src/Model/AbstractModel.php',
        'GeoIp2\\Model\\AnonymousIp' => __DIR__ . '/..' . '/geoip2/geoip2/src/Model/AnonymousIp.php',
        'GeoIp2\\Model\\Asn' => __DIR__ . '/..' . '/geoip2/geoip2/src/Model/Asn.php',
        'GeoIp2\\Model\\City' => __DIR__ . '/..' . '/geoip2/geoip2/src/Model/City.php',
        'GeoIp2\\Model\\ConnectionType' => __DIR__ . '/..' . '/geoip2/geoip2/src/Model/ConnectionType.php',
        'GeoIp2\\Model\\Country' => __DIR__ . '/..' . '/geoip2/geoip2/src/Model/Country.php',
        'GeoIp2\\Model\\Domain' => __DIR__ . '/..' . '/geoip2/geoip2/src/Model/Domain.php',
        'GeoIp2\\Model\\Enterprise' => __DIR__ . '/..' . '/geoip2/geoip2/src/Model/Enterprise.php',
        'GeoIp2\\Model\\Insights' => __DIR__ . '/..' . '/geoip2/geoip2/src/Model/Insights.php',
        'GeoIp2\\Model\\Isp' => __DIR__ . '/..' . '/geoip2/geoip2/src/Model/Isp.php',
        'GeoIp2\\ProviderInterface' => __DIR__ . '/..' . '/geoip2/geoip2/src/ProviderInterface.php',
        'GeoIp2\\Record\\AbstractPlaceRecord' => __DIR__ . '/..' . '/geoip2/geoip2/src/Record/AbstractPlaceRecord.php',
        'GeoIp2\\Record\\AbstractRecord' => __DIR__ . '/..' . '/geoip2/geoip2/src/Record/AbstractRecord.php',
        'GeoIp2\\Record\\City' => __DIR__ . '/..' . '/geoip2/geoip2/src/Record/City.php',
        'GeoIp2\\Record\\Continent' => __DIR__ . '/..' . '/geoip2/geoip2/src/Record/Continent.php',
        'GeoIp2\\Record\\Country' => __DIR__ . '/..' . '/geoip2/geoip2/src/Record/Country.php',
        'GeoIp2\\Record\\Location' => __DIR__ . '/..' . '/geoip2/geoip2/src/Record/Location.php',
        'GeoIp2\\Record\\MaxMind' => __DIR__ . '/..' . '/geoip2/geoip2/src/Record/MaxMind.php',
        'GeoIp2\\Record\\Postal' => __DIR__ . '/..' . '/geoip2/geoip2/src/Record/Postal.php',
        'GeoIp2\\Record\\RepresentedCountry' => __DIR__ . '/..' . '/geoip2/geoip2/src/Record/RepresentedCountry.php',
        'GeoIp2\\Record\\Subdivision' => __DIR__ . '/..' . '/geoip2/geoip2/src/Record/Subdivision.php',
        'GeoIp2\\Record\\Traits' => __DIR__ . '/..' . '/geoip2/geoip2/src/Record/Traits.php',
        'GeoIp2\\Util' => __DIR__ . '/..' . '/geoip2/geoip2/src/Util.php',
        'GeoIp2\\WebService\\Client' => __DIR__ . '/..' . '/geoip2/geoip2/src/WebService/Client.php',
        'MaxMind\\Db\\Reader' => __DIR__ . '/..' . '/maxmind-db/reader/src/MaxMind/Db/Reader.php',
        'MaxMind\\Db\\Reader\\Decoder' => __DIR__ . '/..' . '/maxmind-db/reader/src/MaxMind/Db/Reader/Decoder.php',
        'MaxMind\\Db\\Reader\\InvalidDatabaseException' => __DIR__ . '/..' . '/maxmind-db/reader/src/MaxMind/Db/Reader/InvalidDatabaseException.php',
        'MaxMind\\Db\\Reader\\Metadata' => __DIR__ . '/..' . '/maxmind-db/reader/src/MaxMind/Db/Reader/Metadata.php',
        'MaxMind\\Db\\Reader\\Util' => __DIR__ . '/..' . '/maxmind-db/reader/src/MaxMind/Db/Reader/Util.php',
        'MaxMind\\Exception\\AuthenticationException' => __DIR__ . '/..' . '/maxmind/web-service-common/src/Exception/AuthenticationException.php',
        'MaxMind\\Exception\\HttpException' => __DIR__ . '/..' . '/maxmind/web-service-common/src/Exception/HttpException.php',
        'MaxMind\\Exception\\InsufficientFundsException' => __DIR__ . '/..' . '/maxmind/web-service-common/src/Exception/InsufficientFundsException.php',
        'MaxMind\\Exception\\InvalidInputException' => __DIR__ . '/..' . '/maxmind/web-service-common/src/Exception/InvalidInputException.php',
        'MaxMind\\Exception\\InvalidRequestException' => __DIR__ . '/..' . '/maxmind/web-service-common/src/Exception/InvalidRequestException.php',
        'MaxMind\\Exception\\IpAddressNotFoundException' => __DIR__ . '/..' . '/maxmind/web-service-common/src/Exception/IpAddressNotFoundException.php',
        'MaxMind\\Exception\\PermissionRequiredException' => __DIR__ . '/..' . '/maxmind/web-service-common/src/Exception/PermissionRequiredException.php',
        'MaxMind\\Exception\\WebServiceException' => __DIR__ . '/..' . '/maxmind/web-service-common/src/Exception/WebServiceException.php',
        'MaxMind\\WebService\\Client' => __DIR__ . '/..' . '/maxmind/web-service-common/src/WebService/Client.php',
        'MaxMind\\WebService\\Http\\CurlRequest' => __DIR__ . '/..' . '/maxmind/web-service-common/src/WebService/Http/CurlRequest.php',
        'MaxMind\\WebService\\Http\\Request' => __DIR__ . '/..' . '/maxmind/web-service-common/src/WebService/Http/Request.php',
        'MaxMind\\WebService\\Http\\RequestFactory' => __DIR__ . '/..' . '/maxmind/web-service-common/src/WebService/Http/RequestFactory.php',
        'PhpToken' => __DIR__ . '/..' . '/symfony/polyfill-php80/Resources/stubs/PhpToken.php',
        'Stringable' => __DIR__ . '/..' . '/symfony/polyfill-php80/Resources/stubs/Stringable.php',
        'Symfony\\Component\\CssSelector\\CssSelectorConverter' => __DIR__ . '/..' . '/symfony/css-selector/CssSelectorConverter.php',
        'Symfony\\Component\\CssSelector\\Exception\\ExceptionInterface' => __DIR__ . '/..' . '/symfony/css-selector/Exception/ExceptionInterface.php',
        'Symfony\\Component\\CssSelector\\Exception\\ExpressionErrorException' => __DIR__ . '/..' . '/symfony/css-selector/Exception/ExpressionErrorException.php',
        'Symfony\\Component\\CssSelector\\Exception\\InternalErrorException' => __DIR__ . '/..' . '/symfony/css-selector/Exception/InternalErrorException.php',
        'Symfony\\Component\\CssSelector\\Exception\\ParseException' => __DIR__ . '/..' . '/symfony/css-selector/Exception/ParseException.php',
        'Symfony\\Component\\CssSelector\\Exception\\SyntaxErrorException' => __DIR__ . '/..' . '/symfony/css-selector/Exception/SyntaxErrorException.php',
        'Symfony\\Component\\CssSelector\\Node\\AbstractNode' => __DIR__ . '/..' . '/symfony/css-selector/Node/AbstractNode.php',
        'Symfony\\Component\\CssSelector\\Node\\AttributeNode' => __DIR__ . '/..' . '/symfony/css-selector/Node/AttributeNode.php',
        'Symfony\\Component\\CssSelector\\Node\\ClassNode' => __DIR__ . '/..' . '/symfony/css-selector/Node/ClassNode.php',
        'Symfony\\Component\\CssSelector\\Node\\CombinedSelectorNode' => __DIR__ . '/..' . '/symfony/css-selector/Node/CombinedSelectorNode.php',
        'Symfony\\Component\\CssSelector\\Node\\ElementNode' => __DIR__ . '/..' . '/symfony/css-selector/Node/ElementNode.php',
        'Symfony\\Component\\CssSelector\\Node\\FunctionNode' => __DIR__ . '/..' . '/symfony/css-selector/Node/FunctionNode.php',
        'Symfony\\Component\\CssSelector\\Node\\HashNode' => __DIR__ . '/..' . '/symfony/css-selector/Node/HashNode.php',
        'Symfony\\Component\\CssSelector\\Node\\NegationNode' => __DIR__ . '/..' . '/symfony/css-selector/Node/NegationNode.php',
        'Symfony\\Component\\CssSelector\\Node\\NodeInterface' => __DIR__ . '/..' . '/symfony/css-selector/Node/NodeInterface.php',
        'Symfony\\Component\\CssSelector\\Node\\PseudoNode' => __DIR__ . '/..' . '/symfony/css-selector/Node/PseudoNode.php',
        'Symfony\\Component\\CssSelector\\Node\\SelectorNode' => __DIR__ . '/..' . '/symfony/css-selector/Node/SelectorNode.php',
        'Symfony\\Component\\CssSelector\\Node\\Specificity' => __DIR__ . '/..' . '/symfony/css-selector/Node/Specificity.php',
        'Symfony\\Component\\CssSelector\\Parser\\Handler\\CommentHandler' => __DIR__ . '/..' . '/symfony/css-selector/Parser/Handler/CommentHandler.php',
        'Symfony\\Component\\CssSelector\\Parser\\Handler\\HandlerInterface' => __DIR__ . '/..' . '/symfony/css-selector/Parser/Handler/HandlerInterface.php',
        'Symfony\\Component\\CssSelector\\Parser\\Handler\\HashHandler' => __DIR__ . '/..' . '/symfony/css-selector/Parser/Handler/HashHandler.php',
        'Symfony\\Component\\CssSelector\\Parser\\Handler\\IdentifierHandler' => __DIR__ . '/..' . '/symfony/css-selector/Parser/Handler/IdentifierHandler.php',
        'Symfony\\Component\\CssSelector\\Parser\\Handler\\NumberHandler' => __DIR__ . '/..' . '/symfony/css-selector/Parser/Handler/NumberHandler.php',
        'Symfony\\Component\\CssSelector\\Parser\\Handler\\StringHandler' => __DIR__ . '/..' . '/symfony/css-selector/Parser/Handler/StringHandler.php',
        'Symfony\\Component\\CssSelector\\Parser\\Handler\\WhitespaceHandler' => __DIR__ . '/..' . '/symfony/css-selector/Parser/Handler/WhitespaceHandler.php',
        'Symfony\\Component\\CssSelector\\Parser\\Parser' => __DIR__ . '/..' . '/symfony/css-selector/Parser/Parser.php',
        'Symfony\\Component\\CssSelector\\Parser\\ParserInterface' => __DIR__ . '/..' . '/symfony/css-selector/Parser/ParserInterface.php',
        'Symfony\\Component\\CssSelector\\Parser\\Reader' => __DIR__ . '/..' . '/symfony/css-selector/Parser/Reader.php',
        'Symfony\\Component\\CssSelector\\Parser\\Shortcut\\ClassParser' => __DIR__ . '/..' . '/symfony/css-selector/Parser/Shortcut/ClassParser.php',
        'Symfony\\Component\\CssSelector\\Parser\\Shortcut\\ElementParser' => __DIR__ . '/..' . '/symfony/css-selector/Parser/Shortcut/ElementParser.php',
        'Symfony\\Component\\CssSelector\\Parser\\Shortcut\\EmptyStringParser' => __DIR__ . '/..' . '/symfony/css-selector/Parser/Shortcut/EmptyStringParser.php',
        'Symfony\\Component\\CssSelector\\Parser\\Shortcut\\HashParser' => __DIR__ . '/..' . '/symfony/css-selector/Parser/Shortcut/HashParser.php',
        'Symfony\\Component\\CssSelector\\Parser\\Token' => __DIR__ . '/..' . '/symfony/css-selector/Parser/Token.php',
        'Symfony\\Component\\CssSelector\\Parser\\TokenStream' => __DIR__ . '/..' . '/symfony/css-selector/Parser/TokenStream.php',
        'Symfony\\Component\\CssSelector\\Parser\\Tokenizer\\Tokenizer' => __DIR__ . '/..' . '/symfony/css-selector/Parser/Tokenizer/Tokenizer.php',
        'Symfony\\Component\\CssSelector\\Parser\\Tokenizer\\TokenizerEscaping' => __DIR__ . '/..' . '/symfony/css-selector/Parser/Tokenizer/TokenizerEscaping.php',
        'Symfony\\Component\\CssSelector\\Parser\\Tokenizer\\TokenizerPatterns' => __DIR__ . '/..' . '/symfony/css-selector/Parser/Tokenizer/TokenizerPatterns.php',
        'Symfony\\Component\\CssSelector\\XPath\\Extension\\AbstractExtension' => __DIR__ . '/..' . '/symfony/css-selector/XPath/Extension/AbstractExtension.php',
        'Symfony\\Component\\CssSelector\\XPath\\Extension\\AttributeMatchingExtension' => __DIR__ . '/..' . '/symfony/css-selector/XPath/Extension/AttributeMatchingExtension.php',
        'Symfony\\Component\\CssSelector\\XPath\\Extension\\CombinationExtension' => __DIR__ . '/..' . '/symfony/css-selector/XPath/Extension/CombinationExtension.php',
        'Symfony\\Component\\CssSelector\\XPath\\Extension\\ExtensionInterface' => __DIR__ . '/..' . '/symfony/css-selector/XPath/Extension/ExtensionInterface.php',
        'Symfony\\Component\\CssSelector\\XPath\\Extension\\FunctionExtension' => __DIR__ . '/..' . '/symfony/css-selector/XPath/Extension/FunctionExtension.php',
        'Symfony\\Component\\CssSelector\\XPath\\Extension\\HtmlExtension' => __DIR__ . '/..' . '/symfony/css-selector/XPath/Extension/HtmlExtension.php',
        'Symfony\\Component\\CssSelector\\XPath\\Extension\\NodeExtension' => __DIR__ . '/..' . '/symfony/css-selector/XPath/Extension/NodeExtension.php',
        'Symfony\\Component\\CssSelector\\XPath\\Extension\\PseudoClassExtension' => __DIR__ . '/..' . '/symfony/css-selector/XPath/Extension/PseudoClassExtension.php',
        'Symfony\\Component\\CssSelector\\XPath\\Translator' => __DIR__ . '/..' . '/symfony/css-selector/XPath/Translator.php',
        'Symfony\\Component\\CssSelector\\XPath\\TranslatorInterface' => __DIR__ . '/..' . '/symfony/css-selector/XPath/TranslatorInterface.php',
        'Symfony\\Component\\CssSelector\\XPath\\XPathExpr' => __DIR__ . '/..' . '/symfony/css-selector/XPath/XPathExpr.php',
        'Symfony\\Polyfill\\Php80\\Php80' => __DIR__ . '/..' . '/symfony/polyfill-php80/Php80.php',
        'Symfony\\Polyfill\\Php80\\PhpToken' => __DIR__ . '/..' . '/symfony/polyfill-php80/PhpToken.php',
        'UnhandledMatchError' => __DIR__ . '/..' . '/symfony/polyfill-php80/Resources/stubs/UnhandledMatchError.php',
        'ValueError' => __DIR__ . '/..' . '/symfony/polyfill-php80/Resources/stubs/ValueError.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit_advanced_ads_pro::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit_advanced_ads_pro::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit_advanced_ads_pro::$classMap;

        }, null, ClassLoader::class);
    }
}
