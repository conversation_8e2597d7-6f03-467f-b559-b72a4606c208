/*! For license information please see screen-onboarding.js.LICENSE.txt */
(()=>{var e={20:(e,t,r)=>{"use strict";var n=r(540),o=Symbol.for("react.element"),a=Symbol.for("react.fragment"),i=Object.prototype.hasOwnProperty,c=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,s={key:!0,ref:!0,__self:!0,__source:!0};function l(e,t,r){var n,a={},l=null,u=null;for(n in void 0!==r&&(l=""+r),void 0!==t.key&&(l=""+t.key),void 0!==t.ref&&(u=t.ref),t)i.call(t,n)&&!s.hasOwnProperty(n)&&(a[n]=t[n]);if(e&&e.defaultProps)for(n in t=e.defaultProps)void 0===a[n]&&(a[n]=t[n]);return{$$typeof:o,type:e,key:l,ref:u,props:a,_owner:c.current}}t.Fragment=a,t.jsx=l,t.jsxs=l},287:(e,t)=>{"use strict";var r=Symbol.for("react.element"),n=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),i=Symbol.for("react.profiler"),c=Symbol.for("react.provider"),s=Symbol.for("react.context"),l=Symbol.for("react.forward_ref"),u=Symbol.for("react.suspense"),f=Symbol.for("react.memo"),d=Symbol.for("react.lazy"),p=Symbol.iterator;var h={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},m=Object.assign,y={};function b(e,t,r){this.props=e,this.context=t,this.refs=y,this.updater=r||h}function v(){}function g(e,t,r){this.props=e,this.context=t,this.refs=y,this.updater=r||h}b.prototype.isReactComponent={},b.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},b.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},v.prototype=b.prototype;var j=g.prototype=new v;j.constructor=g,m(j,b.prototype),j.isPureReactComponent=!0;var x=Array.isArray,w=Object.prototype.hasOwnProperty,O={current:null},S={key:!0,ref:!0,__self:!0,__source:!0};function z(e,t,n){var o,a={},i=null,c=null;if(null!=t)for(o in void 0!==t.ref&&(c=t.ref),void 0!==t.key&&(i=""+t.key),t)w.call(t,o)&&!S.hasOwnProperty(o)&&(a[o]=t[o]);var s=arguments.length-2;if(1===s)a.children=n;else if(1<s){for(var l=Array(s),u=0;u<s;u++)l[u]=arguments[u+2];a.children=l}if(e&&e.defaultProps)for(o in s=e.defaultProps)void 0===a[o]&&(a[o]=s[o]);return{$$typeof:r,type:e,key:i,ref:c,props:a,_owner:O.current}}function _(e){return"object"==typeof e&&null!==e&&e.$$typeof===r}var N=/\/+/g;function P(e,t){return"object"==typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,(function(e){return t[e]}))}(""+e.key):t.toString(36)}function E(e,t,o,a,i){var c=typeof e;"undefined"!==c&&"boolean"!==c||(e=null);var s=!1;if(null===e)s=!0;else switch(c){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case r:case n:s=!0}}if(s)return i=i(s=e),e=""===a?"."+P(s,0):a,x(i)?(o="",null!=e&&(o=e.replace(N,"$&/")+"/"),E(i,t,o,"",(function(e){return e}))):null!=i&&(_(i)&&(i=function(e,t){return{$$typeof:r,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(i,o+(!i.key||s&&s.key===i.key?"":(""+i.key).replace(N,"$&/")+"/")+e)),t.push(i)),1;if(s=0,a=""===a?".":a+":",x(e))for(var l=0;l<e.length;l++){var u=a+P(c=e[l],l);s+=E(c,t,o,u,i)}else if(u=function(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=p&&e[p]||e["@@iterator"])?e:null}(e),"function"==typeof u)for(e=u.call(e),l=0;!(c=e.next()).done;)s+=E(c=c.value,t,o,u=a+P(c,l++),i);else if("object"===c)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function A(e,t,r){if(null==e)return e;var n=[],o=0;return E(e,n,"","",(function(e){return t.call(r,e,o++)})),n}function L(e){if(-1===e._status){var t=e._result;(t=t()).then((function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)}),(function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)})),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var k={current:null},M={transition:null},D={ReactCurrentDispatcher:k,ReactCurrentBatchConfig:M,ReactCurrentOwner:O};function T(){throw Error("act(...) is not supported in production builds of React.")}t.Children={map:A,forEach:function(e,t,r){A(e,(function(){t.apply(this,arguments)}),r)},count:function(e){var t=0;return A(e,(function(){t++})),t},toArray:function(e){return A(e,(function(e){return e}))||[]},only:function(e){if(!_(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=b,t.Fragment=o,t.Profiler=i,t.PureComponent=g,t.StrictMode=a,t.Suspense=u,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=D,t.act=T,t.cloneElement=function(e,t,n){if(null==e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var o=m({},e.props),a=e.key,i=e.ref,c=e._owner;if(null!=t){if(void 0!==t.ref&&(i=t.ref,c=O.current),void 0!==t.key&&(a=""+t.key),e.type&&e.type.defaultProps)var s=e.type.defaultProps;for(l in t)w.call(t,l)&&!S.hasOwnProperty(l)&&(o[l]=void 0===t[l]&&void 0!==s?s[l]:t[l])}var l=arguments.length-2;if(1===l)o.children=n;else if(1<l){s=Array(l);for(var u=0;u<l;u++)s[u]=arguments[u+2];o.children=s}return{$$typeof:r,type:e.type,key:a,ref:i,props:o,_owner:c}},t.createContext=function(e){return(e={$$typeof:s,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:c,_context:e},e.Consumer=e},t.createElement=z,t.createFactory=function(e){var t=z.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:l,render:e}},t.isValidElement=_,t.lazy=function(e){return{$$typeof:d,_payload:{_status:-1,_result:e},_init:L}},t.memo=function(e,t){return{$$typeof:f,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=M.transition;M.transition={};try{e()}finally{M.transition=t}},t.unstable_act=T,t.useCallback=function(e,t){return k.current.useCallback(e,t)},t.useContext=function(e){return k.current.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e){return k.current.useDeferredValue(e)},t.useEffect=function(e,t){return k.current.useEffect(e,t)},t.useId=function(){return k.current.useId()},t.useImperativeHandle=function(e,t,r){return k.current.useImperativeHandle(e,t,r)},t.useInsertionEffect=function(e,t){return k.current.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return k.current.useLayoutEffect(e,t)},t.useMemo=function(e,t){return k.current.useMemo(e,t)},t.useReducer=function(e,t,r){return k.current.useReducer(e,t,r)},t.useRef=function(e){return k.current.useRef(e)},t.useState=function(e){return k.current.useState(e)},t.useSyncExternalStore=function(e,t,r){return k.current.useSyncExternalStore(e,t,r)},t.useTransition=function(){return k.current.useTransition()},t.version="18.3.1"},540:(e,t,r)=>{"use strict";e.exports=r(287)},848:(e,t,r)=>{"use strict";e.exports=r(20)},942:(e,t)=>{var r;!function(){"use strict";var n={}.hasOwnProperty;function o(){for(var e="",t=0;t<arguments.length;t++){var r=arguments[t];r&&(e=i(e,a(r)))}return e}function a(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return o.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var r in e)n.call(e,r)&&e[r]&&(t=i(t,r));return t}function i(e,t){return t?e?e+" "+t:e+t:e}e.exports?(o.default=o,e.exports=o):void 0===(r=function(){return o}.apply(t,[]))||(e.exports=r)}()}},t={};function r(n){var o=t[n];if(void 0!==o)return o.exports;var a=t[n]={exports:{}};return e[n](a,a.exports,r),a.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";const e=wp.domReady;var t=r.n(e);const n=wp.element;var o=r(942),a=r.n(o);const i=(0,n.createContext)({isLoading:!1,isFirstStep:!0,isLastStep:!1,stepCount:0,activeStep:0});function c(){var e=(0,n.useContext)(i);if(!e)throw Error("Wrap your step with `Wizard`");return e}var s=r(848);function l(e){return l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},l(e)}function u(){u=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(e,t,r){e[t]=r.value},a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",c=a.asyncIterator||"@@asyncIterator",s=a.toStringTag||"@@toStringTag";function f(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{f({},"")}catch(e){f=function(e,t,r){return e[t]=r}}function d(e,t,r,n){var a=t&&t.prototype instanceof g?t:g,i=Object.create(a.prototype),c=new k(n||[]);return o(i,"_invoke",{value:P(e,r,c)}),i}function p(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=d;var h="suspendedStart",m="suspendedYield",y="executing",b="completed",v={};function g(){}function j(){}function x(){}var w={};f(w,i,(function(){return this}));var O=Object.getPrototypeOf,S=O&&O(O(M([])));S&&S!==r&&n.call(S,i)&&(w=S);var z=x.prototype=g.prototype=Object.create(w);function _(e){["next","throw","return"].forEach((function(t){f(e,t,(function(e){return this._invoke(t,e)}))}))}function N(e,t){function r(o,a,i,c){var s=p(e[o],e,a);if("throw"!==s.type){var u=s.arg,f=u.value;return f&&"object"==l(f)&&n.call(f,"__await")?t.resolve(f.__await).then((function(e){r("next",e,i,c)}),(function(e){r("throw",e,i,c)})):t.resolve(f).then((function(e){u.value=e,i(u)}),(function(e){return r("throw",e,i,c)}))}c(s.arg)}var a;o(this,"_invoke",{value:function(e,n){function o(){return new t((function(t,o){r(e,n,t,o)}))}return a=a?a.then(o,o):o()}})}function P(t,r,n){var o=h;return function(a,i){if(o===y)throw Error("Generator is already running");if(o===b){if("throw"===a)throw i;return{value:e,done:!0}}for(n.method=a,n.arg=i;;){var c=n.delegate;if(c){var s=E(c,n);if(s){if(s===v)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===h)throw o=b,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=y;var l=p(t,r,n);if("normal"===l.type){if(o=n.done?b:m,l.arg===v)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(o=b,n.method="throw",n.arg=l.arg)}}}function E(t,r){var n=r.method,o=t.iterator[n];if(o===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,E(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var a=p(o,t.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,v;var i=a.arg;return i?i.done?(r[t.resultName]=i.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,v):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function A(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function L(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function k(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(A,this),this.reset(!0)}function M(t){if(t||""===t){var r=t[i];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,a=function r(){for(;++o<t.length;)if(n.call(t,o))return r.value=t[o],r.done=!1,r;return r.value=e,r.done=!0,r};return a.next=a}}throw new TypeError(l(t)+" is not iterable")}return j.prototype=x,o(z,"constructor",{value:x,configurable:!0}),o(x,"constructor",{value:j,configurable:!0}),j.displayName=f(x,s,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===j||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,x):(e.__proto__=x,f(e,s,"GeneratorFunction")),e.prototype=Object.create(z),e},t.awrap=function(e){return{__await:e}},_(N.prototype),f(N.prototype,c,(function(){return this})),t.AsyncIterator=N,t.async=function(e,r,n,o,a){void 0===a&&(a=Promise);var i=new N(d(e,r,n,o),a);return t.isGeneratorFunction(r)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},_(z),f(z,s,"Generator"),f(z,i,(function(){return this})),f(z,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=M,k.prototype={constructor:k,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(L),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function o(n,o){return c.type="throw",c.arg=t,r.next=n,o&&(r.method="next",r.arg=e),!!o}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],c=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var s=n.call(i,"catchLoc"),l=n.call(i,"finallyLoc");if(s&&l){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(s){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=e,i.arg=t,a?(this.method="next",this.next=a.finallyLoc,v):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),L(r),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var o=n.arg;L(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:M(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),v}},t}function f(e,t,r,n,o,a,i){try{var c=e[a](i),s=c.value}catch(e){return void r(e)}c.done?t(s):Promise.resolve(s).then(n,o)}function d(e){return function(){var t=this,r=arguments;return new Promise((function(n,o){var a=e.apply(t,r);function i(e){f(a,n,o,i,c,"next",e)}function c(e){f(a,n,o,i,c,"throw",e)}i(void 0)}))}}function p(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,a,i,c=[],s=!0,l=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=a.call(r)).done)&&(c.push(n.value),c.length!==t);s=!0);}catch(e){l=!0,o=e}finally{try{if(!s&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(l)throw o}}return c}}(e,t)||function(e,t){if(e){if("string"==typeof e)return h(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?h(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function h(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}const m=(0,n.memo)((function(e){var t=e.header,r=e.footer,o=e.children,a=e.wrapper,c=e.startIndex,l=void 0===c?1:c,f=p((0,n.useState)(l-1),2),h=f[0],m=f[1],y=p((0,n.useState)(!1),2),b=y[0],v=y[1],g=(0,n.useRef)(!0),j=(0,n.useRef)(!1),x=(0,n.useRef)((function(){})),w=n.Children.toArray(o).length;g.current=h<w-1,j.current=h>0;var O=(0,n.useRef)((function(){g.current&&m((function(e){return e+1}))})),S=(0,n.useRef)((function(){j.current&&(x.current=null,m((function(e){return e-1})))})),z=(0,n.useRef)((function(e){e>=0&&e<w&&(x.current=null,m(e))})),_=(0,n.useRef)((function(e){x.current=e})),N=(0,n.useRef)(d(u().mark((function e(){return u().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!g.current||!x.current){e.next=16;break}return e.prev=1,v(!0),e.next=5,x.current();case 5:v(!1),x.current=null,O.current(),e.next=14;break;case 10:throw e.prev=10,e.t0=e.catch(1),v(!1),e.t0;case 14:e.next=17;break;case 16:O.current();case 17:case"end":return e.stop()}}),e,null,[[1,10]])})))),P=(0,n.useMemo)((function(){return{nextStep:N.current,previousStep:S.current,handleStep:_.current,isLoading:b,activeStep:h,stepCount:w,isFirstStep:!j.current,isLastStep:!g.current,goToStep:z.current}}),[h,w,b]),E=(0,n.useMemo)((function(){return n.Children.toArray(o)[h]}),[h,o]),A=(0,n.useMemo)((function(){return a?(0,n.cloneElement)(a,{children:E}):E}),[a,E]);return(0,s.jsxs)(i.Provider,{value:P,children:[t,A,r]})}));function y(e){return advancedAds.endpoints.adminUrl+e}function b(){var e,t=c(),r=t.stepCount,n=t.activeStep,o=Array.apply(null,{length:r});return(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("img",{className:"w-8/12 m-auto",src:(e="assets/img/advancedads-full-logo.svg",advancedAds.endpoints.assetsUrl+e),alt:""}),(0,s.jsx)("div",{className:"advads-wizard-progress",children:o.map((function(e,t){var r=n===t,o=a()("advads-wizard-progress--item",n>t?"is-done":"",r?"is-active":"");return(0,s.jsx)("div",{className:o,children:(0,s.jsx)("div",{className:"advads-wizard-progress--count",children:r?"Step ".concat(t+1):t+1})},"step-".concat(t))}))})]})}const v=advancedAds.i18n;function g(){return c().isLastStep?null:(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)("a",{href:y("admin.php?page=advanced-ads"),className:"no-underline text-base border-0 border-b-2 pb-0.5 border-solid border-gray-800 text-gray-800",children:v.wizard.exitLabel})})}function j(e){var t=e.children,r=e.className,n=void 0===r?"":r;return(0,s.jsx)("div",{className:"bg-white mt-4 mb-8 p-8 border-solid border-gray-200 rounded-sm ".concat(n),children:t})}function x(e){return x="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},x(e)}function w(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function O(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?w(Object(r),!0).forEach((function(t){S(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):w(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function S(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=x(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=x(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==x(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function z(e){var t=e.id,r=e.onChange,n=e.options,o=e.value,i=void 0===o?"":o,c=e.isButton,l=void 0!==c&&c,u=e.className,f=void 0===u?"":u,d=a()("advads-radio-list",{"is-button":l},f);return(0,s.jsx)("div",{className:d,children:n.map((function(e){var n="radio-".concat(e.value,"-").concat(t),o={type:"radio",id:n,name:t,value:e.value};return i&&(o.checked=i===e.value),(0,s.jsxs)("div",{className:"advads-radio-list--item",children:[(0,s.jsx)("input",O(O({},o),{},{onChange:function(){return r(e.value)}})),(0,s.jsx)("label",{htmlFor:n,children:(0,s.jsx)("span",{children:e.label})})]},e.value)}))})}function _(e){return _="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},_(e)}function N(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function P(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?N(Object(r),!0).forEach((function(t){E(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):N(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function E(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=_(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=_(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==_(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function A(e){return(0,s.jsxs)("svg",P(P({xmlns:"http://www.w3.org/2000/svg",width:60,height:60,overflow:"visible"},e),{},{children:[(0,s.jsx)("path",{fill:"#66acc7",d:"M35.861 21.053c2.025-3.465.823-7.894-2.684-9.895s-7.996-.814-10.021 2.652a9.303 9.303 0 0 0-.251.475l-6.845 11.71a8.885 8.885 0 0 0-.421.719L8.531 38.98l12.703 7.12 7.074-12.163a6.89 6.89 0 0 0 .419-.72l6.846-11.712c.1-.145.198-.296.288-.452"}),(0,s.jsx)("path",{fill:"#0074a2",d:"M21.293 46.063c-2.013 3.522-6.555 4.817-10.042 2.786s-4.728-6.443-2.711-9.964 6.516-4.822 10.003-2.791 4.764 6.449 2.75 9.969"}),(0,s.jsx)("path",{fill:"#3390b5",d:"M48.706 23.372a7.263 7.263 0 0 0-9.907 2.648l-7.253 12.53a7.23 7.23 0 0 0 2.636 9.873c.***************.019.012a7.265 7.265 0 0 0 9.909-2.648l7.252-12.531a7.226 7.226 0 0 0-2.64-9.873"})]}))}function L(e){return L="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},L(e)}function k(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function M(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?k(Object(r),!0).forEach((function(t){D(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):k(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function D(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=L(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=L(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==L(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function T(e){return(0,s.jsx)("svg",M(M({xmlns:"http://www.w3.org/2000/svg",width:60,height:60,fill:"#0074a2",overflow:"visible"},e),{},{children:(0,s.jsx)("path",{d:"M19.632 37.137c-.301 0-.606-.077-.889-.241l-9.279-5.351A1.79 1.79 0 0 1 8.572 30c0-.638.342-1.226.893-1.545l9.279-5.355a1.784 1.784 0 0 1 2.438.656 1.78 1.78 0 0 1-.652 2.434L13.926 30l6.604 3.806a1.787 1.787 0 0 1 .652 2.438 1.78 1.78 0 0 1-1.55.893zm20.731 0a1.776 1.776 0 0 1-1.545-.894 1.78 1.78 0 0 1 .651-2.438l6.6-3.806-6.6-3.81a1.774 1.774 0 0 1-.651-2.434 1.783 1.783 0 0 1 2.438-.656l9.278 5.355c.552.319.894.907.894 1.545s-.342 1.226-.894 1.545l-9.278 5.351a1.754 1.754 0 0 1-.893.242zm-15.276 3.144a1.82 1.82 0 0 1-.894-.237 1.786 1.786 0 0 1-.651-2.438l9.821-16.995a1.784 1.784 0 0 1 2.438-.656 1.79 1.79 0 0 1 .656 2.438l-9.821 16.994a1.78 1.78 0 0 1-1.549.894z"})}))}function C(e){return C="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},C(e)}function I(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function R(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?I(Object(r),!0).forEach((function(t){F(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):I(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function F(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=C(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=C(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==C(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function $(e){return(0,s.jsxs)("svg",R(R({xmlns:"http://www.w3.org/2000/svg",width:60,height:60,fill:"#0074a2",overflow:"visible"},e),{},{children:[(0,s.jsx)("path",{d:"M25.706 33.212a3.68 3.68 0 0 1-3.192 1.842 3.683 3.683 0 0 1-3.192-1.841l-1.073-1.863a3.69 3.69 0 0 0-3.195-1.843 3.68 3.68 0 0 0-3.192 1.843l-3.774 6.536a3.682 3.682 0 0 0 .002 3.687 3.68 3.68 0 0 0 3.192 1.843H48.67a3.686 3.686 0 0 0 3.192-1.843 3.69 3.69 0 0 0 .001-3.687L40.625 18.427a3.68 3.68 0 0 0-3.191-1.842 3.685 3.685 0 0 0-3.193 1.842l-8.535 14.785z"}),(0,s.jsx)("circle",{cx:15.055,cy:20.273,r:3.686})]}))}function B(e){var t=e.setOptions,r=c().nextStep,n=[{label:(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(A,{}),(0,s.jsx)("span",{children:v.wizard.firstStep.taskAdSense})]}),value:"google_adsense"},{label:(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)($,{}),(0,s.jsx)("span",{children:v.wizard.firstStep.taskImage})]}),value:"ad_image"},{label:(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(T,{}),(0,s.jsx)("span",{children:v.wizard.firstStep.taskCode})]}),value:"ad_code"}];return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("p",{className:"font-medium mt-0",children:v.wizard.firstStep.stepHeading}),(0,s.jsx)("p",{className:"mt-4 font-medium",children:(0,s.jsxs)("label",{className:"advads-input-radio",htmlFor:"agreement",children:[(0,s.jsx)("input",{type:"checkbox",name:"agreement",id:"agreement",onChange:function(e){return t("agreement",e.target.value)}}),(0,s.jsx)("span",{dangerouslySetInnerHTML:{__html:v.wizard.firstStep.agreementText}})]})}),(0,s.jsx)("h2",{children:v.wizard.firstStep.inputTitle}),(0,s.jsx)(z,{id:"task",className:"!mb-0",isButton:!0,options:n,onChange:function(e){t("taskOption",e),r()}})]})}function U(e){return U="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},U(e)}function H(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function G(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?H(Object(r),!0).forEach((function(t){V(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):H(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function V(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=U(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=U(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==U(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function J(e){var t=e.id,r=e.onChange,n=e.options,o=e.value,i=void 0===o?"":o,c=e.isButton,l=void 0!==c&&c,u=a()("advads-radio-list",{"is-button":l});return(0,s.jsx)("div",{className:u,children:n.map((function(e){var n="checkbox-".concat(e.value,"-").concat(t),o={type:"checkbox",id:n,value:e.value,checked:!1};return i&&(o.checked=i.includes(e.value)),(0,s.jsxs)("div",{className:"advads-radio-list--item",children:[(0,s.jsx)("input",G(G({},o),{},{onChange:function(){return r(e.value)}})),(0,s.jsx)("label",{htmlFor:n,children:(0,s.jsx)("span",{children:e.label})})]},e.value)}))})}function W(e){var t=e.message;return(0,s.jsx)("div",{className:"p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400",children:t})}function Y(){var e=advancedAds.wizard,t=new URLSearchParams({client_id:e.clientId,redirect_uri:e.redirectUri,state:e.state,access_type:"offline",include_granted_scopes:"true",prompt:"consent",response_type:"code"}).toString();return"".concat(e.authUrl,"&").concat(t)}function q(e){var t=e.statusText;try{t=e.responseJSON.data.error}catch(r){try{t=e.responseJSON.data.msg}catch(r){try{t=e.responseJSON.data.raw}catch(r){try{t=e.responseJSON.data.error_msg}catch(e){}}}}return t}function Z(e){var t=e.className,r=void 0===t?"":t;return(0,s.jsx)("div",{className:"bg-gray-200 my-8 h-[1px] ".concat(r)})}function K(e){return K="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},K(e)}function Q(){Q=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(e,t,r){e[t]=r.value},a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",c=a.asyncIterator||"@@asyncIterator",s=a.toStringTag||"@@toStringTag";function l(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{l({},"")}catch(e){l=function(e,t,r){return e[t]=r}}function u(e,t,r,n){var a=t&&t.prototype instanceof b?t:b,i=Object.create(a.prototype),c=new A(n||[]);return o(i,"_invoke",{value:_(e,r,c)}),i}function f(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=u;var d="suspendedStart",p="suspendedYield",h="executing",m="completed",y={};function b(){}function v(){}function g(){}var j={};l(j,i,(function(){return this}));var x=Object.getPrototypeOf,w=x&&x(x(L([])));w&&w!==r&&n.call(w,i)&&(j=w);var O=g.prototype=b.prototype=Object.create(j);function S(e){["next","throw","return"].forEach((function(t){l(e,t,(function(e){return this._invoke(t,e)}))}))}function z(e,t){function r(o,a,i,c){var s=f(e[o],e,a);if("throw"!==s.type){var l=s.arg,u=l.value;return u&&"object"==K(u)&&n.call(u,"__await")?t.resolve(u.__await).then((function(e){r("next",e,i,c)}),(function(e){r("throw",e,i,c)})):t.resolve(u).then((function(e){l.value=e,i(l)}),(function(e){return r("throw",e,i,c)}))}c(s.arg)}var a;o(this,"_invoke",{value:function(e,n){function o(){return new t((function(t,o){r(e,n,t,o)}))}return a=a?a.then(o,o):o()}})}function _(t,r,n){var o=d;return function(a,i){if(o===h)throw Error("Generator is already running");if(o===m){if("throw"===a)throw i;return{value:e,done:!0}}for(n.method=a,n.arg=i;;){var c=n.delegate;if(c){var s=N(c,n);if(s){if(s===y)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===d)throw o=m,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=h;var l=f(t,r,n);if("normal"===l.type){if(o=n.done?m:p,l.arg===y)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(o=m,n.method="throw",n.arg=l.arg)}}}function N(t,r){var n=r.method,o=t.iterator[n];if(o===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,N(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),y;var a=f(o,t.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,y;var i=a.arg;return i?i.done?(r[t.resultName]=i.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,y):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function P(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function E(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function A(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(P,this),this.reset(!0)}function L(t){if(t||""===t){var r=t[i];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,a=function r(){for(;++o<t.length;)if(n.call(t,o))return r.value=t[o],r.done=!1,r;return r.value=e,r.done=!0,r};return a.next=a}}throw new TypeError(K(t)+" is not iterable")}return v.prototype=g,o(O,"constructor",{value:g,configurable:!0}),o(g,"constructor",{value:v,configurable:!0}),v.displayName=l(g,s,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===v||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,g):(e.__proto__=g,l(e,s,"GeneratorFunction")),e.prototype=Object.create(O),e},t.awrap=function(e){return{__await:e}},S(z.prototype),l(z.prototype,c,(function(){return this})),t.AsyncIterator=z,t.async=function(e,r,n,o,a){void 0===a&&(a=Promise);var i=new z(u(e,r,n,o),a);return t.isGeneratorFunction(r)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},S(O),l(O,s,"Generator"),l(O,i,(function(){return this})),l(O,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=L,A.prototype={constructor:A,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(E),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function o(n,o){return c.type="throw",c.arg=t,r.next=n,o&&(r.method="next",r.arg=e),!!o}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],c=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var s=n.call(i,"catchLoc"),l=n.call(i,"finallyLoc");if(s&&l){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(s){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=e,i.arg=t,a?(this.method="next",this.next=a.finallyLoc,y):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),y},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),E(r),y}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var o=n.arg;E(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:L(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),y}},t}function X(e,t,r,n,o,a,i){try{var c=e[a](i),s=c.value}catch(e){return void r(e)}c.done?t(s):Promise.resolve(s).then(n,o)}function ee(e){var t=e.isEnabled,r=e.enableText,n=e.disableText,o=e.onNext,a=c(),i=a.previousStep,l=a.nextStep,u=function(){var e,t=(e=Q().mark((function e(){return Q().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:o&&o(),l();case 2:case"end":return e.stop()}}),e)})),function(){var t=this,r=arguments;return new Promise((function(n,o){var a=e.apply(t,r);function i(e){X(a,n,o,i,c,"next",e)}function c(e){X(a,n,o,i,c,"throw",e)}i(void 0)}))});return function(){return t.apply(this,arguments)}}();return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(Z,{className:"mb-4"}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("div",{children:(0,s.jsx)("button",{onClick:i,className:"button-onboarding !bg-white !border-gray-100 !text-gray-600",children:v.wizard.btnGoBack})}),(0,s.jsx)("div",{children:t?(0,s.jsx)("button",{className:"button-onboarding",onClick:u,children:r}):(0,s.jsx)("button",{className:"button-onboarding",disabled:!0,children:n})})]})]})}function te(){return(0,s.jsx)("div",{className:"absolute inset-0 flex justify-center items-center z-10 bg-white bg-opacity-70",children:(0,s.jsx)("img",{alt:v.wizard.processing,src:"".concat(advancedAds.endpoints.adminUrl,"images/spinner-2x.gif")})})}function re(e){var t=e.accounts,r=e.tokenData,n=e.done,o=e.fail,a=new URLSearchParams(document.location.search),i=[(0,s.jsx)("option",{value:"",children:v.wizard.selectAccount.optionZero},"select-account")];for(var c in t)i.push((0,s.jsxs)("option",{value:JSON.stringify(t[c]),children:[t[c].name," (",c,")"]},c));return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("h2",{children:v.wizard.selectAccount.title}),(0,s.jsx)("label",{htmlFor:"g-account",children:(0,s.jsx)("select",{id:"g-account",onChange:function(e){return function(e){if(e.target.value){e.target.disabled=!0;var t=JSON.parse(e.target.value);wp.ajax.post("advads_gadsense_mapi_select_account",{nonce:a.get("nonce"),account:t,token_data:r}).done((function(e){"function"==typeof n&&n.call(null,e)})).fail((function(t){"function"==typeof o&&o.call(null,t),e.target.disabled=!1}))}}(e)},children:i})})]})}function ne(e){return function(e){if(Array.isArray(e))return oe(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return oe(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?oe(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function oe(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function ae(e){var t,r=e.options,n=e.setOptions;if(!r.adsenseData.account){if(r.adsenseData.phase)switch(r.adsenseData.phase){case"error":return(0,s.jsx)(W,{message:r.adsenseData.error});case"select":return(0,s.jsx)(re,{accounts:r.adsenseData.accountsList,tokenData:r.adsenseData.tokenData,done:function(e){n("adsenseData",{account:e.account})},fail:function(e){var t=q(e);t||(t=v.wizard.googleAd.errors.notSaved),n("adsenseData",{phase:"error",error:t})}})}return(t=new URLSearchParams(document.location.search)).get("code")&&"adsense"===t.get("route")&&t.get("nonce")?(function(){var e=new URLSearchParams(document.location.search);return wp.ajax.post("advads_gadsense_mapi_confirm_code",{nonce:e.get("nonce"),code:e.get("code")})}().done((function(e){(function(e){var t=new URLSearchParams(document.location.search);return wp.ajax.post("advads_gadsense_mapi_get_details",{nonce:t.get("nonce"),token_data:e})})(e.token_data).done((function(e){e.account&&n("adsenseData",{account:e.account}),e.details&&n("adsenseData",{phase:"select",accountsList:e.details,tokenData:e.token_data})})).fail((function(e){var t=q(e);t||(t=v.wizard.googleAd.errors.notFetched),n("adsenseData",{phase:"error",error:t})}))})).fail((function(e){var t=q(e);t||(t=v.wizard.googleAd.errors.notAuthorized),n("adsenseData",{phase:"error",error:t})})),(0,s.jsx)(te,{})):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("h1",{className:"!mt-0",children:v.wizard.googleAd.stepHeading}),(0,s.jsxs)("div",{className:"mt-8 flex gap-x-8 justify-start items-center font-medium",children:[(0,s.jsx)("a",{className:"button button-hero !text-base !px-3 !py-4",href:advancedAds.wizard.newAccountLink,target:"_blank",rel:"noopener noreferrer",children:v.wizard.googleAd.btnSignup}),(0,s.jsx)("a",{className:"button-onboarding !text-base !px-3 !py-4",href:Y(),children:v.wizard.googleAd.btnConnect})]}),(0,s.jsx)(ee,{isEnabled:!1,enableText:v.wizard.googleAd.footerProcessText,disableText:v.wizard.googleAd.footerDisableText})]})}var o=function(){switch(r.googleAdsPlacement){case"auto_ads":return v.wizard.googleAd.footerEnableText.autoAds;case"manual":return v.wizard.googleAd.footerEnableText.manual;default:return v.wizard.googleAd.footerDisableText}}();return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("h1",{className:"!mt-0 !text-gray-300",children:v.wizard.googleAd.stepHeading}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("p",{children:[v.wizard.googleAd.labelConnected," ",r.adsenseData.account.id,(0,s.jsx)("br",{}),v.wizard.googleAd.labelAccount," ",r.adsenseData.account.name]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"auto_ads"===r.googleAdsPlacement?"!text-gray-300":null,children:v.wizard.googleAd.labelAdsPlacement}),(0,s.jsx)(z,{id:"google_ads_placement",options:v.wizard.googleAd.adsPlacement,value:r.googleAdsPlacement,onChange:function(e){return n("googleAdsPlacement",e)}})]}),r.googleAdsPlacement&&"auto_ads"===r.googleAdsPlacement&&(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{children:v.wizard.googleAd.labelAutoAds}),(0,s.jsx)(J,{id:"auto_ads",options:v.wizard.googleAd.autoAdsOptions,value:r.autoAdsOptions,onChange:function(e){var t=ne(r.autoAdsOptions),o=t.indexOf(e);o>-1?t.splice(o,1):t.push(e),n("autoAdsOptions",t)}})]})]}),(0,s.jsx)(ee,{isEnabled:r.googleAdsPlacement,enableText:o,disableText:v.wizard.googleAd.footerDisableText})]})}function ie(e){var t=e.options,r=e.setOptions,o=null,a=function(e){if(e.preventDefault(),o)return o.uploader.uploader.param("post_id",0),void o.open();(o=wp.media.frames.file_frame=wp.media({title:v.wizard.bannerAd.mediaFrameTitle,button:{text:v.wizard.bannerAd.mediaFrameButton},multiple:!1})).on("select",(function(){var e=o.state().get("selection").first().toJSON();r("adImage",e)})),o.open()};return(0,n.useEffect)((function(){if(t.adImage){var e=t.adImage.url.split("/").pop(),n=document.createElement("p");n.style.visibility="hidden",n.style.whiteSpace="nowrap",n.style.position="absolute",n.innerText=e,document.body.appendChild(n);var o=n.offsetWidth>250;document.body.removeChild(n),r("isTextLong",o)}}),[t.adImage]),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("h1",{className:"!mt-0 ".concat(t.adImage?"text-gray-300":""),children:v.wizard.stepTitles.adImage}),t.adImage?(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-5",children:[(0,s.jsx)("button",{className:"button-onboarding button-onboarding--gray",onClick:a,children:v.wizard.bannerAd.mediaBtnReplace}),(0,s.jsx)("div",{className:"file-name-rtl m-0 ".concat(t.isTextLong?"truncate":""),children:(0,s.jsx)("p",{children:t.adImage.url.split("/").pop()})}),(0,s.jsx)("span",{className:"dashicons dashicons-yes-alt flex items-center justify-center text-4xl w-9 h-9 text-primary"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{children:v.wizard.bannerAd.stepHeading}),(0,s.jsx)("input",{type:"url",name:"ad_image_url",id:"ad_image_url",className:"advads-input-text",placeholder:v.wizard.bannerAd.inputPlaceholder,onChange:function(e){return r("adImageUrl",e.target.value)}})]})]}):(0,s.jsx)("button",{className:"button-onboarding",onClick:a,children:v.wizard.bannerAd.mediaBtnUpload}),(0,s.jsx)(ee,{isEnabled:t.adImage,enableText:v.wizard.bannerAd.footerEnableText,disableText:v.wizard.bannerAd.footerDisableText,onNext:function(){}})]})}function ce(e){var t=e.options,r=e.setOptions;return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("h1",{className:"!mt-0 ".concat(t.adCode?"text-gray-300":""),children:v.wizard.stepTitles.adCode}),(0,s.jsx)("div",{className:"space-y-4",children:(0,s.jsx)("div",{children:(0,s.jsx)("textarea",{name:"ad_code_code",id:"ad_code_code",className:"w-full p-4 text-base",rows:6,placeholder:v.wizard.codeAd.inputPlaceholder,onChange:function(e){return r("adCode",e.target.value)}})})}),(0,s.jsx)(ee,{isEnabled:t.adCode,enableText:v.wizard.codeAd.footerEnableText,disableText:v.wizard.codeAd.footerDisableText,onNext:function(){}})]})}const se=wp.apiFetch;var le=r.n(se);function ue(e){return ue="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ue(e)}function fe(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function de(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?fe(Object(r),!0).forEach((function(t){pe(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):fe(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function pe(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=ue(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=ue(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==ue(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function he(e){return(0,s.jsx)("svg",de(de({xmlns:"http://www.w3.org/2000/svg",width:18,height:20,fill:"#0074a2",overflow:"visible",viewBox:"-0.191 0 18 20"},e),{},{children:(0,s.jsx)("path",{d:"M8.809 20c-.192 0-.384-.049-.556-.149L.556 15.408A1.11 1.11 0 0 1 0 14.446v-8.89a1.11 1.11 0 0 1 .555-.962L8.252.15a1.11 1.11 0 0 1 1.111 0l1.076.62a1.11 1.11 0 0 1 .407 1.518 1.11 1.11 0 0 1-1.517.407l-.52-.299-6.587 3.802v7.606l6.587 3.802 6.587-3.802V9.747a1.111 1.111 0 1 1 2.221 0v4.699a1.11 1.11 0 0 1-.555.962l-7.698 4.443c-.172.1-.364.149-.555.149zm-.002-6.111a1.65 1.65 0 0 1-.906-.269l-3.293-2.138a1.665 1.665 0 1 1 1.815-2.795l1.922 1.247L14.571.733a1.666 1.666 0 0 1 2.314-.446 1.67 1.67 0 0 1 .446 2.314l-7.143 10.556a1.663 1.663 0 0 1-1.381.732z"})}))}function me(e){return me="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},me(e)}function ye(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function be(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ye(Object(r),!0).forEach((function(t){ve(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ye(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function ve(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=me(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=me(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==me(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ge(e){return(0,s.jsxs)("svg",be(be({xmlns:"http://www.w3.org/2000/svg",overflow:"visible",viewBox:"-0.227 -0.137 93 116"},e),{},{children:[(0,s.jsx)("style",{children:".B{fill:#cc3000}.C{fill:#ff6c40}.D{fill:#8c2100}.E{fill:#47456d}.F{fill:#1b193a}.G{fill:#2f2c54}.H{fill:#d9d9d9}"}),(0,s.jsx)("g",{fill:"#0089bf",children:(0,s.jsx)("path",{d:"M65.194 115.726c-.363 0-.704-.089-1.007-.264l-4.926-2.843-.004-.002-4.924-2.842-5.454-3.149c-.69-.398-1.086-1.202-1.085-2.207 0-.859.283-1.821.796-2.709l10.007-17.334c1.153-1.994 3.082-2.959 4.4-2.202l5.346 3.087.103.055 4.93 2.848 4.93 2.85c.477.273.816.738.979 1.341.274 1.018.019 2.351-.684 3.567L68.586 113.26a5.94 5.94 0 0 1-1.383 1.65 4.543 4.543 0 0 1-.445.318l-.003.001c-.3.187-.608.325-.916.407-.22.061-.437.09-.645.09zm-43.6-50.869-5.146-2.971v-4.784l.002-.056a.803.803 0 0 0-.209-.54c-.105-.136-.193-.229-.347-.244l-5.513-3.183.001-.962a7.19 7.19 0 0 0 1.985-.757c1.241-.717 1.924-1.718 1.924-2.819 0-1.537-1.655-2.846-2.604-3.406v-2.138l.77-2.401a2 2 0 0 0 .098-.616c0-.822-.516-1.591-1.417-2.11-1.55-.896-4.156-.896-5.709 0-.9.52-1.417 1.289-1.417 2.11a1.99 1.99 0 0 0 .098.616l.77 2.401v2.138c-.949.56-2.604 1.87-2.604 3.406 0 1.102.684 2.103 1.925 2.819.579.334 1.254.59 1.986.757l.001.962-5.666 3.272H.521c-.023.013-.045.028-.067.043H.453l-.013.009a.805.805 0 0 0-.264.351l-.004.012-.007.021-.002.006-.001.002-.003.009a.84.84 0 0 0-.035.21v.001l-.003 8.523a.8.8 0 0 0 .4.693l7.364 4.251 2.759 1.595a.795.795 0 0 0 .799.001l10.152-5.847a.8.8 0 0 0 .4-.693.8.8 0 0 0-.401-.681zm70.553 14.172-7.365-4.252a.84.84 0 0 0-.095-.047c-1.603-.957-2.432-2.393-3.309-3.912l-1.346-2.142 7.264-4.19a.8.8 0 0 0 0-1.386L76.59 56.919v-4.161l8.285-4.784a.8.8 0 0 0 .293-1.093L76.715 32.24l8.454-14.64a.8.8 0 0 0-.293-1.093L60.327 2.333a.8.8 0 0 0-1.093.293L57.45 5.715c-1.523-1.608-3.174-2.942-4.878-3.926C49.035-.253 45.67-.558 43.094.931L34.92 5.65l-.008.004-.07.041a6.998 6.998 0 0 0-2.257 2.165l-14.976 8.646a.8.8 0 0 0-.293 1.093l8.454 14.642-8.453 14.641a.8.8 0 0 0 .293 1.093l8.282 4.782V60.594a.8.8 0 0 0 .4.686l7.394 4.269-8.606 4.975c-1.432.828-2.007.509-2.862.016l-.023-.014c-.945-.546-2.243-1.294-4.482 0-2.027 1.17-3.017 2.885-3.974 4.543-.879 1.522-1.71 2.961-3.318 3.917a.896.896 0 0 0-.082.041L.4 84.766a.8.8 0 0 0 .4 1.493h6.564v3.453a.8.8 0 0 0 1.2.693l9.858-5.692a.937.937 0 0 0 .09-.044l9.544-5.51c.017.299.085.589.201.865l-1.92 1.108-.042.023a.797.797 0 0 0-.388.556.521.521 0 0 0-.012.299v8.342a.8.8 0 0 0 .4.693l7.364 4.251 2.759 1.595a.795.795 0 0 0 .799.001l10.152-5.847a.8.8 0 0 0 0-1.386l-5.146-2.971V85.24l8.976 5.183.003.001c.892.514 2.105-.045 2.829-1.295s.597-2.583-.292-3.098l-12.274-7.085a1.486 1.486 0 0 0-1.114-.142l-.286-.165V76.18c0-1.135-.746-2.14-1.952-2.832l4.535-2.622 8.19 4.729.023.013.016.006 8.28 4.778a.8.8 0 0 0 .8 0l8.179-4.718 8.453 4.88a.854.854 0 0 0 .134.062l7.256 4.19a.8.8 0 0 0 1.2-.693V80.52h6.564a.8.8 0 0 0 .773-.593.796.796 0 0 0-.369-.898zM36.644 5.783l-.003.001.003-.001"})}),(0,s.jsx)("path",{fill:"#992400",d:"M54.731 105.257 64.74 87.923c.912-1.581 2.394-2.436 3.307-1.91s.912 2.238 0 3.819l-10.012 17.34c-.915 1.583-2.396 2.437-3.308 1.907s-.913-2.233.004-3.822z"}),(0,s.jsx)("path",{d:"M68.046 89.833c-.913 1.582-2.394 2.437-3.306 1.908s-.913-2.24 0-3.823 2.393-2.435 3.306-1.908.912 2.242 0 3.823z",className:"B"}),(0,s.jsx)("path",{fill:"#992400",d:"m62.964 110.014 5.007-8.667-3.306-1.91-5.01 8.67c-.912 1.581-.909 3.288 0 3.817.916.528 2.397-.327 3.309-1.91z"}),(0,s.jsx)("path",{fill:"#e54717",d:"m69.669 90.766-5.006 8.669 3.306 1.908 5.004-8.664c.907-1.582.912-3.293 0-3.824s-2.391.329-3.304 1.911z"}),(0,s.jsx)("path",{d:"M67.958 101.364c-.912 1.583-2.396 2.441-3.303 1.909-.918-.524-.92-2.232-.003-3.818.909-1.579 2.396-2.438 3.306-1.908.915.527.914 2.236 0 3.817z",className:"B"}),(0,s.jsx)("path",{fill:"#e54717",d:"m64.587 110.954 10.008-17.341c.915-1.582 2.396-2.434 3.308-1.907s.916 2.234.003 3.815L67.893 112.86c-.913 1.583-2.394 2.437-3.306 1.909s-.914-2.235 0-3.815z"}),(0,s.jsx)("path",{d:"M66.243 114.604c-1.293.746-2.341.142-2.341-1.35s1.048-3.309 2.341-4.055c1.285-.739 2.336-.138 2.336 1.357s-1.051 3.306-2.336 4.048z",className:"B"}),(0,s.jsx)("path",{fill:"#4c1200",d:"M59.656 111.923c-.909-.529-.912-2.236 0-3.817l-1.621-.934c-.915 1.583-2.396 2.437-3.308 1.907l4.929 2.844z"}),(0,s.jsx)("path",{fill:"#801e00",d:"m63.042 98.499-5.008 8.674 1.621.934 5.01-8.67-1.623-.938z"}),(0,s.jsx)("path",{fill:"#b22a00",d:"m69.669 90.766-5.006 8.669-1.621-.937 5.004-8.666 1.623.934z"}),(0,s.jsx)("path",{fill:"#ff9d80",d:"M72.973 88.856c-.911-.527-2.392.328-3.304 1.91l-1.623-.934c.912-1.581.912-3.295 0-3.823l4.927 2.847z"}),(0,s.jsx)("path",{fill:"#fcbc05",d:"m64.587 110.954 10.008-17.341"}),(0,s.jsx)("path",{fill:"#ff9d80",d:"M72.973 88.856c.912.531.907 2.242 0 3.824l1.622.933c.915-1.582 2.396-2.434 3.308-1.907l-4.93-2.85z"}),(0,s.jsx)("path",{fill:"#b22a00",d:"m64.587 110.954 10.008-17.341-1.622-.933-10.009 17.334 1.623.94z"}),(0,s.jsx)("path",{fill:"#661800",d:"M64.587 114.77c-.914-.527-.914-2.235 0-3.816l-1.623-.94c-.912 1.583-2.394 2.438-3.309 1.91l4.932 2.846z"}),(0,s.jsx)("path",{fill:"#ff6333",d:"M54.731 105.257 64.74 87.923l-5.452-3.149-10.008 17.335 5.451 3.148z"}),(0,s.jsx)("path",{fill:"#4c1200",d:"M49.28 102.109c-.918 1.589-.918 3.296-.004 3.823l5.451 3.147c-.913-.526-.913-2.233.004-3.822l-5.451-3.148z"}),(0,s.jsx)("path",{fill:"#ff8059",d:"M64.74 87.923c.912-1.581 2.394-2.436 3.307-1.91l-5.451-3.147c-.914-.526-2.396.328-3.308 1.908l5.452 3.149z"}),(0,s.jsxs)("g",{fill:"#005273",children:[(0,s.jsx)("path",{d:"m51.25 74.764 8.312 4.797 27.334-15.768-11.097-6.406v3.203zm-18.409.707-14.729 8.504-7.365-4.252 14.729-8.504z"}),(0,s.jsx)("path",{d:"m62.264 71.219 14.729 8.504 7.365-4.252-14.729-8.504z"})]}),(0,s.jsx)("path",{d:"m51.243 18.068 24.548 14.173-.001 28.346L51.24 74.76 26.692 60.588l.003-28.346z",className:"F"}),(0,s.jsx)("path",{d:"M75.791 32.241 51.243 18.068l-.001 28.347z",className:"E"}),(0,s.jsxs)("g",{className:"G",children:[(0,s.jsx)("path",{d:"M26.692 32.242v28.346L51.24 74.76l.002-28.345zm57.785-15.043-8.684 15.042-24.549-14.174 8.684-15.042z"}),(0,s.jsx)("path",{d:"m42.558 3.026 8.684 15.042-24.549 14.174L18.009 17.2zm17.366 58.431L51.24 46.415l24.549-14.174 8.684 15.042z"})]}),(0,s.jsx)("path",{d:"m18.009 47.284 8.684-15.042 24.549 14.174-8.684 15.042z",className:"E"}),(0,s.jsx)("path",{d:"M.8 85.459h7.365l-.001 4.252 9.936-5.737-7.363-4.252z",className:"C"}),(0,s.jsx)("path",{d:"m42.65 69.801-7.364-4.253-9.81 5.671 7.365 4.252z",className:"B"}),(0,s.jsx)("path",{d:"M91.747 79.722h-7.364v4.253l-7.363-4.253 7.362-4.252z",className:"C"}),(0,s.jsxs)("g",{className:"D",children:[(0,s.jsx)("path",{d:"m59.834 69.801 7.364-4.253 2.446 1.419-7.365 4.252z"}),(0,s.jsx)("path",{d:"M69.64 66.967c1.839 1.062 2.76.532 3.679.002l-7.365 4.252c-.919.53-1.84 1.06-3.679-.002l7.365-4.252z"})]}),(0,s.jsx)("path",{d:"M84.369 75.471c-1.842-1.063-2.763-2.659-3.684-4.253L73.32 75.47c.921 1.595 1.842 3.19 3.684 4.253l7.365-4.252z",className:"B"}),(0,s.jsx)("path",{d:"M80.685 71.217c-.92-1.594-1.84-3.188-3.681-4.25l-7.365 4.252c1.841 1.062 2.761 2.656 3.681 4.25l7.365-4.252z",className:"D"}),(0,s.jsx)("path",{d:"M77.004 66.967c-1.843-1.064-2.764-.53-3.686.002l-7.365 4.252c.922-.532 1.843-1.066 3.686-.002l7.365-4.252zm-51.528 4.252c-1.839 1.062-2.76.532-3.679.002l7.365 4.252c.919.53 1.84 1.06 3.679-.002l-7.365-4.252zm-14.729 8.504c1.842-1.063 2.763-2.659 3.684-4.253l7.365 4.252c-.921 1.595-1.842 3.19-3.684 4.253l-7.365-4.252z",className:"B"}),(0,s.jsx)("path",{d:"M14.431 75.469c.92-1.594 1.84-3.188 3.681-4.25l7.365 4.252c-1.841 1.062-2.761 2.656-3.681 4.25l-7.365-4.252z",className:"D"}),(0,s.jsx)("path",{d:"M18.111 71.219c1.843-1.064 2.764-.53 3.686.002l7.365 4.252c-.922-.532-1.843-1.066-3.686-.002l-7.365-4.252z",className:"C"}),(0,s.jsx)("path",{d:"M35.313 6.347c-2.221 1.284-3.595 4.034-3.595 7.947 0 7.828 5.491 17.343 12.272 21.258 3.388 1.956 6.456 2.14 8.678.858l8.182-4.725c2.222-1.283 3.595-4.034 3.595-7.947s-1.373-8.248-3.593-12.093-5.29-7.206-8.679-9.163-6.458-2.141-8.678-.858l-8.182 4.723z",className:"D"}),(0,s.jsx)("path",{d:"M43.99 35.551c6.777 3.912 12.272.738 12.273-7.09S50.768 11.118 43.991 7.206s-12.272-.739-12.273 7.087 5.491 17.343 12.272 21.258z",className:"B"}),(0,s.jsx)("path",{d:"M52.668 16.366c2.221 3.847 3.595 8.183 3.595 12.095s-1.374 6.665-3.595 7.949l8.182-4.725c2.222-1.283 3.595-4.034 3.595-7.947s-1.373-8.248-3.593-12.093l-8.184 4.721z",className:"D"}),(0,s.jsx)("path",{d:"M60.852 11.645c-2.221-3.848-5.29-7.206-8.679-9.163s-6.458-2.141-8.678-.858l-8.182 4.723c2.221-1.283 5.289-1.098 8.678.859s6.456 5.314 8.677 9.16l8.184-4.721z",className:"C"}),(0,s.jsx)("path",{fill:"#f2f2f2",d:"m44.931 13.326.934.539-.922 5.912 2.855 1.645.042.025a1.04 1.04 0 0 1 .464.806c.001.128-.104.282-.104.282l-5.132 6.92-.949-.551.946-5.912-2.872-1.653-.045-.027c-.257-.147-.467-.509-.466-.805 0-.121.041-.205.099-.259l5.15-6.922z"}),(0,s.jsx)("path",{d:"m45.258 13.515-4.658 6.26-.818.472 5.149-6.921z",className:"H"}),(0,s.jsx)("path",{fill:"#bfbfbf",d:"M40.6 19.775a.34.34 0 0 0-.1.259 1.03 1.03 0 0 0 .466.805l-.818.472c-.257-.147-.467-.509-.466-.805 0-.121.041-.205.099-.259l.819-.472z"}),(0,s.jsx)("path",{fill:"#a6a6a6",d:"m40.967 20.839 2.916 1.681-.817.472-2.918-1.68z"}),(0,s.jsx)("path",{d:"m43.883 22.52-.945 5.911-.818.473.946-5.912z",className:"H"}),(0,s.jsx)("path",{fill:"#a6a6a6",d:"m42.938 28.431.622.362-.492.662-.948-.551z"}),(0,s.jsx)("path",{d:"m15.649 57.046-7.364-4.252L.92 57.046l7.364 4.252z",className:"E"}),(0,s.jsx)("path",{d:"M.92 57.046.917 65.55l7.365 4.251.001-8.503z",className:"G"}),(0,s.jsx)("path",{d:"m8.284 69.802 7.364-4.252-.001-8.502-7.363 4.25z",className:"F"}),(0,s.jsx)("path",{fill:"#8c8c8c",d:"M9.578 54.292c-.03.165-.155.325-.374.451-.254.146-.587.22-.92.221v2.333a.62.62 0 0 0 .306-.074.269.269 0 0 0 .115-.118l.873-2.813z"}),(0,s.jsx)("g",{fill:"#b2b2b2",children:(0,s.jsx)("path",{d:"M8.284 54.963c.333 0 .666-.074.92-.221.219-.126.344-.286.374-.451l.007-5.75c0 .192-.126.385-.381.532s-.586.22-.918.22l-.002 5.67zM6.99 54.292c.03.165.155.325.374.451.254.146.587.22.92.221v2.333a.62.62 0 0 1-.306-.074.269.269 0 0 1-.115-.118l-.873-2.813z"})}),(0,s.jsx)("path",{d:"M8.284 54.963c-.333 0-.666-.074-.92-.221-.219-.126-.344-.286-.374-.451l-.007-5.75c0 .192.126.385.381.532s.586.22.918.22l.002 5.67z",className:"H"}),(0,s.jsxs)("g",{className:"D",children:[(0,s.jsx)("path",{d:"M8.283 44.376c-.719 0-1.37-.168-1.841-.44s-.763-.648-.762-1.063l-.808-2.52c.131.404.473.766.957 1.045.628.363 1.496.587 2.455.587v2.391z"}),(0,s.jsx)("path",{d:"M8.283 41.985c.959 0 1.827-.224 2.455-.587.483-.279.825-.641.957-1.045l-.808 2.52c0 .415-.292.791-.762 1.063s-1.122.44-1.841.44v-2.391z"})]}),(0,s.jsx)("path",{d:"M8.283 41.985c-.959 0-1.827-.224-2.455-.587-.483-.279-.825-.641-.957-1.045a1.18 1.18 0 0 1-.06-.372c0-.554.389-1.055 1.017-1.417s1.497-.587 2.455-.587 1.827.224 2.455.587 1.017.864 1.017 1.417c0 .127-.021.251-.06.372-.131.404-.473.766-.957 1.045-.628.363-1.495.587-2.455.587z",className:"C"}),(0,s.jsx)("path",{d:"M8.283 44.376c-.719 0-1.37-.168-1.841-.44s-.763-.648-.762-1.063v2.749c0 .415.292.791.762 1.063s1.123.44 1.841.44v-2.749z",className:"B"}),(0,s.jsx)("path",{d:"M8.283 44.376c.719 0 1.37-.168 1.841-.44s.763-.648.762-1.063v2.749c0 .415-.292.791-.762 1.063s-1.122.44-1.841.44v-2.749z",className:"D"}),(0,s.jsx)("path",{d:"M8.283 51.548c1.439 0 2.74-.336 3.682-.88s1.525-1.296 1.525-2.126c0-1.417-2.188-2.786-2.604-2.92 0 .415-.292.791-.762 1.063s-1.122.44-1.841.44v4.423z",className:"B"}),(0,s.jsx)("path",{d:"M8.283 47.125c-.719 0-1.37-.168-1.841-.44s-.763-.648-.762-1.063c-.416.135-2.604 1.503-2.604 2.92 0 .831.583 1.583 1.525 2.126s2.245.88 3.682.88v-4.423z",className:"C"}),(0,s.jsx)("path",{d:"m41.426 81.846-7.364-4.252-7.364 4.252 7.363 4.252z",className:"E"}),(0,s.jsx)("path",{d:"M26.695 81.846v8.504l7.364 4.251.001-8.503z",className:"G"}),(0,s.jsx)("path",{d:"m34.061 94.602 7.364-4.252-.001-8.502-7.363 4.25z",className:"F"}),(0,s.jsx)("g",{className:"C",children:(0,s.jsx)("path",{d:"M36.578 82.317c.072.072.231.097.356.055s.167-.134.096-.206-.231-.096-.356-.055-.168.135-.096.206zm.186.342c.013.083.14.144.283.137s.249-.08.235-.163-.138-.145-.281-.137-.249.08-.237.163zm.286.432c.037-.058-.015-.124-.116-.145s-.214.009-.25.067.015.124.116.145.214-.009.25-.067zm-.89-1.037c.117.048.28.03.362-.037s.054-.162-.063-.21-.28-.031-.363.038-.054.16.064.209zm-.196-.29c.031-.102-.088-.2-.265-.218s-.346.05-.376.152.087.2.263.218.347-.05.378-.152zM36.353 82.392c-.324-.323-1.04-.435-1.601-.248-.421.141-.635.408-.577.67.002.017 0 .025-.003.042-.029.249-.405.436-.835.418a1.29 1.29 0 0 0-.563.05c-.372.125-.5.4-.285.616s.694.29 1.067.165l2.368-.789c.561-.186.752-.6.429-.924zm-1.161-3.75-1.367 1.367c-.216.215-.088.491.286.616s.852.051 1.067-.165c.102-.103.128-.218.085-.325-.029-.249.295-.466.726-.482l.073-.002c.413.03.827-.075 1.082-.276l-1.626-.939a.978.978 0 0 0-.326.206zm-5.634 3.875c-.125-.042-.168-.134-.096-.206s.231-.097.355-.055.168.133.096.205-.231.097-.355.056zm-.592-.108c-.143-.008-.249-.081-.236-.163s.139-.144.281-.137.25.08.237.163-.139.144-.282.137zm-.749-.165c.102-.022.215.008.251.067s-.016.123-.117.145-.213-.009-.25-.068.016-.123.116-.144zm1.798.514c-.083-.068-.054-.162.063-.209s.28-.031.362.037.055.162-.063.209-.28.031-.362-.037zM30.516 82.871c.177-.018.346.051.377.153s-.087.2-.264.217-.346-.05-.378-.152.088-.2.265-.218zm-.66.7c.324-.324.133-.737-.429-.924a1.905 1.905 0 0 0-1.274.041l1.626.939c.024-.02.055-.035.077-.056z"})}),(0,s.jsx)("g",{className:"B",children:(0,s.jsx)("path",{d:"M32.044 88.206c.098.087.2.078.226-.021s-.032-.249-.13-.335-.199-.078-.226.019.033.25.13.337zm.39.458c.077.121.193.176.259.124s.056-.192-.022-.312-.194-.177-.26-.124-.055.191.023.312zm.517.565c-.032-.098-.114-.18-.184-.183s-.099.075-.066.173.114.181.183.184.099-.076.067-.174zm-1.344-1.298c.1.037.167-.035.149-.161s-.113-.258-.214-.296-.166.034-.149.16.113.259.214.297zm-.348-.378c-.073-.163-.218-.274-.322-.251s-.129.175-.056.338.217.275.32.25.131-.175.058-.337zM31.997 88.382c-.441-.391-.896-.351-1.015.092-.089.331.037.794.291 *************.***************.2.381.175.771-.056.868-.112.018-.2.098-.238.236-.078.295.096.746.391 1.008s.598.233.677-.061l.501-1.868c.118-.44-.145-1.117-.587-1.51zm-3.829-5.291.5 2.446c.079.385.382.763.677.842s.47-.171.391-.556a1.292 1.292 0 0 0-.238-.512c-.229-.364-.256-.783-.055-.933.014-.011.02-.017.035-.023.232-.075.349-.352.302-.729l-1.628-.938c-.017.118-.015.253.016.403z"})}),(0,s.jsx)("ellipse",{cx:52.472,cy:88.225,className:"D",rx:1.003,ry:1.737,transform:"rotate(30.014 52.472 88.218)"}),(0,s.jsx)("path",{d:"M38.357 76.536a1.47 1.47 0 0 0 .043-.357c0-1.385-1.943-2.507-4.341-2.507s-4.34 1.121-4.339 2.505c0 .***************.359.296-1.219 2.106-2.154 4.298-2.156s4.003.938 4.298 2.156z",className:"B"}),(0,s.jsx)("path",{fill:"#33a1cc",d:"M38.358 78.662c.028-.116.042-.236.042-.358 0-1.384-1.943-2.506-4.341-2.506s-4.339 1.121-4.339 2.506a1.52 1.52 0 0 0 .041.357c.296-1.217 2.107-2.153 4.298-2.154s4.003.937 4.299 2.155z"}),(0,s.jsx)("path",{fill:"#66b8d9",d:"M29.761 76.536c.296-1.219 2.106-2.154 4.298-2.156s4.003.938 4.298 2.156l.043 1.768c0-1.384-1.943-2.506-4.341-2.506s-4.339 1.121-4.339 2.506l.041-1.768z"}),(0,s.jsx)("path",{d:"M28.852 76.178c0 1.66 2.331 3.006 5.207 3.007s5.207-1.347 5.208-3.007-2.331-3.007-5.208-3.007-5.207 1.346-5.207 3.007zm5.207-2.506c2.397 0 4.341 1.122 4.341 2.507a1.48 1.48 0 0 1-.044.357c-.302 1.214-2.11 2.147-4.297 2.147s-3.994-.935-4.295-2.147a1.54 1.54 0 0 1-.044-.359c-.001-1.384 1.943-2.504 4.339-2.505z",className:"C"}),(0,s.jsx)("path",{d:"M34.059 82.019c-2.876 0-5.207-1.346-5.207-3.006v-2.835c0 1.66 2.331 3.006 5.207 3.007v2.834z",className:"B"}),(0,s.jsx)("path",{d:"M34.059 79.185c2.877 0 5.207-1.347 5.208-3.007v2.835c-.001 1.66-2.331 3.006-5.208 3.006v-2.834z",className:"D"}),(0,s.jsx)("path",{d:"M37.093 79.347c-.359.622-.359 1.295 0 1.504l2.455 1.417c-.359-.208-.359-.881 0-1.504l-2.455-1.417z",className:"B"}),(0,s.jsx)("path",{d:"M39.548 80.764c.359-.622.942-.959 1.302-.751l-2.455-1.418c-.359-.207-.942.129-1.302.752l2.455 1.417z",className:"C"}),(0,s.jsx)("path",{fill:"#6c6a8a",d:"M37.638 77.597c-.781-.657-2.093-1.09-3.579-1.09s-2.798.432-3.579 1.09c.783.656 2.095 1.087 3.579 1.087s2.796-.431 3.579-1.087z"}),(0,s.jsx)("path",{d:"M39.33 80.638c.48-.83 1.257-1.279 1.736-1.002l12.274 7.086c-.479-.276-1.256.172-1.736 1.002L39.33 80.638z",className:"C"}),(0,s.jsx)("path",{d:"M51.604 87.724c-.479.831-.479 1.729 0 2.006L39.33 82.643c-.479-.277-.479-1.175 0-2.005l12.274 7.086z",className:"B"}),(0,s.jsx)("path",{fill:"#005273",d:"m8.282 69.801 2.76 1.595 10.152-5.846-5.548-3.203v3.203zm25.777 24.8 2.76 1.596 10.152-5.847-5.548-3.203v3.203z"})]}))}function je(e){return je="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},je(e)}function xe(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function we(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=je(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=je(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==je(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Oe(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,a,i,c=[],s=!0,l=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=a.call(r)).done)&&(c.push(n.value),c.length!==t);s=!0);}catch(e){l=!0,o=e}finally{try{if(!s&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(l)throw o}}return c}}(e,t)||function(e,t){if(e){if("string"==typeof e)return Se(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Se(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Se(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function ze(e){var t=e.title,r=void 0===t?"":t,n=e.text,o=e.icon,a=void 0!==o&&o;return(0,s.jsxs)("div",{className:"flex w-full gap-x-3 items-center",children:[a&&(0,s.jsx)("div",{className:"mt-2",children:(0,s.jsx)(he,{})}),(0,s.jsxs)("div",{className:"grow ".concat(a?"":"ml-7"),children:[(0,s.jsx)("strong",{children:r})," ",n]})]})}function _e(e){var t=e.email,r=e.setEmail;return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("h1",{className:"!mt-0",children:v.wizard.newsletter.title}),(0,s.jsx)("div",{className:"advads-admin-notice",children:(0,s.jsxs)("div",{className:"advads-notice-box_wrapper flex gap-7 items-center",children:[(0,s.jsx)("input",{type:"email",id:"newsletter_email",className:"advads-input-text",placeholder:v.wizard.newsletter.inputPlaceholder,value:t,onChange:function(e){return r(e.target.value)},style:{minWidth:"65%",maxWidth:"65%"}}),(0,s.jsx)("div",{children:(0,s.jsx)("button",{className:"button-onboarding advads-notices-button-subscribe","data-notice":"nl_free_addons",children:v.wizard.newsletter.btnLabel})})]})})]})}function Ne(){return(0,s.jsx)("a",{href:"#",className:"button-onboarding button-onboarding--gray disabled",children:v.wizard.loading})}function Pe(e,t){var r,n;return r=v.wizard.stepTitles.congrats.default,n=v.wizard.congrats.stepHeading,"google_adsense"===e.taskOption&&(r="auto_ads"===e.googleAdsPlacement?v.wizard.stepTitles.congrats.adsenseAuto:v.wizard.stepTitles.congrats.adsenseManual,n="auto_ads"===e.googleAdsPlacement?v.wizard.congrats.adsenseAuto.stepHeading:v.wizard.congrats.adsenseManual.stepHeading),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("h1",{className:"!mt-0",children:r}),(0,s.jsxs)("div",{className:"congrats-flex",children:[(0,s.jsx)("p",{className:"text-justify min-w m-0",dangerouslySetInnerHTML:{__html:n}}),Ee(e,t)]}),Ae(e,t)]})}function Ee(e,t){if("google_adsense"===e.taskOption&&null!=t&&t.success){if("auto_ads"===e.googleAdsPlacement&&t.adsenseAccount)return(0,s.jsx)("div",{children:(0,s.jsx)("a",{href:t.adsenseAccount,className:"button-onboarding button-onboarding--gray",target:"_blank",rel:"noreferrer",children:v.wizard.congrats.adsenseAuto.btnAccount})});if("manual"===e.googleAdsPlacement&&t.itemEditLink)return(0,s.jsx)("div",{children:(0,s.jsx)("a",{href:t.itemEditLink,className:"button-onboarding !bg-red-600 !border-red-700 !text-white",children:v.wizard.congrats.adsenseManual.btnEditItem})})}return null!=t&&t.success&&t.itemEditLink?(0,s.jsx)("div",{children:(0,s.jsx)("a",{href:t.itemEditLink,className:"button-onboarding button-onboarding--gray",children:v.wizard.congrats.btnEditItem})}):null}function Ae(e,t){return"google_adsense"===e.taskOption&&"auto_ads"===e.googleAdsPlacement?null:"google_adsense"===e.taskOption&&"manual"===e.googleAdsPlacement?(0,s.jsx)("div",{className:"congrats-flex mt-7",children:(0,s.jsx)("p",{className:"m-0",dangerouslySetInnerHTML:{__html:v.wizard.congrats.adsenseManual.liveHeading}})}):(0,s.jsxs)("div",{className:"congrats-flex mt-7",children:[(0,s.jsx)("p",{className:"m-0",dangerouslySetInnerHTML:{__html:v.wizard.congrats.liveHeading}}),(0,s.jsx)("div",{className:"mr-3",children:t&&t.success&&""!==t.postLink?(0,s.jsx)("a",{href:t.postLink,className:"button-onboarding",children:v.wizard.congrats.btnLiveAd}):(0,s.jsx)(Ne,{})})]})}function Le(e){var t=e.options,r=Oe((0,n.useState)(null),2),o=r[0],a=r[1],i=Oe((0,n.useState)(""),2),c=i[0],l=i[1];return null===o&&le()({path:"/advanced-ads/v1/onboarding",method:"POST",data:t}).then((function(e){a(e)})),""===c&&le()({path:"/advanced-ads/v1/user-email",method:"GET"}).then((function(e){l(e)})),(0,s.jsxs)(s.Fragment,{children:[Pe(t,o),(0,s.jsx)(Z,{}),(0,s.jsx)(_e,{email:c,setEmail:l}),(0,s.jsx)(Z,{}),(0,s.jsx)("h1",{className:"!mt-0",children:v.wizard.congrats.upgradeHeading}),(0,s.jsxs)("div",{className:"congrats-flex items-center gap-x-12",children:[(0,s.jsx)("p",{className:"text-justify m-0",children:v.wizard.congrats.upgradeText}),(0,s.jsx)("a",{href:"https://wpadvancedads.com/add-ons/all-access/?utm_source=advanced-ads&utm_medium=link&utm_campaign=wizard-upgrade",className:"button-onboarding !bg-red-600 !border-red-700 !text-white text-center",target:"_blank",rel:"noreferrer",children:v.wizard.congrats.btnUpgrade})]}),(0,s.jsxs)("div",{className:"flex gap-x-12 items-center",children:[(0,s.jsx)("div",{className:"space-y-2 mt-4 text-lg tracking-wide grow",children:v.wizard.congrats.upgradePoints.map((function(e,t){return(0,s.jsx)(ze,function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?xe(Object(r),!0).forEach((function(t){we(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):xe(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}({},e),"point-".concat(t))}))}),(0,s.jsx)("div",{children:(0,s.jsx)(ge,{className:"w-40"})})]}),(0,s.jsx)(Z,{}),(0,s.jsx)("div",{className:"text-right",children:(0,s.jsx)("a",{href:y("admin.php?page=advanced-ads"),className:"button-onboarding button-onboarding--gray",children:v.wizard.congrats.btnDashboard})})]})}function ke(e){return ke="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ke(e)}function Me(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function De(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Me(Object(r),!0).forEach((function(t){Te(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Me(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Te(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=ke(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=ke(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==ke(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Ce(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,a,i,c=[],s=!0,l=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=a.call(r)).done)&&(c.push(n.value),c.length!==t);s=!0);}catch(e){l=!0,o=e}finally{try{if(!s&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(l)throw o}}return c}}(e,t)||function(e,t){if(e){if("string"==typeof e)return Ie(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Ie(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ie(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var Re=!1;function Fe(){var e=Ce((0,n.useState)(De({},function(){var e={startIndex:1,taskOption:"google_adsense",googleAdsPlacement:"manual",autoAdsOptions:[],adsenseData:!1},t=new URLSearchParams(document.location.search),r=advancedAds.wizard.adsenseData;if("adsense"===t.get("route")&&"advanced-ads-onboarding"===t.get("page")&&(e.startIndex=2,e.taskOption="google_adsense"),"image"===t.get("route")&&"advanced-ads-onboarding"===t.get("page")&&(e.startIndex=2,e.taskOption="ad_image"),!Array.isArray(r.accounts)){var n=r["adsense-id"];e.adsenseData={account:{id:n,name:r.accounts[n].details.name}}}return Re||(r.amp&&e.autoAdsOptions.push("enableAmp"),r["page-level-enabled"]&&e.autoAdsOptions.push("enable"),Re=!0),e}())),2),t=e[0],r=e[1],o=function(e,n){var o=De(De({},t),{},Te({},e,n));r(o)};return(0,s.jsx)("div",{className:"advads-onboarding advads-onboarding-frame",children:(0,s.jsx)("div",{className:"absolute top-0 max-w-3xl w-full py-20",children:(0,s.jsxs)(m,{header:(0,s.jsx)(b,{}),footer:(0,s.jsx)(g,{}),startIndex:t.startIndex,children:[(0,s.jsx)(j,{children:(0,s.jsx)(B,{options:t,setOptions:o})}),"google_adsense"===t.taskOption&&(0,s.jsx)(j,{className:"pb-4",children:(0,s.jsx)(ae,{options:t,setOptions:o})}),"ad_image"===t.taskOption&&(0,s.jsx)(j,{className:"pb-4",children:(0,s.jsx)(ie,{options:t,setOptions:o})}),"ad_code"===t.taskOption&&(0,s.jsx)(j,{className:"pb-4",children:(0,s.jsx)(ce,{options:t,setOptions:o})}),(0,s.jsx)(j,{children:(0,s.jsx)(Le,{options:t,setOptions:o})})]})})})}t()((function(){var e=document.getElementById("advads-onboarding-wizard"),t=(0,n.createElement)(Fe);n.createRoot?(0,n.createRoot)(e).render(t):(0,n.render)(t,e)}))})()})();