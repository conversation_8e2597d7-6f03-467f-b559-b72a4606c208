(()=>{"use strict";var e={n:a=>{var t=a&&a.__esModule?()=>a.default:()=>a;return e.d(t,{a:t}),t},d:(a,t)=>{for(var n in t)e.o(t,n)&&!e.o(a,n)&&Object.defineProperty(a,n,{enumerable:!0,get:t[n]})},o:(e,a)=>Object.prototype.hasOwnProperty.call(e,a)};const a=jQuery;var t=e.n(a);const n='<span class="spinner advads-ad-parameters-spinner advads-spinner"></span>';var r=t()("#advanced-ad-type"),d=t()("#advanced-ads-ad-parameters"),o=t()("#advanced-ads-tinymce-wrapper");function i(){var e=t()("#advanced-ad-type input"),a=t()("#ad-types-box h2"),i=a.text();e.on("change",(function(){AdvancedAdsAdmin.AdImporter.onChangedAdType();var c=e.filter(":checked"),s=c.next("label").text();a.html(i+": "+s),function(e){r.addClass("is-list-disabled"),d.html(n),o.hide();var a=!0;t().ajax({type:"POST",url:advancedAds.endpoints.ajaxUrl,data:{action:"load_ad_parameters_metabox",ad_type:e,ad_id:t()("#post_ID").val(),nonce:advadsglobal.ajax_nonce}}).done((function(t){t&&(d.html(t).trigger("paramloaded"),advads_maybe_textarea_to_tinymce(e),a&&(a=!1,setTimeout((function(){advancedAds.termination.resetInitialValues()}),500)))})).fail((function(e,a,t){d.html(t)})).always((function(){r.removeClass("is-list-disabled")}))}(c.val())})),e.eq(0).trigger("change"),t()('input[name="advanced_ad[type]"]').on("change",(function(){var e="adsense"===t()(this).val();t()(".advads-notice-adsense").toggleClass("block-important",e).toggleClass("!hidden",!e)}))}function c(e){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},c(e)}function s(e,a){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);a&&(n=n.filter((function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable}))),t.push.apply(t,n)}return t}function l(e){for(var a=1;a<arguments.length;a++){var t=null!=arguments[a]?arguments[a]:{};a%2?s(Object(t),!0).forEach((function(a){p(e,a,t[a])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):s(Object(t)).forEach((function(a){Object.defineProperty(e,a,Object.getOwnPropertyDescriptor(t,a))}))}return e}function p(e,a,t){return(a=function(e){var a=function(e,a){if("object"!=c(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,a||"default");if("object"!=c(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===a?String:Number)(e)}(e,"string");return"symbol"==c(a)?a:a+""}(a))in e?Object.defineProperty(e,a,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[a]=t,e}function u(){var e=t()("#advads-ad-injection-box"),a=t()("#post_ID").val();t()(document).on("click",".advads-ad-injection-button",(function(){var n=t()(this).data("placement-type"),r=t()(this).data("placement-id"),d={};(n||r)&&(d=function(e,a){if("post_content"===e){var t=prompt(advadstxt.after_paragraph_promt,1);if(null!==t)return l(l({},a),{},{index:parseInt(t,10)})}return a}(n,d),function(e,a,n,r){var d=t()("#advads-ad-injection-box");t().ajax({type:"POST",url:ajaxurl,data:{action:"advads-ad-injection-content",placement_type:e,placement_id:a,ad_id:n,options:r,nonce:advadsglobal.ajax_nonce},success:function(a){a?(t()("#advads-ad-injection-box *").hide(),t()("#advads-ad-injection-message-placement-created, #advads-ad-injection-message-placement-created *").show(),"server"===e&&t()(".hide-server-placement").hide()):d.html("an error occured")},error:function(e,a,t){d.html(t)}})}(n,r,a,d),e.find(".advads-loader").show(),e.find(".advads-ad-injection-box-placements").hide(),t()("body").animate({scrollTop:e.offset().top-40},1,"linear"))}))}t()((function(){var e,a;i(),u(),e=null,a=function(){var a=e?e.codemirror.getValue():jQuery("#advads-ad-content-plain").val(),t=jQuery("#advads-parameters-php-warning"),n=jQuery("#advads-allow-php-warning");t.hide(),n.hide(),jQuery("#advads-parameters-php").prop("checked")&&(/<\?(?:php|=)/.test(a)?n.show():t.show()),jQuery("#advads-parameters-shortcodes-warning").toggle(jQuery("#advads-parameters-shortcodes").prop("checked")&&!/\[[^\]]+\]/.test(a))},jQuery(document).on("keyup","#advads-ad-content-plain",a),jQuery(document).on("click","#advads-parameters-php,#advads-parameters-shortcodes",a),jQuery(document).on("paramloaded","#advanced-ads-ad-parameters",(function(){var t;try{t=window.advancedAds.admin.codeMirror.settings}catch(a){return void(e=null)}if("plain"===jQuery('input[name="advanced_ad[type]"]:checked').val()){var n=jQuery("#advads-ad-content-plain");n.length?((e=wp.codeEditor.initialize(n,t)).codemirror.on("keyup",a),window.advancedAds.admin.codeMirror=e.codemirror,window.advancedAds=window.advancedAds||{},window.advancedAds.admin.getSourceCode=function(){return e?e.codemirror.getValue():jQuery("#advads-ad-content-plain").val()},window.advancedAds.admin.setSourceCode=function(a){e?e.codemirror.setValue(a):jQuery("#advads-ad-content-plain").val(a)}):e=null}else e=null}))}))})();