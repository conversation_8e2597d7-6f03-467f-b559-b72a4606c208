(()=>{"use strict";var e={n:r=>{var a=r&&r.__esModule?()=>r.default:()=>r;return e.d(a,{a}),a},d:(r,a)=>{for(var n in a)e.o(a,n)&&!e.o(r,n)&&Object.defineProperty(r,n,{enumerable:!0,get:a[n]})},o:(e,r)=>Object.prototype.hasOwnProperty.call(e,r)};const r=jQuery;var a=e.n(r);a()((function(){var e=a()(".advanced-ads-adsense-dashboard");e.find(".report-need-refresh").length&&(e.html('<p style="text-align:center;"><span class="report-need-refresh spinner advads-ad-parameters-spinner advads-spinner"></span></p>'),a().ajax({type:"POST",url:ajaxurl,data:{nonce:Advanced_Ads_Adsense_Report_Helper.nonce,type:"domain",filter:"",action:"advads_adsense_report_refresh"},success:function(r){r.success&&r.data&&r.data.html&&e.html(r.data.html)},error:function(e,r,a){console.log("Refreshing report error: "+a)}}))}))})();