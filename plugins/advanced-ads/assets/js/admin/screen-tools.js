(()=>{"use strict";var e={n:n=>{var a=n&&n.__esModule?()=>n.default:()=>n;return e.d(a,{a}),a},d:(n,a)=>{for(var i in a)e.o(a,i)&&!e.o(n,i)&&Object.defineProperty(n,i,{enumerable:!0,get:a[i]})},o:(e,n)=>Object.prototype.hasOwnProperty.call(e,n)};const n=jQuery;var a=e.n(n);function i(){a()(document).on("submit","#alternative-version",(function(e){var n,i,o;e.preventDefault(),n=a()(this).serialize(),i=a()("#plugin-version,#install-version").prop("disabled",!0),o=a()("#install-version").siblings(".spinner").css("visibility","visible"),a().ajax({url:ajaxurl,type:"POST",data:{action:"advads_install_alternate_version",vars:n}}).done((function(e){e.data.redirect?document.location.href=e.data.redirect:(i.prop("disabled",!1),o.css("visibility","hidden"))})).fail((function(e){console.error(e),i.prop("disabled",!1),o.css("visibility","hidden")}))}));var e=a()("#plugin-version");e.length&&(e.val()||a().ajax({url:ajaxurl,type:"POST",data:{action:"advads_get_usable_versions",nonce:a()("#version-control-nonce").val()}}).done((function(e){var n=[],i=a()("#plugin-version");for(var o in e.data.order){var t=e.data.order[o],r=0===o?" selected":"";n.push('<option value="'.concat(t+"|"+e.data.versions[t],'"').concat(r,">").concat(t,"</option>"))}i.prop("disabled",!1).html(n.join("\n")),a()("#install-version").prop("disabled",!1)})).fail((function(e){console.error(e)})))}a()((function(){a()(".advads_import_type").on("change",(function(){"xml_content"===this.value?(a()("#advads_xml_file").hide(),a()("#advads_xml_content").show()):(a()("#advads_xml_file").show(),a()("#advads_xml_content").hide())})),i()}))})();