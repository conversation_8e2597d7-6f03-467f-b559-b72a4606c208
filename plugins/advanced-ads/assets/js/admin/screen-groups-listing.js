(()=>{"use strict";var a={n:t=>{var d=t&&t.__esModule?()=>t.default:()=>t;return a.d(d,{a:d}),d},d:(t,d)=>{for(var o in d)a.o(d,o)&&!a.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:d[o]})},o:(a,t)=>Object.prototype.hasOwnProperty.call(a,t)};const t=jQuery;var d=a.n(t);var o=d();function e(){var a=o(this).closest(".advads-ad-group-form"),t=a.find(".advads-group-add-ad-list-ads option:selected"),d=a.find(".advads-group-add-ad-list-weights").last(),e=a.find(".advads-group-ads tbody"),n=t.val().match(/\d+/g),s="";n&&(s=advancedAds.endpoints.editAd+n.pop());var i=t.data("status"),r=t.data("status-string");t.length&&d.length&&!e.find('[name="'+t.val()+'"]').length&&e.append(o("<tr></tr>").append(o("<td></td>").html('<a target="_blank" href="'.concat(s,'">').concat(t.text(),"</a>")),o("<td></td>").html('<span class="advads-help advads-help-no-icon advads-ad-status-icon advads-ad-status-icon-'.concat(i,'">\n\t\t\t\t\t\t<span class="advads-tooltip">').concat(r,"</span>\n\t\t\t\t\t</span>")),o("<td></td>").append(d.clone().removeClass().val(d.val()).prop("name",t.val())),'<td><button type="button" class="advads-remove-ad-from-group button">x</button></td>'))}function n(){o(this).closest("tr").remove()}function s(a){a.each((function(){var a=d()(this);a.closest(".advads-ad-group-form").find(".advads-option:not(.static)").hide();var t=a.val();a.parents(".advads-ad-group-form").find(".advads-group-type-"+t).show()}))}const i=wp.apiFetch;var r=a.n(i);function c(a,t){void 0===t&&(t=!0),a.useableInputs||(a.useableInputs=d()(a).closest("dialog").find("select,input,textarea,button,a.button").not(":disabled")),a.useableInputs.prop("disabled",t)}function u(a){var t=d()(a).serialize();c(a),r()({path:"/advanced-ads/v1/group",method:"PUT",data:{fields:t}}).then((function(t){if(t.error)return c(a,!1),a.closest("dialog").close(),void window.advancedAds.notifications.addError(t.error);var d=a.closest("dialog");if(d.advadsTermination.resetInitialValues(),t.reload)return localStorage.setItem("advadsUpdateMessage",JSON.stringify({type:"success",message:window.advadstxt.group_forms.updated})),void window.location.reload();window.advancedAds.notifications.addSuccess(window.advadstxt.group_forms.updated),d.close()}))}function l(a){var t=d()(a).serialize();c(a),r()({path:"/advanced-ads/v1/group",method:"POST",data:{fields:t}}).then((function(t){if(t.error)return c(a,!1),a.closest("dialog").close(),void window.advancedAds.notifications.addError(t.error);a.closest("dialog").advadsTermination.resetInitialValues(),document.location.href="#modal-group-edit-".concat(t.group_data.id),localStorage.setItem("advadsUpdateMessage",JSON.stringify({type:"success",message:window.advadstxt.group_forms.save_new})),document.location.reload()}))}d()((function(){var a;a=d()("#advads-group-filter"),d()("#advads-show-filters").on("click",(function(){return a.toggle()})),d()("#advads-reset-filters").length&&a.show(),o(".advads-group-add-ad button").on("click",e),o("#advads-ad-group-list").on("click",".advads-remove-ad-from-group",n),o(".advads-ad-group-type input").on("click",(function(){s(o(this))})),s(o(".advads-ad-group-type input:checked")),wp.hooks.addFilter("advanced-ads-submit-modal-form","advancedAds",(function(a,t){return"advads-group-new-form"===t.id?(l(t),!1):a})),wp.hooks.addFilter("advanced-ads-submit-modal-form","advancedAds",(function(a,t){return"update-group"===t.name?(u(t),!1):a})),d()('[id^="modal-group-edit-"]').each((function(){d()(this).find(".tablenav.bottom").html('<button class="button button-primary submit-edit-group">'.concat(window.advadstxt.group_forms.save,"</button>"))})),d()("#modal-group-new").find(".tablenav.bottom").html('<button class="button button-primary" id="submit-new-group">'.concat(window.advadstxt.group_forms.save_new,"</button>")),d()(document).on("click",".submit-edit-group",(function(){u(d()(this).closest("dialog").find("form")[0])})),d()(document).on("click","#submit-new-group",(function(){var a=d()("#advads-group-new-form"),t=a.closest("dialog")[0].closeValidation;window[t.function](t.modal_id)&&l(a[0])})),d()(document).on("click","#advads-ad-group-list .delete-tag",(function(a){if(a.preventDefault(),confirm(window.advadstxt.group_forms.confirmation.replace("%s",d()(this).closest("div").siblings(".advads-table-name").find("a").text()))){var t=new URLSearchParams(d()(this).attr("href")),o=d()(this).closest("tr");r()({path:"/advanced-ads/v1/group",method:"DELETE",data:{id:t.get("group_id"),nonce:t.get("_wpnonce")}}).then((function(a){a.done&&(o.remove(),window.advancedAds.notifications.addSuccess(window.advadstxt.group_forms.deleted))}))}}))}))})();