(()=>{"use strict";var e={n:t=>{var a=t&&t.__esModule?()=>t.default:()=>t;return e.d(a,{a}),a},d:(t,a)=>{for(var n in a)e.o(a,n)&&!e.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:a[n]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t)};const t=jQuery;var a=e.n(t);function n(){var e=document.getElementById("advads_overview_addons");e&&e.addEventListener("click",(function(e){var t=e.target;if("a"===t.tagName.toLowerCase()&&t.classList.contains("disabled"))e.preventDefault();else if(t.classList.contains("button")&&t.classList.contains("subscribe")&&(e.preventDefault(),t.disabled=!0,t.classList.add("disabled"),wp.ajax.post("advads_newsletter",{nonce:t.dataset.nonce}).done((function(e){e&&(t.closest(".item-details").querySelector(".description").innerHTML='<p style="font-weight: 600;">'.concat(e,"</p>"))})).fail((function(e){try{t.closest(".item-details").querySelector(".description").innerHTML='<p style="font-weight: 600;">'.concat(e.responseJSON.data.message,"</p>")}catch(e){}}))),t.classList.contains("button")&&t.classList.contains("installed")){var a=t.href?t.href:"";if(-1!==a.indexOf("#activate-aaplugin_")){e.preventDefault();var n=a.split("_");!function(e,t,a,n){n.classList.add("disabled");var s=e.substring(e.indexOf("/")+1,e.indexOf("."));wp.ajax.post("advads_activate_addon",{_ajax_nonce:a,plugin:e,slug:s,name:t}).done((function(){n.className="button active disabled",n.innerText=window.advadstxt.active;var e=document.createElement("i");e.className="dashicons",e.style.cssText='content:"\\f147"',n.insertBefore(e,n.firstChild)})).fail((function(e){void 0!==e.errorMessage&&(n.closest(".cta").parentNode.querySelector(".description").innerText=e.errorMessage)}))}(n[2],decodeURIComponent(n[3]),n[1],t)}}}))}function s(e,t,n){var s=arguments.length>3&&void 0!==arguments[3]&&arguments[3],i=a()('<div class="notice notice-'.concat(t,' is-dismissible" />'));i.html('<div class="py-3">'+e+"</div>"),n.after(i),s&&setTimeout((function(){i.fadeOut(500,(function(){i.remove()}))}),3e3),a()(document).trigger("wp-notice-added")}a()((function(){a()(document).on("click","#dismiss-welcome i",(function(){a().ajax(window.ajaxurl,{method:"POST",data:{action:"advads_dismiss_welcome"},success:function(){a()("#welcome").remove()}})})),a()("#advads-overview").on("click",".notice-dismiss",(function(e){e.preventDefault();var t=a()(this).parent();t.fadeOut(500,(function(){t.remove()}))})),n(),a()(document).on("click",".js-btn-backup-adstxt",(function(){var e=a()(this),t=e.closest(".notice");e.prop("disabled",!0),e.html(e.data("loading")),a().ajax({url:advancedAds.endpoints.ajaxUrl,method:"POST",data:{action:"pubguru_backup_ads_txt",security:e.data("security")}}).always((function(){e.prop("disabled",!1),e.html(e.data("text"))})).done((function(e){e.success?(s(e.data,"success",t,!0),t.remove()):(s(e.data,"error",t),t.remove())}))}))}))})();