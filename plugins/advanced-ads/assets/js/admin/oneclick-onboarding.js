/*! For license information please see oneclick-onboarding.js.LICENSE.txt */
(()=>{var e={20:(e,t,n)=>{"use strict";var r=n(540),o=Symbol.for("react.element"),i=Symbol.for("react.fragment"),s=Object.prototype.hasOwnProperty,a=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,l={key:!0,ref:!0,__self:!0,__source:!0};function c(e,t,n){var r,i={},c=null,u=null;for(r in void 0!==n&&(c=""+n),void 0!==t.key&&(c=""+t.key),void 0!==t.ref&&(u=t.ref),t)s.call(t,r)&&!l.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===i[r]&&(i[r]=t[r]);return{$$typeof:o,type:e,key:c,ref:u,props:i,_owner:a.current}}t.Fragment=i,t.jsx=c,t.jsxs=c},287:(e,t)=>{"use strict";var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),s=Symbol.for("react.profiler"),a=Symbol.for("react.provider"),l=Symbol.for("react.context"),c=Symbol.for("react.forward_ref"),u=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),p=Symbol.for("react.lazy"),h=Symbol.iterator;var f={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},m=Object.assign,g={};function y(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||f}function v(){}function b(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||f}y.prototype.isReactComponent={},y.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},y.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},v.prototype=y.prototype;var _=b.prototype=new v;_.constructor=b,m(_,y.prototype),_.isPureReactComponent=!0;var x=Array.isArray,w=Object.prototype.hasOwnProperty,j={current:null},S={key:!0,ref:!0,__self:!0,__source:!0};function $(e,t,r){var o,i={},s=null,a=null;if(null!=t)for(o in void 0!==t.ref&&(a=t.ref),void 0!==t.key&&(s=""+t.key),t)w.call(t,o)&&!S.hasOwnProperty(o)&&(i[o]=t[o]);var l=arguments.length-2;if(1===l)i.children=r;else if(1<l){for(var c=Array(l),u=0;u<l;u++)c[u]=arguments[u+2];i.children=c}if(e&&e.defaultProps)for(o in l=e.defaultProps)void 0===i[o]&&(i[o]=l[o]);return{$$typeof:n,type:e,key:s,ref:a,props:i,_owner:j.current}}function O(e){return"object"==typeof e&&null!==e&&e.$$typeof===n}var A=/\/+/g;function C(e,t){return"object"==typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,(function(e){return t[e]}))}(""+e.key):t.toString(36)}function E(e,t,o,i,s){var a=typeof e;"undefined"!==a&&"boolean"!==a||(e=null);var l=!1;if(null===e)l=!0;else switch(a){case"string":case"number":l=!0;break;case"object":switch(e.$$typeof){case n:case r:l=!0}}if(l)return s=s(l=e),e=""===i?"."+C(l,0):i,x(s)?(o="",null!=e&&(o=e.replace(A,"$&/")+"/"),E(s,t,o,"",(function(e){return e}))):null!=s&&(O(s)&&(s=function(e,t){return{$$typeof:n,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(s,o+(!s.key||l&&l.key===s.key?"":(""+s.key).replace(A,"$&/")+"/")+e)),t.push(s)),1;if(l=0,i=""===i?".":i+":",x(e))for(var c=0;c<e.length;c++){var u=i+C(a=e[c],c);l+=E(a,t,o,u,s)}else if(u=function(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=h&&e[h]||e["@@iterator"])?e:null}(e),"function"==typeof u)for(e=u.call(e),c=0;!(a=e.next()).done;)l+=E(a=a.value,t,o,u=i+C(a,c++),s);else if("object"===a)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return l}function D(e,t,n){if(null==e)return e;var r=[],o=0;return E(e,r,"","",(function(e){return t.call(n,e,o++)})),r}function k(e){if(-1===e._status){var t=e._result;(t=t()).then((function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)}),(function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)})),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var T={current:null},P={transition:null},N={ReactCurrentDispatcher:T,ReactCurrentBatchConfig:P,ReactCurrentOwner:j};function L(){throw Error("act(...) is not supported in production builds of React.")}t.Children={map:D,forEach:function(e,t,n){D(e,(function(){t.apply(this,arguments)}),n)},count:function(e){var t=0;return D(e,(function(){t++})),t},toArray:function(e){return D(e,(function(e){return e}))||[]},only:function(e){if(!O(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=y,t.Fragment=o,t.Profiler=s,t.PureComponent=b,t.StrictMode=i,t.Suspense=u,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=N,t.act=L,t.cloneElement=function(e,t,r){if(null==e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var o=m({},e.props),i=e.key,s=e.ref,a=e._owner;if(null!=t){if(void 0!==t.ref&&(s=t.ref,a=j.current),void 0!==t.key&&(i=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(c in t)w.call(t,c)&&!S.hasOwnProperty(c)&&(o[c]=void 0===t[c]&&void 0!==l?l[c]:t[c])}var c=arguments.length-2;if(1===c)o.children=r;else if(1<c){l=Array(c);for(var u=0;u<c;u++)l[u]=arguments[u+2];o.children=l}return{$$typeof:n,type:e.type,key:i,ref:s,props:o,_owner:a}},t.createContext=function(e){return(e={$$typeof:l,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:a,_context:e},e.Consumer=e},t.createElement=$,t.createFactory=function(e){var t=$.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:c,render:e}},t.isValidElement=O,t.lazy=function(e){return{$$typeof:p,_payload:{_status:-1,_result:e},_init:k}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=P.transition;P.transition={};try{e()}finally{P.transition=t}},t.unstable_act=L,t.useCallback=function(e,t){return T.current.useCallback(e,t)},t.useContext=function(e){return T.current.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e){return T.current.useDeferredValue(e)},t.useEffect=function(e,t){return T.current.useEffect(e,t)},t.useId=function(){return T.current.useId()},t.useImperativeHandle=function(e,t,n){return T.current.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return T.current.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return T.current.useLayoutEffect(e,t)},t.useMemo=function(e,t){return T.current.useMemo(e,t)},t.useReducer=function(e,t,n){return T.current.useReducer(e,t,n)},t.useRef=function(e){return T.current.useRef(e)},t.useState=function(e){return T.current.useState(e)},t.useSyncExternalStore=function(e,t,n){return T.current.useSyncExternalStore(e,t,n)},t.useTransition=function(){return T.current.useTransition()},t.version="18.3.1"},458:(e,t,n)=>{var r,o,i;o=[n(669)],void 0===(i="function"==typeof(r=function(e){var t=function(){if(e&&e.fn&&e.fn.select2&&e.fn.select2.amd)var t=e.fn.select2.amd;var n,r,o;return t&&t.requirejs||(t?r=t:t={},function(e){var t,i,s,a,l={},c={},u={},d={},p=Object.prototype.hasOwnProperty,h=[].slice,f=/\.js$/;function m(e,t){return p.call(e,t)}function g(e,t){var n,r,o,i,s,a,l,c,d,p,h,m=t&&t.split("/"),g=u.map,y=g&&g["*"]||{};if(e){for(s=(e=e.split("/")).length-1,u.nodeIdCompat&&f.test(e[s])&&(e[s]=e[s].replace(f,"")),"."===e[0].charAt(0)&&m&&(e=m.slice(0,m.length-1).concat(e)),d=0;d<e.length;d++)if("."===(h=e[d]))e.splice(d,1),d-=1;else if(".."===h){if(0===d||1===d&&".."===e[2]||".."===e[d-1])continue;d>0&&(e.splice(d-1,2),d-=2)}e=e.join("/")}if((m||y)&&g){for(d=(n=e.split("/")).length;d>0;d-=1){if(r=n.slice(0,d).join("/"),m)for(p=m.length;p>0;p-=1)if((o=g[m.slice(0,p).join("/")])&&(o=o[r])){i=o,a=d;break}if(i)break;!l&&y&&y[r]&&(l=y[r],c=d)}!i&&l&&(i=l,a=c),i&&(n.splice(0,a,i),e=n.join("/"))}return e}function y(t,n){return function(){var r=h.call(arguments,0);return"string"!=typeof r[0]&&1===r.length&&r.push(null),i.apply(e,r.concat([t,n]))}}function v(e){return function(t){return g(t,e)}}function b(e){return function(t){l[e]=t}}function _(n){if(m(c,n)){var r=c[n];delete c[n],d[n]=!0,t.apply(e,r)}if(!m(l,n)&&!m(d,n))throw new Error("No "+n);return l[n]}function x(e){var t,n=e?e.indexOf("!"):-1;return n>-1&&(t=e.substring(0,n),e=e.substring(n+1,e.length)),[t,e]}function w(e){return e?x(e):[]}function j(e){return function(){return u&&u.config&&u.config[e]||{}}}s=function(e,t){var n,r=x(e),o=r[0],i=t[1];return e=r[1],o&&(n=_(o=g(o,i))),o?e=n&&n.normalize?n.normalize(e,v(i)):g(e,i):(o=(r=x(e=g(e,i)))[0],e=r[1],o&&(n=_(o))),{f:o?o+"!"+e:e,n:e,pr:o,p:n}},a={require:function(e){return y(e)},exports:function(e){var t=l[e];return void 0!==t?t:l[e]={}},module:function(e){return{id:e,uri:"",exports:l[e],config:j(e)}}},t=function(t,n,r,o){var i,u,p,h,f,g,v,x=[],j=typeof r;if(g=w(o=o||t),"undefined"===j||"function"===j){for(n=!n.length&&r.length?["require","exports","module"]:n,f=0;f<n.length;f+=1)if("require"===(u=(h=s(n[f],g)).f))x[f]=a.require(t);else if("exports"===u)x[f]=a.exports(t),v=!0;else if("module"===u)i=x[f]=a.module(t);else if(m(l,u)||m(c,u)||m(d,u))x[f]=_(u);else{if(!h.p)throw new Error(t+" missing "+u);h.p.load(h.n,y(o,!0),b(u),{}),x[f]=l[u]}p=r?r.apply(l[t],x):void 0,t&&(i&&i.exports!==e&&i.exports!==l[t]?l[t]=i.exports:p===e&&v||(l[t]=p))}else t&&(l[t]=r)},n=r=i=function(n,r,o,l,c){if("string"==typeof n)return a[n]?a[n](r):_(s(n,w(r)).f);if(!n.splice){if((u=n).deps&&i(u.deps,u.callback),!r)return;r.splice?(n=r,r=o,o=null):n=e}return r=r||function(){},"function"==typeof o&&(o=l,l=c),l?t(e,n,r,o):setTimeout((function(){t(e,n,r,o)}),4),i},i.config=function(e){return i(e)},n._defined=l,(o=function(e,t,n){if("string"!=typeof e)throw new Error("See almond README: incorrect module build, no module name");t.splice||(n=t,t=[]),m(l,e)||m(c,e)||(c[e]=[e,t,n])}).amd={jQuery:!0}}(),t.requirejs=n,t.require=r,t.define=o),t.define("almond",(function(){})),t.define("jquery",[],(function(){var t=e||$;return null==t&&console&&console.error&&console.error("Select2: An instance of jQuery or a jQuery-compatible library was not found. Make sure that you are including jQuery before Select2 on your web page."),t})),t.define("select2/utils",["jquery"],(function(e){var t={};function n(e){var t=e.prototype,n=[];for(var r in t)"function"==typeof t[r]&&"constructor"!==r&&n.push(r);return n}t.Extend=function(e,t){var n={}.hasOwnProperty;function r(){this.constructor=e}for(var o in t)n.call(t,o)&&(e[o]=t[o]);return r.prototype=t.prototype,e.prototype=new r,e.__super__=t.prototype,e},t.Decorate=function(e,t){var r=n(t),o=n(e);function i(){var n=Array.prototype.unshift,r=t.prototype.constructor.length,o=e.prototype.constructor;r>0&&(n.call(arguments,e.prototype.constructor),o=t.prototype.constructor),o.apply(this,arguments)}function s(){this.constructor=i}t.displayName=e.displayName,i.prototype=new s;for(var a=0;a<o.length;a++){var l=o[a];i.prototype[l]=e.prototype[l]}for(var c=function(e){var n=function(){};e in i.prototype&&(n=i.prototype[e]);var r=t.prototype[e];return function(){return Array.prototype.unshift.call(arguments,n),r.apply(this,arguments)}},u=0;u<r.length;u++){var d=r[u];i.prototype[d]=c(d)}return i};var r=function(){this.listeners={}};r.prototype.on=function(e,t){this.listeners=this.listeners||{},e in this.listeners?this.listeners[e].push(t):this.listeners[e]=[t]},r.prototype.trigger=function(e){var t=Array.prototype.slice,n=t.call(arguments,1);this.listeners=this.listeners||{},null==n&&(n=[]),0===n.length&&n.push({}),n[0]._type=e,e in this.listeners&&this.invoke(this.listeners[e],t.call(arguments,1)),"*"in this.listeners&&this.invoke(this.listeners["*"],arguments)},r.prototype.invoke=function(e,t){for(var n=0,r=e.length;n<r;n++)e[n].apply(this,t)},t.Observable=r,t.generateChars=function(e){for(var t="",n=0;n<e;n++)t+=Math.floor(36*Math.random()).toString(36);return t},t.bind=function(e,t){return function(){e.apply(t,arguments)}},t._convertData=function(e){for(var t in e){var n=t.split("-"),r=e;if(1!==n.length){for(var o=0;o<n.length;o++){var i=n[o];(i=i.substring(0,1).toLowerCase()+i.substring(1))in r||(r[i]={}),o==n.length-1&&(r[i]=e[t]),r=r[i]}delete e[t]}}return e},t.hasScroll=function(t,n){var r=e(n),o=n.style.overflowX,i=n.style.overflowY;return(o!==i||"hidden"!==i&&"visible"!==i)&&("scroll"===o||"scroll"===i||r.innerHeight()<n.scrollHeight||r.innerWidth()<n.scrollWidth)},t.escapeMarkup=function(e){var t={"\\":"&#92;","&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#47;"};return"string"!=typeof e?e:String(e).replace(/[&<>"'\/\\]/g,(function(e){return t[e]}))},t.__cache={};var o=0;return t.GetUniqueElementId=function(e){var n=e.getAttribute("data-select2-id");return null!=n||(n=e.id?"select2-data-"+e.id:"select2-data-"+(++o).toString()+"-"+t.generateChars(4),e.setAttribute("data-select2-id",n)),n},t.StoreData=function(e,n,r){var o=t.GetUniqueElementId(e);t.__cache[o]||(t.__cache[o]={}),t.__cache[o][n]=r},t.GetData=function(n,r){var o=t.GetUniqueElementId(n);return r?t.__cache[o]&&null!=t.__cache[o][r]?t.__cache[o][r]:e(n).data(r):t.__cache[o]},t.RemoveData=function(e){var n=t.GetUniqueElementId(e);null!=t.__cache[n]&&delete t.__cache[n],e.removeAttribute("data-select2-id")},t.copyNonInternalCssClasses=function(e,t){var n=e.getAttribute("class").trim().split(/\s+/);n=n.filter((function(e){return 0===e.indexOf("select2-")}));var r=t.getAttribute("class").trim().split(/\s+/);r=r.filter((function(e){return 0!==e.indexOf("select2-")}));var o=n.concat(r);e.setAttribute("class",o.join(" "))},t})),t.define("select2/results",["jquery","./utils"],(function(e,t){function n(e,t,r){this.$element=e,this.data=r,this.options=t,n.__super__.constructor.call(this)}return t.Extend(n,t.Observable),n.prototype.render=function(){var t=e('<ul class="select2-results__options" role="listbox"></ul>');return this.options.get("multiple")&&t.attr("aria-multiselectable","true"),this.$results=t,t},n.prototype.clear=function(){this.$results.empty()},n.prototype.displayMessage=function(t){var n=this.options.get("escapeMarkup");this.clear(),this.hideLoading();var r=e('<li role="alert" aria-live="assertive" class="select2-results__option"></li>'),o=this.options.get("translations").get(t.message);r.append(n(o(t.args))),r[0].className+=" select2-results__message",this.$results.append(r)},n.prototype.hideMessages=function(){this.$results.find(".select2-results__message").remove()},n.prototype.append=function(e){this.hideLoading();var t=[];if(null!=e.results&&0!==e.results.length){e.results=this.sort(e.results);for(var n=0;n<e.results.length;n++){var r=e.results[n],o=this.option(r);t.push(o)}this.$results.append(t)}else 0===this.$results.children().length&&this.trigger("results:message",{message:"noResults"})},n.prototype.position=function(e,t){t.find(".select2-results").append(e)},n.prototype.sort=function(e){return this.options.get("sorter")(e)},n.prototype.highlightFirstItem=function(){var e=this.$results.find(".select2-results__option--selectable"),t=e.filter(".select2-results__option--selected");t.length>0?t.first().trigger("mouseenter"):e.first().trigger("mouseenter"),this.ensureHighlightVisible()},n.prototype.setClasses=function(){var n=this;this.data.current((function(r){var o=r.map((function(e){return e.id.toString()}));n.$results.find(".select2-results__option--selectable").each((function(){var n=e(this),r=t.GetData(this,"data"),i=""+r.id;null!=r.element&&r.element.selected||null==r.element&&o.indexOf(i)>-1?(this.classList.add("select2-results__option--selected"),n.attr("aria-selected","true")):(this.classList.remove("select2-results__option--selected"),n.attr("aria-selected","false"))}))}))},n.prototype.showLoading=function(e){this.hideLoading();var t={disabled:!0,loading:!0,text:this.options.get("translations").get("searching")(e)},n=this.option(t);n.className+=" loading-results",this.$results.prepend(n)},n.prototype.hideLoading=function(){this.$results.find(".loading-results").remove()},n.prototype.option=function(n){var r=document.createElement("li");r.classList.add("select2-results__option"),r.classList.add("select2-results__option--selectable");var o={role:"option"},i=window.Element.prototype.matches||window.Element.prototype.msMatchesSelector||window.Element.prototype.webkitMatchesSelector;for(var s in(null!=n.element&&i.call(n.element,":disabled")||null==n.element&&n.disabled)&&(o["aria-disabled"]="true",r.classList.remove("select2-results__option--selectable"),r.classList.add("select2-results__option--disabled")),null==n.id&&r.classList.remove("select2-results__option--selectable"),null!=n._resultId&&(r.id=n._resultId),n.title&&(r.title=n.title),n.children&&(o.role="group",o["aria-label"]=n.text,r.classList.remove("select2-results__option--selectable"),r.classList.add("select2-results__option--group")),o){var a=o[s];r.setAttribute(s,a)}if(n.children){var l=e(r),c=document.createElement("strong");c.className="select2-results__group",this.template(n,c);for(var u=[],d=0;d<n.children.length;d++){var p=n.children[d],h=this.option(p);u.push(h)}var f=e("<ul></ul>",{class:"select2-results__options select2-results__options--nested",role:"none"});f.append(u),l.append(c),l.append(f)}else this.template(n,r);return t.StoreData(r,"data",n),r},n.prototype.bind=function(n,r){var o=this,i=n.id+"-results";this.$results.attr("id",i),n.on("results:all",(function(e){o.clear(),o.append(e.data),n.isOpen()&&(o.setClasses(),o.highlightFirstItem())})),n.on("results:append",(function(e){o.append(e.data),n.isOpen()&&o.setClasses()})),n.on("query",(function(e){o.hideMessages(),o.showLoading(e)})),n.on("select",(function(){n.isOpen()&&(o.setClasses(),o.options.get("scrollAfterSelect")&&o.highlightFirstItem())})),n.on("unselect",(function(){n.isOpen()&&(o.setClasses(),o.options.get("scrollAfterSelect")&&o.highlightFirstItem())})),n.on("open",(function(){o.$results.attr("aria-expanded","true"),o.$results.attr("aria-hidden","false"),o.setClasses(),o.ensureHighlightVisible()})),n.on("close",(function(){o.$results.attr("aria-expanded","false"),o.$results.attr("aria-hidden","true"),o.$results.removeAttr("aria-activedescendant")})),n.on("results:toggle",(function(){var e=o.getHighlightedResults();0!==e.length&&e.trigger("mouseup")})),n.on("results:select",(function(){var e=o.getHighlightedResults();if(0!==e.length){var n=t.GetData(e[0],"data");e.hasClass("select2-results__option--selected")?o.trigger("close",{}):o.trigger("select",{data:n})}})),n.on("results:previous",(function(){var e=o.getHighlightedResults(),t=o.$results.find(".select2-results__option--selectable"),n=t.index(e);if(!(n<=0)){var r=n-1;0===e.length&&(r=0);var i=t.eq(r);i.trigger("mouseenter");var s=o.$results.offset().top,a=i.offset().top,l=o.$results.scrollTop()+(a-s);0===r?o.$results.scrollTop(0):a-s<0&&o.$results.scrollTop(l)}})),n.on("results:next",(function(){var e=o.getHighlightedResults(),t=o.$results.find(".select2-results__option--selectable"),n=t.index(e)+1;if(!(n>=t.length)){var r=t.eq(n);r.trigger("mouseenter");var i=o.$results.offset().top+o.$results.outerHeight(!1),s=r.offset().top+r.outerHeight(!1),a=o.$results.scrollTop()+s-i;0===n?o.$results.scrollTop(0):s>i&&o.$results.scrollTop(a)}})),n.on("results:focus",(function(e){e.element[0].classList.add("select2-results__option--highlighted"),e.element[0].setAttribute("aria-selected","true")})),n.on("results:message",(function(e){o.displayMessage(e)})),e.fn.mousewheel&&this.$results.on("mousewheel",(function(e){var t=o.$results.scrollTop(),n=o.$results.get(0).scrollHeight-t+e.deltaY,r=e.deltaY>0&&t-e.deltaY<=0,i=e.deltaY<0&&n<=o.$results.height();r?(o.$results.scrollTop(0),e.preventDefault(),e.stopPropagation()):i&&(o.$results.scrollTop(o.$results.get(0).scrollHeight-o.$results.height()),e.preventDefault(),e.stopPropagation())})),this.$results.on("mouseup",".select2-results__option--selectable",(function(n){var r=e(this),i=t.GetData(this,"data");r.hasClass("select2-results__option--selected")?o.options.get("multiple")?o.trigger("unselect",{originalEvent:n,data:i}):o.trigger("close",{}):o.trigger("select",{originalEvent:n,data:i})})),this.$results.on("mouseenter",".select2-results__option--selectable",(function(n){var r=t.GetData(this,"data");o.getHighlightedResults().removeClass("select2-results__option--highlighted").attr("aria-selected","false"),o.trigger("results:focus",{data:r,element:e(this)})}))},n.prototype.getHighlightedResults=function(){return this.$results.find(".select2-results__option--highlighted")},n.prototype.destroy=function(){this.$results.remove()},n.prototype.ensureHighlightVisible=function(){var e=this.getHighlightedResults();if(0!==e.length){var t=this.$results.find(".select2-results__option--selectable").index(e),n=this.$results.offset().top,r=e.offset().top,o=this.$results.scrollTop()+(r-n),i=r-n;o-=2*e.outerHeight(!1),t<=2?this.$results.scrollTop(0):(i>this.$results.outerHeight()||i<0)&&this.$results.scrollTop(o)}},n.prototype.template=function(t,n){var r=this.options.get("templateResult"),o=this.options.get("escapeMarkup"),i=r(t,n);null==i?n.style.display="none":"string"==typeof i?n.innerHTML=o(i):e(n).append(i)},n})),t.define("select2/keys",[],(function(){return{BACKSPACE:8,TAB:9,ENTER:13,SHIFT:16,CTRL:17,ALT:18,ESC:27,SPACE:32,PAGE_UP:33,PAGE_DOWN:34,END:35,HOME:36,LEFT:37,UP:38,RIGHT:39,DOWN:40,DELETE:46}})),t.define("select2/selection/base",["jquery","../utils","../keys"],(function(e,t,n){function r(e,t){this.$element=e,this.options=t,r.__super__.constructor.call(this)}return t.Extend(r,t.Observable),r.prototype.render=function(){var n=e('<span class="select2-selection" role="combobox"  aria-haspopup="true" aria-expanded="false"></span>');return this._tabindex=0,null!=t.GetData(this.$element[0],"old-tabindex")?this._tabindex=t.GetData(this.$element[0],"old-tabindex"):null!=this.$element.attr("tabindex")&&(this._tabindex=this.$element.attr("tabindex")),n.attr("title",this.$element.attr("title")),n.attr("tabindex",this._tabindex),n.attr("aria-disabled","false"),this.$selection=n,n},r.prototype.bind=function(e,t){var r=this,o=e.id+"-results";this.container=e,this.$selection.on("focus",(function(e){r.trigger("focus",e)})),this.$selection.on("blur",(function(e){r._handleBlur(e)})),this.$selection.on("keydown",(function(e){r.trigger("keypress",e),e.which===n.SPACE&&e.preventDefault()})),e.on("results:focus",(function(e){r.$selection.attr("aria-activedescendant",e.data._resultId)})),e.on("selection:update",(function(e){r.update(e.data)})),e.on("open",(function(){r.$selection.attr("aria-expanded","true"),r.$selection.attr("aria-owns",o),r._attachCloseHandler(e)})),e.on("close",(function(){r.$selection.attr("aria-expanded","false"),r.$selection.removeAttr("aria-activedescendant"),r.$selection.removeAttr("aria-owns"),r.$selection.trigger("focus"),r._detachCloseHandler(e)})),e.on("enable",(function(){r.$selection.attr("tabindex",r._tabindex),r.$selection.attr("aria-disabled","false")})),e.on("disable",(function(){r.$selection.attr("tabindex","-1"),r.$selection.attr("aria-disabled","true")}))},r.prototype._handleBlur=function(t){var n=this;window.setTimeout((function(){document.activeElement==n.$selection[0]||e.contains(n.$selection[0],document.activeElement)||n.trigger("blur",t)}),1)},r.prototype._attachCloseHandler=function(n){e(document.body).on("mousedown.select2."+n.id,(function(n){var r=e(n.target).closest(".select2");e(".select2.select2-container--open").each((function(){this!=r[0]&&t.GetData(this,"element").select2("close")}))}))},r.prototype._detachCloseHandler=function(t){e(document.body).off("mousedown.select2."+t.id)},r.prototype.position=function(e,t){t.find(".selection").append(e)},r.prototype.destroy=function(){this._detachCloseHandler(this.container)},r.prototype.update=function(e){throw new Error("The `update` method must be defined in child classes.")},r.prototype.isEnabled=function(){return!this.isDisabled()},r.prototype.isDisabled=function(){return this.options.get("disabled")},r})),t.define("select2/selection/single",["jquery","./base","../utils","../keys"],(function(e,t,n,r){function o(){o.__super__.constructor.apply(this,arguments)}return n.Extend(o,t),o.prototype.render=function(){var e=o.__super__.render.call(this);return e[0].classList.add("select2-selection--single"),e.html('<span class="select2-selection__rendered"></span><span class="select2-selection__arrow" role="presentation"><b role="presentation"></b></span>'),e},o.prototype.bind=function(e,t){var n=this;o.__super__.bind.apply(this,arguments);var r=e.id+"-container";this.$selection.find(".select2-selection__rendered").attr("id",r).attr("role","textbox").attr("aria-readonly","true"),this.$selection.attr("aria-labelledby",r),this.$selection.attr("aria-controls",r),this.$selection.on("mousedown",(function(e){1===e.which&&n.trigger("toggle",{originalEvent:e})})),this.$selection.on("focus",(function(e){})),this.$selection.on("blur",(function(e){})),e.on("focus",(function(t){e.isOpen()||n.$selection.trigger("focus")}))},o.prototype.clear=function(){var e=this.$selection.find(".select2-selection__rendered");e.empty(),e.removeAttr("title")},o.prototype.display=function(e,t){var n=this.options.get("templateSelection");return this.options.get("escapeMarkup")(n(e,t))},o.prototype.selectionContainer=function(){return e("<span></span>")},o.prototype.update=function(e){if(0!==e.length){var t=e[0],n=this.$selection.find(".select2-selection__rendered"),r=this.display(t,n);n.empty().append(r);var o=t.title||t.text;o?n.attr("title",o):n.removeAttr("title")}else this.clear()},o})),t.define("select2/selection/multiple",["jquery","./base","../utils"],(function(e,t,n){function r(e,t){r.__super__.constructor.apply(this,arguments)}return n.Extend(r,t),r.prototype.render=function(){var e=r.__super__.render.call(this);return e[0].classList.add("select2-selection--multiple"),e.html('<ul class="select2-selection__rendered"></ul>'),e},r.prototype.bind=function(t,o){var i=this;r.__super__.bind.apply(this,arguments);var s=t.id+"-container";this.$selection.find(".select2-selection__rendered").attr("id",s),this.$selection.on("click",(function(e){i.trigger("toggle",{originalEvent:e})})),this.$selection.on("click",".select2-selection__choice__remove",(function(t){if(!i.isDisabled()){var r=e(this).parent(),o=n.GetData(r[0],"data");i.trigger("unselect",{originalEvent:t,data:o})}})),this.$selection.on("keydown",".select2-selection__choice__remove",(function(e){i.isDisabled()||e.stopPropagation()}))},r.prototype.clear=function(){var e=this.$selection.find(".select2-selection__rendered");e.empty(),e.removeAttr("title")},r.prototype.display=function(e,t){var n=this.options.get("templateSelection");return this.options.get("escapeMarkup")(n(e,t))},r.prototype.selectionContainer=function(){return e('<li class="select2-selection__choice"><button type="button" class="select2-selection__choice__remove" tabindex="-1"><span aria-hidden="true">&times;</span></button><span class="select2-selection__choice__display"></span></li>')},r.prototype.update=function(e){if(this.clear(),0!==e.length){for(var t=[],r=this.$selection.find(".select2-selection__rendered").attr("id")+"-choice-",o=0;o<e.length;o++){var i=e[o],s=this.selectionContainer(),a=this.display(i,s),l=r+n.generateChars(4)+"-";i.id?l+=i.id:l+=n.generateChars(4),s.find(".select2-selection__choice__display").append(a).attr("id",l);var c=i.title||i.text;c&&s.attr("title",c);var u=this.options.get("translations").get("removeItem"),d=s.find(".select2-selection__choice__remove");d.attr("title",u()),d.attr("aria-label",u()),d.attr("aria-describedby",l),n.StoreData(s[0],"data",i),t.push(s)}this.$selection.find(".select2-selection__rendered").append(t)}},r})),t.define("select2/selection/placeholder",[],(function(){function e(e,t,n){this.placeholder=this.normalizePlaceholder(n.get("placeholder")),e.call(this,t,n)}return e.prototype.normalizePlaceholder=function(e,t){return"string"==typeof t&&(t={id:"",text:t}),t},e.prototype.createPlaceholder=function(e,t){var n=this.selectionContainer();n.html(this.display(t)),n[0].classList.add("select2-selection__placeholder"),n[0].classList.remove("select2-selection__choice");var r=t.title||t.text||n.text();return this.$selection.find(".select2-selection__rendered").attr("title",r),n},e.prototype.update=function(e,t){var n=1==t.length&&t[0].id!=this.placeholder.id;if(t.length>1||n)return e.call(this,t);this.clear();var r=this.createPlaceholder(this.placeholder);this.$selection.find(".select2-selection__rendered").append(r)},e})),t.define("select2/selection/allowClear",["jquery","../keys","../utils"],(function(e,t,n){function r(){}return r.prototype.bind=function(e,t,n){var r=this;e.call(this,t,n),null==this.placeholder&&this.options.get("debug")&&window.console&&console.error&&console.error("Select2: The `allowClear` option should be used in combination with the `placeholder` option."),this.$selection.on("mousedown",".select2-selection__clear",(function(e){r._handleClear(e)})),t.on("keypress",(function(e){r._handleKeyboardClear(e,t)}))},r.prototype._handleClear=function(e,t){if(!this.isDisabled()){var r=this.$selection.find(".select2-selection__clear");if(0!==r.length){t.stopPropagation();var o=n.GetData(r[0],"data"),i=this.$element.val();this.$element.val(this.placeholder.id);var s={data:o};if(this.trigger("clear",s),s.prevented)this.$element.val(i);else{for(var a=0;a<o.length;a++)if(s={data:o[a]},this.trigger("unselect",s),s.prevented)return void this.$element.val(i);this.$element.trigger("input").trigger("change"),this.trigger("toggle",{})}}}},r.prototype._handleKeyboardClear=function(e,n,r){r.isOpen()||n.which!=t.DELETE&&n.which!=t.BACKSPACE||this._handleClear(n)},r.prototype.update=function(t,r){if(t.call(this,r),this.$selection.find(".select2-selection__clear").remove(),this.$selection[0].classList.remove("select2-selection--clearable"),!(this.$selection.find(".select2-selection__placeholder").length>0||0===r.length)){var o=this.$selection.find(".select2-selection__rendered").attr("id"),i=this.options.get("translations").get("removeAllItems"),s=e('<button type="button" class="select2-selection__clear" tabindex="-1"><span aria-hidden="true">&times;</span></button>');s.attr("title",i()),s.attr("aria-label",i()),s.attr("aria-describedby",o),n.StoreData(s[0],"data",r),this.$selection.prepend(s),this.$selection[0].classList.add("select2-selection--clearable")}},r})),t.define("select2/selection/search",["jquery","../utils","../keys"],(function(e,t,n){function r(e,t,n){e.call(this,t,n)}return r.prototype.render=function(t){var n=this.options.get("translations").get("search"),r=e('<span class="select2-search select2-search--inline"><textarea class="select2-search__field" type="search" tabindex="-1" autocorrect="off" autocapitalize="none" spellcheck="false" role="searchbox" aria-autocomplete="list" ></textarea></span>');this.$searchContainer=r,this.$search=r.find("textarea"),this.$search.prop("autocomplete",this.options.get("autocomplete")),this.$search.attr("aria-label",n());var o=t.call(this);return this._transferTabIndex(),o.append(this.$searchContainer),o},r.prototype.bind=function(e,r,o){var i=this,s=r.id+"-results",a=r.id+"-container";e.call(this,r,o),i.$search.attr("aria-describedby",a),r.on("open",(function(){i.$search.attr("aria-controls",s),i.$search.trigger("focus")})),r.on("close",(function(){i.$search.val(""),i.resizeSearch(),i.$search.removeAttr("aria-controls"),i.$search.removeAttr("aria-activedescendant"),i.$search.trigger("focus")})),r.on("enable",(function(){i.$search.prop("disabled",!1),i._transferTabIndex()})),r.on("disable",(function(){i.$search.prop("disabled",!0)})),r.on("focus",(function(e){i.$search.trigger("focus")})),r.on("results:focus",(function(e){e.data._resultId?i.$search.attr("aria-activedescendant",e.data._resultId):i.$search.removeAttr("aria-activedescendant")})),this.$selection.on("focusin",".select2-search--inline",(function(e){i.trigger("focus",e)})),this.$selection.on("focusout",".select2-search--inline",(function(e){i._handleBlur(e)})),this.$selection.on("keydown",".select2-search--inline",(function(e){if(e.stopPropagation(),i.trigger("keypress",e),i._keyUpPrevented=e.isDefaultPrevented(),e.which===n.BACKSPACE&&""===i.$search.val()){var r=i.$selection.find(".select2-selection__choice").last();if(r.length>0){var o=t.GetData(r[0],"data");i.searchRemoveChoice(o),e.preventDefault()}}})),this.$selection.on("click",".select2-search--inline",(function(e){i.$search.val()&&e.stopPropagation()}));var l=document.documentMode,c=l&&l<=11;this.$selection.on("input.searchcheck",".select2-search--inline",(function(e){c?i.$selection.off("input.search input.searchcheck"):i.$selection.off("keyup.search")})),this.$selection.on("keyup.search input.search",".select2-search--inline",(function(e){if(c&&"input"===e.type)i.$selection.off("input.search input.searchcheck");else{var t=e.which;t!=n.SHIFT&&t!=n.CTRL&&t!=n.ALT&&t!=n.TAB&&i.handleSearch(e)}}))},r.prototype._transferTabIndex=function(e){this.$search.attr("tabindex",this.$selection.attr("tabindex")),this.$selection.attr("tabindex","-1")},r.prototype.createPlaceholder=function(e,t){this.$search.attr("placeholder",t.text)},r.prototype.update=function(e,t){var n=this.$search[0]==document.activeElement;this.$search.attr("placeholder",""),e.call(this,t),this.resizeSearch(),n&&this.$search.trigger("focus")},r.prototype.handleSearch=function(){if(this.resizeSearch(),!this._keyUpPrevented){var e=this.$search.val();this.trigger("query",{term:e})}this._keyUpPrevented=!1},r.prototype.searchRemoveChoice=function(e,t){this.trigger("unselect",{data:t}),this.$search.val(t.text),this.handleSearch()},r.prototype.resizeSearch=function(){this.$search.css("width","25px");var e="100%";""===this.$search.attr("placeholder")&&(e=.75*(this.$search.val().length+1)+"em"),this.$search.css("width",e)},r})),t.define("select2/selection/selectionCss",["../utils"],(function(e){function t(){}return t.prototype.render=function(t){var n=t.call(this),r=this.options.get("selectionCssClass")||"";return-1!==r.indexOf(":all:")&&(r=r.replace(":all:",""),e.copyNonInternalCssClasses(n[0],this.$element[0])),n.addClass(r),n},t})),t.define("select2/selection/eventRelay",["jquery"],(function(e){function t(){}return t.prototype.bind=function(t,n,r){var o=this,i=["open","opening","close","closing","select","selecting","unselect","unselecting","clear","clearing"],s=["opening","closing","selecting","unselecting","clearing"];t.call(this,n,r),n.on("*",(function(t,n){if(-1!==i.indexOf(t)){n=n||{};var r=e.Event("select2:"+t,{params:n});o.$element.trigger(r),-1!==s.indexOf(t)&&(n.prevented=r.isDefaultPrevented())}}))},t})),t.define("select2/translation",["jquery","require"],(function(e,t){function n(e){this.dict=e||{}}return n.prototype.all=function(){return this.dict},n.prototype.get=function(e){return this.dict[e]},n.prototype.extend=function(t){this.dict=e.extend({},t.all(),this.dict)},n._cache={},n.loadPath=function(e){if(!(e in n._cache)){var r=t(e);n._cache[e]=r}return new n(n._cache[e])},n})),t.define("select2/diacritics",[],(function(){return{"Ⓐ":"A",Ａ:"A",À:"A",Á:"A",Â:"A",Ầ:"A",Ấ:"A",Ẫ:"A",Ẩ:"A",Ã:"A",Ā:"A",Ă:"A",Ằ:"A",Ắ:"A",Ẵ:"A",Ẳ:"A",Ȧ:"A",Ǡ:"A",Ä:"A",Ǟ:"A",Ả:"A",Å:"A",Ǻ:"A",Ǎ:"A",Ȁ:"A",Ȃ:"A",Ạ:"A",Ậ:"A",Ặ:"A",Ḁ:"A",Ą:"A",Ⱥ:"A",Ɐ:"A",Ꜳ:"AA",Æ:"AE",Ǽ:"AE",Ǣ:"AE",Ꜵ:"AO",Ꜷ:"AU",Ꜹ:"AV",Ꜻ:"AV",Ꜽ:"AY","Ⓑ":"B",Ｂ:"B",Ḃ:"B",Ḅ:"B",Ḇ:"B",Ƀ:"B",Ƃ:"B",Ɓ:"B","Ⓒ":"C",Ｃ:"C",Ć:"C",Ĉ:"C",Ċ:"C",Č:"C",Ç:"C",Ḉ:"C",Ƈ:"C",Ȼ:"C",Ꜿ:"C","Ⓓ":"D",Ｄ:"D",Ḋ:"D",Ď:"D",Ḍ:"D",Ḑ:"D",Ḓ:"D",Ḏ:"D",Đ:"D",Ƌ:"D",Ɗ:"D",Ɖ:"D",Ꝺ:"D",Ǳ:"DZ",Ǆ:"DZ",ǲ:"Dz",ǅ:"Dz","Ⓔ":"E",Ｅ:"E",È:"E",É:"E",Ê:"E",Ề:"E",Ế:"E",Ễ:"E",Ể:"E",Ẽ:"E",Ē:"E",Ḕ:"E",Ḗ:"E",Ĕ:"E",Ė:"E",Ë:"E",Ẻ:"E",Ě:"E",Ȅ:"E",Ȇ:"E",Ẹ:"E",Ệ:"E",Ȩ:"E",Ḝ:"E",Ę:"E",Ḙ:"E",Ḛ:"E",Ɛ:"E",Ǝ:"E","Ⓕ":"F",Ｆ:"F",Ḟ:"F",Ƒ:"F",Ꝼ:"F","Ⓖ":"G",Ｇ:"G",Ǵ:"G",Ĝ:"G",Ḡ:"G",Ğ:"G",Ġ:"G",Ǧ:"G",Ģ:"G",Ǥ:"G",Ɠ:"G",Ꞡ:"G",Ᵹ:"G",Ꝿ:"G","Ⓗ":"H",Ｈ:"H",Ĥ:"H",Ḣ:"H",Ḧ:"H",Ȟ:"H",Ḥ:"H",Ḩ:"H",Ḫ:"H",Ħ:"H",Ⱨ:"H",Ⱶ:"H",Ɥ:"H","Ⓘ":"I",Ｉ:"I",Ì:"I",Í:"I",Î:"I",Ĩ:"I",Ī:"I",Ĭ:"I",İ:"I",Ï:"I",Ḯ:"I",Ỉ:"I",Ǐ:"I",Ȉ:"I",Ȋ:"I",Ị:"I",Į:"I",Ḭ:"I",Ɨ:"I","Ⓙ":"J",Ｊ:"J",Ĵ:"J",Ɉ:"J","Ⓚ":"K",Ｋ:"K",Ḱ:"K",Ǩ:"K",Ḳ:"K",Ķ:"K",Ḵ:"K",Ƙ:"K",Ⱪ:"K",Ꝁ:"K",Ꝃ:"K",Ꝅ:"K",Ꞣ:"K","Ⓛ":"L",Ｌ:"L",Ŀ:"L",Ĺ:"L",Ľ:"L",Ḷ:"L",Ḹ:"L",Ļ:"L",Ḽ:"L",Ḻ:"L",Ł:"L",Ƚ:"L",Ɫ:"L",Ⱡ:"L",Ꝉ:"L",Ꝇ:"L",Ꞁ:"L",Ǉ:"LJ",ǈ:"Lj","Ⓜ":"M",Ｍ:"M",Ḿ:"M",Ṁ:"M",Ṃ:"M",Ɱ:"M",Ɯ:"M","Ⓝ":"N",Ｎ:"N",Ǹ:"N",Ń:"N",Ñ:"N",Ṅ:"N",Ň:"N",Ṇ:"N",Ņ:"N",Ṋ:"N",Ṉ:"N",Ƞ:"N",Ɲ:"N",Ꞑ:"N",Ꞥ:"N",Ǌ:"NJ",ǋ:"Nj","Ⓞ":"O",Ｏ:"O",Ò:"O",Ó:"O",Ô:"O",Ồ:"O",Ố:"O",Ỗ:"O",Ổ:"O",Õ:"O",Ṍ:"O",Ȭ:"O",Ṏ:"O",Ō:"O",Ṑ:"O",Ṓ:"O",Ŏ:"O",Ȯ:"O",Ȱ:"O",Ö:"O",Ȫ:"O",Ỏ:"O",Ő:"O",Ǒ:"O",Ȍ:"O",Ȏ:"O",Ơ:"O",Ờ:"O",Ớ:"O",Ỡ:"O",Ở:"O",Ợ:"O",Ọ:"O",Ộ:"O",Ǫ:"O",Ǭ:"O",Ø:"O",Ǿ:"O",Ɔ:"O",Ɵ:"O",Ꝋ:"O",Ꝍ:"O",Œ:"OE",Ƣ:"OI",Ꝏ:"OO",Ȣ:"OU","Ⓟ":"P",Ｐ:"P",Ṕ:"P",Ṗ:"P",Ƥ:"P",Ᵽ:"P",Ꝑ:"P",Ꝓ:"P",Ꝕ:"P","Ⓠ":"Q",Ｑ:"Q",Ꝗ:"Q",Ꝙ:"Q",Ɋ:"Q","Ⓡ":"R",Ｒ:"R",Ŕ:"R",Ṙ:"R",Ř:"R",Ȑ:"R",Ȓ:"R",Ṛ:"R",Ṝ:"R",Ŗ:"R",Ṟ:"R",Ɍ:"R",Ɽ:"R",Ꝛ:"R",Ꞧ:"R",Ꞃ:"R","Ⓢ":"S",Ｓ:"S",ẞ:"S",Ś:"S",Ṥ:"S",Ŝ:"S",Ṡ:"S",Š:"S",Ṧ:"S",Ṣ:"S",Ṩ:"S",Ș:"S",Ş:"S",Ȿ:"S",Ꞩ:"S",Ꞅ:"S","Ⓣ":"T",Ｔ:"T",Ṫ:"T",Ť:"T",Ṭ:"T",Ț:"T",Ţ:"T",Ṱ:"T",Ṯ:"T",Ŧ:"T",Ƭ:"T",Ʈ:"T",Ⱦ:"T",Ꞇ:"T",Ꜩ:"TZ","Ⓤ":"U",Ｕ:"U",Ù:"U",Ú:"U",Û:"U",Ũ:"U",Ṹ:"U",Ū:"U",Ṻ:"U",Ŭ:"U",Ü:"U",Ǜ:"U",Ǘ:"U",Ǖ:"U",Ǚ:"U",Ủ:"U",Ů:"U",Ű:"U",Ǔ:"U",Ȕ:"U",Ȗ:"U",Ư:"U",Ừ:"U",Ứ:"U",Ữ:"U",Ử:"U",Ự:"U",Ụ:"U",Ṳ:"U",Ų:"U",Ṷ:"U",Ṵ:"U",Ʉ:"U","Ⓥ":"V",Ｖ:"V",Ṽ:"V",Ṿ:"V",Ʋ:"V",Ꝟ:"V",Ʌ:"V",Ꝡ:"VY","Ⓦ":"W",Ｗ:"W",Ẁ:"W",Ẃ:"W",Ŵ:"W",Ẇ:"W",Ẅ:"W",Ẉ:"W",Ⱳ:"W","Ⓧ":"X",Ｘ:"X",Ẋ:"X",Ẍ:"X","Ⓨ":"Y",Ｙ:"Y",Ỳ:"Y",Ý:"Y",Ŷ:"Y",Ỹ:"Y",Ȳ:"Y",Ẏ:"Y",Ÿ:"Y",Ỷ:"Y",Ỵ:"Y",Ƴ:"Y",Ɏ:"Y",Ỿ:"Y","Ⓩ":"Z",Ｚ:"Z",Ź:"Z",Ẑ:"Z",Ż:"Z",Ž:"Z",Ẓ:"Z",Ẕ:"Z",Ƶ:"Z",Ȥ:"Z",Ɀ:"Z",Ⱬ:"Z",Ꝣ:"Z","ⓐ":"a",ａ:"a",ẚ:"a",à:"a",á:"a",â:"a",ầ:"a",ấ:"a",ẫ:"a",ẩ:"a",ã:"a",ā:"a",ă:"a",ằ:"a",ắ:"a",ẵ:"a",ẳ:"a",ȧ:"a",ǡ:"a",ä:"a",ǟ:"a",ả:"a",å:"a",ǻ:"a",ǎ:"a",ȁ:"a",ȃ:"a",ạ:"a",ậ:"a",ặ:"a",ḁ:"a",ą:"a",ⱥ:"a",ɐ:"a",ꜳ:"aa",æ:"ae",ǽ:"ae",ǣ:"ae",ꜵ:"ao",ꜷ:"au",ꜹ:"av",ꜻ:"av",ꜽ:"ay","ⓑ":"b",ｂ:"b",ḃ:"b",ḅ:"b",ḇ:"b",ƀ:"b",ƃ:"b",ɓ:"b","ⓒ":"c",ｃ:"c",ć:"c",ĉ:"c",ċ:"c",č:"c",ç:"c",ḉ:"c",ƈ:"c",ȼ:"c",ꜿ:"c",ↄ:"c","ⓓ":"d",ｄ:"d",ḋ:"d",ď:"d",ḍ:"d",ḑ:"d",ḓ:"d",ḏ:"d",đ:"d",ƌ:"d",ɖ:"d",ɗ:"d",ꝺ:"d",ǳ:"dz",ǆ:"dz","ⓔ":"e",ｅ:"e",è:"e",é:"e",ê:"e",ề:"e",ế:"e",ễ:"e",ể:"e",ẽ:"e",ē:"e",ḕ:"e",ḗ:"e",ĕ:"e",ė:"e",ë:"e",ẻ:"e",ě:"e",ȅ:"e",ȇ:"e",ẹ:"e",ệ:"e",ȩ:"e",ḝ:"e",ę:"e",ḙ:"e",ḛ:"e",ɇ:"e",ɛ:"e",ǝ:"e","ⓕ":"f",ｆ:"f",ḟ:"f",ƒ:"f",ꝼ:"f","ⓖ":"g",ｇ:"g",ǵ:"g",ĝ:"g",ḡ:"g",ğ:"g",ġ:"g",ǧ:"g",ģ:"g",ǥ:"g",ɠ:"g",ꞡ:"g",ᵹ:"g",ꝿ:"g","ⓗ":"h",ｈ:"h",ĥ:"h",ḣ:"h",ḧ:"h",ȟ:"h",ḥ:"h",ḩ:"h",ḫ:"h",ẖ:"h",ħ:"h",ⱨ:"h",ⱶ:"h",ɥ:"h",ƕ:"hv","ⓘ":"i",ｉ:"i",ì:"i",í:"i",î:"i",ĩ:"i",ī:"i",ĭ:"i",ï:"i",ḯ:"i",ỉ:"i",ǐ:"i",ȉ:"i",ȋ:"i",ị:"i",į:"i",ḭ:"i",ɨ:"i",ı:"i","ⓙ":"j",ｊ:"j",ĵ:"j",ǰ:"j",ɉ:"j","ⓚ":"k",ｋ:"k",ḱ:"k",ǩ:"k",ḳ:"k",ķ:"k",ḵ:"k",ƙ:"k",ⱪ:"k",ꝁ:"k",ꝃ:"k",ꝅ:"k",ꞣ:"k","ⓛ":"l",ｌ:"l",ŀ:"l",ĺ:"l",ľ:"l",ḷ:"l",ḹ:"l",ļ:"l",ḽ:"l",ḻ:"l",ſ:"l",ł:"l",ƚ:"l",ɫ:"l",ⱡ:"l",ꝉ:"l",ꞁ:"l",ꝇ:"l",ǉ:"lj","ⓜ":"m",ｍ:"m",ḿ:"m",ṁ:"m",ṃ:"m",ɱ:"m",ɯ:"m","ⓝ":"n",ｎ:"n",ǹ:"n",ń:"n",ñ:"n",ṅ:"n",ň:"n",ṇ:"n",ņ:"n",ṋ:"n",ṉ:"n",ƞ:"n",ɲ:"n",ŉ:"n",ꞑ:"n",ꞥ:"n",ǌ:"nj","ⓞ":"o",ｏ:"o",ò:"o",ó:"o",ô:"o",ồ:"o",ố:"o",ỗ:"o",ổ:"o",õ:"o",ṍ:"o",ȭ:"o",ṏ:"o",ō:"o",ṑ:"o",ṓ:"o",ŏ:"o",ȯ:"o",ȱ:"o",ö:"o",ȫ:"o",ỏ:"o",ő:"o",ǒ:"o",ȍ:"o",ȏ:"o",ơ:"o",ờ:"o",ớ:"o",ỡ:"o",ở:"o",ợ:"o",ọ:"o",ộ:"o",ǫ:"o",ǭ:"o",ø:"o",ǿ:"o",ɔ:"o",ꝋ:"o",ꝍ:"o",ɵ:"o",œ:"oe",ƣ:"oi",ȣ:"ou",ꝏ:"oo","ⓟ":"p",ｐ:"p",ṕ:"p",ṗ:"p",ƥ:"p",ᵽ:"p",ꝑ:"p",ꝓ:"p",ꝕ:"p","ⓠ":"q",ｑ:"q",ɋ:"q",ꝗ:"q",ꝙ:"q","ⓡ":"r",ｒ:"r",ŕ:"r",ṙ:"r",ř:"r",ȑ:"r",ȓ:"r",ṛ:"r",ṝ:"r",ŗ:"r",ṟ:"r",ɍ:"r",ɽ:"r",ꝛ:"r",ꞧ:"r",ꞃ:"r","ⓢ":"s",ｓ:"s",ß:"s",ś:"s",ṥ:"s",ŝ:"s",ṡ:"s",š:"s",ṧ:"s",ṣ:"s",ṩ:"s",ș:"s",ş:"s",ȿ:"s",ꞩ:"s",ꞅ:"s",ẛ:"s","ⓣ":"t",ｔ:"t",ṫ:"t",ẗ:"t",ť:"t",ṭ:"t",ț:"t",ţ:"t",ṱ:"t",ṯ:"t",ŧ:"t",ƭ:"t",ʈ:"t",ⱦ:"t",ꞇ:"t",ꜩ:"tz","ⓤ":"u",ｕ:"u",ù:"u",ú:"u",û:"u",ũ:"u",ṹ:"u",ū:"u",ṻ:"u",ŭ:"u",ü:"u",ǜ:"u",ǘ:"u",ǖ:"u",ǚ:"u",ủ:"u",ů:"u",ű:"u",ǔ:"u",ȕ:"u",ȗ:"u",ư:"u",ừ:"u",ứ:"u",ữ:"u",ử:"u",ự:"u",ụ:"u",ṳ:"u",ų:"u",ṷ:"u",ṵ:"u",ʉ:"u","ⓥ":"v",ｖ:"v",ṽ:"v",ṿ:"v",ʋ:"v",ꝟ:"v",ʌ:"v",ꝡ:"vy","ⓦ":"w",ｗ:"w",ẁ:"w",ẃ:"w",ŵ:"w",ẇ:"w",ẅ:"w",ẘ:"w",ẉ:"w",ⱳ:"w","ⓧ":"x",ｘ:"x",ẋ:"x",ẍ:"x","ⓨ":"y",ｙ:"y",ỳ:"y",ý:"y",ŷ:"y",ỹ:"y",ȳ:"y",ẏ:"y",ÿ:"y",ỷ:"y",ẙ:"y",ỵ:"y",ƴ:"y",ɏ:"y",ỿ:"y","ⓩ":"z",ｚ:"z",ź:"z",ẑ:"z",ż:"z",ž:"z",ẓ:"z",ẕ:"z",ƶ:"z",ȥ:"z",ɀ:"z",ⱬ:"z",ꝣ:"z",Ά:"Α",Έ:"Ε",Ή:"Η",Ί:"Ι",Ϊ:"Ι",Ό:"Ο",Ύ:"Υ",Ϋ:"Υ",Ώ:"Ω",ά:"α",έ:"ε",ή:"η",ί:"ι",ϊ:"ι",ΐ:"ι",ό:"ο",ύ:"υ",ϋ:"υ",ΰ:"υ",ώ:"ω",ς:"σ","’":"'"}})),t.define("select2/data/base",["../utils"],(function(e){function t(e,n){t.__super__.constructor.call(this)}return e.Extend(t,e.Observable),t.prototype.current=function(e){throw new Error("The `current` method must be defined in child classes.")},t.prototype.query=function(e,t){throw new Error("The `query` method must be defined in child classes.")},t.prototype.bind=function(e,t){},t.prototype.destroy=function(){},t.prototype.generateResultId=function(t,n){var r=t.id+"-result-";return r+=e.generateChars(4),null!=n.id?r+="-"+n.id.toString():r+="-"+e.generateChars(4),r},t})),t.define("select2/data/select",["./base","../utils","jquery"],(function(e,t,n){function r(e,t){this.$element=e,this.options=t,r.__super__.constructor.call(this)}return t.Extend(r,e),r.prototype.current=function(e){var t=this;e(Array.prototype.map.call(this.$element[0].querySelectorAll(":checked"),(function(e){return t.item(n(e))})))},r.prototype.select=function(e){var t=this;if(e.selected=!0,null!=e.element&&"option"===e.element.tagName.toLowerCase())return e.element.selected=!0,void this.$element.trigger("input").trigger("change");if(this.$element.prop("multiple"))this.current((function(n){var r=[];(e=[e]).push.apply(e,n);for(var o=0;o<e.length;o++){var i=e[o].id;-1===r.indexOf(i)&&r.push(i)}t.$element.val(r),t.$element.trigger("input").trigger("change")}));else{var n=e.id;this.$element.val(n),this.$element.trigger("input").trigger("change")}},r.prototype.unselect=function(e){var t=this;if(this.$element.prop("multiple")){if(e.selected=!1,null!=e.element&&"option"===e.element.tagName.toLowerCase())return e.element.selected=!1,void this.$element.trigger("input").trigger("change");this.current((function(n){for(var r=[],o=0;o<n.length;o++){var i=n[o].id;i!==e.id&&-1===r.indexOf(i)&&r.push(i)}t.$element.val(r),t.$element.trigger("input").trigger("change")}))}},r.prototype.bind=function(e,t){var n=this;this.container=e,e.on("select",(function(e){n.select(e.data)})),e.on("unselect",(function(e){n.unselect(e.data)}))},r.prototype.destroy=function(){this.$element.find("*").each((function(){t.RemoveData(this)}))},r.prototype.query=function(e,t){var r=[],o=this;this.$element.children().each((function(){if("option"===this.tagName.toLowerCase()||"optgroup"===this.tagName.toLowerCase()){var t=n(this),i=o.item(t),s=o.matches(e,i);null!==s&&r.push(s)}})),t({results:r})},r.prototype.addOptions=function(e){this.$element.append(e)},r.prototype.option=function(e){var r;e.children?(r=document.createElement("optgroup")).label=e.text:void 0!==(r=document.createElement("option")).textContent?r.textContent=e.text:r.innerText=e.text,void 0!==e.id&&(r.value=e.id),e.disabled&&(r.disabled=!0),e.selected&&(r.selected=!0),e.title&&(r.title=e.title);var o=this._normalizeItem(e);return o.element=r,t.StoreData(r,"data",o),n(r)},r.prototype.item=function(e){var r={};if(null!=(r=t.GetData(e[0],"data")))return r;var o=e[0];if("option"===o.tagName.toLowerCase())r={id:e.val(),text:e.text(),disabled:e.prop("disabled"),selected:e.prop("selected"),title:e.prop("title")};else if("optgroup"===o.tagName.toLowerCase()){r={text:e.prop("label"),children:[],title:e.prop("title")};for(var i=e.children("option"),s=[],a=0;a<i.length;a++){var l=n(i[a]),c=this.item(l);s.push(c)}r.children=s}return(r=this._normalizeItem(r)).element=e[0],t.StoreData(e[0],"data",r),r},r.prototype._normalizeItem=function(e){e!==Object(e)&&(e={id:e,text:e});var t={selected:!1,disabled:!1};return null!=(e=n.extend({},{text:""},e)).id&&(e.id=e.id.toString()),null!=e.text&&(e.text=e.text.toString()),null==e._resultId&&e.id&&null!=this.container&&(e._resultId=this.generateResultId(this.container,e)),n.extend({},t,e)},r.prototype.matches=function(e,t){return this.options.get("matcher")(e,t)},r})),t.define("select2/data/array",["./select","../utils","jquery"],(function(e,t,n){function r(e,t){this._dataToConvert=t.get("data")||[],r.__super__.constructor.call(this,e,t)}return t.Extend(r,e),r.prototype.bind=function(e,t){r.__super__.bind.call(this,e,t),this.addOptions(this.convertToOptions(this._dataToConvert))},r.prototype.select=function(e){var t=this.$element.find("option").filter((function(t,n){return n.value==e.id.toString()}));0===t.length&&(t=this.option(e),this.addOptions(t)),r.__super__.select.call(this,e)},r.prototype.convertToOptions=function(e){var t=this,r=this.$element.find("option"),o=r.map((function(){return t.item(n(this)).id})).get(),i=[];function s(e){return function(){return n(this).val()==e.id}}for(var a=0;a<e.length;a++){var l=this._normalizeItem(e[a]);if(o.indexOf(l.id)>=0){var c=r.filter(s(l)),u=this.item(c),d=n.extend(!0,{},l,u),p=this.option(d);c.replaceWith(p)}else{var h=this.option(l);if(l.children){var f=this.convertToOptions(l.children);h.append(f)}i.push(h)}}return i},r})),t.define("select2/data/ajax",["./array","../utils","jquery"],(function(e,t,n){function r(e,t){this.ajaxOptions=this._applyDefaults(t.get("ajax")),null!=this.ajaxOptions.processResults&&(this.processResults=this.ajaxOptions.processResults),r.__super__.constructor.call(this,e,t)}return t.Extend(r,e),r.prototype._applyDefaults=function(e){var t={data:function(e){return n.extend({},e,{q:e.term})},transport:function(e,t,r){var o=n.ajax(e);return o.then(t),o.fail(r),o}};return n.extend({},t,e,!0)},r.prototype.processResults=function(e){return e},r.prototype.query=function(e,t){var r=this;null!=this._request&&("function"==typeof this._request.abort&&this._request.abort(),this._request=null);var o=n.extend({type:"GET"},this.ajaxOptions);function i(){var n=o.transport(o,(function(n){var o=r.processResults(n,e);r.options.get("debug")&&window.console&&console.error&&(o&&o.results&&Array.isArray(o.results)||console.error("Select2: The AJAX results did not return an array in the `results` key of the response.")),t(o)}),(function(){(!("status"in n)||0!==n.status&&"0"!==n.status)&&r.trigger("results:message",{message:"errorLoading"})}));r._request=n}"function"==typeof o.url&&(o.url=o.url.call(this.$element,e)),"function"==typeof o.data&&(o.data=o.data.call(this.$element,e)),this.ajaxOptions.delay&&null!=e.term?(this._queryTimeout&&window.clearTimeout(this._queryTimeout),this._queryTimeout=window.setTimeout(i,this.ajaxOptions.delay)):i()},r})),t.define("select2/data/tags",["jquery"],(function(e){function t(e,t,n){var r=n.get("tags"),o=n.get("createTag");void 0!==o&&(this.createTag=o);var i=n.get("insertTag");if(void 0!==i&&(this.insertTag=i),e.call(this,t,n),Array.isArray(r))for(var s=0;s<r.length;s++){var a=r[s],l=this._normalizeItem(a),c=this.option(l);this.$element.append(c)}}return t.prototype.query=function(e,t,n){var r=this;function o(e,i){for(var s=e.results,a=0;a<s.length;a++){var l=s[a],c=null!=l.children&&!o({results:l.children},!0);if((l.text||"").toUpperCase()===(t.term||"").toUpperCase()||c)return!i&&(e.data=s,void n(e))}if(i)return!0;var u=r.createTag(t);if(null!=u){var d=r.option(u);d.attr("data-select2-tag","true"),r.addOptions([d]),r.insertTag(s,u)}e.results=s,n(e)}this._removeOldTags(),null!=t.term&&null==t.page?e.call(this,t,o):e.call(this,t,n)},t.prototype.createTag=function(e,t){if(null==t.term)return null;var n=t.term.trim();return""===n?null:{id:n,text:n}},t.prototype.insertTag=function(e,t,n){t.unshift(n)},t.prototype._removeOldTags=function(t){this.$element.find("option[data-select2-tag]").each((function(){this.selected||e(this).remove()}))},t})),t.define("select2/data/tokenizer",["jquery"],(function(e){function t(e,t,n){var r=n.get("tokenizer");void 0!==r&&(this.tokenizer=r),e.call(this,t,n)}return t.prototype.bind=function(e,t,n){e.call(this,t,n),this.$search=t.dropdown.$search||t.selection.$search||n.find(".select2-search__field")},t.prototype.query=function(t,n,r){var o=this;function i(t){var n=o._normalizeItem(t);if(!o.$element.find("option").filter((function(){return e(this).val()===n.id})).length){var r=o.option(n);r.attr("data-select2-tag",!0),o._removeOldTags(),o.addOptions([r])}s(n)}function s(e){o.trigger("select",{data:e})}n.term=n.term||"";var a=this.tokenizer(n,this.options,i);a.term!==n.term&&(this.$search.length&&(this.$search.val(a.term),this.$search.trigger("focus")),n.term=a.term),t.call(this,n,r)},t.prototype.tokenizer=function(t,n,r,o){for(var i=r.get("tokenSeparators")||[],s=n.term,a=0,l=this.createTag||function(e){return{id:e.term,text:e.term}};a<s.length;){var c=s[a];if(-1!==i.indexOf(c)){var u=s.substr(0,a),d=l(e.extend({},n,{term:u}));null!=d?(o(d),s=s.substr(a+1)||"",a=0):a++}else a++}return{term:s}},t})),t.define("select2/data/minimumInputLength",[],(function(){function e(e,t,n){this.minimumInputLength=n.get("minimumInputLength"),e.call(this,t,n)}return e.prototype.query=function(e,t,n){t.term=t.term||"",t.term.length<this.minimumInputLength?this.trigger("results:message",{message:"inputTooShort",args:{minimum:this.minimumInputLength,input:t.term,params:t}}):e.call(this,t,n)},e})),t.define("select2/data/maximumInputLength",[],(function(){function e(e,t,n){this.maximumInputLength=n.get("maximumInputLength"),e.call(this,t,n)}return e.prototype.query=function(e,t,n){t.term=t.term||"",this.maximumInputLength>0&&t.term.length>this.maximumInputLength?this.trigger("results:message",{message:"inputTooLong",args:{maximum:this.maximumInputLength,input:t.term,params:t}}):e.call(this,t,n)},e})),t.define("select2/data/maximumSelectionLength",[],(function(){function e(e,t,n){this.maximumSelectionLength=n.get("maximumSelectionLength"),e.call(this,t,n)}return e.prototype.bind=function(e,t,n){var r=this;e.call(this,t,n),t.on("select",(function(){r._checkIfMaximumSelected()}))},e.prototype.query=function(e,t,n){var r=this;this._checkIfMaximumSelected((function(){e.call(r,t,n)}))},e.prototype._checkIfMaximumSelected=function(e,t){var n=this;this.current((function(e){var r=null!=e?e.length:0;n.maximumSelectionLength>0&&r>=n.maximumSelectionLength?n.trigger("results:message",{message:"maximumSelected",args:{maximum:n.maximumSelectionLength}}):t&&t()}))},e})),t.define("select2/dropdown",["jquery","./utils"],(function(e,t){function n(e,t){this.$element=e,this.options=t,n.__super__.constructor.call(this)}return t.Extend(n,t.Observable),n.prototype.render=function(){var t=e('<span class="select2-dropdown"><span class="select2-results"></span></span>');return t.attr("dir",this.options.get("dir")),this.$dropdown=t,t},n.prototype.bind=function(){},n.prototype.position=function(e,t){},n.prototype.destroy=function(){this.$dropdown.remove()},n})),t.define("select2/dropdown/search",["jquery"],(function(e){function t(){}return t.prototype.render=function(t){var n=t.call(this),r=this.options.get("translations").get("search"),o=e('<span class="select2-search select2-search--dropdown"><input class="select2-search__field" type="search" tabindex="-1" autocorrect="off" autocapitalize="none" spellcheck="false" role="searchbox" aria-autocomplete="list" /></span>');return this.$searchContainer=o,this.$search=o.find("input"),this.$search.prop("autocomplete",this.options.get("autocomplete")),this.$search.attr("aria-label",r()),n.prepend(o),n},t.prototype.bind=function(t,n,r){var o=this,i=n.id+"-results";t.call(this,n,r),this.$search.on("keydown",(function(e){o.trigger("keypress",e),o._keyUpPrevented=e.isDefaultPrevented()})),this.$search.on("input",(function(t){e(this).off("keyup")})),this.$search.on("keyup input",(function(e){o.handleSearch(e)})),n.on("open",(function(){o.$search.attr("tabindex",0),o.$search.attr("aria-controls",i),o.$search.trigger("focus"),window.setTimeout((function(){o.$search.trigger("focus")}),0)})),n.on("close",(function(){o.$search.attr("tabindex",-1),o.$search.removeAttr("aria-controls"),o.$search.removeAttr("aria-activedescendant"),o.$search.val(""),o.$search.trigger("blur")})),n.on("focus",(function(){n.isOpen()||o.$search.trigger("focus")})),n.on("results:all",(function(e){null!=e.query.term&&""!==e.query.term||(o.showSearch(e)?o.$searchContainer[0].classList.remove("select2-search--hide"):o.$searchContainer[0].classList.add("select2-search--hide"))})),n.on("results:focus",(function(e){e.data._resultId?o.$search.attr("aria-activedescendant",e.data._resultId):o.$search.removeAttr("aria-activedescendant")}))},t.prototype.handleSearch=function(e){if(!this._keyUpPrevented){var t=this.$search.val();this.trigger("query",{term:t})}this._keyUpPrevented=!1},t.prototype.showSearch=function(e,t){return!0},t})),t.define("select2/dropdown/hidePlaceholder",[],(function(){function e(e,t,n,r){this.placeholder=this.normalizePlaceholder(n.get("placeholder")),e.call(this,t,n,r)}return e.prototype.append=function(e,t){t.results=this.removePlaceholder(t.results),e.call(this,t)},e.prototype.normalizePlaceholder=function(e,t){return"string"==typeof t&&(t={id:"",text:t}),t},e.prototype.removePlaceholder=function(e,t){for(var n=t.slice(0),r=t.length-1;r>=0;r--){var o=t[r];this.placeholder.id===o.id&&n.splice(r,1)}return n},e})),t.define("select2/dropdown/infiniteScroll",["jquery"],(function(e){function t(e,t,n,r){this.lastParams={},e.call(this,t,n,r),this.$loadingMore=this.createLoadingMore(),this.loading=!1}return t.prototype.append=function(e,t){this.$loadingMore.remove(),this.loading=!1,e.call(this,t),this.showLoadingMore(t)&&(this.$results.append(this.$loadingMore),this.loadMoreIfNeeded())},t.prototype.bind=function(e,t,n){var r=this;e.call(this,t,n),t.on("query",(function(e){r.lastParams=e,r.loading=!0})),t.on("query:append",(function(e){r.lastParams=e,r.loading=!0})),this.$results.on("scroll",this.loadMoreIfNeeded.bind(this))},t.prototype.loadMoreIfNeeded=function(){var t=e.contains(document.documentElement,this.$loadingMore[0]);!this.loading&&t&&this.$results.offset().top+this.$results.outerHeight(!1)+50>=this.$loadingMore.offset().top+this.$loadingMore.outerHeight(!1)&&this.loadMore()},t.prototype.loadMore=function(){this.loading=!0;var t=e.extend({},{page:1},this.lastParams);t.page++,this.trigger("query:append",t)},t.prototype.showLoadingMore=function(e,t){return t.pagination&&t.pagination.more},t.prototype.createLoadingMore=function(){var t=e('<li class="select2-results__option select2-results__option--load-more"role="option" aria-disabled="true"></li>'),n=this.options.get("translations").get("loadingMore");return t.html(n(this.lastParams)),t},t})),t.define("select2/dropdown/attachBody",["jquery","../utils"],(function(e,t){function n(t,n,r){this.$dropdownParent=e(r.get("dropdownParent")||document.body),t.call(this,n,r)}return n.prototype.bind=function(e,t,n){var r=this;e.call(this,t,n),t.on("open",(function(){r._showDropdown(),r._attachPositioningHandler(t),r._bindContainerResultHandlers(t)})),t.on("close",(function(){r._hideDropdown(),r._detachPositioningHandler(t)})),this.$dropdownContainer.on("mousedown",(function(e){e.stopPropagation()}))},n.prototype.destroy=function(e){e.call(this),this.$dropdownContainer.remove()},n.prototype.position=function(e,t,n){t.attr("class",n.attr("class")),t[0].classList.remove("select2"),t[0].classList.add("select2-container--open"),t.css({position:"absolute",top:-999999}),this.$container=n},n.prototype.render=function(t){var n=e("<span></span>"),r=t.call(this);return n.append(r),this.$dropdownContainer=n,n},n.prototype._hideDropdown=function(e){this.$dropdownContainer.detach()},n.prototype._bindContainerResultHandlers=function(e,t){if(!this._containerResultsHandlersBound){var n=this;t.on("results:all",(function(){n._positionDropdown(),n._resizeDropdown()})),t.on("results:append",(function(){n._positionDropdown(),n._resizeDropdown()})),t.on("results:message",(function(){n._positionDropdown(),n._resizeDropdown()})),t.on("select",(function(){n._positionDropdown(),n._resizeDropdown()})),t.on("unselect",(function(){n._positionDropdown(),n._resizeDropdown()})),this._containerResultsHandlersBound=!0}},n.prototype._attachPositioningHandler=function(n,r){var o=this,i="scroll.select2."+r.id,s="resize.select2."+r.id,a="orientationchange.select2."+r.id,l=this.$container.parents().filter(t.hasScroll);l.each((function(){t.StoreData(this,"select2-scroll-position",{x:e(this).scrollLeft(),y:e(this).scrollTop()})})),l.on(i,(function(n){var r=t.GetData(this,"select2-scroll-position");e(this).scrollTop(r.y)})),e(window).on(i+" "+s+" "+a,(function(e){o._positionDropdown(),o._resizeDropdown()}))},n.prototype._detachPositioningHandler=function(n,r){var o="scroll.select2."+r.id,i="resize.select2."+r.id,s="orientationchange.select2."+r.id;this.$container.parents().filter(t.hasScroll).off(o),e(window).off(o+" "+i+" "+s)},n.prototype._positionDropdown=function(){var t=e(window),n=this.$dropdown[0].classList.contains("select2-dropdown--above"),r=this.$dropdown[0].classList.contains("select2-dropdown--below"),o=null,i=this.$container.offset();i.bottom=i.top+this.$container.outerHeight(!1);var s={height:this.$container.outerHeight(!1)};s.top=i.top,s.bottom=i.top+s.height;var a={height:this.$dropdown.outerHeight(!1)},l={top:t.scrollTop(),bottom:t.scrollTop()+t.height()},c=l.top<i.top-a.height,u=l.bottom>i.bottom+a.height,d={left:i.left,top:s.bottom},p=this.$dropdownParent;"static"===p.css("position")&&(p=p.offsetParent());var h={top:0,left:0};(e.contains(document.body,p[0])||p[0].isConnected)&&(h=p.offset()),d.top-=h.top,d.left-=h.left,n||r||(o="below"),u||!c||n?!c&&u&&n&&(o="below"):o="above",("above"==o||n&&"below"!==o)&&(d.top=s.top-h.top-a.height),null!=o&&(this.$dropdown[0].classList.remove("select2-dropdown--below"),this.$dropdown[0].classList.remove("select2-dropdown--above"),this.$dropdown[0].classList.add("select2-dropdown--"+o),this.$container[0].classList.remove("select2-container--below"),this.$container[0].classList.remove("select2-container--above"),this.$container[0].classList.add("select2-container--"+o)),this.$dropdownContainer.css(d)},n.prototype._resizeDropdown=function(){var e={width:this.$container.outerWidth(!1)+"px"};this.options.get("dropdownAutoWidth")&&(e.minWidth=e.width,e.position="relative",e.width="auto"),this.$dropdown.css(e)},n.prototype._showDropdown=function(e){this.$dropdownContainer.appendTo(this.$dropdownParent),this._positionDropdown(),this._resizeDropdown()},n})),t.define("select2/dropdown/minimumResultsForSearch",[],(function(){function e(t){for(var n=0,r=0;r<t.length;r++){var o=t[r];o.children?n+=e(o.children):n++}return n}function t(e,t,n,r){this.minimumResultsForSearch=n.get("minimumResultsForSearch"),this.minimumResultsForSearch<0&&(this.minimumResultsForSearch=1/0),e.call(this,t,n,r)}return t.prototype.showSearch=function(t,n){return!(e(n.data.results)<this.minimumResultsForSearch)&&t.call(this,n)},t})),t.define("select2/dropdown/selectOnClose",["../utils"],(function(e){function t(){}return t.prototype.bind=function(e,t,n){var r=this;e.call(this,t,n),t.on("close",(function(e){r._handleSelectOnClose(e)}))},t.prototype._handleSelectOnClose=function(t,n){if(n&&null!=n.originalSelect2Event){var r=n.originalSelect2Event;if("select"===r._type||"unselect"===r._type)return}var o=this.getHighlightedResults();if(!(o.length<1)){var i=e.GetData(o[0],"data");null!=i.element&&i.element.selected||null==i.element&&i.selected||this.trigger("select",{data:i})}},t})),t.define("select2/dropdown/closeOnSelect",[],(function(){function e(){}return e.prototype.bind=function(e,t,n){var r=this;e.call(this,t,n),t.on("select",(function(e){r._selectTriggered(e)})),t.on("unselect",(function(e){r._selectTriggered(e)}))},e.prototype._selectTriggered=function(e,t){var n=t.originalEvent;n&&(n.ctrlKey||n.metaKey)||this.trigger("close",{originalEvent:n,originalSelect2Event:t})},e})),t.define("select2/dropdown/dropdownCss",["../utils"],(function(e){function t(){}return t.prototype.render=function(t){var n=t.call(this),r=this.options.get("dropdownCssClass")||"";return-1!==r.indexOf(":all:")&&(r=r.replace(":all:",""),e.copyNonInternalCssClasses(n[0],this.$element[0])),n.addClass(r),n},t})),t.define("select2/dropdown/tagsSearchHighlight",["../utils"],(function(e){function t(){}return t.prototype.highlightFirstItem=function(t){var n=this.$results.find(".select2-results__option--selectable:not(.select2-results__option--selected)");if(n.length>0){var r=n.first(),o=e.GetData(r[0],"data").element;if(o&&o.getAttribute&&"true"===o.getAttribute("data-select2-tag"))return void r.trigger("mouseenter")}t.call(this)},t})),t.define("select2/i18n/en",[],(function(){return{errorLoading:function(){return"The results could not be loaded."},inputTooLong:function(e){var t=e.input.length-e.maximum,n="Please delete "+t+" character";return 1!=t&&(n+="s"),n},inputTooShort:function(e){return"Please enter "+(e.minimum-e.input.length)+" or more characters"},loadingMore:function(){return"Loading more results…"},maximumSelected:function(e){var t="You can only select "+e.maximum+" item";return 1!=e.maximum&&(t+="s"),t},noResults:function(){return"No results found"},searching:function(){return"Searching…"},removeAllItems:function(){return"Remove all items"},removeItem:function(){return"Remove item"},search:function(){return"Search"}}})),t.define("select2/defaults",["jquery","./results","./selection/single","./selection/multiple","./selection/placeholder","./selection/allowClear","./selection/search","./selection/selectionCss","./selection/eventRelay","./utils","./translation","./diacritics","./data/select","./data/array","./data/ajax","./data/tags","./data/tokenizer","./data/minimumInputLength","./data/maximumInputLength","./data/maximumSelectionLength","./dropdown","./dropdown/search","./dropdown/hidePlaceholder","./dropdown/infiniteScroll","./dropdown/attachBody","./dropdown/minimumResultsForSearch","./dropdown/selectOnClose","./dropdown/closeOnSelect","./dropdown/dropdownCss","./dropdown/tagsSearchHighlight","./i18n/en"],(function(e,t,n,r,o,i,s,a,l,c,u,d,p,h,f,m,g,y,v,b,_,x,w,j,S,$,O,A,C,E,D){function k(){this.reset()}return k.prototype.apply=function(u){if(null==(u=e.extend(!0,{},this.defaults,u)).dataAdapter&&(null!=u.ajax?u.dataAdapter=f:null!=u.data?u.dataAdapter=h:u.dataAdapter=p,u.minimumInputLength>0&&(u.dataAdapter=c.Decorate(u.dataAdapter,y)),u.maximumInputLength>0&&(u.dataAdapter=c.Decorate(u.dataAdapter,v)),u.maximumSelectionLength>0&&(u.dataAdapter=c.Decorate(u.dataAdapter,b)),u.tags&&(u.dataAdapter=c.Decorate(u.dataAdapter,m)),null==u.tokenSeparators&&null==u.tokenizer||(u.dataAdapter=c.Decorate(u.dataAdapter,g))),null==u.resultsAdapter&&(u.resultsAdapter=t,null!=u.ajax&&(u.resultsAdapter=c.Decorate(u.resultsAdapter,j)),null!=u.placeholder&&(u.resultsAdapter=c.Decorate(u.resultsAdapter,w)),u.selectOnClose&&(u.resultsAdapter=c.Decorate(u.resultsAdapter,O)),u.tags&&(u.resultsAdapter=c.Decorate(u.resultsAdapter,E))),null==u.dropdownAdapter){if(u.multiple)u.dropdownAdapter=_;else{var d=c.Decorate(_,x);u.dropdownAdapter=d}0!==u.minimumResultsForSearch&&(u.dropdownAdapter=c.Decorate(u.dropdownAdapter,$)),u.closeOnSelect&&(u.dropdownAdapter=c.Decorate(u.dropdownAdapter,A)),null!=u.dropdownCssClass&&(u.dropdownAdapter=c.Decorate(u.dropdownAdapter,C)),u.dropdownAdapter=c.Decorate(u.dropdownAdapter,S)}null==u.selectionAdapter&&(u.multiple?u.selectionAdapter=r:u.selectionAdapter=n,null!=u.placeholder&&(u.selectionAdapter=c.Decorate(u.selectionAdapter,o)),u.allowClear&&(u.selectionAdapter=c.Decorate(u.selectionAdapter,i)),u.multiple&&(u.selectionAdapter=c.Decorate(u.selectionAdapter,s)),null!=u.selectionCssClass&&(u.selectionAdapter=c.Decorate(u.selectionAdapter,a)),u.selectionAdapter=c.Decorate(u.selectionAdapter,l)),u.language=this._resolveLanguage(u.language),u.language.push("en");for(var D=[],k=0;k<u.language.length;k++){var T=u.language[k];-1===D.indexOf(T)&&D.push(T)}return u.language=D,u.translations=this._processTranslations(u.language,u.debug),u},k.prototype.reset=function(){function t(e){function t(e){return d[e]||e}return e.replace(/[^\u0000-\u007E]/g,t)}function n(r,o){if(null==r.term||""===r.term.trim())return o;if(o.children&&o.children.length>0){for(var i=e.extend(!0,{},o),s=o.children.length-1;s>=0;s--)null==n(r,o.children[s])&&i.children.splice(s,1);return i.children.length>0?i:n(r,i)}var a=t(o.text).toUpperCase(),l=t(r.term).toUpperCase();return a.indexOf(l)>-1?o:null}this.defaults={amdLanguageBase:"./i18n/",autocomplete:"off",closeOnSelect:!0,debug:!1,dropdownAutoWidth:!1,escapeMarkup:c.escapeMarkup,language:{},matcher:n,minimumInputLength:0,maximumInputLength:0,maximumSelectionLength:0,minimumResultsForSearch:0,selectOnClose:!1,scrollAfterSelect:!1,sorter:function(e){return e},templateResult:function(e){return e.text},templateSelection:function(e){return e.text},theme:"default",width:"resolve"}},k.prototype.applyFromElement=function(e,t){var n=e.language,r=this.defaults.language,o=t.prop("lang"),i=t.closest("[lang]").prop("lang"),s=Array.prototype.concat.call(this._resolveLanguage(o),this._resolveLanguage(n),this._resolveLanguage(r),this._resolveLanguage(i));return e.language=s,e},k.prototype._resolveLanguage=function(t){if(!t)return[];if(e.isEmptyObject(t))return[];if(e.isPlainObject(t))return[t];var n;n=Array.isArray(t)?t:[t];for(var r=[],o=0;o<n.length;o++)if(r.push(n[o]),"string"==typeof n[o]&&n[o].indexOf("-")>0){var i=n[o].split("-")[0];r.push(i)}return r},k.prototype._processTranslations=function(t,n){for(var r=new u,o=0;o<t.length;o++){var i=new u,s=t[o];if("string"==typeof s)try{i=u.loadPath(s)}catch(e){try{s=this.defaults.amdLanguageBase+s,i=u.loadPath(s)}catch(e){n&&window.console&&console.warn&&console.warn('Select2: The language file for "'+s+'" could not be automatically loaded. A fallback will be used instead.')}}else i=e.isPlainObject(s)?new u(s):s;r.extend(i)}return r},k.prototype.set=function(t,n){var r={};r[e.camelCase(t)]=n;var o=c._convertData(r);e.extend(!0,this.defaults,o)},new k})),t.define("select2/options",["jquery","./defaults","./utils"],(function(e,t,n){function r(e,n){this.options=e,null!=n&&this.fromElement(n),null!=n&&(this.options=t.applyFromElement(this.options,n)),this.options=t.apply(this.options)}return r.prototype.fromElement=function(t){var r=["select2"];null==this.options.multiple&&(this.options.multiple=t.prop("multiple")),null==this.options.disabled&&(this.options.disabled=t.prop("disabled")),null==this.options.autocomplete&&t.prop("autocomplete")&&(this.options.autocomplete=t.prop("autocomplete")),null==this.options.dir&&(t.prop("dir")?this.options.dir=t.prop("dir"):t.closest("[dir]").prop("dir")?this.options.dir=t.closest("[dir]").prop("dir"):this.options.dir="ltr"),t.prop("disabled",this.options.disabled),t.prop("multiple",this.options.multiple),n.GetData(t[0],"select2Tags")&&(this.options.debug&&window.console&&console.warn&&console.warn('Select2: The `data-select2-tags` attribute has been changed to use the `data-data` and `data-tags="true"` attributes and will be removed in future versions of Select2.'),n.StoreData(t[0],"data",n.GetData(t[0],"select2Tags")),n.StoreData(t[0],"tags",!0)),n.GetData(t[0],"ajaxUrl")&&(this.options.debug&&window.console&&console.warn&&console.warn("Select2: The `data-ajax-url` attribute has been changed to `data-ajax--url` and support for the old attribute will be removed in future versions of Select2."),t.attr("ajax--url",n.GetData(t[0],"ajaxUrl")),n.StoreData(t[0],"ajax-Url",n.GetData(t[0],"ajaxUrl")));var o={};function i(e,t){return t.toUpperCase()}for(var s=0;s<t[0].attributes.length;s++){var a=t[0].attributes[s].name,l="data-";if(a.substr(0,l.length)==l){var c=a.substring(l.length),u=n.GetData(t[0],c);o[c.replace(/-([a-z])/g,i)]=u}}e.fn.jquery&&"1."==e.fn.jquery.substr(0,2)&&t[0].dataset&&(o=e.extend(!0,{},t[0].dataset,o));var d=e.extend(!0,{},n.GetData(t[0]),o);for(var p in d=n._convertData(d))r.indexOf(p)>-1||(e.isPlainObject(this.options[p])?e.extend(this.options[p],d[p]):this.options[p]=d[p]);return this},r.prototype.get=function(e){return this.options[e]},r.prototype.set=function(e,t){this.options[e]=t},r})),t.define("select2/core",["jquery","./options","./utils","./keys"],(function(e,t,n,r){var o=function(e,r){null!=n.GetData(e[0],"select2")&&n.GetData(e[0],"select2").destroy(),this.$element=e,this.id=this._generateId(e),r=r||{},this.options=new t(r,e),o.__super__.constructor.call(this);var i=e.attr("tabindex")||0;n.StoreData(e[0],"old-tabindex",i),e.attr("tabindex","-1");var s=this.options.get("dataAdapter");this.dataAdapter=new s(e,this.options);var a=this.render();this._placeContainer(a);var l=this.options.get("selectionAdapter");this.selection=new l(e,this.options),this.$selection=this.selection.render(),this.selection.position(this.$selection,a);var c=this.options.get("dropdownAdapter");this.dropdown=new c(e,this.options),this.$dropdown=this.dropdown.render(),this.dropdown.position(this.$dropdown,a);var u=this.options.get("resultsAdapter");this.results=new u(e,this.options,this.dataAdapter),this.$results=this.results.render(),this.results.position(this.$results,this.$dropdown);var d=this;this._bindAdapters(),this._registerDomEvents(),this._registerDataEvents(),this._registerSelectionEvents(),this._registerDropdownEvents(),this._registerResultsEvents(),this._registerEvents(),this.dataAdapter.current((function(e){d.trigger("selection:update",{data:e})})),e[0].classList.add("select2-hidden-accessible"),e.attr("aria-hidden","true"),this._syncAttributes(),n.StoreData(e[0],"select2",this),e.data("select2",this)};return n.Extend(o,n.Observable),o.prototype._generateId=function(e){return"select2-"+(null!=e.attr("id")?e.attr("id"):null!=e.attr("name")?e.attr("name")+"-"+n.generateChars(2):n.generateChars(4)).replace(/(:|\.|\[|\]|,)/g,"")},o.prototype._placeContainer=function(e){e.insertAfter(this.$element);var t=this._resolveWidth(this.$element,this.options.get("width"));null!=t&&e.css("width",t)},o.prototype._resolveWidth=function(e,t){var n=/^width:(([-+]?([0-9]*\.)?[0-9]+)(px|em|ex|%|in|cm|mm|pt|pc))/i;if("resolve"==t){var r=this._resolveWidth(e,"style");return null!=r?r:this._resolveWidth(e,"element")}if("element"==t){var o=e.outerWidth(!1);return o<=0?"auto":o+"px"}if("style"==t){var i=e.attr("style");if("string"!=typeof i)return null;for(var s=i.split(";"),a=0,l=s.length;a<l;a+=1){var c=s[a].replace(/\s/g,"").match(n);if(null!==c&&c.length>=1)return c[1]}return null}return"computedstyle"==t?window.getComputedStyle(e[0]).width:t},o.prototype._bindAdapters=function(){this.dataAdapter.bind(this,this.$container),this.selection.bind(this,this.$container),this.dropdown.bind(this,this.$container),this.results.bind(this,this.$container)},o.prototype._registerDomEvents=function(){var e=this;this.$element.on("change.select2",(function(){e.dataAdapter.current((function(t){e.trigger("selection:update",{data:t})}))})),this.$element.on("focus.select2",(function(t){e.trigger("focus",t)})),this._syncA=n.bind(this._syncAttributes,this),this._syncS=n.bind(this._syncSubtree,this),this._observer=new window.MutationObserver((function(t){e._syncA(),e._syncS(t)})),this._observer.observe(this.$element[0],{attributes:!0,childList:!0,subtree:!1})},o.prototype._registerDataEvents=function(){var e=this;this.dataAdapter.on("*",(function(t,n){e.trigger(t,n)}))},o.prototype._registerSelectionEvents=function(){var e=this,t=["toggle","focus"];this.selection.on("toggle",(function(){e.toggleDropdown()})),this.selection.on("focus",(function(t){e.focus(t)})),this.selection.on("*",(function(n,r){-1===t.indexOf(n)&&e.trigger(n,r)}))},o.prototype._registerDropdownEvents=function(){var e=this;this.dropdown.on("*",(function(t,n){e.trigger(t,n)}))},o.prototype._registerResultsEvents=function(){var e=this;this.results.on("*",(function(t,n){e.trigger(t,n)}))},o.prototype._registerEvents=function(){var e=this;this.on("open",(function(){e.$container[0].classList.add("select2-container--open")})),this.on("close",(function(){e.$container[0].classList.remove("select2-container--open")})),this.on("enable",(function(){e.$container[0].classList.remove("select2-container--disabled")})),this.on("disable",(function(){e.$container[0].classList.add("select2-container--disabled")})),this.on("blur",(function(){e.$container[0].classList.remove("select2-container--focus")})),this.on("query",(function(t){e.isOpen()||e.trigger("open",{}),this.dataAdapter.query(t,(function(n){e.trigger("results:all",{data:n,query:t})}))})),this.on("query:append",(function(t){this.dataAdapter.query(t,(function(n){e.trigger("results:append",{data:n,query:t})}))})),this.on("keypress",(function(t){var n=t.which;e.isOpen()?n===r.ESC||n===r.UP&&t.altKey?(e.close(t),t.preventDefault()):n===r.ENTER||n===r.TAB?(e.trigger("results:select",{}),t.preventDefault()):n===r.SPACE&&t.ctrlKey?(e.trigger("results:toggle",{}),t.preventDefault()):n===r.UP?(e.trigger("results:previous",{}),t.preventDefault()):n===r.DOWN&&(e.trigger("results:next",{}),t.preventDefault()):(n===r.ENTER||n===r.SPACE||n===r.DOWN&&t.altKey)&&(e.open(),t.preventDefault())}))},o.prototype._syncAttributes=function(){this.options.set("disabled",this.$element.prop("disabled")),this.isDisabled()?(this.isOpen()&&this.close(),this.trigger("disable",{})):this.trigger("enable",{})},o.prototype._isChangeMutation=function(e){var t=this;if(e.addedNodes&&e.addedNodes.length>0){for(var n=0;n<e.addedNodes.length;n++)if(e.addedNodes[n].selected)return!0}else{if(e.removedNodes&&e.removedNodes.length>0)return!0;if(Array.isArray(e))return e.some((function(e){return t._isChangeMutation(e)}))}return!1},o.prototype._syncSubtree=function(e){var t=this._isChangeMutation(e),n=this;t&&this.dataAdapter.current((function(e){n.trigger("selection:update",{data:e})}))},o.prototype.trigger=function(e,t){var n=o.__super__.trigger,r={open:"opening",close:"closing",select:"selecting",unselect:"unselecting",clear:"clearing"};if(void 0===t&&(t={}),e in r){var i=r[e],s={prevented:!1,name:e,args:t};if(n.call(this,i,s),s.prevented)return void(t.prevented=!0)}n.call(this,e,t)},o.prototype.toggleDropdown=function(){this.isDisabled()||(this.isOpen()?this.close():this.open())},o.prototype.open=function(){this.isOpen()||this.isDisabled()||this.trigger("query",{})},o.prototype.close=function(e){this.isOpen()&&this.trigger("close",{originalEvent:e})},o.prototype.isEnabled=function(){return!this.isDisabled()},o.prototype.isDisabled=function(){return this.options.get("disabled")},o.prototype.isOpen=function(){return this.$container[0].classList.contains("select2-container--open")},o.prototype.hasFocus=function(){return this.$container[0].classList.contains("select2-container--focus")},o.prototype.focus=function(e){this.hasFocus()||(this.$container[0].classList.add("select2-container--focus"),this.trigger("focus",{}))},o.prototype.enable=function(e){this.options.get("debug")&&window.console&&console.warn&&console.warn('Select2: The `select2("enable")` method has been deprecated and will be removed in later Select2 versions. Use $element.prop("disabled") instead.'),null!=e&&0!==e.length||(e=[!0]);var t=!e[0];this.$element.prop("disabled",t)},o.prototype.data=function(){this.options.get("debug")&&arguments.length>0&&window.console&&console.warn&&console.warn('Select2: Data can no longer be set using `select2("data")`. You should consider setting the value instead using `$element.val()`.');var e=[];return this.dataAdapter.current((function(t){e=t})),e},o.prototype.val=function(e){if(this.options.get("debug")&&window.console&&console.warn&&console.warn('Select2: The `select2("val")` method has been deprecated and will be removed in later Select2 versions. Use $element.val() instead.'),null==e||0===e.length)return this.$element.val();var t=e[0];Array.isArray(t)&&(t=t.map((function(e){return e.toString()}))),this.$element.val(t).trigger("input").trigger("change")},o.prototype.destroy=function(){n.RemoveData(this.$container[0]),this.$container.remove(),this._observer.disconnect(),this._observer=null,this._syncA=null,this._syncS=null,this.$element.off(".select2"),this.$element.attr("tabindex",n.GetData(this.$element[0],"old-tabindex")),this.$element[0].classList.remove("select2-hidden-accessible"),this.$element.attr("aria-hidden","false"),n.RemoveData(this.$element[0]),this.$element.removeData("select2"),this.dataAdapter.destroy(),this.selection.destroy(),this.dropdown.destroy(),this.results.destroy(),this.dataAdapter=null,this.selection=null,this.dropdown=null,this.results=null},o.prototype.render=function(){var t=e('<span class="select2 select2-container"><span class="selection"></span><span class="dropdown-wrapper" aria-hidden="true"></span></span>');return t.attr("dir",this.options.get("dir")),this.$container=t,this.$container[0].classList.add("select2-container--"+this.options.get("theme")),n.StoreData(t[0],"element",this.$element),t},o})),t.define("jquery-mousewheel",["jquery"],(function(e){return e})),t.define("jquery.select2",["jquery","jquery-mousewheel","./select2/core","./select2/defaults","./select2/utils"],(function(e,t,n,r,o){if(null==e.fn.select2){var i=["open","close","destroy"];e.fn.select2=function(t){if("object"==typeof(t=t||{}))return this.each((function(){var r=e.extend(!0,{},t);new n(e(this),r)})),this;if("string"==typeof t){var r,s=Array.prototype.slice.call(arguments,1);return this.each((function(){var e=o.GetData(this,"select2");null==e&&window.console&&console.error&&console.error("The select2('"+t+"') method was called on an element that is not using Select2."),r=e[t].apply(e,s)})),i.indexOf(t)>-1?this:r}throw new Error("Invalid arguments for Select2: "+t)}}return null==e.fn.select2.defaults&&(e.fn.select2.defaults=r),n})),{define:t.define,require:t.require}}(),n=t.require("jquery.select2");return e.fn.select2.amd=t,n})?r.apply(t,o):r)||(e.exports=i)},540:(e,t,n)=>{"use strict";e.exports=n(287)},669:e=>{"use strict";e.exports=jQuery},848:(e,t,n)=>{"use strict";e.exports=n(20)},875:e=>{var t=Object.prototype.toString;function n(e){return"[object String]"===t.call(e)}function r(e){return"[object Number]"===t.call(e)}var o=Object.prototype.hasOwnProperty;e.exports=function e(t,i){if((s=t)==(a=i)&&(n(s)||r(s))&&(n(a)||r(a))||function(e,t){return e===t?0!==e||1/e==1/t:e!=e&&t!=t}(s,a))return!0;var s,a;if("object"!=typeof t||null===t||"object"!=typeof i||null===i)return!1;if(t instanceof Array&&i instanceof Array){if(t.length!==i.length)return!1;for(var l,c=t.length,u=new Array(c),d=0;d<c;d++)if(e(l=t[d],i[d]))u[d]=!0;else{for(var p=!1,h=0,f=i.length;h<f;h++)if(!u[h]&&e(l,i[h])){u[h]=!0,p=!0;break}if(!p)return!1}return!0}var m=Object.keys(t),g=Object.keys(i);if(m.length!==g.length)return!1;for(var y=0;y<m.length;y++)if(!o.call(i,m[y])||!e(t[m[y]],i[m[y]]))return!1;return!0}},942:(e,t)=>{var n;!function(){"use strict";var r={}.hasOwnProperty;function o(){for(var e="",t=0;t<arguments.length;t++){var n=arguments[t];n&&(e=s(e,i(n)))}return e}function i(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return o.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var n in e)r.call(e,n)&&e[n]&&(t=s(t,n));return t}function s(e,t){return t?e?e+" "+t:e+t:e}e.exports?(o.default=o,e.exports=o):void 0===(n=function(){return o}.apply(t,[]))||(e.exports=n)}()}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var i=t[r]={exports:{}};return e[r](i,i.exports,n),i.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";const e=wp.domReady;var t=n.n(e);const r=wp.element;var o=n(669),i=n.n(o),s=n(942),a=n.n(s);function l(e){return l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},l(e)}function c(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function u(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?c(Object(n),!0).forEach((function(t){d(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):c(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function d(e,t,n){return(t=function(e){var t=function(e,t){if("object"!=l(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=l(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==l(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function p(){var e=advancedAds.oneclick,t=e.options;return{count:0,isConnected:e.isConnected,showMetabox:e.isConnected,currentStep:e.isConnected?3:1,settings:u({},advancedAds.oneclick.options),selectedMethod:t.selectedMethod,selectedPage:t.selectedPage,selectedPageTitle:t.selectedPageTitle}}function h(e){return h="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},h(e)}function f(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function m(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?f(Object(n),!0).forEach((function(t){g(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):f(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function g(e,t,n){return(t=function(e){var t=function(e,t){if("object"!=h(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=h(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==h(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function y(e,t){switch(t.type){case"SET_COUNT":return m(m({},e),{},{count:t.value});case"TOGGLE_METABOX":return m(m({},e),{},{showMetabox:t.value});case"SET_STEP":return m(m({},e),{},{currentStep:t.value});case"UPDATE_SETTINGS":return m(m({},e),{},{settings:m(m({},e.settings),{},g({},t.name,t.value))});case"SET_METHOD":return m(m({},e),{},{selectedMethod:t.value});case"SET_PAGE":return m(m({},e),{},{selectedPage:t.value});case"DISCONNECT":return m(m({},e),{},{isConnected:!1,showMetabox:!1,currentStep:1});case"CONNECTED":return m(m({},e),{},{isConnected:!0,showMetabox:!0,currentStep:3});default:return e}}var v=n(848);function b(e){return b="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},b(e)}function _(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function x(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?_(Object(n),!0).forEach((function(t){w(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):_(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function w(e,t,n){return(t=function(e){var t=function(e,t){if("object"!=b(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=b(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==b(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function j(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,s,a=[],l=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=i.call(n)).done)&&(a.push(r.value),a.length!==t);l=!0);}catch(e){c=!0,o=e}finally{try{if(!l&&null!=n.return&&(s=n.return(),Object(s)!==s))return}finally{if(c)throw o}}return a}}(e,t)||function(e,t){if(e){if("string"==typeof e)return S(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?S(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function S(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}var $=(0,r.createContext)(p());function O(e){var t=e.children,n=j((0,r.useReducer)(y,x({},p())),2),o=n[0],i=n[1],s=x(x({},o),{},{dispatch:i},function(e,t){return{connected:function(){t({type:"CONNECTED"})},disconnect:function(){t({type:"DISCONNECT"})},setMethod:function(e){t({type:"SET_METHOD",value:e})},setPage:function(e){t({type:"SET_PAGE",value:e})},setStep:function(e){t({type:"SET_STEP",value:e})},toggleMetabox:function(e){t({type:"TOGGLE_METABOX",value:e})},updateSettings:function(e,n){t({type:"UPDATE_SETTINGS",name:e,value:n})}}}(0,i));return(0,v.jsx)($.Provider,{value:s,children:t})}const A=wp.notices,C=wp.data;function E(e){return E="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},E(e)}function D(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function k(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?D(Object(n),!0).forEach((function(t){T(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):D(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function T(e,t,n){return(t=function(e){var t=function(e,t){if("object"!=E(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=E(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==E(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function P(e,t){(0,C.dispatch)(A.store).createSuccessNotice(e,t)}function N(e,t){(0,C.dispatch)(A.store).createErrorNotice(e,t)}function L(e){var t=e.status,n=e.isDismissible,o=e.actions,i=e.onRemove,s=e.children,l=e.type,c=a()("flex items-center justify-between notice notice-alt !px-3","notice-".concat(t),{"is-dismissible":n});return(0,r.useEffect)((function(){if("timeout"===l){var e=setTimeout((function(){i()}),5e3);return function(){return clearTimeout(e)}}}),[]),(0,v.jsxs)("div",{className:c,children:[(0,v.jsx)("div",{className:"py-3",dangerouslySetInnerHTML:{__html:s}}),o.map((function(e,t){return(0,v.jsx)("button",{className:"button button-primary !ml-auto !mr-2",onClick:function(t){return e.onClick(t,i)},children:e.label},t)})),n&&(0,v.jsx)("button",{className:"button-link !no-underline",onClick:i,children:(0,v.jsx)("span",{className:"dashicons dashicons-no-alt"})})]})}function I(){var e=(0,C.useSelect)((function(e){return e(A.store).getNotices()}),[]),t=(0,C.useDispatch)(A.store).removeNotice;return(0,v.jsx)(v.Fragment,{children:e.map((function(e){return(0,v.jsx)(L,k(k({onRemove:function(){return t(e.id)}},e),{},{children:e.content}),e.id)}))})}function R(){var e=advancedAds.oneclick.addonRow,t=(0,r.useContext)($),n=t.isConnected,o=t.toggleMetabox,s=t.disconnect,l=a()("cta",{primary:!n,secondary:n});return(0,v.jsx)("div",{className:"single-item add-on js-pubguru-connect",children:(0,v.jsxs)("div",{className:"item-details",children:[(0,v.jsx)("div",{className:"icon",children:(0,v.jsx)("img",{src:e.icon,alt:""})}),(0,v.jsx)("span",{}),(0,v.jsx)("div",{className:"name",children:e.title}),(0,v.jsx)("span",{}),(0,v.jsx)("div",{className:"description",children:e.content}),(0,v.jsx)("span",{}),(0,v.jsx)("div",{className:l,children:n?(0,v.jsxs)("button",{className:"button",onClick:function(){i().ajax({url:advancedAds.endpoints.ajaxUrl,type:"POST",data:{action:"pubguru_disconnect",nonce:advancedAds.oneclick.security},success:function(e){e.success?(P(e.data.message),s()):N(e.data.message)},error:function(e){N("Error disconnecting: "+e.statusText)}})},children:[(0,v.jsx)("i",{className:"dashicons dashicons-dismiss"}),e.disconnect]}):(0,v.jsxs)("button",{className:"button",onClick:function(){(0,C.dispatch)(A.store).removeAllNotices(),o(!0)},children:[(0,v.jsx)("i",{className:"dashicons dashicons-plus"}),e.connect]})})]})})}function M(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,s,a=[],l=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=i.call(n)).done)&&(a.push(r.value),a.length!==t);l=!0);}catch(e){c=!0,o=e}finally{try{if(!l&&null!=n.return&&(s=n.return(),Object(s)!==s))return}finally{if(c)throw o}}return a}}(e,t)||function(e,t){if(e){if("string"==typeof e)return q(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?q(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function q(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function U(){var e=M((0,r.useState)(!1),2),t=e[0],n=e[1],o=(0,r.useContext)($),i=o.setStep,s=o.toggleMetabox,a=advancedAds.oneclick.step1;return(0,v.jsxs)("div",{className:"mt-6 mb-8",children:[(0,v.jsx)("p",{dangerouslySetInnerHTML:{__html:a.content}}),(0,v.jsx)("p",{children:(0,v.jsxs)("label",{htmlFor:"consent",children:[(0,v.jsx)("input",{type:"checkbox",id:"consent",onClick:function(){return n(!t)}}),(0,v.jsx)("span",{children:a.agreeText})]})}),(0,v.jsxs)("p",{className:"buttons-set",children:[(0,v.jsx)("button",{className:"button button-primary",disabled:!t,onClick:function(){i(2)},children:a.btnAgree}),(0,v.jsx)("button",{className:"button",onClick:function(){n(!1),s(!1),i(1)},children:advancedAds.oneclick.btnCancel})]})]})}const H=wp.htmlEntities;function G(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,s,a=[],l=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=i.call(n)).done)&&(a.push(r.value),a.length!==t);l=!0);}catch(e){c=!0,o=e}finally{try{if(!l&&null!=n.return&&(s=n.return(),Object(s)!==s))return}finally{if(c)throw o}}return a}}(e,t)||function(e,t){if(e){if("string"==typeof e)return z(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?z(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function z(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function F(){var e=advancedAds.oneclick.step2;return(0,v.jsxs)("div",{className:"mt-6 mb-8",children:[(0,v.jsx)("p",{children:e.loading}),(0,v.jsx)("p",{children:(0,v.jsx)("img",{src:advancedAds.oneclick.spinner,alt:""})})]})}function B(e){var t=e.domain,n=e.onCancel,r=e.setDomain,o=e.setFetched,i=advancedAds.oneclick.step2;return(0,v.jsxs)("div",{className:"mt-6 mb-8",children:[(0,v.jsx)("p",{className:"step-error",children:String.format((0,H.decodeEntities)(i.notRegistered),advancedAds.oneclick.siteDomain)}),(0,v.jsx)("p",{dangerouslySetInnerHTML:{__html:i.content}}),(0,v.jsxs)("p",{children:[(0,v.jsx)("strong",{children:i.inputLabel})," ",(0,v.jsx)("input",{type:"text",value:t,onChange:function(e){return r(e.target.value)}})]}),(0,v.jsxs)("p",{className:"buttons-set",children:[(0,v.jsx)("button",{className:"button button-primary",disabled:""===t,onClick:function(){return o(!1)},children:advancedAds.oneclick.btnContinue}),(0,v.jsx)("button",{className:"button",onClick:n,children:advancedAds.oneclick.btnCancel})]})]})}function W(e){var t=e.error,n=e.onCancel,r=e.setFetched,o=advancedAds.oneclick.step2;return(0,v.jsxs)("div",{className:"mt-6 mb-8",children:[(0,v.jsx)("p",{className:"step-error",children:String.format((0,H.decodeEntities)(o.serverError),t)}),(0,v.jsx)("p",{dangerouslySetInnerHTML:{__html:o.serverContent}}),(0,v.jsxs)("p",{className:"buttons-set",children:[(0,v.jsx)("button",{className:"button button-primary",onClick:function(){return r(!1)},children:advancedAds.oneclick.btnRetry}),(0,v.jsx)("button",{className:"button",onClick:n,children:advancedAds.oneclick.btnCancel})]})]})}function Y(){var e=G((0,r.useState)(!1),2),t=e[0],n=e[1],o=G((0,r.useState)(""),2),s=o[0],a=o[1],l=G((0,r.useState)(""),2),c=l[0],u=l[1],d=(0,r.useContext)($),p=d.setStep,h=d.toggleMetabox,f=d.connected,m=function(){a(""),n(!1),h(!1),p(1)};return(0,r.useEffect)((function(){t||i().ajax({type:"POST",url:ajaxurl,data:{action:"pubguru_connect",nonce:advancedAds.oneclick.security,testDomain:s},dataType:"json"}).done((function(e){if(!e.success)return"connect_error"===e.code&&(n("server-error"),u(e.message)),void("domain_not_found"===e.code&&n("error"));advancedAds.oneclick.options.connectedDomain=""!==s?s:advancedAds.oneclick.siteDomain,f()})).fail((function(e){n("server-error"),u(e.statusText)}))}),[t]),t?"error"===t?(0,v.jsx)(B,{domain:s,onCancel:m,setDomain:a,setFetched:n}):"server-error"===t?(0,v.jsx)(W,{error:c,onCancel:m,setFetched:n}):"unknow error":(0,v.jsx)(F,{})}n(458);var K=n(875),V=n.n(K),Z=["defaultValue","value","data","events","options","multiple","onChange","onOpen","onClose","onSelect","onUnselect"],Q=["value","label"];function X(e){return X="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},X(e)}function J(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ee(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?J(Object(n),!0).forEach((function(t){te(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):J(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function te(e,t,n){return(t=function(e){var t=function(e,t){if("object"!=X(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=X(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==X(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ne(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,s,a=[],l=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=i.call(n)).done)&&(a.push(r.value),a.length!==t);l=!0);}catch(e){c=!0,o=e}finally{try{if(!l&&null!=n.return&&(s=n.return(),Object(s)!==s))return}finally{if(c)throw o}}return a}}(e,t)||function(e,t){if(e){if("string"==typeof e)return re(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?re(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function re(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function oe(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],-1===t.indexOf(n)&&{}.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}var ie="react-select2";const se=(0,r.forwardRef)((function(e,t){var n=e.defaultValue,o=void 0===n?"":n,s=e.value,a=e.data,l=void 0===a?[]:a,c=e.events,u=void 0===c?[["change.".concat(ie),"onChange"],["select2:open.".concat(ie),"onOpen"],["select2:close.".concat(ie),"onClose"],["select2:select.".concat(ie),"onSelect"],["select2:unselect.".concat(ie),"onUnselect"]]:c,d=e.options,p=void 0===d?{}:d,h=(e.multiple,e.onChange),f=e.onOpen,m=e.onClose,g=e.onSelect,y=e.onUnselect,b=oe(e,Z),_=t||(0,r.useRef)(null),x=ne((0,r.useState)(s||o),2),w=x[0];x[1];(0,r.useEffect)((function(){var e=i()(_.current);return e.select2(j(p)),S(e),O(e,w),function(){$(e),e.select2("destroy")}}),[]),(0,r.useEffect)((function(){var e=i()(_.current);e.select2(j(p)),void 0===s||A(e.val(),s)||O(e,s)}),[s,p]);var j=function(e){var t=ee({},e);return"string"==typeof t.dropdownParent&&(t.dropdownParent=i()(t.dropdownParent)),t},S=function(e){var t={onChange:h,onOpen:f,onClose:m,onSelect:g,onUnselect:y};u.forEach((function(n){var r=ne(n,2),o=r[0],i=r[1];t[i]&&e.on(o,t[i])}))},$=function(e){u.forEach((function(t){var n=ne(t,1)[0];e.off(n)}))},O=function(e,t){e.off("change.".concat(ie)).val(t).trigger("change"),h&&e.on("change.".concat(ie),h)},A=function(e,t){return null===e&&""===t||V()(e,t)},C=function(e){if("object"===X(e)){var t=e.value,n=e.label,r=oe(e,Q);return(0,v.jsx)("option",ee(ee({value:t},r),{},{children:n}),"option-".concat(t))}return(0,v.jsx)("option",{value:e,children:e},"option-".concat(e))};return(0,v.jsx)("select",ee(ee({ref:_},b),{},{children:l.map((function(e,t){return e.children?(0,v.jsx)("optgroup",ee(ee({label:e.label},e),{},{children:e.children.map((function(e){return C(e)}))}),"optgroup-".concat(t)):C(e)}))}))}));function ae(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,s,a=[],l=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=i.call(n)).done)&&(a.push(r.value),a.length!==t);l=!0);}catch(e){c=!0,o=e}finally{try{if(!l&&null!=n.return&&(s=n.return(),Object(s)!==s))return}finally{if(c)throw o}}return a}}(e,t)||function(e,t){if(e){if("string"==typeof e)return le(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?le(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function le(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}var ce={minimumInputLength:3,dropdownParent:"#modal-id",ajax:{url:advancedAds.endpoints.ajaxUrl,dataType:"json",delay:250,data:function(e){return{q:e.term,action:"search_posts",security:advancedAds.oneclick.security}},processResults:function(e){return{results:e}}}};function ue(e){var t=e.open,n=e.onClose,o=(0,r.useRef)(null),s=ae((0,r.useState)(!1),2),l=s[0],c=s[1],u=ae((0,r.useState)(!1),2),d=u[0],p=u[1],h=advancedAds.oneclick.modal,f=(0,r.useContext)($),m=f.selectedMethod,g=f.selectedPage,y=f.selectedPageTitle,b=f.setMethod,_=f.setPage,x=ae((0,r.useState)(m),2),w=x[0],j=x[1],S=ae((0,r.useState)(g),2),O=S[0],A=S[1];(0,r.useEffect)((function(){t?o.current.showModal():o.current.close()}),[t]);var C=g?[{value:g,label:y}]:[],E="final"===w||!O,D=function(){c(!0),p(!0),i().ajax({url:ajaxurl,method:"POST",data:{action:"update_oneclick_preview",security:advancedAds.oneclick.security,method:w,page:O}}).complete((function(){c(!1),b(w),_(O)}))},k=a()("ml-7",{"import-active":"final"===w,"text-[#a7aaad]":"final"!==w}),T=a()("button button-primary advads-modal-close-action",{"button-primary":"final"!==w,"!bg-[#cc3000] !border-[#cc3000] !shadow-none":"final"===w});return(0,v.jsxs)("dialog",{id:"modal-id",className:"advads-modal",ref:o,children:[(0,v.jsx)("a",{href:"#close",className:"advads-modal-close-background",onClick:n,children:advancedAds.oneclick.btnClose}),(0,v.jsxs)("div",{className:"advads-modal-content",children:[(0,v.jsxs)("div",{className:"advads-modal-header",children:[(0,v.jsx)("a",{href:"#close",className:"advads-modal-close",title:advancedAds.oneclick.btnCancel,onClick:n,children:"×"}),(0,v.jsx)("h3",{children:h.title})]}),(0,v.jsx)("div",{className:"advads-modal-body",children:(0,v.jsxs)("div",{className:"flex gap-x-8",children:[(0,v.jsx)("div",{children:(0,v.jsx)("strong",{children:h.labelImport})}),(0,v.jsxs)("div",{children:[(0,v.jsxs)("div",{className:"mb-5",children:[(0,v.jsxs)("label",{htmlFor:"specific-page",children:[(0,v.jsx)("input",{type:"radio",name:"import-methods",id:"specific-page",value:"page",checked:"page"===w,onChange:function(){return j("page")}}),(0,v.jsx)("span",{className:"pl-1",children:h.labelSpecificPage})]}),(0,v.jsxs)("div",{className:"ml-7 mt-6",children:[(0,v.jsx)("div",{children:(0,v.jsx)(se,{defaultValue:O,data:C,options:ce,style:{width:"100%"},disabled:"final"===w,onChange:function(e){return A(e.target.value)}})}),(0,v.jsxs)("p",{className:"buttons-set",children:[(0,v.jsx)("button",{className:"button button-primary",disabled:E,onClick:D,children:h.btnUpdate}),(0,v.jsxs)("a",{href:"".concat(advancedAds.siteInfo.homeUrl,"/?p=").concat(g),target:"_blank",className:"button button-secondary !flex items-center gap-x-1",disabled:E,children:[(0,v.jsx)("span",{children:h.btnGoto}),(0,v.jsx)("span",{className:"dashicons dashicons-external"})]}),l&&(0,v.jsx)("img",{src:advancedAds.oneclick.spinner,alt:"",className:"h-[11px]"})]})]})]}),(0,v.jsxs)("div",{children:[(0,v.jsxs)("label",{htmlFor:"final-import",children:[(0,v.jsx)("input",{type:"radio",name:"import-methods",id:"final-import",value:"final",checked:"final"===w,onChange:function(){return j("final")}}),(0,v.jsx)("span",{className:"pl-1",children:h.labelFinalImport})]}),(0,v.jsx)("div",{className:k,dangerouslySetInnerHTML:{__html:h.descFinalImport}})]})]})]})}),(0,v.jsx)("div",{className:"advads-modal-footer",children:(0,v.jsxs)("div",{className:"tablenav bottom",children:[(0,v.jsx)("a",{href:"#close",className:"button button-secondary advads-modal-close",onClick:n,children:advancedAds.oneclick.btnCancel}),(0,v.jsx)("button",{type:"submit",form:"",className:T,onClick:function(){if(d)return p(!1),void n();"page"===w?D():"final"===w&&(p(!0),i().ajax({url:ajaxurl,method:"POST",data:{action:"update_oneclick_preview",security:advancedAds.oneclick.security,method:w}}).complete((function(){c(!1),b(w),_(0)}))),p(!1),n()},children:"final"===w?h.btnFinal:h.btnSave})]})})]})]})}function de(e){var t,n=e.id,o=e.label,s=e.className,a=e.option,l=e.disabled,c=void 0!==l&&l,u=e.children,d=(0,r.useContext)($),p=d.settings,h=d.updateSettings,f=null!==(t=p[a])&&void 0!==t&&t,m=n.replace(/-/g,"_").replace("pubguru_","");return(0,v.jsx)("div",{className:s,children:(0,v.jsxs)("label",{htmlFor:n,className:"advads-ui-switch",children:[(0,v.jsx)("input",{type:"checkbox",id:n,checked:f,onChange:function(e){var t=e.target.checked;(function(e,t){return i().ajax({url:ajaxurl,method:"POST",data:{action:"pubguru_module_change",security:advancedAds.oneclick.security,module:e,status:t}})})(m,t).done((function(e){e.data.notice&&""!==e.data.notice&&N(e.data.notice,e.data.action?{actions:[{label:e.data.action,onClick:function(e,t){e.target.disabled=!0,i().ajax({url:ajaxurl,method:"POST",data:{action:"pubguru_backup_ads_txt",security:advancedAds.oneclick.security}}).done((function(n){t(),e.target.disabled=!1,n.success?P(n.data):N(n.data)})).error((function(t){e.target.disabled=!1,N("Error: "+t.statusText)}))}}]}:null),h(a,t)}))},disabled:c}),(0,v.jsx)("div",{}),(0,v.jsx)("span",{dangerouslySetInnerHTML:{__html:o}}),u]})})}function pe(){var e=advancedAds.oneclick.settings,t=(0,r.useContext)($),n=t.settings,o="page"===t.selectedMethod,i=e.headerBidding;return o&&(i+=' &nbsp;<em class="muted">'+e.onlyPreview+"</em>"),(0,v.jsxs)("div",{className:"mb-8",children:[(0,v.jsx)("div",{className:"subheader inline",children:e.title}),(0,v.jsxs)("div",{className:"advads-ui-switch-list mt-6",children:[(0,v.jsx)(de,{id:"pubguru-header-bidding",label:i,option:"headerBidding",disabled:o}),n.headerBidding&&(0,v.jsx)(de,{id:"pubguru-header-bidding-at-body",label:e.scriptLocation,className:"ml-4",option:"headerBiddingAtBody"}),(0,v.jsx)(de,{id:"pubguru-ads-txt",label:e.adsTxt,option:"adsTxt"}),(0,v.jsx)(de,{id:"pubguru-traffic-cop",label:e.trafficCop,option:"trafficCop",children:e.hasTrafficCop&&(0,v.jsx)("span",{className:"pg-tc-trail",children:e.trafficCopTrial})}),(0,v.jsx)(de,{id:"pubguru-tag-conversion",className:"hidden",label:e.activateTags,option:"tagConversion"})]}),(0,v.jsx)("p",{dangerouslySetInnerHTML:{__html:e.help}})]})}function he(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,s,a=[],l=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=i.call(n)).done)&&(a.push(r.value),a.length!==t);l=!0);}catch(e){c=!0,o=e}finally{try{if(!l&&null!=n.return&&(s=n.return(),Object(s)!==s))return}finally{if(c)throw o}}return a}}(e,t)||function(e,t){if(e){if("string"==typeof e)return fe(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?fe(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function fe(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function me(){var e=advancedAds.oneclick.step3,t=he((0,r.useState)(!1),2),n=t[0],o=t[1],i=(0,r.useContext)($),s=i.selectedMethod,a=i.selectedPage;return(0,v.jsxs)(v.Fragment,{children:[(0,v.jsxs)("div",{className:"mt-6 mb-8",children:[(0,v.jsx)("p",{children:String.format((0,H.decodeEntities)(e.yourDomain),advancedAds.oneclick.options.connectedDomain)}),"final"===s&&(0,v.jsx)("p",{dangerouslySetInnerHTML:{__html:e.finalContent}})]}),"page"===s&&(0,v.jsxs)("div",{className:"mt-6 mb-8",children:[(0,v.jsx)("div",{className:"subheader inline",children:e.title}),a?(0,v.jsx)("p",{dangerouslySetInnerHTML:{__html:e.previewContent}}):(0,v.jsx)("div",{className:"mt-6",dangerouslySetInnerHTML:{__html:e.importContent}}),(0,v.jsxs)("p",{className:"buttons-set",children:[(0,v.jsx)("button",{className:"button button-primary",onClick:function(){return o(!0)},children:e.btnImport}),a>0&&(0,v.jsxs)("a",{href:"".concat(advancedAds.siteInfo.homeUrl,"/?p=").concat(a),target:"_blank",className:"button button-secondary !flex items-center gap-x-1",children:[(0,v.jsx)("span",{children:advancedAds.oneclick.modal.btnGoto}),(0,v.jsx)("span",{className:"dashicons dashicons-external"})]})]})]}),(0,v.jsx)(ue,{open:n,onClose:function(){return o(!1)}}),(0,v.jsx)(pe,{})]})}function ge(e){var t=e.title,n=e.children;return(0,v.jsxs)("div",{children:[t&&(0,v.jsx)("div",{className:"subheader",children:t}),n]})}function ye(){var e=(0,r.useContext)($),t=e.showMetabox,n=e.currentStep,o=advancedAds.oneclick.metabox;return t?(0,v.jsxs)("div",{id:"advads-m2-connect",className:"postbox position-full",children:[(0,v.jsx)("h2",{className:"hndle",children:(0,v.jsx)("span",{children:o.title})}),(0,v.jsxs)("div",{className:"inside",children:[1===n&&(0,v.jsx)(ge,{title:advancedAds.oneclick.step1.title,children:(0,v.jsx)(U,{})}),2===n&&(0,v.jsx)(ge,{title:advancedAds.oneclick.step2.title,children:(0,v.jsx)(Y,{})}),3===n&&(0,v.jsx)(ge,{children:(0,v.jsx)(me,{})}),(0,v.jsx)("footer",{children:(0,v.jsxs)("a",{href:o.visitLink,target:"_blank",rel:"noreferrer",children:[o.visitText,(0,v.jsxs)("span",{className:"screen-reader-text",children:[" ","(opens in a new tab)"]}),(0,v.jsx)("span",{"aria-hidden":"true",className:"dashicons dashicons-external"})]})})]})]}):null}const ve=function(){return(0,v.jsxs)(O,{children:[(0,r.createPortal)((0,v.jsx)(R,{}),document.getElementById("advads-oneclick-addon-row")),(0,v.jsx)("div",{className:"mb-4",children:(0,v.jsx)(I,{})}),(0,v.jsx)(ye,{})]})};String.format||(String.format=function(e){var t=Array.prototype.slice.call(arguments,1);return e.replace(/{(\d+)}/g,(function(e,n){return void 0!==t[n]?t[n]:e}))}),t()((function(){var e=document.getElementById("advads-oneclick-app");if(e){var t=document.createElement("div");t.id="advads-oneclick-addon-row",document.getElementById("advanced-ads-addon-box").appendChild(t);var n=(0,r.createElement)(ve);r.createRoot?(0,r.createRoot)(e).render(n):(0,r.render)(n,e)}}))})()})();