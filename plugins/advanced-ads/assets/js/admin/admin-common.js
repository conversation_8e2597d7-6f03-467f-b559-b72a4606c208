(()=>{"use strict";var e={n:a=>{var n=a&&a.__esModule?()=>a.default:()=>a;return e.d(n,{a:n}),n},d:(a,n)=>{for(var t in n)e.o(n,t)&&!e.o(a,t)&&Object.defineProperty(a,t,{enumerable:!0,get:n[t]})},o:(e,a)=>Object.prototype.hasOwnProperty.call(e,a)};const a=jQuery;var n=e.n(a);n()((function(){!function(){var e=n()(".advads-tab-menu",".advads-tab-container");e.on("click","a",(function(e){e.preventDefault();var a=n()(this),t=a.closest(".advads-tab-container"),s=n()(a.attr("href"));t.find("a.is-active").removeClass("is-active"),a.addClass("is-active"),t.find(".advads-tab-target").hide(),s.show()})),e.each((function(){var e=n()(this),a=window.location.hash,t=void 0!==a&&a,s=e.find("a:first");t&&e.find("a[href="+t+"]").length>0&&(s=e.find("a[href="+t+"]")),s.trigger("click")}))}(),n()("#advads-show-filters").on("click",(function(){var e=n()(this).find(".dashicons"),a=e.hasClass("dashicons-arrow-up");e.toggleClass("dashicons-filter",a),e.toggleClass("dashicons-arrow-up",!a)})),document.querySelectorAll(".advads-multiple-subscribe_button").forEach((function(e){e.addEventListener("click",(function(){var a=e.closest(".advads-multiple-subscribe"),n=Array.from(a.querySelectorAll('input[name="advads-multiple-subscribe"]:checked')).map((function(e){return e.value}));if(0!==n.length){var t=document.createElement("span");t.className="spinner advads-spinner",e.insertAdjacentElement("afterend",t);var s=new FormData;s.append("action","advads-multiple-subscribe"),s.append("groups",JSON.stringify(n)),s.append("nonce",advadsglobal.ajax_nonce),fetch(ajaxurl,{method:"POST",body:s}).then((function(e){return e.json()})).then((function(n){e.style.display="none";var t=document.createElement("p");t.innerHTML=n.data.message,a.innerHTML="",a.appendChild(t),a.classList.add("notice-success","notice")})).catch((function(e){var n,t=document.createElement("p");t.innerHTML=(null===(n=e.responseJSON)||void 0===n||null===(n=n.data)||void 0===n?void 0:n.message)||"An error occurred",a.innerHTML="",a.appendChild(t),a.classList.add("notice-error","notice")}))}}))}))}))})();