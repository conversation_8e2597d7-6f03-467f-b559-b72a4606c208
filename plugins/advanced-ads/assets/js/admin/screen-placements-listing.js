(()=>{"use strict";var e={n:t=>{var a=t&&t.__esModule?()=>t.default:()=>t;return e.d(a,{a}),a},d:(t,a)=>{for(var d in a)e.o(a,d)&&!e.o(t,d)&&Object.defineProperty(t,d,{enumerable:!0,get:a[d]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t)};const t=jQuery;var a=e.n(t),d=function(){a()(".search-box").toggle(),a()(".tablenav.top .alignleft.actions:not(.bulkactions)").toggle()};function n(){a()("#advads-show-filters").on("click",d),a()("#advads-reset-filters").length&&d()}var o=function(){["advads_frontend_action","advads_frontend_element","advads_frontend_picker","advads_prev_url","advads_frontend_pathtype","advads_frontend_boundary","advads_frontend_blog_id","advads_frontend_starttime"].forEach((function(e){return localStorage.removeItem(e)})),window.Advanced_Ads_Admin.set_cookie("advads_frontend_picker","",-1)};const c=wp.apiFetch;var i=e.n(c);function s(e,t){void 0===t&&(t=!0),a()(e).find("select,input,textarea").add('.submit-placement-form[data-id="'.concat(e.id.replace("advanced-ads-placement-form-",""),'"]')).prop("disabled",t)}function r(e){var t=a()(e).serialize();s(e),i()({path:"/advanced-ads/v1/placement",method:"PUT",data:{fields:t}}).then((function(t){if(s(e,!1),t.error)return s(e,!1),e.closest("dialog").close(),void window.advancedAds.notifications.addError(t.error);var d=e.closest("dialog");d.advadsTermination.resetInitialValues();var n=a()("#post-".concat(t.placement_data.id," .column-name .row-title"));(n.text(t.title),a()("#post-".concat(t.placement_data.id," .column-ad_group .advads-placement-item-select")).val(t.item),t.payload.post_status&&"draft"===t.payload.post_status)?n.parent().text().includes(advancedAds.placements.draft)||n.parent().append(a()('<strong>— <span class="post-state">'.concat(advancedAds.placements.draft,"</span></strong>"))):n.siblings().remove();if(wp.hooks.doAction("advanced-ads-placement-updated",t),t.reload)return localStorage.setItem("advadsUpdateMessage",JSON.stringify({type:"success",message:window.advadstxt.placement_forms.updated})),void window.location.reload();window.advancedAds.notifications.addSuccess(window.advadstxt.placement_forms.updated),d.close()}))}function l(e){var t=e.closest("dialog");if("function"==typeof window[t.closeValidation.function]&&!window[t.closeValidation.function](t.closeValidation.modal_id))return;var d=a()(e).serialize();s(e),i()({path:"/advanced-ads/v1/placement",method:"POST",data:{fields:d}}).then((function(t){s(e,!1),t.redirectUrl?window.location.href=t.redirectUrl:t.reload&&(localStorage.setItem("advadsUpdateMessage",JSON.stringify({type:"success",message:window.advadstxt.placement_forms.created})),window.location.reload())}))}a()(document).on("click",".submit-placement-edit",(function(){r(a()("#advanced-ads-placement-form-".concat(this.dataset.id))[0])})),a()(document).on("click","#submit-new-placement",(function(){l(a()("#advads-placements-new-form")[0])}));const m=wp.url;function f(e){return f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},f(e)}var p=function(e,t){var d=a()("#edit-".concat(e));d.find("fieldset:disabled").prop("disabled",!1),d.find('select[name="status"]').val(t.status),d.find('[name="post_title"]').val(t.title),d.find('[name="mm"]').val("01"),wp.hooks.doAction("advanced-ads-quick-edit-plaacement-fields-init",e,t)};const v=function(){var e=window.inlineEditPost.edit;window.inlineEditPost.edit=function(t){e.apply(this,arguments),"object"===f(t)&&function(e){i()({path:(0,m.addQueryArgs)("/advanced-ads/v1/placement",{id:e}),method:"GET"}).then((function(t){t.error||p(e,t)}))}(parseInt(this.getId(t),10))}};a()((function(){n(),function(){var e=localStorage.getItem("advads_frontend_element"),t=localStorage.getItem("advads_frontend_picker");if(e){var a=document.querySelector('[id="advads-frontend-element-'+t+'"]');if(a.querySelector(".advads-frontend-element").value=e,void 0!==localStorage.getItem("advads_frontend_action")){var d=a.closest("form"),n=new FormData(d);n.set("nonce",advadsglobal.ajax_nonce),n.set("ID",n.get("post_ID")),wp.ajax.post("advads-update-frontend-element",Object.fromEntries(n.entries())).then(o).fail((function(e){return console.error(e)}))}}Array.from(document.querySelectorAll(".advads-activate-frontend-picker")).forEach((function(e){e.addEventListener("click",(function(){localStorage.setItem("advads_frontend_picker",this.dataset.placementid),localStorage.setItem("advads_frontend_action",this.dataset.action),localStorage.setItem("advads_prev_url",window.location),localStorage.setItem("advads_frontend_pathtype",this.dataset.pathtype),localStorage.setItem("advads_frontend_boundary",this.dataset.boundary),localStorage.setItem("advads_frontend_blog_id",window.advancedAds.siteInfo.blogId),localStorage.setItem("advads_frontend_starttime",(new Date).getTime()),window.Advanced_Ads_Admin.set_cookie("advads_frontend_picker",this.dataset.placementid,null),this.dataset.boundary?window.location=window.advancedAds.content_placement_picker_url:window.location=window.advancedAds.siteInfo.homeUrl}))})),t&&(document.querySelector('[id="advads-frontend-element-'+t+'"]').querySelector(".advads-deactivate-frontend-picker").style.display="block"),Array.from(document.querySelectorAll(".advads-deactivate-frontend-picker")).forEach((function(e){e.addEventListener("click",(function(){o(),Array.from(document.querySelectorAll(".advads-deactivate-frontend-picker")).forEach((function(e){e.style.display="none"}))}))}))}(),a()(".js-update-placement-item").on("change",(function(){var e=a()(this),t=e.parent(),d=t.find(".advads-loader");t.find(".advads-error"),e.prop("disabled",!0),d.removeClass("hidden"),a().ajax({type:"POST",url:advancedAds.endpoints.ajaxUrl,data:{action:"advads-placement-update-item",placement_id:e.data("placement-id"),item_id:e.val()}}).always((function(){e.prop("disabled",!1),d.addClass("hidden")})).fail((function(e){window.advancedAds.notifications.addError(e.responseJSON.data.message)})).done((function(e){var d=e.data,n=(t.find(".advads-success-message"),a()("#advanced-ads-placement-form-"+d.placement_id));[t.find(".advads-placement-item-edit"),n.find(".advads-placement-item-edit")].forEach((function(e){e.attr("href",d.edit_href),e.css("display",""===d.edit_href?"none":"inline")})),n.find(".advads-placement-item-select").val(d.item_id),window.advancedAds.notifications.addSuccess(window.advadstxt.placement_forms.updated)}))})),wp.hooks.addFilter("advanced-ads-submit-modal-form","advancedAds",(function(e,t){return"advads-placements-new-form"===t.id?(l(t),!1):e})),wp.hooks.addFilter("advanced-ads-submit-modal-form","advancedAds",(function(e,t){return 0===t.id.indexOf("advanced-ads-placement-form-")?(r(t),!1):e})),a()('[id^="advanced-ads-placement-form-"]').each((function(){var e=this.id.replace("advanced-ads-placement-form-","");a()("#modal-placement-edit-".concat(e)).find(".tablenav.bottom").html('<button class="button button-primary submit-placement-edit" data-id="'.concat(e,'">').concat(advadstxt.close_save,"</button>"))})),a()("#modal-placement-new").find(".tablenav.bottom").html('<button class="button button-primary" id="submit-new-placement">'.concat(advadstxt.save_new_placement,"</button>")),v(),function(){if(a()("#posts-filter tr.no-items").length){var e=document.querySelector("#modal-placement-new");e&&"function"==typeof e.showModal&&e.showModal()}}()})),a()(document).on("click",'.post-type-advanced_ads_plcmnt .wp-list-table [type="checkbox"]',(function(){a()(".post-type-advanced_ads_plcmnt .tablenav.bottom .bulkactions").toggleClass("fixed",0<a()('.post-type-advanced_ads_plcmnt .check-column [type="checkbox"]:checked').length)}))})();