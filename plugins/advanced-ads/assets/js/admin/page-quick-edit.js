(()=>{"use strict";var e={n:t=>{var n=t&&t.__esModule?()=>t.default:()=>t;return e.d(n,{a:n}),n},d:(t,n)=>{for(var o in n)e.o(n,o)&&!e.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:n[o]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t)};const t=jQuery;var n=e.n(t);const o=wp.apiFetch;var a=e.n(o);function d(e){return d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},d(e)}function i(e){a()({path:"/advanced-ads/v1/page_quick_edit?id=".concat(e,"&nonce=").concat(window.advancedAds.page_quick_edit.nonce),method:"GET"}).then((function(t){!function(e,t){var o=n()("#edit-".concat(e)),a=o.find('[name="advads-disable-ads"]');a.closest("fieldset").prop("disabled",!1),a.prop("checked",Boolean(t.disable_ads));var d=o.find('[name="advads-disable-the-content"]');d.length&&d.prop("disabled",!1).prop("checked",Boolean(t.disable_the_content))}(e,t)}))}n()((function(){var e=window.inlineEditPost.edit;window.inlineEditPost.edit=function(t){e.apply(this,arguments),"object"===d(t)&&i(parseInt(this.getId(t)))}}))})();