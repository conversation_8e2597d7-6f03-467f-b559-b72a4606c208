(()=>{"use strict";const e=function(){"notifications"===new URLSearchParams(window.location.search).get("aa-debug")&&function(){var e=document.createElement("div");e.id="qwerty",e.style="position:fixed;z-index:99999;bottom:20px;left:".concat(document.getElementById("adminmenuwrap").clientWidth+22,"px");var t=document.createElement("div");t.style="height:0;overflow:hidden;";var n=document.createElement("div");n.style="width:640px;height:322px;background-color:#f0f0f0;border:1px solid #a6a6a6;padding:20px";var i=document.createElement("i");i.style="position:absolute;top:-35px;left:5px;cursor:pointer",i.className="dashicons dashicons-plus-alt2",i.id="show-tester";var o=document.createElement("p");o.style="background-color:#fbfbfb;padding:1em",o.innerHTML='<i class="dashicons dashicons-info"></i>Please don\'t use HTML tags other than links';var a=document.createElement("textarea");a.style="resize:none;width:100%;height:210px";var s=document.createElement("select");s.innerHTML='<option value="addError">Error</option><option value="addInfo">Info</option><option value="addSuccess">Success</option>';var r=document.createElement("label");r.innerText="Type: ",r.className="alignleft",r.append(s);var c=document.createElement("button");c.className="button button-primary alignright",c.innerText="Create notification",c.addEventListener("click",(function(){return d(a.value)}));var d=function(e){e.length&&window.advancedAds.notifications[s.value](e)};e.append(i),t.append(n),n.append(o),n.append(a),n.append(r),n.append(c),e.append(t);var l=!1;i.addEventListener("click",(function(e){if(!l){l=!0,e.target.classList.toggle("dashicons-plus-alt2"),e.target.classList.toggle("dashicons-minus"),console.log(t.clientHeight);var n=new Animation(new KeyframeEffect(t,{height:0===t.clientHeight?"365px":0},{duration:250,easing:"ease-in-out",iterations:1,fill:"forwards"}));n.onfinish=function(){l=!1},n.play()}})),document.getElementById("wpwrap").append(e)}()};function t(e){return function(e){if(Array.isArray(e))return n(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return n(e,t);var i={}.toString.call(e).slice(8,-1);return"Object"===i&&e.constructor&&(i=e.constructor.name),"Map"===i||"Set"===i?Array.from(e):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?n(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function n(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=Array(t);n<t;n++)i[n]=e[n];return i}var i,o,a=[],s={busy:!1,addItem:function(e,t){if(s.busy)a.push({fn:s.addItem,args:[e,t]});else{s.busy=!0;["error","info","success"].includes(t)||(t="info");var n=document.createElement("div");n.className="item item-".concat(t),n.innerHTML='<div class="item-inner"><div class="content"><p>__MSG__</p></div><div class="sep"></div><div class="dismiss"><span class="dashicons"></span></div></div>'.replace("__MSG__",e),o.append(n),n.style.left="auto";var i=new Animation(new KeyframeEffect(n,[{right:"".concat(-n.clientWidth-30,"px")},{right:0}],{duration:500,easing:"ease-in-out",iterations:1}));i.onfinish=function(){s.unlockPositions(),"error"!==t&&setTimeout((function(){return s.dismiss(n)}),5e3),s.busy=!1,s.checkQueue()},i.play()}},unlockPositions:function(){o.querySelectorAll(".item").forEach((function(e){e.style="position:relative;left:0;top:0;margin:0;right:auto;"}))},lockPositions:function(){var e=[],t=o.querySelectorAll(".item");t.forEach((function(t){e.push(t.offsetTop)})),t.forEach((function(t,n){t.style="position:absolute;top:".concat(e[n],"px;right:0;")}))},checkQueue:function(){if(a.length){var e=a.shift();e.fn.apply(null,e.args)}},dismiss:function(e){if(s.busy)a.push({fn:s.dismiss,args:[e]});else{if(s.busy=!0,!document.contains(e))return s.busy=!1,void s.checkQueue();var n=t(o.querySelectorAll(".item")).filter((function(t){return!t.isEqualNode(e)&&t.offsetTop>e.offsetTop}));s.lockPositions();var i=new Animation(new KeyframeEffect(e,[{right:"-".concat(e.querySelector(".item-inner").clientWidth+60,"px")}],{duration:500,easing:"ease-in-out",iterations:1,fill:"forwards"}));i.onfinish=function(){n.length?s.moveOtherItems(e.clientHeight,n,s.endOfDismiss,e):s.endOfDismiss(e)},i.play()}},endOfDismiss:function(e){e.remove(),s.unlockPositions(),s.busy=!1,s.checkQueue()},moveOtherItems:function(e,t,n,i){var o=0;t.forEach((function(a,s){!function(t,a){var s=new Animation(new KeyframeEffect(t[a],[{marginTop:"-".concat(e,"px")}],{duration:200,easing:"ease-in-out",iterations:1}));s.onfinish=function(){"function"==typeof n&&(o++,t.length===o&&n.call(null,i))},s.play()}(t,s)}))}},r={addError:function(e){s.addItem(e,"error")},addInfo:function(e){s.addItem(e,"info")},addSuccess:function(e){s.addItem(e,"success")}};document.addEventListener("DOMContentLoaded",(function(t){var n;(i=document.createElement("div")).id="advads-notifications",o=document.createElement("div"),i.append(o),document.getElementById("wpwrap").append(i),document.addEventListener("click",(function(e){var t=e.target;t.closest("#advads-notifications")&&t.classList&&(t.classList.contains("dismiss")||t.parentNode.classList&&t.parentNode.classList.contains("dismiss"))&&s.dismiss(t.closest(".item"))})),e(),function(){var e=document.getElementById("message");e&&r.addSuccess(e.querySelector("p").innerHTML);var t=localStorage.getItem("advadsUpdateMessage");if(t){var n=JSON.parse(t);s.addItem(n.message,n.type),localStorage.removeItem("advadsUpdateMessage")}}(),(n=document.getElementById("setting-error-settings_updated"))&&r.addSuccess(n.querySelector("p").innerHTML),window.advancedAds.notifications=r}))})();