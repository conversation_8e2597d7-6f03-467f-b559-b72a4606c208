.switch{position:relative}.switch input{background:#0000;cursor:pointer;opacity:0;position:absolute;top:0;z-index:2}.switch input:checked{z-index:1}.switch input:checked+label{cursor:default;opacity:1}.switch input:not(:checked)+label:hover{opacity:.5}.switch label{color:#fff;cursor:pointer;opacity:.33;transition:opacity .25s ease}.switch .toggle-outside{border-radius:2rem;height:100%;overflow:hidden;padding:.25rem;transition:all .25s ease}.switch .toggle-inside{background:#4a4a4a;border-radius:5rem;position:absolute;transition:all .25s ease}.switch-horizontal{border:1px solid #ccc;border-radius:2rem;font-size:0;height:1.6rem;margin-bottom:1rem;width:3rem}.switch-horizontal input{height:1.6rem;left:3rem;margin:0;width:3rem}.switch-horizontal label{display:inline-block;font-size:1.5rem;height:100%;line-height:3;margin:0;text-align:center;width:3rem}.switch-horizontal label:last-of-type{margin-left:3rem}.switch-horizontal .toggle-outside{left:3rem;position:absolute;width:3rem}.switch-horizontal .toggle-inside{height:1rem;width:1rem}.switch-horizontal input:checked~.toggle-outside .toggle-inside{left:.25rem}.switch-horizontal input~input:checked~.toggle-outside .toggle-inside{left:1.6rem}.switch--no-label label{height:0;overflow:hidden;visibility:hidden;width:0}.switch--no-label input:checked~.toggle-outside .toggle-inside{background:#0003;border:1px solid #0003}.switch--no-label input~input:checked~.toggle-outside .toggle-inside{background:#3582c4}.switch--no-label.switch-horizontal .toggle-outside,.switch--no-label.switch-horizontal input{left:0}.is-list-disabled{pointer-events:none}.is-list-disabled input{background:#ffffff80;border-color:#dcdcdebf;box-shadow:inset 0 1px 2px #0000000a;color:#2c333880;pointer-events:none}
