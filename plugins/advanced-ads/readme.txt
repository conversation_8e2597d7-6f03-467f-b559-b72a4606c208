=== Advanced Ads – Ad Manager & AdSense ===
Contributors: webzunft, advancedads
Tags: ads, adsense, amazon, affiliate, ad manager
Requires at least: 5.7
Tested up to: 6.8
Requires PHP: 7.4
Stable tag: 2.0.9
License: GPLv2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html

The only complete toolkit for all ad types. Grow your revenue with AdSense, Amazon—or any affiliate network. Get pinpoint targeting and best support!

== Description ==

= The ultimate ad management plugin for WordPress =

Advanced Ads is the most comprehensive ad management tool available. It strikes the perfect balance between ease of use and powerful features, trusted by thousands of companies and businesses.

We took the pain out of advertising and made it easy to embed ads, banners, or any other code automatically—all within minutes and without the need for theme file editing. If you are looking for the best plugin for ad management, you have found it.

[Manual](https://wpadvancedads.com/manual/?utm_source=wporg&utm_medium=link&utm_campaign=wp-linkbar-manual) | [Support](https://wpadvancedads.com/aa-links/support) | [Demo](https://wpadvancedads.com/aa-links/demo) | [Premium Features](https://wpadvancedads.com/add-ons/all-access/?utm_source=wporg&utm_medium=link&utm_campaign=wp-linkbar-features)

* <strong>Usability:</strong> Clearly structured interface • Smart workflows for fluid day-to-day operations
* <strong>Functionality:</strong> Setting the gold standard for the market with outstanding features
* <strong>Compatibility:</strong> All ad networks • Dedicated integrations with loads of popular plugins and themes
* <strong>Expertise:</strong> Fast and top-rated support • Approved in publishing and ad optimization since 2009
* <strong>Reputation:</strong> Recommended by Google • Fully compliant with Google AdSense policies

Would you like to know if there is a certain feature, what the optimized setup would be, or how to implement your client’s demands? Just [open a thread in the forum](https://wordpress.org/support/plugin/advanced-ads#new-post)!

This is what our users are saying about Advanced Ads:

> We use this plugin to deliver rotating ads on a community news site, and it’s great. We’ve delivered over a million ad impressions since we launched less than a year ago, using a combination of sidebar, top, sticky and in-content placements — both HTML5 ads and images. Advanced Ads makes it easy for our small team to deliver a good experience to our users and our advertisers.
> <i>mytown304 on wp.org</i>

Advanced Ads allowed our founder to grow from 0 to 100 MM monthly ad impressions. Benefit from our experience as publishers and monetize your website today!

= Premium Features =
This plugin is the lite version of the Advanced Ads plugin that comes with all the features you will ever need to optimize your ads and increase your revenues, including performance tracking, advanced ad targeting, split tests, click fraud protection, lazy loading, background ads, floating ads, popups and sticky ads, full AMP support, adblocker detection, the most comfortable Google Ad Manager integration, and tons more. [Click here to purchase the best premium WordPress ad management plugin now!](https://wpadvancedads.com/add-ons/all-access/?utm_source=wporg&utm_medium=link&utm_campaign=wp-all-access)

[Full Feature List](https://wpadvancedads.com/features/?utm_source=wporg&utm_medium=link&utm_campaign=wp-features)

= Ad Management =

* create and display **unlimited** ad units
* ad rotation
* schedule ads and set start time and expiration date
* target ads by content and user groups
* inject ads into posts and pages automatically without coding

= Ad Types =

Choose between different ad types that enable you to:

* insert ads and banners from all ad and affiliate networks (e.g., [Google AdSense](https://wpadvancedads.com/adsense-ads/), [Amazon Affiliate Program Amazon Associates](https://wpadvancedads.com/amazon-affiliate-program-wordpress/), BuySellAds, Google Ad Manager, Ezoic, media.net, [Booking.com](https://wpadvancedads.com/booking-com-ads-wordpress/), Tradedoubler, Awin, GetYourGuide, MonetizeMore, The Moneytizer, Infolinks...)
* dedicated support for all types of Google AdSense ads, including text and display ads, native ads (In-article, In-feed, Multiplex ads), Auto ads, and Auto ads for AMP
* display images and image banners
* create content-rich ads with the WordPress TinyMCE editor
* inject HTML, CSS, JavaScript, or PHP code
* use shortcodes within ads (to also deliver advertisements from another ad plugin like Ad Inserter, AdRotate, Quick AdSense, AdSanity, Ads for WP, or the Google AdSense plugin WP QUADS)

= Display Ads for WordPress =

* auto-inject ads via placements
* use functions to display ads in template files
* use shortcodes to place ads manually
* show ads in the sidebar and in widgets
* disable all ads on specific pages
* display a customizable ad label, e.g., “Advertisements”, above each banner ad
* display multiple ads in ad grids ([Pro](https://wpadvancedads.com/add-ons/advanced-ads-pro/?utm_source=wporg&utm_medium=link&utm_campaign=wp-features-pro-5))

= Display Conditions =

Show ads based on content. [List of all display conditions](https://wpadvancedads.com/manual/display-conditions/)

* individual posts, pages, and other post types
* posts by category, tags, taxonomies, author, and age
* archive pages by category, tags, taxonomies
* special page types like 404, attachment and front page
* hide ads on secondary queries (e.g., posts in sidebars)
* display or hide banners within the post feed
* hide all ads from specific page types, e.g., 404 pages, feed
* hide ads from bots and web crawlers
* posts and pages by contained keywords [(keyword targeting)](https://wpadvancedads.com/manual/ads-based-on-keywords/?utm_source=wporg&utm_medium=link&utm_campaign=features) or URL parameters, included in [Advanced Ads Pro](https://wpadvancedads.com/add-ons/advanced-ads-pro/?utm_source=wporg&utm_medium=link&utm_campaign=wp-features-pro-1)

= Visitor Conditions =

Serve ads based on conditions related to the visitor. [List of all visitor conditions](https://wpadvancedads.com/manual/visitor-conditions/)

* display or hide a banner by device: mobile, tablet, or desktop
* display or hide a banner by role and for logged-in visitors
* advanced visitor conditions: geolocation, previously visited URL (referrer), user capability, browser language, ad blocker, IP address, browser, and browser width included in [Advanced Ads Pro](https://wpadvancedads.com/add-ons/advanced-ads-pro/?utm_source=wporg&utm_medium=link&utm_campaign=wp-features-pro-2)
* ads by time of the day ([Pro](https://wpadvancedads.com/add-ons/advanced-ads-pro/?utm_source=wporg&utm_medium=link&utm_campaign=wp-features-pro-4))
* frequency capping ([Pro](https://wpadvancedads.com/add-ons/advanced-ads-pro/?utm_source=wporg&utm_medium=link&utm_campaign=wp-features-pro-6))

> **Fantastic plugin and outstanding support**
> I tried at least three other ad plugins for WordPress and ‘Advanced Ads’ is by and far the best one. Last but not least in the support. The first port of call are a number of excellent tutorials. And finally the hands on support. I don’t quite know how he does it but the speed and depth of responses are absolutely amazing.
> djsawyer on wp.org

= Ad Injection | Placements =

Placements to insert ads in pre-defined positions in your theme and content. [List of all placements](https://wpadvancedads.com/manual/placements/)

* ads after any given paragraph, headline, image, or other HTML element
* ads at the top or bottom of the post content
* ads before closing `</head>` tag
* ads in the footer
* [Page peel banners](https://wpadvancedads.com/page-peel-ads/)
* create [split tests and A/B testing](https://wpadvancedads.com/ab-testing-wordpress/)
* automatic insertion of any kind of footer and header code, not only advertising
* use the [ad server](https://wpadvancedads.com/ad-server-wordpress/) placement to display ads on other sites
* many more ad positions with [add-ons](https://wpadvancedads.com/add-ons/?utm_source=wporg&utm_medium=link&utm_campaign=wp-features)

= Mobile Devices =

* target ads to mobile devices, or tablets, or desktops
* display responsive image ads
* ads only for specific browser sizes [Pro](https://wpadvancedads.com/add-ons/advanced-ads-pro/?utm_source=wporg&utm_medium=link&utm_campaign=wp-features-pro-3)
* insert ads on AMP pages [AMP Ads add-on](https://wpadvancedads.com/add-ons/amp-ads/?utm_source=wporg&utm_medium=link&utm_campaign=wp-features-amp-ads-1)

= Google AdSense =

Amazing features of the most powerful and easy Google AdSense plugin.

* unlimited Google AdSense ads banners
* pull ad units directly from your Google AdSense account
* show AdSense revenue in WP Admin
* change settings of your Google AdSense ads directly from your WordPress backend
* supports all Google AdSense ad types, including Google AdSense display ads, native ads like In-feed ads, In-article ads, Multiplex ads, Google AdSense Auto ads, and Google AdSense Auto ads for AMP
* change the type and sizes of AdSense ads without going into your Google AdSense account
* hide Google AdSense advertisements on 404 pages by default (to comply with Google AdSense terms)
* insert Google AdSense code for verification and AdSense Auto Ads
* enable AdSense Auto ads on AMP
* easy Ad Health integration and Google AdSense violation checks
* option to remove the Google AdSense background color
* ads.txt generated with the correct AdSense information automatically
* works along with Google Site Kit or can replace it if you want to [control your ad placements](https://wpadvancedads.com/place-adsense-ad-unit-manually/)
* place Google AdSense In-feed ads ([free In-feed add-on](https://wordpress.org/plugins/advanced-ads-adsense-in-feed/))
* fallback ads for unfilled AdSense ad blocks ([Pro](https://wpadvancedads.com/add-ons/advanced-ads-pro/?utm_source=wporg&utm_medium=link&utm_campaign=wp-features-pro-9)
* assistant for exact sizes of responsive Google AdSense ads ([AMP Ads add-on](https://wpadvancedads.com/add-ons/amp-ads/?utm_source=wporg&utm_medium=link&utm_campaign=wp-features-amp-ads-2))
* convert Google AdSense ads into AMP ads automatically ([AMP Ads add-on](https://wpadvancedads.com/add-ons/amp-ads/?utm_source=wporg&utm_medium=link&utm_campaign=wp-features-amp-ads-3))

https://vimeo.com/577120971

Like j4ckson185, there are thousands of happy AdSense users:

> Your app is awesome, congratulations! Google Adsense suggests using your app on its official website, it’s incredible!

= Affiliate Marketing =

Easily integrate affiliate marketing with a comprehensive toolkit.

* unlimited affiliate ads and banners
* text link support
* affiliate disclosure
* automatic affiliate ad insertion
* advanced targeting
* dedicated AAWP ad type (Amazon Affiliate WordPress Plugin)
* integrates with other affiliate plugins like Pretty Links, Thirsty Affiliates, Lasso, and many more
* support for all Amazon ad formats
* compatible with all affiliate networks
* link masking and link cloaking ([Pro](https://wpadvancedads.com/add-ons/advanced-ads-pro/?utm_source=wporg&utm_medium=link&utm_campaign=wp-features-pro-7))
* affiliate ad tracking ([Tracking add-on](https://wpadvancedads.com/add-ons/tracking/?utm_source=wporg&utm_medium=link&utm_campaign=affiliate-tracking))


= Ads.txt =

* generates an ads.txt with custom content
* adds the content for AdSense to the ads.txt automatically

https://vimeo.com/577170591

= Add-Ons =

* all add-ons include priority email support
* [All Access](https://wpadvancedads.com/add-ons/all-access/?utm_source=wporg&utm_medium=link&utm_campaign=wp-add-ons) – includes all available add-ons
* [Advanced Ads Pro](https://wpadvancedads.com/advanced-ads-pro/?utm_source=wporg&utm_medium=link&utm_campaign=wp-add-ons) – powerful tools for ad optimizations: cache-busting, more placements, [lazy loading](https://wpadvancedads.com/lazy-load-ads/?utm_source=wporg&utm_medium=link&utm_campaign=features), ad blocker module, ad refresh, [click fraud protection](https://wpadvancedads.com/manual/click-fraud-protection/?utm_source=wporg&utm_medium=link&utm_campaign=features), [geo targeting](https://wpadvancedads.com/add-ons/geo-targeting/?utm_source=wporg&utm_medium=link&utm_campaign=features), [parallax ads](https://wpadvancedads.com/parallax-ads/?utm_source=wporg&utm_medium=link&utm_campaign=features) and many more
* [Tracking](https://wpadvancedads.com/add-ons/tracking/?utm_source=wporg&utm_medium=link&utm_campaign=wp-add-ons) – track ad impressions and ad clicks with local methods or Google Analytics
* [AMP Ads](https://wpadvancedads.com/add-ons/amp-ads/?utm_source=wporg&utm_medium=link&utm_campaign=wp-add-ons) – make your ads compatible with AMP and convert Google AdSense ads automatically
* [Google Ad Manager Integration](https://wpadvancedads.com/add-ons/google-ad-manager/?utm_source=wporg&utm_medium=link&utm_campaign=wp-add-ons) – a quick and error-free way to load ad units from your Google Ad Manager account without touching any ad codes
* [Sticky Ads](https://wpadvancedads.com/add-ons/sticky-ads/?utm_source=wporg&utm_medium=link&utm_campaign=wp-add-ons) – increase click rates with fixed, sticky, and anchor ads
* [Fixed Widget for WordPress](https://wordpress.org/plugins/q2w3-fixed-widget/) – turn sidebar widgets into performant fixed sticky ads
* [PopUp and Layer Ads](https://wpadvancedads.com/add-ons/popup-and-layer-ads/?utm_source=wporg&utm_medium=link&utm_campaign=wp-add-ons) – display ads and other content in layers, popups, and interstitials
* [Selling Ads](https://wpadvancedads.com/add-ons/selling-ads/?utm_source=wporg&utm_medium=link&utm_campaign=wp-add-ons) - allows you to sell ads on your website fully automated, including payments and advertiser profiles
* [Ad Slider](https://wpadvancedads.com/add-ons/slider/?utm_source=wporg&utm_medium=link&utm_campaign=wp-add-ons) – create a simple slider from your ads
* [Full Feature List](https://wpadvancedads.com/features/?utm_source=wporg&utm_medium=link&utm_campaign=wp-add-ons)

= Ad Blocker =

* basic features to prevent ads from being removed by ad blockers
* prevent ad blockers from breaking sites where plugin scripts are running
* dedicated visitor condition to target ads to users with activated ad blocker
* show alternative content to ad block users and improve the monetization of your website ([Pro](https://wpadvancedads.com/add-ons/advanced-ads-pro/?utm_source=wporg&utm_medium=link&utm_campaign=wp-features-pro-4))
* ad blocker fallback ads ([Pro](https://wpadvancedads.com/add-ons/advanced-ads-pro/?utm_source=wporg&utm_medium=link&utm_campaign=wp-features-pro-4))
* show an overlay to ad blocker users ([Pro](https://wpadvancedads.com/add-ons/advanced-ads-pro/?utm_source=wporg&utm_medium=link&utm_campaign=wp-features-pro-4))
* redirect ad blocker users ([Pro](https://wpadvancedads.com/add-ons/advanced-ads-pro/?utm_source=wporg&utm_medium=link&utm_campaign=wp-features-pro-4))
* learn more in our [ad blocker tutorial](https://wpadvancedads.com/manual/ad-blockers/?utm_source=wporg&utm_medium=link&utm_campaign=wp-ad-blocker).

Thank you for motivating us with your [positive review](https://wordpress.org/support/plugin/advanced-ads/reviews/?rate=5#new-post).

Localizations: Arabic, Chinese, Croatian, Czech, Danish, Dutch, English, French, German, Greek, Hebrew, Hungarian, Italian, Japanese, Norwegian, Polish, Portuguese, Romanian, Russian, Slovak, Slovenian, Spanish, Turkish, Vietnamese

If you have problems with Advanced Ads, please open a [new topic](https://wordpress.org/support/plugin/advanced-ads/#new-topic-0) in our forums on wordpress.org, or reach out to our [premium support](https://wpadvancedads.com/support/), if you have an active license.

= Integrations =

Advanced Ads integrates with plenty of other plugins:

- BuddyBoss – [How to integrate ads into BuddyBoss](https://wpadvancedads.com/manual/buddyboss-ads/)
- BuddyPress – [How to add ads on BuddyPress pages](https://wpadvancedads.com/ads-on-buddypress-pages/)
- bbPress – [How to show ads on bbPress pages?](https://wpadvancedads.com/ads-in-bbpress/)
- Cookie Consent, Borlabs Cookies, Complianz, GDPR Cookie Consent, and other privacy and consent manager – [How to show ads based on visitors’ consent](https://wpadvancedads.com/manual/ad-cookie-consent/)
- Elementor - [How to create and implement ads in Elementor](https://wpadvancedads.com/elementor-ads/)
- GamiPress – [How to target ads on GamiPress websites](https://wpadvancedads.com/manual/gamipress-ads/)
- Genesis – [Genesis Ads add-on](https://wpadvancedads.com/add-ons/genesis-ads/)
- [IAB TCF 2.2 consent](https://wpadvancedads.com/manual/tcf-consent-wordpress/) – hide ads until users give their consent. Integrating with any CMP, Quantcast Choices, iubenda, Google Funding Choices, cookiebot, etc.
- LearnDash - [How to integrate ads into LearnDash](https://wpadvancedads.com/learndash-ads/)
- MailPoet - [How to integrate Advanced Ads in MailPoet Newsletters](https://wpadvancedads.com/mailpoet-newsletters/)
- Paid Memberships Pro – [How to manage ads on membership sites running Paid Memberships Pro](https://wpadvancedads.com/paid-memberships-pro/)
- Polylang - [How to target ads to multiple languages in Polylang](https://wpadvancedads.com/polylang/)
- TranslatePress - [How to target  ads to multiple languages in TranslatePress](https://wpadvancedads.com/translatepress/)
- Weglot - [How to target ads to multiple languages in Weglot](https://wpadvancedads.com/weglot/)
- WPBakery Page Builder – [Displaying Ads with WPBakery Page Builder](https://wpadvancedads.com/visual-composer-ads/)
- WPML – [Showing different ads per language with WPML](https://wpadvancedads.com/translating-ads-wpml/)
- Youzify – [How to integrate ads in Youzify](https://wpadvancedads.com/how-to-integrate-ads-in-youzify/)

== Installation ==

How to install the plugin and get it working?

= Using The WordPress Dashboard =

1. Navigate to the 'Add New' in the plugins dashboard
2. Search for 'advanced ads'
3. Click 'Install Now'
4. Activate Advanced Ads on the Plugin dashboard

= Uploading in WordPress Dashboard =

1. Navigate to the 'Add New' in the plugins dashboard
2. Navigate to the 'Upload' area
3. Select `advanced-ads.zip` from your computer
4. Click 'Install Now'
5. Activate Advanced Ads in the Plugin dashboard

= Using FTP =

1. Download `advanced-ads.zip`
2. Extract the `advanced-ads` directory to your computer
3. Upload the `advanced-ads` directory to the `/wp-content/plugins/` directory
4. Activate Advanced Ads in the Plugin dashboard

== Frequently Asked Questions ==

= How to put ads on WordPress? =

You can use Advanced Ads to insert ads into your WordPress site without any coding.

To get started, just take a look at

* the [general first ad tutorial](https://wpadvancedads.com/manual/first-ad/)
* using [AdSense Auto ads](https://wpadvancedads.com/adsense-auto-ads-wordpress/) in WordPress
* the [AdSense overview page](https://wpadvancedads.com/adsense-ads).
* Ads not showing up? Take a look [here](https://wpadvancedads.com/manual/ads-not-showing-up).

= What about my users’ privacy and GDPR compliance? =

The plugin comes with Privacy settings that help you gather consent from users before showing ads to them. The feature works for any ads managed with the plugin, including AdSense Auto ads.

Once you enable one of the Privacy options, Advanced Ads blocks ads that need consent until it is given. You can disable that check for individual ads as well (e.g., for image ads).
You can also deliver non-personalized AdSense ads when that is legally allowed in your area.

- [Cookie consent integration](https://wpadvancedads.com/manual/ad-cookie-consent/).
- [IAB TCF 2.2 integration](https://wpadvancedads.com/manual/tcf-consent-wordpress/).

Advanced Ads itself does neither save personal information (e.g., an IP address) in your database nor cookies in the visitor’s browser.

You can learn more about how Advanced Ads and its add-ons handles data and privacy of your visitors [on this page](https://wpadvancedads.com/manual/privacy-information-for-users/).

= Which ad networks are supported? =

Advanced Ads is compatible with all ad networks and banners from affiliate programs like Awin, Google AdSense, Chitika, Clickbank, CJ Affiliate, Amazon, ShareASale, Impact, Rakuten, Booking.com, GetYourGuide, media.net, and also Google Ad Manager (formerly Google DoubleClick for Publishers, DFP).

You can also use it to insert additional ad network tags into your site's header or footer without coding to integrate header bidding.
Advanced Ads even provides a dedicated AdSense ad type to leverage this ad network's specific options.

= PHP functions and shortcodes =

You can use functions and shortcodes to display ads and ad groups.

The integers in this example are the IDs of the elements.

Use these shortcodes to insert an ad or group into your post/page.

`[the_ad id="24"]`
`[the_ad_group id="5"]`

Use these functions to insert an ad or ad group into your template file.

`<?php the_ad(24); ?>`
`<?php the_ad_group(5); ?>`

In addition to directly displaying ads and groups you can define ad placements and assign either an ad or group to them.

`[the_ad_placement id="header-left"]`
`<?php the_ad_placement('header-left'); ?>`

= Is there an ad revenue share? =

There is no revenue sharing. Advanced Ads doesn’t alter your ad codes in a way that you earn less than you would directly including the ad code in your template. To implement revenue sharing between multiple authors, follow the instructions of [this tutorial](https://wpadvancedads.com/publisher-revenue-share/).

= Can I place ads directly in my theme files? =

Yes. I would add a "Manual" placement into your theme files. It would allow you to change the displayed ads or groups later without changing your code again.

= Does it work with other ad plugins? =

Yes. Advanced Ads can be combined with other ad plugins.
Just use their shortcodes in our "Rich Media" ad type to combine both features.
Works with AdRotate, Ad Inserter, Ad Injection, Quick AdSense, Quick AdSense Reloaded (WPQUADS), Simple Ads Manager, and other plugins.
Advanced Ads can be used along Google Site Kit or replace it if you need more control over your ad setup.

= Is the plugin compatible with PHP 8? =

Yes, Advanced Ads supports current PHP versions and has been successfully tested on servers running PHP 8.

= Is the plugin compatible with page builders? =

Yes. It works out of the box with all site builders that allow shortcodes or widgets, like [Elementor](https://wpadvancedads.com/elementor-ads/), [Divi](https://wpadvancedads.com/divi-theme-builder/), [SiteOrigin](https://wpadvancedads.com/siteorigin-page-builder/), [Beaver Builder](https://wpadvancedads.com/beaver-builder-ads/), [Nimble Page Builder](https://wordpress.org/plugins/nimble-builder/), [Pagelayer](https://wpadvancedads.com/pagelayer/), and others.
There is also a [free add-on to support the WPBakery Page Builder](https://wordpress.org/plugins/ads-for-visual-composer/) (formerly Visual Composer).

= Will ads show up for ad block users? =

Visitors who have any ad blocker (e.g., AdBlock Plus) enabled won’t see ads from known external sources (e.g., AdSense).
You can still monetize those spots with custom content.

Read more about ad blockers and the features Advanced Ads has to deal with them [on this page](https://wpadvancedads.com/manual/ad-blockers/).

= Does the plugin support an ads.txt? =

Google AdSense and some other networks ask you to provide an ads.txt.
Advanced Ads can create that file automatically with the correct information for AdSense, when you enable the ads.txt feature in Advanced Ads > Settings > General > ads.txt and enter your AdSense publisher ID in Advanced Ads > Settings > AdSense.

= I am a developer. Can I customize the plugin? =

Yes. You can use plenty of [hooks](https://wpadvancedads.com/codex/) to customize Advanced Ads.

== Screenshots ==

1. Placements that let you inject ads anywhere into your site without coding (7 in Advanced Ads + 14 through add-ons).
2. Support for all kinds of ad types, including dedicated AdSense types. AMP is included in the AMP Ads add-on.
3. Flexible ad input with the Plain Text ad type and code highlighting.
4. Align your ads within the content.
5. Dynamically change AdSense ad options in your WordPress backend.
6. Choose where to display your ads using many conditions.
7. Use various conditions to choose who should see ads (basic plugin and more in add-ons).
8. See AdSense earnings in your WP Backend
9. Track impressions and clicks (Tracking add-on).
10. Convert AdSense ads into AMP automatically (AMP Ads add-on)

== Changelog ==

= 2.0.9 (May 21, 2025) =

- Improvement: improve amp conditions
- Improvement: Test plugin compatibility with WordPress 6.8.
- Improvement: add capability check in importer
- Improvement: standardize `ADVADS_ADS_DISABLED` check
- Improvement: null checks before accessing ad properties
- Fix: update failure help link only for Advanced Ads add-ons

= 2.0.8 (April 28, 2025) =

- Improvement: type safety in `add_body_class` to prevent issues from third-party filters
- Improvement: improve the ad and group relation both way
- Fix: expiry date not updating for ads that already had one set

= 2.0.7 (April 9, 2025) =
- Improvement: optimize MailPoet compatibility
- Fix: force array return in post_updated_messages
- Fix: quick edit for ads ignores tracking options and overwrites content

= 2.0.6 (April 1, 2025) =
- Improvement: redirect to placement list after creating a new placement
- Improvement: remove ad creation message for server type placement
- Improvement: unify admin notifications
- Fix: prevent duplicate revision controls
- Fix: correct order of placement icons
- Fix: open create placement modal when no placements found
- Fix: show the search field when "Show filters permanently" is checked
- Fix: prevent CodeMirror to run on Rich Content ad edit page
- Fix: prevent 'undefined key' warning in XML feed
- Fix: use native php get error message function

= 2.0.5 (March 20, 2025) =

- Fix: prevent false unsaved changes notifications for ads
- Fix: prevent errors with certain custom placement types
- Fix: hide discarded notification on the Advanced Ads dashboard
- Fix: prevent an error on the Advanced Ads dashboard
- Fix: correct ad expiry date inconsistencies
- Fix: prevent fatal error on Gutenberg editor
- Fix: restore execute shortcode feature

= 2.0.4 (March 18, 2025) =

- Improvement: add update functionality to ensure receipt of premium updates
- Fix: resolve a fatal error caused by an invalid license
- Fix: prevent ad expiry from changing during save when timezone is not UTC
- Fix: prevent unknown placement types from breaking the page
- Fix: ensure "Execute Shortcodes" function is savable again
- Fix: restore PeepSo placement compatibility for Advanced Ads 2.0

= 2.0.3 (March 12, 2025) =

- Improvement: reflect placement status changes immediately in the placement list
- Improvement: prevent plugin upgrade notice reappearing if already dismissed
- Fix: ensure ad centers when selected
- Fix: restore pagination functionality on ad list page
