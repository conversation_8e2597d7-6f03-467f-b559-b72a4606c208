jQuery(document).ready((function(){if(!(!advads.supports_localstorage()||!localStorage.getItem("advads_frontend_picker")||window.advads_options.blog_id&&localStorage.getItem("advads_frontend_blog_id")&&window.advads_options.blog_id!==localStorage.getItem("advads_frontend_blog_id")||localStorage.getItem("advads_frontend_starttime")&&parseInt(localStorage.getItem("advads_frontend_starttime"),10)<(new Date).getTime()-27e5&&(localStorage.removeItem("advads_frontend_action"),localStorage.removeItem("advads_frontend_element"),localStorage.removeItem("advads_frontend_picker"),localStorage.removeItem("advads_prev_url"),localStorage.removeItem("advads_frontend_pathtype"),localStorage.removeItem("advads_frontend_boundary"),localStorage.removeItem("advads_frontend_blog_id"),localStorage.removeItem("advads_frontend_starttime"),advads.set_cookie("advads_frontend_picker","",-1),1))){var e,t=jQuery("<div id='advads-picker-overlay'>"),a=[document.body,document.documentElement,document];if(t.css({position:"absolute",border:"solid 2px #428bca",backgroundColor:"rgba(66,139,202,0.5)",boxSizing:"border-box",zIndex:1e6,pointerEvents:"none"}).prependTo("body"),"true"===localStorage.getItem("advads_frontend_boundary")&&jQuery("body").css("cursor","not-allowed"),window.advads.is_boundary_reached=function(e){if("true"!==localStorage.getItem("advads_frontend_boundary"))return!1;$advads_picker_cur=jQuery(e);var t=jQuery(".advads-frontend-picker-boundary-helper");return $boundaries=t.parent(),$boundaries.css("cursor","pointer"),$advads_picker_cur.is($boundaries)||!$advads_picker_cur.closest($boundaries).length},"xpath"===localStorage.getItem("advads_frontend_pathtype"))var o="getXPath";else o="getPath";jQuery(document).mousemove((function(r){if(r.target!==e){if(~a.indexOf(r.target))return e=null,void t.hide();var d=jQuery(r.target),n=d.offset(),s=d.outerWidth(),i=d.outerHeight();e=r.target;var l=jQuery(e)[o]();l&&(console.log(l),t.css({top:n.top,left:n.left,width:s,height:i}).show())}})),jQuery(document).click((function(t){var a=jQuery(e)[o]();advads.is_boundary_reached(e)||(localStorage.setItem("advads_frontend_element",a),window.location=localStorage.getItem("advads_prev_url"))}))}})),jQuery.fn.extend({getPath:function(e,t){if(void 0===e&&(e=""),void 0===t&&(t=0),this.is("html"))return"html > "+e;if(3===t)return e;var a=this.get(0).nodeName.toLowerCase(),o=this.attr("id"),r=this.attr("class");return t+=1,void 0===o||/\d/.test(o)?void 0!==r&&(r=r.split(/[\s\n]+/),(r=jQuery.grep(r,(function(e,t){return!/\d/.test(e)}))).length&&(a+="."+r.slice(0,2).join("."))):a+="#"+o,this.siblings(a).length&&(a+=":eq("+this.siblings(a).addBack().not("#advads-picker-overlay").index(this)+")"),""===e?this.parent().getPath(a,t):this.parent().getPath(a+" > "+e,t)},getXPath:function(e,t){if(void 0===e&&(e=""),void 0===t&&(t=0),this.is("body")||3===t)return e;if(advads.is_boundary_reached(this))return e;var a=this.get(0).nodeName.toLowerCase(),o=a,r=this.attr("id"),d=this.attr("class"),n=[];if(void 0!==r&&!/\d/.test(r))return o+'[@id and id="'+r+'"]/'+e;if(void 0!==d&&(d=d.split(/[\s\n]+/),(d=jQuery.grep(d,(function(e,t){return!/\d/.test(e)}))).length)){t+=1;for(var s=[],i=0,l=(n=d.slice(0,2)).length;i<l;i++)s.push('(@class and contains(concat(" ", normalize-space(@class), " "), " '+n[i]+' "))');o+="["+s.join(" and ")+"]"}if(n.length)var c=this.siblings(a+"."+n.join("."));else c=this.siblings(a);return c.length&&(o+="["+c.addBack().not("#advads-picker-overlay").index(this)+"]"),""===e?this.parent().getXPath(o,t):this.parent().getXPath(o+"/"+e,t)}});