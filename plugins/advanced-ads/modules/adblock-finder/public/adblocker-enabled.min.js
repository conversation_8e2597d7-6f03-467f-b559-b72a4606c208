window.advanced_ads_check_adblocker=function(){var t=[],n=null;function e(t){var n=window.requestAnimationFrame||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame||function(t){return setTimeout(t,16)};n.call(window,t)}return e((function(){var i=document.createElement("div");i.innerHTML="&nbsp;",i.setAttribute("class","ad_unit ad-unit text-ad text_ad pub_300x250"),i.setAttribute("style","width: 1px !important; height: 1px !important; position: absolute !important; left: 0px !important; top: 0px !important; overflow: hidden !important;"),document.body.appendChild(i),e((function(){var e,o,a=null===(e=(o=window).getComputedStyle)||void 0===e?void 0:e.call(o,i),d=null==a?void 0:a.getPropertyValue("-moz-binding");n=a&&"none"===a.getPropertyValue("display")||"string"==typeof d&&-1!==d.indexOf("about:");for(var r=0,u=t.length;r<u;r++)t[r](n);t=[]}))})),function(e){"undefined"==typeof advanced_ads_adblocker_test&&(n=!0),null!==n?e(n):t.push(e)}}();