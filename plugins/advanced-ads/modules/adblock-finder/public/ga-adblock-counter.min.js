window.advanced_ads_check_adblocker=function(){var t=[],n=null;function e(t){var n=window.requestAnimationFrame||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame||function(t){return setTimeout(t,16)};n.call(window,t)}return e((function(){var a=document.createElement("div");a.innerHTML="&nbsp;",a.setAttribute("class","ad_unit ad-unit text-ad text_ad pub_300x250"),a.setAttribute("style","width: 1px !important; height: 1px !important; position: absolute !important; left: 0px !important; top: 0px !important; overflow: hidden !important;"),document.body.appendChild(a),e((function(){var e,o,i=null===(e=(o=window).getComputedStyle)||void 0===e?void 0:e.call(o,a),d=null==i?void 0:i.getPropertyValue("-moz-binding");n=i&&"none"===i.getPropertyValue("display")||"string"==typeof d&&-1!==d.indexOf("about:");for(var c=0,r=t.length;c<r;c++)t[c](n);t=[]}))})),function(e){"undefined"==typeof advanced_ads_adblocker_test&&(n=!0),null!==n?e(n):t.push(e)}}(),(()=>{function t(t){this.UID=t,this.analyticsObject="function"==typeof gtag;var n=this;return this.count=function(){gtag("event","AdBlock",{event_category:"Advanced Ads",event_label:"Yes",non_interaction:!0,send_to:n.UID})},function(){if(!n.analyticsObject){var e=document.createElement("script");e.src="https://www.googletagmanager.com/gtag/js?id="+t,e.async=!0,document.body.appendChild(e),window.dataLayer=window.dataLayer||[],window.gtag=function(){dataLayer.push(arguments)},n.analyticsObject=!0,gtag("js",new Date)}var a={send_page_view:!1,transport_type:"beacon"};window.advanced_ads_ga_anonymIP&&(a.anonymize_ip=!0),gtag("config",t,a)}(),this}advanced_ads_check_adblocker((function(n){n&&new t(advanced_ads_ga_UID).count()}))})();