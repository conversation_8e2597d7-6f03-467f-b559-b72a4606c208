function modal_submit_form(e,a,t,d=""){""===d||window[d](t)?document.getElementById(a).submit():e.preventDefault()}function advads_set_tab_hashes(){jQuery("#advads-tabs").find("a").each((function(){const e=jQuery(this).attr("id").replace("-tab",""),a=jQuery("#"+e).children(".advads-settings-tab-main-form");if(a.length){const e=a.attr("action").split("#")[0];a.attr("action",e+jQuery(this).attr("href"))}}))}function advads_scroll_to_element(e){const a=jQuery("#wpadminbar").height();jQuery("html, body").animate({scrollTop:jQuery(e).offset().top-a},1e3)}function advads_toggle(e){jQuery(e).slideToggle()}function advads_toggle_box(e,a){jQuery(e).is(":checked")?jQuery(a).slideDown():jQuery(a).slideUp()}function advads_toggle_box_enable(e,a){jQuery(e).is(":checked")?jQuery(a).find("input").removeAttr("disabled",""):jQuery(a).find("input").attr("disabled","disabled")}function advads_validate_new_form(e){return jQuery(".advads-form-type input:checked").length?(jQuery(".advads-form-type-error").hide(),""==jQuery(".advads-form-name").val()?(jQuery(".advads-form-name-error").show(),!1):(jQuery(".advads-form-name-error").hide(),!0)):(jQuery(".advads-form-type-error").show(),!1)}function advads_group_edit_submit(e){return jQuery(".advads-ad-group-form").filter(((a,t)=>!jQuery(t).parents(e).length)).remove(),!0}function advads_maybe_textarea_to_tinymce(e){let a=jQuery("#advads-ad-content-plain"),t=a.val(),d="advanced-ads-tinymce",n=jQuery("#"+d),s=jQuery("#advanced-ads-tinymce-wrapper");if("content"!==e)return n.prop("name",d),s.hide(),!1;if("object"==typeof tinyMCE&&null!==tinyMCE.get(d)){if(t){const e=window.wp;e&&e.editor&&e.editor.autop&&tinyMCE.get(d).getParam("wpautop",!0)&&(t=e.editor.autop(t)),tinyMCE.get(d).setContent(t)}a.remove(),n.prop("name",a.prop("name")),s.show()}else n.length&&(n.val(t),a.remove(),n.prop("name",a.prop("name")),s.show())}function advads_show_adsense_auto_ads_warning(){$msg=jQuery(".advads-auto-ad-in-ad-content").show(),$msg.on("click","button",(function(){$msg.hide(),jQuery.ajax({type:"POST",url:ajaxurl,data:{action:"advads-adsense-enable-pla",nonce:advadsglobal.ajax_nonce}}).done((function(e){$msg.show().html(advadstxt.page_level_ads_enabled)})).fail((function(e,a){$msg.show()}))}))}function advads_ads_txt_find_issues(){const e=jQuery("#advads-ads-txt-notice-wrapper"),a=jQuery("#advads-ads-txt-notice-refresh"),t=jQuery(".advads-ads-txt-action");function d(a){t.toggle(!a),a&&e.html('<span class="spinner advads-spinner"></span>')}function n(a){e.html(a.notices),d(!1)}function s(a){e.html('<p class="advads-notice-inline advads-error">'+jQuery("#advads-ads-txt-notice-error").text().replace("%s",parseInt(a.status,10)),NaN),d(!1)}function o(e){d(!0),jQuery.ajax({type:"POST",url:ajaxurl,data:{action:"advads-ads-txt",nonce:advadsglobal.ajax_nonce,type:e}}).done(n).fail(s)}e.length&&(e.find("ul").length||o("get_notices"),a.on("click",(function(){o("get_notices")})),jQuery(document).on("click","#advads-ads-txt-remove-real",(function(e){e.preventDefault();const a={data:{action:"advads-ads-txt",nonce:advadsglobal.ajax_nonce,type:"remove_real_file"},done(e){e.additional_content&&jQuery("#advads-ads-txt-additional-content").val(e.additional_content),n(e)},fail:s,before_send(){d(!0)}};advanced_ads_admin.filesystem.ajax(a)})),jQuery(document).on("click","#advads-ads-txt-create-real",(function(e){e.preventDefault();const a={data:{action:"advads-ads-txt",nonce:advadsglobal.ajax_nonce,type:"create_real_file"},done:n,fail:s,before_send(){d(!0)}};advanced_ads_admin.filesystem.ajax(a)})))}jQuery(document).ready((function(e){function a(a=!0){const t=e("button.advads-license-activate, button.advads-license-deactivate");a?t.attr("disabled","disabled"):t.removeAttr("disabled")}function t(e){const a=e.replace(/^#top(#|%23)/,"").replace(/(#|%23)/,"").split("__");return{tab:a[0]||jQuery(".advads-tab").attr("id"),anchor:a[1]}}function d(e){if(jQuery("#advads-tabs").find("a").removeClass("nav-tab-active"),jQuery(".advads-tab").removeClass("active"),jQuery("#"+e.tab).addClass("active"),jQuery("#"+e.tab+"-tab").addClass("nav-tab-active"),e.anchor){const a=document.getElementById(e.anchor).getBoundingClientRect().top,t=48;window.scrollTo(0,a+window.scrollY-t)}}e(document).on("click","#switch-to-adsense-type",(function(a){a.preventDefault(),AdvancedAdsAdmin.AdImporter.adsenseCode=Advanced_Ads_Admin.get_ad_source_editor_text(),e("#advanced-ad-type-adsense").trigger("click"),e(this).closest("li").addClass("hidden")})),e(".advads-buttonset").advads_buttonset(),e.fn.accordion&&e(".advads-accordion").accordion({active:!1,collapsible:!0}),e(".advads-ad-list-tooltip").advads_tooltip({content(){return jQuery(this).find(".advads-ad-list-tooltip-content").html()}}),e(".post-type-advanced_ads .wp-list-table thead th:last-of-type").append('<span class="dashicons dashicons-edit"></span>').on("click",(function(){e("#show-settings-link").trigger("click")})),(()=>{let e="0",a="";const t=jQuery(".advads-placements-table tbody tr"),d=d=>{t.each(((t,d)=>{const n=jQuery(d),s=n.data("order");void 0!==s&&void 0!==s.type&&void 0!==s.name?n.toggle(!("0"!==e&&s.type!==e||""!==a&&-1===s.name.toLowerCase().indexOf(a.toLowerCase()))):n.show()}))};jQuery(".advads_filter_placement_type").on("change",(function(){e=jQuery(this).val(),d()})),jQuery(".advads_search_placement_name").on("keyup",(function(){a=this.value,d()}))})(),document.querySelectorAll('[name="advads[placement][type]"]').forEach((e=>{e.addEventListener("input",(e=>{jQuery('[name="advads[placement][item]"]').attr("disabled",!0),wp.ajax.post(window.advadstxt.placements_allowed_ads.action,{_ajax_nonce:window.advadstxt.placements_allowed_ads.nonce,placement_type:e.target.value}).done((e=>{jQuery('[name="advads[placement][item]"]').replaceWith(wp.template("advads-placement-ad-select")({items:Object.values(e.items)}))}))}))})),jQuery(".advads-delete-tag").each((function(){jQuery(this).on("click",(function(){if(!0===confirm(window.advadstxt.delete_placement_confirmation)){const e=jQuery(this).parents(".advanced-ads-placement-row");e.find(".advads-placements-item-delete").prop("checked",!0),e.data("touched",!0),jQuery("#advanced-ads-placements-form").submit()}}))})),jQuery(".advads-sort").on("click",(function(e){const a=jQuery(this),t=a.data("orderby"),d=jQuery(".advads-placements-table"),n=jQuery("> tbody > tr",d);jQuery("> thead th > a",d).each((function(){jQuery(this).removeClass("advads-placement-sorted")})),a.addClass("advads-placement-sorted"),n.sort((function(e,a){const d=jQuery(e).data("order"),n=jQuery(a).data("order");return"type"===t?d["words-between-repeats"]!==n["words-between-repeats"]?d["words-between-repeats"]?1:-1:d.order===n.order?d["post-content-index"]&&n["post-content-index"]&&d["post-content-index"]!==n["post-content-index"]?d["post-content-index"]<n["post-content-index"]?-1:1:d.name.localeCompare(n.name,void 0,{numeric:!0}):d.order-n.order:d.name.localeCompare(n.name,void 0,{numeric:!0})})),jQuery.each(n,(function(e,a){d.append(a)}));let s=window.location.pathname+window.location.search;-1!==s.indexOf("orderby=")?s=s.replace(/\borderby=[0-9a-zA-Z_@.#+-]{1,50}\b/,"orderby="+t):s+="&orderby="+t,window.history.replaceState({orderby:t},document.title,s),e.preventDefault()})),e("#advads-output-wrapper-id").on("keyup",(function(){const a=e(this).val();/^[a-z-0-9]*$/.test(a)?e(".advads-output-wrapper-id-error").addClass("hidden"):e(".advads-output-wrapper-id-error").removeClass("hidden")})),e(".advads-group-ads-list-show-more").on("click",(function(){e(this).hide().parent().siblings(".advads-ad-group-list-ads").children("div").show()})),e(".advads-settings-tab-main-form .advads-license-key").on("blur",(function(){const a=e(this).val();if(""===a)return;const t=e(".advads-settings-tab-main-form .advads-license-key"),d=[];t.each((function(a,t){""===e(t).val()&&d.push(t)})),t.length===d.length+1&&e.each(d,(function(t,d){e(d).val(a)}))})),e(".advads-license-activate").on("click",(function(){const t=e(this);if(!this.dataset.addon)return;a(!0);const d={action:"advads-activate-license",addon:this.dataset.addon,pluginname:this.dataset.pluginname,optionslug:this.dataset.optionslug,license:e(this).parents("td").find(".advads-license-key").val(),security:e("#advads-licenses-ajax-referrer").val()};e('<span class="spinner advads-spinner"></span>').insertAfter(t),e.post(ajaxurl,d,(function(n){e("span.spinner").remove();const s=t.parents("td");if("1"===n){const e="advanced-ads-licenses["+d.addon+"]";o.setInitialValue(e,document.querySelector('[name="'+e+'"]')),s.find(".advads-license-activate-error").remove(),s.find(".advads-license-deactivate").show(),t.fadeOut(),s.find(".advads-license-activate-active").fadeIn(),s.find("input").prop("readonly","readonly"),a(!1)}else if("ex"===n){const e=s.find("input.advads-license-key"),t=s.find("a.advads-renewal-link");if(e&&t){const a=e.val(),d=t.prop("href");t.prop("href",d.replace("%LICENSE_KEY%",a))}s.find(".advads-license-activate-error").remove(),s.find(".advads-license-expired-error").show(),a(!1)}else s.find(".advads-license-activate-error").show().html(n),a(!1)}))})),e(".advads-license-deactivate").on("click",(function(){const t=e(this);if(!this.dataset.addon)return;a(!0);const d={action:"advads-deactivate-license",addon:this.dataset.addon,pluginname:this.dataset.pluginname,optionslug:this.dataset.optionslug,security:e("#advads-licenses-ajax-referrer").val()};e('<span class="spinner advads-spinner"></span>').insertAfter(t),e.post(ajaxurl,d,(function(d){e("span.spinner").remove(),"1"===d?(t.siblings(".advads-license-activate-error").hide(),t.siblings(".advads-license-activate-active").hide(),t.siblings(".advads-license-activate").show(),t.siblings("input").prop("readonly",!1),t.fadeOut(),a(!1)):"ex"===d?(t.siblings(".advads-license-activate-error").hide(),t.siblings(".advads-license-activate-active").hide(),t.siblings(".advads-license-expired-error").show(),t.siblings("input").prop("readonly",!1),t.fadeOut(),a(!1)):(console.log(d),t.siblings(".advads-license-activate-error").show().html(d),t.siblings(".advads-license-activate-active").hide(),a(!1))}))})),jQuery(document).on("click",'a[href*="page=advanced-ads-settings"]:not(.nav-tab)',(function(){d(t(jQuery(this).attr("href").split("advanced-ads-settings")[1]))})),window.addEventListener("hashchange",(e=>{const a=t(new URL(e.newURL).hash);try{document.getElementById(a.tab+"-tab").dispatchEvent(new Event("click"))}catch(e){}}));d(t(window.location.hash)),advads_set_tab_hashes(),jQuery(".advads-tab-sub-menu").each((function(e,a){if("function"!=typeof a.scrollIntoView)return;advads_settings_parent_tab=jQuery(a).parent(".advads-tab");const t=advads_settings_parent_tab.find("h2");t.length>1&&(advads_submenu_list=jQuery("<ul>"),t.each((function(e,a){const t="advads-tab-headline-"+advads_settings_parent_tab.attr("id")+e;jQuery(a).attr("id",t);var d=d=a.textContent||a.innerText;jQuery("<li><a onclick=\"document.getElementById('"+t+"').scrollIntoView()\">"+d+"</a></li>").appendTo(advads_submenu_list)})),advads_submenu_list.appendTo(a))})),e('.post-type-advanced_ads .check-column input[type="checkbox"]').on("change",(function(){e(".post-type-advanced_ads .tablenav.bottom .bulkactions").toggleClass("fixed",0<e('.post-type-advanced_ads .check-column input[type="checkbox"]:checked').length)})),e("#advads-show-screen-options").on("click",(function(){e("#show-settings-link").trigger("click")})),e('<button type="button" class="button advads-button-secondary">'+advadstxt.close+"</button>").appendTo(e(".post-type-advanced_ads #adv-settings .submit")).on("click",(function(){e("#show-settings-link").trigger("click")}));const n=function(){const a=e(this).closest("tr.advanced-ads-placement-row");a&&a.data("touched",!0)};function s(a){"custom"===e(a).find("option:selected").val()?e(a).next(".advads-placements-content-custom-xpath").show():e(a).next(".advads-placements-content-custom-xpath").hide()}e("form#advanced-ads-placements-form input, #advanced-ads-placements-form select").on("change",n),e("form#advanced-ads-placements-form button").on("click",n),e("form#advanced-ads-placements-form .advads-modal").on("mouseover",n),e(document).on("advads-modal-canceled",(a=>{const t=e("#"+a.detail.modal_id).parents(".advanced-ads-placement-row");t.length&&t.data("touched",!1)})),e("form#advanced-ads-placements-form").on("submit",(function(){jQuery("form#advanced-ads-placements-form tr.advanced-ads-placement-row");jQuery("form#advanced-ads-placements-form tr.advanced-ads-placement-row").each((function(e,a){(a=jQuery(a)).data("touched")||a.find("input, select").each((function(e,a){(a=jQuery(a)).prop("disabled",!0)}))}))})),e(".advads-placements-content-tag").each((function(){s(this)})),e(".advads-placements-content-tag").on("change",(function(){s(this)})),e(".advads-form-type").advads_tooltip({content(){return jQuery(this).find(".advads-form-description").html()},parent:e=>{const a=e.parents(".advads-modal");return a.length?"#"+a[0].id:"body"}});const o=(()=>{let e,a,n=!1;if("advanced-ads_page_advanced-ads-placements"===window.advadstxt.admin_page&&(a=document.getElementById("advanced-ads-placements-form"),null!==a&&(e=new Advads_Termination(a))),"advanced_ads"===window.advadstxt.admin_page&&(a=document.getElementById("post"),null!==a&&(e=new Advads_Termination(a))),"advanced-ads_page_advanced-ads-settings"===window.advadstxt.admin_page&&(a=document.querySelector(".advads-tab.active > form"),null!==a&&(e=new Advads_Termination(a)),[...document.getElementsByClassName("nav-tab")].forEach((s=>{s.addEventListener("click",(s=>{if(!e.terminationNotice())return s.preventDefault(),e;d(t(new URL(s.target.href).hash)),a=document.querySelector(".advads-tab.active > form"),null!==a&&(e=new Advads_Termination(a),e.collectValues(),a.addEventListener("submit",(()=>{n=!0})))}))}))),void 0!==e){e.collectValues();const t=a=>{if(!n&&!e.terminationNotice())return a.preventDefault(),a.returnValue="string",e};window.addEventListener("beforeunload",t),a.addEventListener("submit",(()=>{n=!0}))}return e})();let i;advancedAds.termination=o,e("body").on("click",".advads_image_upload",(function(a){a.preventDefault();const t=e(this);i||(i=wp.media.frames.file_frame=wp.media({id:"advads_type_image_wp_media",title:t.data("uploaderTitle"),button:{text:t.data("uploaderButtonText")},library:{type:"image"},multiple:!1}),i.on("select",(function(){i.state().get("selection").each((function(a,t){if(a=a.toJSON(),0===t){e("#advads-image-id").val(a.id),e('#advanced-ads-ad-parameters-size input[name="advanced_ad[width]"]').val(a.width),e('#advanced-ads-ad-parameters-size input[name="advanced_ad[height]"]').val(a.height);const t='<img width="'+a.width+'" height="'+a.height+'" title="'+a.title+'" alt="'+a.alt+'" src="'+a.url+'"/>';e("#advads-image-preview").html(t),e("#advads-image-edit-link").attr("href",a.editLink),e("#advads-image-edit-link").removeClass("hidden"),e("#advanced-ads-ad-parameters-size input[type=number]:first").trigger("change")}}))}))),i.open()})),window.formfield="",e("#advanced-ads-use-adblocker").on("change",(function(){advads_toggle_box(this,"#advads-adblocker-wrapper")}));let r=e("#advads-adblocker-wrapper"),c=e("#advads-adblocker-rebuild");c.prop("disabled",!1),e(document).on("click","#advads-adblocker-rebuild",(function(a){a.preventDefault();const t=e("#advanced-ads-rebuild-assets-form");t.prev(".error").remove(),c.prop("disabled",!0).after('<span class="spinner advads-spinner"></span>');const d={data:{action:"advads-adblock-rebuild-assets",nonce:advadsglobal.ajax_nonce},done(a){r.html(a),c=e("#advads-adblocker-rebuild")},fail(e,a,d){t.before('<div class="error"><p>'+a+": "+d+"</p></div>"),c.prop("disabled",!1).next(".advads-spinner").remove()},on_modal_close(){c.prop("disabled",!1).next(".advads-spinner").remove()}};e('[name="advads_ab_assign_new_folder"]').is(":checked")&&(d.data.advads_ab_assign_new_folder=!0),advanced_ads_admin.filesystem.ajax(d)})),e("#advanced-ads-ad-parameters").on("change","#advanced-ads-ad-parameters-size input[type=number]",(function(){e("#advanced-ads-ad-parameters-size input[type=number]").filter((function(){return parseInt(this.value,10)>0})).length>=1?e("#advads-wrapper-add-sizes").prop("disabled",!1):e("#advads-wrapper-add-sizes").prop("disabled",!0).prop("checked",!1)})),e("#advanced-ads-ad-parameters").on("paramloaded",(function(){e("#advanced-ads-ad-parameters-size input[type=number]:first").trigger("change")})),e("#advanced-ads-ad-parameters-size input[type=number]:first").trigger("change"),e(".advads-hndlelinks").each((function(){e(this).appendTo(e(this).parents(".postbox").find("h2.hndle")),e(this).removeClass("hidden")})),e(".advads-video-link").on("click",(function(a){a.preventDefault();const t=e(a.target).closest(".postbox");t.removeClass("closed");const d=t.find(".advads-video-link-container");d.html(d.data("videolink"))}));const l=jQuery("textarea[name=advanced_ad\\[content\\]]").text()||"";(-1!==l.indexOf("enable_page_level_ads")||/script[^>]+(?:data-ad-client=|adsbygoogle\.js\?client=)/.test(l))&&advads_show_adsense_auto_ads_warning(),advads_ads_txt_find_issues(),jQuery(".advanced-ads-adsense-dashboard").each((function(e,a){Advanced_Ads_Adsense_Report_Helper&&Advanced_Ads_Adsense_Report_Helper.init(a)}))})),window.advanced_ads_admin=window.advanced_ads_admin||{},advanced_ads_admin.filesystem={_locked_job:!1,_requestForCredentialsModalToggle(){this.$filesystemModal.toggle(),jQuery("body").toggleClass("modal-open")},_init(){this._init=function(){};const e=this;e.$filesystemModal=jQuery("#advanced-ads-rfc-dialog"),e.$filesystemModal.on("submit","form",(function(a){a.preventDefault(),e.ajax(e._locked_job,!0),e._requestForCredentialsModalToggle()})),e.$filesystemModal.on("click",'[data-js-action="close"]',(function(){jQuery.isPlainObject(e._locked_job)&&e._locked_job.on_modal_close&&e._locked_job.on_modal_close(),e._locked_job=!1,e._requestForCredentialsModalToggle()}))},ajax(e,a){if(this._init(),!a&&this.$filesystemModal.length>0)return this._requestForCredentialsModalToggle(),this.$filesystemModal.find("input:enabled:first").focus(),void(this._locked_job=e);const t={method:"POST",url:window.ajaxurl,data:{username:jQuery("#username").val(),password:jQuery("#password").val(),hostname:jQuery("#hostname").val(),connection_type:jQuery('input[name="connection_type"]:checked').val(),public_key:jQuery("#public_key").val(),private_key:jQuery("#private_key").val(),_fs_nonce:jQuery("#_fs_nonce").val()}};e.before_send&&e.before_send(),t.data=jQuery.extend(t.data,e.data);const d=jQuery.ajax(t);e.done&&d.done(e.done),e.fail&&d.fail(e.fail)}},window.Advanced_Ads_Admin=window.Advanced_Ads_Admin||{reassign_ad(e){let a=jQuery("#post_author_override");a.length||(a=jQuery("#post_author")),a.val(e).queue((()=>{jQuery("#post").submit()}))},toggle_placements_visibility(e,a){const t=jQuery(e).next(".advads-placements-advanced-options");if(void 0!==a?!a:t.is(":visible"))t.hide(),jQuery("#advads-last-edited-placement").val("");else{const a=jQuery(e).parents(".advads-placements-table-options").find(".advads-placement-id").val();t.show(),jQuery("#advads-last-edited-placement").val(a);const d=jQuery(e).closest("tr.advanced-ads-placement-row");d&&d.data("touched",!0)}},get_cookie(e){let a,t,d,n=document.cookie.split(";");for(a=0;a<n.length;a++)if(t=n[a].substr(0,n[a].indexOf("=")),d=n[a].substr(n[a].indexOf("=")+1),t=t.replace(/^\s+|\s+$/g,""),t===e)return unescape(d)},set_cookie(e,a,t,d,n,s){const o=null==t?null:24*t*60*60;this.set_cookie_sec(e,a,o,d,n,s)},set_cookie_sec(e,a,t,d,n,s){const o=new Date;o.setSeconds(o.getSeconds()+parseInt(t)),document.cookie=e+"="+escape(a)+(null==t?"":"; expires="+o.toUTCString())+(null==d?"; path=/":"; path="+d)+(null==n?"":"; domain="+n)+(null==s?"":"; secure")}},window.AdvancedAdsAdmin||(window.AdvancedAdsAdmin={}),window.AdvancedAdsAdmin.AdImporter||(window.AdvancedAdsAdmin.AdImporter={highlightSelectedRowInExternalAdsList(e){void 0===e&&(e=AdvancedAdsAdmin.AdImporter.adNetwork.hideIdle);const a=jQuery("#mapi-table-wrap tbody");jQuery("#mapi-toggle-idle");(e?jQuery("#mapi-table-wrap tbody tr[data-active=1]").length:jQuery("#mapi-table-wrap tbody tr").length)>8?jQuery("#mapi-table-wrap").addClass("overflow"):jQuery("#mapi-table-wrap").removeClass("overflow");const t=AdvancedAdsAdmin.AdImporter.getSelectedRow();a.find("tr").removeClass("selected error"),t&&t.show();const d=a.find("tr:visible");return d.filter(":odd").css("background-color","#FFFFFF"),d.filter(":even").css("background-color","#F9F9F9"),t&&(t.css("background-color",""),t.addClass("selected"),this.scrollToSelectedRow(t)),t||!1},scrollToSelectedRow(e){const a=jQuery("#mapi-table-wrap"),t=a.height(),d=a.scrollTop();if(!e)return void a.animate({scrollTop:0},200);let n=e.position().top,s=+n+e.height();(n<d||s>d||n>d+t)&&(s>a.children("table").height()-t&&(n=s),n<t&&(n=0),a.animate({scrollTop:n},200))},getSelectedRow(){const e=AdvancedAdsAdmin.AdImporter.adNetwork.getSelectedId(),a=jQuery("#mapi-table-wrap tbody");if(e){const t=a.find('tr[data-slotid="'+e+'"]');if(t.length)return t}return null},openExternalAdsList(){const e=AdvancedAdsAdmin.AdImporter.adNetwork;e.openSelector(),jQuery(".mapi-insert-code").css("display","inline"),jQuery(".mapi-open-selector").css("display","none"),jQuery(".mapi-close-selector-link").css("display","inline"),jQuery(".advads-adsense-code").css("display","none"),jQuery("#remote-ad-unsupported-ad-type").css("display","none"),AdvancedAdsAdmin.AdImporter.highlightSelectedRowInExternalAdsList(e.hideIdle);if(e.getCustomInputs().css("display","none"),jQuery("#mapi-wrap").css("display","block"),!e.fetchedExternalAds){e.fetchedExternalAds=!0;0==jQuery("#mapi-table-wrap tbody tr[data-slotid]").length&&AdvancedAdsAdmin.AdImporter.refreshAds()}jQuery("#wpwrap").trigger("advads-mapi-adlist-opened")},onChangedAdType(){AdvancedAdsAdmin.AdImporter.adNetwork&&(AdvancedAdsAdmin.AdImporter.adNetwork.onBlur(),AdvancedAdsAdmin.AdImporter.adNetwork=null)},setup(e){AdvancedAdsAdmin.AdImporter.adNetwork=e,e.onSelected(),AdvancedAdsAdmin.AdImporter.isSetup?AdvancedAdsAdmin.AdImporter.highlightSelectedRowInExternalAdsList():(AdvancedAdsAdmin.AdImporter.isSetup=!0,jQuery(document).on("click",".prevent-default",(function(e){e.preventDefault()})),jQuery(document).on("click",".mapi-insert-code",(function(e){e.preventDefault(),jQuery("#remote-ad-unsupported-ad-type").css("display","none"),jQuery(".advads-adsense-code").show(),jQuery(".mapi-open-selector").css("display","inline"),jQuery(".mapi-close-selector-link").css("display","inline"),jQuery(".mapi-insert-code").css("display","none"),jQuery("#mapi-wrap").css("display","none");AdvancedAdsAdmin.AdImporter.adNetwork.getCustomInputs().css("display","none")})),jQuery(document).on("click",".mapi-open-selector a",(function(){AdvancedAdsAdmin.AdImporter.openExternalAdsList()})),jQuery(document).on("click","#mapi-close-selector,.mapi-close-selector-link",(function(){jQuery("#remote-ad-unsupported-ad-type").css("display","none"),AdvancedAdsAdmin.AdImporter.manualSetup()})),jQuery(document).on("click",".mapiaction",(function(e){switch(jQuery(this).attr("data-mapiaction")){case"updateList":AdvancedAdsAdmin.AdImporter.refreshAds();break;case"getCode":if(jQuery(this).hasClass("disabled"))break;var a=jQuery(this).attr("data-slotid");AdvancedAdsAdmin.AdImporter.adNetwork.selectAdFromList(a);break;case"updateCode":a=jQuery(this).attr("data-slotid");AdvancedAdsAdmin.AdImporter.adNetwork.updateAdFromList(a);break;case"toggleidle":if(void 0===AdvancedAdsAdmin.AdImporter.adNetwork.getMapiAction||"function"!=typeof AdvancedAdsAdmin.AdImporter.adNetwork.getMapiAction("toggleidle")){AdvancedAdsAdmin.AdImporter.adNetwork.hideIdle=!AdvancedAdsAdmin.AdImporter.adNetwork.hideIdle,AdvancedAdsAdmin.AdImporter.toggleIdleAds(AdvancedAdsAdmin.AdImporter.adNetwork.hideIdle);const e=jQuery("#mapi-notice-inactive");e.length&&e.toggle(AdvancedAdsAdmin.AdImporter.adNetwork.hideIdle);break}AdvancedAdsAdmin.AdImporter.adNetwork.getMapiAction("toggleidle")(e,this)}})),AdvancedAdsAdmin.AdImporter.adNetwork.onDomReady())},manualSetup(){jQuery(".mapi-open-selector,.advads-adsense-show-code").css("display","inline"),jQuery(".mapi-insert-code").css("display","inline"),jQuery(".mapi-close-selector-link").css("display","none"),jQuery("#mapi-wrap").css("display","none");AdvancedAdsAdmin.AdImporter.adNetwork.getCustomInputs().css("display","block"),"in-feed"!==jQuery("#unit-type").val()&&jQuery(".advads-adsense-layout-key").css("display","none").next("div").css("display","none"),AdvancedAdsAdmin.AdImporter.adNetwork.onManualSetup()},setRemoteErrorMessage(e){e?jQuery("#remote-ad-code-msg").html(e):jQuery("#remote-ad-code-msg").empty()},closeAdSelector(){setTimeout((function(){jQuery("#mapi-wrap").animate({height:0},360,(function(){jQuery(".mapi-open-selector,.advads-adsense-show-code").css("display","inline"),jQuery(".mapi-close-selector-link").css("display","none"),jQuery("#mapi-wrap").css({display:"none",height:"auto"});AdvancedAdsAdmin.AdImporter.adNetwork.getCustomInputs().css("display","block")}))}),80)},unitIsNotSupported(e){jQuery("#remote-ad-unsupported-ad-type").css("display","block"),AdsenseMAPI.unsupportedUnits[e]=1,jQuery("#unit-code").val(""),jQuery("#unit-type").val("normal"),jQuery("#ad-layout-key").val(""),jQuery('tr[data-slotid^="ca-"]').removeClass("selected error");const a=jQuery('tr[data-slotid="'+e+'"]');a.addClass("selected error").css("background-color",""),this.scrollToSelectedRow(a)},unitIsSupported(e){if(jQuery("#remote-ad-unsupported-ad-type").css("display","none"),void 0!==AdsenseMAPI.unsupportedUnits[e]&&delete AdsenseMAPI.unsupportedUnits[e],jQuery('i[data-mapiaction="getCode"][data-slotid="'+e+'"]').removeClass("disabled"),jQuery('tr[data-slotid="'+e+'"] .unittype a').length){var a=jQuery('tr[data-slotid="'+e+'"] .unittype'),t=jQuery('tr[data-slotid="'+e+'"] .unittype a').attr("data-type");a.text(t)}if(jQuery('tr[data-slotid="'+e+'"] .unitsize a').length){a=jQuery('tr[data-slotid="'+e+'"] .unitsize'),t=jQuery('tr[data-slotid="'+e+'"] .unitsize a').attr("data-size");a.text(t)}},emptyMapiSelector(e){const a='<div class="advads-notice-inline advads-error"><p>'+e+"</p></div>";jQuery("#mapi-loading-overlay").css("display","none"),jQuery("#mapi-wrap").html(jQuery(a))},refreshAds(){const e=AdvancedAdsAdmin.AdImporter.adNetwork;jQuery("#mapi-loading-overlay").css("display","block"),jQuery.ajax({type:"post",url:ajaxurl,data:e.getRefreshAdsParameters(),success(e,a,t){void 0!==e.html?(jQuery("#mapi-wrap").replaceWith(jQuery(e.html)),AdvancedAdsAdmin.AdImporter.openExternalAdsList()):void 0!==e.msg&&AdvancedAdsAdmin.AdImporter.emptyMapiSelector(e.msg),void 0!==e.raw&&console.log(e.raw),jQuery("#mapi-loading-overlay").css("display","none")},error(e,a,t){jQuery("#mapi-loading-overlay").css("display","none")}})},toggleIdleAds(e){void 0===e&&(e=!0),AdvancedAdsAdmin.AdImporter.highlightSelectedRowInExternalAdsList(e)}});class AdvancedAdsAdNetwork{constructor(e){this.id=e,this.units=[],this.vars=window[e+"AdvancedAdsJS"],this.hideIdle=!0,this.fetchedExternalAds=!1}onSelected(){console.error("Please override onSelected.")}onBlur(){console.error("Please override onBlur.")}openSelector(){console.error("Please override openSelector.")}getSelectedId(){console.error("Please override getSelectedId.")}selectAdFromList(e){console.error("Please override selectAdFromList.")}updateAdFromList(e){console.error("Please override updateAdFromList.")}getRefreshAdsParameters(){console.error("Please override getRefreshAdsParameters.")}getCustomInputs(){console.error("Please override getCustomInputs.")}onDomReady(){console.error("Please override onDomReady.")}onManualSetup(){}}class AdvancedAdsExternalAdUnit{}function Advads_Termination(e){function a(){this.addedNodes=[],this.removedNodes=[]}this.initialFormValues=new a,this.changedFormValues=new a;const t=["active_post_lock"];this.observers={list:[],push:e=>{this.observers.list.push(e)},disconnect:()=>{this.observers.list.forEach((e=>{e.disconnect()})),this.observers.list=[]}},this.setInitialValue=(e,a)=>{a&&a.value&&(this.initialFormValues[e]=a.value)};const d=function(a,t){if("checkbox"===t.type){const n=e.querySelectorAll('[name="'+t.name+'"]');return n.length>1?(a[(d=n)[0].name]=[],d.forEach((e=>{e.checked&&a[e.name].push(e.value)})),a):(a[t.name]=t.checked,a)}var d;return"radio"!==t.type||t.checked?(a[t.name]=t.value,a):a},n=new MutationObserver((e=>{for(const a of e){for(const e of a.removedNodes){const a=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT);for(;a.nextNode();)if("INPUT"===a.currentNode.tagName||"SELECT"===a.currentNode.tagName){const e=this.changedFormValues.addedNodes.indexOf(a.currentNode.name);e>-1?this.changedFormValues.addedNodes.splice(e,1):this.changedFormValues.removedNodes.push(a.currentNode.name)}}for(const e of a.addedNodes){if(e.nodeType===Node.TEXT_NODE)continue;const a=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT);for(;a.nextNode();)if("INPUT"===a.currentNode.tagName||"SELECT"===a.currentNode.tagName){if(""===a.currentNode.name)continue;this.changedFormValues.addedNodes.push(a.currentNode.name)}}}}));n.observe(e,{childList:!0,subtree:!0}),this.observers.push(n),this.hasChanged=(e,a)=>{for(const t in a)if(!e.hasOwnProperty(t)||e[t].toString()!==a[t].toString())return!0;return!1},this.terminationNotice=(a=!1)=>{if(!this.hasChanged(this.initialFormValues,this.changedFormValues))return!0;if(window.confirm(window.advadstxt.confirmation)){if(this.changedFormValues.addedNodes.length||this.changedFormValues.removedNodes.length)return a&&window.location.reload(),!0;for(const a in this.changedFormValues){const t=e.querySelector('[name="'+a+'"]');if(null!==t)if("checkbox"===t.type)t.checked=this.initialFormValues[a];else if("radio"===t.type){let d=null!==this.initialFormValues[a]&&void 0!==this.initialFormValues[a]?this.initialFormValues[a]:t.value;e.querySelector('[name="'+a+'"][value="'+d+'"]').checked=!0}else t.value=this.initialFormValues[a]}return!0}return!1},this.resetInitialValues=()=>{if(this.changedFormValues.addedNodes.length)for(const e in this.changedFormValues.addedNodes)this.initialFormValues[e]=this.changedFormValues.addedNodes[e];if(this.changedFormValues.removedNodes.length)for(const e in this.changedFormValues.removedNodes)void 0!==this.initialFormValues[e]&&delete this.initialFormValues[e];for(const e in this.changedFormValues)"removedNodes"!==e&&"addedNodes"!==e&&void 0!==this.initialFormValues[e]&&(this.initialFormValues[e]=this.changedFormValues[e]);this.changedFormValues=new a},this.collectValues=()=>{const a="DIALOG"===e.tagName;e.querySelectorAll("input, select, textarea").forEach((n=>{if(n.name.length&&!t.includes(n.id)&&!t.includes(n.name)&&(a||!n.closest("dialog"))){if(this.initialFormValues=d(this.initialFormValues,n),"hidden"===n.type){const a=new MutationObserver((function(e,a){e.forEach((e=>{"value"===e.attributeName&&e.target.dispatchEvent(new Event("input"))}))}));a.observe(e,{attributes:!0,subtree:!0}),this.observers.push(a)}n.addEventListener("input",(e=>{this.changedFormValues=d(this.changedFormValues,n)}))}}))}}jQuery(document).ready((function(){jQuery(document).on("click",".advads-tr-remove",(function(){jQuery(this).closest("tr").remove()}))})),void 0===jQuery.escapeSelector&&(jQuery.escapeSelector=function(e){return e.replace(/([$%&()*+,./:;<=>?@\[\\\]^{|}~])/g,"\\$1")});const modal=e=>{let a;const t=()=>window.location.hash.replace("#",""),d=()=>{e.showModal(),e.dispatchEvent(new CustomEvent("advads-modal-opened")),e.advadsTermination=new Advads_Termination(e),a&&e.advadsTermination.collectValues()},n=()=>{t()===e.id&&d()};if(n(),window.addEventListener("hashchange",(()=>{n(),"close"===t()&&(a&&!e.advadsTermination.terminationNotice(!0)||e.close())})),document.querySelectorAll('a[href$="#'+e.id+'"]').forEach((e=>{e.addEventListener("click",(e=>{e.preventDefault(),d()}))})),e.addEventListener("cancel",(t=>{t.preventDefault(),a?e.advadsTermination.terminationNotice(!0)&&(e.close(),e.advadsTermination.observers.disconnect(),document.dispatchEvent(new CustomEvent("advads-modal-canceled",{detail:{modal_id:e.id}}))):e.close()})),e.addEventListener("close",(a=>{t()===e.id&&(window.location.hash="")})),a=e.querySelector("form"),null===a)try{a=e.querySelector("button.advads-modal-close-action").form}catch(e){}a&&(e.querySelectorAll("input").forEach((t=>{t.addEventListener("keydown",(t=>{if("Enter"===t.key){if(a.reportValidity()){let d=!0;if("function"==typeof window[e.closeValidation.function]&&!window[e.closeValidation.function](e.closeValidation.modal_id))return void t.preventDefault();if(d=wp.hooks.applyFilters("advanced-ads-submit-modal-form",!0,a,e.advadsTermination.initialFormValues,e.advadsTermination.changedFormValues),!d)return void t.preventDefault();a.submit()}t.preventDefault()}}))})),a.addEventListener("submit",(()=>{window.location.hash=""}))),e.querySelectorAll(".advads-modal-close, .advads-modal-close-background").forEach((a=>{a.addEventListener("click",(a=>{a.preventDefault(),e.dispatchEvent(new Event("cancel"))}))}));try{e.querySelector("a.advads-modal-close-action").addEventListener("click",(a=>{a.preventDefault(),e.close()}))}catch(e){}};window.addEventListener("DOMContentLoaded",(()=>{try{if("function"!=typeof document.querySelector(".advads-modal[id^=modal-]").showModal)return}catch(e){return}[...document.getElementsByClassName("advads-modal")].forEach(modal)}));
