# Copyright (C) 2025 Advanced Ads
# This file is distributed under the GPL-2.0+.
msgid ""
msgstr ""
"Project-Id-Version: Advanced Ads 2.0.8\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/advanced-ads\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: webgilde <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2025-05-21T13:33:32+00:00\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"X-Generator: WP-CLI 2.11.0\n"
"X-Domain: advanced-ads\n"

#. Plugin Name of the plugin
#. Author of the plugin
#: advanced-ads.php
#: includes/admin/class-wordpress-dashboard.php:70
#: includes/compatibility/class-capability-manager.php:67
#: modules/gutenberg/includes/class-gutenberg.php:134
msgid "Advanced Ads"
msgstr ""

#. Plugin URI of the plugin
#. Author URI of the plugin
#: advanced-ads.php
msgid "https://wpadvancedads.com"
msgstr ""

#. Description of the plugin
#: advanced-ads.php
msgid "Manage and optimize your ads in WordPress"
msgstr ""

#. translators: %1$s is a version number
#: admin/includes/ad-health-notices.php:37
msgid "Your <strong>PHP version (%1$s) is too low</strong>. Advanced Ads is built for PHP %2$s and higher. It might work, but updating PHP is highly recommended. Please ask your hosting provider for more information."
msgstr ""

#. translators: %1$s is a list of plugin names; %2$s is a target URL
#: admin/includes/ad-health-notices.php:48
msgid "Plugins that are known to cause (partial) problems: <strong>%1$s</strong>. <a href=\"%2$s\" target=\"_blank\">Learn more</a>."
msgstr ""

#. translators: %s is a list of PHP extensions
#: admin/includes/ad-health-notices.php:59
msgid "Missing PHP extensions could cause issues. Please ask your hosting provider to enable them: %s"
msgstr ""

#. translators: %s is a target URL
#: admin/includes/ad-health-notices.php:69
msgid "Ads are disabled for all or some pages. See \"disabled ads\" in <a href=\"%s\">settings</a>."
msgstr ""

#: admin/includes/ad-health-notices.php:77
#: admin/views/support.php:84
msgid "Advanced Ads related constants enabled"
msgstr ""

#. translators: %s is a target URL
#: admin/includes/ad-health-notices.php:85
msgid "Some assets were changed. Please <strong>rebuild the asset folder</strong> in the <a href=\"%s\">Advanced Ads settings</a> to update the ad blocker disguise."
msgstr ""

#: admin/includes/ad-health-notices.php:94
#: admin/includes/notices.php:66
msgid "One or more license keys for <strong>Advanced Ads add-ons are invalid or missing</strong>."
msgstr ""

#. translators: %s is a target URL.
#: admin/includes/ad-health-notices.php:97
#: admin/includes/notices.php:68
msgid "Please add valid license keys <a href=\"%s\">here</a>."
msgstr ""

#: admin/includes/ad-health-notices.php:106
msgid "Ad expired"
msgstr ""

#. translators: %s is empty here, but the string will be followed by a name of an ad unit.
#: admin/includes/ad-health-notices.php:116
#: classes/frontend_checks.php:329
msgid "Visible ads should not use the Header placement: %s"
msgstr ""

#: admin/includes/ad-health-notices.php:127
#: classes/frontend_checks.php:312
#: includes/utilities/class-validation.php:74
msgid "Your website is using HTTPS, but the ad code contains HTTP and might not work."
msgstr ""

#: admin/includes/ad-health-notices.php:135
msgid "AdSense issue"
msgstr ""

#: admin/includes/ad-health-notices.php:140
#: admin/includes/ad-health-notices.php:147
#: modules/gadsense/includes/class-mapi.php:144
msgid "Last AdSense account connection attempt failed."
msgstr ""

#: admin/includes/ad-health-notices.php:140
msgid "Your account was not approved by AdSense."
msgstr ""

#. translators: %1$s is the opening a tag and %2$s the closing one.
#: admin/includes/ad-health-notices.php:149
msgid "Create a new AdSense account %1$shere%2$s."
msgstr ""

#: admin/includes/ad-health-notices.php:159
#: admin/includes/ad-health-notices.php:169
#: admin/includes/ad-health-notices.php:179
msgid "One of your sites is missing the AdSense publisher ID in the ads.txt file."
msgstr ""

#: admin/includes/ad-health-notices.php:161
#: admin/includes/ad-health-notices.php:171
#: admin/includes/ad-health-notices.php:181
msgctxt "related to ads.txt file"
msgid "Create one now."
msgstr ""

#: admin/includes/ad-health-notices.php:189
msgid "Google AdSense deprecated Link Units. Please choose another format."
msgstr ""

#: admin/includes/ad-health-notices.php:191
#: includes/admin/class-addon-box.php:466
#: includes/admin/class-addon-box.php:560
#: includes/admin/class-addon-box.php:609
#: includes/admin/class-addon-box.php:654
#: modules/gadsense/admin/views/adsense-ad-parameters.php:147
msgid "Learn more"
msgstr ""

#. translators: %s is a filter hook, here `the_content`.
#: admin/includes/ad-health-notices.php:198
msgid "<strong>%s</strong> filter found multiple times."
msgstr ""

#: admin/includes/ad-health-notices.php:200
msgid "Advanced Ads uses the outermost of them."
msgstr ""

#. translators: %1$s is a plugin name, %2$s is the opening a tag and %3$s the closing one.
#: admin/includes/ad-health-notices.php:209
#: admin/includes/ad-health-notices.php:220
#: admin/includes/ad-health-notices.php:231
#: admin/includes/ad-health-notices.php:242
#: admin/includes/ad-health-notices.php:255
msgid "Learn how to integrate %1$s with Advanced Ads %2$shere%3$s."
msgstr ""

#. translators: %s is a service or plugin name
#: admin/includes/ad-health-notices.php:269
msgid "%s detected."
msgstr ""

#: admin/includes/ad-health-notices.php:270
msgid "Learn how this might impact your ad setup."
msgstr ""

#. translators: %s removable ads.txt plugins
#: admin/includes/ad-health-notices.php:282
msgid "Advanced Ads handles your ads.txt file automatically. You might be able to <strong>remove %1$s</strong>."
msgstr ""

#: admin/includes/ad-health-notices.php:293
msgid "Advanced Ads handles header and footer codes."
msgstr ""

#. translators: %s removable header and footer plugins
#: admin/includes/ad-health-notices.php:296
msgid "You might be able to <strong>remove %1$s</strong>."
msgstr ""

#: admin/includes/ad-health-notices.php:299
msgid "Learn how."
msgstr ""

#. translators: 1 opening anchor tag, 2 closing tag, 3 icon
#: admin/includes/ad-health-notices.php:308
msgid "Learn how to target ads on GamiPress websites %1$shere%2$s %3$s"
msgstr ""

#. translators: 1 opening anchor tag, 2 closing tag, 3 icon
#: admin/includes/ad-health-notices.php:319
msgid "Learn how to manage ads on membership sites running Paid Memberships Pro %1$shere%2$s %3$s"
msgstr ""

#. translators: 1 opening anchor tag, 2 closing tag, 3 icon
#: admin/includes/ad-health-notices.php:330
msgid "Learn how to target ads to specific user roles created with the Members plugin %1$shere%2$s %3$s"
msgstr ""

#. translators: 1 opening anchor tag, 2 closing tag, 3 icon
#: admin/includes/ad-health-notices.php:341
msgid "Learn how to target ads to multiple languages in TranslatePress %1$shere%2$s %3$s"
msgstr ""

#. translators: 1 opening anchor tag, 2 closing tag, 3 icon
#: admin/includes/ad-health-notices.php:352
msgid "Learn how to target ads to multiple languages in Weglot %1$shere%2$s %3$s"
msgstr ""

#. translators: 1 opening anchor tag, 2 closing tag, 3 icon
#: admin/includes/ad-health-notices.php:363
msgid "Learn how to integrate ads into LearnDash %1$shere%2$s %3$s"
msgstr ""

#. translators: 1 opening anchor tag, 2 closing tag, 3 icon
#: admin/includes/ad-health-notices.php:374
msgid "Learn how to auto-inject Amazon Ads with AAWP and Advanced Ads %1$shere%2$s %3$s"
msgstr ""

#. translators: 1 opening anchor tag, 2 closing tag, 3 icon
#: admin/includes/ad-health-notices.php:385
msgid "Learn how to target ads to multiple languages in Polylang %1$shere%2$s %3$s"
msgstr ""

#. translators: 1 opening anchor tag, 2 closing tag, 3 icon
#: admin/includes/ad-health-notices.php:396
msgid "Learn how to integrate Advanced Ads in MailPoet Newsletters %1$shere%2$s %3$s"
msgstr ""

#. translators: 1 opening anchor tag, 2 closing tag, 3 icon
#: admin/includes/ad-health-notices.php:407
msgid "Learn how to use WP Rocket with Advanced Ads %1$shere%2$s %3$s"
msgstr ""

#. translators: 1 opening anchor tag, 2 closing tag, 3 icon
#: admin/includes/ad-health-notices.php:418
msgid "Learn how to embed Ads into a Quiz %1$shere%2$s %3$s"
msgstr ""

#. translators: 1 opening anchor tag, 2 closing tag, 3 icon
#: admin/includes/ad-health-notices.php:429
msgid "Learn how to create and implement ads in Elementor %1$shere%2$s %3$s"
msgstr ""

#. translators: 1 opening anchor tag, 2 closing tag, 3 icon
#: admin/includes/ad-health-notices.php:440
msgid "Learn how to embed ads into a website built with the SiteOrigin page builder %1$shere%2$s %3$s"
msgstr ""

#. translators: 1 opening anchor tag, 2 closing tag, 3 icon
#: admin/includes/ad-health-notices.php:451
msgid "Learn how to integrate ads into the Divi theme and Divi builder %1$shere%2$s %3$s"
msgstr ""

#. translators: 1 opening anchor tag, 2 closing tag, 3 icon
#: admin/includes/ad-health-notices.php:462
msgid "Learn how to embed ads into your Beaver Builder website %1$shere%2$s %3$s"
msgstr ""

#. translators: 1 opening anchor tag, 2 closing tag, 3 icon
#: admin/includes/ad-health-notices.php:473
msgid "Learn how to embed ads into a website built with Pagelayer %1$shere%2$s %3$s"
msgstr ""

#. translators: 1 opening anchor tag, 2 closing tag, 3 icon
#: admin/includes/ad-health-notices.php:484
msgid "Learn more about displaying Ads with WPBakery Page Builder %1$shere%2$s %3$s"
msgstr ""

#. translators: 1 opening anchor tag, 2 closing tag, 3 icon
#: admin/includes/ad-health-notices.php:495
msgid "Learn how to integrate ads into the tagDiv Newspaper theme %1$shere%2$s %3$s"
msgstr ""

#: admin/includes/class-ad-network.php:310
msgid "You don't have the permission to manage ads."
msgstr ""

#: admin/includes/class-ad-network.php:315
msgid "You sent an invalid request."
msgstr ""

#: admin/includes/class-licenses.php:65
msgid "Error while trying to register the license. Please contact support."
msgstr ""

#: admin/includes/class-licenses.php:70
#: admin/views/setting-license.php:84
msgid "Please enter a valid license key"
msgstr ""

#: admin/includes/class-licenses.php:111
#: admin/includes/class-licenses.php:312
msgid "The license status does not change as long as ADVANCED_ADS_SHOW_LICENSE_RESPONSE is enabled in wp-config.php."
msgstr ""

#: admin/includes/class-licenses.php:130
msgid "License couldn’t be activated. Please try again later."
msgstr ""

#: admin/includes/class-licenses.php:149
msgid "This is the bundle license key."
msgstr ""

#: admin/includes/class-licenses.php:150
msgid "This is not the correct key for this add-on."
msgstr ""

#: admin/includes/class-licenses.php:151
msgid "There are no activations left."
msgstr ""

#. translators: %1$s is a starting link tag, %2$s is the closing one.
#: admin/includes/class-licenses.php:155
msgid "You can manage activations in %1$syour account%2$s."
msgstr ""

#. translators: %1$s is a starting link tag, %2$s is the closing one.
#: admin/includes/class-licenses.php:161
msgid "%1$sUpgrade%2$s for more activations."
msgstr ""

#. translators: %s is a string containing information about the issue.
#: admin/includes/class-licenses.php:175
msgid "License is invalid. Reason: %s"
msgstr ""

#. translators: %s is a list of server information like IP address. Just keep it as is.
#: admin/includes/class-licenses.php:215
msgid "Your request was blocked by our firewall. Please send us the following information to unblock you: %s."
msgstr ""

#: admin/includes/class-licenses.php:278
msgid "Error while trying to disable the license. Please contact support."
msgstr ""

#: admin/includes/class-licenses.php:321
#: admin/includes/class-licenses.php:345
msgid "License couldn’t be deactivated. Please try again later."
msgstr ""

#. translators: %s plugin update link
#: admin/includes/class-licenses.php:485
msgid "Download failed. <a href=\"%s\">Click here to try another method</a>."
msgstr ""

#. translators: %s download failed knowledgebase link
#: admin/includes/class-licenses.php:488
msgid "Download failed. <a href=\"%s\" target=\"_blank\">Click here to learn why</a>."
msgstr ""

#: admin/includes/class-licenses.php:649
msgid "License deactivated. Please try again later."
msgstr ""

#. translators: %s: is a URL.
#: admin/includes/class-notices.php:464
msgid "You don’t seem to have an email address. Please use <a href=\"%s\" target=\"_blank\">this form</a> to sign up."
msgstr ""

#: admin/includes/class-notices.php:485
msgid "How embarrassing. The email server seems to be down. Please try again later."
msgstr ""

#. translators: the first %s is an email address, the seconds %s is a URL.
#: admin/includes/class-notices.php:493
msgid "Please check your email (%1$s) for the confirmation message. If you didn’t receive one or want to use another email address then please use <a href=\"%2$s\" target=\"_blank\">this form</a> to sign up."
msgstr ""

#: admin/includes/class-overview-widgets.php:47
msgid "Notifications"
msgstr ""

#: admin/includes/class-overview-widgets.php:52
msgid "Next Steps"
msgstr ""

#: admin/includes/class-overview-widgets.php:59
msgid "Manual & Support"
msgstr ""

#: admin/includes/class-overview-widgets.php:68
#: includes/admin/metaboxes/class-ad-adsense.php:101
#: views/admin/page-bulk-edit.php:22
#: views/admin/page-bulk-edit.php:35
msgid "Disable"
msgstr ""

#: admin/includes/class-overview-widgets.php:72
#: includes/admin/class-wordpress-dashboard.php:92
msgid "AdSense Earnings"
msgstr ""

#: admin/includes/class-overview-widgets.php:79
#: includes/admin/class-action-links.php:59
msgid "Add-Ons"
msgstr ""

#: admin/includes/notices.php:46
msgid "Thank you for activating <strong>Advanced Ads</strong>. Would you like to receive the first steps via email?"
msgstr ""

#: admin/includes/notices.php:47
msgid "Yes, send it"
msgstr ""

#: admin/includes/notices.php:53
msgid "Hey, welcome to Advanced Ads! Join our newsletter and snag <strong>2 free add-ons</strong> plus our email intro course. It’s the perfect way to get started smoothly!"
msgstr ""

#: admin/includes/notices.php:54
#: admin/views/notices/inline.php:19
#: admin/views/notices/subscribe.php:17
#: admin/views/support.php:26
#: views/admin/widgets/aa-dashboard/next-steps/newsletter.php:75
msgid "Subscribe me now"
msgstr ""

#: admin/includes/notices.php:60
msgid "Learn more about how and <strong>how much you can earn with AdSense</strong> and Advanced Ads from my dedicated newsletter."
msgstr ""

#: admin/includes/notices.php:76
msgid "… ads created using <strong>Advanced Ads</strong>."
msgstr ""

#: admin/includes/notices.php:77
msgid "Do you find the plugin useful and would like to thank us for updates, fixing bugs and improving your ad setup?"
msgstr ""

#. translators: this belongs to our message asking the user for a review. You can find a nice equivalent in your own language.
#: admin/includes/notices.php:80
msgid "When you give 5-stars, an actual person does a little happy dance!"
msgstr ""

#: admin/includes/notices.php:82
msgid "Sure, I appreciate your work"
msgstr ""

#: admin/includes/notices.php:83
msgid "Yes, but help me first to solve a problem, please"
msgstr ""

#. translators: %1$s is the markup for the discount value, %2$s starts a button link, %3$s closes the button link.
#: admin/includes/notices.php:92
msgid "Save %1$s on all products with our Black Friday / Cyber Monday offer! %2$sGet this deal%3$s"
msgstr ""

#. translators: %s: URL to the Advanced Ads onboarding wizard.
#: admin/includes/notices.php:104
msgid "Quickly set up Advanced Ads and monetize your website with just a few clicks. <a class=\"button button-primary\" href=\"%s\">Launch the wizard</a>"
msgstr ""

#. translators: %1$s: URL to the plugin file, %2$s: URL to the guide
#: admin/includes/notices.php:119
msgid "Your automatically deactivated version of <strong>%1$s needs to be updated manually</strong>. Please <a href=\"%2$s\" target=\"_blank\">download the newest plugin file</a> and follow our guide on <a href=\"%3$s\" target=\"_blank\">How to install an add-on</a>."
msgstr ""

#. translators: %1$s: URL to the plugin file, %2$s: URL to the guide
#: admin/includes/notices.php:140
msgid "Your version of <strong>%1$s</strong> is incompatible with <strong>Advanced Ads %2$s</strong> and has been deactivated. Please update the plugin to the latest version."
msgstr ""

#: admin/views/ad-conditions-string-operators.php:17
#: modules/privacy/admin/views/setting-general.php:56
msgid "contains"
msgstr ""

#: admin/views/ad-conditions-string-operators.php:18
msgid "starts with"
msgstr ""

#: admin/views/ad-conditions-string-operators.php:19
msgid "ends with"
msgstr ""

#: admin/views/ad-conditions-string-operators.php:20
msgid "matches"
msgstr ""

#: admin/views/ad-conditions-string-operators.php:21
msgid "matches regex"
msgstr ""

#: admin/views/ad-conditions-string-operators.php:22
msgid "does not contain"
msgstr ""

#: admin/views/ad-conditions-string-operators.php:23
msgid "does not start with"
msgstr ""

#: admin/views/ad-conditions-string-operators.php:24
msgid "does not end with"
msgstr ""

#: admin/views/ad-conditions-string-operators.php:25
msgid "does not match"
msgstr ""

#: admin/views/ad-conditions-string-operators.php:26
msgid "does not match regex"
msgstr ""

#. translators: %1$s is a starting link tag, %2$s is closing the link tag.
#: admin/views/checks.php:16
msgid "Advanced Ads detected potential problems with your ad setup. %1$sShow me these errors%2$s"
msgstr ""

#: admin/views/conditions/condition-author.php:38
msgctxt "display the authors search field on ad edit page"
msgid "add more authors"
msgstr ""

#: admin/views/conditions/condition-author.php:44
msgid "author name or id"
msgstr ""

#: admin/views/conditions/condition-device.php:34
#: admin/views/conditions/condition-is-or-not.php:28
#: admin/views/conditions/condition-number.php:23
#: admin/views/conditions/condition-string.php:9
#: admin/views/notices/adblock.php:11
#: classes/display-conditions.php:919
#: includes/admin/metaboxes/class-ad-layout.php:63
#: includes/admin/metaboxes/class-ad-types.php:63
#: includes/ads/types/type-plain.php:230
#: includes/utilities/class-wordpress.php:429
#: modules/ads-txt/admin/views/setting-create.php:26
#: modules/gadsense/admin/views/adsense-ad-parameters.php:142
#: modules/privacy/admin/views/setting-general.php:43
#: views/admin/header.php:58
#: views/admin/metaboxes/ads/ad-layout.php:80
#: views/admin/metaboxes/ads/ad-types.php:47
#: views/admin/placements/create-modal/new-modal-content.php:15
#: views/admin/screens/groups.php:26
#: views/admin/settings/general/custom-label.php:22
#: views/admin/settings/general/disable-notices.php:19
#: views/admin/settings/general/link-target.php:17
#: views/admin/tables/placements/description.php:16
#: views/admin/widgets/aa-dashboard/support.php:16
msgid "Manual"
msgstr ""

#: admin/views/conditions/condition-number.php:13
msgid "equal"
msgstr ""

#: admin/views/conditions/condition-number.php:15
msgid "equal or higher"
msgstr ""

#: admin/views/conditions/condition-number.php:17
msgid "equal or lower"
msgstr ""

#: admin/views/conditions/condition-operator.php:12
msgid "is"
msgstr ""

#: admin/views/conditions/condition-operator.php:14
msgid "is not"
msgstr ""

#: admin/views/conditions/conditions-form.php:10
msgid "New condition"
msgstr ""

#: admin/views/conditions/conditions-form.php:15
msgid "-- choose a condition --"
msgstr ""

#: admin/views/conditions/conditions-form.php:24
msgid "Add-On features"
msgstr ""

#: admin/views/conditions/display-conditions-list.php:65
msgid "Forced to OR."
msgstr ""

#: admin/views/conditions/display-conditions-list.php:66
#: admin/views/conditions/display-conditions-list.php:69
msgid "manual"
msgstr ""

#: admin/views/conditions/display-conditions-list.php:68
msgid "The ad might always show due to OR and \"is not\". Better use AND."
msgstr ""

#. translators: %s is a name of a taxonomy.
#: admin/views/conditions/no-option.php:17
msgctxt "Error message shown when no terms exists for display condition; placeholder is taxonomy label."
msgid "No %s found on your site."
msgstr ""

#: admin/views/conditions/not-selected.php:2
msgctxt "Error message shown when no display condition term is selected"
msgid "Please select some items."
msgstr ""

#: admin/views/modal.php:38
#: admin/views/modal.php:53
#: modules/one-click/admin/class-admin.php:54
msgid "Cancel"
msgstr ""

#: admin/views/notices/adblock.php:10
msgid "Please disable your <strong>AdBlocker</strong>. Otherwise, the features of Advanced Ads and the layout are broken."
msgstr ""

#: admin/views/notices/info.php:28
#: admin/views/notices/promo.php:45
msgid "Dismiss this notice."
msgstr ""

#: admin/views/notices/starter-setup-success.php:15
msgid "2 Test Ads successfully added!"
msgstr ""

#: admin/views/notices/starter-setup-success.php:18
msgid "Look below for the list of created ads."
msgstr ""

#: admin/views/notices/starter-setup-success.php:21
msgid "Visit list of placements"
msgstr ""

#: admin/views/notices/starter-setup-success.php:26
msgid "See them in action"
msgstr ""

#: admin/views/placement-form.php:14
#: views/admin/placements/create-modal/new-modal-content.php:22
msgid "Choose a placement type"
msgstr ""

#. translators: %s is a URL.
#: admin/views/placement-form.php:20
#: views/admin/placements/create-modal/new-modal-content.php:28
msgid "Placement types define where the ad is going to be displayed. Learn more about the different types from the <a href=\"%s\">manual</a>"
msgstr ""

#: admin/views/placement-form.php:43
#: views/admin/placements/create-modal/new-modal-content.php:50
#: views/admin/screens/group-form.php:62
msgid "Please select a type."
msgstr ""

#: admin/views/placement-form.php:45
#: views/admin/placements/create-modal/new-modal-content.php:55
msgid "Choose a Name"
msgstr ""

#: admin/views/placement-form.php:47
#: views/admin/placements/create-modal/new-modal-content.php:59
msgid "Placement Name"
msgstr ""

#: admin/views/placement-form.php:50
#: views/admin/placements/create-modal/new-modal-content.php:62
msgid "The name of the placement is only visible to you. Tip: choose a descriptive one, e.g. Below Post Headline."
msgstr ""

#: admin/views/placement-form.php:54
#: views/admin/placements/create-modal/new-modal-content.php:67
#: views/admin/screens/group-form.php:66
msgid "Please enter a name."
msgstr ""

#: admin/views/placement-form.php:56
#: views/admin/placements/create-modal/new-modal-content.php:71
#: views/admin/tables/placements/column-ad-group.php:24
msgid "Choose the Ad or Group"
msgstr ""

#: admin/views/placement-form.php:60
#: admin/views/placement-form.php:68
#: admin/views/placements-item.php:15
#: views/admin/placements/create-modal/item-select.php:12
#: views/admin/placements/create-modal/new-modal-content.php:83
#: views/admin/placements/edit-modal/fields/item.php:19
#: views/admin/tables/placements/item-select.php:19
msgid "--not selected--"
msgstr ""

#: admin/views/placement-injection-top.php:18
msgid "Congratulations! Your ad is now visible in the frontend."
msgstr ""

#: admin/views/placement-injection-top.php:20
msgid "Take a look at your ad"
msgstr ""

#. translators: %s is a URL.
#: admin/views/placement-injection-top.php:27
msgid "Ad not showing up? Take a look <a href=\"%s\" target=\"_blank\">here</a>"
msgstr ""

#. translators: %1$s is the opening link tag, %2$s is closing link tag.
#: admin/views/placement-injection-top.php:43
msgid "Adjust the placement options? Take a look  %1$shere.%2$s"
msgstr ""

#: admin/views/placement-injection-top.php:51
msgid "Where do you want to display the ad?"
msgstr ""

#. translators: %s is a URL.
#: admin/views/placement-injection-top.php:62
#: modules/gadsense/admin/admin.php:238
msgid "The AdSense verification and Auto ads code is already activated in the <a href=\"%s\">AdSense settings</a>."
msgstr ""

#: admin/views/placement-injection-top.php:74
#: modules/gadsense/admin/admin.php:241
msgid "No need to add the code manually here, unless you want to include it into certain pages only."
msgstr ""

#: admin/views/placement-injection-top.php:77
msgid "Click on the button below to add the Auto ads code to the header of your site."
msgstr ""

#: admin/views/placement-injection-top.php:89
msgid "inject Auto ads"
msgstr ""

#: admin/views/placement-injection-top.php:95
msgid "New placement"
msgstr ""

#: admin/views/placement-injection-top.php:96
#: includes/placements/types/class-before-content.php:47
msgid "Before Content"
msgstr ""

#: admin/views/placement-injection-top.php:97
#: includes/placements/types/class-content.php:47
msgid "Content"
msgstr ""

#: admin/views/placement-injection-top.php:98
#: includes/placements/types/class-after-content.php:47
msgid "After Content"
msgstr ""

#: admin/views/placement-injection-top.php:99
msgid "Manage Sidebar"
msgstr ""

#: admin/views/placement-injection-top.php:100
msgid "PHP or Shortcode"
msgstr ""

#: admin/views/placement-injection-top.php:101
msgid "Header (Manual)"
msgstr ""

#: admin/views/placement-injection-top.php:105
#: admin/views/placement-injection-top.php:109
#: admin/views/upgrades/pro-placements.php:32
msgid "Custom Position"
msgstr ""

#: admin/views/placement-injection-top.php:105
msgid "Show Pro Places"
msgstr ""

#: admin/views/placement-injection-top.php:114
msgid "AdSense In-feed"
msgstr ""

#: admin/views/placement-injection-top.php:120
msgid "Show Sticky Places"
msgstr ""

#: admin/views/placement-injection-top.php:126
msgid "Show PopUp"
msgstr ""

#: admin/views/placement-injection-top.php:130
msgid "PopUp & Layer"
msgstr ""

#: admin/views/placement-injection-top.php:138
msgid "see all…"
msgstr ""

#: admin/views/placement-injection-top.php:170
msgid "Existing placement"
msgstr ""

#. translators: %s is some HTML.
#: admin/views/placement-injection-top.php:182
msgid "Or use the shortcode %s to insert the ad into the content manually."
msgstr ""

#. translators: %s is a URL.
#: admin/views/placement-injection-top.php:189
msgid "Learn more about your choices to display an ad in the <a href=\"%s\" target=\"_blank\">manual</a>."
msgstr ""

#: admin/views/placements-ad-label.php:13
#: admin/views/placements-ad-label.php:15
#: deprecated/placements-ad-label-position.php:12
#: deprecated/placements-ad-label-position.php:14
#: modules/gadsense/includes/class-network-adsense.php:342
#: views/admin/placements/edit-modal/fields/ad-label-position.php:14
#: views/admin/placements/edit-modal/fields/ad-label-position.php:16
#: views/admin/placements/edit-modal/fields/ad-label.php:13
#: views/admin/placements/edit-modal/fields/ad-label.php:15
msgid "default"
msgstr ""

#: admin/views/placements-ad-label.php:17
#: admin/views/placements-ad-label.php:19
#: modules/ads-txt/admin/views/setting-create.php:16
#: modules/ads-txt/admin/views/setting-create.php:18
#: views/admin/placements/edit-modal/fields/ad-label.php:17
#: views/admin/placements/edit-modal/fields/ad-label.php:19
msgid "enabled"
msgstr ""

#: admin/views/placements-ad-label.php:21
#: admin/views/placements-ad-label.php:23
#: modules/ads-txt/admin/views/setting-create.php:20
#: modules/ads-txt/admin/views/setting-create.php:22
#: views/admin/placements/edit-modal/fields/ad-label.php:21
#: views/admin/placements/edit-modal/fields/ad-label.php:23
msgid "disabled"
msgstr ""

#: admin/views/placements-content-index.php:42
#: views/admin/placements/edit-modal/fields/content-index.php:39
msgid "use xpath, e.g. `p[not(parent::blockquote)]`"
msgstr ""

#: admin/views/placements-content-index.php:43
#: views/admin/placements/edit-modal/fields/content-index.php:42
msgctxt "frontend picker"
msgid "stop selection"
msgstr ""

#: admin/views/placements-content-index.php:44
#: views/admin/placements/edit-modal/fields/content-index.php:46
msgid "select position"
msgstr ""

#: admin/views/placements-content-index.php:50
#: views/admin/placements/edit-modal/fields/content-index.php:53
msgid "start counting from bottom"
msgstr ""

#. translators: %s is the name of a language in English.
#: admin/views/placements-item.php:57
msgid "The ad is not translated into %s"
msgstr ""

#: admin/views/placements-item.php:68
#: includes/admin/class-assets.php:115
#: views/admin/tables/groups/list-row-loop-none.php:17
#: views/admin/tables/groups/list-row-option-ads.php:96
#: views/admin/tables/placements/column-ad-group.php:20
#: views/admin/widgets/aa-dashboard/next-steps/widget.php:17
msgid "Create your first ad"
msgstr ""

#: admin/views/post-list-filter-dropdown.php:10
msgid "All ad states"
msgstr ""

#: admin/views/post-list-filter-dropdown.php:13
#: includes/admin/class-post-list.php:165
msgid "All ads disabled"
msgstr ""

#: admin/views/post-list-filter-dropdown.php:20
#: includes/admin/class-post-list.php:169
msgid "Ads in content disabled"
msgstr ""

#: admin/views/setting-license.php:15
msgid "Your license expired."
msgstr ""

#: admin/views/setting-license.php:23
msgid "Update expiry date"
msgstr ""

#. translators: 1: is a URL, 2: is HTML of a button.
#: admin/views/setting-license.php:31
msgid "Click on %2$s if you renewed it or have a subscription or <a href=\"%1$s\" class=\"advads-renewal-link\" target=\"_blank\">renew your license</a>."
msgstr ""

#. translators: %d is a number of days.
#: admin/views/setting-license.php:49
msgid "(%d days left)"
msgstr ""

#: admin/views/setting-license.php:55
msgid "License key"
msgstr ""

#: admin/views/setting-license.php:72
msgid "Deactivate License"
msgstr ""

#: admin/views/setting-license.php:81
msgid "Update License"
msgstr ""

#: admin/views/setting-license.php:81
msgid "Activate License"
msgstr ""

#: admin/views/setting-license.php:86
msgid "License key invalid"
msgstr ""

#: admin/views/setting-license.php:90
msgid "active"
msgstr ""

#: admin/views/support-callout.php:14
msgid "Problems or questions?"
msgstr ""

#: admin/views/support-callout.php:15
#: views/admin/widgets/aa-dashboard/overview-notices.php:37
msgid "Save time and get personal support."
msgstr ""

#: admin/views/support-callout.php:15
#: views/admin/widgets/aa-dashboard/overview-notices.php:38
msgid "Ask your question!"
msgstr ""

#. translators: %s: number of free add-ons.
#: admin/views/support.php:19
msgid "Subscribe to our free First Steps email course, receive our newsletter for periodic tutorials, and get %s for Advanced Ads."
msgstr ""

#: admin/views/support.php:23
#: modules/gadsense/admin/views/adsense-account.php:227
#: modules/gadsense/includes/class-network-adsense.php:79
#: views/admin/metaboxes/ads/ad-types.php:65
#: views/admin/widgets/aa-dashboard/next-steps/newsletter.php:30
#: views/admin/widgets/wordpress-dashboard/newsletter.php:24
msgid "2 free add-ons"
msgstr ""

#: admin/views/support.php:31
msgid "Please fix the red highlighted issues on this page or try to understand their consequences before contacting support."
msgstr ""

#: admin/views/support.php:32
msgid "Possible Issues"
msgstr ""

#: admin/views/support.php:34
msgid "Ads not showing up"
msgstr ""

#: admin/views/support.php:35
msgid "Purchase & Licenses"
msgstr ""

#: admin/views/support.php:36
msgid "Issues after updating"
msgstr ""

#: admin/views/support.php:37
msgid "General Issues"
msgstr ""

#: admin/views/support.php:38
msgid "Issues with Add-Ons"
msgstr ""

#: admin/views/support.php:40
msgid "Use the following form to search for solutions in the manual on wpadvancedads.com"
msgstr ""

#: admin/views/support.php:43
msgid "search"
msgstr ""

#. translators: %s is a URL.
#: admin/views/support.php:51
msgid "Take a look at more common issues or contact us directly through the <a href=\"%s\" target=\"_blank\">support page</a>."
msgstr ""

#. translators: %s is a URL.
#: admin/views/support.php:69
msgid "Upgrade to any premium add-on and get <strong>priority email support</strong> or reach out through the <a href=\"%s\" target=\"_blank\">support forum</a> for individual help."
msgstr ""

#: admin/views/upgrades/adsense-amp.php:13
msgid "Automatically convert AdSense ads into their AMP format"
msgstr ""

#: admin/views/upgrades/pro-placements.php:14
msgid "Random Paragraph"
msgstr ""

#: admin/views/upgrades/pro-placements.php:15
msgid "After a random paragraph in the main content."
msgstr ""

#: admin/views/upgrades/pro-placements.php:20
msgid "Above Headline"
msgstr ""

#: admin/views/upgrades/pro-placements.php:21
msgid "Above the main headline on the page (&lt;h1&gt;)."
msgstr ""

#: admin/views/upgrades/pro-placements.php:26
msgid "Content Middle"
msgstr ""

#: admin/views/upgrades/pro-placements.php:27
msgid "In the middle of the main content based on the number of paragraphs."
msgstr ""

#: admin/views/upgrades/pro-placements.php:33
msgid "Attach the ad to any element in the frontend."
msgstr ""

#: admin/views/upgrades/pro-placements.php:38
msgid "Post Lists"
msgstr ""

#: admin/views/upgrades/pro-placements.php:39
msgid "Display the ad between posts on post lists, e.g. home, archives, search etc."
msgstr ""

#: admin/views/upgrades/pro-placements.php:43
msgid "Background Ad"
msgstr ""

#: admin/views/upgrades/pro-placements.php:44
msgid "Background of the website behind the main wrapper."
msgstr ""

#: admin/views/upgrades/pro-placements.php:51
msgid "BuddyBoss Content"
msgstr ""

#: admin/views/upgrades/pro-placements.php:52
msgid "Display ads on BuddyBoss related pages."
msgstr ""

#: admin/views/upgrades/pro-placements.php:57
msgid "BuddyPress Content"
msgstr ""

#: admin/views/upgrades/pro-placements.php:58
msgid "Display ads on BuddyPress related pages."
msgstr ""

#: admin/views/upgrades/pro-placements.php:65
msgid "bbPress Content"
msgstr ""

#: admin/views/upgrades/pro-placements.php:66
msgid "Display ads in content created with bbPress."
msgstr ""

#: admin/views/upgrades/pro-placements.php:95
msgid "Get all placements with All Access"
msgstr ""

#: admin/views/upgrades/repeat-the-position.php:11
msgid "repeat the position"
msgstr ""

#. translators: %1$s is an anchor (link) opening tag, %2$s is the closing tag.
#: classes/ad-health-notices.php:814
#: modules/gadsense/includes/class-mapi.php:1680
msgid "Learn more about AdSense account issues %1$shere%2$s."
msgstr ""

#: classes/display-conditions.php:95
msgid "post type"
msgstr ""

#: classes/display-conditions.php:96
#: includes/array_ad_conditions.php:36
msgid "Choose the public post types on which to display the ad."
msgstr ""

#: classes/display-conditions.php:102
msgid "specific pages"
msgstr ""

#: classes/display-conditions.php:103
#: includes/array_ad_conditions.php:54
msgid "Choose on which individual posts, pages and public post type pages you want to display or hide ads."
msgstr ""

#: classes/display-conditions.php:109
msgid "general conditions"
msgstr ""

#: classes/display-conditions.php:115
msgid "author"
msgstr ""

#: classes/display-conditions.php:121
msgid "content age"
msgstr ""

#: classes/display-conditions.php:122
msgid "Display ads based on age of the page."
msgstr ""

#: classes/display-conditions.php:128
msgid "taxonomy"
msgstr ""

#: classes/display-conditions.php:129
msgid "Display ads based on the taxonomy of an archive page."
msgstr ""

#. translators: %s is a label of an archive page.
#: classes/display-conditions.php:177
msgid "archive: %s"
msgstr ""

#: classes/display-conditions.php:285
msgid "parent page"
msgstr ""

#: classes/display-conditions.php:286
msgid "post meta"
msgstr ""

#: classes/display-conditions.php:287
msgid "page template"
msgstr ""

#: classes/display-conditions.php:288
msgid "url parameters"
msgstr ""

#: classes/display-conditions.php:291
msgid "accelerated mobile pages"
msgstr ""

#: classes/display-conditions.php:315
#: classes/visitor-conditions.php:318
#: includes/admin/class-assets.php:130
#: modules/gadsense/admin/views/external-ads-links.php:27
#: modules/gadsense/admin/views/external-ads-links.php:32
#: modules/gadsense/admin/views/external-ads-links.php:45
msgid "or"
msgstr ""

#: classes/display-conditions.php:315
#: classes/visitor-conditions.php:318
#: includes/admin/class-assets.php:131
msgid "and"
msgstr ""

#: classes/display-conditions.php:644
msgctxt "display the terms search field on ad edit page"
msgid "add more terms"
msgstr ""

#: classes/display-conditions.php:650
msgid "term name or id"
msgstr ""

#: classes/display-conditions.php:732
msgid "ID"
msgstr ""

#: classes/display-conditions.php:732
#: includes/admin/class-placement-edit-modal.php:120
#: views/admin/placements/quick-edit.php:14
#: views/admin/tables/groups/list-row-option-ads.php:21
msgid "Status"
msgstr ""

#: classes/display-conditions.php:734
#: includes/abstracts/abstract-ad.php:830
#: includes/admin/class-groups-list-table.php:362
#: views/admin/placements/quick-edit.php:16
#: views/admin/tables/ads/column-date.php:16
msgid "Published"
msgstr ""

#: classes/display-conditions.php:736
#: includes/admin/class-groups-list-table.php:362
#: views/admin/tables/ads/column-date.php:18
msgid "Last Modified"
msgstr ""

#: classes/display-conditions.php:758
msgid "title or id"
msgstr ""

#: classes/display-conditions.php:832
#: includes/array_ad_conditions.php:59
msgid "Home Page"
msgstr ""

#: classes/display-conditions.php:833
#: includes/array_ad_conditions.php:60
msgid "show on Home page"
msgstr ""

#: classes/display-conditions.php:837
#: includes/array_ad_conditions.php:64
msgid "Singular Pages"
msgstr ""

#: classes/display-conditions.php:838
#: includes/array_ad_conditions.php:65
msgid "show on singular pages/posts"
msgstr ""

#: classes/display-conditions.php:842
#: includes/array_ad_conditions.php:69
msgid "Archive Pages"
msgstr ""

#: classes/display-conditions.php:843
#: includes/array_ad_conditions.php:70
msgid "show on any type of archive page (category, tag, author and date)"
msgstr ""

#: classes/display-conditions.php:847
#: includes/array_ad_conditions.php:74
msgid "Search Results"
msgstr ""

#: classes/display-conditions.php:848
#: includes/array_ad_conditions.php:75
msgid "show on search result pages"
msgstr ""

#: classes/display-conditions.php:852
#: includes/array_ad_conditions.php:79
msgid "404 Page"
msgstr ""

#: classes/display-conditions.php:853
#: includes/array_ad_conditions.php:80
msgid "show on 404 error page"
msgstr ""

#: classes/display-conditions.php:857
#: includes/array_ad_conditions.php:84
msgid "Attachment Pages"
msgstr ""

#: classes/display-conditions.php:858
#: includes/array_ad_conditions.php:85
msgid "show on attachment pages"
msgstr ""

#: classes/display-conditions.php:862
#: includes/array_ad_conditions.php:89
msgid "Secondary Queries"
msgstr ""

#: classes/display-conditions.php:863
#: includes/array_ad_conditions.php:90
msgid "allow ads in secondary queries"
msgstr ""

#: classes/display-conditions.php:867
msgid "RSS Feed"
msgstr ""

#: classes/display-conditions.php:868
msgid "allow ads in RSS Feed"
msgstr ""

#: classes/display-conditions.php:872
msgid "REST API"
msgstr ""

#: classes/display-conditions.php:873
msgid "allow ads in REST API"
msgstr ""

#: classes/display-conditions.php:908
msgid "older than"
msgstr ""

#: classes/display-conditions.php:909
msgid "younger than"
msgstr ""

#: classes/display-conditions.php:914
msgid "days"
msgstr ""

#: classes/display-conditions.php:917
msgid "Display ads based on the age of a page or post."
msgstr ""

#: classes/filesystem.php:62
msgid "Could not access filesystem."
msgstr ""

#: classes/filesystem.php:66
msgid "Filesystem error."
msgstr ""

#: classes/filesystem.php:73
msgid "Unable to locate WordPress root directory."
msgstr ""

#: classes/filesystem.php:77
msgid "Unable to locate WordPress content directory (wp-content)."
msgstr ""

#. translators: %s directory
#: classes/filesystem.php:82
msgid "Unable to locate needed folder (%s)."
msgstr ""

#: classes/frontend_checks.php:127
msgid "Random AdSense ads"
msgstr ""

#: classes/frontend_checks.php:145
#: views/admin/settings/general/block-bots.php:18
msgid "You look like a bot"
msgstr ""

#: classes/frontend_checks.php:159
msgid "Ad blocker enabled"
msgstr ""

#: classes/frontend_checks.php:174
msgid "<em>the_content</em> filter does not exist"
msgstr ""

#: classes/frontend_checks.php:194
msgid "Ads are disabled in the content of this page"
msgstr ""

#: classes/frontend_checks.php:207
msgid "the current post ID is 0 "
msgstr ""

#: classes/frontend_checks.php:227
msgid "Ads are disabled on this page"
msgstr ""

#: classes/frontend_checks.php:239
msgid "Ads are disabled on all pages"
msgstr ""

#: classes/frontend_checks.php:251
msgid "Ads are disabled on 404 pages"
msgstr ""

#: classes/frontend_checks.php:263
msgid "Ads are disabled on non singular pages"
msgstr ""

#: classes/frontend_checks.php:277
msgid "Ads are disabled for the user role(s)"
msgstr ""

#: classes/frontend_checks.php:298
msgid "Ads are disabled for this IP address"
msgstr ""

#. translators: em tags
#: classes/frontend_checks.php:314
msgid "Ad IDs: %s"
msgstr ""

#: classes/frontend_checks.php:347
msgid "AdSense violation"
msgstr ""

#: classes/frontend_checks.php:348
msgid "Ad is hidden"
msgstr ""

#. translators: em tags
#: classes/frontend_checks.php:350
msgid "IDs: %s"
msgstr ""

#. translators: em tags
#: classes/frontend_checks.php:367
msgid "The following responsive AdSense ads are not showing up: %s"
msgstr ""

#: classes/frontend_checks.php:384
msgid "Consent not given"
msgstr ""

#: classes/frontend_checks.php:401
msgid "Enable TCF integration"
msgstr ""

#: classes/frontend_checks.php:416
msgid "Debug Google Ad Manager"
msgstr ""

#: classes/frontend_checks.php:433
msgid "highlight ads"
msgstr ""

#: classes/frontend_checks.php:555
msgid "Ad Health"
msgstr ""

#. translators: number of notices
#: classes/frontend_checks.php:571
msgid "Show %d more notifications"
msgstr ""

#: classes/frontend_checks.php:590
msgid "Everything is fine"
msgstr ""

#: classes/frontend_checks.php:603
#: includes/admin/class-misc.php:56
msgid "Get help"
msgstr ""

#: classes/frontend_checks.php:665
msgid "the following code is used for automatic error detection and only visible to admins"
msgstr ""

#. translators: %s stands for the name of the "Disable level limitation" option and automatically translated as well
#: classes/in-content-injector.php:615
msgid "Set <em>%s</em> to show more ads"
msgstr ""

#: classes/in-content-injector.php:616
#: includes/admin/class-settings.php:617
msgid "Disable level limitation"
msgstr ""

#. translators: timezone name
#: classes/utils.php:290
#: includes/utilities/class-wordpress.php:188
msgid "time of %s"
msgstr ""

#: classes/visitor-conditions.php:42
msgid "Device"
msgstr ""

#: classes/visitor-conditions.php:49
msgctxt "Device condition"
msgid "Mobile"
msgstr ""

#: classes/visitor-conditions.php:53
msgctxt "Device condition"
msgid "Tablet"
msgstr ""

#: classes/visitor-conditions.php:57
msgctxt "Device condition"
msgid "Desktop"
msgstr ""

#: classes/visitor-conditions.php:62
msgid "logged-in visitor"
msgstr ""

#: classes/visitor-conditions.php:63
msgid "Whether the visitor has to be logged in or not in order to see the ad."
msgstr ""

#: classes/visitor-conditions.php:279
msgid "browser language"
msgstr ""

#: classes/visitor-conditions.php:280
msgid "cookie"
msgstr ""

#: classes/visitor-conditions.php:281
msgid "max. ad clicks"
msgstr ""

#: classes/visitor-conditions.php:282
msgid "max. ad impressions"
msgstr ""

#: classes/visitor-conditions.php:283
msgid "new visitor"
msgstr ""

#: classes/visitor-conditions.php:284
msgid "page impressions"
msgstr ""

#: classes/visitor-conditions.php:285
msgid "geo location"
msgstr ""

#: classes/visitor-conditions.php:286
msgid "referrer url"
msgstr ""

#: classes/visitor-conditions.php:287
msgid "user agent"
msgstr ""

#: classes/visitor-conditions.php:288
msgid "user can (capabilities)"
msgstr ""

#: classes/visitor-conditions.php:289
msgid "user role"
msgstr ""

#: classes/visitor-conditions.php:290
msgid "browser width"
msgstr ""

#: deprecated/placements-ad-label-position.php:16
#: deprecated/placements-ad-label-position.php:18
#: views/admin/placements/edit-modal/fields/ad-label-position.php:19
#: views/admin/placements/edit-modal/fields/ad-label-position.php:21
msgid "left"
msgstr ""

#: deprecated/placements-ad-label-position.php:19
#: deprecated/placements-ad-label-position.php:21
#: views/admin/placements/edit-modal/fields/ad-label-position.php:24
#: views/admin/placements/edit-modal/fields/ad-label-position.php:26
msgid "center"
msgstr ""

#: deprecated/placements-ad-label-position.php:22
#: deprecated/placements-ad-label-position.php:24
#: views/admin/placements/edit-modal/fields/ad-label-position.php:29
#: views/admin/placements/edit-modal/fields/ad-label-position.php:31
msgid "right"
msgstr ""

#: deprecated/placements-ad-label-position.php:28
#: views/admin/placements/edit-modal/fields/ad-label-position.php:37
msgid "Check this if you don’t want the following elements to float around the ad. (adds a placement_clearfix)"
msgstr ""

#. translators: %s is a date.
#: includes/abstracts/abstract-ad.php:804
msgid "starts %s"
msgstr ""

#. translators: %s is a date.
#: includes/abstracts/abstract-ad.php:818
msgid "expires %s"
msgstr ""

#. translators: %s is a date.
#: includes/abstracts/abstract-ad.php:824
msgid "expired %s"
msgstr ""

#: includes/abstracts/abstract-ad.php:833
#: includes/admin/class-placement-quick-edit.php:48
#: views/admin/placements/edit-modal/fields/status.php:13
#: views/admin/placements/quick-edit.php:17
#: views/admin/tables/placements/column-name.php:17
msgid "Draft"
msgstr ""

#: includes/abstracts/abstract-ad.php:836
msgid "Trashed"
msgstr ""

#. translators: %1$s is a placement name, %2$s is the ads name.
#: includes/abstracts/abstract-ad.php:1213
msgid "Placement name: %1$s; Ads: %2$s"
msgstr ""

#: includes/abstracts/abstract-admin-list-table.php:100
msgid "Showing search results for"
msgstr ""

#: includes/abstracts/abstract-group.php:470
#: includes/admin/class-addon-box.php:732
msgid "Get this add-on"
msgstr ""

#: includes/abstracts/abstract-group.php:473
#: includes/admin/class-addon-box.php:449
#: includes/admin/class-addon-box.php:671
msgid "Activate now"
msgstr ""

#. translators: %1$s is an URL, %2$s is a URL text
#: includes/abstracts/abstract-group.php:479
msgid "It seems that a caching plugin is activated. Your ads might not rotate properly. The cache busting in Advanced Ads Pro will solve that. <a href=\"%1$s\" target=\"_blank\">%2$s.</a>"
msgstr ""

#: includes/abstracts/abstract-placement-type.php:63
#: includes/class-widget.php:128
#: includes/utilities/class-data.php:296
#: modules/gutenberg/includes/class-gutenberg.php:136
#: views/admin/tables/ads/filters.php:75
msgid "Ad Groups"
msgstr ""

#: includes/abstracts/abstract-placement-type.php:67
#: includes/admin/class-groups-list-table.php:193
#: includes/admin/pages/class-ads.php:46
#: includes/admin/pages/class-ads.php:47
#: includes/class-entities.php:44
#: includes/class-widget.php:135
#: includes/utilities/class-data.php:289
#: modules/gutenberg/includes/class-gutenberg.php:135
#: views/admin/tables/groups/edit-form-modal.php:113
msgid "Ads"
msgstr ""

#: includes/abstracts/abstract-placement-type.php:86
msgid "draft"
msgstr ""

#. translators: 1: Entity type 2: Item type 3: Placement title
#: includes/abstracts/abstract-placement.php:291
msgid "%1$s type \"%2$s\" not allowed for placement type \"%3$s\""
msgstr ""

#: includes/admin/class-action-links.php:54
#: includes/admin/class-admin-menu.php:92
#: includes/admin/class-admin-menu.php:95
#: views/admin/screens/settings.php:32
msgid "Support"
msgstr ""

#: includes/admin/class-action-links.php:60
msgid "See Pro Features"
msgstr ""

#. translators: %1$s is the URL to add a new review
#: includes/admin/class-action-links.php:102
msgid "Thank the developer with a &#9733;&#9733;&#9733;&#9733;&#9733; review on <a href=\"%1$s\" target=\"_blank\">wordpress.org</a>"
msgstr ""

#: includes/admin/class-ad-list-table.php:77
#: includes/admin/class-groups-list-table.php:190
#: includes/admin/class-placement-list-table.php:84
#: modules/gadsense/admin/views/adsense-ad-parameters.php:127
#: views/admin/tables/groups/edit-form-modal.php:78
msgid "Type"
msgstr ""

#: includes/admin/class-ad-list-table.php:82
#: includes/admin/class-placement-edit-modal.php:108
#: includes/admin/class-placement-list-table.php:86
#: modules/gadsense/admin/views/external-ads-list.php:38
#: modules/privacy/admin/views/setting-general.php:53
#: views/admin/tables/groups/edit-form-modal.php:33
msgid "Name"
msgstr ""

#: includes/admin/class-ad-list-table.php:83
#: modules/gadsense/admin/views/external-ads-list.php:43
#: modules/gutenberg/includes/class-gutenberg.php:140
msgid "Size"
msgstr ""

#: includes/admin/class-ad-list-table.php:84
msgid "Ad Planning"
msgstr ""

#: includes/admin/class-ad-list-table.php:85
msgid "Ad Shortcode"
msgstr ""

#: includes/admin/class-ad-list-table.php:86
msgid "AdSense ID"
msgstr ""

#: includes/admin/class-ad-list-table.php:87
#: includes/admin/class-groups-list-table.php:194
msgid "Date"
msgstr ""

#: includes/admin/class-ad-list-table.php:88
msgid "Notes"
msgstr ""

#: includes/admin/class-ad-list-table.php:89
#: modules/ads-txt/admin/views/setting-additional-content.php:44
msgid "Preview"
msgstr ""

#: includes/admin/class-ad-list-table.php:90
msgid "Used"
msgstr ""

#: includes/admin/class-ad-list-table.php:91
#: includes/admin/class-screen-options.php:178
#: views/admin/tables/ads/filters.php:85
msgid "Debug Mode"
msgstr ""

#: includes/admin/class-ad-list-table.php:95
#: includes/admin/class-screen-options.php:183
#: views/admin/tables/ads/filters.php:113
msgid "Privacy Ignore"
msgstr ""

#: includes/admin/class-ad-list-table.php:176
msgctxt "Post list header for ads expiring in the future."
msgid "Expiring"
msgstr ""

#: includes/admin/class-ad-list-table.php:194
msgctxt "Post list header for expired ads."
msgid "Expired"
msgstr ""

#: includes/admin/class-addon-box.php:60
msgid "Take the monetization of your website to the next level."
msgstr ""

#: includes/admin/class-addon-box.php:68
msgid "Integrate your ads on AMP (Accelerated Mobile Pages) and auto-convert your Google AdSense ad units for enhanced mobile performance."
msgstr ""

#: includes/admin/class-addon-box.php:76
msgid "Simplify the process of implementing ad units from Google Ad Manager swiftly and without errors."
msgstr ""

#: includes/admin/class-addon-box.php:84
msgid "Capture attention with customizable pop-ups that ensure your ads and messages get noticed. Set timing and closing options for optimal user engagement."
msgstr ""

#: includes/admin/class-addon-box.php:92
msgid "Earn more money by enabling advertisers to buy ad space directly on your site’s frontend."
msgstr ""

#: includes/admin/class-addon-box.php:100
msgid "Increase click rates by anchoring ads in sticky positions above, alongside, or below your website."
msgstr ""

#: includes/admin/class-addon-box.php:108
msgid "Monitor your ad performance to maximize your revenue."
msgstr ""

#: includes/admin/class-addon-box.php:116
msgid "Create a beautiful ad slider and increase the ad impressions per page view. Free add-on for subscribers to our newsletter."
msgstr ""

#: includes/admin/class-addon-box.php:124
msgid "Place AdSense In-feed ads between posts on homepage, category, and archive pages for optimal engagement."
msgstr ""

#: includes/admin/class-addon-box.php:189
msgid "Cache Busting"
msgstr ""

#: includes/admin/class-addon-box.php:190
#: includes/admin/pages/class-onboarding.php:247
#: views/admin/upgrades/pro-tab.php:18
msgid "Click Fraud Protection"
msgstr ""

#: includes/admin/class-addon-box.php:191
#: includes/admin/pages/class-onboarding.php:252
#: views/admin/upgrades/pro-tab.php:19
msgid "Lazy Loading"
msgstr ""

#: includes/admin/class-addon-box.php:192
msgid "Anti Ad Blocker"
msgstr ""

#: includes/admin/class-addon-box.php:193
#: views/admin/upgrades/pro-tab.php:21
msgid "Geo Targeting"
msgstr ""

#: includes/admin/class-addon-box.php:194
msgid "+23 Conditions"
msgstr ""

#: includes/admin/class-addon-box.php:195
msgid "+11 Placements"
msgstr ""

#: includes/admin/class-addon-box.php:196
msgid "Parallax Ads"
msgstr ""

#: includes/admin/class-addon-box.php:197
msgid "Ad Grids"
msgstr ""

#: includes/admin/class-addon-box.php:198
msgid "Ad Refresh"
msgstr ""

#: includes/admin/class-addon-box.php:199
msgid "A/B Tests"
msgstr ""

#: includes/admin/class-addon-box.php:205
msgid "Impressions & Clicks"
msgstr ""

#: includes/admin/class-addon-box.php:206
msgid "Click-Through Rate"
msgstr ""

#: includes/admin/class-addon-box.php:207
#: includes/admin/class-marketing.php:51
msgid "Statistics"
msgstr ""

#: includes/admin/class-addon-box.php:208
msgid "Google Analytics"
msgstr ""

#: includes/admin/class-addon-box.php:209
msgid "Local Data Processing"
msgstr ""

#: includes/admin/class-addon-box.php:210
msgid "Email Reports"
msgstr ""

#: includes/admin/class-addon-box.php:211
msgid "Link Cloaking"
msgstr ""

#: includes/admin/class-addon-box.php:388
msgid "No add-ons installed"
msgstr ""

#: includes/admin/class-addon-box.php:391
msgid "Please select from the list below."
msgstr ""

#: includes/admin/class-addon-box.php:393
msgid "Learn how to download, install, and activate an add-on"
msgstr ""

#: includes/admin/class-addon-box.php:440
#: includes/admin/pages/class-onboarding.php:130
msgid "Subscribe now"
msgstr ""

#: includes/admin/class-addon-box.php:443
#: includes/admin/class-addon-box.php:673
msgid "Download"
msgstr ""

#: includes/admin/class-addon-box.php:446
msgid "Install now"
msgstr ""

#: includes/admin/class-addon-box.php:446
#: includes/admin/class-addon-box.php:556
#: includes/admin/class-addon-box.php:605
#: includes/admin/class-upgrades.php:52
msgid "Upgrade"
msgstr ""

#: includes/admin/class-addon-box.php:452
#: includes/admin/class-addon-box.php:669
#: includes/admin/class-assets.php:142
msgid "Active"
msgstr ""

#: includes/admin/class-addon-box.php:460
#: views/admin/widgets/wordpress-dashboard/footer.php:14
msgid "See the manual"
msgstr ""

#: includes/admin/class-addon-box.php:482
msgid "many more features"
msgstr ""

#: includes/admin/class-addon-box.php:545
msgid "Advanced Ads All Access long-term"
msgstr ""

#: includes/admin/class-addon-box.php:549
msgid "Secure 4 years of ongoing support and updates with just one payment. Enjoy savings of up to 70% compared to individual add-on purchases."
msgstr ""

#: includes/admin/class-addon-box.php:594
msgid "Advanced Ads All Access"
msgstr ""

#: includes/admin/class-addon-box.php:598
msgid "Every tool you need for website success in one package. Enjoy our complete suite of add-ons for limitless possibilities."
msgstr ""

#: includes/admin/class-addon-box.php:667
msgid "Included"
msgstr ""

#: includes/admin/class-addon-box.php:694
msgid "Installed Add-ons"
msgstr ""

#: includes/admin/class-addon-box.php:698
msgid "Available Add-ons"
msgstr ""

#: includes/admin/class-addon-box.php:702
msgid "Free Add-ons & Special Purpose"
msgstr ""

#: includes/admin/class-addon-box.php:742
msgid "How to download, install, and activate an add-on"
msgstr ""

#. Translators: 1: add-on name 2: admin URL to license page
#: includes/admin/class-addon-updater.php:132
msgid "There might be a new version of %1$s. Please <strong>provide a valid license key</strong> in order to receive updates and support <a href=\"%2$s\">on this page</a>."
msgstr ""

#: includes/admin/class-admin-menu.php:107
#: includes/admin/class-admin-menu.php:111
#: includes/admin/class-settings.php:91
msgid "Licenses"
msgstr ""

#. translators: %s: version number
#: includes/admin/class-admin-notices.php:69
msgid "You have successfully rolled back to Advanced Ads %s"
msgstr ""

#: includes/admin/class-admin-notices.php:70
msgid "You have successfully rolled back to a previous version of Advanced Ads."
msgstr ""

#. translators: %s is a URL.
#: includes/admin/class-ajax.php:124
#: includes/admin/class-ajax.php:277
#: includes/admin/class-ajax.php:601
msgid "An error occurred. Please use <a href=\"%s\" target=\"_blank\">this form</a> to sign up."
msgstr ""

#: includes/admin/class-assets.php:132
msgid "After which paragraph?"
msgstr ""

#: includes/admin/class-assets.php:134
#: modules/gadsense/includes/class-adsense-report.php:159
msgid "Today"
msgstr ""

#: includes/admin/class-assets.php:135
#: modules/gadsense/includes/class-adsense-report.php:160
msgid "Yesterday"
msgstr ""

#: includes/admin/class-assets.php:136
#: modules/gadsense/includes/class-adsense-report.php:163
msgid "This Month"
msgstr ""

#. translators: 1: The number of days.
#: includes/admin/class-assets.php:138
#: modules/gadsense/includes/class-adsense-report.php:162
#: modules/gadsense/includes/class-adsense-report.php:165
msgid "Last %1$d days"
msgstr ""

#. translators: 1: An error message.
#: includes/admin/class-assets.php:140
msgid "An error occurred: %1$s"
msgstr ""

#: includes/admin/class-assets.php:141
#: includes/importers/class-google-sheet.php:112
#: modules/gadsense/admin/views/adsense-report.php:24
msgid "All"
msgstr ""

#: includes/admin/class-assets.php:143
msgid "There were no results returned for this ad. Please make sure it is active, generating impressions and double check your ad parameters."
msgstr ""

#: includes/admin/class-assets.php:144
msgid "Show inactive ads"
msgstr ""

#: includes/admin/class-assets.php:145
msgid "Hide inactive ads"
msgstr ""

#: includes/admin/class-assets.php:147
msgid "Permanently delete this placement?"
msgstr ""

#: includes/admin/class-assets.php:148
#: includes/admin/class-groups-list-table.php:453
#: includes/admin/class-placement-list-table.php:364
#: modules/one-click/admin/class-admin.php:55
msgid "Close"
msgstr ""

#: includes/admin/class-assets.php:149
msgid "Close and save"
msgstr ""

#: includes/admin/class-assets.php:150
msgid "Save new placement"
msgstr ""

#: includes/admin/class-assets.php:151
msgid "Data you have entered has not been saved. Are you sure you want to discard your changes?"
msgstr ""

#: includes/admin/class-assets.php:158
msgid "Save"
msgstr ""

#: includes/admin/class-assets.php:159
msgid "Save New Group"
msgstr ""

#: includes/admin/class-assets.php:160
msgid "Group updated"
msgstr ""

#: includes/admin/class-assets.php:161
msgid "Group deleted"
msgstr ""

#. translators: an ad group title.
#: includes/admin/class-assets.php:163
msgid "You are about to permanently delete %s"
msgstr ""

#: includes/admin/class-assets.php:166
msgid "New placement created"
msgstr ""

#: includes/admin/class-assets.php:167
#: views/admin/tables/placements/column-type.php:25
msgid "Placement updated"
msgstr ""

#: includes/admin/class-authors.php:123
msgid "Sorry, you're not allowed to assign this user."
msgstr ""

#. translators: the plugin name.
#: includes/admin/class-edd-updater.php:342
msgid "There is a new version of %1$s available."
msgstr ""

#: includes/admin/class-edd-updater.php:348
msgid "Contact your network administrator to install the update."
msgstr ""

#. translators: 1: opening anchor tag, do not translate 2. the new plugin version 3. closing anchor tag, do not translate.
#: includes/admin/class-edd-updater.php:353
msgid "%1$sView version %2$s details%3$s."
msgstr ""

#: includes/admin/class-edd-updater.php:361
msgid "%1$sView version %2$s details%3$s or %4$supdate now%5$s."
msgstr ""

#: includes/admin/class-edd-updater.php:372
msgid "Update now."
msgstr ""

#: includes/admin/class-edd-updater.php:603
msgid "You do not have permission to install plugin updates"
msgstr ""

#: includes/admin/class-edd-updater.php:603
msgid "Error"
msgstr ""

#: includes/admin/class-groups-list-table.php:180
#: includes/class-entities.php:201
msgid "No Ad Group found"
msgstr ""

#: includes/admin/class-groups-list-table.php:191
msgctxt "term name"
msgid "Name"
msgstr ""

#: includes/admin/class-groups-list-table.php:192
msgid "Details"
msgstr ""

#. translators: %s is the group type string
#: includes/admin/class-groups-list-table.php:288
msgid "The originally selected group type “%s” is not enabled."
msgstr ""

#: includes/admin/class-groups-list-table.php:388
#: includes/admin/class-groups-list-table.php:430
#: includes/class-entities.php:48
#: includes/class-entities.php:128
#: includes/importers/class-xml-importer.php:340
#: includes/importers/class-xml-importer.php:848
msgid "Edit"
msgstr ""

#: includes/admin/class-groups-list-table.php:396
#: includes/admin/class-placement-list-table.php:204
msgid "Show Usage"
msgstr ""

#: includes/admin/class-groups-list-table.php:406
msgid "Delete"
msgstr ""

#: includes/admin/class-groups-list-table.php:451
#: includes/admin/class-placement-list-table.php:362
#: includes/admin/metaboxes/class-ad-usage.php:43
msgid "Usage"
msgstr ""

#: includes/admin/class-header.php:58
#: includes/admin/class-header.php:64
#: includes/ads/class-ad-repository.php:55
#: includes/class-entities.php:46
#: includes/class-entities.php:50
#: views/admin/tables/groups/list-row-option-ads.php:71
msgid "New Ad"
msgstr ""

#: includes/admin/class-header.php:63
msgid "Your Ads"
msgstr ""

#: includes/admin/class-header.php:72
msgid "Your Groups"
msgstr ""

#: includes/admin/class-header.php:73
#: includes/class-entities.php:198
#: views/admin/screens/groups.php:37
msgid "New Ad Group"
msgstr ""

#: includes/admin/class-header.php:83
#: includes/admin/class-header.php:91
msgid "Your Placements"
msgstr ""

#: includes/admin/class-header.php:84
#: includes/admin/class-header.php:92
#: includes/admin/class-placement-create-modal.php:49
#: includes/class-entities.php:126
#: includes/class-entities.php:130
#: includes/placements/class-placement-repository.php:43
msgid "New Placement"
msgstr ""

#: includes/admin/class-header.php:101
#: includes/admin/pages/class-settings.php:48
msgid "Advanced Ads Settings"
msgstr ""

#: includes/admin/class-list-filters.php:122
#: modules/gadsense/admin/views/adsense-ad-parameters.php:131
#: modules/gadsense/includes/class-ad-type-adsense.php:31
msgid "Responsive"
msgstr ""

#: includes/admin/class-marketing.php:40
msgid "Increase your ad revenue"
msgstr ""

#: includes/admin/class-metabox-ad-settings.php:63
msgid "Ad Settings"
msgstr ""

#. translators: %s is the time the ad was first saved.
#: includes/admin/class-metabox-ad.php:237
msgid "Ad created on %s"
msgstr ""

#: includes/admin/class-metabox-ad.php:292
#: includes/admin/class-metabox-ad.php:299
msgid "Use advanced-ads-ad-pre-save action"
msgstr ""

#. translators: %1$s opening button tag, %2$s closing button tag.
#: includes/admin/class-metabox-ad.php:386
msgid "This looks like an AdSense ad. Switch the ad type to “AdSense ad” to make use of more features. %1$sSwitch to AdSense ad%2$s."
msgstr ""

#: includes/admin/class-misc.php:55
msgid "You don’t have access to ads. Please deactivate and re-enable Advanced Ads again to fix this."
msgstr ""

#: includes/admin/class-placement-create-modal.php:50
msgid "Save New Placement"
msgstr ""

#. translators: 1: "Options", 2: the name of a placement.
#: includes/admin/class-placement-edit-modal.php:70
msgid "Options"
msgstr ""

#: includes/admin/class-placement-edit-modal.php:114
msgid "Item"
msgstr ""

#: includes/admin/class-placement-edit-modal.php:129
msgid "position"
msgstr ""

#: includes/admin/class-placement-edit-modal.php:154
msgid "ad label"
msgstr ""

#: includes/admin/class-placement-edit-modal.php:163
msgid "Position"
msgstr ""

#: includes/admin/class-placement-edit-modal.php:171
msgid "Inline CSS"
msgstr ""

#: includes/admin/class-placement-edit-modal.php:180
msgid "Minimum Content Length"
msgstr ""

#: includes/admin/class-placement-edit-modal.php:182
msgid "Minimum length of content before automatically injected ads are allowed in them."
msgstr ""

#: includes/admin/class-placement-edit-modal.php:188
msgid "Words Between Ads"
msgstr ""

#: includes/admin/class-placement-edit-modal.php:190
msgid "A minimum amount of words between automatically injected ads."
msgstr ""

#: includes/admin/class-placement-edit-modal.php:200
#: includes/admin/metaboxes/class-ad-targeting.php:65
#: includes/frontend/class-debug-ads.php:217
#: views/admin/metaboxes/ads/ad-targeting.php:19
#: views/admin/tables/placements/column-conditions.php:16
msgid "Display Conditions"
msgstr ""

#: includes/admin/class-placement-edit-modal.php:202
msgid "Use display conditions for placements."
msgstr ""

#: includes/admin/class-placement-edit-modal.php:202
#: includes/admin/class-placement-edit-modal.php:210
msgid "The free version provides conditions on the ad edit page."
msgstr ""

#: includes/admin/class-placement-edit-modal.php:208
#: includes/admin/metaboxes/class-ad-targeting.php:66
#: includes/frontend/class-debug-ads.php:322
#: views/admin/metaboxes/ads/ad-targeting.php:38
#: views/admin/tables/placements/column-conditions.php:28
msgid "Visitor Conditions"
msgstr ""

#: includes/admin/class-placement-edit-modal.php:210
msgid "Use visitor conditions for placements."
msgstr ""

#: includes/admin/class-placement-edit-modal.php:363
msgid "after"
msgstr ""

#: includes/admin/class-placement-edit-modal.php:364
msgid "before"
msgstr ""

#: includes/admin/class-placement-edit-modal.php:381
msgid "Important Notice"
msgstr ""

#. translators: %s is a name of a module.
#: includes/admin/class-placement-edit-modal.php:386
msgid "Missing PHP extension could cause issues. Please ask your hosting provider to enable it: %s"
msgstr ""

#: includes/admin/class-placement-list-table.php:85
msgid "Title"
msgstr ""

#: includes/admin/class-placement-list-table.php:87
#: includes/class-entities.php:45
#: includes/frontend/class-debug-ads.php:183
#: includes/frontend/class-debug-ads.php:236
#: includes/frontend/class-debug-ads.php:238
#: modules/ad-positioning/views/ad-spacing.php:45
#: views/admin/tables/groups/list-row-option-ads.php:20
msgid "Ad"
msgstr ""

#: includes/admin/class-placement-list-table.php:87
msgid "Group"
msgstr ""

#: includes/admin/class-placement-list-table.php:88
msgid "Delivery"
msgstr ""

#: includes/admin/class-plugin-installer.php:103
msgid "Rollback to Previous Version"
msgstr ""

#: includes/admin/class-post-list.php:142
msgid "Ad injection"
msgstr ""

#: includes/admin/class-post-types.php:94
#: includes/admin/class-post-types.php:95
msgid "Ad updated."
msgstr ""

#. translators: %s: date and time of the revision
#: includes/admin/class-post-types.php:98
msgid "Ad restored to revision from %s"
msgstr ""

#: includes/admin/class-post-types.php:100
#: includes/admin/class-post-types.php:101
msgid "Ad saved."
msgstr ""

#: includes/admin/class-post-types.php:102
msgid "Ad submitted."
msgstr ""

#. translators: %s: date
#: includes/admin/class-post-types.php:105
msgid "Ad scheduled for: <strong>%1$s</strong>."
msgstr ""

#: includes/admin/class-post-types.php:106
msgid "M j, Y @ G:i"
msgstr ""

#: includes/admin/class-post-types.php:108
msgid "Ad draft updated."
msgstr ""

#. translators: %s: ad count
#: includes/admin/class-post-types.php:125
msgid "%s ad updated."
msgid_plural "%s ads updated."
msgstr[0] ""
msgstr[1] ""

#. translators: %s: ad count
#: includes/admin/class-post-types.php:127
msgid "%s ad not updated, somebody is editing it."
msgid_plural "%s ads not updated, somebody is editing them."
msgstr[0] ""
msgstr[1] ""

#. translators: %s: ad count
#: includes/admin/class-post-types.php:129
msgid "%s ad permanently deleted."
msgid_plural "%s ads permanently deleted."
msgstr[0] ""
msgstr[1] ""

#. translators: %s: ad count
#: includes/admin/class-post-types.php:131
msgid "%s ad moved to the Trash."
msgid_plural "%s ads moved to the Trash."
msgstr[0] ""
msgstr[1] ""

#. translators: %s: ad count
#: includes/admin/class-post-types.php:133
msgid "%s ad restored from the Trash."
msgid_plural "%s ads restored from the Trash."
msgstr[0] ""
msgstr[1] ""

#. translators: %s: placement count
#: includes/admin/class-post-types.php:138
msgid "%s placement updated."
msgid_plural "%s placements updated."
msgstr[0] ""
msgstr[1] ""

#. translators: %s: placement count
#: includes/admin/class-post-types.php:140
msgid "%s placement not updated, somebody is editing it."
msgid_plural "%s placements not updated, somebody is editing them."
msgstr[0] ""
msgstr[1] ""

#. translators: %s: placement count
#: includes/admin/class-post-types.php:142
msgid "%s placement permanently deleted."
msgid_plural "%s placements permanently deleted."
msgstr[0] ""
msgstr[1] ""

#. translators: %s: placement count
#: includes/admin/class-post-types.php:144
msgid "%s placement moved to the Trash."
msgid_plural "%s placements moved to the Trash."
msgstr[0] ""
msgstr[1] ""

#. translators: %s: placement count
#: includes/admin/class-post-types.php:146
msgid "%s placement restored from the Trash."
msgid_plural "%s placements restored from the Trash."
msgstr[0] ""
msgstr[1] ""

#: includes/admin/class-quick-bulk-edit.php:288
#: views/admin/ads/submitbox-meta.php:21
msgid "Month"
msgstr ""

#: includes/admin/class-quick-bulk-edit.php:299
#: views/admin/ads/submitbox-meta.php:34
msgid "Day"
msgstr ""

#: includes/admin/class-quick-bulk-edit.php:303
#: views/admin/ads/submitbox-meta.php:35
msgid "Year"
msgstr ""

#: includes/admin/class-quick-bulk-edit.php:312
#: views/admin/ads/submitbox-meta.php:36
msgid "Hour"
msgstr ""

#: includes/admin/class-quick-bulk-edit.php:316
#: views/admin/ads/submitbox-meta.php:37
msgid "Minute"
msgstr ""

#: includes/admin/class-quick-bulk-edit.php:322
msgid "Second"
msgstr ""

#: includes/admin/class-screen-options.php:179
msgid "Author"
msgstr ""

#: includes/admin/class-settings.php:75
msgid "Pro"
msgstr ""

#: includes/admin/class-settings.php:83
#: views/marketing/ad-metabox-all-access.php:14
#: views/marketing/ad-metabox-tracking.php:13
msgid "Tracking"
msgstr ""

#: includes/admin/class-settings.php:340
msgctxt "label before ads"
msgid "Advertisements"
msgstr ""

#: includes/admin/class-settings.php:434
msgid "Admin"
msgstr ""

#: includes/admin/class-settings.php:441
msgid "Disable Ad Health and other notices"
msgstr ""

#: includes/admin/class-settings.php:449
msgid "Allow editors to manage ads"
msgstr ""

#. translators: unfiltered_html
#: includes/admin/class-settings.php:463
msgid "Add the %s capability to user roles on multisite"
msgstr ""

#: includes/admin/class-settings.php:473
msgid "Delete data on uninstall"
msgstr ""

#: includes/admin/class-settings.php:490
#: includes/admin/class-settings.php:497
#: views/admin/page-bulk-edit.php:17
msgid "Disable ads"
msgstr ""

#: includes/admin/class-settings.php:505
msgid "Hide ads for user roles"
msgstr ""

#: includes/admin/class-settings.php:513
msgid "Hide ads for IP addresses"
msgstr ""

#: includes/admin/class-settings.php:521
msgid "Hide ads from bots"
msgstr ""

#: includes/admin/class-settings.php:530
msgid "Disable ads for post types"
msgstr ""

#: includes/admin/class-settings.php:547
#: includes/admin/metaboxes/class-ad-layout.php:40
msgid "Layout / Output"
msgstr ""

#: includes/admin/class-settings.php:554
msgid "ID prefix"
msgstr ""

#: includes/admin/class-settings.php:562
#: views/admin/bulk-edit.php:42
#: views/admin/metaboxes/ads/ad-layout.php:52
#: views/admin/quick-edit.php:38
msgid "Ad label"
msgstr ""

#: includes/admin/class-settings.php:570
msgid "Open links in a new window"
msgstr ""

#: includes/admin/class-settings.php:578
msgid "Use advanced JavaScript"
msgstr ""

#: includes/admin/class-settings.php:594
msgid "Content injection"
msgstr ""

#: includes/admin/class-settings.php:601
msgid "Content placement in post lists"
msgstr ""

#: includes/admin/class-settings.php:609
msgid "Priority of content injection filter"
msgstr ""

#: includes/admin/class-settings.php:700
msgid "Are you missing something?"
msgstr ""

#: includes/admin/class-shortcode-creator.php:193
msgctxt "shortcode creator"
msgid "Add an ad"
msgstr ""

#: includes/admin/class-shortcode-creator.php:194
msgctxt "shortcode creator"
msgid "Add shortcode"
msgstr ""

#: includes/admin/class-shortcode-creator.php:195
msgctxt "shortcode creator"
msgid "Cancel"
msgstr ""

#: includes/admin/class-upgrades.php:76
msgid "Pro Feature"
msgstr ""

#. translators: %1$s and %2$s are opening and closing <a> tags
#: includes/admin/class-upgrades.php:98
msgid "This looks like a Google Ad Manager ad. Use the %1$sGAM Integration%2$s."
msgstr ""

#: includes/admin/class-upgrades.php:101
msgid "A quick and error-free way of implementing ad units from your Google Ad Manager account."
msgstr ""

#: includes/admin/class-upgrades.php:198
msgid "Duplicate"
msgstr ""

#: includes/admin/class-version-control.php:220
msgid "Plugin info not found"
msgstr ""

#. translators: 1: Name of ad unit
#: includes/admin/metaboxes/class-ad-adsense.php:57
msgid "Earnings of  %1$s"
msgstr ""

#: includes/admin/metaboxes/class-ad-parameters.php:40
msgid "Ad Parameters"
msgstr ""

#: includes/admin/metaboxes/class-ad-targeting.php:40
msgid "Targeting"
msgstr ""

#: includes/admin/metaboxes/class-ad-targeting.php:64
msgid "Video"
msgstr ""

#: includes/admin/metaboxes/class-ad-types.php:40
msgid "Ad Type"
msgstr ""

#: includes/admin/pages/class-dashboard.php:41
#: includes/admin/pages/class-dashboard.php:52
#: includes/admin/pages/class-dashboard.php:53
msgid "Dashboard"
msgstr ""

#: includes/admin/pages/class-groups.php:48
msgid "Ad Groups & Rotations"
msgstr ""

#: includes/admin/pages/class-groups.php:49
msgid "Groups & Rotation"
msgstr ""

#: includes/admin/pages/class-onboarding.php:43
#: includes/admin/pages/class-onboarding.php:44
msgid "Onboarding Wizard"
msgstr ""

#: includes/admin/pages/class-onboarding.php:120
msgid "Loading..."
msgstr ""

#: includes/admin/pages/class-onboarding.php:121
msgid "Processing authorization..."
msgstr ""

#: includes/admin/pages/class-onboarding.php:123
msgid "Select an account"
msgstr ""

#: includes/admin/pages/class-onboarding.php:124
msgid "Please select an account to use"
msgstr ""

#: includes/admin/pages/class-onboarding.php:126
msgid "Exit the wizard without saving"
msgstr ""

#: includes/admin/pages/class-onboarding.php:127
msgid "Go back"
msgstr ""

#: includes/admin/pages/class-onboarding.php:129
msgid "Subscribe to our newsletter and get 2 add-ons for free"
msgstr ""

#: includes/admin/pages/class-onboarding.php:131
msgid "Enter your email address"
msgstr ""

#: includes/admin/pages/class-onboarding.php:134
msgid "Please select your image"
msgstr ""

#: includes/admin/pages/class-onboarding.php:135
#: includes/admin/pages/class-onboarding.php:163
msgid "Please paste your ad code"
msgstr ""

#: includes/admin/pages/class-onboarding.php:137
msgid "Congratulations, your ad is now published!"
msgstr ""

#: includes/admin/pages/class-onboarding.php:138
msgid "Your ad is almost ready!"
msgstr ""

#: includes/admin/pages/class-onboarding.php:139
msgid "Congratulations, AdSense Auto Ads are now set up!"
msgstr ""

#: includes/admin/pages/class-onboarding.php:143
msgid "I want to use mostly Google AdSense or Google Auto Ads"
msgstr ""

#: includes/admin/pages/class-onboarding.php:144
msgid "I want to add a banner ad with an image"
msgstr ""

#: includes/admin/pages/class-onboarding.php:145
msgid "I want to insert an ad code from an ad network"
msgstr ""

#: includes/admin/pages/class-onboarding.php:146
msgid "Welcome! To kick things off, and to simplify your journey, answer a few questions and let Advanced Ads tailor the perfect ad for your site's needs."
msgstr ""

#: includes/admin/pages/class-onboarding.php:147
msgid "I agree to share usage data to <strong>help the developers</strong> improve the plugin. Read more in our <a href=\"https://wpadvancedads.com/privacy-policy/\" target=\"_blank\">privacy policy</a>."
msgstr ""

#: includes/admin/pages/class-onboarding.php:148
msgid "What's your task?"
msgstr ""

#: includes/admin/pages/class-onboarding.php:151
msgid "Select an image to upload"
msgstr ""

#: includes/admin/pages/class-onboarding.php:152
msgid "Use this image"
msgstr ""

#: includes/admin/pages/class-onboarding.php:153
msgid "Upload"
msgstr ""

#: includes/admin/pages/class-onboarding.php:154
msgid "Replace"
msgstr ""

#: includes/admin/pages/class-onboarding.php:155
msgid "Would you like to set a target URL for your image ad?"
msgstr ""

#: includes/admin/pages/class-onboarding.php:156
msgid "Enter an optional target URL for your image ad"
msgstr ""

#: includes/admin/pages/class-onboarding.php:157
#: includes/admin/pages/class-onboarding.php:200
msgid "Create placement and ad"
msgstr ""

#: includes/admin/pages/class-onboarding.php:158
msgid "Please select an image"
msgstr ""

#: includes/admin/pages/class-onboarding.php:161
msgid "Paste the ad code that your advertising network has provided to you"
msgstr ""

#: includes/admin/pages/class-onboarding.php:162
msgid "Insert the ad code into my site"
msgstr ""

#: includes/admin/pages/class-onboarding.php:168
msgid "I will place ad units manually"
msgstr ""

#: includes/admin/pages/class-onboarding.php:172
msgid "I will use Auto Ads and let Google place the ads automatically"
msgstr ""

#: includes/admin/pages/class-onboarding.php:178
msgid "Enable Auto Ads on my site"
msgstr ""

#: includes/admin/pages/class-onboarding.php:182
msgid "Enable Accelerated Mobile Pages (AMP) Auto Ads"
msgstr ""

#: includes/admin/pages/class-onboarding.php:187
msgid "Unknown error while saving account information."
msgstr ""

#: includes/admin/pages/class-onboarding.php:188
msgid "Unknown error while fetching AdSense account information."
msgstr ""

#: includes/admin/pages/class-onboarding.php:189
msgid "Unknown error while submitting the authorization code."
msgstr ""

#: includes/admin/pages/class-onboarding.php:191
msgid "Do you have a Google AdSense account?"
msgstr ""

#: includes/admin/pages/class-onboarding.php:192
msgid "No, I’d like to sign up for free now"
msgstr ""

#: includes/admin/pages/class-onboarding.php:193
msgid "Yes, connect to AdSense now"
msgstr ""

#: includes/admin/pages/class-onboarding.php:194
msgid "Account holder name:"
msgstr ""

#: includes/admin/pages/class-onboarding.php:195
msgid "You are connected to Google AdSense. Publisher ID:"
msgstr ""

#: includes/admin/pages/class-onboarding.php:196
msgid "Will you place ad units manually or use Google Auto Ads?"
msgstr ""

#: includes/admin/pages/class-onboarding.php:197
msgid "Please confirm these Auto Ads options"
msgstr ""

#: includes/admin/pages/class-onboarding.php:198
msgid "Process"
msgstr ""

#: includes/admin/pages/class-onboarding.php:201
msgid "Confirm Auto Ads options"
msgstr ""

#: includes/admin/pages/class-onboarding.php:203
msgid "Please select an option"
msgstr ""

#: includes/admin/pages/class-onboarding.php:207
msgid "For the last step, import the desired ad unit from AdSense. Visit the ad's edit screen to make your selection."
msgstr ""

#: includes/admin/pages/class-onboarding.php:208
msgid "Select ad unit"
msgstr ""

#. translators: 1: opening strong tag, 2: closing strong tag, 3: opening italic tag, 4: closing italic tag.
#: includes/admin/pages/class-onboarding.php:211
msgid "We have created a placement for your ad that will display %1$safter the 3rd paragraph on every post%2$s. Go to %3$sAdvanced Ads > Placements%4$s and edit the placement to change this."
msgstr ""

#: includes/admin/pages/class-onboarding.php:219
msgid "Everything's ready for AdSense to populate your site with Auto Ads. Make sure your site is verified, <strong>enable Auto Ads in your AdSense account</strong>, and, optionally, fine-tune their settings further."
msgstr ""

#: includes/admin/pages/class-onboarding.php:220
msgid "Go to AdSense account"
msgstr ""

#: includes/admin/pages/class-onboarding.php:222
msgid "We have created a placement for your ad that will display <strong>after the 3rd paragraph on every post</strong>. You may edit the placement to change this."
msgstr ""

#: includes/admin/pages/class-onboarding.php:223
msgid "See the live ad in your website's frontend."
msgstr ""

#: includes/admin/pages/class-onboarding.php:224
msgid "Edit the placement"
msgstr ""

#: includes/admin/pages/class-onboarding.php:225
msgid "See the live ad"
msgstr ""

#: includes/admin/pages/class-onboarding.php:226
msgid "Upgrade to all features and full support today"
msgstr ""

#: includes/admin/pages/class-onboarding.php:227
msgid "Our All Access deal offers every drop of ad expertise that we've acquired in more than ten years, distilled into one jam-packed plugin bundle, supported by a dedicated team of real persons eager to help you."
msgstr ""

#: includes/admin/pages/class-onboarding.php:228
msgid "Upgrade now"
msgstr ""

#: includes/admin/pages/class-onboarding.php:229
msgid "Go to the Dashboard"
msgstr ""

#: includes/admin/pages/class-onboarding.php:232
msgid "More placements"
msgstr ""

#: includes/admin/pages/class-onboarding.php:233
msgid "to embed in high-converting spots"
msgstr ""

#: includes/admin/pages/class-onboarding.php:237
msgid "More conditions"
msgstr ""

#: includes/admin/pages/class-onboarding.php:238
msgid "for advanced targeting"
msgstr ""

#: includes/admin/pages/class-onboarding.php:242
msgid "Ad Tracking"
msgstr ""

#: includes/admin/pages/class-onboarding.php:243
msgid "to optimize performance"
msgstr ""

#: includes/admin/pages/class-onboarding.php:248
msgid "to safeguard your accounts"
msgstr ""

#: includes/admin/pages/class-onboarding.php:253
msgid "to speed up your website"
msgstr ""

#: includes/admin/pages/class-onboarding.php:257
msgid "and much more!"
msgstr ""

#: includes/admin/pages/class-placements.php:43
#: includes/class-entities.php:124
msgid "Ad Placements"
msgstr ""

#: includes/admin/pages/class-placements.php:44
#: includes/class-widget.php:121
#: includes/utilities/class-data.php:303
#: modules/gutenberg/includes/class-gutenberg.php:137
#: views/admin/tables/ads/column-used.php:34
msgid "Placements"
msgstr ""

#: includes/admin/pages/class-settings.php:49
msgid "Settings"
msgstr ""

#: includes/admin/pages/class-tools.php:39
#: includes/admin/pages/class-tools.php:40
msgid "Tools"
msgstr ""

#: includes/admin/pages/class-tools.php:50
msgid "Import & Export"
msgstr ""

#: includes/admin/pages/class-tools.php:54
msgid "Version Control"
msgstr ""

#: includes/admin/pages/class-ui-toolkit.php:38
msgid "Advanced Ads Ui Toolkit"
msgstr ""

#: includes/admin/pages/class-ui-toolkit.php:39
#: views/admin/screens/ui-toolkit.php:13
msgid "Ui Toolkit"
msgstr ""

#: includes/admin/pages/class-ui-toolkit.php:49
msgid "Basic"
msgstr ""

#: includes/admin/pages/class-ui-toolkit.php:53
msgid "Forms"
msgstr ""

#: includes/admin/pages/class-ui-toolkit.php:57
msgid "Advanced"
msgstr ""

#: includes/ads/class-ad-repository.php:56
msgid "New ad content goes here"
msgstr ""

#: includes/ads/class-ad-repository.php:91
msgid "Invalid ad."
msgstr ""

#: includes/ads/types/type-amp.php:46
msgid "AMP"
msgstr ""

#: includes/ads/types/type-amp.php:55
msgid "Ads that are visible on Accelerated Mobile Pages."
msgstr ""

#: includes/ads/types/type-content.php:46
msgid "Rich Content"
msgstr ""

#: includes/ads/types/type-content.php:55
msgid "The full content editor from WordPress with all features like shortcodes, image upload or styling, but also simple text/html mode for scripts and code."
msgstr ""

#: includes/ads/types/type-dummy.php:46
msgid "Dummy"
msgstr ""

#: includes/ads/types/type-dummy.php:55
msgid "Uses a simple placeholder ad for quick testing."
msgstr ""

#: includes/ads/types/type-dummy.php:105
#: includes/ads/types/type-image.php:164
msgid "URL"
msgstr ""

#: includes/ads/types/type-gam.php:45
msgid "Google Ad Manager"
msgstr ""

#: includes/ads/types/type-gam.php:54
msgid "Load ad units directly from your Google Ad Manager account."
msgstr ""

#: includes/ads/types/type-group.php:47
#: includes/ads/types/type-group.php:105
msgid "Ad Group"
msgstr ""

#: includes/ads/types/type-group.php:56
msgid "Choose an existing ad group. Use this type when you want to assign the same display and visitor conditions to all ads in that group."
msgstr ""

#: includes/ads/types/type-group.php:117
msgid "Select a group"
msgstr ""

#: includes/ads/types/type-image.php:47
msgid "Image Ad"
msgstr ""

#: includes/ads/types/type-image.php:56
msgid "Ads in various image formats."
msgstr ""

#: includes/ads/types/type-image.php:147
msgid "Insert File"
msgstr ""

#: includes/ads/types/type-image.php:148
msgid "Insert"
msgstr ""

#: includes/ads/types/type-image.php:150
msgid "Select image"
msgstr ""

#: includes/ads/types/type-image.php:168
msgid "Link to target site including http(s)"
msgstr ""

#. translators: $s is a size string like "728 x 90".
#: includes/ads/types/type-image.php:196
msgid "Original size: %s"
msgstr ""

#: includes/ads/types/type-plain.php:47
msgid "Plain Text and Code"
msgstr ""

#: includes/ads/types/type-plain.php:56
msgid "Any ad network, Amazon, customized AdSense codes, shortcodes, and code like JavaScript, HTML or PHP."
msgstr ""

#: includes/ads/types/type-plain.php:106
msgid "Insert plain text or code into this field."
msgstr ""

#: includes/ads/types/type-plain.php:134
msgid "Allow PHP"
msgstr ""

#: includes/ads/types/type-plain.php:143
msgid "Execute PHP code (wrapped in <code>&lt;?php ?&gt;</code>)"
msgstr ""

#. translators: The name of the constant preventing PHP execution
#: includes/ads/types/type-plain.php:156
msgid "Executing PHP code has been disallowed by %s"
msgstr ""

#: includes/ads/types/type-plain.php:163
msgid "Using PHP code can be dangerous. Please make sure you know what you are doing."
msgstr ""

#: includes/ads/types/type-plain.php:167
msgid "No PHP tag detected in your code."
msgstr ""

#: includes/ads/types/type-plain.php:167
#: includes/ads/types/type-plain.php:190
msgid "Uncheck this checkbox for improved performance."
msgstr ""

#: includes/ads/types/type-plain.php:185
msgid "Execute shortcodes"
msgstr ""

#: includes/ads/types/type-plain.php:190
msgid "No shortcode detected in your code."
msgstr ""

#: includes/ads/types/type-plain.php:217
msgid "You do not have sufficient permissions to include all HTML tags."
msgstr ""

#: includes/ads/types/type-plain.php:219
msgid "The creator of the ad does not have sufficient permissions to include all HTML tags."
msgstr ""

#: includes/ads/types/type-plain.php:225
msgid "Assign ad to me"
msgstr ""

#: includes/ads/types/type-unknown.php:61
#: includes/groups/types/type-unknown.php:61
#: includes/placements/types/class-unknown.php:63
msgid "Unknown type"
msgstr ""

#: includes/ads/types/type-unknown.php:70
#: includes/groups/types/type-unknown.php:70
#: includes/placements/types/class-unknown.php:72
msgid "No description"
msgstr ""

#: includes/array_ad_conditions.php:35
msgid "Post Types"
msgstr ""

#: includes/array_ad_conditions.php:41
msgid "Categories, Tags and Taxonomies"
msgstr ""

#: includes/array_ad_conditions.php:42
msgid "Choose terms from public category, tag and other taxonomies a post must belong to in order to have ads."
msgstr ""

#: includes/array_ad_conditions.php:47
msgid "Category Archives"
msgstr ""

#: includes/array_ad_conditions.php:48
msgid "comma seperated IDs of category archives"
msgstr ""

#: includes/array_ad_conditions.php:53
msgid "Individual Posts, Pages and Public Post Types"
msgstr ""

#. translators: 1: is a link to a support document. 2: closing link
#: includes/class-autoloader.php:128
msgid "Your installation of Advanced Ads is incomplete. If you installed Advanced Ads from GitHub, %1$s please refer to this document%2$s to set up your development environment."
msgstr ""

#. translators: %s: WP_ENVIRONMENT_TYPES
#: includes/class-autoloader.php:172
msgid "The %s constant is no longer supported."
msgstr ""

#: includes/class-entities.php:47
msgid "Add New Ad"
msgstr ""

#: includes/class-entities.php:49
msgid "Edit Ad"
msgstr ""

#: includes/class-entities.php:51
#: includes/class-entities.php:131
msgid "View"
msgstr ""

#: includes/class-entities.php:52
msgid "View the Ad"
msgstr ""

#: includes/class-entities.php:53
msgid "Search Ads"
msgstr ""

#: includes/class-entities.php:54
msgid "No Ads found"
msgstr ""

#: includes/class-entities.php:55
msgid "No Ads found in Trash"
msgstr ""

#: includes/class-entities.php:56
msgid "Parent Ad"
msgstr ""

#: includes/class-entities.php:106
msgid "Expired"
msgstr ""

#: includes/class-entities.php:125
msgid "Ad Placement"
msgstr ""

#: includes/class-entities.php:127
msgid "Add New Placement"
msgstr ""

#: includes/class-entities.php:129
msgid "Edit Placement"
msgstr ""

#: includes/class-entities.php:132
msgid "View the Ad Placement"
msgstr ""

#: includes/class-entities.php:133
msgid "Search Ad Placements"
msgstr ""

#: includes/class-entities.php:134
msgid "No Ad Placements found"
msgstr ""

#: includes/class-entities.php:135
msgid "No Ad Placements found in Trash"
msgstr ""

#: includes/class-entities.php:140
msgid "Placements are physically places in your theme and posts. You can use them if you plan to change ads and ad groups on the same place without the need to change your templates."
msgstr ""

#: includes/class-entities.php:190
msgctxt "ad group general name"
msgid "Ad Groups & Rotations"
msgstr ""

#: includes/class-entities.php:191
#: includes/traits/class-entity.php:38
msgctxt "ad group singular name"
msgid "Ad Group"
msgstr ""

#: includes/class-entities.php:192
msgid "Search Ad Groups"
msgstr ""

#: includes/class-entities.php:193
msgid "All Ad Groups"
msgstr ""

#: includes/class-entities.php:194
msgid "Parent Ad Groups"
msgstr ""

#: includes/class-entities.php:195
msgid "Parent Ad Groups:"
msgstr ""

#: includes/class-entities.php:196
msgid "Edit Ad Group"
msgstr ""

#: includes/class-entities.php:197
msgid "Update Ad Group"
msgstr ""

#: includes/class-entities.php:199
msgid "New Ad Groups Name"
msgstr ""

#: includes/class-entities.php:200
#: views/admin/tables/ads/column-used.php:14
msgid "Groups"
msgstr ""

#: includes/class-entities.php:236
msgid "Placements are customizable ad spots on your site. Use them to see and change all the assigned ads and groups on this page. Furthermore, you can set up exclusive features like Cache Busting, Lazy Loading, AdBlocker fallbacks, or Parallax effects."
msgstr ""

#: includes/class-entities.php:245
msgid "Ad Groups are a flexible method to bundle ads. Use them to create ad rotations, run split tests, and organize your ads in the backend. An ad can belong to multiple ad groups."
msgstr ""

#: includes/class-widget.php:31
msgid "Display Ads and Ad Groups."
msgstr ""

#: includes/class-widget.php:115
msgid "Title:"
msgstr ""

#: includes/class-widget.php:119
#: includes/utilities/class-data.php:287
#: modules/gutenberg/includes/class-gutenberg.php:133
msgid "--empty--"
msgstr ""

#: includes/compatibility/class-peepso.php:52
msgid "Display this ad in PeepSo Stream"
msgstr ""

#: includes/frontend/class-debug-ads.php:82
msgid "The ad is displayed on the page"
msgstr ""

#: includes/frontend/class-debug-ads.php:84
msgid "The ad is not displayed on the page"
msgstr ""

#: includes/frontend/class-debug-ads.php:89
msgid "Current query is not identical to main query."
msgstr ""

#: includes/frontend/class-debug-ads.php:94
msgid "current post"
msgstr ""

#: includes/frontend/class-debug-ads.php:99
msgid "Current post is not identical to main post."
msgstr ""

#: includes/frontend/class-debug-ads.php:101
msgid "main post"
msgstr ""

#: includes/frontend/class-debug-ads.php:154
msgid "current query"
msgstr ""

#: includes/frontend/class-debug-ads.php:155
msgid "main query"
msgstr ""

#: includes/groups/class-group-repository.php:81
msgid "Invalid group."
msgstr ""

#: includes/groups/types/type-grid.php:46
msgid "Grid"
msgstr ""

#: includes/groups/types/type-ordered.php:46
msgid "Ordered ads"
msgstr ""

#: includes/groups/types/type-ordered.php:55
msgid "Display ads with the highest ad weight first"
msgstr ""

#: includes/groups/types/type-slider.php:46
#: views/marketing/ad-metabox-all-access.php:20
msgid "Ad Slider"
msgstr ""

#: includes/groups/types/type-standard.php:46
msgid "Random ads"
msgstr ""

#: includes/groups/types/type-standard.php:55
msgid "Display random ads based on ad weight"
msgstr ""

#: includes/importers/class-ad-inserter.php:44
msgid "Ad Inserter"
msgstr ""

#. translators: %d: number of ad blocks
#: includes/importers/class-ad-inserter.php:88
msgid "We found Ad Inserter configuration with <strong>%d ad blocks</strong>."
msgstr ""

#: includes/importers/class-ad-inserter.php:91
msgid "Import Ad Blocks"
msgstr ""

#: includes/importers/class-ad-inserter.php:92
#: includes/importers/class-ads-wp-ads.php:77
#: includes/importers/class-amp-wp-ads.php:78
#: includes/importers/class-quick-adsense.php:78
#: includes/importers/class-wp-quads.php:118
msgid "Import Settings"
msgstr ""

#. translators: 1: counts 2: Importer title
#: includes/importers/class-ad-inserter.php:117
#: includes/importers/class-api-ads.php:212
#: includes/importers/class-google-sheet.php:281
#: includes/importers/class-wp-quads.php:151
msgid "%1$d ads migrated from %2$s"
msgstr ""

#: includes/importers/class-ads-wp-ads.php:36
msgid "Ads for WP Ads"
msgstr ""

#: includes/importers/class-ads-wp-ads.php:74
#: includes/importers/class-amp-wp-ads.php:75
#: includes/importers/class-quick-adsense.php:75
#: includes/importers/class-wp-quads.php:112
msgid "Import Ads"
msgstr ""

#: includes/importers/class-ads-wp-ads.php:75
#: includes/importers/class-amp-wp-ads.php:76
#: includes/importers/class-quick-adsense.php:76
msgid "Import Groups"
msgstr ""

#: includes/importers/class-ads-wp-ads.php:76
#: includes/importers/class-amp-wp-ads.php:77
#: includes/importers/class-quick-adsense.php:77
msgid "Import Placements"
msgstr ""

#: includes/importers/class-amp-wp-ads.php:37
msgid "AMP for WP Ads"
msgstr ""

#: includes/importers/class-api-ads.php:55
msgid "Ads from API"
msgstr ""

#: includes/importers/class-api-ads.php:64
msgid "For MonetizeMore clients using PubGuru, you will be able to create all of your new ads from api."
msgstr ""

#: includes/importers/class-google-sheet.php:45
msgid "PubGuru Importer"
msgstr ""

#: includes/importers/class-google-sheet.php:54
msgid "For MonetizeMore clients using PubGuru, you will be able to upload all of your new ads from your Google sheet. Please make sure that you support rep has confirmed that you are ready to do so. Below you will a “rollback changes” option, in case of any error. As a warning, these ad placements will over take your current ad setup."
msgstr ""

#: includes/importers/class-google-sheet.php:113
#: includes/importers/class-wp-quads.php:839
msgid "Desktop"
msgstr ""

#: includes/importers/class-google-sheet.php:114
#: includes/importers/class-wp-quads.php:865
msgid "Mobile"
msgstr ""

#: includes/importers/class-google-sheet.php:143
msgid "No google sheet url found."
msgstr ""

#: includes/importers/class-google-sheet.php:154
msgid "No ads found in google sheet."
msgstr ""

#: includes/importers/class-manager.php:109
msgid "History deleted successfully."
msgstr ""

#: includes/importers/class-plugin-exporter.php:55
msgid "User dont have premission to export."
msgstr ""

#: includes/importers/class-plugin-exporter.php:60
msgid "No content option selected to export."
msgstr ""

#: includes/importers/class-quick-adsense.php:37
msgid "Quick Adsense"
msgstr ""

#: includes/importers/class-tutorials.php:36
msgid "Tutorials"
msgstr ""

#: includes/importers/class-tutorials.php:85
msgid "While these other import options are still in beta, we do have resources on how to make setting up your first ad easier than ever.  If you have any specific feature requests please make sure to contact us or <a href=\"https://wpadvancedads.com/support/missing-feature/\">request a feature</a>."
msgstr ""

#: includes/importers/class-tutorials.php:90
msgid "Quick Links"
msgstr ""

#: includes/importers/class-tutorials.php:95
msgid "Import and Export"
msgstr ""

#: includes/importers/class-tutorials.php:100
msgid "Ad Templates"
msgstr ""

#: includes/importers/class-wp-quads.php:62
msgid "WP Quads"
msgstr ""

#. translators: Number of ads
#: includes/importers/class-wp-quads.php:104
msgid "We found <strong>%d ads</strong>."
msgstr ""

#: includes/importers/class-wp-quads.php:133
msgid "Nothing imported."
msgstr ""

#: includes/importers/class-wp-quads.php:158
msgid "Settings imported."
msgstr ""

#. translators: %s: A name of not loaded extension.
#: includes/importers/class-xml-encoder.php:61
#: includes/importers/class-xml-encoder.php:64
#: includes/importers/class-xml-encoder.php:202
#: includes/importers/class-xml-encoder.php:206
msgid "The %s extension(s) is not loaded"
msgstr ""

#: includes/importers/class-xml-encoder.php:75
msgctxt "import_export"
msgid "The data must be an array"
msgstr ""

#. translators: %s node data
#: includes/importers/class-xml-encoder.php:148
msgctxt "import_export"
msgid "An unexpected value could not be serialized: %s"
msgstr ""

#: includes/importers/class-xml-encoder.php:211
msgctxt "import_export"
msgid "Invalid XML data, it can not be empty"
msgstr ""

#. translators: %s error messages while trying to decode xml file
#: includes/importers/class-xml-encoder.php:246
msgctxt "import_export"
msgid "XML error: %s"
msgstr ""

#: includes/importers/class-xml-importer.php:92
msgid "XML"
msgstr ""

#: includes/importers/class-xml-importer.php:139
msgid "Please enter XML content"
msgstr ""

#: includes/importers/class-xml-importer.php:153
msgid "Please select import type"
msgstr ""

#. translators: %s number of attachments
#: includes/importers/class-xml-importer.php:158
msgid "%s attachment uploaded"
msgid_plural "%s attachments uploaded"
msgstr[0] ""
msgstr[1] ""

#. translators: %s import directory
#: includes/importers/class-xml-importer.php:251
msgid "Failed to create import directory <em>%s</em>"
msgstr ""

#. translators: %s import directory
#: includes/importers/class-xml-importer.php:257
msgid "Import directory is not writable: <em>%s</em>"
msgstr ""

#: includes/importers/class-xml-importer.php:266
msgid "File is empty, uploads are disabled or post_max_size is smaller than upload_max_filesize in php.ini"
msgstr ""

#. translators: %s error in file
#: includes/importers/class-xml-importer.php:277
msgid "Failed to upload file, error: <em>%s</em>"
msgstr ""

#: includes/importers/class-xml-importer.php:282
msgid "File is empty."
msgstr ""

#. translators: %s import id
#: includes/importers/class-xml-importer.php:288
msgid "The file could not be created: <em>%s</em>. This is probably a permissions problem"
msgstr ""

#. translators: %s Ad title
#: includes/importers/class-xml-importer.php:356
msgid "Failed to import <em>%s</em>"
msgstr ""

#. translators: %s number of ads
#: includes/importers/class-xml-importer.php:432
msgid "%s ad imported"
msgid_plural "%s ads imported"
msgstr[0] ""
msgstr[1] ""

#. translators: %s number of groups
#: includes/importers/class-xml-importer.php:490
msgid "%s group imported"
msgid_plural "%s groups imported"
msgstr[0] ""
msgstr[1] ""

#. translators: %s number of placements
#: includes/importers/class-xml-importer.php:646
msgid "%s placement imported"
msgid_plural "%s placements imported"
msgstr[0] ""
msgstr[1] ""

#. translators: %s number of options
#: includes/importers/class-xml-importer.php:726
msgid "%s option imported"
msgid_plural "%s options imported"
msgstr[0] ""
msgstr[1] ""

#. translators: %s image url
#: includes/importers/class-xml-importer.php:757
msgid "Invalid filetype <em>%s</em>"
msgstr ""

#. translators: %s image url
#: includes/importers/class-xml-importer.php:763
#: includes/importers/class-xml-importer.php:771
#: includes/importers/class-xml-importer.php:780
#: includes/importers/class-xml-importer.php:808
msgid "Error getting remote image <em>%s</em>"
msgstr ""

#. translators: %s image url
#: includes/importers/class-xml-importer.php:790
msgid "Zero size file downloaded <em>%s</em>"
msgstr ""

#. translators: 1: Attachment ID 2: Attachment link
#: includes/importers/class-xml-importer.php:850
msgid "New attachment created <em>%1$s</em> %2$s"
msgstr ""

#: includes/installation/class-capabilities.php:48
msgid "Allows changing plugin options"
msgstr ""

#: includes/installation/class-capabilities.php:54
msgid "Allows access to the Advanced Ads backend"
msgstr ""

#: includes/installation/class-capabilities.php:60
msgid "Allows editing ads"
msgstr ""

#: includes/installation/class-capabilities.php:66
msgid "Allows changing the placements page"
msgstr ""

#: includes/installation/class-capabilities.php:72
msgid "Enables shortcode button"
msgstr ""

#: includes/placements/class-placement-repository.php:44
msgid "New placement content goes here"
msgstr ""

#: includes/placements/class-placement-repository.php:75
msgid "Invalid placement."
msgstr ""

#: includes/placements/types/class-after-content.php:56
msgid "Injected after the post content."
msgstr ""

#: includes/placements/types/class-before-content.php:56
msgid "Injected before the post content."
msgstr ""

#: includes/placements/types/class-content.php:56
msgid "Injected into the content. You can choose the paragraph after which the ad content is displayed."
msgstr ""

#: includes/placements/types/class-footer.php:47
msgid "Footer Code"
msgstr ""

#: includes/placements/types/class-footer.php:56
msgid "Injected in Footer (before closing &lt;/body&gt; Tag)."
msgstr ""

#: includes/placements/types/class-header.php:47
msgid "Header Code"
msgstr ""

#: includes/placements/types/class-header.php:56
msgid "Injected in Header (before closing &lt;/head&gt; Tag, often not visible)."
msgstr ""

#: includes/placements/types/class-sidebar-widget.php:47
msgid "Sidebar Widget"
msgstr ""

#: includes/placements/types/class-sidebar-widget.php:56
msgid "Create a sidebar widget with an ad. Can be placed and used like any other widget."
msgstr ""

#: includes/placements/types/class-standard.php:47
msgid "Manual Placement"
msgstr ""

#: includes/placements/types/class-standard.php:56
msgid "Manual placement to use as function or shortcode."
msgstr ""

#: includes/rest/class-groups.php:60
#: includes/rest/class-placements.php:64
msgid "No endpoint found"
msgstr ""

#: includes/rest/class-groups.php:74
msgid "Sorry, you are not allowed to access this feature."
msgstr ""

#: includes/rest/class-groups.php:97
#: includes/rest/class-groups.php:136
msgid "Invalid nonce"
msgstr ""

#: includes/rest/class-onboarding.php:66
msgid "The task code posted is not allowed"
msgstr ""

#: includes/rest/class-placements.php:79
msgid "No placement found"
msgstr ""

#: includes/rest/class-placements.php:102
msgid "Not authorized create"
msgstr ""

#: includes/rest/class-placements.php:108
msgid "No placement data provided"
msgstr ""

#: includes/rest/class-placements.php:140
msgid "Not authorized update"
msgstr ""

#: includes/rest/class-placements.php:146
msgid "Placement not found"
msgstr ""

#: includes/traits/class-entity.php:42
msgctxt "ad placement singular name"
msgid "Placement"
msgstr ""

#: includes/traits/class-entity.php:45
msgid "Unknown"
msgstr ""

#. translators: %s is an html tag.
#: includes/utilities/class-content-injection.php:34
msgid "paragraph (%s)"
msgstr ""

#. translators: %s is an html tag.
#: includes/utilities/class-content-injection.php:36
msgid "paragraph without image (%s)"
msgstr ""

#. translators: %s is an html tag.
#: includes/utilities/class-content-injection.php:38
msgid "headline 2 (%s)"
msgstr ""

#. translators: %s is an html tag.
#: includes/utilities/class-content-injection.php:40
msgid "headline 3 (%s)"
msgstr ""

#. translators: %s is an html tag.
#: includes/utilities/class-content-injection.php:42
msgid "headline 4 (%s)"
msgstr ""

#. translators: %s is an html tag.
#: includes/utilities/class-content-injection.php:44
msgid "any headline (%s)"
msgstr ""

#. translators: %s is an html tag.
#: includes/utilities/class-content-injection.php:46
msgid "image (%s)"
msgstr ""

#. translators: %s is an html tag.
#: includes/utilities/class-content-injection.php:48
msgid "table (%s)"
msgstr ""

#. translators: %s is an html tag.
#: includes/utilities/class-content-injection.php:50
msgid "list item (%s)"
msgstr ""

#. translators: %s is an html tag.
#: includes/utilities/class-content-injection.php:52
msgid "quote (%s)"
msgstr ""

#. translators: %s is an html tag.
#: includes/utilities/class-content-injection.php:54
msgid "iframe (%s)"
msgstr ""

#. translators: %s is an html tag.
#: includes/utilities/class-content-injection.php:56
msgid "container (%s)"
msgstr ""

#: includes/utilities/class-content-injection.php:57
msgid "any element"
msgstr ""

#: includes/utilities/class-content-injection.php:58
msgctxt "for the \"custom\" content placement option"
msgid "custom"
msgstr ""

#. translators: %1$s is the opening link tag, %2$s is closing link tag
#: includes/utilities/class-wordpress.php:396
msgid "This feature is deprecated. Please find the removal schedule %1$shere%2$s"
msgstr ""

#: modules/ad-blocker/admin/admin.php:91
msgid "Ad blocker disguise"
msgstr ""

#: modules/ad-blocker/admin/admin.php:138
#: modules/ads-txt/admin/class-advanced-ads-ads-txt-admin.php:418
msgid "Unable to connect to the filesystem. Please confirm your credentials."
msgstr ""

#: modules/ad-blocker/admin/admin.php:152
msgid "The asset folder was rebuilt successfully"
msgstr ""

#: modules/ad-blocker/admin/admin.php:197
msgid "There is no writable upload folder"
msgstr ""

#. translators: %s old folder name
#: modules/ad-blocker/admin/admin.php:230
msgid "Unable to rename \"%s\" directory"
msgstr ""

#. translators: %s folder name
#: modules/ad-blocker/admin/admin.php:247
#: modules/ad-blocker/admin/admin.php:261
#: modules/ad-blocker/admin/admin.php:278
msgid "Unable to copy assets to the \"%s\" directory"
msgstr ""

#. translators: %s directory path
#: modules/ad-blocker/admin/admin.php:312
#: modules/ad-blocker/admin/admin.php:397
msgid "We do not have direct write access to the \"%s\" directory"
msgstr ""

#. translators: %s directory path
#: modules/ad-blocker/admin/admin.php:409
msgid "Unable to copy files to %s"
msgstr ""

#: modules/ad-blocker/admin/views/rebuild_form.php:21
msgid "Upload folder is not writable"
msgstr ""

#: modules/ad-blocker/admin/views/rebuild_form.php:32
msgid "Asset path"
msgstr ""

#: modules/ad-blocker/admin/views/rebuild_form.php:36
msgid "Asset URL"
msgstr ""

#: modules/ad-blocker/admin/views/rebuild_form.php:40
msgid "Rename assets"
msgstr ""

#: modules/ad-blocker/admin/views/rebuild_form.php:44
msgid "Check if you want to change the names of the assets"
msgstr ""

#: modules/ad-blocker/admin/views/rebuild_form.php:47
msgid "This feature relocates potentially blocked scripts to a new, randomly named folder to help bypass ad blockers. The folder receives updates during plugin updates. Occasional rebuilding of the asset folder prevents browsers from caching outdated versions. If you're already using a plugin that renames scripts, like Autoptimize or WP Rocket, turn off this feature to avoid conflicts."
msgstr ""

#. translators: placeholder is path to folder in uploads dir
#: modules/ad-blocker/admin/views/rebuild_form.php:63
msgid "Please, rebuild the asset folder. All assets will be located in %s"
msgstr ""

#: modules/ad-blocker/admin/views/rebuild_form.php:72
msgid "Rebuild asset folder"
msgstr ""

#: modules/ad-blocker/admin/views/setting-use-adblocker.php:15
msgid "The ad block disguise can only be set by the super admin on the main site in the network."
msgstr ""

#: modules/ad-blocker/admin/views/setting-use-adblocker.php:17
msgid "Prevents ad blockers from breaking your website when blocking asset files (.js, .css)."
msgstr ""

#. translators: %s is a URL.
#: modules/ad-blocker/admin/views/setting-use-adblocker.php:24
msgid "Learn how to display alternative content to ad block users <a href=\"%s\" target=\"_blank\">in the manual</a>."
msgstr ""

#: modules/ad-positioning/classes/ad-positioning.php:114
msgid "Theme’s Default"
msgstr ""

#: modules/ad-positioning/classes/ad-positioning.php:115
#: modules/gutenberg/includes/class-gutenberg.php:159
msgid "The ad will behave as predefined by the theme."
msgstr ""

#: modules/ad-positioning/classes/ad-positioning.php:121
msgctxt "Layout options \"Text Flow\" heading"
msgid "Float"
msgstr ""

#: modules/ad-positioning/classes/ad-positioning.php:122
#: modules/gutenberg/includes/class-gutenberg.php:163
#: modules/gutenberg/includes/class-gutenberg.php:167
msgid "Text will wrap around the ad and its margin."
msgstr ""

#: modules/ad-positioning/classes/ad-positioning.php:129
msgctxt "Layout options \"Text Flow\" heading"
msgid "Block"
msgstr ""

#: modules/ad-positioning/classes/ad-positioning.php:130
#: modules/gutenberg/includes/class-gutenberg.php:171
#: modules/gutenberg/includes/class-gutenberg.php:175
#: modules/gutenberg/includes/class-gutenberg.php:179
msgid "Text will continue after the ad and its margin."
msgstr ""

#: modules/ad-positioning/classes/ad-positioning.php:179
msgctxt "Ad positioning spacing label"
msgid "Top"
msgstr ""

#: modules/ad-positioning/classes/ad-positioning.php:182
msgctxt "Ad positioning spacing label"
msgid "Right"
msgstr ""

#: modules/ad-positioning/classes/ad-positioning.php:185
msgctxt "Ad positioning spacing label"
msgid "Bottom"
msgstr ""

#: modules/ad-positioning/classes/ad-positioning.php:188
msgctxt "Ad positioning spacing label"
msgid "Left"
msgstr ""

#: modules/ad-positioning/views/ad-positioning.php:13
msgid "Text Flow"
msgstr ""

#: modules/ad-positioning/views/ad-spacing.php:17
msgid "Margin"
msgstr ""

#: modules/ad-positioning/views/ad-spacing.php:47
msgctxt "Ad positioning spacing legend text"
msgid "in px"
msgstr ""

#: modules/adblock-finder/admin/admin.php:33
#: modules/adblock-finder/admin/admin.php:47
msgid "Ad Blocker"
msgstr ""

#: modules/adblock-finder/admin/admin.php:54
msgid "Ad blocker counter"
msgstr ""

#: modules/adblock-finder/admin/views/setting-ga.php:12
msgid "Google Analytics Tracking ID"
msgstr ""

#. translators: %s is demo GA4 ID.
#: modules/adblock-finder/admin/views/setting-ga.php:19
msgid "Enter your Google Analytics property ID (e.g. %s) above to track the page views of visitors who use an ad blocker."
msgstr ""

#. translators: %s homepage link
#: modules/ads-txt/admin/class-advanced-ads-ads-txt-admin.php:211
msgid "The ads.txt file cannot be placed because the URL contains a subdirectory. You need to make the file available at %s"
msgstr ""

#. translators: %s link of ads.txt
#: modules/ads-txt/admin/class-advanced-ads-ads-txt-admin.php:227
msgid "The file is available on %s."
msgstr ""

#: modules/ads-txt/admin/class-advanced-ads-ads-txt-admin.php:232
msgid "The file was not created."
msgstr ""

#. translators: %s link
#: modules/ads-txt/admin/class-advanced-ads-ads-txt-admin.php:237
msgid "A third-party file exists: %s"
msgstr ""

#: modules/ads-txt/admin/class-advanced-ads-ads-txt-admin.php:240
msgid "Import & Replace"
msgstr ""

#: modules/ads-txt/admin/class-advanced-ads-ads-txt-admin.php:242
msgid "Move the content of the existing ads.txt file into Advanced Ads and remove it."
msgstr ""

#. translators: %s is replaced with an error message.
#: modules/ads-txt/admin/class-advanced-ads-ads-txt-admin.php:252
#: modules/ads-txt/admin/views/setting-additional-content.php:40
msgid "An error occured: %s."
msgstr ""

#. translators: %s the line that may need to be added manually
#: modules/ads-txt/admin/class-advanced-ads-ads-txt-admin.php:269
msgid "If your site is located on a subdomain, you need to add the following line to the ads.txt file of the root domain: %s"
msgstr ""

#: modules/ads-txt/admin/class-advanced-ads-ads-txt-admin.php:391
msgid "The ads.txt is now managed with Advanced Ads."
msgstr ""

#: modules/ads-txt/admin/class-advanced-ads-ads-txt-admin.php:438
msgid "Not the main blog"
msgstr ""

#: modules/ads-txt/admin/class-advanced-ads-ads-txt-admin.php:466
msgid "Could not delete the existing ads.txt file"
msgstr ""

#: modules/ads-txt/admin/class-advanced-ads-ads-txt-admin.php:469
msgid "Could not find the existing ads.txt file"
msgstr ""

#. translators: %s: The adsense line added automically by Advanced Ads.
#: modules/ads-txt/admin/views/setting-additional-content.php:16
msgid "The following line will be added automatically because you connected your AdSense account with Advanced Ads: %s"
msgstr ""

#: modules/ads-txt/admin/views/setting-additional-content.php:30
msgid "Additional records to add to the file, one record per line. AdSense is added automatically."
msgstr ""

#: modules/ads-txt/admin/views/setting-additional-content.php:43
msgid "Check for problems"
msgstr ""

#: modules/ads-txt/admin/views/setting-create.php:34
msgid "Generate a single ads.txt file for all sites in the multisite network."
msgstr ""

#: modules/ads-txt/admin/views/setting-create.php:38
msgid "Usually, this should be enabled on the main site of the network - often the one without a subdomain or subdirectory."
msgstr ""

#. translators: %s is a URL.
#: modules/gadsense/admin/admin.php:187
msgid "Responsive AdSense ads don’t work reliably with <em>Position</em> set to left or right. Either switch the <em>Type</em> to \"normal\" or follow <a href=\"%s\" target=\"_blank\">this tutorial</a> if you want the ad to be wrapped in text."
msgstr ""

#. translators: %s is a URL.
#: modules/gadsense/admin/admin.php:197
msgid "<a href=\"%s\" target=\"_blank\">Install the free AdSense In-feed add-on</a> in order to place ads between posts."
msgstr ""

#. translators: %s is a URL.
#: modules/gadsense/admin/admin.php:246
msgid "The AdSense verification and Auto ads code should be set up in the <a href=\"%s\">AdSense settings</a>. Click on the following button to enable it now."
msgstr ""

#: modules/gadsense/admin/admin.php:249
msgid "Activate"
msgstr ""

#. translators: 1: opening anchor tag for link to adsense account  2: closing anchor tag for link to adsense account
#: modules/gadsense/admin/views/adsense-account.php:39
#: modules/gadsense/admin/views/adsense-account.php:45
msgid "Warning from your %1$sAdSense account%2$s"
msgstr ""

#. translators: 1: opening anchor tag for link to adsense account  2: closing anchor tag for link to adsense account
#: modules/gadsense/admin/views/adsense-account.php:39
#: modules/gadsense/admin/views/adsense-account.php:56
msgid "AdSense warnings"
msgstr ""

#: modules/gadsense/admin/views/adsense-account.php:58
msgid "dismiss"
msgstr ""

#. translators: %s: date and time of last check in the format set in wp_options
#: modules/gadsense/admin/views/adsense-account.php:92
msgid "last checked: %s"
msgstr ""

#: modules/gadsense/admin/views/adsense-account.php:123
#: modules/gadsense/admin/views/adsense-account.php:143
#: views/admin/widgets/aa-dashboard/next-steps/widget.php:23
msgid "Connect to AdSense"
msgstr ""

#: modules/gadsense/admin/views/adsense-account.php:126
msgid "Revoke API acccess"
msgstr ""

#: modules/gadsense/admin/views/adsense-account.php:132
msgid "Account holder name"
msgstr ""

#: modules/gadsense/admin/views/adsense-account.php:135
#: modules/gadsense/includes/class-ad-type-adsense.php:96
#: modules/gadsense/includes/types/type-adsense.php:142
msgid "The Publisher ID has an incorrect format. (must start with \"pub-\")"
msgstr ""

#: modules/gadsense/admin/views/adsense-account.php:142
msgid "Yes, I have an AdSense account"
msgstr ""

#: modules/gadsense/admin/views/adsense-account.php:144
msgid "Configure everything manually"
msgstr ""

#: modules/gadsense/admin/views/adsense-account.php:147
msgid "No, I still don't have an AdSense account"
msgstr ""

#: modules/gadsense/admin/views/adsense-account.php:148
msgid "Get a free AdSense account"
msgstr ""

#. translators: %1$s is an opening a tag, %2$s is the closing one
#: modules/gadsense/admin/views/adsense-account.php:154
#: modules/gadsense/admin/views/adsense-account.php:256
msgid "See all %1$srecommended ad networks%2$s."
msgstr ""

#: modules/gadsense/admin/views/adsense-account.php:217
msgid "How to choose specific positions for AdSense ad units"
msgstr ""

#. translators: %s: number of add-ons.
#: modules/gadsense/admin/views/adsense-account.php:226
#: modules/gadsense/includes/class-network-adsense.php:78
#: views/admin/metaboxes/ads/ad-types.php:64
msgid "Subscribe to our free email course for Google AdSense, receive our newsletter for periodic tutorials, and get %s for Advanced Ads."
msgstr ""

#. translators: %1$s is the opening link tag to our manual; %2$s is the appropriate closing link tag; %3$s is the opening link tag to our help forum; %4$s is the appropriate closing link tag
#: modules/gadsense/admin/views/adsense-account.php:238
msgid "Problems with AdSense? Check out the %1$smanual%2$s or %3$sask here%4$s."
msgstr ""

#: modules/gadsense/admin/views/adsense-account.php:270
#: modules/gadsense/admin/views/external-ads-links.php:57
msgid "Can not connect AdSense account. PHP version is too low."
msgstr ""

#: modules/gadsense/admin/views/adsense-ad-parameters.php:73
msgid "The ad details couldn't be retrieved from the ad code"
msgstr ""

#: modules/gadsense/admin/views/adsense-ad-parameters.php:74
msgid "Warning: The AdSense account from this code does not match the one set in the Advanced Ads options."
msgstr ""

#: modules/gadsense/admin/views/adsense-ad-parameters.php:82
msgid "Copy the ad code from your AdSense account, paste it into the area below and click on <em>Get details</em>."
msgstr ""

#: modules/gadsense/admin/views/adsense-ad-parameters.php:84
msgid "Get details"
msgstr ""

#: modules/gadsense/admin/views/adsense-ad-parameters.php:85
msgid "cancel"
msgstr ""

#: modules/gadsense/admin/views/adsense-ad-parameters.php:87
msgid "connect to your AdSense account"
msgstr ""

#: modules/gadsense/admin/views/adsense-ad-parameters.php:102
msgid "Ad Slot ID"
msgstr ""

#. translators: %s is the publisher ID.
#: modules/gadsense/admin/views/adsense-ad-parameters.php:108
msgid "Publisher ID: %s"
msgstr ""

#: modules/gadsense/admin/views/adsense-ad-parameters.php:112
msgid "The ad slot ID is either a number or empty and not the same as the publisher ID."
msgstr ""

#. translators: %s the setting page link
#: modules/gadsense/admin/views/adsense-ad-parameters.php:124
msgid "Please <a href=\"%s\" target=\"_blank\">change it here</a>."
msgstr ""

#: modules/gadsense/admin/views/adsense-ad-parameters.php:130
msgid "Fixed Size"
msgstr ""

#: modules/gadsense/admin/views/adsense-ad-parameters.php:132
#: modules/gadsense/includes/class-ad-type-adsense.php:32
msgid "Multiplex"
msgstr ""

#: modules/gadsense/admin/views/adsense-ad-parameters.php:134
#: modules/gadsense/includes/class-ad-type-adsense.php:33
msgid "Link ads"
msgstr ""

#: modules/gadsense/admin/views/adsense-ad-parameters.php:137
#: modules/gadsense/includes/class-ad-type-adsense.php:34
msgid "Link ads (Responsive)"
msgstr ""

#: modules/gadsense/admin/views/adsense-ad-parameters.php:139
#: modules/gadsense/includes/class-ad-type-adsense.php:35
msgid "In-article"
msgstr ""

#: modules/gadsense/admin/views/adsense-ad-parameters.php:140
#: modules/gadsense/includes/class-ad-type-adsense.php:36
msgid "In-feed"
msgstr ""

#: modules/gadsense/admin/views/adsense-ad-parameters.php:145
msgid "Google AdSense deprecated Link Units. Please choose another type."
msgstr ""

#: modules/gadsense/admin/views/adsense-ad-parameters.php:154
msgid "Resizing"
msgstr ""

#: modules/gadsense/admin/views/adsense-ad-parameters.php:167
msgid "Layout"
msgstr ""

#: modules/gadsense/admin/views/adsense-ad-parameters.php:176
msgid "Layout-Key"
msgstr ""

#: modules/gadsense/admin/views/adsense-ad-parameters.php:185
msgid "Clearfix"
msgstr ""

#: modules/gadsense/admin/views/adsense-ad-parameters.php:191
msgid "Enable this if responsive ads cover something on your site."
msgstr ""

#: modules/gadsense/admin/views/auto-ads-video.php:4
msgid "How to enable Auto ads in 30 seconds (video tutorial)"
msgstr ""

#. translators: 1: the plugin name that is managing the Auto ads code.
#: modules/gadsense/admin/views/borlabs-cookie-auto-ads-warning.php:4
msgid "Advanced Ads detected that <strong>%s</strong> is managing the Auto ads code and will therefore not add it."
msgstr ""

#: modules/gadsense/admin/views/connect-adsense.php:53
msgid "Processing authorization"
msgstr ""

#: modules/gadsense/admin/views/connect-adsense.php:60
msgid "Cannot access your account information."
msgstr ""

#: modules/gadsense/admin/views/connect-adsense.php:66
msgid "Please select an account"
msgstr ""

#: modules/gadsense/admin/views/connect-adsense.php:71
msgid "Use this account"
msgstr ""

#. translators: 1: The name of an ad network.
#: modules/gadsense/admin/views/external-ads-links.php:18
msgid "Insert new %1$s code"
msgstr ""

#: modules/gadsense/admin/views/external-ads-links.php:28
msgid "Get ad code from your linked account"
msgstr ""

#. translators: 1: The name of an ad network.
#: modules/gadsense/admin/views/external-ads-links.php:36
msgid "Set up %1$s code manually"
msgstr ""

#. translators: 1: The name of an ad network.
#: modules/gadsense/admin/views/external-ads-links.php:48
msgid "Connect to %1$s"
msgstr ""

#: modules/gadsense/admin/views/external-ads-list.php:29
msgid "Hide archived ads"
msgstr ""

#: modules/gadsense/admin/views/external-ads-list.php:29
msgid "Show archived ads"
msgstr ""

#: modules/gadsense/admin/views/external-ads-list.php:30
#: modules/gadsense/admin/views/external-ads-list.php:52
msgid "Update the ad units list"
msgstr ""

#: modules/gadsense/admin/views/external-ads-list.php:40
msgctxt "AdSense ad"
msgid "Slot ID"
msgstr ""

#: modules/gadsense/admin/views/external-ads-list.php:42
msgctxt "AdSense ad"
msgid "Type"
msgstr ""

#: modules/gadsense/admin/views/external-ads-list.php:50
msgid "No ad units found"
msgstr ""

#: modules/gadsense/admin/views/external-ads-list.php:73
msgid "Archived"
msgstr ""

#: modules/gadsense/admin/views/external-ads-list.php:82
msgid "Get the code for this ad"
msgstr ""

#: modules/gadsense/admin/views/external-ads-list.php:83
msgid "Load"
msgstr ""

#: modules/gadsense/admin/views/external-ads-list.php:87
msgid "Ad can't be imported, click for details"
msgstr ""

#: modules/gadsense/admin/views/external-ads-list.php:99
msgid "Unrecognized ad code"
msgstr ""

#: modules/gadsense/admin/views/external-ads-list.php:105
msgid "This ad type can currently not be imported from AdSense."
msgstr ""

#: modules/gadsense/admin/views/external-ads-list.php:108
msgid "You can proceed with one of the following solutions"
msgstr ""

#. Translators: 1: opening tag for AdSense account link 2: opening tag for a link to insert ad code 3: closing a tag
#: modules/gadsense/admin/views/external-ads-list.php:114
msgid "%1$sCopy the code from your AdSense account%3$s and %2$sinsert a new AdSense code here%3$s."
msgstr ""

#. translators: 1: opening tag for a link to create an ad manually 2: closing tag
#: modules/gadsense/admin/views/external-ads-list.php:123
msgid "%1$sCreate an AdSense code manually%2$s: Select the <em>Normal</em> or <em>Responsive</em> type and the size."
msgstr ""

#: modules/gadsense/admin/views/external-ads-list.php:135
msgid "Choose a different ad from your AdSense account above."
msgstr ""

#: modules/gadsense/admin/views/settings/amp-auto-ads.php:13
msgid "Enable AMP Auto ads"
msgstr ""

#. translators: AdSense ID.
#: modules/gadsense/includes/adsense-report-api.php:148
msgid "Error while retrieving report for \"%s\"."
msgstr ""

#. translators: AdSense ID.
#: modules/gadsense/includes/adsense-report-api.php:159
msgid "Invalid response while retrieving report for \"%s\"."
msgstr ""

#: modules/gadsense/includes/class-ad-type-adsense.php:30
#: modules/gadsense/includes/class-ad-type-adsense.php:48
msgid "Normal"
msgstr ""

#: modules/gadsense/includes/class-ad-type-adsense.php:137
#: modules/gadsense/includes/types/type-adsense.php:183
msgid "Your AdSense Publisher ID is missing."
msgstr ""

#: modules/gadsense/includes/class-adsense-report.php:99
msgid "No valid tokens"
msgstr ""

#: modules/gadsense/includes/class-gadsense-data.php:54
msgid "Auto"
msgstr ""

#. translators: %s: ad unit ID.
#: modules/gadsense/includes/class-mapi.php:347
msgid "Error while retrieving ad code for \"%s\"."
msgstr ""

#. translators: %s: ad unit ID.
#: modules/gadsense/includes/class-mapi.php:374
msgid "Invalid response while retrieving ad code for \"%s\"."
msgstr ""

#. translators: %s is the publisher ID.
#: modules/gadsense/includes/class-mapi.php:435
#: modules/gadsense/includes/class-mapi.php:532
msgid "Error while retrieving ad unit list for \"%s\"."
msgstr ""

#. translators: %1$s is the AdSense publisher ID; %2$s a starting a tag to the AdSense ad unit list and %3$s the closing link.
#: modules/gadsense/includes/class-mapi.php:446
msgid "The account \"%1$s\" does not seem to have any ad units. Please create some %2$shere%3$s."
msgstr ""

#. translators: %s is the publisher ID.
#: modules/gadsense/includes/class-mapi.php:459
msgid "Invalid response while retrieving ad unit list for \"%s\"."
msgstr ""

#: modules/gadsense/includes/class-mapi.php:469
msgctxt "Reason of the API call error"
msgid "Reason:"
msgstr ""

#: modules/gadsense/includes/class-mapi.php:471
msgctxt "Error message from Google"
msgid "Message:"
msgstr ""

#: modules/gadsense/includes/class-mapi.php:593
msgid "It seems that some changes have been made in the Advanced Ads settings. Please refresh this page."
msgstr ""

#. translators: %s account id
#: modules/gadsense/includes/class-mapi.php:601
msgid "Advanced Ads does not have access to your account (<code>%s</code>) anymore."
msgstr ""

#. translators: %s account id
#: modules/gadsense/includes/class-mapi.php:646
msgid "error while renewing access token for \"%s\""
msgstr ""

#. translators: %s AdSense account ID
#: modules/gadsense/includes/class-mapi.php:673
msgid "invalid response received while renewing access token for \"%s\""
msgstr ""

#: modules/gadsense/includes/class-mapi.php:675
msgid "You could try to connect again under Advanced Ads > Settings > AdSense."
msgstr ""

#: modules/gadsense/includes/class-mapi.php:742
msgid "This ad code is from a different AdSense Account"
msgstr ""

#: modules/gadsense/includes/class-mapi.php:980
msgid "Invalid response body while retrieving account alerts"
msgstr ""

#: modules/gadsense/includes/class-mapi.php:988
msgid "error while retrieving account alerts"
msgstr ""

#: modules/gadsense/includes/class-mapi.php:1113
msgid "No token provided. Token data needed to get account details."
msgstr ""

#: modules/gadsense/includes/class-mapi.php:1135
#: modules/gadsense/includes/class-mapi.php:1139
msgid "No AdSense account data found."
msgstr ""

#: modules/gadsense/includes/class-mapi.php:1409
#: modules/gadsense/includes/class-mapi.php:1427
#: modules/gadsense/includes/class-network-adsense.php:525
msgctxt "AdSense ad type"
msgid "Multiplex"
msgstr ""

#: modules/gadsense/includes/class-mapi.php:1412
#: modules/gadsense/includes/class-mapi.php:1430
#: modules/gadsense/includes/class-network-adsense.php:526
msgctxt "AdSense ad type"
msgid "In-article"
msgstr ""

#: modules/gadsense/includes/class-mapi.php:1414
#: modules/gadsense/includes/class-mapi.php:1433
#: modules/gadsense/includes/class-network-adsense.php:527
msgctxt "AdSense ad type"
msgid "In-feed"
msgstr ""

#: modules/gadsense/includes/class-mapi.php:1421
#: modules/gadsense/includes/class-network-adsense.php:528
msgctxt "AdSense ad type"
msgid "Display"
msgstr ""

#: modules/gadsense/includes/class-mapi.php:1424
#: modules/gadsense/includes/class-network-adsense.php:529
msgctxt "AdSense ad type"
msgid "Link"
msgstr ""

#. translators: %s admin setting page link
#: modules/gadsense/includes/class-mapi.php:1646
msgid "There are one or more warnings about the currently linked AdSense account. You can view them <a href=\"%s\">here</a>"
msgstr ""

#. translators: 1: A link to the settings page 2: The name of an ad network
#: modules/gadsense/includes/class-mapi.php:1754
msgid "Please try to <a href=\"%1$s\" target=\"_blank\">reconnect to your %2$s account</a>."
msgstr ""

#: modules/gadsense/includes/class-network-adsense.php:96
msgid "AdSense account"
msgstr ""

#: modules/gadsense/includes/class-network-adsense.php:105
#: modules/gadsense/includes/class-network-adsense.php:116
msgid "Auto ads"
msgstr ""

#: modules/gadsense/includes/class-network-adsense.php:116
msgid "Disable top anchor ad"
msgstr ""

#: modules/gadsense/includes/class-network-adsense.php:126
msgid "Disable stats"
msgstr ""

#: modules/gadsense/includes/class-network-adsense.php:135
msgid "Disable violation warnings"
msgstr ""

#: modules/gadsense/includes/class-network-adsense.php:144
msgid "Show AdSense Earnings"
msgstr ""

#: modules/gadsense/includes/class-network-adsense.php:152
msgid "Transparent background"
msgstr ""

#: modules/gadsense/includes/class-network-adsense.php:160
msgid "Full width responsive ads on mobile"
msgstr ""

#: modules/gadsense/includes/class-network-adsense.php:197
msgid "Enable this box if you don’t want Google Auto ads to place anchor ads at the top of your page."
msgstr ""

#: modules/gadsense/includes/class-network-adsense.php:212
msgid "Enable this option to stop loading stats from AdSense into your WordPress backend."
msgstr ""

#: modules/gadsense/includes/class-network-adsense.php:226
msgid "Show Earnings widget on the WordPress dashboard."
msgstr ""

#: modules/gadsense/includes/class-network-adsense.php:242
msgid "Insert the AdSense header code to enable Auto ads and verify your website."
msgstr ""

#: modules/gadsense/includes/class-network-adsense.php:245
msgid "Display Auto ads only on specific pages"
msgstr ""

#: modules/gadsense/includes/class-network-adsense.php:246
msgid "Why are ads appearing in random positions?"
msgstr ""

#. translators: this is the text for a link to a sub-page in an AdSense account
#: modules/gadsense/includes/class-network-adsense.php:253
msgid "Adjust Auto ads options"
msgstr ""

#: modules/gadsense/includes/class-network-adsense.php:298
msgid "Disable warnings about potential violations of the AdSense terms."
msgstr ""

#. translators: %s is a URL.
#: modules/gadsense/includes/class-network-adsense.php:304
msgid "Our <a href=\"%s\" target=\"_blank\">Ad Health</a> feature monitors if AdSense is implemented correctly on your site. It also considers ads not managed with Advanced Ads. Enable this option to remove these checks"
msgstr ""

#: modules/gadsense/includes/class-network-adsense.php:329
msgid "Enable this option in case your theme adds an unfortunate background color to AdSense ads."
msgstr ""

#: modules/gadsense/includes/class-network-adsense.php:343
msgid "enable"
msgstr ""

#: modules/gadsense/includes/class-network-adsense.php:344
msgid "disable"
msgstr ""

#. translators: %s is a URL.
#: modules/gadsense/includes/class-network-adsense.php:351
msgid "Whether your responsive ad unit may expand to <a href='%s' target='blank'>use the full width</a> of your visitor's mobile device screen"
msgstr ""

#: modules/gadsense/includes/types/type-adsense.php:47
msgid "AdSense ad"
msgstr ""

#: modules/gadsense/includes/types/type-adsense.php:56
msgid "Use ads from your Google AdSense account"
msgstr ""

#: modules/gutenberg/includes/class-gutenberg.php:138
msgid "Width"
msgstr ""

#: modules/gutenberg/includes/class-gutenberg.php:139
msgid "Height"
msgstr ""

#: modules/gutenberg/includes/class-gutenberg.php:141
msgid "Alignment"
msgstr ""

#: modules/gutenberg/includes/class-gutenberg.php:158
msgid "Theme's default"
msgstr ""

#: modules/gutenberg/includes/class-gutenberg.php:162
msgid "Float left"
msgstr ""

#: modules/gutenberg/includes/class-gutenberg.php:166
msgid "Float right"
msgstr ""

#: modules/gutenberg/includes/class-gutenberg.php:170
msgid "Block left"
msgstr ""

#: modules/gutenberg/includes/class-gutenberg.php:174
msgid "Block right"
msgstr ""

#: modules/gutenberg/includes/class-gutenberg.php:178
msgid "Centered"
msgstr ""

#: modules/one-click/admin/class-admin.php:56
msgid "Continue"
msgstr ""

#: modules/one-click/admin/class-admin.php:57
msgid "Retry"
msgstr ""

#: modules/one-click/admin/class-admin.php:62
#: modules/one-click/admin/class-admin.php:68
msgid "MonetizeMore & PubGuru Integration"
msgstr ""

#: modules/one-click/admin/class-admin.php:63
msgid "Enables MonetizeMore users to link their settings with the PubGuru insights & analytics dashboard."
msgstr ""

#: modules/one-click/admin/class-admin.php:64
msgid "Connect now"
msgstr ""

#: modules/one-click/admin/class-admin.php:65
msgid "Disconnect now"
msgstr ""

#: modules/one-click/admin/class-admin.php:69
msgid "Visit the MonetizeMore website to learn about PubGuru"
msgstr ""

#: modules/one-click/admin/class-admin.php:73
msgid "Onboarding Step 1 of 3, Consent and Privacy Policy"
msgstr ""

#. translators: %s link to privacy policy
#: modules/one-click/admin/class-admin.php:77
msgid "This form is designed exclusively for MonetizeMore customers who wish to integrate Advanced Ads with their PubGuru Dashboard. In alignment with our <a href=\"%s\">Privacy Policy</a>, no information other than your domain name is exchanged, and Advanced Ads does not engage in any tracking activities."
msgstr ""

#: modules/one-click/admin/class-admin.php:81
msgid "I agree to share my domain name to facilitate the connection with my PubGuru account."
msgstr ""

#: modules/one-click/admin/class-admin.php:82
msgid "Connect with PubGuru"
msgstr ""

#: modules/one-click/admin/class-admin.php:85
msgid "Onboarding Step 2 of 3, Connecting to PubGuru"
msgstr ""

#: modules/one-click/admin/class-admin.php:86
msgid "Fetching your domain information from PubGuru, please wait..."
msgstr ""

#: modules/one-click/admin/class-admin.php:87
msgid "The domain &ldquo;{0}&rdquo; is not registered with PubGuru"
msgstr ""

#. translators: %1$s is contact link, %2$s is email link
#: modules/one-click/admin/class-admin.php:90
msgid "If you are on a domain &ldquo;unknow&rdquo; to PubGuru, e.g., a staging site, please indicate the domain that you registered with PubGuru. If you need assistance, please <a href=\"%1$s\">click here to contact PubGuru support</a> or <a href=\"%2$s\">send an <NAME_EMAIL></a>."
msgstr ""

#: modules/one-click/admin/class-admin.php:94
msgid "Registered domain"
msgstr ""

#: modules/one-click/admin/class-admin.php:95
msgid "The onboarding process has encountered an error: &ldquo;{0}&rdquo;"
msgstr ""

#. translators: %1$s is contact link, %2$s is email link
#: modules/one-click/admin/class-admin.php:98
msgid "Please wait a few minutes and try again. If you need assistance, please <a href=\"%1$s\">click here to contact PubGuru support</a> or <a href=\"%2$s\">send an <NAME_EMAIL></a>."
msgstr ""

#: modules/one-click/admin/class-admin.php:104
msgid "Onboarding Step 3 of 3, Test and Finalize Ad Unit Import"
msgstr ""

#: modules/one-click/admin/class-admin.php:105
msgid "Your domain &ldquo;{0}&rdquo; is connected to PubGuru."
msgstr ""

#: modules/one-click/admin/class-admin.php:106
#: modules/one-click/admin/class-admin.php:160
msgid "Import PubGuru Ad Units"
msgstr ""

#: modules/one-click/admin/class-admin.php:111
msgid "This step is entirely optional. Your PubGuru configuration shows the following available ad units"
msgstr ""

#: modules/one-click/admin/class-admin.php:113
msgid "3 In-Content Ads"
msgstr ""

#: modules/one-click/admin/class-admin.php:114
msgid "1 Leaderboard Ad"
msgstr ""

#: modules/one-click/admin/class-admin.php:116
msgid "You will be able to preview the ad units injections on a test page before and there is a rollback option after the import."
msgstr ""

#: modules/one-click/admin/class-admin.php:120
msgid "You may preview or change the test page for the ad units&rsquo; injections, or finalize the PubGuru Ad Unit import."
msgstr ""

#. translators: %s rollback page link
#: modules/one-click/admin/class-admin.php:123
msgid "You have successfully imported your PubGuru Ad Units. If necessary, use the <a href=\"%s\">Rollback Tool</a> to revert your ad setup to a previous state."
msgstr ""

#: modules/one-click/admin/class-admin.php:128
msgid "General Settings"
msgstr ""

#. translators: %1$s is contact link, %2$s is email link
#: modules/one-click/admin/class-admin.php:131
msgid "If you need assistance, please <a href=\"%1$s\">click here to contact PubGuru support</a> or <a href=\"%2$s\">send an <NAME_EMAIL></a>."
msgstr ""

#: modules/one-click/admin/class-admin.php:135
msgid "Activate PubGuru Header Bidding"
msgstr ""

#: modules/one-click/admin/class-admin.php:136
msgid "Activate Tag Conversion"
msgstr ""

#: modules/one-click/admin/class-admin.php:137
msgid "Activate Traffic Cop Invalid Traffic Protection"
msgstr ""

#: modules/one-click/admin/class-admin.php:138
msgid "7 Days Trial"
msgstr ""

#. translators: %s is link to PubGuru
#: modules/one-click/admin/class-admin.php:141
msgid "Redirect ads.txt calls to the <a href=\"%s\" target=\"_blank\" rel=\"noreferrer\">PubGuru platform</a>"
msgstr ""

#: modules/one-click/admin/class-admin.php:144
msgid "Move the PubGuru Header Bidding script to the footer. <span class=\"muted\">Keep this option disabled to maximize revenue. Only enable it if PageSpeed is your priority.</span>"
msgstr ""

#: modules/one-click/admin/class-admin.php:145
msgid "(Only enabled on Preview Page)"
msgstr ""

#: modules/one-click/admin/class-admin.php:161
msgid "Close and update preview"
msgstr ""

#: modules/one-click/admin/class-admin.php:162
msgid "Finalize ad unit import"
msgstr ""

#: modules/one-click/admin/class-admin.php:163
msgid "Update preview"
msgstr ""

#: modules/one-click/admin/class-admin.php:164
msgid "Go to the preview"
msgstr ""

#: modules/one-click/admin/class-admin.php:165
msgid "Import method"
msgstr ""

#: modules/one-click/admin/class-admin.php:166
msgid "Preview ad units on specific page"
msgstr ""

#: modules/one-click/admin/class-admin.php:167
msgid "Finalize ad units import"
msgstr ""

#: modules/one-click/admin/class-admin.php:172
msgid "Your existing ads and placements will be set to &lsquo;Draft&rsquo; mode"
msgstr ""

#: modules/one-click/admin/class-admin.php:173
msgid "Your PubGuru ad units will be imported as suitable ads and placements, and published right away"
msgstr ""

#: modules/one-click/admin/class-admin.php:175
msgid "You can manually republish specific ads and placements or fully rollback at any time."
msgstr ""

#: modules/one-click/admin/class-ajax.php:65
msgid "Refreshing PubGuru Ads"
msgstr ""

#: modules/one-click/admin/class-ajax.php:98
msgid "We have successfully migrated your MonetizeMore PubGuru Ad Units to your WordPress site. The existing placements and ads have been paused."
msgstr ""

#: modules/one-click/admin/class-ajax.php:116
msgid "PubGuru successfully disconnected."
msgstr ""

#. translators: 1 is the opening link to the Advanced Ads website, 2 the closing link
#: modules/one-click/admin/class-ajax.php:152
msgid "The backup of your ads.txt file has failed. Please ensure that a manual backup is created You can find detailed instructions on how to manually back up your ads.txt file in the manual. %1$sManual%2$s"
msgstr ""

#: modules/one-click/admin/class-ajax.php:162
msgid "File successfully backed up."
msgstr ""

#: modules/one-click/admin/class-ajax.php:242
msgid "An error has occurred please try again."
msgstr ""

#: modules/one-click/modules/adstxt/class-detector.php:93
#: modules/one-click/modules/class-workflow.php:118
msgid "File alert!"
msgstr ""

#: modules/one-click/modules/adstxt/class-detector.php:93
#: modules/one-click/modules/class-workflow.php:120
msgid "Physical ads.txt found. In order to use PubGuru service you need to delete it or back it up."
msgstr ""

#: modules/one-click/modules/adstxt/class-detector.php:96
#: modules/one-click/modules/adstxt/class-detector.php:97
#: modules/one-click/modules/class-workflow.php:123
msgid "Backup the File"
msgstr ""

#: modules/one-click/modules/adstxt/class-detector.php:96
msgid "Backing Up"
msgstr ""

#: modules/one-click/modules/adstxt/class-detector.php:96
msgid "Backed Up"
msgstr ""

#: modules/pef/class-module.php:184
msgid "FROM THE ADVANCED ADS LABS:"
msgstr ""

#: modules/pef/class-module.php:185
msgid "The Amazon Integration"
msgstr ""

#: modules/pef/class-module.php:187
msgid "Our latest product concept puts Amazon affiliate marketing at your fingertips—right within Advanced Ads. It offers features like direct product import via Amazon API, multiple product display formats, and efficient ad tracking. We aim to create a one-stop solution for featuring Amazon products on your site without resorting to expensive third-party plugins."
msgstr ""

#: modules/pef/class-module.php:188
msgid "Are you interested in this product concept?"
msgstr ""

#: modules/pef/class-module.php:189
msgid "Yes, I want to know more!"
msgstr ""

#: modules/privacy/admin/admin.php:54
msgid "Privacy"
msgstr ""

#: modules/privacy/admin/admin.php:91
msgid "Enable Privacy module"
msgstr ""

#: modules/privacy/admin/admin.php:121
msgid "Show all ads even without consent"
msgstr ""

#: modules/privacy/admin/admin.php:124
msgid "Cookie"
msgstr ""

#: modules/privacy/admin/admin.php:128
msgid "IAB Transparency and Consent Framework (TCF) integration"
msgstr ""

#: modules/privacy/admin/views/setting-ad-ignore-consent.php:10
msgid "privacy"
msgstr ""

#. Translators: 1: a tag with link to general privacy settings, 2: closing a tag
#: modules/privacy/admin/views/setting-ad-ignore-consent.php:17
msgid "Ignore %1$sgeneral Privacy settings%2$s and display the ad even without consent."
msgstr ""

#: modules/privacy/admin/views/setting-general.php:25
msgid "Show ads only to users who give their permission to cookies and ads."
msgstr ""

#: modules/privacy/admin/views/setting-general.php:29
msgid "Consent method"
msgstr ""

#: modules/privacy/admin/views/setting-general.php:52
msgid "Cookie name"
msgstr ""

#: modules/privacy/admin/views/setting-general.php:57
msgid "value"
msgstr ""

#: modules/privacy/admin/views/setting-general.php:58
msgid "Value"
msgstr ""

#: modules/privacy/admin/views/setting-general.php:62
msgid "Show non-personalized AdSense ads until consent is given."
msgstr ""

#: modules/privacy/admin/views/setting-general.php:73
msgid "It seems that a caching plugin is activated."
msgstr ""

#: modules/privacy/admin/views/setting-general.php:76
msgid "Your users’ consent might get cached and show ads to users who didn’t give their consent yet. "
msgstr ""

#: modules/privacy/admin/views/setting-general.php:79
msgid "Cache-busting in Advanced Ads Pro solves that."
msgstr ""

#: modules/privacy/admin/views/setting-general.php:88
msgid "Ads are loaded after the user gives their consent and reloads the page."
msgstr ""

#. Translators: 1: opening link tag with link to Advanced Ads Pro 2: closing link tag
#: modules/privacy/admin/views/setting-general.php:92
msgid "Install %1$sAdvanced Ads Pro%2$s to reload the ads instantly without an additional page request."
msgstr ""

#: modules/privacy/admin/views/setting-general.php:105
msgid "The selected tracking method is not compatible with the TCF 2.0 integration."
msgstr ""

#: public/class-advanced-ads.php:177
msgid "Advanced Ads Error following:"
msgstr ""

#. translators: %s is an error message generated by the plugin.
#: public/class-advanced-ads.php:181
msgid "Advanced Ads Error: %s"
msgstr ""

#: public/class-advanced-ads.php:576
msgctxt "label above ads"
msgid "Advertisements"
msgstr ""

#: public/views/ad-debug.php:22
msgid "Ad debug output"
msgstr ""

#: public/views/ad-debug.php:26
msgid "Find solutions in the manual"
msgstr ""

#: views/admin/ads/info-bottom.php:16
msgctxt "wizard navigation"
msgid "previous"
msgstr ""

#: views/admin/ads/info-bottom.php:18
msgctxt "wizard navigation"
msgid "save"
msgstr ""

#: views/admin/ads/info-bottom.php:21
msgctxt "wizard navigation"
msgid "next"
msgstr ""

#: views/admin/ads/info-bottom.php:22
#: views/admin/ads/info-top.php:33
msgid "Stop Wizard and show all options"
msgstr ""

#: views/admin/ads/info-top.php:14
msgid "Start Wizard"
msgstr ""

#: views/admin/ads/info-top.php:17
msgid "Stop Wizard"
msgstr ""

#: views/admin/ads/submitbox-meta.php:16
#: views/admin/quick-edit.php:24
msgid "Set expiry date"
msgstr ""

#. translators: %1$s is the month number, %2$s is the month shortname.
#: views/admin/ads/submitbox-meta.php:27
msgctxt "1: month number (01, 02, etc.), 2: month abbreviation"
msgid "%1$s-%2$s"
msgstr ""

#. translators: %1$s month, %2$s day, %3$s year, %4$s hour, %5$s minute.
#: views/admin/ads/submitbox-meta.php:45
msgctxt "order of expiry date fields 1: month, 2: day, 3: year, 4: hour, 5: minute"
msgid "%1$s %2$s, %3$s @ %4$s %5$s"
msgstr ""

#: views/admin/bulk-edit.php:21
#: views/admin/quick-edit.php:20
msgid "Debug mode"
msgstr ""

#: views/admin/bulk-edit.php:23
#: views/admin/bulk-edit.php:32
#: views/admin/bulk-edit.php:44
#: views/admin/bulk-edit.php:67
#: views/admin/page-bulk-edit.php:21
#: views/admin/page-bulk-edit.php:34
#: views/admin/placements/bulk-edit.php:17
msgid "No Change"
msgstr ""

#: views/admin/bulk-edit.php:24
#: views/admin/bulk-edit.php:68
#: views/admin/placements/bulk-edit.php:19
#: views/admin/tables/ads/column-debug.php:10
#: views/admin/tables/ads/column-displayonce.php:10
#: views/admin/tables/ads/column-privacyignore.php:10
#: views/admin/tables/ads/filters.php:86
#: views/admin/tables/ads/filters.php:105
#: views/admin/tables/ads/filters.php:114
msgid "Enabled"
msgstr ""

#: views/admin/bulk-edit.php:25
#: views/admin/bulk-edit.php:69
#: views/admin/placements/bulk-edit.php:20
#: views/admin/tables/ads/filters.php:87
#: views/admin/tables/ads/filters.php:106
#: views/admin/tables/ads/filters.php:115
msgid "Disabled"
msgstr ""

#: views/admin/bulk-edit.php:30
msgid "Expiry date"
msgstr ""

#: views/admin/bulk-edit.php:33
msgid "Set"
msgstr ""

#: views/admin/bulk-edit.php:34
msgid "Unset"
msgstr ""

#. Translators: %s is the URL to the settings page.
#: views/admin/bulk-edit.php:51
#: views/admin/metaboxes/ads/ad-layout.php:62
#: views/admin/quick-edit.php:46
msgid "Enable the Ad Label %1$s in the settings%2$s."
msgstr ""

#: views/admin/bulk-edit.php:65
#: views/admin/quick-edit.php:32
msgid "Ignore privacy settings"
msgstr ""

#: views/admin/feedback-disable.php:16
msgid "Why did you decide to disable Advanced Ads?"
msgstr ""

#: views/admin/feedback-disable.php:21
msgid "I have a problem, a question or need help."
msgstr ""

#: views/admin/feedback-disable.php:25
msgid "Please let us know how we can help"
msgstr ""

#: views/admin/feedback-disable.php:30
msgid "Send me free help to "
msgstr ""

#: views/admin/feedback-disable.php:38
msgid "Ads are not showing up"
msgstr ""

#: views/admin/feedback-disable.php:43
msgid "It is only temporary"
msgstr ""

#: views/admin/feedback-disable.php:48
msgid "I miss a feature"
msgstr ""

#: views/admin/feedback-disable.php:52
msgid "Which one?"
msgstr ""

#: views/admin/feedback-disable.php:56
msgid "I stopped using ads on my site."
msgstr ""

#: views/admin/feedback-disable.php:61
msgid "I switched to another plugin"
msgstr ""

#: views/admin/feedback-disable.php:68
msgid "Send feedback & deactivate"
msgstr ""

#: views/admin/feedback-disable.php:69
msgid "Send feedback"
msgstr ""

#: views/admin/feedback-disable.php:71
msgid "Only Deactivate"
msgstr ""

#: views/admin/feedback-disable.php:75
msgid "Thanks for submitting your feedback. I will reply within 24 hours on working days."
msgstr ""

#. translators: %s is the title of the website.
#: views/admin/feedback-disable.php:81
msgid "All the best to you and <em>%s</em>."
msgstr ""

#: views/admin/feedback-disable.php:87
msgid "Disabling the plugin now…"
msgstr ""

#: views/admin/header.php:41
msgid "Reset"
msgstr ""

#: views/admin/header.php:46
#: views/admin/screen-options.php:19
msgid "Filters"
msgstr ""

#: views/admin/header.php:50
msgid "Screen Options"
msgstr ""

#: views/admin/header.php:54
#: views/admin/ui/header.php:33
msgid "See all Add-ons"
msgstr ""

#: views/admin/metaboxes/ads/ad-gadsense-dashboard.php:19
msgid "There is an error in your AdSense setup."
msgstr ""

#: views/admin/metaboxes/ads/ad-info-after-textarea.php:21
msgid "The code of this ad might not work properly with the Content placement."
msgstr ""

#. translators: %s is a URL.
#: views/admin/metaboxes/ads/ad-info-after-textarea.php:28
msgid "Reach out to <a href=\"%s\">support</a> to get help."
msgstr ""

#: views/admin/metaboxes/ads/ad-layout.php:24
msgid "container ID"
msgstr ""

#: views/admin/metaboxes/ads/ad-layout.php:31
msgid "Specify the id of the ad container. Leave blank for random or no id."
msgstr ""

#: views/admin/metaboxes/ads/ad-layout.php:32
#: views/admin/metaboxes/ads/ad-layout.php:35
msgid "An id-like string with only letters in lower case, numbers, and hyphens."
msgstr ""

#: views/admin/metaboxes/ads/ad-layout.php:41
msgid "container classes"
msgstr ""

#: views/admin/metaboxes/ads/ad-layout.php:46
msgid "Specify one or more classes for the container. Separate multiple classes with a space"
msgstr ""

#: views/admin/metaboxes/ads/ad-layout.php:74
msgid "Enable debug mode"
msgstr ""

#: views/admin/metaboxes/ads/ad-layout.php:85
msgid "Display only once"
msgstr ""

#: views/admin/metaboxes/ads/ad-layout.php:87
msgid "Display the ad only once per page"
msgstr ""

#: views/admin/metaboxes/ads/ad-layout.php:94
msgid "Custom Code"
msgstr ""

#: views/admin/metaboxes/ads/ad-layout.php:97
msgid "Place your own code below the ad"
msgstr ""

#: views/admin/metaboxes/ads/ad-parameters-size.php:20
msgid "size"
msgstr ""

#: views/admin/metaboxes/ads/ad-parameters-size.php:23
msgid "width"
msgstr ""

#: views/admin/metaboxes/ads/ad-parameters-size.php:26
msgid "height"
msgstr ""

#: views/admin/metaboxes/ads/ad-parameters-size.php:29
msgid "reserve this space"
msgstr ""

#: views/admin/metaboxes/ads/ad-targeting.php:22
msgid "Limit the ad to pages that match the following conditions. Don‘t do anything here if the ad should appear everywhere you embed it."
msgstr ""

#: views/admin/metaboxes/ads/ad-targeting.php:28
msgid "Click on the button below if the ad should NOT show up on all pages when included automatically."
msgstr ""

#: views/admin/metaboxes/ads/ad-targeting.php:29
msgid "Hide the ad on some pages"
msgstr ""

#: views/admin/metaboxes/ads/ad-targeting.php:41
msgid "Target the ad to specific user groups that match the following conditions. Don‘t do anything here if all users should see the ad."
msgstr ""

#: views/admin/metaboxes/ads/ad-targeting.php:47
msgid "Click on the button below if the ad should NOT be visible to all visitors"
msgstr ""

#: views/admin/metaboxes/ads/ad-targeting.php:48
msgid "Hide the ad from some users"
msgstr ""

#: views/admin/metaboxes/ads/ad-types.php:19
msgid "No ad types defined"
msgstr ""

#: views/admin/metaboxes/ads/ad-usage-notes.php:14
msgid "notes"
msgstr ""

#: views/admin/metaboxes/ads/ad-usage-notes.php:16
msgid "click to change"
msgstr ""

#: views/admin/metaboxes/ads/ad-usage-notes.php:21
msgid "Click to add notes"
msgstr ""

#: views/admin/metaboxes/ads/ad-usage-shortcodes.php:13
#: views/admin/tables/placements/column-usage.php:16
msgid "Shortcode"
msgstr ""

#: views/admin/metaboxes/ads/ad-usage-shortcodes.php:19
#: views/admin/tables/placements/column-usage.php:24
msgid "Template (PHP)"
msgstr ""

#. translators: 1: is an opening a tag, 2: is a closing a tag
#: views/admin/metaboxes/ads/ad-usage-shortcodes.php:25
msgid "Find more display options in the %1$smanual%2$s."
msgstr ""

#: views/admin/metaboxes/ads/post-ad-settings.php:17
msgid "How to disable ads on specific pages"
msgstr ""

#: views/admin/metaboxes/ads/post-ad-settings.php:22
#: views/admin/page-quick-edit.php:21
msgid "Disable ads on this page"
msgstr ""

#: views/admin/page-bulk-edit.php:23
#: views/admin/page-bulk-edit.php:36
msgid "Allow"
msgstr ""

#: views/admin/page-bulk-edit.php:30
msgid "Disable injection into the content"
msgstr ""

#: views/admin/page-quick-edit.php:21
msgid "Disable ads on this post"
msgstr ""

#: views/admin/page-quick-edit.php:29
msgid "Disable automatic ad injection into the content"
msgstr ""

#: views/admin/placements/bulk-edit.php:15
msgid "Ad Label"
msgstr ""

#: views/admin/placements/bulk-edit.php:18
msgid "Default"
msgstr ""

#: views/admin/placements/edit-modal/fields/item.php:31
#: views/admin/tables/placements/column-ad-group.php:31
msgid "Edit item"
msgstr ""

#: views/admin/placements/edit-modal/fields/name.php:16
msgid "Modifying the placement name will result in a change to the placement slug as well. Remember to update any customized CSS accordingly."
msgstr ""

#: views/admin/placements/edit-modal/fields/status.php:14
msgid "Publish"
msgstr ""

#: views/admin/screen-options.php:28
msgid "Show filters permanently"
msgstr ""

#: views/admin/screens/group-form.php:16
msgid "Choose the type"
msgstr ""

#: views/admin/screens/group-form.php:58
#: views/admin/tables/groups/edit-form-modal.php:69
msgid "Get all group types with All Access"
msgstr ""

#: views/admin/screens/group-form.php:64
msgid "Choose a name"
msgstr ""

#: views/admin/screens/group-form.php:65
msgid "Group title"
msgstr ""

#: views/admin/screens/settings.php:17
msgid "General"
msgstr ""

#: views/admin/screens/settings.php:47
msgid "Save settings on this page"
msgstr ""

#: views/admin/settings/general/advanced-js.php:14
msgid "The file is currently enabled by an add-on that needs it."
msgstr ""

#. translators: %s is a URL.
#: views/admin/settings/general/advanced-js.php:22
msgid "Enable advanced JavaScript functions (<a href=\"%s\" target=\"_blank\">here</a>). Some features and add-ons might override this setting if they need features from this file."
msgstr ""

#: views/admin/settings/general/allow-unfiltered-html.php:15
msgid "Allow unfiltered HTML for these user roles:"
msgstr ""

#: views/admin/settings/general/allow-unfiltered-html.php:30
msgid "Enabling this option for untrusted users may cause them to publish malicious or poorly formatted code in their ads and can be a potential security risk. You should carefully consider which user roles you grant this capability."
msgstr ""

#: views/admin/settings/general/block-bots.php:20
msgid "Hide ads from crawlers, bots and empty user agents."
msgstr ""

#: views/admin/settings/general/block-bots.php:23
msgid "Read this first"
msgstr ""

#. translators: %s is a URL.
#: views/admin/settings/general/content-injection-everywhere.php:40
msgid "To ensure compatibility, ads are not injected into excerpts or the full content of posts on archive pages. However, by enabling this option, you can override this restriction and set a limit on the number of posts where ads will be injected. Please note that if you want to insert ads between post listing items on archive pages, you can utilize the Post list placement (<a href=\"%s\" target=\"_blank\">manual</a>) feature."
msgstr ""

#: views/admin/settings/general/content-injection-level-limitation.php:15
msgid "Advanced Ads ignores paragraphs and other elements in containers when injecting ads into the post content. Check this option to ignore this limitation and ads might show up again."
msgstr ""

#: views/admin/settings/general/content-injection-priority.php:17
msgid "Please check your post content. A priority of 10 and below might cause issues (wpautop function might run twice)."
msgstr ""

#: views/admin/settings/general/content-injection-priority.php:20
msgid "Play with this value in order to change the priority of the injected ads compared to other auto injected elements in the post content."
msgstr ""

#: views/admin/settings/general/custom-label.php:20
msgid "Displayed above ads."
msgstr ""

#: views/admin/settings/general/custom-label.php:28
msgid "Enable HTML for the field"
msgstr ""

#: views/admin/settings/general/disable-ads.php:19
msgid "Disable all ads in frontend"
msgstr ""

#: views/admin/settings/general/disable-ads.php:23
msgid "Use this option to disable all ads in the frontend, but still be able to use the plugin."
msgstr ""

#: views/admin/settings/general/disable-ads.php:37
msgid "Disable ads on 404 error pages"
msgstr ""

#: views/admin/settings/general/disable-ads.php:50
msgid "Disable ads on non-singular pages"
msgstr ""

#: views/admin/settings/general/disable-ads.php:54
msgid "e.g. archive pages like categories, tags, authors, front page (if a list)"
msgstr ""

#: views/admin/settings/general/disable-ads.php:68
msgid "Disable ads on secondary queries"
msgstr ""

#: views/admin/settings/general/disable-ads.php:72
msgid "Secondary queries are custom queries of posts outside the main query of a page. Try this option if you see ads injected on places where they shouldn’t appear."
msgstr ""

#: views/admin/settings/general/disable-ads.php:86
msgid "Disable ads in RSS Feed"
msgstr ""

#: views/admin/settings/general/disable-ads.php:99
msgid "Disable ads in REST API"
msgstr ""

#: views/admin/settings/general/disable-notices.php:16
msgid "Disable Ad Health in frontend and backend, warnings and internal notices like tips, tutorials, email newsletters and update notices."
msgstr ""

#: views/admin/settings/general/disable-post-types.php:21
msgid "The free version provides the post type display condition on the ad edit page."
msgstr ""

#: views/admin/settings/general/editors-manage-ads.php:15
msgid "Allow editors to also manage and publish ads."
msgstr ""

#. translators: %s is a URL.
#: views/admin/settings/general/editors-manage-ads.php:20
msgid "You can assign different ad-related roles on a user basis with <a href=\"%s\" target=\"_blank\">Advanced Ads Pro</a>."
msgstr ""

#: views/admin/settings/general/frontend-prefix.php:14
msgid "Prefix of class and id attributes for elements created in the frontend."
msgstr ""

#: views/admin/settings/general/hide-for-ip-address.php:21
msgid "Activate module"
msgstr ""

#: views/admin/settings/general/hide-for-ip-address.php:25
#: views/admin/settings/general/hide-for-ip-address.php:34
msgid "Enter one IP address per line for which no ads are displayed."
msgstr ""

#: views/admin/settings/general/hide-for-user-role.php:22
msgid "Choose the roles a user must have in order to not see any ads."
msgstr ""

#: views/admin/settings/general/link-target.php:15
msgid "Open programmatically created links in a new window (use <code>target=\"_blank\"</code>)"
msgstr ""

#: views/admin/settings/general/uninstall-delete-data.php:15
msgid "Clean up all data related to Advanced Ads when removing the plugin."
msgstr ""

#: views/admin/settings/license/section-help.php:13
msgid "How to install and activate an add-on."
msgstr ""

#. translators: %s is a URL.
#: views/admin/settings/license/section-help.php:19
msgid "See also <a href=\"%s\" target=\"_blank\">Issues and questions about licenses</a>."
msgstr ""

#: views/admin/tables/ads/column-type.php:37
msgid "Ad updated"
msgstr ""

#: views/admin/tables/ads/filters.php:46
msgid "Ad Types"
msgstr ""

#: views/admin/tables/ads/filters.php:55
msgid "Ad Sizes"
msgstr ""

#: views/admin/tables/ads/filters.php:65
msgid "Ad Dates"
msgstr ""

#: views/admin/tables/ads/filters.php:94
msgid "Ad Authors"
msgstr ""

#: views/admin/tables/ads/filters.php:104
msgid "Display Once"
msgstr ""

#: views/admin/tables/ads/filters.php:123
msgid "Customize filters"
msgstr ""

#: views/admin/tables/ads/view-list.php:37
#: views/admin/tables/placements/views-list.php:37
msgid "Empty Trash"
msgstr ""

#. translators: %s is the ID of an ad group
#: views/admin/tables/groups/column-details.php:19
msgid "ID: %s"
msgstr ""

#. translators: %s is the name of a group type
#: views/admin/tables/groups/column-details.php:26
msgid "Type: %s"
msgstr ""

#: views/admin/tables/groups/column-usage.php:14
msgid "shortcode"
msgstr ""

#: views/admin/tables/groups/column-usage.php:17
msgid "template (PHP)"
msgstr ""

#: views/admin/tables/groups/edit-form-modal.php:94
msgctxt "option to display all ads in an ad groups"
msgid "all"
msgstr ""

#: views/admin/tables/groups/edit-form-modal.php:101
msgid "Visible ads"
msgstr ""

#: views/admin/tables/groups/edit-form-modal.php:103
msgid "Number of ads that are visible at the same time"
msgstr ""

#: views/admin/tables/groups/filters.php:30
msgid "all group types"
msgstr ""

#: views/admin/tables/groups/list-row-loop-none.php:11
msgid "No ads assigned"
msgstr ""

#: views/admin/tables/groups/list-row-loop-none.php:15
msgid "Add some"
msgstr ""

#: views/admin/tables/groups/list-row-loop.php:49
msgid "Ad weight"
msgstr ""

#. translators: %d is a number.
#: views/admin/tables/groups/list-row-loop.php:64
msgid "+ show %d more ads"
msgstr ""

#. translators: amount of ads displayed
#: views/admin/tables/groups/list-row-loop.php:83
msgid "Up to %d ad displayed."
msgid_plural "Up to %d ads displayed"
msgstr[0] ""
msgstr[1] ""

#: views/admin/tables/groups/list-row-option-ads.php:22
msgid "Weight"
msgstr ""

#. translators: %s is the title for ad.
#: views/admin/tables/groups/list-row-option-ads.php:39
msgid "Opens ad %s in a new tab"
msgstr ""

#: views/admin/tables/groups/list-row-option-ads.php:93
msgid "add"
msgstr ""

#: views/admin/tables/placements/column-conditions.php:40
msgid "edit conditions"
msgstr ""

#. translators: %s is the placement type string
#: views/admin/tables/placements/column-name.php:26
msgid "The originally selected placement type “%s” is not enabled."
msgstr ""

#: views/admin/tables/placements/filter-types.php:14
msgid "Placement Type"
msgstr ""

#: views/admin/tables/placements/filter-types.php:17
msgid "- show all types -"
msgstr ""

#. translators: %s is a URL.
#: views/admin/tables/placements/header-note.php:16
msgid "Tutorial: <a href=\"%s\" target=\"_blank\">How to place visible ads in the header of your website</a>."
msgstr ""

#: views/admin/upgrades/pro-tab.php:14
msgid "Advanced Ads Pro – test and optimize your ad performance"
msgstr ""

#: views/admin/upgrades/pro-tab.php:17
msgid "Ads for Ad Blockers"
msgstr ""

#: views/admin/upgrades/pro-tab.php:20
msgid "support for cached sites"
msgstr ""

#: views/admin/upgrades/pro-tab.php:22
msgid "11 more display and visitor conditions"
msgstr ""

#: views/admin/upgrades/pro-tab.php:23
msgid "6 more placements"
msgstr ""

#: views/admin/upgrades/pro-tab.php:24
msgid "placement tests for ad optimization"
msgstr ""

#: views/admin/upgrades/pro-tab.php:25
msgid "ad grids and many more advanced features"
msgstr ""

#: views/admin/upgrades/pro-tab.php:27
#: views/marketing/ad-metabox-tracking.php:21
msgid "See all features and pricing"
msgstr ""

#: views/admin/welcome-box.php:13
msgid "How to get started"
msgstr ""

#: views/admin/welcome-box.php:14
msgid "Welcome to Advanced Ads"
msgstr ""

#: views/admin/welcome-box.php:16
msgid "Thank you for choosing the best ad management plugin for WordPress."
msgstr ""

#: views/admin/welcome-box.php:19
msgid "Get started quickly with our setup wizard to ensure optimal ad performance. Advanced Ads empowers you to create, target, and analyze your ads like never before."
msgstr ""

#: views/admin/welcome-box.php:20
msgid "Let's unlock your advertising potential and gain your income."
msgstr ""

#: views/admin/welcome-box.php:23
msgid "Launch the Setup Wizard"
msgstr ""

#: views/admin/welcome-box.php:24
msgid "Read the First Steps"
msgstr ""

#: views/admin/widgets/aa-dashboard/next-steps/newsletter.php:12
#: views/admin/widgets/wordpress-dashboard/newsletter.php:13
msgid "Newsletter & Email Courses"
msgstr ""

#. translators: %s is 'free email courses'.
#: views/admin/widgets/aa-dashboard/next-steps/newsletter.php:19
msgid "Join our %s for more benefits and insights:"
msgstr ""

#: views/admin/widgets/aa-dashboard/next-steps/newsletter.php:20
msgid "free email courses"
msgstr ""

#. translators: %s is '2 free add-ons' in bold.
#: views/admin/widgets/aa-dashboard/next-steps/newsletter.php:29
msgid "Gain %s"
msgstr ""

#: views/admin/widgets/aa-dashboard/next-steps/newsletter.php:34
msgid "Take the First Steps with Advanced Ads"
msgstr ""

#: views/admin/widgets/aa-dashboard/next-steps/newsletter.php:35
msgid "Learn how to increase your Google AdSense earnings"
msgstr ""

#: views/admin/widgets/aa-dashboard/next-steps/newsletter.php:36
msgid "Get periodic ad monetization tutorials"
msgstr ""

#: views/admin/widgets/aa-dashboard/next-steps/newsletter.php:47
#: views/admin/widgets/wordpress-dashboard/newsletter.php:35
msgid "5-part First Steps series"
msgstr ""

#: views/admin/widgets/aa-dashboard/next-steps/newsletter.php:57
#: views/admin/widgets/wordpress-dashboard/newsletter.php:45
msgid "8-part Google AdSense series"
msgstr ""

#: views/admin/widgets/aa-dashboard/next-steps/newsletter.php:67
msgid "Periodic expert tutorials"
msgstr ""

#: views/admin/widgets/aa-dashboard/next-steps/widget.php:34
msgid "Do you find Advanced Ads useful and would like to keep us motivated? Please help us with a review."
msgstr ""

#: views/admin/widgets/aa-dashboard/next-steps/widget.php:37
msgid "Sure, I’ll rate the plugin"
msgstr ""

#: views/admin/widgets/aa-dashboard/next-steps/widget.php:39
msgid "I already did"
msgstr ""

#: views/admin/widgets/aa-dashboard/next-steps/widget.php:44
msgid "Manage your ads"
msgstr ""

#: views/admin/widgets/aa-dashboard/next-steps/widget.php:57
msgid "Get the All Access pass"
msgstr ""

#: views/admin/widgets/aa-dashboard/overview-notices.php:15
msgid "There are no notifications."
msgstr ""

#. translators: %s is the number of hidden notices.
#: views/admin/widgets/aa-dashboard/overview-notices.php:25
msgid "%s hidden notifications"
msgstr ""

#: views/admin/widgets/aa-dashboard/support.php:23
msgid "Support & FAQ"
msgstr ""

#: views/admin/widgets/aa-dashboard/support.php:31
msgid "Latest Tutorials"
msgstr ""

#. translators: %1$s is the opening <a> tag, %2$s is the closing </a> tag.
#: views/admin/widgets/aa-dashboard/support.php:41
msgid "%1$sThank the developer with a &#9733;&#9733;&#9733;&#9733;&#9733; review on wordpress.org%2$s"
msgstr ""

#: views/admin/widgets/rss-posts.php:13
msgid "Error: the Advanced Ads blog feed could not be downloaded."
msgstr ""

#: views/admin/widgets/wordpress-dashboard/footer.php:11
msgid "Visit our blog"
msgstr ""

#: views/admin/widgets/wordpress-dashboard/footer.php:17
#: views/marketing/ad-metabox-all-access.php:23
msgid "Get All Access"
msgstr ""

#. translators: %1$d is the number of ads.
#: views/admin/widgets/wordpress-dashboard/header.php:18
msgid "%1$d Ads"
msgstr ""

#: views/admin/widgets/wordpress-dashboard/header.php:24
msgid "Manage Ads"
msgstr ""

#: views/admin/widgets/wordpress-dashboard/header.php:28
msgid "Create Ad"
msgstr ""

#. translators: %1$s 'free' (in bold), %2$s '2 free add-ons' (in bold)
#: views/admin/widgets/wordpress-dashboard/newsletter.php:19
msgid "Join our newsletter and take our %1$s ad monetization email courses. Get tutorials, optimization tips, and %2$s!"
msgstr ""

#: views/admin/widgets/wordpress-dashboard/newsletter.php:23
msgid "free"
msgstr ""

#: views/admin/widgets/wordpress-dashboard/performing-ads.php:9
msgid "today"
msgstr ""

#: views/admin/widgets/wordpress-dashboard/performing-ads.php:10
msgid "yesterday"
msgstr ""

#: views/admin/widgets/wordpress-dashboard/performing-ads.php:11
msgid "last 7 days"
msgstr ""

#: views/admin/widgets/wordpress-dashboard/performing-ads.php:12
msgid "this month"
msgstr ""

#: views/admin/widgets/wordpress-dashboard/performing-ads.php:13
msgid "last month"
msgstr ""

#: views/admin/widgets/wordpress-dashboard/performing-ads.php:14
msgid "this year"
msgstr ""

#: views/admin/widgets/wordpress-dashboard/performing-ads.php:15
msgid "last year"
msgstr ""

#: views/admin/widgets/wordpress-dashboard/performing-ads.php:16
msgid "custom"
msgstr ""

#: views/admin/widgets/wordpress-dashboard/performing-ads.php:22
msgid "Best-performing Ads for"
msgstr ""

#: views/admin/widgets/wordpress-dashboard/performing-ads.php:32
msgid "from"
msgstr ""

#: views/admin/widgets/wordpress-dashboard/performing-ads.php:33
msgid "to"
msgstr ""

#: views/admin/widgets/wordpress-dashboard/performing-ads.php:44
msgid "See full statistics"
msgstr ""

#: views/admin/widgets/wordpress-dashboard/performing-ads.php:49
msgid "No tracking add-on installed."
msgstr ""

#: views/admin/widgets/wordpress-dashboard/performing-ads.php:53
msgid "Advanced Ads All Access includes the Tracking add-on"
msgstr ""

#: views/admin/widgets/wordpress-dashboard/rss.php:13
msgid "Latest Tutorials from Advanced Ads"
msgstr ""

#: views/marketing/ad-metabox-all-access.php:11
msgid "All Access – with all available add-ons"
msgstr ""

#: views/marketing/ad-metabox-all-access.php:13
msgid "Advanced Ads Pro"
msgstr ""

#: views/marketing/ad-metabox-all-access.php:15
msgid "Responsive, AMP and Mobile ads"
msgstr ""

#: views/marketing/ad-metabox-all-access.php:16
msgid "Google Ad Manager Integration"
msgstr ""

#: views/marketing/ad-metabox-all-access.php:17
msgid "Sticky Ads"
msgstr ""

#: views/marketing/ad-metabox-all-access.php:18
msgid "PopUp Ads"
msgstr ""

#: views/marketing/ad-metabox-all-access.php:19
msgid "Selling Ads"
msgstr ""

#: views/marketing/ad-metabox-all-access.php:22
msgid "Risk free with 30-day Money-Back guarantee"
msgstr ""

#: views/marketing/ad-metabox-tracking.php:15
msgid "track impressions and click on your ads"
msgstr ""

#: views/marketing/ad-metabox-tracking.php:16
msgid "compare ads and periods"
msgstr ""

#: views/marketing/ad-metabox-tracking.php:17
msgid "share reports via link or email"
msgstr ""

#: views/marketing/ad-metabox-tracking.php:18
msgid "limit ads views by overall number of impressions or clicks"
msgstr ""

#: views/marketing/ad-metabox-tracking.php:19
msgid "spread impressions or clicks equally over a given period"
msgstr ""

#. translators: %s post type plural name
#: views/notices/ad-disable-post-type.php:17
msgid "Ads are disabled for all %s"
msgstr ""

#: views/notices/create-first-ad.php:17
msgid "Watch the “First Ad” Tutorial (Video)"
msgstr ""
