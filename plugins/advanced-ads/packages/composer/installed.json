{"packages": [{"name": "advanced-ads/framework", "version": "dev-main", "version_normalized": "dev-main", "source": {"type": "git", "url": "https://github.com/advanced-ads/framework.git", "reference": "bafe2c32b1530cfb18d8b738adb9524f8406b9aa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/advanced-ads/framework/zipball/bafe2c32b1530cfb18d8b738adb9524f8406b9aa", "reference": "bafe2c32b1530cfb18d8b738adb9524f8406b9aa", "shasum": ""}, "require": {"php": ">=7.2"}, "require-dev": {"phpcompatibility/phpcompatibility-wp": "*", "wp-coding-standards/wpcs": "^3.0.0"}, "time": "2025-03-10T12:31:18+00:00", "default-branch": true, "type": "wordpress-plugin", "installation-source": "dist", "autoload": {"files": ["src/assets.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-3.0-or-later"], "authors": [{"name": "Advanced Ads", "email": "<EMAIL>", "homepage": "https://wpadvancedads.com"}], "description": "Place ads on various positions within Genesis themes", "homepage": "https://wpadvancedads.com/", "support": {"issues": "https://github.com/advanced-ads/framework/issues", "source": "https://github.com/advanced-ads/framework/tree/main"}, "install-path": "../advanced-ads/framework"}, {"name": "mobiledetect/mobiledetectlib", "version": "3.74.3", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/serbanghita/Mobile-Detect.git", "reference": "39582ab62f86b40e4edb698159f895929a29c346"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/serbanghita/Mobile-Detect/zipball/39582ab62f86b40e4edb698159f895929a29c346", "reference": "39582ab62f86b40e4edb698159f895929a29c346", "shasum": ""}, "require": {"php": ">=7.4"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.14", "phpunit/phpunit": "^9.6", "squizlabs/php_codesniffer": "^3.7"}, "time": "2023-10-27T16:28:04+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Detection\\": "src/"}, "classmap": ["src/MobileDetect.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "serban<PERSON><PERSON>@gmail.com", "homepage": "https://mobiledetect.net", "role": "Developer"}], "description": "Mobile_Detect is a lightweight PHP class for detecting mobile devices. It uses the User-Agent string combined with specific HTTP headers to detect the mobile environment.", "homepage": "https://github.com/serbanghita/Mobile-Detect", "keywords": ["detect mobile devices", "mobile", "mobile detect", "mobile detector", "php mobile detect"], "support": {"issues": "https://github.com/serbanghita/Mobile-Detect/issues", "source": "https://github.com/serbanghita/Mobile-Detect/tree/3.74.3"}, "funding": [{"url": "https://github.com/serbanghita", "type": "github"}], "install-path": "../mobiledetect/mobiledetectlib"}], "dev": false, "dev-package-names": []}