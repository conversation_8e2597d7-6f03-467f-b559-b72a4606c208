<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'AdvancedAds\\Abstracts\\Ad' => $baseDir . '/includes/abstracts/abstract-ad.php',
    'AdvancedAds\\Abstracts\\Admin_List_Table' => $baseDir . '/includes/abstracts/abstract-admin-list-table.php',
    'AdvancedAds\\Abstracts\\Data' => $baseDir . '/includes/abstracts/abstract-data.php',
    'AdvancedAds\\Abstracts\\Factory' => $baseDir . '/includes/abstracts/abstract-factory.php',
    'AdvancedAds\\Abstracts\\Group' => $baseDir . '/includes/abstracts/abstract-group.php',
    'AdvancedAds\\Abstracts\\Placement' => $baseDir . '/includes/abstracts/abstract-placement.php',
    'AdvancedAds\\Abstracts\\Placement_Type' => $baseDir . '/includes/abstracts/abstract-placement-type.php',
    'AdvancedAds\\Abstracts\\Screen' => $baseDir . '/includes/abstracts/abstract-screen.php',
    'AdvancedAds\\Abstracts\\Types' => $baseDir . '/includes/abstracts/abstract-types.php',
    'AdvancedAds\\Admin\\AJAX' => $baseDir . '/includes/admin/class-ajax.php',
    'AdvancedAds\\Admin\\Action_Links' => $baseDir . '/includes/admin/class-action-links.php',
    'AdvancedAds\\Admin\\Ad_List_Table' => $baseDir . '/includes/admin/class-ad-list-table.php',
    'AdvancedAds\\Admin\\Addon_Box' => $baseDir . '/includes/admin/class-addon-box.php',
    'AdvancedAds\\Admin\\Addon_Updater' => $baseDir . '/includes/admin/class-addon-updater.php',
    'AdvancedAds\\Admin\\Admin_Menu' => $baseDir . '/includes/admin/class-admin-menu.php',
    'AdvancedAds\\Admin\\Admin_Notices' => $baseDir . '/includes/admin/class-admin-notices.php',
    'AdvancedAds\\Admin\\Assets' => $baseDir . '/includes/admin/class-assets.php',
    'AdvancedAds\\Admin\\Authors' => $baseDir . '/includes/admin/class-authors.php',
    'AdvancedAds\\Admin\\Compatibility' => $baseDir . '/includes/admin/class-compatibility.php',
    'AdvancedAds\\Admin\\EDD_Updater' => $baseDir . '/includes/admin/class-edd-updater.php',
    'AdvancedAds\\Admin\\Groups_List_Table' => $baseDir . '/includes/admin/class-groups-list-table.php',
    'AdvancedAds\\Admin\\Header' => $baseDir . '/includes/admin/class-header.php',
    'AdvancedAds\\Admin\\List_Filters' => $baseDir . '/includes/admin/class-list-filters.php',
    'AdvancedAds\\Admin\\Marketing' => $baseDir . '/includes/admin/class-marketing.php',
    'AdvancedAds\\Admin\\Metabox_Ad' => $baseDir . '/includes/admin/class-metabox-ad.php',
    'AdvancedAds\\Admin\\Metabox_Ad_Settings' => $baseDir . '/includes/admin/class-metabox-ad-settings.php',
    'AdvancedAds\\Admin\\Metaboxes\\Ad_Adsense' => $baseDir . '/includes/admin/metaboxes/class-ad-adsense.php',
    'AdvancedAds\\Admin\\Metaboxes\\Ad_Layout' => $baseDir . '/includes/admin/metaboxes/class-ad-layout.php',
    'AdvancedAds\\Admin\\Metaboxes\\Ad_Parameters' => $baseDir . '/includes/admin/metaboxes/class-ad-parameters.php',
    'AdvancedAds\\Admin\\Metaboxes\\Ad_Targeting' => $baseDir . '/includes/admin/metaboxes/class-ad-targeting.php',
    'AdvancedAds\\Admin\\Metaboxes\\Ad_Types' => $baseDir . '/includes/admin/metaboxes/class-ad-types.php',
    'AdvancedAds\\Admin\\Metaboxes\\Ad_Usage' => $baseDir . '/includes/admin/metaboxes/class-ad-usage.php',
    'AdvancedAds\\Admin\\Misc' => $baseDir . '/includes/admin/class-misc.php',
    'AdvancedAds\\Admin\\Page_Quick_Edit' => $baseDir . '/includes/admin/class-page-quick-edit.php',
    'AdvancedAds\\Admin\\Pages\\Ads' => $baseDir . '/includes/admin/pages/class-ads.php',
    'AdvancedAds\\Admin\\Pages\\Ads_Editing' => $baseDir . '/includes/admin/pages/class-ads-editing.php',
    'AdvancedAds\\Admin\\Pages\\Dashboard' => $baseDir . '/includes/admin/pages/class-dashboard.php',
    'AdvancedAds\\Admin\\Pages\\Groups' => $baseDir . '/includes/admin/pages/class-groups.php',
    'AdvancedAds\\Admin\\Pages\\Onboarding' => $baseDir . '/includes/admin/pages/class-onboarding.php',
    'AdvancedAds\\Admin\\Pages\\Placements' => $baseDir . '/includes/admin/pages/class-placements.php',
    'AdvancedAds\\Admin\\Pages\\Settings' => $baseDir . '/includes/admin/pages/class-settings.php',
    'AdvancedAds\\Admin\\Pages\\Tools' => $baseDir . '/includes/admin/pages/class-tools.php',
    'AdvancedAds\\Admin\\Pages\\Ui_Toolkit' => $baseDir . '/includes/admin/pages/class-ui-toolkit.php',
    'AdvancedAds\\Admin\\Placement\\Bulk_Edit' => $baseDir . '/includes/admin/placement/class-bulk-edit.php',
    'AdvancedAds\\Admin\\Placement_Create_Modal' => $baseDir . '/includes/admin/class-placement-create-modal.php',
    'AdvancedAds\\Admin\\Placement_Edit_Modal' => $baseDir . '/includes/admin/class-placement-edit-modal.php',
    'AdvancedAds\\Admin\\Placement_List_Table' => $baseDir . '/includes/admin/class-placement-list-table.php',
    'AdvancedAds\\Admin\\Placement_Quick_Edit' => $baseDir . '/includes/admin/class-placement-quick-edit.php',
    'AdvancedAds\\Admin\\Plugin_Installer' => $baseDir . '/includes/admin/class-plugin-installer.php',
    'AdvancedAds\\Admin\\Post_List' => $baseDir . '/includes/admin/class-post-list.php',
    'AdvancedAds\\Admin\\Post_Types' => $baseDir . '/includes/admin/class-post-types.php',
    'AdvancedAds\\Admin\\Quick_Bulk_Edit' => $baseDir . '/includes/admin/class-quick-bulk-edit.php',
    'AdvancedAds\\Admin\\Screen_Options' => $baseDir . '/includes/admin/class-screen-options.php',
    'AdvancedAds\\Admin\\Settings' => $baseDir . '/includes/admin/class-settings.php',
    'AdvancedAds\\Admin\\Shortcode_Creator' => $baseDir . '/includes/admin/class-shortcode-creator.php',
    'AdvancedAds\\Admin\\System_Info' => $baseDir . '/includes/admin/class-system-info.php',
    'AdvancedAds\\Admin\\TinyMCE' => $baseDir . '/includes/admin/class-tinymce.php',
    'AdvancedAds\\Admin\\Translation_Promo' => $baseDir . '/includes/admin/class-translation-promo.php',
    'AdvancedAds\\Admin\\Upgrades' => $baseDir . '/includes/admin/class-upgrades.php',
    'AdvancedAds\\Admin\\Version_Control' => $baseDir . '/includes/admin/class-version-control.php',
    'AdvancedAds\\Admin\\Welcome' => $baseDir . '/includes/admin/class-welcome.php',
    'AdvancedAds\\Admin\\WordPress_Dashboard' => $baseDir . '/includes/admin/class-wordpress-dashboard.php',
    'AdvancedAds\\Ads\\Ad_Content' => $baseDir . '/includes/ads/class-ad-content.php',
    'AdvancedAds\\Ads\\Ad_Dummy' => $baseDir . '/includes/ads/class-ad-dummy.php',
    'AdvancedAds\\Ads\\Ad_Factory' => $baseDir . '/includes/ads/class-ad-factory.php',
    'AdvancedAds\\Ads\\Ad_Group' => $baseDir . '/includes/ads/class-ad-group.php',
    'AdvancedAds\\Ads\\Ad_Group_Relation' => $baseDir . '/includes/ads/class-ad-group-relation.php',
    'AdvancedAds\\Ads\\Ad_Image' => $baseDir . '/includes/ads/class-ad-image.php',
    'AdvancedAds\\Ads\\Ad_Plain' => $baseDir . '/includes/ads/class-ad-plain.php',
    'AdvancedAds\\Ads\\Ad_Repository' => $baseDir . '/includes/ads/class-ad-repository.php',
    'AdvancedAds\\Ads\\Ad_Types' => $baseDir . '/includes/ads/class-ad-types.php',
    'AdvancedAds\\Ads\\Ads' => $baseDir . '/includes/ads/class-ads.php',
    'AdvancedAds\\Ads\\Types\\AMP' => $baseDir . '/includes/ads/types/type-amp.php',
    'AdvancedAds\\Ads\\Types\\Content' => $baseDir . '/includes/ads/types/type-content.php',
    'AdvancedAds\\Ads\\Types\\Dummy' => $baseDir . '/includes/ads/types/type-dummy.php',
    'AdvancedAds\\Ads\\Types\\GAM' => $baseDir . '/includes/ads/types/type-gam.php',
    'AdvancedAds\\Ads\\Types\\Group' => $baseDir . '/includes/ads/types/type-group.php',
    'AdvancedAds\\Ads\\Types\\Image' => $baseDir . '/includes/ads/types/type-image.php',
    'AdvancedAds\\Ads\\Types\\Plain' => $baseDir . '/includes/ads/types/type-plain.php',
    'AdvancedAds\\Ads\\Types\\Unknown' => $baseDir . '/includes/ads/types/type-unknown.php',
    'AdvancedAds\\Assets_Registry' => $baseDir . '/includes/class-assets-registry.php',
    'AdvancedAds\\Autoloader' => $baseDir . '/includes/class-autoloader.php',
    'AdvancedAds\\Compatibility\\AAWP' => $baseDir . '/includes/compatibility/class-aawp.php',
    'AdvancedAds\\Compatibility\\AAWP_Ad' => $baseDir . '/includes/compatibility/class-aawp-ad.php',
    'AdvancedAds\\Compatibility\\Admin_Compatibility' => $baseDir . '/includes/compatibility/class-admin-compatibility.php',
    'AdvancedAds\\Compatibility\\Capability_Manager' => $baseDir . '/includes/compatibility/class-capability-manager.php',
    'AdvancedAds\\Compatibility\\Compatibility' => $baseDir . '/includes/compatibility/class-compatibility.php',
    'AdvancedAds\\Compatibility\\Inline_JS' => $baseDir . '/includes/compatibility/class-inline-js.php',
    'AdvancedAds\\Compatibility\\Peepso' => $baseDir . '/includes/compatibility/class-peepso.php',
    'AdvancedAds\\Compatibility\\Peepso_Ad' => $baseDir . '/includes/compatibility/class-peepso-ad.php',
    'AdvancedAds\\Constants' => $baseDir . '/includes/class-constants.php',
    'AdvancedAds\\Crons\\Ads' => $baseDir . '/includes/crons/class-ads.php',
    'AdvancedAds\\Entities' => $baseDir . '/includes/class-entities.php',
    'AdvancedAds\\Framework\\Assets_Registry' => $vendorDir . '/advanced-ads/framework/src/class-assets-registry.php',
    'AdvancedAds\\Framework\\Form\\Field' => $vendorDir . '/advanced-ads/framework/src/form/class-field.php',
    'AdvancedAds\\Framework\\Form\\Field_Checkbox' => $vendorDir . '/advanced-ads/framework/src/form/class-field-checkbox.php',
    'AdvancedAds\\Framework\\Form\\Field_Color' => $vendorDir . '/advanced-ads/framework/src/form/class-field-color.php',
    'AdvancedAds\\Framework\\Form\\Field_Position' => $vendorDir . '/advanced-ads/framework/src/form/class-field-position.php',
    'AdvancedAds\\Framework\\Form\\Field_Radio' => $vendorDir . '/advanced-ads/framework/src/form/class-field-radio.php',
    'AdvancedAds\\Framework\\Form\\Field_Selector' => $vendorDir . '/advanced-ads/framework/src/form/class-field-selector.php',
    'AdvancedAds\\Framework\\Form\\Field_Size' => $vendorDir . '/advanced-ads/framework/src/form/class-field-size.php',
    'AdvancedAds\\Framework\\Form\\Field_Switch' => $vendorDir . '/advanced-ads/framework/src/form/class-field-switch.php',
    'AdvancedAds\\Framework\\Form\\Field_Text' => $vendorDir . '/advanced-ads/framework/src/form/class-field-text.php',
    'AdvancedAds\\Framework\\Form\\Field_Textarea' => $vendorDir . '/advanced-ads/framework/src/form/class-field-textarea.php',
    'AdvancedAds\\Framework\\Form\\Form' => $vendorDir . '/advanced-ads/framework/src/form/class-form.php',
    'AdvancedAds\\Framework\\Installation\\Install' => $vendorDir . '/advanced-ads/framework/src/installation/class-install.php',
    'AdvancedAds\\Framework\\Interfaces\\Initializer_Interface' => $vendorDir . '/advanced-ads/framework/src/interfaces/interface-initializer.php',
    'AdvancedAds\\Framework\\Interfaces\\Integration_Interface' => $vendorDir . '/advanced-ads/framework/src/interfaces/interface-integration.php',
    'AdvancedAds\\Framework\\Interfaces\\Routes_Interface' => $vendorDir . '/advanced-ads/framework/src/interfaces/interface-routes.php',
    'AdvancedAds\\Framework\\JSON' => $vendorDir . '/advanced-ads/framework/src/class-json.php',
    'AdvancedAds\\Framework\\Loader' => $vendorDir . '/advanced-ads/framework/src/class-loader.php',
    'AdvancedAds\\Framework\\Notices\\Manager' => $vendorDir . '/advanced-ads/framework/src/notices/class-manager.php',
    'AdvancedAds\\Framework\\Notices\\Notice' => $vendorDir . '/advanced-ads/framework/src/notices/class-notice.php',
    'AdvancedAds\\Framework\\Notices\\Storage' => $vendorDir . '/advanced-ads/framework/src/notices/class-storage.php',
    'AdvancedAds\\Framework\\Updates' => $vendorDir . '/advanced-ads/framework/src/class-updates.php',
    'AdvancedAds\\Framework\\Utilities\\Arr' => $vendorDir . '/advanced-ads/framework/src/utilities/class-arr.php',
    'AdvancedAds\\Framework\\Utilities\\Formatting' => $vendorDir . '/advanced-ads/framework/src/utilities/class-formatting.php',
    'AdvancedAds\\Framework\\Utilities\\HTML' => $vendorDir . '/advanced-ads/framework/src/utilities/class-html.php',
    'AdvancedAds\\Framework\\Utilities\\Params' => $vendorDir . '/advanced-ads/framework/src/utilities/class-params.php',
    'AdvancedAds\\Framework\\Utilities\\Str' => $vendorDir . '/advanced-ads/framework/src/utilities/class-str.php',
    'AdvancedAds\\Frontend\\Ad_Display_Condition' => $baseDir . '/includes/frontend/class-ad-display-condition.php',
    'AdvancedAds\\Frontend\\Ad_Renderer' => $baseDir . '/includes/frontend/class-ad-renderer.php',
    'AdvancedAds\\Frontend\\Debug_Ads' => $baseDir . '/includes/frontend/class-debug-ads.php',
    'AdvancedAds\\Frontend\\Manager' => $baseDir . '/includes/frontend/class-manager.php',
    'AdvancedAds\\Frontend\\Scripts' => $baseDir . '/includes/frontend/class-scripts.php',
    'AdvancedAds\\Frontend\\Stats' => $baseDir . '/includes/frontend/class-stats.php',
    'AdvancedAds\\Groups\\Group_Ad_Relation' => $baseDir . '/includes/groups/class-group-ad-relation.php',
    'AdvancedAds\\Groups\\Group_Factory' => $baseDir . '/includes/groups/class-group-factory.php',
    'AdvancedAds\\Groups\\Group_Ordered' => $baseDir . '/includes/groups/class-group-ordered.php',
    'AdvancedAds\\Groups\\Group_Repository' => $baseDir . '/includes/groups/class-group-repository.php',
    'AdvancedAds\\Groups\\Group_Slider' => $baseDir . '/includes/groups/class-group-slider.php',
    'AdvancedAds\\Groups\\Group_Standard' => $baseDir . '/includes/groups/class-group-standard.php',
    'AdvancedAds\\Groups\\Group_Types' => $baseDir . '/includes/groups/class-group-types.php',
    'AdvancedAds\\Groups\\Groups' => $baseDir . '/includes/groups/class-groups.php',
    'AdvancedAds\\Groups\\Types\\Grid' => $baseDir . '/includes/groups/types/type-grid.php',
    'AdvancedAds\\Groups\\Types\\Ordered' => $baseDir . '/includes/groups/types/type-ordered.php',
    'AdvancedAds\\Groups\\Types\\Slider' => $baseDir . '/includes/groups/types/type-slider.php',
    'AdvancedAds\\Groups\\Types\\Standard' => $baseDir . '/includes/groups/types/type-standard.php',
    'AdvancedAds\\Groups\\Types\\Unknown' => $baseDir . '/includes/groups/types/type-unknown.php',
    'AdvancedAds\\Importers\\Ad_Inserter' => $baseDir . '/includes/importers/class-ad-inserter.php',
    'AdvancedAds\\Importers\\Ads_WP_Ads' => $baseDir . '/includes/importers/class-ads-wp-ads.php',
    'AdvancedAds\\Importers\\Amp_WP_Ads' => $baseDir . '/includes/importers/class-amp-wp-ads.php',
    'AdvancedAds\\Importers\\Api_Ads' => $baseDir . '/includes/importers/class-api-ads.php',
    'AdvancedAds\\Importers\\Google_Sheet' => $baseDir . '/includes/importers/class-google-sheet.php',
    'AdvancedAds\\Importers\\Importer' => $baseDir . '/includes/importers/abstract-importer.php',
    'AdvancedAds\\Importers\\Manager' => $baseDir . '/includes/importers/class-manager.php',
    'AdvancedAds\\Importers\\Plugin_Exporter' => $baseDir . '/includes/importers/class-plugin-exporter.php',
    'AdvancedAds\\Importers\\Quick_Adsense' => $baseDir . '/includes/importers/class-quick-adsense.php',
    'AdvancedAds\\Importers\\Tutorials' => $baseDir . '/includes/importers/class-tutorials.php',
    'AdvancedAds\\Importers\\WP_Quads' => $baseDir . '/includes/importers/class-wp-quads.php',
    'AdvancedAds\\Importers\\XML_Importer' => $baseDir . '/includes/importers/class-xml-importer.php',
    'AdvancedAds\\Installation\\Capabilities' => $baseDir . '/includes/installation/class-capabilities.php',
    'AdvancedAds\\Installation\\Compatibility' => $baseDir . '/includes/installation/class-compatibility.php',
    'AdvancedAds\\Installation\\Install' => $baseDir . '/includes/installation/class-install.php',
    'AdvancedAds\\Installation\\Uninstall' => $baseDir . '/includes/installation/class-uninstall.php',
    'AdvancedAds\\Interfaces\\Ad_Interface' => $baseDir . '/includes/interfaces/interface-ad.php',
    'AdvancedAds\\Interfaces\\Ad_Type' => $baseDir . '/includes/interfaces/interface-ad-type.php',
    'AdvancedAds\\Interfaces\\Group_Interface' => $baseDir . '/includes/interfaces/interface-group.php',
    'AdvancedAds\\Interfaces\\Group_Type' => $baseDir . '/includes/interfaces/interface-group-type.php',
    'AdvancedAds\\Interfaces\\Importer' => $baseDir . '/includes/interfaces/interface-importer.php',
    'AdvancedAds\\Interfaces\\Module_Interface' => $baseDir . '/includes/interfaces/interface-module.php',
    'AdvancedAds\\Interfaces\\Placement_Interface' => $baseDir . '/includes/interfaces/interface-placement.php',
    'AdvancedAds\\Interfaces\\Placement_Type' => $baseDir . '/includes/interfaces/interface-placement-type.php',
    'AdvancedAds\\Modal' => $baseDir . '/includes/class-modal.php',
    'AdvancedAds\\Modules' => $baseDir . '/includes/class-modules.php',
    'AdvancedAds\\Modules\\OneClick\\Admin\\Admin' => $baseDir . '/modules/one-click/admin/class-admin.php',
    'AdvancedAds\\Modules\\OneClick\\Admin\\Ajax' => $baseDir . '/modules/one-click/admin/class-ajax.php',
    'AdvancedAds\\Modules\\OneClick\\AdsTxt\\AdsTxt' => $baseDir . '/modules/one-click/modules/adstxt/class-adstxt.php',
    'AdvancedAds\\Modules\\OneClick\\AdsTxt\\Detector' => $baseDir . '/modules/one-click/modules/adstxt/class-detector.php',
    'AdvancedAds\\Modules\\OneClick\\Header_Bidding' => $baseDir . '/modules/one-click/modules/class-header-bidding.php',
    'AdvancedAds\\Modules\\OneClick\\Helpers' => $baseDir . '/modules/one-click/class-helpers.php',
    'AdvancedAds\\Modules\\OneClick\\Options' => $baseDir . '/modules/one-click/class-options.php',
    'AdvancedAds\\Modules\\OneClick\\Page_Parser' => $baseDir . '/modules/one-click/class-page-parser.php',
    'AdvancedAds\\Modules\\OneClick\\Tags_Conversion' => $baseDir . '/modules/one-click/modules/class-tags-conversion.php',
    'AdvancedAds\\Modules\\OneClick\\Traffic_Cop' => $baseDir . '/modules/one-click/modules/class-traffic-cop.php',
    'AdvancedAds\\Modules\\OneClick\\Workflow' => $baseDir . '/modules/one-click/modules/class-workflow.php',
    'AdvancedAds\\Modules\\ProductExperimentationFramework\\Module' => $baseDir . '/modules/pef/class-module.php',
    'AdvancedAds\\Options' => $baseDir . '/includes/class-options.php',
    'AdvancedAds\\Placements\\Placement_After_Content' => $baseDir . '/includes/placements/class-placement-after-content.php',
    'AdvancedAds\\Placements\\Placement_Before_Content' => $baseDir . '/includes/placements/class-placement-before-content.php',
    'AdvancedAds\\Placements\\Placement_Content' => $baseDir . '/includes/placements/class-placement-content.php',
    'AdvancedAds\\Placements\\Placement_Factory' => $baseDir . '/includes/placements/class-placement-factory.php',
    'AdvancedAds\\Placements\\Placement_Footer' => $baseDir . '/includes/placements/class-placement-footer.php',
    'AdvancedAds\\Placements\\Placement_Header' => $baseDir . '/includes/placements/class-placement-header.php',
    'AdvancedAds\\Placements\\Placement_Repository' => $baseDir . '/includes/placements/class-placement-repository.php',
    'AdvancedAds\\Placements\\Placement_Sidebar_Widget' => $baseDir . '/includes/placements/class-placement-sidebar-widget.php',
    'AdvancedAds\\Placements\\Placement_Standard' => $baseDir . '/includes/placements/class-placement-standard.php',
    'AdvancedAds\\Placements\\Placement_Types' => $baseDir . '/includes/placements/class-placement-types.php',
    'AdvancedAds\\Placements\\Placements' => $baseDir . '/includes/placements/class-placements.php',
    'AdvancedAds\\Placements\\Types\\After_Content' => $baseDir . '/includes/placements/types/class-after-content.php',
    'AdvancedAds\\Placements\\Types\\Before_Content' => $baseDir . '/includes/placements/types/class-before-content.php',
    'AdvancedAds\\Placements\\Types\\Content' => $baseDir . '/includes/placements/types/class-content.php',
    'AdvancedAds\\Placements\\Types\\Footer' => $baseDir . '/includes/placements/types/class-footer.php',
    'AdvancedAds\\Placements\\Types\\Header' => $baseDir . '/includes/placements/types/class-header.php',
    'AdvancedAds\\Placements\\Types\\Sidebar_Widget' => $baseDir . '/includes/placements/types/class-sidebar-widget.php',
    'AdvancedAds\\Placements\\Types\\Standard' => $baseDir . '/includes/placements/types/class-standard.php',
    'AdvancedAds\\Placements\\Types\\Unknown' => $baseDir . '/includes/placements/types/class-unknown.php',
    'AdvancedAds\\Plugin' => $baseDir . '/includes/class-plugin.php',
    'AdvancedAds\\Post_Data' => $baseDir . '/includes/class-post-data.php',
    'AdvancedAds\\Rest\\Groups' => $baseDir . '/includes/rest/class-groups.php',
    'AdvancedAds\\Rest\\OnBoarding' => $baseDir . '/includes/rest/class-onboarding.php',
    'AdvancedAds\\Rest\\Page_Quick_Edit' => $baseDir . '/includes/rest/class-page-quick-edit.php',
    'AdvancedAds\\Rest\\Placements' => $baseDir . '/includes/rest/class-placements.php',
    'AdvancedAds\\Rest\\Utilities' => $baseDir . '/includes/rest/class-utilities.php',
    'AdvancedAds\\Shortcodes' => $baseDir . '/includes/class-shortcodes.php',
    'AdvancedAds\\Traits\\Entity' => $baseDir . '/includes/traits/class-entity.php',
    'AdvancedAds\\Traits\\Extras' => $baseDir . '/includes/traits/class-extras.php',
    'AdvancedAds\\Traits\\Wrapper' => $baseDir . '/includes/traits/class-wrapper.php',
    'AdvancedAds\\Upgrades' => $baseDir . '/includes/class-upgrades.php',
    'AdvancedAds\\Utilities\\Conditional' => $baseDir . '/includes/utilities/class-conditional.php',
    'AdvancedAds\\Utilities\\Content_Injection' => $baseDir . '/includes/utilities/class-content-injection.php',
    'AdvancedAds\\Utilities\\Data' => $baseDir . '/includes/utilities/class-data.php',
    'AdvancedAds\\Utilities\\Sanitize' => $baseDir . '/includes/utilities/class-sanitize.php',
    'AdvancedAds\\Utilities\\Testing' => $baseDir . '/includes/utilities/class-testing.php',
    'AdvancedAds\\Utilities\\Validation' => $baseDir . '/includes/utilities/class-validation.php',
    'AdvancedAds\\Utilities\\WordPress' => $baseDir . '/includes/utilities/class-wordpress.php',
    'AdvancedAds\\Widget' => $baseDir . '/includes/class-widget.php',
    'Advanced_Ads' => $baseDir . '/public/class-advanced-ads.php',
    'Advanced_Ads_Ad_Health_Notices' => $baseDir . '/classes/ad-health-notices.php',
    'Advanced_Ads_Ad_Network' => $baseDir . '/admin/includes/class-ad-network.php',
    'Advanced_Ads_Ad_Network_Ad_Unit' => $baseDir . '/admin/includes/class-ad-network-ad-unit.php',
    'Advanced_Ads_Ad_Positioning' => $baseDir . '/modules/ad-positioning/classes/ad-positioning.php',
    'Advanced_Ads_Admin_Licenses' => $baseDir . '/admin/includes/class-licenses.php',
    'Advanced_Ads_Admin_Notices' => $baseDir . '/admin/includes/class-notices.php',
    'Advanced_Ads_Checks' => $baseDir . '/classes/checks.php',
    'Advanced_Ads_Display_Conditions' => $baseDir . '/classes/display-conditions.php',
    'Advanced_Ads_Filesystem' => $baseDir . '/classes/filesystem.php',
    'Advanced_Ads_Frontend_Checks' => $baseDir . '/classes/frontend_checks.php',
    'Advanced_Ads_In_Content_Injector' => $baseDir . '/classes/in-content-injector.php',
    'Advanced_Ads_Inline_Css' => $baseDir . '/classes/inline-css.php',
    'Advanced_Ads_ModuleLoader' => $baseDir . '/includes/load_modules.php',
    'Advanced_Ads_Overview_Widgets_Callbacks' => $baseDir . '/admin/includes/class-overview-widgets.php',
    'Advanced_Ads_Utils' => $baseDir . '/classes/utils.php',
    'Advanced_Ads_Visitor_Conditions' => $baseDir . '/classes/visitor-conditions.php',
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'Detection\\MobileDetect' => $vendorDir . '/mobiledetect/mobiledetectlib/src/MobileDetect.php',
    'XML_Encoder' => $baseDir . '/includes/importers/class-xml-encoder.php',
);
