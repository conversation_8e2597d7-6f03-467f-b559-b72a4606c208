<?php
/**
 * This class represents the "Before Content" placement type.
 *
 * @package AdvancedAds
 * <AUTHOR> Ads <<EMAIL>>
 * @since   1.47.0
 */

namespace AdvancedAds\Placements\Types;

use AdvancedAds\Abstracts\Placement_Type as Base;
use AdvancedAds\Interfaces\Placement_Type;
use AdvancedAds\Placements\Placement_Before_Content;

defined( 'ABSPATH' ) || exit;

/**
 * Type Before Content.
 */
class Before_Content extends Base implements Placement_Type {

	/**
	 * Get the unique identifier (ID) of the placement type.
	 *
	 * @return string The unique ID of the placement type.
	 */
	public function get_id(): string {
		return 'post_top';
	}

	/**
	 * Get the class name of the object as a string.
	 *
	 * @return string
	 */
	public function get_classname(): string {
		return Placement_Before_Content::class;
	}

	/**
	 * Get the title or name of the placement type.
	 *
	 * @return string The title of the placement type.
	 */
	public function get_title(): string {
		return __( 'Before Content', 'advanced-ads' );
	}

	/**
	 * Get a description of the placement type.
	 *
	 * @return string The description of the placement type.
	 */
	public function get_description(): string {
		return __( 'Injected before the post content.', 'advanced-ads' );
	}

	/**
	 * Check if this placement type requires premium.
	 *
	 * @return bool True if premium is required; otherwise, false.
	 */
	public function is_premium(): bool {
		return false;
	}

	/**
	 * Get the URL for upgrading to this placement type.
	 *
	 * @return string The upgrade URL for the placement type.
	 */
	public function get_image(): string {
		return ADVADS_BASE_URL . 'admin/assets/img/placements/content-before.png';
	}

	/**
	 * Get order number for this placement type.
	 *
	 * @return int The order number.
	 */
	public function get_order(): int {
		return 5;
	}

	/**
	 * Get options for this placement type.
	 *
	 * @return array The options array.
	 */
	public function get_options(): array {
		return $this->apply_filter_on_options(
			[
				'show_position'    => true,
				'show_lazy_load'   => true,
				'uses_the_content' => true,
				'amp'              => true,
			]
		);
	}
}
