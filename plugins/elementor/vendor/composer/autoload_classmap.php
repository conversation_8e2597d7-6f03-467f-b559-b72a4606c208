<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'ElementorDeps\\Attribute' => $baseDir . '/vendor_prefixed/twig/symfony/polyfill-php80/Resources/stubs/Attribute.php',
    'ElementorDeps\\CURLStringFile' => $baseDir . '/vendor_prefixed/twig/symfony/polyfill-php81/Resources/stubs/CURLStringFile.php',
    'ElementorDeps\\DI\\Annotation\\Inject' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/Annotation/Inject.php',
    'ElementorDeps\\DI\\Annotation\\Injectable' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/Annotation/Injectable.php',
    'ElementorDeps\\DI\\CompiledContainer' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/CompiledContainer.php',
    'ElementorDeps\\DI\\Compiler\\Compiler' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/Compiler/Compiler.php',
    'ElementorDeps\\DI\\Compiler\\ObjectCreationCompiler' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/Compiler/ObjectCreationCompiler.php',
    'ElementorDeps\\DI\\Compiler\\RequestedEntryHolder' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/Compiler/RequestedEntryHolder.php',
    'ElementorDeps\\DI\\Container' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/Container.php',
    'ElementorDeps\\DI\\ContainerBuilder' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/ContainerBuilder.php',
    'ElementorDeps\\DI\\Definition\\ArrayDefinition' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/Definition/ArrayDefinition.php',
    'ElementorDeps\\DI\\Definition\\ArrayDefinitionExtension' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/Definition/ArrayDefinitionExtension.php',
    'ElementorDeps\\DI\\Definition\\AutowireDefinition' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/Definition/AutowireDefinition.php',
    'ElementorDeps\\DI\\Definition\\DecoratorDefinition' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/Definition/DecoratorDefinition.php',
    'ElementorDeps\\DI\\Definition\\Definition' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/Definition/Definition.php',
    'ElementorDeps\\DI\\Definition\\Dumper\\ObjectDefinitionDumper' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/Definition/Dumper/ObjectDefinitionDumper.php',
    'ElementorDeps\\DI\\Definition\\EnvironmentVariableDefinition' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/Definition/EnvironmentVariableDefinition.php',
    'ElementorDeps\\DI\\Definition\\Exception\\InvalidAnnotation' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/Definition/Exception/InvalidAnnotation.php',
    'ElementorDeps\\DI\\Definition\\Exception\\InvalidDefinition' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/Definition/Exception/InvalidDefinition.php',
    'ElementorDeps\\DI\\Definition\\ExtendsPreviousDefinition' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/Definition/ExtendsPreviousDefinition.php',
    'ElementorDeps\\DI\\Definition\\FactoryDefinition' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/Definition/FactoryDefinition.php',
    'ElementorDeps\\DI\\Definition\\Helper\\AutowireDefinitionHelper' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/Definition/Helper/AutowireDefinitionHelper.php',
    'ElementorDeps\\DI\\Definition\\Helper\\CreateDefinitionHelper' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/Definition/Helper/CreateDefinitionHelper.php',
    'ElementorDeps\\DI\\Definition\\Helper\\DefinitionHelper' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/Definition/Helper/DefinitionHelper.php',
    'ElementorDeps\\DI\\Definition\\Helper\\FactoryDefinitionHelper' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/Definition/Helper/FactoryDefinitionHelper.php',
    'ElementorDeps\\DI\\Definition\\InstanceDefinition' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/Definition/InstanceDefinition.php',
    'ElementorDeps\\DI\\Definition\\ObjectDefinition' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/Definition/ObjectDefinition.php',
    'ElementorDeps\\DI\\Definition\\ObjectDefinition\\MethodInjection' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/Definition/ObjectDefinition/MethodInjection.php',
    'ElementorDeps\\DI\\Definition\\ObjectDefinition\\PropertyInjection' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/Definition/ObjectDefinition/PropertyInjection.php',
    'ElementorDeps\\DI\\Definition\\Reference' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/Definition/Reference.php',
    'ElementorDeps\\DI\\Definition\\Resolver\\ArrayResolver' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/Definition/Resolver/ArrayResolver.php',
    'ElementorDeps\\DI\\Definition\\Resolver\\DecoratorResolver' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/Definition/Resolver/DecoratorResolver.php',
    'ElementorDeps\\DI\\Definition\\Resolver\\DefinitionResolver' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/Definition/Resolver/DefinitionResolver.php',
    'ElementorDeps\\DI\\Definition\\Resolver\\EnvironmentVariableResolver' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/Definition/Resolver/EnvironmentVariableResolver.php',
    'ElementorDeps\\DI\\Definition\\Resolver\\FactoryResolver' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/Definition/Resolver/FactoryResolver.php',
    'ElementorDeps\\DI\\Definition\\Resolver\\InstanceInjector' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/Definition/Resolver/InstanceInjector.php',
    'ElementorDeps\\DI\\Definition\\Resolver\\ObjectCreator' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/Definition/Resolver/ObjectCreator.php',
    'ElementorDeps\\DI\\Definition\\Resolver\\ParameterResolver' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/Definition/Resolver/ParameterResolver.php',
    'ElementorDeps\\DI\\Definition\\Resolver\\ResolverDispatcher' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/Definition/Resolver/ResolverDispatcher.php',
    'ElementorDeps\\DI\\Definition\\SelfResolvingDefinition' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/Definition/SelfResolvingDefinition.php',
    'ElementorDeps\\DI\\Definition\\Source\\AnnotationBasedAutowiring' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/Definition/Source/AnnotationBasedAutowiring.php',
    'ElementorDeps\\DI\\Definition\\Source\\Autowiring' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/Definition/Source/Autowiring.php',
    'ElementorDeps\\DI\\Definition\\Source\\DefinitionArray' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/Definition/Source/DefinitionArray.php',
    'ElementorDeps\\DI\\Definition\\Source\\DefinitionFile' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/Definition/Source/DefinitionFile.php',
    'ElementorDeps\\DI\\Definition\\Source\\DefinitionNormalizer' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/Definition/Source/DefinitionNormalizer.php',
    'ElementorDeps\\DI\\Definition\\Source\\DefinitionSource' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/Definition/Source/DefinitionSource.php',
    'ElementorDeps\\DI\\Definition\\Source\\MutableDefinitionSource' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/Definition/Source/MutableDefinitionSource.php',
    'ElementorDeps\\DI\\Definition\\Source\\NoAutowiring' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/Definition/Source/NoAutowiring.php',
    'ElementorDeps\\DI\\Definition\\Source\\ReflectionBasedAutowiring' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/Definition/Source/ReflectionBasedAutowiring.php',
    'ElementorDeps\\DI\\Definition\\Source\\SourceCache' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/Definition/Source/SourceCache.php',
    'ElementorDeps\\DI\\Definition\\Source\\SourceChain' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/Definition/Source/SourceChain.php',
    'ElementorDeps\\DI\\Definition\\StringDefinition' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/Definition/StringDefinition.php',
    'ElementorDeps\\DI\\Definition\\ValueDefinition' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/Definition/ValueDefinition.php',
    'ElementorDeps\\DI\\DependencyException' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/DependencyException.php',
    'ElementorDeps\\DI\\FactoryInterface' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/FactoryInterface.php',
    'ElementorDeps\\DI\\Factory\\RequestedEntry' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/Factory/RequestedEntry.php',
    'ElementorDeps\\DI\\Invoker\\DefinitionParameterResolver' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/Invoker/DefinitionParameterResolver.php',
    'ElementorDeps\\DI\\Invoker\\FactoryParameterResolver' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/Invoker/FactoryParameterResolver.php',
    'ElementorDeps\\DI\\NotFoundException' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/NotFoundException.php',
    'ElementorDeps\\DI\\Proxy\\ProxyFactory' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/php-di/src/Proxy/ProxyFactory.php',
    'ElementorDeps\\Invoker\\CallableResolver' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/invoker/src/CallableResolver.php',
    'ElementorDeps\\Invoker\\Exception\\InvocationException' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/invoker/src/Exception/InvocationException.php',
    'ElementorDeps\\Invoker\\Exception\\NotCallableException' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/invoker/src/Exception/NotCallableException.php',
    'ElementorDeps\\Invoker\\Exception\\NotEnoughParametersException' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/invoker/src/Exception/NotEnoughParametersException.php',
    'ElementorDeps\\Invoker\\Invoker' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/invoker/src/Invoker.php',
    'ElementorDeps\\Invoker\\InvokerInterface' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/invoker/src/InvokerInterface.php',
    'ElementorDeps\\Invoker\\ParameterResolver\\AssociativeArrayResolver' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/invoker/src/ParameterResolver/AssociativeArrayResolver.php',
    'ElementorDeps\\Invoker\\ParameterResolver\\Container\\ParameterNameContainerResolver' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/invoker/src/ParameterResolver/Container/ParameterNameContainerResolver.php',
    'ElementorDeps\\Invoker\\ParameterResolver\\Container\\TypeHintContainerResolver' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/invoker/src/ParameterResolver/Container/TypeHintContainerResolver.php',
    'ElementorDeps\\Invoker\\ParameterResolver\\DefaultValueResolver' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/invoker/src/ParameterResolver/DefaultValueResolver.php',
    'ElementorDeps\\Invoker\\ParameterResolver\\NumericArrayResolver' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/invoker/src/ParameterResolver/NumericArrayResolver.php',
    'ElementorDeps\\Invoker\\ParameterResolver\\ParameterResolver' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/invoker/src/ParameterResolver/ParameterResolver.php',
    'ElementorDeps\\Invoker\\ParameterResolver\\ResolverChain' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/invoker/src/ParameterResolver/ResolverChain.php',
    'ElementorDeps\\Invoker\\ParameterResolver\\TypeHintResolver' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/invoker/src/ParameterResolver/TypeHintResolver.php',
    'ElementorDeps\\Invoker\\Reflection\\CallableReflection' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/invoker/src/Reflection/CallableReflection.php',
    'ElementorDeps\\Laravel\\SerializableClosure\\Contracts\\Serializable' => $baseDir . '/vendor_prefixed/dependency-injection/laravel/serializable-closure/src/Contracts/Serializable.php',
    'ElementorDeps\\Laravel\\SerializableClosure\\Contracts\\Signer' => $baseDir . '/vendor_prefixed/dependency-injection/laravel/serializable-closure/src/Contracts/Signer.php',
    'ElementorDeps\\Laravel\\SerializableClosure\\Exceptions\\InvalidSignatureException' => $baseDir . '/vendor_prefixed/dependency-injection/laravel/serializable-closure/src/Exceptions/InvalidSignatureException.php',
    'ElementorDeps\\Laravel\\SerializableClosure\\Exceptions\\MissingSecretKeyException' => $baseDir . '/vendor_prefixed/dependency-injection/laravel/serializable-closure/src/Exceptions/MissingSecretKeyException.php',
    'ElementorDeps\\Laravel\\SerializableClosure\\Exceptions\\PhpVersionNotSupportedException' => $baseDir . '/vendor_prefixed/dependency-injection/laravel/serializable-closure/src/Exceptions/PhpVersionNotSupportedException.php',
    'ElementorDeps\\Laravel\\SerializableClosure\\SerializableClosure' => $baseDir . '/vendor_prefixed/dependency-injection/laravel/serializable-closure/src/SerializableClosure.php',
    'ElementorDeps\\Laravel\\SerializableClosure\\Serializers\\Native' => $baseDir . '/vendor_prefixed/dependency-injection/laravel/serializable-closure/src/Serializers/Native.php',
    'ElementorDeps\\Laravel\\SerializableClosure\\Serializers\\Signed' => $baseDir . '/vendor_prefixed/dependency-injection/laravel/serializable-closure/src/Serializers/Signed.php',
    'ElementorDeps\\Laravel\\SerializableClosure\\Signers\\Hmac' => $baseDir . '/vendor_prefixed/dependency-injection/laravel/serializable-closure/src/Signers/Hmac.php',
    'ElementorDeps\\Laravel\\SerializableClosure\\Support\\ClosureScope' => $baseDir . '/vendor_prefixed/dependency-injection/laravel/serializable-closure/src/Support/ClosureScope.php',
    'ElementorDeps\\Laravel\\SerializableClosure\\Support\\ClosureStream' => $baseDir . '/vendor_prefixed/dependency-injection/laravel/serializable-closure/src/Support/ClosureStream.php',
    'ElementorDeps\\Laravel\\SerializableClosure\\Support\\ReflectionClosure' => $baseDir . '/vendor_prefixed/dependency-injection/laravel/serializable-closure/src/Support/ReflectionClosure.php',
    'ElementorDeps\\Laravel\\SerializableClosure\\Support\\SelfReference' => $baseDir . '/vendor_prefixed/dependency-injection/laravel/serializable-closure/src/Support/SelfReference.php',
    'ElementorDeps\\Laravel\\SerializableClosure\\UnsignedSerializableClosure' => $baseDir . '/vendor_prefixed/dependency-injection/laravel/serializable-closure/src/UnsignedSerializableClosure.php',
    'ElementorDeps\\PhpDocReader\\AnnotationException' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/phpdoc-reader/src/PhpDocReader/AnnotationException.php',
    'ElementorDeps\\PhpDocReader\\PhpDocReader' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/phpdoc-reader/src/PhpDocReader/PhpDocReader.php',
    'ElementorDeps\\PhpDocReader\\PhpParser\\TokenParser' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/phpdoc-reader/src/PhpDocReader/PhpParser/TokenParser.php',
    'ElementorDeps\\PhpDocReader\\PhpParser\\UseStatementParser' => $baseDir . '/vendor_prefixed/dependency-injection/php-di/phpdoc-reader/src/PhpDocReader/PhpParser/UseStatementParser.php',
    'ElementorDeps\\PhpToken' => $baseDir . '/vendor_prefixed/twig/symfony/polyfill-php80/Resources/stubs/PhpToken.php',
    'ElementorDeps\\Psr\\Container\\ContainerExceptionInterface' => $baseDir . '/vendor_prefixed/dependency-injection/psr/container/src/ContainerExceptionInterface.php',
    'ElementorDeps\\Psr\\Container\\ContainerInterface' => $baseDir . '/vendor_prefixed/dependency-injection/psr/container/src/ContainerInterface.php',
    'ElementorDeps\\Psr\\Container\\NotFoundExceptionInterface' => $baseDir . '/vendor_prefixed/dependency-injection/psr/container/src/NotFoundExceptionInterface.php',
    'ElementorDeps\\ReturnTypeWillChange' => $baseDir . '/vendor_prefixed/twig/symfony/polyfill-php81/Resources/stubs/ReturnTypeWillChange.php',
    'ElementorDeps\\Stringable' => $baseDir . '/vendor_prefixed/twig/symfony/polyfill-php80/Resources/stubs/Stringable.php',
    'ElementorDeps\\Symfony\\Polyfill\\Ctype\\Ctype' => $baseDir . '/vendor_prefixed/twig/symfony/polyfill-ctype/Ctype.php',
    'ElementorDeps\\Symfony\\Polyfill\\Mbstring\\Mbstring' => $baseDir . '/vendor_prefixed/twig/symfony/polyfill-mbstring/Mbstring.php',
    'ElementorDeps\\Symfony\\Polyfill\\Php80\\Php80' => $baseDir . '/vendor_prefixed/twig/symfony/polyfill-php80/Php80.php',
    'ElementorDeps\\Symfony\\Polyfill\\Php80\\PhpToken' => $baseDir . '/vendor_prefixed/twig/symfony/polyfill-php80/PhpToken.php',
    'ElementorDeps\\Symfony\\Polyfill\\Php81\\Php81' => $baseDir . '/vendor_prefixed/twig/symfony/polyfill-php81/Php81.php',
    'ElementorDeps\\Twig\\Attribute\\YieldReady' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Attribute/YieldReady.php',
    'ElementorDeps\\Twig\\Cache\\CacheInterface' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Cache/CacheInterface.php',
    'ElementorDeps\\Twig\\Cache\\ChainCache' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Cache/ChainCache.php',
    'ElementorDeps\\Twig\\Cache\\FilesystemCache' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Cache/FilesystemCache.php',
    'ElementorDeps\\Twig\\Cache\\NullCache' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Cache/NullCache.php',
    'ElementorDeps\\Twig\\Cache\\ReadOnlyFilesystemCache' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Cache/ReadOnlyFilesystemCache.php',
    'ElementorDeps\\Twig\\Compiler' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Compiler.php',
    'ElementorDeps\\Twig\\Environment' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Environment.php',
    'ElementorDeps\\Twig\\Error\\Error' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Error/Error.php',
    'ElementorDeps\\Twig\\Error\\LoaderError' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Error/LoaderError.php',
    'ElementorDeps\\Twig\\Error\\RuntimeError' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Error/RuntimeError.php',
    'ElementorDeps\\Twig\\Error\\SyntaxError' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Error/SyntaxError.php',
    'ElementorDeps\\Twig\\ExpressionParser' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/ExpressionParser.php',
    'ElementorDeps\\Twig\\ExtensionSet' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/ExtensionSet.php',
    'ElementorDeps\\Twig\\Extension\\AbstractExtension' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Extension/AbstractExtension.php',
    'ElementorDeps\\Twig\\Extension\\CoreExtension' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Extension/CoreExtension.php',
    'ElementorDeps\\Twig\\Extension\\DebugExtension' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Extension/DebugExtension.php',
    'ElementorDeps\\Twig\\Extension\\EscaperExtension' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Extension/EscaperExtension.php',
    'ElementorDeps\\Twig\\Extension\\ExtensionInterface' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Extension/ExtensionInterface.php',
    'ElementorDeps\\Twig\\Extension\\GlobalsInterface' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Extension/GlobalsInterface.php',
    'ElementorDeps\\Twig\\Extension\\OptimizerExtension' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Extension/OptimizerExtension.php',
    'ElementorDeps\\Twig\\Extension\\ProfilerExtension' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Extension/ProfilerExtension.php',
    'ElementorDeps\\Twig\\Extension\\RuntimeExtensionInterface' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Extension/RuntimeExtensionInterface.php',
    'ElementorDeps\\Twig\\Extension\\SandboxExtension' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Extension/SandboxExtension.php',
    'ElementorDeps\\Twig\\Extension\\StagingExtension' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Extension/StagingExtension.php',
    'ElementorDeps\\Twig\\Extension\\StringLoaderExtension' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Extension/StringLoaderExtension.php',
    'ElementorDeps\\Twig\\Extension\\YieldNotReadyExtension' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Extension/YieldNotReadyExtension.php',
    'ElementorDeps\\Twig\\FileExtensionEscapingStrategy' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/FileExtensionEscapingStrategy.php',
    'ElementorDeps\\Twig\\Lexer' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Lexer.php',
    'ElementorDeps\\Twig\\Loader\\ArrayLoader' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Loader/ArrayLoader.php',
    'ElementorDeps\\Twig\\Loader\\ChainLoader' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Loader/ChainLoader.php',
    'ElementorDeps\\Twig\\Loader\\FilesystemLoader' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Loader/FilesystemLoader.php',
    'ElementorDeps\\Twig\\Loader\\LoaderInterface' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Loader/LoaderInterface.php',
    'ElementorDeps\\Twig\\Markup' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Markup.php',
    'ElementorDeps\\Twig\\NodeTraverser' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/NodeTraverser.php',
    'ElementorDeps\\Twig\\NodeVisitor\\AbstractNodeVisitor' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/NodeVisitor/AbstractNodeVisitor.php',
    'ElementorDeps\\Twig\\NodeVisitor\\EscaperNodeVisitor' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/NodeVisitor/EscaperNodeVisitor.php',
    'ElementorDeps\\Twig\\NodeVisitor\\MacroAutoImportNodeVisitor' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/NodeVisitor/MacroAutoImportNodeVisitor.php',
    'ElementorDeps\\Twig\\NodeVisitor\\NodeVisitorInterface' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/NodeVisitor/NodeVisitorInterface.php',
    'ElementorDeps\\Twig\\NodeVisitor\\OptimizerNodeVisitor' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/NodeVisitor/OptimizerNodeVisitor.php',
    'ElementorDeps\\Twig\\NodeVisitor\\SafeAnalysisNodeVisitor' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/NodeVisitor/SafeAnalysisNodeVisitor.php',
    'ElementorDeps\\Twig\\NodeVisitor\\SandboxNodeVisitor' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/NodeVisitor/SandboxNodeVisitor.php',
    'ElementorDeps\\Twig\\NodeVisitor\\YieldNotReadyNodeVisitor' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/NodeVisitor/YieldNotReadyNodeVisitor.php',
    'ElementorDeps\\Twig\\Node\\AutoEscapeNode' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/AutoEscapeNode.php',
    'ElementorDeps\\Twig\\Node\\BlockNode' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/BlockNode.php',
    'ElementorDeps\\Twig\\Node\\BlockReferenceNode' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/BlockReferenceNode.php',
    'ElementorDeps\\Twig\\Node\\BodyNode' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/BodyNode.php',
    'ElementorDeps\\Twig\\Node\\CaptureNode' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/CaptureNode.php',
    'ElementorDeps\\Twig\\Node\\CheckSecurityCallNode' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/CheckSecurityCallNode.php',
    'ElementorDeps\\Twig\\Node\\CheckSecurityNode' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/CheckSecurityNode.php',
    'ElementorDeps\\Twig\\Node\\CheckToStringNode' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/CheckToStringNode.php',
    'ElementorDeps\\Twig\\Node\\DeprecatedNode' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/DeprecatedNode.php',
    'ElementorDeps\\Twig\\Node\\DoNode' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/DoNode.php',
    'ElementorDeps\\Twig\\Node\\EmbedNode' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/EmbedNode.php',
    'ElementorDeps\\Twig\\Node\\Expression\\AbstractExpression' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/AbstractExpression.php',
    'ElementorDeps\\Twig\\Node\\Expression\\ArrayExpression' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/ArrayExpression.php',
    'ElementorDeps\\Twig\\Node\\Expression\\ArrowFunctionExpression' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/ArrowFunctionExpression.php',
    'ElementorDeps\\Twig\\Node\\Expression\\AssignNameExpression' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/AssignNameExpression.php',
    'ElementorDeps\\Twig\\Node\\Expression\\Binary\\AbstractBinary' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/Binary/AbstractBinary.php',
    'ElementorDeps\\Twig\\Node\\Expression\\Binary\\AddBinary' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/Binary/AddBinary.php',
    'ElementorDeps\\Twig\\Node\\Expression\\Binary\\AndBinary' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/Binary/AndBinary.php',
    'ElementorDeps\\Twig\\Node\\Expression\\Binary\\BitwiseAndBinary' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/Binary/BitwiseAndBinary.php',
    'ElementorDeps\\Twig\\Node\\Expression\\Binary\\BitwiseOrBinary' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/Binary/BitwiseOrBinary.php',
    'ElementorDeps\\Twig\\Node\\Expression\\Binary\\BitwiseXorBinary' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/Binary/BitwiseXorBinary.php',
    'ElementorDeps\\Twig\\Node\\Expression\\Binary\\ConcatBinary' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/Binary/ConcatBinary.php',
    'ElementorDeps\\Twig\\Node\\Expression\\Binary\\DivBinary' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/Binary/DivBinary.php',
    'ElementorDeps\\Twig\\Node\\Expression\\Binary\\EndsWithBinary' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/Binary/EndsWithBinary.php',
    'ElementorDeps\\Twig\\Node\\Expression\\Binary\\EqualBinary' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/Binary/EqualBinary.php',
    'ElementorDeps\\Twig\\Node\\Expression\\Binary\\FloorDivBinary' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/Binary/FloorDivBinary.php',
    'ElementorDeps\\Twig\\Node\\Expression\\Binary\\GreaterBinary' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/Binary/GreaterBinary.php',
    'ElementorDeps\\Twig\\Node\\Expression\\Binary\\GreaterEqualBinary' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/Binary/GreaterEqualBinary.php',
    'ElementorDeps\\Twig\\Node\\Expression\\Binary\\HasEveryBinary' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/Binary/HasEveryBinary.php',
    'ElementorDeps\\Twig\\Node\\Expression\\Binary\\HasSomeBinary' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/Binary/HasSomeBinary.php',
    'ElementorDeps\\Twig\\Node\\Expression\\Binary\\InBinary' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/Binary/InBinary.php',
    'ElementorDeps\\Twig\\Node\\Expression\\Binary\\LessBinary' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/Binary/LessBinary.php',
    'ElementorDeps\\Twig\\Node\\Expression\\Binary\\LessEqualBinary' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/Binary/LessEqualBinary.php',
    'ElementorDeps\\Twig\\Node\\Expression\\Binary\\MatchesBinary' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/Binary/MatchesBinary.php',
    'ElementorDeps\\Twig\\Node\\Expression\\Binary\\ModBinary' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/Binary/ModBinary.php',
    'ElementorDeps\\Twig\\Node\\Expression\\Binary\\MulBinary' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/Binary/MulBinary.php',
    'ElementorDeps\\Twig\\Node\\Expression\\Binary\\NotEqualBinary' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/Binary/NotEqualBinary.php',
    'ElementorDeps\\Twig\\Node\\Expression\\Binary\\NotInBinary' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/Binary/NotInBinary.php',
    'ElementorDeps\\Twig\\Node\\Expression\\Binary\\OrBinary' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/Binary/OrBinary.php',
    'ElementorDeps\\Twig\\Node\\Expression\\Binary\\PowerBinary' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/Binary/PowerBinary.php',
    'ElementorDeps\\Twig\\Node\\Expression\\Binary\\RangeBinary' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/Binary/RangeBinary.php',
    'ElementorDeps\\Twig\\Node\\Expression\\Binary\\SpaceshipBinary' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/Binary/SpaceshipBinary.php',
    'ElementorDeps\\Twig\\Node\\Expression\\Binary\\StartsWithBinary' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/Binary/StartsWithBinary.php',
    'ElementorDeps\\Twig\\Node\\Expression\\Binary\\SubBinary' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/Binary/SubBinary.php',
    'ElementorDeps\\Twig\\Node\\Expression\\BlockReferenceExpression' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/BlockReferenceExpression.php',
    'ElementorDeps\\Twig\\Node\\Expression\\CallExpression' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/CallExpression.php',
    'ElementorDeps\\Twig\\Node\\Expression\\ConditionalExpression' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/ConditionalExpression.php',
    'ElementorDeps\\Twig\\Node\\Expression\\ConstantExpression' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/ConstantExpression.php',
    'ElementorDeps\\Twig\\Node\\Expression\\FilterExpression' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/FilterExpression.php',
    'ElementorDeps\\Twig\\Node\\Expression\\Filter\\DefaultFilter' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/Filter/DefaultFilter.php',
    'ElementorDeps\\Twig\\Node\\Expression\\Filter\\RawFilter' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/Filter/RawFilter.php',
    'ElementorDeps\\Twig\\Node\\Expression\\FunctionExpression' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/FunctionExpression.php',
    'ElementorDeps\\Twig\\Node\\Expression\\GetAttrExpression' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/GetAttrExpression.php',
    'ElementorDeps\\Twig\\Node\\Expression\\InlinePrint' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/InlinePrint.php',
    'ElementorDeps\\Twig\\Node\\Expression\\MethodCallExpression' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/MethodCallExpression.php',
    'ElementorDeps\\Twig\\Node\\Expression\\NameExpression' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/NameExpression.php',
    'ElementorDeps\\Twig\\Node\\Expression\\NullCoalesceExpression' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/NullCoalesceExpression.php',
    'ElementorDeps\\Twig\\Node\\Expression\\ParentExpression' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/ParentExpression.php',
    'ElementorDeps\\Twig\\Node\\Expression\\TempNameExpression' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/TempNameExpression.php',
    'ElementorDeps\\Twig\\Node\\Expression\\TestExpression' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/TestExpression.php',
    'ElementorDeps\\Twig\\Node\\Expression\\Test\\ConstantTest' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/Test/ConstantTest.php',
    'ElementorDeps\\Twig\\Node\\Expression\\Test\\DefinedTest' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/Test/DefinedTest.php',
    'ElementorDeps\\Twig\\Node\\Expression\\Test\\DivisiblebyTest' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/Test/DivisiblebyTest.php',
    'ElementorDeps\\Twig\\Node\\Expression\\Test\\EvenTest' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/Test/EvenTest.php',
    'ElementorDeps\\Twig\\Node\\Expression\\Test\\NullTest' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/Test/NullTest.php',
    'ElementorDeps\\Twig\\Node\\Expression\\Test\\OddTest' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/Test/OddTest.php',
    'ElementorDeps\\Twig\\Node\\Expression\\Test\\SameasTest' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/Test/SameasTest.php',
    'ElementorDeps\\Twig\\Node\\Expression\\Unary\\AbstractUnary' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/Unary/AbstractUnary.php',
    'ElementorDeps\\Twig\\Node\\Expression\\Unary\\NegUnary' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/Unary/NegUnary.php',
    'ElementorDeps\\Twig\\Node\\Expression\\Unary\\NotUnary' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/Unary/NotUnary.php',
    'ElementorDeps\\Twig\\Node\\Expression\\Unary\\PosUnary' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/Unary/PosUnary.php',
    'ElementorDeps\\Twig\\Node\\Expression\\VariadicExpression' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Expression/VariadicExpression.php',
    'ElementorDeps\\Twig\\Node\\FlushNode' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/FlushNode.php',
    'ElementorDeps\\Twig\\Node\\ForLoopNode' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/ForLoopNode.php',
    'ElementorDeps\\Twig\\Node\\ForNode' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/ForNode.php',
    'ElementorDeps\\Twig\\Node\\IfNode' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/IfNode.php',
    'ElementorDeps\\Twig\\Node\\ImportNode' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/ImportNode.php',
    'ElementorDeps\\Twig\\Node\\IncludeNode' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/IncludeNode.php',
    'ElementorDeps\\Twig\\Node\\MacroNode' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/MacroNode.php',
    'ElementorDeps\\Twig\\Node\\ModuleNode' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/ModuleNode.php',
    'ElementorDeps\\Twig\\Node\\NameDeprecation' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/NameDeprecation.php',
    'ElementorDeps\\Twig\\Node\\Node' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/Node.php',
    'ElementorDeps\\Twig\\Node\\NodeCaptureInterface' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/NodeCaptureInterface.php',
    'ElementorDeps\\Twig\\Node\\NodeOutputInterface' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/NodeOutputInterface.php',
    'ElementorDeps\\Twig\\Node\\PrintNode' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/PrintNode.php',
    'ElementorDeps\\Twig\\Node\\SandboxNode' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/SandboxNode.php',
    'ElementorDeps\\Twig\\Node\\SetNode' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/SetNode.php',
    'ElementorDeps\\Twig\\Node\\TextNode' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/TextNode.php',
    'ElementorDeps\\Twig\\Node\\WithNode' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Node/WithNode.php',
    'ElementorDeps\\Twig\\Parser' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Parser.php',
    'ElementorDeps\\Twig\\Profiler\\Dumper\\BaseDumper' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Profiler/Dumper/BaseDumper.php',
    'ElementorDeps\\Twig\\Profiler\\Dumper\\BlackfireDumper' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Profiler/Dumper/BlackfireDumper.php',
    'ElementorDeps\\Twig\\Profiler\\Dumper\\HtmlDumper' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Profiler/Dumper/HtmlDumper.php',
    'ElementorDeps\\Twig\\Profiler\\Dumper\\TextDumper' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Profiler/Dumper/TextDumper.php',
    'ElementorDeps\\Twig\\Profiler\\NodeVisitor\\ProfilerNodeVisitor' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Profiler/NodeVisitor/ProfilerNodeVisitor.php',
    'ElementorDeps\\Twig\\Profiler\\Node\\EnterProfileNode' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Profiler/Node/EnterProfileNode.php',
    'ElementorDeps\\Twig\\Profiler\\Node\\LeaveProfileNode' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Profiler/Node/LeaveProfileNode.php',
    'ElementorDeps\\Twig\\Profiler\\Profile' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Profiler/Profile.php',
    'ElementorDeps\\Twig\\RuntimeLoader\\ContainerRuntimeLoader' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/RuntimeLoader/ContainerRuntimeLoader.php',
    'ElementorDeps\\Twig\\RuntimeLoader\\FactoryRuntimeLoader' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/RuntimeLoader/FactoryRuntimeLoader.php',
    'ElementorDeps\\Twig\\RuntimeLoader\\RuntimeLoaderInterface' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/RuntimeLoader/RuntimeLoaderInterface.php',
    'ElementorDeps\\Twig\\Runtime\\EscaperRuntime' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Runtime/EscaperRuntime.php',
    'ElementorDeps\\Twig\\Sandbox\\SecurityError' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Sandbox/SecurityError.php',
    'ElementorDeps\\Twig\\Sandbox\\SecurityNotAllowedFilterError' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Sandbox/SecurityNotAllowedFilterError.php',
    'ElementorDeps\\Twig\\Sandbox\\SecurityNotAllowedFunctionError' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Sandbox/SecurityNotAllowedFunctionError.php',
    'ElementorDeps\\Twig\\Sandbox\\SecurityNotAllowedMethodError' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Sandbox/SecurityNotAllowedMethodError.php',
    'ElementorDeps\\Twig\\Sandbox\\SecurityNotAllowedPropertyError' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Sandbox/SecurityNotAllowedPropertyError.php',
    'ElementorDeps\\Twig\\Sandbox\\SecurityNotAllowedTagError' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Sandbox/SecurityNotAllowedTagError.php',
    'ElementorDeps\\Twig\\Sandbox\\SecurityPolicy' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Sandbox/SecurityPolicy.php',
    'ElementorDeps\\Twig\\Sandbox\\SecurityPolicyInterface' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Sandbox/SecurityPolicyInterface.php',
    'ElementorDeps\\Twig\\Sandbox\\SourcePolicyInterface' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Sandbox/SourcePolicyInterface.php',
    'ElementorDeps\\Twig\\Source' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Source.php',
    'ElementorDeps\\Twig\\Template' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Template.php',
    'ElementorDeps\\Twig\\TemplateWrapper' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/TemplateWrapper.php',
    'ElementorDeps\\Twig\\Test\\IntegrationTestCase' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Test/IntegrationTestCase.php',
    'ElementorDeps\\Twig\\Test\\NodeTestCase' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Test/NodeTestCase.php',
    'ElementorDeps\\Twig\\Token' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Token.php',
    'ElementorDeps\\Twig\\TokenParser\\AbstractTokenParser' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/TokenParser/AbstractTokenParser.php',
    'ElementorDeps\\Twig\\TokenParser\\ApplyTokenParser' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/TokenParser/ApplyTokenParser.php',
    'ElementorDeps\\Twig\\TokenParser\\AutoEscapeTokenParser' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/TokenParser/AutoEscapeTokenParser.php',
    'ElementorDeps\\Twig\\TokenParser\\BlockTokenParser' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/TokenParser/BlockTokenParser.php',
    'ElementorDeps\\Twig\\TokenParser\\DeprecatedTokenParser' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/TokenParser/DeprecatedTokenParser.php',
    'ElementorDeps\\Twig\\TokenParser\\DoTokenParser' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/TokenParser/DoTokenParser.php',
    'ElementorDeps\\Twig\\TokenParser\\EmbedTokenParser' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/TokenParser/EmbedTokenParser.php',
    'ElementorDeps\\Twig\\TokenParser\\ExtendsTokenParser' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/TokenParser/ExtendsTokenParser.php',
    'ElementorDeps\\Twig\\TokenParser\\FlushTokenParser' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/TokenParser/FlushTokenParser.php',
    'ElementorDeps\\Twig\\TokenParser\\ForTokenParser' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/TokenParser/ForTokenParser.php',
    'ElementorDeps\\Twig\\TokenParser\\FromTokenParser' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/TokenParser/FromTokenParser.php',
    'ElementorDeps\\Twig\\TokenParser\\IfTokenParser' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/TokenParser/IfTokenParser.php',
    'ElementorDeps\\Twig\\TokenParser\\ImportTokenParser' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/TokenParser/ImportTokenParser.php',
    'ElementorDeps\\Twig\\TokenParser\\IncludeTokenParser' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/TokenParser/IncludeTokenParser.php',
    'ElementorDeps\\Twig\\TokenParser\\MacroTokenParser' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/TokenParser/MacroTokenParser.php',
    'ElementorDeps\\Twig\\TokenParser\\SandboxTokenParser' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/TokenParser/SandboxTokenParser.php',
    'ElementorDeps\\Twig\\TokenParser\\SetTokenParser' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/TokenParser/SetTokenParser.php',
    'ElementorDeps\\Twig\\TokenParser\\TokenParserInterface' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/TokenParser/TokenParserInterface.php',
    'ElementorDeps\\Twig\\TokenParser\\UseTokenParser' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/TokenParser/UseTokenParser.php',
    'ElementorDeps\\Twig\\TokenParser\\WithTokenParser' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/TokenParser/WithTokenParser.php',
    'ElementorDeps\\Twig\\TokenStream' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/TokenStream.php',
    'ElementorDeps\\Twig\\TwigFilter' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/TwigFilter.php',
    'ElementorDeps\\Twig\\TwigFunction' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/TwigFunction.php',
    'ElementorDeps\\Twig\\TwigTest' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/TwigTest.php',
    'ElementorDeps\\Twig\\Util\\DeprecationCollector' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Util/DeprecationCollector.php',
    'ElementorDeps\\Twig\\Util\\ReflectionCallable' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Util/ReflectionCallable.php',
    'ElementorDeps\\Twig\\Util\\TemplateDirIterator' => $baseDir . '/vendor_prefixed/twig/twig/twig/src/Util/TemplateDirIterator.php',
    'ElementorDeps\\UnhandledMatchError' => $baseDir . '/vendor_prefixed/twig/symfony/polyfill-php80/Resources/stubs/UnhandledMatchError.php',
    'ElementorDeps\\ValueError' => $baseDir . '/vendor_prefixed/twig/symfony/polyfill-php80/Resources/stubs/ValueError.php',
);
