.e-ai-button {
  background: none;
  border: none;
  font-size: var(--control-title-size);
  cursor: pointer;
  transition: var(--e-a-transition-hover);
}
.e-ai-button:not(.e-active) {
  color: var(--e-a-color-primary-bold);
}
.e-ai-button:hover {
  color: #E73CF6;
}

.e-ai-promotion {
  font-size: var(--control-title-size);
}
.e-ai-promotion i {
  margin-inline-end: 5px;
}

.elementor-label-block .e-ai-promotion,
.elementor-label-block .e-ai-button {
  margin-inline-start: auto;
}

.e-ai-border-button {
  border-radius: var(--e-a-border-radius);
  border: var(--e-a-border-bold);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 27px;
  height: 27px;
}

/*# sourceMappingURL=editor.css.map */