#e-announcements-root {
  position: fixed;
  display: flex;
  inset: 0;
  align-items: center;
  justify-content: center;
  color: var(--e-a-color-txt);
  font-family: Roboto, Arial, Helvetica, sans-serif;
  z-index: 9998;
}
#e-announcements-root .announcements-container {
  width: 640px;
  max-width: 100%;
  background-color: var(--e-a-bg-default);
  border-radius: var(--e-a-border-radius);
  z-index: 9998;
}
#e-announcements-root .announcements-container .announcements-heading-container {
  display: flex;
  align-items: center;
  padding: 16px 32px;
  border-block-end: var(--e-a-border);
}
#e-announcements-root .announcements-container .announcements-heading-container .eicon-elementor {
  color: var(--e-a-color-logo);
  background-color: var(--e-a-bg-logo);
  border-radius: 50%;
  padding: 8px;
}
#e-announcements-root .announcements-container .announcements-heading-container .heading-title {
  margin-inline-start: 12px;
  text-transform: uppercase;
  font-weight: 700;
  color: var(--e-a-color-txt-active);
}
#e-announcements-root .announcements-container .announcements-heading-container .close-button {
  margin-inline-start: auto;
  background: none;
  color: inherit;
  border: none;
  padding: 0;
  font: inherit;
  cursor: pointer;
  outline: inherit;
}
#e-announcements-root .announcements-container .announcements-heading-container .close-button:hover {
  color: var(--e-a-color-txt-hover);
}
#e-announcements-root .announcements-container .announcement-body-container .announcement-body-media img {
  width: 100%;
  max-height: 360px;
  object-fit: contain;
}
#e-announcements-root .announcements-container .announcement-body-container .announcement-body-content {
  padding: 30px;
}
#e-announcements-root .announcements-container .announcement-body-container .announcement-body-content .announcement-body-title {
  font-size: 18px;
  font-weight: 700;
  color: var(--e-a-color-txt-accent);
  padding-block-end: 15px;
}
#e-announcements-root .announcements-container .announcement-body-container .announcement-body-content .announcement-body-description {
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;
  color: var(--e-a-color-txt);
}
#e-announcements-root .announcements-container .announcement-body-container .announcement-body-content p {
  margin-block-end: 15px;
}
#e-announcements-root .announcements-container .announcement-body-container .announcement-body-content ul {
  margin-inline-start: 20px;
  margin-block-end: 15px;
}
#e-announcements-root .announcements-container .announcement-body-container .announcement-body-content li {
  list-style: disc;
  margin-block-end: 5px;
}
#e-announcements-root .announcements-container .announcement-footer-container {
  display: flex;
  flex-direction: row-reverse;
  padding: 12px 32px 32px;
}
#e-announcements-root .announcements-container .announcement-footer-container .button-item {
  margin-inline-start: 12px;
  padding: 8px 16px;
  outline: none;
  border: none;
  border-radius: var(--e-a-border-radius);
  transition: var(--e-a-transition-hover);
  background: transparent;
  color: var(--e-a-color-txt);
  cursor: pointer;
}
#e-announcements-root .announcements-container .announcement-footer-container .button-item:hover {
  background: var(--e-a-bg-hover);
  color: var(--e-a-color-txt-hover);
}
#e-announcements-root .announcements-container .announcement-footer-container .button-item:disabled {
  background: transparent;
  color: var(--e-a-color-txt-disabled);
}
#e-announcements-root .announcements-container .announcement-footer-container .button-item.primary {
  background-color: var(--e-a-btn-bg-accent);
  color: var(--e-a-btn-color-invert);
}
#e-announcements-root .announcements-container .announcement-footer-container .button-item.primary:hover {
  background-color: var(--e-a-btn-bg-accent-hover);
}
#e-announcements-root .announcements-container .announcement-footer-container .button-item.primary:active {
  background-color: var(--e-a-btn-bg-accent-active);
}
#e-announcements-root .announcements-screen-overlay {
  position: fixed;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
}

/*# sourceMappingURL=announcements.css.map */