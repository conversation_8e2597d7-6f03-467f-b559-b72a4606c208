/*! elementor - v3.29.0 - 04-06-2025 */
"use strict";(self.webpackChunkelementorFrontend=self.webpackChunkelementorFrontend||[]).push([[707],{7243:(e,t,s)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,s(4846),s(6211);class Alert extends elementorModules.frontend.handlers.Base{getDefaultSettings(){return{selectors:{dismissButton:".elementor-alert-dismiss"}}}getDefaultElements(){const e=this.getSettings("selectors");return{$dismissButton:this.$element.find(e.dismissButton)}}bindEvents(){this.elements.$dismissButton.on("click",this.onDismissButtonClick.bind(this))}onDismissButtonClick(){this.$element.fadeOut()}}t.default=Alert}}]);