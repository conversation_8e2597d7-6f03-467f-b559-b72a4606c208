/*! elementor - v3.29.0 - 04-06-2025 */
(()=>{var e={12470:e=>{"use strict";e.exports=wp.i18n},39805:e=>{e.exports=function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},e.exports.__esModule=!0,e.exports.default=e.exports},40989:(e,t,r)=>{var o=r(45498);function _defineProperties(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,o(i.key),i)}}e.exports=function _createClass(e,t,r){return t&&_defineProperties(e.prototype,t),r&&_defineProperties(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e},e.exports.__esModule=!0,e.exports.default=e.exports},85707:(e,t,r)=>{var o=r(45498);e.exports=function _defineProperty(e,t,r){return(t=o(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e},e.exports.__esModule=!0,e.exports.default=e.exports},96784:e=>{e.exports=function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports},11327:(e,t,r)=>{var o=r(10564).default;e.exports=function toPrimitive(e,t){if("object"!=o(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var i=r.call(e,t||"default");if("object"!=o(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)},e.exports.__esModule=!0,e.exports.default=e.exports},45498:(e,t,r)=>{var o=r(10564).default,i=r(11327);e.exports=function toPropertyKey(e){var t=i(e,"string");return"symbol"==o(t)?t:t+""},e.exports.__esModule=!0,e.exports.default=e.exports},10564:e=>{function _typeof(t){return e.exports=_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,_typeof(t)}e.exports=_typeof,e.exports.__esModule=!0,e.exports.default=e.exports}},t={};function __webpack_require__(r){var o=t[r];if(void 0!==o)return o.exports;var i=t[r]={exports:{}};return e[r](i,i.exports,__webpack_require__),i.exports}(()=>{"use strict";var e=__webpack_require__(12470).__,t=__webpack_require__(96784),r=t(__webpack_require__(39805)),o=t(__webpack_require__(40989)),i=t(__webpack_require__(85707)),n=function(){return(0,o.default)((function Admin(){(0,r.default)(this,Admin),(0,i.default)(this,"KIT_DATA_KEY","elementor-kit-data"),(0,i.default)(this,"cachedKitData",void 0),(0,i.default)(this,"revertButton",void 0),(0,i.default)(this,"activeKitName",void 0),this.activeKitName=this.getActiveKitName(),this.revertButton=document.getElementById("elementor-import-export__revert_kit"),this.revertButton&&(this.revertButton.addEventListener("click",this.onRevertButtonClick.bind(this)),this.maybeAddRevertBtnMargin()),this.maybeShowReferrerKitDialog()}),[{key:"maybeAddRevertBtnMargin",value:function maybeAddRevertBtnMargin(){new URLSearchParams(this.revertButton.href).get("referrer_kit")&&(this.revertButton.style.marginBottom=this.calculateMargin(),this.scrollToBottom())}},{key:"calculateMargin",value:function calculateMargin(){var e=document.getElementById("wpadminbar"),t=e?e.offsetHeight:0,r=this.revertButton.parentElement.offsetHeight;return document.body.clientHeight-t-r-document.getElementById("wpfooter").offsetHeight-15+"px"}},{key:"scrollToBottom",value:function scrollToBottom(){setTimeout((function(){window.scrollTo(0,document.body.scrollHeight)}))}},{key:"onRevertButtonClick",value:function onRevertButtonClick(t){var r=this;t.preventDefault(),elementorCommon.dialogsManager.createWidget("confirm",{headerMessage:e("Are you sure?","elementor"),message:e("Removing %s will permanently delete changes made to the Kit's content and site settings","elementor").replace("%s",this.activeKitName),strings:{confirm:e("Delete","elementor"),cancel:e("Cancel","elementor")},onConfirm:function onConfirm(){return r.onRevertConfirm()}}).show()}},{key:"onRevertConfirm",value:function onRevertConfirm(){var e=new URLSearchParams(this.revertButton.href).get("referrer_kit");this.saveToCache(null!=e?e:""),location.href=this.revertButton.href}},{key:"maybeShowReferrerKitDialog",value:function maybeShowReferrerKitDialog(){var t=this.getDataFromCache().referrerKitId;if(void 0!==t){if(0===t.length)return this.createKitDeletedWidget({message:e("Try a different Kit or build your site from scratch.","elementor"),strings:{confirm:e("OK","elementor"),cancel:e("Kit Library","elementor")},onCancel:function onCancel(){location.href=elementorImportExport.appUrl}}),void this.clearCache();this.createKitDeletedWidget({message:e("You're ready to apply a new Kit!","elementor"),strings:{confirm:e("Continue to new Kit","elementor"),cancel:e("Close","elementor")},onConfirm:function onConfirm(){location.href=elementorImportExport.appUrl+"/preview/"+t}}),this.clearCache()}}},{key:"createKitDeletedWidget",value:function createKitDeletedWidget(t){var r=this.getDataFromCache().activeKitName;elementorCommon.dialogsManager.createWidget("confirm",{id:"e-revert-kit-deleted-dialog",headerMessage:e("%s was successfully deleted","elementor").replace("%s",r),message:t.message,strings:{confirm:t.strings.confirm,cancel:t.strings.cancel},onConfirm:t.onConfirm,onCancel:t.onCancel}).show()}},{key:"getActiveKitName",value:function getActiveKitName(){var t=elementorImportExport.lastImportedSession;return t.kit_title?t.kit_title:t.kit_name?this.convertNameToTitle(t.kit_name):e("Your Kit","elementor")}},{key:"convertNameToTitle",value:function convertNameToTitle(e){return e.split(/[-_]+/).map((function(e){return e[0].toUpperCase()+e.substring(1)})).join(" ")}},{key:"saveToCache",value:function saveToCache(e){sessionStorage.setItem(this.KIT_DATA_KEY,JSON.stringify({referrerKitId:e,activeKitName:this.activeKitName}))}},{key:"getDataFromCache",value:function getDataFromCache(){var e;if(this.cachedKitData)return this.cachedKitData;try{this.cachedKitData=JSON.parse(sessionStorage.getItem(this.KIT_DATA_KEY))}catch(e){return{}}return null!==(e=this.cachedKitData)&&void 0!==e?e:{}}},{key:"clearCache",value:function clearCache(){sessionStorage.removeItem(this.KIT_DATA_KEY)}}])}();window.addEventListener("load",(function(){new n}))})()})();