/*! elementor - v3.29.0 - 04-06-2025 */
(()=>{var t={85707:(t,e,n)=>{var i=n(45498);t.exports=function _defineProperty(t,e,n){return(e=i(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t},t.exports.__esModule=!0,t.exports.default=t.exports},96784:t=>{t.exports=function _interopRequireDefault(t){return t&&t.__esModule?t:{default:t}},t.exports.__esModule=!0,t.exports.default=t.exports},11327:(t,e,n)=>{var i=n(10564).default;t.exports=function toPrimitive(t,e){if("object"!=i(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var o=n.call(t,e||"default");if("object"!=i(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)},t.exports.__esModule=!0,t.exports.default=t.exports},45498:(t,e,n)=>{var i=n(10564).default,o=n(11327);t.exports=function toPropertyKey(t){var e=o(t,"string");return"symbol"==i(e)?e:e+""},t.exports.__esModule=!0,t.exports.default=t.exports},10564:t=>{function _typeof(e){return t.exports=_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t.exports.__esModule=!0,t.exports.default=t.exports,_typeof(e)}t.exports=_typeof,t.exports.__esModule=!0,t.exports.default=t.exports}},e={};function __webpack_require__(n){var i=e[n];if(void 0!==i)return i.exports;var o=e[n]={exports:{}};return t[n](o,o.exports,__webpack_require__),o.exports}(()=>{"use strict";var t,e,n=__webpack_require__(96784)(__webpack_require__(85707));function ownKeys(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,i)}return n}function _objectSpread(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?ownKeys(Object(i),!0).forEach((function(e){(0,n.default)(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):ownKeys(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}null!==(t=elementorAdminHints)&&void 0!==t&&t.mediaHint&&null!==(e=wp)&&void 0!==e&&null!==(e=e.media)&&void 0!==e&&null!==(e=e.view)&&void 0!==e&&null!==(e=e.Attachment)&&void 0!==e&&e.Details&&(wp.media.view.Attachment.Details=wp.media.view.Attachment.Details.extend({_tmpl:'<div class="e-hint__container" style="clear:both" data-event="<%= dismissible %>">\n\t\t<div class="elementor-control-notice elementor-control-notice-type-<%= type %>" data-display="<%= display %>">\n\t\t\t<div class="elementor-control-notice-icon">\n\t\t\t\t<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">\n\t\t\t\t\t<path d="M2.25 9H3M9 2.25V3M15 9H15.75M4.2 4.2L4.725 4.725M13.8 4.2L13.275 4.725M7.27496 12.75H10.725M6.75 12C6.12035 11.5278 5.65525 10.8694 5.42057 10.1181C5.1859 9.36687 5.19355 8.56082 5.44244 7.81415C5.69133 7.06748 6.16884 6.41804 6.80734 5.95784C7.44583 5.49764 8.21294 5.25 9 5.25C9.78706 5.25 10.5542 5.49764 11.1927 5.95784C11.8312 6.41804 12.3087 7.06748 12.5576 7.81415C12.8065 8.56082 12.8141 9.36687 12.5794 10.1181C12.3448 10.8694 11.8796 11.5278 11.25 12C10.9572 12.2899 10.7367 12.6446 10.6064 13.0355C10.4761 13.4264 10.4397 13.8424 10.5 14.25C10.5 14.6478 10.342 15.0294 10.0607 15.3107C9.77936 15.592 9.39782 15.75 9 15.75C8.60218 15.75 8.22064 15.592 7.93934 15.3107C7.65804 15.0294 7.5 14.6478 7.5 14.25C7.56034 13.8424 7.52389 13.4264 7.3936 13.0355C7.2633 12.6446 7.04282 12.2899 6.75 12Z" stroke="currentColor" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"></path>\n\t\t\t\t</svg>\n\t\t\t</div>\n\t\t\t<div class="elementor-control-notice-main action-handler" data-event="<%= button_event %>" data-settings="<%= button_data %>">\n\t\t\t\t<div class="elementor-control-notice-main-content"><%= content %></div>\n\t\t\t\t<div class="elementor-control-notice-main-actions">\n\t\t\t\t<% if ( typeof(button_text) !== "undefined" ) { %>\n\t\t\t\t\t<button class="e-btn e-<%= type %> e-btn-1">\n\t\t\t\t\t\t<%= button_text %>\n\t\t\t\t\t</button>\n\t\t\t\t<% } %>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t\t<button class="elementor-control-notice-dismiss" data-event="<%= dismissible %>" aria-label="<%= dismiss %>">\n\t\t\t\t<i class="eicon eicon-close" aria-hidden="true"></i>\n\t\t\t</button>\n\t\t</div>\n\t</div>',template:function template(t){var e=wp.media.template("attachment-details")(t),n=document.createElement("div");if(n.innerHTML=e,!this.shouldDisplayHint(t))return n.innerHTML;var i=document.createElement("div");return i.classList.add("e-hint"),i.innerHTML=_.template(this._tmpl)(elementorAdminHints.mediaHint),n.querySelector(".attachment-info").appendChild(i),n.innerHTML},events:_objectSpread(_objectSpread({},wp.media.view.Attachment.Details.prototype.events),{},{"click .elementor-control-notice-dismiss":"dismiss","click .e-hint__container a":"onHintAnchorClick","click .e-hint__container button.e-btn-1":"onHintAction"}),shouldDisplayHint:function shouldDisplayHint(t){var e;return!(!elementorAdminHints||null===(e=elementorAdminHints)||void 0===e||!e.mediaHint)&&void 0===window.elementorHints&&"image"===t.type&&(!!elementorAdminHints.mediaHint.display||this.imageNotOptimized(t))},imageNotOptimized:function imageNotOptimized(t){var e={height:1080,width:1920,filesizeInBytes:1e5};return Object.keys(e).some((function(n){var i=t[n]||!1;return i&&i>e[n]}))},onHintAction:function onHintAction(t){t.preventDefault();var e=t.target.closest(".action-handler").dataset.settings,n=atob(e),i=JSON.parse(n).action_url,o=void 0===i?null:i;o&&window.open(o,"_blank"),this.dismiss(t)},onHintAnchorClick:function onHintAnchorClick(t){this.dismiss(t)},dismiss:function dismiss(t){elementorCommon.ajax.addRequest("dismissed_editor_notices",{data:{dismissId:t.target.closest(".e-hint__container").dataset.event}}),this.hideHint(t)},hideHint:function hideHint(t){t.target.closest(".e-hint__container").remove(),window.elementorHints={}}}))})()})();