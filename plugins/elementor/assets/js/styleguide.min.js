/*! elementor - v3.29.0 - 04-06-2025 */
(()=>{var t={8323:(t,o,i)=>{"use strict";var r=i(12470).__,a=i(96784)(i(85707)),l=i(47838);t.exports=Marionette.Behavior.extend({tagView:null,listenerAttached:!1,initialize:function initialize(){this.listenerAttached||(this.listenTo(this.view.options.container.settings,"change:external:__dynamic__",this.onAfterExternalChange),this.listenerAttached=!0)},shouldRenderTools:function shouldRenderTools(){if(this.getOption("dynamicSettings").default)return!1;var t=elementor.helpers.hasPro()&&!elementor.helpers.hasProAndNotConnected(),o=this.getOption("tags").length>0;return!t||o},renderTools:function renderTools(){var t=this;if(this.shouldRenderTools()){var o=jQuery(Marionette.Renderer.render("#tmpl-elementor-control-dynamic-switcher"));o.on("click",(function(o){return t.onDynamicSwitcherClick(o)})),this.$el.find(".elementor-control-dynamic-switcher-wrapper").append(o),this.ui.dynamicSwitcher=o,"color"===this.view.model.get("type")&&(this.view.colorPicker?this.moveDynamicSwitcherToColorPicker():setTimeout((function(){return t.moveDynamicSwitcherToColorPicker()}))),this.ui.dynamicSwitcher.tipsy({title:function title(){return this.getAttribute("data-tooltip")},gravity:"s"})}},moveDynamicSwitcherToColorPicker:function moveDynamicSwitcherToColorPicker(){var t=this.view.colorPicker.$pickerToolsContainer;this.ui.dynamicSwitcher.removeClass("elementor-control-unit-1").addClass("e-control-tool");var o=t.find(".elementor-control-element-color-picker");o.length?this.ui.dynamicSwitcher.insertBefore(o):t.append(this.ui.dynamicSwitcher)},toggleDynamicClass:function toggleDynamicClass(){this.$el.toggleClass("elementor-control-dynamic-value",this.isDynamicMode())},isDynamicMode:function isDynamicMode(){var t=this.view.container.settings.get("__dynamic__");return!(!t||!t[this.view.model.get("name")])},createTagsList:function createTagsList(){var t=_.groupBy(this.getOption("tags"),"group"),o=elementor.dynamicTags.getConfig("groups"),i=this.ui.tagsList=jQuery("<div>",{class:"elementor-tags-list"}),r=jQuery("<div>",{class:"elementor-tags-list__inner"});if(i.append(r),jQuery.each(o,(function(o){var i=t[o];if(i){var a=jQuery("<div>",{class:"elementor-tags-list__group-title"}).text(this.title);r.append(a),i.forEach((function(t){var o=jQuery("<div>",{class:"elementor-tags-list__item"});o.text(t.title).attr("data-tag-name",t.name),r.append(o)}))}})),!elementor.helpers.hasPro()&&Object.keys(t).length){var a=Marionette.Renderer.render("#tmpl-elementor-dynamic-tags-promo",{promotionUrl:elementor.config.dynamicPromotionURL.replace("%s",this.view.model.get("name"))});r.append(a)}r.on("click",".elementor-tags-list__item",this.onTagsListItemClick.bind(this)),elementorCommon.elements.$body.append(i)},getTagsList:function getTagsList(){return this.ui.tagsList||this.createTagsList(),this.ui.tagsList},toggleTagsList:function toggleTagsList(){var t=this.getTagsList();if(t.is(":visible"))t.hide();else{var o=elementorCommon.config.isRTL?"left":"right";t.show().position({my:"".concat(o," top"),at:"".concat(o," bottom+5"),of:this.ui.dynamicSwitcher})}},setTagView:function setTagView(t,o,i){this.tagView&&this.tagView.destroy();var r=this.tagView=new l({id:t,name:o,settings:i,controlName:this.view.model.get("name"),dynamicSettings:this.getOption("dynamicSettings")}),a=this.view.options.container,u=a.controls[r.options.controlName].label;r.options.container=new elementorModules.editor.Container({type:"dynamic",id:t,model:r.model,settings:r.model,view:r,parent:a,label:a.label+" "+u,controls:r.model.options.controls,renderer:a}),r.render(),this.$el.find(".elementor-control-tag-area").after(r.el),this.listenTo(r,"remove",this.onTagViewRemove.bind(this))},setDefaultTagView:function setDefaultTagView(){var t=elementor.dynamicTags.tagTextToTagData(this.getDynamicValue());this.setTagView(t.id,t.name,t.settings)},tagViewToTagText:function tagViewToTagText(){var t=this.tagView;return elementor.dynamicTags.tagDataToTagText(t.getOption("id"),t.getOption("name"),t.model)},getDynamicValue:function getDynamicValue(){return this.view.container.dynamic.get(this.view.model.get("name"))},destroyTagView:function destroyTagView(){this.tagView&&(this.tagView.destroy(),this.tagView=null)},showPromotion:function showPromotion(){var t=elementor.helpers.hasProAndNotConnected(),o={title:r("Dynamic Content","elementor"),content:r("Create more personalized and dynamic sites by populating data from various sources with dozens of dynamic tags to choose from.","elementor"),targetElement:this.ui.dynamicSwitcher,position:{blockStart:"-10"},actionButton:{url:t?elementorProEditorConfig.urls.connect:elementor.config.dynamicPromotionURL.replace("%s",this.view.model.get("name")),text:r(t?"Connect & Activate":"Upgrade","elementor")}};elementor.promotion.showDialog(o)},onRender:function onRender(){this.$el.addClass("elementor-control-dynamic"),this.renderTools(),this.toggleDynamicClass(),this.isDynamicMode()&&this.setDefaultTagView()},onDynamicSwitcherClick:function onDynamicSwitcherClick(t){t.stopPropagation(),this.getOption("tags").length?this.toggleTagsList():this.showPromotion()},onTagsListItemClick:function onTagsListItemClick(t){var o=jQuery(t.currentTarget);this.setTagView(elementorCommon.helpers.getUniqueId(),o.data("tagName"),{}),this.view.getGlobalKey()&&this.view.triggerMethod("unset:global:value"),this.isDynamicMode()?$e.run("document/dynamic/settings",{container:this.view.options.container,settings:(0,a.default)({},this.view.model.get("name"),this.tagViewToTagText())}):$e.run("document/dynamic/enable",{container:this.view.options.container,settings:(0,a.default)({},this.view.model.get("name"),this.tagViewToTagText())}),this.toggleDynamicClass(),this.toggleTagsList(),this.tagView.getTagConfig().settings_required&&this.tagView.showSettingsPopup()},onTagViewRemove:function onTagViewRemove(){$e.run("document/dynamic/disable",{container:this.view.options.container,settings:(0,a.default)({},this.view.model.get("name"),this.tagViewToTagText())}),this.toggleDynamicClass()},onAfterExternalChange:function onAfterExternalChange(){this.destroyTagView(),this.isDynamicMode()&&this.setDefaultTagView(),this.toggleDynamicClass()},onDestroy:function onDestroy(){this.destroyTagView(),this.ui.tagsList&&this.ui.tagsList.remove()}})},84593:t=>{"use strict";t.exports=Marionette.ItemView.extend({className:"elementor-tag-controls-stack-empty",template:"#tmpl-elementor-tag-controls-stack-empty"})},77109:(t,o,i)=>{"use strict";var r=i(84593);t.exports=elementorModules.editor.views.ControlsStack.extend({activeTab:"content",template:_.noop,emptyView:r,isEmpty:function isEmpty(){return this.collection.length<2},childViewOptions:function childViewOptions(){return{container:this.options.container}},getNamespaceArray:function getNamespaceArray(){var t=elementor.getPanelView().getCurrentPageView(),o=t.getNamespaceArray();return o.push(t.activeSection),o.push(this.getOption("controlName")),o.push(this.getOption("name")),o},onRenderTemplate:function onRenderTemplate(){this.activateFirstSection()}})},47838:(t,o,i)=>{"use strict";var r=i(77109);t.exports=Marionette.ItemView.extend({className:"elementor-dynamic-cover e-input-style",tagControlsStack:null,templateHelpers:function templateHelpers(){var t={};return this.model&&(t.controls=this.model.options.controls),t},ui:{remove:".elementor-dynamic-cover__remove"},events:function events(){var events={"click @ui.remove":"onRemoveClick"};return this.hasSettings()&&(events.click="onClick"),events},getTemplate:function getTemplate(){var t=this.getTagConfig(),o=Marionette.TemplateCache.get("#tmpl-elementor-control-dynamic-cover"),i=Marionette.Renderer.render(o,{hasSettings:this.hasSettings(),isRemovable:!this.getOption("dynamicSettings").default,title:t.title,content:t.panel_template});return Marionette.TemplateCache.prototype.compileTemplate(i.trim())},getTagConfig:function getTagConfig(){return elementor.dynamicTags.getConfig("tags."+this.getOption("name"))},initSettingsPopup:function initSettingsPopup(){var t={className:"elementor-tag-settings-popup",position:{my:"left top+5",at:"left bottom",of:this.$el,autoRefresh:!0},hide:{ignore:".select2-container"}},o=elementorCommon.dialogsManager.createWidget("buttons",t);this.getSettingsPopup=function(){return o}},hasSettings:function hasSettings(){return!!Object.values(this.getTagConfig().controls).length},showSettingsPopup:function showSettingsPopup(){this.tagControlsStack||this.initTagControlsStack();var t=this.getSettingsPopup();t.isVisible()||t.show()},initTagControlsStack:function initTagControlsStack(){this.tagControlsStack=new r({model:this.model,controls:this.model.controls,name:this.options.name,controlName:this.options.controlName,container:this.options.container,el:this.getSettingsPopup().getElements("message")[0]}),this.tagControlsStack.render()},initModel:function initModel(){this.model=new elementorModules.editor.elements.models.BaseSettings(this.getOption("settings"),{controls:this.getTagConfig().controls})},initialize:function initialize(){this.initModel(),this.hasSettings()&&(this.initSettingsPopup(),this.listenTo(this.model,"change",this.render))},onClick:function onClick(){this.showSettingsPopup()},onRemoveClick:function onRemoveClick(t){t.stopPropagation(),this.destroy(),this.trigger("remove")},onDestroy:function onDestroy(){this.hasSettings()&&this.getSettingsPopup().destroy(),this.tagControlsStack&&this.tagControlsStack.destroy()}})},47697:t=>{"use strict";t.exports=elementorModules.Module.extend({errors:[],__construct:function __construct(t){var o=t.customValidationMethod;o&&(this.validationMethod=o)},getDefaultSettings:function getDefaultSettings(){return{validationTerms:{}}},isValid:function isValid(){var t=this.validationMethod.apply(this,arguments);return!t.length||(this.errors=t,!1)},validationMethod:function validationMethod(t){var o=[];return this.getSettings("validationTerms").required&&((""+t).length||o.push("Required value is empty")),o}})},7895:(t,o,i)=>{"use strict";var r=i(96784);Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var a=r(i(39805)),l=r(i(40989)),u=r(i(15118)),c=r(i(29402)),d=r(i(87861));function _isNativeReflectConstruct(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!t})()}var p=i(62133);o.default=function(t){function BreakpointValidator(){return(0,a.default)(this,BreakpointValidator),function _callSuper(t,o,i){return o=(0,c.default)(o),(0,u.default)(t,_isNativeReflectConstruct()?Reflect.construct(o,i||[],(0,c.default)(t).constructor):o.apply(t,i))}(this,BreakpointValidator,arguments)}return(0,d.default)(BreakpointValidator,t),(0,l.default)(BreakpointValidator,[{key:"getDefaultSettings",value:function getDefaultSettings(){return{validationTerms:{max:5120}}}},{key:"getPanelActiveBreakpoints",value:function getPanelActiveBreakpoints(){var t=elementor.documents.currentDocument.config.settings.settings.active_breakpoints.map((function(t){return t.replace("viewport_","")})),o={};return t.forEach((function(t){o[t]=elementorFrontend.config.responsive.breakpoints[t]})),o}},{key:"initBreakpointProperties",value:function initBreakpointProperties(){var t,o,i=this.getSettings("validationTerms"),r=this.getPanelActiveBreakpoints(),a=Object.keys(r);this.breakpointIndex=a.indexOf(i.breakpointName),this.topBreakpoint=null===(t=r[a[this.breakpointIndex+1]])||void 0===t?void 0:t.value,this.bottomBreakpoint=null===(o=r[a[this.breakpointIndex-1]])||void 0===o?void 0:o.value}},{key:"validationMethod",value:function validationMethod(t){var o=this.getSettings("validationTerms"),i=p.prototype.validationMethod.call(this,t);return(_.isFinite(t)||""===t)&&(this.validateMinMaxForBreakpoint(t,o)||i.push("Value is not between the breakpoints above or under the edited breakpoint")),i}},{key:"validateMinMaxForBreakpoint",value:function validateMinMaxForBreakpoint(t,o){var i=elementorFrontend.config.responsive.breakpoints[o.breakpointName].default_value,r=!0;return this.initBreakpointProperties(),"mobile"===o.breakpointName&&320===this.bottomBreakpoint&&(this.bottomBreakpoint-=1),this.bottomBreakpoint&&(""!==t&&t<=this.bottomBreakpoint&&(r=!1),""===t&&i<=this.bottomBreakpoint&&(r=!1)),this.topBreakpoint&&(""!==t&&t>=this.topBreakpoint&&(r=!1),""===t&&i>=this.topBreakpoint&&(r=!1)),r}}])}(p)},62133:(t,o,i)=>{"use strict";var r=i(47697);t.exports=r.extend({validationMethod:function validationMethod(t){var o=this.getSettings("validationTerms"),i=[];return _.isFinite(t)&&(void 0!==o.min&&t<o.min&&i.push("Value is less than minimum"),void 0!==o.max&&t>o.max&&i.push("Value is greater than maximum")),i}})},35741:(t,o,i)=>{"use strict";var r=i(96784),a=r(i(18821)),l=r(i(85707)),u=r(i(7895));function _createForOfIteratorHelper(t,o){var i="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!i){if(Array.isArray(t)||(i=function _unsupportedIterableToArray(t,o){if(t){if("string"==typeof t)return _arrayLikeToArray(t,o);var i={}.toString.call(t).slice(8,-1);return"Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?_arrayLikeToArray(t,o):void 0}}(t))||o&&t&&"number"==typeof t.length){i&&(t=i);var r=0,a=function F(){};return{s:a,n:function n(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function e(t){throw t},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var l,u=!0,c=!1;return{s:function s(){i=i.call(t)},n:function n(){var t=i.next();return u=t.done,t},e:function e(t){c=!0,l=t},f:function f(){try{u||null==i.return||i.return()}finally{if(c)throw l}}}}function _arrayLikeToArray(t,o){(null==o||o>t.length)&&(o=t.length);for(var i=0,r=Array(o);i<o;i++)r[i]=t[i];return r}var c,d=i(95384),p=i(8323),g=i(47697),h=i(62133);c=d.extend({validatorTypes:{Base:g,Number:h,Breakpoint:u.default},ui:function ui(){var ui=d.prototype.ui.apply(this,arguments);return _.extend(ui,{input:'input[data-setting][type!="checkbox"][type!="radio"]',checkbox:'input[data-setting][type="checkbox"]',radio:'input[data-setting][type="radio"]',select:"select[data-setting]",textarea:"textarea[data-setting]",responsiveSwitchersSibling:"".concat(ui.controlTitle,'[data-e-responsive-switcher-sibling!="false"]'),responsiveSwitchers:".elementor-responsive-switcher",contentEditable:'[contenteditable="true"]'}),ui},templateHelpers:function templateHelpers(){var t=d.prototype.templateHelpers.apply(this,arguments);return t.data.controlValue=this.getControlValue(),t},events:function events(){return{"input @ui.input":"onBaseInputTextChange","change @ui.checkbox":"onBaseInputChange","change @ui.radio":"onBaseInputChange","input @ui.textarea":"onBaseInputTextChange","change @ui.select":"onBaseInputChange","input @ui.contentEditable":"onBaseInputTextChange","click @ui.responsiveSwitchers":"onResponsiveSwitchersClick"}},behaviors:function behaviors(){var behaviors=d.prototype.behaviors.apply(this,arguments),t=this.options.model.get("dynamic");if(t&&t.active){var o=_.filter(elementor.dynamicTags.getConfig("tags"),(function(o){return o.editable&&_.intersection(o.categories,t.categories).length}));(o.length||elementor.config.user.is_administrator)&&(behaviors.tags={behaviorClass:p,tags:o,dynamicSettings:t})}return behaviors},initialize:function initialize(){d.prototype.initialize.apply(this,arguments),this.registerValidators(),this.model.get("responsive")&&this.setPlaceholderFromParent(),void 0===this.model.get("inherit_placeholders")&&this.model.set("inherit_placeholders",!0);var t=this.container?this.container.settings:this.elementSettingsModel;this.listenTo(t,"change:external:"+this.model.get("name"),this.onAfterExternalChange)},getControlValue:function getControlValue(){return this.container.settings.get(this.model.get("name"))},getGlobalKey:function getGlobalKey(){return this.container.globals.get(this.model.get("name"))},getGlobalValue:function getGlobalValue(){return this.globalValue},getGlobalDefault:function getGlobalDefault(){var t=this.model.get("global");if(null!=t&&t.default){if(!elementor.config.globals.defaults_enabled[this.getGlobalMeta().controlType])return"";var o=$e.data.commandExtractArgs(t.default),i=o.command,r=o.args,a=$e.data.getCache($e.components.get("globals"),i,r.query);return null==a?void 0:a.value}return""},getCurrentValue:function getCurrentValue(){if(this.getGlobalKey()&&!this.globalValue)return"";if(this.globalValue)return this.globalValue;var t=this.getControlValue();return t||this.getGlobalDefault()},isGlobalActive:function isGlobalActive(){var t;return null===(t=this.options.model.get("global"))||void 0===t?void 0:t.active},setValue:function setValue(t){this.setSettingsModel(t)},setSettingsModel:function setSettingsModel(t){var o=this.model.get("name");$e.run("document/elements/settings",{container:this.options.container,settings:(0,l.default)({},o,t)}),this.triggerMethod("settings:change")},applySavedValue:function applySavedValue(){this.setInputValue('[data-setting="'+this.model.get("name")+'"]',this.getControlValue())},getEditSettings:function getEditSettings(t){var o=this.getOption("elementEditSettings").toJSON();return t?o[t]:o},setEditSetting:function setEditSetting(t,o){(this.getOption("elementEditSettings")||this.getOption("container").settings).set(t,o)},getControlPlaceholder:function getControlPlaceholder(){var t=this.model.get("placeholder");return this.model.get("responsive")&&this.model.get("inherit_placeholders")&&(t=t||this.container.placeholders[this.model.get("name")]),t},getResponsiveParentView:function getResponsiveParentView(){var t=this.model.get("parent");try{return t&&this.container.panel.getControlView(t)}catch(t){}},getResponsiveChildrenViews:function getResponsiveChildrenViews(){var t=this.model.get("inheritors"),o=[];try{var i,r=_createForOfIteratorHelper(t);try{for(r.s();!(i=r.n()).done;){var a=i.value;o.push(this.container.panel.getControlView(a))}}catch(t){r.e(t)}finally{r.f()}}catch(t){}return o},setPlaceholderFromParent:function setPlaceholderFromParent(){var t=this.getResponsiveParentView();t&&(this.container.placeholders[this.model.get("name")]=t.preparePlaceholderForChildren())},preparePlaceholderForChildren:function preparePlaceholderForChildren(){var t,o=this.getCleanControlValue(),i=null===(t=this.getResponsiveParentView())||void 0===t?void 0:t.preparePlaceholderForChildren();return o instanceof Object?Object.assign({},i,o):o||i},propagatePlaceholder:function propagatePlaceholder(){var t,o=_createForOfIteratorHelper(this.getResponsiveChildrenViews());try{for(o.s();!(t=o.n()).done;){t.value.renderWithChildren()}}catch(t){o.e(t)}finally{o.f()}},renderWithChildren:function renderWithChildren(){this.render(),this.propagatePlaceholder()},getCleanControlValue:function getCleanControlValue(){var t=this.getControlValue();return t&&t!==this.model.get("default")?t:void 0},onAfterChange:function onAfterChange(t){Object.keys(t.changed).includes(this.model.get("name"))&&this.propagatePlaceholder(),d.prototype.onAfterChange.apply(this,arguments)},getInputValue:function getInputValue(t){var o=this.$(t);if(o.is('[contenteditable="true"]'))return o.html();var i=o.val(),r=o.attr("type");return-1!==["radio","checkbox"].indexOf(r)?o.prop("checked")?i:"":"number"===r&&_.isFinite(i)?+i:("SELECT"===t.tagName&&o.prop("multiple")&&null===i&&(i=[]),i)},setInputValue:function setInputValue(t,o){var i=this.$(t),r=i.attr("type");"checkbox"===r?i.prop("checked",!!o):"radio"===r?i.filter('[value="'+o+'"]').prop("checked",!0):i.val(o)},addValidator:function addValidator(t){this.validators.push(t)},registerValidators:function registerValidators(){var t=this;this.validators=[];var o={};this.model.get("required")&&(o.required=!0),jQuery.isEmptyObject(o)||this.addValidator(new this.validatorTypes.Base({validationTerms:o}));var i=this.model.get("validators");i&&Object.entries(i).forEach((function(o){var i=(0,a.default)(o,2),r=i[0],l=i[1];t.addValidator(new t.validatorTypes[r]({validationTerms:l}))}))},onBeforeRender:function onBeforeRender(){this.setPlaceholderFromParent()},onRender:function onRender(){d.prototype.onRender.apply(this,arguments),this.model.get("responsive")&&this.renderResponsiveSwitchers(),this.applySavedValue(),this.triggerMethod("ready"),this.toggleControlVisibility(),this.addTooltip()},onBaseInputTextChange:function onBaseInputTextChange(t){this.onBaseInputChange(t)},onBaseInputChange:function onBaseInputChange(t){clearTimeout(this.correctionTimeout);var o=t.currentTarget,i=this.getInputValue(o),r=this.validators.slice(0),a=this.container.settings.validators[this.model.get("name")];if(a&&(r=r.concat(a)),r){var l=this.getControlValue(o.dataset.setting);if(!r.every((function(t){return t.isValid(i,l)})))return void(this.correctionTimeout=setTimeout(this.setInputValue.bind(this,o,l),1200))}this.updateElementModel(i,o),this.triggerMethod("input:change",t)},onResponsiveSwitchersClick:function onResponsiveSwitchersClick(t){var o=jQuery(t.currentTarget),i=o.data("device"),r=this.ui.responsiveSwitchersWrapper,a=o.index();r.toggleClass("elementor-responsive-switchers-open"),r[0].style.setProperty("--selected-option",a),this.triggerMethod("responsive:switcher:click",i),elementor.changeDeviceMode(i)},renderResponsiveSwitchers:function renderResponsiveSwitchers(){var t=Marionette.Renderer.render("#tmpl-elementor-control-responsive-switchers",this.model.attributes);this.ui.responsiveSwitchersSibling.after(t),this.ui.responsiveSwitchersWrapper=this.$el.find(".elementor-control-responsive-switchers")},onAfterExternalChange:function onAfterExternalChange(){this.hideTooltip(),this.applySavedValue()},addTooltip:function addTooltip(){this.ui.tooltipTargets=this.$el.find(".tooltip-target"),this.ui.tooltipTargets.length&&this.ui.tooltipTargets.tipsy({gravity:function gravity(){var gravity=jQuery(this).data("tooltip-pos");return void 0!==gravity?gravity:"s"},title:function title(){return this.getAttribute("data-tooltip")}})},hideTooltip:function hideTooltip(){this.ui.tooltipTargets.length&&this.ui.tooltipTargets.tipsy("hide")},updateElementModel:function updateElementModel(t){this.setValue(t)}},{getStyleValue:function getStyleValue(t,o,i){return"DEFAULT"===t?i.default:o},onPasteStyle:function onPasteStyle(){return!0}}),t.exports=c},95384:(t,o,i)=>{"use strict";var r,a=i(96784)(i(85707));function ownKeys(t,o){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);o&&(r=r.filter((function(o){return Object.getOwnPropertyDescriptor(t,o).enumerable}))),i.push.apply(i,r)}return i}function _objectSpread(t){for(var o=1;o<arguments.length;o++){var i=null!=arguments[o]?arguments[o]:{};o%2?ownKeys(Object(i),!0).forEach((function(o){(0,a.default)(t,o,i[o])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):ownKeys(Object(i)).forEach((function(o){Object.defineProperty(t,o,Object.getOwnPropertyDescriptor(i,o))}))}return t}r=Marionette.CompositeView.extend({ui:function ui(){return{controlTitle:".elementor-control-title"}},behaviors:function behaviors(){return elementor.hooks.applyFilters("controls/base/behaviors",{},this)},getBehavior:function getBehavior(t){return this._behaviors[Object.keys(this.behaviors()).indexOf(t)]},className:function className(){var t="elementor-control elementor-control-"+this.model.get("name")+" elementor-control-type-"+this.model.get("type"),o=this.model.get("classes"),i=this.model.get("responsive");(_.isEmpty(o)||(t+=" "+o),_.isEmpty(i))||(t+=" elementor-control-responsive-"+(i.max||i.min));return t},templateHelpers:function templateHelpers(){var t={_cid:this.model.cid};return{view:this,data:_.extend({},this.model.toJSON(),t)}},getTemplate:function getTemplate(){return Marionette.TemplateCache.get("#tmpl-elementor-control-"+this.model.get("type")+"-content")},initialize:function initialize(t){var o=this.model.get("label");Object.defineProperty(this,"container",{get:function get(){if(!t.container){var i=t.elementSettingsModel,r=$e.components.get("document").utils.findViewById(i.id);r&&r.getContainer?t.container=r.getContainer():(i.id||(i.id="bc-"+elementorCommon.helpers.getUniqueId()),t.container=new elementorModules.editor.Container({type:"bc-container",id:i.id,model:i,settings:i,label:o,view:!1,parent:!1,renderer:!1,controls:i.options.controls}))}return t.container}}),Object.defineProperty(this,"elementSettingsModel",{get:function get(){return elementorDevTools.deprecation.deprecated("elementSettingsModel","2.8.0","container.settings"),t.container?t.container.settings:t.elementSettingsModel}});var i=this.model.get("type"),r=jQuery.extend(!0,{},elementor.config.controls[i],this.model.attributes);this.model.set(r);var a=this.container?this.container.settings:this.elementSettingsModel;this.listenTo(a,"change",this.onAfterChange),this.model.attributes.responsive&&(this.onDeviceModeChange=this.onDeviceModeChange.bind(this),elementor.listenTo(elementor.channels.deviceMode,"change",this.onDeviceModeChange))},onDestroy:function onDestroy(){elementor.stopListening(elementor.channels.deviceMode,"change",this.onDeviceModeChange)},onDeviceModeChange:function onDeviceModeChange(){this.toggleControlVisibility()},onAfterChange:function onAfterChange(){this.toggleControlVisibility()},toggleControlVisibility:function toggleControlVisibility(){var t=this.container?this.container.settings:this.elementSettingsModel,o=elementor.helpers.isActiveControl(this.model,t.attributes,t.controls);this.$el.toggleClass("elementor-hidden-control",!o),elementor.getPanelView().updateScrollbar()},onRender:function onRender(){var t=this.model.get("label_block")?"block":"inline",o=this.model.get("show_label"),i="elementor-label-"+t;i+=" elementor-control-separator-"+this.model.get("separator"),o||(i+=" elementor-control-hidden-label"),this.$el.addClass(i),this.toggleControlVisibility()},reRoute:function reRoute(t){$e.route($e.routes.getCurrent("panel"),this.getControlInRouteArgs(t?this.getControlPath():""),{history:!1})},getControlInRouteArgs:function getControlInRouteArgs(t){return _objectSpread(_objectSpread({},$e.routes.getCurrentArgs("panel")),{},{activeControl:t})},getControlPath:function getControlPath(){for(var t=this.model.get("name"),o=this._parent;!o.$el.hasClass("elementor-controls-stack");){t=(o.model.get("name")||o.model.get("_id"))+"/"+t,o=o._parent}return t}}),t.exports=r},42778:(t,o,i)=>{"use strict";var r=i(35741);t.exports=r.extend({setInputValue:function setInputValue(t,o){this.$(t).prop("checked",this.model.get("return_value")===o)}},{onPasteStyle:function onPasteStyle(t,o){return!o||o===t.return_value}})},64326:(t,o,i)=>{"use strict";var r=i(96784);Object.defineProperty(o,"__esModule",{value:!0}),o.default=o.Enable=void 0;var a=r(i(39805)),l=r(i(40989)),u=r(i(15118)),c=r(i(29402)),d=r(i(87861));function _isNativeReflectConstruct(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!t})()}var p=o.Enable=function(t){function Enable(){return(0,a.default)(this,Enable),function _callSuper(t,o,i){return o=(0,c.default)(o),(0,u.default)(t,_isNativeReflectConstruct()?Reflect.construct(o,i||[],(0,c.default)(t).constructor):o.apply(t,i))}(this,Enable,arguments)}return(0,d.default)(Enable,t),(0,l.default)(Enable,[{key:"apply",value:function apply(t){$e.components.get("preview/styleguide").enableStyleguidePreview(t)}}])}($e.modules.CommandBase);o.default=p},68031:(t,o,i)=>{"use strict";var r=i(96784);Object.defineProperty(o,"__esModule",{value:!0}),o.default=o.GlobalColors=void 0;var a=r(i(39805)),l=r(i(40989)),u=r(i(15118)),c=r(i(29402)),d=r(i(87861));function _isNativeReflectConstruct(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!t})()}var p=o.GlobalColors=function(t){function GlobalColors(){return(0,a.default)(this,GlobalColors),function _callSuper(t,o,i){return o=(0,c.default)(o),(0,u.default)(t,_isNativeReflectConstruct()?Reflect.construct(o,i||[],(0,c.default)(t).constructor):o.apply(t,i))}(this,GlobalColors,arguments)}return(0,d.default)(GlobalColors,t),(0,l.default)(GlobalColors,[{key:"apply",value:function apply(){$e.components.get("preview/styleguide").showStyleguidePreview()}}])}($e.modules.CommandBase);o.default=p},16466:(t,o,i)=>{"use strict";var r=i(96784);Object.defineProperty(o,"__esModule",{value:!0}),o.default=o.GlobalTypography=void 0;var a=r(i(39805)),l=r(i(40989)),u=r(i(15118)),c=r(i(29402)),d=r(i(87861));function _isNativeReflectConstruct(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!t})()}var p=o.GlobalTypography=function(t){function GlobalTypography(){return(0,a.default)(this,GlobalTypography),function _callSuper(t,o,i){return o=(0,c.default)(o),(0,u.default)(t,_isNativeReflectConstruct()?Reflect.construct(o,i||[],(0,c.default)(t).constructor):o.apply(t,i))}(this,GlobalTypography,arguments)}return(0,d.default)(GlobalTypography,t),(0,l.default)(GlobalTypography,[{key:"apply",value:function apply(){$e.components.get("preview/styleguide").showStyleguidePreview()}}])}($e.modules.CommandBase);o.default=p},42577:(t,o,i)=>{"use strict";var r=i(96784);Object.defineProperty(o,"__esModule",{value:!0}),o.default=o.Hide=void 0;var a=r(i(39805)),l=r(i(40989)),u=r(i(15118)),c=r(i(29402)),d=r(i(87861));function _isNativeReflectConstruct(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!t})()}var p=o.Hide=function(t){function Hide(){return(0,a.default)(this,Hide),function _callSuper(t,o,i){return o=(0,c.default)(o),(0,u.default)(t,_isNativeReflectConstruct()?Reflect.construct(o,i||[],(0,c.default)(t).constructor):o.apply(t,i))}(this,Hide,arguments)}return(0,d.default)(Hide,t),(0,l.default)(Hide,[{key:"apply",value:function apply(){$e.components.get("preview/styleguide").hideStyleguidePreview()}}])}($e.modules.CommandBase);o.default=p},26205:(t,o,i)=>{"use strict";Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"Enable",{enumerable:!0,get:function get(){return r.Enable}}),Object.defineProperty(o,"GlobalColors",{enumerable:!0,get:function get(){return a.GlobalColors}}),Object.defineProperty(o,"GlobalTypography",{enumerable:!0,get:function get(){return l.GlobalTypography}}),Object.defineProperty(o,"Hide",{enumerable:!0,get:function get(){return u.Hide}}),Object.defineProperty(o,"SwitcherChange",{enumerable:!0,get:function get(){return c.SwitcherChange}});var r=i(64326),a=i(68031),l=i(16466),u=i(42577),c=i(62163)},62163:(t,o,i)=>{"use strict";var r=i(96784);Object.defineProperty(o,"__esModule",{value:!0}),o.default=o.SwitcherChange=void 0;var a=r(i(39805)),l=r(i(40989)),u=r(i(15118)),c=r(i(29402)),d=r(i(87861));function _isNativeReflectConstruct(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!t})()}var p=o.SwitcherChange=function(t){function SwitcherChange(){return(0,a.default)(this,SwitcherChange),function _callSuper(t,o,i){return o=(0,c.default)(o),(0,u.default)(t,_isNativeReflectConstruct()?Reflect.construct(o,i||[],(0,c.default)(t).constructor):o.apply(t,i))}(this,SwitcherChange,arguments)}return(0,d.default)(SwitcherChange,t),(0,l.default)(SwitcherChange,[{key:"validateArgs",value:function validateArgs(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.requireArgumentType("name","string",t),this.requireArgumentType("value","string",t)}},{key:"apply",value:function apply(t){t.name.includes("enable_styleguide_preview")&&$e.components.get("preview/styleguide").enableStyleguidePreview({value:t.value})}}])}($e.modules.CommandBase);o.default=p},99446:(t,o,i)=>{"use strict";var r=i(96784);Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var a=r(i(39805)),l=r(i(40989)),u=r(i(15118)),c=r(i(29402)),d=r(i(41621)),p=r(i(87861)),g=r(i(42778)),h=r(i(35741));function _isNativeReflectConstruct(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!t})()}o.default=function(t){function _default(){return(0,a.default)(this,_default),function _callSuper(t,o,i){return o=(0,c.default)(o),(0,u.default)(t,_isNativeReflectConstruct()?Reflect.construct(o,i||[],(0,c.default)(t).constructor):o.apply(t,i))}(this,_default,arguments)}return(0,p.default)(_default,t),(0,l.default)(_default,[{key:"initialize",value:function initialize(){h.default.prototype.initialize.apply(this,arguments),this.$el.addClass("elementor-control-type-switcher")}},{key:"onBeforeRender",value:function onBeforeRender(){for(var t=arguments.length,o=new Array(t),i=0;i<t;i++)o[i]=arguments[i];!function _superPropGet(t,o,i,r){var a=(0,d.default)((0,c.default)(1&r?t.prototype:t),o,i);return 2&r&&"function"==typeof a?function(t){return a.apply(i,t)}:a}(_default,"onBeforeRender",this,3)(o);var r=elementor.getPreferences("enable_styleguide_preview");r!==this.getCurrentValue()&&this.setValue(r)}},{key:"onBaseInputChange",value:function onBaseInputChange(t){h.default.prototype.onBaseInputChange.apply(this,arguments);var o=t.currentTarget,i=this.getInputValue(o);this.model.get("on_change_command")&&this.runCommand(i),this.model.set("return_value",null)}},{key:"runCommand",value:function runCommand(t){$e.run("preview/styleguide/switcher-change",{name:this.model.get("name"),value:t})}}])}(g.default)},28819:(t,o,i)=>{"use strict";var r=i(96784),a=i(10564);Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var l=r(i(39805)),u=r(i(40989)),c=r(i(15118)),d=r(i(29402)),p=r(i(87861)),g=function _interopRequireWildcard(t,o){if(!o&&t&&t.__esModule)return t;if(null===t||"object"!=a(t)&&"function"!=typeof t)return{default:t};var i=_getRequireWildcardCache(o);if(i&&i.has(t))return i.get(t);var r={__proto__:null},l=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var u in t)if("default"!==u&&{}.hasOwnProperty.call(t,u)){var c=l?Object.getOwnPropertyDescriptor(t,u):null;c&&(c.get||c.set)?Object.defineProperty(r,u,c):r[u]=t[u]}return r.default=t,i&&i.set(t,r),r}(i(26205)),h=r(i(99446));function _getRequireWildcardCache(t){if("function"!=typeof WeakMap)return null;var o=new WeakMap,i=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(t){return t?i:o})(t)}function _isNativeReflectConstruct(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!t})()}o.default=function(t){function _default(t){var o;return(0,l.default)(this,_default),o=function _callSuper(t,o,i){return o=(0,d.default)(o),(0,c.default)(t,_isNativeReflectConstruct()?Reflect.construct(o,i||[],(0,d.default)(t).constructor):o.apply(t,i))}(this,_default,[t]),elementor.addControlView("global-style-switcher",h.default),o.registerStyleguideDialogType(),elementor.once("preview:loaded",(function(){o.initModal()})),o}return(0,p.default)(_default,t),(0,u.default)(_default,[{key:"getNamespace",value:function getNamespace(){return"preview/styleguide"}},{key:"defaultCommands",value:function defaultCommands(){return this.importCommands(g)}},{key:"registerStyleguideDialogType",value:function registerStyleguideDialogType(){DialogsManager.addWidgetType("styleguide",DialogsManager.getWidgetType("lightbox").extend("alert",{buildWidget:function buildWidget(){DialogsManager.getWidgetType("lightbox").prototype.buildWidget.apply(this,arguments);var t=this.addElement("widgetContent"),o=this.getElements();t.append(o.message),o.widget.html(t)}}))}},{key:"initModal",value:function initModal(){var t;this.getModal=function(){return t||(t=elementorCommon.dialogsManager.createWidget("styleguide",{id:"e-styleguide-preview-dialog",message:'<div class="e-styleguide-preview-root"></div>',position:{my:"center center",at:"center center"},hide:{onOutsideClick:!1,onEscKeyPress:!1,onClick:!1,onBackgroundClick:!1},container:elementor.$previewContents.find("body")}))}}},{key:"showStyleguidePreview",value:function showStyleguidePreview(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.getModal().isVisible()||!t&&!elementor.getPreferences("enable_styleguide_preview")||(this.getPreviewFrame().postMessage({name:"elementor/styleguide/preview/show"},"*"),this.getModal().show())}},{key:"hideStyleguidePreview",value:function hideStyleguidePreview(){this.getPreviewFrame().postMessage({name:"elementor/styleguide/preview/hide"},"*"),this.getModal().hide()}},{key:"enableStyleguidePreview",value:function enableStyleguidePreview(t){t.value?this.showStyleguidePreview(!0):this.hideStyleguidePreview(),$e.run("document/elements/settings",{container:elementor.settings.editorPreferences.getEditedView().getContainer(),settings:{enable_styleguide_preview:t.value},options:{external:!0}})}},{key:"isInEditor",value:function isInEditor(){return!!window.elementor}},{key:"getPreviewFrame",value:function getPreviewFrame(){return this.isInEditor()?elementor.$preview[0].contentWindow:window}}])}($e.modules.ComponentBase)},12470:t=>{"use strict";t.exports=wp.i18n},78113:t=>{t.exports=function _arrayLikeToArray(t,o){(null==o||o>t.length)&&(o=t.length);for(var i=0,r=Array(o);i<o;i++)r[i]=t[i];return r},t.exports.__esModule=!0,t.exports.default=t.exports},70569:t=>{t.exports=function _arrayWithHoles(t){if(Array.isArray(t))return t},t.exports.__esModule=!0,t.exports.default=t.exports},36417:t=>{t.exports=function _assertThisInitialized(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t},t.exports.__esModule=!0,t.exports.default=t.exports},39805:t=>{t.exports=function _classCallCheck(t,o){if(!(t instanceof o))throw new TypeError("Cannot call a class as a function")},t.exports.__esModule=!0,t.exports.default=t.exports},40989:(t,o,i)=>{var r=i(45498);function _defineProperties(t,o){for(var i=0;i<o.length;i++){var a=o[i];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(t,r(a.key),a)}}t.exports=function _createClass(t,o,i){return o&&_defineProperties(t.prototype,o),i&&_defineProperties(t,i),Object.defineProperty(t,"prototype",{writable:!1}),t},t.exports.__esModule=!0,t.exports.default=t.exports},85707:(t,o,i)=>{var r=i(45498);t.exports=function _defineProperty(t,o,i){return(o=r(o))in t?Object.defineProperty(t,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[o]=i,t},t.exports.__esModule=!0,t.exports.default=t.exports},41621:(t,o,i)=>{var r=i(14718);function _get(){return t.exports=_get="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,o,i){var a=r(t,o);if(a){var l=Object.getOwnPropertyDescriptor(a,o);return l.get?l.get.call(arguments.length<3?t:i):l.value}},t.exports.__esModule=!0,t.exports.default=t.exports,_get.apply(null,arguments)}t.exports=_get,t.exports.__esModule=!0,t.exports.default=t.exports},29402:t=>{function _getPrototypeOf(o){return t.exports=_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},t.exports.__esModule=!0,t.exports.default=t.exports,_getPrototypeOf(o)}t.exports=_getPrototypeOf,t.exports.__esModule=!0,t.exports.default=t.exports},87861:(t,o,i)=>{var r=i(91270);t.exports=function _inherits(t,o){if("function"!=typeof o&&null!==o)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(o&&o.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),o&&r(t,o)},t.exports.__esModule=!0,t.exports.default=t.exports},96784:t=>{t.exports=function _interopRequireDefault(t){return t&&t.__esModule?t:{default:t}},t.exports.__esModule=!0,t.exports.default=t.exports},65474:t=>{t.exports=function _iterableToArrayLimit(t,o){var i=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=i){var r,a,l,u,c=[],d=!0,p=!1;try{if(l=(i=i.call(t)).next,0===o){if(Object(i)!==i)return;d=!1}else for(;!(d=(r=l.call(i)).done)&&(c.push(r.value),c.length!==o);d=!0);}catch(t){p=!0,a=t}finally{try{if(!d&&null!=i.return&&(u=i.return(),Object(u)!==u))return}finally{if(p)throw a}}return c}},t.exports.__esModule=!0,t.exports.default=t.exports},11018:t=>{t.exports=function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},t.exports.__esModule=!0,t.exports.default=t.exports},15118:(t,o,i)=>{var r=i(10564).default,a=i(36417);t.exports=function _possibleConstructorReturn(t,o){if(o&&("object"==r(o)||"function"==typeof o))return o;if(void 0!==o)throw new TypeError("Derived constructors may only return object or undefined");return a(t)},t.exports.__esModule=!0,t.exports.default=t.exports},91270:t=>{function _setPrototypeOf(o,i){return t.exports=_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,o){return t.__proto__=o,t},t.exports.__esModule=!0,t.exports.default=t.exports,_setPrototypeOf(o,i)}t.exports=_setPrototypeOf,t.exports.__esModule=!0,t.exports.default=t.exports},18821:(t,o,i)=>{var r=i(70569),a=i(65474),l=i(37744),u=i(11018);t.exports=function _slicedToArray(t,o){return r(t)||a(t,o)||l(t,o)||u()},t.exports.__esModule=!0,t.exports.default=t.exports},14718:(t,o,i)=>{var r=i(29402);t.exports=function _superPropBase(t,o){for(;!{}.hasOwnProperty.call(t,o)&&null!==(t=r(t)););return t},t.exports.__esModule=!0,t.exports.default=t.exports},11327:(t,o,i)=>{var r=i(10564).default;t.exports=function toPrimitive(t,o){if("object"!=r(t)||!t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var a=i.call(t,o||"default");if("object"!=r(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===o?String:Number)(t)},t.exports.__esModule=!0,t.exports.default=t.exports},45498:(t,o,i)=>{var r=i(10564).default,a=i(11327);t.exports=function toPropertyKey(t){var o=a(t,"string");return"symbol"==r(o)?o:o+""},t.exports.__esModule=!0,t.exports.default=t.exports},10564:t=>{function _typeof(o){return t.exports=_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t.exports.__esModule=!0,t.exports.default=t.exports,_typeof(o)}t.exports=_typeof,t.exports.__esModule=!0,t.exports.default=t.exports},37744:(t,o,i)=>{var r=i(78113);t.exports=function _unsupportedIterableToArray(t,o){if(t){if("string"==typeof t)return r(t,o);var i={}.toString.call(t).slice(8,-1);return"Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?r(t,o):void 0}},t.exports.__esModule=!0,t.exports.default=t.exports}},o={};function __webpack_require__(i){var r=o[i];if(void 0!==r)return r.exports;var a=o[i]={exports:{}};return t[i](a,a.exports,__webpack_require__),a.exports}(()=>{"use strict";var t=__webpack_require__(96784),o=t(__webpack_require__(39805)),i=t(__webpack_require__(40989)),r=t(__webpack_require__(15118)),a=t(__webpack_require__(29402)),l=t(__webpack_require__(87861)),u=t(__webpack_require__(28819));function _isNativeReflectConstruct(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!t})()}new(function(t){function Styleguide(){return(0,o.default)(this,Styleguide),function _callSuper(t,o,i){return o=(0,a.default)(o),(0,r.default)(t,_isNativeReflectConstruct()?Reflect.construct(o,i||[],(0,a.default)(t).constructor):o.apply(t,i))}(this,Styleguide,arguments)}return(0,l.default)(Styleguide,t),(0,i.default)(Styleguide,[{key:"onInit",value:function onInit(){$e.components.register(new u.default),this.addHooks()}},{key:"getGlobalRoutes",value:function getGlobalRoutes(){return{"global-colors":"panel/global/global-colors","global-typography":"panel/global/global-typography"}}},{key:"addHooks",value:function addHooks(){elementor.hooks.addAction("panel/global/tab/before-show",this.show.bind(this)),elementor.hooks.addAction("panel/global/tab/before-destroy",this.hide.bind(this))}},{key:"show",value:function show(t){t.id&&t.id in this.getGlobalRoutes()&&$e.run("preview/styleguide/".concat(t.id))}},{key:"hide",value:function hide(t){t.id&&t.id in this.getGlobalRoutes()&&(Object.values(this.getGlobalRoutes()).some((function(t){return $e.routes.current.panel===t}))||$e.run("preview/styleguide/hide"))}}])}(elementorModules.editor.utils.Module))})()})();