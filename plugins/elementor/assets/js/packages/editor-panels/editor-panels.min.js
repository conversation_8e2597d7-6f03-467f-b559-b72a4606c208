!function(){"use strict";var e={d:function(n,t){for(var o in t)e.o(t,o)&&!e.o(n,o)&&Object.defineProperty(n,o,{enumerable:!0,get:t[o]})},o:function(e,n){return Object.prototype.hasOwnProperty.call(e,n)},r:function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},n={};e.r(n),e.d(n,{Panel:function(){return b},PanelBody:function(){return O},PanelFooter:function(){return T},PanelHeader:function(){return P},PanelHeaderTitle:function(){return x},__createPanel:function(){return y},__registerPanel:function(){return g},init:function(){return h}});var t=window.elementorV2.editor,o=window.elementorV2.store,r=window.React,i=window.elementorV2.locations,l=window.elementorV2.ui,c=window.elementorV2.editorV1Adapters,{inject:a,useInjections:u}=(0,i.createLocation)(),s=e=>e.panels.openId,p=(0,o.__createSlice)({name:"panels",initialState:{openId:null},reducers:{open(e,n){e.openId=n.payload},close(e,n){n.payload&&e.openId!==n.payload||(e.openId=null)}}}),d="panel/v2";function m(){return document.querySelector("#elementor-panel-inner")}function f(){const e=["#elementor-panel-header-wrapper","#elementor-panel-content-wrapper","#elementor-panel-state-loading","#elementor-panel-footer"].join(", ");return document.querySelectorAll(e)}function _({on:e,when:n,callback:t}){let r;(0,o.__subscribe)((()=>{const i=e((0,o.__getState)());n({prev:r,current:i})&&t({prev:r,current:i}),r=i}))}function v(e){const n=(0,r.useRef)(m);return n.current?r.createElement(l.Portal,{container:n.current,...e}):null}function w(){const e=function(){const e=u(),n=(0,o.__useSelector)(s);return(0,r.useMemo)((()=>e.find((e=>n===e.id))),[e,n])}(),n=e?.component??null;return n?r.createElement(v,null,r.createElement(n,null)):null}function h(){(0,c.__privateListenTo)((0,c.windowEvent)("elementor/panel/init"),(()=>(0,c.__privateRegisterRoute)(d))),(0,c.__privateListenTo)((0,c.routeOpenEvent)(d),(()=>{f().forEach((e=>{e.setAttribute("hidden","hidden"),e.setAttribute("inert","true")}))})),(0,c.__privateListenTo)((0,c.routeCloseEvent)(d),(()=>s((0,o.__getState)())&&(0,o.__dispatch)(p.actions.close()))),(0,c.__privateListenTo)((0,c.routeCloseEvent)(d),(()=>{f().forEach((e=>{e.removeAttribute("hidden"),e.removeAttribute("inert")}))})),(0,c.__privateListenTo)((0,c.windowEvent)("elementor/panel/init"),(()=>_({on:e=>s(e),when:({prev:e,current:n})=>!(e||!n),callback:()=>(0,c.__privateOpenRoute)(d)}))),(0,c.__privateListenTo)((0,c.windowEvent)("elementor/panel/init"),(()=>_({on:e=>s(e),when:({prev:e,current:n})=>!(n||!e),callback:()=>(0,c.__privateIsRouteActive)(d)&&(0,c.__privateOpenRoute)(function(){const e=window?.elementor?.documents?.getCurrent?.()?.config?.panel?.default_route;return e||"panel/elements/categories"}())}))),(0,o.__registerSlice)(p),(0,t.injectIntoTop)({id:"panels",component:w})}function y({id:e,component:n,onOpen:t,onClose:r,allowedEditModes:i,blockOnKitRoutes:l}){const a=function(e,n={}){return()=>{const t=(0,o.__useSelector)(s),r=(0,c.__privateUseRouteStatus)(d,n);return{isOpen:t===e&&r.isActive,isBlocked:r.isBlocked}}}(e,{allowedEditModes:i,blockOnKitRoutes:l}),u=function(e,n,t={}){let r=null;return()=>{const i=(0,o.__useDispatch)(),{isBlocked:l}=n();return{open:async()=>{l||(i(p.actions.open(e)),r=t.onOpen?.()??null)},close:async()=>{l||(i(p.actions.close(e)),t.onClose?.(r))}}}}(e,a,{onOpen:t,onClose:r});return{panel:{id:e,component:n},usePanelStatus:a,usePanelActions:u}}function g({id:e,component:n}){a({id:e,component:n})}function b({children:e,sx:n,...t}){return r.createElement(l.Drawer,{open:!0,variant:"persistent",anchor:"left",PaperProps:{sx:{position:"relative",width:"100%",bgcolor:"background.default",border:"none"}},sx:{height:"100%",...n},...t},e)}var E=(0,l.styled)(l.Box)((({theme:e})=>({height:e?.spacing(6)||"48px",display:"flex",alignItems:"center",justifyContent:"center",gap:e?.spacing(.5)||"4px"})));function P({children:e,...n}){return r.createElement(r.Fragment,null,r.createElement(E,{component:"header",...n},e))}var S=(0,l.styled)(l.Typography)((({theme:e,variant:n="body1"})=>"inherit"===n?{}:{"&.MuiTypography-root":{...e.typography[n]}}));function x({children:e,...n}){return r.createElement(S,{component:"h2",variant:"subtitle1",...n},e)}function O({children:e,sx:n,...t}){return r.createElement(l.Box,{component:"main",sx:{overflowY:"auto",height:"100%",...n},...t},e)}function T({children:e,sx:n,...t}){return r.createElement(r.Fragment,null,r.createElement(l.Divider,null),r.createElement(l.Box,{component:"footer",sx:{display:"flex",position:"sticky",bottom:0,px:2,py:1.5},...t},e))}(window.elementorV2=window.elementorV2||{}).editorPanels=n}(),window.elementorV2.editorPanels?.init?.();