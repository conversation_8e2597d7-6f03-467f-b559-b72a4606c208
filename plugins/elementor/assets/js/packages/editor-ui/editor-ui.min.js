!function(){"use strict";var e={d:function(t,n){for(var r in n)e.o(n,r)&&!e.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:n[r]})},o:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r:function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{EditableField:function(){return u},EllipsisWithTooltip:function(){return l},InfoTipCard:function(){return v},IntroductionModal:function(){return d},MenuItemInfotip:function(){return w},MenuListItem:function(){return h},ThemeProvider:function(){return g},WarningInfotip:function(){return y},useEditable:function(){return x}});var n=window.React,r=window.elementorV2.ui,o=window.wp.i18n,i=window.elementorV2.editorV1Adapters,a=window.elementorV2.icons,l=({maxWidth:e,title:t,as:o,...i})=>{const[a,l]=s();return l?n.createElement(r.Tooltip,{title:t,placement:"top"},n.createElement(c,{maxWidth:e,ref:a,as:o,...i},t)):n.createElement(c,{maxWidth:e,ref:a,as:o,...i},t)},c=n.forwardRef((({maxWidth:e,as:t=r.Box,...o},i)=>n.createElement(t,{ref:i,position:"relative",...o,style:{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",maxWidth:e}}))),s=()=>{const[e,t]=(0,n.useState)(null),[r,o]=(0,n.useState)(!1);return(0,n.useEffect)((()=>{const t=new ResizeObserver((([{target:e}])=>{o(e.scrollWidth>e.clientWidth)}));return e&&t.observe(e),()=>{t.disconnect()}}),[e]),[t,r]},u=(0,n.forwardRef)((({value:e,error:t,as:o="span",sx:i,...a},l)=>n.createElement(r.Tooltip,{title:t,open:!!t,placement:"top"},n.createElement(m,{ref:l,component:o,...a},e)))),m=(0,r.styled)(r.Box)`
	width: 100%;
	&:focus {
		outline: none;
	}
`,d=({open:e,handleClose:t,title:i,children:a})=>{const[l,c]=(0,n.useState)(!0);return n.createElement(r.Dialog,{open:e,onClose:t,maxWidth:"sm",TransitionComponent:p},i&&n.createElement(r.DialogHeader,{logo:!1},n.createElement(r.DialogTitle,null,i)),a,n.createElement(r.DialogActions,null,n.createElement(r.FormControlLabel,{sx:{marginRight:"auto"},control:n.createElement(r.Checkbox,{checked:!l,onChange:()=>c(!l)}),label:n.createElement(r.Typography,{variant:"body2"},(0,o.__)("Don't show this again","elementor"))}),n.createElement(r.Button,{size:"medium",variant:"contained",sx:{minWidth:"135px"},onClick:()=>t(l)},(0,o.__)("Got it","elementor"))))},p=n.forwardRef(((e,t)=>n.createElement(r.Fade,{ref:t,...e,timeout:{enter:1e3,exit:200}})));function f(){return window.elementor?.getPreferences?.("ui_theme")||"auto"}var E="unstable";function g({children:e}){const t=function(){const[e,t]=(0,n.useState)((()=>f()));return(0,n.useEffect)((()=>(0,i.__privateListenTo)((0,i.v1ReadyEvent)(),(()=>t(f())))),[]),(0,n.useEffect)((()=>(0,i.__privateListenTo)((0,i.commandEndEvent)("document/elements/settings"),(e=>{const n=e;n.args?.settings&&"ui_theme"in n.args.settings&&t(f())}))),[]),e}();return n.createElement(r.ThemeProvider,{colorScheme:t,palette:E},e)}var h=({children:e,...t})=>n.createElement(r.MenuItem,{dense:!0,...t,sx:{...t.sx??{}}},n.createElement(r.MenuItemText,{primary:e,primaryTypographyProps:{variant:"caption"}})),w=(0,n.forwardRef)((({showInfoTip:e=!1,children:t,content:o},i)=>e?n.createElement(r.Infotip,{ref:i,placement:"right",arrow:!1,content:n.createElement(r.Paper,{color:"secondary",sx:{display:"flex",gap:.5,p:2,maxWidth:325},elevation:0},n.createElement(a.InfoCircleFilledIcon,{fontSize:"small",color:"secondary"}),n.createElement(r.Stack,null,n.createElement(r.Typography,{variant:"caption",color:"text.primary"},o)))},n.createElement("div",{style:{pointerEvents:"initial",width:"100%"},onClick:e=>e.stopPropagation()},t)):n.createElement(n.Fragment,null,t))),v=({content:e,svgIcon:t,learnMoreButton:o,ctaButton:i})=>n.createElement(r.Card,{elevation:0,sx:{width:320}},n.createElement(r.CardContent,{sx:{pb:0}},n.createElement(r.Box,{display:"flex",alignItems:"start"},n.createElement(r.SvgIcon,{fontSize:"tiny",sx:{mr:.5}},t),n.createElement(r.Typography,{variant:"body2"},e))),(i||o)&&n.createElement(r.CardActions,null,o&&n.createElement(r.Button,{size:"small",color:"warning",href:o.href,target:"_blank"},o.label),i&&n.createElement(r.Button,{size:"small",color:"warning",variant:"contained",onClick:i.onClick},i.label))),y=(0,n.forwardRef)((({children:e,open:t,title:o,text:i,placement:a,width:l,offset:c},s)=>n.createElement(r.Infotip,{ref:s,open:t,placement:a,PopperProps:{sx:{width:l||"initial",".MuiTooltip-tooltip":{marginLeft:0,marginRight:0}},modifiers:c?[{name:"offset",options:{offset:c}}]:[]},arrow:!1,content:n.createElement(r.Alert,{color:"error",severity:"warning",variant:"standard",sx:e=>({".MuiAlert-icon":{fontSize:"1.25rem",marginRight:e.spacing(.5)}})},o?n.createElement(r.AlertTitle,null,o):null,n.createElement(r.Typography,{variant:"caption",sx:{color:"text.primary"}},i))},e))),x=({value:e,onSubmit:t,validation:r,onClick:o,onError:i})=>{const[a,l]=(0,n.useState)(!1),[c,s]=(0,n.useState)(null),u=b(a),m=t=>t!==e,d=()=>{u.current?.blur(),s(null),i?.(null),l(!1)},p={onClick:e=>{a&&e.stopPropagation(),o?.(e)},onKeyDown:e=>(e.stopPropagation(),["Escape"].includes(e.key)?d():["Enter"].includes(e.key)?(e.preventDefault(),(e=>{if(m(e)&&!c)try{t(e)}finally{d()}})(e.target.innerText)):void 0),onInput:e=>{const{innerText:t}=e.target;if(r){const e=m(t)?r(t):null;s(e),i?.(e)}},onBlur:d},f={value:e,role:"textbox",contentEditable:a,...a&&{suppressContentEditableWarning:!0}};return{ref:u,isEditing:a,openEditMode:()=>{l(!0)},closeEditMode:d,value:e,error:c,getProps:()=>({...p,...f})}},b=e=>{const t=(0,n.useRef)(null);return(0,n.useEffect)((()=>{e&&T(t.current)}),[e]),t},T=e=>{const t=getSelection();if(!t||!e)return;const n=document.createRange();n.selectNodeContents(e),t.removeAllRanges(),t.addRange(n)};(window.elementorV2=window.elementorV2||{}).editorUi=t}(),window.elementorV2.editorUi?.init?.();