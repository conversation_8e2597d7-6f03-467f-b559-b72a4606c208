/*! For license information please see editor.js.LICENSE.txt */
!function(){"use strict";var e={"./node_modules/react-dom/client.js":function(e,t,n){var o=n("react-dom"),r=o.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;t.createRoot=function(e,t){r.usingClientEntryPoint=!0;try{return o.createRoot(e,t)}finally{r.usingClientEntryPoint=!1}},t.hydrateRoot=function(e,t,n){r.usingClientEntryPoint=!0;try{return o.hydrateRoot(e,t,n)}finally{r.usingClientEntryPoint=!1}}},react:function(e){e.exports=window.React},"react-dom":function(e){e.exports=window.ReactDOM},"@elementor/editor-v1-adapters":function(e){e.exports=window.elementorV2.editorV1Adapters},"@elementor/locations":function(e){e.exports=window.elementorV2.locations},"@elementor/query":function(e){e.exports=window.elementorV2.query},"@elementor/store":function(e){e.exports=window.elementorV2.store},"@elementor/ui":function(e){e.exports=window.elementorV2.ui}},t={};function n(o){var r=t[o];if(void 0!==r)return r.exports;var i=t[o]={exports:{}};return e[o](i,i.exports,n),i.exports}n.d=function(e,t){for(var o in t)n.o(t,o)&&!n.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var o={};!function(){n.r(o),n.d(o,{injectIntoLogic:function(){return f},injectIntoTop:function(){return m},start:function(){return p}});var e=n("@elementor/locations"),t=n("react"),r=n("react-dom"),i=n("./node_modules/react-dom/client.js"),c=n("@elementor/editor-v1-adapters"),l=n("@elementor/query"),a=n("@elementor/store"),u=n("@elementor/ui"),{Slot:d,inject:m}=(0,e.createLocation)(),{Slot:s,inject:f}=(0,e.createLocation)();function y(){return t.createElement(t.Fragment,null,t.createElement(d,null),t.createElement("div",{style:{display:"none"}},t.createElement(s,null)))}function p(e){const n=(0,a.__createStore)(),o=(0,l.createQueryClient)();(0,c.__privateDispatchReadyEvent)(),function(e,t){let n;try{const o=(0,i.createRoot)(t);n=()=>{o.render(e)}}catch{n=()=>{r.render(e,t)}}n()}(t.createElement(a.__StoreProvider,{store:n},t.createElement(l.QueryClientProvider,{client:o},t.createElement(u.DirectionProvider,{rtl:"rtl"===window.document.dir},t.createElement(u.ThemeProvider,null,t.createElement(y,null))))),e)}}(),(window.elementorV2=window.elementorV2||{}).editor=o}(),window.elementorV2.editor?.init?.();