!function(){"use strict";var e={5338:function(e,t,n){var r=n(3617);t.H=r.createRoot,r.hydrateRoot},3617:function(e){e.exports=window.ReactDOM}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var i=t[r]={exports:{}};return e[r](i,i.exports,n),i.exports}n.d=function(e,t){for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};n.r(r),n.d(r,{injectIntoLogic:function(){return p},injectIntoTop:function(){return f},start:function(){return y}});var o=window.elementorV2.locations,i=window.React,c=n(3617),l=n(5338),a=window.elementorV2.editorV1Adapters,d=window.elementorV2.query,u=window.elementorV2.store,m=window.elementorV2.ui,{Slot:w,inject:f}=(0,o.createLocation)(),{Slot:s,inject:p}=(0,o.createLocation)();function v(){return i.createElement(i.Fragment,null,i.createElement(w,null),i.createElement("div",{style:{display:"none"}},i.createElement(s,null)))}function y(e){const t=(0,u.__createStore)(),n=(0,d.createQueryClient)();(0,a.__privateDispatchReadyEvent)(),function(e,t){let n;try{const r=(0,l.H)(t);n=()=>{r.render(e)}}catch{n=()=>{c.render(e,t)}}n()}(i.createElement(u.__StoreProvider,{store:t},i.createElement(d.QueryClientProvider,{client:n},i.createElement(m.DirectionProvider,{rtl:"rtl"===window.document.dir},i.createElement(m.ThemeProvider,null,i.createElement(v,null))))),e)}(window.elementorV2=window.elementorV2||{}).editor=r}(),window.elementorV2.editor?.init?.();