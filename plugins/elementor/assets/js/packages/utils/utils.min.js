!function(){"use strict";var e={d:function(t,n){for(var r in n)e.o(n,r)&&!e.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:n[r]})},o:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r:function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{ElementorError:function(){return n},createError:function(){return r},debounce:function(){return u},ensureError:function(){return o}});var n=class extends Error{context;code;constructor(e,{code:t,context:n=null,cause:r=null}){super(e,{cause:r}),this.context=n,this.code=t}},r=({code:e,message:t})=>class extends n{constructor({cause:n,context:r}={}){super(t,{cause:n,code:e,context:r})}},o=e=>{if(e instanceof Error)return e;let t,n=null;try{t=JSON.stringify(e)}catch(e){n=e,t="Unable to stringify the thrown value"}return new Error(`Unexpected non-error thrown: ${t}`,{cause:n})};function u(e,t){let n=null;const r=()=>{n&&(clearTimeout(n),n=null)},o=(...o)=>{r(),n=setTimeout((()=>{e(...o),n=null}),t)};return o.flush=(...t)=>{r(),e(...t)},o.cancel=r,o.pending=()=>!!n,o}(window.elementorV2=window.elementorV2||{}).utils=t}(),window.elementorV2.utils?.init?.();