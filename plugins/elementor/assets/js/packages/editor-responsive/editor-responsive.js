/*! For license information please see editor-responsive.js.LICENSE.txt */
!function(){"use strict";var e={react:function(e){e.exports=window.React},"@elementor/editor-v1-adapters":function(e){e.exports=window.elementorV2.editorV1Adapters},"@wordpress/i18n":function(e){e.exports=window.wp.i18n}},t={};function n(r){var i=t[r];if(void 0!==i)return i.exports;var o=t[r]={exports:{}};return e[r](o,o.exports,n),o.exports}n.d=function(e,t){for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};!function(){n.r(r),n.d(r,{getBreakpoints:function(){return d},getBreakpointsTree:function(){return l},useActivateBreakpoint:function(){return a},useActiveBreakpoint:function(){return s},useBreakpoints:function(){return u},useBreakpointsMap:function(){return p}});var e=n("@elementor/editor-v1-adapters"),t=n("@wordpress/i18n"),i=n("react");function o(){const{breakpoints:e}=window.elementor?.config?.responsive||{};if(!e||0===Object.entries(e).length)return{minWidth:[],defaults:[],maxWidth:[]};const n=[],r=[],i=[{id:"desktop",label:(0,t.__)("Desktop","elementor")}];Object.entries(e).forEach((([e,t])=>{if(!t.is_enabled)return;const o={id:e,label:t.label,width:t.value,type:"min"===t.direction?"min-width":"max-width"};o.width?"min-width"===o.type?n.push(o):"max-width"===o.type&&r.push(o):i.push(o)}));const o=(e,t)=>e.width&&t.width?t.width-e.width:0;return{minWidth:n.sort(o),defaults:i,maxWidth:r.sort(o)}}function d(){const{minWidth:e,defaults:t,maxWidth:n}=o();return[...e,...t,...n]}function u(){return(0,e.__privateUseListenTo)((0,e.v1ReadyEvent)(),d)}function s(){return(0,e.__privateUseListenTo)((0,e.windowEvent)("elementor/device-mode/change"),c)}function c(){const e=window;return e.elementor?.channels?.deviceMode?.request?.("currentMode")||null}function a(){return(0,i.useCallback)((t=>(0,e.__privateRunCommand)("panel/change-device-mode",{device:t})),[])}function p(){const e=u().map((e=>[e.id,e]));return Object.fromEntries(e)}function l(){const{minWidth:e,defaults:t,maxWidth:n}=o(),[r]=t,i={...r,children:[]},d=e=>{let t=i;e.forEach((e=>{const n={...e,children:[]};t.children.push(n),t=n}))};return d(e),d(n),i}}(),(window.elementorV2=window.elementorV2||{}).editorResponsive=r}(),window.elementorV2.editorResponsive?.init?.();