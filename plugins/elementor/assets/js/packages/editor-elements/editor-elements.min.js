!function(){"use strict";var e={d:function(t,n){for(var r in n)e.o(n,r)&&!e.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:n[r]})},o:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r:function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{ELEMENT_STYLE_CHANGE_EVENT:function(){return I},createElementStyle:function(){return O},deleteElementStyle:function(){return V},getAnchoredAncestorId:function(){return A},getAnchoredDescendantId:function(){return P},getContainer:function(){return l},getCurrentDocumentId:function(){return b},getElementLabel:function(){return _},getElementSetting:function(){return i},getElementStyles:function(){return E},getElements:function(){return h},getLinkInLinkRestriction:function(){return L},getSelectedElements:function(){return a},getWidgetsCache:function(){return d},isElementAnchored:function(){return R},selectElement:function(){return c},styleRerenderEvents:function(){return T},updateElementSettings:function(){return S},updateElementStyle:function(){return j},useElementSetting:function(){return u},useElementType:function(){return m},useParentElement:function(){return p},useSelectedElement:function(){return f}});var n=window.elementorV2.editorV1Adapters,r=window.elementorV2.utils,o=window.elementorV2.editorProps,s=window.elementorV2.editorStyles;function l(e){const t=window,n=t.elementor?.getContainer?.(e);return n??null}var c=e=>{try{const t=l(e);(0,n.__privateRunCommand)("document/elements/select",{container:t})}catch{}},i=(e,t)=>{const n=l(e),r=n?.settings?.get(t);return r??null},u=(e,t)=>(0,n.__privateUseListenTo)((0,n.commandEndEvent)("document/elements/set-settings"),(()=>i(e,t)),[e,t]);function d(){const e=window;return e?.elementor?.widgetsCache||null}function m(e){return(0,n.__privateUseListenTo)((0,n.commandEndEvent)("editor/documents/load"),(()=>{if(!e)return null;const t=d(),n=t?.[e];return n?.atomic_controls&&n?.atomic_props_schema?{key:e,controls:n.atomic_controls,propsSchema:n.atomic_props_schema,title:n.title}:null}),[e])}function a(){const e=window;return(e.elementor?.selection?.getElements?.()??[]).reduce(((e,t)=>{const n=t.model.get("widgetType")||t.model.get("elType");return n&&e.push({id:t.model.get("id"),type:n}),e}),[])}function f(){const e=(0,n.__privateUseListenTo)([(0,n.commandEndEvent)("document/elements/select"),(0,n.commandEndEvent)("document/elements/deselect"),(0,n.commandEndEvent)("document/elements/select-all"),(0,n.commandEndEvent)("document/elements/deselect-all")],a),[t]=e,r=m(t?.type);return 1===e.length&&r?{element:t,elementType:r}:{element:null,elementType:null}}function p(e){return(0,n.__privateUseListenTo)([(0,n.commandEndEvent)("document/elements/create")],(()=>{if(!e)return null;const t=window,n=t?.elementor?.getContainer?.(e);return n?n.parent:null}),[e])}var E=e=>{const t=l(e);return t?.model.get("styles")||null},g=(0,r.createError)({code:"element_not_found",message:"Element not found."}),y=(0,r.createError)({code:"style_not_found",message:"Style not found."}),w=(0,r.createError)({code:"element_type_not_exists",message:"Element type does not exist."}),v=(0,r.createError)({code:"element_label_not_exists",message:"Element label does not exist."});function _(e){const t=l(e),n=t?.model.get("widgetType")||t?.model.get("elType");if(!n)throw new w({context:{elementId:e}});const r=d()?.[n]?.title;if(!r)throw new v({context:{elementType:n}});return r}function h(e){const t=e?l(e):function(){const e=window;return e.elementor?.documents?.getCurrent?.()?.container??null}();if(!t)return[];const n=[...t.model.get("elements")??[]].flatMap((e=>h(e.get("id"))));return[t,...n]}function b(){const e=window;return e.elementor?.documents?.getCurrentId?.()??null}var S=({id:e,props:t,withHistory:r=!0})=>{const o={container:l(e),settings:{...t}};r?(0,n.__privateRunCommandSync)("document/elements/settings",o):(0,n.__privateRunCommandSync)("document/elements/set-settings",o,{internal:!0})},I="elementor/editor-v2/editor-elements/style",T=[(0,n.commandEndEvent)("document/elements/create"),(0,n.commandEndEvent)("document/elements/duplicate"),(0,n.commandEndEvent)("document/elements/import"),(0,n.commandEndEvent)("document/elements/paste"),(0,n.windowEvent)(I)];function C(e,t){const r=l(e);if(!r)throw new g({context:{elementId:e}});const s=Object.keys(r.model.get("styles")??{}),c=function(e,t){const n=structuredClone(e.model.get("styles"))??{},r=Object.entries(t(n)).map((([e,t])=>(t.variants=function(e){return e.variants.filter((({props:e})=>Object.keys(e).length>0))}(t),[e,t]))).filter((([,e])=>!function(e){return 0===e.variants.length}(e))),o=Object.fromEntries(r);return e.model.set("styles",o),o}(r,t);return function(e,{oldIds:t,newIds:n}){const r=t.filter((e=>!n.includes(e))),s=structuredClone(function(e){return Object.entries(e.settings.toJSON()).filter((e=>{const[,t]=e;return o.classesPropTypeUtil.isValid(t)}))}(e));s.forEach((([,e])=>{e.value=e.value.filter((e=>!r.includes(e)))})),S({id:e.id,props:Object.fromEntries(s),withHistory:!1})}(r,{oldIds:s,newIds:Object.keys(c)}),window.dispatchEvent(new CustomEvent(I)),(0,n.__privateRunCommandSync)("document/save/set-is-modified",{status:!0},{internal:!0}),c}function O({styleId:e,elementId:t,classesProp:n,label:r,meta:l,props:c,additionalVariants:u=[]}){let d=e;return C(t,(e=>{d??=(0,s.generateId)(`e-${t}-`,Object.keys(e));const m=[{meta:l,props:c},...u];return e[d]={id:d,label:r,type:"class",variants:m},function(e,t,n){const r=i(e,t),s=o.classesPropTypeUtil.create((e=>[...e??[],n]),{base:r});S({id:e,props:{[t]:s},withHistory:!1})}(t,n,d),e})),d}function j(e){C(e.elementId,(t=>{const n=t[e.styleId];if(!n)throw new y({context:{styleId:e.styleId}});const r=(0,s.getVariantByMeta)(n,e.meta);return r?r.props=(0,o.mergeProps)(r.props,e.props):n.variants.push({meta:e.meta,props:e.props}),t}))}function V(e,t){C(e,(e=>(delete e[t],e)))}function L(e){const t=P(e);if(t)return{shouldRestrict:!0,reason:"descendant",elementId:t};const n=A(e);return n?{shouldRestrict:!0,reason:"ancestor",elementId:n}:{shouldRestrict:!1}}function P(e){const t=N(e);if(!t)return null;for(const n of Array.from(t.querySelectorAll("a"))){const t=k(n);if(t!==e)return t}return null}function A(e){const t=N(e);if(!t||null===t.parentElement)return null;const n=t.parentElement.closest("a");return n?k(n):null}function R(e){const t=N(e);return!!t&&(!!U(t.tagName)||x(t))}function x(e){for(const t of e.children)if(!M(t)){if(U(t.tagName))return!0;if(x(t))return!0}return!1}function k(e){return e.closest("[data-id]")?.dataset.id||null}function N(e){try{return l(e)?.view?.el||null}catch{return null}}function U(e){return"a"===e.toLowerCase()}function M(e){return e.hasAttribute("data-id")}(window.elementorV2=window.elementorV2||{}).editorElements=t}(),window.elementorV2.editorElements?.init?.();