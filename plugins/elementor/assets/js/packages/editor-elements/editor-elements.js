/*! For license information please see editor-elements.js.LICENSE.txt */
!function(){"use strict";var e={"@elementor/editor-props":function(e){e.exports=window.elementorV2.editorProps},"@elementor/editor-styles":function(e){e.exports=window.elementorV2.editorStyles},"@elementor/editor-v1-adapters":function(e){e.exports=window.elementorV2.editorV1Adapters},"@elementor/utils":function(e){e.exports=window.elementorV2.utils}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var s=t[r]={exports:{}};return e[r](s,s.exports,n),s.exports}n.d=function(e,t){for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};!function(){n.r(r),n.d(r,{ELEMENT_STYLE_CHANGE_EVENT:function(){return I},createElementStyle:function(){return x},deleteElementStyle:function(){return j},getAnchoredAncestorId:function(){return P},getAnchoredDescendantId:function(){return L},getContainer:function(){return l},getCurrentDocumentId:function(){return b},getElementLabel:function(){return _},getElementSetting:function(){return u},getElementStyles:function(){return E},getElements:function(){return h},getLinkInLinkRestriction:function(){return V},getSelectedElements:function(){return a},getWidgetsCache:function(){return d},isElementAnchored:function(){return A},selectElement:function(){return i},styleRerenderEvents:function(){return T},updateElementSettings:function(){return S},updateElementStyle:function(){return O},useElementSetting:function(){return c},useElementType:function(){return m},useParentElement:function(){return p},useSelectedElement:function(){return f}});var e=n("@elementor/editor-v1-adapters"),t=n("@elementor/utils"),o=n("@elementor/editor-props"),s=n("@elementor/editor-styles");function l(e){const t=window,n=t.elementor?.getContainer?.(e);return n??null}var i=t=>{try{const n=l(t);(0,e.__privateRunCommand)("document/elements/select",{container:n})}catch{}},u=(e,t)=>{const n=l(e),r=n?.settings?.get(t);return r??null},c=(t,n)=>(0,e.__privateUseListenTo)((0,e.commandEndEvent)("document/elements/set-settings"),(()=>u(t,n)),[t,n]);function d(){const e=window;return e?.elementor?.widgetsCache||null}function m(t){return(0,e.__privateUseListenTo)((0,e.commandEndEvent)("editor/documents/load"),(()=>{if(!t)return null;const e=d(),n=e?.[t];return n?.atomic_controls&&n?.atomic_props_schema?{key:t,controls:n.atomic_controls,propsSchema:n.atomic_props_schema,title:n.title}:null}),[t])}function a(){const e=window;return(e.elementor?.selection?.getElements?.()??[]).reduce(((e,t)=>{const n=t.model.get("widgetType")||t.model.get("elType");return n&&e.push({id:t.model.get("id"),type:n}),e}),[])}function f(){const t=(0,e.__privateUseListenTo)([(0,e.commandEndEvent)("document/elements/select"),(0,e.commandEndEvent)("document/elements/deselect"),(0,e.commandEndEvent)("document/elements/select-all"),(0,e.commandEndEvent)("document/elements/deselect-all")],a),[n]=t,r=m(n?.type);return 1===t.length&&r?{element:n,elementType:r}:{element:null,elementType:null}}function p(t){return(0,e.__privateUseListenTo)([(0,e.commandEndEvent)("document/elements/create")],(()=>{if(!t)return null;const e=window,n=e?.elementor?.getContainer?.(t);return n?n.parent:null}),[t])}var E=e=>{const t=l(e);return t?.model.get("styles")||null},y=(0,t.createError)({code:"element_not_found",message:"Element not found."}),g=(0,t.createError)({code:"style_not_found",message:"Style not found."}),w=(0,t.createError)({code:"element_type_not_exists",message:"Element type does not exist."}),v=(0,t.createError)({code:"element_label_not_exists",message:"Element label does not exist."});function _(e){const t=l(e),n=t?.model.get("widgetType")||t?.model.get("elType");if(!n)throw new w({context:{elementId:e}});const r=d()?.[n]?.title;if(!r)throw new v({context:{elementType:n}});return r}function h(e){const t=e?l(e):function(){const e=window;return e.elementor?.documents?.getCurrent?.()?.container??null}();if(!t)return[];const n=[...t.model.get("elements")??[]].flatMap((e=>h(e.get("id"))));return[t,...n]}function b(){const e=window;return e.elementor?.documents?.getCurrentId?.()??null}var S=({id:t,props:n,withHistory:r=!0})=>{const o={container:l(t),settings:{...n}};r?(0,e.__privateRunCommandSync)("document/elements/settings",o):(0,e.__privateRunCommandSync)("document/elements/set-settings",o,{internal:!0})},I="elementor/editor-v2/editor-elements/style",T=[(0,e.commandEndEvent)("document/elements/create"),(0,e.commandEndEvent)("document/elements/duplicate"),(0,e.commandEndEvent)("document/elements/import"),(0,e.commandEndEvent)("document/elements/paste"),(0,e.windowEvent)(I)];function C(t,n){const r=l(t);if(!r)throw new y({context:{elementId:t}});const s=Object.keys(r.model.get("styles")??{}),i=function(e,t){const n=structuredClone(e.model.get("styles"))??{},r=Object.entries(t(n)).map((([e,t])=>(t.variants=function(e){return e.variants.filter((({props:e})=>Object.keys(e).length>0))}(t),[e,t]))).filter((([,e])=>!function(e){return 0===e.variants.length}(e))),o=Object.fromEntries(r);return e.model.set("styles",o),o}(r,n);return function(e,{oldIds:t,newIds:n}){const r=t.filter((e=>!n.includes(e))),s=structuredClone(function(e){return Object.entries(e.settings.toJSON()).filter((e=>{const[,t]=e;return o.classesPropTypeUtil.isValid(t)}))}(e));s.forEach((([,e])=>{e.value=e.value.filter((e=>!r.includes(e)))})),S({id:e.id,props:Object.fromEntries(s),withHistory:!1})}(r,{oldIds:s,newIds:Object.keys(i)}),window.dispatchEvent(new CustomEvent(I)),(0,e.__privateRunCommandSync)("document/save/set-is-modified",{status:!0},{internal:!0}),i}function x({styleId:e,elementId:t,classesProp:n,label:r,meta:l,props:i,additionalVariants:c=[]}){let d=e;return C(t,(e=>{d??=(0,s.generateId)(`e-${t}-`,Object.keys(e));const m=[{meta:l,props:i},...c];return e[d]={id:d,label:r,type:"class",variants:m},function(e,t,n){const r=u(e,t),s=o.classesPropTypeUtil.create((e=>[...e??[],n]),{base:r});S({id:e,props:{[t]:s},withHistory:!1})}(t,n,d),e})),d}function O(e){C(e.elementId,(t=>{const n=t[e.styleId];if(!n)throw new g({context:{styleId:e.styleId}});const r=(0,s.getVariantByMeta)(n,e.meta);return r?r.props=(0,o.mergeProps)(r.props,e.props):n.variants.push({meta:e.meta,props:e.props}),t}))}function j(e,t){C(e,(e=>(delete e[t],e)))}function V(e){const t=L(e);if(t)return{shouldRestrict:!0,reason:"descendant",elementId:t};const n=P(e);return n?{shouldRestrict:!0,reason:"ancestor",elementId:n}:{shouldRestrict:!1}}function L(e){const t=N(e);if(!t)return null;for(const n of Array.from(t.querySelectorAll("a"))){const t=k(n);if(t!==e)return t}return null}function P(e){const t=N(e);if(!t||null===t.parentElement)return null;const n=t.parentElement.closest("a");return n?k(n):null}function A(e){const t=N(e);return!!t&&(!!U(t.tagName)||R(t))}function R(e){for(const t of e.children)if(!M(t)){if(U(t.tagName))return!0;if(R(t))return!0}return!1}function k(e){return e.closest("[data-id]")?.dataset.id||null}function N(e){try{return l(e)?.view?.el||null}catch{return null}}function U(e){return"a"===e.toLowerCase()}function M(e){return e.hasAttribute("data-id")}}(),(window.elementorV2=window.elementorV2||{}).editorElements=r}(),window.elementorV2.editorElements?.init?.();