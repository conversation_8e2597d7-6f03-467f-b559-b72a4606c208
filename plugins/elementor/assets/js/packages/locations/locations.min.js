!function(){"use strict";var e={d:function(n,t){for(var r in t)e.o(t,r)&&!e.o(n,r)&&Object.defineProperty(n,r,{enumerable:!0,get:t[r]})},o:function(e,n){return Object.prototype.hasOwnProperty.call(e,n)},r:function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},n={};e.r(n),e.d(n,{__flushAllInjections:function(){return u},createLocation:function(){return p},createReplaceableLocation:function(){return d}});var t=window.React,r=class extends t.Component{state={hasError:!1};static getDerivedStateFromError(){return{hasError:!0}}render(){return this.state.hasError?this.props.fallback:this.props.children}};function o({children:e}){return t.createElement(r,{fallback:null},t.createElement(t.Suspense,{fallback:null},e))}var i=10,c=[];function u(){c.forEach((e=>e()))}function a(e){return()=>[...e.values()].sort(((e,n)=>e.priority-n.priority))}function l(e){return()=>(0,t.useMemo)((()=>e()),[])}function s(e){return n=>t.createElement(o,null,t.createElement(e,{...n}))}function p(){const e=new Map,n=a(e),r=l(n),o=function(e){return n=>{const r=e();return t.createElement(t.Fragment,null,r.map((({id:e,component:r})=>t.createElement(r,{...n,key:e}))))}}(r),u=function(e){return({component:n,id:t,options:r={}})=>{!e.has(t)||r?.overwrite?e.set(t,{id:t,component:s(n),priority:r.priority??i}):console.warn(`An injection with the id "${t}" already exists. Did you mean to use "options.overwrite"?`)}}(e);return c.push((()=>e.clear())),{inject:u,getInjections:n,useInjections:r,Slot:o}}function d(){const e=new Map,n=a(e),r=l(n),o=function(e){return n=>{const r=e(),{component:o}=r.find((({condition:e})=>e?.(n)))??{};return o?t.createElement(o,{...n}):n.children}}(r),u=function(e){return({component:n,id:t,condition:r=()=>!0,options:o={}})=>{e.set(t,{id:t,component:s(n),condition:r,priority:o.priority??i})}}(e);return c.push((()=>e.clear())),{getInjections:n,useInjections:r,inject:u,Slot:o}}(window.elementorV2=window.elementorV2||{}).locations=n}(),window.elementorV2.locations?.init?.();