<?php
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}
/**
 * This file is generated by Webpack, do not edit it directly.
 */
return [
	'handle' => 'elementor-v2-editor-global-classes',
	'deps' => [
		'elementor-v2-editor',
		'elementor-v2-editor-current-user',
		'elementor-v2-editor-documents',
		'elementor-v2-editor-editing-panel',
		'elementor-v2-editor-panels',
		'elementor-v2-editor-props',
		'elementor-v2-editor-styles',
		'elementor-v2-editor-styles-repository',
		'elementor-v2-editor-ui',
		'elementor-v2-editor-v1-adapters',
		'elementor-v2-http-client',
		'elementor-v2-icons',
		'elementor-v2-query',
		'elementor-v2-store',
		'elementor-v2-ui',
		'elementor-v2-utils',
		'react',
		'wp-i18n',
	],
];
