!function(){"use strict";var e={d:function(t,n){for(var a in n)e.o(n,a)&&!e.o(t,a)&&Object.defineProperty(t,a,{enumerable:!0,get:n[a]})},o:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r:function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{init:function(){return we}});var n=window.elementorV2.editor,a=window.elementorV2.editorEditingPanel,r=window.elementorV2.editorPanels,l=window.elementorV2.editorStylesRepository,o=window.elementorV2.editorV1Adapters,i=window.elementorV2.store,s=window.React,c=window.elementorV2.editorDocuments,d=window.elementorV2.ui,m=window.wp.i18n,p=window.elementorV2.editorStyles,u=window.elementorV2.utils,g=window.elementorV2.editorProps,y=window.elementorV2.editorUi,h=window.elementorV2.icons,_=window.elementorV2.query,b=window.elementorV2.httpClient,E=window.elementorV2.editorCurrentUser,v="elementor_global_classes_update_class",w=(0,u.createError)({code:"global_class_not_found",message:"Global class not found."}),f=(0,u.createError)({code:"global_class_label_already_exists",message:"Class with this name already exists."}),x="globalClasses",S=(0,i.__createSlice)({name:x,initialState:{data:{items:{},order:[]},initialData:{frontend:{items:{},order:[]},preview:{items:{},order:[]}},isDirty:!1},reducers:{load(e,{payload:{frontend:t,preview:n}}){e.initialData.frontend=t,e.initialData.preview=n,e.data=n,e.isDirty=!1},add(e,{payload:t}){e.data.items[t.id]=t,e.data.order.unshift(t.id),e.isDirty=!0},delete(e,{payload:t}){e.data.items=Object.fromEntries(Object.entries(e.data.items).filter((([e])=>e!==t))),e.data.order=e.data.order.filter((e=>e!==t)),e.isDirty=!0},setOrder(e,{payload:t}){e.data.order=t,e.isDirty=!0},update(e,{payload:t}){const n={...e.data.items[t.style.id],...t.style};e.data.items[t.style.id]=n,e.isDirty=!0},updateProps(e,{payload:t}){const n=e.data.items[t.id];if(!n)throw new w({context:{styleId:t.id}});const a=(0,p.getVariantByMeta)(n,t.meta);a?a.props=(0,g.mergeProps)(a.props,t.props):n.variants.push({meta:t.meta,props:t.props}),e.isDirty=!0},reset(e,{payload:{context:t}}){"frontend"===t&&(e.initialData.frontend=e.data,e.isDirty=!1),e.initialData.preview=e.data}}}),C=e=>e[x].data,D=e=>e[x].initialData.frontend,P=e=>e[x].initialData.preview,T=(0,i.__createSelector)(C,(({order:e})=>e)),A=(0,i.__createSelector)(C,(({items:e})=>e)),I=e=>e[x].isDirty,k=(0,i.__createSelector)(A,T,((e,t)=>t.map((t=>e[t])))),M=(e,t)=>e[x].data.items[t]??null,V=(0,l.createStylesProvider)({key:"global-classes",priority:30,limit:50,labels:{singular:(0,m.__)("class","elementor"),plural:(0,m.__)("classes","elementor")},subscribe:e=>(0,i.__subscribeWithSelector)((e=>e.globalClasses),e),capabilities:(()=>{if((0,o.isExperimentActive)("global_classes_should_enforce_capabilities"))return{update:v,create:v,delete:v,updateProps:v}})(),actions:{all:()=>k((0,i.__getState)()),get:e=>M((0,i.__getState)(),e),resolveCssName:e=>(0,o.isExperimentActive)("e_v_3_30")?M((0,i.__getState)(),e)?.label??e:e,create:e=>{const t=A((0,i.__getState)());if(Object.values(t).map((e=>e.label)).includes(e))throw new f({context:{label:e}});const n=Object.keys(t),a=(0,p.generateId)("g-",n);return(0,i.__dispatch)(S.actions.add({id:a,type:"class",label:e,variants:[]})),a},update:e=>{(0,i.__dispatch)(S.actions.update({style:e}))},delete:e=>{(0,i.__dispatch)(S.actions.delete(e))},updateProps:e=>{(0,i.__dispatch)(S.actions.updateProps({id:e.id,meta:e.meta,props:e.props}))}}}),O=()=>(0,i.__useSelector)(I),B="/global-classes",j={all:(e="preview")=>(0,b.httpService)().get("elementor/v1"+B,{params:{context:e}}),publish:e=>(0,b.httpService)().put("elementor/v1"+B,e,{params:{context:"frontend"}}),saveDraft:e=>(0,b.httpService)().put("elementor/v1"+B,e,{params:{context:"preview"}})};async function z({context:e}){const t=C((0,i.__getState)());"preview"===e?await j.saveDraft({items:t.items,order:t.order,changes:R(t,P((0,i.__getState)()))}):await j.publish({items:t.items,order:t.order,changes:R(t,D((0,i.__getState)()))}),(0,i.__dispatch)(S.actions.reset({context:e}))}function R(e,t){const n=Object.keys(e.items),a=Object.keys(t.items);return{added:n.filter((e=>!a.includes(e))),deleted:a.filter((e=>!n.includes(e))),modified:n.filter((n=>n in t.items&&L(e.items[n])!==L(t.items[n])))}}function L(e){return JSON.stringify(e,((e,t)=>function(e){return!!e&&"object"==typeof e&&!Array.isArray(e)}(t)?Object.keys(t).sort().reduce(((e,n)=>(e[n]=t[n],e)),{}):t))}var W=()=>{const[e,t]=(0,E.useSuppressedMessage)("global-class-manager"),[n,a]=(0,s.useState)(!e);return s.createElement(y.IntroductionModal,{open:n,title:(0,m.__)("Class Manager","elementor"),handleClose:e=>{e||t(),a(!1)}},s.createElement(d.Image,{sx:{width:"100%",aspectRatio:"16 / 9"},src:"https://assets.elementor.com/packages/v1/images/class-manager-intro.svg",alt:""}),s.createElement(F,null))},F=()=>s.createElement(d.Box,{p:3},s.createElement(d.Typography,{variant:"body2"},(0,m.__)("The Class Manager lets you see all the classes you've created, plus adjust their priority, rename them, and delete unused classes to keep your CSS structured.","elementor")),s.createElement("br",null),s.createElement(d.Typography,{variant:"body2"},(0,m.__)("Remember, when editing an item within a specific class, any changes you make will apply across all elements in that class.","elementor"))),$=!1,N=({sx:e,...t})=>s.createElement(h.ColorSwatchIcon,{sx:{transform:"rotate(90deg)",...e},...t}),U=(0,s.createContext)(null),G=({children:e})=>{const[t,n]=(0,s.useState)(null);return s.createElement(U.Provider,{value:{openDialog:e=>{n(e)},closeDialog:()=>{n(null)},dialogProps:t}},e,!!t&&s.createElement(Y,{...t}))},H="delete-class-dialog",Y=({label:e,id:t})=>{const{closeDialog:n}=q();return s.createElement(d.Dialog,{open:!0,onClose:n,"aria-labelledby":H,maxWidth:"xs"},s.createElement(d.DialogTitle,{id:H,display:"flex",alignItems:"center",gap:1,sx:{lineHeight:1}},s.createElement(h.AlertOctagonFilledIcon,{color:"error"}),(0,m.__)("Delete this class?","elementor")),s.createElement(d.DialogContent,null,s.createElement(d.DialogContentText,{variant:"body2",color:"textPrimary"},(0,m.__)("Deleting","elementor"),s.createElement(d.Typography,{variant:"subtitle2",component:"span"}," ",e," "),(0,m.__)("will permanently remove it from your project and may affect the design across all elements using it. This action cannot be undone.","elementor"))),s.createElement(d.DialogActions,null,s.createElement(d.Button,{color:"secondary",onClick:n},(0,m.__)("Not now","elementor")),s.createElement(d.Button,{variant:"contained",color:"error",onClick:()=>{(e=>{(0,i.__dispatch)(S.actions.delete(e)),$=!0})(t),n()}},(0,m.__)("Delete","elementor"))))},q=()=>{const e=(0,s.useContext)(U);if(!e)throw new Error("useDeleteConfirmation must be used within a DeleteConfirmationProvider");return e},J=e=>s.createElement(d.UnstableSortableProvider,{restrictAxis:!0,variant:"static",dragPlaceholderStyle:{opacity:"1"},...e}),K=e=>s.createElement(Q,{...e,role:"button",className:"class-item-sortable-trigger"},s.createElement(h.GripVerticalIcon,{fontSize:"tiny"})),X=({children:e,id:t,...n})=>s.createElement(d.UnstableSortableItem,{...n,id:t,render:({itemProps:t,isDragged:n,triggerProps:a,itemStyle:r,triggerStyle:l,dropIndicationStyle:o,showDropIndication:i,isDragOverlay:c,isDragPlaceholder:m})=>s.createElement(d.Box,{...t,style:r,component:"li",role:"listitem",sx:{backgroundColor:c?"background.paper":void 0}},e({itemProps:t,isDragged:n,triggerProps:a,itemStyle:r,triggerStyle:l,isDragPlaceholder:m}),i&&s.createElement(Z,{style:o}))}),Q=(0,d.styled)("div")((({theme:e})=>({position:"absolute",left:0,top:"50%",transform:`translate( -${e.spacing(1.5)}, -50% )`,color:e.palette.action.active}))),Z=(0,d.styled)(d.Box)`
	width: 100%;
	height: 1px;
	background-color: ${({theme:e})=>e.palette.text.primary};
`,ee=({disabled:e})=>{const t=(0,i.__useSelector)(k),n=(0,i.__useDispatch)(),[a,r]=te();return t?.length?s.createElement(G,null,s.createElement(d.List,{sx:{display:"flex",flexDirection:"column",gap:.5}},s.createElement(J,{value:a,onChange:r},t?.map((({id:t,label:a})=>{const r=e=>{n(S.actions.update({style:{id:t,label:e}}))};return s.createElement(X,{key:t,id:t},(({isDragged:n,isDragPlaceholder:l,triggerProps:o,triggerStyle:i})=>s.createElement(ne,{id:t,label:a,renameClass:r,selected:n,disabled:e||l,sortableTriggerProps:{...o,style:i}})))}))))):s.createElement(re,null)},te=()=>{const e=(0,i.__useDispatch)();return[(0,i.__useSelector)(T),t=>{e(S.actions.setOrder(t))}]},ne=({id:e,label:t,renameClass:n,selected:a,disabled:r,sortableTriggerProps:l})=>{const o=(0,s.useRef)(null),{ref:i,openEditMode:c,isEditing:p,error:u,getProps:g}=(0,y.useEditable)({value:t,onSubmit:n,validation:se}),{openDialog:_}=q(),b=(0,d.usePopupState)({variant:"popover",disableAutoFocus:!0}),E=(a||b.isOpen)&&!r;return s.createElement(s.Fragment,null,s.createElement(d.Stack,{p:0},s.createElement(y.WarningInfotip,{open:Boolean(u),text:u??"",placement:"bottom",width:o.current?.getBoundingClientRect().width,offset:[0,-15]},s.createElement(ae,{ref:o,dense:!0,disableGutters:!0,showActions:E||p,shape:"rounded",onDoubleClick:c,selected:E,disabled:r,focusVisibleClassName:"visible-class-item"},s.createElement(K,{...l}),s.createElement(oe,{isActive:p,isError:!!u},p?s.createElement(y.EditableField,{ref:i,as:d.Typography,variant:"caption",...g()}):s.createElement(y.EllipsisWithTooltip,{title:t,as:d.Typography,variant:"caption"})),s.createElement(d.Tooltip,{placement:"top",className:"class-item-more-actions",title:(0,m.__)("More actions","elementor")},s.createElement(d.IconButton,{size:"tiny",...(0,d.bindTrigger)(b),"aria-label":"More actions"},s.createElement(h.DotsVerticalIcon,{fontSize:"tiny"})))))),s.createElement(d.Menu,{...(0,d.bindMenu)(b),anchorOrigin:{vertical:"bottom",horizontal:"right"},transformOrigin:{vertical:"top",horizontal:"right"}},s.createElement(y.MenuListItem,{sx:{minWidth:"160px"},onClick:()=>{b.close(),c()}},s.createElement(d.Typography,{variant:"caption",sx:{color:"text.primary"}},(0,m.__)("Rename","elementor"))),s.createElement(y.MenuListItem,{onClick:()=>{b.close(),_({id:e,label:t})}},s.createElement(d.Typography,{variant:"caption",sx:{color:"error.light"}},(0,m.__)("Delete","elementor")))))},ae=(0,d.styled)(d.ListItemButton,{shouldForwardProp:e=>!["showActions"].includes(e)})((({showActions:e})=>`\n\tmin-height: 36px;\n\n\t&.visible-class-item {\n\t\tbox-shadow: none !important;\n\t}\n\n\t.class-item-more-actions, .class-item-sortable-trigger {\n\t\tvisibility: ${e?"visible":"hidden"};\n\t}\n\n\t.class-item-sortable-trigger {\n\t\tvisibility: ${e?"visible":"hidden"};\n\t}\n\n\t&:hover&:not(:disabled) {\n\t\t.class-item-more-actions, .class-item-sortable-trigger  {\n\t\t\tvisibility: visible;\n\t\t}\n\t}\n`)),re=()=>s.createElement(d.Stack,{alignItems:"center",gap:1.5,pt:10,px:.5,maxWidth:"260px",margin:"auto"},s.createElement(N,{fontSize:"large"}),s.createElement(le,{variant:"subtitle2",component:"h2",color:"text.secondary"},(0,m.__)("There are no global classes yet.","elementor")),s.createElement(d.Typography,{align:"center",variant:"caption",color:"text.secondary"},(0,m.__)("CSS classes created in the editor panel will appear here. Once they are available, you can arrange their hierarchy, rename them, or delete them as needed.","elementor"))),le=(0,d.styled)(d.Typography)((({theme:e,variant:t})=>({"&.MuiTypography-root":{...e.typography[t]}}))),oe=(0,d.styled)(d.Box,{shouldForwardProp:e=>!["isActive","isError"].includes(e)})((({theme:e,isActive:t,isError:n})=>({display:"flex",width:"100%",flexGrow:1,borderRadius:e.spacing(.5),border:ie({isActive:t,isError:n,theme:e}),padding:`0 ${e.spacing(1)}`,marginLeft:t?e.spacing(1):0,minWidth:0}))),ie=({isActive:e,isError:t,theme:n})=>t?`2px solid ${n.palette.error.main}`:e?`2px solid ${n.palette.secondary.main}`:"none",se=e=>{const t=(0,l.validateStyleLabel)(e,"rename");return t.isValid?null:t.errorMessage},ce="save-changes-dialog",de=({children:e,onClose:t})=>s.createElement(d.Dialog,{open:!0,onClose:t,"aria-labelledby":ce,maxWidth:"xs"},e);de.Title=({children:e})=>s.createElement(d.DialogTitle,{id:ce,display:"flex",alignItems:"center",gap:1,sx:{lineHeight:1}},s.createElement(h.AlertTriangleFilledIcon,{color:"secondary"}),e),de.Content=({children:e})=>s.createElement(d.DialogContent,null,e),de.ContentText=e=>s.createElement(d.DialogContentText,{variant:"body2",color:"textPrimary",display:"flex",flexDirection:"column",...e}),de.Actions=({actions:e})=>{const[t,n]=(0,s.useState)(!1),{cancel:a,confirm:r}=e;return s.createElement(d.DialogActions,null,s.createElement(d.Button,{variant:"text",color:"secondary",onClick:a.action},a.label),s.createElement(d.Button,{variant:"contained",color:"secondary",onClick:async()=>{n(!0),await r.action(),n(!1)},loading:t},r.label))};var me=()=>{const[e,t]=(0,s.useState)(!1);return{isOpen:e,open:()=>t(!0),close:()=>t(!1)}},pe="global-classes-manager",{panel:ue,usePanelActions:ge}=(0,r.__createPanel)({id:pe,component:function(){const e=O(),{close:t}=ge(),{open:n,close:a,isOpen:l}=me(),{mutateAsync:o,isPending:i}=be();return _e(),s.createElement(y.ThemeProvider,null,s.createElement(d.ErrorBoundary,{fallback:s.createElement(he,null)},s.createElement(r.Panel,null,s.createElement(r.PanelHeader,null,s.createElement(d.Stack,{p:1,pl:2,width:"100%",direction:"row",alignItems:"center"},s.createElement(r.PanelHeaderTitle,{sx:{display:"flex",alignItems:"center",gap:.5}},s.createElement(N,{fontSize:"inherit"}),(0,m.__)("Class Manager","elementor")),s.createElement(ye,{sx:{marginLeft:"auto"},disabled:i,onClose:()=>{e?n():t()}}))),s.createElement(r.PanelBody,{px:2},s.createElement(ee,{disabled:i})),s.createElement(r.PanelFooter,null,s.createElement(d.Button,{fullWidth:!0,size:"small",color:"global",variant:"contained",onClick:o,disabled:!e,loading:i},(0,m.__)("Save changes","elementor"))))),s.createElement(W,null),l&&s.createElement(de,null,s.createElement(de.Title,null,(0,m.__)("You have unsaved changes","elementor")),s.createElement(de.Content,null,s.createElement(de.ContentText,null,(0,m.__)("You have unsaved changes in the Class Manager.","elementor")),s.createElement(de.ContentText,null,(0,m.__)("To avoid losing your updates, save your changes before leaving.","elementor"))),s.createElement(de.Actions,{actions:{cancel:{label:(0,m.__)("Cancel","elementor"),action:a},confirm:{label:(0,m.__)("Save & Continue","elementor"),action:async()=>{await o(),a(),t()}}}})))},allowedEditModes:["edit",pe],onOpen:()=>{(0,o.changeEditMode)(pe),function(){const e=window;e.$e?.components?.get?.("panel")?.blockUserInteractions?.()}()},onClose:()=>{(0,o.changeEditMode)("edit"),function(){const e=window;e.$e?.components?.get?.("panel")?.unblockUserInteractions?.()}()}}),ye=({onClose:e,...t})=>s.createElement(d.IconButton,{size:"small",color:"secondary",onClick:e,"aria-label":"Close",...t},s.createElement(h.XIcon,{fontSize:"small"})),he=()=>s.createElement(d.Box,{role:"alert",sx:{minHeight:"100%",p:2}},s.createElement(d.Alert,{severity:"error",sx:{mb:2,maxWidth:400,textAlign:"center"}},s.createElement("strong",null,(0,m.__)("Something went wrong","elementor")))),_e=()=>{const e=O();(0,s.useEffect)((()=>{const t=t=>{e&&t.preventDefault()};return window.addEventListener("beforeunload",t),()=>{window.removeEventListener("beforeunload",t)}}),[e])},be=()=>(0,_.useMutation)({mutationFn:()=>z({context:"frontend"}),onSuccess:async()=>{(0,c.setDocumentModifiedStatus)(!1),$&&await(async()=>{await(()=>{const e=(0,c.getCurrentDocument)();return(0,c.getV1DocumentsManager)().invalidateCache(),(0,o.__privateRunCommand)("editor/documents/switch",{id:e?.id,shouldScroll:!1,shouldNavigateToDefaultRoute:!1})})(),$=!1})()}}),Ee=()=>{const e=(0,c.__useActiveDocument)(),{open:t}=ge(),{save:n}=(0,c.__useActiveDocumentActions)(),{open:a,close:r,isOpen:o}=me(),{userCan:i}=(0,l.useUserStylesCapability)();return i(V.getKey()).update?s.createElement(s.Fragment,null,s.createElement(d.Tooltip,{title:(0,m.__)("Class Manager","elementor"),placement:"top"},s.createElement(d.IconButton,{size:"tiny",onClick:()=>{e?.isDirty?a():t()},sx:{marginInlineEnd:-.75}},s.createElement(N,{fontSize:"tiny"}))),o&&s.createElement(de,null,s.createElement(de.Title,null,(0,m.__)("You have unsaved changes","elementor")),s.createElement(de.Content,null,s.createElement(de.ContentText,{sx:{mb:2}},(0,m.__)("To open the Class Manager, save your page first. You can't continue without saving.","elementor"))),s.createElement(de.Actions,{actions:{cancel:{label:(0,m.__)("Stay here","elementor"),action:r},confirm:{label:(0,m.__)("Save & Continue","elementor"),action:async()=>{await n(),r(),t()}}}}))):null};function ve(){const e=(0,i.__useDispatch)();return(0,s.useEffect)((()=>{Promise.all([j.all("preview"),j.all("frontend")]).then((([t,n])=>{const{data:a}=t,{data:r}=n;e(S.actions.load({preview:{items:a.data,order:a.meta.order},frontend:{items:r.data,order:r.meta.order}}))}))}),[e]),null}function we(){(0,i.__registerSlice)(S),(0,r.__registerPanel)(ue),l.stylesRepository.register(V),(0,n.injectIntoLogic)({id:"global-classes-populate-store",component:ve}),(0,a.injectIntoClassSelectorActions)({id:"global-classes-manager-button",component:Ee}),(0,o.__privateListenTo)((0,o.v1ReadyEvent)(),(()=>{!function(){const e=(0,i.__subscribeWithSelector)(I,(()=>{I((0,i.__getState)())&&(0,c.setDocumentModifiedStatus)(!0)}));(0,o.registerDataHook)("after","document/save/save",(e=>z({context:"publish"===e.status?"frontend":"preview"})))}()}))}(window.elementorV2=window.elementorV2||{}).editorGlobalClasses=t}(),window.elementorV2.editorGlobalClasses?.init?.();