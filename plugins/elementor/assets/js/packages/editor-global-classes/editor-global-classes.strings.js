__( 'class', 'elementor' );
__( 'classes', 'elementor' );
__( 'More actions', 'elementor' );
__( 'Rename', 'elementor' );
__( 'Delete', 'elementor' );
__( 'There are no global classes yet.', 'elementor' );
__(
				'CSS classes created in the editor panel will appear here. Once they are available, you can arrange their hierarchy, rename them, or delete them as needed.',
				'elementor'
			);
__( 'Delete this class?', 'elementor' );
__( 'Deleting', 'elementor' );
__(
						'will permanently remove it from your project and may affect the design across all elements using it. This action cannot be undone.',
						'elementor'
					);
__( 'Not now', 'elementor' );
__( 'Delete', 'elementor' );
__( 'Class Manager', 'elementor' );
__( 'Save changes', 'elementor' );
__( 'You have unsaved changes', 'elementor' );
__( 'You have unsaved changes in the Class Manager.', 'elementor' );
__( 'To avoid losing your updates, save your changes before leaving.', 'elementor' );
__( 'Cancel', 'elementor' );
__( 'Save & Continue', 'elementor' );
__( 'Something went wrong', 'elementor' );
__( 'Class Manager', 'elementor' );
__(
					"The Class Manager lets you see all the classes you've created, plus adjust their priority, rename them, and delete unused classes to keep your CSS structured.",
					'elementor'
				);
__(
					'Remember, when editing an item within a specific class, any changes you make will apply across all elements in that class.',
					'elementor'
				);
__( 'Class Manager', 'elementor' );
__( 'You have unsaved changes', 'elementor' );
__(
								"To open the Class Manager, save your page first. You can't continue without saving.",
								'elementor'
							);
__( 'Stay here', 'elementor' );
__( 'Save & Continue', 'elementor' );