!function(){"use strict";var e={d:function(t,n){for(var l in n)e.o(n,l)&&!e.o(t,l)&&Object.defineProperty(t,l,{enumerable:!0,get:n[l]})},o:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r:function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{AspectRatioControl:function(){return Ft},BackgroundControl:function(){return An},BoxShadowRepeaterControl:function(){return Ke},ColorControl:function(){return we},ControlActionsProvider:function(){return ae},ControlAdornments:function(){return Ve},ControlAdornmentsProvider:function(){return Pe},ControlFormLabel:function(){return q},ControlReplacementsProvider:function(){return X},ControlToggleButtonGroup:function(){return tt},EqualUnequalSizesControl:function(){return ut},FontFamilyControl:function(){return yt},GapControl:function(){return Ut},ImageControl:function(){return me},LinkControl:function(){return kt},LinkedDimensionsControl:function(){return dt},NumberControl:function(){return it},PropKeyProvider:function(){return B},PropProvider:function(){return L},SelectControl:function(){return ue},SizeControl:function(){return be},StrokeControl:function(){return Se},SvgMediaControl:function(){return tn},SwitchControl:function(){return Gn},TextAreaControl:function(){return pe},TextControl:function(){return de},ToggleControl:function(){return rt},UrlControl:function(){return xt},createControlReplacementsRegistry:function(){return Y},injectIntoRepeaterItemIcon:function(){return Ue},injectIntoRepeaterItemLabel:function(){return Ge},useBoundProp:function(){return j},useControlActions:function(){return oe},useSyncExternalState:function(){return Ee}});var n=window.React,l=window.elementorV2.editorProps,r=window.elementorV2.ui,a=window.wp.i18n,o=window.elementorV2.utils,i=window.elementorV2.query,s=window.elementorV2.httpClient,c=window.elementorV2.icons,u=window.elementorV2.wpMedia,m=window.elementorV2.editorUi,d=window.elementorV2.locations,p=window.elementorV2.editorV1Adapters,h=window.ReactDOM;function g(e,t,n){let l,r=n.initialDeps??[];return()=>{var a,o,i,s;let c;n.key&&(null==(a=n.debug)?void 0:a.call(n))&&(c=Date.now());const u=e();if(u.length===r.length&&!u.some(((e,t)=>r[t]!==e)))return l;let m;if(r=u,n.key&&(null==(o=n.debug)?void 0:o.call(n))&&(m=Date.now()),l=t(...u),n.key&&(null==(i=n.debug)?void 0:i.call(n))){const e=Math.round(100*(Date.now()-c))/100,t=Math.round(100*(Date.now()-m))/100,l=t/16,r=(e,t)=>{for(e=String(e);e.length<t;)e=" "+e;return e};console.info(`%c⏱ ${r(t,5)} /${r(e,5)} ms`,`\n            font-size: .6rem;\n            font-weight: bold;\n            color: hsl(${Math.max(0,Math.min(120-120*l,120))}deg 100% 31%);`,null==n?void 0:n.key)}return null==(s=null==n?void 0:n.onChange)||s.call(n,l),l}}function E(e,t){if(void 0===e)throw new Error("Unexpected undefined"+(t?`: ${t}`:""));return e}const v=(e,t,n)=>{let l;return function(...r){e.clearTimeout(l),l=e.setTimeout((()=>t.apply(this,r)),n)}},b=e=>e,f=e=>{const t=Math.max(e.startIndex-e.overscan,0),n=Math.min(e.endIndex+e.overscan,e.count-1),l=[];for(let e=t;e<=n;e++)l.push(e);return l},y=(e,t)=>{const n=e.scrollElement;if(!n)return;const l=e.targetWindow;if(!l)return;const r=e=>{const{width:n,height:l}=e;t({width:Math.round(n),height:Math.round(l)})};if(r(n.getBoundingClientRect()),!l.ResizeObserver)return()=>{};const a=new l.ResizeObserver((t=>{const l=()=>{const e=t[0];if(null==e?void 0:e.borderBoxSize){const t=e.borderBoxSize[0];if(t)return void r({width:t.inlineSize,height:t.blockSize})}r(n.getBoundingClientRect())};e.options.useAnimationFrameWithResizeObserver?requestAnimationFrame(l):l()}));return a.observe(n,{box:"border-box"}),()=>{a.unobserve(n)}},x={passive:!0},C="undefined"==typeof window||"onscrollend"in window,w=(e,t)=>{const n=e.scrollElement;if(!n)return;const l=e.targetWindow;if(!l)return;let r=0;const a=e.options.useScrollendEvent&&C?()=>{}:v(l,(()=>{t(r,!1)}),e.options.isScrollingResetDelay),o=l=>()=>{const{horizontal:o,isRtl:i}=e.options;r=o?n.scrollLeft*(i?-1:1):n.scrollTop,a(),t(r,l)},i=o(!0),s=o(!1);s(),n.addEventListener("scroll",i,x);const c=e.options.useScrollendEvent&&C;return c&&n.addEventListener("scrollend",s,x),()=>{n.removeEventListener("scroll",i),c&&n.removeEventListener("scrollend",s)}},_=(e,t,n)=>{if(null==t?void 0:t.borderBoxSize){const e=t.borderBoxSize[0];if(e)return Math.round(e[n.options.horizontal?"inlineSize":"blockSize"])}return Math.round(e.getBoundingClientRect()[n.options.horizontal?"width":"height"])},S=(e,{adjustments:t=0,behavior:n},l)=>{var r,a;const o=e+t;null==(a=null==(r=l.scrollElement)?void 0:r.scrollTo)||a.call(r,{[l.options.horizontal?"left":"top"]:o,behavior:n})};class I{constructor(e){this.unsubs=[],this.scrollElement=null,this.targetWindow=null,this.isScrolling=!1,this.scrollToIndexTimeoutId=null,this.measurementsCache=[],this.itemSizeCache=new Map,this.pendingMeasuredCacheIndexes=[],this.scrollRect=null,this.scrollOffset=null,this.scrollDirection=null,this.scrollAdjustments=0,this.elementsCache=new Map,this.observer=(()=>{let e=null;const t=()=>e||(this.targetWindow&&this.targetWindow.ResizeObserver?e=new this.targetWindow.ResizeObserver((e=>{e.forEach((e=>{const t=()=>{this._measureElement(e.target,e)};this.options.useAnimationFrameWithResizeObserver?requestAnimationFrame(t):t()}))})):null);return{disconnect:()=>{var n;null==(n=t())||n.disconnect(),e=null},observe:e=>{var n;return null==(n=t())?void 0:n.observe(e,{box:"border-box"})},unobserve:e=>{var n;return null==(n=t())?void 0:n.unobserve(e)}}})(),this.range=null,this.setOptions=e=>{Object.entries(e).forEach((([t,n])=>{void 0===n&&delete e[t]})),this.options={debug:!1,initialOffset:0,overscan:1,paddingStart:0,paddingEnd:0,scrollPaddingStart:0,scrollPaddingEnd:0,horizontal:!1,getItemKey:b,rangeExtractor:f,onChange:()=>{},measureElement:_,initialRect:{width:0,height:0},scrollMargin:0,gap:0,indexAttribute:"data-index",initialMeasurementsCache:[],lanes:1,isScrollingResetDelay:150,enabled:!0,isRtl:!1,useScrollendEvent:!0,useAnimationFrameWithResizeObserver:!1,...e}},this.notify=e=>{var t,n;null==(n=(t=this.options).onChange)||n.call(t,this,e)},this.maybeNotify=g((()=>(this.calculateRange(),[this.isScrolling,this.range?this.range.startIndex:null,this.range?this.range.endIndex:null])),(e=>{this.notify(e)}),{key:!1,debug:()=>this.options.debug,initialDeps:[this.isScrolling,this.range?this.range.startIndex:null,this.range?this.range.endIndex:null]}),this.cleanup=()=>{this.unsubs.filter(Boolean).forEach((e=>e())),this.unsubs=[],this.observer.disconnect(),this.scrollElement=null,this.targetWindow=null},this._didMount=()=>()=>{this.cleanup()},this._willUpdate=()=>{var e;const t=this.options.enabled?this.options.getScrollElement():null;if(this.scrollElement!==t){if(this.cleanup(),!t)return void this.maybeNotify();this.scrollElement=t,this.scrollElement&&"ownerDocument"in this.scrollElement?this.targetWindow=this.scrollElement.ownerDocument.defaultView:this.targetWindow=(null==(e=this.scrollElement)?void 0:e.window)??null,this.elementsCache.forEach((e=>{this.observer.observe(e)})),this._scrollToOffset(this.getScrollOffset(),{adjustments:void 0,behavior:void 0}),this.unsubs.push(this.options.observeElementRect(this,(e=>{this.scrollRect=e,this.maybeNotify()}))),this.unsubs.push(this.options.observeElementOffset(this,((e,t)=>{this.scrollAdjustments=0,this.scrollDirection=t?this.getScrollOffset()<e?"forward":"backward":null,this.scrollOffset=e,this.isScrolling=t,this.maybeNotify()})))}},this.getSize=()=>this.options.enabled?(this.scrollRect=this.scrollRect??this.options.initialRect,this.scrollRect[this.options.horizontal?"width":"height"]):(this.scrollRect=null,0),this.getScrollOffset=()=>this.options.enabled?(this.scrollOffset=this.scrollOffset??("function"==typeof this.options.initialOffset?this.options.initialOffset():this.options.initialOffset),this.scrollOffset):(this.scrollOffset=null,0),this.getFurthestMeasurement=(e,t)=>{const n=new Map,l=new Map;for(let r=t-1;r>=0;r--){const t=e[r];if(n.has(t.lane))continue;const a=l.get(t.lane);if(null==a||t.end>a.end?l.set(t.lane,t):t.end<a.end&&n.set(t.lane,!0),n.size===this.options.lanes)break}return l.size===this.options.lanes?Array.from(l.values()).sort(((e,t)=>e.end===t.end?e.index-t.index:e.end-t.end))[0]:void 0},this.getMeasurementOptions=g((()=>[this.options.count,this.options.paddingStart,this.options.scrollMargin,this.options.getItemKey,this.options.enabled]),((e,t,n,l,r)=>(this.pendingMeasuredCacheIndexes=[],{count:e,paddingStart:t,scrollMargin:n,getItemKey:l,enabled:r})),{key:!1}),this.getMeasurements=g((()=>[this.getMeasurementOptions(),this.itemSizeCache]),(({count:e,paddingStart:t,scrollMargin:n,getItemKey:l,enabled:r},a)=>{if(!r)return this.measurementsCache=[],this.itemSizeCache.clear(),[];0===this.measurementsCache.length&&(this.measurementsCache=this.options.initialMeasurementsCache,this.measurementsCache.forEach((e=>{this.itemSizeCache.set(e.key,e.size)})));const o=this.pendingMeasuredCacheIndexes.length>0?Math.min(...this.pendingMeasuredCacheIndexes):0;this.pendingMeasuredCacheIndexes=[];const i=this.measurementsCache.slice(0,o);for(let r=o;r<e;r++){const e=l(r),o=1===this.options.lanes?i[r-1]:this.getFurthestMeasurement(i,r),s=o?o.end+this.options.gap:t+n,c=a.get(e),u="number"==typeof c?c:this.options.estimateSize(r),m=s+u,d=o?o.lane:r%this.options.lanes;i[r]={index:r,start:s,size:u,end:m,key:e,lane:d}}return this.measurementsCache=i,i}),{key:!1,debug:()=>this.options.debug}),this.calculateRange=g((()=>[this.getMeasurements(),this.getSize(),this.getScrollOffset(),this.options.lanes]),((e,t,n,l)=>this.range=e.length>0&&t>0?function({measurements:e,outerSize:t,scrollOffset:n,lanes:l}){const r=e.length-1;let a=T(0,r,(t=>e[t].start),n),o=a;if(1===l)for(;o<r&&e[o].end<n+t;)o++;else if(l>1){const i=Array(l).fill(0);for(;o<r&&i.some((e=>e<n+t));){const t=e[o];i[t.lane]=t.end,o++}const s=Array(l).fill(n+t);for(;a>0&&s.some((e=>e>=n));){const t=e[a];s[t.lane]=t.start,a--}a=Math.max(0,a-a%l),o=Math.min(r,o+(l-1-o%l))}return{startIndex:a,endIndex:o}}({measurements:e,outerSize:t,scrollOffset:n,lanes:l}):null),{key:!1,debug:()=>this.options.debug}),this.getVirtualIndexes=g((()=>{let e=null,t=null;const n=this.calculateRange();return n&&(e=n.startIndex,t=n.endIndex),[this.options.rangeExtractor,this.options.overscan,this.options.count,e,t]}),((e,t,n,l,r)=>null===l||null===r?[]:e({startIndex:l,endIndex:r,overscan:t,count:n})),{key:!1,debug:()=>this.options.debug}),this.indexFromElement=e=>{const t=this.options.indexAttribute,n=e.getAttribute(t);return n?parseInt(n,10):(console.warn(`Missing attribute name '${t}={index}' on measured element.`),-1)},this._measureElement=(e,t)=>{const n=this.indexFromElement(e),l=this.measurementsCache[n];if(!l)return;const r=l.key,a=this.elementsCache.get(r);a!==e&&(a&&this.observer.unobserve(a),this.observer.observe(e),this.elementsCache.set(r,e)),e.isConnected&&this.resizeItem(n,this.options.measureElement(e,t,this))},this.resizeItem=(e,t)=>{const n=this.measurementsCache[e];if(!n)return;const l=t-(this.itemSizeCache.get(n.key)??n.size);0!==l&&((void 0!==this.shouldAdjustScrollPositionOnItemSizeChange?this.shouldAdjustScrollPositionOnItemSizeChange(n,l,this):n.start<this.getScrollOffset()+this.scrollAdjustments)&&this._scrollToOffset(this.getScrollOffset(),{adjustments:this.scrollAdjustments+=l,behavior:void 0}),this.pendingMeasuredCacheIndexes.push(n.index),this.itemSizeCache=new Map(this.itemSizeCache.set(n.key,t)),this.notify(!1))},this.measureElement=e=>{e?this._measureElement(e,void 0):this.elementsCache.forEach(((e,t)=>{e.isConnected||(this.observer.unobserve(e),this.elementsCache.delete(t))}))},this.getVirtualItems=g((()=>[this.getVirtualIndexes(),this.getMeasurements()]),((e,t)=>{const n=[];for(let l=0,r=e.length;l<r;l++){const r=t[e[l]];n.push(r)}return n}),{key:!1,debug:()=>this.options.debug}),this.getVirtualItemForOffset=e=>{const t=this.getMeasurements();if(0!==t.length)return E(t[T(0,t.length-1,(e=>E(t[e]).start),e)])},this.getOffsetForAlignment=(e,t,n=0)=>{const l=this.getSize(),r=this.getScrollOffset();"auto"===t&&(t=e>=r+l?"end":"start"),"center"===t?e+=(n-l)/2:"end"===t&&(e-=l);const a=this.options.horizontal?"scrollWidth":"scrollHeight",o=(this.scrollElement?"document"in this.scrollElement?this.scrollElement.document.documentElement[a]:this.scrollElement[a]:0)-l;return Math.max(Math.min(o,e),0)},this.getOffsetForIndex=(e,t="auto")=>{e=Math.max(0,Math.min(e,this.options.count-1));const n=this.measurementsCache[e];if(!n)return;const l=this.getSize(),r=this.getScrollOffset();if("auto"===t)if(n.end>=r+l-this.options.scrollPaddingEnd)t="end";else{if(!(n.start<=r+this.options.scrollPaddingStart))return[r,t];t="start"}const a="end"===t?n.end+this.options.scrollPaddingEnd:n.start-this.options.scrollPaddingStart;return[this.getOffsetForAlignment(a,t,n.size),t]},this.isDynamicMode=()=>this.elementsCache.size>0,this.cancelScrollToIndex=()=>{null!==this.scrollToIndexTimeoutId&&this.targetWindow&&(this.targetWindow.clearTimeout(this.scrollToIndexTimeoutId),this.scrollToIndexTimeoutId=null)},this.scrollToOffset=(e,{align:t="start",behavior:n}={})=>{this.cancelScrollToIndex(),"smooth"===n&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size."),this._scrollToOffset(this.getOffsetForAlignment(e,t),{adjustments:void 0,behavior:n})},this.scrollToIndex=(e,{align:t="auto",behavior:n}={})=>{e=Math.max(0,Math.min(e,this.options.count-1)),this.cancelScrollToIndex(),"smooth"===n&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size.");const l=this.getOffsetForIndex(e,t);if(!l)return;const[r,a]=l;this._scrollToOffset(r,{adjustments:void 0,behavior:n}),"smooth"!==n&&this.isDynamicMode()&&this.targetWindow&&(this.scrollToIndexTimeoutId=this.targetWindow.setTimeout((()=>{if(this.scrollToIndexTimeoutId=null,this.elementsCache.has(this.options.getItemKey(e))){const[r]=E(this.getOffsetForIndex(e,a));t=r,l=this.getScrollOffset(),Math.abs(t-l)<1||this.scrollToIndex(e,{align:a,behavior:n})}else this.scrollToIndex(e,{align:a,behavior:n});var t,l})))},this.scrollBy=(e,{behavior:t}={})=>{this.cancelScrollToIndex(),"smooth"===t&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size."),this._scrollToOffset(this.getScrollOffset()+e,{adjustments:void 0,behavior:t})},this.getTotalSize=()=>{var e;const t=this.getMeasurements();let n;if(0===t.length)n=this.options.paddingStart;else if(1===this.options.lanes)n=(null==(e=t[t.length-1])?void 0:e.end)??0;else{const e=Array(this.options.lanes).fill(null);let l=t.length-1;for(;l>0&&e.some((e=>null===e));){const n=t[l];null===e[n.lane]&&(e[n.lane]=n.end),l--}n=Math.max(...e.filter((e=>null!==e)))}return Math.max(n-this.options.scrollMargin+this.options.paddingEnd,0)},this._scrollToOffset=(e,{adjustments:t,behavior:n})=>{this.options.scrollToFn(e,{behavior:n,adjustments:t},this)},this.measure=()=>{this.itemSizeCache=new Map,this.notify(!1)},this.setOptions(e)}}const T=(e,t,n,l)=>{for(;e<=t;){const r=(e+t)/2|0,a=n(r);if(a<l)e=r+1;else{if(!(a>l))return r;t=r-1}}return e>0?e-1:0},z="undefined"!=typeof document?n.useLayoutEffect:n.useEffect;var k=window.elementorV2.editorElements,P=window.elementorV2.session,O=window.elementorV2.editorCurrentUser,V=window.elementorV2.env,M=(0,o.createError)({code:"missing_prop_provider_prop_type",message:"Prop type is missing"}),U=(0,o.createError)({code:"unsupported_prop_provider_prop_type",message:"Parent prop type is not supported"}),A=(0,o.createError)({code:"hook_outside_provider",message:"Hook used outside of provider"}),G=(0,n.createContext)(null),L=({children:e,value:t,setValue:l,propType:r,placeholder:a,disabled:o})=>n.createElement(G.Provider,{value:{value:t,propType:r,setValue:l,placeholder:a,disabled:o}},e),F=()=>{const e=(0,n.useContext)(G);if(!e)throw new A({context:{hook:"usePropContext",provider:"PropProvider"}});return e},$=(0,n.createContext)(null),B=({children:e,bind:t})=>{const{propType:l}=F();if(!l)throw new M({context:{bind:t}});if("array"===l.kind)return n.createElement(W,{bind:t},e);if("object"===l.kind)return n.createElement(R,{bind:t},e);throw new U({context:{propType:l}})},R=({children:e,bind:t})=>{const l=F(),{path:r}=(0,n.useContext)($)??{},a=l.value?.[t],o=l.placeholder?.[t],i=l.propType.shape[t];return n.createElement($.Provider,{value:{...l,value:a,setValue:(e,n,r)=>{const a={...l.value,[t]:e};return l?.setValue(a,n,{...r,bind:t})},placeholder:o,bind:t,propType:i,path:[...r??[],t]}},e)},W=({children:e,bind:t})=>{const l=F(),{path:r}=(0,n.useContext)($)??{},a=l.value?.[Number(t)],o=l.propType.item_prop_type;return n.createElement($.Provider,{value:{...l,value:a,setValue:(e,n)=>{const r=[...l.value??[]];return r[Number(t)]=e,l?.setValue(r,n,{bind:t})},bind:t,propType:o,path:[...r??[],t]}},e)},D=()=>{const e=(0,n.useContext)($);if(!e)throw new A({context:{hook:"usePropKeyContext",provider:"PropKeyProvider"}});return e};function j(e){const t=D(),{isValid:n,validate:l,restoreValue:r}=N(t.propType);if(!e)return t;const a=K(t.propType,e.key),o=e.extract(t.value??a.default??null),i=e.extract(t.placeholder??null);return{...t,propType:a,setValue:function(n,r,a){if(l(n))return null===n?t?.setValue(null,r,a):t?.setValue(e?.create(n,r),{},a)},value:n?o:null,restoreValue:r,placeholder:i}}var N=e=>{const[t,l]=(0,n.useState)(!0);return{isValid:t,setIsValid:l,validate:t=>{let n=!0;return e.settings.required&&null===t&&(n=!1),l(n),n},restoreValue:()=>l(!0)}},K=(e,t)=>{let n=e;if("union"===e.kind&&(n=e.prop_types[t]),!n)throw new M({context:{key:t}});return n},q=({children:e})=>n.createElement(r.FormLabel,{size:"tiny"},e),H=(0,n.createContext)([]),X=({replacements:e,children:t})=>n.createElement(H.Provider,{value:e},t),Y=()=>{const e=[];return{registerControlReplacement:function(t){e.push(t)},getControlReplacements:function(){return e}}};function Q(e){return t=>{const l=(e=>{const{value:t}=j(),l=(0,n.useContext)(H);try{const n=l.find((e=>e.condition({value:t})));return n?.component??e}catch{return e}})(e);return n.createElement(r.ErrorBoundary,{fallback:null},n.createElement(l,{...t}))}}Symbol("control");var Z="elementor/v1/settings",J=e=>e.data.value,ee="elementor_unfiltered_files_upload",te={queryKey:[ee]},ne=()=>(0,i.useQuery)({...te,queryFn:()=>{return(e=ee,(0,s.httpService)().get(`${Z}/${e}`).then((e=>J(e.data)))).then((e=>le(e)));var e},staleTime:1/0}),le=e=>Boolean("1"===e),re=(0,n.createContext)(null),ae=({children:e,items:t})=>n.createElement(re.Provider,{value:{items:t}},e),oe=()=>{const e=(0,n.useContext)(re);if(!e)throw new Error("useControlActions must be used within a ControlActionsProvider");return e},ie=(0,r.styled)("span")`
	display: contents;

	.MuiFloatingActionBar-popper:has( .MuiFloatingActionBar-actions:empty ) {
		display: none;
	}

	.MuiFloatingActionBar-popper {
		z-index: 1000;
	}
`;function se({children:e}){const{items:t}=oe(),{disabled:l}=j();if(0===t.length||l)return e;const a=t.map((({MenuItem:e,id:t})=>n.createElement(e,{key:t})));return n.createElement(ie,null,n.createElement(r.UnstableFloatingActionBar,{actions:a},e))}var ce=Q((({mediaTypes:e=["image"]})=>{const{value:t,setValue:o}=j(l.imageSrcPropTypeUtil),{id:i,url:s}=t??{},{data:m,isFetching:d}=(0,u.useWpMediaAttachment)(i?.value||null),p=m?.url??s?.value??null,{open:h}=(0,u.useWpMediaFrame)({mediaTypes:e,multiple:!1,selected:i?.value||null,onSelect:e=>{o({id:{$$type:"image-attachment-id",value:e.id},url:null})}});return n.createElement(se,null,n.createElement(r.Card,{variant:"outlined"},n.createElement(r.CardMedia,{image:p,sx:{height:150}},d?n.createElement(r.Stack,{justifyContent:"center",alignItems:"center",width:"100%",height:"100%"},n.createElement(r.CircularProgress,null)):n.createElement(n.Fragment,null)),n.createElement(r.CardOverlay,null,n.createElement(r.Stack,{gap:1},n.createElement(r.Button,{size:"tiny",color:"inherit",variant:"outlined",onClick:()=>h({mode:"browse"})},(0,a.__)("Select image","elementor")),n.createElement(r.Button,{size:"tiny",variant:"text",color:"inherit",startIcon:n.createElement(c.UploadIcon,null),onClick:()=>h({mode:"upload"})},(0,a.__)("Upload","elementor"))))))})),ue=Q((({options:e,onChange:t})=>{const{value:a,setValue:o,disabled:i}=j(l.stringPropTypeUtil);return n.createElement(se,null,n.createElement(r.Select,{sx:{overflow:"hidden"},displayEmpty:!0,size:"tiny",value:a??"",onChange:e=>{const n=e.target.value||null;t?.(n,a),o(n)},disabled:i,fullWidth:!0},e.map((({label:e,...t})=>n.createElement(m.MenuListItem,{key:t.value,...t,value:t.value??""},e)))))})),me=Q((({sizes:e,resolutionLabel:t=(0,a.__)("Image resolution","elementor"),showMode:o="all"})=>{const i=j(l.imagePropTypeUtil),{data:s}=ne(),c=s?["image","svg"]:["image"];return n.createElement(L,{...i},n.createElement(r.Stack,{gap:1.5},["all","media"].includes(o)?n.createElement(B,{bind:"src"},n.createElement(q,null," ",(0,a.__)("Image","elementor")," "),n.createElement(ce,{mediaTypes:c})):null,["all","sizes"].includes(o)?n.createElement(B,{bind:"size"},n.createElement(r.Grid,{container:!0,gap:1.5,alignItems:"center",flexWrap:"nowrap"},n.createElement(r.Grid,{item:!0,xs:6},n.createElement(q,null," ",t," ")),n.createElement(r.Grid,{item:!0,xs:6,sx:{overflow:"hidden"}},n.createElement(ue,{options:e})))):null))})),de=Q((({placeholder:e})=>{const{value:t,setValue:a,disabled:o}=j(l.stringPropTypeUtil);return n.createElement(se,null,n.createElement(r.TextField,{size:"tiny",fullWidth:!0,disabled:o,value:t??"",onChange:e=>a(e.target.value),placeholder:e}))})),pe=Q((({placeholder:e})=>{const{value:t,setValue:a,disabled:o}=j(l.stringPropTypeUtil);return n.createElement(se,null,n.createElement(r.TextField,{size:"tiny",multiline:!0,fullWidth:!0,minRows:5,disabled:o,value:t??"",onChange:e=>{a(e.target.value)},placeholder:e}))})),he=(0,n.forwardRef)((({placeholder:e,type:t,value:l,onChange:a,onBlur:o,onKeyDown:i,onKeyUp:s,endAdornment:c,startAdornment:u,disabled:m},d)=>n.createElement(r.TextField,{ref:d,size:"tiny",fullWidth:!0,type:t,value:l,disabled:m,onChange:a,onKeyDown:i,onKeyUp:s,onBlur:o,placeholder:e,InputProps:{endAdornment:c,startAdornment:u}}))),ge=({options:e,onClick:t,value:l,disabled:a})=>{const o=(0,r.usePopupState)({variant:"popover",popupId:(0,n.useId)()});return n.createElement(r.InputAdornment,{position:"end"},n.createElement(r.Button,{size:"small",color:"secondary",disabled:a,sx:{font:"inherit",minWidth:"initial",textTransform:"uppercase"},...(0,r.bindTrigger)(o)},l),n.createElement(r.Menu,{MenuListProps:{dense:!0},...(0,r.bindMenu)(o)},e.map(((l,r)=>n.createElement(m.MenuListItem,{key:l,onClick:()=>(n=>{t(e[n]),o.close()})(r)},l.toUpperCase())))))},Ee=({external:e,setExternal:t,persistWhen:l,fallback:r})=>{function a(e,t){return e||r(t)}const[o,i]=(0,n.useState)(a(e,null));return(0,n.useEffect)((()=>{i((t=>a(e,t)))}),[e]),[o,e=>{const n=("function"==typeof e?e:()=>e)(o);var r;i(n),t(l(r=n)?r:null)}]},ve=["px","%","em","rem","vw","vh"],be=Q((({units:e=ve,extendedValues:t=[],placeholder:r,startIcon:a})=>{const{value:o,setValue:i,restoreValue:s,disabled:c}=j(l.sizePropTypeUtil),[u,m]=Ee({external:o,setExternal:i,persistWhen:e=>!!e?.size||0===e?.size,fallback:e=>({unit:e?.unit||"px",size:NaN})}),d=t?.length?fe:xe;return n.createElement(d,{disabled:c,size:u.size,unit:u.unit,placeholder:r,startIcon:a,units:e,extendedValues:t,handleSizeChange:e=>{const{value:t}=e.target;m((e=>({...e,size:t||"0"===t?parseFloat(t):NaN})))},handleUnitChange:e=>{m((t=>({size:t?.size??NaN,unit:e})))},onBlur:s})})),fe=e=>{const{value:t,setValue:r}=j(l.stringPropTypeUtil),{extendedValues:a=[]}=e,o=t??e.unit;return n.createElement(xe,{...e,units:[...e.units,...a],handleUnitChange:t=>{a.includes(t)?r(t):e.handleUnitChange(t)},unit:o})},ye=["e","E","+","-"],xe=({units:e,handleUnitChange:t,handleSizeChange:l,placeholder:a,startIcon:o,onBlur:i,size:s,unit:c,disabled:u})=>{const m=(0,n.useRef)("");return n.createElement(se,null,n.createElement(r.Box,null,n.createElement(he,{disabled:u,endAdornment:n.createElement(ge,{disabled:u,options:e,onClick:t,value:c??"px"}),placeholder:a,startAdornment:o?n.createElement(r.InputAdornment,{position:"start",disabled:u},o):void 0,type:"number",value:Number.isNaN(s)?"":s,onChange:l,onBlur:i,onKeyDown:e=>{ye.includes(e.key)&&e.preventDefault()},onKeyUp:n=>{const{key:l}=n;if(!/^[a-zA-Z%]$/.test(l))return;n.preventDefault();const r=l.toLowerCase(),a=(m.current+r).slice(-3);m.current=a;const o=e.find((e=>e.includes(a)))||e.find((e=>e.startsWith(r)))||e.find((e=>e.includes(r)));o&&t(o)}})))},Ce=({gap:e=2,sx:t,children:l})=>n.createElement(r.Stack,{gap:e,sx:{...t}},l),we=Q((({propTypeUtil:e=l.colorPropTypeUtil,anchorEl:t,slotProps:a={},...o})=>{const{value:i,setValue:s,disabled:c}=j(e);return n.createElement(se,null,n.createElement(r.UnstableColorField,{size:"tiny",fullWidth:!0,value:i??"",onChange:e=>{s(e||null)},...o,disabled:c,slotProps:{...a,colorPicker:{anchorEl:t,anchorOrigin:{vertical:"top",horizontal:"right"},transformOrigin:{vertical:"top",horizontal:-10}}}}))})),_e=["px","em","rem"],Se=Q((()=>{const e=j(l.strokePropTypeUtil);return n.createElement(L,{...e},n.createElement(Ce,null,n.createElement(Ie,{bind:"width",label:(0,a.__)("Stroke width","elementor")},n.createElement(be,{units:_e})),n.createElement(Ie,{bind:"color",label:(0,a.__)("Stroke color","elementor")},n.createElement(we,null))))})),Ie=({bind:e,label:t,children:l})=>n.createElement(B,{bind:e},n.createElement(r.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},n.createElement(r.Grid,{item:!0,xs:6},n.createElement(q,null,t)),n.createElement(r.Grid,{item:!0,xs:6},l))),Te=({alignItems:e,gap:t=1.5,p:l,pt:a,pb:o,children:i})=>n.createElement(r.Stack,{alignItems:e,gap:t,p:l,pt:a,pb:o},i),ze=({gap:e=1.5,alignItems:t="center",flexWrap:l="nowrap",children:a})=>n.createElement(r.Grid,{container:!0,gap:e,alignItems:t,flexWrap:l},a),ke=(0,n.createContext)(null),Pe=({children:e,items:t})=>n.createElement(ke.Provider,{value:{items:t}},e),Oe=()=>{const e=(0,n.useContext)(ke);return e?.items??[]};function Ve(){const e=Oe();return 0===e?.length?null:n.createElement(n.Fragment,null,e.map((({Adornment:e,id:t})=>n.createElement(e,{key:t}))))}var{Slot:Me,inject:Ue}=(0,d.createReplaceableLocation)(),{Slot:Ae,inject:Ge}=(0,d.createReplaceableLocation)(),Le=e=>n.createElement(r.List,{sx:{p:0,my:-.5,mx:0}},n.createElement(r.UnstableSortableProvider,{restrictAxis:!0,disableDragOverlay:!1,variant:"static",...e})),Fe=({id:e,children:t})=>n.createElement(r.UnstableSortableItem,{id:e,render:({itemProps:e,triggerProps:l,itemStyle:r,triggerStyle:a,showDropIndication:o,dropIndicationStyle:i})=>n.createElement($e,{...e,style:r},n.createElement(Be,{...l,style:a}),t,o&&n.createElement(Re,{style:i}))}),$e=(0,r.styled)(r.ListItem)`
	position: relative;
	margin-inline: 0px;
	padding-inline: 0px;
	padding-block: ${({theme:e})=>e.spacing(.5)};

	& .class-item-sortable-trigger {
		color: ${({theme:e})=>e.palette.action.active};
		height: 100%;
		display: flex;
		align-items: center;
		visibility: hidden;
		position: absolute;
		top: 50%;
		padding-inline-end: ${({theme:e})=>e.spacing(.5)};
		transform: translate( -75%, -50% );
	}

	&[aria-describedby=''] > .MuiTag-root {
		background-color: ${({theme:e})=>e.palette.background.paper};
		box-shadow: ${({theme:e})=>e.shadows[3]};
	}

	&:hover {
		& .class-item-sortable-trigger {
			visibility: visible;
		}
	}
`,Be=e=>n.createElement("div",{...e,role:"button",className:"class-item-sortable-trigger"},n.createElement(c.GripVerticalIcon,{fontSize:"tiny"})),Re=(0,r.styled)(r.Divider)`
	height: 0px;
	border: none;
	overflow: visible;

	&:after {
		--height: 2px;
		content: '';
		display: block;
		width: 100%;
		height: var( --height );
		margin-block: calc( -1 * var( --height ) / 2 );
		border-radius: ${({theme:e})=>e.spacing(.5)};
		background-color: ${({theme:e})=>e.palette.text.primary};
	}
`,We="tiny",De=({label:e,itemSettings:t,disabled:l=!1,openOnAdd:o=!1,addToBottom:i=!1,values:s=[],setValues:u})=>{const[m,d]=(0,n.useState)(-1),[p,h]=Ee({external:s,setExternal:u,persistWhen:()=>!0}),[g,E]=(0,n.useState)(p.map(((e,t)=>t))),v=e=>1+Math.max(0,...e),b=l?n.Fragment:Fe;return n.createElement(Ce,null,n.createElement(r.Stack,{direction:"row",justifyContent:"start",alignItems:"center",gap:1,sx:{marginInlineEnd:-.75}},n.createElement(r.Typography,{component:"label",variant:"caption",color:"text.secondary"},e),n.createElement(Ve,null),n.createElement(r.IconButton,{size:We,sx:{ml:"auto"},disabled:l,onClick:()=>{const e=structuredClone(t.initialValues),n=v(g);i?(h([...p,e]),E([...g,n])):(h([e,...p]),E([n,...g])),o&&d(n)},"aria-label":(0,a.__)("Add item","elementor")},n.createElement(c.PlusIcon,{fontSize:We}))),0<g.length&&n.createElement(Le,{value:g,onChange:e=>{E(e),h((t=>e.map((e=>{const n=g.indexOf(e);return t[n]}))))}},g.map(((e,r)=>{const a=p[r];return a?n.createElement(b,{id:e,key:`sortable-${e}`},n.createElement(je,{disabled:l,propDisabled:a?.disabled,label:n.createElement(Ae,{value:a},n.createElement(t.Label,{value:a})),startIcon:n.createElement(Me,{value:a},n.createElement(t.Icon,{value:a})),removeItem:()=>(e=>{E(g.filter(((t,n)=>n!==e))),h(p.filter(((t,n)=>n!==e)))})(r),duplicateItem:()=>(e=>{const t=structuredClone(p[e]),n=v(g),l=1+e;h([...p.slice(0,l),t,...p.slice(l)]),E([...g.slice(0,l),n,...g.slice(l)])})(r),toggleDisableItem:()=>(e=>{h(p.map(((t,n)=>{if(n===e){const{disabled:e,...n}=t;return{...n,...e?{}:{disabled:!0}}}return t})))})(r),openOnMount:o&&m===e,onOpen:()=>d(-1)},(e=>n.createElement(t.Content,{...e,value:a,bind:String(r)})))):null}))))},je=({label:e,propDisabled:t,startIcon:l,children:o,removeItem:i,duplicateItem:s,toggleDisableItem:u,openOnMount:m,onOpen:d,disabled:p})=>{const[h,g]=(0,n.useState)(null),{popoverState:E,popoverProps:v,ref:b,setRef:f}=Ne(m,d),y=(0,a.__)("Duplicate","elementor"),x=t?(0,a.__)("Show","elementor"):(0,a.__)("Hide","elementor"),C=(0,a.__)("Remove","elementor");return n.createElement(n.Fragment,null,n.createElement(r.UnstableTag,{disabled:p,label:e,showActionsOnHover:!0,fullWidth:!0,ref:f,variant:"outlined","aria-label":(0,a.__)("Open item","elementor"),...(0,r.bindTrigger)(E),startIcon:l,actions:n.createElement(n.Fragment,null,n.createElement(r.Tooltip,{title:y,placement:"top"},n.createElement(r.IconButton,{size:We,onClick:s,"aria-label":y},n.createElement(c.CopyIcon,{fontSize:We}))),n.createElement(r.Tooltip,{title:x,placement:"top"},n.createElement(r.IconButton,{size:We,onClick:u,"aria-label":x},t?n.createElement(c.EyeOffIcon,{fontSize:We}):n.createElement(c.EyeIcon,{fontSize:We}))),n.createElement(r.Tooltip,{title:C,placement:"top"},n.createElement(r.IconButton,{size:We,onClick:i,"aria-label":C},n.createElement(c.XIcon,{fontSize:We}))))}),n.createElement(r.Popover,{disablePortal:!0,slotProps:{paper:{ref:g,sx:{mt:.5,width:b?.getBoundingClientRect().width}}},anchorOrigin:{vertical:"bottom",horizontal:"left"},...v,anchorEl:b},n.createElement(r.Box,null,o({anchorEl:h}))))},Ne=(e,t)=>{const[l,a]=(0,n.useState)(null),o=(0,r.usePopupState)({variant:"popover"}),i=(0,r.bindPopover)(o);return(0,n.useEffect)((()=>{e&&l&&(o.open(l),t?.())}),[l]),{popoverState:o,ref:l,setRef:a,popoverProps:i}},Ke=Q((()=>{const{propType:e,value:t,setValue:r,disabled:o}=j(l.boxShadowPropTypeUtil);return n.createElement(L,{propType:e,value:t,setValue:r},n.createElement(De,{openOnAdd:!0,disabled:o,values:t??[],setValues:r,label:(0,a.__)("Box shadow","elementor"),itemSettings:{Icon:qe,Label:Qe,Content:He,initialValues:Ze}}))})),qe=({value:e})=>n.createElement(r.UnstableColorIndicator,{size:"inherit",component:"span",value:e.value.color?.value}),He=({anchorEl:e,bind:t})=>n.createElement(B,{bind:t},n.createElement(Xe,{anchorEl:e})),Xe=({anchorEl:e})=>{const{propType:t,value:r,setValue:o}=j(l.shadowPropTypeUtil);return n.createElement(L,{propType:t,value:r,setValue:o},n.createElement(Te,{p:1.5},n.createElement(ze,null,n.createElement(Ye,{bind:"color",label:(0,a.__)("Color","elementor")},n.createElement(we,{anchorEl:e})),n.createElement(Ye,{bind:"position",label:(0,a.__)("Position","elementor"),sx:{overflow:"hidden"}},n.createElement(ue,{options:[{label:(0,a.__)("Inset","elementor"),value:"inset"},{label:(0,a.__)("Outset","elementor"),value:null}]}))),n.createElement(ze,null,n.createElement(Ye,{bind:"hOffset",label:(0,a.__)("Horizontal","elementor")},n.createElement(be,null)),n.createElement(Ye,{bind:"vOffset",label:(0,a.__)("Vertical","elementor")},n.createElement(be,null))),n.createElement(ze,null,n.createElement(Ye,{bind:"blur",label:(0,a.__)("Blur","elementor")},n.createElement(be,null)),n.createElement(Ye,{bind:"spread",label:(0,a.__)("Spread","elementor")},n.createElement(be,null)))))},Ye=({label:e,bind:t,children:l,sx:a})=>n.createElement(B,{bind:t},n.createElement(r.Grid,{item:!0,xs:6,sx:a},n.createElement(r.Grid,{container:!0,gap:.75,alignItems:"center"},n.createElement(r.Grid,{item:!0,xs:12},n.createElement(r.FormLabel,{size:"tiny"},e)),n.createElement(r.Grid,{item:!0,xs:12},l)))),Qe=({value:e})=>{const{position:t,hOffset:l,vOffset:r,blur:a,spread:o}=e.value,{size:i="",unit:s=""}=a?.value||{},{size:c="",unit:u=""}=o?.value||{},{size:m="unset",unit:d=""}=l?.value||{},{size:p="unset",unit:h=""}=r?.value||{},g=t?.value||"outset",E=[m+d,p+h,i+s,c+u].join(" ");return n.createElement("span",{style:{textTransform:"capitalize"}},g,": ",E)},Ze={$$type:"shadow",value:{hOffset:{$$type:"size",value:{unit:"px",size:0}},vOffset:{$$type:"size",value:{unit:"px",size:0}},blur:{$$type:"size",value:{unit:"px",size:10}},spread:{$$type:"size",value:{unit:"px",size:0}},color:{$$type:"color",value:"rgba(0, 0, 0, 1)"},position:null}},Je=({showTooltip:e,children:t,label:l})=>e&&l?n.createElement(r.Tooltip,{title:l,disableFocusListener:!0,placement:"top"},t):t,et=(0,r.styled)(r.ToggleButtonGroup)`
	${({justify:e})=>`justify-content: ${e};`}
	button:not( :last-of-type ) {
		border-start-end-radius: 0;
		border-end-end-radius: 0;
	}
	button:not( :first-of-type ) {
		border-start-start-radius: 0;
		border-end-start-radius: 0;
	}
	button:last-of-type {
		border-start-end-radius: 8px;
		border-end-end-radius: 8px;
	}
`,tt=({justify:e="end",size:t="tiny",value:l,onChange:a,items:o,maxItems:i,exclusive:s=!1,fullWidth:c=!1,disabled:u})=>{const m=s&&void 0!==i&&o.length>i,d=m?o.slice(i-1):[],p=m?o.slice(0,i-1):o,h="rtl"===(0,r.useTheme)().direction,g=(0,n.useMemo)((()=>{const e=d?.length;return`repeat(${e?p.length+1:p.length}, minmax(0, 25%)) ${e?"auto":""}`}),[d?.length,p.length]);return n.createElement(se,null,n.createElement(et,{justify:e,value:l,onChange:(e,t)=>{a(t)},exclusive:s,disabled:u,sx:{direction:h?"rtl /* @noflip */":"ltr /* @noflip */",display:"grid",gridTemplateColumns:g,width:"100%"}},p.map((({label:e,value:l,renderContent:a,showTooltip:o})=>n.createElement(Je,{key:l,label:e,showTooltip:o||!1},n.createElement(r.ToggleButton,{value:l,"aria-label":e,size:t,fullWidth:c},n.createElement(a,{size:t}))))),d.length&&s&&n.createElement(nt,{size:t,value:l||null,onChange:a,items:d,fullWidth:c})))},nt=({size:e="tiny",onChange:t,items:l,fullWidth:a,value:o})=>{const i=lt(l,o),[s,u]=(0,n.useState)(!1),m=(0,n.useRef)(null),d=e=>{u(!1),p(e)},p=e=>{t(e===o?null:e)};return n.createElement(n.Fragment,null,n.createElement(r.ToggleButton,{value:i.value,"aria-label":i.label,size:e,fullWidth:a,onClick:e=>{e.preventDefault(),d(i.value)},ref:m},i.renderContent({size:e})),n.createElement(r.ToggleButton,{size:e,"aria-expanded":s?"true":void 0,"aria-haspopup":"menu","aria-pressed":void 0,onClick:e=>{u((e=>!e)),e.preventDefault()},ref:m,value:"__chevron-icon-button__"},n.createElement(c.ChevronDownIcon,{fontSize:e})),n.createElement(r.Menu,{open:s,onClose:()=>u(!1),anchorEl:m.current,anchorOrigin:{vertical:"bottom",horizontal:"right"},transformOrigin:{vertical:"top",horizontal:"right"},sx:{mt:.5}},l.map((({label:e,value:t})=>n.createElement(r.MenuItem,{key:t,selected:t===o,onClick:()=>d(t)},n.createElement(r.ListItemText,null,n.createElement(r.Typography,{sx:{fontSize:"14px"}},e)))))))},lt=(e,t)=>{const[l,r]=(0,n.useState)(e.find((e=>e.value===t))??e[0]);return(0,n.useEffect)((()=>{const n=e.find((e=>e.value===t));n&&r(n)}),[e,t]),l},rt=Q((({options:e,fullWidth:t=!1,size:r="tiny",exclusive:a=!0,maxItems:o})=>{const{value:i,setValue:s,placeholder:c,disabled:u}=j(l.stringPropTypeUtil),m=e.filter((e=>e.exclusive)).map((e=>e.value)),d={items:e,maxItems:o,fullWidth:t,size:r};return a?n.createElement(tt,{...d,value:i??c??null,onChange:s,disabled:u,exclusive:!0}):n.createElement(tt,{...d,value:(i??c)?.split(" ")??[],onChange:e=>{const t=e[e.length-1],n=m.includes(t)?[t]:e?.filter((e=>!m.includes(e)));s(n?.join(" ")||null)},disabled:u,exclusive:!1})})),at=e=>null==e||""===e||Number.isNaN(Number(e)),ot=["e","E","+","-"],it=Q((({placeholder:e,max:t=Number.MAX_VALUE,min:a=-Number.MAX_VALUE,step:o=1,shouldForceInt:i=!1})=>{const{value:s,setValue:c,disabled:u}=j(l.numberPropTypeUtil);return n.createElement(se,null,n.createElement(r.TextField,{size:"tiny",type:"number",fullWidth:!0,disabled:u,value:at(s)?"":s,onChange:e=>{const n=e.target.value;if(at(n))return void c(null);const l=i?+parseInt(n):Number(n);c(Math.min(Math.max(l,a),t))},placeholder:e,inputProps:{step:o},onKeyDown:e=>{ot.includes(e.key)&&e.preventDefault()}}))})),st=({children:e})=>n.createElement(r.Stack,{direction:"row",alignItems:"center",justifyItems:"start",gap:1},n.createElement(q,null,e),n.createElement(Ve,null)),ct=(e,t)=>{const n=Object.values(e);if(n.length!==t.length)return!1;const[l,...r]=n;return r.every((e=>e?.value?.size===l?.value?.size&&e?.value?.unit===l?.value?.unit))};function ut({label:e,icon:t,tooltipLabel:o,items:i,multiSizePropTypeUtil:s}){const c=(0,n.useId)(),u=(0,n.useRef)(null),m=(0,r.usePopupState)({variant:"popover",popupId:c}),{propType:d,value:p,setValue:h,disabled:g}=j(s),{value:E,setValue:v,disabled:b}=j(l.sizePropTypeUtil),f=()=>E?i.reduce(((e,{bind:t})=>({...e,[t]:l.sizePropTypeUtil.create(E)})),{}):null,y=!!p;return n.createElement(n.Fragment,null,n.createElement(r.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap",ref:u},n.createElement(r.Grid,{item:!0,xs:6},n.createElement(st,null,e)),n.createElement(r.Grid,{item:!0,xs:6},n.createElement(r.Stack,{direction:"row",alignItems:"center",gap:1},n.createElement(be,{placeholder:y?(0,a.__)("Mixed","elementor"):void 0}),n.createElement(r.Tooltip,{title:o,placement:"top"},n.createElement(r.ToggleButton,{size:"tiny",value:"check",sx:{marginLeft:"auto"},...(0,r.bindToggle)(m),selected:m.isOpen,"aria-label":o,disabled:g||b},t))))),n.createElement(r.Popover,{disablePortal:!0,disableScrollLock:!0,anchorOrigin:{vertical:"bottom",horizontal:"right"},transformOrigin:{vertical:"top",horizontal:"right"},...(0,r.bindPopover)(m),slotProps:{paper:{sx:{mt:.5,width:u.current?.getBoundingClientRect().width}}}},n.createElement(L,{propType:d,value:p||(f()??null),setValue:e=>{const t={...p??f(),...e};if(ct(t,i))return v(Object.values(t)[0]?.value);h(t)}},n.createElement(Te,{p:1.5,pt:2.5,pb:3},n.createElement(ze,null,n.createElement(mt,{item:i[0]}),n.createElement(mt,{item:i[1]})),n.createElement(ze,null,n.createElement(mt,{item:i[2]}),n.createElement(mt,{item:i[3]}))))))}var mt=({item:e})=>{const t=(0,p.isExperimentActive)("e_v_3_30");return n.createElement(B,{bind:e.bind},n.createElement(r.Grid,{item:!0,xs:6},n.createElement(r.Grid,{container:!0,gap:.75,alignItems:"center"},n.createElement(r.Grid,{item:!0,xs:12},t?n.createElement(st,null,e.label):n.createElement(q,null,e.label)),n.createElement(r.Grid,{item:!0,xs:12},n.createElement(be,{startIcon:e.icon})))))},dt=Q((({label:e,isSiteRtl:t=!1,extendedValues:o})=>{const{value:i,setValue:s,disabled:u}=j(l.sizePropTypeUtil),{value:m,setValue:d,propType:h,disabled:g}=j(l.dimensionsPropTypeUtil),E=!m&&!i||!!i,v=(0,p.isExperimentActive)("e_v_3_30"),b=e.toLowerCase(),f=E?c.LinkIcon:c.DetachIcon,y=(0,a.__)("Link %s","elementor").replace("%s",b),x=(0,a.__)("Unlink %s","elementor").replace("%s",b);return n.createElement(L,{propType:h,value:m,setValue:d},n.createElement(r.Stack,{direction:"row",gap:2,flexWrap:"nowrap"},v?n.createElement(q,null,e):n.createElement(st,null,e),n.createElement(r.Tooltip,{title:E?x:y,placement:"top"},n.createElement(r.ToggleButton,{"aria-label":E?x:y,size:"tiny",value:"check",selected:E,sx:{marginLeft:"auto"},onChange:()=>{if(!E)return void s(m["block-start"]?.value??null);const e=i?l.sizePropTypeUtil.create(i):null;d({"block-start":e,"block-end":e,"inline-start":e,"inline-end":e})},disabled:u||g},n.createElement(f,{fontSize:"tiny"})))),n.createElement(r.Stack,{direction:"row",gap:2,flexWrap:"nowrap"},n.createElement(r.Grid,{container:!0,gap:.75,alignItems:"center"},n.createElement(r.Grid,{item:!0,xs:12},n.createElement(ht,{bind:"block-start",label:(0,a.__)("Top","elementor")})),n.createElement(r.Grid,{item:!0,xs:12},n.createElement(pt,{bind:"block-start",startIcon:n.createElement(c.SideTopIcon,{fontSize:"tiny"}),isLinked:E,extendedValues:o}))),n.createElement(r.Grid,{container:!0,gap:.75,alignItems:"center"},n.createElement(r.Grid,{item:!0,xs:12},n.createElement(ht,{bind:"inline-end",label:t?(0,a.__)("Left","elementor"):(0,a.__)("Right","elementor")})),n.createElement(r.Grid,{item:!0,xs:12},n.createElement(pt,{bind:"inline-end",startIcon:t?n.createElement(c.SideLeftIcon,{fontSize:"tiny"}):n.createElement(c.SideRightIcon,{fontSize:"tiny"}),isLinked:E,extendedValues:o})))),n.createElement(r.Stack,{direction:"row",gap:2,flexWrap:"nowrap"},n.createElement(r.Grid,{container:!0,gap:.75,alignItems:"center"},n.createElement(r.Grid,{item:!0,xs:12},n.createElement(ht,{bind:"block-end",label:(0,a.__)("Bottom","elementor")})),n.createElement(r.Grid,{item:!0,xs:12},n.createElement(pt,{bind:"block-end",startIcon:n.createElement(c.SideBottomIcon,{fontSize:"tiny"}),isLinked:E,extendedValues:o}))),n.createElement(r.Grid,{container:!0,gap:.75,alignItems:"center"},n.createElement(r.Grid,{item:!0,xs:12},n.createElement(ht,{bind:"inline-start",label:t?(0,a.__)("Right","elementor"):(0,a.__)("Left","elementor")})),n.createElement(r.Grid,{item:!0,xs:12},n.createElement(pt,{bind:"inline-start",startIcon:t?n.createElement(c.SideRightIcon,{fontSize:"tiny"}):n.createElement(c.SideLeftIcon,{fontSize:"tiny"}),isLinked:E,extendedValues:o})))))})),pt=({bind:e,startIcon:t,isLinked:l,extendedValues:r})=>l?n.createElement(be,{startIcon:t,extendedValues:r}):n.createElement(B,{bind:e},n.createElement(be,{startIcon:t,extendedValues:r})),ht=({label:e,bind:t})=>(0,p.isExperimentActive)("e_v_3_30")?n.createElement(B,{bind:t},n.createElement(st,null,e)):n.createElement(q,null,e),gt="tiny",Et=({fontFamilies:e,fontFamily:t,onFontFamilyChange:l,onClose:o})=>{const[i,s]=(0,n.useState)(""),u=((e,t)=>e.reduce(((e,n)=>{const l=n.fonts.filter((e=>e.toLowerCase().includes(t.toLowerCase())));return l.length&&(e.push({type:"category",value:n.label}),l.forEach((t=>{e.push({type:"font",value:t})}))),e}),[]))(e,i),m=()=>{s(""),o()};return n.createElement(r.Stack,null,n.createElement(r.Stack,{direction:"row",alignItems:"center",pl:1.5,pr:.5,py:1.5},n.createElement(c.TextIcon,{fontSize:gt,sx:{mr:.5}}),n.createElement(r.Typography,{variant:"subtitle2"},(0,a.__)("Font Family","elementor")),n.createElement(r.IconButton,{size:gt,sx:{ml:"auto"},onClick:m},n.createElement(c.XIcon,{fontSize:gt}))),n.createElement(r.Box,{px:1.5,pb:1},n.createElement(r.TextField,{autoFocus:!0,fullWidth:!0,size:gt,value:i,placeholder:(0,a.__)("Search","elementor"),onChange:e=>{s(e.target.value)},InputProps:{startAdornment:n.createElement(r.InputAdornment,{position:"start"},n.createElement(c.SearchIcon,{fontSize:gt}))}})),n.createElement(r.Divider,null),u.length>0?n.createElement(vt,{fontListItems:u,setFontFamily:l,handleClose:m,fontFamily:t}):n.createElement(r.Box,{sx:{overflowY:"auto",height:260,width:220}},n.createElement(r.Stack,{alignItems:"center",p:2.5,gap:1.5,overflow:"hidden"},n.createElement(c.TextIcon,{fontSize:"large"}),n.createElement(r.Box,{sx:{maxWidth:160,overflow:"hidden"}},n.createElement(r.Typography,{align:"center",variant:"subtitle2",color:"text.secondary"},(0,a.__)("Sorry, nothing matched","elementor")),n.createElement(r.Typography,{variant:"subtitle2",color:"text.secondary",sx:{display:"flex",width:"100%",justifyContent:"center"}},n.createElement("span",null,"“"),n.createElement("span",{style:{maxWidth:"80%",overflow:"hidden",textOverflow:"ellipsis"}},i),n.createElement("span",null,"”."))),n.createElement(r.Typography,{align:"center",variant:"caption",color:"text.secondary"},(0,a.__)("Try something else.","elementor"),n.createElement(r.Link,{color:"secondary",variant:"caption",component:"button",onClick:()=>s("")},(0,a.__)("Clear & try again","elementor"))))))},vt=({fontListItems:e,setFontFamily:t,handleClose:l,fontFamily:a})=>{const o=(0,n.useRef)(null),i=e.find((e=>e.value===a)),s=ft((({getVirtualIndexes:t})=>{t().forEach((t=>{const n=e[t];n&&"font"===n.type&&((e,t="editor")=>{const n=window;n.elementor?.helpers?.enqueueFont?.(e,t)})(n.value)}))}),100),c=(u={count:e.length,getScrollElement:()=>o.current,estimateSize:()=>36,overscan:6,onChange:s},function(e){const t=n.useReducer((()=>({})),{})[1],l={...e,onChange:(n,l)=>{var r;l?(0,h.flushSync)(t):t(),null==(r=e.onChange)||r.call(e,n,l)}},[r]=n.useState((()=>new I(l)));return r.setOptions(l),z((()=>r._didMount()),[]),z((()=>r._willUpdate())),r}({observeElementRect:y,observeElementOffset:w,scrollToFn:S,...u}));var u;return(0,n.useEffect)((()=>{c.scrollToIndex(e.findIndex((e=>e.value===a)))}),[a]),n.createElement(r.Box,{ref:o,sx:{overflowY:"auto",height:260,width:220}},n.createElement(bt,{role:"listbox",style:{height:`${c.getTotalSize()}px`},"data-testid":"font-list"},c.getVirtualItems().map((a=>{const o=e[a.index],s=a.index===e.length-1,c=1===a.index,u=i?.value===o.value,m=i?-1:0;return"category"===o.type?n.createElement(r.MenuSubheader,{key:a.key,style:{transform:`translateY(${a.start}px)`}},o.value):n.createElement("li",{key:a.key,role:"option","aria-selected":u,onClick:()=>{t(o.value),l()},onKeyDown:e=>{"Enter"===e.key&&(t(o.value),l()),"ArrowDown"===e.key&&s&&(e.preventDefault(),e.stopPropagation()),"ArrowUp"===e.key&&c&&(e.preventDefault(),e.stopPropagation())},tabIndex:u?0:m,style:{transform:`translateY(${a.start}px)`,fontFamily:o.value}},o.value)}))))},bt=(0,r.styled)(r.MenuList)((({theme:e})=>({"& > li":{height:36,position:"absolute",top:0,left:0,width:"100%",display:"flex",alignItems:"center"},'& > [role="option"]':{...e.typography.caption,lineHeight:"inherit",padding:e.spacing(.75,2,.75,4),"&:hover, &:focus":{backgroundColor:e.palette.action.hover},'&[aria-selected="true"]':{backgroundColor:e.palette.action.selected},cursor:"pointer",textOverflow:"ellipsis"},width:"100%",position:"relative"}))),ft=(e,t)=>{const[l]=(0,n.useState)((()=>(0,o.debounce)(e,t)));return(0,n.useEffect)((()=>()=>l.cancel()),[l]),l},yt=Q((({fontFamilies:e})=>{const{value:t,setValue:a,disabled:o}=j(l.stringPropTypeUtil),i=(0,r.usePopupState)({variant:"popover"});return n.createElement(n.Fragment,null,n.createElement(se,null,n.createElement(r.UnstableTag,{variant:"outlined",label:t,endIcon:n.createElement(c.ChevronDownIcon,{fontSize:"tiny"}),...(0,r.bindTrigger)(i),fullWidth:!0,disabled:o})),n.createElement(r.Popover,{disablePortal:!0,disableScrollLock:!0,anchorOrigin:{vertical:"bottom",horizontal:"left"},...(0,r.bindPopover)(i)},n.createElement(Et,{fontFamilies:e,fontFamily:t,onFontFamilyChange:a,onClose:i.close})))})),xt=Q((({placeholder:e})=>{const{value:t,setValue:a,disabled:o}=j(l.urlPropTypeUtil);return n.createElement(se,null,n.createElement(r.TextField,{size:"tiny",fullWidth:!0,value:t??"",disabled:o,onChange:e=>a(e.target.value),placeholder:e}))})),Ct=(0,n.forwardRef)(((e,t)=>{const{options:l,onOptionChange:a,onTextChange:o,allowCustomValues:i=!1,placeholder:s="",minInputLength:c=2,value:u="",...m}=e,d=function(e,t,n){if(null===e)return t;const l=String(e||"")?.toLowerCase();return l.length<n?new Array(0):t.filter((e=>String(e.id).toLowerCase().includes(l)||e.label.toLowerCase().includes(l)))}(u,l,c).map((({id:e})=>e)),p=!!u,h=i||u?.toString()?.length?void 0:()=>!0,g="number"==typeof u&&!!St(l,u);return n.createElement(r.Autocomplete,{...m,ref:t,forcePopupIcon:!1,disableClearable:!0,freeSolo:i,value:u?.toString()||"",size:"tiny",onChange:(e,t)=>a(Number(t)),readOnly:g,options:d,getOptionKey:e=>St(l,e)?.id||e,getOptionLabel:e=>St(l,e)?.label||e.toString(),groupBy:It(l)?e=>St(l,e)?.groupLabel||e:void 0,isOptionEqualToValue:h,filterOptions:()=>d,renderOption:(e,t)=>n.createElement(r.Box,{component:"li",...e,key:e.id},St(l,t)?.label??t),renderInput:e=>n.createElement(wt,{params:e,handleChange:e=>o?.(e),allowClear:p,placeholder:s,hasSelectedValue:g})})})),wt=({params:e,allowClear:t,placeholder:l,handleChange:a,hasSelectedValue:o})=>n.createElement(r.TextField,{...e,placeholder:l,onChange:e=>{a(e.target.value)},sx:{"& .MuiInputBase-input":{cursor:o?"default":void 0}},InputProps:{...e.InputProps,endAdornment:n.createElement(_t,{params:e,allowClear:t,handleChange:a})}}),_t=({allowClear:e,handleChange:t,params:l})=>n.createElement(r.InputAdornment,{position:"end"},e&&n.createElement(r.IconButton,{size:l.size,onClick:()=>t(null),sx:{cursor:"pointer"}},n.createElement(c.XIcon,{fontSize:l.size})));function St(e,t=null){const n=(t||"").toString();return e.find((({id:e})=>n===e.toString()))}function It(e){return e.every((e=>"groupLabel"in e))}var Tt="tiny",zt={label:(0,a.__)("Learn More","elementor"),href:"https://go.elementor.com/element-link-inside-link-infotip"},kt=Q((e=>{const{value:t,path:i,setValue:c,...u}=j(l.linkPropTypeUtil),[m,d]=(0,P.useSessionStorage)(i.join("/")),[p,h]=(0,n.useState)(!!t),{allowCustomValues:g,queryOptions:{endpoint:E="",requestParams:v={}},placeholder:b,minInputLength:f=2,context:{elementId:y}}=e||{},[x,C]=(0,n.useState)((0,k.getLinkInLinkRestriction)(y)),[w,_]=(0,n.useState)(function(e){const t=e?.destination?.value,n=e?.label?.value;return t&&n&&"number"===(e?.destination?.$$type||"url")?[{id:t.toString(),label:n}]:[]}(t)),S=!p&&x.shouldRestrict,I=e=>{c(e),d({...m,value:e})},T=(0,n.useMemo)((()=>(0,o.debounce)((e=>async function(e,t){if(!t||!e)return[];try{const{data:n}=await(0,s.httpService)().get(e,{params:t});return n.data.value}catch{return[]}}(E,e).then((e=>{_(function(e){const t=It(e)?"groupLabel":"label";return e.sort(((e,n)=>e[t]&&n[t]?e[t].localeCompare(n[t]):0))}(e))}))),400)),[E]);return n.createElement(L,{...u,value:t,setValue:c},n.createElement(r.Stack,{gap:1.5},n.createElement(r.Stack,{direction:"row",sx:{justifyContent:"space-between",alignItems:"center",marginInlineEnd:-.75}},n.createElement(q,null,(0,a.__)("Link","elementor")),n.createElement(Vt,{isVisible:!p,linkInLinkRestriction:x},n.createElement(Pt,{disabled:S,active:p,onIconClick:()=>{if(C((0,k.getLinkInLinkRestriction)(y)),x.shouldRestrict&&!p)return;const e=!p;h(e),e||null===t||c(null),e&&m?.value&&c(m.value),d({value:m?.value,meta:{isEnabled:e}})},label:(0,a.__)("Toggle link","elementor")}))),n.createElement(r.Collapse,{in:p,timeout:"auto",unmountOnExit:!0},n.createElement(r.Stack,{gap:1.5},n.createElement(B,{bind:"destination"},n.createElement(se,null,n.createElement(Ct,{options:w,allowCustomValues:g,placeholder:b,value:t?.destination?.value?.settings?.label||t?.destination?.value,onOptionChange:e=>{const n=e?{...t,destination:l.numberPropTypeUtil.create(e),label:l.stringPropTypeUtil.create(St(w,e)?.label||null)}:null;I(n)},onTextChange:e=>{const n=(e=e?.trim()||"")?{...t,destination:l.urlPropTypeUtil.create(e),label:l.stringPropTypeUtil.create("")}:null;I(n),(e=>{_([]),!e||!E||e.length<f||T({...v,term:e})})(e)},minInputLength:f}))),n.createElement(B,{bind:"isTargetBlank"},n.createElement(Ot,{disabled:!t}))))))})),Pt=({disabled:e,active:t,onIconClick:l,label:a})=>n.createElement(r.IconButton,{size:Tt,onClick:l,"aria-label":a,disabled:e},t?n.createElement(c.MinusIcon,{fontSize:Tt}):n.createElement(c.PlusIcon,{fontSize:Tt})),Ot=({disabled:e})=>{const{value:t=!1,setValue:o}=j(l.booleanPropTypeUtil),i=e?{style:{opacity:0}}:{};return n.createElement(r.Grid,{container:!0,alignItems:"center",flexWrap:"nowrap",justifyContent:"space-between"},n.createElement(r.Grid,{item:!0},n.createElement(q,null,(0,a.__)("Open in a new tab","elementor"))),n.createElement(r.Grid,{item:!0,sx:{marginInlineEnd:-1}},n.createElement(r.Switch,{checked:t,onClick:()=>{o(!t)},disabled:e,inputProps:i})))},Vt=({linkInLinkRestriction:e,isVisible:t,children:l})=>{const{shouldRestrict:o,reason:i,elementId:s}=e;return o&&t?n.createElement(r.Infotip,{placement:"right",content:n.createElement(m.InfoTipCard,{content:Mt[i],svgIcon:n.createElement(c.AlertTriangleIcon,null),learnMoreButton:zt,ctaButton:{label:(0,a.__)("Take me there","elementor"),onClick:()=>{s&&(0,k.selectElement)(s)}}})},n.createElement(r.Box,null,l)):n.createElement(n.Fragment,null,l)},Mt={descendant:n.createElement(n.Fragment,null,(0,a.__)("To add a link to this container,","elementor"),n.createElement("br",null),(0,a.__)("first remove the link from the elements inside of it.","elementor")),ancestor:n.createElement(n.Fragment,null,(0,a.__)("To add a link to this element,","elementor"),n.createElement("br",null),(0,a.__)("first remove the link from its parent container.","elementor"))},Ut=Q((({label:e})=>{const{value:t,setValue:o,propType:i,disabled:s}=j(l.layoutDirectionPropTypeUtil),{value:u,setValue:m,disabled:d}=j(l.sizePropTypeUtil),p=!t&&!u||!!u,h=e.toLowerCase(),g=p?c.LinkIcon:c.DetachIcon,E=(0,a.__)("Link %s","elementor").replace("%s",h),v=(0,a.__)("Unlink %s","elementor").replace("%s",h);return n.createElement(L,{propType:i,value:t,setValue:o},n.createElement(r.Stack,{direction:"row",gap:2,flexWrap:"nowrap"},n.createElement(st,null,e),n.createElement(r.Tooltip,{title:p?v:E,placement:"top"},n.createElement(r.ToggleButton,{"aria-label":p?v:E,size:"tiny",value:"check",selected:p,sx:{marginLeft:"auto"},onChange:()=>{if(!p)return void m(t?.column?.value??null);const e=u?l.sizePropTypeUtil.create(u):null;o({row:e,column:e})},disabled:d||s},n.createElement(g,{fontSize:"tiny"})))),n.createElement(r.Stack,{direction:"row",gap:2,flexWrap:"nowrap"},n.createElement(r.Grid,{container:!0,gap:.75,alignItems:"center"},n.createElement(r.Grid,{item:!0,xs:12},n.createElement(q,null,(0,a.__)("Column","elementor"))),n.createElement(r.Grid,{item:!0,xs:12},n.createElement(At,{bind:"column",isLinked:p}))),n.createElement(r.Grid,{container:!0,gap:.75,alignItems:"center"},n.createElement(r.Grid,{item:!0,xs:12},n.createElement(q,null,(0,a.__)("Row","elementor"))),n.createElement(r.Grid,{item:!0,xs:12},n.createElement(At,{bind:"row",isLinked:p})))))})),At=({bind:e,isLinked:t})=>t?n.createElement(be,null):n.createElement(B,{bind:e},n.createElement(be,null)),Gt=[{label:(0,a.__)("Auto","elementor"),value:"auto"},{label:"1/1",value:"1/1"},{label:"4/3",value:"4/3"},{label:"3/4",value:"3/4"},{label:"16/9",value:"16/9"},{label:"9/16",value:"9/16"},{label:"3/2",value:"3/2"},{label:"2/3",value:"2/3"}],Lt="custom",Ft=Q((({label:e})=>{const{value:t,setValue:o,disabled:i}=j(l.stringPropTypeUtil),s=t&&!Gt.some((e=>e.value===t)),[u,d]=s?t.split("/"):["",""],[p,h]=(0,n.useState)(s),[g,E]=(0,n.useState)(u),[v,b]=(0,n.useState)(d),[f,y]=(0,n.useState)(s?Lt:t||"");return n.createElement(se,null,n.createElement(r.Stack,{direction:"column",pt:2,gap:2},n.createElement(r.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},n.createElement(r.Grid,{item:!0,xs:6},n.createElement(st,null,e)),n.createElement(r.Grid,{item:!0,xs:6},n.createElement(r.Select,{size:"tiny",displayEmpty:!0,sx:{overflow:"hidden"},disabled:i,value:f,onChange:e=>{const t=e.target.value,n=t===Lt;h(n),y(t),n||o(t)},fullWidth:!0},[...Gt,{label:(0,a.__)("Custom","elementor"),value:Lt}].map((({label:e,...t})=>n.createElement(m.MenuListItem,{key:t.value,...t,value:t.value??""},e)))))),p&&n.createElement(r.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},n.createElement(r.Grid,{item:!0,xs:6},n.createElement(r.TextField,{size:"tiny",type:"number",fullWidth:!0,disabled:i,value:g,onChange:e=>{const t=e.target.value;E(t),t&&v&&o(`${t}/${v}`)},InputProps:{startAdornment:n.createElement(c.ArrowsMoveHorizontalIcon,{fontSize:"tiny"})}})),n.createElement(r.Grid,{item:!0,xs:6},n.createElement(r.TextField,{size:"tiny",type:"number",fullWidth:!0,disabled:i,value:v,onChange:e=>{const t=e.target.value;b(t),g&&t&&o(`${g}/${t}`)},InputProps:{startAdornment:n.createElement(c.ArrowsMoveVerticalIcon,{fontSize:"tiny"})}})))))})),$t=(0,a.__)("Enable Unfiltered Uploads","elementor"),Bt=(0,a.__)("Before you enable unfiltered files upload, note that such files include a security risk. Elementor does run a process to remove possible malicious code, but there is still risk involved when using such files.","elementor"),Rt=(0,a.__)("Sorry, you can't upload that file yet","elementor"),Wt=(0,a.__)("This is because this file type may pose a security risk. To upload them anyway, ask the site administrator to enable unfiltered file uploads.","elementor"),Dt=(0,a.__)("Failed to enable unfiltered files upload.","elementor"),jt=(0,a.__)("You can try again, if the problem persists, please contact support.","elementor"),Nt=e=>{const{mutateAsync:t,isPending:l}=function(){const e=(0,i.useQueryClient)();return(0,i.useMutation)({mutationFn:({allowUnfilteredFilesUpload:e})=>{return t=ee,n=e?"1":"0",(0,s.httpService)().put(`${Z}/${t}`,{value:n});var t,n},onSuccess:()=>e.invalidateQueries(te)})}(),{canUser:r}=(0,O.useCurrentUserCapabilities)(),[a,o]=(0,n.useState)(!1),c=r("manage_options"),u={...e,isPending:l,handleEnable:async()=>{try{const n=await t({allowUnfilteredFilesUpload:!0});!1===n?.data?.success?o(!0):e.onClose(!0)}catch{o(!0)}},isError:a,onClose:t=>{e.onClose(t),setTimeout((()=>o(!1)),300)}};return c?n.createElement(Kt,{...u}):n.createElement(qt,{...u})},Kt=({open:e,onClose:t,handleEnable:l,isPending:o,isError:i})=>n.createElement(r.Dialog,{open:e,maxWidth:"sm",onClose:()=>t(!1)},n.createElement(r.DialogHeader,{logo:!1},n.createElement(r.DialogTitle,null,$t)),n.createElement(r.Divider,null),n.createElement(r.DialogContent,null,n.createElement(r.DialogContentText,null,i?n.createElement(n.Fragment,null,Dt," ",n.createElement("br",null)," ",jt):Bt)),n.createElement(r.DialogActions,null,n.createElement(r.Button,{size:"medium",color:"secondary",onClick:()=>t(!1)},(0,a.__)("Cancel","elementor")),n.createElement(r.Button,{size:"medium",onClick:()=>l(),variant:"contained",color:"primary",disabled:o},o?n.createElement(r.CircularProgress,{size:24}):(0,a.__)("Enable","elementor")))),qt=({open:e,onClose:t})=>n.createElement(r.Dialog,{open:e,maxWidth:"sm",onClose:()=>t(!1)},n.createElement(r.DialogHeader,{logo:!1},n.createElement(r.DialogTitle,null,Rt)),n.createElement(r.Divider,null),n.createElement(r.DialogContent,null,n.createElement(r.DialogContentText,null,Wt)),n.createElement(r.DialogActions,null,n.createElement(r.Button,{size:"medium",onClick:()=>t(!1),variant:"contained",color:"primary"},(0,a.__)("Got it","elementor")))),Ht="transparent",Xt="#c1c1c1",Yt=`linear-gradient(45deg, ${Xt} 25%, ${Ht} 0, ${Ht} 75%, ${Xt} 0, ${Xt})`,Qt=(0,r.styled)(r.Card)`
	background-color: white;
	background-image: ${Yt}, ${Yt};
	background-size: ${8}px ${8}px;
	background-position:
		0 0,
		${4}px ${4}px;
	border: none;
`,Zt=(0,r.styled)(r.Stack)`
	position: relative;
	height: 140px;
	object-fit: contain;
	padding: 5px;
	justify-content: center;
	align-items: center;
	background-color: rgba( 255, 255, 255, 0.37 );
`,Jt={mode:"browse"},en={mode:"upload"},tn=Q((()=>{const{value:e,setValue:t}=j(l.imageSrcPropTypeUtil),{id:o,url:i}=e??{},{data:s,isFetching:m}=(0,u.useWpMediaAttachment)(o?.value||null),d=s?.url??i?.value??null,{data:p}=ne(),[h,g]=(0,n.useState)(!1),{open:E}=(0,u.useWpMediaFrame)({mediaTypes:["svg"],multiple:!1,selected:o?.value||null,onSelect:e=>{t({id:{$$type:"image-attachment-id",value:e.id},url:null})}}),v=e=>{p||e!==en?E(e):g(!0)};return n.createElement(r.Stack,{gap:1},n.createElement(Nt,{open:h,onClose:e=>{g(!1),e&&E(en)}}),n.createElement(q,null," ",(0,a.__)("SVG","elementor")," "),n.createElement(se,null,n.createElement(Qt,{variant:"outlined"},n.createElement(Zt,null,m?n.createElement(r.CircularProgress,{role:"progressbar"}):n.createElement(r.CardMedia,{component:"img",image:d,alt:(0,a.__)("Preview SVG","elementor"),sx:{maxHeight:"140px",width:"50px"}})),n.createElement(r.CardOverlay,{sx:{"&:hover":{backgroundColor:"rgba( 0, 0, 0, 0.75 )"}}},n.createElement(r.Stack,{gap:1},n.createElement(r.Button,{size:"tiny",color:"inherit",variant:"outlined",onClick:()=>v(Jt)},(0,a.__)("Select SVG","elementor")),n.createElement(r.Button,{size:"tiny",variant:"text",color:"inherit",startIcon:n.createElement(c.UploadIcon,null),onClick:()=>v(en)},(0,a.__)("Upload","elementor")))))))})),{env:nn}=(0,V.parseEnv)("@elementor/editor-controls"),ln=Q((()=>{const{value:e,setValue:t}=j(l.backgroundGradientOverlayPropTypeUtil);return n.createElement(se,null,n.createElement(r.UnstableGradientBox,{sx:{width:"auto",padding:1.5},value:(()=>{if(!e)return;const{type:t,angle:n,stops:l,positions:r}=e;return{type:t.value,angle:n.value,stops:l.value.map((({value:{color:e,offset:t}})=>({color:e.value,offset:t.value}))),positions:r?.value.split(" ")}})(),onChange:e=>{const n=(e=>({...e,type:l.stringPropTypeUtil.create(e.type),angle:l.numberPropTypeUtil.create(e.angle),stops:l.gradientColorStopPropTypeUtil.create(e.stops.map((({color:e,offset:t})=>l.colorStopPropTypeUtil.create({color:l.colorPropTypeUtil.create(e),offset:l.numberPropTypeUtil.create(t)}))))}))(e);n.positions&&(n.positions=l.stringPropTypeUtil.create(e.positions.join(" "))),t(n)}}))})),rn=l.backgroundGradientOverlayPropTypeUtil.create({type:l.stringPropTypeUtil.create("linear"),angle:l.numberPropTypeUtil.create(180),stops:l.gradientColorStopPropTypeUtil.create([l.colorStopPropTypeUtil.create({color:l.colorPropTypeUtil.create("rgb(0,0,0)"),offset:l.numberPropTypeUtil.create(0)}),l.colorStopPropTypeUtil.create({color:l.colorPropTypeUtil.create("rgb(255,255,255)"),offset:l.numberPropTypeUtil.create(100)})])}),an=[{value:"fixed",label:(0,a.__)("Fixed","elementor"),renderContent:({size:e})=>n.createElement(c.PinIcon,{fontSize:e}),showTooltip:!0},{value:"scroll",label:(0,a.__)("Scroll","elementor"),renderContent:({size:e})=>n.createElement(c.PinnedOffIcon,{fontSize:e}),showTooltip:!0}],on=()=>n.createElement(ze,null,n.createElement(r.Grid,{item:!0,xs:6},n.createElement(q,null,(0,a.__)("Attachment","elementor"))),n.createElement(r.Grid,{item:!0,xs:6,sx:{display:"flex",justifyContent:"flex-end",overflow:"hidden"}},n.createElement(rt,{options:an}))),sn=[{label:(0,a.__)("Center center","elementor"),value:"center center"},{label:(0,a.__)("Center left","elementor"),value:"center left"},{label:(0,a.__)("Center right","elementor"),value:"center right"},{label:(0,a.__)("Top center","elementor"),value:"top center"},{label:(0,a.__)("Top left","elementor"),value:"top left"},{label:(0,a.__)("Top right","elementor"),value:"top right"},{label:(0,a.__)("Bottom center","elementor"),value:"bottom center"},{label:(0,a.__)("Bottom left","elementor"),value:"bottom left"},{label:(0,a.__)("Bottom right","elementor"),value:"bottom right"},{label:(0,a.__)("Custom","elementor"),value:"custom"}],cn=()=>{const e=j(l.backgroundImagePositionOffsetPropTypeUtil),t=j(l.stringPropTypeUtil),o=!!e.value;return n.createElement(r.Grid,{container:!0,spacing:1.5},n.createElement(r.Grid,{item:!0,xs:12},n.createElement(ze,null,n.createElement(r.Grid,{item:!0,xs:6},n.createElement(q,null,(0,a.__)("Position","elementor"))),n.createElement(r.Grid,{item:!0,xs:6,sx:{display:"flex",justifyContent:"flex-end",overflow:"hidden"}},n.createElement(r.Select,{size:"tiny",value:(e.value?"custom":t.value)??"",onChange:n=>{const l=n.target.value||null;"custom"===l?e.setValue({x:null,y:null}):t.setValue(l)},fullWidth:!0},sn.map((({label:e,value:t})=>n.createElement(m.MenuListItem,{key:t,value:t??""},e))))))),o?n.createElement(L,{...e},n.createElement(r.Grid,{item:!0,xs:12},n.createElement(r.Grid,{container:!0,spacing:1.5},n.createElement(r.Grid,{item:!0,xs:6},n.createElement(B,{bind:"x"},n.createElement(be,{startIcon:n.createElement(c.LetterXIcon,{fontSize:"tiny"})}))),n.createElement(r.Grid,{item:!0,xs:6},n.createElement(B,{bind:"y"},n.createElement(be,{startIcon:n.createElement(c.LetterYIcon,{fontSize:"tiny"})})))))):null)},un=[{value:"repeat",label:(0,a.__)("Repeat","elementor"),renderContent:({size:e})=>n.createElement(c.GridDotsIcon,{fontSize:e}),showTooltip:!0},{value:"repeat-x",label:(0,a.__)("Repeat-x","elementor"),renderContent:({size:e})=>n.createElement(c.DotsHorizontalIcon,{fontSize:e}),showTooltip:!0},{value:"repeat-y",label:(0,a.__)("Repeat-y","elementor"),renderContent:({size:e})=>n.createElement(c.DotsVerticalIcon,{fontSize:e}),showTooltip:!0},{value:"no-repeat",label:(0,a.__)("No-repeat","elementor"),renderContent:({size:e})=>n.createElement(c.XIcon,{fontSize:e}),showTooltip:!0}],mn=()=>n.createElement(ze,null,n.createElement(r.Grid,{item:!0,xs:6},n.createElement(q,null,(0,a.__)("Repeat","elementor"))),n.createElement(r.Grid,{item:!0,xs:6,sx:{display:"flex",justifyContent:"flex-end"}},n.createElement(rt,{options:un}))),dn=[{value:"auto",label:(0,a.__)("Auto","elementor"),renderContent:({size:e})=>n.createElement(c.LetterAIcon,{fontSize:e}),showTooltip:!0},{value:"cover",label:(0,a.__)("Cover","elementor"),renderContent:({size:e})=>n.createElement(c.ArrowsMaximizeIcon,{fontSize:e}),showTooltip:!0},{value:"contain",label:(0,a.__)("Contain","elementor"),renderContent:({size:e})=>n.createElement(c.ArrowBarBothIcon,{fontSize:e}),showTooltip:!0},{value:"custom",label:(0,a.__)("Custom","elementor"),renderContent:({size:e})=>n.createElement(c.PencilIcon,{fontSize:e}),showTooltip:!0}],pn=()=>{const e=j(l.backgroundImageSizeScalePropTypeUtil),t=j(l.stringPropTypeUtil),o=!!e.value;return n.createElement(r.Grid,{container:!0,spacing:1.5},n.createElement(r.Grid,{item:!0,xs:12},n.createElement(ze,null,n.createElement(r.Grid,{item:!0,xs:6},n.createElement(q,null,(0,a.__)("Size","elementor"))),n.createElement(r.Grid,{item:!0,xs:6,sx:{display:"flex",justifyContent:"flex-end"}},n.createElement(tt,{exclusive:!0,items:dn,value:e.value?"custom":t.value,onChange:n=>{"custom"===n?e.setValue({width:null,height:null}):t.setValue(n)}})))),o?n.createElement(L,{...e},n.createElement(r.Grid,{item:!0,xs:12},n.createElement(ze,null,n.createElement(r.Grid,{item:!0,xs:6},n.createElement(B,{bind:"width"},n.createElement(be,{startIcon:n.createElement(c.ArrowsMoveHorizontalIcon,{fontSize:"tiny"}),extendedValues:["auto"]}))),n.createElement(r.Grid,{item:!0,xs:6},n.createElement(B,{bind:"height"},n.createElement(be,{startIcon:n.createElement(c.ArrowsMoveVerticalIcon,{fontSize:"tiny"}),extendedValues:["auto"]})))))):null)},hn=l.backgroundColorOverlayPropTypeUtil.create({color:l.colorPropTypeUtil.create("#00000033")}),gn=()=>({$$type:"background-image-overlay",value:{image:{$$type:"image",value:{src:{$$type:"image-src",value:{url:{$$type:"url",value:nn.background_placeholder_image},id:null}},size:{$$type:"string",value:"large"}}}}}),En=[{label:(0,a.__)("Thumbnail - 150 x 150","elementor"),value:"thumbnail"},{label:(0,a.__)("Medium - 300 x 300","elementor"),value:"medium"},{label:(0,a.__)("Large 1024 x 1024","elementor"),value:"large"},{label:(0,a.__)("Full","elementor"),value:"full"}],vn=Q((()=>{const{propType:e,value:t,setValue:r,disabled:o}=j(l.backgroundOverlayPropTypeUtil);return n.createElement(L,{propType:e,value:t,setValue:r},n.createElement(De,{openOnAdd:!0,disabled:o,values:t??[],setValues:r,label:(0,a.__)("Overlay","elementor"),itemSettings:{Icon:yn,Label:Sn,Content:bn,initialValues:gn()}}))})),bn=({anchorEl:e=null,bind:t})=>n.createElement(B,{bind:t},n.createElement(fn,{anchorEl:e})),fn=({anchorEl:e})=>{const{getTabsProps:t,getTabProps:o,getTabPanelProps:i}=(({color:e,image:t,gradient:a})=>{const{value:o,setValue:i}=j(l.backgroundImageOverlayPropTypeUtil),{value:s,setValue:c}=j(l.backgroundColorOverlayPropTypeUtil),{value:u,setValue:m}=j(l.backgroundGradientOverlayPropTypeUtil),{getTabsProps:d,getTabProps:p,getTabPanelProps:h}=(0,r.useTabs)(s?"color":u?"gradient":"image"),g=(0,n.useRef)({image:t,color:e,gradient:a}),E=(e,t)=>{t&&(g.current[e]=t)},v=(e,t)=>{switch(t){case"image":i(g.current.image),E("color",s),E("gradient",u);break;case"gradient":m(g.current.gradient),E("color",s),E("image",o);break;case"color":c(g.current.color),E("image",o),E("gradient",u)}return d().onChange(e,t)};return{getTabProps:p,getTabPanelProps:h,getTabsProps:()=>({...d(),onChange:v})}})({image:gn().value,color:hn.value,gradient:rn.value});return n.createElement(r.Box,{sx:{width:"100%"}},n.createElement(r.Box,{sx:{borderBottom:1,borderColor:"divider"}},n.createElement(r.Tabs,{size:"small",variant:"fullWidth",...t(),"aria-label":(0,a.__)("Background Overlay","elementor")},n.createElement(r.Tab,{label:(0,a.__)("Image","elementor"),...o("image")}),n.createElement(r.Tab,{label:(0,a.__)("Gradient","elementor"),...o("gradient")}),n.createElement(r.Tab,{label:(0,a.__)("Color","elementor"),...o("color")}))),n.createElement(r.TabPanel,{sx:{p:1.5},...i("image")},n.createElement(Te,null,n.createElement(Pn,null))),n.createElement(r.TabPanel,{sx:{p:1.5},...i("gradient")},n.createElement(ln,null)),n.createElement(r.TabPanel,{sx:{p:1.5},...i("color")},n.createElement(Te,null,n.createElement(kn,{anchorEl:e}))))},yn=({value:e})=>{switch(e.$$type){case"background-image-overlay":return n.createElement(wn,{value:e});case"background-color-overlay":return n.createElement(Cn,{value:e});case"background-gradient-overlay":return n.createElement(_n,{value:e});default:return null}},xn=e=>e?.value?.color?.value?e.value.color.value:"",Cn=({value:e})=>{const t=xn(e);return n.createElement(On,{size:"inherit",component:"span",value:t})},wn=({value:e})=>{const{imageUrl:t}=Vn(e);return n.createElement(r.CardMedia,{image:t,sx:e=>({height:"1em",width:"1em",borderRadius:e.shape.borderRadius/2+"px",outline:`1px solid ${e.palette.action.disabled}`})})},_n=({value:e})=>{const t=Un(e);return n.createElement(On,{size:"inherit",component:"span",value:t})},Sn=({value:e})=>{switch(e.$$type){case"background-image-overlay":return n.createElement(Tn,{value:e});case"background-color-overlay":return n.createElement(In,{value:e});case"background-gradient-overlay":return n.createElement(zn,{value:e});default:return null}},In=({value:e})=>{const t=xn(e);return n.createElement("span",null,t)},Tn=({value:e})=>{const{imageTitle:t}=Vn(e);return n.createElement("span",null,t)},zn=({value:e})=>"linear"===e.value.type.value?n.createElement("span",null,(0,a.__)("Linear Gradient","elementor")):n.createElement("span",null,(0,a.__)("Radial Gradient","elementor")),kn=({anchorEl:e})=>{const t=j(l.backgroundColorOverlayPropTypeUtil);return n.createElement(L,{...t},n.createElement(B,{bind:"color"},n.createElement(we,{anchorEl:e})))},Pn=()=>{const e=j(l.backgroundImageOverlayPropTypeUtil);return n.createElement(L,{...e},n.createElement(B,{bind:"image"},n.createElement(r.Grid,{container:!0,spacing:1,alignItems:"center"},n.createElement(r.Grid,{item:!0,xs:12},n.createElement(me,{resolutionLabel:(0,a.__)("Resolution","elementor"),sizes:En})))),n.createElement(B,{bind:"position"},n.createElement(cn,null)),n.createElement(B,{bind:"repeat"},n.createElement(mn,null)),n.createElement(B,{bind:"size"},n.createElement(pn,null)),n.createElement(B,{bind:"attachment"},n.createElement(on,null)))},On=(0,r.styled)(r.UnstableColorIndicator)((({theme:e})=>({borderRadius:e.shape.borderRadius/2+"px"}))),Vn=e=>{let t,n=null;const l=e?.value.image.value?.src.value,{data:r}=(0,u.useWpMediaAttachment)(l.id?.value||null);if(l.id){const e=Mn(r?.filename);t=`${r?.title}${e}`||null,n=r?.url||null}else l.url&&(n=l.url.value,t=n?.substring(n.lastIndexOf("/")+1)||null);return{imageTitle:t,imageUrl:n}},Mn=e=>e?`.${e.substring(e.lastIndexOf(".")+1)}`:"",Un=e=>{const t=e.value,n=t.stops.value?.map((({value:{color:e,offset:t}})=>`${e.value} ${t.value??0}%`))?.join(",");return"linear"===t.type.value?`linear-gradient(${t.angle.value}deg, ${n})`:`radial-gradient(circle at ${t.positions.value}, ${n})`},An=Q((()=>{const e=j(l.backgroundPropTypeUtil),t=(0,p.isExperimentActive)("e_v_3_30"),o=(0,a.__)("Color","elementor");return n.createElement(L,{...e},n.createElement(B,{bind:"background-overlay"},n.createElement(vn,null)),n.createElement(B,{bind:"color"},n.createElement(r.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},n.createElement(r.Grid,{item:!0,xs:6},t?n.createElement(st,null,o):n.createElement(q,null,o)),n.createElement(r.Grid,{item:!0,xs:6},n.createElement(we,null)))))})),Gn=Q((()=>{const{value:e,setValue:t,disabled:a}=j(l.booleanPropTypeUtil);return n.createElement("div",{style:{display:"flex",justifyContent:"flex-end"}},n.createElement(r.Switch,{checked:!!e,onChange:e=>{t(e.target.checked)},size:"small",disabled:a}))}));(window.elementorV2=window.elementorV2||{}).editorControls=t}(),window.elementorV2.editorControls?.init?.();