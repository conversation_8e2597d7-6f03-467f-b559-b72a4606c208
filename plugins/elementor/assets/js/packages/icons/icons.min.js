!function(){"use strict";var C={n:function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return C.d(t,{a:t}),t},d:function(e,t){for(var n in t)C.o(t,n)&&!C.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},o:function(C,e){return Object.prototype.hasOwnProperty.call(C,e)},r:function(C){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(C,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(C,"__esModule",{value:!0})}},e={};C.r(e),C.d(e,{AIIcon:function(){return l},AcademyIcon:function(){return o},AdjustmentsHorizontalIcon:function(){return d},AdjustmentsIcon:function(){return c},AffiliatesIcon:function(){return a},AlertCircleIcon:function(){return f},AlertOctagonFilledIcon:function(){return L},AlertTriangleFilledIcon:function(){return i},AlertTriangleIcon:function(){return u},AlignCenterIcon:function(){return V},AlignJustifiedIcon:function(){return H},AlignLeftIcon:function(){return M},AlignRightIcon:function(){return Z},AppsIcon:function(){return R},ArchiveTemplateIcon:function(){return v},ArrowBackIcon:function(){return p},ArrowBarBothIcon:function(){return m},ArrowDownSmallIcon:function(){return E},ArrowForwardIcon:function(){return w},ArrowLeftIcon:function(){return h},ArrowRightIcon:function(){return B},ArrowUpRightIcon:function(){return x},ArrowUpSmallIcon:function(){return I},ArrowsLeftRightIcon:function(){return s},ArrowsMaximizeIcon:function(){return g},ArrowsMoveHorizontalIcon:function(){return A},ArrowsMoveVerticalIcon:function(){return S},ArrowsRightLeftIcon:function(){return T},AtomIcon:function(){return y},BanIcon:function(){return P},BoltIcon:function(){return k},BorderCornersIcon:function(){return b},BrandFacebookIcon:function(){return D},BriefcaseIcon:function(){return F},BrushIcon:function(){return U},BugOffIcon:function(){return O},CalendarDollarIcon:function(){return J},CalendarIcon:function(){return W},CameraIcon:function(){return j},CartIcon:function(){return z},ChatbotIcon:function(){return X},CheckIcon:function(){return _},CheckedCircleIcon:function(){return G},ChecklistIcon:function(){return N},ChevronDownIcon:function(){return K},ChevronDownSmallIcon:function(){return Y},ChevronLeftIcon:function(){return q},ChevronLeftSmallIcon:function(){return Q},ChevronRightIcon:function(){return $},ChevronRightSmallIcon:function(){return CC},ChevronUpIcon:function(){return eC},ChevronUpSmallIcon:function(){return tC},CircleCheckFilledIcon:function(){return nC},CircleMinusIcon:function(){return rC},CircleNumber1Icon:function(){return lC},CirclePlusIcon:function(){return oC},CircleXIcon:function(){return dC},ClearIcon:function(){return cC},ClockIcon:function(){return aC},CloudBackupIcon:function(){return fC},CloudIcon:function(){return LC},CodeIcon:function(){return iC},ColorFilterIcon:function(){return uC},ColorSwatchIcon:function(){return VC},ContainerTemplateIcon:function(){return HC},CopyIcon:function(){return MC},CopyPageIcon:function(){return ZC},CornerUpRightIcon:function(){return RC},CreditCardIcon:function(){return vC},CrownIcon:function(){return pC},DatabaseIcon:function(){return mC},DesktopIcon:function(){return EC},DetachIcon:function(){return wC},DiamondIcon:function(){return hC},DiscountCheckFilledIcon:function(){return BC},DomainIcon:function(){return xC},DotsHorizontalIcon:function(){return IC},DotsVerticalIcon:function(){return sC},DownloadIcon:function(){return gC},EditIcon:function(){return AC},EraseIcon:function(){return SC},Error404TemplateIcon:function(){return TC},ExpandBottomIcon:function(){return yC},ExpandDiagonalIcon:function(){return PC},ExpandIcon:function(){return kC},ExternalLinkIcon:function(){return bC},EyeIcon:function(){return DC},EyeOffIcon:function(){return FC},FileReportIcon:function(){return UC},FilesIcon:function(){return OC},FilterIcon:function(){return JC},FolderIcon:function(){return WC},FooterTemplateIcon:function(){return jC},GiftIcon:function(){return zC},GridDotsIcon:function(){return XC},GripVerticalIcon:function(){return _C},HeaderTemplateIcon:function(){return GC},HeadsetIcon:function(){return NC},HeartHandShakeIcon:function(){return KC},HelpIcon:function(){return YC},HistoryIcon:function(){return qC},HomeIcon:function(){return QC},InfoCircleFilledIcon:function(){return $C},InfoCircleIcon:function(){return Ce},ItalicIcon:function(){return ee},JustifyBottomIcon:function(){return te},JustifyCenterIcon:function(){return ne},JustifyDistributeVerticalIcon:function(){return re},JustifySpaceAroundVerticalIcon:function(){return le},JustifySpaceBetweenVerticalIcon:function(){return oe},JustifyTopIcon:function(){return de},KeyboardIcon:function(){return ce},LandingPageTemplateIcon:function(){return ae},LaptopIcon:function(){return fe},LayoutAlignCenterIcon:function(){return Le},LayoutAlignLeftIcon:function(){return ie},LayoutAlignRightIcon:function(){return ue},LayoutDistributeVerticalIcon:function(){return Ve},LetterAIcon:function(){return He},LetterCaseIcon:function(){return Me},LetterCaseLowerIcon:function(){return Ze},LetterCaseUpperIcon:function(){return Re},LetterXIcon:function(){return ve},LetterYIcon:function(){return pe},LibraryIcon:function(){return me},LikeIcon:function(){return Ee},LinkIcon:function(){return we},ListIcon:function(){return he},Loader2Icon:function(){return Be},LockFilledIcon:function(){return xe},LockIcon:function(){return Ie},LogoutIcon:function(){return se},LoopItemTemplateIcon:function(){return ge},MailIcon:function(){return Ae},MapPinIcon:function(){return Se},Menu2Icon:function(){return Te},MenuIcon:function(){return ye},MessageIcon:function(){return Pe},MessageLinesIcon:function(){return ke},MessagesIcon:function(){return be},MinimizeDiagonalIcon:function(){return De},MinusIcon:function(){return Fe},MobileIcon:function(){return Ue},MobileLandscapeIcon:function(){return Oe},MobilePortraitIcon:function(){return Je},NotificationFilledIcon:function(){return We},NotificationIcon:function(){return je},OverlineIcon:function(){return ze},PageTemplateIcon:function(){return Xe},PageTypeIcon:function(){return _e},PagesIcon:function(){return Ge},PencilIcon:function(){return Ne},PencilSparklesIcon:function(){return Ke},PhotoIcon:function(){return Ye},PinIcon:function(){return qe},PinnedOffIcon:function(){return Qe},PlugCheckIcon:function(){return $e},PlugIcon:function(){return Ct},PlugRefreshIcon:function(){return et},PlugXIcon:function(){return tt},PlusIcon:function(){return nt},PointFilledIcon:function(){return rt},PopupTemplateIcon:function(){return lt},PostTypeIcon:function(){return ot},RadioButtonUncheckedIcon:function(){return dt},RadiusBottomLeftIcon:function(){return ct},RadiusBottomRightIcon:function(){return at},RadiusTopLeftIcon:function(){return ft},RadiusTopRightIcon:function(){return Lt},RefreshIcon:function(){return it},ReloadIcon:function(){return ut},RepeatIcon:function(){return Vt},ResetIcon:function(){return Ht},RocketIcon:function(){return Mt},RotateIcon:function(){return Zt},RouterIcon:function(){return Rt},SFTPIcon:function(){return vt},ScreenShareIcon:function(){return pt},SearchIcon:function(){return mt},SearchResultsTemplateIcon:function(){return Et},SectionTemplateIcon:function(){return wt},SelectorIcon:function(){return ht},Send2Icon:function(){return Bt},SendIcon:function(){return xt},ServerCogIcon:function(){return It},SettingsIcon:function(){return st},ShieldCheckIcon:function(){return gt},ShieldIcon:function(){return At},ShrinkIcon:function(){return St},SideAllIcon:function(){return Tt},SideBottomIcon:function(){return yt},SideLeftIcon:function(){return Pt},SideRightIcon:function(){return kt},SideTopIcon:function(){return bt},SiteLockIcon:function(){return Dt},SiteLockOpenIcon:function(){return Ft},SocialIcon:function(){return Ut},SpeakerphoneIcon:function(){return Ot},StarIcon:function(){return Jt},StarOffIcon:function(){return Wt},StrikethroughIcon:function(){return jt},StructureIcon:function(){return zt},TabletIcon:function(){return Xt},TabletLandscapeIcon:function(){return _t},TabletPortraitIcon:function(){return Gt},TextDirectionLtrIcon:function(){return Nt},TextDirectionRtlIcon:function(){return Kt},TextIcon:function(){return Yt},ThemeBuilderIcon:function(){return qt},ThumbUpIcon:function(){return Qt},ThumbsDownIcon:function(){return $t},ToggleRightIcon:function(){return Cn},TrashIcon:function(){return en},UnderlineIcon:function(){return tn},UpgradeIcon:function(){return nn},UploadIcon:function(){return rn},UserHeartIcon:function(){return ln},UserIcon:function(){return on},UserPlusIcon:function(){return dn},UsersIcon:function(){return cn},WalletIcon:function(){return an},WebsiteIcon:function(){return fn},WhatsappIcon:function(){return Ln},WidescreenIcon:function(){return un},WidgetsIcon:function(){return Vn},WordpressIcon:function(){return Hn},WorldIcon:function(){return Mn},XIcon:function(){return Zn}});var t=window.React,n=window.elementorV2.ui.SvgIcon,r=C.n(n),l=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M18.25 3.25C18.6642 3.25 19 3.58579 19 4C19 4.33152 19.1317 4.64946 19.3661 4.88388C19.6005 5.1183 19.9185 5.25 20.25 5.25C20.6642 5.25 21 5.58579 21 6C21 6.41421 20.6642 6.75 20.25 6.75C19.9185 6.75 19.6005 6.8817 19.3661 7.11612C19.1317 7.35054 19 7.66848 19 8C19 8.41421 18.6642 8.75 18.25 8.75C17.8358 8.75 17.5 8.41421 17.5 8C17.5 7.66848 17.3683 7.35054 17.1339 7.11612C16.8995 6.8817 16.5815 6.75 16.25 6.75C15.8358 6.75 15.5 6.41421 15.5 6C15.5 5.58579 15.8358 5.25 16.25 5.25C16.5815 5.25 16.8995 5.1183 17.1339 4.88388C17.3683 4.64946 17.5 4.33152 17.5 4C17.5 3.58579 17.8358 3.25 18.25 3.25ZM18.25 5.88746C18.2318 5.90673 18.2133 5.92576 18.1945 5.94454C18.1758 5.96333 18.1567 5.98182 18.1375 6C18.1567 6.01819 18.1758 6.03667 18.1945 6.05546C18.2133 6.07424 18.2318 6.09327 18.25 6.11254C18.2682 6.09327 18.2867 6.07424 18.3055 6.05546C18.3242 6.03667 18.3433 6.01819 18.3625 6C18.3433 5.98182 18.3242 5.96333 18.3055 5.94454C18.2867 5.92576 18.2682 5.90673 18.25 5.88746ZM9.25 5.25C9.66421 5.25 10 5.58579 10 6C10 7.39239 10.5531 8.72774 11.5377 9.71231C12.5223 10.6969 13.8576 11.25 15.25 11.25C15.6642 11.25 16 11.5858 16 12C16 12.4142 15.6642 12.75 15.25 12.75C13.8576 12.75 12.5223 13.3031 11.5377 14.2877C10.5531 15.2723 10 16.6076 10 18C10 18.4142 9.66421 18.75 9.25 18.75C8.83579 18.75 8.5 18.4142 8.5 18C8.5 16.6076 7.94688 15.2723 6.96231 14.2877C5.97774 13.3031 4.64239 12.75 3.25 12.75C2.83579 12.75 2.5 12.4142 2.5 12C2.5 11.5858 2.83579 11.25 3.25 11.25C4.64239 11.25 5.97774 10.6969 6.96231 9.71231C7.94688 8.72774 8.5 7.39239 8.5 6C8.5 5.58579 8.83579 5.25 9.25 5.25ZM9.25 9.09234C8.93321 9.70704 8.52103 10.2749 8.02297 10.773C7.52491 11.271 6.95704 11.6832 6.34234 12C6.95704 12.3168 7.52491 12.729 8.02297 13.227C8.52103 13.7251 8.93321 14.293 9.25 14.9077C9.56679 14.293 9.97897 13.7251 10.477 13.227C10.9751 12.729 11.543 12.3168 12.1577 12C11.543 11.6832 10.9751 11.271 10.477 10.773C9.97897 10.2749 9.56679 9.70704 9.25 9.09234ZM18.25 15.25C18.6642 15.25 19 15.5858 19 16C19 16.3315 19.1317 16.6495 19.3661 16.8839C19.6005 17.1183 19.9185 17.25 20.25 17.25C20.6642 17.25 21 17.5858 21 18C21 18.4142 20.6642 18.75 20.25 18.75C19.9185 18.75 19.6005 18.8817 19.3661 19.1161C19.1317 19.3505 19 19.6685 19 20C19 20.4142 18.6642 20.75 18.25 20.75C17.8358 20.75 17.5 20.4142 17.5 20C17.5 19.6685 17.3683 19.3505 17.1339 19.1161C16.8995 18.8817 16.5815 18.75 16.25 18.75C15.8358 18.75 15.5 18.4142 15.5 18C15.5 17.5858 15.8358 17.25 16.25 17.25C16.5815 17.25 16.8995 17.1183 17.1339 16.8839C17.3683 16.6495 17.5 16.3315 17.5 16C17.5 15.5858 17.8358 15.25 18.25 15.25ZM18.25 17.8875C18.2318 17.9067 18.2133 17.9258 18.1945 17.9445C18.1758 17.9633 18.1567 17.9818 18.1375 18C18.1567 18.0182 18.1758 18.0367 18.1945 18.0555C18.2133 18.0742 18.2318 18.0933 18.25 18.1125C18.2682 18.0933 18.2867 18.0742 18.3055 18.0555C18.3242 18.0367 18.3433 18.0182 18.3625 18C18.3433 17.9818 18.3242 17.9633 18.3055 17.9445C18.2867 17.9258 18.2682 17.9067 18.25 17.8875Z"})))),o=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M11.7215 4.30364C11.9003 4.23212 12.0997 4.23212 12.2785 4.30364L22.2785 8.30364C22.5633 8.41754 22.75 8.69332 22.75 9V15C22.75 15.4142 22.4142 15.75 22 15.75C21.5858 15.75 21.25 15.4142 21.25 15V10.1078L18.75 11.1078V16C18.75 17.2236 17.7962 18.1831 16.5781 18.7921C15.3248 19.4188 13.6777 19.75 12 19.75C10.3223 19.75 8.67519 19.4188 7.42195 18.7921C6.20379 18.1831 5.25 17.2236 5.25 16V11.1078L1.72146 9.69636C1.43671 9.58246 1.25 9.30668 1.25 9C1.25 8.69332 1.43671 8.41754 1.72146 8.30364L11.7215 4.30364ZM6.29432 9.90995C6.28378 9.90545 6.27312 9.90119 6.26234 9.89716L4.01944 9L12 5.80777L19.9806 9L17.7377 9.89716C17.7269 9.90119 17.7162 9.90545 17.7057 9.90995L12 12.1922L6.29432 9.90995ZM6.75 11.7078V16C6.75 16.3677 7.06049 16.9344 8.09277 17.4505C9.08996 17.9491 10.4951 18.25 12 18.25C13.5049 18.25 14.91 17.9491 15.9072 17.4505C16.9395 16.9344 17.25 16.3677 17.25 16V11.7078L12.2785 13.6964C12.0997 13.7679 11.9003 13.7679 11.7215 13.6964L6.75 11.7078Z"})))),d=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M14 4.75C13.3096 4.75 12.75 5.30964 12.75 6C12.75 6.69036 13.3096 7.25 14 7.25C14.6904 7.25 15.25 6.69036 15.25 6C15.25 5.30964 14.6904 4.75 14 4.75ZM11.3535 5.25C11.68 4.09575 12.7412 3.25 14 3.25C15.2588 3.25 16.32 4.09575 16.6465 5.25H20C20.4142 5.25 20.75 5.58579 20.75 6C20.75 6.41421 20.4142 6.75 20 6.75H16.6465C16.32 7.90425 15.2588 8.75 14 8.75C12.7412 8.75 11.68 7.90425 11.3535 6.75H4C3.58579 6.75 3.25 6.41421 3.25 6C3.25 5.58579 3.58579 5.25 4 5.25H11.3535ZM8 10.75C7.30964 10.75 6.75 11.3096 6.75 12C6.75 12.6904 7.30964 13.25 8 13.25C8.69036 13.25 9.25 12.6904 9.25 12C9.25 11.3096 8.69036 10.75 8 10.75ZM5.35352 11.25C5.67998 10.0957 6.74122 9.25 8 9.25C9.25878 9.25 10.32 10.0957 10.6465 11.25H20C20.4142 11.25 20.75 11.5858 20.75 12C20.75 12.4142 20.4142 12.75 20 12.75H10.6465C10.32 13.9043 9.25878 14.75 8 14.75C6.74122 14.75 5.67998 13.9043 5.35352 12.75H4C3.58579 12.75 3.25 12.4142 3.25 12C3.25 11.5858 3.58579 11.25 4 11.25H5.35352ZM17 16.75C16.3096 16.75 15.75 17.3096 15.75 18C15.75 18.6904 16.3096 19.25 17 19.25C17.6904 19.25 18.25 18.6904 18.25 18C18.25 17.3096 17.6904 16.75 17 16.75ZM14.3535 17.25C14.68 16.0957 15.7412 15.25 17 15.25C18.2588 15.25 19.32 16.0957 19.6465 17.25H20C20.4142 17.25 20.75 17.5858 20.75 18C20.75 18.4142 20.4142 18.75 20 18.75H19.6465C19.32 19.9043 18.2588 20.75 17 20.75C15.7412 20.75 14.68 19.9043 14.3535 18.75H4C3.58579 18.75 3.25 18.4142 3.25 18C3.25 17.5858 3.58579 17.25 4 17.25H14.3535Z"})))),c=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6 3.25C6.41421 3.25 6.75 3.58579 6.75 4V7.35352C7.90425 7.67998 8.75 8.74122 8.75 10C8.75 11.2588 7.90425 12.32 6.75 12.6465V20C6.75 20.4142 6.41421 20.75 6 20.75C5.58579 20.75 5.25 20.4142 5.25 20V12.6465C4.09575 12.32 3.25 11.2588 3.25 10C3.25 8.74122 4.09575 7.67998 5.25 7.35352V4C5.25 3.58579 5.58579 3.25 6 3.25ZM12 3.25C12.4142 3.25 12.75 3.58579 12.75 4V13.3535C13.9043 13.68 14.75 14.7412 14.75 16C14.75 17.2588 13.9043 18.32 12.75 18.6465V20C12.75 20.4142 12.4142 20.75 12 20.75C11.5858 20.75 11.25 20.4142 11.25 20V18.6465C10.0957 18.32 9.25 17.2588 9.25 16C9.25 14.7412 10.0957 13.68 11.25 13.3535V4C11.25 3.58579 11.5858 3.25 12 3.25ZM18 3.25C18.4142 3.25 18.75 3.58579 18.75 4V4.35352C19.9043 4.67998 20.75 5.74122 20.75 7C20.75 8.25878 19.9043 9.32002 18.75 9.64648V20C18.75 20.4142 18.4142 20.75 18 20.75C17.5858 20.75 17.25 20.4142 17.25 20V9.64648C16.0957 9.32002 15.25 8.25878 15.25 7C15.25 5.74122 16.0957 4.67998 17.25 4.35352V4C17.25 3.58579 17.5858 3.25 18 3.25ZM18 5.75C17.3096 5.75 16.75 6.30964 16.75 7C16.75 7.69036 17.3096 8.25 18 8.25C18.6904 8.25 19.25 7.69036 19.25 7C19.25 6.30964 18.6904 5.75 18 5.75ZM6 8.75C5.30964 8.75 4.75 9.30964 4.75 10C4.75 10.6904 5.30964 11.25 6 11.25C6.69036 11.25 7.25 10.6904 7.25 10C7.25 9.30964 6.69036 8.75 6 8.75ZM12 14.75C11.3096 14.75 10.75 15.3096 10.75 16C10.75 16.6904 11.3096 17.25 12 17.25C12.6904 17.25 13.25 16.6904 13.25 16C13.25 15.3096 12.6904 14.75 12 14.75Z"})))),a=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4.63896 3.42127C4.91195 3.3082 5.20453 3.25 5.5 3.25C5.79547 3.25 6.08805 3.3082 6.36104 3.42127C6.63402 3.53434 6.88206 3.70008 7.09099 3.90901C7.29992 4.11794 7.46566 4.36598 7.57873 4.63896C7.6918 4.91195 7.75 5.20453 7.75 5.5C7.75 5.79547 7.6918 6.08805 7.57873 6.36104C7.46566 6.63402 7.29992 6.88206 7.09099 7.09099C7.00909 7.17289 6.92118 7.24815 6.82811 7.31621L7.72568 10.3074C7.9805 10.2694 8.23925 10.25 8.5 10.25C9.63995 10.25 10.7417 10.6207 11.644 11.2955L16.4697 6.46973C16.3264 6.16973 16.25 5.8388 16.25 5.5C16.25 4.90326 16.4871 4.33097 16.909 3.90901C17.331 3.48705 17.9033 3.25 18.5 3.25C19.0967 3.25 19.669 3.48705 20.091 3.90901C20.5129 4.33097 20.75 4.90326 20.75 5.5C20.75 6.09674 20.5129 6.66903 20.091 7.09099C19.669 7.51295 19.0967 7.75 18.5 7.75C18.1612 7.75 17.8304 7.67361 17.5304 7.53036L12.7046 12.3561C13.3793 13.2584 13.75 14.3601 13.75 15.5C13.75 15.7609 13.7306 16.0198 13.6925 16.2748L16.6837 17.1719C16.7516 17.0792 16.8268 16.9913 16.909 16.909C17.331 16.4871 17.9033 16.25 18.5 16.25C19.0967 16.25 19.669 16.4871 20.091 16.909C20.5129 17.331 20.75 17.9033 20.75 18.5C20.75 19.0967 20.5129 19.669 20.091 20.091C19.669 20.5129 19.0967 20.75 18.5 20.75C17.9033 20.75 17.331 20.5129 16.909 20.091C16.5127 19.6947 16.2795 19.1658 16.2526 18.6086L13.2615 17.7115C13.0048 18.2642 12.6515 18.7731 12.2123 19.2123C11.2277 20.1969 9.89239 20.75 8.5 20.75C7.10761 20.75 5.77226 20.1969 4.78769 19.2123C3.80312 18.2277 3.25 16.8924 3.25 15.5C3.25 14.1076 3.80312 12.7723 4.78769 11.7877C5.22696 11.3484 5.73607 10.995 6.28891 10.7383L5.39142 7.74738C5.13304 7.7349 4.87846 7.67793 4.63896 7.57873C4.36598 7.46566 4.11794 7.29992 3.90901 7.09099C3.70008 6.88206 3.53434 6.63402 3.42127 6.36104C3.3082 6.08805 3.25 5.79547 3.25 5.5C3.25 5.20453 3.3082 4.91195 3.42127 4.63896C3.53434 4.36598 3.70008 4.11794 3.90901 3.90901C4.11794 3.70008 4.36598 3.53434 4.63896 3.42127ZM17.7754 18.3065C17.7587 18.3691 17.75 18.4341 17.75 18.5C17.75 18.6989 17.829 18.8897 17.9697 19.0303C18.1103 19.171 18.3011 19.25 18.5 19.25C18.6989 19.25 18.8897 19.171 19.0303 19.0303C19.171 18.8897 19.25 18.6989 19.25 18.5C19.25 18.3011 19.171 18.1103 19.0303 17.9697C18.8897 17.829 18.6989 17.75 18.5 17.75C18.3011 17.75 18.1103 17.829 17.9697 17.9697C17.8866 18.0527 17.8251 18.1532 17.7886 18.2626C17.7866 18.2699 17.7846 18.2772 17.7824 18.2845C17.7801 18.2919 17.7778 18.2992 17.7754 18.3065ZM5.78701 6.19291C5.77087 6.19959 5.75453 6.2057 5.73801 6.21123C5.73048 6.21324 5.72295 6.21538 5.71543 6.21763C5.70788 6.2199 5.70039 6.22227 5.69297 6.22475C5.63015 6.24148 5.56529 6.25 5.5 6.25C5.40151 6.25 5.30398 6.2306 5.21299 6.19291C5.12199 6.15522 5.03931 6.09997 4.96967 6.03033C4.90003 5.96069 4.84478 5.87801 4.80709 5.78701C4.7694 5.69602 4.75 5.59849 4.75 5.5C4.75 5.40151 4.7694 5.30398 4.80709 5.21299C4.84478 5.12199 4.90003 5.03931 4.96967 4.96967C5.03931 4.90003 5.12199 4.84478 5.21299 4.80709C5.30398 4.7694 5.40151 4.75 5.5 4.75C5.59849 4.75 5.69602 4.7694 5.78701 4.80709C5.87801 4.84478 5.96069 4.90003 6.03033 4.96967C6.09997 5.03931 6.15522 5.12199 6.19291 5.21299C6.2306 5.30398 6.25 5.40151 6.25 5.5C6.25 5.59849 6.2306 5.69602 6.19291 5.78701C6.15522 5.87801 6.09997 5.96069 6.03033 6.03033C5.96069 6.09997 5.87801 6.15522 5.78701 6.19291ZM18.0083 6.06631C17.9968 6.05315 17.9848 6.0403 17.9723 6.02778C17.9598 6.01524 17.9469 6.00324 17.9337 5.99178C17.8156 5.8558 17.75 5.68128 17.75 5.5C17.75 5.30109 17.829 5.11032 17.9697 4.96967C18.1103 4.82902 18.3011 4.75 18.5 4.75C18.6989 4.75 18.8897 4.82902 19.0303 4.96967C19.171 5.11032 19.25 5.30109 19.25 5.5C19.25 5.69891 19.171 5.88968 19.0303 6.03033C18.8897 6.17098 18.6989 6.25 18.5 6.25C18.3187 6.25 18.1442 6.18438 18.0083 6.06631ZM8.5 11.75C7.50544 11.75 6.55161 12.1451 5.84835 12.8484C5.14509 13.5516 4.75 14.5054 4.75 15.5C4.75 16.4946 5.14509 17.4484 5.84835 18.1516C6.55161 18.8549 7.50544 19.25 8.5 19.25C9.49456 19.25 10.4484 18.8549 11.1516 18.1516C11.8549 17.4484 12.25 16.4946 12.25 15.5C12.25 14.5054 11.8549 13.5516 11.1516 12.8484C10.4484 12.1451 9.49456 11.75 8.5 11.75Z"})))),f=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 3.75C7.44365 3.75 3.75 7.44365 3.75 12C3.75 16.5563 7.44365 20.25 12 20.25C16.5563 20.25 20.25 16.5563 20.25 12C20.25 7.44365 16.5563 3.75 12 3.75ZM2.25 12C2.25 6.61522 6.61522 2.25 12 2.25C17.3848 2.25 21.75 6.61522 21.75 12C21.75 17.3848 17.3848 21.75 12 21.75C6.61522 21.75 2.25 17.3848 2.25 12ZM12 7.25C12.4142 7.25 12.75 7.58579 12.75 8V12C12.75 12.4142 12.4142 12.75 12 12.75C11.5858 12.75 11.25 12.4142 11.25 12V8C11.25 7.58579 11.5858 7.25 12 7.25ZM11.25 16C11.25 15.5858 11.5858 15.25 12 15.25H12.01C12.4242 15.25 12.76 15.5858 12.76 16C12.76 16.4142 12.4242 16.75 12.01 16.75H12C11.5858 16.75 11.25 16.4142 11.25 16Z"})))),L=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M8.7 2.25C8.46249 2.25 8.23103 2.29047 8.0079 2.38964C7.78802 2.48736 7.61395 2.62539 7.46967 2.76967L2.76967 7.46967C2.62539 7.61395 2.48736 7.78802 2.38964 8.0079C2.29047 8.23103 2.25 8.46249 2.25 8.7V15.3C2.25 15.5375 2.29047 15.769 2.38964 15.9921C2.48736 16.212 2.62539 16.3861 2.76967 16.5303L7.46967 21.2303C7.61395 21.3746 7.78802 21.5126 8.0079 21.6104C8.23103 21.7095 8.46249 21.75 8.7 21.75H15.3C15.5375 21.75 15.769 21.7095 15.9921 21.6104C16.212 21.5126 16.3861 21.3746 16.5303 21.2303L21.2303 16.5303C21.3746 16.3861 21.5126 16.212 21.6104 15.9921C21.7095 15.769 21.75 15.5375 21.75 15.3V8.7C21.75 8.46249 21.7095 8.23103 21.6104 8.0079C21.5126 7.78802 21.3746 7.61395 21.2303 7.46967L16.5303 2.76967C16.3861 2.62539 16.212 2.48736 15.9921 2.38964C15.769 2.29047 15.5375 2.25 15.3 2.25H8.7ZM12.75 8C12.75 7.58579 12.4142 7.25 12 7.25C11.5858 7.25 11.25 7.58579 11.25 8V12C11.25 12.4142 11.5858 12.75 12 12.75C12.4142 12.75 12.75 12.4142 12.75 12V8ZM12 15.25C11.5858 15.25 11.25 15.5858 11.25 16C11.25 16.4142 11.5858 16.75 12 16.75H12.01C12.4242 16.75 12.76 16.4142 12.76 16C12.76 15.5858 12.4242 15.25 12.01 15.25H12Z"})))),i=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M11.9932 3C11.5018 3 11.0194 3.13134 10.596 3.38038C10.175 3.62805 9.82781 3.98314 9.59 4.40906L2.4909 16.6309C2.47341 16.661 2.45804 16.6923 2.44491 16.7246C2.27977 17.1303 2.21428 17.5695 2.25392 18.0056C2.29356 18.4416 2.43717 18.8619 2.67276 19.2313C2.90835 19.6008 3.22909 19.9086 3.6082 20.1291C3.98731 20.3496 4.41379 20.4764 4.85202 20.499C4.88374 20.5006 4.9151 20.5003 4.94598 20.498C4.96405 20.4993 4.98229 20.5 5.00069 20.5H19.0057L19.011 20.5C19.4598 20.4968 19.9011 20.3841 20.2962 20.1718C20.6914 19.9594 21.0285 19.6537 21.2781 19.2815C21.5277 18.9093 21.6822 18.4818 21.7282 18.0362C21.7742 17.5907 21.7102 17.1408 21.5419 16.7256C21.5287 16.693 21.5132 16.6613 21.4955 16.6309L14.3964 4.40904C14.1586 3.98312 13.8114 3.62805 13.3904 3.38038C12.9671 3.13134 12.4846 3 11.9932 3ZM12.7538 8.76945C12.7538 8.35599 12.4179 8.02081 12.0035 8.02081C11.5891 8.02081 11.2532 8.35599 11.2532 8.76945V12.7658C11.2532 13.1793 11.5891 13.5145 12.0035 13.5145C12.4179 13.5145 12.7538 13.1793 12.7538 12.7658V8.76945ZM12.7538 15.7586C12.7538 15.3451 12.4179 15.0099 12.0035 15.0099C11.5891 15.0099 11.2532 15.3451 11.2532 15.7586V15.7686C11.2532 16.182 11.5891 16.5172 12.0035 16.5172C12.4179 16.5172 12.7538 16.182 12.7538 15.7686V15.7586Z"})))),u=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10.5963 3.38038C11.0196 3.13134 11.502 3 11.9934 3C12.4849 3 12.9673 3.13134 13.3906 3.38038C13.8116 3.62805 14.1588 3.98312 14.3966 4.40904L21.4957 16.6309C21.5134 16.6613 21.5289 16.693 21.5421 16.7256C21.7105 17.1408 21.7744 17.5907 21.7284 18.0362C21.6825 18.4818 21.5279 18.9093 21.2783 19.2815C21.0287 19.6537 20.6916 19.9594 20.2965 20.1718C19.9013 20.3841 19.4601 20.4968 19.0112 20.5L19.006 20.5H5.00094C4.98254 20.5 4.96429 20.4993 4.94623 20.498C4.91535 20.5003 4.88399 20.5006 4.85227 20.499C4.41403 20.4764 3.98755 20.3496 3.60844 20.1291C3.22934 19.9086 2.9086 19.6008 2.67301 19.2313C2.43741 18.8619 2.2938 18.4416 2.25417 18.0056C2.21453 17.5695 2.28001 17.1303 2.44515 16.7246C2.45828 16.6923 2.47365 16.661 2.49115 16.6309L9.59024 4.40906C9.82805 3.98314 10.1753 3.62805 10.5963 3.38038ZM4.94571 19.0047C4.96395 19.0034 4.98236 19.0027 5.00094 19.0027H19.003C19.2062 19.0009 19.406 18.9497 19.5849 18.8535C19.7645 18.757 19.9178 18.6181 20.0312 18.4489C20.1447 18.2797 20.2149 18.0854 20.2358 17.8828C20.2551 17.6958 20.2318 17.5071 20.1678 17.3307L13.095 5.15393L13.0876 5.14096C12.9795 4.94594 12.8209 4.78338 12.6285 4.67018C12.4361 4.55698 12.2168 4.49728 11.9934 4.49728C11.7701 4.49728 11.5508 4.55698 11.3584 4.67018C11.166 4.78338 11.0074 4.94594 10.8993 5.14096L10.8919 5.15393L3.81876 17.3312C3.756 17.5034 3.73195 17.6875 3.74857 17.8703C3.76659 18.0685 3.83187 18.2596 3.93895 18.4275C4.04604 18.5954 4.19183 18.7353 4.36415 18.8356C4.53647 18.9358 4.73032 18.9934 4.92953 19.0037C4.93494 19.004 4.94033 19.0043 4.94571 19.0047ZM12.0035 8.02081C12.4179 8.02081 12.7538 8.35599 12.7538 8.76945V12.7658C12.7538 13.1793 12.4179 13.5145 12.0035 13.5145C11.5891 13.5145 11.2532 13.1793 11.2532 12.7658V8.76945C11.2532 8.35599 11.5891 8.02081 12.0035 8.02081ZM12.0035 15.0099C12.4179 15.0099 12.7538 15.3451 12.7538 15.7586V15.7686C12.7538 16.182 12.4179 16.5172 12.0035 16.5172C11.5891 16.5172 11.2532 16.182 11.2532 15.7686V15.7586C11.2532 15.3451 11.5891 15.0099 12.0035 15.0099Z"})))),V=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3.25 6C3.25 5.58579 3.58579 5.25 4 5.25H20C20.4142 5.25 20.75 5.58579 20.75 6C20.75 6.41421 20.4142 6.75 20 6.75H4C3.58579 6.75 3.25 6.41421 3.25 6ZM7.25 12C7.25 11.5858 7.58579 11.25 8 11.25H16C16.4142 11.25 16.75 11.5858 16.75 12C16.75 12.4142 16.4142 12.75 16 12.75H8C7.58579 12.75 7.25 12.4142 7.25 12ZM5.25 18C5.25 17.5858 5.58579 17.25 6 17.25H18C18.4142 17.25 18.75 17.5858 18.75 18C18.75 18.4142 18.4142 18.75 18 18.75H6C5.58579 18.75 5.25 18.4142 5.25 18Z"})))),H=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3.25 6C3.25 5.58579 3.58579 5.25 4 5.25H20C20.4142 5.25 20.75 5.58579 20.75 6C20.75 6.41421 20.4142 6.75 20 6.75H4C3.58579 6.75 3.25 6.41421 3.25 6ZM3.25 12C3.25 11.5858 3.58579 11.25 4 11.25H20C20.4142 11.25 20.75 11.5858 20.75 12C20.75 12.4142 20.4142 12.75 20 12.75H4C3.58579 12.75 3.25 12.4142 3.25 12ZM3.25 18C3.25 17.5858 3.58579 17.25 4 17.25H20C20.4142 17.25 20.75 17.5858 20.75 18C20.75 18.4142 20.4142 18.75 20 18.75H4C3.58579 18.75 3.25 18.4142 3.25 18Z"})))),M=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3.25 6C3.25 5.58579 3.58579 5.25 4 5.25H20C20.4142 5.25 20.75 5.58579 20.75 6C20.75 6.41421 20.4142 6.75 20 6.75H4C3.58579 6.75 3.25 6.41421 3.25 6ZM3.25 12C3.25 11.5858 3.58579 11.25 4 11.25H14C14.4142 11.25 14.75 11.5858 14.75 12C14.75 12.4142 14.4142 12.75 14 12.75H4C3.58579 12.75 3.25 12.4142 3.25 12ZM3.25 18C3.25 17.5858 3.58579 17.25 4 17.25H18C18.4142 17.25 18.75 17.5858 18.75 18C18.75 18.4142 18.4142 18.75 18 18.75H4C3.58579 18.75 3.25 18.4142 3.25 18Z"})))),Z=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3.25 6C3.25 5.58579 3.58579 5.25 4 5.25H20C20.4142 5.25 20.75 5.58579 20.75 6C20.75 6.41421 20.4142 6.75 20 6.75H4C3.58579 6.75 3.25 6.41421 3.25 6ZM9.25 12C9.25 11.5858 9.58579 11.25 10 11.25H20C20.4142 11.25 20.75 11.5858 20.75 12C20.75 12.4142 20.4142 12.75 20 12.75H10C9.58579 12.75 9.25 12.4142 9.25 12ZM5.25 18C5.25 17.5858 5.58579 17.25 6 17.25H20C20.4142 17.25 20.75 17.5858 20.75 18C20.75 18.4142 20.4142 18.75 20 18.75H6C5.58579 18.75 5.25 18.4142 5.25 18Z"})))),R=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3.76256 3.76256C4.09075 3.43438 4.53587 3.25 5 3.25H9C9.46413 3.25 9.90925 3.43438 10.2374 3.76256C10.5656 4.09075 10.75 4.53587 10.75 5V9C10.75 9.46413 10.5656 9.90925 10.2374 10.2374C9.90925 10.5656 9.46413 10.75 9 10.75H5C4.53587 10.75 4.09075 10.5656 3.76256 10.2374C3.43438 9.90925 3.25 9.46413 3.25 9V5C3.25 4.53587 3.43438 4.09075 3.76256 3.76256ZM5 4.75C4.9337 4.75 4.87011 4.77634 4.82322 4.82322C4.77634 4.87011 4.75 4.9337 4.75 5V9C4.75 9.0663 4.77634 9.12989 4.82322 9.17678C4.87011 9.22366 4.9337 9.25 5 9.25H9C9.0663 9.25 9.12989 9.22366 9.17678 9.17678C9.22366 9.12989 9.25 9.0663 9.25 9V5C9.25 4.9337 9.22366 4.87011 9.17678 4.82322C9.12989 4.77634 9.0663 4.75 9 4.75H5Z"}),t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3.76256 13.7626C4.09075 13.4344 4.53587 13.25 5 13.25H9C9.46413 13.25 9.90925 13.4344 10.2374 13.7626C10.5656 14.0908 10.75 14.5359 10.75 15V19C10.75 19.4641 10.5656 19.9092 10.2374 20.2374C9.90925 20.5656 9.46413 20.75 9 20.75H5C4.53587 20.75 4.09075 20.5656 3.76256 20.2374C3.43437 19.9092 3.25 19.4641 3.25 19V15C3.25 14.5359 3.43438 14.0908 3.76256 13.7626ZM5 14.75C4.9337 14.75 4.87011 14.7763 4.82322 14.8232C4.77634 14.8701 4.75 14.9337 4.75 15V19C4.75 19.0663 4.77634 19.1299 4.82322 19.1768C4.87011 19.2237 4.93369 19.25 5 19.25H9C9.06631 19.25 9.12989 19.2237 9.17678 19.1768C9.22366 19.1299 9.25 19.0663 9.25 19V15C9.25 14.9337 9.22366 14.8701 9.17678 14.8232C9.12989 14.7763 9.0663 14.75 9 14.75H5Z"}),t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M15 13.25C14.5359 13.25 14.0908 13.4344 13.7626 13.7626C13.4344 14.0908 13.25 14.5359 13.25 15V19C13.25 19.4641 13.4344 19.9092 13.7626 20.2374C14.0908 20.5656 14.5359 20.75 15 20.75H19C19.4641 20.75 19.9092 20.5656 20.2374 20.2374C20.5656 19.9092 20.75 19.4641 20.75 19V15C20.75 14.5359 20.5656 14.0908 20.2374 13.7626C19.9092 13.4344 19.4641 13.25 19 13.25H15ZM14.8232 14.8232C14.8701 14.7763 14.9337 14.75 15 14.75H19C19.0663 14.75 19.1299 14.7763 19.1768 14.8232C19.2237 14.8701 19.25 14.9337 19.25 15V19C19.25 19.0663 19.2237 19.1299 19.1768 19.1768C19.1299 19.2237 19.0663 19.25 19 19.25H15C14.9337 19.25 14.8701 19.2237 14.8232 19.1768C14.7763 19.1299 14.75 19.0663 14.75 19V15C14.75 14.9337 14.7763 14.8701 14.8232 14.8232Z"}),t.createElement("path",{d:"M13.25 7C13.25 6.58579 13.5858 6.25 14 6.25H16.25V4C16.25 3.58579 16.5858 3.25 17 3.25C17.4142 3.25 17.75 3.58579 17.75 4V6.25H20C20.4142 6.25 20.75 6.58579 20.75 7C20.75 7.41421 20.4142 7.75 20 7.75H17.75V10C17.75 10.4142 17.4142 10.75 17 10.75C16.5858 10.75 16.25 10.4142 16.25 10V7.75H14C13.5858 7.75 13.25 7.41421 13.25 7Z"})))),v=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3.25 4.5C3.25 4.08579 3.58579 3.75 4 3.75H10C10.4142 3.75 10.75 4.08579 10.75 4.5V12C10.75 12.4142 10.4142 12.75 10 12.75H4C3.58579 12.75 3.25 12.4142 3.25 12V4.5ZM4.75 5.25V11.25H9.25V5.25H4.75ZM13.25 4.5C13.25 4.08579 13.5858 3.75 14 3.75H20C20.4142 3.75 20.75 4.08579 20.75 4.5V12C20.75 12.4142 20.4142 12.75 20 12.75H14C13.5858 12.75 13.25 12.4142 13.25 12V4.5ZM14.75 5.25V11.25H19.25V5.25H14.75ZM3.25 16C3.25 15.5858 3.58579 15.25 4 15.25H10C10.4142 15.25 10.75 15.5858 10.75 16C10.75 16.4142 10.4142 16.75 10 16.75H4C3.58579 16.75 3.25 16.4142 3.25 16ZM13.25 16C13.25 15.5858 13.5858 15.25 14 15.25H20C20.4142 15.25 20.75 15.5858 20.75 16C20.75 16.4142 20.4142 16.75 20 16.75H14C13.5858 16.75 13.25 16.4142 13.25 16ZM3.25 20C3.25 19.5858 3.58579 19.25 4 19.25H10C10.4142 19.25 10.75 19.5858 10.75 20C10.75 20.4142 10.4142 20.75 10 20.75H4C3.58579 20.75 3.25 20.4142 3.25 20ZM13.25 20C13.25 19.5858 13.5858 19.25 14 19.25H20C20.4142 19.25 20.75 19.5858 20.75 20C20.75 20.4142 20.4142 20.75 20 20.75H14C13.5858 20.75 13.25 20.4142 13.25 20Z"})))),p=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M14 5.75C14 5.33579 14.3358 5 14.75 5H15.75C17.0098 5 18.218 5.50044 19.1088 6.39124C19.9996 7.28204 20.5 8.49022 20.5 9.75C20.5 11.0098 19.9996 12.218 19.1088 13.1088C18.218 13.9996 17.0098 14.5 15.75 14.5H6.56066L9.28033 17.2197C9.57322 17.5126 9.57322 17.9874 9.28033 18.2803C8.98744 18.5732 8.51256 18.5732 8.21967 18.2803L4.21967 14.2803C3.92678 13.9874 3.92678 13.5126 4.21967 13.2197L8.21967 9.21967C8.51256 8.92678 8.98744 8.92678 9.28033 9.21967C9.57322 9.51256 9.57322 9.98744 9.28033 10.2803L6.56066 13H15.75C16.612 13 17.4386 12.6576 18.0481 12.0481C18.6576 11.4386 19 10.612 19 9.75C19 8.88805 18.6576 8.0614 18.0481 7.4519C17.4386 6.84241 16.612 6.5 15.75 6.5H14.75C14.3358 6.5 14 6.16421 14 5.75Z"})))),m=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{d:"M12.5 3.75C12.5 3.33579 12.1642 3 11.75 3C11.3358 3 11 3.33579 11 3.75V19.75C11 20.1642 11.3358 20.5 11.75 20.5C12.1642 20.5 12.5 20.1642 12.5 19.75V3.75Z"}),t.createElement("path",{d:"M5.28033 8.21967C5.57322 8.51256 5.57322 8.98744 5.28033 9.28033L3.56066 11H7.75C8.16421 11 8.5 11.3358 8.5 11.75C8.5 12.1642 8.16421 12.5 7.75 12.5H3.56066L5.28033 14.2197C5.57322 14.5126 5.57322 14.9874 5.28033 15.2803C4.98744 15.5732 4.51256 15.5732 4.21967 15.2803L1.21967 12.2803C1.14776 12.2084 1.09351 12.1255 1.05691 12.0371C1.02024 11.9487 1 11.8517 1 11.75C1 11.5581 1.07322 11.3661 1.21967 11.2197L4.21967 8.21967C4.51256 7.92678 4.98744 7.92678 5.28033 8.21967Z"}),t.createElement("path",{d:"M15.75 11C15.3358 11 15 11.3358 15 11.75C15 12.1642 15.3358 12.5 15.75 12.5H19.9393L18.2197 14.2197C17.9268 14.5126 17.9268 14.9874 18.2197 15.2803C18.5126 15.5732 18.9874 15.5732 19.2803 15.2803L22.2803 12.2803C22.3522 12.2084 22.4065 12.1255 22.4431 12.0371C22.4798 11.9487 22.5 11.8517 22.5 11.75C22.5 11.7371 22.4997 11.7242 22.499 11.7114C22.4946 11.6238 22.4751 11.5401 22.4431 11.4629C22.4065 11.3745 22.3522 11.2916 22.2803 11.2197L19.2803 8.21967C18.9874 7.92678 18.5126 7.92678 18.2197 8.21967C17.9268 8.51256 17.9268 8.98744 18.2197 9.28033L19.9393 11H15.75Z"})))),E=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7.46967 14.4697C7.76256 14.1768 8.23744 14.1768 8.53033 14.4697L11.25 17.1893L11.25 5C11.25 4.58579 11.5858 4.25 12 4.25C12.4142 4.25 12.75 4.58579 12.75 5L12.75 17.1893L15.4697 14.4697C15.7626 14.1768 16.2374 14.1768 16.5303 14.4697C16.8232 14.7626 16.8232 15.2374 16.5303 15.5303L12.5303 19.5303C12.2374 19.8232 11.7626 19.8232 11.4697 19.5303L7.46967 15.5303C7.17678 15.2374 7.17678 14.7626 7.46967 14.4697Z"})))),w=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M8.75 6.5C7.88805 6.5 7.0614 6.84241 6.4519 7.4519C5.84241 8.0614 5.5 8.88805 5.5 9.75C5.5 10.612 5.84241 11.4386 6.4519 12.0481C7.0614 12.6576 7.88805 13 8.75 13H17.9393L15.2197 10.2803C14.9268 9.98744 14.9268 9.51256 15.2197 9.21967C15.5126 8.92678 15.9874 8.92678 16.2803 9.21967L20.2803 13.2197C20.5732 13.5126 20.5732 13.9874 20.2803 14.2803L16.2803 18.2803C15.9874 18.5732 15.5126 18.5732 15.2197 18.2803C14.9268 17.9874 14.9268 17.5126 15.2197 17.2197L17.9393 14.5H8.75C7.49022 14.5 6.28204 13.9996 5.39124 13.1088C4.50045 12.218 4 11.0098 4 9.75C4 8.49022 4.50045 7.28204 5.39124 6.39124C6.28204 5.50044 7.49022 5 8.75 5H9.75C10.1642 5 10.5 5.33579 10.5 5.75C10.5 6.16421 10.1642 6.5 9.75 6.5H8.75Z"})))),h=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M9.53033 7.46967C9.82322 7.76256 9.82322 8.23744 9.53033 8.53033L6.81066 11.25H19C19.4142 11.25 19.75 11.5858 19.75 12C19.75 12.4142 19.4142 12.75 19 12.75H6.81066L9.53033 15.4697C9.82322 15.7626 9.82322 16.2374 9.53033 16.5303C9.23744 16.8232 8.76256 16.8232 8.46967 16.5303L4.46967 12.5303C4.17678 12.2374 4.17678 11.7626 4.46967 11.4697L8.46967 7.46967C8.76256 7.17678 9.23744 7.17678 9.53033 7.46967Z"})))),B=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M14.4697 16.5303C14.1768 16.2374 14.1768 15.7626 14.4697 15.4697L17.1893 12.75L5 12.75C4.58579 12.75 4.25 12.4142 4.25 12C4.25 11.5858 4.58579 11.25 5 11.25L17.1893 11.25L14.4697 8.53033C14.1768 8.23744 14.1768 7.76256 14.4697 7.46967C14.7626 7.17678 15.2374 7.17678 15.5303 7.46967L19.5303 11.4697C19.8232 11.7626 19.8232 12.2374 19.5303 12.5303L15.5303 16.5303C15.2374 16.8232 14.7626 16.8232 14.4697 16.5303Z"})))),x=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e,sx:{stroke:"currentColor",...C.sx}},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7.25 7C7.25 6.58579 7.58579 6.25 8 6.25H17C17.4142 6.25 17.75 6.58579 17.75 7V16C17.75 16.4142 17.4142 16.75 17 16.75C16.5858 16.75 16.25 16.4142 16.25 16V8.81066L7.53033 17.5303C7.23744 17.8232 6.76256 17.8232 6.46967 17.5303C6.17678 17.2374 6.17678 16.7626 6.46967 16.4697L15.1893 7.75H8C7.58579 7.75 7.25 7.41421 7.25 7Z"})))),I=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M16.5303 9.53033C16.2374 9.82322 15.7626 9.82322 15.4697 9.53033L12.75 6.81066L12.75 19C12.75 19.4142 12.4142 19.75 12 19.75C11.5858 19.75 11.25 19.4142 11.25 19L11.25 6.81066L8.53033 9.53033C8.23744 9.82322 7.76256 9.82322 7.46967 9.53033C7.17678 9.23744 7.17678 8.76256 7.46967 8.46967L11.4697 4.46967C11.7626 4.17678 12.2374 4.17678 12.5303 4.46967L16.5303 8.46967C16.8232 8.76256 16.8232 9.23744 16.5303 9.53033Z"})))),s=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{d:"M6.53033 3.46967C6.82322 3.76256 6.82322 4.23744 6.53033 4.53033L4.81066 6.25H21C21.4142 6.25 21.75 6.58579 21.75 7C21.75 7.41421 21.4142 7.75 21 7.75H4.81066L6.53033 9.46967C6.82322 9.76256 6.82322 10.2374 6.53033 10.5303C6.23744 10.8232 5.76256 10.8232 5.46967 10.5303L2.46967 7.53033C2.17678 7.23744 2.17678 6.76256 2.46967 6.46967L5.46967 3.46967C5.76256 3.17678 6.23744 3.17678 6.53033 3.46967Z"}),t.createElement("path",{d:"M17.4697 13.4697C17.7626 13.1768 18.2374 13.1768 18.5303 13.4697L21.5303 16.4697C21.8232 16.7626 21.8232 17.2374 21.5303 17.5303L18.5303 20.5303C18.2374 20.8232 17.7626 20.8232 17.4697 20.5303C17.1768 20.2374 17.1768 19.7626 17.4697 19.4697L19.1893 17.75H3C2.58579 17.75 2.25 17.4142 2.25 17C2.25 16.5858 2.58579 16.25 3 16.25H19.1893L17.4697 14.5303C17.1768 14.2374 17.1768 13.7626 17.4697 13.4697Z"})))),g=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M16 3.25C15.5858 3.25 15.25 3.58579 15.25 4C15.25 4.41421 15.5858 4.75 16 4.75H18.1893L13.4697 9.46967C13.1768 9.76256 13.1768 10.2374 13.4697 10.5303C13.7626 10.8232 14.2374 10.8232 14.5303 10.5303L19.25 5.81066V8C19.25 8.41421 19.5858 8.75 20 8.75C20.4142 8.75 20.75 8.41421 20.75 8V4C20.75 3.80806 20.6768 3.61612 20.5303 3.46967C20.4584 3.39776 20.3755 3.34351 20.2871 3.30691C20.1987 3.27024 20.1017 3.25 20 3.25H16ZM4 15.25C4.41421 15.25 4.75 15.5858 4.75 16V18.1893L9.46967 13.4697C9.76256 13.1768 10.2374 13.1768 10.5303 13.4697C10.8232 13.7626 10.8232 14.2374 10.5303 14.5303L5.81066 19.25H8C8.41421 19.25 8.75 19.5858 8.75 20C8.75 20.4142 8.41421 20.75 8 20.75H4C3.80806 20.75 3.61612 20.6768 3.46967 20.5303C3.39776 20.4584 3.34351 20.3755 3.30691 20.2871C3.27024 20.1987 3.25 20.1017 3.25 20V16C3.25 15.5858 3.58579 15.25 4 15.25ZM20.75 16V20C20.75 20.1017 20.7298 20.1987 20.6931 20.2871C20.6565 20.3755 20.6022 20.4584 20.5303 20.5303C20.4584 20.6022 20.3755 20.6565 20.2871 20.6931C20.2099 20.7251 20.1262 20.7446 20.0386 20.749C20.0258 20.7497 20.0129 20.75 20 20.75H16C15.5858 20.75 15.25 20.4142 15.25 20C15.25 19.5858 15.5858 19.25 16 19.25H18.1893L13.4697 14.5303C13.1768 14.2374 13.1768 13.7626 13.4697 13.4697C13.7626 13.1768 14.2374 13.1768 14.5303 13.4697L19.25 18.1893V16C19.25 15.5858 19.5858 15.25 20 15.25C20.4142 15.25 20.75 15.5858 20.75 16ZM3.71291 3.30691C3.80134 3.27024 3.89831 3.25 4 3.25H8C8.41421 3.25 8.75 3.58579 8.75 4C8.75 4.41421 8.41421 4.75 8 4.75H5.81066L10.5303 9.46967C10.8232 9.76256 10.8232 10.2374 10.5303 10.5303C10.2374 10.8232 9.76256 10.8232 9.46967 10.5303L4.75 5.81066V8C4.75 8.41421 4.41421 8.75 4 8.75C3.58579 8.75 3.25 8.41421 3.25 8V4C3.25 3.7937 3.33329 3.60686 3.46808 3.47126C3.46861 3.47073 3.46914 3.4702 3.46967 3.46967C3.4702 3.46914 3.47073 3.46861 3.47126 3.46808C3.5428 3.39696 3.62511 3.34324 3.71291 3.30691Z"})))),A=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6.53033 8.46967C6.82322 8.76256 6.82322 9.23744 6.53033 9.53033L4.81066 11.25H9C9.41421 11.25 9.75 11.5858 9.75 12C9.75 12.4142 9.41421 12.75 9 12.75H4.81066L6.53033 14.4697C6.82322 14.7626 6.82322 15.2374 6.53033 15.5303C6.23744 15.8232 5.76256 15.8232 5.46967 15.5303L2.46967 12.5303C2.17678 12.2374 2.17678 11.7626 2.46967 11.4697L5.46967 8.46967C5.76256 8.17678 6.23744 8.17678 6.53033 8.46967ZM17.4697 8.46967C17.7626 8.17678 18.2374 8.17678 18.5303 8.46967L21.5303 11.4697C21.8232 11.7626 21.8232 12.2374 21.5303 12.5303L18.5303 15.5303C18.2374 15.8232 17.7626 15.8232 17.4697 15.5303C17.1768 15.2374 17.1768 14.7626 17.4697 14.4697L19.1893 12.75H15C14.5858 12.75 14.25 12.4142 14.25 12C14.25 11.5858 14.5858 11.25 15 11.25H19.1893L17.4697 9.53033C17.1768 9.23744 17.1768 8.76256 17.4697 8.46967Z"})))),S=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M11.4697 2.46967C11.7626 2.17678 12.2374 2.17678 12.5303 2.46967L15.5303 5.46967C15.8232 5.76256 15.8232 6.23744 15.5303 6.53033C15.2374 6.82322 14.7626 6.82322 14.4697 6.53033L12.75 4.81066V9C12.75 9.41421 12.4142 9.75 12 9.75C11.5858 9.75 11.25 9.41421 11.25 9V4.81066L9.53033 6.53033C9.23744 6.82322 8.76256 6.82322 8.46967 6.53033C8.17678 6.23744 8.17678 5.76256 8.46967 5.46967L11.4697 2.46967ZM12 14.25C12.4142 14.25 12.75 14.5858 12.75 15V19.1893L14.4697 17.4697C14.7626 17.1768 15.2374 17.1768 15.5303 17.4697C15.8232 17.7626 15.8232 18.2374 15.5303 18.5303L12.5303 21.5303C12.2374 21.8232 11.7626 21.8232 11.4697 21.5303L8.46967 18.5303C8.17678 18.2374 8.17678 17.7626 8.46967 17.4697C8.76256 17.1768 9.23744 17.1768 9.53033 17.4697L11.25 19.1893V15C11.25 14.5858 11.5858 14.25 12 14.25Z"})))),T=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{d:"M17.4697 3.46967C17.7626 3.17678 18.2374 3.17678 18.5303 3.46967L21.5303 6.46967C21.8232 6.76256 21.8232 7.23744 21.5303 7.53033L18.5303 10.5303C18.2374 10.8232 17.7626 10.8232 17.4697 10.5303C17.1768 10.2374 17.1768 9.76256 17.4697 9.46967L19.1893 7.75H3C2.58579 7.75 2.25 7.41421 2.25 7C2.25 6.58579 2.58579 6.25 3 6.25H19.1893L17.4697 4.53033C17.1768 4.23744 17.1768 3.76256 17.4697 3.46967Z"}),t.createElement("path",{d:"M6.53033 13.4697C6.82322 13.7626 6.82322 14.2374 6.53033 14.5303L4.81066 16.25H21C21.4142 16.25 21.75 16.5858 21.75 17C21.75 17.4142 21.4142 17.75 21 17.75H4.81066L6.53033 19.4697C6.82322 19.7626 6.82322 20.2374 6.53033 20.5303C6.23744 20.8232 5.76256 20.8232 5.46967 20.5303L2.46967 17.5303C2.17678 17.2374 2.17678 16.7626 2.46967 16.4697L5.46967 13.4697C5.76256 13.1768 6.23744 13.1768 6.53033 13.4697Z"})))),y=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{d:"M12.75 12C12.75 11.5858 12.4142 11.25 12 11.25C11.5858 11.25 11.25 11.5858 11.25 12V12.01C11.25 12.4242 11.5858 12.76 12 12.76C12.4142 12.76 12.75 12.4242 12.75 12.01V12Z"}),t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M17.3928 3.63405C18.2177 3.61107 19.018 3.8153 19.6014 4.39871C20.1845 4.9822 20.389 5.78211 20.3661 6.60689C20.3431 7.42947 20.0956 8.3483 19.6895 9.29571C19.3209 10.156 18.8085 11.0701 18.1722 11.9989C18.8085 12.9276 19.3208 13.8419 19.6896 14.7024C20.0957 15.6504 20.3434 16.5697 20.3664 17.3926C20.3896 18.2175 20.1856 19.0177 19.6025 19.6012C19.019 20.1843 18.2189 20.389 17.394 20.3661C16.5714 20.3431 15.6524 20.0956 14.7049 19.6896C13.8445 19.3209 12.9302 18.8085 12.0013 18.1721C11.0726 18.8085 10.1583 19.3208 9.29767 19.6896C8.34974 20.0957 7.43037 20.3434 6.60749 20.3664C5.78261 20.3896 4.98237 20.1856 4.39889 19.6025C3.81576 19.019 3.61108 18.2189 3.63405 17.394C3.65695 16.5714 3.90454 15.6524 4.31055 14.7049C4.67923 13.8445 5.19159 12.9302 5.828 12.0013C5.19161 11.0726 4.6793 10.1582 4.31063 9.29763C3.90456 8.34969 3.65697 7.43028 3.63405 6.60734C3.61107 5.78236 3.8153 4.98213 4.39871 4.39872C4.9822 3.81561 5.78211 3.61107 6.60689 3.63405C7.42947 3.65696 8.3483 3.90456 9.29571 4.31056C10.156 4.67923 11.0701 5.19157 11.9989 5.82795C12.9276 5.19159 13.8419 4.67929 14.7025 4.31063C15.6504 3.90456 16.5698 3.65697 17.3928 3.63405ZM18.3108 8.70487C18.0395 9.33787 17.674 10.0166 17.2222 10.7184C16.6628 10.0182 16.0386 9.31993 15.358 8.64031C14.6779 7.96032 13.9797 7.33671 13.2796 6.77773C13.9811 6.32616 14.6599 5.96069 15.2931 5.68945C16.1374 5.32777 16.8641 5.14936 17.4345 5.13347C18.0027 5.11764 18.3431 5.26186 18.5407 5.45937C18.7385 5.65741 18.8824 5.99763 18.8666 6.56513C18.8508 7.13499 18.6724 7.86103 18.3108 8.70487ZM14.2977 9.70138C13.5391 8.94274 12.7638 8.26585 11.999 7.68041C11.2343 8.26601 10.4595 8.94309 9.70179 9.70196C8.94306 10.4607 8.26604 11.2362 7.68047 12.0012C8.26605 12.7658 8.94312 13.5406 9.70196 14.2983C10.4606 15.0569 11.2363 15.7342 12.0011 16.3197C12.7658 15.7341 13.5406 15.057 14.2983 14.2981C15.0571 13.5394 15.7341 12.7639 16.3197 11.999C15.7341 11.2343 15.0566 10.4591 14.2977 9.70138ZM8.70487 5.68929C9.33791 5.96058 10.0167 6.32615 10.7184 6.77787C10.0183 7.33717 9.32021 7.9612 8.64072 8.64171C7.96058 9.32185 7.33683 10.0203 6.77775 10.7205C6.32617 10.019 5.96069 9.34018 5.68945 8.70698C5.32777 7.86267 5.14936 7.13595 5.13347 6.56557C5.11764 5.9974 5.26169 5.65717 5.4592 5.45955C5.65724 5.26175 5.99763 5.11766 6.56513 5.13346C7.13499 5.14934 7.86103 5.32767 8.70487 5.68929ZM5.68931 15.2957C5.96059 14.6626 6.3262 13.9837 6.77797 13.2818C7.33735 13.982 7.96149 14.6802 8.64212 15.3598C9.32213 16.0398 10.0204 16.6634 10.7205 17.2224C10.019 17.674 9.34016 18.0395 8.70694 18.3108C7.86261 18.6725 7.13586 18.851 6.56542 18.867C5.99716 18.883 5.65693 18.739 5.45938 18.5417C5.2616 18.3437 5.11766 18.0034 5.13346 17.4358C5.14934 16.8658 5.32769 16.1396 5.68931 15.2957ZM15.3598 15.358C14.6802 16.0386 13.982 16.6628 13.2818 17.2222C13.9836 17.6739 14.6625 18.0395 15.2957 18.3108C16.1396 18.6724 16.8658 18.8508 17.4358 18.8666C18.0034 18.8824 18.3437 18.7385 18.5417 18.5407C18.739 18.3432 18.883 18.0029 18.867 17.4347C18.851 16.8642 18.6725 16.1375 18.3108 15.2932C18.0395 14.66 17.674 13.9811 17.2224 13.2796C16.6634 13.9797 16.0398 14.6779 15.3598 15.358Z"})))),P=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M5.66005 6.72071C4.46747 8.15133 3.75 9.99188 3.75 12C3.75 16.5563 7.44365 20.25 12 20.25C14.0081 20.25 15.8487 19.5325 17.2793 18.34L5.66005 6.72071ZM6.72071 5.66005L18.34 17.2793C19.5325 15.8487 20.25 14.0081 20.25 12C20.25 7.44365 16.5563 3.75 12 3.75C9.99188 3.75 8.15133 4.46747 6.72071 5.66005ZM2.25 12C2.25 6.61522 6.61522 2.25 12 2.25C17.3848 2.25 21.75 6.61522 21.75 12C21.75 17.3848 17.3848 21.75 12 21.75C6.61522 21.75 2.25 17.3848 2.25 12Z"})))),k=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M13.2319 2.28681C13.5409 2.38727 13.75 2.6752 13.75 3.00005V9.25005H19C19.2821 9.25005 19.5403 9.40834 19.6683 9.65972C19.7963 9.9111 19.7725 10.213 19.6066 10.4412L11.6066 21.4412C11.4155 21.7039 11.077 21.8137 10.7681 21.7133C10.4591 21.6128 10.25 21.3249 10.25 21.0001V14.7501H5C4.71791 14.7501 4.45967 14.5918 4.33167 14.3404C4.20366 14.089 4.22753 13.7871 4.39345 13.5589L12.3935 2.55892C12.5845 2.2962 12.923 2.18635 13.2319 2.28681ZM6.47283 13.2501H11C11.4142 13.2501 11.75 13.5858 11.75 14.0001V18.6937L17.5272 10.7501H13C12.5858 10.7501 12.25 10.4143 12.25 10.0001V5.30644L6.47283 13.2501Z"})))),b=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4.00005 4.00005L3.25005 4.00004L3.25 8C3.25 8.41421 3.58579 8.75 4 8.75C4.41421 8.75 4.75 8.41421 4.75 8L4.75003 4.75003L8 4.75C8.41421 4.75 8.75 4.41421 8.75 4C8.75 3.58579 8.41421 3.25 8 3.25L4.00009 3.25005L4.00005 4.00005ZM4.00005 4.00005L4.00009 3.25005C3.5859 3.25006 3.25006 3.58585 3.25005 4.00004L4.00005 4.00005ZM15.25 4C15.25 3.58579 15.5858 3.25 16 3.25L20 3.25005C20.4142 3.25006 20.75 3.58585 20.75 4.00005V8C20.75 8.41421 20.4142 8.75 20 8.75C19.5858 8.75 19.25 8.41421 19.25 8V4.75061L16 4.75C15.5858 4.75 15.25 4.41421 15.25 4ZM4 15.25C4.41421 15.25 4.75 15.5858 4.75 16L4.75002 19.25H8C8.41421 19.25 8.75 19.5858 8.75 20C8.75 20.4142 8.41421 20.75 8 20.75H4.00031C3.58609 20.75 3.25006 20.4142 3.25005 20L3.25 16C3.25 15.5858 3.58579 15.25 4 15.25ZM20 15.25C20.4142 15.25 20.75 15.5858 20.75 16V19.9997C20.75 20.1987 20.671 20.3897 20.5303 20.5303C20.3897 20.671 20.1989 20.75 20 20.75L16 20.75C15.5858 20.75 15.25 20.4142 15.25 20C15.25 19.5858 15.5858 19.25 16 19.25L19.25 19.25V16C19.25 15.5858 19.5858 15.25 20 15.25Z"})))),D=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10.9341 3.93414C12.0125 2.8558 13.475 2.25 15 2.25H18C18.4142 2.25 18.75 2.58579 18.75 3V7C18.75 7.41421 18.4142 7.75 18 7.75H15C14.9337 7.75 14.8701 7.77634 14.8232 7.82322C14.7763 7.87011 14.75 7.9337 14.75 8V9.25H18C18.231 9.25 18.449 9.3564 18.5912 9.53844C18.7333 9.72048 18.7836 9.95785 18.7276 10.1819L17.7276 14.1819C17.6441 14.5158 17.3442 14.75 17 14.75H14.75V21C14.75 21.4142 14.4142 21.75 14 21.75H10C9.58579 21.75 9.25 21.4142 9.25 21V14.75H7C6.58579 14.75 6.25 14.4142 6.25 14V10C6.25 9.58579 6.58579 9.25 7 9.25H9.25V8C9.25 6.47501 9.8558 5.01247 10.9341 3.93414ZM15 3.75C13.8728 3.75 12.7918 4.19777 11.9948 4.9948C11.1978 5.79183 10.75 6.87283 10.75 8V10C10.75 10.4142 10.4142 10.75 10 10.75H7.75V13.25H10C10.4142 13.25 10.75 13.5858 10.75 14V20.25H13.25V14C13.25 13.5858 13.5858 13.25 14 13.25H16.4144L17.0394 10.75H14C13.5858 10.75 13.25 10.4142 13.25 10V8C13.25 7.53587 13.4344 7.09075 13.7626 6.76256C14.0908 6.43437 14.5359 6.25 15 6.25H17.25V3.75H15Z"})))),F=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{d:"M12 11.25C12.4142 11.25 12.75 11.5858 12.75 12V12.01C12.75 12.4242 12.4142 12.76 12 12.76C11.5858 12.76 11.25 12.4242 11.25 12.01V12C11.25 11.5858 11.5858 11.25 12 11.25Z"}),t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7.25 6.25V5C7.25 4.27065 7.53973 3.57118 8.05546 3.05546C8.57118 2.53973 9.27065 2.25 10 2.25H14C14.7293 2.25 15.4288 2.53973 15.9445 3.05546C16.4603 3.57118 16.75 4.27065 16.75 5V6.25H19C19.7293 6.25 20.4288 6.53973 20.9445 7.05546C21.4603 7.57118 21.75 8.27065 21.75 9V12.9819C21.7503 12.9936 21.7503 13.0051 21.75 13.0167V18C21.75 18.7293 21.4603 19.4288 20.9445 19.9445C20.4288 20.4603 19.7293 20.75 19 20.75H5C4.27065 20.75 3.57118 20.4603 3.05546 19.9445C2.53973 19.4288 2.25 18.7293 2.25 18V13.0128C2.24984 13.0038 2.24984 12.9948 2.25 12.9858V9C2.25 8.27065 2.53973 7.57118 3.05546 7.05546C3.57118 6.53973 4.27065 6.25 5 6.25H7.25ZM9.11612 4.11612C9.35054 3.8817 9.66848 3.75 10 3.75H14C14.3315 3.75 14.6495 3.8817 14.8839 4.11612C15.1183 4.35054 15.25 4.66848 15.25 5V6.25H8.75V5C8.75 4.66848 8.8817 4.35054 9.11612 4.11612ZM3.75 14.1788V18C3.75 18.3315 3.8817 18.6495 4.11612 18.8839C4.35054 19.1183 4.66848 19.25 5 19.25H19C19.3315 19.25 19.6495 19.1183 19.8839 18.8839C20.1183 18.6495 20.25 18.3315 20.25 18V14.1788C17.6498 15.3055 14.8423 15.8893 12 15.8893C9.15777 15.8893 6.35018 15.3055 3.75 14.1788ZM20.25 12.5319C17.6734 13.7541 14.8556 14.3893 12 14.3893C9.14445 14.3893 6.32659 13.7541 3.75 12.5319V9C3.75 8.66848 3.8817 8.35054 4.11612 8.11612C4.35054 7.8817 4.66848 7.75 5 7.75H19C19.3315 7.75 19.6495 7.8817 19.8839 8.11612C20.1183 8.35054 20.25 8.66848 20.25 9V12.5319Z"})))),U=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M20.8815 2.25919C20.9203 2.25301 20.9599 2.24983 21 2.24988C21.0827 2.24978 21.1629 2.26335 21.2384 2.2887C21.367 2.33162 21.4784 2.40776 21.5642 2.5058C21.6895 2.64891 21.76 2.83868 21.749 3.03992C21.7477 3.06492 21.7452 3.08966 21.7414 3.11407C21.3311 6.09743 20.124 8.91507 18.2472 11.2703C16.5079 13.4529 14.2532 15.1635 11.6906 16.2509C11.8289 17.1168 11.7249 18.0054 11.3884 18.8177C11.0289 19.6857 10.4201 20.4275 9.63896 20.9495C8.85782 21.4714 7.93946 21.75 7 21.75H3C2.58579 21.75 2.25 21.4142 2.25 21V17C2.25 16.0605 2.52858 15.1421 3.05052 14.361C3.57246 13.5799 4.3143 12.9711 5.18225 12.6115C5.99455 12.2751 6.88314 12.1711 7.74905 12.3094C8.83643 9.74682 10.547 7.49212 12.7296 5.75287C15.0837 3.87693 17.8998 2.67012 20.8815 2.25919ZM10.0984 16.0649C10.077 16.0082 10.0629 15.9506 10.0557 15.893C9.89413 15.4471 9.63624 15.04 9.2981 14.7019C8.96001 14.3638 8.55301 14.1059 8.10721 13.9444C8.04953 13.9372 7.99178 13.9231 7.93499 13.9016C7.90509 13.8903 7.87632 13.8773 7.84877 13.8628C7.77794 13.8436 7.70633 13.8268 7.63404 13.8124C7.0036 13.687 6.35014 13.7514 5.75628 13.9974C5.16242 14.2433 4.65484 14.6599 4.29772 15.1944C3.94061 15.7288 3.75 16.3572 3.75 17V20.25H7C7.64279 20.25 8.27114 20.0594 8.8056 19.7022C9.34006 19.3451 9.75662 18.8376 10.0026 18.2437C10.2486 17.6498 10.313 16.9964 10.1876 16.3659C10.1732 16.2935 10.1563 16.2218 10.1371 16.1509C10.1226 16.1234 10.1097 16.0947 10.0984 16.0649ZM10.3588 13.6412C10.7069 13.9894 10.9969 14.3876 11.2204 14.8204C12.2258 14.3839 13.1782 13.8417 14.0621 13.2048C13.3066 11.827 12.173 10.6933 10.7952 9.93782C10.1583 10.8218 9.61603 11.7741 9.17957 12.7795C9.61238 13.0031 10.0106 13.293 10.3588 13.6412ZM11.7434 8.75107C13.1943 9.59789 14.4021 10.8057 15.2489 12.2565C15.9093 11.6727 16.5204 11.0304 17.0741 10.3355C18.5698 8.45849 19.5983 6.25889 20.0821 3.9179C17.7411 4.40165 15.5415 5.43018 13.6644 6.92595C12.9696 7.47964 12.3273 8.0907 11.7434 8.75107Z"})))),O=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M2.46967 2.46967C2.76256 2.17678 3.23744 2.17678 3.53033 2.46967L21.5303 20.4697C21.8232 20.7626 21.8232 21.2374 21.5303 21.5303C21.2374 21.8232 20.7626 21.8232 20.4697 21.5303L16.92 17.9806C16.4226 18.8029 15.7236 19.4929 14.8816 19.9805C13.7105 20.6587 12.3377 20.9021 11.0048 20.6679C9.67186 20.4336 8.4643 19.7368 7.59458 18.6999C7.4139 18.4845 7.2502 18.2573 7.10425 18.0203L4.38445 19.6441C4.0288 19.8564 3.56836 19.7402 3.35603 19.3846C3.1437 19.0289 3.25988 18.5685 3.61554 18.3561L6.48785 16.6413C6.33078 16.1124 6.24952 15.5597 6.25 14.9997V13.75H3C2.58579 13.75 2.25 13.4142 2.25 13C2.25 12.5858 2.58579 12.25 3 12.25H6.25V12C6.25 11.9862 6.25038 11.9725 6.25114 11.9587C6.29471 11.1684 6.47673 10.3952 6.78577 9.67345L3.59568 7.6318C3.2468 7.40851 3.14498 6.94468 3.36827 6.5958C3.59155 6.24692 4.05538 6.14511 4.40426 6.36839L6.91389 7.97455L2.46967 3.53033C2.17678 3.23744 2.17678 2.76256 2.46967 2.46967ZM8.41875 9.75H8.68934L12.2231 13.2837C12.1526 13.2618 12.0777 13.25 12 13.25C11.5858 13.25 11.25 13.5858 11.25 14V19.188C10.2705 19.0124 9.38352 18.4986 8.74382 17.7359C8.10099 16.9695 7.74907 16.0003 7.75 15V12.0213C7.79715 11.2216 8.02659 10.4449 8.41875 9.75ZM12.75 19.188C13.2322 19.1015 13.6995 18.9317 14.1299 18.6824C14.8612 18.2589 15.4465 17.6289 15.8157 16.8763L12.7163 13.7769C12.7382 13.8474 12.75 13.9223 12.75 14V19.188Z"}),t.createElement("path",{d:"M16.25 12.9983V12.0213C16.2029 11.2216 15.9734 10.4449 15.5813 9.75H13C12.5858 9.75 12.25 9.41421 12.25 9C12.25 8.58579 12.5858 8.25 13 8.25H14.25V7.99989C14.2501 7.55533 14.1185 7.12064 13.8717 6.75083C13.625 6.38102 13.2743 6.09258 12.8638 5.92192C12.4533 5.75126 12.0015 5.70602 11.5653 5.79191C11.1291 5.87781 10.7282 6.09099 10.413 6.40454C10.1194 6.6967 9.64454 6.69551 9.35238 6.40189C9.06022 6.10826 9.06141 5.63339 9.35503 5.34123C9.88025 4.81863 10.5485 4.46334 11.2755 4.32018C12.0024 4.17702 12.7555 4.25242 13.4396 4.53685C14.1238 4.82129 14.7084 5.30202 15.1195 5.91837C15.5307 6.53469 15.7501 7.259 15.75 7.99989V8.25H16C16.1588 8.25 16.311 8.30029 16.4366 8.39019L19.5957 6.36839C19.9446 6.14511 20.4084 6.24692 20.6317 6.5958C20.855 6.94468 20.7531 7.40851 20.4043 7.6318L17.2142 9.67342C17.5233 10.3952 17.7053 11.1684 17.7489 11.9587C17.7496 11.9725 17.75 11.9862 17.75 12V12.25H21C21.4142 12.25 21.75 12.5858 21.75 13C21.75 13.4142 21.4142 13.75 21 13.75H17C16.5858 13.75 16.25 13.4142 16.25 13L16.25 12.9983Z"})))),J=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{d:"M16.5 2.75C16.5 2.33579 16.1642 2 15.75 2C15.3358 2 15 2.33579 15 2.75V4H8.5V2.75C8.5 2.33579 8.16421 2 7.75 2C7.33579 2 7 2.33579 7 2.75V4H5.75C5.02065 4 4.32118 4.28973 3.80546 4.80546C3.28973 5.32118 3 6.02065 3 6.75V18.75C3 19.4793 3.28973 20.1788 3.80546 20.6945C4.32118 21.2103 5.02065 21.5 5.75 21.5H12.75C13.1642 21.5 13.5 21.1642 13.5 20.75C13.5 20.3358 13.1642 20 12.75 20H5.75C5.41848 20 5.10054 19.8683 4.86612 19.6339C4.6317 19.3995 4.5 19.0815 4.5 18.75V11.5H16.25C16.6642 11.5 17 11.1642 17 10.75C17 10.3358 16.6642 10 16.25 10H4.5V6.75C4.5 6.41848 4.6317 6.10054 4.86612 5.86612C5.10054 5.6317 5.41848 5.5 5.75 5.5H7V6.75C7 7.16421 7.33579 7.5 7.75 7.5C8.16421 7.5 8.5 7.16421 8.5 6.75V5.5H15V6.75C15 7.16421 15.3358 7.5 15.75 7.5C16.1642 7.5 16.5 7.16421 16.5 6.75V5.5H17.75C18.0815 5.5 18.3995 5.6317 18.6339 5.86612C18.8683 6.10054 19 6.41848 19 6.75V9.75C19 10.1642 19.3358 10.5 19.75 10.5C20.1642 10.5 20.5 10.1642 20.5 9.75V6.75C20.5 6.02065 20.2103 5.32118 19.6945 4.80546C19.1788 4.28973 18.4793 4 17.75 4H16.5V2.75Z"}),t.createElement("path",{d:"M19.5 14V13.75C19.5 13.3358 19.1642 13 18.75 13C18.3358 13 18 13.3358 18 13.75V14.0139C17.4952 14.0703 17.0216 14.2964 16.659 14.659C16.2371 15.081 16 15.6533 16 16.25C16 16.8467 16.2371 17.419 16.659 17.841C17.081 18.2629 17.6533 18.5 18.25 18.5H19.25C19.4489 18.5 19.6397 18.579 19.7803 18.7197C19.921 18.8603 20 19.0511 20 19.25C20 19.4489 19.921 19.6397 19.7803 19.7803C19.6397 19.921 19.4489 20 19.25 20H16.75C16.3358 20 16 20.3358 16 20.75C16 21.1642 16.3358 21.5 16.75 21.5H18V21.75C18 22.1642 18.3358 22.5 18.75 22.5C19.1642 22.5 19.5 22.1642 19.5 21.75V21.4861C20.0048 21.4297 20.4784 21.2036 20.841 20.841C21.2629 20.419 21.5 19.8467 21.5 19.25C21.5 18.6533 21.2629 18.081 20.841 17.659C20.419 17.2371 19.8467 17 19.25 17H18.25C18.0511 17 17.8603 16.921 17.7197 16.7803C17.579 16.6397 17.5 16.4489 17.5 16.25C17.5 16.0511 17.579 15.8603 17.7197 15.7197C17.8603 15.579 18.0511 15.5 18.25 15.5H20.75C21.1642 15.5 21.5 15.1642 21.5 14.75C21.5 14.3358 21.1642 14 20.75 14H19.5Z"})))),W=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M8 2.25C8.41421 2.25 8.75 2.58579 8.75 3V4.25H15.25V3C15.25 2.58579 15.5858 2.25 16 2.25C16.4142 2.25 16.75 2.58579 16.75 3V4.25H18C19.5188 4.25 20.75 5.48122 20.75 7V19C20.75 20.5188 19.5188 21.75 18 21.75H6C4.48122 21.75 3.25 20.5188 3.25 19V7C3.25 5.48122 4.48122 4.25 6 4.25H7.25V3C7.25 2.58579 7.58579 2.25 8 2.25ZM7.25 5.75H6C5.30964 5.75 4.75 6.30964 4.75 7V10.25H19.25V7C19.25 6.30964 18.6904 5.75 18 5.75H16.75V7C16.75 7.41421 16.4142 7.75 16 7.75C15.5858 7.75 15.25 7.41421 15.25 7V5.75H8.75V7C8.75 7.41421 8.41421 7.75 8 7.75C7.58579 7.75 7.25 7.41421 7.25 7V5.75ZM19.25 11.75H4.75V19C4.75 19.6904 5.30964 20.25 6 20.25H18C18.6904 20.25 19.25 19.6904 19.25 19V11.75Z"})))),j=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 9.25C11.0054 9.25 10.0516 9.64509 9.34835 10.3483C8.64509 11.0516 8.25 12.0054 8.25 13C8.25 13.9946 8.64509 14.9484 9.34835 15.6517C10.0516 16.3549 11.0054 16.75 12 16.75C12.9946 16.75 13.9484 16.3549 14.6517 15.6517C15.3549 14.9484 15.75 13.9946 15.75 13C15.75 12.0054 15.3549 11.0516 14.6517 10.3483C13.9484 9.64509 12.9946 9.25 12 9.25ZM10.409 11.409C10.831 10.9871 11.4033 10.75 12 10.75C12.5967 10.75 13.169 10.9871 13.591 11.409C14.0129 11.831 14.25 12.4033 14.25 13C14.25 13.5967 14.0129 14.169 13.591 14.591C13.169 15.0129 12.5967 15.25 12 15.25C11.4033 15.25 10.831 15.0129 10.409 14.591C9.98705 14.169 9.75 13.5967 9.75 13C9.75 12.4033 9.98705 11.831 10.409 11.409Z"}),t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M9 3.25C8.53587 3.25 8.09075 3.43438 7.76256 3.76256C7.43437 4.09075 7.25 4.53587 7.25 5C7.25 5.33152 7.1183 5.64946 6.88388 5.88388C6.64946 6.1183 6.33152 6.25 6 6.25H5C4.27065 6.25 3.57118 6.53973 3.05546 7.05546C2.53973 7.57118 2.25 8.27065 2.25 9V18C2.25 18.7293 2.53973 19.4288 3.05546 19.9445C3.57118 20.4603 4.27065 20.75 5 20.75H19C19.7293 20.75 20.4288 20.4603 20.9445 19.9445C21.4603 19.4288 21.75 18.7293 21.75 18V9C21.75 8.27065 21.4603 7.57118 20.9445 7.05546C20.4288 6.53973 19.7293 6.25 19 6.25H18C17.6685 6.25 17.3505 6.1183 17.1161 5.88388C16.8817 5.64946 16.75 5.33152 16.75 5C16.75 4.53587 16.5656 4.09075 16.2374 3.76256C15.9092 3.43438 15.4641 3.25 15 3.25H9ZM8.82322 4.82322C8.87011 4.77634 8.9337 4.75 9 4.75H15C15.0663 4.75 15.1299 4.77634 15.1768 4.82322C15.2237 4.87011 15.25 4.9337 15.25 5C15.25 5.72935 15.5397 6.42882 16.0555 6.94454C16.5712 7.46027 17.2707 7.75 18 7.75H19C19.3315 7.75 19.6495 7.8817 19.8839 8.11612C20.1183 8.35054 20.25 8.66848 20.25 9V18C20.25 18.3315 20.1183 18.6495 19.8839 18.8839C19.6495 19.1183 19.3315 19.25 19 19.25H5C4.66848 19.25 4.35054 19.1183 4.11612 18.8839C3.8817 18.6495 3.75 18.3315 3.75 18V9C3.75 8.66848 3.8817 8.35054 4.11612 8.11612C4.35054 7.8817 4.66848 7.75 5 7.75H6C6.72935 7.75 7.42882 7.46027 7.94454 6.94454C8.46027 6.42882 8.75 5.72935 8.75 5C8.75 4.9337 8.77634 4.87011 8.82322 4.82322Z"})))),z=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3.25 3C3.25 2.58579 3.58579 2.25 4 2.25H6C6.41421 2.25 6.75 2.58579 6.75 3V4.30169L20.0535 5.25194C20.261 5.26676 20.4531 5.36717 20.5838 5.52914C20.7144 5.69111 20.7719 5.90009 20.7425 6.1061L19.7425 13.1061C19.6897 13.4756 19.3733 13.75 19 13.75H6.75V16.25H17C17.7293 16.25 18.4288 16.5397 18.9445 17.0555C19.4603 17.5712 19.75 18.2707 19.75 19C19.75 19.7293 19.4603 20.4288 18.9445 20.9445C18.4288 21.4603 17.7293 21.75 17 21.75C16.2707 21.75 15.5712 21.4603 15.0555 20.9445C14.5397 20.4288 14.25 19.7293 14.25 19C14.25 18.5614 14.3548 18.1335 14.5505 17.75H8.44949C8.64521 18.1335 8.75 18.5614 8.75 19C8.75 19.7293 8.46027 20.4288 7.94454 20.9445C7.42882 21.4603 6.72935 21.75 6 21.75C5.27065 21.75 4.57118 21.4603 4.05546 20.9445C3.53973 20.4288 3.25 19.7293 3.25 19C3.25 18.2707 3.53973 17.5712 4.05546 17.0555C4.39024 16.7207 4.80245 16.4811 5.25 16.3542V3.75H4C3.58579 3.75 3.25 3.41421 3.25 3ZM6.75 5.80551V12.25H18.3496L19.1437 6.69078L6.75 5.80551ZM16.1161 18.1161C16.3505 17.8817 16.6685 17.75 17 17.75C17.3315 17.75 17.6495 17.8817 17.8839 18.1161C18.1183 18.3505 18.25 18.6685 18.25 19C18.25 19.3315 18.1183 19.6495 17.8839 19.8839C17.6495 20.1183 17.3315 20.25 17 20.25C16.6685 20.25 16.3505 20.1183 16.1161 19.8839C15.8817 19.6495 15.75 19.3315 15.75 19C15.75 18.6685 15.8817 18.3505 16.1161 18.1161ZM5.11612 18.1161C5.35054 17.8817 5.66848 17.75 6 17.75C6.33152 17.75 6.64946 17.8817 6.88388 18.1161C7.1183 18.3505 7.25 18.6685 7.25 19C7.25 19.3315 7.1183 19.6495 6.88388 19.8839C6.64946 20.1183 6.33152 20.25 6 20.25C5.66848 20.25 5.35054 20.1183 5.11612 19.8839C4.8817 19.6495 4.75 19.3315 4.75 19C4.75 18.6685 4.8817 18.3505 5.11612 18.1161Z"})))),X=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{d:"M9.5 12C9.08579 12 8.75 12.3358 8.75 12.75C8.75 13.1642 9.08579 13.5 9.5 13.5H9.51C9.92421 13.5 10.26 13.1642 10.26 12.75C10.26 12.3358 9.92421 12 9.51 12H9.5Z"}),t.createElement("path",{d:"M13.75 12.75C13.75 12.3358 14.0858 12 14.5 12H14.51C14.9242 12 15.26 12.3358 15.26 12.75C15.26 13.1642 14.9242 13.5 14.51 13.5H14.5C14.0858 13.5 13.75 13.1642 13.75 12.75Z"}),t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M14 4C14 4.83934 13.483 5.55793 12.75 5.85462V7H18C18.9946 7 19.9484 7.39509 20.6517 8.09835C21.3549 8.80161 21.75 9.75544 21.75 10.75V15.75C21.75 16.7446 21.3549 17.6984 20.6517 18.4016C20 19.0533 19.1333 19.4403 18.2183 19.4936L13.3859 22.3931C13.1542 22.5321 12.8656 22.5358 12.6305 22.4026C12.3953 22.2695 12.25 22.0202 12.25 21.75V19.5H6C5.00544 19.5 4.05161 19.1049 3.34835 18.4016C2.64509 17.6984 2.25 16.7446 2.25 15.75V10.75C2.25 9.75544 2.64509 8.80161 3.34835 8.09835C4.05161 7.39509 5.00544 7 6 7H11.25V5.85462C10.517 5.55793 10 4.83934 10 4C10 2.89543 10.8954 2 12 2C13.1046 2 14 2.89543 14 4ZM12 4.5C12.2761 4.5 12.5 4.27614 12.5 4C12.5 3.72386 12.2761 3.5 12 3.5C11.7239 3.5 11.5 3.72386 11.5 4C11.5 4.27614 11.7239 4.5 12 4.5ZM4.40901 9.15901C4.83097 8.73705 5.40326 8.5 6 8.5H18C18.5967 8.5 19.169 8.73705 19.591 9.15901C20.0129 9.58097 20.25 10.1533 20.25 10.75V15.75C20.25 16.3467 20.0129 16.919 19.591 17.341C19.169 17.7629 18.5967 18 18 18C17.8641 18 17.7307 18.0369 17.6141 18.1069L13.75 20.4254V18.75C13.75 18.3358 13.4142 18 13 18H6C5.40326 18 4.83097 17.7629 4.40901 17.341C3.98705 16.919 3.75 16.3467 3.75 15.75V10.75C3.75 10.1533 3.98705 9.58097 4.40901 9.15901Z"})))),_=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M20.5303 6.46967C20.8232 6.76256 20.8232 7.23744 20.5303 7.53033L10.5303 17.5303C10.2374 17.8232 9.76256 17.8232 9.46967 17.5303L4.46967 12.5303C4.17678 12.2374 4.17678 11.7626 4.46967 11.4697C4.76256 11.1768 5.23744 11.1768 5.53033 11.4697L10 15.9393L19.4697 6.46967C19.7626 6.17678 20.2374 6.17678 20.5303 6.46967Z"})))),G=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M8.26884 2.99217C9.45176 2.50219 10.7196 2.25 12 2.25C13.2804 2.25 14.5482 2.50219 15.7312 2.99217C16.9141 3.48216 17.9889 4.20034 18.8943 5.10571C19.7997 6.01108 20.5178 7.08591 21.0078 8.26884C21.4978 9.45176 21.75 10.7196 21.75 12C21.75 13.2804 21.4978 14.5482 21.0078 15.7312C20.5178 16.9141 19.7997 17.9889 18.8943 18.8943C17.9889 19.7997 16.9141 20.5178 15.7312 21.0078C14.5482 21.4978 13.2804 21.75 12 21.75C10.7196 21.75 9.45176 21.4978 8.26884 21.0078C7.08591 20.5178 6.01108 19.7997 5.10571 18.8943C4.20034 17.9889 3.48216 16.9141 2.99217 15.7312C2.50219 14.5482 2.25 13.2804 2.25 12C2.25 10.7196 2.50219 9.45176 2.99217 8.26884C3.48216 7.08591 4.20034 6.01108 5.10571 5.10571C6.01108 4.20034 7.08591 3.48216 8.26884 2.99217ZM12 3.75C10.9166 3.75 9.8438 3.96339 8.84286 4.37799C7.84193 4.7926 6.93245 5.40029 6.16637 6.16637C5.40029 6.93245 4.79259 7.84193 4.37799 8.84286C3.96339 9.8438 3.75 10.9166 3.75 12C3.75 13.0834 3.96339 14.1562 4.37799 15.1571C4.79259 16.1581 5.40029 17.0675 6.16637 17.8336C6.93245 18.5997 7.84193 19.2074 8.84286 19.622C9.8438 20.0366 10.9166 20.25 12 20.25C13.0834 20.25 14.1562 20.0366 15.1571 19.622C16.1581 19.2074 17.0675 18.5997 17.8336 17.8336C18.5997 17.0675 19.2074 16.1581 19.622 15.1571C20.0366 14.1562 20.25 13.0834 20.25 12C20.25 10.9166 20.0366 9.8438 19.622 8.84286C19.2074 7.84193 18.5997 6.93245 17.8336 6.16637C17.0675 5.40029 16.1581 4.7926 15.1571 4.37799C14.1562 3.96339 13.0834 3.75 12 3.75Z"}),t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M16.2414 8.99563C16.5343 9.28852 16.5343 9.7634 16.2414 10.0563L11.2933 15.0044C11.0004 15.2973 10.5255 15.2973 10.2326 15.0044L7.75861 12.5303C7.46572 12.2374 7.46572 11.7626 7.75861 11.4697C8.0515 11.1768 8.52638 11.1768 8.81927 11.4697L10.763 13.4134L15.1807 8.99563C15.4736 8.70274 15.9485 8.70274 16.2414 8.99563Z"})))),N=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7 4.75C6.66848 4.75 6.35054 4.8817 6.11612 5.11612C5.8817 5.35054 5.75 5.66848 5.75 6V18C5.75 18.3315 5.8817 18.6495 6.11612 18.8839C6.35054 19.1183 6.66848 19.25 7 19.25H9.615C10.0292 19.25 10.365 19.5858 10.365 20C10.365 20.4142 10.0292 20.75 9.615 20.75H7C6.27065 20.75 5.57118 20.4603 5.05546 19.9445C4.53973 19.4288 4.25 18.7293 4.25 18V6C4.25 5.27065 4.53973 4.57118 5.05546 4.05546C5.57118 3.53973 6.27065 3.25 7 3.25H15C15.7293 3.25 16.4288 3.53973 16.9445 4.05546C17.4603 4.57118 17.75 5.27065 17.75 6V14C17.75 14.4142 17.4142 14.75 17 14.75C16.5858 14.75 16.25 14.4142 16.25 14V6C16.25 5.66848 16.1183 5.35054 15.8839 5.11612C15.6495 4.8817 15.3315 4.75 15 4.75H7ZM8.25 8C8.25 7.58579 8.58579 7.25 9 7.25H13C13.4142 7.25 13.75 7.58579 13.75 8C13.75 8.41421 13.4142 8.75 13 8.75H9C8.58579 8.75 8.25 8.41421 8.25 8ZM8.25 12C8.25 11.5858 8.58579 11.25 9 11.25H11C11.4142 11.25 11.75 11.5858 11.75 12C11.75 12.4142 11.4142 12.75 11 12.75H9C8.58579 12.75 8.25 12.4142 8.25 12ZM20.5303 16.4697C20.8232 16.7626 20.8232 17.2374 20.5303 17.5303L16.5303 21.5303C16.2374 21.8232 15.7626 21.8232 15.4697 21.5303L13.4697 19.5303C13.1768 19.2374 13.1768 18.7626 13.4697 18.4697C13.7626 18.1768 14.2374 18.1768 14.5303 18.4697L16 19.9393L19.4697 16.4697C19.7626 16.1768 20.2374 16.1768 20.5303 16.4697Z"})))),K=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M5.46967 9.21967C5.76256 8.92678 6.23744 8.92678 6.53033 9.21967L12 14.6893L17.4697 9.21967C17.7626 8.92678 18.2374 8.92678 18.5303 9.21967C18.8232 9.51256 18.8232 9.98744 18.5303 10.2803L12.5303 16.2803C12.2374 16.5732 11.7626 16.5732 11.4697 16.2803L5.46967 10.2803C5.17678 9.98744 5.17678 9.51256 5.46967 9.21967Z"})))),Y=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7.46967 10.4697C7.76256 10.1768 8.23744 10.1768 8.53033 10.4697L12 13.9393L15.4697 10.4697C15.7626 10.1768 16.2374 10.1768 16.5303 10.4697C16.8232 10.7626 16.8232 11.2374 16.5303 11.5303L12.5303 15.5303C12.2374 15.8232 11.7626 15.8232 11.4697 15.5303L7.46967 11.5303C7.17678 11.2374 7.17678 10.7626 7.46967 10.4697Z"})))),q=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M15.3803 18.2803C15.6732 17.9874 15.6732 17.5126 15.3803 17.2197L9.91064 11.75L15.3803 6.28033C15.6732 5.98744 15.6732 5.51256 15.3803 5.21967C15.0874 4.92678 14.6125 4.92678 14.3196 5.21967L8.31965 11.2197C8.02675 11.5126 8.02675 11.9874 8.31965 12.2803L14.3196 18.2803C14.6125 18.5732 15.0874 18.5732 15.3803 18.2803Z"})))),Q=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M14.2803 7.46967C14.5732 7.76256 14.5732 8.23744 14.2803 8.53033L10.8107 12L14.2803 15.4697C14.5732 15.7626 14.5732 16.2374 14.2803 16.5303C13.9874 16.8232 13.5126 16.8232 13.2197 16.5303L9.21967 12.5303C8.92678 12.2374 8.92678 11.7626 9.21967 11.4697L13.2197 7.46967C13.5126 7.17678 13.9874 7.17678 14.2803 7.46967Z"})))),$=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M9.01972 18.2803C8.72683 17.9874 8.72683 17.5126 9.01972 17.2197L14.4894 11.75L9.01972 6.28033C8.72682 5.98744 8.72682 5.51256 9.01972 5.21967C9.31261 4.92678 9.78749 4.92678 10.0804 5.21967L16.0804 11.2197C16.3733 11.5126 16.3733 11.9874 16.0804 12.2803L10.0804 18.2803C9.78749 18.5732 9.31261 18.5732 9.01972 18.2803Z"})))),CC=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M9.71967 16.5303C9.42678 16.2374 9.42678 15.7626 9.71967 15.4697L13.1893 12L9.71967 8.53033C9.42678 8.23744 9.42678 7.76256 9.71967 7.46967C10.0126 7.17678 10.4874 7.17678 10.7803 7.46967L14.7803 11.4697C15.0732 11.7626 15.0732 12.2374 14.7803 12.5303L10.7803 16.5303C10.4874 16.8232 10.0126 16.8232 9.71967 16.5303Z"})))),eC=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M11.4697 8.46967C11.7626 8.17678 12.2374 8.17678 12.5303 8.46967L18.5303 14.4697C18.8232 14.7626 18.8232 15.2374 18.5303 15.5303C18.2374 15.8232 17.7626 15.8232 17.4697 15.5303L12 10.0607L6.53033 15.5303C6.23744 15.8232 5.76256 15.8232 5.46967 15.5303C5.17678 15.2374 5.17678 14.7626 5.46967 14.4697L11.4697 8.46967Z"})))),tC=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M16.5303 14.5303C16.2374 14.8232 15.7626 14.8232 15.4697 14.5303L12 11.0607L8.53033 14.5303C8.23744 14.8232 7.76256 14.8232 7.46967 14.5303C7.17678 14.2374 7.17678 13.7626 7.46967 13.4697L11.4697 9.46967C11.7626 9.17678 12.2374 9.17678 12.5303 9.46967L16.5303 13.4697C16.8232 13.7626 16.8232 14.2374 16.5303 14.5303Z"})))),nC=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 2.25C10.7196 2.25 9.45176 2.50219 8.26884 2.99217C7.08591 3.48216 6.01108 4.20034 5.10571 5.10571C4.20034 6.01108 3.48216 7.08591 2.99217 8.26884C2.50219 9.45176 2.25 10.7196 2.25 12C2.25 13.2804 2.50219 14.5482 2.99217 15.7312C3.48216 16.9141 4.20034 17.9889 5.10571 18.8943C6.01108 19.7997 7.08591 20.5178 8.26884 21.0078C9.45176 21.4978 10.7196 21.75 12 21.75C13.2804 21.75 14.5482 21.4978 15.7312 21.0078C16.9141 20.5178 17.9889 19.7997 18.8943 18.8943C19.7997 17.9889 20.5178 16.9141 21.0078 15.7312C21.4978 14.5482 21.75 13.2804 21.75 12C21.75 10.7196 21.4978 9.45176 21.0078 8.26884C20.5178 7.08591 19.7997 6.01108 18.8943 5.10571C17.9889 4.20034 16.9141 3.48216 15.7312 2.99217C14.5482 2.50219 13.2804 2.25 12 2.25ZM16.2415 10.0563C16.5344 9.76339 16.5344 9.28852 16.2415 8.99563C15.9486 8.70273 15.4737 8.70273 15.1809 8.99563L10.7631 13.4134L8.81939 11.4697C8.5265 11.1768 8.05163 11.1768 7.75873 11.4697C7.46584 11.7626 7.46584 12.2374 7.75873 12.5303L10.2328 15.0044C10.3734 15.145 10.5642 15.224 10.7631 15.224C10.962 15.224 11.1528 15.145 11.2934 15.0044L16.2415 10.0563Z"})))),rC=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 3.75C7.44365 3.75 3.75 7.44365 3.75 12C3.75 16.5563 7.44365 20.25 12 20.25C16.5563 20.25 20.25 16.5563 20.25 12C20.25 7.44365 16.5563 3.75 12 3.75ZM2.25 12C2.25 6.61522 6.61522 2.25 12 2.25C17.3848 2.25 21.75 6.61522 21.75 12C21.75 17.3848 17.3848 21.75 12 21.75C6.61522 21.75 2.25 17.3848 2.25 12ZM8.25 12C8.25 11.5858 8.58579 11.25 9 11.25H15C15.4142 11.25 15.75 11.5858 15.75 12C15.75 12.4142 15.4142 12.75 15 12.75H9C8.58579 12.75 8.25 12.4142 8.25 12Z"})))),lC=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{d:"M13 8.00002C13 7.69668 12.8173 7.4232 12.537 7.30711C12.2568 7.19103 11.9342 7.25519 11.7197 7.46969L9.71967 9.46969C9.42678 9.76259 9.42678 10.2375 9.71967 10.5304C10.0126 10.8232 10.4874 10.8232 10.7803 10.5304L11.5 9.81068V16C11.5 16.4142 11.8358 16.75 12.25 16.75C12.6642 16.75 13 16.4142 13 16V8.00002Z"}),t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 2.25C10.7196 2.25 9.45176 2.50219 8.26884 2.99217C7.08591 3.48216 6.01108 4.20034 5.10571 5.10571C4.20034 6.01108 3.48216 7.08591 2.99217 8.26884C2.50219 9.45176 2.25 10.7196 2.25 12C2.25 13.2804 2.50219 14.5482 2.99217 15.7312C3.48216 16.9141 4.20034 17.9889 5.10571 18.8943C6.01108 19.7997 7.08591 20.5178 8.26884 21.0078C9.45176 21.4978 10.7196 21.75 12 21.75C13.2804 21.75 14.5482 21.4978 15.7312 21.0078C16.9141 20.5178 17.9889 19.7997 18.8943 18.8943C19.7997 17.9889 20.5178 16.9141 21.0078 15.7312C21.4978 14.5482 21.75 13.2804 21.75 12C21.75 10.7196 21.4978 9.45176 21.0078 8.26884C20.5178 7.08591 19.7997 6.01108 18.8943 5.10571C17.9889 4.20034 16.9141 3.48216 15.7312 2.99217C14.5482 2.50219 13.2804 2.25 12 2.25ZM8.84286 4.37799C9.8438 3.96339 10.9166 3.75 12 3.75C13.0834 3.75 14.1562 3.96339 15.1571 4.37799C16.1581 4.7926 17.0675 5.40029 17.8336 6.16637C18.5997 6.93245 19.2074 7.84193 19.622 8.84286C20.0366 9.8438 20.25 10.9166 20.25 12C20.25 13.0834 20.0366 14.1562 19.622 15.1571C19.2074 16.1581 18.5997 17.0675 17.8336 17.8336C17.0675 18.5997 16.1581 19.2074 15.1571 19.622C14.1562 20.0366 13.0834 20.25 12 20.25C10.9166 20.25 9.8438 20.0366 8.84286 19.622C7.84193 19.2074 6.93245 18.5997 6.16637 17.8336C5.40029 17.0675 4.79259 16.1581 4.37799 15.1571C3.96339 14.1562 3.75 13.0834 3.75 12C3.75 10.9166 3.96339 9.8438 4.37799 8.84286C4.79259 7.84193 5.40029 6.93245 6.16637 6.16637C6.93245 5.40029 7.84193 4.7926 8.84286 4.37799Z"})))),oC=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 3.75C7.44365 3.75 3.75 7.44365 3.75 12C3.75 16.5563 7.44365 20.25 12 20.25C16.5563 20.25 20.25 16.5563 20.25 12C20.25 7.44365 16.5563 3.75 12 3.75ZM2.25 12C2.25 6.61522 6.61522 2.25 12 2.25C17.3848 2.25 21.75 6.61522 21.75 12C21.75 17.3848 17.3848 21.75 12 21.75C6.61522 21.75 2.25 17.3848 2.25 12ZM12 8.25C12.4142 8.25 12.75 8.58579 12.75 9V11.25H15C15.4142 11.25 15.75 11.5858 15.75 12C15.75 12.4142 15.4142 12.75 15 12.75H12.75V15C12.75 15.4142 12.4142 15.75 12 15.75C11.5858 15.75 11.25 15.4142 11.25 15V12.75H9C8.58579 12.75 8.25 12.4142 8.25 12C8.25 11.5858 8.58579 11.25 9 11.25H11.25V9C11.25 8.58579 11.5858 8.25 12 8.25Z"})))),dC=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M8.26884 2.99217C9.45176 2.50219 10.7196 2.25 12 2.25C13.2804 2.25 14.5482 2.50219 15.7312 2.99217C16.9141 3.48216 17.9889 4.20034 18.8943 5.10571C19.7997 6.01108 20.5178 7.08591 21.0078 8.26884C21.4978 9.45176 21.75 10.7196 21.75 12C21.75 13.2804 21.4978 14.5482 21.0078 15.7312C20.5178 16.9141 19.7997 17.9889 18.8943 18.8943C17.9889 19.7997 16.9141 20.5178 15.7312 21.0078C14.5482 21.4978 13.2804 21.75 12 21.75C10.7196 21.75 9.45176 21.4978 8.26884 21.0078C7.08591 20.5178 6.01108 19.7997 5.10571 18.8943C4.20034 17.9889 3.48216 16.9141 2.99217 15.7312C2.50219 14.5482 2.25 13.2804 2.25 12C2.25 10.7196 2.50219 9.45176 2.99217 8.26884C3.48216 7.08591 4.20034 6.01108 5.10571 5.10571C6.01108 4.20034 7.08591 3.48216 8.26884 2.99217ZM12 3.75C10.9166 3.75 9.8438 3.96339 8.84286 4.37799C7.84193 4.7926 6.93245 5.40029 6.16637 6.16637C5.40029 6.93245 4.79259 7.84193 4.37799 8.84286C3.96339 9.8438 3.75 10.9166 3.75 12C3.75 13.0834 3.96339 14.1562 4.37799 15.1571C4.79259 16.1581 5.40029 17.0675 6.16637 17.8336C6.93245 18.5997 7.84193 19.2074 8.84286 19.622C9.8438 20.0366 10.9166 20.25 12 20.25C13.0834 20.25 14.1562 20.0366 15.1571 19.622C16.1581 19.2074 17.0675 18.5997 17.8336 17.8336C18.5997 17.0675 19.2074 16.1581 19.622 15.1571C20.0366 14.1562 20.25 13.0834 20.25 12C20.25 10.9166 20.0366 9.8438 19.622 8.84286C19.2074 7.84193 18.5997 6.93245 17.8336 6.16637C17.0675 5.40029 16.1581 4.7926 15.1571 4.37799C14.1562 3.96339 13.0834 3.75 12 3.75Z"}),t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M9.46967 9.46967C9.76256 9.17678 10.2374 9.17678 10.5303 9.46967L12 10.9393L13.4697 9.46967C13.7626 9.17678 14.2374 9.17678 14.5303 9.46967C14.8232 9.76256 14.8232 10.2374 14.5303 10.5303L13.0607 12L14.5303 13.4697C14.8232 13.7626 14.8232 14.2374 14.5303 14.5303C14.2374 14.8232 13.7626 14.8232 13.4697 14.5303L12 13.0607L10.5303 14.5303C10.2374 14.8232 9.76256 14.8232 9.46967 14.5303C9.17678 14.2374 9.17678 13.7626 9.46967 13.4697L10.9393 12L9.46967 10.5303C9.17678 10.2374 9.17678 9.76256 9.46967 9.46967Z"})))),cC=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M9.71403 6.89535C9.70479 6.84833 9.69995 6.79973 9.69995 6.75L9.69995 2.58333C9.69995 2.11469 9.9244 1.69984 10.2627 1.41791C10.597 1.13931 11.0256 1 11.45 1H12.45C12.8743 1 13.3029 1.13931 13.6372 1.41791C13.9755 1.69984 14.2 2.11469 14.2 2.58333V6.75C14.2 6.80216 14.1946 6.85307 14.1845 6.90222C14.3206 6.94951 14.4554 7.00114 14.5886 7.05709C15.4274 7.40937 16.1876 7.9254 16.8245 8.575C17.4615 9.2246 17.9625 9.99472 18.2983 10.8403C18.4747 11.2844 18.6036 11.7447 18.6836 12.2135C18.7873 12.3147 18.8623 12.4464 18.8937 12.5965L20.9841 21.5965C21.0303 21.8176 20.9745 22.0477 20.8321 22.223C20.6897 22.3982 20.4758 22.5 20.25 22.5H3.75001C3.52417 22.5 3.31036 22.3982 3.16794 22.223C3.02552 22.0477 2.96966 21.8176 3.01588 21.5965L5.10636 12.5965C5.11848 12.5386 5.1371 12.4834 5.16135 12.4316C5.23631 11.8875 5.37667 11.3532 5.58032 10.8403C5.91608 9.99473 6.41711 9.22462 7.05409 8.57502C7.69107 7.92541 8.45122 7.40937 9.29004 7.05709C9.42974 6.99842 9.57114 6.9445 9.71403 6.89535ZM12.7 6.56577V2.59448C12.6968 2.58977 12.69 2.58113 12.6769 2.57024C12.6362 2.53628 12.556 2.5 12.45 2.5L11.45 2.5C11.3439 2.5 11.2637 2.53628 11.223 2.57024C11.2099 2.58113 11.2031 2.58977 11.2 2.59448V6.56343C11.445 6.53679 11.6918 6.52336 11.9393 6.52336C12.194 6.52336 12.4479 6.53757 12.7 6.56577ZM9.80005 21V18.75C9.80005 18.3358 9.46426 18 9.05005 18C8.63584 18 8.30005 18.3358 8.30005 18.75V21H4.67301L6.44992 13.5H17.5501L19.327 21H15.8V18.75C15.8 18.3358 15.4643 18 15.05 18C14.6358 18 14.3 18.3358 14.3 18.75V21H9.80005ZM16.9042 11.3939C16.9829 11.5922 17.0496 11.7946 17.1039 12H6.77477C6.82907 11.7946 6.89569 11.5922 6.97443 11.3939C7.23658 10.7337 7.62777 10.1324 8.1251 9.62522C8.62244 9.11804 9.21593 8.71513 9.87085 8.44008C10.5258 8.16503 11.229 8.02336 11.9393 8.02336C12.6497 8.02336 13.3529 8.16503 14.0078 8.44007C14.6627 8.71512 15.2562 9.11803 15.7535 9.62521C16.2509 10.1324 16.642 10.7337 16.9042 11.3939Z"})))),aC=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 3.75C7.44365 3.75 3.75 7.44365 3.75 12C3.75 16.5563 7.44365 20.25 12 20.25C16.5563 20.25 20.25 16.5563 20.25 12C20.25 7.44365 16.5563 3.75 12 3.75ZM2.25 12C2.25 6.61522 6.61522 2.25 12 2.25C17.3848 2.25 21.75 6.61522 21.75 12C21.75 17.3848 17.3848 21.75 12 21.75C6.61522 21.75 2.25 17.3848 2.25 12ZM12 6.25C12.4142 6.25 12.75 6.58579 12.75 7V11.6893L15.5303 14.4697C15.8232 14.7626 15.8232 15.2374 15.5303 15.5303C15.2374 15.8232 14.7626 15.8232 14.4697 15.5303L11.4697 12.5303C11.329 12.3897 11.25 12.1989 11.25 12V7C11.25 6.58579 11.5858 6.25 12 6.25Z"})))),fC=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{d:"M10.4947 4.49524C12.6208 3.85181 14.9751 4.27375 16.6783 5.62565C18.1374 6.78482 18.9392 8.48993 18.8555 10.2501H19C19.7778 10.2501 20.5336 10.4633 21.1892 10.8573C21.4838 11.0343 21.7582 11.2479 22.0052 11.4949C22.2855 11.7752 22.2993 12.2253 22.0366 12.5222C21.7739 12.8191 21.3255 12.8603 21.0131 12.6162L20.3795 12.1211C19.9639 11.8801 19.4886 11.7501 19 11.7501H18C17.7722 11.7501 17.5568 11.6466 17.4144 11.4687C17.2721 11.2909 17.2183 11.058 17.2682 10.8358C17.5975 9.36919 17.0404 7.82917 15.7457 6.80053C14.4429 5.76643 12.6031 5.42436 10.9292 5.93093C9.25799 6.4367 8.06299 7.68936 7.73177 9.16441C7.65242 9.51778 7.33188 9.76397 6.97 9.74949C5.10385 9.67481 3.48126 10.8739 3.06166 12.5547L3.05875 12.5664C2.33183 15.2919 4.06737 17.2502 5.99998 17.2502H11.5C11.9142 17.2502 12.25 17.586 12.25 18.0002C12.25 18.4144 11.9142 18.7502 11.5 18.7502H5.99998C2.93439 18.7502 0.67068 15.7117 1.60779 12.1856C2.16436 9.9673 4.14699 8.41303 6.4318 8.25883C7.04699 6.47223 8.56878 5.0781 10.4947 4.49524Z"}),t.createElement("path",{d:"M20.5991 16.95H18.0002C17.5873 16.9512 17.1898 17.1072 16.8862 17.3872C16.5824 17.6674 16.3947 18.0513 16.3602 18.4631C16.3258 18.875 16.4471 19.2847 16.7001 19.6115C16.9531 19.9382 17.3195 20.1582 17.7269 20.2279C18.1351 20.2978 18.4095 20.6854 18.3396 21.0936C18.2698 21.5019 17.8822 21.7763 17.4739 21.7064C16.6964 21.5734 15.9971 21.1536 15.5141 20.5299C15.0312 19.9062 14.7997 19.1241 14.8655 18.3381C14.9312 17.552 15.2894 16.8192 15.8693 16.2845C16.4491 15.7498 17.2085 15.452 17.9973 15.45H20.599C20.3782 15.1561 20.4014 14.7371 20.6688 14.4697C20.9617 14.1768 21.4366 14.1768 21.7295 14.4697L22.9244 15.6646C22.943 15.6828 22.9606 15.702 22.9773 15.7221C23.0202 15.774 23.0552 15.8303 23.082 15.8893C23.1251 15.984 23.1492 16.0892 23.1492 16.2C23.1492 16.2023 23.1492 16.2046 23.1491 16.2069C23.1483 16.2948 23.1322 16.3825 23.1007 16.4658C23.0642 16.5623 23.0071 16.6527 22.9295 16.7303L21.7295 17.9303C21.4366 18.2232 20.9617 18.2232 20.6688 17.9303C20.4014 17.6629 20.3782 17.2438 20.5991 16.95Z"})))),LC=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M9.0073 4.94047C10.361 4.30065 11.8805 4.09878 13.3543 4.36296C14.8281 4.62714 16.1829 5.34422 17.2301 6.4144C18.1447 7.34909 18.7849 8.5125 19.087 9.7776C20.0589 9.96567 20.9482 10.469 21.612 11.2179C22.4073 12.1153 22.8136 13.2918 22.7417 14.4887C22.7063 15.0815 22.5546 15.6615 22.2951 16.1956C22.0355 16.7298 21.6733 17.2076 21.2291 17.6017C20.7849 17.9958 20.2674 18.2985 19.7061 18.4926C19.1523 18.6841 18.5665 18.7661 17.9816 18.7341H7.28153C6.55819 18.7892 5.83091 18.7033 5.14009 18.4813C4.4392 18.2559 3.78955 17.8948 3.22824 17.4184C2.66693 16.942 2.20496 16.3596 1.8687 15.7047C1.53245 15.0498 1.32849 14.335 1.26848 13.6012C1.20776 12.8674 1.29223 12.1295 1.51695 11.4283C1.74168 10.7271 2.10231 10.077 2.57827 9.51522C3.05423 8.9534 3.63619 8.49082 4.29092 8.15391C4.82807 7.8775 5.40558 7.69011 6.00091 7.5982C6.72947 6.44756 7.77119 5.5247 9.0073 4.94047ZM17.7087 10.527C17.519 9.37102 16.9777 8.30121 16.158 7.46349C15.329 6.61626 14.2564 6.04857 13.0897 5.83943C11.9229 5.63029 10.72 5.7901 9.64827 6.29662C8.57658 6.80314 7.68947 7.63117 7.1104 8.66546C6.98854 8.88313 6.76643 9.02596 6.51782 9.04652C5.98031 9.09099 5.45683 9.2409 4.97725 9.48768C4.49768 9.73446 4.07141 10.0733 3.72278 10.4848C3.37415 10.8963 3.10999 11.3725 2.94539 11.8861C2.78078 12.3997 2.71895 12.9407 2.76343 13.4782C2.80738 14.0157 2.95683 14.5399 3.20311 15.0196C3.4494 15.4993 3.78776 15.9258 4.19887 16.2747C4.60999 16.6237 5.08581 16.8882 5.59917 17.0532C6.11253 17.2183 6.65336 17.2806 7.1908 17.2366C7.21115 17.2349 7.23156 17.2341 7.25199 17.2341H18.003C18.018 17.2341 18.0329 17.2345 18.0479 17.2354C18.444 17.2592 18.8409 17.2046 19.2159 17.075C19.591 16.9453 19.9368 16.743 20.2336 16.4796C20.5304 16.2163 20.7725 15.897 20.9459 15.5401C21.1193 15.1832 21.2207 14.7956 21.2443 14.3995C21.2925 13.5995 21.021 12.8126 20.4894 12.2128C19.9578 11.613 19.2098 11.249 18.4098 11.2007C18.1584 11.1855 17.9314 11.0452 17.8054 10.827C17.7515 10.7336 17.7192 10.6312 17.7087 10.527Z"})))),iC=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7.53033 7.4696C7.82322 7.7625 7.82322 8.23737 7.53033 8.53026L4.06066 11.9999L7.53033 15.4696C7.82322 15.7625 7.82322 16.2374 7.53033 16.5303C7.23744 16.8232 6.76256 16.8232 6.46967 16.5303L2.46967 12.5303C2.17678 12.2374 2.17678 11.7625 2.46967 11.4696L6.46967 7.4696C6.76256 7.17671 7.23744 7.17671 7.53033 7.4696Z"}),t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M16.4697 7.4696C16.7626 7.17671 17.2374 7.17671 17.5303 7.4696L21.5303 11.4696C21.8232 11.7625 21.8232 12.2374 21.5303 12.5303L17.5303 16.5303C17.2374 16.8232 16.7626 16.8232 16.4697 16.5303C16.1768 16.2374 16.1768 15.7625 16.4697 15.4696L19.9393 11.9999L16.4697 8.53026C16.1768 8.23737 16.1768 7.7625 16.4697 7.4696Z"}),t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M14.1819 3.27233C14.5837 3.37279 14.8281 3.77999 14.7276 4.18183L10.7276 20.1818C10.6271 20.5837 10.2199 20.828 9.8181 20.7275C9.41625 20.6271 9.17193 20.2199 9.27239 19.818L13.2724 3.81803C13.3729 3.41619 13.7801 3.17186 14.1819 3.27233Z"})))),uC=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12.0001 2.36841C10.3879 2.36841 8.84165 3.00887 7.70163 4.14889C6.5616 5.28892 5.92114 6.83512 5.92114 8.44736C5.92114 8.95514 5.98467 9.45637 6.10733 9.94041C3.9161 10.8566 2.36841 13.0302 2.36841 15.5525C2.36841 18.9063 5.09288 21.631 8.44652 21.6315C9.72873 21.6342 10.9723 21.2274 12 20.4794C13.0277 21.2274 14.2713 21.6342 15.5535 21.6315C18.9071 21.631 21.6316 18.9063 21.6316 15.5525C21.6316 13.0303 20.084 10.8567 17.8928 9.94049C18.0155 9.45642 18.0791 8.95517 18.0791 8.44736C18.0791 6.83512 17.4386 5.28892 16.2986 4.14889C15.1585 3.00887 13.6123 2.36841 12.0001 2.36841ZM17.3476 11.3384C17.0703 11.8513 16.7183 12.3261 16.2986 12.7458C15.7323 13.3121 15.0659 13.7551 14.3409 14.0575C14.4618 14.5362 14.5263 15.0382 14.5263 15.5525C14.5263 17.0232 14.0011 18.3797 13.1235 19.4303C13.845 19.8873 14.6863 20.1335 15.5509 20.1315L15.5526 20.1315C18.0782 20.1315 20.1316 18.0781 20.1316 15.5525C20.1316 13.6672 18.98 12.0362 17.3476 11.3384ZM12.8953 14.4601C12.6007 14.5039 12.3016 14.5263 12.0001 14.5263C10.3879 14.5263 8.84165 13.8859 7.70163 12.7458C7.28188 12.3261 6.92985 11.8513 6.65259 11.3384C5.02004 12.0361 3.86841 13.6671 3.86841 15.5525C3.86841 18.0781 5.92177 20.1315 8.44736 20.1315L8.44906 20.1315C9.57297 20.134 10.6574 19.7175 11.4905 18.9633C11.4968 18.9574 11.5033 18.9516 11.5098 18.946C12.4423 18.1161 13.0263 16.9 13.0263 15.5525C13.0263 15.1761 12.9806 14.8093 12.8953 14.4601ZM8.76229 5.20955C9.62101 4.35083 10.7857 3.86841 12.0001 3.86841C13.2145 3.86841 14.3792 4.35083 15.2379 5.20955C16.0966 6.06827 16.5791 7.23295 16.5791 8.44736C16.5791 9.66178 16.0966 10.8265 15.2379 11.6852C14.3792 12.5439 13.2145 13.0263 12.0001 13.0263C10.7857 13.0263 9.62101 12.5439 8.76229 11.6852C7.90357 10.8265 7.42114 9.66178 7.42114 8.44736C7.42114 7.23295 7.90357 6.06827 8.76229 5.20955Z"})))),VC=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{d:"M18 17a1 1 0 1 0-2 0v.01a1 1 0 1 0 2 0V17Z"}),t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M15 2.25A2.75 2.75 0 0 0 12.25 5v.532l-.649-.648a2.75 2.75 0 0 0-3.889 0L4.884 7.712a2.75 2.75 0 0 0 0 3.89l.649.648H5A2.75 2.75 0 0 0 2.25 15v4A2.75 2.75 0 0 0 5 21.75h12A4.75 4.75 0 0 0 21.75 17V5A2.75 2.75 0 0 0 19 2.25h-4Zm-8 11.5h.032l6.5 6.5H5A1.25 1.25 0 0 1 3.75 19v-4A1.25 1.25 0 0 1 5 13.75h2Zm12.298 5.548a3.25 3.25 0 0 1-4.573.023l-.046-.046A3.25 3.25 0 0 1 13.75 17V5A1.25 1.25 0 0 1 15 3.75h4A1.25 1.25 0 0 1 20.25 5v12a3.25 3.25 0 0 1-.952 2.298Zm-7.048-2.452V7.654l-1.71-1.71a1.25 1.25 0 0 0-1.767 0L5.945 8.773a1.25 1.25 0 0 0 0 1.768l6.305 6.305Z"})))),HC=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4.75 5.25C4.05964 5.25 3.5 5.80964 3.5 6.5V17.5C3.5 18.1904 4.05964 18.75 4.75 18.75H7.75C8.16421 18.75 8.5 19.0858 8.5 19.5C8.5 19.9142 8.16421 20.25 7.75 20.25H4.75C3.23122 20.25 2 19.0188 2 17.5V6.5C2 4.98122 3.23122 3.75 4.75 3.75H16.75C18.2688 3.75 19.5 4.98122 19.5 6.5V8C19.5 8.41421 19.1642 8.75 18.75 8.75C18.3358 8.75 18 8.41421 18 8V6.5C18 5.80964 17.4404 5.25 16.75 5.25H4.75ZM12.75 13.25C12.6676 13.25 12.5982 13.281 12.5546 13.3217C12.5128 13.3607 12.5 13.4021 12.5 13.4333V18.5667C12.5 18.5979 12.5128 18.6393 12.5546 18.6783C12.5982 18.719 12.6676 18.75 12.75 18.75H19.75C19.8324 18.75 19.9018 18.719 19.9454 18.6783C19.9872 18.6393 20 18.5979 20 18.5667V14.8333C20 14.8021 19.9872 14.7607 19.9454 14.7217C19.9018 14.681 19.8324 14.65 19.75 14.65H16.25C16.06 14.65 15.8771 14.5779 15.7383 14.4483L14.4544 13.25H12.75ZM11.5312 12.2251C11.8627 11.9156 12.3019 11.75 12.75 11.75H14.75C14.94 11.75 15.1229 11.8221 15.2617 11.9517L16.5456 13.15H19.75C20.1981 13.15 20.6373 13.3156 20.9688 13.6251C21.3021 13.9361 21.5 14.3695 21.5 14.8333V18.5667C21.5 19.0305 21.3021 19.4639 20.9688 19.7749C20.6373 20.0844 20.1981 20.25 19.75 20.25H12.75C12.3019 20.25 11.8627 20.0844 11.5312 19.7749C11.1979 19.4639 11 19.0305 11 18.5667V13.4333C11 12.9695 11.1979 12.5361 11.5312 12.2251Z"})))),MC=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4.05546 4.05546C4.57118 3.53973 5.27065 3.25 6 3.25H14C14.7293 3.25 15.4288 3.53973 15.9445 4.05546C16.4603 4.57118 16.75 5.27065 16.75 6V7.25H18C19.5188 7.25 20.75 8.48122 20.75 10V18C20.75 19.5188 19.5188 20.75 18 20.75H10C8.48122 20.75 7.25 19.5188 7.25 18V16.75H6C5.27065 16.75 4.57118 16.4603 4.05546 15.9445C3.53973 15.4288 3.25 14.7293 3.25 14V6C3.25 5.27065 3.53973 4.57118 4.05546 4.05546ZM8.75 18C8.75 18.6904 9.30964 19.25 10 19.25H18C18.6904 19.25 19.25 18.6904 19.25 18V10C19.25 9.30964 18.6904 8.75 18 8.75H10C9.30964 8.75 8.75 9.30964 8.75 10V18ZM15.25 7.25H10C8.48122 7.25 7.25 8.48122 7.25 10V15.25H6C5.66848 15.25 5.35054 15.1183 5.11612 14.8839C4.8817 14.6495 4.75 14.3315 4.75 14V6C4.75 5.66848 4.8817 5.35054 5.11612 5.11612C5.35054 4.8817 5.66848 4.75 6 4.75H14C14.3315 4.75 14.6495 4.8817 14.8839 5.11612C15.1183 5.35054 15.25 5.66848 15.25 6V7.25Z"})))),ZC=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M20 0.25C20.4641 0.25 20.9092 0.434375 21.2374 0.762563C21.5656 1.09075 21.75 1.53587 21.75 2V14C21.75 14.4641 21.5656 14.9092 21.2374 15.2374C20.9092 15.5656 20.4641 15.75 20 15.75H17.75V20C17.75 20.4641 17.5656 20.9092 17.2374 21.2374C16.9092 21.5656 16.4641 21.75 16 21.75H4C3.53587 21.75 3.09075 21.5656 2.76256 21.2374C2.43437 20.9092 2.25 20.4641 2.25 20V8C2.25 7.53587 2.43438 7.09075 2.76256 6.76256C3.09075 6.43437 3.53587 6.25 4 6.25H6.25V2C6.25 1.53587 6.43437 1.09075 6.76256 0.762563C7.09075 0.434375 7.53587 0.25 8 0.25H20ZM8 1.75C7.9337 1.75 7.87011 1.77634 7.82322 1.82322C7.77634 1.87011 7.75 1.9337 7.75 2V4.25H10.25V1.75H8ZM3.75 11.75V20C3.75 20.0663 3.77634 20.1299 3.82322 20.1768C3.87011 20.2237 3.93369 20.25 4 20.25H16C16.0663 20.25 16.1299 20.2237 16.1768 20.1768C16.2237 20.1299 16.25 20.0663 16.25 20V15.75H8C7.53587 15.75 7.09075 15.5656 6.76256 15.2374C6.43437 14.9092 6.25 14.4641 6.25 14V11.75H3.75ZM6.25 10.25H3.75V8C3.75 7.9337 3.77634 7.87011 3.82322 7.82322C3.87011 7.77634 3.9337 7.75 4 7.75H6.25V10.25ZM7.75 14C7.75 14.0663 7.77634 14.1299 7.82322 14.1768C7.87011 14.2237 7.93369 14.25 8 14.25H20C20.0663 14.25 20.1299 14.2237 20.1768 14.1768C20.2237 14.1299 20.25 14.0663 20.25 14V5.75H7.75V14ZM11.75 1.75V4.25H20.25V2C20.25 1.93369 20.2237 1.87011 20.1768 1.82322C20.1299 1.77634 20.0663 1.75 20 1.75H11.75Z"})))),RC=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M14.4697 4.46967C14.7626 4.17678 15.2374 4.17678 15.5303 4.46967L19.5303 8.46967C19.8232 8.76256 19.8232 9.23744 19.5303 9.53033L15.5303 13.5303C15.2374 13.8232 14.7626 13.8232 14.4697 13.5303C14.1768 13.2374 14.1768 12.7626 14.4697 12.4697L17.1893 9.75H9C8.40326 9.75 7.83097 9.98705 7.40901 10.409C6.98705 10.831 6.75 11.4033 6.75 12V18C6.75 18.4142 6.41421 18.75 6 18.75C5.58579 18.75 5.25 18.4142 5.25 18V12C5.25 11.0054 5.64509 10.0516 6.34835 9.34835C7.05161 8.64509 8.00544 8.25 9 8.25H17.1893L14.4697 5.53033C14.1768 5.23744 14.1768 4.76256 14.4697 4.46967Z"})))),vC=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{d:"M5.25 15.5C5.25 15.0858 5.58579 14.75 6 14.75H6.01C6.42421 14.75 6.76 15.0858 6.76 15.5C6.76 15.9142 6.42421 16.25 6.01 16.25H6C5.58579 16.25 5.25 15.9142 5.25 15.5Z"}),t.createElement("path",{d:"M9 14.75C8.58579 14.75 8.25 15.0858 8.25 15.5C8.25 15.9142 8.58579 16.25 9 16.25H11C11.4142 16.25 11.75 15.9142 11.75 15.5C11.75 15.0858 11.4142 14.75 11 14.75H9Z"}),t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M2.25 8C2.25 7.00544 2.64509 6.05161 3.34835 5.34835C4.05161 4.64509 5.00544 4.25 6 4.25H18C18.9946 4.25 19.9484 4.64509 20.6517 5.34835C21.3549 6.05161 21.75 7.00544 21.75 8V16C21.75 16.9946 21.3549 17.9484 20.6517 18.6517C19.9484 19.3549 18.9946 19.75 18 19.75H6C5.00544 19.75 4.05161 19.3549 3.34835 18.6517C2.64509 17.9484 2.25 16.9946 2.25 16V8ZM4.40901 6.40901C4.83097 5.98705 5.40326 5.75 6 5.75H18C18.5967 5.75 19.169 5.98705 19.591 6.40901C20.0129 6.83097 20.25 7.40326 20.25 8V9.25H3.75V8C3.75 7.40326 3.98705 6.83097 4.40901 6.40901ZM20.25 10.75V16C20.25 16.5967 20.0129 17.169 19.591 17.591C19.169 18.0129 18.5967 18.25 18 18.25H6C5.40326 18.25 4.83097 18.0129 4.40901 17.591C3.98705 17.169 3.75 16.5967 3.75 16V10.75H20.25Z"})))),pC=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 5.25C12.2508 5.25 12.485 5.37533 12.6241 5.58397L16.1703 10.9033L20.5315 7.41435C20.7777 7.21743 21.1207 7.19544 21.39 7.35933C21.6592 7.52321 21.7973 7.83798 21.7355 8.14709L19.7355 18.1471C19.6654 18.4977 19.3576 18.75 19 18.75H5.00004C4.64253 18.75 4.33472 18.4977 4.26461 18.1471L2.2646 8.14709C2.20278 7.83798 2.34084 7.52321 2.61012 7.35933C2.8794 7.19544 3.22241 7.21743 3.46856 7.41435L7.82977 10.9033L11.376 5.58397C11.5151 5.37533 11.7493 5.25 12 5.25ZM12 7.35208L8.62408 12.416C8.50748 12.5909 8.32282 12.7089 8.1151 12.7411C7.90738 12.7734 7.69566 12.717 7.53152 12.5857L4.13926 9.87185L5.61489 17.25H18.3852L19.8608 9.87185L16.4686 12.5857C16.3044 12.717 16.0927 12.7734 15.885 12.7411C15.6773 12.7089 15.4926 12.5909 15.376 12.416L12 7.35208Z"})))),mC=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6.51126 3.81961C8.00104 3.26094 9.975 2.96069 12 2.96069C14.025 2.96069 15.999 3.26094 17.4887 3.81961C18.2325 4.09853 18.8626 4.44434 19.3191 4.85754C19.7752 5.27052 20.1123 5.80494 20.1123 6.4374V17.5628C20.1123 18.1953 19.7752 18.7297 19.3191 19.1427C18.8626 19.5559 18.2325 19.9017 17.4887 20.1806C15.999 20.7393 14.025 21.0395 12 21.0395C9.975 21.0395 8.00104 20.7393 6.51126 20.1806C5.76746 19.9017 5.13736 19.5559 4.68094 19.1427C4.22478 18.7297 3.8877 18.1953 3.8877 17.5628V6.4374C3.8877 5.80494 4.22478 5.27052 4.68094 4.85754C5.13736 4.44434 5.76746 4.09853 6.51126 3.81961ZM5.27838 8.46236V12.0001C5.27838 12.1053 5.33201 12.2935 5.61429 12.549C5.89633 12.8044 6.35242 13.0731 6.99956 13.3158C8.29169 13.8003 10.0908 14.0861 12 14.0861C13.9092 14.0861 15.7083 13.8003 17.0004 13.3158C17.6476 13.0731 18.1037 12.8044 18.3857 12.549C18.668 12.2935 18.7216 12.1053 18.7216 12.0001V8.46236C18.3633 8.68551 17.9477 8.88308 17.4887 9.05518C15.999 9.61385 14.025 9.9141 12 9.9141C9.975 9.9141 8.00104 9.61385 6.51126 9.05518C6.05232 8.88308 5.63665 8.68551 5.27838 8.46236ZM18.7216 6.4374C18.7216 6.5426 18.668 6.73074 18.3857 6.9863C18.1037 7.24164 17.6476 7.51037 17.0004 7.75305C15.7083 8.2376 13.9092 8.52342 12 8.52342C10.0908 8.52342 8.29169 8.2376 6.99956 7.75305C6.35242 7.51037 5.89633 7.24164 5.61429 6.9863C5.33201 6.73074 5.27838 6.5426 5.27838 6.4374C5.27838 6.33219 5.33201 6.14405 5.61429 5.88849C5.89633 5.63315 6.35242 5.36442 6.99956 5.12174C8.29169 4.6372 10.0908 4.35137 12 4.35137C13.9092 4.35137 15.7083 4.6372 17.0004 5.12174C17.6476 5.36442 18.1037 5.63315 18.3857 5.88849C18.668 6.14405 18.7216 6.33219 18.7216 6.4374ZM18.7216 14.0251C18.3633 14.2482 17.9477 14.4458 17.4887 14.6179C15.999 15.1766 14.025 15.4768 12 15.4768C9.975 15.4768 8.00104 15.1766 6.51126 14.6179C6.05232 14.4458 5.63665 14.2482 5.27838 14.0251V17.5628C5.27838 17.668 5.33201 17.8562 5.61429 18.1117C5.89633 18.3671 6.35242 18.6358 6.99956 18.8785C8.29169 19.363 10.0908 19.6489 12 19.6489C13.9092 19.6489 15.7083 19.363 17.0004 18.8785C17.6476 18.6358 18.1037 18.3671 18.3857 18.1117C18.668 17.8562 18.7216 17.668 18.7216 17.5628V14.0251Z"})))),EC=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4.82091 5.29117C4.7847 5.3319 4.75 5.40356 4.75 5.5V15.5C4.75 15.5964 4.7847 15.6681 4.82091 15.7088C4.85589 15.7482 4.88124 15.75 4.88889 15.75H19.1111C19.1188 15.75 19.1441 15.7482 19.1791 15.7088C19.2153 15.6681 19.25 15.5964 19.25 15.5V5.5C19.25 5.40356 19.2153 5.3319 19.1791 5.29117C19.1441 5.25181 19.1188 5.25 19.1111 5.25H4.88889C4.88124 5.25 4.85589 5.25181 4.82091 5.29117ZM3.25 5.5C3.25 4.61899 3.90315 3.75 4.88889 3.75H19.1111C20.0968 3.75 20.75 4.61899 20.75 5.5V15.5C20.75 16.381 20.0968 17.25 19.1111 17.25H4.88889C3.90315 17.25 3.25 16.381 3.25 15.5V5.5ZM6.25 19.5C6.25 19.0858 6.58579 18.75 7 18.75H17C17.4142 18.75 17.75 19.0858 17.75 19.5C17.75 19.9142 17.4142 20.25 17 20.25H7C6.58579 20.25 6.25 19.9142 6.25 19.5Z"})))),wC=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{d:"M18.8972 6.0871L18.4637 5.6536C16.9256 4.11547 14.4318 4.11547 12.8936 5.6536L11.864 6.68328C11.5711 6.97618 11.0962 6.97618 10.8033 6.68328C10.5104 6.39039 10.5104 5.91552 10.8033 5.62262L11.833 4.59294C13.9569 2.46902 17.4005 2.46902 19.5244 4.59294L19.9579 5.02644C22.0818 7.15037 22.0818 10.5939 19.9579 12.7178L18.9282 13.7475C18.6353 14.0404 18.1604 14.0404 17.8676 13.7475C17.5747 13.4546 17.5747 12.9798 17.8676 12.6869L18.8972 11.6572C20.4354 10.1191 20.4354 7.62524 18.8972 6.0871Z"}),t.createElement("path",{d:"M5.6536 12.8936C4.11547 14.4318 4.11547 16.9256 5.6536 18.4637L6.0871 18.8972C7.62524 20.4354 10.1191 20.4354 11.6572 18.8972L12.6869 17.8675C12.9798 17.5747 13.4546 17.5747 13.7475 17.8675C14.0404 18.1604 14.0404 18.6353 13.7475 18.9282L12.7179 19.9579C10.5939 22.0818 7.15037 22.0818 5.02644 19.9579L4.59294 19.5244C2.46902 17.4005 2.46902 13.9569 4.59294 11.833L5.62262 10.8033C5.91551 10.5104 6.39039 10.5104 6.68328 10.8033C6.97618 11.0962 6.97617 11.5711 6.68328 11.864L5.6536 12.8936Z"})))),hC=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{d:"M9.24313 9.18579C9.45624 8.83061 9.34107 8.36991 8.98588 8.1568C8.6307 7.94369 8.17 8.05886 7.95689 8.41405L7.35689 9.41405C7.18664 9.69779 7.22247 10.0596 7.44505 10.3044L9.44505 12.5044C9.72368 12.8109 10.198 12.8335 10.5045 12.5549C10.811 12.2762 10.8336 11.8019 10.555 11.4954L8.9299 9.70784L9.24313 9.18579Z"}),t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6.00001 4.25C5.73656 4.25 5.49243 4.38822 5.35689 4.61413L2.35689 9.61413C2.18777 9.89599 2.2219 10.2551 2.44108 10.5001L10.9411 20.0001C10.9486 20.0085 10.9564 20.0168 10.9643 20.0249C11.0993 20.1627 11.2604 20.2721 11.4383 20.3469C11.6161 20.4216 11.8071 20.4601 12 20.4601C12.1929 20.4601 12.3839 20.4216 12.5617 20.3469C12.7396 20.2721 12.9007 20.1627 13.0357 20.0249C13.0436 20.0168 13.0514 20.0085 13.0589 20.0001L21.5589 10.5001C21.7781 10.2551 21.8122 9.89599 21.6431 9.61413L18.6431 4.61413C18.5076 4.38822 18.2635 4.25 18 4.25H6.00001ZM3.92753 9.91186L6.42465 5.75H17.5754L20.0725 9.91186L12 18.934L3.92753 9.91186Z"})))),BC=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{d:"M12.01 2.01099C12.7878 2.01103 13.539 2.29436 14.123 2.80799L14.277 2.95299L14.975 3.65099C15.1666 3.8413 15.4168 3.96144 15.685 3.99199L15.82 3.99999H16.82C17.6373 3.99994 18.4235 4.31256 19.0176 4.87373C19.6117 5.4349 19.9686 6.20209 20.015 7.01799L20.02 7.19999V8.19999C20.02 8.46999 20.112 8.73299 20.278 8.94299L20.368 9.04299L21.065 9.74099C21.6428 10.3154 21.9796 11.0886 22.0069 11.9029C22.0343 12.7171 21.75 13.5111 21.212 14.123L21.067 14.277L20.369 14.975C20.1787 15.1665 20.0586 15.4167 20.028 15.685L20.02 15.82V16.82C20.0201 17.6372 19.7075 18.4235 19.1463 19.0176C18.5851 19.6116 17.8179 19.9685 17.002 20.015L16.82 20.02H15.82C15.5504 20.0201 15.2887 20.111 15.077 20.278L14.977 20.368L14.279 21.065C13.7046 21.6427 12.9314 21.9796 12.1172 22.0069C11.3029 22.0342 10.5089 21.7499 9.89705 21.212L9.74305 21.067L9.04505 20.369C8.85349 20.1787 8.60334 20.0585 8.33505 20.028L8.20005 20.02H7.20005C6.38283 20.02 5.59656 19.7074 5.00248 19.1462C4.4084 18.5851 4.05152 17.8179 4.00505 17.002L4.00005 16.82V15.82C3.99996 15.5504 3.90907 15.2886 3.74205 15.077L3.65205 14.977L2.95505 14.279C2.37733 13.7045 2.04047 12.9314 2.01315 12.1171C1.98584 11.3029 2.27012 10.5089 2.80805 9.89699L2.95305 9.74299L3.65105 9.04499C3.84136 8.85343 3.9615 8.60328 3.99205 8.33499L4.00005 8.19999V7.19999L4.00505 7.01799C4.0497 6.23343 4.38147 5.49274 4.93713 4.93707C5.4928 4.38141 6.23349 4.04964 7.01805 4.00499L7.20005 3.99999H8.20005C8.46966 3.9999 8.7314 3.90901 8.94305 3.74199L9.04305 3.65199L9.74105 2.95499C10.0384 2.65588 10.3919 2.41849 10.7813 2.25649C11.1707 2.09448 11.5883 2.01105 12.01 2.01099ZM15.707 9.29299C15.5195 9.10552 15.2652 9.0002 15 9.0002C14.7349 9.0002 14.4806 9.10552 14.293 9.29299L11 12.585L9.70705 11.293L9.61305 11.21C9.41205 11.0546 9.15944 10.9815 8.90652 11.0056C8.6536 11.0297 8.41933 11.1491 8.25129 11.3397C8.08326 11.5303 7.99406 11.7777 8.00182 12.0316C8.00957 12.2856 8.1137 12.527 8.29305 12.707L10.293 14.707L10.387 14.79C10.5794 14.9392 10.8197 15.0132 11.0627 14.9979C11.3058 14.9826 11.5349 14.8792 11.707 14.707L15.707 10.707L15.79 10.613C15.9393 10.4206 16.0132 10.1803 15.9979 9.93731C15.9827 9.69428 15.8792 9.46518 15.707 9.29299Z"})))),xC=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12.6833 2.27282C12.5896 2.24924 12.4915 2.24365 12.3946 2.25737C12.2631 2.25228 12.1314 2.24985 11.9995 2.2501C11.9999 2.2501 12.0002 2.2501 12.0005 2.2501M11.3121 2.27403C9.94689 2.36991 8.61447 2.75264 7.40293 3.40017C5.98846 4.15617 4.78253 5.24973 3.89222 6.58377C3.66229 6.9283 3.75519 7.394 4.09972 7.62393C4.44426 7.85387 4.90995 7.76097 5.13989 7.41643C5.8931 6.28783 6.91333 5.36266 8.10999 4.72307C8.73764 4.38761 9.40365 4.13615 10.0909 3.97299C9.63416 4.87365 9.25473 5.81253 8.95721 6.77955C8.83541 7.17545 9.05761 7.59514 9.45351 7.71694C9.84941 7.83874 10.2691 7.61655 10.3909 7.22065C10.7641 6.00761 11.2781 4.84297 11.922 3.75042C11.9479 3.75019 11.9737 3.75008 11.9996 3.7501L12.0015 3.7501C12.0271 3.75005 12.0526 3.75012 12.0781 3.7503C12.723 4.84404 13.2377 6.01006 13.6112 7.22458C13.733 7.62049 14.1526 7.84273 14.5485 7.72096C14.9444 7.5992 15.1667 7.17954 15.0449 6.78362C14.7467 5.81391 14.3661 4.8725 13.9078 3.96954C14.5986 4.13217 15.2682 4.38391 15.8992 4.72044C17.0986 5.36012 18.1213 6.28634 18.8764 7.41669C19.1065 7.76113 19.5722 7.85384 19.9166 7.62376C20.2611 7.39368 20.3538 6.92795 20.1237 6.58351C19.2313 5.24759 18.0226 4.15293 16.6051 3.39691C15.3895 2.74858 14.0526 2.36652 12.6833 2.27282M1.81815 9.27249C2.22 9.17203 2.6272 9.41635 2.72766 9.8182L3.1435 11.4816L3.79781 9.73676C3.90758 9.44403 4.18742 9.2501 4.50006 9.2501C4.81269 9.2501 5.09253 9.44403 5.2023 9.73676L5.85661 11.4816L6.27245 9.8182C6.37291 9.41635 6.78011 9.17203 7.18196 9.27249C7.5838 9.37296 7.82812 9.78016 7.72766 10.182L6.72766 14.182C6.64803 14.5005 6.37049 14.7302 6.0427 14.7489C5.71492 14.7676 5.41309 14.5709 5.29781 14.2634L4.50006 12.1361L3.7023 14.2634C3.58702 14.5709 3.28519 14.7676 2.95741 14.7489C2.62962 14.7302 2.35208 14.5005 2.27245 14.182L1.27245 10.182C1.17199 9.78016 1.41631 9.37296 1.81815 9.27249ZM9.31815 9.27249C9.72 9.17203 10.1272 9.41635 10.2277 9.8182L10.6435 11.4816L11.2978 9.73676C11.4076 9.44403 11.6874 9.2501 12.0001 9.2501C12.3127 9.2501 12.5925 9.44403 12.7023 9.73676L13.3566 11.4816L13.7724 9.8182C13.8729 9.41635 14.2801 9.17203 14.682 9.27249C15.0838 9.37296 15.3281 9.78016 15.2277 10.182L14.2277 14.182C14.148 14.5005 13.8705 14.7302 13.5427 14.7489C13.2149 14.7676 12.9131 14.5709 12.7978 14.2634L12.0001 12.1361L11.2023 14.2634C11.087 14.5709 10.7852 14.7676 10.4574 14.7489C10.1296 14.7302 9.85208 14.5005 9.77245 14.182L8.77245 10.182C8.67199 9.78016 8.91631 9.37296 9.31815 9.27249ZM16.8182 9.27249C17.22 9.17203 17.6272 9.41635 17.7277 9.8182L18.1435 11.4816L18.7978 9.73676C18.9076 9.44403 19.1874 9.2501 19.5001 9.2501C19.8127 9.2501 20.0925 9.44403 20.2023 9.73676L20.8566 11.4816L21.2724 9.8182C21.3729 9.41635 21.7801 9.17203 22.182 9.27249C22.5838 9.37296 22.8281 9.78016 22.7277 10.182L21.7277 14.182C21.648 14.5005 21.3705 14.7302 21.0427 14.7489C20.7149 14.7676 20.4131 14.5709 20.2978 14.2634L19.5001 12.1361L18.7023 14.2634C18.587 14.5709 18.2852 14.7676 17.9574 14.7489C17.6296 14.7302 17.3521 14.5005 17.2724 14.182L16.2724 10.182C16.172 9.78016 16.4163 9.37296 16.8182 9.27249ZM14.5485 16.2792C14.9444 16.401 15.1667 16.8207 15.0449 17.2166C14.7467 18.1863 14.3661 19.1277 13.9078 20.0307C14.5986 19.868 15.2682 19.6163 15.8992 19.2798C17.0986 18.6401 18.1213 17.7139 18.8764 16.5835C19.1065 16.2391 19.5722 16.1464 19.9166 16.3764C20.2611 16.6065 20.3538 17.0723 20.1237 17.4167C19.2313 18.7526 18.0226 19.8473 16.6051 20.6033C15.3895 21.2516 14.0526 21.6337 12.6833 21.7274C12.5896 21.751 12.4915 21.7565 12.3946 21.7428C12.2632 21.7479 12.1316 21.7503 11.9998 21.7501C11.8691 21.7502 11.7386 21.7476 11.6083 21.7424C11.5088 21.7569 11.4081 21.751 11.3121 21.7262C9.94689 21.6303 8.61447 21.2476 7.40293 20.6C5.98846 19.844 4.78253 18.7505 3.89222 17.4164C3.66229 17.0719 3.75519 16.6062 4.09972 16.3763C4.44426 16.1463 4.90995 16.2392 5.13989 16.5838C5.8931 17.7124 6.91333 18.6375 8.10999 19.2771C8.73764 19.6126 9.40364 19.864 10.0909 20.0272C9.63416 19.1266 9.25473 18.1877 8.95721 17.2206C8.83541 16.8247 9.05761 16.4051 9.45351 16.2833C9.84941 16.1615 10.2691 16.3837 10.3909 16.7796C10.7641 17.9926 11.2781 19.1572 11.922 20.2498C11.9479 20.25 11.9737 20.2501 11.9996 20.2501L12.0015 20.2501C12.0271 20.2502 12.0526 20.2501 12.0781 20.2499C12.723 19.1562 13.2377 17.9901 13.6112 16.7756C13.733 16.3797 14.1526 16.1575 14.5485 16.2792ZM11.999 21.5603L11.9986 21.7501C11.999 21.7501 11.9994 21.7501 11.9998 21.7501C12 21.7501 12.0003 21.7501 12.0005 21.7501L12.0004 21.5596"})))),IC=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{d:"M6.90002 11.75C6.90002 12.5784 6.22845 13.25 5.40002 13.25C4.5716 13.25 3.90002 12.5784 3.90002 11.75C3.90002 10.9216 4.5716 10.25 5.40002 10.25C6.22845 10.25 6.90002 10.9216 6.90002 11.75Z"}),t.createElement("path",{d:"M13.5 11.75C13.5 12.5784 12.8285 13.25 12 13.25C11.1716 13.25 10.5 12.5784 10.5 11.75C10.5 10.9216 11.1716 10.25 12 10.25C12.8285 10.25 13.5 10.9216 13.5 11.75Z"}),t.createElement("path",{d:"M20.1 11.75C20.1 12.5784 19.4285 13.25 18.6 13.25C17.7716 13.25 17.1 12.5784 17.1 11.75C17.1 10.9216 17.7716 10.25 18.6 10.25C19.4285 10.25 20.1 10.9216 20.1 11.75Z"})))),sC=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{d:"M11.9999 6.90002C11.1715 6.90002 10.4999 6.22845 10.4999 5.40002C10.4999 4.5716 11.1715 3.90002 11.9999 3.90002C12.8283 3.90002 13.4999 4.5716 13.4999 5.40002C13.4999 6.22845 12.8283 6.90002 11.9999 6.90002Z"}),t.createElement("path",{d:"M11.9999 13.5C11.1715 13.5 10.4999 12.8285 10.4999 12C10.4999 11.1716 11.1715 10.5 11.9999 10.5C12.8283 10.5 13.4999 11.1716 13.4999 12C13.4999 12.8285 12.8283 13.5 11.9999 13.5Z"}),t.createElement("path",{d:"M11.9999 20.1C11.1714 20.1 10.4999 19.4285 10.4999 18.6C10.4999 17.7716 11.1715 17.1 11.9999 17.1C12.8283 17.1 13.4999 17.7716 13.4999 18.6C13.4999 19.4285 12.8283 20.1 11.9999 20.1Z"})))),gC=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12.75 4C12.75 3.58579 12.4142 3.25 12 3.25C11.5858 3.25 11.25 3.58579 11.25 4V14.1893L7.53033 10.4697C7.23744 10.1768 6.76256 10.1768 6.46967 10.4697C6.17678 10.7626 6.17678 11.2374 6.46967 11.5303L11.4697 16.5303C11.5416 16.6022 11.6245 16.6565 11.7129 16.6931C11.8013 16.7298 11.8983 16.75 12 16.75C12.1017 16.75 12.1987 16.7298 12.2871 16.6931C12.3755 16.6565 12.4584 16.6022 12.5303 16.5303L17.5303 11.5303C17.8232 11.2374 17.8232 10.7626 17.5303 10.4697C17.2374 10.1768 16.7626 10.1768 16.4697 10.4697L12.75 14.1893V4ZM4 16.25C4.41421 16.25 4.75 16.5858 4.75 17V19C4.75 19.3315 4.8817 19.6495 5.11612 19.8839C5.35054 20.1183 5.66848 20.25 6 20.25H18C18.3315 20.25 18.6495 20.1183 18.8839 19.8839C19.1183 19.6495 19.25 19.3315 19.25 19V17C19.25 16.5858 19.5858 16.25 20 16.25C20.4142 16.25 20.75 16.5858 20.75 17V19C20.75 19.7293 20.4603 20.4288 19.9445 20.9445C19.4288 21.4603 18.7293 21.75 18 21.75H6C5.27065 21.75 4.57118 21.4603 4.05546 20.9445C3.53973 20.4288 3.25 19.7293 3.25 19V17C3.25 16.5858 3.58579 16.25 4 16.25Z"})))),AC=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M13.9697 4.96967C14.6408 4.29858 15.5509 3.92157 16.5 3.92157C17.4491 3.92157 18.3592 4.29858 19.0303 4.96967C19.7014 5.64075 20.0784 6.55094 20.0784 7.5C20.0784 8.44905 19.7014 9.35924 19.0303 10.0303L8.53033 20.5303C8.38968 20.671 8.19891 20.75 8 20.75H4C3.58579 20.75 3.25 20.4142 3.25 20V16C3.25 15.8011 3.32902 15.6103 3.46967 15.4697L13.9697 4.96967ZM16.5 5.42157C15.9488 5.42157 15.4201 5.64055 15.0303 6.03033L4.75 16.3107V19.25H7.68934L17.9697 8.96967C18.3595 8.57989 18.5784 8.05123 18.5784 7.5C18.5784 6.94876 18.3595 6.42011 17.9697 6.03033C17.5799 5.64055 17.0512 5.42157 16.5 5.42157Z"}),t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12.9697 5.96967C13.2626 5.67677 13.7374 5.67677 14.0303 5.96967L18.0303 9.96967C18.3232 10.2626 18.3232 10.7374 18.0303 11.0303C17.7374 11.3232 17.2626 11.3232 16.9697 11.0303L12.9697 7.03033C12.6768 6.73743 12.6768 6.26256 12.9697 5.96967Z"})))),SC=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M14.8188 4.82172C14.8187 4.82187 14.819 4.82157 14.8188 4.82172L12.7008 6.93978L18.0615 12.3006L20.1669 10.1724L20.1682 10.1711C20.2147 10.1242 20.2409 10.0609 20.2409 9.99482C20.2409 9.92902 20.2149 9.86589 20.1687 9.8191C20.1685 9.81892 20.1689 9.81928 20.1687 9.8191L15.1713 4.82172C15.1712 4.82157 15.1715 4.82188 15.1713 4.82172C15.1245 4.77545 15.0609 4.74902 14.9951 4.74902C14.9292 4.74902 14.8656 4.77544 14.8188 4.82172ZM17.0066 13.367L11.6401 8.00044L4.82197 14.8186C4.82181 14.8187 4.82212 14.8184 4.82197 14.8186C4.77569 14.8654 4.74927 14.929 4.74927 14.9948C4.74927 15.0609 4.7754 15.1242 4.82197 15.1711L4.82598 15.1751L8.81538 19.2498H11.187L17.0066 13.367ZM11.5009 20.7498H19.0001C19.4143 20.7498 19.7501 20.414 19.7501 19.9998C19.7501 19.5856 19.4143 19.2498 19.0001 19.2498H13.297L21.232 11.2286C21.2322 11.2283 21.2325 11.228 21.2328 11.2277C21.5582 10.8999 21.7409 10.4568 21.7409 9.99482C21.7409 9.53249 21.5579 9.08895 21.232 8.76106L16.2304 3.75948L16.2288 3.75791C15.9009 3.43197 15.4574 3.24902 14.9951 3.24902C14.5327 3.24902 14.0892 3.43197 13.7613 3.75791L13.7597 3.75949L3.75973 13.7595L3.75815 13.7611C3.43222 14.089 3.24927 14.5325 3.24927 14.9948C3.24927 15.4561 3.43139 15.8987 3.75594 16.2263C3.75668 16.2271 3.75742 16.2278 3.75815 16.2286L7.96415 20.5245C8.10523 20.6686 8.2984 20.7498 8.50006 20.7498H11.4991C11.4997 20.7498 11.5003 20.7498 11.5009 20.7498Z"})))),TC=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7 3.75C6.66848 3.75 6.35054 3.8817 6.11612 4.11612C5.8817 4.35054 5.75 4.66848 5.75 5V19C5.75 19.3315 5.8817 19.6495 6.11612 19.8839C6.35054 20.1183 6.66848 20.25 7 20.25H17C17.3315 20.25 17.6495 20.1183 17.8839 19.8839C18.1183 19.6495 18.25 19.3315 18.25 19V8.75H15C14.5359 8.75 14.0908 8.56563 13.7626 8.23744C13.4344 7.90925 13.25 7.46413 13.25 7V3.75H7ZM14.75 4.81066L17.1893 7.25H15C14.9337 7.25 14.8701 7.22366 14.8232 7.17678C14.7763 7.12989 14.75 7.0663 14.75 7V4.81066ZM5.05546 3.05546C5.57118 2.53973 6.27065 2.25 7 2.25H14C14.1989 2.25 14.3897 2.32902 14.5303 2.46967L19.5303 7.46967C19.671 7.61032 19.75 7.80109 19.75 8V19C19.75 19.7293 19.4603 20.4288 18.9445 20.9445C18.4288 21.4603 17.7293 21.75 17 21.75H7C6.27065 21.75 5.57118 21.4603 5.05546 20.9445C4.53973 20.4288 4.25 19.7293 4.25 19V5C4.25 4.27065 4.53973 3.57118 5.05546 3.05546ZM12 10.25C12.4142 10.25 12.75 10.5858 12.75 11V14C12.75 14.4142 12.4142 14.75 12 14.75C11.5858 14.75 11.25 14.4142 11.25 14V11C11.25 10.5858 11.5858 10.25 12 10.25ZM11.25 17C11.25 16.5858 11.5858 16.25 12 16.25H12.01C12.4242 16.25 12.76 16.5858 12.76 17C12.76 17.4142 12.4242 17.75 12.01 17.75H12C11.5858 17.75 11.25 17.4142 11.25 17Z"})))),yC=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6 4.75C5.66848 4.75 5.35054 4.8817 5.11612 5.11612C4.8817 5.35054 4.75 5.66848 4.75 6V18C4.75 18.3315 4.8817 18.6495 5.11612 18.8839C5.35054 19.1183 5.66848 19.25 6 19.25H12C12.4142 19.25 12.75 19.5858 12.75 20C12.75 20.4142 12.4142 20.75 12 20.75H6C5.27065 20.75 4.57118 20.4603 4.05546 19.9445C3.53973 19.4288 3.25 18.7293 3.25 18V6C3.25 5.27065 3.53973 4.57118 4.05546 4.05546C4.57118 3.53973 5.27065 3.25 6 3.25H14C14.4142 3.25 14.75 3.58579 14.75 4C14.75 4.41421 14.4142 4.75 14 4.75H6Z"}),t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M18 3.25C18.4142 3.25 18.75 3.58579 18.75 4V19.1893L20.4697 17.4697C20.7626 17.1768 21.2374 17.1768 21.5303 17.4697C21.8232 17.7626 21.8232 18.2374 21.5303 18.5303L18.5303 21.5303C18.4584 21.6022 18.3755 21.6565 18.2871 21.6931C18.2099 21.7251 18.1262 21.7446 18.0386 21.749C18.0258 21.7497 18.0129 21.75 18 21.75C17.8983 21.75 17.8013 21.7298 17.7129 21.6931C17.6245 21.6565 17.5416 21.6022 17.4697 21.5303L14.4697 18.5303C14.1768 18.2374 14.1768 17.7626 14.4697 17.4697C14.7626 17.1768 15.2374 17.1768 15.5303 17.4697L17.25 19.1893V4C17.25 3.58579 17.5858 3.25 18 3.25Z"})))),PC=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4 3.25H8C8.41421 3.25 8.75 3.58579 8.75 4C8.75 4.41421 8.41421 4.75 8 4.75H5.81066L10.5303 9.46967C10.8232 9.76256 10.8232 10.2374 10.5303 10.5303C10.2374 10.8232 9.76256 10.8232 9.46967 10.5303L4.75 5.81066V8C4.75 8.41421 4.41421 8.75 4 8.75C3.58579 8.75 3.25 8.41421 3.25 8V4C3.25 3.58579 3.58579 3.25 4 3.25ZM13.4697 13.4697C13.7626 13.1768 14.2374 13.1768 14.5303 13.4697L19.25 18.1893V16C19.25 15.5858 19.5858 15.25 20 15.25C20.4142 15.25 20.75 15.5858 20.75 16V20C20.75 20.4142 20.4142 20.75 20 20.75H16C15.5858 20.75 15.25 20.4142 15.25 20C15.25 19.5858 15.5858 19.25 16 19.25H18.1893L13.4697 14.5303C13.1768 14.2374 13.1768 13.7626 13.4697 13.4697Z"})))),kC=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6.53033 8.46967C6.82322 8.76256 6.82322 9.23744 6.53033 9.53033L4.81066 11.25H9C9.41421 11.25 9.75 11.5858 9.75 12C9.75 12.4142 9.41421 12.75 9 12.75H4.81066L6.53033 14.4697C6.82322 14.7626 6.82322 15.2374 6.53033 15.5303C6.23744 15.8232 5.76256 15.8232 5.46967 15.5303L2.46967 12.5303C2.17678 12.2374 2.17678 11.7626 2.46967 11.4697L5.46967 8.46967C5.76256 8.17678 6.23744 8.17678 6.53033 8.46967ZM17.4697 8.46967C17.7626 8.17678 18.2374 8.17678 18.5303 8.46967L21.5303 11.4697C21.8232 11.7626 21.8232 12.2374 21.5303 12.5303L18.5303 15.5303C18.2374 15.8232 17.7626 15.8232 17.4697 15.5303C17.1768 15.2374 17.1768 14.7626 17.4697 14.4697L19.1893 12.75H15C14.5858 12.75 14.25 12.4142 14.25 12C14.25 11.5858 14.5858 11.25 15 11.25H19.1893L17.4697 9.53033C17.1768 9.23744 17.1768 8.76256 17.4697 8.46967Z"})))),bC=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M14 4C14 3.58579 14.3358 3.25 14.75 3.25H19.75C20.1642 3.25 20.5 3.58579 20.5 4V9C20.5 9.41421 20.1642 9.75 19.75 9.75C19.3358 9.75 19 9.41421 19 9V5.81066L10.2803 14.5303C9.98744 14.8232 9.51256 14.8232 9.21967 14.5303C8.92678 14.2374 8.92678 13.7626 9.21967 13.4697L17.9393 4.75H14.75C14.3358 4.75 14 4.41421 14 4ZM3.80546 7.05546C4.32118 6.53973 5.02065 6.25 5.75 6.25H10.75C11.1642 6.25 11.5 6.58579 11.5 7C11.5 7.41421 11.1642 7.75 10.75 7.75H5.75C5.41848 7.75 5.10054 7.8817 4.86612 8.11612C4.6317 8.35054 4.5 8.66848 4.5 9V18C4.5 18.3315 4.6317 18.6495 4.86612 18.8839C5.10054 19.1183 5.41848 19.25 5.75 19.25H14.75C15.0815 19.25 15.3995 19.1183 15.6339 18.8839C15.8683 18.6495 16 18.3315 16 18V13C16 12.5858 16.3358 12.25 16.75 12.25C17.1642 12.25 17.5 12.5858 17.5 13V18C17.5 18.7293 17.2103 19.4288 16.6945 19.9445C16.1788 20.4603 15.4793 20.75 14.75 20.75H5.75C5.02065 20.75 4.32118 20.4603 3.80546 19.9445C3.28973 19.4288 3 18.7293 3 18V9C3 8.27065 3.28973 7.57118 3.80546 7.05546Z"})))),DC=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M2.86829 12C5.41108 16.2677 8.46131 18.25 12 18.25C15.5387 18.25 18.5889 16.2677 21.1317 12C18.5889 7.73232 15.5387 5.75 12 5.75C8.46131 5.75 5.41108 7.73232 2.86829 12ZM1.34883 11.6279C4.09715 6.81857 7.63999 4.25 12 4.25C16.36 4.25 19.9028 6.81857 22.6512 11.6279C22.7829 11.8585 22.7829 12.1415 22.6512 12.3721C19.9028 17.1814 16.36 19.75 12 19.75C7.63999 19.75 4.09715 17.1814 1.34883 12.3721C1.21706 12.1415 1.21706 11.8585 1.34883 11.6279ZM12 10.75C11.3096 10.75 10.75 11.3096 10.75 12C10.75 12.6904 11.3096 13.25 12 13.25C12.6904 13.25 13.25 12.6904 13.25 12C13.25 11.3096 12.6904 10.75 12 10.75ZM9.25 12C9.25 10.4812 10.4812 9.25 12 9.25C13.5188 9.25 14.75 10.4812 14.75 12C14.75 13.5188 13.5188 14.75 12 14.75C10.4812 14.75 9.25 13.5188 9.25 12Z"})))),FC=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M2.46967 2.46967C2.76256 2.17678 3.23744 2.17678 3.53033 2.46967L17.8174 16.7568C17.8654 16.7941 17.9095 16.8378 17.9483 16.8876L21.5303 20.4697C21.8232 20.7626 21.8232 21.2374 21.5303 21.5303C21.2374 21.8232 20.7626 21.8232 20.4697 21.5303L17.2465 18.3072C15.6369 19.2662 13.8851 19.7501 12 19.7501C7.63999 19.7501 4.09715 17.1815 1.34883 12.3722C1.21704 12.1416 1.21706 11.8585 1.34887 11.6279C2.57253 9.48713 3.95235 7.78794 5.50007 6.56074L2.46967 3.53033C2.17678 3.23744 2.17678 2.76256 2.46967 2.46967ZM6.56879 7.62945C5.2411 8.64776 4.00403 10.095 2.86833 12.0001C5.41111 16.2678 8.46133 18.2501 12 18.2501C13.4819 18.2501 14.8599 17.904 16.1467 17.2073L13.3414 14.402C12.9342 14.6299 12.4719 14.7526 11.997 14.7524C11.2675 14.7522 10.5681 14.4622 10.0524 13.9462C9.53683 13.4302 9.2473 12.7305 9.24756 12.0011C9.24773 11.5268 9.37037 11.0652 9.59796 10.6586L6.56879 7.62945ZM10.7607 11.8213C10.752 11.8807 10.7476 11.9409 10.7476 12.0016C10.7474 12.3332 10.8791 12.6513 11.1135 12.8859C11.3479 13.1205 11.6659 13.2523 11.9975 13.2524C12.0585 13.2525 12.119 13.248 12.1787 13.2393L10.7607 11.8213ZM11.9973 5.75005C11.1762 5.74715 10.3588 5.86029 9.56927 6.08614C9.17104 6.20006 8.75585 5.96957 8.64192 5.57133C8.528 5.17309 8.75849 4.75791 9.15672 4.64398C10.0817 4.37939 11.0393 4.24678 12.0013 4.25006C16.3607 4.25058 19.9031 6.81912 22.6512 11.6279C22.783 11.8586 22.7829 12.1417 22.6511 12.3723C21.8503 13.7731 20.984 14.9842 20.0478 15.9971C19.7666 16.3013 19.2921 16.32 18.9879 16.0388C18.6838 15.7577 18.6651 15.2832 18.9462 14.979C19.7114 14.1512 20.4404 13.1597 21.1317 12C18.5889 7.73236 15.5387 5.75006 12 5.75006L11.9973 5.75005Z"})))),UC=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6 3.75C5.66848 3.75 5.35054 3.8817 5.11612 4.11612C4.8817 4.35054 4.75 4.66848 4.75 5V19C4.75 19.3315 4.8817 19.6495 5.11612 19.8839C5.35054 20.1183 5.66848 20.25 6 20.25H13C13.4142 20.25 13.75 20.5858 13.75 21C13.75 21.4142 13.4142 21.75 13 21.75H6C5.27065 21.75 4.57118 21.4603 4.05546 20.9445C3.53973 20.4288 3.25 19.7293 3.25 19V5C3.25 4.27065 3.53973 3.57118 4.05546 3.05546C4.57118 2.53973 5.27065 2.25 6 2.25H13C13.1989 2.25 13.3897 2.32902 13.5303 2.46967L18.5303 7.46967C18.671 7.61032 18.75 7.80109 18.75 8V11C18.75 11.4142 18.4142 11.75 18 11.75C17.5858 11.75 17.25 11.4142 17.25 11V8.75H14C13.5359 8.75 13.0908 8.56563 12.7626 8.23744C12.4344 7.90925 12.25 7.46413 12.25 7V3.75H6ZM13.75 4.81066L16.1893 7.25H14C13.9337 7.25 13.8701 7.22366 13.8232 7.17678C13.7763 7.12989 13.75 7.0663 13.75 7V4.81066ZM18.5 14.75C16.9812 14.75 15.75 15.9812 15.75 17.5C15.75 19.0188 16.9812 20.25 18.5 20.25C20.0188 20.25 21.25 19.0188 21.25 17.5C21.25 15.9812 20.0188 14.75 18.5 14.75ZM14.25 17.5C14.25 15.1528 16.1528 13.25 18.5 13.25C20.8472 13.25 22.75 15.1528 22.75 17.5C22.75 19.8472 20.8472 21.75 18.5 21.75C16.1528 21.75 14.25 19.8472 14.25 17.5ZM18.1111 15.5833C18.5253 15.5833 18.8611 15.9191 18.8611 16.3333V17.1389H19.6667C20.0809 17.1389 20.4167 17.4747 20.4167 17.8889C20.4167 18.3031 20.0809 18.6389 19.6667 18.6389H18.1111C17.6969 18.6389 17.3611 18.3031 17.3611 17.8889V16.3333C17.3611 15.9191 17.6969 15.5833 18.1111 15.5833Z"})))),OC=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M8.1056 4C7.49166 4 6.90442 4.24645 6.47264 4.68263C6.04112 5.11855 5.80005 5.70819 5.80005 6.32143V7H5.1056C4.49166 7 3.90442 7.24645 3.47264 7.68263C3.04112 8.11855 2.80005 8.70819 2.80005 9.32143V17.1786C2.80005 17.7918 3.04112 18.3814 3.47264 18.8174C3.90442 19.2536 4.49166 19.5 5.1056 19.5H15.9945C16.6084 19.5 17.1957 19.2536 17.6275 18.8174C18.059 18.3814 18.3 17.7918 18.3 17.1786V16.5H18.9945C19.6084 16.5 20.1957 16.2536 20.6275 15.8174C21.059 15.3814 21.3 14.7918 21.3 14.1786V8.67857C21.3 8.48098 21.2221 8.29137 21.0831 8.15094L17.1942 4.22237C17.0533 4.08007 16.8614 4 16.6612 4H8.1056ZM16.8 16.5V17.1786C16.8 17.3989 16.7133 17.6086 16.5614 17.7621C16.4097 17.9153 16.2057 18 15.9945 18H5.1056C4.89443 18 4.69035 17.9153 4.53868 17.7621C4.38675 17.6086 4.30005 17.3989 4.30005 17.1786V9.32143C4.30005 9.10113 4.38675 8.89137 4.53868 8.73789C4.69035 8.58467 4.89443 8.5 5.1056 8.5H5.80005V14.1786C5.80005 14.7918 6.04112 15.3814 6.47264 15.8174C6.90442 16.2536 7.49166 16.5 8.1056 16.5H16.8ZM7.30005 6.32143C7.30005 6.10113 7.38675 5.89137 7.53868 5.73789C7.69035 5.58467 7.89443 5.5 8.1056 5.5H15.9112V7.89286C15.9112 8.29771 16.0703 8.6875 16.356 8.97607C16.6419 9.2649 17.0313 9.42857 17.4389 9.42857H19.8V14.1786C19.8 14.3989 19.7133 14.6086 19.5614 14.7621C19.4098 14.9153 19.2057 15 18.9945 15H8.1056C7.89443 15 7.69035 14.9153 7.53868 14.7621C7.38675 14.6086 7.30005 14.3989 7.30005 14.1786V6.32143ZM18.7523 7.92857L17.4112 6.57374V7.89286C17.4112 7.90477 17.4159 7.91468 17.422 7.92081C17.4278 7.92668 17.434 7.92857 17.4389 7.92857H18.7523Z"})))),JC=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M5.25177 4.29226C5.33154 4.26429 5.41546 4.25 5.49999 4.25H18.5C18.5845 4.25 18.6684 4.26429 18.7482 4.29226C19.0002 4.38065 19.2287 4.52556 19.4161 4.71588C19.6034 4.90619 19.7448 5.13688 19.8292 5.39025C19.9137 5.64362 19.939 5.91297 19.9033 6.17765C19.8676 6.44234 19.7718 6.69533 19.6232 6.91726C19.6026 6.94799 19.5798 6.97715 19.5549 7.0045L14.75 12.29V19C14.75 19.2841 14.5895 19.5438 14.3354 19.6708C14.0813 19.7979 13.7772 19.7704 13.55 19.6L9.54999 16.6C9.36113 16.4584 9.24999 16.2361 9.24999 16V12.29L4.44503 7.0045C4.42016 6.97715 4.39734 6.94799 4.37677 6.91726C4.22818 6.69533 4.13236 6.44234 4.09665 6.17765C4.06094 5.91297 4.08628 5.64363 4.17074 5.39025C4.2552 5.13687 4.39653 4.90619 4.58391 4.71588C4.77129 4.52556 4.99974 4.38065 5.25177 4.29226ZM5.67282 5.75C5.66583 5.7557 5.65914 5.7618 5.65279 5.76825C5.62602 5.79544 5.60583 5.8284 5.59376 5.86459C5.5817 5.90079 5.57808 5.93927 5.58318 5.97708C5.58662 6.00257 5.59396 6.02731 5.60489 6.05044L10.5549 11.4955C10.6804 11.6335 10.75 11.8134 10.75 12V15.625L13.25 17.5V12C13.25 11.8134 13.3195 11.6335 13.445 11.4955L18.3951 6.05044C18.406 6.02731 18.4134 6.00257 18.4168 5.97708C18.4219 5.93927 18.4183 5.90079 18.4062 5.86459C18.3941 5.82839 18.374 5.79544 18.3472 5.76825C18.3408 5.7618 18.3341 5.7557 18.3272 5.75H5.67282Z"})))),WC=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M5 4.75C4.66848 4.75 4.35054 4.8817 4.11612 5.11612C3.8817 5.35054 3.75 5.66848 3.75 6V17C3.75 17.3315 3.8817 17.6495 4.11612 17.8839C4.35054 18.1183 4.66848 18.25 5 18.25H19C19.3315 18.25 19.6495 18.1183 19.8839 17.8839C20.1183 17.6495 20.25 17.3315 20.25 17V9C20.25 8.66848 20.1183 8.35054 19.8839 8.11612C19.6495 7.8817 19.3315 7.75 19 7.75H12C11.8011 7.75 11.6103 7.67098 11.4697 7.53033L8.68934 4.75H5ZM3.05546 4.05546C3.57118 3.53973 4.27065 3.25 5 3.25H9C9.19891 3.25 9.38968 3.32902 9.53033 3.46967L12.3107 6.25H19C19.7293 6.25 20.4288 6.53973 20.9445 7.05546C21.4603 7.57118 21.75 8.27065 21.75 9V17C21.75 17.7293 21.4603 18.4288 20.9445 18.9445C20.4288 19.4603 19.7293 19.75 19 19.75H5C4.27065 19.75 3.57118 19.4603 3.05546 18.9445C2.53973 18.4288 2.25 17.7293 2.25 17V6C2.25 5.27065 2.53973 4.57118 3.05546 4.05546Z"})))),jC=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M19 19.25C19.1381 19.25 19.25 19.1381 19.25 19L19.25 16.75L4.75 16.75L4.75 19C4.75 19.1381 4.86193 19.25 5 19.25L19 19.25ZM3.25 19C3.25 19.9665 4.0335 20.75 5 20.75L19 20.75C19.9665 20.75 20.75 19.9665 20.75 19L20.75 5C20.75 4.0335 19.9665 3.25 19 3.25L5 3.25C4.0335 3.25 3.25 4.0335 3.25 5L3.25 19ZM4.75 15.25L19.25 15.25L19.25 5C19.25 4.86193 19.1381 4.75 19 4.75L5 4.75C4.86193 4.75 4.75 4.86193 4.75 5L4.75 15.25Z"})))),zC=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M9.65527 4.84484C8.95951 4.07178 8.20923 3.73771 7.51306 3.74984L7.5 3.75007C7.03587 3.75007 6.59075 3.93433 6.26256 4.26252C5.93437 4.59071 5.75 5.03583 5.75 5.49995C5.75 5.96408 5.93437 6.4092 6.26256 6.73739C6.59075 7.06558 7.03587 7.24995 7.5 7.24995C7.50295 7.24995 7.5059 7.24997 7.50884 7.25001H11.0002C10.6592 6.26394 10.1939 5.44328 9.65527 4.84484ZM11.25 8.75001V11.25H4C3.86193 11.25 3.75 11.1381 3.75 11V9.00001C3.75 8.86193 3.86193 8.75001 4 8.75001H11.25ZM4.25 12.75H4C3.0335 12.75 2.25 11.9665 2.25 11V9.00001C2.25 8.03351 3.0335 7.25001 4 7.25001H4.76141C4.43004 6.73144 4.25 6.12498 4.25 5.49995C4.25 4.638 4.59241 3.81135 5.2019 3.20186C5.80984 2.59392 6.63384 2.2517 7.49342 2.24996C8.72414 2.23069 9.86213 2.83242 10.7702 3.84139C11.2484 4.37275 11.6608 5.01284 12 5.73103C12.3392 5.01284 12.7516 4.37275 13.2298 3.84139C14.1379 2.83242 15.2759 2.23069 16.5066 2.24996C17.3662 2.2517 18.1902 2.59392 18.7981 3.20186C19.4076 3.81135 19.75 4.638 19.75 5.49995C19.75 6.12498 19.57 6.73144 19.2386 7.25001H20C20.9665 7.25001 21.75 8.03351 21.75 9.00001V11C21.75 11.9665 20.9665 12.75 20 12.75H19.75V19C19.75 19.7294 19.4603 20.4288 18.9445 20.9445C18.4288 21.4603 17.7293 21.75 17 21.75H7C6.27065 21.75 5.57118 21.4603 5.05546 20.9445C4.53973 20.4288 4.25 19.7294 4.25 19V12.75ZM11.25 20.25H7C6.66848 20.25 6.35054 20.1183 6.11612 19.8839C5.8817 19.6495 5.75 19.3315 5.75 19V12.75H11.25V20.25ZM12.75 20.25H17C17.3315 20.25 17.6495 20.1183 17.8839 19.8839C18.1183 19.6495 18.25 19.3315 18.25 19V12.75H12.75V20.25ZM12.75 11.25V8.75001H20C20.1381 8.75001 20.25 8.86193 20.25 9.00001V11C20.25 11.1381 20.1381 11.25 20 11.25H12.75ZM16.4912 7.25001C16.4941 7.24997 16.497 7.24995 16.5 7.24995C16.9641 7.24995 17.4092 7.06558 17.7374 6.73739C18.0656 6.4092 18.25 5.96408 18.25 5.49995C18.25 5.03583 18.0656 4.59071 17.7374 4.26252C17.4092 3.93433 16.9641 3.74995 16.5 3.74995H16.4869C15.7908 3.73783 15.0405 4.07178 14.3447 4.84484C13.8061 5.44328 13.3408 6.26394 12.9998 7.25001H16.4912Z"})))),XC=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3.25 5C3.25 4.0335 4.0335 3.25 5 3.25C5.9665 3.25 6.75 4.0335 6.75 5C6.75 5.9665 5.9665 6.75 5 6.75C4.0335 6.75 3.25 5.9665 3.25 5ZM10.25 5C10.25 4.0335 11.0335 3.25 12 3.25C12.9665 3.25 13.75 4.0335 13.75 5C13.75 5.9665 12.9665 6.75 12 6.75C11.0335 6.75 10.25 5.9665 10.25 5ZM17.25 5C17.25 4.0335 18.0335 3.25 19 3.25C19.9665 3.25 20.75 4.0335 20.75 5C20.75 5.9665 19.9665 6.75 19 6.75C18.0335 6.75 17.25 5.9665 17.25 5ZM3.25 12C3.25 11.0335 4.0335 10.25 5 10.25C5.9665 10.25 6.75 11.0335 6.75 12C6.75 12.9665 5.9665 13.75 5 13.75C4.0335 13.75 3.25 12.9665 3.25 12ZM10.25 12C10.25 11.0335 11.0335 10.25 12 10.25C12.9665 10.25 13.75 11.0335 13.75 12C13.75 12.9665 12.9665 13.75 12 13.75C11.0335 13.75 10.25 12.9665 10.25 12ZM17.25 12C17.25 11.0335 18.0335 10.25 19 10.25C19.9665 10.25 20.75 11.0335 20.75 12C20.75 12.9665 19.9665 13.75 19 13.75C18.0335 13.75 17.25 12.9665 17.25 12ZM3.25 19C3.25 18.0335 4.0335 17.25 5 17.25C5.9665 17.25 6.75 18.0335 6.75 19C6.75 19.9665 5.9665 20.75 5 20.75C4.0335 20.75 3.25 19.9665 3.25 19ZM10.25 19C10.25 18.0335 11.0335 17.25 12 17.25C12.9665 17.25 13.75 18.0335 13.75 19C13.75 19.9665 12.9665 20.75 12 20.75C11.0335 20.75 10.25 19.9665 10.25 19ZM17.25 19C17.25 18.0335 18.0335 17.25 19 17.25C19.9665 17.25 20.75 18.0335 20.75 19C20.75 19.9665 19.9665 20.75 19 20.75C18.0335 20.75 17.25 19.9665 17.25 19Z"})))),_C=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7.25 5C7.25 4.0335 8.0335 3.25 9 3.25C9.9665 3.25 10.75 4.0335 10.75 5C10.75 5.9665 9.9665 6.75 9 6.75C8.0335 6.75 7.25 5.9665 7.25 5ZM13.25 5C13.25 4.0335 14.0335 3.25 15 3.25C15.9665 3.25 16.75 4.0335 16.75 5C16.75 5.9665 15.9665 6.75 15 6.75C14.0335 6.75 13.25 5.9665 13.25 5ZM7.25 12C7.25 11.0335 8.0335 10.25 9 10.25C9.9665 10.25 10.75 11.0335 10.75 12C10.75 12.9665 9.9665 13.75 9 13.75C8.0335 13.75 7.25 12.9665 7.25 12ZM13.25 12C13.25 11.0335 14.0335 10.25 15 10.25C15.9665 10.25 16.75 11.0335 16.75 12C16.75 12.9665 15.9665 13.75 15 13.75C14.0335 13.75 13.25 12.9665 13.25 12ZM7.25 19C7.25 18.0335 8.0335 17.25 9 17.25C9.9665 17.25 10.75 18.0335 10.75 19C10.75 19.9665 9.9665 20.75 9 20.75C8.0335 20.75 7.25 19.9665 7.25 19ZM13.25 19C13.25 18.0335 14.0335 17.25 15 17.25C15.9665 17.25 16.75 18.0335 16.75 19C16.75 19.9665 15.9665 20.75 15 20.75C14.0335 20.75 13.25 19.9665 13.25 19Z"})))),GC=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M5 4.75C4.86193 4.75 4.75 4.86193 4.75 5V7.25H19.25V5C19.25 4.86193 19.1381 4.75 19 4.75H5ZM20.75 5C20.75 4.0335 19.9665 3.25 19 3.25H5C4.0335 3.25 3.25 4.0335 3.25 5V19C3.25 19.9665 4.0335 20.75 5 20.75H19C19.9665 20.75 20.75 19.9665 20.75 19V5ZM19.25 8.75H4.75V19C4.75 19.1381 4.86193 19.25 5 19.25H19C19.1381 19.25 19.25 19.1381 19.25 19V8.75Z"})))),NC=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 4.75C10.0772 4.75 8.23311 5.51384 6.87348 6.87348C5.51384 8.23311 4.75 10.0772 4.75 12V12.5499C5.12503 12.3581 5.54989 12.25 6 12.25C7.51878 12.25 8.75 13.4812 8.75 15V17C8.75 18.5188 7.51878 19.75 6 19.75C4.48122 19.75 3.25 18.5188 3.25 17V12C3.25 9.67936 4.17187 7.45376 5.81282 5.81282C7.45376 4.17187 9.67936 3.25 12 3.25C14.3206 3.25 16.5462 4.17187 18.1872 5.81282C19.8281 7.45376 20.75 9.67936 20.75 12V17C20.75 18.2948 19.8551 19.3806 18.6501 19.6727C18.3754 20.5779 17.5631 21.2996 16.5781 21.7921C15.3248 22.4188 13.6777 22.75 12 22.75C11.5858 22.75 11.25 22.4142 11.25 22C11.25 21.5858 11.5858 21.25 12 21.25C13.5049 21.25 14.91 20.9491 15.9072 20.4505C16.4874 20.1604 16.8396 19.8543 17.0368 19.5766C15.9931 19.1863 15.25 18.1799 15.25 17V15C15.25 13.4812 16.4812 12.25 18 12.25C18.4501 12.25 18.875 12.3581 19.25 12.5499V12C19.25 10.0772 18.4862 8.23311 17.1265 6.87348C15.7669 5.51384 13.9228 4.75 12 4.75ZM19.25 15C19.25 14.3096 18.6904 13.75 18 13.75C17.3096 13.75 16.75 14.3096 16.75 15V17C16.75 17.6904 17.3096 18.25 18 18.25C18.6904 18.25 19.25 17.6904 19.25 17V15ZM4.75 15V17C4.75 17.6904 5.30964 18.25 6 18.25C6.69036 18.25 7.25 17.6904 7.25 17V15C7.25 14.3096 6.69036 13.75 6 13.75C5.30964 13.75 4.75 14.3096 4.75 15Z"})))),KC=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6.7051 4.93648C7.26449 4.7595 7.85446 4.70027 8.43787 4.7625C9.02128 4.82474 9.58548 5.0071 10.095 5.29809C10.4165 5.48178 10.7119 5.70623 10.9741 5.96524L8.17656 8.76275C7.84849 9.09092 7.66418 9.53596 7.66418 10C7.66418 10.464 7.84857 10.9092 8.17664 11.2373L8.71964 11.7803C9.70253 12.7632 11.2974 12.7632 12.2803 11.7803L13.2803 10.7803C13.7364 10.3242 14.355 10.068 15 10.068C15.645 10.068 16.2636 10.3243 16.7196 10.7803L18.4729 12.5336L17.0267 13.966L15.5303 12.4697C15.2374 12.1768 14.7626 12.1768 14.4697 12.4697C14.1768 12.7626 14.1768 13.2374 14.4697 13.5303L15.9609 15.0215L14.5146 16.4539L13.0303 14.9697C12.7374 14.6768 12.2626 14.6768 11.9697 14.9697C11.6768 15.2626 11.6768 15.7374 11.9697 16.0303L13.4488 17.5095L12.0001 18.9444L5.02782 12.0391L5.02784 12.039L5.02311 12.0344C4.60262 11.6252 4.2714 11.1334 4.05032 10.59C3.82924 10.0465 3.72308 9.46313 3.73853 8.87662C3.75398 8.2901 3.89071 7.71314 4.14009 7.18206C4.38948 6.65098 4.74612 6.17729 5.18757 5.79081C5.62902 5.40434 6.14571 5.11346 6.7051 4.93648ZM19.4907 11.43C19.6699 11.1712 19.8203 10.8925 19.9385 10.5989C20.1571 10.0564 20.2613 9.47462 20.2447 8.88997C20.228 8.30532 20.0909 7.73039 19.8419 7.20117C19.5928 6.67196 19.2372 6.19984 18.7973 5.81437C18.3574 5.42891 17.8427 5.13839 17.2854 4.961C16.728 4.78361 16.1401 4.72317 15.5583 4.78346C14.9765 4.84375 14.4135 5.02347 13.9043 5.31138C13.3952 5.59929 12.951 5.98918 12.5995 6.45667C12.5619 6.50669 12.5186 6.55125 12.4709 6.58971L9.23738 9.82325C9.19056 9.87012 9.16418 9.93375 9.16418 10C9.16418 10.0663 9.19048 10.1298 9.2373 10.1767L9.7803 10.7197C10.1774 11.1168 10.8225 11.1168 11.2196 10.7197L12.2196 9.71967C12.957 8.98229 13.9571 8.56803 15 8.56803C16.0428 8.56803 17.0429 8.98228 17.7803 9.71967L19.4907 11.43ZM12.5278 20.5328L19.9677 13.1644C19.9876 13.1484 20.007 13.1313 20.0256 13.113C20.5902 12.5585 21.0342 11.8934 21.3299 11.1594C21.6256 10.4254 21.7666 9.63831 21.7441 8.84731C21.7216 8.05632 21.536 7.27848 21.1991 6.56248C20.8621 5.84648 20.381 5.20773 19.7859 4.68622C19.1907 4.1647 18.4943 3.77165 17.7403 3.53165C16.9862 3.29165 16.1908 3.20988 15.4037 3.29145C14.6166 3.37302 13.8548 3.61617 13.166 4.0057C12.7422 4.24532 12.3517 4.53717 12.0032 4.87355C11.6548 4.53352 11.2638 4.23825 10.8389 3.99559C10.1496 3.60188 9.3863 3.35517 8.59698 3.27097C7.80766 3.18677 7.00946 3.26691 6.25264 3.50635C5.49582 3.74579 4.79677 4.13933 4.19952 4.66221C3.60226 5.18509 3.11974 5.82596 2.78234 6.54448C2.44493 7.263 2.25996 8.0436 2.23905 8.83712C2.21815 9.63064 2.36178 10.4199 2.66089 11.1552C2.95956 11.8894 3.40682 12.5539 3.97455 13.107L3.97701 13.1094L11.4723 20.5328C11.7646 20.8223 12.2355 20.8223 12.5278 20.5328Z"})))),YC=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 3.75C7.44365 3.75 3.75 7.44365 3.75 12C3.75 16.5563 7.44365 20.25 12 20.25C16.5563 20.25 20.25 16.5563 20.25 12C20.25 7.44365 16.5563 3.75 12 3.75ZM2.25 12C2.25 6.61522 6.61522 2.25 12 2.25C17.3848 2.25 21.75 6.61522 21.75 12C21.75 17.3848 17.3848 21.75 12 21.75C6.61522 21.75 2.25 17.3848 2.25 12ZM11.4346 6.31004C12.1055 6.17314 12.8016 6.27204 13.4089 6.58932L13.4116 6.59074C14.0173 6.91037 14.4974 7.42629 14.7778 8.05316C15.0582 8.6798 15.1241 9.38318 14.9657 10.0516C14.8073 10.7201 14.4329 11.3179 13.8992 11.7478C13.5634 12.0182 13.1769 12.2121 12.766 12.3194L12.766 13C12.766 13.4142 12.4302 13.75 12.016 13.75C11.6018 13.75 11.266 13.4142 11.266 13L11.266 11.6666C11.266 11.2533 11.6003 10.9179 12.0136 10.9166C12.3547 10.9155 12.6874 10.7978 12.9583 10.5796C13.2296 10.3611 13.4236 10.054 13.5061 9.7057C13.5887 9.35728 13.5541 8.99081 13.4087 8.66579C13.2635 8.34144 13.0175 8.07918 12.7129 7.91806C12.4103 7.76042 12.0658 7.71214 11.7345 7.77976C11.4024 7.84752 11.0997 8.02843 10.8772 8.29658C10.6126 8.61532 10.1398 8.65925 9.82106 8.39471C9.50232 8.13018 9.45839 7.65734 9.72293 7.3386C10.1611 6.81066 10.7638 6.44691 11.4346 6.31004ZM12 15.25C12.4142 15.25 12.75 15.5858 12.75 16V16.04C12.75 16.4542 12.4142 16.79 12 16.79C11.5858 16.79 11.25 16.4542 11.25 16.04V16C11.25 15.5858 11.5858 15.25 12 15.25Z"})))),qC=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M8.08961 4.0956C10.0932 3.02269 12.4216 2.72496 14.6307 3.25921C16.8397 3.79346 18.7748 5.1223 20.0667 6.99219C21.3585 8.86208 21.9168 11.1421 21.6349 13.3973C21.353 15.6525 20.2507 17.725 18.5383 19.2194C16.8259 20.7137 14.6233 21.5254 12.3506 21.4994C10.078 21.4734 7.89454 20.6117 6.21673 19.0786C4.53891 17.5456 3.48423 15.4484 3.25392 13.1874C3.21194 12.7753 3.51197 12.4072 3.92405 12.3652C4.33614 12.3233 4.70422 12.6233 4.7462 13.0354C4.93916 14.9298 5.82281 16.6868 7.22855 17.9713C8.63428 19.2558 10.4637 19.9777 12.3678 19.9995C14.2719 20.0212 16.1173 19.3412 17.552 18.0892C18.9867 16.8372 19.9103 15.1008 20.1464 13.2113C20.3826 11.3218 19.9149 9.41147 18.8325 7.84481C17.7502 6.27814 16.1289 5.16479 14.2781 4.71718C12.4272 4.26956 10.4764 4.51901 8.79772 5.41794C7.44561 6.14199 6.34633 7.24658 5.62839 8.58361H8.72228C9.13649 8.58361 9.47228 8.91939 9.47228 9.33361C9.47228 9.74782 9.13649 10.0836 8.72228 10.0836H4.48963C4.47805 10.0839 4.46644 10.0839 4.4548 10.0836H4.00006C3.58584 10.0836 3.25006 9.74782 3.25006 9.33361V4.61139C3.25006 4.19717 3.58584 3.86139 4.00006 3.86139C4.41427 3.86139 4.75006 4.19717 4.75006 4.61139V7.1337C5.58912 5.86995 6.73269 4.82222 8.08961 4.0956ZM12.4528 8.27753C12.867 8.27753 13.2028 8.61332 13.2028 9.02753V12.4946L14.872 14.1639C15.1649 14.4568 15.1649 14.9316 14.872 15.2245C14.5792 15.5174 14.1043 15.5174 13.8114 15.2245L11.9225 13.3356C11.7818 13.195 11.7028 13.0042 11.7028 12.8053V9.02753C11.7028 8.61332 12.0386 8.27753 12.4528 8.27753Z"})))),QC=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M11.4697 2.46967C11.7626 2.17678 12.2375 2.17678 12.5304 2.46967L21.5304 11.4697C21.7449 11.6842 21.809 12.0068 21.6929 12.287C21.5768 12.5673 21.3034 12.75 21 12.75H19.75V19C19.75 19.7293 19.4603 20.4288 18.9446 20.9445C18.4288 21.4603 17.7294 21.75 17 21.75H7.00002C6.27068 21.75 5.5712 21.4603 5.05548 20.9445C4.53975 20.4288 4.25002 19.7293 4.25002 19V12.75H3.00002C2.69668 12.75 2.4232 12.5673 2.30711 12.287C2.19103 12.0068 2.25519 11.6842 2.46969 11.4697L11.4697 2.46967ZM9.75002 20.25H14.25V15C14.25 14.6685 14.1183 14.3505 13.8839 14.1161C13.6495 13.8817 13.3315 13.75 13 13.75H11C10.6685 13.75 10.3506 13.8817 10.1161 14.1161C9.88172 14.3505 9.75002 14.6685 9.75002 15V20.25ZM15.75 20.25V15C15.75 14.2707 15.4603 13.5712 14.9446 13.0555C14.4288 12.5397 13.7294 12.25 13 12.25H11C10.2707 12.25 9.5712 12.5397 9.05548 13.0555C8.53975 13.5712 8.25002 14.2707 8.25002 15V20.25H7.00002C6.6685 20.25 6.35056 20.1183 6.11614 19.8839C5.88172 19.6495 5.75002 19.3315 5.75002 19V12C5.75002 11.5858 5.41424 11.25 5.00002 11.25H4.81068L12 4.06066L19.1894 11.25H19C18.5858 11.25 18.25 11.5858 18.25 12V19C18.25 19.3315 18.1183 19.6495 17.8839 19.8839C17.6495 20.1183 17.3315 20.25 17 20.25H15.75Z"})))),$C=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M2.25 11.75C2.25 6.36522 6.61522 2 12 2C17.3848 2 21.75 6.36522 21.75 11.75C21.75 17.1348 17.3848 21.5 12 21.5C6.61522 21.5 2.25 17.1348 2.25 11.75ZM11.25 7.75C11.25 7.33579 11.5858 7 12 7H12.01C12.4242 7 12.76 7.33579 12.76 7.75C12.76 8.16421 12.4242 8.5 12.01 8.5H12C11.5858 8.5 11.25 8.16421 11.25 7.75ZM10.25 11.75C10.25 11.3358 10.5858 11 11 11H12C12.4142 11 12.75 11.3358 12.75 11.75V15H13C13.4142 15 13.75 15.3358 13.75 15.75C13.75 16.1642 13.4142 16.5 13 16.5H12C11.5858 16.5 11.25 16.1642 11.25 15.75V12.5H11C10.5858 12.5 10.25 12.1642 10.25 11.75Z"})))),Ce=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 3.5C7.44365 3.5 3.75 7.19365 3.75 11.75C3.75 16.3063 7.44365 20 12 20C16.5563 20 20.25 16.3063 20.25 11.75C20.25 7.19365 16.5563 3.5 12 3.5ZM2.25 11.75C2.25 6.36522 6.61522 2 12 2C17.3848 2 21.75 6.36522 21.75 11.75C21.75 17.1348 17.3848 21.5 12 21.5C6.61522 21.5 2.25 17.1348 2.25 11.75ZM11.25 7.75C11.25 7.33579 11.5858 7 12 7H12.01C12.4242 7 12.76 7.33579 12.76 7.75C12.76 8.16421 12.4242 8.5 12.01 8.5H12C11.5858 8.5 11.25 8.16421 11.25 7.75ZM10.25 11.75C10.25 11.3358 10.5858 11 11 11H12C12.4142 11 12.75 11.3358 12.75 11.75V15H13C13.4142 15 13.75 15.3358 13.75 15.75C13.75 16.1642 13.4142 16.5 13 16.5H12C11.5858 16.5 11.25 16.1642 11.25 15.75V12.5H11C10.5858 12.5 10.25 12.1642 10.25 11.75Z"})))),ee=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M13.4204 5.00117H10.5306C10.1249 5.00117 9.79595 5.33693 9.79595 5.75111C9.79595 6.16529 10.1249 6.50105 10.5306 6.50105H12.5003L9.48253 17.4991L7.59136 17.5004C7.1856 17.5007 6.8569 17.8367 6.85718 18.2509C6.85746 18.6651 7.18662 19.0006 7.59238 19.0003L9.99207 18.9986C10.0242 19.0008 10.0561 19.0007 10.0877 18.9986L12.9792 19.0003C13.385 19.0005 13.7141 18.665 13.7143 18.2508C13.7146 17.8366 13.3858 17.5007 12.98 17.5004L11.0084 17.4993L14.0262 6.50105H16.4082C16.814 6.50105 17.1429 6.16529 17.1429 5.75111C17.1429 5.33693 16.814 5.00117 16.4082 5.00117H13.5176C13.485 4.99894 13.4525 4.99898 13.4204 5.00117Z"})))),te=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{d:"M7.00012 8C7.00012 7.44772 7.44784 7 8.00012 7H16.019C16.5713 7 17.019 7.44772 17.019 8C17.019 8.55228 16.5713 9 16.019 9L8.00012 9C7.44784 9 7.00012 8.55229 7.00012 8Z"}),t.createElement("path",{d:"M3.25 16C3.25 15.5858 3.58579 15.25 4 15.25L20 15.25C20.4142 15.25 20.75 15.5858 20.75 16C20.75 16.4142 20.4142 16.75 20 16.75L4 16.75C3.58579 16.75 3.25 16.4142 3.25 16Z"}),t.createElement("path",{d:"M8.00012 11C7.44784 11 7.00012 11.4477 7.00012 12C7.00012 12.5523 7.44784 13 8.00012 13L16.019 13C16.5713 13 17.019 12.5523 17.019 12C17.019 11.4477 16.5713 11 16.019 11L8.00012 11Z"})))),ne=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{d:"M16.0096 9C16.5619 9 17.0096 8.55228 17.0096 8C17.0096 7.44772 16.5619 7 16.0096 7H8.00964C7.45736 7 7.00964 7.44772 7.00964 8C7.00964 8.55229 7.45736 9 8.00964 9L16.0096 9Z"}),t.createElement("path",{d:"M20.75 11.8618C20.75 12.276 20.4142 12.6118 20 12.6118L4 12.6118C3.58579 12.6118 3.25 12.276 3.25 11.8618C3.25 11.4476 3.58579 11.1118 4 11.1118L20 11.1118C20.4142 11.1118 20.75 11.4476 20.75 11.8618Z"}),t.createElement("path",{d:"M17.0096 15.8623C17.0096 16.4146 16.5619 16.8623 16.0096 16.8623L8.00964 16.8623C7.45736 16.8623 7.00964 16.4146 7.00964 15.8623C7.00964 15.31 7.45736 14.8623 8.00964 14.8623L16.0096 14.8623C16.5619 14.8623 17.0096 15.31 17.0096 15.8623Z"})))),re=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{d:"M4 4C3.58579 4 3.25 4.33579 3.25 4.75C3.25 5.16421 3.58579 5.5 4 5.5L20 5.5C20.4142 5.5 20.75 5.16422 20.75 4.75C20.75 4.33579 20.4142 4 20 4H4Z"}),t.createElement("path",{d:"M4 18.6484C3.58579 18.6484 3.25 18.9842 3.25 19.3984C3.25 19.8127 3.58579 20.1484 4 20.1484L20 20.1484C20.4142 20.1484 20.75 19.8127 20.75 19.3984C20.75 18.9842 20.4142 18.6484 20 18.6484L4 18.6484Z"}),t.createElement("path",{d:"M7 9.51904C7 8.96676 7.44771 8.51904 8 8.51904L16.0189 8.51904C16.5712 8.51904 17.0189 8.96676 17.0189 9.51904C17.0189 10.0713 16.5712 10.519 16.0189 10.519L8 10.519C7.44772 10.519 7 10.0713 7 9.51904Z"}),t.createElement("path",{d:"M8 13.333C7.44771 13.333 7 13.7807 7 14.333C7 14.8853 7.44772 15.333 8 15.333L16.0189 15.333C16.5712 15.333 17.0189 14.8853 17.0189 14.333C17.0189 13.7807 16.5712 13.333 16.0189 13.333L8 13.333Z"})))),le=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{d:"M4 4C3.58579 4 3.25 4.33579 3.25 4.75C3.25 5.16421 3.58579 5.5 4 5.5L20 5.5C20.4142 5.5 20.75 5.16422 20.75 4.75C20.75 4.33579 20.4142 4 20 4H4Z"}),t.createElement("path",{d:"M4 18.6484C3.58579 18.6484 3.25 18.9842 3.25 19.3984C3.25 19.8127 3.58579 20.1484 4 20.1484L20 20.1484C20.4142 20.1484 20.75 19.8127 20.75 19.3984C20.75 18.9842 20.4142 18.6484 20 18.6484L4 18.6484Z"}),t.createElement("path",{d:"M7 10.5742C7 10.0219 7.44771 9.57422 8 9.57422L16.0189 9.57422C16.5712 9.57422 17.0189 10.0219 17.0189 10.5742C17.0189 11.1265 16.5712 11.5742 16.0189 11.5742L8 11.5742C7.44772 11.5742 7 11.1265 7 10.5742Z"}),t.createElement("path",{d:"M8 12.5742C7.44771 12.5742 7 13.0219 7 13.5742C7 14.1265 7.44772 14.5742 8 14.5742L16.0189 14.5742C16.5712 14.5742 17.0189 14.1265 17.0189 13.5742C17.0189 13.0219 16.5712 12.5742 16.0189 12.5742L8 12.5742Z"})))),oe=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{d:"M4 4C3.58579 4 3.25 4.33579 3.25 4.75C3.25 5.16421 3.58579 5.5 4 5.5L20 5.5C20.4142 5.5 20.75 5.16422 20.75 4.75C20.75 4.33579 20.4142 4 20 4H4Z"}),t.createElement("path",{d:"M4 18.6484C3.58579 18.6484 3.25 18.9842 3.25 19.3984C3.25 19.8127 3.58579 20.1484 4 20.1484L20 20.1484C20.4142 20.1484 20.75 19.8127 20.75 19.3984C20.75 18.9842 20.4142 18.6484 20 18.6484L4 18.6484Z"}),t.createElement("path",{d:"M7 8.89844C7 8.34615 7.44771 7.89844 8 7.89844L16.0189 7.89844C16.5712 7.89844 17.0189 8.34615 17.0189 8.89844C17.0189 9.45072 16.5712 9.89844 16.0189 9.89844L8 9.89844C7.44772 9.89844 7 9.45072 7 8.89844Z"}),t.createElement("path",{d:"M8 14.2856C7.44771 14.2856 7 14.7334 7 15.2856C7 15.8379 7.44772 16.2856 8 16.2856L16.0189 16.2856C16.5712 16.2856 17.0189 15.8379 17.0189 15.2856C17.0189 14.7334 16.5712 14.2856 16.0189 14.2856L8 14.2856Z"})))),de=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{d:"M20.5 7.75C20.5 8.16421 20.1642 8.5 19.75 8.5L3.75 8.5C3.33579 8.5 3 8.16421 3 7.75C3 7.33579 3.33579 7 3.75 7H19.75C20.1642 7 20.5 7.33579 20.5 7.75Z"}),t.createElement("path",{d:"M16.7595 15.75C16.7595 16.3023 16.3118 16.75 15.7595 16.75L7.75952 16.75C7.20724 16.75 6.75952 16.3023 6.75952 15.75C6.75952 15.1977 7.20724 14.75 7.75952 14.75L15.7595 14.75C16.3118 14.75 16.7595 15.1977 16.7595 15.75Z"}),t.createElement("path",{d:"M15.7595 12.75C16.3118 12.75 16.7595 12.3023 16.7595 11.75C16.7595 11.1977 16.3118 10.75 15.7595 10.75L7.75952 10.75C7.20724 10.75 6.75952 11.1977 6.75952 11.75C6.75952 12.3023 7.20724 12.75 7.75952 12.75L15.7595 12.75Z"})))),ce=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4 6.75C3.30964 6.75 2.75 7.30964 2.75 8V16C2.75 16.6904 3.30964 17.25 4 17.25H20C20.6904 17.25 21.25 16.6904 21.25 16V8C21.25 7.30964 20.6904 6.75 20 6.75H4ZM1.25 8C1.25 6.48122 2.48122 5.25 4 5.25H20C21.5188 5.25 22.75 6.48122 22.75 8V16C22.75 17.5188 21.5188 18.75 20 18.75H4C2.48122 18.75 1.25 17.5188 1.25 16V8ZM6 9.25C6.41421 9.25 6.75 9.58579 6.75 10V10.01C6.75 10.4242 6.41421 10.76 6 10.76C5.58579 10.76 5.25 10.4242 5.25 10.01V10C5.25 9.58579 5.58579 9.25 6 9.25ZM10 9.25C10.4142 9.25 10.75 9.58579 10.75 10V10.01C10.75 10.4242 10.4142 10.76 10 10.76C9.58579 10.76 9.25 10.4242 9.25 10.01V10C9.25 9.58579 9.58579 9.25 10 9.25ZM14 9.25C14.4142 9.25 14.75 9.58579 14.75 10V10.01C14.75 10.4242 14.4142 10.76 14 10.76C13.5858 10.76 13.25 10.4242 13.25 10.01V10C13.25 9.58579 13.5858 9.25 14 9.25ZM18 9.25C18.4142 9.25 18.75 9.58579 18.75 10V10.01C18.75 10.4242 18.4142 10.76 18 10.76C17.5858 10.76 17.25 10.4242 17.25 10.01V10C17.25 9.58579 17.5858 9.25 18 9.25ZM6 13.25C6.41421 13.25 6.75 13.5858 6.75 14V14.01C6.75 14.4242 6.41421 14.76 6 14.76C5.58579 14.76 5.25 14.4242 5.25 14.01V14C5.25 13.5858 5.58579 13.25 6 13.25ZM9.25 14C9.25 13.5858 9.58579 13.25 10 13.25H14C14.4142 13.25 14.75 13.5858 14.75 14C14.75 14.4142 14.4142 14.75 14 14.75H10C9.58579 14.75 9.25 14.4142 9.25 14ZM18 13.25C18.4142 13.25 18.75 13.5858 18.75 14V14.01C18.75 14.4242 18.4142 14.76 18 14.76C17.5858 14.76 17.25 14.4242 17.25 14.01V14C17.25 13.5858 17.5858 13.25 18 13.25Z"})))),ae=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7 3.5C6.66848 3.5 6.35054 3.6317 6.11612 3.86612C5.8817 4.10054 5.75 4.41848 5.75 4.75V9.25C5.75 9.66421 5.41421 10 5 10C4.58579 10 4.25 9.66421 4.25 9.25V4.75C4.25 4.02065 4.53973 3.32118 5.05546 2.80546C5.57118 2.28973 6.27065 2 7 2H14C14.1989 2 14.3897 2.07902 14.5303 2.21967L19.5303 7.21967C19.671 7.36032 19.75 7.55109 19.75 7.75V9.25C19.75 9.66421 19.4142 10 19 10C18.5858 10 18.25 9.66421 18.25 9.25V8.5H15C14.5359 8.5 14.0908 8.31563 13.7626 7.98744C13.4344 7.65925 13.25 7.21413 13.25 6.75V3.5H7ZM14.75 4.56066L17.1893 7H15C14.9337 7 14.8701 6.97366 14.8232 6.92678C14.7763 6.87989 14.75 6.8163 14.75 6.75V4.56066ZM5 11C5.41421 11 5.75 11.3358 5.75 11.75V12.25C5.75 12.6642 5.41421 13 5 13C4.58579 13 4.25 12.6642 4.25 12.25V11.75C4.25 11.3358 4.58579 11 5 11ZM18.25 12.25V11.75C18.25 11.3358 18.5858 11 19 11C19.4142 11 19.75 11.3358 19.75 11.75V12.25C19.75 12.6642 19.4142 13 19 13C18.5858 13 18.25 12.6642 18.25 12.25ZM5 14.5C5.41421 14.5 5.75 14.8358 5.75 15.25V15.75C5.75 16.1642 5.41421 16.5 5 16.5C4.58579 16.5 4.25 16.1642 4.25 15.75V15.25C4.25 14.8358 4.58579 14.5 5 14.5ZM18.25 15.75V15.25C18.25 14.8358 18.5858 14.5 19 14.5C19.4142 14.5 19.75 14.8358 19.75 15.25V15.75C19.75 16.1642 19.4142 16.5 19 16.5C18.5858 16.5 18.25 16.1642 18.25 15.75ZM5 18C5.41421 18 5.75 18.3358 5.75 18.75C5.75 19.0815 5.8817 19.3995 6.11612 19.6339C6.35054 19.8683 6.66848 20 7 20H17C17.3315 20 17.6495 19.8683 17.8839 19.6339C18.1183 19.3995 18.25 19.0815 18.25 18.75C18.25 18.3358 18.5858 18 19 18C19.4142 18 19.75 18.3358 19.75 18.75C19.75 19.4793 19.4603 20.1788 18.9445 20.6945C18.4288 21.2103 17.7293 21.5 17 21.5H7C6.27065 21.5 5.57118 21.2103 5.05546 20.6945C4.53973 20.1788 4.25 19.4793 4.25 18.75C4.25 18.3358 4.58579 18 5 18Z"})))),fe=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6 6.75C5.9337 6.75 5.87011 6.77634 5.82322 6.82322C5.77634 6.87011 5.75 6.9337 5.75 7V15C5.75 15.0663 5.77634 15.1299 5.82322 15.1768C5.87011 15.2237 5.9337 15.25 6 15.25H18C18.0663 15.25 18.1299 15.2237 18.1768 15.1768C18.2237 15.1299 18.25 15.0663 18.25 15V7C18.25 6.93369 18.2237 6.87011 18.1768 6.82322C18.1299 6.77634 18.0663 6.75 18 6.75H6ZM4.76256 5.76256C5.09075 5.43438 5.53587 5.25 6 5.25H18C18.4641 5.25 18.9092 5.43437 19.2374 5.76256C19.5656 6.09075 19.75 6.53587 19.75 7V15C19.75 15.4641 19.5656 15.9092 19.2374 16.2374C18.9092 16.5656 18.4641 16.75 18 16.75H6C5.53587 16.75 5.09075 16.5656 4.76256 16.2374C4.43437 15.9092 4.25 15.4641 4.25 15V7C4.25 6.53587 4.43437 6.09075 4.76256 5.76256ZM2.25 19C2.25 18.5858 2.58579 18.25 3 18.25H21C21.4142 18.25 21.75 18.5858 21.75 19C21.75 19.4142 21.4142 19.75 21 19.75H3C2.58579 19.75 2.25 19.4142 2.25 19Z"})))),Le=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{d:"M12 20.5C11.5858 20.5 11.25 20.1642 11.25 19.75V14.751H8C7.44772 14.751 7 14.3033 7 13.751C7 13.1987 7.44772 12.751 8 12.751H11.25L11.25 10.749L6 10.749C5.44772 10.749 5 10.3013 5 9.74902C5 9.19674 5.44771 8.74902 6 8.74902L11.25 8.74902V3.75C11.25 3.33579 11.5858 3 12 3C12.4142 3 12.75 3.33579 12.75 3.75V8.74902L18 8.74902C18.5523 8.74902 19 9.19673 19 9.74902C19 10.3013 18.5523 10.749 18 10.749L12.75 10.749L12.75 12.751L16 12.751C16.5523 12.751 17 13.1987 17 13.751C17 14.3033 16.5523 14.751 16 14.751L12.75 14.751V19.75C12.75 20.1642 12.4142 20.5 12 20.5Z"})))),ie=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{d:"M5.75 20.5C5.33579 20.5 5 20.1642 5 19.75L5 3.75C5 3.33579 5.33579 3 5.75 3C6.16421 3 6.5 3.33579 6.5 3.75L6.5 19.75C6.5 20.1642 6.16421 20.5 5.75 20.5Z"}),t.createElement("path",{d:"M8.24988 9.74902C8.24988 9.19674 8.69759 8.74902 9.24988 8.74902L18.2499 8.74902C18.8022 8.74902 19.2499 9.19673 19.2499 9.74902C19.2499 10.3013 18.8022 10.749 18.2499 10.749L9.24988 10.749C8.69759 10.749 8.24988 10.3013 8.24988 9.74902Z"}),t.createElement("path",{d:"M9.24988 12.751C8.69759 12.751 8.24988 13.1987 8.24988 13.751C8.24988 14.3033 8.69759 14.751 9.24988 14.751L15.2499 14.751C15.8022 14.751 16.2499 14.3033 16.2499 13.751C16.2499 13.1987 15.8022 12.751 15.2499 12.751L9.24988 12.751Z"})))),ue=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{d:"M18.4999 3C18.9141 3 19.2499 3.33579 19.2499 3.75V19.75C19.2499 20.1642 18.9141 20.5 18.4999 20.5C18.0857 20.5 17.7499 20.1642 17.7499 19.75V3.75C17.7499 3.33579 18.0857 3 18.4999 3Z"}),t.createElement("path",{d:"M16 13.751C16 14.3033 15.5523 14.751 15 14.751L6 14.751C5.44772 14.751 5 14.3033 5 13.751C5 13.1987 5.44772 12.751 6 12.751L15 12.751C15.5523 12.751 16 13.1987 16 13.751Z"}),t.createElement("path",{d:"M15 10.749C15.5523 10.749 16 10.3013 16 9.74902C16 9.19674 15.5523 8.74902 15 8.74902L9 8.74902C8.44771 8.74902 8 9.19674 8 9.74902C8 10.3013 8.44771 10.749 9 10.749L15 10.749Z"})))),Ve=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{d:"M21.5278 3.75C21.5278 3.33579 21.192 3 20.7778 3C20.3636 3 20.0278 3.33579 20.0278 3.75V19.75C20.0278 20.1642 20.3636 20.5 20.7778 20.5C21.192 20.5 21.5278 20.1642 21.5278 19.75V3.75Z"}),t.createElement("path",{d:"M4.5 3.75C4.5 3.33579 4.16421 3 3.75 3C3.33579 3 3 3.33579 3 3.75L3 19.75C3 20.1642 3.33579 20.5 3.75 20.5C4.16421 20.5 4.5 20.1642 4.5 19.75L4.5 3.75Z"}),t.createElement("path",{d:"M17.7639 13.751C17.7639 14.3033 17.3162 14.751 16.7639 14.751L7.76392 14.751C7.21163 14.751 6.76392 14.3033 6.76392 13.751C6.76392 13.1987 7.21163 12.751 7.76392 12.751L16.7639 12.751C17.3162 12.751 17.7639 13.1987 17.7639 13.751Z"}),t.createElement("path",{d:"M16.7639 10.749C17.3162 10.749 17.7639 10.3013 17.7639 9.74902C17.7639 9.19674 17.3162 8.74902 16.7639 8.74902L7.76392 8.74902C7.21163 8.74902 6.76392 9.19674 6.76392 9.74902C6.76392 10.3013 7.21163 10.749 7.76392 10.749L16.7639 10.749Z"})))),He=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6.25 8C6.25 6.74022 6.75044 5.53204 7.64124 4.64124C8.53204 3.75044 9.74022 3.25 11 3.25H13C14.2598 3.25 15.468 3.75044 16.3588 4.64124C17.2496 5.53204 17.75 6.74022 17.75 8V20C17.75 20.4142 17.4142 20.75 17 20.75C16.5858 20.75 16.25 20.4142 16.25 20V13.75H7.75V20C7.75 20.4142 7.41421 20.75 7 20.75C6.58579 20.75 6.25 20.4142 6.25 20V8ZM8.7019 5.7019C9.3114 5.09241 10.138 4.75 11 4.75H13C13.862 4.75 14.6886 5.09241 15.2981 5.7019C15.9076 6.3114 16.25 7.13805 16.25 8V12.25H7.75V8C7.75 7.13805 8.09241 6.3114 8.7019 5.7019Z"})))),Me=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6.5 5.75C5.77065 5.75 5.07118 6.03973 4.55546 6.55546C4.03973 7.07118 3.75 7.77065 3.75 8.5V12.25H9.25V8.5C9.25 7.77065 8.96027 7.07118 8.44454 6.55546C7.92882 6.03973 7.22935 5.75 6.5 5.75ZM10.75 8.5C10.75 7.37283 10.3022 6.29183 9.5052 5.4948C8.70817 4.69777 7.62717 4.25 6.5 4.25C5.37283 4.25 4.29183 4.69777 3.4948 5.4948C2.69777 6.29183 2.25 7.37283 2.25 8.5V19C2.25 19.4142 2.58579 19.75 3 19.75C3.41421 19.75 3.75 19.4142 3.75 19V13.75H9.25V19C9.25 19.4142 9.58579 19.75 10 19.75C10.4142 19.75 10.75 19.4142 10.75 19V8.5ZM14.4948 12.4948C15.2918 11.6978 16.3728 11.25 17.5 11.25C18.511 11.25 19.4849 11.6102 20.25 12.2596V12C20.25 11.5858 20.5858 11.25 21 11.25C21.4142 11.25 21.75 11.5858 21.75 12V19C21.75 19.4142 21.4142 19.75 21 19.75C20.5858 19.75 20.25 19.4142 20.25 19V18.7404C19.4849 19.3898 18.511 19.75 17.5 19.75C16.3728 19.75 15.2918 19.3022 14.4948 18.5052C13.6978 17.7082 13.25 16.6272 13.25 15.5C13.25 14.3728 13.6978 13.2918 14.4948 12.4948ZM17.5 12.75C16.7707 12.75 16.0712 13.0397 15.5555 13.5555C15.0397 14.0712 14.75 14.7707 14.75 15.5C14.75 16.2293 15.0397 16.9288 15.5555 17.4445C16.0712 17.9603 16.7707 18.25 17.5 18.25C18.2293 18.25 18.9288 17.9603 19.4445 17.4445C19.9603 16.9288 20.25 16.2293 20.25 15.5C20.25 14.7707 19.9603 14.0712 19.4445 13.5555C18.9288 13.0397 18.2293 12.75 17.5 12.75Z"})))),Ze=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4.8736 11.5735C5.38923 11.3599 5.94188 11.25 6.5 11.25C7.05812 11.25 7.61077 11.3599 8.1264 11.5735C8.53495 11.7427 8.91392 11.9744 9.25 12.2596V12C9.25 11.5858 9.58579 11.25 10 11.25C10.4142 11.25 10.75 11.5858 10.75 12V19C10.75 19.4142 10.4142 19.75 10 19.75C9.58579 19.75 9.25 19.4142 9.25 19V18.7404C8.91392 19.0256 8.53495 19.2573 8.1264 19.4265C7.61077 19.6401 7.05812 19.75 6.5 19.75C5.94188 19.75 5.38923 19.6401 4.8736 19.4265C4.35796 19.2129 3.88945 18.8999 3.4948 18.5052C3.10015 18.1106 2.78709 17.642 2.57351 17.1264C2.35993 16.6108 2.25 16.0581 2.25 15.5C2.25 14.9419 2.35993 14.3892 2.57351 13.8736C2.78709 13.358 3.10015 12.8894 3.4948 12.4948C3.88944 12.1001 4.35796 11.7871 4.8736 11.5735ZM6.5 12.75C6.13886 12.75 5.78127 12.8211 5.44762 12.9593C5.11398 13.0975 4.81082 13.3001 4.55546 13.5555C4.3001 13.8108 4.09753 14.114 3.95933 14.4476C3.82113 14.7813 3.75 15.1389 3.75 15.5C3.75 15.8611 3.82113 16.2187 3.95933 16.5524C4.09753 16.886 4.3001 17.1892 4.55546 17.4445C4.81082 17.6999 5.11397 17.9025 5.44762 18.0407C5.78127 18.1789 6.13887 18.25 6.5 18.25C6.86113 18.25 7.21873 18.1789 7.55238 18.0407C7.88603 17.9025 8.18918 17.6999 8.44454 17.4445C8.6999 17.1892 8.90247 16.886 9.04067 16.5524C9.17887 16.2187 9.25 15.8611 9.25 15.5C9.25 15.1389 9.17887 14.7813 9.04067 14.4476C8.90247 14.114 8.6999 13.8108 8.44454 13.5555C8.18918 13.3001 7.88602 13.0975 7.55238 12.9593C7.21873 12.8211 6.86114 12.75 6.5 12.75ZM14.4948 12.4948C15.2918 11.6978 16.3728 11.25 17.5 11.25C18.511 11.25 19.4849 11.6102 20.25 12.2596V12C20.25 11.5858 20.5858 11.25 21 11.25C21.4142 11.25 21.75 11.5858 21.75 12V19C21.75 19.4142 21.4142 19.75 21 19.75C20.5858 19.75 20.25 19.4142 20.25 19V18.7404C19.4849 19.3898 18.511 19.75 17.5 19.75C16.3728 19.75 15.2918 19.3022 14.4948 18.5052C13.6978 17.7082 13.25 16.6272 13.25 15.5C13.25 14.3728 13.6978 13.2918 14.4948 12.4948ZM17.5 12.75C16.7707 12.75 16.0712 13.0397 15.5555 13.5555C15.0397 14.0712 14.75 14.7707 14.75 15.5C14.75 16.2293 15.0397 16.9288 15.5555 17.4445C16.0712 17.9603 16.7707 18.25 17.5 18.25C18.2293 18.25 18.9288 17.9603 19.4445 17.4445C19.9603 16.9288 20.25 16.2293 20.25 15.5C20.25 14.7707 19.9603 14.0712 19.4445 13.5555C18.9288 13.0397 18.2293 12.75 17.5 12.75Z"})))),Re=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6.5 5.75C5.77065 5.75 5.07118 6.03973 4.55546 6.55546C4.03973 7.07118 3.75 7.77065 3.75 8.5V12.25H9.25V8.5C9.25 7.77065 8.96027 7.07118 8.44454 6.55546C7.92882 6.03973 7.22935 5.75 6.5 5.75ZM10.75 8.5C10.75 7.37283 10.3022 6.29183 9.5052 5.4948C8.70817 4.69777 7.62717 4.25 6.5 4.25C5.37283 4.25 4.29183 4.69777 3.4948 5.4948C2.69777 6.29183 2.25 7.37283 2.25 8.5V19C2.25 19.4142 2.58579 19.75 3 19.75C3.41421 19.75 3.75 19.4142 3.75 19V13.75H9.25V19C9.25 19.4142 9.58579 19.75 10 19.75C10.4142 19.75 10.75 19.4142 10.75 19V8.5ZM17.5 5.75C16.7707 5.75 16.0712 6.03973 15.5555 6.55546C15.0397 7.07118 14.75 7.77065 14.75 8.5V12.25H20.25V8.5C20.25 7.77065 19.9603 7.07118 19.4445 6.55546C18.9288 6.03973 18.2293 5.75 17.5 5.75ZM21.75 8.5C21.75 7.37283 21.3022 6.29183 20.5052 5.4948C19.7082 4.69777 18.6272 4.25 17.5 4.25C16.3728 4.25 15.2918 4.69777 14.4948 5.4948C13.6978 6.29183 13.25 7.37283 13.25 8.5V19C13.25 19.4142 13.5858 19.75 14 19.75C14.4142 19.75 14.75 19.4142 14.75 19V13.75H20.25V19C20.25 19.4142 20.5858 19.75 21 19.75C21.4142 19.75 21.75 19.4142 21.75 19V8.5Z"})))),ve=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6.56413 4.38976C6.90119 4.149 7.3696 4.22707 7.61036 4.56413L12.0001 10.7097L16.3898 4.56413C16.6305 4.22707 17.0989 4.149 17.436 4.38976C17.773 4.63051 17.8511 5.09893 17.6104 5.43599L12.9217 12.0001L17.6104 18.5641C17.8511 18.9012 17.773 19.3696 17.436 19.6104C17.0989 19.8511 16.6305 19.773 16.3898 19.436L12.0001 13.2904L7.61036 19.436C7.3696 19.773 6.90119 19.8511 6.56413 19.6104C6.22707 19.3696 6.149 18.9012 6.38976 18.5641L11.0784 12.0001L6.38976 5.43599C6.149 5.09893 6.22707 4.63051 6.56413 4.38976Z"})))),pe=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6.59797 4.36694C6.94765 4.14491 7.41111 4.2484 7.63313 4.59809L12 11.4759L16.3668 4.59809C16.5888 4.2484 17.0523 4.14491 17.402 4.36694C17.7517 4.58896 17.8552 5.05242 17.6331 5.4021L12.75 13.0931V19.0001C12.75 19.4143 12.4142 19.7501 12 19.7501C11.5858 19.7501 11.25 19.4143 11.25 19.0001V13.0931L6.36681 5.4021C6.14479 5.05242 6.24828 4.58896 6.59797 4.36694Z"})))),me=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{d:"M9 7.25C8.58579 7.25 8.25 7.58579 8.25 8C8.25 8.41421 8.58579 8.75 9 8.75H15C15.4142 8.75 15.75 8.41421 15.75 8C15.75 7.58579 15.4142 7.25 15 7.25H9Z"}),t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M19.75 4V20C19.75 20.4142 19.4142 20.75 19 20.75H7C6.27065 20.75 5.57118 20.4603 5.05546 19.9445C4.53973 19.4288 4.25 18.7293 4.25 18V6C4.25 5.27065 4.53973 4.57118 5.05546 4.05546C5.57118 3.53973 6.27065 3.25 7 3.25H19C19.4142 3.25 19.75 3.58579 19.75 4ZM7 4.75C6.66848 4.75 6.35054 4.8817 6.11612 5.11612C5.8817 5.35054 5.75 5.66848 5.75 6V15.5505C6.13355 15.3548 6.56137 15.25 7 15.25H18.25V4.75H7ZM6.11612 18.8839C5.8817 18.6495 5.75 18.3315 5.75 18C5.75 17.6685 5.8817 17.3505 6.11612 17.1161C6.35054 16.8817 6.66848 16.75 7 16.75H18.25V19.25H7C6.66848 19.25 6.35054 19.1183 6.11612 18.8839Z"})))),Ee=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M13 4.75C12.6685 4.75 12.3505 4.8817 12.1161 5.11612C11.8817 5.35054 11.75 5.66848 11.75 6V7C11.75 8.25978 11.2496 9.46796 10.3588 10.3588C9.64671 11.0708 8.73188 11.5334 7.75 11.6904V17C7.75 17.5967 7.98705 18.169 8.40901 18.591C8.83097 19.0129 9.40326 19.25 10 19.25H17C17.0212 19.25 17.0424 19.2509 17.0636 19.2527C17.2369 19.2674 17.4577 19.2052 17.6923 18.9706C17.9316 18.7314 18.1471 18.3441 18.2671 17.8402L19.2483 12.9344C19.2321 12.6267 19.1028 12.335 18.8839 12.1161C18.6495 11.8817 18.3315 11.75 18 11.75H15C14.5858 11.75 14.25 11.4142 14.25 11V6C14.25 5.66848 14.1183 5.35054 13.8839 5.11612C13.6495 4.8817 13.3315 4.75 13 4.75ZM7.54031 19.8306C7.46035 19.9789 7.35872 20.1162 7.23744 20.2374C6.90925 20.5656 6.46413 20.75 6 20.75H4C3.53587 20.75 3.09075 20.5656 2.76256 20.2374C2.43437 19.9092 2.25 19.4641 2.25 19V12C2.25 11.5359 2.43437 11.0908 2.76256 10.7626C3.09075 10.4344 3.53587 10.25 4 10.25H7C7.86195 10.25 8.6886 9.90759 9.2981 9.2981C9.90759 8.6886 10.25 7.86195 10.25 7V6C10.25 5.27065 10.5397 4.57118 11.0555 4.05546C11.5712 3.53973 12.2707 3.25 13 3.25C13.7293 3.25 14.4288 3.53973 14.9445 4.05546C15.4603 4.57118 15.75 5.27065 15.75 6V10.25H18C18.7293 10.25 19.4288 10.5397 19.9445 11.0555C20.4603 11.5712 20.75 12.2707 20.75 13C20.75 13.0494 20.7451 13.0987 20.7354 13.1471L19.7354 18.1471C19.7338 18.1551 19.7321 18.1632 19.7302 18.1712C19.562 18.8887 19.2333 19.5509 18.753 20.0313C18.2744 20.5099 17.6504 20.7964 16.9716 20.75H10C9.09323 20.75 8.22033 20.4216 7.54031 19.8306ZM6.25 11.75H4C3.9337 11.75 3.87011 11.7763 3.82322 11.8232C3.77634 11.8701 3.75 11.9337 3.75 12V19C3.75 19.0663 3.77634 19.1299 3.82322 19.1768C3.87011 19.2237 3.93369 19.25 4 19.25H6C6.06631 19.25 6.12989 19.2237 6.17678 19.1768C6.22366 19.1299 6.25 19.0663 6.25 19V11.75Z"})))),we=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M15.5304 8.46959C15.8233 8.76249 15.8233 9.23736 15.5304 9.53025L9.53036 15.5303C9.23747 15.8231 8.7626 15.8231 8.4697 15.5303C8.17681 15.2374 8.17681 14.7625 8.4697 14.4696L14.4697 8.46959C14.7626 8.1767 15.2375 8.1767 15.5304 8.46959Z"}),t.createElement("path",{d:"M18.1021 6.29169L17.6686 5.85819C16.1304 4.32006 13.6366 4.32006 12.0985 5.85819L11.0688 6.88787C10.7759 7.18077 10.301 7.18077 10.0081 6.88787C9.71524 6.59498 9.71524 6.12011 10.0081 5.82721L11.0378 4.79753C13.1617 2.67361 16.6053 2.67361 18.7292 4.79753L19.1627 5.23103C21.2866 7.35496 21.2866 10.7985 19.1627 12.9224L18.133 13.9521C17.8402 14.245 17.3653 14.245 17.0724 13.9521C16.7795 13.6592 16.7795 13.1844 17.0724 12.8915L18.1021 11.8618C19.6402 10.3236 19.6402 7.82983 18.1021 6.29169Z"}),t.createElement("path",{d:"M5.85844 12.0982C4.3203 13.6364 4.3203 16.1302 5.85844 17.6683L6.29194 18.1018C7.83007 19.64 10.3239 19.64 11.862 18.1018L12.8917 17.0721C13.1846 16.7792 13.6595 16.7792 13.9524 17.0721C14.2453 17.365 14.2453 17.8399 13.9524 18.1328L12.9227 19.1625C10.7988 21.2864 7.3552 21.2864 5.23128 19.1625L4.79778 18.729C2.67385 16.6051 2.67385 13.1615 4.79778 11.0376L5.82746 10.0079C6.12035 9.715 6.59522 9.715 6.88812 10.0079C7.18101 10.3008 7.18101 10.7757 6.88812 11.0686L5.85844 12.0982Z"})))),he=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{d:"M5.75 6C5.75 5.58579 5.41421 5.25 5 5.25C4.58579 5.25 4.25 5.58579 4.25 6V6.01C4.25 6.42421 4.58579 6.76 5 6.76C5.41421 6.76 5.75 6.42421 5.75 6.01V6Z"}),t.createElement("path",{d:"M9 5.25C8.58579 5.25 8.25 5.58579 8.25 6C8.25 6.41421 8.58579 6.75 9 6.75H20C20.4142 6.75 20.75 6.41421 20.75 6C20.75 5.58579 20.4142 5.25 20 5.25H9Z"}),t.createElement("path",{d:"M9 11.25C8.58579 11.25 8.25 11.5858 8.25 12C8.25 12.4142 8.58579 12.75 9 12.75H20C20.4142 12.75 20.75 12.4142 20.75 12C20.75 11.5858 20.4142 11.25 20 11.25H9Z"}),t.createElement("path",{d:"M8.25 18C8.25 17.5858 8.58579 17.25 9 17.25H20C20.4142 17.25 20.75 17.5858 20.75 18C20.75 18.4142 20.4142 18.75 20 18.75H9C8.58579 18.75 8.25 18.4142 8.25 18Z"}),t.createElement("path",{d:"M5 11.25C5.41421 11.25 5.75 11.5858 5.75 12V12.01C5.75 12.4242 5.41421 12.76 5 12.76C4.58579 12.76 4.25 12.4242 4.25 12.01V12C4.25 11.5858 4.58579 11.25 5 11.25Z"}),t.createElement("path",{d:"M5.75 18C5.75 17.5858 5.41421 17.25 5 17.25C4.58579 17.25 4.25 17.5858 4.25 18V18.01C4.25 18.4242 4.58579 18.76 5 18.76C5.41421 18.76 5.75 18.4242 5.75 18.01V18Z"})))),Be=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6.58319 3.89317C8.18657 2.82183 10.0716 2.25 12 2.25C12.4142 2.25 12.75 2.58579 12.75 3C12.75 3.41421 12.4142 3.75 12 3.75C10.3683 3.75 8.77326 4.23385 7.41655 5.14038C6.05984 6.0469 5.00242 7.33537 4.378 8.84286C3.75358 10.3504 3.5902 12.0092 3.90853 13.6095C4.22685 15.2098 5.01259 16.6798 6.16637 17.8336C7.32016 18.9874 8.79017 19.7732 10.3905 20.0915C11.9909 20.4098 13.6497 20.2464 15.1571 19.622C16.6646 18.9976 17.9531 17.9402 18.8596 16.5835C19.7661 15.2267 20.25 13.6317 20.25 12C20.25 11.5858 20.5858 11.25 21 11.25C21.4142 11.25 21.75 11.5858 21.75 12C21.75 13.9284 21.1782 15.8134 20.1068 17.4168C19.0355 19.0202 17.5127 20.2699 15.7312 21.0078C13.9496 21.7458 11.9892 21.9389 10.0979 21.5627C8.20656 21.1865 6.46927 20.2579 5.10571 18.8943C3.74215 17.5307 2.81355 15.7934 2.43735 13.9021C2.06114 12.0108 2.25422 10.0504 2.99218 8.26884C3.73013 6.48726 4.97982 4.96452 6.58319 3.89317Z"})))),xe=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M11.75 3.5C10.888 3.5 10.0614 3.84241 9.4519 4.4519C8.84241 5.0614 8.5 5.88805 8.5 6.75V10H15V6.75C15 5.88805 14.6576 5.0614 14.0481 4.4519C13.4386 3.84241 12.612 3.5 11.75 3.5ZM16.5 10V6.75C16.5 5.49022 15.9996 4.28204 15.1088 3.39124C14.218 2.50044 13.0098 2 11.75 2C10.4902 2 9.28204 2.50044 8.39124 3.39124C7.50044 4.28204 7 5.49022 7 6.75V10H6.75C6.02065 10 5.32118 10.2897 4.80546 10.8055C4.28973 11.3212 4 12.0207 4 12.75V18.75C4 19.4793 4.28973 20.1788 4.80546 20.6945C5.32118 21.2103 6.02065 21.5 6.75 21.5H16.75C17.4793 21.5 18.1788 21.2103 18.6945 20.6945C19.2103 20.1788 19.5 19.4793 19.5 18.75V12.75C19.5 12.0207 19.2103 11.3212 18.6945 10.8055C18.1788 10.2897 17.4793 10 16.75 10H16.5Z"})))),Ie=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 3.75C11.138 3.75 10.3114 4.09241 9.7019 4.7019C9.09241 5.3114 8.75 6.13805 8.75 7V10.25H15.25V7C15.25 6.13805 14.9076 5.3114 14.2981 4.7019C13.6886 4.09241 12.862 3.75 12 3.75ZM16.75 10.25V7C16.75 5.74022 16.2496 4.53204 15.3588 3.64124C14.468 2.75044 13.2598 2.25 12 2.25C10.7402 2.25 9.53204 2.75044 8.64124 3.64124C7.75044 4.53204 7.25 5.74022 7.25 7V10.25H7C6.27065 10.25 5.57118 10.5397 5.05546 11.0555C4.53973 11.5712 4.25 12.2707 4.25 13V19C4.25 19.7293 4.53973 20.4288 5.05546 20.9445C5.57118 21.4603 6.27065 21.75 7 21.75H17C17.7293 21.75 18.4288 21.4603 18.9445 20.9445C19.4603 20.4288 19.75 19.7293 19.75 19V13C19.75 12.2707 19.4603 11.5712 18.9445 11.0555C18.4288 10.5397 17.7293 10.25 17 10.25H16.75ZM7 11.75C6.66848 11.75 6.35054 11.8817 6.11612 12.1161C5.8817 12.3505 5.75 12.6685 5.75 13V19C5.75 19.3315 5.8817 19.6495 6.11612 19.8839C6.35054 20.1183 6.66848 20.25 7 20.25H17C17.3315 20.25 17.6495 20.1183 17.8839 19.8839C18.1183 19.6495 18.25 19.3315 18.25 19V13C18.25 12.6685 18.1183 12.3505 17.8839 12.1161C17.6495 11.8817 17.3315 11.75 17 11.75H7Z"})))),se=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3.05546 4.05546C3.57118 3.53973 4.27065 3.25 5 3.25H12C12.7293 3.25 13.4288 3.53973 13.9445 4.05546C14.4603 4.57118 14.75 5.27065 14.75 6V8C14.75 8.41421 14.4142 8.75 14 8.75C13.5858 8.75 13.25 8.41421 13.25 8V6C13.25 5.66848 13.1183 5.35054 12.8839 5.11612C12.6495 4.8817 12.3315 4.75 12 4.75H5C4.66848 4.75 4.35054 4.8817 4.11612 5.11612C3.8817 5.35054 3.75 5.66848 3.75 6V18C3.75 18.3315 3.8817 18.6495 4.11612 18.8839C4.35054 19.1183 4.66848 19.25 5 19.25H12C12.3315 19.25 12.6495 19.1183 12.8839 18.8839C13.1183 18.6495 13.25 18.3315 13.25 18V16C13.25 15.5858 13.5858 15.25 14 15.25C14.4142 15.25 14.75 15.5858 14.75 16V18C14.75 18.7293 14.4603 19.4288 13.9445 19.9445C13.4288 20.4603 12.7293 20.75 12 20.75H5C4.27065 20.75 3.57118 20.4603 3.05546 19.9445C2.53973 19.4288 2.25 18.7293 2.25 18V6C2.25 5.27065 2.53973 4.57118 3.05546 4.05546ZM17.4697 8.46967C17.7626 8.17678 18.2374 8.17678 18.5303 8.46967L21.5303 11.4697C21.8232 11.7626 21.8232 12.2374 21.5303 12.5303L18.5303 15.5303C18.2374 15.8232 17.7626 15.8232 17.4697 15.5303C17.1768 15.2374 17.1768 14.7626 17.4697 14.4697L19.1893 12.75H7C6.58579 12.75 6.25 12.4142 6.25 12C6.25 11.5858 6.58579 11.25 7 11.25H19.1893L17.4697 9.53033C17.1768 9.23744 17.1768 8.76256 17.4697 8.46967Z"})))),ge=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3.25003 3C3.25003 2.58578 3.58581 2.25 4.00003 2.25H12.1795C12.3784 2.25 12.5692 2.32902 12.7099 2.46967C12.8505 2.61032 12.9295 2.80109 12.9295 3L12.9295 10.5C12.9295 10.9142 12.5937 11.25 12.1795 11.25H4C3.80109 11.25 3.61032 11.171 3.46967 11.0303C3.32902 10.8897 3.25 10.6989 3.25 10.5L3.25003 3ZM4.75002 3.75L4.75 9.75H11.4295L11.4295 3.75H4.75002ZM3.25003 13.5C3.25003 13.0858 3.58581 12.75 4.00003 12.75H12.1795C12.5937 12.75 12.9295 13.0858 12.9295 13.5C12.9295 13.9142 12.5937 14.25 12.1795 14.25H4.00003C3.58581 14.25 3.25003 13.9142 3.25003 13.5Z"}),t.createElement("path",{d:"M20.4919 6.96052L20.4757 7.02512L20.4514 7.08164L18.8002 9.14069L18.7516 9.19721C18.6059 9.31833 18.3874 9.32641 18.2336 9.20529C18.0555 9.06802 18.0312 8.8177 18.1688 8.64006L19.2616 7.27543H15.6515L15.5787 7.26736C15.3925 7.23506 15.2468 7.07357 15.2468 6.8717C15.2468 6.64561 15.4249 6.46796 15.6515 6.46796H19.2616L18.1607 5.10334L18.1203 5.03874C18.0312 4.86917 18.0717 4.65115 18.2255 4.53003C18.3955 4.39276 18.6545 4.41699 18.7921 4.59463L20.411 6.61331L20.4514 6.67791L20.4757 6.72635L20.5 6.81518V6.88785L20.4919 6.96052Z"}),t.createElement("path",{d:"M7.96771 21.4919L7.90296 21.4758L7.8463 21.4516L5.78226 19.8043L5.72559 19.7559C5.60418 19.6105 5.59609 19.3925 5.7175 19.2391C5.8551 19.0614 6.10603 19.0372 6.2841 19.1745L7.65204 20.2646V16.6633L7.66013 16.5906C7.69251 16.4049 7.85439 16.2595 8.05675 16.2595C8.28339 16.2595 8.46147 16.4372 8.46147 16.6633V20.2646L9.8294 19.1664L9.89415 19.126C10.0641 19.0372 10.2827 19.0776 10.4041 19.231C10.5417 19.4006 10.5174 19.659 10.3393 19.7962L8.31577 21.4112L8.25101 21.4516L8.20245 21.4758L8.11341 21.5H8.04056L7.96771 21.4919Z"}),t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M18.9883 4.43971L20.606 6.4569C20.6121 6.46446 20.6177 6.47237 20.6228 6.48058L20.6633 6.54518C20.6675 6.55189 20.6714 6.5588 20.6749 6.56588L20.6992 6.61433C20.7066 6.62908 20.7125 6.64452 20.7169 6.66043L20.7411 6.74925C20.747 6.77073 20.75 6.7929 20.75 6.81518V6.88785C20.75 6.89709 20.7495 6.90633 20.7485 6.91552L20.7404 6.9882C20.7391 6.99935 20.7371 7.0104 20.7344 7.02129L20.7182 7.08589C20.715 7.09885 20.7107 7.11153 20.7054 7.1238L20.6811 7.18032C20.6722 7.20104 20.6606 7.22046 20.6465 7.23805L18.9899 9.30371L18.9413 9.36014C18.9322 9.37073 18.9222 9.38053 18.9115 9.38946C18.6782 9.58335 18.3292 9.59806 18.0799 9.40245C17.7863 9.17522 17.7547 8.76653 17.9712 8.48697L17.9737 8.48378L18.7411 7.52543H15.6515C15.6423 7.52543 15.6331 7.52493 15.624 7.52391L15.5511 7.51584C15.546 7.51527 15.541 7.51455 15.5359 7.51368C15.2339 7.46128 14.9968 7.1991 14.9968 6.8717C14.9968 6.50696 15.2874 6.21796 15.6515 6.21796H18.7387L17.9662 5.2603C17.9599 5.25258 17.9542 5.24448 17.9489 5.23607L17.9084 5.17147C17.9051 5.16609 17.9019 5.16058 17.8989 5.15496C17.7566 4.88388 17.8181 4.53362 18.0697 4.33455C18.3447 4.11356 18.7623 4.15006 18.9883 4.43971ZM18.5945 4.74773C18.5452 4.68419 18.447 4.67254 18.3826 4.72453L18.3802 4.72645C18.3269 4.7684 18.3074 4.84949 18.3384 4.91599L18.3647 4.95798L19.4562 6.311C19.5166 6.38594 19.5287 6.48894 19.4871 6.5758C19.4456 6.66267 19.3579 6.71796 19.2616 6.71796H15.6515C15.5624 6.71796 15.4968 6.78425 15.4968 6.8717C15.4968 6.94594 15.5482 7.0055 15.6157 7.01993L15.6653 7.02543H19.2616C19.3577 7.02543 19.4453 7.08054 19.4869 7.16719C19.5285 7.25383 19.5168 7.35667 19.4567 7.4317L18.3655 8.79442C18.3083 8.86955 18.325 8.96008 18.3862 9.00729L18.3883 9.00889C18.4423 9.05143 18.5216 9.05306 18.5789 9.0146L18.6078 8.98098L20.2351 6.95174L20.2381 6.94488L20.2453 6.9161L20.25 6.87397V6.84873L20.2411 6.8162L20.2333 6.80056L20.2068 6.75836L18.5945 4.74773ZM7.41385 16.5476C7.46655 16.2453 7.72979 16.0095 8.05675 16.0095C8.42089 16.0095 8.71147 16.2985 8.71147 16.6633V19.7433L9.6729 18.9715C9.68063 18.9653 9.68872 18.9595 9.69713 18.9543L9.76189 18.9139C9.76726 18.9105 9.77276 18.9074 9.77837 18.9045C10.0493 18.7629 10.3996 18.8237 10.5991 19.0746C10.8212 19.3496 10.7843 19.7673 10.4938 19.9928L8.47171 21.6066C8.46415 21.6126 8.45625 21.6182 8.44804 21.6233L8.38329 21.6637C8.37658 21.6679 8.36967 21.6717 8.3626 21.6753L8.31403 21.6995C8.29933 21.7068 8.28394 21.7127 8.26808 21.717L8.17904 21.7412C8.15765 21.7471 8.13558 21.75 8.11341 21.75H8.04056C8.03136 21.75 8.02216 21.7495 8.01302 21.7485L7.94017 21.7404C7.92907 21.7392 7.91806 21.7372 7.90722 21.7345L7.84246 21.7184C7.82955 21.7151 7.81692 21.7109 7.80468 21.7057L7.74802 21.6814C7.72734 21.6726 7.70794 21.661 7.69036 21.647L5.61969 19.9944L5.56313 19.9459C5.55251 19.9368 5.54268 19.9269 5.53373 19.9161C5.33908 19.6831 5.32427 19.3341 5.52071 19.0849C5.7484 18.7922 6.15709 18.7609 6.43673 18.9765L6.43992 18.979L7.40204 19.7457V16.6633C7.40204 16.654 7.40255 16.6448 7.40357 16.6356L7.41167 16.5629C7.41224 16.5578 7.41296 16.5527 7.41385 16.5476ZM7.90753 16.6278L7.90204 16.6771V20.2646C7.90204 20.3606 7.84705 20.4481 7.76055 20.4898C7.67405 20.5315 7.57132 20.5199 7.49623 20.4601L6.13019 19.3715C6.05427 19.314 5.96256 19.331 5.91514 19.3922L5.91354 19.3942C5.87123 19.4477 5.86954 19.5259 5.90781 19.5827L5.94151 19.6116L7.97575 21.235L7.98285 21.238L8.01194 21.2453L8.05438 21.25H8.08001L8.11297 21.241L8.12882 21.2331L8.17117 21.2067L10.1867 19.5982C10.2503 19.5492 10.2616 19.4522 10.21 19.3885L10.208 19.3862C10.166 19.333 10.0839 19.313 10.0165 19.3444L9.9743 19.3707L8.61797 20.4595C8.54297 20.5197 8.44008 20.5316 8.35336 20.49C8.26664 20.4484 8.21147 20.3608 8.21147 20.2646V16.6633C8.21147 16.5758 8.14589 16.5095 8.05675 16.5095C7.98116 16.5095 7.92184 16.5614 7.90753 16.6278Z"})))),Ae=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3.80421 6.63475L12 12.0986L20.1958 6.63475C20.0396 6.12258 19.5633 5.75 19 5.75H5C4.43671 5.75 3.96045 6.12258 3.80421 6.63475ZM20.25 8.40139L12.416 13.624C12.1641 13.792 11.8359 13.792 11.584 13.624L3.75 8.40139V17C3.75 17.6904 4.30964 18.25 5 18.25H19C19.6904 18.25 20.25 17.6904 20.25 17V8.40139ZM2.25 7C2.25 5.48122 3.48122 4.25 5 4.25H19C20.5188 4.25 21.75 5.48122 21.75 7V17C21.75 18.5188 20.5188 19.75 19 19.75H5C3.48122 19.75 2.25 18.5188 2.25 17V7Z"})))),Se=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 7.25C11.0054 7.25 10.0516 7.64509 9.34835 8.34835C8.64509 9.05161 8.25 10.0054 8.25 11C8.25 11.9946 8.64509 12.9484 9.34835 13.6517C10.0516 14.3549 11.0054 14.75 12 14.75C12.9946 14.75 13.9484 14.3549 14.6517 13.6517C15.3549 12.9484 15.75 11.9946 15.75 11C15.75 10.0054 15.3549 9.05161 14.6517 8.34835C13.9484 7.64509 12.9946 7.25 12 7.25ZM10.409 9.40901C10.831 8.98705 11.4033 8.75 12 8.75C12.5967 8.75 13.169 8.98705 13.591 9.40901C14.0129 9.83097 14.25 10.4033 14.25 11C14.25 11.5967 14.0129 12.169 13.591 12.591C13.169 13.0129 12.5967 13.25 12 13.25C11.4033 13.25 10.831 13.0129 10.409 12.591C9.98705 12.169 9.75 11.5967 9.75 11C9.75 10.4033 9.98705 9.83097 10.409 9.40901Z"}),t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 2.25024C10.2694 2.25024 8.57773 2.76341 7.13881 3.72485C5.69989 4.6863 4.57838 6.05283 3.9161 7.65166C3.25382 9.25049 3.08052 11.0098 3.4181 12.7071C3.75568 14.4044 4.58907 15.9636 5.81274 17.1873L10.0569 21.4305C10.5726 21.9457 11.2716 22.235 12.0005 22.235C12.7294 22.235 13.4287 21.9454 13.9443 21.4303L18.1873 17.1873C19.411 15.9635 20.2443 14.4044 20.5819 12.7071C20.9195 11.0098 20.7462 9.25049 20.0839 7.65166C19.4216 6.05283 18.3001 4.6863 16.8612 3.72485C15.4223 2.76341 13.7306 2.25024 12 2.25024ZM7.97216 4.97206C9.16441 4.17544 10.5661 3.75024 12 3.75024C13.4339 3.75024 14.8356 4.17544 16.0278 4.97206C17.2201 5.76869 18.1493 6.90096 18.6981 8.2257C19.2468 9.55045 19.3904 11.0082 19.1107 12.4145C18.831 13.8209 18.1406 15.1127 17.1267 16.1266L12.8839 20.3694C12.6496 20.6035 12.3318 20.735 12.0005 20.735C11.6692 20.735 11.3515 20.6035 11.1171 20.3694L6.87327 16.1266C5.85942 15.1126 5.16899 13.8208 4.88928 12.4145C4.60957 11.0082 4.75317 9.55045 5.30191 8.2257C5.85066 6.90096 6.77991 5.76869 7.97216 4.97206Z"})))),Te=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{d:"M3.25 8C3.25 7.58579 3.58579 7.25 4 7.25H20C20.4142 7.25 20.75 7.58579 20.75 8C20.75 8.41421 20.4142 8.75 20 8.75H4C3.58579 8.75 3.25 8.41421 3.25 8Z"}),t.createElement("path",{d:"M3.25 16C3.25 15.5858 3.58579 15.25 4 15.25H20C20.4142 15.25 20.75 15.5858 20.75 16C20.75 16.4142 20.4142 16.75 20 16.75H4C3.58579 16.75 3.25 16.4142 3.25 16Z"})))),ye=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3.25 6C3.25 5.58579 3.58579 5.25 4 5.25H20C20.4142 5.25 20.75 5.58579 20.75 6C20.75 6.41421 20.4142 6.75 20 6.75H4C3.58579 6.75 3.25 6.41421 3.25 6ZM3.25 12C3.25 11.5858 3.58579 11.25 4 11.25H20C20.4142 11.25 20.75 11.5858 20.75 12C20.75 12.4142 20.4142 12.75 20 12.75H4C3.58579 12.75 3.25 12.4142 3.25 12ZM3.25 18C3.25 17.5858 3.58579 17.25 4 17.25H20C20.4142 17.25 20.75 17.5858 20.75 18C20.75 18.4142 20.4142 18.75 20 18.75H4C3.58579 18.75 3.25 18.4142 3.25 18Z"})))),Pe=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7 5C6.40326 5 5.83097 5.23705 5.40901 5.65901C4.98705 6.08097 4.75 6.65326 4.75 7.25V18.4393L7.46967 15.7197C7.61032 15.579 7.80109 15.5 8 15.5H17C17.5967 15.5 18.169 15.2629 18.591 14.841C19.0129 14.419 19.25 13.8467 19.25 13.25V7.25C19.25 6.65326 19.0129 6.08097 18.591 5.65901C18.169 5.23705 17.5967 5 17 5H7ZM4.34835 4.59835C5.05161 3.89509 6.00544 3.5 7 3.5H17C17.9946 3.5 18.9484 3.89509 19.6516 4.59835C20.3549 5.30161 20.75 6.25544 20.75 7.25V13.25C20.75 14.2446 20.3549 15.1984 19.6516 15.9017C18.9484 16.6049 17.9946 17 17 17H8.31066L4.53033 20.7803C4.31583 20.9948 3.99324 21.059 3.71299 20.9429C3.43273 20.8268 3.25 20.5533 3.25 20.25V7.25C3.25 6.25544 3.64509 5.30161 4.34835 4.59835Z"})))),ke=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7 5.75C6.40326 5.75 5.83097 5.98705 5.40901 6.40901C4.98705 6.83097 4.75 7.40326 4.75 8V19.1893L7.46967 16.4697C7.61032 16.329 7.80109 16.25 8 16.25H17C17.5967 16.25 18.169 16.0129 18.591 15.591C19.0129 15.169 19.25 14.5967 19.25 14V8C19.25 7.40326 19.0129 6.83097 18.591 6.40901C18.169 5.98705 17.5967 5.75 17 5.75H7ZM4.34835 5.34835C5.05161 4.64509 6.00544 4.25 7 4.25H17C17.9946 4.25 18.9484 4.64509 19.6516 5.34835C20.3549 6.05161 20.75 7.00544 20.75 8V14C20.75 14.9946 20.3549 15.9484 19.6516 16.6517C18.9484 17.3549 17.9946 17.75 17 17.75H8.31066L4.53033 21.5303C4.31583 21.7448 3.99324 21.809 3.71299 21.6929C3.43273 21.5768 3.25 21.3033 3.25 21V8C3.25 7.00544 3.64509 6.05161 4.34835 5.34835ZM7.25 9C7.25 8.58579 7.58579 8.25 8 8.25H16C16.4142 8.25 16.75 8.58579 16.75 9C16.75 9.41421 16.4142 9.75 16 9.75H8C7.58579 9.75 7.25 9.41421 7.25 9ZM7.25 13C7.25 12.5858 7.58579 12.25 8 12.25H14C14.4142 12.25 14.75 12.5858 14.75 13C14.75 13.4142 14.4142 13.75 14 13.75H8C7.58579 13.75 7.25 13.4142 7.25 13Z"})))),be=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M11 3.75C10.9337 3.75 10.8701 3.77634 10.8232 3.82322C10.7763 3.87011 10.75 3.9337 10.75 4V10C10.75 10.0663 10.7763 10.1299 10.8232 10.1768C10.8701 10.2237 10.9337 10.25 11 10.25H18C18.1989 10.25 18.3897 10.329 18.5303 10.4697L20.25 12.1893V4C20.25 3.93369 20.2237 3.87011 20.1768 3.82322C20.1299 3.77634 20.0663 3.75 20 3.75H11ZM9.76256 2.76256C10.0908 2.43437 10.5359 2.25 11 2.25H20C20.4641 2.25 20.9092 2.43437 21.2374 2.76256C21.5656 3.09075 21.75 3.53587 21.75 4V14C21.75 14.3033 21.5673 14.5768 21.287 14.6929C21.0068 14.809 20.6842 14.7448 20.4697 14.5303L17.6893 11.75H11C10.5359 11.75 10.0908 11.5656 9.76256 11.2374C9.43437 10.9092 9.25 10.4641 9.25 10V4C9.25 3.53587 9.43437 3.09075 9.76256 2.76256ZM4 10.75C3.9337 10.75 3.87011 10.7763 3.82322 10.8232C3.77634 10.8701 3.75 10.9337 3.75 11V19.1893L5.46967 17.4697C5.61032 17.329 5.80109 17.25 6 17.25H13C13.0663 17.25 13.1299 17.2237 13.1768 17.1768C13.2237 17.1299 13.25 17.0663 13.25 17V15C13.25 14.5858 13.5858 14.25 14 14.25C14.4142 14.25 14.75 14.5858 14.75 15V17C14.75 17.4641 14.5656 17.9092 14.2374 18.2374C13.9092 18.5656 13.4641 18.75 13 18.75H6.31066L3.53033 21.5303C3.31583 21.7448 2.99324 21.809 2.71299 21.6929C2.43273 21.5768 2.25 21.3033 2.25 21V11C2.25 10.5359 2.43437 10.0908 2.76256 9.76256C3.09075 9.43437 3.53587 9.25 4 9.25H6C6.41421 9.25 6.75 9.58579 6.75 10C6.75 10.4142 6.41421 10.75 6 10.75H4Z"})))),De=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3.46967 3.46967C3.76256 3.17678 4.23744 3.17678 4.53033 3.46967L9.25 8.18934V6C9.25 5.58579 9.58579 5.25 10 5.25C10.4142 5.25 10.75 5.58579 10.75 6V10C10.75 10.4142 10.4142 10.75 10 10.75H6C5.58579 10.75 5.25 10.4142 5.25 10C5.25 9.58579 5.58579 9.25 6 9.25H8.18934L3.46967 4.53033C3.17678 4.23744 3.17678 3.76256 3.46967 3.46967ZM14 13.25H18C18.4142 13.25 18.75 13.5858 18.75 14C18.75 14.4142 18.4142 14.75 18 14.75H15.8107L20.5303 19.4697C20.8232 19.7626 20.8232 20.2374 20.5303 20.5303C20.2374 20.8232 19.7626 20.8232 19.4697 20.5303L14.75 15.8107V18C14.75 18.4142 14.4142 18.75 14 18.75C13.5858 18.75 13.25 18.4142 13.25 18V14C13.25 13.5858 13.5858 13.25 14 13.25Z"})))),Fe=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4.19995 12.0498C4.19995 11.6356 4.53574 11.2998 4.94995 11.2998H18.95C19.3642 11.2998 19.7 11.6356 19.7 12.0498C19.7 12.464 19.3642 12.7998 18.95 12.7998H4.94995C4.53574 12.7998 4.19995 12.464 4.19995 12.0498Z"})))),Ue=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M8.66667 4.25C8.24587 4.25 7.75 4.66893 7.75 5.38889V18.6111C7.75 19.3311 8.24587 19.75 8.66667 19.75H15.3333C15.7541 19.75 16.25 19.3311 16.25 18.6111V5.38889C16.25 4.66893 15.7541 4.25 15.3333 4.25H13.7073C13.735 4.32819 13.75 4.41234 13.75 4.5C13.75 4.91421 13.4142 5.25 13 5.25H11C10.5858 5.25 10.25 4.91421 10.25 4.5C10.25 4.41234 10.265 4.32819 10.2927 4.25H8.66667ZM6.25 5.38889C6.25 4.02244 7.24652 2.75 8.66667 2.75H15.3333C16.7535 2.75 17.75 4.02244 17.75 5.38889V18.6111C17.75 19.9776 16.7535 21.25 15.3333 21.25H8.66667C7.24652 21.25 6.25 19.9776 6.25 18.6111V5.38889Z"})))),Oe=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M5.38889 7.75C4.66893 7.75 4.25 8.24587 4.25 8.66667L4.25 15.3333C4.25 15.7541 4.66893 16.25 5.38889 16.25L18.6111 16.25C19.3311 16.25 19.75 15.7541 19.75 15.3333V13.7073C19.6718 13.735 19.5877 13.75 19.5 13.75C19.0858 13.75 18.75 13.4142 18.75 13V11C18.75 10.5858 19.0858 10.25 19.5 10.25C19.5877 10.25 19.6718 10.265 19.75 10.2927V8.66667C19.75 8.24587 19.3311 7.75 18.6111 7.75L5.38889 7.75ZM2.75 8.66667C2.75 7.24652 4.02244 6.25 5.38889 6.25L18.6111 6.25C19.9776 6.25 21.25 7.24652 21.25 8.66667V15.3333C21.25 16.7535 19.9776 17.75 18.6111 17.75L5.38889 17.75C4.02244 17.75 2.75 16.7535 2.75 15.3333L2.75 8.66667Z"})))),Je=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M8.66667 4.25C8.24587 4.25 7.75 4.66893 7.75 5.38889V18.6111C7.75 19.3311 8.24587 19.75 8.66667 19.75H15.3333C15.7541 19.75 16.25 19.3311 16.25 18.6111V5.38889C16.25 4.66893 15.7541 4.25 15.3333 4.25H13.7073C13.735 4.32819 13.75 4.41234 13.75 4.5C13.75 4.91421 13.4142 5.25 13 5.25H11C10.5858 5.25 10.25 4.91421 10.25 4.5C10.25 4.41234 10.265 4.32819 10.2927 4.25H8.66667ZM6.25 5.38889C6.25 4.02244 7.24652 2.75 8.66667 2.75H15.3333C16.7535 2.75 17.75 4.02244 17.75 5.38889V18.6111C17.75 19.9776 16.7535 21.25 15.3333 21.25H8.66667C7.24652 21.25 6.25 19.9776 6.25 18.6111V5.38889Z"})))),We=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M14 18H9.99997C9.99997 18.5304 10.2107 19.0391 10.5858 19.4142C10.9608 19.7893 11.4695 20 12 20C12.5304 20 13.0391 19.7893 13.4142 19.4142C13.7893 19.0391 14 18.5304 14 18ZM7.99997 18C7.99997 19.0609 8.4214 20.0783 9.17154 20.8284C9.92169 21.5786 10.9391 22 12 22C13.0608 22 14.0783 21.5786 14.8284 20.8284C15.5785 20.0783 16 19.0609 16 18H20C20.4541 18 20.8513 17.694 20.967 17.2548C21.0827 16.8156 20.8879 16.3536 20.4927 16.1298C20.084 15.8984 19.7354 15.5742 19.4748 15.1834C19.2247 14.8082 19.0624 14.3815 19 13.9354V11C19 10.9844 18.9996 10.9687 18.9989 10.9531C18.9308 9.50291 18.4694 8.09857 17.6641 6.8906C16.9636 5.83991 16.0259 4.97224 14.93 4.35572C14.8079 3.80059 14.5295 3.28688 14.1213 2.87868C13.5587 2.31607 12.7956 2 12 2C11.2043 2 10.4413 2.31607 9.87865 2.87868C9.47045 3.28688 9.19204 3.80059 9.06996 4.35572C7.974 4.97224 7.03633 5.83991 6.33587 6.8906C5.53056 8.09857 5.06914 9.50291 5.00107 10.9531C5.00034 10.9687 4.99997 10.9844 4.99997 11V13.9353C4.93749 14.3815 4.77528 14.8082 4.52511 15.1834C4.26457 15.5742 3.91592 15.8984 3.50722 16.1298C3.11202 16.3536 2.91726 16.8156 3.03297 17.2548C3.14868 17.694 3.54581 18 3.99997 18H7.99997Z"})))),je=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M11.9999 3.75C11.6684 3.75 11.3505 3.8817 11.1161 4.11612C10.8816 4.35054 10.7499 4.66848 10.7499 5C10.7499 5.29002 10.5827 5.55405 10.3206 5.67802C9.29519 6.16287 8.42108 6.9176 7.79193 7.86132C7.16594 8.80031 6.80593 9.89117 6.74995 11.0181V14C6.74995 14.0301 6.74813 14.0602 6.74451 14.0901C6.65515 14.8284 6.39368 15.5354 5.98117 16.1542C5.95968 16.1864 5.93781 16.2183 5.91557 16.25H18.0843C18.0621 16.2183 18.0402 16.1864 18.0187 16.1542C17.6062 15.5354 17.3447 14.8284 17.2554 14.0901C17.2518 14.0602 17.2499 14.0301 17.2499 14V11.0181C17.194 9.89117 16.8339 8.80031 16.208 7.86132C15.5788 6.9176 14.7047 6.16287 13.6793 5.67802C13.4172 5.55405 13.2499 5.29002 13.2499 5C13.2499 4.66848 13.1183 4.35054 12.8838 4.11612C12.6494 3.8817 12.3315 3.75 11.9999 3.75ZM14.2499 17.75H9.74995V18C9.74995 18.5967 9.987 19.169 10.409 19.591C10.8309 20.0129 11.4032 20.25 11.9999 20.25C12.5967 20.25 13.169 20.0129 13.5909 19.591C14.0129 19.169 14.2499 18.5967 14.2499 18V17.75ZM8.24995 17.75V18C8.24995 18.9946 8.64503 19.9484 9.3483 20.6517C10.0516 21.3549 11.0054 21.75 11.9999 21.75C12.9945 21.75 13.9483 21.3549 14.6516 20.6517C15.3549 19.9484 15.7499 18.9946 15.7499 18V17.75H19.9999C20.3406 17.75 20.6384 17.5205 20.7252 17.1911C20.812 16.8617 20.6659 16.5152 20.3695 16.3474C19.9268 16.0966 19.549 15.7455 19.2668 15.3221C18.9925 14.9106 18.8158 14.4422 18.7499 13.9524V11C18.7499 10.9883 18.7497 10.9765 18.7491 10.9648C18.6832 9.55995 18.2362 8.19949 17.456 7.02927C16.7563 5.97962 15.8121 5.11853 14.7075 4.51836C14.6095 3.96791 14.3452 3.45621 13.9445 3.05546C13.4288 2.53973 12.7293 2.25 11.9999 2.25C11.2706 2.25 10.5711 2.53973 10.0554 3.05546C9.65465 3.45621 9.39036 3.96791 9.29244 4.51836C8.18776 5.11852 7.24363 5.97962 6.54386 7.02927C5.76371 8.19949 5.31671 9.55995 5.25077 10.9648C5.25022 10.9765 5.24995 10.9883 5.24995 11V13.9524C5.1841 14.4422 5.00741 14.9106 4.7331 15.3221C4.45085 15.7455 4.07314 16.0966 3.63038 16.3474C3.33399 16.5152 3.18791 16.8617 3.2747 17.1911C3.36148 17.5205 3.65933 17.75 3.99995 17.75H8.24995Z"})))),ze=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M5 4.72C5 4.32236 5.32235 4 5.72 4H18.0755C18.4732 4 18.7955 4.32236 18.7955 4.72C18.7955 5.11765 18.4732 5.44 18.0755 5.44H5.72C5.32235 5.44 5 5.11765 5 4.72ZM5 7.8726C5 7.47495 5.32235 7.1526 5.72 7.1526H18.0755C18.4732 7.1526 18.7955 7.47495 18.7955 7.8726V9.52C18.7955 9.91765 18.4732 10.24 18.0755 10.24C17.6779 10.24 17.3555 9.91765 17.3555 9.52V8.5926H12.6181V18.2793H13.4091C13.8067 18.2793 14.1291 18.6016 14.1291 18.9993C14.1291 19.3969 13.8067 19.7193 13.4091 19.7193H10.3868C9.9892 19.7193 9.66685 19.3969 9.66685 18.9993C9.66685 18.6016 9.9892 18.2793 10.3868 18.2793H11.1781V8.5926H6.44V9.52C6.44 9.91765 6.11765 10.24 5.72 10.24C5.32235 10.24 5 9.91765 5 9.52V7.8726Z"})))),Xe=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6 3.75C5.66848 3.75 5.35054 3.8817 5.11612 4.11612C4.8817 4.35054 4.75 4.66848 4.75 5V19C4.75 19.3315 4.8817 19.6495 5.11612 19.8839C5.35054 20.1183 5.66848 20.25 6 20.25H8C8.41421 20.25 8.75 20.5858 8.75 21C8.75 21.4142 8.41421 21.75 8 21.75H6C5.27065 21.75 4.57118 21.4603 4.05546 20.9445C3.53973 20.4288 3.25 19.7293 3.25 19V5C3.25 4.27065 3.53973 3.57118 4.05546 3.05546C4.57118 2.53973 5.27065 2.25 6 2.25H13C13.1989 2.25 13.3897 2.32902 13.5303 2.46967L18.5303 7.46967C18.671 7.61032 18.75 7.80109 18.75 8V12C18.75 12.4142 18.4142 12.75 18 12.75C17.5858 12.75 17.25 12.4142 17.25 12V8.75H14C13.5359 8.75 13.0908 8.56563 12.7626 8.23744C12.4344 7.90925 12.25 7.46413 12.25 7V3.75H6ZM13.75 4.81066L16.1893 7.25H14C13.9337 7.25 13.8701 7.22366 13.8232 7.17678C13.7763 7.12989 13.75 7.0663 13.75 7V4.81066ZM12 14.75C11.9176 14.75 11.8482 14.781 11.8046 14.8217C11.7628 14.8607 11.75 14.9021 11.75 14.9333V20.0667C11.75 20.0979 11.7628 20.1393 11.8046 20.1783C11.8482 20.219 11.9176 20.25 12 20.25H19C19.0824 20.25 19.1518 20.219 19.1954 20.1783C19.2372 20.1393 19.25 20.0979 19.25 20.0667V16.3333C19.25 16.3021 19.2372 16.2607 19.1954 16.2217C19.1518 16.181 19.0824 16.15 19 16.15H15.5C15.31 16.15 15.1271 16.0779 14.9883 15.9483L13.7044 14.75H12ZM10.7812 13.7251C11.1127 13.4156 11.5519 13.25 12 13.25H14C14.19 13.25 14.3729 13.3221 14.5117 13.4517L15.7956 14.65H19C19.4481 14.65 19.8873 14.8156 20.2188 15.1251C20.5521 15.4361 20.75 15.8695 20.75 16.3333V20.0667C20.75 20.5305 20.5521 20.9639 20.2188 21.2749C19.8873 21.5844 19.4481 21.75 19 21.75H12C11.5519 21.75 11.1127 21.5844 10.7812 21.2749C10.4479 20.9639 10.25 20.5305 10.25 20.0667V14.9333C10.25 14.4695 10.4479 14.0361 10.7812 13.7251Z"})))),_e=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7 3.75C6.66848 3.75 6.35054 3.8817 6.11612 4.11612C5.8817 4.35054 5.75 4.66848 5.75 5V19C5.75 19.3315 5.8817 19.6495 6.11612 19.8839C6.35054 20.1183 6.66848 20.25 7 20.25H17C17.3315 20.25 17.6495 20.1183 17.8839 19.8839C18.1183 19.6495 18.25 19.3315 18.25 19V8.75H15C14.5359 8.75 14.0908 8.56563 13.7626 8.23744C13.4344 7.90925 13.25 7.46413 13.25 7V3.75H7ZM14.75 4.81066L17.1893 7.25H15C14.9337 7.25 14.8701 7.22366 14.8232 7.17678C14.7763 7.12989 14.75 7.0663 14.75 7V4.81066ZM5.05546 3.05546C5.57118 2.53973 6.27065 2.25 7 2.25H14C14.1989 2.25 14.3897 2.32902 14.5303 2.46967L19.5303 7.46967C19.671 7.61032 19.75 7.80109 19.75 8V19C19.75 19.7293 19.4603 20.4288 18.9445 20.9445C18.4288 21.4603 17.7293 21.75 17 21.75H7C6.27065 21.75 5.57118 21.4603 5.05546 20.9445C4.53973 20.4288 4.25 19.7293 4.25 19V5C4.25 4.27065 4.53973 3.57118 5.05546 3.05546Z"})))),Ge=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6.75 2C6.02066 2 5.32118 2.28973 4.80546 2.80546C4.28973 3.32118 4 4.02065 4 4.75V18.75C4 19.4793 4.28973 20.1788 4.80546 20.6945C5.32118 21.2103 6.02065 21.5 6.75 21.5H16.75C17.4793 21.5 18.1788 21.2103 18.6945 20.6945C19.2103 20.1788 19.5 19.4793 19.5 18.75V7.75C19.5 7.55109 19.421 7.36032 19.2803 7.21967L14.2803 2.21967C14.1397 2.07902 13.9489 2 13.75 2H6.75ZM5.86612 3.86612C6.10054 3.6317 6.41848 3.5 6.75 3.5H13V6.75C13 7.21413 13.1844 7.65925 13.5126 7.98744C13.8408 8.31563 14.2859 8.5 14.75 8.5H18V18.75C18 19.0815 17.8683 19.3995 17.6339 19.6339C17.3995 19.8683 17.0815 20 16.75 20H6.75C6.41848 20 6.10054 19.8683 5.86612 19.6339C5.6317 19.3995 5.5 19.0815 5.5 18.75V4.75C5.5 4.41848 5.6317 4.10054 5.86612 3.86612ZM16.9393 7L14.5 4.56066V6.75C14.5 6.8163 14.5263 6.87989 14.5732 6.92678C14.6201 6.97366 14.6837 7 14.75 7H16.9393Z"}),t.createElement("path",{d:"M8.5 12.25C8.08579 12.25 7.75 12.5858 7.75 13C7.75 13.4142 8.08579 13.75 8.5 13.75H15C15.4142 13.75 15.75 13.4142 15.75 13C15.75 12.5858 15.4142 12.25 15 12.25H8.5Z"}),t.createElement("path",{d:"M8.5 16.25C8.08579 16.25 7.75 16.5858 7.75 17C7.75 17.4142 8.08579 17.75 8.5 17.75H15C15.4142 17.75 15.75 17.4142 15.75 17C15.75 16.5858 15.4142 16.25 15 16.25H8.5Z"})))),Ne=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M13.9697 4.96973C14.6408 4.29864 15.5509 3.92163 16.5 3.92163C17.4491 3.92163 18.3592 4.29864 19.0303 4.96973C19.7014 5.64081 20.0784 6.551 20.0784 7.50006C20.0784 8.44912 19.7014 9.3593 19.0303 10.0304L8.53033 20.5304C8.38968 20.671 8.19891 20.7501 8 20.7501H4C3.58579 20.7501 3.25 20.4143 3.25 20.0001V16.0001C3.25 15.8011 3.32902 15.6104 3.46967 15.4697L12.9641 5.97531C12.9659 5.97342 12.9678 5.97154 12.9697 5.96967C12.9715 5.9678 12.9734 5.96594 12.9753 5.96409L13.9697 4.96973ZM13.5 7.56069L4.75 16.3107V19.2501H7.68934L16.4394 10.5L13.5 7.56069ZM17.5 9.43937L14.5607 6.50003L15.0303 6.03039C15.4201 5.64061 15.9488 5.42163 16.5 5.42163C17.0512 5.42163 17.5799 5.64061 17.9697 6.03039C18.3595 6.42017 18.5784 6.94882 18.5784 7.50006C18.5784 8.05129 18.3595 8.57995 17.9697 8.96973L17.5 9.43937Z"})))),Ke=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6.5 3.75C6.5 3.33579 6.16421 3 5.75 3C5.33579 3 5 3.33579 5 3.75C5 4.08152 4.8683 4.39946 4.63388 4.63388C4.39946 4.8683 4.08152 5 3.75 5C3.33579 5 3 5.33579 3 5.75C3 6.16421 3.33579 6.5 3.75 6.5C4.08152 6.5 4.39946 6.6317 4.63388 6.86612C4.8683 7.10054 5 7.41848 5 7.75C5 8.16421 5.33579 8.5 5.75 8.5C6.16421 8.5 6.5 8.16421 6.5 7.75C6.5 7.41848 6.6317 7.10054 6.86612 6.86612C7.10054 6.6317 7.41848 6.5 7.75 6.5C8.16421 6.5 8.5 6.16421 8.5 5.75C8.5 5.33579 8.16421 5 7.75 5C7.41848 5 7.10054 4.8683 6.86612 4.63388C6.6317 4.39946 6.5 4.08152 6.5 3.75ZM5.69454 5.69454C5.71333 5.67576 5.73182 5.65673 5.75 5.63746C5.76818 5.65673 5.78667 5.67576 5.80546 5.69454C5.82424 5.71333 5.84327 5.73182 5.86254 5.75C5.84327 5.76818 5.82424 5.78667 5.80546 5.80546C5.78667 5.82424 5.76818 5.84327 5.75 5.86254C5.73182 5.84327 5.71333 5.82424 5.69454 5.80546C5.67576 5.78667 5.65673 5.76818 5.63746 5.75C5.65673 5.73182 5.67576 5.71333 5.69454 5.69454Z"}),t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M17.25 3.67188C16.3009 3.67188 15.3908 4.04889 14.7197 4.71997L13.7253 5.71433L13.7197 5.71991L13.7141 5.72555L4.21967 15.22C4.07902 15.3606 4 15.5514 4 15.7503V19.7503C4 20.1645 4.33579 20.5003 4.75 20.5003H8.75C8.94891 20.5003 9.13968 20.4213 9.28033 20.2806L19.7803 9.78063C20.4514 9.10955 20.8284 8.19936 20.8284 7.2503C20.8284 6.30124 20.4514 5.39106 19.7803 4.71997C19.1092 4.04889 18.1991 3.67188 17.25 3.67188ZM5.5 16.061L14.25 7.31093L17.1894 10.2503L8.43934 19.0003H5.5V16.061ZM15.3107 6.25027L18.25 9.18961L18.7197 8.71997C19.1095 8.33019 19.3284 7.80154 19.3284 7.2503C19.3284 6.69907 19.1095 6.17041 18.7197 5.78063C18.3299 5.39085 17.8012 5.17188 17.25 5.17188C16.6988 5.17188 16.1701 5.39085 15.7803 5.78063L15.3107 6.25027Z"})))),Ye=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{d:"M15 7.25C14.5858 7.25 14.25 7.58579 14.25 8C14.25 8.41421 14.5858 8.75 15 8.75H15.01C15.4242 8.75 15.76 8.41421 15.76 8C15.76 7.58579 15.4242 7.25 15.01 7.25H15Z"}),t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M21.75 6C21.75 5.00544 21.3549 4.05161 20.6517 3.34835C19.9484 2.64509 18.9946 2.25 18 2.25H6C5.00544 2.25 4.05161 2.64509 3.34835 3.34835C2.64509 4.05161 2.25 5.00544 2.25 6V18C2.25 18.9946 2.64509 19.9484 3.34835 20.6517C4.05161 21.3549 5.00544 21.75 6 21.75H18C18.9946 21.75 19.9484 21.3549 20.6517 20.6517C21.3549 19.9484 21.75 18.9946 21.75 18V6ZM4.40901 4.40901C4.83097 3.98705 5.40326 3.75 6 3.75H18C18.5967 3.75 19.169 3.98705 19.591 4.40901C20.0129 4.83097 20.25 5.40326 20.25 6V14.1894L18.5303 12.4697L18.52 12.4596C17.9434 11.9048 17.2466 11.5803 16.5 11.5803C15.7534 11.5803 15.0566 11.9048 14.48 12.4596L14.4697 12.4697L14 12.9394L11.5303 10.4697L11.52 10.4596C10.9434 9.90478 10.2466 9.58032 9.5 9.58032C8.75344 9.58032 8.05657 9.90478 7.47996 10.4596L7.46967 10.4697L3.75 14.1894V6C3.75 5.40326 3.98705 4.83097 4.40901 4.40901ZM15.0607 14.0001L15.5249 13.5358C15.8745 13.2012 16.2119 13.0803 16.5 13.0803C16.7881 13.0803 17.1254 13.2012 17.4751 13.5358L20.25 16.3107V18C20.25 18.5967 20.0129 19.169 19.591 19.591C19.169 20.0129 18.5967 20.25 18 20.25H6C5.40326 20.25 4.83097 20.0129 4.40901 19.591C3.98705 19.169 3.75 18.5967 3.75 18V16.3107L8.52489 11.5358C8.87455 11.2012 9.21189 11.0803 9.5 11.0803C9.78811 11.0803 10.1254 11.2012 10.4751 11.5358L15.4697 16.5304C15.7626 16.8233 16.2374 16.8233 16.5303 16.5304C16.8232 16.2375 16.8232 15.7626 16.5303 15.4697L15.0607 14.0001Z"})))),qe=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M13.9697 3.46967C14.2626 3.17678 14.7374 3.17678 15.0303 3.46967L20.5303 8.96967C20.8232 9.26256 20.8232 9.73744 20.5303 10.0303C20.2476 10.313 19.7954 10.3229 19.5009 10.0598L16.1452 13.4155L14.7022 17.2633C14.6647 17.3636 14.606 17.4546 14.5303 17.5303L13.0303 19.0303C12.7374 19.3232 12.2626 19.3232 11.9697 19.0303L9 16.0607L5.03033 20.0303C4.73744 20.3232 4.26256 20.3232 3.96967 20.0303C3.67678 19.7374 3.67678 19.2626 3.96967 18.9697L7.93934 15L4.96967 12.0303C4.67678 11.7374 4.67678 11.2626 4.96967 10.9697L6.46967 9.46967C6.54537 9.39396 6.63641 9.33535 6.73666 9.29775L10.5845 7.8548L13.9402 4.49915C13.6771 4.20465 13.687 3.75238 13.9697 3.46967ZM15 5.56066L11.5303 9.03033C11.4546 9.10604 11.3636 9.16465 11.2633 9.20225L7.41546 10.6452L6.56066 11.5L9.53015 14.4695L12.5 17.4393L13.3548 16.5845L14.7978 12.7367C14.8353 12.6364 14.894 12.5454 14.9697 12.4697L18.4393 9L15 5.56066Z"})))),Qe=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{d:"M3.53033 2.46967C3.23744 2.17678 2.76256 2.17678 2.46967 2.46967C2.17678 2.76256 2.17678 3.23744 2.46967 3.53033L7.82784 8.8885L6.73662 9.29777C6.63639 9.33536 6.54537 9.39397 6.46967 9.46967L4.96967 10.9697C4.67678 11.2626 4.67678 11.7374 4.96967 12.0303L7.93934 15L3.96967 18.9697C3.67678 19.2626 3.67678 19.7374 3.96967 20.0303C4.26256 20.3232 4.73744 20.3232 5.03033 20.0303L9 16.0607L11.9697 19.0303C12.2626 19.3232 12.7374 19.3232 13.0303 19.0303L14.5303 17.5303C14.606 17.4546 14.6646 17.3636 14.7022 17.2634L15.1115 16.1722L20.4697 21.5303C20.7626 21.8232 21.2374 21.8232 21.5303 21.5303C21.8232 21.2374 21.8232 20.7626 21.5303 20.4697L15.4044 14.3438C15.3707 14.3018 15.3324 14.2633 15.2897 14.229L3.53033 2.46967ZM12.5 17.4393L6.56066 11.5L7.41547 10.6452L8.9929 10.0536L13.9465 15.0072L13.3548 16.5845L12.5 17.4393Z"}),t.createElement("path",{d:"M15.5303 3.96967L15.0303 3.46967C14.7374 3.17678 14.2626 3.17678 13.9697 3.46967C13.687 3.75238 13.6771 4.20465 13.9402 4.49915L11.2207 7.21867C10.9278 7.51156 10.9278 7.98644 11.2207 8.27933C11.5136 8.57222 11.9884 8.57222 12.2813 8.27933L15 5.56066L18.4394 9.00008L15.7196 11.7208C15.4267 12.0137 15.4268 12.4886 15.7198 12.7814C16.0127 13.0743 16.4876 13.0742 16.7804 12.7812L19.5009 10.0599C19.7954 10.3229 20.2476 10.313 20.5303 10.0303C20.8232 9.73744 20.8232 9.26256 20.5303 8.96967L15.5303 3.96967Z"})))),$e=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{d:"M15.2803 4.28033C15.5732 3.98744 15.5732 3.51256 15.2803 3.21967C14.9874 2.92678 14.5126 2.92678 14.2197 3.21967L11.1425 6.29686L10.0653 5.21967C9.7724 4.92678 9.29752 4.92678 9.00463 5.21967L6.95568 7.26862C6.32944 7.87215 5.82945 8.59421 5.48481 9.39279C5.13918 10.1937 4.95675 11.0555 4.94819 11.9277C4.93964 12.8 5.10513 13.6652 5.43499 14.4727C5.66989 15.0478 5.98478 15.5854 6.36936 16.07L3.21967 19.2197C2.92678 19.5126 2.92678 19.9874 3.21967 20.2803C3.51256 20.5732 3.98744 20.5732 4.28033 20.2803L7.43002 17.1306C7.91462 17.5152 8.45219 17.8301 9.02723 18.065C9.83475 18.3948 10.7 18.5603 11.5722 18.5518C11.9864 18.5477 12.3189 18.2086 12.3148 17.7944C12.3108 17.3803 11.9717 17.0478 11.5575 17.0518C10.8847 17.0584 10.2173 16.9308 9.59445 16.6764C8.97157 16.4219 8.40569 16.0458 7.92992 15.57C7.45415 15.0943 7.07804 14.5284 6.82361 13.9055C6.56917 13.2826 6.44152 12.6153 6.44812 11.9425C6.45472 11.2696 6.59544 10.6049 6.86204 9.98714C7.12864 9.36937 7.51577 8.81098 8.00079 8.34463L8.01129 8.33433L9.53496 6.81066L16.0046 13.2803C16.2975 13.5732 16.7724 13.5732 17.0653 13.2803C17.3105 13.0351 17.3504 12.6624 17.185 12.3756L20.2803 9.28033C20.5732 8.98744 20.5732 8.51256 20.2803 8.21967C19.9874 7.92678 19.5126 7.92678 19.2197 8.21967L16.1425 11.2969L12.2031 7.35752L15.2803 4.28033Z"}),t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M20.2803 13.4911C20.5732 13.784 20.5732 14.2589 20.2803 14.5518L16.2803 18.5518C15.9874 18.8447 15.5126 18.8447 15.2197 18.5518L13.2197 16.5518C12.9268 16.2589 12.9268 15.784 13.2197 15.4911C13.5126 15.1982 13.9874 15.1982 14.2803 15.4911L15.75 16.9608L19.2197 13.4911C19.5126 13.1982 19.9874 13.1982 20.2803 13.4911Z"})))),Ct=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M15.5303 3.46967C15.8232 3.76256 15.8232 4.23744 15.5303 4.53033L12.4532 7.60746L16.3925 11.5468L19.4697 8.46967C19.7626 8.17678 20.2374 8.17678 20.5303 8.46967C20.8232 8.76256 20.8232 9.23744 20.5303 9.53033L17.4532 12.6075L18.5304 13.6847C18.6711 13.8253 18.7501 14.0161 18.7501 14.215C18.7501 14.4139 18.6711 14.6047 18.5304 14.7453L16.4815 16.7943C15.8779 17.4205 15.1559 17.9205 14.3573 18.2651C13.5564 18.6108 12.6946 18.7932 11.8223 18.8018C10.9501 18.8103 10.0849 18.6448 9.27735 18.315C8.70228 18.0801 8.16469 17.7652 7.68008 17.3806L4.53033 20.5303C4.23744 20.8232 3.76256 20.8232 3.46967 20.5303C3.17678 20.2374 3.17678 19.7626 3.46967 19.4697L6.61943 16.3199C6.23487 15.8353 5.92 15.2978 5.68511 14.7227C5.35525 13.9152 5.18976 13.05 5.19831 12.1777C5.20687 11.3055 5.3893 10.4437 5.73494 9.64279C6.07957 8.84421 6.57956 8.12216 7.20579 7.51863L9.25475 5.46967C9.54765 5.17678 10.0225 5.17678 10.3154 5.46967L11.3925 6.5468L14.4697 3.46967C14.7626 3.17678 15.2374 3.17678 15.5303 3.46967ZM9.78508 7.06066L8.26141 8.58433L8.25091 8.59463C7.7659 9.06098 7.37876 9.61937 7.11216 10.2371C6.84556 10.8549 6.70484 11.5196 6.69824 12.1925C6.69164 12.8653 6.81929 13.5326 7.07373 14.1555C7.32816 14.7784 7.70427 15.3443 8.18004 15.82C8.65581 16.2958 9.22169 16.6719 9.84457 16.9264C10.4674 17.1808 11.1348 17.3084 11.8076 17.3018C12.4804 17.2952 13.1452 17.1545 13.7629 16.8879C14.3807 16.6213 14.9391 16.2342 15.4055 15.7492L15.4158 15.7387L16.9394 14.215L9.78508 7.06066Z"})))),et=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{d:"M15.2803 3.21967C15.5732 3.51256 15.5732 3.98744 15.2803 4.28033L12.2028 7.35783L16.1422 11.2972L19.2197 8.21967C19.5126 7.92678 19.9874 7.92678 20.2803 8.21967C20.5732 8.51256 20.5732 8.98744 20.2803 9.28033L17.1847 12.376C17.3498 12.6627 17.3098 13.0352 17.0647 13.2803C16.7718 13.5732 16.2969 13.5732 16.004 13.2803L9.53435 6.81066L8.01068 8.33433L8.00018 8.34463C7.51516 8.81098 7.12803 9.36937 6.86143 9.98714C6.59483 10.6049 6.45411 11.2696 6.44751 11.9425C6.44091 12.6153 6.56856 13.2826 6.823 13.9055C7.07743 14.5284 7.45354 15.0943 7.92931 15.57C8.22439 15.8651 8.55414 16.1219 8.91066 16.335C9.26618 16.5476 9.38207 17.0081 9.16951 17.3636C8.95695 17.7191 8.49643 17.835 8.14091 17.6225C7.89339 17.4745 7.65581 17.3103 7.42975 17.1309L4.28033 20.2803C3.98744 20.5732 3.51256 20.5732 3.21967 20.2803C2.92678 19.9874 2.92678 19.5126 3.21967 19.2197L6.36902 16.0703C5.98432 15.5856 5.66933 15.0479 5.43438 14.4727C5.10452 13.6652 4.93903 12.8 4.94758 11.9277C4.95614 11.0555 5.13857 10.1937 5.4842 9.39279C5.82884 8.59421 6.32883 7.87215 6.95507 7.26862L9.00402 5.21967C9.29691 4.92678 9.77179 4.92678 10.0647 5.21967L11.1422 6.29716L14.2197 3.21967C14.5126 2.92678 14.9874 2.92678 15.2803 3.21967Z"}),t.createElement("path",{d:"M14.0346 13.4648C13.2717 13.442 12.5201 13.6637 11.8951 14.0974C11.6882 14.2411 11.4988 14.4054 11.3296 14.5871C11.296 14.2897 11.0301 14.073 10.731 14.1015C10.4286 14.1303 10.2068 14.3988 10.2356 14.7012L10.3697 16.1102C10.3985 16.4126 10.667 16.6344 10.9694 16.6056L11.1379 16.5896C11.1462 16.589 11.1545 16.5882 11.1627 16.5872L12.4187 16.4676C12.7211 16.4388 12.9429 16.1704 12.9141 15.868C12.8853 15.5656 12.6168 15.3438 12.3144 15.3726L12.0824 15.3947C12.2106 15.2475 12.3581 15.1151 12.5223 15.0011C12.9525 14.7025 13.4725 14.5485 14.0018 14.5643C14.531 14.5801 15.0368 14.7647 15.442 15.0867C15.8468 15.4084 16.1286 15.8492 16.247 16.3397C16.3183 16.635 16.6154 16.8166 16.9107 16.7453C17.206 16.674 17.3876 16.3768 17.3163 16.0816C17.1394 15.3492 16.7202 14.6974 16.1263 14.2255C15.5328 13.7538 14.7976 13.4876 14.0346 13.4648Z"}),t.createElement("path",{d:"M13.3284 20.7439C14.0857 20.8389 14.855 20.6894 15.5182 20.3169C15.7379 20.1935 15.942 20.0478 16.1276 19.883C16.1329 20.1822 16.3771 20.4232 16.6775 20.4232C16.9813 20.4232 17.2275 20.1769 17.2275 19.8732V18.4577C17.2275 18.154 16.9813 17.9077 16.6775 17.9077H16.5082C16.4999 17.9075 16.4917 17.9075 16.4834 17.9077H15.2217C14.9179 17.9077 14.6717 18.154 14.6717 18.4577C14.6717 18.7615 14.9179 19.0077 15.2217 19.0077H15.4547C15.3132 19.142 15.1538 19.2599 14.9795 19.3578C14.5229 19.6143 13.9907 19.7183 13.4653 19.6524C12.94 19.5865 12.4539 19.3548 12.0811 18.9959C11.7086 18.6372 11.4699 18.1717 11.3985 17.6722C11.3555 17.3715 11.0769 17.1625 10.7762 17.2055C10.4755 17.2485 10.2666 17.5271 10.3096 17.8278C10.4162 18.5737 10.7718 19.2622 11.3182 19.7883C11.8644 20.3142 12.571 20.6489 13.3284 20.7439Z"})))),tt=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{d:"M15.2803 4.28033C15.5732 3.98744 15.5732 3.51256 15.2803 3.21967C14.9874 2.92678 14.5126 2.92678 14.2197 3.21967L11.1425 6.29686L10.0653 5.21967C9.7724 4.92678 9.29752 4.92678 9.00463 5.21967L6.95568 7.26862C6.32944 7.87215 5.82945 8.59421 5.48481 9.39279C5.13918 10.1937 4.95675 11.0555 4.94819 11.9277C4.93964 12.8 5.10513 13.6652 5.43499 14.4727C5.66989 15.0478 5.98478 15.5854 6.36936 16.07L3.21967 19.2197C2.92678 19.5126 2.92678 19.9874 3.21967 20.2803C3.51256 20.5732 3.98744 20.5732 4.28033 20.2803L7.43002 17.1306C7.91462 17.5152 8.45219 17.8301 9.02723 18.065C9.83475 18.3948 10.7 18.5603 11.5722 18.5518C11.9864 18.5477 12.3189 18.2086 12.3148 17.7944C12.3108 17.3803 11.9717 17.0478 11.5575 17.0518C10.8847 17.0584 10.2173 16.9308 9.59445 16.6764C8.97157 16.4219 8.40569 16.0458 7.92992 15.57C7.45415 15.0943 7.07804 14.5284 6.82361 13.9055C6.56917 13.2826 6.44152 12.6153 6.44812 11.9425C6.45472 11.2696 6.59544 10.6049 6.86204 9.98714C7.12864 9.36937 7.51577 8.81098 8.00079 8.34463L8.01129 8.33433L9.53496 6.81066L16.0046 13.2803C16.2975 13.5732 16.7724 13.5732 17.0653 13.2803C17.3105 13.0351 17.3504 12.6624 17.185 12.3756L20.2803 9.28033C20.5732 8.98744 20.5732 8.51256 20.2803 8.21967C19.9874 7.92678 19.5126 7.92678 19.2197 8.21967L16.1425 11.2969L12.2031 7.35752L15.2803 4.28033Z"}),t.createElement("path",{d:"M14.2197 15.2197C14.5126 14.9268 14.9874 14.9268 15.2803 15.2197L16.75 16.6893L18.2197 15.2197C18.5126 14.9268 18.9874 14.9268 19.2803 15.2197C19.5732 15.5126 19.5732 15.9874 19.2803 16.2803L17.8107 17.75L19.2803 19.2197C19.5732 19.5126 19.5732 19.9874 19.2803 20.2803C18.9874 20.5732 18.5126 20.5732 18.2197 20.2803L16.75 18.8107L15.2803 20.2803C14.9874 20.5732 14.5126 20.5732 14.2197 20.2803C13.9268 19.9874 13.9268 19.5126 14.2197 19.2197L15.6893 17.75L14.2197 16.2803C13.9268 15.9874 13.9268 15.5126 14.2197 15.2197Z"})))),nt=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M11 4.75C11 4.33579 11.3358 4 11.75 4C12.1642 4 12.5 4.33579 12.5 4.75V11H18.75C19.1642 11 19.5 11.3358 19.5 11.75C19.5 12.1642 19.1642 12.5 18.75 12.5H12.5V18.75C12.5 19.1642 12.1642 19.5 11.75 19.5C11.3358 19.5 11 19.1642 11 18.75V12.5H4.75C4.33579 12.5 4 12.1642 4 11.75C4 11.3358 4.33579 11 4.75 11H11V4.75Z"})))),rt=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{d:"M12 7C12.9797 6.99994 13.9378 7.28769 14.7553 7.8275C15.5729 8.36731 16.2138 9.1354 16.5986 10.0364C16.9833 10.9373 17.0949 11.9315 16.9195 12.8953C16.7441 13.8592 16.2893 14.7502 15.6118 15.4579C14.9343 16.1655 14.0638 16.6585 13.1085 16.8756C12.1532 17.0928 11.1552 17.0245 10.2383 16.6793C9.3215 16.334 8.52629 15.7271 7.95146 14.9338C7.37663 14.1405 7.04752 13.1958 7.005 12.217L7 12L7.005 11.783C7.06092 10.4958 7.61161 9.27978 8.54222 8.38866C9.47284 7.49754 10.7115 7.00007 12 7Z"})))),lt=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M19 19.25C19.1381 19.25 19.25 19.1381 19.25 19V4.75L4.75 4.75L4.75 19.25L19 19.25ZM19 20.75C19.9665 20.75 20.75 19.9665 20.75 19L20.75 5C20.75 4.0335 19.9665 3.25 19 3.25L5 3.25C4.0335 3.25 3.25 4.0335 3.25 5L3.25 19C3.25 19.9665 4.0335 20.75 5 20.75L19 20.75ZM4.75 19.25L4.75 4.75L4.75 19C4.75 19.1381 4.86193 19.25 5 19.25H4.75ZM4.75 4.75L19.25 4.75L19.25 5C19.25 4.86193 19.1381 4.75 19 4.75L5 4.75C4.86193 4.75 4.75 4.86193 4.75 5V4.75Z"}),t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M11.5911 7.46967C11.884 7.17678 12.3588 7.17678 12.6517 7.46967L16.6519 11.4698C16.9448 11.7627 16.9448 12.2376 16.6519 12.5305C16.359 12.8234 15.8841 12.8234 15.5912 12.5305L11.5911 8.53033C11.2982 8.23744 11.2982 7.76256 11.5911 7.46967Z"}),t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M16.6514 7.46967C16.9443 7.76256 16.9443 8.23744 16.6514 8.53033L12.6513 12.5305C12.3584 12.8234 11.8835 12.8234 11.5906 12.5305C11.2977 12.2376 11.2977 11.7627 11.5906 11.4698L15.5908 7.46967C15.8837 7.17678 16.3585 7.17678 16.6514 7.46967Z"})))),ot=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7 3.75C6.30964 3.75 5.75 4.30964 5.75 5V19C5.75 19.6904 6.30964 20.25 7 20.25H17C17.6904 20.25 18.25 19.6904 18.25 19V5C18.25 4.30964 17.6904 3.75 17 3.75H7ZM4.25 5C4.25 3.48122 5.48122 2.25 7 2.25H17C18.5188 2.25 19.75 3.48122 19.75 5V19C19.75 20.5188 18.5188 21.75 17 21.75H7C5.48122 21.75 4.25 20.5188 4.25 19V5ZM8.25 7C8.25 6.58579 8.58579 6.25 9 6.25H15C15.4142 6.25 15.75 6.58579 15.75 7C15.75 7.41421 15.4142 7.75 15 7.75H9C8.58579 7.75 8.25 7.41421 8.25 7ZM8.25 11C8.25 10.5858 8.58579 10.25 9 10.25H15C15.4142 10.25 15.75 10.5858 15.75 11C15.75 11.4142 15.4142 11.75 15 11.75H9C8.58579 11.75 8.25 11.4142 8.25 11ZM8.25 15C8.25 14.5858 8.58579 14.25 9 14.25H13C13.4142 14.25 13.75 14.5858 13.75 15C13.75 15.4142 13.4142 15.75 13 15.75H9C8.58579 15.75 8.25 15.4142 8.25 15Z"})))),dt=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 3.75C7.44365 3.75 3.75 7.44365 3.75 12C3.75 16.5563 7.44365 20.25 12 20.25C16.5563 20.25 20.25 16.5563 20.25 12C20.25 7.44365 16.5563 3.75 12 3.75ZM2.25 12C2.25 6.61522 6.61522 2.25 12 2.25C17.3848 2.25 21.75 6.61522 21.75 12C21.75 17.3848 17.3848 21.75 12 21.75C6.61522 21.75 2.25 17.3848 2.25 12Z"})))),ct=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M20 20.76C19.5858 20.76 19.25 20.4242 19.25 20.01V20C19.25 19.5858 19.5858 19.25 20 19.25C20.4142 19.25 20.75 19.5858 20.75 20V20.01C20.75 20.4242 20.4142 20.76 20 20.76ZM12.75 20.01C12.75 20.4242 12.4142 20.76 12 20.76H5C4.0335 20.76 3.25 19.9765 3.25 19.01L3.25 12.01C3.25 11.5958 3.58579 11.26 4 11.26C4.41421 11.26 4.75 11.5958 4.75 12.01L4.75 19.01C4.75 19.1481 4.86193 19.26 5 19.26H12C12.4142 19.26 12.75 19.5958 12.75 20.01ZM20 12.76C19.5858 12.76 19.25 12.4242 19.25 12.01V12.0009C19.25 11.5867 19.5858 11.25 20 11.25C20.4142 11.25 20.75 11.5858 20.75 12V12.0091C20.75 12.4233 20.4142 12.76 20 12.76ZM20 4.76C19.5858 4.76 19.25 4.42421 19.25 4.01V4C19.25 3.58579 19.5858 3.25 20 3.25C20.4142 3.25 20.75 3.58579 20.75 4V4.01C20.75 4.42421 20.4142 4.76 20 4.76ZM12 4.76C11.5858 4.76 11.25 4.42421 11.25 4.01V4C11.25 3.58579 11.5858 3.25 12 3.25C12.4142 3.25 12.75 3.58579 12.75 4V4.01C12.75 4.42421 12.4142 4.76 12 4.76ZM4 4.76C3.58579 4.76 3.25 4.42421 3.25 4.01L3.25 4C3.25 3.58579 3.58579 3.25 4 3.25C4.41421 3.25 4.75 3.58579 4.75 4V4.01C4.75 4.42421 4.41421 4.76 4 4.76Z"})))),at=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4 20.76C4.41422 20.76 4.75 20.4242 4.75 20.01V20C4.75 19.5858 4.41421 19.25 4 19.25C3.58579 19.25 3.25 19.5858 3.25 20L3.25 20.01C3.25 20.4242 3.58579 20.76 4 20.76ZM11.25 20.01C11.25 20.4242 11.5858 20.76 12 20.76H19C19.9665 20.76 20.75 19.9765 20.75 19.01L20.75 12.01C20.75 11.5958 20.4142 11.26 20 11.26C19.5858 11.26 19.25 11.5958 19.25 12.01L19.25 19.01C19.25 19.1481 19.1381 19.26 19 19.26H12C11.5858 19.26 11.25 19.5958 11.25 20.01ZM4 12.76C4.41421 12.76 4.75 12.4242 4.75 12.01V12.0009C4.75 11.5867 4.41421 11.25 4 11.25C3.58579 11.25 3.25 11.5858 3.25 12L3.25 12.0091C3.25 12.4233 3.58579 12.76 4 12.76ZM4 4.76C4.41421 4.76 4.75 4.42421 4.75 4.01V4C4.75 3.58579 4.41421 3.25 4 3.25C3.58579 3.25 3.25 3.58579 3.25 4L3.25 4.01C3.25 4.42421 3.58579 4.76 4 4.76ZM12 4.76C12.4142 4.76 12.75 4.42421 12.75 4.01V4C12.75 3.58579 12.4142 3.25 12 3.25C11.5858 3.25 11.25 3.58579 11.25 4V4.01C11.25 4.42421 11.5858 4.76 12 4.76ZM20 4.76C20.4142 4.76 20.75 4.42421 20.75 4.01V4C20.75 3.58579 20.4142 3.25 20 3.25C19.5858 3.25 19.25 3.58579 19.25 4V4.01C19.25 4.42421 19.5858 4.76 20 4.76Z"})))),ft=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M20 3.25C19.5858 3.25 19.25 3.58579 19.25 4V4.01C19.25 4.42421 19.5858 4.76 20 4.76C20.4142 4.76 20.75 4.42421 20.75 4.01V4C20.75 3.58579 20.4142 3.25 20 3.25ZM12.75 4C12.75 3.58579 12.4142 3.25 12 3.25H5C4.0335 3.25 3.25 4.0335 3.25 5V12C3.25 12.4142 3.58579 12.75 4 12.75C4.41421 12.75 4.75 12.4142 4.75 12V5C4.75 4.86193 4.86193 4.75 5 4.75H12C12.4142 4.75 12.75 4.41421 12.75 4ZM20 11.25C19.5858 11.25 19.25 11.5858 19.25 12V12.0091C19.25 12.4233 19.5858 12.76 20 12.76C20.4142 12.76 20.75 12.4242 20.75 12.01V12.0009C20.75 11.5867 20.4142 11.25 20 11.25ZM20 19.25C19.5858 19.25 19.25 19.5858 19.25 20V20.01C19.25 20.4242 19.5858 20.76 20 20.76C20.4142 20.76 20.75 20.4242 20.75 20.01V20C20.75 19.5858 20.4142 19.25 20 19.25ZM12 19.25C11.5858 19.25 11.25 19.5858 11.25 20V20.01C11.25 20.4242 11.5858 20.76 12 20.76C12.4142 20.76 12.75 20.4242 12.75 20.01V20C12.75 19.5858 12.4142 19.25 12 19.25ZM4 19.25C3.58579 19.25 3.25 19.5858 3.25 20V20.01C3.25 20.4242 3.58579 20.76 4 20.76C4.41421 20.76 4.75 20.4242 4.75 20.01V20C4.75 19.5858 4.41421 19.25 4 19.25Z"})))),Lt=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4 3.25C4.41421 3.25 4.75 3.58579 4.75 4V4.01C4.75 4.42421 4.41421 4.76 4 4.76C3.58579 4.76 3.25 4.42421 3.25 4.01V4C3.25 3.58579 3.58579 3.25 4 3.25ZM11.25 4C11.25 3.58579 11.5858 3.25 12 3.25H19C19.9665 3.25 20.75 4.0335 20.75 5V12C20.75 12.4142 20.4142 12.75 20 12.75C19.5858 12.75 19.25 12.4142 19.25 12V5C19.25 4.86193 19.1381 4.75 19 4.75H12C11.5858 4.75 11.25 4.41421 11.25 4ZM4 11.25C4.41421 11.25 4.75 11.5858 4.75 12V12.0091C4.75 12.4233 4.41421 12.76 4 12.76C3.58579 12.76 3.25 12.4242 3.25 12.01V12.0009C3.25 11.5867 3.58579 11.25 4 11.25ZM4 19.25C4.41421 19.25 4.75 19.5858 4.75 20V20.01C4.75 20.4242 4.41421 20.76 4 20.76C3.58579 20.76 3.25 20.4242 3.25 20.01V20C3.25 19.5858 3.58579 19.25 4 19.25ZM12 19.25C12.4142 19.25 12.75 19.5858 12.75 20V20.01C12.75 20.4242 12.4142 20.76 12 20.76C11.5858 20.76 11.25 20.4242 11.25 20.01V20C11.25 19.5858 11.5858 19.25 12 19.25ZM20 19.25C20.4142 19.25 20.75 19.5858 20.75 20V20.01C20.75 20.4242 20.4142 20.76 20 20.76C19.5858 20.76 19.25 20.4242 19.25 20.01V20C19.25 19.5858 19.5858 19.25 20 19.25Z"})))),it=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7.55012 4.45178C9.23098 3.48072 11.1845 3.08925 13.1097 3.33767C15.035 3.58609 16.8251 4.46061 18.2045 5.82653C19.5838 7.19245 20.4757 8.97399 20.743 10.8967C20.8 11.307 20.5136 11.6858 20.1033 11.7428C19.6931 11.7998 19.3142 11.5135 19.2572 11.1032C19.0353 9.50635 18.2945 8.02677 17.149 6.89236C16.0035 5.75795 14.5167 5.03165 12.9178 4.82534C11.3189 4.61902 9.69644 4.94414 8.30047 5.75061C7.24361 6.36117 6.36093 7.22198 5.72541 8.24995H8.00009C8.41431 8.24995 8.75009 8.58574 8.75009 8.99995C8.75009 9.41417 8.41431 9.74995 8.00009 9.74995H4.51686C4.5055 9.75021 4.49412 9.75021 4.48272 9.74995H4.00009C3.58588 9.74995 3.25009 9.41417 3.25009 8.99995V4.99995C3.25009 4.58574 3.58588 4.24995 4.00009 4.24995C4.41431 4.24995 4.75009 4.58574 4.75009 4.99995V7.00691C5.48358 5.96916 6.43655 5.0951 7.55012 4.45178Z"}),t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3.89686 12.2571C4.30713 12.2001 4.68594 12.4864 4.74295 12.8967C4.96487 14.4936 5.70565 15.9731 6.85119 17.1075C7.99673 18.242 9.48347 18.9683 11.0824 19.1746C12.6813 19.3809 14.3037 19.0558 15.6997 18.2493C16.7566 17.6387 17.6393 16.7779 18.2748 15.75H16.0001C15.5859 15.75 15.2501 15.4142 15.2501 15C15.2501 14.5857 15.5859 14.25 16.0001 14.25H19.4833C19.4947 14.2497 19.5061 14.2497 19.5175 14.25H20.0001C20.4143 14.25 20.7501 14.5857 20.7501 15V19C20.7501 19.4142 20.4143 19.75 20.0001 19.75C19.5859 19.75 19.2501 19.4142 19.2501 19V16.993C18.5166 18.0307 17.5636 18.9048 16.4501 19.5481C14.7692 20.5192 12.8157 20.9107 10.8904 20.6622C8.9652 20.4138 7.17504 19.5393 5.79572 18.1734C4.4164 16.8074 3.52443 15.0259 3.25723 13.1032C3.20022 12.6929 3.48658 12.3141 3.89686 12.2571Z"})))),ut=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M18.2295 8.24989H15C14.5858 8.24989 14.25 8.58568 14.25 8.99989C14.25 9.4141 14.5858 9.74989 15 9.74989H19.4164C19.4276 9.75014 19.4389 9.75015 19.4502 9.74989H20C20.4142 9.74989 20.75 9.4141 20.75 8.99989V3.99989C20.75 3.58568 20.4142 3.24989 20 3.24989C19.5858 3.24989 19.25 3.58568 19.25 3.99989V7.05997C17.216 4.05724 13.4602 2.59135 9.82139 3.52648C8.28303 3.92219 6.88124 4.72923 5.76667 5.86098C4.65209 6.99273 3.86662 8.40653 3.49449 9.95076C3.12236 11.495 3.1776 13.1114 3.6543 14.6266C4.13101 16.1418 5.01118 17.4987 6.20042 18.5517C7.38967 19.6047 8.84313 20.3141 10.4049 20.6039C11.9667 20.8936 13.5779 20.7528 15.0657 20.1964C16.5535 19.64 17.8618 18.6892 18.8503 17.4458C19.8388 16.2024 20.4702 14.7134 20.6767 13.1384C20.7306 12.7277 20.4413 12.3511 20.0306 12.2972C19.6199 12.2434 19.2433 12.5327 19.1894 12.9434C19.0183 14.2483 18.4952 15.4821 17.6761 16.5123C16.8571 17.5426 15.7731 18.3304 14.5403 18.7914C13.3076 19.2524 11.9726 19.3691 10.6785 19.129C9.38447 18.889 8.18018 18.3012 7.1948 17.4287C6.20943 16.5562 5.48014 15.4319 5.08516 14.1765C4.69018 12.921 4.6444 11.5817 4.95274 10.3022C5.26108 9.02267 5.9119 7.85123 6.83541 6.9135C7.75887 5.9758 8.92032 5.30712 10.1949 4.97924M10.1949 4.97924C13.3573 4.1666 16.6106 5.53614 18.2295 8.24989L10.1949 4.97924Z"})))),Vt=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M16.4697 2.46967C16.7626 2.17678 17.2374 2.17678 17.5303 2.46967L20.5303 5.46967C20.8232 5.76256 20.8232 6.23744 20.5303 6.53033L17.5303 9.53033C17.2374 9.82322 16.7626 9.82322 16.4697 9.53033C16.1768 9.23744 16.1768 8.76256 16.4697 8.46967L18.1893 6.75H7C6.40326 6.75 5.83097 6.98705 5.40901 7.40901C4.98705 7.83097 4.75 8.40326 4.75 9V12C4.75 12.4142 4.41421 12.75 4 12.75C3.58579 12.75 3.25 12.4142 3.25 12V9C3.25 8.00544 3.64509 7.05161 4.34835 6.34835C5.05161 5.64509 6.00544 5.25 7 5.25H18.1893L16.4697 3.53033C16.1768 3.23744 16.1768 2.76256 16.4697 2.46967ZM19.25 15V12C19.25 11.5858 19.5858 11.25 20 11.25C20.4142 11.25 20.75 11.5858 20.75 12V15C20.75 15.9946 20.3549 16.9484 19.6516 17.6517C18.9484 18.3549 17.9946 18.75 17 18.75H5.81066L7.53033 20.4697C7.82322 20.7626 7.82322 21.2374 7.53033 21.5303C7.23744 21.8232 6.76256 21.8232 6.46967 21.5303L3.46967 18.5303C3.17678 18.2374 3.17678 17.7626 3.46967 17.4697L6.46967 14.4697C6.76256 14.1768 7.23744 14.1768 7.53033 14.4697C7.82322 14.7626 7.82322 15.2374 7.53033 15.5303L5.81066 17.25H17C17.5967 17.25 18.169 17.0129 18.591 16.591C19.0129 16.169 19.25 15.5967 19.25 15Z"})))),Ht=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12.0284 19.2895C13.7917 19.2948 15.4965 18.6572 16.8237 17.4962C18.1509 16.3351 19.0094 14.7302 19.2386 12.9818C19.2924 12.5711 19.669 12.2818 20.0797 12.3357C20.4904 12.3895 20.7797 12.7661 20.7259 13.1768C20.4493 15.2869 19.4131 17.2239 17.8114 18.6252C16.2096 20.0264 14.1521 20.7959 12.0239 20.7895C9.89574 20.7832 7.84284 20.0014 6.24949 18.5906C4.65615 17.1798 3.63161 15.2366 3.36764 13.1249C3.10367 11.0131 3.61838 8.87756 4.81542 7.11794C6.01245 5.35833 7.80975 4.0953 9.87087 3.56527C11.932 3.03525 14.1156 3.27457 16.013 4.23844C17.3102 4.89741 18.4128 5.86415 19.2322 7.04161V4.0793C19.2322 3.66508 19.568 3.32929 19.9822 3.32929C20.3964 3.32929 20.7322 3.66508 20.7322 4.0793V9.07929C20.7322 9.49351 20.3964 9.82929 19.9822 9.82929H19.4997C19.4882 9.82956 19.4767 9.82956 19.4652 9.82929L14.9822 9.82929C14.568 9.82929 14.2322 9.49351 14.2322 9.07929C14.2322 8.66508 14.568 8.32929 14.9822 8.32929H18.2788C17.5811 7.15812 16.5628 6.20019 15.3337 5.57578C13.7615 4.77714 11.9522 4.57884 10.2445 5.01801C8.53667 5.45717 7.04747 6.50368 6.05564 7.96165C5.06381 9.41962 4.63734 11.1891 4.85605 12.9388C5.07477 14.6885 5.92368 16.2986 7.24388 17.4675C8.56408 18.6365 10.2651 19.2843 12.0284 19.2895Z"})))),Mt=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M16.9592 3.25111C16.9728 3.25037 16.9864 3.25 17 3.25C17.9946 3.25 18.9484 3.64509 19.6516 4.34835C20.3549 5.05161 20.75 6.00544 20.75 7C20.75 7.01362 20.7496 7.02723 20.7489 7.04083C20.6439 8.96628 19.9703 10.8174 18.8133 12.36C17.756 13.7697 16.3385 14.864 14.7138 15.5305C14.6087 16.4992 14.2947 17.4358 13.7913 18.2748C13.2008 19.2591 12.369 20.0765 11.3746 20.6498C11.1562 20.7757 10.8892 20.7834 10.6638 20.6704C10.4385 20.5575 10.285 20.3388 10.2552 20.0885C10.1894 19.5343 10.0602 18.9933 9.87254 18.4751C9.29495 19.1405 8.58972 19.6897 7.79383 20.0877C6.59121 20.689 5.2364 20.9174 3.9031 20.7437C3.56575 20.6998 3.30023 20.4342 3.25628 20.0969C3.08257 18.7636 3.31102 17.4088 3.91233 16.2062C4.31027 15.4103 4.85953 14.705 5.5249 14.1275C5.00673 13.9398 4.46567 13.8106 3.91148 13.7448C3.66117 13.715 3.44254 13.5615 3.32955 13.3362C3.21656 13.1108 3.22434 12.8438 3.35024 12.6254C3.92353 11.631 4.74093 10.7992 5.72519 10.2087C6.56418 9.70526 7.50078 9.39133 8.46951 9.28616C9.13596 7.66154 10.2303 6.24394 11.64 5.18665C13.1826 4.02967 15.0337 3.3561 16.9592 3.25111ZM7.01588 14.8711C6.26748 15.3754 5.65964 16.0656 5.25397 16.877C4.87685 17.6312 4.6883 18.4624 4.70024 19.2997C5.53757 19.3117 6.36877 19.1231 7.12301 18.746C7.93435 18.3403 8.62458 17.7325 9.12888 16.9841C8.85626 16.566 8.5396 16.1748 8.18242 15.8176C7.82524 15.4604 7.43398 15.1437 7.01588 14.8711ZM17.0194 4.75008C15.3979 4.84272 13.8396 5.41196 12.54 6.38665C11.2347 7.36564 10.249 8.7097 9.70749 10.2489C9.60488 10.5406 9.33378 10.7394 9.02475 10.7496C8.13248 10.7791 7.26246 11.0356 6.49693 11.4949C6.04752 11.7645 5.64284 12.0988 5.29504 12.4854C6.7815 12.8789 8.14558 13.6594 9.24308 14.7569C10.3406 15.8544 11.1211 17.2185 11.5146 18.7049C11.9012 18.3571 12.2355 17.9525 12.5051 17.5031C12.9644 16.7375 13.2209 15.8675 13.2504 14.9752C13.2606 14.6662 13.4594 14.3951 13.7511 14.2925C15.2903 13.751 16.6344 12.7653 17.6133 11.46C18.588 10.1604 19.1573 8.60212 19.2499 6.98058C19.2448 6.39086 19.0084 5.82639 18.591 5.40901C18.1736 4.99163 17.6091 4.75516 17.0194 4.75008ZM13.7626 7.76256C14.0907 7.43437 14.5359 7.25 15 7.25C15.4641 7.25 15.9092 7.43437 16.2374 7.76256C16.5656 8.09075 16.75 8.53587 16.75 9C16.75 9.46413 16.5656 9.90925 16.2374 10.2374C15.9092 10.5656 15.4641 10.75 15 10.75C14.5359 10.75 14.0907 10.5656 13.7626 10.2374C13.4344 9.90925 13.25 9.46413 13.25 9C13.25 8.53587 13.4344 8.09075 13.7626 7.76256ZM15 8.75C14.9337 8.75 14.8701 8.77634 14.8232 8.82322C14.7763 8.87011 14.75 8.9337 14.75 9C14.75 9.0663 14.7763 9.12989 14.8232 9.17678C14.8701 9.22366 14.9337 9.25 15 9.25C15.0663 9.25 15.1299 9.22366 15.1768 9.17678C15.2237 9.12989 15.25 9.0663 15.25 9C15.25 8.9337 15.2237 8.87011 15.1768 8.82322C15.1299 8.77634 15.0663 8.75 15 8.75Z"})))),Zt=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{d:"M11.9917 3.28959C14.1198 3.28323 16.1774 4.0527 17.7791 5.45396C19.3809 6.85522 20.417 8.79222 20.6936 10.9023C20.7475 11.313 20.4582 11.6896 20.0475 11.7435C19.6368 11.7973 19.2602 11.508 19.2064 11.0973C18.9772 9.3489 18.1186 7.74397 16.7915 6.58292C15.4643 5.42187 13.7595 4.78431 11.9962 4.78958C10.2328 4.79486 8.53185 5.4426 7.21165 6.61156C5.89146 7.78053 5.04255 9.39057 4.82383 11.1403C4.60511 12.89 5.03159 14.6595 6.02342 16.1175C7.01525 17.5754 8.50444 18.6219 10.2122 19.0611C11.92 19.5003 13.7293 19.302 15.3014 18.5033C16.5306 17.8789 17.5489 16.921 18.2465 15.7498H14.95C14.5358 15.7498 14.2 15.414 14.2 14.9998C14.2 14.5856 14.5358 14.2498 14.95 14.2498H19.433C19.4444 14.2496 19.4559 14.2496 19.4675 14.2498H19.95C20.3642 14.2498 20.7 14.5856 20.7 14.9998V19.9998C20.7 20.414 20.3642 20.7498 19.95 20.7498C19.5358 20.7498 19.2 20.414 19.2 19.9998V17.0375C18.3806 18.215 17.278 19.1817 15.9808 19.8407C14.0834 20.8045 11.8998 21.0439 9.83865 20.5138C7.77753 19.9838 5.98023 18.7208 4.78319 16.9612C3.58615 15.2016 3.07144 13.066 3.33541 10.9542C3.59938 8.8425 4.62393 6.89935 6.21727 5.48853C7.81061 4.07771 9.86351 3.29595 11.9917 3.28959Z"})))),Rt=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{d:"M11.6894 4.7136C12.7138 4.18782 13.8486 3.91357 15.0001 3.91357C16.1515 3.91357 17.2863 4.18782 18.3107 4.7136C19.3351 5.23938 20.2195 6.00158 20.8907 6.93711C21.1322 7.27365 21.6007 7.35073 21.9373 7.10926C22.2738 6.86779 22.3509 6.39921 22.1094 6.06266C21.2993 4.93358 20.2319 4.01368 18.9957 3.37912C17.7594 2.74456 16.3897 2.41357 15.0001 2.41357C13.6104 2.41357 12.2408 2.74456 11.0045 3.37912C9.76817 4.01368 8.70079 4.93358 7.89068 6.06266C7.64921 6.39921 7.72629 6.86779 8.06284 7.10926C8.39938 7.35073 8.86796 7.27365 9.10943 6.93711C9.78066 6.00158 10.6651 5.23938 11.6894 4.7136Z"}),t.createElement("path",{d:"M15.0001 7.83179C14.4839 7.83179 13.9752 7.95472 13.516 8.19042C13.0568 8.42611 12.6603 8.76779 12.3594 9.18716C12.118 9.52371 11.6494 9.60079 11.3128 9.35932C10.9763 9.11785 10.8992 8.64927 11.1407 8.31272C11.5805 7.69979 12.1599 7.20042 12.831 6.85594C13.5022 6.51146 14.2457 6.33179 15.0001 6.33179C15.7544 6.33179 16.498 6.51146 17.1691 6.85594C17.8402 7.20042 18.4197 7.69979 18.8594 8.31272C19.1009 8.64927 19.0238 9.11785 18.6873 9.35932C18.3507 9.60079 17.8822 9.52371 17.6407 9.18716C17.3398 8.76779 16.9433 8.42611 16.4841 8.19042C16.0249 7.95472 15.5162 7.83179 15.0001 7.83179Z"}),t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M15.75 11C15.75 10.5858 15.4142 10.25 15 10.25C14.5858 10.25 14.25 10.5858 14.25 11V12.25H5C4.27065 12.25 3.57118 12.5397 3.05546 13.0555C2.53973 13.5712 2.25 14.2707 2.25 15V19C2.25 19.7293 2.53973 20.4288 3.05546 20.9445C3.57118 21.4603 4.27065 21.75 5 21.75H19C19.7293 21.75 20.4288 21.4603 20.9445 20.9445C21.4603 20.4288 21.75 19.7293 21.75 19V15C21.75 14.2707 21.4603 13.5712 20.9445 13.0555C20.4288 12.5397 19.7293 12.25 19 12.25H15.75V11ZM5 13.75C4.66848 13.75 4.35054 13.8817 4.11612 14.1161C3.8817 14.3505 3.75 14.6685 3.75 15V19C3.75 19.3315 3.8817 19.6495 4.11612 19.8839C4.35054 20.1183 4.66848 20.25 5 20.25H19C19.3315 20.25 19.6495 20.1183 19.8839 19.8839C20.1183 19.6495 20.25 19.3315 20.25 19V15C20.25 14.6685 20.1183 14.3505 19.8839 14.1161C19.6495 13.8817 19.3315 13.75 19 13.75H5Z"})))),vt=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4.75 4C4.41848 4 4.10054 4.1317 3.86612 4.36612C3.6317 4.60054 3.5 4.91848 3.5 5.25V16.25C3.5 16.5815 3.6317 16.8995 3.86612 17.1339C4.10054 17.3683 4.41848 17.5 4.75 17.5C5.16421 17.5 5.5 17.8358 5.5 18.25C5.5 18.6642 5.16421 19 4.75 19C4.02066 19 3.32118 18.7103 2.80546 18.1945C2.28973 17.6788 2 16.9793 2 16.25V5.25C2 4.52065 2.28973 3.82118 2.80546 3.30546C3.32118 2.78973 4.02065 2.5 4.75 2.5H8.75C8.94891 2.5 9.13968 2.57902 9.28033 2.71967L12.0607 5.5H18.75C19.4793 5.5 20.1788 5.78973 20.6945 6.30546C21.2103 6.82118 21.5 7.52066 21.5 8.25V11.75C21.5 12.1642 21.1642 12.5 20.75 12.5C20.3358 12.5 20 12.1642 20 11.75V8.25C20 7.91848 19.8683 7.60054 19.6339 7.36612C19.3995 7.1317 19.0815 7 18.75 7H11.75C11.5511 7 11.3603 6.92098 11.2197 6.78033L8.43934 4H4.75ZM11.25 14.5C11.6642 14.5 12 14.8358 12 15.25V19.1785L13.1738 17.7699C13.439 17.4517 13.9119 17.4087 14.2301 17.6738C14.5483 17.939 14.5913 18.4119 14.3262 18.7301L11.8262 21.7301C11.6837 21.9011 11.4726 22 11.25 22C11.0274 22 10.8163 21.9011 10.6738 21.7301L8.17383 18.7301C7.90866 18.4119 7.95165 17.939 8.26986 17.6738C8.58807 17.4087 9.06099 17.4517 9.32617 17.7699L10.5 19.1785V15.25C10.5 14.8358 10.8358 14.5 11.25 14.5ZM19.25 14.5C19.4726 14.5 19.6837 14.5989 19.8262 14.7699L22.3262 17.7699C22.5913 18.0881 22.5483 18.561 22.2301 18.8262C21.9119 19.0913 21.439 19.0483 21.1738 18.7301L20 17.3215V21.25C20 21.6642 19.6642 22 19.25 22C18.8358 22 18.5 21.6642 18.5 21.25V17.3215L17.3262 18.7301C17.061 19.0483 16.5881 19.0913 16.2699 18.8262C15.9517 18.561 15.9087 18.0881 16.1738 17.7699L18.6738 14.7699C18.8163 14.5989 19.0274 14.5 19.25 14.5Z"})))),pt=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M13 3.25C13.4142 3.25 13.75 3.58579 13.75 4C13.75 4.41421 13.4142 4.75 13 4.75H4C3.9337 4.75 3.87013 4.77636 3.82324 4.82324C3.77636 4.87013 3.75 4.9337 3.75 5V15L3.75488 15.0488C3.76447 15.0969 3.78804 15.1416 3.82324 15.1768C3.87013 15.2236 3.9337 15.25 4 15.25H20C20.0663 15.25 20.1299 15.2236 20.1768 15.1768C20.2236 15.1299 20.25 15.0663 20.25 15V12C20.25 11.5858 20.5858 11.25 21 11.25C21.4142 11.25 21.75 11.5858 21.75 12V15C21.75 15.4641 21.5655 15.9091 21.2373 16.2373C20.9091 16.5655 20.4641 16.75 20 16.75H15.75V19.25H17C17.4142 19.25 17.75 19.5858 17.75 20C17.75 20.4142 17.4142 20.75 17 20.75H7C6.58579 20.75 6.25 20.4142 6.25 20C6.25 19.5858 6.58579 19.25 7 19.25H8.25V16.75H4C3.53587 16.75 3.09088 16.5655 2.7627 16.2373C2.47551 15.9501 2.29855 15.5735 2.25879 15.1729L2.25 15V5C2.25 4.53587 2.43451 4.09088 2.7627 3.7627C3.09088 3.43451 3.53587 3.25 4 3.25H13ZM9.75 19.25H14.25V16.75H9.75V19.25Z"}),t.createElement("path",{d:"M21.0264 3.25098C21.0326 3.2512 21.0387 3.25158 21.0449 3.25195L21.0771 3.25391C21.0927 3.2555 21.1078 3.26016 21.123 3.2627C21.1457 3.26647 21.1681 3.27051 21.1904 3.27637C21.2156 3.28296 21.2396 3.29171 21.2637 3.30078C21.2806 3.30717 21.2979 3.31265 21.3145 3.32031C21.3407 3.33247 21.3652 3.34731 21.3896 3.3623C21.4041 3.37115 21.4187 3.37973 21.4326 3.38965C21.4563 3.40648 21.4784 3.42493 21.5 3.44434C21.5097 3.4531 21.5209 3.46036 21.5303 3.46973C21.5339 3.47331 21.5365 3.47781 21.54 3.48145C21.5588 3.501 21.5752 3.52244 21.5918 3.54395C21.6036 3.55918 21.6155 3.57395 21.626 3.58984C21.6431 3.61592 21.6571 3.64367 21.6709 3.67188C21.6767 3.68382 21.6843 3.69485 21.6895 3.70703C21.7 3.73189 21.707 3.75803 21.7148 3.78418C21.7209 3.8044 21.7281 3.82415 21.7324 3.84473C21.7356 3.85983 21.737 3.8752 21.7393 3.89062C21.7437 3.92086 21.7473 3.95096 21.748 3.98145C21.7482 3.98763 21.75 3.99378 21.75 4V8C21.75 8.41421 21.4142 8.75 21 8.75C20.5858 8.75 20.25 8.41421 20.25 8V5.81055L16.5303 9.53027C16.2374 9.82317 15.7626 9.82317 15.4697 9.53027C15.1769 9.23738 15.1768 8.76261 15.4697 8.46973L19.1895 4.75H17C16.5858 4.75 16.25 4.41421 16.25 4C16.25 3.58579 16.5858 3.25 17 3.25H21L21.0264 3.25098Z"})))),mt=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10 3.75C6.54822 3.75 3.75 6.54822 3.75 10C3.75 13.4518 6.54822 16.25 10 16.25C13.4518 16.25 16.25 13.4518 16.25 10C16.25 6.54822 13.4518 3.75 10 3.75ZM2.25 10C2.25 5.71979 5.71979 2.25 10 2.25C14.2802 2.25 17.75 5.71979 17.75 10C17.75 11.87 17.0877 13.5853 15.9848 14.9242L21.5303 20.4697C21.8232 20.7626 21.8232 21.2374 21.5303 21.5303C21.2374 21.8232 20.7626 21.8232 20.4697 21.5303L14.9242 15.9848C13.5853 17.0877 11.87 17.75 10 17.75C5.71979 17.75 2.25 14.2802 2.25 10Z"})))),Et=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7 3.75C6.66848 3.75 6.35054 3.8817 6.11612 4.11612C5.8817 4.35054 5.75 4.66848 5.75 5V19C5.75 19.3315 5.8817 19.6495 6.11612 19.8839C6.35054 20.1183 6.66848 20.25 7 20.25H12C12.4142 20.25 12.75 20.5858 12.75 21C12.75 21.4142 12.4142 21.75 12 21.75H7C6.27065 21.75 5.57118 21.4603 5.05546 20.9445C4.53973 20.4288 4.25 19.7293 4.25 19V5C4.25 4.27065 4.53973 3.57118 5.05546 3.05546C5.57118 2.53973 6.27065 2.25 7 2.25H14C14.1989 2.25 14.3897 2.32902 14.5303 2.46967L19.5303 7.46967C19.671 7.61032 19.75 7.80109 19.75 8V12.5C19.75 12.9142 19.4142 13.25 19 13.25C18.5858 13.25 18.25 12.9142 18.25 12.5V8.75H15C14.5359 8.75 14.0908 8.56563 13.7626 8.23744C13.4344 7.90925 13.25 7.46413 13.25 7V3.75H7ZM14.75 4.81066L17.1893 7.25H15C14.9337 7.25 14.8701 7.22366 14.8232 7.17678C14.7763 7.12989 14.75 7.0663 14.75 7V4.81066ZM16.5 15.75C15.5335 15.75 14.75 16.5335 14.75 17.5C14.75 18.4665 15.5335 19.25 16.5 19.25C17.4665 19.25 18.25 18.4665 18.25 17.5C18.25 16.5335 17.4665 15.75 16.5 15.75ZM13.25 17.5C13.25 15.7051 14.7051 14.25 16.5 14.25C18.2949 14.25 19.75 15.7051 19.75 17.5C19.75 18.1257 19.5732 18.7102 19.2667 19.2061L21.5303 21.4697C21.8232 21.7626 21.8232 22.2374 21.5303 22.5303C21.2374 22.8232 20.7626 22.8232 20.4697 22.5303L18.2061 20.2667C17.7102 20.5732 17.1257 20.75 16.5 20.75C14.7051 20.75 13.25 19.2949 13.25 17.5Z"})))),wt=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3.75 5.25C3.6837 5.25 3.62011 5.27634 3.57322 5.32322C3.52634 5.37011 3.5 5.4337 3.5 5.5V6.5C3.5 6.91421 3.16421 7.25 2.75 7.25C2.33579 7.25 2 6.91421 2 6.5V5.5C2 5.03587 2.18438 4.59075 2.51256 4.26256C2.84075 3.93438 3.28587 3.75 3.75 3.75H4.75C5.16421 3.75 5.5 4.08579 5.5 4.5C5.5 4.91421 5.16421 5.25 4.75 5.25H3.75ZM8.5 4.5C8.5 4.08579 8.83579 3.75 9.25 3.75H12.25C12.6642 3.75 13 4.08579 13 4.5C13 4.91421 12.6642 5.25 12.25 5.25H9.25C8.83579 5.25 8.5 4.91421 8.5 4.5ZM16 4.5C16 4.08579 16.3358 3.75 16.75 3.75H17.75C18.2141 3.75 18.6592 3.93437 18.9874 4.26256C19.3156 4.59075 19.5 5.03587 19.5 5.5V6.5C19.5 6.91421 19.1642 7.25 18.75 7.25C18.3358 7.25 18 6.91421 18 6.5V5.5C18 5.43369 17.9737 5.37011 17.9268 5.32322C17.8799 5.27634 17.8163 5.25 17.75 5.25H16.75C16.3358 5.25 16 4.91421 16 4.5ZM2.75 9.75C3.16421 9.75 3.5 10.0858 3.5 10.5V13.5C3.5 13.9142 3.16421 14.25 2.75 14.25C2.33579 14.25 2 13.9142 2 13.5V10.5C2 10.0858 2.33579 9.75 2.75 9.75ZM12.75 13.25C12.6676 13.25 12.5982 13.281 12.5546 13.3217C12.5128 13.3607 12.5 13.4021 12.5 13.4333V18.5667C12.5 18.5979 12.5128 18.6393 12.5546 18.6783C12.5982 18.719 12.6676 18.75 12.75 18.75H19.75C19.8324 18.75 19.9018 18.719 19.9454 18.6783C19.9872 18.6393 20 18.5979 20 18.5667V14.8333C20 14.8021 19.9872 14.7607 19.9454 14.7217C19.9018 14.681 19.8324 14.65 19.75 14.65H16.25C16.06 14.65 15.8771 14.5779 15.7383 14.4483L14.4544 13.25H12.75ZM11.5312 12.2251C11.8627 11.9156 12.3019 11.75 12.75 11.75H14.75C14.94 11.75 15.1229 11.8221 15.2617 11.9517L16.5456 13.15H19.75C20.1981 13.15 20.6373 13.3156 20.9688 13.6251C21.3021 13.9361 21.5 14.3695 21.5 14.8333V18.5667C21.5 19.0305 21.3021 19.4639 20.9688 19.7749C20.6373 20.0844 20.1981 20.25 19.75 20.25H12.75C12.3019 20.25 11.8627 20.0844 11.5312 19.7749C11.1979 19.4639 11 19.0305 11 18.5667V13.4333C11 12.9695 11.1979 12.5361 11.5312 12.2251ZM2.75 16.75C3.16421 16.75 3.5 17.0858 3.5 17.5V18.5C3.5 18.5663 3.52634 18.6299 3.57322 18.6768C3.62011 18.7237 3.68369 18.75 3.75 18.75H4.75C5.16421 18.75 5.5 19.0858 5.5 19.5C5.5 19.9142 5.16421 20.25 4.75 20.25H3.75C3.28587 20.25 2.84075 20.0656 2.51256 19.7374C2.18437 19.4092 2 18.9641 2 18.5V17.5C2 17.0858 2.33579 16.75 2.75 16.75Z"})))),ht=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M11.4697 4.46967C11.7626 4.17678 12.2374 4.17678 12.5303 4.46967L16.5303 8.46967C16.8232 8.76256 16.8232 9.23744 16.5303 9.53033C16.2374 9.82322 15.7626 9.82322 15.4697 9.53033L12 6.06066L8.53033 9.53033C8.23744 9.82322 7.76256 9.82322 7.46967 9.53033C7.17678 9.23744 7.17678 8.76256 7.46967 8.46967L11.4697 4.46967ZM7.46967 14.4697C7.76256 14.1768 8.23744 14.1768 8.53033 14.4697L12 17.9393L15.4697 14.4697C15.7626 14.1768 16.2374 14.1768 16.5303 14.4697C16.8232 14.7626 16.8232 15.2374 16.5303 15.5303L12.5303 19.5303C12.2374 19.8232 11.7626 19.8232 11.4697 19.5303L7.46967 15.5303C7.17678 15.2374 7.17678 14.7626 7.46967 14.4697Z"})))),Bt=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M21.3144 12.6811L5.02733 20.6398C5.00765 20.6494 4.98756 20.6582 4.96711 20.666C4.73531 20.7551 4.48227 20.7733 4.2401 20.7183C3.99827 20.6634 3.77817 20.538 3.60769 20.3579C3.44196 20.183 3.32787 19.9657 3.27806 19.7299C3.22873 19.4964 3.24438 19.2539 3.32325 19.0288L5.70806 12L3.32325 4.97117C3.24438 4.74602 3.22873 4.5035 3.27806 4.27C3.32783 4.03443 3.4418 3.81723 3.60734 3.64242C3.77788 3.46214 3.99811 3.33656 4.2401 3.2816C4.48227 3.22661 4.73531 3.24481 4.96711 3.3339C4.98756 3.34175 5.00765 3.3505 5.02733 3.36012L21.3168 11.32C21.3536 11.3372 21.3889 11.3573 21.4222 11.38C21.4973 11.4311 21.5616 11.4949 21.6129 11.5677C21.7005 11.6918 21.7501 11.8421 21.7501 12C21.7501 12.0263 21.7487 12.0524 21.746 12.0782C21.7309 12.224 21.674 12.3574 21.5875 12.4662C21.5508 12.5125 21.5085 12.5546 21.4613 12.5914C21.4161 12.6267 21.3669 12.6569 21.3144 12.6811ZM7.03759 11.25L4.90715 4.9709L17.757 11.25H7.03759ZM7.03757 12.75L4.90715 19.029L17.7569 12.75H7.03757Z"})))),xt=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M21.5303 2.46969C21.7359 2.67531 21.8042 2.98125 21.7054 3.25475L15.2055 21.2548C15.1984 21.2743 15.1906 21.2936 15.1819 21.3125C15.0782 21.5388 14.9117 21.7305 14.7022 21.865C14.4927 21.9994 14.249 22.0709 14.0001 22.0709C13.7511 22.0709 13.5074 21.9994 13.2979 21.865C13.0928 21.7333 12.9288 21.5467 12.8248 21.3264L9.44099 14.559L2.67362 11.1753C2.45341 11.0712 2.26678 10.9073 2.13511 10.7021C2.00067 10.4926 1.9292 10.2489 1.9292 10C1.9292 9.75109 2.00067 9.5074 2.13511 9.2979C2.26956 9.0884 2.46132 8.92191 2.68762 8.8182C2.70652 8.80954 2.72578 8.80167 2.74534 8.79461L20.7453 2.29461C21.0188 2.19584 21.3247 2.26407 21.5303 2.46969ZM10.9126 14.1481L13.9693 20.2615L19.1524 5.90827L10.9126 14.1481ZM18.0917 4.84764L3.73856 10.0307L9.85191 13.0874L18.0917 4.84764Z"})))),It=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6 4.75C4.75736 4.75 3.75 5.75736 3.75 7V9C3.75 10.2426 4.75736 11.25 6 11.25H18C19.2426 11.25 20.25 10.2426 20.25 9V7C20.25 5.75736 19.2426 4.75 18 4.75H6ZM6 12.75C5.40326 12.75 4.83097 12.9871 4.40901 13.409C3.98705 13.831 3.75 14.4033 3.75 15V17C3.75 17.5967 3.98705 18.169 4.40901 18.591C4.83097 19.0129 5.40326 19.25 6 19.25H12C12.4142 19.25 12.75 19.5858 12.75 20C12.75 20.4142 12.4142 20.75 12 20.75H6C5.00544 20.75 4.05161 20.3549 3.34835 19.6516C2.64509 18.9484 2.25 17.9946 2.25 17V15C2.25 14.0054 2.64509 13.0516 3.34835 12.3483C3.47451 12.2222 3.60873 12.106 3.74981 12.0001C2.83908 11.316 2.25 10.2268 2.25 9V7C2.25 4.92893 3.92893 3.25 6 3.25H18C20.0711 3.25 21.75 4.92893 21.75 7V9C21.75 11.0711 20.0711 12.75 18 12.75H6ZM7 7.25C7.41421 7.25 7.75 7.58579 7.75 8V8.01C7.75 8.42421 7.41421 8.76 7 8.76C6.58579 8.76 6.25 8.42421 6.25 8.01V8C6.25 7.58579 6.58579 7.25 7 7.25ZM18.001 13.75C18.4152 13.75 18.751 14.0858 18.751 14.5V15.3535C19.1958 15.4793 19.5948 15.7143 19.9172 16.0276L20.6569 15.6005C21.0156 15.3934 21.4743 15.5163 21.6814 15.875C21.8885 16.2337 21.7656 16.6924 21.4069 16.8995L20.6678 17.3262C20.7221 17.5418 20.751 17.7675 20.751 18C20.751 18.2324 20.7222 18.458 20.6679 18.6735L21.4077 19.1004C21.7665 19.3074 21.8895 19.766 21.6826 20.1248C21.4756 20.4836 21.0169 20.6066 20.6581 20.3996L19.9174 19.9723C19.5949 20.2857 19.1959 20.5207 18.751 20.6465V21.5C18.751 21.9142 18.4152 22.25 18.001 22.25C17.5868 22.25 17.251 21.9142 17.251 21.5V20.6465C16.8063 20.5207 16.4073 20.2858 16.0849 19.9726L15.3448 20.3996C14.986 20.6066 14.5273 20.4836 14.3203 20.1248C14.1133 19.766 14.2364 19.3074 14.5952 19.1004L15.3342 18.674C15.2798 18.4583 15.251 18.2325 15.251 18C15.251 17.7675 15.2798 17.5417 15.3342 17.326L14.5952 16.8996C14.2364 16.6926 14.1133 16.234 14.3203 15.8752C14.5273 15.5164 14.986 15.3934 15.3448 15.6004L16.0849 16.0274C16.4073 15.7142 16.8063 15.4793 17.251 15.3535V14.5C17.251 14.0858 17.5868 13.75 18.001 13.75ZM16.9023 17.4033C16.9083 17.394 16.914 17.3845 16.9196 17.3748C16.9249 17.3656 16.93 17.3564 16.9348 17.3471C17.1547 16.9889 17.5499 16.75 18.001 16.75C18.4576 16.75 18.8571 16.9949 19.0752 17.3604C19.0779 17.3653 19.0806 17.3702 19.0834 17.375C19.0863 17.3801 19.0893 17.3852 19.0924 17.3902C19.1934 17.5706 19.251 17.7786 19.251 18C19.251 18.2215 19.1934 18.4295 19.0924 18.6099C19.0893 18.6149 19.0863 18.62 19.0833 18.6252C19.0805 18.63 19.0778 18.6347 19.0752 18.6395C18.8571 19.0051 18.4576 19.25 18.001 19.25C17.5499 19.25 17.1547 19.0111 16.9348 18.6529C16.93 18.6436 16.9249 18.6344 16.9196 18.6252C16.914 18.6155 16.9083 18.606 16.9023 18.5967C16.8058 18.4194 16.751 18.2161 16.751 18C16.751 17.7839 16.8058 17.5806 16.9023 17.4033ZM7 15.25C7.41421 15.25 7.75 15.5858 7.75 16V16.01C7.75 16.4242 7.41421 16.76 7 16.76C6.58579 16.76 6.25 16.4242 6.25 16.01V16C6.25 15.5858 6.58579 15.25 7 15.25Z"})))),st=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12.9461 4.49382C12.7055 3.50206 11.2945 3.50206 11.0539 4.49382L11.0538 4.49421C10.6578 6.12252 8.79686 6.89441 7.36336 6.02285L7.36299 6.02262C6.49035 5.49135 5.49253 6.49022 6.0235 7.3618C6.22619 7.69432 6.34752 8.06998 6.37762 8.45824C6.40773 8.84659 6.34572 9.23656 6.19663 9.59641C6.04755 9.95627 5.8156 10.2758 5.51966 10.5291C5.22378 10.7823 4.8723 10.9621 4.49382 11.0539C3.50206 11.2945 3.50206 12.7055 4.49382 12.9461L4.49422 12.9462C4.87244 13.0382 5.22363 13.2181 5.51923 13.4714C5.81483 13.7246 6.0465 14.0441 6.19542 14.4037C6.34433 14.7633 6.40629 15.153 6.37625 15.5411C6.34621 15.9292 6.22502 16.3047 6.02253 16.6371C5.49145 17.5098 6.49026 18.5074 7.3618 17.9765C7.69431 17.7738 8.06998 17.6525 8.45824 17.6224C8.84659 17.5923 9.23656 17.6543 9.59641 17.8034C9.95627 17.9525 10.2758 18.1844 10.5291 18.4803C10.7823 18.7762 10.9621 19.1277 11.0539 19.5062C11.2945 20.4979 12.7055 20.4979 12.9461 19.5062L12.9462 19.5058C13.0382 19.1276 13.2181 18.7764 13.4714 18.4808C13.7246 18.1852 14.0441 17.9535 14.4037 17.8046C14.7633 17.6557 15.153 17.5937 15.5411 17.6238C15.9292 17.6538 16.3047 17.775 16.6371 17.9775C17.5097 18.5085 18.5074 17.5097 17.9765 16.6382C17.7738 16.3057 17.6525 15.93 17.6224 15.5418C17.5923 15.1534 17.6543 14.7634 17.8034 14.4036C17.9525 14.0437 18.1844 13.7242 18.4803 13.4709C18.7762 13.2177 19.1277 13.0379 19.5062 12.9461C20.4979 12.7055 20.4979 11.2945 19.5062 11.0539L19.5058 11.0538C19.1276 10.9618 18.7764 10.7819 18.4808 10.5286C18.1852 10.2754 17.9535 9.95594 17.8046 9.59631C17.6557 9.23668 17.5937 8.84698 17.6238 8.45889C17.6538 8.07081 17.775 7.69528 17.9775 7.36285C18.5085 6.49025 17.5097 5.49256 16.6382 6.0235C16.3057 6.22619 15.93 6.34752 15.5418 6.37762C15.1534 6.40773 14.7634 6.34572 14.4036 6.19663C14.0437 6.04755 13.7242 5.8156 13.4709 5.51966C13.2177 5.22378 13.0379 4.8723 12.9461 4.49382ZM9.59624 4.13979C10.2079 1.61994 13.7925 1.62007 14.4039 4.14018L14.4039 4.14039C14.44 4.28943 14.5108 4.42783 14.6105 4.54434C14.7102 4.66085 14.836 4.75216 14.9777 4.81086C15.1194 4.86955 15.2729 4.89397 15.4258 4.88211C15.5787 4.87026 15.7266 4.82247 15.8576 4.74264L15.8578 4.7425C18.0722 3.39347 20.6074 5.92764 19.2586 8.14301L19.2585 8.14315C19.1788 8.27403 19.1311 8.42187 19.1193 8.57465C19.1075 8.72744 19.1318 8.88086 19.1905 9.02245C19.2491 9.16404 19.3403 9.28979 19.4567 9.38949C19.573 9.4891 19.7111 9.5599 19.8598 9.59614C22.3801 10.2075 22.3801 13.7925 19.8598 14.4039L19.8596 14.4039C19.7106 14.44 19.5722 14.5108 19.4557 14.6105C19.3392 14.7102 19.2478 14.836 19.1891 14.9777C19.1304 15.1194 19.106 15.2729 19.1179 15.4258C19.1297 15.5787 19.1775 15.7266 19.2574 15.8576L19.2575 15.8578C20.6065 18.0722 18.0724 20.6074 15.857 19.2586L15.8569 19.2585C15.726 19.1788 15.5781 19.1311 15.4253 19.1193C15.2726 19.1075 15.1191 19.1318 14.9776 19.1905C14.836 19.2491 14.7102 19.3403 14.6105 19.4567C14.5109 19.573 14.4401 19.7111 14.4039 19.8598C13.7925 22.3801 10.2075 22.3801 9.59614 19.8598L9.59609 19.8596C9.55998 19.7106 9.48919 19.5722 9.38948 19.4557C9.28977 19.3392 9.16396 19.2478 9.02228 19.1891C8.88061 19.1304 8.72708 19.106 8.57419 19.1179C8.4213 19.1297 8.27337 19.1775 8.14244 19.2574L8.1422 19.2575C5.92778 20.6065 3.39265 18.0724 4.74138 15.857L4.74147 15.8569C4.82118 15.726 4.86889 15.5781 4.88072 15.4253C4.89255 15.2726 4.86816 15.1191 4.80953 14.9776C4.7509 14.836 4.65969 14.7102 4.54332 14.6105C4.42705 14.5109 4.28893 14.4401 4.14018 14.4039C1.61994 13.7925 1.61994 10.2075 4.14018 9.59614L4.14039 9.59609C4.28943 9.55998 4.42783 9.48919 4.54434 9.38948C4.66085 9.28977 4.75216 9.16396 4.81086 9.02228C4.86955 8.88061 4.89397 8.72708 4.88211 8.57419C4.87026 8.4213 4.82247 8.27337 4.74264 8.14244L4.7425 8.1422C3.39354 5.92791 5.92736 3.39294 8.14263 4.74115C8.70903 5.08552 9.4399 4.7816 9.59614 4.14018M12 9.75C10.7574 9.75 9.75 10.7574 9.75 12C9.75 13.2426 10.7574 14.25 12 14.25C13.2426 14.25 14.25 13.2426 14.25 12C14.25 10.7574 13.2426 9.75 12 9.75ZM8.25 12C8.25 9.92893 9.92893 8.25 12 8.25C14.0711 8.25 15.75 9.92893 15.75 12C15.75 14.0711 14.0711 15.75 12 15.75C9.92893 15.75 8.25 14.0711 8.25 12Z"})))),gt=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M11.5031 2.43828C11.7869 2.18724 12.2133 2.18724 12.497 2.43828C14.6869 4.3757 17.545 5.38444 20.4658 5.25078C20.8109 5.23499 21.1222 5.45701 21.2196 5.78848C21.7016 7.42793 21.849 9.14753 21.6533 10.8451C21.4575 12.5427 20.9225 14.1836 20.08 15.6703C19.2375 17.157 18.1048 18.4592 16.7491 19.4995C15.3934 20.5398 13.8425 21.2969 12.1884 21.726C12.0649 21.758 11.9353 21.758 11.8118 21.726C10.1577 21.2969 8.60672 20.5398 7.25104 19.4995C5.89536 18.4592 4.76263 17.157 3.92016 15.6703C3.07769 14.1836 2.54267 12.5427 2.34689 10.8451C2.15112 9.14753 2.29858 7.42793 2.78052 5.78848C2.87796 5.45701 3.18923 5.23499 3.53436 5.25078C6.45517 5.38444 9.31327 4.3757 11.5031 2.43828ZM4.07272 6.76253C3.76637 8.04085 3.68608 9.36448 3.83702 10.6733C4.00976 12.1711 4.48183 13.619 5.22519 14.9308C5.96855 16.2426 6.96802 17.3916 8.16421 18.3095C9.30881 19.1878 10.6112 19.8374 12.0001 20.2234C13.389 19.8374 14.6913 19.1878 15.836 18.3095C17.0321 17.3916 18.0316 16.2426 18.775 14.9308C19.5183 13.619 19.9904 12.1711 20.1631 10.6733C20.3141 9.36447 20.2338 8.04085 19.9274 6.76253C17.0464 6.75719 14.2515 5.77568 12.0001 3.97935C9.74862 5.77568 6.95379 6.75719 4.07272 6.76253ZM15.5304 9.46967C15.8233 9.76256 15.8233 10.2374 15.5304 10.5303L11.5304 14.5303C11.2375 14.8232 10.7626 14.8232 10.4697 14.5303L8.46973 12.5303C8.17684 12.2374 8.17684 11.7626 8.46973 11.4697C8.76262 11.1768 9.2375 11.1768 9.53039 11.4697L11.0001 12.9393L14.4697 9.46967C14.7626 9.17678 15.2375 9.17678 15.5304 9.46967Z"})))),At=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M11.5031 2.43828C11.7869 2.18724 12.2133 2.18724 12.497 2.43828C14.6869 4.3757 17.545 5.38444 20.4658 5.25078C20.8109 5.23499 21.1222 5.45701 21.2196 5.78848C21.7016 7.42793 21.849 9.14753 21.6533 10.8451C21.4575 12.5427 20.9225 14.1836 20.08 15.6703C19.2375 17.157 18.1048 18.4592 16.7491 19.4995C15.3934 20.5398 13.8425 21.2969 12.1884 21.726C12.0649 21.758 11.9353 21.758 11.8118 21.726C10.1577 21.2969 8.60672 20.5398 7.25104 19.4995C5.89536 18.4592 4.76263 17.157 3.92016 15.6703C3.07769 14.1836 2.54267 12.5427 2.34689 10.8451C2.15112 9.14753 2.29858 7.42793 2.78052 5.78848C2.87796 5.45701 3.18923 5.23499 3.53436 5.25078C6.45517 5.38444 9.31327 4.3757 11.5031 2.43828ZM4.07272 6.76253C3.76637 8.04085 3.68608 9.36448 3.83702 10.6733C4.00976 12.1711 4.48183 13.619 5.22519 14.9308C5.96855 16.2426 6.96802 17.3916 8.16421 18.3095C9.30881 19.1878 10.6112 19.8374 12.0001 20.2234C13.389 19.8374 14.6913 19.1878 15.836 18.3095C17.0321 17.3916 18.0316 16.2426 18.775 14.9308C19.5183 13.619 19.9904 12.1711 20.1631 10.6733C20.3141 9.36447 20.2338 8.04085 19.9274 6.76253C17.0464 6.75719 14.2515 5.77568 12.0001 3.97935C9.74862 5.77568 6.95379 6.75719 4.07272 6.76253Z"})))),St=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{d:"M5.46967 9.53033C5.17678 9.23744 5.17678 8.76256 5.46967 8.46967C5.76256 8.17678 6.23744 8.17678 6.53033 8.46967L9.53033 11.4697C9.82322 11.7626 9.82322 12.2374 9.53033 12.5303L6.53033 15.5303C6.23744 15.8232 5.76256 15.8232 5.46967 15.5303C5.17678 15.2374 5.17678 14.7626 5.46967 14.4697L7.18934 12.75H3C2.58579 12.75 2.25 12.4142 2.25 12C2.25 11.5858 2.58579 11.25 3 11.25H7.18934L5.46967 9.53033Z"}),t.createElement("path",{d:"M17.4697 8.46967C17.7626 8.17678 18.2374 8.17678 18.5303 8.46967C18.8232 8.76256 18.8232 9.23744 18.5303 9.53033L16.8107 11.25H21C21.4142 11.25 21.75 11.5858 21.75 12C21.75 12.4142 21.4142 12.75 21 12.75H16.8107L18.5303 14.4697C18.8232 14.7626 18.8232 15.2374 18.5303 15.5303C18.2374 15.8232 17.7626 15.8232 17.4697 15.5303L14.4697 12.5303C14.1768 12.2374 14.1768 11.7626 14.4697 11.4697L17.4697 8.46967Z"})))),Tt=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6.25 4C6.25 3.58579 6.58579 3.25 7 3.25H17C17.4142 3.25 17.75 3.58579 17.75 4C17.75 4.41421 17.4142 4.75 17 4.75H7C6.58579 4.75 6.25 4.41421 6.25 4ZM4 6.25C4.41421 6.25 4.75 6.58579 4.75 7V17C4.75 17.4142 4.41421 17.75 4 17.75C3.58579 17.75 3.25 17.4142 3.25 17V7C3.25 6.58579 3.58579 6.25 4 6.25ZM20 6.25C20.4142 6.25 20.75 6.58579 20.75 7V17C20.75 17.4142 20.4142 17.75 20 17.75C19.5858 17.75 19.25 17.4142 19.25 17V7C19.25 6.58579 19.5858 6.25 20 6.25ZM6.25 20C6.25 19.5858 6.58579 19.25 7 19.25H17C17.4142 19.25 17.75 19.5858 17.75 20C17.75 20.4142 17.4142 20.75 17 20.75H7C6.58579 20.75 6.25 20.4142 6.25 20Z"})))),yt=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4 3.25C4.41421 3.25 4.75 3.58579 4.75 4V4.01C4.75 4.42421 4.41421 4.76 4 4.76C3.58579 4.76 3.25 4.42421 3.25 4.01V4C3.25 3.58579 3.58579 3.25 4 3.25ZM12 3.25C12.4142 3.25 12.75 3.58579 12.75 4V4.01C12.75 4.42421 12.4142 4.76 12 4.76C11.5858 4.76 11.25 4.42421 11.25 4.01V4C11.25 3.58579 11.5858 3.25 12 3.25ZM20 3.25C20.4142 3.25 20.75 3.58579 20.75 4V4.01C20.75 4.42421 20.4142 4.76 20 4.76C19.5858 4.76 19.25 4.42421 19.25 4.01V4C19.25 3.58579 19.5858 3.25 20 3.25ZM4 11.25C4.41421 11.25 4.75 11.5858 4.75 12V12.01C4.75 12.4242 4.41421 12.76 4 12.76C3.58579 12.76 3.25 12.4242 3.25 12.01V12C3.25 11.5858 3.58579 11.25 4 11.25ZM20 11.25C20.4142 11.25 20.75 11.5858 20.75 12V12.01C20.75 12.4242 20.4142 12.76 20 12.76C19.5858 12.76 19.25 12.4242 19.25 12.01V12C19.25 11.5858 19.5858 11.25 20 11.25ZM3.25 20C3.25 19.5858 3.58579 19.25 4 19.25H20C20.4142 19.25 20.75 19.5858 20.75 20C20.75 20.4142 20.4142 20.75 20 20.75H4C3.58579 20.75 3.25 20.4142 3.25 20Z"})))),Pt=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4 3.25C4.41421 3.25 4.75 3.58579 4.75 4V20C4.75 20.4142 4.41421 20.75 4 20.75C3.58579 20.75 3.25 20.4142 3.25 20V4C3.25 3.58579 3.58579 3.25 4 3.25ZM12 3.25C12.4142 3.25 12.75 3.58579 12.75 4V4.01C12.75 4.42421 12.4142 4.76 12 4.76C11.5858 4.76 11.25 4.42421 11.25 4.01V4C11.25 3.58579 11.5858 3.25 12 3.25ZM20 3.25C20.4142 3.25 20.75 3.58579 20.75 4V4.01C20.75 4.42421 20.4142 4.76 20 4.76C19.5858 4.76 19.25 4.42421 19.25 4.01V4C19.25 3.58579 19.5858 3.25 20 3.25ZM20 11.25C20.4142 11.25 20.75 11.5858 20.75 12V12.01C20.75 12.4242 20.4142 12.76 20 12.76C19.5858 12.76 19.25 12.4242 19.25 12.01V12C19.25 11.5858 19.5858 11.25 20 11.25ZM12 19.25C12.4142 19.25 12.75 19.5858 12.75 20V20.01C12.75 20.4242 12.4142 20.76 12 20.76C11.5858 20.76 11.25 20.4242 11.25 20.01V20C11.25 19.5858 11.5858 19.25 12 19.25ZM20 19.25C20.4142 19.25 20.75 19.5858 20.75 20V20.01C20.75 20.4242 20.4142 20.76 20 20.76C19.5858 20.76 19.25 20.4242 19.25 20.01V20C19.25 19.5858 19.5858 19.25 20 19.25Z"})))),kt=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4 3.25C4.41421 3.25 4.75 3.58579 4.75 4V4.01C4.75 4.42421 4.41421 4.76 4 4.76C3.58579 4.76 3.25 4.42421 3.25 4.01V4C3.25 3.58579 3.58579 3.25 4 3.25ZM12 3.25C12.4142 3.25 12.75 3.58579 12.75 4V4.01C12.75 4.42421 12.4142 4.76 12 4.76C11.5858 4.76 11.25 4.42421 11.25 4.01V4C11.25 3.58579 11.5858 3.25 12 3.25ZM20 3.25C20.4142 3.25 20.75 3.58579 20.75 4V20C20.75 20.4142 20.4142 20.75 20 20.75C19.5858 20.75 19.25 20.4142 19.25 20V4C19.25 3.58579 19.5858 3.25 20 3.25ZM4 11.25C4.41421 11.25 4.75 11.5858 4.75 12V12.01C4.75 12.4242 4.41421 12.76 4 12.76C3.58579 12.76 3.25 12.4242 3.25 12.01V12C3.25 11.5858 3.58579 11.25 4 11.25ZM4 19.25C4.41421 19.25 4.75 19.5858 4.75 20V20.01C4.75 20.4242 4.41421 20.76 4 20.76C3.58579 20.76 3.25 20.4242 3.25 20.01V20C3.25 19.5858 3.58579 19.25 4 19.25ZM12 19.25C12.4142 19.25 12.75 19.5858 12.75 20V20.01C12.75 20.4242 12.4142 20.76 12 20.76C11.5858 20.76 11.25 20.4242 11.25 20.01V20C11.25 19.5858 11.5858 19.25 12 19.25Z"})))),bt=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3.25 4C3.25 3.58579 3.58579 3.25 4 3.25H20C20.4142 3.25 20.75 3.58579 20.75 4C20.75 4.41421 20.4142 4.75 20 4.75H4C3.58579 4.75 3.25 4.41421 3.25 4ZM4 11.25C4.41421 11.25 4.75 11.5858 4.75 12V12.01C4.75 12.4242 4.41421 12.76 4 12.76C3.58579 12.76 3.25 12.4242 3.25 12.01V12C3.25 11.5858 3.58579 11.25 4 11.25ZM20 11.25C20.4142 11.25 20.75 11.5858 20.75 12V12.01C20.75 12.4242 20.4142 12.76 20 12.76C19.5858 12.76 19.25 12.4242 19.25 12.01V12C19.25 11.5858 19.5858 11.25 20 11.25ZM4 19.25C4.41421 19.25 4.75 19.5858 4.75 20V20.01C4.75 20.4242 4.41421 20.76 4 20.76C3.58579 20.76 3.25 20.4242 3.25 20.01V20C3.25 19.5858 3.58579 19.25 4 19.25ZM12 19.25C12.4142 19.25 12.75 19.5858 12.75 20V20.01C12.75 20.4242 12.4142 20.76 12 20.76C11.5858 20.76 11.25 20.4242 11.25 20.01V20C11.25 19.5858 11.5858 19.25 12 19.25ZM20 19.25C20.4142 19.25 20.75 19.5858 20.75 20V20.01C20.75 20.4242 20.4142 20.76 20 20.76C19.5858 20.76 19.25 20.4242 19.25 20.01V20C19.25 19.5858 19.5858 19.25 20 19.25Z"})))),Dt=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 3.75C11.138 3.75 10.3114 4.09241 9.7019 4.7019C9.09241 5.3114 8.75 6.13805 8.75 7V10.25H15.25V7C15.25 6.13805 14.9076 5.3114 14.2981 4.7019C13.6886 4.09241 12.862 3.75 12 3.75ZM16.75 10.25V7C16.75 5.74022 16.2496 4.53204 15.3588 3.64124C14.468 2.75044 13.2598 2.25 12 2.25C10.7402 2.25 9.53204 2.75044 8.64124 3.64124C7.75044 4.53204 7.25 5.74022 7.25 7V10.25H7C6.27065 10.25 5.57118 10.5397 5.05546 11.0555C4.53973 11.5712 4.25 12.2707 4.25 13V19C4.25 19.7293 4.53973 20.4288 5.05546 20.9445C5.57118 21.4603 6.27065 21.75 7 21.75H17C17.7293 21.75 18.4288 21.4603 18.9445 20.9445C19.4603 20.4288 19.75 19.7293 19.75 19V13C19.75 12.2707 19.4603 11.5712 18.9445 11.0555C18.4288 10.5397 17.7293 10.25 17 10.25H16.75ZM7 11.75C6.66848 11.75 6.35054 11.8817 6.11612 12.1161C5.8817 12.3505 5.75 12.6685 5.75 13V19C5.75 19.3315 5.8817 19.6495 6.11612 19.8839C6.35054 20.1183 6.66848 20.25 7 20.25H17C17.3315 20.25 17.6495 20.1183 17.8839 19.8839C18.1183 19.6495 18.25 19.3315 18.25 19V13C18.25 12.6685 18.1183 12.3505 17.8839 12.1161C17.6495 11.8817 17.3315 11.75 17 11.75H7ZM12.75 13.25V14.7865L14.3354 13.9938L15.0062 15.3354L13.6771 16L15.0062 16.6646L14.3354 18.0062L12.75 17.2135V18.75H11.25V17.2135L9.66459 18.0062L8.99377 16.6646L10.3229 16L8.99377 15.3354L9.66459 13.9938L11.25 14.7865V13.25H12.75Z"})))),Ft=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 2.75C11.138 2.75 10.3114 3.09241 9.7019 3.7019C9.09241 4.3114 8.75 5.13805 8.75 6V10.25H17C18.5188 10.25 19.75 11.4812 19.75 13V19C19.75 20.5188 18.5188 21.75 17 21.75H7C5.48122 21.75 4.25 20.5188 4.25 19V13C4.25 11.4812 5.48122 10.25 7 10.25H7.25V6C7.25 4.74022 7.75044 3.53204 8.64124 2.64124C9.53204 1.75045 10.7402 1.25 12 1.25C13.2598 1.25 14.468 1.75045 15.3588 2.64124C16.2496 3.53204 16.75 4.74022 16.75 6C16.75 6.41421 16.4142 6.75 16 6.75C15.5858 6.75 15.25 6.41421 15.25 6C15.25 5.13805 14.9076 4.3114 14.2981 3.7019C13.6886 3.09241 12.862 2.75 12 2.75ZM7 11.75C6.30964 11.75 5.75 12.3096 5.75 13V19C5.75 19.6904 6.30964 20.25 7 20.25H17C17.6904 20.25 18.25 19.6904 18.25 19V13C18.25 12.3096 17.6904 11.75 17 11.75H7Z"}),t.createElement("path",{d:"M14.3353 13.9938L15.0061 15.3354L13.6769 16L15.0061 16.6646L14.3353 18.0062L12.7499 17.2135V18.75H11.2499V17.2135L9.66447 18.0062L8.99365 16.6646L10.3228 16L8.99365 15.3354L9.66447 13.9938L11.2499 14.7865V13.25H12.7499V14.7865L14.3353 13.9938Z"})))),Ut=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 3.75C11.3096 3.75 10.75 4.30964 10.75 5C10.75 5.69036 11.3096 6.25 12 6.25C12.6904 6.25 13.25 5.69036 13.25 5C13.25 4.30964 12.6904 3.75 12 3.75ZM9.25 5C9.25 3.48122 10.4812 2.25 12 2.25C13.5188 2.25 14.75 3.48122 14.75 5C14.75 6.25878 13.9043 7.32002 12.75 7.64648V10.325C14.4617 10.6725 15.75 12.1858 15.75 14C15.75 14.5476 15.6326 15.0677 15.4217 15.5367L17.2735 16.8594C17.7455 16.4782 18.3461 16.25 19 16.25C20.5188 16.25 21.75 17.4812 21.75 19C21.75 20.5188 20.5188 21.75 19 21.75C17.4812 21.75 16.25 20.5188 16.25 19C16.25 18.6786 16.3051 18.3701 16.4064 18.0835L14.5452 16.754C13.8766 17.3722 12.9824 17.75 12 17.75C11.0176 17.75 10.1234 17.3722 9.45478 16.754L7.59356 18.0834C7.69487 18.3701 7.75 18.6786 7.75 19C7.75 20.5188 6.51878 21.75 5 21.75C3.48122 21.75 2.25 20.5188 2.25 19C2.25 17.4812 3.48122 16.25 5 16.25C5.65392 16.25 6.25453 16.4782 6.72651 16.8594L8.5783 15.5367C8.36736 15.0677 8.25 14.5476 8.25 14C8.25 12.1858 9.53832 10.6725 11.25 10.325V7.64648C10.0957 7.32002 9.25 6.25878 9.25 5ZM12 11.75C10.7574 11.75 9.75 12.7574 9.75 14C9.75 15.2426 10.7574 16.25 12 16.25C13.2426 16.25 14.25 15.2426 14.25 14C14.25 12.7574 13.2426 11.75 12 11.75ZM5 17.75C4.30964 17.75 3.75 18.3096 3.75 19C3.75 19.6904 4.30964 20.25 5 20.25C5.69036 20.25 6.25 19.6904 6.25 19C6.25 18.3096 5.69036 17.75 5 17.75ZM19 17.75C18.3096 17.75 17.75 18.3096 17.75 19C17.75 19.6904 18.3096 20.25 19 20.25C19.6904 20.25 20.25 19.6904 20.25 19C20.25 18.3096 19.6904 17.75 19 17.75Z"})))),Ot=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M15.0221 2.2505C14.7086 2.2428 14.3993 2.32462 14.1306 2.48639C13.8619 2.64816 13.6449 2.88316 13.5049 3.16386L10.9999 8.19197L3.92384 11.2998C3.49889 11.4865 3.16549 11.8343 2.99698 12.2667C2.82848 12.6992 2.83867 13.1809 3.02531 13.6058L4.63384 17.2682C4.82048 17.6931 5.16829 18.0265 5.60074 18.195C6.0332 18.3635 6.51489 18.3533 6.93984 18.1667L8.9999 17.2619L10.709 21.1532C10.8956 21.5781 11.2434 21.9115 11.6759 22.08C12.1083 22.2485 12.59 22.2383 13.015 22.0517L13.9306 21.6496C14.3555 21.4629 14.6889 21.1151 14.8574 20.6827C15.0259 20.2502 15.0157 19.7685 14.8291 19.3436L13.12 15.4523L14.0159 15.0588L19.4131 16.616C19.7145 16.7029 20.0346 16.7021 20.3355 16.6137C20.6364 16.5253 20.9059 16.3529 21.1124 16.1168C21.3188 15.8807 21.4537 15.5907 21.5013 15.2806C21.5488 14.9707 21.507 14.6537 21.3808 14.3667M21.3808 14.3667L20.4142 12.1659C21.0102 11.7447 21.4752 11.1551 21.7441 10.4649C22.1052 9.53824 22.0834 8.50606 21.6834 7.59546C21.2835 6.68486 20.5382 5.97043 19.6115 5.60934C18.9213 5.3404 18.1726 5.28388 17.4591 5.43778L16.4927 3.23726C16.3667 2.95018 16.1613 2.70457 15.901 2.52988C15.6405 2.35511 15.3357 2.2582 15.0221 2.2505M18.0836 6.85961C18.4152 6.83504 18.7512 6.88399 19.0669 7.00698C19.6229 7.22364 20.0701 7.6523 20.3101 8.19866C20.55 8.74502 20.5631 9.36433 20.3465 9.92035C20.2235 10.236 20.0321 10.5166 19.7897 10.7441L18.0836 6.85961ZM15.1191 3.8401C15.1077 3.81399 15.0889 3.79133 15.0652 3.77544C15.0415 3.75956 15.0138 3.75075 14.9853 3.75005C14.9568 3.74935 14.9287 3.75678 14.9042 3.77149C14.8798 3.7862 14.8601 3.80756 14.8474 3.83308L12.2214 9.10391C12.1432 9.26095 12.0123 9.38559 11.8517 9.45615L10.7072 9.95881L12.5168 14.0789L13.6613 13.5763C13.8219 13.5057 14.0022 13.4937 14.1708 13.5423L19.8288 15.1748C19.8562 15.1827 19.8852 15.1826 19.9126 15.1746C19.9399 15.1665 19.9645 15.1509 19.9832 15.1294C20.002 15.1079 20.0143 15.0816 20.0186 15.0534C20.0229 15.0252 20.0191 14.9964 20.0076 14.9703L15.1191 3.8401ZM11.7466 16.0555L13.4557 19.9468C13.4824 20.0075 13.4838 20.0763 13.4598 20.1381C13.4357 20.1999 13.3881 20.2495 13.3274 20.2762L12.4118 20.6783C12.3511 20.705 12.2823 20.7064 12.2205 20.6824C12.1587 20.6583 12.109 20.6107 12.0824 20.55L10.3733 16.6587L11.7466 16.0555ZM11.1434 14.6821L9.33384 10.562L4.52704 12.6732C4.46633 12.6999 4.4187 12.7496 4.39463 12.8113C4.37056 12.8731 4.37201 12.9419 4.39868 13.0026L6.00721 16.665C6.03387 16.7257 6.08356 16.7733 6.14534 16.7974C6.20712 16.8214 6.27593 16.82 6.33664 16.7933L11.1434 14.6821Z"})))),Jt=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12.0001 2C12.266 2 12.5089 2.15027 12.6266 2.38762L15.3384 7.85709L21.4017 8.73178C21.665 8.76976 21.8838 8.95331 21.9659 9.20518C22.0481 9.45705 21.9794 9.73351 21.7888 9.91822L17.395 14.1754L18.431 20.1871C18.476 20.4481 18.3681 20.7118 18.1528 20.8674C17.9375 21.0229 17.6522 21.0433 17.4168 20.9198L12.0063 18.0819L6.58287 20.9201C6.3475 21.0433 6.06229 21.0228 5.84715 20.8672C5.63202 20.7116 5.52429 20.448 5.56925 20.1871L6.60527 14.1754L2.21146 9.91822C2.02081 9.73351 1.95213 9.45705 2.0343 9.20518C2.11647 8.95331 2.33523 8.76976 2.59853 8.73178L8.66185 7.85709L11.3737 2.38762C11.4914 2.15027 11.7342 2 12.0001 2ZM12.0001 4.26658L9.75213 8.80054C9.65033 9.00586 9.45352 9.14813 9.22588 9.18097L4.20166 9.90576L7.84321 13.4341C8.0081 13.5938 8.08337 13.8242 8.04447 14.0499L7.18563 19.0334L11.6815 16.6806C11.8853 16.574 12.1287 16.5741 12.3323 16.6809L16.8143 19.0318L15.9558 14.0499C15.9169 13.8242 15.9921 13.5938 16.157 13.4341L19.7986 9.90576L14.7744 9.18097C14.5467 9.14813 14.3499 9.00586 14.2481 8.80054L12.0001 4.26658Z"})))),Wt=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12.0001 2C12.266 2 12.5089 2.15023 12.6266 2.38752L15.3384 7.85565L21.4017 8.73012C21.665 8.76809 21.8838 8.95158 21.9659 9.20338C22.0481 9.45518 21.9794 9.73157 21.7888 9.91624L17.6709 13.9057C17.3945 14.1735 16.9522 14.1676 16.6831 13.8925C16.4139 13.6174 16.4199 13.1773 16.6963 12.9095L19.7987 9.90383L14.7744 9.1792C14.5467 9.14637 14.3499 9.00413 14.2481 8.79887L12.0001 4.26602L10.7814 6.7235C10.6106 7.06779 10.1917 7.20915 9.84577 7.03924C9.49979 6.86932 9.35774 6.45247 9.52849 6.10817L11.3737 2.38753C11.4914 2.15023 11.7342 2 12.0001 2ZM3.12972 3.12872C3.40253 2.85724 3.84485 2.85724 4.11766 3.12872L17.6663 16.6115C17.6819 16.6257 17.6968 16.6406 17.7111 16.6561L20.8836 19.8132C21.1565 20.0847 21.1565 20.5249 20.8836 20.7964C20.6108 21.0679 20.1685 21.0679 19.8957 20.7964L18.2553 19.164L18.431 20.1825C18.476 20.4435 18.3682 20.7072 18.1528 20.8627C17.9375 21.0183 17.6522 21.0386 17.4168 20.9152L12.0063 18.0779L6.58287 20.9155C6.3475 21.0386 6.06228 21.0181 5.84715 20.8626C5.63202 20.707 5.52429 20.4434 5.56925 20.1826L6.60527 14.1724L2.21146 9.91627C2.02082 9.73161 1.95213 9.45521 2.0343 9.20341C2.11647 8.9516 2.33523 8.7681 2.59852 8.73012L7.11581 8.07859L3.12972 4.11187C2.8569 3.84038 2.8569 3.40021 3.12972 3.12872ZM8.34886 9.30565L4.20165 9.90381L7.84321 13.4313C8.0081 13.591 8.08337 13.8213 8.04447 14.047L7.18563 19.0293L11.6815 16.677C11.8853 16.5704 12.1287 16.5705 12.3323 16.6773L16.8142 19.0275L16.544 17.461L8.34886 9.30565Z"})))),jt=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M5 4.75C5 4.33579 5.33579 4 5.75 4H18.6203C19.0345 4 19.3703 4.33579 19.3703 4.75V6.46604C19.3703 6.88026 19.0345 7.21604 18.6203 7.21604C18.2061 7.21604 17.8703 6.88026 17.8703 6.46604V5.5H12.9355V10.6806H17.6203C18.0345 10.6806 18.3703 11.0164 18.3703 11.4306C18.3703 11.8448 18.0345 12.1806 17.6203 12.1806H12.9355V18.0852H13.7594C14.1736 18.0852 14.5094 18.421 14.5094 18.8352C14.5094 19.2494 14.1736 19.5852 13.7594 19.5852H10.6113C10.1971 19.5852 9.8613 19.2494 9.8613 18.8352C9.8613 18.421 10.1971 18.0852 10.6113 18.0852H11.4355V12.1806H6.75C6.33579 12.1806 6 11.8448 6 11.4306C6 11.0164 6.33579 10.6806 6.75 10.6806H11.4355V5.5H6.5V6.46604C6.5 6.88026 6.16421 7.21604 5.75 7.21604C5.33579 7.21604 5 6.88026 5 6.46604V4.75Z"})))),zt=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M11.6645 3.32918C11.8757 3.22361 12.1242 3.22361 12.3353 3.32918L20.3353 7.32918C20.5894 7.45622 20.7499 7.71592 20.7499 8C20.7499 8.28408 20.5894 8.54378 20.3353 8.67082L12.3353 12.6708C12.1242 12.7764 11.8757 12.7764 11.6645 12.6708L3.66451 8.67082C3.41042 8.54378 3.24992 8.28408 3.24992 8C3.24992 7.71592 3.41042 7.45622 3.66451 7.32918L11.6645 3.32918ZM5.67697 8L11.9999 11.1615L18.3229 8L11.9999 4.83853L5.67697 8ZM3.3291 11.6646C3.51434 11.2941 3.96485 11.1439 4.33533 11.3292L11.9999 15.1615L19.6645 11.3292C20.035 11.1439 20.4855 11.2941 20.6707 11.6646C20.856 12.0351 20.7058 12.4856 20.3353 12.6708L12.3353 16.6708C12.1242 16.7764 11.8757 16.7764 11.6645 16.6708L3.66451 12.6708C3.29403 12.4856 3.14386 12.0351 3.3291 11.6646ZM3.3291 15.6646C3.51434 15.2941 3.96485 15.1439 4.33533 15.3292L11.9999 19.1615L19.6645 15.3292C20.035 15.1439 20.4855 15.2941 20.6707 15.6646C20.856 16.0351 20.7058 16.4856 20.3353 16.6708L12.3353 20.6708C12.1242 20.7764 11.8757 20.7764 11.6645 20.6708L3.66451 16.6708C3.29403 16.4856 3.14386 16.0351 3.3291 15.6646Z"})))),Xt=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7.33333 3.25C6.3865 3.25 5.75 3.92825 5.75 4.61111V19.3889C5.75 20.0718 6.3865 20.75 7.33333 20.75H16.6667C17.6135 20.75 18.25 20.0718 18.25 19.3889V4.61111C18.25 3.92825 17.6135 3.25 16.6667 3.25H13.7073C13.735 3.32819 13.75 3.41234 13.75 3.5C13.75 3.91421 13.4142 4.25 13 4.25H11C10.5858 4.25 10.25 3.91421 10.25 3.5C10.25 3.41234 10.265 3.32819 10.2927 3.25H7.33333ZM4.25 4.61111C4.25 2.96211 5.70284 1.75 7.33333 1.75H16.6667C18.2972 1.75 19.75 2.96211 19.75 4.61111V19.3889C19.75 21.0379 18.2972 22.25 16.6667 22.25H7.33333C5.70284 22.25 4.25 21.0379 4.25 19.3889V4.61111Z"})))),_t=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4.61111 5.75C3.92825 5.75 3.25 6.3865 3.25 7.33333L3.25 16.6667C3.25 17.6135 3.92825 18.25 4.61111 18.25L19.3889 18.25C20.0718 18.25 20.75 17.6135 20.75 16.6667V13.7073C20.6718 13.735 20.5877 13.75 20.5 13.75C20.0858 13.75 19.75 13.4142 19.75 13V11C19.75 10.5858 20.0858 10.25 20.5 10.25C20.5877 10.25 20.6718 10.265 20.75 10.2927V7.33333C20.75 6.3865 20.0718 5.75 19.3889 5.75L4.61111 5.75ZM1.75 7.33333C1.75 5.70284 2.96211 4.25 4.61111 4.25L19.3889 4.25C21.0379 4.25 22.25 5.70284 22.25 7.33333V16.6667C22.25 18.2972 21.0379 19.75 19.3889 19.75L4.61111 19.75C2.96211 19.75 1.75 18.2972 1.75 16.6667L1.75 7.33333Z"})))),Gt=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7.33333 3.25C6.3865 3.25 5.75 3.92825 5.75 4.61111V19.3889C5.75 20.0718 6.3865 20.75 7.33333 20.75H16.6667C17.6135 20.75 18.25 20.0718 18.25 19.3889V4.61111C18.25 3.92825 17.6135 3.25 16.6667 3.25H13.7073C13.735 3.32819 13.75 3.41234 13.75 3.5C13.75 3.91421 13.4142 4.25 13 4.25H11C10.5858 4.25 10.25 3.91421 10.25 3.5C10.25 3.41234 10.265 3.32819 10.2927 3.25H7.33333ZM4.25 4.61111C4.25 2.96211 5.70284 1.75 7.33333 1.75H16.6667C18.2972 1.75 19.75 2.96211 19.75 4.61111V19.3889C19.75 21.0379 18.2972 22.25 16.6667 22.25H7.33333C5.70284 22.25 4.25 21.0379 4.25 19.3889V4.61111Z"})))),Nt=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6.4948 4.4948C7.29183 3.69777 8.37283 3.25 9.5 3.25H16C16.4142 3.25 16.75 3.58579 16.75 4C16.75 4.41421 16.4142 4.75 16 4.75H14.75V15C14.75 15.4142 14.4142 15.75 14 15.75C13.5858 15.75 13.25 15.4142 13.25 15V4.75H10.75V15C10.75 15.4142 10.4142 15.75 10 15.75C9.58579 15.75 9.25 15.4142 9.25 15V11.7426C8.214 11.6816 7.2327 11.2431 6.4948 10.5052C5.69777 9.70817 5.25 8.62717 5.25 7.5C5.25 6.37283 5.69777 5.29183 6.4948 4.4948ZM9.25 10.2386V4.76138C8.61233 4.81957 8.01192 5.09899 7.55546 5.55546C7.03973 6.07118 6.75 6.77065 6.75 7.5C6.75 8.22935 7.03973 8.92882 7.55546 9.44454C8.01192 9.90101 8.61233 10.1804 9.25 10.2386ZM16.4697 16.4697C16.7626 16.1768 17.2374 16.1768 17.5303 16.4697L19.5303 18.4697C19.8232 18.7626 19.8232 19.2374 19.5303 19.5303L17.5303 21.5303C17.2374 21.8232 16.7626 21.8232 16.4697 21.5303C16.1768 21.2374 16.1768 20.7626 16.4697 20.4697L17.1893 19.75H5C4.58579 19.75 4.25 19.4142 4.25 19C4.25 18.5858 4.58579 18.25 5 18.25H17.1893L16.4697 17.5303C16.1768 17.2374 16.1768 16.7626 16.4697 16.4697Z"})))),Kt=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6.4948 4.4948C7.29183 3.69777 8.37283 3.25 9.5 3.25H16C16.4142 3.25 16.75 3.58579 16.75 4C16.75 4.41421 16.4142 4.75 16 4.75H14.75V15C14.75 15.4142 14.4142 15.75 14 15.75C13.5858 15.75 13.25 15.4142 13.25 15V4.75H10.75V15C10.75 15.4142 10.4142 15.75 10 15.75C9.58579 15.75 9.25 15.4142 9.25 15V11.7426C8.214 11.6816 7.2327 11.2431 6.4948 10.5052C5.69777 9.70817 5.25 8.62717 5.25 7.5C5.25 6.37283 5.69777 5.29183 6.4948 4.4948ZM9.25 10.2386V4.76138C8.61233 4.81957 8.01192 5.09899 7.55546 5.55546C7.03973 6.07118 6.75 6.77065 6.75 7.5C6.75 8.22935 7.03973 8.92882 7.55546 9.44454C8.01192 9.90101 8.61233 10.1804 9.25 10.2386ZM7.53033 16.4697C7.82322 16.7626 7.82322 17.2374 7.53033 17.5303L6.81066 18.25H19C19.4142 18.25 19.75 18.5858 19.75 19C19.75 19.4142 19.4142 19.75 19 19.75H6.81066L7.53033 20.4697C7.82322 20.7626 7.82322 21.2374 7.53033 21.5303C7.23744 21.8232 6.76256 21.8232 6.46967 21.5303L4.46967 19.5303C4.17678 19.2374 4.17678 18.7626 4.46967 18.4697L6.46967 16.4697C6.76256 16.1768 7.23744 16.1768 7.53033 16.4697Z"})))),Yt=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{d:"M4.25 5C4.25 4.58579 4.58579 4.25 5 4.25H19C19.4142 4.25 19.75 4.58579 19.75 5V7C19.75 7.41421 19.4142 7.75 19 7.75C18.5858 7.75 18.25 7.41421 18.25 7V5.75H12.75V18.25H14C14.4142 18.25 14.75 18.5858 14.75 19C14.75 19.4142 14.4142 19.75 14 19.75H10C9.58579 19.75 9.25 19.4142 9.25 19C9.25 18.5858 9.58579 18.25 10 18.25H11.25V5.75H5.75V7C5.75 7.41421 5.41421 7.75 5 7.75C4.58579 7.75 4.25 7.41421 4.25 7V5Z"})))),qt=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M5 4.75C4.86193 4.75 4.75 4.86193 4.75 5V7C4.75 7.13807 4.86193 7.25 5 7.25H19C19.1381 7.25 19.25 7.13807 19.25 7V5C19.25 4.86193 19.1381 4.75 19 4.75H5ZM3.25 5C3.25 4.0335 4.0335 3.25 5 3.25H19C19.9665 3.25 20.75 4.0335 20.75 5V7C20.75 7.9665 19.9665 8.75 19 8.75H5C4.0335 8.75 3.25 7.9665 3.25 7V5ZM5 12.75C4.86193 12.75 4.75 12.8619 4.75 13V19C4.75 19.1381 4.86193 19.25 5 19.25H9C9.13807 19.25 9.25 19.1381 9.25 19V13C9.25 12.8619 9.13807 12.75 9 12.75H5ZM3.25 13C3.25 12.0335 4.0335 11.25 5 11.25H9C9.9665 11.25 10.75 12.0335 10.75 13V19C10.75 19.9665 9.9665 20.75 9 20.75H5C4.0335 20.75 3.25 19.9665 3.25 19V13ZM13.25 12C13.25 11.5858 13.5858 11.25 14 11.25H20C20.4142 11.25 20.75 11.5858 20.75 12C20.75 12.4142 20.4142 12.75 20 12.75H14C13.5858 12.75 13.25 12.4142 13.25 12ZM13.25 16C13.25 15.5858 13.5858 15.25 14 15.25H20C20.4142 15.25 20.75 15.5858 20.75 16C20.75 16.4142 20.4142 16.75 20 16.75H14C13.5858 16.75 13.25 16.4142 13.25 16ZM13.25 20C13.25 19.5858 13.5858 19.25 14 19.25H20C20.4142 19.25 20.75 19.5858 20.75 20C20.75 20.4142 20.4142 20.75 20 20.75H14C13.5858 20.75 13.25 20.4142 13.25 20Z"})))),Qt=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7.54031 19.7806C7.46035 19.9289 7.35872 20.0661 7.23744 20.1874C6.90925 20.5156 6.46413 20.7 6 20.7H4C3.53587 20.7 3.09075 20.5156 2.76256 20.1874C2.43437 19.8592 2.25 19.4141 2.25 18.95V11.95C2.25 11.4858 2.43437 11.0407 2.76256 10.7125C3.09075 10.3843 3.53587 10.2 4 10.2H7C7.86195 10.2 8.6886 9.85754 9.2981 9.24805C9.90759 8.63856 10.25 7.8119 10.25 6.94995V5.94995C10.25 5.22061 10.5397 4.52113 11.0555 4.00541C11.5712 3.48968 12.2707 3.19995 13 3.19995C13.7293 3.19995 14.4288 3.48968 14.9445 4.00541C15.4603 4.52113 15.75 5.22061 15.75 5.94995V10.2H18C18.7293 10.2 19.4288 10.4897 19.9445 11.0054C20.4603 11.5211 20.75 12.2206 20.75 12.95C20.75 12.9993 20.7451 13.0486 20.7354 13.097L19.7354 18.097C19.7338 18.1051 19.7321 18.1131 19.7302 18.1211C19.562 18.8387 19.2333 19.5009 18.753 19.9812C18.2744 20.4598 17.6504 20.7463 16.9716 20.7H10C9.09323 20.7 8.22033 20.3715 7.54031 19.7806ZM13 4.69995C12.6685 4.69995 12.3505 4.83165 12.1161 5.06607C11.8817 5.30049 11.75 5.61843 11.75 5.94995V6.94995C11.75 8.20973 11.2496 9.41791 10.3588 10.3087C9.64671 11.0208 8.73188 11.4834 7.75 11.6404V16.95C7.75 17.5467 7.98705 18.119 8.40901 18.5409C8.83097 18.9629 9.40326 19.2 10 19.2H17C17.0212 19.2 17.0424 19.2009 17.0636 19.2027C17.2369 19.2174 17.4577 19.1552 17.6923 18.9206C17.9316 18.6813 18.1471 18.294 18.2671 17.7902L19.2483 12.8843C19.2321 12.5767 19.1028 12.285 18.8839 12.0661C18.6495 11.8316 18.3315 11.7 18 11.7H15C14.5858 11.7 14.25 11.3642 14.25 10.95V5.94995C14.25 5.61843 14.1183 5.30049 13.8839 5.06607C13.6495 4.83165 13.3315 4.69995 13 4.69995ZM6.25 11.7H4C3.9337 11.7 3.87011 11.7263 3.82322 11.7732C3.77634 11.8201 3.75 11.8836 3.75 11.95V18.95C3.75 19.0163 3.77634 19.0798 3.82322 19.1267C3.87011 19.1736 3.93369 19.2 4 19.2H6C6.06631 19.2 6.12989 19.1736 6.17678 19.1267C6.22366 19.0798 6.25 19.0163 6.25 18.95V11.7Z"})))),$t=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M16.9716 3.25489C17.6504 3.20852 18.2744 3.49503 18.753 3.9736C19.2333 4.45396 19.562 5.11618 19.7302 5.83371C19.7321 5.84172 19.7338 5.84975 19.7354 5.85781L20.7354 10.8578C20.7451 10.9062 20.75 10.9555 20.75 11.0049C20.75 11.7342 20.4603 12.4337 19.9445 12.9494C19.4288 13.4652 18.7293 13.7549 18 13.7549H15.75V18.0049C15.75 18.7342 15.4603 19.4337 14.9445 19.9494C14.4288 20.4652 13.7293 20.7549 13 20.7549C12.2707 20.7549 11.5712 20.4652 11.0555 19.9494C10.5397 19.4337 10.25 18.7342 10.25 18.0049V17.0049C10.25 16.1429 9.90759 15.3163 9.2981 14.7068C8.6886 14.0973 7.86195 13.7549 7 13.7549H4C3.53587 13.7549 3.09075 13.5705 2.76256 13.2423C2.43437 12.9141 2.25 12.469 2.25 12.0049V5.00489C2.25 4.54076 2.43437 4.09565 2.76256 3.76746C3.09075 3.43927 3.53587 3.25489 4 3.25489H6C6.46413 3.25489 6.90925 3.43927 7.23744 3.76746C7.35872 3.88874 7.46036 4.02599 7.54031 4.17426C8.22033 3.58331 9.09324 3.25489 10 3.25489H16.9716ZM6.25 5.00489C6.25 4.93859 6.22366 4.875 6.17678 4.82812C6.12989 4.78123 6.0663 4.75489 6 4.75489H4C3.9337 4.75489 3.87011 4.78123 3.82322 4.82812C3.77634 4.875 3.75 4.93859 3.75 5.00489V12.0049C3.75 12.0712 3.77634 12.1348 3.82322 12.1817C3.87011 12.2286 3.9337 12.2549 4 12.2549H6.25V5.00489ZM7.75 12.3145V7.00489C7.75 6.40816 7.98705 5.83586 8.40901 5.4139C8.83097 4.99195 9.40326 4.75489 10 4.75489H17C17.0212 4.75489 17.0424 4.75399 17.0636 4.75219C17.2369 4.73745 17.4577 4.79965 17.6923 5.03426C17.9316 5.27354 18.1471 5.66082 18.2671 6.16467L19.2483 11.0705C19.2321 11.3781 19.1028 11.6699 18.8839 11.8888C18.6495 12.1232 18.3315 12.2549 18 12.2549H15C14.5858 12.2549 14.25 12.5907 14.25 13.0049V18.0049C14.25 18.3364 14.1183 18.6544 13.8839 18.8888C13.6495 19.1232 13.3315 19.2549 13 19.2549C12.6685 19.2549 12.3505 19.1232 12.1161 18.8888C11.8817 18.6544 11.75 18.3364 11.75 18.0049V17.0049C11.75 15.7451 11.2496 14.5369 10.3588 13.6461C9.64671 12.9341 8.73188 12.4715 7.75 12.3145Z"})))),Cn=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M8 6.75C5.10051 6.75 2.75 9.10051 2.75 12C2.75 14.8995 5.10051 17.25 8 17.25H16C18.8995 17.25 21.25 14.8995 21.25 12C21.25 9.1005 18.8995 6.75 16 6.75H8ZM1.25 12C1.25 8.27208 4.27208 5.25 8 5.25H16C19.7279 5.25 22.75 8.27208 22.75 12C22.75 15.7279 19.7279 18.75 16 18.75H8C4.27208 18.75 1.25 15.7279 1.25 12ZM16 10.75C15.3096 10.75 14.75 11.3096 14.75 12C14.75 12.6904 15.3096 13.25 16 13.25C16.6904 13.25 17.25 12.6904 17.25 12C17.25 11.3096 16.6904 10.75 16 10.75ZM13.25 12C13.25 10.4812 14.4812 9.25 16 9.25C17.5188 9.25 18.75 10.4812 18.75 12C18.75 13.5188 17.5188 14.75 16 14.75C14.4812 14.75 13.25 13.5188 13.25 12Z"})))),en=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10 3.75C9.9337 3.75 9.87011 3.77634 9.82322 3.82322C9.77634 3.87011 9.75 3.9337 9.75 4V6.25H14.25V4C14.25 3.9337 14.2237 3.87011 14.1768 3.82322C14.1299 3.77634 14.0663 3.75 14 3.75H10ZM15.75 6.25V4C15.75 3.53587 15.5656 3.09075 15.2374 2.76256C14.9092 2.43437 14.4641 2.25 14 2.25H10C9.53587 2.25 9.09075 2.43437 8.76256 2.76256C8.43437 3.09075 8.25 3.53587 8.25 4V6.25H5.00877C5.00349 6.24994 4.9982 6.24994 4.9929 6.25H4C3.58579 6.25 3.25 6.58579 3.25 7C3.25 7.41421 3.58579 7.75 4 7.75H4.3099L5.25021 19.0337C5.25898 19.7508 5.54767 20.4368 6.05546 20.9445C6.57118 21.4603 7.27065 21.75 8 21.75H16C16.7293 21.75 17.4288 21.4603 17.9445 20.9445C18.4523 20.4368 18.741 19.7508 18.7498 19.0337L19.6901 7.75H20C20.4142 7.75 20.75 7.41421 20.75 7C20.75 6.58579 20.4142 6.25 20 6.25H19.0071C19.0018 6.24994 18.9965 6.24994 18.9912 6.25H15.75ZM5.8151 7.75L6.74741 18.9377C6.74914 18.9584 6.75 18.9792 6.75 19C6.75 19.3315 6.8817 19.6495 7.11612 19.8839C7.35054 20.1183 7.66848 20.25 8 20.25H16C16.3315 20.25 16.6495 20.1183 16.8839 19.8839C17.1183 19.6495 17.25 19.3315 17.25 19C17.25 18.9792 17.2509 18.9584 17.2526 18.9377L18.1849 7.75H5.8151ZM10 10.25C10.4142 10.25 10.75 10.5858 10.75 11V17C10.75 17.4142 10.4142 17.75 10 17.75C9.58579 17.75 9.25 17.4142 9.25 17V11C9.25 10.5858 9.58579 10.25 10 10.25ZM14 10.25C14.4142 10.25 14.75 10.5858 14.75 11V17C14.75 17.4142 14.4142 17.75 14 17.75C13.5858 17.75 13.25 17.4142 13.25 17V11C13.25 10.5858 13.5858 10.25 14 10.25Z"})))),tn=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M5 4.75C5 4.33579 5.33579 4 5.75 4H18.6203C19.0345 4 19.3703 4.33579 19.3703 4.75V6.46604C19.3703 6.88026 19.0345 7.21604 18.6203 7.21604C18.2061 7.21604 17.8703 6.88026 17.8703 6.46604V5.5H12.9355V15.3932H13.7594C14.1736 15.3932 14.5094 15.729 14.5094 16.1432C14.5094 16.5574 14.1736 16.8932 13.7594 16.8932H10.6113C10.1971 16.8932 9.8613 16.5574 9.8613 16.1432C9.8613 15.729 10.1971 15.3932 10.6113 15.3932H11.4355V5.5H6.5V6.46604C6.5 6.88026 6.16421 7.21604 5.75 7.21604C5.33579 7.21604 5 6.88026 5 6.46604V4.75ZM6 19.1426C6 18.7284 6.33579 18.3926 6.75 18.3926H17.6203C18.0345 18.3926 18.3703 18.7284 18.3703 19.1426C18.3703 19.5568 18.0345 19.8926 17.6203 19.8926H6.75C6.33579 19.8926 6 19.5568 6 19.1426Z"})))),nn=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 5.25C12.2508 5.25 12.485 5.37533 12.6241 5.58397L16.1703 10.9033L20.5315 7.41435C20.7777 7.21743 21.1207 7.19544 21.39 7.35933C21.6592 7.52321 21.7973 7.83798 21.7355 8.14709L19.7355 18.1471C19.6654 18.4977 19.3576 18.75 19 18.75H5.00004C4.64253 18.75 4.33472 18.4977 4.26461 18.1471L2.2646 8.14709C2.20278 7.83798 2.34084 7.52321 2.61012 7.35933C2.8794 7.19544 3.22241 7.21743 3.46856 7.41435L7.82977 10.9033L11.376 5.58397C11.5151 5.37533 11.7493 5.25 12 5.25ZM12 7.35208L8.62408 12.416C8.50748 12.5909 8.32282 12.7089 8.1151 12.7411C7.90738 12.7734 7.69566 12.717 7.53152 12.5857L4.13926 9.87185L5.61489 17.25H18.3852L19.8608 9.87185L16.4686 12.5857C16.3044 12.717 16.0927 12.7734 15.885 12.7411C15.6773 12.7089 15.4926 12.5909 15.376 12.416L12 7.35208Z"})))),rn=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M11.4697 3.46967C11.7626 3.17678 12.2374 3.17678 12.5303 3.46967L17.5303 8.46967C17.8232 8.76256 17.8232 9.23744 17.5303 9.53033C17.2374 9.82322 16.7626 9.82322 16.4697 9.53033L12.75 5.81066V16C12.75 16.4142 12.4142 16.75 12 16.75C11.5858 16.75 11.25 16.4142 11.25 16V5.81066L7.53033 9.53033C7.23744 9.82322 6.76256 9.82322 6.46967 9.53033C6.17678 9.23744 6.17678 8.76256 6.46967 8.46967L11.4697 3.46967ZM4 16.25C4.41421 16.25 4.75 16.5858 4.75 17V19C4.75 19.3315 4.8817 19.6495 5.11612 19.8839C5.35054 20.1183 5.66848 20.25 6 20.25H18C18.3315 20.25 18.6495 20.1183 18.8839 19.8839C19.1183 19.6495 19.25 19.3315 19.25 19V17C19.25 16.5858 19.5858 16.25 20 16.25C20.4142 16.25 20.75 16.5858 20.75 17V19C20.75 19.7293 20.4603 20.4288 19.9445 20.9445C19.4288 21.4603 18.7293 21.75 18 21.75H6C5.27065 21.75 4.57118 21.4603 4.05546 20.9445C3.53973 20.4288 3.25 19.7293 3.25 19V17C3.25 16.5858 3.58579 16.25 4 16.25Z"})))),ln=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6.89124 3.64124C7.78204 2.75044 8.99022 2.25 10.25 2.25C11.5098 2.25 12.718 2.75044 13.6088 3.64124C14.4996 4.53204 15 5.74022 15 7C15 8.25978 14.4996 9.46796 13.6088 10.3588C12.718 11.2496 11.5098 11.75 10.25 11.75C8.99022 11.75 7.78204 11.2496 6.89124 10.3588C6.00044 9.46796 5.5 8.25978 5.5 7C5.5 5.74022 6.00044 4.53204 6.89124 3.64124ZM10.25 3.75C9.38805 3.75 8.5614 4.09241 7.9519 4.7019C7.34241 5.3114 7 6.13805 7 7C7 7.86195 7.34241 8.6886 7.9519 9.2981C8.5614 9.90759 9.38805 10.25 10.25 10.25C11.112 10.25 11.9386 9.90759 12.5481 9.2981C13.1576 8.6886 13.5 7.86195 13.5 7C13.5 6.13805 13.1576 5.3114 12.5481 4.7019C11.9386 4.09241 11.112 3.75 10.25 3.75Z"}),t.createElement("path",{d:"M8.25 15.75C7.38805 15.75 6.5614 16.0924 5.9519 16.7019C5.34241 17.3114 5 18.138 5 19V21C5 21.4142 4.66421 21.75 4.25 21.75C3.83579 21.75 3.5 21.4142 3.5 21V19C3.5 17.7402 4.00044 16.532 4.89124 15.6412C5.78204 14.7504 6.99022 14.25 8.25 14.25H8.75C9.16421 14.25 9.5 14.5858 9.5 15C9.5 15.4142 9.16421 15.75 8.75 15.75H8.25Z"}),t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M14.4677 14.2505C13.688 14.249 12.9385 14.5519 12.3789 15.0947C12.1015 15.3637 11.8807 15.6856 11.7298 16.0413C11.5788 16.3971 11.5006 16.7794 11.4999 17.1659C11.4991 17.5523 11.5758 17.935 11.7254 18.2913C11.8748 18.6473 12.0947 18.9705 12.3708 19.2405L15.7244 22.5351C16.0159 22.8216 16.4832 22.8218 16.775 22.5357L20.1238 19.2529C20.4004 18.9841 20.6205 18.6627 20.7711 18.3075C20.9219 17.9517 21 17.5693 21.0006 17.1829C21.0012 16.7964 20.9244 16.4138 20.7747 16.0575C20.6251 15.7014 20.4057 15.3789 20.1294 15.1089C19.5717 14.5635 18.8232 14.2575 18.0432 14.256C17.3951 14.2548 16.768 14.4638 16.2534 14.8461C15.7405 14.4627 15.1149 14.2517 14.4677 14.2505ZM13.4232 16.1715C13.7023 15.9008 14.076 15.7497 14.4648 15.7505C14.853 15.7512 15.2257 15.9033 15.5036 16.1744L15.7253 16.393C16.0169 16.6807 16.4853 16.6812 16.7775 16.3942L16.9998 16.1759C17.2787 15.9059 17.652 15.7553 18.0403 15.756C18.4292 15.7568 18.8029 15.9097 19.0809 16.1816C19.2141 16.3117 19.3198 16.467 19.3919 16.6386C19.4639 16.8101 19.5009 16.9944 19.5006 17.1804C19.5003 17.3665 19.4627 17.5506 19.3901 17.722C19.3175 17.8933 19.2112 18.0483 19.0777 18.1779L16.2506 20.9493L13.4206 18.1691L13.4192 18.1677C13.2861 18.0377 13.1805 17.8823 13.1085 17.7107C13.0364 17.5391 12.9995 17.3549 12.9999 17.1688C13.0002 16.9827 13.0379 16.7986 13.1106 16.6273C13.1833 16.456 13.2896 16.301 13.4232 16.1715Z"})))),on=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 3.75C10.2051 3.75 8.75 5.20507 8.75 7C8.75 8.79493 10.2051 10.25 12 10.25C13.7949 10.25 15.25 8.79493 15.25 7C15.25 5.20507 13.7949 3.75 12 3.75ZM7.25 7C7.25 4.37665 9.37665 2.25 12 2.25C14.6234 2.25 16.75 4.37665 16.75 7C16.75 9.62335 14.6234 11.75 12 11.75C9.37665 11.75 7.25 9.62335 7.25 7ZM10 15.75C9.13805 15.75 8.3114 16.0924 7.7019 16.7019C7.09241 17.3114 6.75 18.138 6.75 19V21C6.75 21.4142 6.41421 21.75 6 21.75C5.58579 21.75 5.25 21.4142 5.25 21V19C5.25 17.7402 5.75044 16.532 6.64124 15.6412C7.53204 14.7504 8.74022 14.25 10 14.25H14C15.2598 14.25 16.468 14.7504 17.3588 15.6412C18.2496 16.532 18.75 17.7402 18.75 19V21C18.75 21.4142 18.4142 21.75 18 21.75C17.5858 21.75 17.25 21.4142 17.25 21V19C17.25 18.138 16.9076 17.3114 16.2981 16.7019C15.6886 16.0924 14.862 15.75 14 15.75H10Z"})))),dn=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10.25 2.25C8.99022 2.25 7.78204 2.75044 6.89124 3.64124C6.00044 4.53204 5.5 5.74022 5.5 7C5.5 8.25978 6.00044 9.46796 6.89124 10.3588C7.78204 11.2496 8.99022 11.75 10.25 11.75C11.5098 11.75 12.718 11.2496 13.6088 10.3588C14.4996 9.46796 15 8.25978 15 7C15 5.74022 14.4996 4.53204 13.6088 3.64124C12.718 2.75044 11.5098 2.25 10.25 2.25ZM7.9519 4.7019C8.5614 4.09241 9.38805 3.75 10.25 3.75C11.112 3.75 11.9386 4.09241 12.5481 4.7019C13.1576 5.3114 13.5 6.13805 13.5 7C13.5 7.86195 13.1576 8.6886 12.5481 9.2981C11.9386 9.90759 11.112 10.25 10.25 10.25C9.38805 10.25 8.5614 9.90759 7.9519 9.2981C7.34241 8.6886 7 7.86195 7 7C7 6.13805 7.34241 5.3114 7.9519 4.7019Z"}),t.createElement("path",{d:"M14.25 18.25C13.8358 18.25 13.5 18.5858 13.5 19C13.5 19.4142 13.8358 19.75 14.25 19.75H16.5V22C16.5 22.4142 16.8358 22.75 17.25 22.75C17.6642 22.75 18 22.4142 18 22V19.75H20.25C20.6642 19.75 21 19.4142 21 19C21 18.5858 20.6642 18.25 20.25 18.25H18V16C18 15.5858 17.6642 15.25 17.25 15.25C16.8358 15.25 16.5 15.5858 16.5 16V18.25H14.25Z"}),t.createElement("path",{d:"M5.9519 16.7019C6.5614 16.0924 7.38805 15.75 8.25 15.75H12.25C12.6642 15.75 13 15.4142 13 15C13 14.5858 12.6642 14.25 12.25 14.25H8.25C6.99022 14.25 5.78204 14.7504 4.89124 15.6412C4.00044 16.532 3.5 17.7402 3.5 19V21C3.5 21.4142 3.83579 21.75 4.25 21.75C4.66421 21.75 5 21.4142 5 21V19C5 18.138 5.34241 17.3114 5.9519 16.7019Z"})))),cn=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M9 2.25C7.74022 2.25 6.53204 2.75044 5.64124 3.64124C4.75044 4.53204 4.25 5.74022 4.25 7C4.25 8.25978 4.75044 9.46796 5.64124 10.3588C6.53204 11.2496 7.74022 11.75 9 11.75C10.2598 11.75 11.468 11.2496 12.3588 10.3588C13.2496 9.46796 13.75 8.25978 13.75 7C13.75 5.74022 13.2496 4.53204 12.3588 3.64124C11.468 2.75044 10.2598 2.25 9 2.25ZM6.7019 4.7019C7.3114 4.09241 8.13805 3.75 9 3.75C9.86195 3.75 10.6886 4.09241 11.2981 4.7019C11.9076 5.3114 12.25 6.13805 12.25 7C12.25 7.86195 11.9076 8.6886 11.2981 9.2981C10.6886 9.90759 9.86195 10.25 9 10.25C8.13805 10.25 7.3114 9.90759 6.7019 9.2981C6.09241 8.6886 5.75 7.86195 5.75 7C5.75 6.13805 6.09241 5.3114 6.7019 4.7019Z"}),t.createElement("path",{d:"M4.7019 16.7019C5.3114 16.0924 6.13805 15.75 7 15.75H11C11.862 15.75 12.6886 16.0924 13.2981 16.7019C13.9076 17.3114 14.25 18.138 14.25 19V21C14.25 21.4142 14.5858 21.75 15 21.75C15.4142 21.75 15.75 21.4142 15.75 21V19C15.75 17.7402 15.2496 16.532 14.3588 15.6412C13.468 14.7504 12.2598 14.25 11 14.25H7C5.74022 14.25 4.53204 14.7504 3.64124 15.6412C2.75044 16.532 2.25 17.7402 2.25 19V21C2.25 21.4142 2.58579 21.75 3 21.75C3.41421 21.75 3.75 21.4142 3.75 21V19C3.75 18.138 4.09241 17.3114 4.7019 16.7019Z"}),t.createElement("path",{d:"M15.2735 2.94403C15.3762 2.54276 15.7848 2.30076 16.1861 2.4035C17.2078 2.66511 18.1134 3.25933 18.7602 4.09249C19.4069 4.92566 19.7579 5.95036 19.7579 7.00506C19.7579 8.05977 19.4069 9.08447 18.7602 9.91763C18.1134 10.7508 17.2078 11.345 16.1861 11.6066C15.7848 11.7094 15.3762 11.4674 15.2735 11.0661C15.1708 10.6648 15.4128 10.2562 15.814 10.1535C16.5131 9.97451 17.1327 9.56793 17.5752 8.99787C18.0177 8.42782 18.2579 7.7267 18.2579 7.00506C18.2579 6.28343 18.0177 5.58231 17.5752 5.01225C17.1327 4.4422 16.5131 4.03562 15.814 3.85663C15.4128 3.75388 15.1708 3.3453 15.2735 2.94403Z"}),t.createElement("path",{d:"M18.1875 14.4239C17.7865 14.3203 17.3774 14.5615 17.2739 14.9626C17.1703 15.3637 17.4115 15.7727 17.8126 15.8763C18.5071 16.0556 19.1228 16.4597 19.5637 17.0255C20.0042 17.5908 20.2455 18.2858 20.2501 19.0024V21.0001C20.2501 21.4143 20.5858 21.7501 21.0001 21.7501C21.4143 21.7501 21.7501 21.4143 21.7501 21.0001L21.7501 19.0001L21.75 18.9958C21.744 17.9474 21.3913 16.9305 20.7469 16.1035C20.1025 15.2765 19.2027 14.686 18.1875 14.4239Z"})))),an=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6 3.25C5.27065 3.25 4.57118 3.53973 4.05546 4.05546C3.53973 4.57118 3.25 5.27065 3.25 6V18C3.25 18.7293 3.53973 19.4288 4.05546 19.9445C4.57118 20.4603 5.27065 20.75 6 20.75H18C18.4641 20.75 18.9092 20.5656 19.2374 20.2374C19.5656 19.9092 19.75 19.4641 19.75 19V16.75H20C20.4142 16.75 20.75 16.4142 20.75 16V12C20.75 11.5858 20.4142 11.25 20 11.25H19.75V9C19.75 8.53587 19.5656 8.09075 19.2374 7.76256C18.9092 7.43437 18.4641 7.25 18 7.25H17.75V5C17.75 4.53587 17.5656 4.09075 17.2374 3.76256C16.9092 3.43437 16.4641 3.25 16 3.25H6ZM18.25 11.25V9C18.25 8.93369 18.2237 8.87011 18.1768 8.82322C18.1299 8.77634 18.0663 8.75 18 8.75H6C5.56137 8.75 5.13355 8.64521 4.75 8.44949V18C4.75 18.3315 4.8817 18.6495 5.11612 18.8839C5.35054 19.1183 5.66848 19.25 6 19.25H18C18.0663 19.25 18.1299 19.2237 18.1768 19.1768C18.2237 19.1299 18.25 19.0663 18.25 19V16.75H16C15.2707 16.75 14.5712 16.4603 14.0555 15.9445C13.5397 15.4288 13.25 14.7293 13.25 14C13.25 13.2707 13.5397 12.5712 14.0555 12.0555C14.5712 11.5397 15.2707 11.25 16 11.25H18.25ZM19.25 15.25V12.75H16C15.6685 12.75 15.3505 12.8817 15.1161 13.1161C14.8817 13.3505 14.75 13.6685 14.75 14C14.75 14.3315 14.8817 14.6495 15.1161 14.8839C15.3505 15.1183 15.6685 15.25 16 15.25H19.25ZM5.11612 6.88388C4.8817 6.64946 4.75 6.33152 4.75 6C4.75 5.66848 4.8817 5.35054 5.11612 5.11612C5.35054 4.8817 5.66848 4.75 6 4.75H16C16.0663 4.75 16.1299 4.77634 16.1768 4.82322C16.2237 4.87011 16.25 4.93369 16.25 5V7.25H6C5.66848 7.25 5.35054 7.1183 5.11612 6.88388Z"})))),fn=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M5 4.75C4.9337 4.75 4.87011 4.77634 4.82322 4.82322C4.77634 4.87011 4.75 4.9337 4.75 5V7.25H7.25V4.75H5ZM5 3.25C4.53587 3.25 4.09075 3.43438 3.76256 3.76256C3.43438 4.09075 3.25 4.53587 3.25 5V19C3.25 19.4641 3.43437 19.9092 3.76256 20.2374C4.09075 20.5656 4.53587 20.75 5 20.75H19C19.4641 20.75 19.9092 20.5656 20.2374 20.2374C20.5656 19.9092 20.75 19.4641 20.75 19V5C20.75 4.53587 20.5656 4.09075 20.2374 3.76256C19.9092 3.43437 19.4641 3.25 19 3.25H5ZM8.75 4.75V7.25H19.25V5C19.25 4.93369 19.2237 4.87011 19.1768 4.82322C19.1299 4.77634 19.0663 4.75 19 4.75H8.75ZM19.25 8.75H4.75V19C4.75 19.0663 4.77634 19.1299 4.82322 19.1768C4.87011 19.2237 4.93369 19.25 5 19.25H19C19.0663 19.25 19.1299 19.2237 19.1768 19.1768C19.2237 19.1299 19.25 19.0663 19.25 19V8.75Z"})))),Ln=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M17.3615 5.73997C15.8328 4.43606 13.8807 3.73486 11.8717 3.76797C9.86275 3.80107 7.93489 4.5662 6.44996 5.91977C4.96503 7.27334 4.02512 9.12229 3.8066 11.1196C3.58809 13.117 4.106 15.1254 5.26315 16.768C5.41379 16.9819 5.44213 17.2588 5.33795 17.4987L4.24477 20.0163L7.91842 19.3616C8.07519 19.3337 8.23679 19.3564 8.37981 19.4264C10.1844 20.3099 12.2494 20.5046 14.1873 19.9737C16.1251 19.4429 17.8027 18.2231 18.9051 16.5432C20.0075 14.8634 20.4589 12.839 20.1747 10.8499C19.8905 8.86086 18.8902 7.04387 17.3615 5.73997ZM11.847 2.26817C14.2213 2.22905 16.5282 3.05773 18.3349 4.59872C20.1416 6.1397 21.3238 8.28704 21.6596 10.6378C21.9955 12.9885 21.462 15.381 20.1591 17.3662C18.8563 19.3515 16.8738 20.7931 14.5835 21.4204C12.3736 22.0258 10.0238 21.8328 7.94563 20.8804L3.1316 21.7383C2.85956 21.7868 2.58289 21.6818 2.41156 21.465C2.24024 21.2482 2.202 20.9547 2.31206 20.7013L3.79867 17.2775C2.59895 15.4056 2.07277 13.1752 2.3155 10.9565C2.57374 8.59601 3.68455 6.41088 5.43947 4.81121C7.19439 3.21154 9.47276 2.30729 11.847 2.26817Z"}),t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M8.61612 8.11609C8.85055 7.88167 9.16849 7.74997 9.50001 7.74997C9.83153 7.74997 10.1495 7.88167 10.3839 8.11609C10.6183 8.35051 10.75 8.66845 10.75 8.99997V9.99997C10.75 10.3315 10.6183 10.6494 10.3839 10.8839C10.2508 11.0169 10.0908 11.1169 9.91667 11.1785C10.1141 11.8627 10.4823 12.4926 10.9948 13.0052C11.5073 13.5177 12.1373 13.8858 12.8215 14.0833C12.8831 13.9092 12.983 13.7492 13.1161 13.6161C13.3505 13.3817 13.6685 13.25 14 13.25H15C15.3315 13.25 15.6495 13.3817 15.8839 13.6161C16.1183 13.8505 16.25 14.1685 16.25 14.5C16.25 14.8315 16.1183 15.1494 15.8839 15.3839C15.6495 15.6183 15.3315 15.75 15 15.75H14C12.475 15.75 11.0125 15.1442 9.93414 14.0658C8.85581 12.9875 8.25001 11.525 8.25001 9.99997V8.99997C8.25001 8.66845 8.3817 8.35051 8.61612 8.11609Z"})))),un=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3 5.25C2.86193 5.25 2.75 5.36193 2.75 5.5V15.5C2.75 15.6381 2.86193 15.75 3 15.75H21C21.1381 15.75 21.25 15.6381 21.25 15.5V5.5C21.25 5.36193 21.1381 5.25 21 5.25H3ZM1.25 5.5C1.25 4.5335 2.0335 3.75 3 3.75H21C21.9665 3.75 22.75 4.5335 22.75 5.5V15.5C22.75 16.4665 21.9665 17.25 21 17.25H3C2.0335 17.25 1.25 16.4665 1.25 15.5V5.5ZM6.25 19.5C6.25 19.0858 6.58579 18.75 7 18.75H17C17.4142 18.75 17.75 19.0858 17.75 19.5C17.75 19.9142 17.4142 20.25 17 20.25H7C6.58579 20.25 6.25 19.9142 6.25 19.5Z"})))),Vn=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M5 4.75C4.86193 4.75 4.75 4.86193 4.75 5V9C4.75 9.13807 4.86193 9.25 5 9.25H9C9.13807 9.25 9.25 9.13807 9.25 9V5C9.25 4.86193 9.13807 4.75 9 4.75H5ZM3.25 5C3.25 4.0335 4.0335 3.25 5 3.25H9C9.9665 3.25 10.75 4.0335 10.75 5V9C10.75 9.9665 9.9665 10.75 9 10.75H5C4.0335 10.75 3.25 9.9665 3.25 9V5ZM15 4.75C14.8619 4.75 14.75 4.86193 14.75 5V9C14.75 9.13807 14.8619 9.25 15 9.25H19C19.1381 9.25 19.25 9.13807 19.25 9V5C19.25 4.86193 19.1381 4.75 19 4.75H15ZM13.25 5C13.25 4.0335 14.0335 3.25 15 3.25H19C19.9665 3.25 20.75 4.0335 20.75 5V9C20.75 9.9665 19.9665 10.75 19 10.75H15C14.0335 10.75 13.25 9.9665 13.25 9V5ZM5 14.75C4.86193 14.75 4.75 14.8619 4.75 15V19C4.75 19.1381 4.86193 19.25 5 19.25H9C9.13807 19.25 9.25 19.1381 9.25 19V15C9.25 14.8619 9.13807 14.75 9 14.75H5ZM3.25 15C3.25 14.0335 4.0335 13.25 5 13.25H9C9.9665 13.25 10.75 14.0335 10.75 15V19C10.75 19.9665 9.9665 20.75 9 20.75H5C4.0335 20.75 3.25 19.9665 3.25 19V15ZM15 14.75C14.8619 14.75 14.75 14.8619 14.75 15V19C14.75 19.1381 14.8619 19.25 15 19.25H19C19.1381 19.25 19.25 19.1381 19.25 19V15C19.25 14.8619 19.1381 14.75 19 14.75H15ZM13.25 15C13.25 14.0335 14.0335 13.25 15 13.25H19C19.9665 13.25 20.75 14.0335 20.75 15V19C20.75 19.9665 19.9665 20.75 19 20.75H15C14.0335 20.75 13.25 19.9665 13.25 19V15Z"})))),Hn=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12.0004 2.01562C6.49444 2.01562 2.01562 6.49404 2.01562 11.9996C2.01562 17.5053 6.49444 21.9844 12.0004 21.9844C17.5056 21.9844 21.9844 17.5053 21.9844 11.9996C21.9844 6.49466 17.5056 2.01562 12.0004 2.01562ZM3.16156 11.9996C3.16156 10.7184 3.43668 9.5017 3.92703 8.40311L8.14311 19.9539C5.19483 18.5215 3.16156 15.4984 3.16156 11.9996ZM12.0004 20.8387C11.1327 20.8387 10.2954 20.7106 9.50324 20.4785L12.1549 12.7731L14.8725 20.2154C14.8898 20.2589 14.9115 20.2992 14.9353 20.3372C14.0167 20.6607 13.0292 20.8387 12.0004 20.8387ZM13.218 7.85596C13.7501 7.82787 14.2293 7.77149 14.2293 7.77149C14.7058 7.71531 14.65 7.01576 14.1733 7.04385C14.1733 7.04385 12.7415 7.156 11.8176 7.156C10.9495 7.156 9.4894 7.04385 9.4894 7.04385C9.0133 7.01576 8.95794 7.74402 9.43363 7.77149C9.43363 7.77149 9.88452 7.82767 10.3602 7.85596L11.7373 11.6286L9.80335 17.4297L6.58511 7.85638C7.1178 7.82829 7.59679 7.77211 7.59679 7.77211C8.07247 7.71593 8.01691 7.01596 7.53999 7.04446C7.53999 7.04446 6.10881 7.15641 5.18429 7.15641C5.01782 7.15641 4.82304 7.15207 4.61566 7.14567C6.19535 4.74588 8.9123 3.16171 12.0004 3.16171C14.3018 3.16171 16.3964 4.04157 17.9689 5.48157C17.9302 5.47971 17.8937 5.47476 17.854 5.47476C16.9861 5.47476 16.3695 6.2309 16.3695 7.04343C16.3695 7.77149 16.789 8.38801 17.2377 9.11586C17.5741 9.70512 17.9662 10.4613 17.9662 11.5537C17.9662 12.3102 17.6758 13.1882 17.2936 14.4107L16.4121 17.3566L13.218 7.85596ZM16.4435 19.6389L19.1431 11.8337C19.6481 10.573 19.8152 9.56469 19.8152 8.66789C19.8152 8.343 19.7937 8.04042 19.7557 7.75911C20.4466 9.01797 20.8391 10.4629 20.8386 11.9998C20.8386 15.2602 19.0708 18.1068 16.4435 19.6389Z"})))),Mn=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12.6878 2.27398C12.5917 2.24917 12.4909 2.24331 12.3914 2.25781C12.2616 2.25269 12.1311 2.2501 12 2.2501C11.8689 2.2501 11.7384 2.25269 11.6086 2.25781C11.5091 2.24331 11.4083 2.24917 11.3123 2.27398C6.24841 2.62684 2.25 6.84653 2.25 12.0001C2.25 17.1537 6.24842 21.3734 11.3123 21.7262C11.4083 21.751 11.5091 21.7569 11.6086 21.7424C11.7384 21.7475 11.8689 21.7501 12 21.7501C12.1311 21.7501 12.2616 21.7475 12.3914 21.7424C12.4909 21.7569 12.5917 21.751 12.6878 21.7262C17.7516 21.3733 21.75 17.1537 21.75 12.0001C21.75 6.84655 17.7516 2.62687 12.6878 2.27398ZM11.922 3.75046C11.948 3.75022 11.974 3.7501 12 3.7501C12.026 3.7501 12.052 3.75022 12.078 3.75046C12.9067 5.15671 13.5154 6.67415 13.8892 8.2501H10.1109C10.4846 6.67415 11.0934 5.15671 11.922 3.75046ZM10.0915 3.97196C7.71452 4.5349 5.7353 6.12619 4.64956 8.2501H8.57289C8.89327 6.76788 9.40294 5.32976 10.0915 3.97196ZM8.31541 9.7501H4.06055C3.85825 10.4653 3.75 11.2201 3.75 12.0001C3.75 12.7801 3.85825 13.5349 4.06055 14.2501H8.31541C8.22038 13.5064 8.17224 12.755 8.17224 12.0001C8.17224 11.2452 8.22038 10.4938 8.31541 9.7501ZM8.57289 15.7501H4.64956C5.7353 17.874 7.71452 19.4653 10.0915 20.0282C9.40294 18.6704 8.89327 17.2323 8.57289 15.7501ZM11.922 20.2497C11.0934 18.8435 10.4846 17.3261 10.1109 15.7501H13.8892C13.5154 17.3261 12.9067 18.8435 12.078 20.2497C12.052 20.25 12.026 20.2501 12 20.2501C11.974 20.2501 11.948 20.25 11.922 20.2497ZM13.9085 20.0282C16.2855 19.4653 18.2647 17.874 19.3504 15.7501H15.4271C15.1068 17.2323 14.5971 18.6704 13.9085 20.0282ZM15.6846 14.2501H19.9395C20.1417 13.5349 20.25 12.7801 20.25 12.0001C20.25 11.2201 20.1417 10.4653 19.9395 9.7501H15.6846C15.7797 10.4938 15.8278 11.2452 15.8278 12.0001C15.8278 12.755 15.7797 13.5064 15.6846 14.2501ZM14.1713 9.7501H9.82876C9.72491 10.4929 9.67224 11.2446 9.67224 12.0001C9.67224 12.7556 9.72491 13.5073 9.82876 14.2501H14.1713C14.2751 13.5073 14.3278 12.7556 14.3278 12.0001C14.3278 11.2446 14.2751 10.4929 14.1713 9.7501ZM15.4271 8.2501C15.1068 6.76788 14.5971 5.32976 13.9085 3.97197C16.2855 4.53492 18.2647 6.1262 19.3504 8.2501H15.4271Z"})))),Zn=t.forwardRef(((C,e)=>t.createElement(r(),{viewBox:"0 0 24 24",...C,ref:e},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M18.5303 5.46967C18.8232 5.76256 18.8232 6.23744 18.5303 6.53033L6.53033 18.5303C6.23744 18.8232 5.76256 18.8232 5.46967 18.5303C5.17678 18.2374 5.17678 17.7626 5.46967 17.4697L17.4697 5.46967C17.7626 5.17678 18.2374 5.17678 18.5303 5.46967Z"}),t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M5.46967 5.46967C5.76256 5.17678 6.23744 5.17678 6.53033 5.46967L18.5303 17.4697C18.8232 17.7626 18.8232 18.2374 18.5303 18.5303C18.2374 18.8232 17.7626 18.8232 17.4697 18.5303L5.46967 6.53033C5.17678 6.23744 5.17678 5.76256 5.46967 5.46967Z"}))));(window.elementorV2=window.elementorV2||{}).icons=e}(),window.elementorV2.icons?.init?.();