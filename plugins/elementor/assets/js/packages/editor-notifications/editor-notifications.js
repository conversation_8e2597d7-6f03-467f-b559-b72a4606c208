/*! For license information please see editor-notifications.js.LICENSE.txt */
!function(){"use strict";var e={"./node_modules/notistack/node_modules/clsx/dist/clsx.m.js":function(e,n,t){function r(e){var n,t,o="";if("string"==typeof e||"number"==typeof e)o+=e;else if("object"==typeof e)if(Array.isArray(e))for(n=0;n<e.length;n++)e[n]&&(t=r(e[n]))&&(o&&(o+=" "),o+=t);else for(n in e)e[n]&&(o&&(o+=" "),o+=n);return o}function o(){for(var e,n,t=0,o="";t<arguments.length;)(e=arguments[t++])&&(n=r(e))&&(o&&(o+=" "),o+=n);return o}t.r(n),t.d(n,{clsx:function(){return o}}),n.default=o},"./node_modules/notistack/notistack.esm.js":function(e,n,t){t.r(n),t.d(n,{MaterialDesignContent:function(){return he},SnackbarContent:function(){return oe},SnackbarProvider:function(){return Te},Transition:function(){return O},closeSnackbar:function(){return me},enqueueSnackbar:function(){return pe},useSnackbar:function(){return Le}});var r=t("react"),o=t.n(r),i=t("react-dom"),a=t("./node_modules/notistack/node_modules/clsx/dist/clsx.m.js"),s=t("./node_modules/goober/dist/goober.modern.js");function u(e,n){for(var t=0;t<n.length;t++){var r=n[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function c(e,n,t){return n&&u(e.prototype,n),t&&u(e,t),e}function l(){return l=Object.assign||function(e){for(var n=1;n<arguments.length;n++){var t=arguments[n];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e},l.apply(this,arguments)}function d(e,n){e.prototype=Object.create(n.prototype),e.prototype.constructor=e,e.__proto__=n}function f(e,n){if(null==e)return{};var t,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)t=i[r],n.indexOf(t)>=0||(o[t]=e[t]);return o}function p(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}var m=function(){return""},h=o().createContext({enqueueSnackbar:m,closeSnackbar:m}),g="@media (max-width:599.95px)",x="@media (min-width:600px)",v=function(e){return e.charAt(0).toUpperCase()+e.slice(1)},b=function(e){return""+v(e.vertical)+v(e.horizontal)},E=function(e){return!!e||0===e},k="unmounted",y="exited",w="entering",S="entered",C="exiting",O=function(e){function n(n){var t;t=e.call(this,n)||this;var r,o=n.appear;return t.appearStatus=null,n.in?o?(r=y,t.appearStatus=w):r=S:r=n.unmountOnExit||n.mountOnEnter?k:y,t.state={status:r},t.nextCallback=null,t}d(n,e),n.getDerivedStateFromProps=function(e,n){return e.in&&n.status===k?{status:y}:null};var t=n.prototype;return t.componentDidMount=function(){this.updateStatus(!0,this.appearStatus)},t.componentDidUpdate=function(e){var n=null;if(e!==this.props){var t=this.state.status;this.props.in?t!==w&&t!==S&&(n=w):t!==w&&t!==S||(n=C)}this.updateStatus(!1,n)},t.componentWillUnmount=function(){this.cancelNextCallback()},t.getTimeouts=function(){var e=this.props.timeout,n=e,t=e;return null!=e&&"number"!=typeof e&&"string"!=typeof e&&(t=e.exit,n=e.enter),{exit:t,enter:n}},t.updateStatus=function(e,n){void 0===e&&(e=!1),null!==n?(this.cancelNextCallback(),n===w?this.performEnter(e):this.performExit()):this.props.unmountOnExit&&this.state.status===y&&this.setState({status:k})},t.performEnter=function(e){var n=this,t=this.props.enter,r=e,o=this.getTimeouts();e||t?(this.props.onEnter&&this.props.onEnter(this.node,r),this.safeSetState({status:w},(function(){n.props.onEntering&&n.props.onEntering(n.node,r),n.onTransitionEnd(o.enter,(function(){n.safeSetState({status:S},(function(){n.props.onEntered&&n.props.onEntered(n.node,r)}))}))}))):this.safeSetState({status:S},(function(){n.props.onEntered&&n.props.onEntered(n.node,r)}))},t.performExit=function(){var e=this,n=this.props.exit,t=this.getTimeouts();n?(this.props.onExit&&this.props.onExit(this.node),this.safeSetState({status:C},(function(){e.props.onExiting&&e.props.onExiting(e.node),e.onTransitionEnd(t.exit,(function(){e.safeSetState({status:y},(function(){e.props.onExited&&e.props.onExited(e.node)}))}))}))):this.safeSetState({status:y},(function(){e.props.onExited&&e.props.onExited(e.node)}))},t.cancelNextCallback=function(){null!==this.nextCallback&&this.nextCallback.cancel&&(this.nextCallback.cancel(),this.nextCallback=null)},t.safeSetState=function(e,n){n=this.setNextCallback(n),this.setState(e,n)},t.setNextCallback=function(e){var n=this,t=!0;return this.nextCallback=function(){t&&(t=!1,n.nextCallback=null,e())},this.nextCallback.cancel=function(){t=!1},this.nextCallback},t.onTransitionEnd=function(e,n){this.setNextCallback(n);var t=null==e&&!this.props.addEndListener;this.node&&!t?(this.props.addEndListener&&this.props.addEndListener(this.node,this.nextCallback),null!=e&&setTimeout(this.nextCallback,e)):setTimeout(this.nextCallback,0)},t.render=function(){var e=this.state.status;if(e===k)return null;var n=this.props;return(0,n.children)(e,f(n,["children","in","mountOnEnter","unmountOnExit","appear","enter","exit","timeout","addEndListener","onEnter","onEntering","onEntered","onExit","onExiting","onExited","nodeRef"]))},c(n,[{key:"node",get:function(){var e,n=null===(e=this.props.nodeRef)||void 0===e?void 0:e.current;if(!n)throw new Error("notistack - Custom snackbar is not refForwarding");return n}}]),n}(o().Component);function T(){}function L(e,n){"function"==typeof e?e(n):e&&(e.current=n)}function D(e,n){return(0,r.useMemo)((function(){return null==e&&null==n?null:function(t){L(e,t),L(n,t)}}),[e,n])}function j(e){var n=e.timeout,t=e.style,r=void 0===t?{}:t,o=e.mode;return{duration:"object"==typeof n?n[o]||0:n,easing:r.transitionTimingFunction,delay:r.transitionDelay}}O.defaultProps={in:!1,mountOnEnter:!1,unmountOnExit:!1,appear:!1,enter:!0,exit:!0,onEnter:T,onEntering:T,onEntered:T,onExit:T,onExiting:T,onExited:T};var N=function(e){e.scrollTop=e.scrollTop},_=function(e){return Math.round(e)+"ms"};function R(e,n){void 0===e&&(e=["all"]);var t=n||{},r=t.duration,o=void 0===r?300:r,i=t.easing,a=void 0===i?"cubic-bezier(0.4, 0, 0.2, 1)":i,s=t.delay,u=void 0===s?0:s;return(Array.isArray(e)?e:[e]).map((function(e){var n="string"==typeof o?o:_(o),t="string"==typeof u?u:_(u);return e+" "+n+" "+a+" "+t})).join(",")}function M(e){var n=function(e){return e&&e.ownerDocument||document}(e);return n.defaultView||window}function P(e,n){if(n){var t=function(e,n){var t,r=n.getBoundingClientRect(),o=M(n);if(n.fakeTransform)t=n.fakeTransform;else{var i=o.getComputedStyle(n);t=i.getPropertyValue("-webkit-transform")||i.getPropertyValue("transform")}var a=0,s=0;if(t&&"none"!==t&&"string"==typeof t){var u=t.split("(")[1].split(")")[0].split(",");a=parseInt(u[4],10),s=parseInt(u[5],10)}switch(e){case"left":return"translateX("+(o.innerWidth+a-r.left)+"px)";case"right":return"translateX(-"+(r.left+r.width-a)+"px)";case"up":return"translateY("+(o.innerHeight+s-r.top)+"px)";default:return"translateY(-"+(r.top+r.height-s)+"px)"}}(e,n);t&&(n.style.webkitTransform=t,n.style.transform=t)}}var A=(0,r.forwardRef)((function(e,n){var t=e.children,o=e.direction,i=void 0===o?"down":o,a=e.in,s=e.style,u=e.timeout,c=void 0===u?0:u,d=e.onEnter,p=e.onEntered,m=e.onExit,h=e.onExited,g=f(e,["children","direction","in","style","timeout","onEnter","onEntered","onExit","onExited"]),x=(0,r.useRef)(null),v=D(t.ref,x),b=D(v,n),E=(0,r.useCallback)((function(){x.current&&P(i,x.current)}),[i]);return(0,r.useEffect)((function(){if(!a&&"down"!==i&&"right"!==i){var e=function(e,n){var t;function r(){for(var r=this,o=arguments.length,i=new Array(o),a=0;a<o;a++)i[a]=arguments[a];clearTimeout(t),t=setTimeout((function(){e.apply(r,i)}),n)}return void 0===n&&(n=166),r.clear=function(){clearTimeout(t)},r}((function(){x.current&&P(i,x.current)})),n=M(x.current);return n.addEventListener("resize",e),function(){e.clear(),n.removeEventListener("resize",e)}}}),[i,a]),(0,r.useEffect)((function(){a||E()}),[a,E]),(0,r.createElement)(O,Object.assign({appear:!0,nodeRef:x,onEnter:function(e,n){P(i,e),N(e),d&&d(e,n)},onEntered:p,onEntering:function(e){var n=(null==s?void 0:s.transitionTimingFunction)||"cubic-bezier(0.0, 0, 0.2, 1)",t=j({timeout:c,mode:"enter",style:l({},s,{transitionTimingFunction:n})});e.style.webkitTransition=R("-webkit-transform",t),e.style.transition=R("transform",t),e.style.webkitTransform="none",e.style.transform="none"},onExit:function(e){var n=(null==s?void 0:s.transitionTimingFunction)||"cubic-bezier(0.4, 0, 0.6, 1)",t=j({timeout:c,mode:"exit",style:l({},s,{transitionTimingFunction:n})});e.style.webkitTransition=R("-webkit-transform",t),e.style.transition=R("transform",t),P(i,e),m&&m(e)},onExited:function(e){e.style.webkitTransition="",e.style.transition="",h&&h(e)},in:a,timeout:c},g),(function(e,n){return(0,r.cloneElement)(t,l({ref:b,style:l({visibility:"exited"!==e||a?void 0:"hidden"},s,{},t.props.style)},n))}))}));A.displayName="Slide";var H=function(e){return o().createElement("svg",Object.assign({viewBox:"0 0 24 24",focusable:"false",style:{fontSize:20,marginInlineEnd:8,userSelect:"none",width:"1em",height:"1em",display:"inline-block",fill:"currentColor",flexShrink:0}},e))},q=function(){return o().createElement(H,null,o().createElement("path",{d:"M12 2C6.5 2 2 6.5 2 12S6.5 22 12 22 22 17.5 22 12 17.5 2 12 2M10 17L5 12L6.41\n        10.59L10 14.17L17.59 6.58L19 8L10 17Z"}))},V=function(){return o().createElement(H,null,o().createElement("path",{d:"M13,14H11V10H13M13,18H11V16H13M1,21H23L12,2L1,21Z"}))},I=function(){return o().createElement(H,null,o().createElement("path",{d:"M12,2C17.53,2 22,6.47 22,12C22,17.53 17.53,22 12,22C6.47,22 2,17.53 2,12C2,\n        6.47 6.47,2 12,2M15.59,7L12,10.59L8.41,7L7,8.41L10.59,12L7,15.59L8.41,17L12,\n        13.41L15.59,17L17,15.59L13.41,12L17,8.41L15.59,7Z"}))},W=function(){return o().createElement(H,null,o().createElement("path",{d:"M13,9H11V7H13M13,17H11V11H13M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,\n        0 22,12A10,10 0 0,0 12,2Z"}))},z={maxSnack:3,persist:!1,hideIconVariant:!1,disableWindowBlurListener:!1,variant:"default",autoHideDuration:5e3,iconVariant:{default:void 0,success:o().createElement(q,null),warning:o().createElement(V,null),error:o().createElement(I,null),info:o().createElement(W,null)},anchorOrigin:{vertical:"bottom",horizontal:"left"},TransitionComponent:A,transitionDuration:{enter:225,exit:195}};function B(e){return Object.entries(e).reduce((function(e,n){var t,r=n[0],o=n[1];return l({},e,((t={})[r]=(0,s.css)(o),t))}),{})}var F="notistack-CollapseWrapper",Z=function(e){return"notistack-MuiContent-"+e},X=B({root:{height:0},entered:{height:"auto"}}),G="0px",Q=(0,r.forwardRef)((function(e,n){var t=e.children,o=e.in,i=e.onExited,s=(0,r.useRef)(null),u=(0,r.useRef)(null),c=D(n,u),d=function(){return s.current?s.current.clientHeight:0};return(0,r.createElement)(O,{in:o,unmountOnExit:!0,onEnter:function(e){e.style.height=G},onEntered:function(e){e.style.height="auto"},onEntering:function(e){var n=d(),t=j({timeout:175,mode:"enter"}),r=t.duration,o=t.easing;e.style.transitionDuration="string"==typeof r?r:r+"ms",e.style.height=n+"px",e.style.transitionTimingFunction=o||""},onExit:function(e){e.style.height=d()+"px"},onExited:i,onExiting:function(e){N(e);var n=j({timeout:175,mode:"exit"}),t=n.duration,r=n.easing;e.style.transitionDuration="string"==typeof t?t:t+"ms",e.style.height=G,e.style.transitionTimingFunction=r||""},nodeRef:u,timeout:175},(function(e,n){return(0,r.createElement)("div",Object.assign({ref:c,className:(0,a.default)(X.root,"entered"===e&&X.entered),style:l({pointerEvents:"all",overflow:"hidden",minHeight:G,transition:R("height")},"entered"===e&&{overflow:"visible"},{},"exited"===e&&!o&&{visibility:"hidden"})},n),(0,r.createElement)("div",{ref:s,className:F,style:{display:"flex",width:"100%"}},t))}))}));Q.displayName="Collapse";var U={right:"left",left:"right",bottom:"up",top:"down"},Y=function(e){return"anchorOrigin"+b(e)},$=function(){};function J(e,n){return e.reduce((function(e,t){return null==t?e:function(){for(var r=arguments.length,o=new Array(r),i=0;i<r;i++)o[i]=arguments[i];var a=[].concat(o);n&&-1===a.indexOf(n)&&a.push(n),e.apply(this,a),t.apply(this,a)}}),$)}var K="undefined"!=typeof window?r.useLayoutEffect:r.useEffect;function ee(e){var n=(0,r.useRef)(e);return K((function(){n.current=e})),(0,r.useCallback)((function(){return n.current.apply(void 0,arguments)}),[])}var ne,te=(0,r.forwardRef)((function(e,n){var t=e.children,o=e.className,i=e.autoHideDuration,s=e.disableWindowBlurListener,u=void 0!==s&&s,c=e.onClose,l=e.id,d=e.open,f=e.SnackbarProps,p=void 0===f?{}:f,m=(0,r.useRef)(),h=ee((function(){c&&c.apply(void 0,arguments)})),g=ee((function(e){c&&null!=e&&(m.current&&clearTimeout(m.current),m.current=setTimeout((function(){h(null,"timeout",l)}),e))}));(0,r.useEffect)((function(){return d&&g(i),function(){m.current&&clearTimeout(m.current)}}),[d,i,g]);var x=function(){m.current&&clearTimeout(m.current)},v=(0,r.useCallback)((function(){null!=i&&g(.5*i)}),[i,g]);return(0,r.useEffect)((function(){if(!u&&d)return window.addEventListener("focus",v),window.addEventListener("blur",x),function(){window.removeEventListener("focus",v),window.removeEventListener("blur",x)}}),[u,v,d]),(0,r.createElement)("div",Object.assign({ref:n},p,{className:(0,a.default)("notistack-Snackbar",o),onMouseEnter:function(e){p.onMouseEnter&&p.onMouseEnter(e),x()},onMouseLeave:function(e){p.onMouseLeave&&p.onMouseLeave(e),v()}}),t)}));te.displayName="Snackbar";var re=B({root:(ne={display:"flex",flexWrap:"wrap",flexGrow:1},ne[x]={flexGrow:"initial",minWidth:"288px"},ne)}),oe=(0,r.forwardRef)((function(e,n){var t=e.className,r=f(e,["className"]);return o().createElement("div",Object.assign({ref:n,className:(0,a.default)(re.root,t)},r))}));oe.displayName="SnackbarContent";var ie=B({root:{backgroundColor:"#313131",fontSize:"0.875rem",lineHeight:1.43,letterSpacing:"0.01071em",color:"#fff",alignItems:"center",padding:"6px 16px",borderRadius:"4px",boxShadow:"0px 3px 5px -1px rgba(0,0,0,0.2),0px 6px 10px 0px rgba(0,0,0,0.14),0px 1px 18px 0px rgba(0,0,0,0.12)"},lessPadding:{paddingLeft:"20px"},default:{backgroundColor:"#313131"},success:{backgroundColor:"#43a047"},error:{backgroundColor:"#d32f2f"},warning:{backgroundColor:"#ff9800"},info:{backgroundColor:"#2196f3"},message:{display:"flex",alignItems:"center",padding:"8px 0"},action:{display:"flex",alignItems:"center",marginLeft:"auto",paddingLeft:"16px",marginRight:"-8px"}}),ae="notistack-snackbar",se=(0,r.forwardRef)((function(e,n){var t=e.id,r=e.message,i=e.action,s=e.iconVariant,u=e.variant,c=e.hideIconVariant,l=e.style,d=e.className,f=s[u],p=i;return"function"==typeof p&&(p=p(t)),o().createElement(oe,{ref:n,role:"alert","aria-describedby":ae,style:l,className:(0,a.default)("notistack-MuiContent",Z(u),ie.root,ie[u],d,!c&&f&&ie.lessPadding)},o().createElement("div",{id:ae,className:ie.message},c?null:f,r),p&&o().createElement("div",{className:ie.action},p))}));se.displayName="MaterialDesignContent";var ue,ce,le,de,fe,pe,me,he=(0,r.memo)(se),ge=B({wrappedRoot:{width:"100%",position:"relative",transform:"translateX(0)",top:0,right:0,bottom:0,left:0,minWidth:"288px"}}),xe=function(e){var n=(0,r.useRef)(),t=(0,r.useState)(!0),i=t[0],s=t[1],u=J([e.snack.onClose,e.onClose]),c=(0,r.useCallback)((function(){n.current=setTimeout((function(){s((function(e){return!e}))}),125)}),[]);(0,r.useEffect)((function(){return function(){n.current&&clearTimeout(n.current)}}),[]);var d,p=e.snack,m=e.classes,h=e.Component,g=void 0===h?he:h,x=(0,r.useMemo)((function(){return function(e){void 0===e&&(e={});var n={containerRoot:!0,containerAnchorOriginTopCenter:!0,containerAnchorOriginBottomCenter:!0,containerAnchorOriginTopRight:!0,containerAnchorOriginBottomRight:!0,containerAnchorOriginTopLeft:!0,containerAnchorOriginBottomLeft:!0};return Object.keys(e).filter((function(e){return!n[e]})).reduce((function(n,t){var r;return l({},n,((r={})[t]=e[t],r))}),{})}(m)}),[m]),v=p.open,b=p.SnackbarProps,E=p.TransitionComponent,k=p.TransitionProps,y=p.transitionDuration,w=p.disableWindowBlurListener,S=p.content,C=f(p,["open","SnackbarProps","TransitionComponent","TransitionProps","transitionDuration","disableWindowBlurListener","content","entered","requestClose","onEnter","onEntered","onExit","onExited"]),O=l({direction:(d=C.anchorOrigin,"center"!==d.horizontal?U[d.horizontal]:U[d.vertical]),timeout:y},k),T=S;"function"==typeof T&&(T=T(C.id,C.message));var L=["onEnter","onEntered","onExit","onExited"].reduce((function(n,t){var r;return l({},n,((r={})[t]=J([e.snack[t],e[t]],C.id),r))}),{});return o().createElement(Q,{in:i,onExited:L.onExited},o().createElement(te,{open:v,id:C.id,disableWindowBlurListener:w,autoHideDuration:C.autoHideDuration,className:(0,a.default)(ge.wrappedRoot,x.root,x[Y(C.anchorOrigin)]),SnackbarProps:b,onClose:u},o().createElement(E,Object.assign({},O,{appear:!0,in:v,onExit:L.onExit,onExited:c,onEnter:L.onEnter,onEntered:J([L.onEntered,function(){e.snack.requestClose&&u(null,"instructed",e.snack.id)}],C.id)}),T||o().createElement(g,Object.assign({},C)))))},ve=20,be=6,Ee=2,ke="."+F,ye=B({root:(ue={boxSizing:"border-box",display:"flex",maxHeight:"100%",position:"fixed",zIndex:1400,height:"auto",width:"auto",transition:R(["top","right","bottom","left","max-width"],{duration:300,easing:"ease"}),pointerEvents:"none"},ue[ke]={padding:be+"px 0px",transition:"padding 300ms ease 0ms"},ue.maxWidth="calc(100% - "+2*ve+"px)",ue[g]={width:"100%",maxWidth:"calc(100% - 32px)"},ue),rootDense:(ce={},ce[ke]={padding:Ee+"px 0px"},ce),top:{top:ve-be+"px",flexDirection:"column"},bottom:{bottom:ve-be+"px",flexDirection:"column-reverse"},left:(le={left:ve+"px"},le[x]={alignItems:"flex-start"},le[g]={left:"16px"},le),right:(de={right:ve+"px"},de[x]={alignItems:"flex-end"},de[g]={right:"16px"},de),center:(fe={left:"50%",transform:"translateX(-50%)"},fe[x]={alignItems:"center"},fe)}),we=function(e){var n=e.classes,t=void 0===n?{}:n,r=e.anchorOrigin,i=e.dense,s=e.children,u=(0,a.default)("notistack-SnackbarContainer",ye[r.vertical],ye[r.horizontal],ye.root,t.containerRoot,t["containerAnchorOrigin"+b(r)],i&&ye.rootDense);return o().createElement("div",{className:u},s)},Se=(0,r.memo)(we),Ce={NO_PERSIST_ALL:"Reached maxSnack while all enqueued snackbars have 'persist' flag. Notistack will dismiss the oldest snackbar anyway to allow other ones in the queue to be presented."},Oe=function(e){return!("string"==typeof e||(0,r.isValidElement)(e))},Te=function(e){function n(n){var t;return(t=e.call(this,n)||this).enqueueSnackbar=function(e,n){if(void 0===n&&(n={}),null==e)throw new Error("enqueueSnackbar called with invalid argument");var r=Oe(e)?e:n,o=Oe(e)?e.message:e,i=r.key,s=r.preventDuplicate,u=f(r,["key","preventDuplicate"]),c=E(i),d=c?i:(new Date).getTime()+Math.random(),p=function(e,n){return function(t,r){return void 0===r&&(r=!1),r?l({},z[t],{},n[t],{},e[t]):"autoHideDuration"===t?(o=e.autoHideDuration,i=n.autoHideDuration,(a=function(e){return"number"==typeof e||null===e})(o)?o:a(i)?i:z.autoHideDuration):"transitionDuration"===t?function(e,n){var t=function(e,n){return n.some((function(n){return typeof e===n}))};return t(e,["string","number"])?e:t(e,["object"])?l({},z.transitionDuration,{},t(n,["object"])&&n,{},e):t(n,["string","number"])?n:t(n,["object"])?l({},z.transitionDuration,{},n):z.transitionDuration}(e.transitionDuration,n.transitionDuration):e[t]||n[t]||z[t];var o,i,a}}(u,t.props),m=l({id:d},u,{message:o,open:!0,entered:!1,requestClose:!1,persist:p("persist"),action:p("action"),content:p("content"),variant:p("variant"),anchorOrigin:p("anchorOrigin"),disableWindowBlurListener:p("disableWindowBlurListener"),autoHideDuration:p("autoHideDuration"),hideIconVariant:p("hideIconVariant"),TransitionComponent:p("TransitionComponent"),transitionDuration:p("transitionDuration"),TransitionProps:p("TransitionProps",!0),iconVariant:p("iconVariant",!0),style:p("style",!0),SnackbarProps:p("SnackbarProps",!0),className:(0,a.default)(t.props.className,u.className)});return m.persist&&(m.autoHideDuration=void 0),t.setState((function(e){if(void 0===s&&t.props.preventDuplicate||s){var n=function(e){return c?e.id===d:e.message===o},r=e.queue.findIndex(n)>-1,i=e.snacks.findIndex(n)>-1;if(r||i)return e}return t.handleDisplaySnack(l({},e,{queue:[].concat(e.queue,[m])}))})),d},t.handleDisplaySnack=function(e){return e.snacks.length>=t.maxSnack?t.handleDismissOldest(e):t.processQueue(e)},t.processQueue=function(e){var n=e.queue,t=e.snacks;return n.length>0?l({},e,{snacks:[].concat(t,[n[0]]),queue:n.slice(1,n.length)}):e},t.handleDismissOldest=function(e){if(e.snacks.some((function(e){return!e.open||e.requestClose})))return e;var n=!1,r=!1;e.snacks.reduce((function(e,n){return e+(n.open&&n.persist?1:0)}),0)===t.maxSnack&&(function(){var e=Ce.NO_PERSIST_ALL;"undefined"!=typeof console&&console.error("WARNING - notistack: "+e);try{throw new Error(e)}catch(e){}}(),r=!0);var o=e.snacks.map((function(e){return n||e.persist&&!r?l({},e):(n=!0,e.entered?(e.onClose&&e.onClose(null,"maxsnack",e.id),t.props.onClose&&t.props.onClose(null,"maxsnack",e.id),l({},e,{open:!1})):l({},e,{requestClose:!0}))}));return l({},e,{snacks:o})},t.handleEnteredSnack=function(e,n,r){if(!E(r))throw new Error("handleEnteredSnack Cannot be called with undefined key");t.setState((function(e){return{snacks:e.snacks.map((function(e){return e.id===r?l({},e,{entered:!0}):l({},e)}))}}))},t.handleCloseSnack=function(e,n,r){t.props.onClose&&t.props.onClose(e,n,r);var o=void 0===r;t.setState((function(e){var n=e.snacks,t=e.queue;return{snacks:n.map((function(e){return o||e.id===r?e.entered?l({},e,{open:!1}):l({},e,{requestClose:!0}):l({},e)})),queue:t.filter((function(e){return e.id!==r}))}}))},t.closeSnackbar=function(e){var n=t.state.snacks.find((function(n){return n.id===e}));E(e)&&n&&n.onClose&&n.onClose(null,"instructed",e),t.handleCloseSnack(null,"instructed",e)},t.handleExitedSnack=function(e,n){if(!E(n))throw new Error("handleExitedSnack Cannot be called with undefined key");t.setState((function(e){var r=t.processQueue(l({},e,{snacks:e.snacks.filter((function(e){return e.id!==n}))}));return 0===r.queue.length?r:t.handleDismissOldest(r)}))},pe=t.enqueueSnackbar,me=t.closeSnackbar,t.state={snacks:[],queue:[],contextValue:{enqueueSnackbar:t.enqueueSnackbar.bind(p(t)),closeSnackbar:t.closeSnackbar.bind(p(t))}},t}return d(n,e),n.prototype.render=function(){var e=this,n=this.state.contextValue,t=this.props,r=t.domRoot,a=t.children,s=t.dense,u=void 0!==s&&s,c=t.Components,d=void 0===c?{}:c,f=t.classes,p=this.state.snacks.reduce((function(e,n){var t,r=b(n.anchorOrigin),o=e[r]||[];return l({},e,((t={})[r]=[].concat(o,[n]),t))}),{}),m=Object.keys(p).map((function(n){var t=p[n],r=t[0];return o().createElement(Se,{key:n,dense:u,anchorOrigin:r.anchorOrigin,classes:f},t.map((function(n){return o().createElement(xe,{key:n.id,snack:n,classes:f,Component:d[n.variant],onClose:e.handleCloseSnack,onEnter:e.props.onEnter,onExit:e.props.onExit,onExited:J([e.handleExitedSnack,e.props.onExited],n.id),onEntered:J([e.handleEnteredSnack,e.props.onEntered],n.id)})})))}));return o().createElement(h.Provider,{value:n},a,r?(0,i.createPortal)(m,r):m)},c(n,[{key:"maxSnack",get:function(){return this.props.maxSnack||z.maxSnack}}]),n}(r.Component),Le=function(){return(0,r.useContext)(h)}},react:function(e){e.exports=window.React},"react-dom":function(e){e.exports=window.ReactDOM},"@elementor/editor":function(e){e.exports=window.elementorV2.editor},"@elementor/store":function(e){e.exports=window.elementorV2.store},"@elementor/ui":function(e){e.exports=window.elementorV2.ui},"./node_modules/goober/dist/goober.modern.js":function(e,n,t){t.r(n),t.d(n,{css:function(){return m},extractCss:function(){return i},glob:function(){return v},keyframes:function(){return b},setup:function(){return E},styled:function(){return k}});let r={data:""},o=e=>"object"==typeof window?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||r,i=e=>{let n=o(e),t=n.data;return n.data="",t},a=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,s=/\/\*[^]*?\*\/|  +/g,u=/\n+/g,c=(e,n)=>{let t="",r="",o="";for(let i in e){let a=e[i];"@"==i[0]?"i"==i[1]?t=i+" "+a+";":r+="f"==i[1]?c(a,i):i+"{"+c(a,"k"==i[1]?"":n)+"}":"object"==typeof a?r+=c(a,n?n.replace(/([^,])+/g,(e=>i.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,(n=>/&/.test(n)?n.replace(/&/g,e):e?e+" "+n:n)))):i):null!=a&&(i=/^--/.test(i)?i:i.replace(/[A-Z]/g,"-$&").toLowerCase(),o+=c.p?c.p(i,a):i+":"+a+";")}return t+(n&&o?n+"{"+o+"}":o)+r},l={},d=e=>{if("object"==typeof e){let n="";for(let t in e)n+=t+d(e[t]);return n}return e},f=(e,n,t,r,o)=>{let i=d(e),f=l[i]||(l[i]=(e=>{let n=0,t=11;for(;n<e.length;)t=101*t+e.charCodeAt(n++)>>>0;return"go"+t})(i));if(!l[f]){let n=i!==e?e:(e=>{let n,t,r=[{}];for(;n=a.exec(e.replace(s,""));)n[4]?r.shift():n[3]?(t=n[3].replace(u," ").trim(),r.unshift(r[0][t]=r[0][t]||{})):r[0][n[1]]=n[2].replace(u," ").trim();return r[0]})(e);l[f]=c(o?{["@keyframes "+f]:n}:n,t?"":"."+f)}let p=t&&l.g?l.g:null;return t&&(l.g=l[f]),((e,n,t,r)=>{r?n.data=n.data.replace(r,e):-1===n.data.indexOf(e)&&(n.data=t?e+n.data:n.data+e)})(l[f],n,r,p),f},p=(e,n,t)=>e.reduce(((e,r,o)=>{let i=n[o];if(i&&i.call){let e=i(t),n=e&&e.props&&e.props.className||/^go/.test(e)&&e;i=n?"."+n:e&&"object"==typeof e?e.props?"":c(e,""):!1===e?"":e}return e+r+(null==i?"":i)}),"");function m(e){let n=this||{},t=e.call?e(n.p):e;return f(t.unshift?t.raw?p(t,[].slice.call(arguments,1),n.p):t.reduce(((e,t)=>Object.assign(e,t&&t.call?t(n.p):t)),{}):t,o(n.target),n.g,n.o,n.k)}let h,g,x,v=m.bind({g:1}),b=m.bind({k:1});function E(e,n,t,r){c.p=n,h=e,g=t,x=r}function k(e,n){let t=this||{};return function(){let r=arguments;function o(i,a){let s=Object.assign({},i),u=s.className||o.className;t.p=Object.assign({theme:g&&g()},s),t.o=/ *go\d+/.test(u),s.className=m.apply(t,r)+(u?" "+u:""),n&&(s.ref=a);let c=e;return e[0]&&(c=s.as||e,delete s.as),x&&c[0]&&x(s),h(c,s)}return n?n(o):o}}}},n={};function t(r){var o=n[r];if(void 0!==o)return o.exports;var i=n[r]={exports:{}};return e[r](i,i.exports,t),i.exports}t.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,{a:n}),n},t.d=function(e,n){for(var r in n)t.o(n,r)&&!t.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:n[r]})},t.o=function(e,n){return Object.prototype.hasOwnProperty.call(e,n)},t.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};!function(){t.r(r),t.d(r,{NotifyReact:function(){return p},init:function(){return h},notify:function(){return f}});var e=t("@elementor/editor"),n=t("@elementor/store"),o=t("react"),i=t("./node_modules/notistack/notistack.esm.js"),a=t("@elementor/ui"),s=(0,n.__createSlice)({name:"notifications",initialState:{},reducers:{notifyAction:(e,n)=>{const t={...e};return t[n.payload.id]||(t[n.payload.id]=n.payload),t},clearAction:(e,n)=>{const t={...e};return t[n.payload.id]&&delete t[n.payload.id],t}}}),{notifyAction:u,clearAction:c}=s.actions,l={default:(0,o.forwardRef)(((e,n)=>{const t=function(e){const n=["autoHideDuration","persist","hideIconVariant","iconVariant","anchorOrigin"];return Object.entries(e).reduce(((e,[t,r])=>(n.includes(t)||(e[t]=r),e)),{})}(e),r=document.querySelector(".elementor-panel")?.clientWidth||0;return o.createElement(a.ThemeProvider,{palette:"unstable"},o.createElement(a.SnackbarContent,{ref:n,...t,sx:{"&.MuiPaper-root":{minWidth:"max-content"},ml:r+"px"}}))}))},d=()=>((e=>{const{enqueueSnackbar:t}=(0,i.useSnackbar)(),r=(0,n.__useDispatch)();(0,o.useEffect)((()=>{Object.values(e).forEach((e=>{const n=()=>o.createElement(o.Fragment,{key:e.id},e.additionalActionProps?.map(((e,n)=>o.createElement(a.Button,{key:`${n}`,...e}))),o.createElement(a.CloseButton,{"aria-label":"close",color:"inherit",onClick:()=>{(0,i.closeSnackbar)(e.id),r(c({id:e.id}))}}));t(e.message,{persist:!0,variant:e.type,key:e.id,onClose:()=>r(c({id:e.id})),preventDuplicate:!0,action:o.createElement(n,null)})}))}),[e,t,r])})((0,n.__useSelector)((e=>e.notifications))),null);function f(e){const t=(0,n.__getStore)();t?.dispatch(u(e))}function p(e){(0,n.__useDispatch)()(u(e))}var m=()=>o.createElement(i.SnackbarProvider,{maxSnack:3,autoHideDuration:8e3,anchorOrigin:{horizontal:"center",vertical:"bottom"},Components:l},o.createElement(d,null));function h(){(0,n.__registerSlice)(s),(0,e.injectIntoTop)({id:"notifications",component:m})}}(),(window.elementorV2=window.elementorV2||{}).editorNotifications=r}(),window.elementorV2.editorNotifications?.init?.();