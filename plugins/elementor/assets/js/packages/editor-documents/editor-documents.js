/*! For license information please see editor-documents.js.LICENSE.txt */
!function(){"use strict";var t={react:function(t){t.exports=window.React},"@elementor/editor":function(t){t.exports=window.elementorV2.editor},"@elementor/editor-v1-adapters":function(t){t.exports=window.elementorV2.editorV1Adapters},"@elementor/store":function(t){t.exports=window.elementorV2.store},"@elementor/utils":function(t){t.exports=window.elementorV2.utils},"@wordpress/i18n":function(t){t.exports=window.wp.i18n}},e={};function n(i){var o=e[i];if(void 0!==o)return o.exports;var r=e[i]={exports:{}};return t[i](r,r.exports,n),r.exports}n.d=function(t,e){for(var i in e)n.o(e,i)&&!n.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};var i={};!function(){n.r(i),n.d(i,{__useActiveDocument:function(){return l},__useActiveDocumentActions:function(){return D},__useHostDocument:function(){return m},__useNavigateToDocument:function(){return b},getCurrentDocument:function(){return C},getV1DocumentsManager:function(){return f},init:function(){return I},setDocumentModifiedStatus:function(){return y},slice:function(){return p}});var t=n("@elementor/editor"),e=n("@elementor/store"),o=n("react"),r=n("@wordpress/i18n"),s=n("@elementor/editor-v1-adapters"),a=n("@elementor/utils"),c=t=>t.documents.entities,d=(0,e.__createSelector)(c,(t=>t.documents.activeId),((t,e)=>e&&t[e]?t[e]:null)),u=(0,e.__createSelector)(c,(t=>t.documents.hostId),((t,e)=>e&&t[e]?t[e]:null));function l(){return(0,e.__useSelector)(d)}function m(){return(0,e.__useSelector)(u)}function v(){return function(){const t=l(),e=m(),n=t&&"kit"!==t.type.value?t:e;(0,o.useEffect)((()=>{if(void 0===n?.title)return;const t=(0,r.__)('Edit "%s" with Elementor',"elementor").replace("%s",n.title);window.document.title=t}),[n?.title])}(),null}function _(t){return!(!t.activeId||!t.entities[t.activeId])}var p=(0,e.__createSlice)({name:"documents",initialState:{entities:{},activeId:null,hostId:null},reducers:{init(t,{payload:e}){t.entities=e.entities,t.hostId=e.hostId,t.activeId=e.activeId},activateDocument(t,e){t.entities[e.payload.id]=e.payload,t.activeId=e.payload.id},setAsHost(t,e){t.hostId=e.payload},updateActiveDocument(t,e){_(t)&&(t.entities[t.activeId]={...t.entities[t.activeId],...e.payload})},startSaving(t){_(t)&&(t.entities[t.activeId].isSaving=!0)},endSaving(t,e){_(t)&&(t.entities[t.activeId]={...e.payload,isSaving:!1})},startSavingDraft:t=>{_(t)&&(t.entities[t.activeId].isSavingDraft=!0)},endSavingDraft(t,e){_(t)&&(t.entities[t.activeId]={...e.payload,isSavingDraft:!1})},markAsDirty(t){_(t)&&(t.entities[t.activeId].isDirty=!0)},markAsPristine(t){_(t)&&(t.entities[t.activeId].isDirty=!1)}}});function f(){const t=window.elementor?.documents;if(!t)throw new Error("Elementor Editor V1 documents manager not found");return t}function g(t){switch(window.elementor?.getPreferences?.("exit_to")||"this_post"){case"dashboard":return t.config.urls.main_dashboard;case"all_posts":return t.config.urls.all_post_type;default:return t.config.urls.exit_to_dashboard}}function w(t){return t?.config?.panel?.show_copy_and_share??!1}function h(t){return t.config.urls.permalink??""}function S(t){const e=t.config.revisions.current_id!==t.id,n=g(t);return{id:t.id,title:t.container.settings.get("post_title"),type:{value:t.config.type,label:t.config.panel.title},status:{value:t.config.status.value,label:t.config.status.label},links:{permalink:h(t),platformEdit:n},isDirty:t.editor.isChanged||e,isSaving:t.editor.isSaving,isSavingDraft:!1,permissions:{allowAddingWidgets:t.config.panel?.allow_adding_widgets??!0,showCopyAndShare:w(t)},userCan:{publish:t.config.user.can_publish}}}function y(t){(0,s.__privateRunCommandSync)("document/save/set-is-modified",{status:t},{internal:!0})}function I(){(0,e.__registerSlice)(p),function(){const{init:t}=p.actions;(0,s.__privateListenTo)((0,s.v1ReadyEvent)(),(()=>{const n=f(),i=Object.entries(n.documents).reduce(((t,[e,n])=>(t[e]=S(n),t)),{});(0,e.__dispatch)(t({entities:i,hostId:n.getInitialId(),activeId:n.getCurrentId()}))}))}(),function(){const{activateDocument:t,setAsHost:n}=p.actions;(0,s.__privateListenTo)((0,s.commandEndEvent)("editor/documents/open"),(()=>{const i=f(),o=S(i.getCurrent());(0,e.__dispatch)(t(o)),i.getInitialId()===o.id&&(0,e.__dispatch)(n(o.id))}))}(),function(){const{startSaving:t,endSaving:n,startSavingDraft:i,endSavingDraft:o}=p.actions,r=t=>{const e=t;return"autosave"===e.args?.status};(0,s.__privateListenTo)((0,s.commandStartEvent)("document/save/save"),(n=>{r(n)?(0,e.__dispatch)(i()):(0,e.__dispatch)(t())})),(0,s.__privateListenTo)((0,s.commandEndEvent)("document/save/save"),(t=>{const i=S(f().getCurrent());r(t)?(0,e.__dispatch)(o(i)):(0,e.__dispatch)(n(i))}))}(),function(){const{updateActiveDocument:t}=p.actions,n=(0,a.debounce)((n=>{const i=n;if(!("post_title"in i.args?.settings))return;const o=f().getCurrent().container.settings.get("post_title");(0,e.__dispatch)(t({title:o}))}),400);(0,s.__privateListenTo)((0,s.commandEndEvent)("document/elements/settings"),n)}(),function(){const{markAsDirty:t,markAsPristine:n}=p.actions;(0,s.__privateListenTo)((0,s.commandEndEvent)("document/save/set-is-modified"),(()=>{const i=d((0,e.__getState)())?.isSaving;i||(f().getCurrent().editor.isChanged?(0,e.__dispatch)(t()):(0,e.__dispatch)(n()))}))}(),function(){const{updateActiveDocument:t}=p.actions,n=(0,a.debounce)((n=>{const i=n;if(!("exit_to"in i.args?.settings))return;const o=f().getCurrent(),r=g(o),s=h(o);(0,e.__dispatch)(t({links:{platformEdit:r,permalink:s}}))}),400);(0,s.__privateListenTo)((0,s.commandEndEvent)("document/elements/settings"),n)}(),(0,t.injectIntoLogic)({id:"documents-hooks",component:v})}function D(){const t=l(),e=t?.links?.permalink??"";return{save:(0,o.useCallback)((()=>(0,s.__privateRunCommand)("document/save/default")),[]),saveDraft:(0,o.useCallback)((()=>(0,s.__privateRunCommand)("document/save/draft")),[]),saveTemplate:(0,o.useCallback)((()=>(0,s.__privateOpenRoute)("library/save-template")),[]),copyAndShare:(0,o.useCallback)((()=>{navigator.clipboard.writeText(e)}),[e])}}function b(){return(0,o.useCallback)((async t=>{await(0,s.__privateRunCommand)("editor/documents/switch",{id:t,setAsInitial:!0});const e=new URL(window.location.href);e.searchParams.set("post",t.toString()),e.searchParams.delete("active-document"),history.replaceState({},"",e)}),[])}function C(){return d((0,e.__getState)())}}(),(window.elementorV2=window.elementorV2||{}).editorDocuments=i}(),window.elementorV2.editorDocuments?.init?.();