<?php
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}
/**
 * This file is generated by Webpack, do not edit it directly.
 */
return [
	'handle' => 'elementor-v2-editor-editing-panel',
	'deps' => [
		'elementor-v2-editor',
		'elementor-v2-editor-canvas',
		'elementor-v2-editor-controls',
		'elementor-v2-editor-current-user',
		'elementor-v2-editor-documents',
		'elementor-v2-editor-elements',
		'elementor-v2-editor-panels',
		'elementor-v2-editor-props',
		'elementor-v2-editor-responsive',
		'elementor-v2-editor-styles',
		'elementor-v2-editor-styles-repository',
		'elementor-v2-editor-ui',
		'elementor-v2-editor-v1-adapters',
		'elementor-v2-icons',
		'elementor-v2-locations',
		'elementor-v2-menus',
		'elementor-v2-schema',
		'elementor-v2-session',
		'elementor-v2-ui',
		'elementor-v2-utils',
		'elementor-v2-wp-media',
		'react',
		'wp-i18n',
	],
];
