__( 'Clear', 'elementor' );
__( 'Style origin', 'elementor' );
/* translators: %s: Label of the inheritance item */
__( 'Inheritance item: %s', 'elementor' );
__( 'Style origin', 'elementor' );
__( 'Style origin', 'elementor' );
__( 'This is the final value', 'elementor' );
__( 'This value is overridden by another style', 'elementor' );
__( 'This has value from another style', 'elementor' );
__( 'Style edited', 'elementor' );
__( 'Style edited', 'elementor' );
__( 'Layout', 'elementor' );
__( 'Spacing', 'elementor' );
__( 'Size', 'elementor' );
__( 'Position', 'elementor' );
__( 'Typography', 'elementor' );
__( 'Background', 'elementor' );
__( 'Border', 'elementor' );
__( 'Effects', 'elementor' );
/* translators: %s: Element type title. */
__( 'Edit %s', 'elementor' );
__( 'General', 'elementor' );
__( 'Style', 'elementor' );
__( 'Show less', 'elementor' );
__( 'Show more', 'elementor' );
__( 'Linear Gradient', 'elementor' );
__( 'Radial Gradient', 'elementor' );
__( 'Base', 'elementor' );
__( 'Inherited from base styles', 'elementor' );
__( 'Search dynamic tags…', 'elementor' );
__( 'Sorry, nothing matched', 'elementor' );
__( 'Try something else.', 'elementor' );
__( 'Clear & try again', 'elementor' );
__( 'Streamline your workflow with dynamic tags', 'elementor' );
__( 'You’ll need Elementor Pro to use this feature.', 'elementor' );
__( 'Remove dynamic value', 'elementor' );
__( 'Dynamic tags', 'elementor' );
__( 'Settings', 'elementor' );
__( 'Dynamic tags', 'elementor' );
/* translators: %s is the class name. */
__( `class %s applied`, 'elementor' );
/* translators: %s is the class name. */
__( `class %s removed`, 'elementor' );
__( 'Class', 'elementor' );
/* translators: %s is the class name. */
__( `%s created`, 'elementor' );
__( 'local', 'elementor' );
__( 'Classes', 'elementor' );
__( 'Type class name', 'elementor' );
__(
						'With your current role, you can use existing classes but can’t modify them.',
						'elementor'
					);
__( 'Sorry, nothing matched', 'elementor' );
__( 'With your current role,', 'elementor' );
__( 'you can only use existing classes.', 'elementor' );
__( 'Clear & try again', 'elementor' );
__(
					'You’ve reached the limit of 50 classes. Please remove an existing one to create a new class.',
					'elementor'
				);
__( 'States', 'elementor' );
__( 'With your current role, you can only use existing states.', 'elementor' );
__( 'Has style', 'elementor' );
__( 'Remove', 'elementor' );
__(
					'With your current role, you can use existing classes but can’t modify them.',
					'elementor'
				);
__( 'Rename', 'elementor' );
__( 'Open CSS Class Menu', 'elementor' );
__( 'Word spacing', 'elementor' );
__( 'None', 'elementor' );
__( 'Capitalize', 'elementor' );
__( 'Uppercase', 'elementor' );
__( 'Lowercase', 'elementor' );
__( 'Text transform', 'elementor' );
__( 'Text stroke', 'elementor' );
__( 'Left to right', 'elementor' );
__( 'Right to left', 'elementor' );
__( 'Direction', 'elementor' );
__( 'None', 'elementor' );
__( 'Underline', 'elementor' );
__( 'Line-through', 'elementor' );
__( 'Overline', 'elementor' );
__( 'Line decoration', 'elementor' );
__( 'Text color', 'elementor' );
__( 'Start', 'elementor' );
__( 'Center', 'elementor' );
__( 'End', 'elementor' );
__( 'Justify', 'elementor' );
__( 'Text align', 'elementor' );
__( 'Line height', 'elementor' );
__( 'Letter spacing', 'elementor' );
__( '100 - Thin', 'elementor' );
__( '200 - Extra light', 'elementor' );
__( '300 - Light', 'elementor' );
__( '400 - Normal', 'elementor' );
__( '500 - Medium', 'elementor' );
__( '600 - Semi bold', 'elementor' );
__( '700 - Bold', 'elementor' );
__( '800 - Extra bold', 'elementor' );
__( '900 - Black', 'elementor' );
__( 'Font weight', 'elementor' );
__( 'Normal', 'elementor' );
__( 'Italic', 'elementor' );
__( 'Font style', 'elementor' );
__( 'Font size', 'elementor' );
__( 'Font family', 'elementor' );
__( 'Column gap', 'elementor' );
__( 'Columns', 'elementor' );
__( 'Margin', 'elementor' );
__( 'Padding', 'elementor' );
__( 'Width', 'elementor' );
__( 'Height', 'elementor' );
__( 'Min width', 'elementor' );
__( 'Min height', 'elementor' );
__( 'Max width', 'elementor' );
__( 'Max height', 'elementor' );
__( 'Aspect Ratio', 'elementor' );
__( 'Visible', 'elementor' );
__( 'Hidden', 'elementor' );
__( 'Auto', 'elementor' );
__( 'Overflow', 'elementor' );
__( 'Center center', 'elementor' );
__( 'Center left', 'elementor' );
__( 'Center right', 'elementor' );
__( 'Top center', 'elementor' );
__( 'Top left', 'elementor' );
__( 'Top right', 'elementor' );
__( 'Bottom center', 'elementor' );
__( 'Bottom left', 'elementor' );
__( 'Bottom right', 'elementor' );
__( 'Object position', 'elementor' );
__( 'Fill', 'elementor' );
__( 'Cover', 'elementor' );
__( 'Contain', 'elementor' );
__( 'None', 'elementor' );
__( 'Scale down', 'elementor' );
__( 'Object fit', 'elementor' );
__( 'Z-index', 'elementor' );
__( 'Static', 'elementor' );
__( 'Relative', 'elementor' );
__( 'Absolute', 'elementor' );
__( 'Fixed', 'elementor' );
__( 'Sticky', 'elementor' );
__( 'Position', 'elementor' );
__( 'Anchor offset', 'elementor' );
__( 'Right', 'elementor' );
__( 'Left', 'elementor' );
__( 'Left', 'elementor' );
__( 'Right', 'elementor' );
__( 'Top', 'elementor' );
__( 'Bottom', 'elementor' );
__( 'No wrap', 'elementor' );
__( 'Wrap', 'elementor' );
__( 'Reversed wrap', 'elementor' );
__( 'Wrap', 'elementor' );
__( 'Flex child', 'elementor' );
__( 'Start', 'elementor' );
__( 'Center', 'elementor' );
__( 'End', 'elementor' );
__( 'Space between', 'elementor' );
__( 'Space around', 'elementor' );
__( 'Space evenly', 'elementor' );
__( 'Justify content', 'elementor' );
__( 'Gaps', 'elementor' );
__( 'Grow', 'elementor' );
__( 'Shrink', 'elementor' );
__( 'Custom', 'elementor' );
__( 'Size', 'elementor' );
__( 'Grow', 'elementor' );
__( 'Shrink', 'elementor' );
__( 'Basis', 'elementor' );
__( 'First', 'elementor' );
__( 'Last', 'elementor' );
__( 'Custom', 'elementor' );
__( 'Order', 'elementor' );
__( 'Custom order', 'elementor' );
__( 'Row', 'elementor' );
__( 'Column', 'elementor' );
__( 'Reversed row', 'elementor' );
__( 'Reversed column', 'elementor' );
__( 'Direction', 'elementor' );
__( 'Block', 'elementor' );
__( 'Block', 'elementor' );
__( 'Flex', 'elementor' );
__( 'Flex', 'elementor' );
__( 'In-blk', 'elementor' );
__( 'Inline-block', 'elementor' );
__( 'None', 'elementor' );
__( 'None', 'elementor' );
__( 'In-flx', 'elementor' );
__( 'Inline-flex', 'elementor' );
__( 'Display', 'elementor' );
__( 'Start', 'elementor' );
__( 'Center', 'elementor' );
__( 'End', 'elementor' );
__( 'Stretch', 'elementor' );
__( 'Align self', 'elementor' );
__( 'Start', 'elementor' );
__( 'Center', 'elementor' );
__( 'End', 'elementor' );
__( 'Stretch', 'elementor' );
__( 'Align items', 'elementor' );
__( 'Start', 'elementor' );
__( 'Center', 'elementor' );
__( 'End', 'elementor' );
__( 'Space between', 'elementor' );
__( 'Space around', 'elementor' );
__( 'Space evenly', 'elementor' );
__( 'Align content', 'elementor' );
__( 'Top', 'elementor' );
__( 'Left', 'elementor' );
__( 'Right', 'elementor' );
__( 'Bottom', 'elementor' );
__( 'Right', 'elementor' );
__( 'Left', 'elementor' );
__( 'Border width', 'elementor' );
__( 'Adjust borders', 'elementor' );
__( 'None', 'elementor' );
__( 'Solid', 'elementor' );
__( 'Dashed', 'elementor' );
__( 'Dotted', 'elementor' );
__( 'Double', 'elementor' );
__( 'Groove', 'elementor' );
__( 'Ridge', 'elementor' );
__( 'Inset', 'elementor' );
__( 'Outset', 'elementor' );
__( 'Border type', 'elementor' );
__( 'Top right', 'elementor' );
__( 'Top left', 'elementor' );
__( 'Top left', 'elementor' );
__( 'Top right', 'elementor' );
__( 'Bottom right', 'elementor' );
__( 'Bottom left', 'elementor' );
__( 'Bottom left', 'elementor' );
__( 'Bottom right', 'elementor' );
__( 'Border radius', 'elementor' );
__( 'Adjust corners', 'elementor' );
__( 'Border', 'elementor' );
__( 'Border color', 'elementor' );
__( 'System', 'elementor' );
__( 'Custom Fonts', 'elementor' );
__( 'Google Fonts', 'elementor' );