!function(){"use strict";var e={d:function(t,r){for(var n in r)e.o(r,n)&&!e.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:r[n]})},o:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r:function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{ELEMENTS_BASE_STYLES_PROVIDER_KEY:function(){return V},ELEMENTS_STYLES_PROVIDER_KEY_PREFIX:function(){return w},ELEMENTS_STYLES_RESERVED_LABEL:function(){return v},createStylesProvider:function(){return b},init:function(){return x},isElementsStylesProvider:function(){return R},stylesRepository:function(){return u},useGetStylesRepositoryCreateAction:function(){return p},useProviders:function(){return c},useUserStylesCapability:function(){return m},validateStyleLabel:function(){return C}});var r=window.React,n=window.elementorV2.editorCurrentUser,s=window.elementorV2.schema,i=window.wp.i18n,o=window.elementorV2.editorElements,a=window.elementorV2.editorV1Adapters,l=window.elementorV2.utils,u=(()=>{const e=[],t=()=>e.slice(0).sort(((e,t)=>e.priority>t.priority?-1:1));return{all:(e={})=>t().flatMap((t=>t.actions.all(e))),register:t=>{e.push(t)},subscribe:t=>{const r=e.map((e=>e.subscribe(t)));return()=>{r.forEach((e=>e()))}},getProviders:t,getProviderByKey:t=>e.find((e=>e.getKey()===t))}})();function c(){const[,e]=(0,r.useReducer)((e=>!e),!1);return(0,r.useEffect)((()=>u.subscribe(e)),[]),u.getProviders()}var d={create:!0,delete:!0,update:!0,updateProps:!0},m=()=>{const{capabilities:e}=(0,n.useCurrentUserCapabilities)();return{userCan:t=>{const r=u.getProviderByKey(t);return r?.capabilities?Object.entries(r.capabilities).reduce(((t,[r,n])=>({...t,[r]:e?.includes(n)??!0})),d):d}}};function p(){const{userCan:e}=m();return(0,r.useMemo)((()=>{const t=u.getProviders().map((t=>t.actions.create&&e(t.getKey()).create?[t,t.actions.create]:null)).filter(Boolean);if(1===t.length)return t[0];if(0===t.length)return null;throw new Error("Multiple providers with create action found in styles repository.")}),[])}var y=(0,l.createError)({code:"invalid_elements_style_provider_meta",message:"Invalid elements style provider meta."}),f=(0,l.createError)({code:"active_document_must_exist",message:"Active document must exist."}),g=1e4,_=10;function b({key:e,priority:t=_,limit:r=g,subscribe:n=()=>()=>{},labels:s,actions:i,capabilities:o}){return{getKey:"string"==typeof e?()=>e:e,priority:t,limit:r,capabilities:o,subscribe:n,labels:{singular:s?.singular??null,plural:s?.plural??null},actions:{all:i.all,get:i.get,resolveCssName:i.resolveCssName??(e=>e),create:i.create,delete:i.delete,update:i.update,updateProps:i.updateProps}}}var w="document-elements-",v="local",E=b({key:()=>{const e=(0,o.getCurrentDocumentId)();if(!e)throw new f;return`${w}${e}`},priority:50,subscribe:e=>(0,a.__privateListenTo)(o.styleRerenderEvents,e),actions:{all:(e={})=>{let t=(0,o.getElements)();return h(e)&&(t=t.filter((t=>t.id===e.elementId))),t.flatMap((e=>Object.values(e.model.get("styles")??{})))},get:(e,t={})=>{if(!h(t))throw new y({context:{meta:t}});return((0,o.getElementStyles)(t.elementId)??{})[e]??null},updateProps:(e,t={})=>{if(!h(t))throw new y({context:{meta:t}});(0,o.updateElementStyle)({elementId:t.elementId,styleId:e.id,meta:e.meta,props:e.props})}}});function h(e){return"elementId"in e&&"string"==typeof e.elementId&&!!e.elementId}var S=["container"],P=s.z.string().max(50,(0,i.__)("Class name is too long. Please keep it under 50 characters.","elementor")).regex(/^(|[^0-9].*)$/,(0,i.__)("Class names must start with a letter.","elementor")).regex(/^\S*$/,(0,i.__)("Class names can’t contain spaces.","elementor")).regex(/^(|[a-zA-Z0-9_-]+)$/,(0,i.__)("Class names can only use letters, numbers, dashes (-), and underscores (_).","elementor")).regex(/^(?!--).*/,(0,i.__)("Double hyphens are reserved for custom properties.","elementor")).regex(/^(?!-[0-9])/,(0,i.__)("Class names can’t start with a hyphen followed by a number.","elementor")).refine((e=>!S.includes(e)),{message:(0,i.__)("This name is reserved and can’t be used. Try something more specific.","elementor")});function C(e,t){const r=new Set([v,...u.all().map((e=>e.label.toLowerCase()))]),n=["create","rename"].includes(t),s=P.refine((e=>!(n&&e.length<2)),{message:(0,i.__)("Class name is too short. Use at least 2 characters.","elementor")}).refine((e=>!(n&&r.has(e))),{message:(0,i.__)("This class name already exists. Please choose a unique name.","elementor")}).safeParse(e.toLowerCase());return s.success?{isValid:!0,errorMessage:null}:{isValid:!1,errorMessage:s.error.format()._errors[0]}}function R(e){return new RegExp(`^${w}\\d+$`).test(e)}var V="element-base-styles",I=b({key:V,actions:{all(){const e=(0,o.getWidgetsCache)();return Object.values(e??{}).flatMap((e=>Object.values(e.base_styles??{})))},get(e){return this.all().find((t=>t.id===e))??null}}});function x(){u.register(E),u.register(I)}(window.elementorV2=window.elementorV2||{}).editorStylesRepository=t}(),window.elementorV2.editorStylesRepository?.init?.();