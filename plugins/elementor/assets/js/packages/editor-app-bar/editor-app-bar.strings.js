__( 'Exit to WordPress', 'elementor' );
__( 'Help', 'elementor' );
__( 'More', 'elementor' );
__( 'Elementor Logo', 'elementor' );
__( 'Integrations', 'elementor' );
__( 'User Preferences', 'elementor' );
__( 'Theme Builder', 'elementor' );
__( 'Structure', 'elementor' );
__( 'Site Settings', 'elementor' );
__( 'Save Changes', 'elementor' );
__( 'Switch Device', 'elementor' );
// translators: %s: Breakpoint label, %d: Breakpoint size.
__( '%s (%dpx and up)', 'elementor' );
// translators: %s: Breakpoint label, %d: Breakpoint size.
__( '%s (up to %dpx)', 'elementor' );
__( 'Keyboard Shortcuts', 'elementor' );
__( 'History', 'elementor' );
__( 'Finder', 'elementor' );
__( 'Elements', 'elementor' );
__( 'Widgets', 'elementor' );
__( 'Add Element', 'elementor' );
__( 'View Page', 'elementor' );
__( 'Save as Template', 'elementor' );
__( 'Save Draft', 'elementor' );
__( 'Copy and Share', 'elementor' );
__( 'Save Options', 'elementor' );
__( 'Save Options', 'elementor' );
__( 'Publish', 'elementor' );
__( 'Submit', 'elementor' );
__( 'Preview Changes', 'elementor' );
/* translators: %s: Post type label. */
__( '%s Settings', 'elementor' );