!function(){"use strict";var e={d:function(t,n){for(var o in n)e.o(n,o)&&!e.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:n[o]})},o:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r:function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{documentOptionsMenu:function(){return R},init:function(){return Ae},injectIntoPageIndication:function(){return I},injectIntoPrimaryAction:function(){return S},injectIntoResponsive:function(){return B},integrationsMenu:function(){return D},mainMenu:function(){return T},toolsMenu:function(){return A},utilitiesMenu:function(){return L}});var n=window.elementorV2.locations,o=window.elementorV2.menus,r=window.React,i=window.elementorV2.icons,l=window.elementorV2.ui,a=window.elementorV2.editor,c=window.elementorV2.editorDocuments,s=window.wp.i18n,u=window.elementorV2.editorV1Adapters,p=window.elementorV2.editorResponsive,m=(0,r.createContext)({type:"toolbar"});function d({type:e,popupState:t,children:n}){return r.createElement(m.Provider,{value:{type:e,popupState:t}},n)}function g(){return(0,r.useContext)(m)}var v=(0,l.withDirection)(i.ArrowUpRightIcon),E=(0,l.withDirection)(i.ChevronRightIcon);function f({text:e,icon:t,onClick:n,href:o,target:i,disabled:a,isGroupParent:c,...s}){const u=o&&"_blank"===i;return r.createElement(l.MenuItem,{...s,disabled:a,onClick:n,component:o?"a":"div",href:o,target:i,sx:{"&:hover":{color:"text.primary"}}},r.createElement(l.ListItemIcon,null,t),r.createElement(l.ListItemText,{primary:e}),u&&r.createElement(v,null),c&&r.createElement(E,null))}function _({title:e,...t}){return r.createElement(y,{title:e},r.createElement(l.Box,{component:"span","aria-label":void 0},r.createElement(l.IconButton,{...t,"aria-label":e,size:"medium",sx:{"& svg":{fontSize:"1.25rem",height:"1em",width:"1em"},"&:hover":{color:"text.primary"}}})))}function y(e){return r.createElement(l.Tooltip,{PopperProps:{sx:{"&.MuiTooltip-popper .MuiTooltip-tooltip.MuiTooltip-tooltipPlacementBottom":{mt:2}}},...e})}function h({icon:e,title:t,visible:n=!0,...o}){const{type:i}=g();return n?"toolbar"===i?r.createElement(_,{title:t,...o},r.createElement(e,null)):r.createElement(f,{...o,text:t,icon:r.createElement(e,null)}):null}function b({icon:e,title:t,visible:n=!0,...o}){const{type:i}=g();return n?"toolbar"===i?r.createElement(_,{title:t,...o},r.createElement(e,null)):r.createElement(f,{...o,text:t,icon:r.createElement(e,null)}):null}function w({title:e,onClick:t,...n}){return r.createElement(l.Tooltip,{title:e},r.createElement(l.Box,{component:"span","aria-label":void 0},r.createElement(l.ToggleButton,{...n,onChange:t,"aria-label":e,size:"small",sx:{border:0,"&.Mui-disabled":{border:0},"& svg":{fontSize:"1.25rem",height:"1em",width:"1em"}}})))}function k({icon:e,title:t,value:n,visible:o=!0,...i}){const{type:l}=g();return o?"toolbar"===l?r.createElement(w,{value:n||t,title:t,...i},r.createElement(e,null)):r.createElement(f,{...i,text:t,icon:r.createElement(e,null)}):null}var{inject:I,Slot:M}=(0,n.createLocation)(),{inject:B,Slot:C}=(0,n.createLocation)(),{inject:S,Slot:P}=(0,n.createLocation)(),x={Action:h,ToggleAction:k,Link:b},T=(0,o.createMenu)({groups:["exits"],components:x}),A=(0,o.createMenu)({components:x}),L=(0,o.createMenu)({components:x}),D=(0,o.createMenu)({components:x}),R=(0,o.createMenu)({groups:["save"],components:{Action:h,ToggleAction:k,Link:b}});function V({children:e,popupState:t,...n}){return r.createElement(d,{type:"popover",popupState:t},r.createElement(l.Menu,{PaperProps:{sx:{mt:1.5}},...n,MenuListProps:{component:"div",dense:!0}},e))}var O=(0,l.styled)(l.ToggleButton)((({theme:e})=>({padding:0,border:0,color:e.palette.text.primary,"&.MuiToggleButton-root:hover":{backgroundColor:"initial"},"&.MuiToggleButton-root.Mui-selected":{backgroundColor:"initial"}}))),j=(0,l.styled)((e=>r.createElement(l.SvgIcon,{viewBox:"0 0 32 32",...e},r.createElement("g",null,r.createElement("circle",{cx:"16",cy:"16",r:"16"}),r.createElement("path",{d:"M11.7 9H9V22.3H11.7V9Z"}),r.createElement("path",{d:"M22.4 9H9V11.7H22.4V9Z"}),r.createElement("path",{d:"M22.4 14.4004H9V17.1004H22.4V14.4004Z"}),r.createElement("path",{d:"M22.4 19.6992H9V22.3992H22.4V19.6992Z"})))),{shouldForwardProp:e=>"showMenuIcon"!==e})((({theme:e,showMenuIcon:t})=>({"& path":{fill:e.palette.background.default,transition:"all 0.2s linear",transformOrigin:"bottom left","&:first-of-type":{transitionDelay:!t&&"0.2s",transform:t&&"translateY(-9px) scaleY(0)"},"&:not(:first-of-type)":{transform:!t&&`translateX(${"rtl"===e.direction?"4":"9"}px) scaleX(0.6)`},"&:nth-of-type(2)":{transitionDelay:t?"0":"0.2s"},"&:nth-of-type(3)":{transitionDelay:"0.1s"},"&:nth-of-type(4)":{transitionDelay:t?"0.2s":"0"}}})));function z(e){const[t,n]=(0,r.useState)(!1),o=e.selected||t;return r.createElement(O,{...e,value:"selected",size:"large",onMouseEnter:()=>n(!0),onMouseLeave:()=>n(!1)},r.createElement(j,{fontSize:"large",showMenuIcon:o,titleAccess:(0,s.__)("Elementor Logo","elementor")}))}var{useMenuItems:H}=T;function W(){const e=H(),t=(0,l.usePopupState)({variant:"popover",popupId:"elementor-v2-app-bar-main-menu"}),n=(0,l.bindTrigger)(t);return r.createElement(l.Stack,{sx:{paddingInlineStart:3},direction:"row",alignItems:"center"},r.createElement(z,{...n,onClick:e=>{const t=window,o=t?.elementor?.editorEvents?.config;o&&t.elementor.editorEvents.dispatchEvent(o.names.topBar.elementorLogoDropdown,{location:o.locations.topBar,secondaryLocation:o.secondaryLocations.elementorLogo,trigger:o.triggers.dropdownClick,element:o.elements.buttonIcon}),n.onClick(e)},selected:t.isOpen}),r.createElement(V,{onClick:t.close,...(0,l.bindMenu)(t),marginThreshold:8},e.default.map((({MenuItem:e,id:t})=>r.createElement(e,{key:t}))),e.exits.length>0&&r.createElement(l.Divider,null),e.exits.map((({MenuItem:e,id:t})=>r.createElement(e,{key:t})))))}function U(){return r.createElement(M,null)}function F(){return r.createElement(P,null)}function G(){return r.createElement(C,null)}function Z({children:e,...t}){return r.createElement(d,{type:"toolbar"},r.createElement(l.Stack,{sx:{px:1.5},spacing:1.5,direction:"row",alignItems:"center",...t},e))}function $({children:e,id:t}){const n=(0,l.usePopupState)({variant:"popover",popupId:t});return r.createElement(r.Fragment,null,r.createElement(_,{...(0,l.bindTrigger)(n),title:(0,s.__)("More","elementor")},r.createElement(i.DotsVerticalIcon,null)),r.createElement(V,{onClick:n.close,...(0,l.bindMenu)(n)},e))}var{useMenuItems:K}=D;function q(){const e=K(),t=(0,l.usePopupState)({variant:"popover",popupId:"elementor-v2-app-bar-integrations"});return 0===e.default.length?null:r.createElement(r.Fragment,null,r.createElement(_,{...(0,l.bindTrigger)(t),title:(0,s.__)("Integrations","elementor")},r.createElement(i.PlugIcon,null)),r.createElement(V,{onClick:t.close,...(0,l.bindMenu)(t),marginThreshold:8,open:t.isOpen},e.default.map((({MenuItem:e,id:t})=>r.createElement(e,{key:t})))))}var X=5,{useMenuItems:Y}=A;function J(){const e=Y(),t=e.default.slice(0,X),n=e.default.slice(X);return r.createElement(Z,null,t.map((({MenuItem:e,id:t})=>r.createElement(e,{key:t}))),r.createElement(q,null),n.length>0&&r.createElement($,{id:"elementor-editor-app-bar-tools-more"},n.map((({MenuItem:e,id:t})=>r.createElement(e,{key:t})))))}var N=4,{useMenuItems:Q}=L;function ee(){const e=Q(),t=e.default.length>N+1,n=t?e.default.slice(0,N):e.default,o=t?e.default.slice(N):[];return r.createElement(Z,null,n.map((({MenuItem:e,id:t},n)=>r.createElement(r.Fragment,{key:t},0===n&&r.createElement(l.Divider,{orientation:"vertical"}),r.createElement(e,null)))),o.length>0&&r.createElement($,{id:"elementor-editor-app-bar-utilities-more"},o.map((({MenuItem:e,id:t})=>r.createElement(e,{key:t})))))}function te(){const e=(0,c.__useActiveDocument)();return r.createElement(l.ThemeProvider,{colorScheme:"dark"},r.createElement(l.AppBar,{position:"sticky"},r.createElement(l.Toolbar,{disableGutters:!0,variant:"dense"},r.createElement(l.Box,{display:"grid",gridTemplateColumns:"repeat(3, 1fr)",flexGrow:1},r.createElement(l.Grid,{container:!0,flexWrap:"nowrap"},r.createElement(W,null),e?.permissions?.allowAddingWidgets&&r.createElement(J,null)),r.createElement(l.Grid,{container:!0,justifyContent:"center"},r.createElement(Z,{spacing:1.5},r.createElement(l.Divider,{orientation:"vertical"}),r.createElement(U,null),r.createElement(l.Divider,{orientation:"vertical"}),r.createElement(G,null),r.createElement(l.Divider,{orientation:"vertical"}))),r.createElement(l.Grid,{container:!0,justifyContent:"flex-end",flexWrap:"nowrap"},r.createElement(ee,null),r.createElement(F,null))))))}function ne(){const e=(0,c.__useActiveDocument)(),t=(0,c.__useHostDocument)(),n=e&&"kit"!==e.type.value?e:t,{isActive:o,isBlocked:a}=(0,u.__privateUseRouteStatus)("panel/page-settings");if(!n)return null;const p=(0,s.__)("%s Settings","elementor").replace("%s",n.type.label);return r.createElement(oe,{title:p},r.createElement(l.Box,{component:"span","aria-label":void 0},r.createElement(l.ToggleButton,{value:"document-settings",selected:o,disabled:a,onChange:()=>{const e=window,t=e?.elementor?.editorEvents?.config;t&&e.elementor.editorEvents.dispatchEvent(t.names.topBar.documentSettings,{location:t.locations.topBar,secondaryLocation:t.secondaryLocations["document-settings"],trigger:t.triggers.click,element:t.elements.buttonIcon}),(0,u.__privateOpenRoute)("panel/page-settings/settings")},"aria-label":p,size:"small",sx:{border:0,"&.Mui-disabled":{border:0}}},r.createElement(i.SettingsIcon,{fontSize:"small"}))))}function oe(e){return r.createElement(l.Tooltip,{PopperProps:{sx:{"&.MuiTooltip-popper .MuiTooltip-tooltip.MuiTooltip-tooltipPlacementBottom":{mt:1.7}}},...e})}function re(){const e=(0,c.__useActiveDocument)();return{icon:i.EyeIcon,title:(0,s.__)("Preview Changes","elementor"),onClick:()=>{const t=window,n=t?.elementor?.editorEvents?.config;n&&t.elementor.editorEvents.dispatchEvent(n.names.topBar.previewPage,{location:n.locations.topBar,secondaryLocation:n.secondaryLocations["preview-page"],trigger:n.triggers.click,element:n.elements.buttonIcon}),e&&(0,u.__privateRunCommand)("editor/documents/preview",{id:e.id,force:!0})}}}var{useMenuItems:ie}=R,le=(0,l.styled)(V)`
	& > .MuiPopover-paper > .MuiList-root {
		& > .MuiDivider-root {
			display: none;
		}

		& > *:not( .MuiDivider-root ):not( :last-of-type ) + .MuiDivider-root {
			display: block;
		}
	}
`;function ae(e){const{save:t,default:n}=ie();return r.createElement(le,{...e,anchorOrigin:{vertical:"bottom",horizontal:"right"},transformOrigin:{vertical:"top",horizontal:"right"},marginThreshold:4,PaperProps:{sx:{mt:.5}}},t.map((({MenuItem:e,id:t},n)=>[n>0&&r.createElement(l.Divider,{key:`${t}-divider`}),r.createElement(e,{key:t})])),t.length>0&&n.length>0&&r.createElement(l.Divider,null),n.map((({MenuItem:e,id:t},n)=>[n>0&&r.createElement(l.Divider,{key:`${t}-divider`}),r.createElement(e,{key:t})])))}function ce(){const e=(0,c.__useActiveDocument)(),{save:t}=(0,c.__useActiveDocumentActions)(),n="edit"===(0,u.useEditMode)(),o=(0,l.usePopupState)({variant:"popover",popupId:"document-save-options"});if(!e)return null;const a=!n||!function(e){return"kit"!==e.type.value&&(e.isDirty||"draft"===e.status.value)}(e),p=!n||"kit"===e.type.value,m=e.isSaving&&!a;return r.createElement(r.Fragment,null,r.createElement(l.ButtonGroup,{size:"large",variant:"contained"},r.createElement(l.Button,{onClick:()=>{const n=window,o=n?.elementor?.editorEvents?.config;o&&n.elementor.editorEvents.dispatchEvent(o.names.topBar.publishButton,{location:o.locations.topBar,secondaryLocation:o.secondaryLocations["publish-button"],trigger:o.triggers.click,element:o.elements.mainCta}),e.isSaving||t()},sx:{height:"100%",borderRadius:0,maxWidth:"158px","&.MuiButtonBase-root.MuiButtonGroup-grouped":{minWidth:"110px"}},disabled:a},m?r.createElement(l.CircularProgress,{color:"inherit",size:"1.5em"}):function(e){return e.userCan.publish?(0,s.__)("Publish","elementor"):(0,s.__)("Submit","elementor")}(e)),r.createElement(l.Tooltip,{title:(0,s.__)("Save Options","elementor"),PopperProps:{sx:{"&.MuiTooltip-popper .MuiTooltip-tooltip.MuiTooltip-tooltipPlacementBottom":{mt:1,mr:.25}}}},r.createElement(l.Box,{component:"span","aria-label":void 0},r.createElement(l.Button,{size:"small",...(0,l.bindTrigger)(o),sx:{px:0,height:"100%",borderRadius:0},disabled:p,"aria-label":(0,s.__)("Save Options","elementor")},r.createElement(i.ChevronDownIcon,null))))),r.createElement(ae,{...(0,l.bindMenu)(o),onClick:o.close}))}function se(){const e=(0,c.__useActiveDocument)(),{copyAndShare:t}=(0,c.__useActiveDocumentActions)();return{icon:i.LinkIcon,title:(0,s.__)("Copy and Share","elementor"),onClick:t,disabled:!e||e.isSaving||e.isSavingDraft||!("publish"===e.status.value),visible:e?.permissions?.showCopyAndShare}}function ue(){const e=(0,c.__useActiveDocument)(),{saveDraft:t}=(0,c.__useActiveDocumentActions)();return{icon:i.FileReportIcon,title:(0,s.__)("Save Draft","elementor"),onClick:t,disabled:!e||e.isSaving||e.isSavingDraft||!e.isDirty}}function pe(){const{saveTemplate:e}=(0,c.__useActiveDocumentActions)();return{icon:i.FolderIcon,title:(0,s.__)("Save as Template","elementor"),onClick:e}}function me(){const e=(0,c.__useActiveDocument)();return{icon:i.EyeIcon,title:(0,s.__)("View Page","elementor"),onClick:()=>e?.id&&(0,u.__privateRunCommand)("editor/documents/view",{id:e.id})}}function de(){const{isActive:e,isBlocked:t}=(0,u.__privateUseRouteStatus)("panel/elements");return{title:(0,s.__)("Add Element","elementor"),icon:i.PlusIcon,onClick:()=>{const e=window,t=e?.elementor?.editorEvents?.config;t&&e.elementor.editorEvents.dispatchEvent(t.names.topBar.widgetPanel,{location:t.locations.topBar,secondaryLocation:t.secondaryLocations["widget-panel"],trigger:t.triggers.toggleClick,element:t.elements.buttonIcon}),(0,u.__privateOpenRoute)("panel/elements/categories")},selected:e,disabled:t}}function ge(e){window.elementor?.getPanelView?.()?.getHeaderView?.()?.setTitle?.(e)}function ve(e){const t=document.querySelector('.elementor-component-tab[data-tab="categories"]');t&&(t.textContent=e)}function Ee(){return{title:(0,s.__)("Finder","elementor"),icon:i.SearchIcon,onClick:()=>{const e=window,t=e?.elementor?.editorEvents?.config;t&&e.elementor.editorEvents.dispatchEvent(t.names.topBar.finder,{location:t.locations.topBar,secondaryLocation:t.secondaryLocations.finder,trigger:t.triggers.toggleClick,element:t.elements.buttonIcon}),(0,u.__privateRunCommand)("finder/toggle")}}}function fe(){const{isActive:e,isBlocked:t}=(0,u.__privateUseRouteStatus)("panel/history");return{title:(0,s.__)("History","elementor"),icon:i.HistoryIcon,onClick:()=>{const e=window,t=e?.elementor?.editorEvents?.config;t&&e.elementor.editorEvents.dispatchEvent(t.names.topBar.history,{location:t.locations.topBar,secondaryLocation:t.secondaryLocations.elementorLogo,trigger:t.triggers.click,element:t.elements.link}),(0,u.__privateOpenRoute)("panel/history/actions")},selected:e,disabled:t}}function _e(){return{icon:i.KeyboardIcon,title:(0,s.__)("Keyboard Shortcuts","elementor"),onClick:()=>{const e=window,t=e?.elementor?.editorEvents?.config;t&&e.elementor.editorEvents.dispatchEvent(t.names.topBar.keyboardShortcuts,{location:t.locations.topBar,secondaryLocation:t.secondaryLocations.elementorLogo,trigger:t.triggers.click,element:t.elements.link}),(0,u.__privateRunCommand)("shortcuts/open")}}}function ye(){const e=(0,p.useBreakpoints)(),t=(0,p.useActiveBreakpoint)(),n=(0,p.useActivateBreakpoint)();return e.length&&t?r.createElement(l.Tabs,{textColor:"inherit",indicatorColor:"secondary",value:t,onChange:(e,t)=>{const o=window,r=o?.elementor?.editorEvents?.config;r&&o.elementor.editorEvents.dispatchEvent(r.names.topBar.responsiveControls,{location:r.locations.topBar,secondaryLocation:r.secondaryLocations.responsiveControls,trigger:r.triggers.click,element:r.elements.buttonIcon,mode:t}),n(t)},"aria-label":(0,s.__)("Switch Device","elementor"),sx:{"& .MuiTabs-indicator":{backgroundColor:"text.primary"}}},e.map((({id:e,label:t,type:n,width:o})=>{const i=be[e],a=we[n||"default"].replace("%s",t).replace("%d",o?.toString()||"");return r.createElement(l.Tab,{value:e,key:e,"aria-label":a,icon:r.createElement(he,{title:a},r.createElement(i,null)),sx:{minWidth:"auto"},"data-testid":`switch-device-to-${e}`})}))):null}function he(e){return r.createElement(l.Tooltip,{PopperProps:{sx:{"&.MuiTooltip-popper .MuiTooltip-tooltip.MuiTooltip-tooltipPlacementBottom":{mt:2.5}}},...e})}var be={widescreen:i.WidescreenIcon,desktop:i.DesktopIcon,laptop:i.LaptopIcon,tablet_extra:i.TabletLandscapeIcon,tablet:i.TabletPortraitIcon,mobile_extra:i.MobileLandscapeIcon,mobile:i.MobilePortraitIcon},we={default:"%s","min-width":(0,s.__)("%s (%dpx and up)","elementor"),"max-width":(0,s.__)("%s (up to %dpx)","elementor")};function ke(e){const t=(0,u.__privateUseListenTo)([(0,u.routeOpenEvent)("panel/global"),(0,u.routeCloseEvent)("panel/global")],Ie);return t.current?r.createElement(l.Portal,{container:t.current,...e}):null}function Ie(){return(0,u.__privateIsRouteActive)("panel/global")?{current:document.querySelector("#elementor-panel-inner")}:{current:null}}function Me(){const e=(0,c.__useActiveDocument)(),{save:t}=(0,c.__useActiveDocumentActions)();return r.createElement(l.Paper,{sx:{px:5,py:4,borderTop:1,borderColor:"divider"}},r.createElement(l.Button,{variant:"contained",disabled:!e||!e.isDirty,size:"medium",sx:{width:"100%"},onClick:()=>e&&!e.isSaving?t():null},e?.isSaving?r.createElement(l.CircularProgress,null):(0,s.__)("Save Changes","elementor")))}function Be(){return r.createElement(ke,null,r.createElement(Me,null))}function Ce(){const{isActive:e,isBlocked:t}=(0,u.__privateUseRouteStatus)("panel/global",{blockOnKitRoutes:!1});return{title:(0,s.__)("Site Settings","elementor"),icon:i.AdjustmentsHorizontalIcon,onClick:()=>{const t=window,n=t?.elementor?.editorEvents?.config;n&&t.elementor.editorEvents.dispatchEvent(n.names.topBar.siteSettings,{location:n.locations.topBar,secondaryLocation:n.secondaryLocations.siteSettings,trigger:n.triggers.toggleClick,element:n.elements.buttonIcon}),e?(0,u.__privateRunCommand)("panel/global/close"):(0,u.__privateRunCommand)("panel/global/open")},selected:e,disabled:t}}function Se(){const{isActive:e,isBlocked:t}=(0,u.__privateUseRouteStatus)("navigator");return{title:(0,s.__)("Structure","elementor"),icon:i.StructureIcon,onClick:()=>{const e=window,t=e?.elementor?.editorEvents?.config;t&&e.elementor.editorEvents.dispatchEvent(t.names.topBar.structure,{location:t.locations.topBar,secondaryLocation:t.secondaryLocations.structure,trigger:t.triggers.toggleClick,element:t.elements.buttonIcon}),(0,u.__privateRunCommand)("navigator/toggle")},selected:e,disabled:t}}function Pe(){return{icon:i.ThemeBuilderIcon,title:(0,s.__)("Theme Builder","elementor"),onClick:()=>{const e=window,t=e?.elementor?.editorEvents?.config;t&&e.elementor.editorEvents.dispatchEvent(t.names.topBar.themeBuilder,{location:t.locations.topBar,secondaryLocation:t.secondaryLocations.elementorLogo,trigger:t.triggers.click,element:t.elements.link}),(0,u.__privateRunCommand)("app/open")}}}function xe(){const{isActive:e,isBlocked:t}=(0,u.__privateUseRouteStatus)("panel/editor-preferences");return{icon:i.ToggleRightIcon,title:(0,s.__)("User Preferences","elementor"),onClick:()=>{const e=window,t=e?.elementor?.editorEvents?.config;t&&e.elementor.editorEvents.dispatchEvent(t.names.topBar.userPreferences,{location:t.locations.topBar,secondaryLocation:t.secondaryLocations.elementorLogo,trigger:t.triggers.click,element:t.elements.link}),(0,u.__privateOpenRoute)("panel/editor-preferences")},selected:e,disabled:t}}function Te(){I({id:"document-settings-button",component:ne,options:{priority:20}}),L.registerAction({id:"document-preview-button",priority:30,useProps:re}),S({id:"document-primary-action",component:ce}),R.registerAction({group:"save",id:"document-save-draft",priority:10,useProps:ue}),R.registerAction({group:"save",id:"document-save-as-template",priority:20,useProps:pe}),R.registerAction({id:"document-copy-and-share",priority:10,useProps:se}),R.registerAction({id:"document-view-page",priority:50,useProps:me}),function(){const e=(0,s.__)("Elements","elementor"),t=(0,s.__)("Widgets","elementor");(0,u.__privateListenTo)((0,u.routeOpenEvent)("panel/elements"),(()=>{ge(e),ve(t)})),(0,u.__privateListenTo)((0,u.v1ReadyEvent)(),(()=>{(0,u.__privateIsRouteActive)("panel/elements")&&(ge(e),ve(t))}))}(),A.registerToggleAction({id:"open-elements-panel",priority:1,useProps:de}),L.registerAction({id:"toggle-finder",priority:15,useProps:Ee}),L.registerLink({id:"open-help-center",priority:25,useProps:()=>({title:(0,s.__)("Help","elementor"),href:"https://go.elementor.com/editor-top-bar-learn/",icon:i.HelpIcon,target:"_blank",onClick:()=>{const e=window,t=e?.elementor?.editorEvents?.config;t&&e.elementor.editorEvents.dispatchEvent(t.names.topBar.help,{location:t.locations.topBar,secondaryLocation:t.secondaryLocations.help,trigger:t.triggers.click,element:t.elements.buttonIcon})}})}),T.registerToggleAction({id:"open-history",priority:20,useProps:fe}),T.registerAction({id:"open-keyboard-shortcuts",group:"default",priority:40,useProps:_e}),B({id:"responsive-breakpoints-switcher",component:ye,options:{priority:20}}),(0,a.injectIntoTop)({id:"site-settings-primary-action-portal",component:Be}),A.registerToggleAction({id:"toggle-site-settings",priority:2,useProps:Ce}),A.registerToggleAction({id:"toggle-structure-view",priority:3,useProps:Se}),T.registerAction({id:"open-theme-builder",useProps:Pe}),T.registerToggleAction({id:"open-user-preferences",priority:30,useProps:xe}),T.registerLink({id:"exit-to-wordpress",group:"exits",useProps:()=>{const e=(0,c.__useActiveDocument)();return{title:(0,s.__)("Exit to WordPress","elementor"),href:e?.links?.platformEdit,icon:i.WordpressIcon,onClick:()=>{const e=window,t=e?.elementor?.editorEvents?.config;t&&e.elementor.editorEvents.dispatchEvent(t.names.topBar.exitToWordpress,{location:t.locations.topBar,secondaryLocation:t.secondaryLocations.elementorLogo,trigger:t.triggers.click,element:t.elements.link})}}}})}function Ae(){(0,u.__privateListenTo)((0,u.routeOpenEvent)("panel/menu"),(()=>{(0,u.__privateOpenRoute)("panel/elements/categories")})),Te(),(0,a.injectIntoTop)({id:"app-bar",component:te})}(window.elementorV2=window.elementorV2||{}).editorAppBar=t}(),window.elementorV2.editorAppBar?.init?.();