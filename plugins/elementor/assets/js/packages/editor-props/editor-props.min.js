!function(){"use strict";var e={d:function(r,t){for(var n in t)e.o(t,n)&&!e.o(r,n)&&Object.defineProperty(r,n,{enumerable:!0,get:t[n]})},o:function(e,r){return Object.prototype.hasOwnProperty.call(e,r)},r:function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},r={};e.r(r),e.d(r,{CLASSES_PROP_KEY:function(){return c},backgroundColorOverlayPropTypeUtil:function(){return j},backgroundGradientOverlayPropTypeUtil:function(){return k},backgroundImageOverlayPropTypeUtil:function(){return h},backgroundImagePositionOffsetPropTypeUtil:function(){return $},backgroundImageSizeScalePropTypeUtil:function(){return E},backgroundOverlayPropTypeUtil:function(){return S},backgroundPropTypeUtil:function(){return U},booleanPropTypeUtil:function(){return A},borderRadiusPropTypeUtil:function(){return u},borderWidthPropTypeUtil:function(){return l},boxShadowPropTypeUtil:function(){return a},classesPropTypeUtil:function(){return s},colorPropTypeUtil:function(){return p},colorStopPropTypeUtil:function(){return V},createPropUtils:function(){return n},dimensionsPropTypeUtil:function(){return y},filterEmptyValues:function(){return M},gradientColorStopPropTypeUtil:function(){return x},imageAttachmentIdPropType:function(){return d},imagePropTypeUtil:function(){return b},imageSrcPropTypeUtil:function(){return f},isEmpty:function(){return R},isTransformable:function(){return I},layoutDirectionPropTypeUtil:function(){return T},linkPropTypeUtil:function(){return v},mergeProps:function(){return C},numberPropTypeUtil:function(){return m},shadowPropTypeUtil:function(){return i},sizePropTypeUtil:function(){return g},stringPropTypeUtil:function(){return z},strokePropTypeUtil:function(){return P},urlPropTypeUtil:function(){return O}});var t=window.elementorV2.schema;function n(e,r){const n=t.z.strictObject({$$type:t.z.literal(e),value:r,disabled:t.z.boolean().optional()});function o(e){return n.safeParse(e).success}return{extract:function(e){return o(e)?e.value:null},isValid:o,create:function(r,t){const n="function"==typeof r?r:()=>r,{base:i,disabled:a}=t||{};if(!i)return{$$type:e,value:n(),...a&&{disabled:a}};if(!o(i))throw new Error(`Cannot create prop based on invalid value: ${JSON.stringify(i)}`);return{$$type:e,value:n(i.value),...a&&{disabled:a}}},schema:n,key:e}}var o=t.z.any().nullable(),i=n("shadow",t.z.strictObject({position:o,hOffset:o,vOffset:o,blur:o,spread:o,color:o})),a=n("box-shadow",t.z.array(i.schema)),u=n("border-radius",t.z.strictObject({"start-start":o,"start-end":o,"end-start":o,"end-end":o})),l=n("border-width",t.z.strictObject({"block-start":o,"block-end":o,"inline-start":o,"inline-end":o})),c="classes",s=n(c,t.z.array(t.z.string().regex(/^[a-z][a-z-_0-9]*$/i))),p=n("color",t.z.string()),b=n("image",t.z.strictObject({src:o,size:o})),d=n("image-attachment-id",t.z.number()),f=n("image-src",t.z.strictObject({id:o,url:t.z.null()}).or(t.z.strictObject({id:t.z.null(),url:o}))),y=n("dimensions",t.z.strictObject({"block-start":o,"block-end":o,"inline-start":o,"inline-end":o})),m=n("number",t.z.number().nullable()),g=n("size",t.z.strictObject({unit:t.z.enum(["px","em","rem","%","vw","vh"]),size:t.z.number()})),z=n("string",t.z.string().nullable()),P=n("stroke",t.z.strictObject({color:o,width:o})),O=n("url",t.z.string().nullable()),T=n("layout-direction",t.z.object({row:t.z.any(),column:t.z.any()})),v=n("link",t.z.strictObject({destination:o,label:o,isTargetBlank:o})),U=n("background",t.z.strictObject({color:o,"background-overlay":o})),j=n("background-color-overlay",o),k=n("background-gradient-overlay",o),h=n("background-image-overlay",o),w=j.schema.or(k.schema).or(h.schema),S=n("background-overlay",t.z.array(w)),$=n("background-image-position-offset",o),E=n("background-image-size-scale",o),A=n("boolean",t.z.boolean().nullable()),V=n("color-stop",t.z.strictObject({color:o,offset:o})),x=n("gradient-color-stop",t.z.array(V.schema));function C(e,r){const t=structuredClone(e);return Object.entries(r).forEach((([e,r])=>{null==r?delete t[e]:t[e]=r})),t}var _=t.z.object({$$type:t.z.string(),value:t.z.any(),disabled:t.z.boolean().optional()}),I=e=>_.safeParse(e).success,M=e=>R(e)?null:Array.isArray(e)?e.map(M).filter((e=>!R(e))):"object"==typeof e?Object.fromEntries(Object.entries(e).map((([e,r])=>[e,M(r)])).filter((([,e])=>!R(e)))):e,R=e=>e&&I(e)?R(e.value):B(e)||D(e)||G(e),B=e=>null==e||""===e,D=e=>Array.isArray(e)&&e.every(R),G=e=>"object"==typeof e&&D(Object.values(e));(window.elementorV2=window.elementorV2||{}).editorProps=r}(),window.elementorV2.editorProps?.init?.();