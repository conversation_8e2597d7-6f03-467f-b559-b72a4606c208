/*! For license information please see editor-props.js.LICENSE.txt */
!function(){"use strict";var e={"@elementor/schema":function(e){e.exports=window.elementorV2.schema}},r={};function t(n){var o=r[n];if(void 0!==o)return o.exports;var i=r[n]={exports:{}};return e[n](i,i.exports,t),i.exports}t.d=function(e,r){for(var n in r)t.o(r,n)&&!t.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:r[n]})},t.o=function(e,r){return Object.prototype.hasOwnProperty.call(e,r)},t.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};!function(){t.r(n),t.d(n,{CLASSES_PROP_KEY:function(){return l},backgroundColorOverlayPropTypeUtil:function(){return j},backgroundGradientOverlayPropTypeUtil:function(){return k},backgroundImageOverlayPropTypeUtil:function(){return h},backgroundImagePositionOffsetPropTypeUtil:function(){return x},backgroundImageSizeScalePropTypeUtil:function(){return $},backgroundOverlayPropTypeUtil:function(){return S},backgroundPropTypeUtil:function(){return U},booleanPropTypeUtil:function(){return E},borderRadiusPropTypeUtil:function(){return u},borderWidthPropTypeUtil:function(){return c},boxShadowPropTypeUtil:function(){return a},classesPropTypeUtil:function(){return s},colorPropTypeUtil:function(){return p},colorStopPropTypeUtil:function(){return A},createPropUtils:function(){return r},dimensionsPropTypeUtil:function(){return y},filterEmptyValues:function(){return M},gradientColorStopPropTypeUtil:function(){return V},imageAttachmentIdPropType:function(){return b},imagePropTypeUtil:function(){return d},imageSrcPropTypeUtil:function(){return f},isEmpty:function(){return R},isTransformable:function(){return I},layoutDirectionPropTypeUtil:function(){return O},linkPropTypeUtil:function(){return T},mergeProps:function(){return C},numberPropTypeUtil:function(){return m},shadowPropTypeUtil:function(){return i},sizePropTypeUtil:function(){return g},stringPropTypeUtil:function(){return z},strokePropTypeUtil:function(){return P},urlPropTypeUtil:function(){return v}});var e=t("@elementor/schema");function r(r,t){const n=e.z.strictObject({$$type:e.z.literal(r),value:t,disabled:e.z.boolean().optional()});function o(e){return n.safeParse(e).success}return{extract:function(e){return o(e)?e.value:null},isValid:o,create:function(e,t){const n="function"==typeof e?e:()=>e,{base:i,disabled:a}=t||{};if(!i)return{$$type:r,value:n(),...a&&{disabled:a}};if(!o(i))throw new Error(`Cannot create prop based on invalid value: ${JSON.stringify(i)}`);return{$$type:r,value:n(i.value),...a&&{disabled:a}}},schema:n,key:r}}var o=e.z.any().nullable(),i=r("shadow",e.z.strictObject({position:o,hOffset:o,vOffset:o,blur:o,spread:o,color:o})),a=r("box-shadow",e.z.array(i.schema)),u=r("border-radius",e.z.strictObject({"start-start":o,"start-end":o,"end-start":o,"end-end":o})),c=r("border-width",e.z.strictObject({"block-start":o,"block-end":o,"inline-start":o,"inline-end":o})),l="classes",s=r(l,e.z.array(e.z.string().regex(/^[a-z][a-z-_0-9]*$/i))),p=r("color",e.z.string()),d=r("image",e.z.strictObject({src:o,size:o})),b=r("image-attachment-id",e.z.number()),f=r("image-src",e.z.strictObject({id:o,url:e.z.null()}).or(e.z.strictObject({id:e.z.null(),url:o}))),y=r("dimensions",e.z.strictObject({"block-start":o,"block-end":o,"inline-start":o,"inline-end":o})),m=r("number",e.z.number().nullable()),g=r("size",e.z.strictObject({unit:e.z.enum(["px","em","rem","%","vw","vh"]),size:e.z.number()})),z=r("string",e.z.string().nullable()),P=r("stroke",e.z.strictObject({color:o,width:o})),v=r("url",e.z.string().nullable()),O=r("layout-direction",e.z.object({row:e.z.any(),column:e.z.any()})),T=r("link",e.z.strictObject({destination:o,label:o,isTargetBlank:o})),U=r("background",e.z.strictObject({color:o,"background-overlay":o})),j=r("background-color-overlay",o),k=r("background-gradient-overlay",o),h=r("background-image-overlay",o),w=j.schema.or(k.schema).or(h.schema),S=r("background-overlay",e.z.array(w)),x=r("background-image-position-offset",o),$=r("background-image-size-scale",o),E=r("boolean",e.z.boolean().nullable()),A=r("color-stop",e.z.strictObject({color:o,offset:o})),V=r("gradient-color-stop",e.z.array(A.schema));function C(e,r){const t=structuredClone(e);return Object.entries(r).forEach((([e,r])=>{null==r?delete t[e]:t[e]=r})),t}var _=e.z.object({$$type:e.z.string(),value:e.z.any(),disabled:e.z.boolean().optional()}),I=e=>_.safeParse(e).success,M=e=>R(e)?null:Array.isArray(e)?e.map(M).filter((e=>!R(e))):"object"==typeof e?Object.fromEntries(Object.entries(e).map((([e,r])=>[e,M(r)])).filter((([,e])=>!R(e)))):e,R=e=>e&&I(e)?R(e.value):B(e)||D(e)||G(e),B=e=>null==e||""===e,D=e=>Array.isArray(e)&&e.every(R),G=e=>"object"==typeof e&&D(Object.values(e))}(),(window.elementorV2=window.elementorV2||{}).editorProps=n}(),window.elementorV2.editorProps?.init?.();