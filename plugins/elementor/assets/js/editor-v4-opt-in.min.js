/*! elementor - v3.29.0 - 04-06-2025 */
/*! For license information please see editor-v4-opt-in.min.js.LICENSE.txt */
(()=>{var e={18791:(e,t,r)=>{"use strict";var n=r(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;_interopRequireWildcard(r(41594));var o=_interopRequireWildcard(r(75206)),a=r(7470);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=n(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var o={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&{}.hasOwnProperty.call(e,i)){var l=a?Object.getOwnPropertyDescriptor(e,i):null;l&&(l.get||l.set)?Object.defineProperty(o,i,l):o[i]=e[i]}return o.default=e,r&&r.set(e,o),o}t.default={render:function render(e,t){var r;try{var n=(0,a.createRoot)(t);n.render(e),r=function unmountFunction(){n.unmount()}}catch(n){o.render(e,t),r=function unmountFunction(){o.unmountComponentAtNode(t)}}return{unmount:r}}}},21698:(e,t,r)=>{"use strict";var n=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.triggerOptOut=t.triggerOptIn=void 0;var o=n(r(61790)),a=n(r(58155));t.triggerOptIn=function(){var e=(0,a.default)(o.default.mark((function _callee(){return o.default.wrap((function _callee$(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",elementorCommon.ajax.addRequest("editor_v4_opt_in"));case 1:case"end":return e.stop()}}),_callee)})));return function triggerOptIn(){return e.apply(this,arguments)}}(),t.triggerOptOut=function(){var e=(0,a.default)(o.default.mark((function _callee2(){return o.default.wrap((function _callee2$(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",elementorCommon.ajax.addRequest("editor_v4_opt_out"));case 1:case"end":return e.stop()}}),_callee2)})));return function triggerOptOut(){return e.apply(this,arguments)}}()},77123:(e,t,r)=>{"use strict";var n=r(62688),o=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.TextNode=t.ContentListItem=t.ContentList=t.AdvantagesListItem=t.AdvantagesList=void 0;var a=o(r(41594)),i=o(r(78304)),l=o(r(40453)),c=r(86956),s=r(44048),u=["children"],d=["children"],p=["children"],f=["children"],m=["children"],h=t.TextNode=function TextNode(e){var t=e.children,r=(0,l.default)(e,u);return a.default.createElement(c.Typography,(0,i.default)({color:"text.primary"},r),t)};h.propTypes={children:n.node},(t.ContentList=function ContentList(e){var t=e.children,r=(0,l.default)(e,d);return a.default.createElement(c.Box,(0,i.default)({component:"ul",sx:{my:0}},r),t)}).propTypes={children:n.node},(t.ContentListItem=function ContentListItem(e){var t=e.children,r=(0,l.default)(e,p);return a.default.createElement(h,(0,i.default)({component:"li",sx:{listStyle:"disc",marginInlineStart:3}},r),t)}).propTypes={children:n.node},(t.AdvantagesList=function AdvantagesList(e){var t=e.children,r=(0,l.default)(e,f);return a.default.createElement(c.Box,(0,i.default)({component:"ul",sx:{display:"flex",flexDirection:"column",gap:.5,my:0}},r),t)}).propTypes={children:n.node},(t.AdvantagesListItem=function AdvantagesListItem(e){var t=e.children,r=(0,l.default)(e,m);return a.default.createElement(h,(0,i.default)({component:"li",sx:{listStyle:"none",marginInlineStart:0,lineHeight:"150%",display:"flex",alignItems:"flex-start",gap:.5}},r),a.default.createElement(s.CheckIcon,{fontSize:"small"}),t)}).propTypes={children:n.node}},94088:(e,t,r)=>{"use strict";var n=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.ImageSquarePlaceholder=t.ImageLandscapePlaceholder=void 0;var o=n(r(41594)),a=n(r(78304)),i=r(86956);t.ImageLandscapePlaceholder=function ImageLandscapePlaceholder(e){return o.default.createElement(i.SvgIcon,(0,a.default)({viewBox:"0 0 600 260"},e),o.default.createElement("rect",{x:"0",y:"0",width:"600",height:"260",fill:"#d9d9d9"}))},t.ImageSquarePlaceholder=function ImageSquarePlaceholder(e){return o.default.createElement(i.SvgIcon,(0,a.default)({viewBox:"0 0 500 500"},e),o.default.createElement("rect",{x:"0",y:"0",width:"500",height:"500",fill:"#d9d9d9"}))}},14901:(e,t,r)=>{"use strict";var n=r(62688),o=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.Message=void 0;var a=o(r(41594)),i=r(86956),l=r(44048);(t.Message=function Message(e){var t=e.action,r=e.children,n=e.severity,o=void 0===n?"message":n,c=e.onClose;return a.default.createElement(i.Snackbar,{open:!0,autoHideDuration:4e3,anchorOrigin:{vertical:"bottom",horizontal:"right"},onClose:c},"message"!==o?a.default.createElement(i.Alert,{variant:"filled",severity:o,onClose:c},r):a.default.createElement(i.SnackbarContent,{message:a.default.createElement(i.Stack,{direction:"row",gap:1.5,alignItems:"center"},a.default.createElement(l.CircleCheckFilledIcon,null),r),action:a.default.createElement(i.Stack,{direction:"row",spacing:1,alignItems:"center"},t,a.default.createElement(i.IconButton,{color:"inherit",size:"small",onClick:c},a.default.createElement(l.XIcon,{fontSize:"small"})))}))}).propTypes={action:n.node,children:n.node,severity:n.oneOf(["message","success","warning","error"]),onClose:n.func}},16305:(e,t,r)=>{"use strict";var n=r(62688),o=r(96784),a=r(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.Terms=void 0;var i=o(r(78304)),l=o(r(18821)),c=o(r(40453)),s=r(12470),u=r(86956),d=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=a(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&{}.hasOwnProperty.call(e,i)){var l=o?Object.getOwnPropertyDescriptor(e,i):null;l&&(l.get||l.set)?Object.defineProperty(n,i,l):n[i]=e[i]}return n.default=e,r&&r.set(e,n),n}(r(41594)),p=r(77123),f=o(r(72318)),m=o(r(44867)),h=["onClose","onSubmit","isEnrolled"];function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}var y={header:(0,s.__)("Editor V4","elementor"),chip:(0,s.__)("Alpha","elementor"),checkboxText:(0,s.__)("I’ve read and understood.","elementor"),optIn:{titleText:(0,s.__)("You are about to enable Editor V4 features!","elementor"),introText:(0,s.__)("By activating, you’ll get early access to the next generation of Elementor’s Editor. This is your chance to explore new capabilities and help shape the future of Elementor! ","elementor"),notesHeader:(0,s.__)(" Important notes:","elementor"),notes:{alphaPrefix:(0,s.__)("Editor V4 is currently in alpha, ","elementor"),details:[(0,s.__)("and development is still in progress. Do not use it on live sites - use a staging or development environment instead.","elementor"),(0,s.__)("When you activate Editor V4, you’ll also be activating Containers, the Top Bar, and Nested Elements. You can turn them back off by going to WP Admin > Elementor > Settings > Features.","elementor")]},activateButton:(0,s.__)("Activate","elementor"),cancelButton:(0,s.__)("Cancel","elementor")},optOut:{titleText:(0,s.__)("You’re deactivating Editor V4","elementor"),introText:(0,s.__)("We hope you enjoyed testing and building with these new features.","elementor"),notesHeader:(0,s.__)("Keep in mind:","elementor"),notes:{details:[(0,s.__)("By deactivating, you’ll lose all Editor V4 features, and any content you created with V4-specific features will no longer be available or appear on your site.","elementor"),(0,s.__)("Containers, the Top Bar, and Nested Elements will stay in their current status.","elementor")]},activateButton:(0,s.__)("Deactivate V4","elementor"),cancelButton:(0,s.__)("Cancel","elementor")}};(t.Terms=function Terms(e){var t=e.onClose,r=e.onSubmit,n=e.isEnrolled,o=(0,c.default)(e,h),a=(0,d.useState)(!1),s=(0,l.default)(a,2),g=s[0],_=s[1],v=n?"optOut":"optIn";return d.default.createElement(u.Dialog,(0,i.default)({},o,{open:!0,onClose:t}),d.default.createElement(m.default,null,d.default.createElement(f.default,null,d.default.createElement(u.DialogTitle,null,y.header),d.default.createElement(u.Chip,{label:y.chip,color:"secondary",variant:"filled",size:"small"}))),d.default.createElement(u.DialogContent,{dividers:!0},d.default.createElement(u.Stack,{gap:2.5},d.default.createElement(u.Stack,{gap:1},d.default.createElement(p.TextNode,{align:"center",variant:"h6"},y[v].titleText),d.default.createElement(p.TextNode,{align:"center",variant:"body2"},y[v].introText)),d.default.createElement(u.Stack,{gap:1},d.default.createElement(p.TextNode,{variant:"body2"},y[v].notesHeader),d.default.createElement(p.ContentList,null,d.default.createElement(p.ContentListItem,{variant:"body2"},!n&&d.default.createElement(p.TextNode,{variant:"subtitle2",component:"span"},y.optIn.notes.alphaPrefix),y[v].notes.details[0]),y[v].notes.details.slice(1).map((function(e,t){return d.default.createElement(p.ContentListItem,{key:t,variant:"body2"},e)})))),d.default.createElement(u.FormControlLabel,{control:d.default.createElement(u.Checkbox,{checked:!!g,onChange:function handleCheckboxChange(){_((function(e){return!e}))},color:"secondary",size:"small"}),label:d.default.createElement(p.TextNode,{variant:"body2"},y.checkboxText)}))),d.default.createElement(u.DialogActions,null,d.default.createElement(u.Button,{variant:"text",color:"secondary",onClick:t},y[v].cancelButton),d.default.createElement(u.Button,{disabled:!g,variant:"contained",onClick:function handleSubmit(){g&&r()}},y[v].activateButton)))}).propTypes={onClose:n.func,onSubmit:n.func,isEnrolled:n.bool}},34915:(e,t,r)=>{"use strict";var n=r(62688),o=r(96784),a=r(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.OptIn=void 0;var i,l=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=a(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&{}.hasOwnProperty.call(e,i)){var l=o?Object.getOwnPropertyDescriptor(e,i):null;l&&(l.get||l.set)?Object.defineProperty(n,i,l):n[i]=e[i]}return n.default=e,r&&r.set(e,n),n}(r(41594)),c=o(r(85707)),s=o(r(18821)),u=r(86956),d=r(44048),p=r(12470),f=r(77123),m=r(94088),h=r(14901),y=r(21698),g=o(r(94786)),_=r(16305);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}function ownKeys(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(r),!0).forEach((function(t){(0,c.default)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ownKeys(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var v="e-editor-v4-opt-in-message",x="e-editor-v4-opt-out-message",b={title:(0,p.__)("The road to Editor V4","elementor"),chip:(0,p.__)("Alpha","elementor"),welcomeText:(0,p.__)("Welcome to a new era of web creation with Editor V4. It’s faster, more flexible, and built with a fresh approach to structure & styling.","elementor"),advantagesHeader:(0,p.__)("Here’s what’s inside the alpha version:","elementor"),advantages:[(0,p.__)("Unparalleled performance - Cleaner code & a lighter CSS footprint.","elementor"),(0,p.__)("Professional tools at your fingertips - classes and states.","elementor"),(0,p.__)("Consistent styling experience - A unified Style tab for all elements.","elementor"),(0,p.__)("Fully responsive design - Customize any style property per screen.","elementor")],andMore:(0,p.__)("And much more!","elementor"),readMore:(0,p.__)("Learn more","elementor"),warning:(0,p.__)("Editor V4 is still in alpha and should not be used on live sites yet.","elementor"),feedback:(0,p.__)("We’d love your feedback!","elementor"),overToGithub:(0,p.__)("Head over to Github","elementor"),tellUsWhy:(0,p.__)("Tell us why","elementor"),image:(0,p.__)("Editor V4","elementor"),buttons:{tryOut:(0,p.__)("Try out the new experience","elementor"),optIn:(0,p.__)("Activate the new experience","elementor"),optOut:(0,p.__)("Deactivate V4","elementor")},messages:{optInSuccess:(0,p.__)("Welcome! You’ve got the newest version of the editor.","elementor"),optOut:(0,p.__)("You’ve deactivated the new Editor. Have feedback?","elementor"),error:(0,p.__)("Ouch, there was a glitch. Try activating V4 again soon.","elementor")}},T={feedbackUrl:"https://go.elementor.com/wp-dash-opt-in-v4-feedback/",readMoreUrl:"https://go.elementor.com/wp-dash-opt-in-v4-help-center/",tryOutUrl:g.default.sanitize(function decodeHtmlUrl(e){var t=document.createElement("textarea");return t.innerHTML=e,t.value}(null===(i=elementorSettingsEditor4OptIn)||void 0===i||null===(i=i.urls)||void 0===i?void 0:i.start_building))||"#"},E={src:"https://assets.elementor.com/v4-promotion/v1/images/v4_opt_in_500.png",sx:{width:"100%",maxHeight:"507px",maxWidth:"sm",height:"auto",mx:"auto",borderRadius:2}},S={src:"https://assets.elementor.com/v4-promotion/v1/images/v4_opt_in_260.png",sx:{width:"100%",height:"auto",maxHeight:"260px",mx:"auto",maxWidth:"sm",borderRadius:2}};(t.OptIn=function OptIn(e){var t,r=e.state,n=(0,l.useState)(!1),o=(0,s.default)(n,2),a=o[0],i=o[1],c=(0,l.useState)(""),p=(0,s.default)(c,2),g=p[0],w=p[1],O=(0,l.useState)(""),k=(0,s.default)(O,2),A=k[0],C=k[1],I=(0,l.useState)(""),N=(0,s.default)(I,2),L=N[0],R=N[1];(0,l.useEffect)((function(){var e=sessionStorage.getItem(v),t=sessionStorage.getItem(x);e&&(setTimeout((function(){w(e)}),100),sessionStorage.removeItem(v)),t&&(setTimeout((function(){C(t)}),100),sessionStorage.removeItem(x))}),[]);var M=!(null==r||null===(t=r.features)||void 0===t||!t.editor_v4);return l.default.createElement(u.Container,{maxWidth:"xl",sx:{marginBlockStart:2.5,display:"flex",flexBasis:"100%",gap:3,flexDirection:{xs:"column-reverse",md:"row"}}},l.default.createElement(u.Stack,{sx:{flex:1,maxWidth:{md:"507px",sm:"600px"},gap:2.5,mx:"auto"}},l.default.createElement(u.Stack,{direction:"row",alignItems:"center",gap:1},l.default.createElement(f.TextNode,{variant:"h4",width:"fit-content"},b.title),l.default.createElement(u.Chip,{size:"small",color:"secondary",variant:"filled",label:b.chip})),l.default.createElement(u.Stack,{direction:"column",gap:3},l.default.createElement(u.Box,null,l.default.createElement(f.TextNode,null,b.welcomeText)),l.default.createElement(u.Box,null,l.default.createElement(f.TextNode,{variant:"subtitle1",sx:{mb:1.5}},b.advantagesHeader),l.default.createElement(f.AdvantagesList,null,b.advantages.map((function(e,t){return l.default.createElement(f.AdvantagesListItem,{key:t},e)})),l.default.createElement(f.AdvantagesListItem,{key:b.advantages.length},b.andMore," ",l.default.createElement(u.Link,{color:"text.primary",href:T.readMoreUrl,target:"_blank"},b.readMore))))),l.default.createElement(u.Stack,{direction:"row",alignItems:"self-start",gap:.5,sx:{mb:2.5}},l.default.createElement(d.AlertTriangleIcon,{color:"action"}),l.default.createElement(u.Box,null,l.default.createElement(f.TextNode,null,b.warning))),l.default.createElement(u.Stack,{direction:"column",width:"clamp(240px, max(340px, 75%), 340px)",maxWidth:"100%",gap:2},M?l.default.createElement(u.Button,{onClick:function onClick(){return window.location.href=T.tryOutUrl},size:"large",color:"primary",variant:"contained",sx:{flexGrow:1}},b.buttons.tryOut):l.default.createElement(u.Button,{onClick:function onClick(){i(!0)},size:"large",color:"primary",variant:"contained",sx:{flexGrow:1}},b.buttons.optIn),l.default.createElement(u.Button,{onClick:function onClick(){i(!0)},size:"large",color:"secondary",variant:"outlined",sx:{flexGrow:1,visibility:M?"visible":"hidden"}},b.buttons.optOut)),l.default.createElement(f.TextNode,null,b.feedback," ",l.default.createElement(u.Link,{underline:"hover",href:T.feedbackUrl,target:"_blank"},b.overToGithub))),l.default.createElement(u.Stack,{sx:{flex:1,px:0,maxWidth:{md:"507px",sm:"600px"},mx:"auto"}},E.src?l.default.createElement(u.Image,{src:E.src,alt:b.image,sx:_objectSpread(_objectSpread({},E.sx),{},{display:{xs:"none",md:"block"}})}):l.default.createElement(m.ImageSquarePlaceholder,{sx:_objectSpread(_objectSpread({},E.sx),{},{display:{xs:"none",md:"block"}})}),S.src?l.default.createElement(u.Image,{src:S.src,alt:b.image,sx:_objectSpread(_objectSpread({},S.sx),{},{display:{xs:"block",md:"none"}})}):l.default.createElement(m.ImageLandscapePlaceholder,{sx:_objectSpread(_objectSpread({},S.sx),{},{display:{xs:"block",md:"none"}})})),a&&l.default.createElement(_.Terms,{onClose:function handlePopoverClose(){i(!1)},onSubmit:M?function maybeOptOut(){(0,y.triggerOptOut)().then((function(){sessionStorage.setItem(x,b.messages.optOut),window.location.reload()})).catch((function(){R(b.messages.error)}))}:function maybeOptIn(){(0,y.triggerOptIn)().then((function(){sessionStorage.setItem(v,b.messages.optInSuccess),window.location.reload()})).catch((function(){R(b.messages.error)}))},isEnrolled:M}),g&&l.default.createElement(h.Message,{onClose:function onClose(){return w("")}},g),A&&l.default.createElement(h.Message,{onClose:function onClose(){return C("")},action:l.default.createElement(u.Link,{href:T.feedbackUrl,target:"_blank",color:"inherit",sx:{cursor:"pointer",textDecoration:"none",pl:3}},b.tellUsWhy)},A),L&&l.default.createElement(h.Message,{severity:"error",onClose:function onClose(){return R("")}},L))}).propTypes={state:n.shape({features:n.shape({editor_v4:n.bool})}).isRequired}},94786:e=>{"use strict";const{entries:t,setPrototypeOf:r,isFrozen:n,getPrototypeOf:o,getOwnPropertyDescriptor:a}=Object;let{freeze:i,seal:l,create:c}=Object,{apply:s,construct:u}="undefined"!=typeof Reflect&&Reflect;i||(i=function freeze(e){return e}),l||(l=function seal(e){return e}),s||(s=function apply(e,t,r){return e.apply(t,r)}),u||(u=function construct(e,t){return new e(...t)});const d=unapply(Array.prototype.forEach),p=unapply(Array.prototype.lastIndexOf),f=unapply(Array.prototype.pop),m=unapply(Array.prototype.push),h=unapply(Array.prototype.splice),y=unapply(String.prototype.toLowerCase),g=unapply(String.prototype.toString),_=unapply(String.prototype.match),v=unapply(String.prototype.replace),x=unapply(String.prototype.indexOf),b=unapply(String.prototype.trim),T=unapply(Object.prototype.hasOwnProperty),E=unapply(RegExp.prototype.test),S=function unconstruct(e){return function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];return u(e,r)}}(TypeError);function unapply(e){return function(t){t instanceof RegExp&&(t.lastIndex=0);for(var r=arguments.length,n=new Array(r>1?r-1:0),o=1;o<r;o++)n[o-1]=arguments[o];return s(e,t,n)}}function addToSet(e,t){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:y;r&&r(e,null);let a=t.length;for(;a--;){let r=t[a];if("string"==typeof r){const e=o(r);e!==r&&(n(t)||(t[a]=e),r=e)}e[r]=!0}return e}function cleanArray(e){for(let t=0;t<e.length;t++){T(e,t)||(e[t]=null)}return e}function clone(e){const r=c(null);for(const[n,o]of t(e)){T(e,n)&&(Array.isArray(o)?r[n]=cleanArray(o):o&&"object"==typeof o&&o.constructor===Object?r[n]=clone(o):r[n]=o)}return r}function lookupGetter(e,t){for(;null!==e;){const r=a(e,t);if(r){if(r.get)return unapply(r.get);if("function"==typeof r.value)return unapply(r.value)}e=o(e)}return function fallbackValue(){return null}}const w=i(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),O=i(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),k=i(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),A=i(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),C=i(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),I=i(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),N=i(["#text"]),L=i(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),R=i(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),M=i(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),P=i(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),D=l(/\{\{[\w\W]*|[\w\W]*\}\}/gm),j=l(/<%[\w\W]*|[\w\W]*%>/gm),W=l(/\$\{[\w\W]*/gm),F=l(/^data-[\-\w.\u00B7-\uFFFF]+$/),z=l(/^aria-[\-\w]+$/),H=l(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp|matrix):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),U=l(/^(?:\w+script|data):/i),G=l(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),B=l(/^html$/i),q=l(/^[a-z][.\w]*(-[.\w]+)+$/i);var Y=Object.freeze({__proto__:null,ARIA_ATTR:z,ATTR_WHITESPACE:G,CUSTOM_ELEMENT:q,DATA_ATTR:F,DOCTYPE_NAME:B,ERB_EXPR:j,IS_ALLOWED_URI:H,IS_SCRIPT_OR_DATA:U,MUSTACHE_EXPR:D,TMPLIT_EXPR:W});const V=1,X=3,K=7,$=8,Z=9,J=function getGlobal(){return"undefined"==typeof window?null:window};var Q=function createDOMPurify(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:J();const DOMPurify=e=>createDOMPurify(e);if(DOMPurify.version="3.2.6",DOMPurify.removed=[],!e||!e.document||e.document.nodeType!==Z||!e.Element)return DOMPurify.isSupported=!1,DOMPurify;let{document:r}=e;const n=r,o=n.currentScript,{DocumentFragment:a,HTMLTemplateElement:l,Node:s,Element:u,NodeFilter:D,NamedNodeMap:j=e.NamedNodeMap||e.MozNamedAttrMap,HTMLFormElement:W,DOMParser:F,trustedTypes:z}=e,U=u.prototype,G=lookupGetter(U,"cloneNode"),q=lookupGetter(U,"remove"),Q=lookupGetter(U,"nextSibling"),ee=lookupGetter(U,"childNodes"),te=lookupGetter(U,"parentNode");if("function"==typeof l){const e=r.createElement("template");e.content&&e.content.ownerDocument&&(r=e.content.ownerDocument)}let re,ne="";const{implementation:oe,createNodeIterator:ae,createDocumentFragment:ie,getElementsByTagName:le}=r,{importNode:ce}=n;let se={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]};DOMPurify.isSupported="function"==typeof t&&"function"==typeof te&&oe&&void 0!==oe.createHTMLDocument;const{MUSTACHE_EXPR:ue,ERB_EXPR:de,TMPLIT_EXPR:pe,DATA_ATTR:fe,ARIA_ATTR:me,IS_SCRIPT_OR_DATA:he,ATTR_WHITESPACE:ye,CUSTOM_ELEMENT:ge}=Y;let{IS_ALLOWED_URI:_e}=Y,ve=null;const xe=addToSet({},[...w,...O,...k,...C,...N]);let be=null;const Te=addToSet({},[...L,...R,...M,...P]);let Ee=Object.seal(c(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),Se=null,we=null,Oe=!0,ke=!0,Ae=!1,Ce=!0,Ie=!1,Ne=!0,Le=!1,Re=!1,Me=!1,Pe=!1,De=!1,je=!1,We=!0,Fe=!1,ze=!0,He=!1,Ue={},Ge=null;const Be=addToSet({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let qe=null;const Ye=addToSet({},["audio","video","img","source","image","track"]);let Ve=null;const Xe=addToSet({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),Ke="http://www.w3.org/1998/Math/MathML",$e="http://www.w3.org/2000/svg",Ze="http://www.w3.org/1999/xhtml";let Je=Ze,Qe=!1,et=null;const tt=addToSet({},[Ke,$e,Ze],g);let rt=addToSet({},["mi","mo","mn","ms","mtext"]),nt=addToSet({},["annotation-xml"]);const ot=addToSet({},["title","style","font","a","script"]);let at=null;const it=["application/xhtml+xml","text/html"];let lt=null,ct=null;const st=r.createElement("form"),ut=function isRegexOrFunction(e){return e instanceof RegExp||e instanceof Function},dt=function _parseConfig(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!ct||ct!==e){if(e&&"object"==typeof e||(e={}),e=clone(e),at=-1===it.indexOf(e.PARSER_MEDIA_TYPE)?"text/html":e.PARSER_MEDIA_TYPE,lt="application/xhtml+xml"===at?g:y,ve=T(e,"ALLOWED_TAGS")?addToSet({},e.ALLOWED_TAGS,lt):xe,be=T(e,"ALLOWED_ATTR")?addToSet({},e.ALLOWED_ATTR,lt):Te,et=T(e,"ALLOWED_NAMESPACES")?addToSet({},e.ALLOWED_NAMESPACES,g):tt,Ve=T(e,"ADD_URI_SAFE_ATTR")?addToSet(clone(Xe),e.ADD_URI_SAFE_ATTR,lt):Xe,qe=T(e,"ADD_DATA_URI_TAGS")?addToSet(clone(Ye),e.ADD_DATA_URI_TAGS,lt):Ye,Ge=T(e,"FORBID_CONTENTS")?addToSet({},e.FORBID_CONTENTS,lt):Be,Se=T(e,"FORBID_TAGS")?addToSet({},e.FORBID_TAGS,lt):clone({}),we=T(e,"FORBID_ATTR")?addToSet({},e.FORBID_ATTR,lt):clone({}),Ue=!!T(e,"USE_PROFILES")&&e.USE_PROFILES,Oe=!1!==e.ALLOW_ARIA_ATTR,ke=!1!==e.ALLOW_DATA_ATTR,Ae=e.ALLOW_UNKNOWN_PROTOCOLS||!1,Ce=!1!==e.ALLOW_SELF_CLOSE_IN_ATTR,Ie=e.SAFE_FOR_TEMPLATES||!1,Ne=!1!==e.SAFE_FOR_XML,Le=e.WHOLE_DOCUMENT||!1,Pe=e.RETURN_DOM||!1,De=e.RETURN_DOM_FRAGMENT||!1,je=e.RETURN_TRUSTED_TYPE||!1,Me=e.FORCE_BODY||!1,We=!1!==e.SANITIZE_DOM,Fe=e.SANITIZE_NAMED_PROPS||!1,ze=!1!==e.KEEP_CONTENT,He=e.IN_PLACE||!1,_e=e.ALLOWED_URI_REGEXP||H,Je=e.NAMESPACE||Ze,rt=e.MATHML_TEXT_INTEGRATION_POINTS||rt,nt=e.HTML_INTEGRATION_POINTS||nt,Ee=e.CUSTOM_ELEMENT_HANDLING||{},e.CUSTOM_ELEMENT_HANDLING&&ut(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(Ee.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&ut(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(Ee.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(Ee.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),Ie&&(ke=!1),De&&(Pe=!0),Ue&&(ve=addToSet({},N),be=[],!0===Ue.html&&(addToSet(ve,w),addToSet(be,L)),!0===Ue.svg&&(addToSet(ve,O),addToSet(be,R),addToSet(be,P)),!0===Ue.svgFilters&&(addToSet(ve,k),addToSet(be,R),addToSet(be,P)),!0===Ue.mathMl&&(addToSet(ve,C),addToSet(be,M),addToSet(be,P))),e.ADD_TAGS&&(ve===xe&&(ve=clone(ve)),addToSet(ve,e.ADD_TAGS,lt)),e.ADD_ATTR&&(be===Te&&(be=clone(be)),addToSet(be,e.ADD_ATTR,lt)),e.ADD_URI_SAFE_ATTR&&addToSet(Ve,e.ADD_URI_SAFE_ATTR,lt),e.FORBID_CONTENTS&&(Ge===Be&&(Ge=clone(Ge)),addToSet(Ge,e.FORBID_CONTENTS,lt)),ze&&(ve["#text"]=!0),Le&&addToSet(ve,["html","head","body"]),ve.table&&(addToSet(ve,["tbody"]),delete Se.tbody),e.TRUSTED_TYPES_POLICY){if("function"!=typeof e.TRUSTED_TYPES_POLICY.createHTML)throw S('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if("function"!=typeof e.TRUSTED_TYPES_POLICY.createScriptURL)throw S('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');re=e.TRUSTED_TYPES_POLICY,ne=re.createHTML("")}else void 0===re&&(re=function _createTrustedTypesPolicy(e,t){if("object"!=typeof e||"function"!=typeof e.createPolicy)return null;let r=null;const n="data-tt-policy-suffix";t&&t.hasAttribute(n)&&(r=t.getAttribute(n));const o="dompurify"+(r?"#"+r:"");try{return e.createPolicy(o,{createHTML:e=>e,createScriptURL:e=>e})}catch(e){return console.warn("TrustedTypes policy "+o+" could not be created."),null}}(z,o)),null!==re&&"string"==typeof ne&&(ne=re.createHTML(""));i&&i(e),ct=e}},pt=addToSet({},[...O,...k,...A]),ft=addToSet({},[...C,...I]),mt=function _forceRemove(e){m(DOMPurify.removed,{element:e});try{te(e).removeChild(e)}catch(t){q(e)}},ht=function _removeAttribute(e,t){try{m(DOMPurify.removed,{attribute:t.getAttributeNode(e),from:t})}catch(e){m(DOMPurify.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e)if(Pe||De)try{mt(t)}catch(e){}else try{t.setAttribute(e,"")}catch(e){}},yt=function _initDocument(e){let t=null,n=null;if(Me)e="<remove></remove>"+e;else{const t=_(e,/^[\r\n\t ]+/);n=t&&t[0]}"application/xhtml+xml"===at&&Je===Ze&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");const o=re?re.createHTML(e):e;if(Je===Ze)try{t=(new F).parseFromString(o,at)}catch(e){}if(!t||!t.documentElement){t=oe.createDocument(Je,"template",null);try{t.documentElement.innerHTML=Qe?ne:o}catch(e){}}const a=t.body||t.documentElement;return e&&n&&a.insertBefore(r.createTextNode(n),a.childNodes[0]||null),Je===Ze?le.call(t,Le?"html":"body")[0]:Le?t.documentElement:a},gt=function _createNodeIterator(e){return ae.call(e.ownerDocument||e,e,D.SHOW_ELEMENT|D.SHOW_COMMENT|D.SHOW_TEXT|D.SHOW_PROCESSING_INSTRUCTION|D.SHOW_CDATA_SECTION,null)},_t=function _isClobbered(e){return e instanceof W&&("string"!=typeof e.nodeName||"string"!=typeof e.textContent||"function"!=typeof e.removeChild||!(e.attributes instanceof j)||"function"!=typeof e.removeAttribute||"function"!=typeof e.setAttribute||"string"!=typeof e.namespaceURI||"function"!=typeof e.insertBefore||"function"!=typeof e.hasChildNodes)},vt=function _isNode(e){return"function"==typeof s&&e instanceof s};function _executeHooks(e,t,r){d(e,(e=>{e.call(DOMPurify,t,r,ct)}))}const xt=function _sanitizeElements(e){let t=null;if(_executeHooks(se.beforeSanitizeElements,e,null),_t(e))return mt(e),!0;const r=lt(e.nodeName);if(_executeHooks(se.uponSanitizeElement,e,{tagName:r,allowedTags:ve}),Ne&&e.hasChildNodes()&&!vt(e.firstElementChild)&&E(/<[/\w!]/g,e.innerHTML)&&E(/<[/\w!]/g,e.textContent))return mt(e),!0;if(e.nodeType===K)return mt(e),!0;if(Ne&&e.nodeType===$&&E(/<[/\w]/g,e.data))return mt(e),!0;if(!ve[r]||Se[r]){if(!Se[r]&&Tt(r)){if(Ee.tagNameCheck instanceof RegExp&&E(Ee.tagNameCheck,r))return!1;if(Ee.tagNameCheck instanceof Function&&Ee.tagNameCheck(r))return!1}if(ze&&!Ge[r]){const t=te(e)||e.parentNode,r=ee(e)||e.childNodes;if(r&&t){for(let n=r.length-1;n>=0;--n){const o=G(r[n],!0);o.__removalCount=(e.__removalCount||0)+1,t.insertBefore(o,Q(e))}}}return mt(e),!0}return e instanceof u&&!function _checkValidNamespace(e){let t=te(e);t&&t.tagName||(t={namespaceURI:Je,tagName:"template"});const r=y(e.tagName),n=y(t.tagName);return!!et[e.namespaceURI]&&(e.namespaceURI===$e?t.namespaceURI===Ze?"svg"===r:t.namespaceURI===Ke?"svg"===r&&("annotation-xml"===n||rt[n]):Boolean(pt[r]):e.namespaceURI===Ke?t.namespaceURI===Ze?"math"===r:t.namespaceURI===$e?"math"===r&&nt[n]:Boolean(ft[r]):e.namespaceURI===Ze?!(t.namespaceURI===$e&&!nt[n])&&!(t.namespaceURI===Ke&&!rt[n])&&!ft[r]&&(ot[r]||!pt[r]):!("application/xhtml+xml"!==at||!et[e.namespaceURI]))}(e)?(mt(e),!0):"noscript"!==r&&"noembed"!==r&&"noframes"!==r||!E(/<\/no(script|embed|frames)/i,e.innerHTML)?(Ie&&e.nodeType===X&&(t=e.textContent,d([ue,de,pe],(e=>{t=v(t,e," ")})),e.textContent!==t&&(m(DOMPurify.removed,{element:e.cloneNode()}),e.textContent=t)),_executeHooks(se.afterSanitizeElements,e,null),!1):(mt(e),!0)},bt=function _isValidAttribute(e,t,n){if(We&&("id"===t||"name"===t)&&(n in r||n in st))return!1;if(ke&&!we[t]&&E(fe,t));else if(Oe&&E(me,t));else if(!be[t]||we[t]){if(!(Tt(e)&&(Ee.tagNameCheck instanceof RegExp&&E(Ee.tagNameCheck,e)||Ee.tagNameCheck instanceof Function&&Ee.tagNameCheck(e))&&(Ee.attributeNameCheck instanceof RegExp&&E(Ee.attributeNameCheck,t)||Ee.attributeNameCheck instanceof Function&&Ee.attributeNameCheck(t))||"is"===t&&Ee.allowCustomizedBuiltInElements&&(Ee.tagNameCheck instanceof RegExp&&E(Ee.tagNameCheck,n)||Ee.tagNameCheck instanceof Function&&Ee.tagNameCheck(n))))return!1}else if(Ve[t]);else if(E(_e,v(n,ye,"")));else if("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==x(n,"data:")||!qe[e]){if(Ae&&!E(he,v(n,ye,"")));else if(n)return!1}else;return!0},Tt=function _isBasicCustomElement(e){return"annotation-xml"!==e&&_(e,ge)},Et=function _sanitizeAttributes(e){_executeHooks(se.beforeSanitizeAttributes,e,null);const{attributes:t}=e;if(!t||_t(e))return;const r={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:be,forceKeepAttr:void 0};let n=t.length;for(;n--;){const o=t[n],{name:a,namespaceURI:i,value:l}=o,c=lt(a),s=l;let u="value"===a?s:b(s);if(r.attrName=c,r.attrValue=u,r.keepAttr=!0,r.forceKeepAttr=void 0,_executeHooks(se.uponSanitizeAttribute,e,r),u=r.attrValue,!Fe||"id"!==c&&"name"!==c||(ht(a,e),u="user-content-"+u),Ne&&E(/((--!?|])>)|<\/(style|title)/i,u)){ht(a,e);continue}if(r.forceKeepAttr)continue;if(!r.keepAttr){ht(a,e);continue}if(!Ce&&E(/\/>/i,u)){ht(a,e);continue}Ie&&d([ue,de,pe],(e=>{u=v(u,e," ")}));const p=lt(e.nodeName);if(bt(p,c,u)){if(re&&"object"==typeof z&&"function"==typeof z.getAttributeType)if(i);else switch(z.getAttributeType(p,c)){case"TrustedHTML":u=re.createHTML(u);break;case"TrustedScriptURL":u=re.createScriptURL(u)}if(u!==s)try{i?e.setAttributeNS(i,a,u):e.setAttribute(a,u),_t(e)?mt(e):f(DOMPurify.removed)}catch(t){ht(a,e)}}else ht(a,e)}_executeHooks(se.afterSanitizeAttributes,e,null)},St=function _sanitizeShadowDOM(e){let t=null;const r=gt(e);for(_executeHooks(se.beforeSanitizeShadowDOM,e,null);t=r.nextNode();)_executeHooks(se.uponSanitizeShadowNode,t,null),xt(t),Et(t),t.content instanceof a&&_sanitizeShadowDOM(t.content);_executeHooks(se.afterSanitizeShadowDOM,e,null)};return DOMPurify.sanitize=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=null,o=null,i=null,l=null;if(Qe=!e,Qe&&(e="\x3c!--\x3e"),"string"!=typeof e&&!vt(e)){if("function"!=typeof e.toString)throw S("toString is not a function");if("string"!=typeof(e=e.toString()))throw S("dirty is not a string, aborting")}if(!DOMPurify.isSupported)return e;if(Re||dt(t),DOMPurify.removed=[],"string"==typeof e&&(He=!1),He){if(e.nodeName){const t=lt(e.nodeName);if(!ve[t]||Se[t])throw S("root node is forbidden and cannot be sanitized in-place")}}else if(e instanceof s)r=yt("\x3c!----\x3e"),o=r.ownerDocument.importNode(e,!0),o.nodeType===V&&"BODY"===o.nodeName||"HTML"===o.nodeName?r=o:r.appendChild(o);else{if(!Pe&&!Ie&&!Le&&-1===e.indexOf("<"))return re&&je?re.createHTML(e):e;if(r=yt(e),!r)return Pe?null:je?ne:""}r&&Me&&mt(r.firstChild);const c=gt(He?e:r);for(;i=c.nextNode();)xt(i),Et(i),i.content instanceof a&&St(i.content);if(He)return e;if(Pe){if(De)for(l=ie.call(r.ownerDocument);r.firstChild;)l.appendChild(r.firstChild);else l=r;return(be.shadowroot||be.shadowrootmode)&&(l=ce.call(n,l,!0)),l}let u=Le?r.outerHTML:r.innerHTML;return Le&&ve["!doctype"]&&r.ownerDocument&&r.ownerDocument.doctype&&r.ownerDocument.doctype.name&&E(B,r.ownerDocument.doctype.name)&&(u="<!DOCTYPE "+r.ownerDocument.doctype.name+">\n"+u),Ie&&d([ue,de,pe],(e=>{u=v(u,e," ")})),re&&je?re.createHTML(u):u},DOMPurify.setConfig=function(){dt(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}),Re=!0},DOMPurify.clearConfig=function(){ct=null,Re=!1},DOMPurify.isValidAttribute=function(e,t,r){ct||dt({});const n=lt(e),o=lt(t);return bt(n,o,r)},DOMPurify.addHook=function(e,t){"function"==typeof t&&m(se[e],t)},DOMPurify.removeHook=function(e,t){if(void 0!==t){const r=p(se[e],t);return-1===r?void 0:h(se[e],r,1)[0]}return f(se[e])},DOMPurify.removeHooks=function(e){se[e]=[]},DOMPurify.removeAllHooks=function(){se={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}},DOMPurify}();e.exports=Q},40362:(e,t,r)=>{"use strict";var n=r(56441);function emptyFunction(){}function emptyFunctionWithReset(){}emptyFunctionWithReset.resetWarningCache=emptyFunction,e.exports=function(){function shim(e,t,r,o,a,i){if(i!==n){var l=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw l.name="Invariant Violation",l}}function getShim(){return shim}shim.isRequired=shim;var e={array:shim,bigint:shim,bool:shim,func:shim,number:shim,object:shim,string:shim,symbol:shim,any:shim,arrayOf:getShim,element:shim,elementType:shim,instanceOf:getShim,node:shim,objectOf:getShim,oneOf:getShim,oneOfType:getShim,shape:getShim,exact:getShim,checkPropTypes:emptyFunctionWithReset,resetWarningCache:emptyFunction};return e.PropTypes=e,e}},62688:(e,t,r)=>{e.exports=r(40362)()},56441:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},7470:(e,t,r)=>{"use strict";var n=r(75206);t.createRoot=n.createRoot,t.hydrateRoot=n.hydrateRoot},41594:e=>{"use strict";e.exports=React},75206:e=>{"use strict";e.exports=ReactDOM},44048:e=>{"use strict";e.exports=elementorV2.icons},86956:e=>{"use strict";e.exports=elementorV2.ui},44867:e=>{"use strict";e.exports=elementorV2.ui.DialogHeader},72318:e=>{"use strict";e.exports=elementorV2.ui.DialogHeaderGroup},12470:e=>{"use strict";e.exports=wp.i18n},78113:e=>{e.exports=function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n},e.exports.__esModule=!0,e.exports.default=e.exports},70569:e=>{e.exports=function _arrayWithHoles(e){if(Array.isArray(e))return e},e.exports.__esModule=!0,e.exports.default=e.exports},58155:e=>{function asyncGeneratorStep(e,t,r,n,o,a,i){try{var l=e[a](i),c=l.value}catch(e){return void r(e)}l.done?t(c):Promise.resolve(c).then(n,o)}e.exports=function _asyncToGenerator(e){return function(){var t=this,r=arguments;return new Promise((function(n,o){var a=e.apply(t,r);function _next(e){asyncGeneratorStep(a,n,o,_next,_throw,"next",e)}function _throw(e){asyncGeneratorStep(a,n,o,_next,_throw,"throw",e)}_next(void 0)}))}},e.exports.__esModule=!0,e.exports.default=e.exports},85707:(e,t,r)=>{var n=r(45498);e.exports=function _defineProperty(e,t,r){return(t=n(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e},e.exports.__esModule=!0,e.exports.default=e.exports},78304:e=>{function _extends(){return e.exports=_extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,_extends.apply(null,arguments)}e.exports=_extends,e.exports.__esModule=!0,e.exports.default=e.exports},96784:e=>{e.exports=function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports},65474:e=>{e.exports=function _iterableToArrayLimit(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,a,i,l=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(l.push(n.value),l.length!==t);c=!0);}catch(e){s=!0,o=e}finally{try{if(!c&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(s)throw o}}return l}},e.exports.__esModule=!0,e.exports.default=e.exports},11018:e=>{e.exports=function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports.default=e.exports},40453:(e,t,r)=>{var n=r(10739);e.exports=function _objectWithoutProperties(e,t){if(null==e)return{};var r,o,a=n(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++)r=i[o],t.includes(r)||{}.propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a},e.exports.__esModule=!0,e.exports.default=e.exports},10739:e=>{e.exports=function _objectWithoutPropertiesLoose(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.includes(n))continue;r[n]=e[n]}return r},e.exports.__esModule=!0,e.exports.default=e.exports},53051:(e,t,r)=>{var n=r(10564).default;function _regeneratorRuntime(){"use strict";e.exports=_regeneratorRuntime=function _regeneratorRuntime(){return r},e.exports.__esModule=!0,e.exports.default=e.exports;var t,r={},o=Object.prototype,a=o.hasOwnProperty,i=Object.defineProperty||function(e,t,r){e[t]=r.value},l="function"==typeof Symbol?Symbol:{},c=l.iterator||"@@iterator",s=l.asyncIterator||"@@asyncIterator",u=l.toStringTag||"@@toStringTag";function define(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{define({},"")}catch(t){define=function define(e,t,r){return e[t]=r}}function wrap(e,t,r,n){var o=t&&t.prototype instanceof Generator?t:Generator,a=Object.create(o.prototype),l=new Context(n||[]);return i(a,"_invoke",{value:makeInvokeMethod(e,r,l)}),a}function tryCatch(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}r.wrap=wrap;var d="suspendedStart",p="suspendedYield",f="executing",m="completed",h={};function Generator(){}function GeneratorFunction(){}function GeneratorFunctionPrototype(){}var y={};define(y,c,(function(){return this}));var g=Object.getPrototypeOf,_=g&&g(g(values([])));_&&_!==o&&a.call(_,c)&&(y=_);var v=GeneratorFunctionPrototype.prototype=Generator.prototype=Object.create(y);function defineIteratorMethods(e){["next","throw","return"].forEach((function(t){define(e,t,(function(e){return this._invoke(t,e)}))}))}function AsyncIterator(e,t){function invoke(r,o,i,l){var c=tryCatch(e[r],e,o);if("throw"!==c.type){var s=c.arg,u=s.value;return u&&"object"==n(u)&&a.call(u,"__await")?t.resolve(u.__await).then((function(e){invoke("next",e,i,l)}),(function(e){invoke("throw",e,i,l)})):t.resolve(u).then((function(e){s.value=e,i(s)}),(function(e){return invoke("throw",e,i,l)}))}l(c.arg)}var r;i(this,"_invoke",{value:function value(e,n){function callInvokeWithMethodAndArg(){return new t((function(t,r){invoke(e,n,t,r)}))}return r=r?r.then(callInvokeWithMethodAndArg,callInvokeWithMethodAndArg):callInvokeWithMethodAndArg()}})}function makeInvokeMethod(e,r,n){var o=d;return function(a,i){if(o===f)throw Error("Generator is already running");if(o===m){if("throw"===a)throw i;return{value:t,done:!0}}for(n.method=a,n.arg=i;;){var l=n.delegate;if(l){var c=maybeInvokeDelegate(l,n);if(c){if(c===h)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===d)throw o=m,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=f;var s=tryCatch(e,r,n);if("normal"===s.type){if(o=n.done?m:p,s.arg===h)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(o=m,n.method="throw",n.arg=s.arg)}}}function maybeInvokeDelegate(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,maybeInvokeDelegate(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),h;var a=tryCatch(o,e.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,h;var i=a.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,h):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,h)}function pushTryEntry(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function resetTryEntry(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function Context(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(pushTryEntry,this),this.reset(!0)}function values(e){if(e||""===e){var r=e[c];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function next(){for(;++o<e.length;)if(a.call(e,o))return next.value=e[o],next.done=!1,next;return next.value=t,next.done=!0,next};return i.next=i}}throw new TypeError(n(e)+" is not iterable")}return GeneratorFunction.prototype=GeneratorFunctionPrototype,i(v,"constructor",{value:GeneratorFunctionPrototype,configurable:!0}),i(GeneratorFunctionPrototype,"constructor",{value:GeneratorFunction,configurable:!0}),GeneratorFunction.displayName=define(GeneratorFunctionPrototype,u,"GeneratorFunction"),r.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===GeneratorFunction||"GeneratorFunction"===(t.displayName||t.name))},r.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,GeneratorFunctionPrototype):(e.__proto__=GeneratorFunctionPrototype,define(e,u,"GeneratorFunction")),e.prototype=Object.create(v),e},r.awrap=function(e){return{__await:e}},defineIteratorMethods(AsyncIterator.prototype),define(AsyncIterator.prototype,s,(function(){return this})),r.AsyncIterator=AsyncIterator,r.async=function(e,t,n,o,a){void 0===a&&(a=Promise);var i=new AsyncIterator(wrap(e,t,n,o),a);return r.isGeneratorFunction(t)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},defineIteratorMethods(v),define(v,u,"Generator"),define(v,c,(function(){return this})),define(v,"toString",(function(){return"[object Generator]"})),r.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function next(){for(;r.length;){var e=r.pop();if(e in t)return next.value=e,next.done=!1,next}return next.done=!0,next}},r.values=values,Context.prototype={constructor:Context,reset:function reset(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(resetTryEntry),!e)for(var r in this)"t"===r.charAt(0)&&a.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function stop(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function dispatchException(e){if(this.done)throw e;var r=this;function handle(n,o){return i.type="throw",i.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n],i=o.completion;if("root"===o.tryLoc)return handle("end");if(o.tryLoc<=this.prev){var l=a.call(o,"catchLoc"),c=a.call(o,"finallyLoc");if(l&&c){if(this.prev<o.catchLoc)return handle(o.catchLoc,!0);if(this.prev<o.finallyLoc)return handle(o.finallyLoc)}else if(l){if(this.prev<o.catchLoc)return handle(o.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return handle(o.finallyLoc)}}}},abrupt:function abrupt(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&a.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var o=n;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=e,i.arg=t,o?(this.method="next",this.next=o.finallyLoc,h):this.complete(i)},complete:function complete(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),h},finish:function finish(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),resetTryEntry(r),h}},catch:function _catch(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var o=n.arg;resetTryEntry(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function delegateYield(e,r,n){return this.delegate={iterator:values(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),h}},r}e.exports=_regeneratorRuntime,e.exports.__esModule=!0,e.exports.default=e.exports},18821:(e,t,r)=>{var n=r(70569),o=r(65474),a=r(37744),i=r(11018);e.exports=function _slicedToArray(e,t){return n(e)||o(e,t)||a(e,t)||i()},e.exports.__esModule=!0,e.exports.default=e.exports},11327:(e,t,r)=>{var n=r(10564).default;e.exports=function toPrimitive(e,t){if("object"!=n(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var o=r.call(e,t||"default");if("object"!=n(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)},e.exports.__esModule=!0,e.exports.default=e.exports},45498:(e,t,r)=>{var n=r(10564).default,o=r(11327);e.exports=function toPropertyKey(e){var t=o(e,"string");return"symbol"==n(t)?t:t+""},e.exports.__esModule=!0,e.exports.default=e.exports},10564:e=>{function _typeof(t){return e.exports=_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,_typeof(t)}e.exports=_typeof,e.exports.__esModule=!0,e.exports.default=e.exports},37744:(e,t,r)=>{var n=r(78113);e.exports=function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return n(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?n(e,t):void 0}},e.exports.__esModule=!0,e.exports.default=e.exports},61790:(e,t,r)=>{var n=r(53051)();e.exports=n;try{regeneratorRuntime=n}catch(e){"object"==typeof globalThis?globalThis.regeneratorRuntime=n:Function("r","regeneratorRuntime = r")(n)}}},t={};function __webpack_require__(r){var n=t[r];if(void 0!==n)return n.exports;var o=t[r]={exports:{}};return e[r](o,o.exports,__webpack_require__),o.exports}(()=>{"use strict";var e=__webpack_require__(62688),t=__webpack_require__(96784),r=t(__webpack_require__(41594)),n=t(__webpack_require__(18791)),o=__webpack_require__(86956),a=__webpack_require__(34915),i=function App(e){return r.default.createElement(o.DirectionProvider,{rtl:e.isRTL},r.default.createElement(o.LocalizationProvider,null,r.default.createElement(o.ThemeProvider,{colorScheme:"light"},r.default.createElement(a.OptIn,{state:null==e?void 0:e.state}))))};i.propTypes={isRTL:e.bool,state:e.object};!function init(){var e=document.querySelector("#page-editor-v4-opt-in");e&&n.default.render(r.default.createElement(i,{isRTL:!!elementorCommon.config.isRTL,state:elementorSettingsEditor4OptIn}),e)}()})()})();