/*! elementor - v3.29.0 - 04-06-2025 */
(()=>{var e={6379:(e,t,o)=>{"use strict";var n=o(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=n(o(39805)),r=n(o(40989));t.default=function(){return(0,r.default)((function LockPro(e){(0,i.default)(this,LockPro),this.elements=e}),[{key:"bindEvents",value:function bindEvents(){var e=this.elements,t=e.form,o=e.templateType;t.addEventListener("submit",this.onFormSubmit.bind(this)),o.addEventListener("change",this.onTemplateTypeChange.bind(this)),this.onTemplateTypeChange()}},{key:"onFormSubmit",value:function onFormSubmit(e){this.getCurrentLockOptions().is_locked&&e.preventDefault()}},{key:"onTemplateTypeChange",value:function onTemplateTypeChange(){var e=this.getCurrentLockOptions();e.is_locked?this.lock(e):this.unlock()}},{key:"getCurrentLockOptions",value:function getCurrentLockOptions(){var e=this.elements.templateType,t=e.options[e.selectedIndex];return JSON.parse(t.dataset.lock||"{}")}},{key:"lock",value:function lock(e){this.showLockBadge(e.badge),this.showLockButton(e.button),this.hideSubmitButton()}},{key:"unlock",value:function unlock(){this.hideLockBadge(),this.hideLockButton(),this.showSubmitButton()}},{key:"showLockBadge",value:function showLockBadge(e){var t=this.elements,o=t.lockBadge,n=t.lockBadgeText,i=t.lockBadgeIcon;n.innerText=e.text,i.className=e.icon,o.classList.remove("e-hidden")}},{key:"hideLockBadge",value:function hideLockBadge(){this.elements.lockBadge.classList.add("e-hidden")}},{key:"showLockButton",value:function showLockButton(e){var t=this.elements.lockButton;t.href=this.replaceLockLinkPlaceholders(e.url),t.innerText=e.text,t.classList.remove("e-hidden")}},{key:"hideLockButton",value:function hideLockButton(){this.elements.lockButton.classList.add("e-hidden")}},{key:"showSubmitButton",value:function showSubmitButton(){this.elements.submitButton.classList.remove("e-hidden")}},{key:"hideSubmitButton",value:function hideSubmitButton(){this.elements.submitButton.classList.add("e-hidden")}},{key:"replaceLockLinkPlaceholders",value:function replaceLockLinkPlaceholders(e){return e.replace(/%%utm_source%%/g,"wp-add-new").replace(/%%utm_medium%%/g,"wp-dash")}}])}()},54556:(e,t,o)=>{"use strict";var n=o(12470).__,i=o(96784)(o(6379)),r=o(57135);e.exports=elementorModules.common.views.modal.Layout.extend({getModalOptions:function getModalOptions(){return{id:"elementor-new-template-modal"}},getLogoOptions:function getLogoOptions(){return{title:n("New Template","elementor")}},initialize:function initialize(){elementorModules.common.views.modal.Layout.prototype.initialize.apply(this,arguments);var e="elementor-new-template__form__",t="".concat(e,"template-type");this.showLogo(),this.showContentView(),this.initElements(),this.lockProBehavior=new i.default(this.elements),this.lockProBehavior.bindEvents();var o=function dynamicControlsVisibilityListener(){elementorAdmin.templateControls.setDynamicControlsVisibility(e,elementor_new_template_form_controls)};this.getModal().onShow=function(){o(),document.getElementById(t).addEventListener("change",o)},this.getModal().onHide=function(){document.getElementById(t).removeEventListener("change",o)}},initElements:function initElements(){var e=this.$el[0],t="#elementor-new-template__form";this.elements={form:e.querySelector(t),submitButton:e.querySelector("".concat(t,"__submit")),lockButton:e.querySelector("".concat(t,"__lock_button")),templateType:e.querySelector("".concat(t,"__template-type")),lockBadge:e.querySelector("".concat(t,"__template-type-badge")),lockBadgeText:e.querySelector("".concat(t,"__template-type-badge__text")),lockBadgeIcon:e.querySelector("".concat(t,"__template-type-badge__icon"))}},showContentView:function showContentView(){this.modalContent.show(new r)}})},57135:e=>{"use strict";e.exports=Marionette.ItemView.extend({id:"elementor-new-template-dialog-content",template:"#tmpl-elementor-new-template",ui:{},events:{},onRender:function onRender(){}})},12470:e=>{"use strict";e.exports=wp.i18n},39805:e=>{e.exports=function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},e.exports.__esModule=!0,e.exports.default=e.exports},40989:(e,t,o)=>{var n=o(45498);function _defineProperties(e,t){for(var o=0;o<t.length;o++){var i=t[o];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,n(i.key),i)}}e.exports=function _createClass(e,t,o){return t&&_defineProperties(e.prototype,t),o&&_defineProperties(e,o),Object.defineProperty(e,"prototype",{writable:!1}),e},e.exports.__esModule=!0,e.exports.default=e.exports},96784:e=>{e.exports=function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports},11327:(e,t,o)=>{var n=o(10564).default;e.exports=function toPrimitive(e,t){if("object"!=n(e)||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var i=o.call(e,t||"default");if("object"!=n(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)},e.exports.__esModule=!0,e.exports.default=e.exports},45498:(e,t,o)=>{var n=o(10564).default,i=o(11327);e.exports=function toPropertyKey(e){var t=i(e,"string");return"symbol"==n(t)?t:t+""},e.exports.__esModule=!0,e.exports.default=e.exports},10564:e=>{function _typeof(t){return e.exports=_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,_typeof(t)}e.exports=_typeof,e.exports.__esModule=!0,e.exports.default=e.exports}},t={};function __webpack_require__(o){var n=t[o];if(void 0!==n)return n.exports;var i=t[o]={exports:{}};return e[o](i,i.exports,__webpack_require__),i.exports}(()=>{"use strict";var e=__webpack_require__(54556),t=elementorModules.ViewModule.extend({getDefaultSettings:function getDefaultSettings(){return{selectors:{addButton:".page-title-action:first, #elementor-template-library-add-new"}}},getDefaultElements:function getDefaultElements(){var e=this.getSettings("selectors");return{$addButton:jQuery(e.addButton)}},bindEvents:function bindEvents(){this.elements.$addButton.on("click",this.onAddButtonClick),elementorCommon.elements.$window.on("hashchange",this.showModalByHash.bind(this))},showModalByHash:function showModalByHash(){"#add_new"===location.hash&&(this.layout.showModal(),location.hash="")},onInit:function onInit(){elementorModules.ViewModule.prototype.onInit.apply(this,arguments),this.layout=new e,this.showModalByHash()},onAddButtonClick:function onAddButtonClick(e){e.preventDefault(),this.layout.showModal()}});jQuery((function(){window.elementorNewTemplate=new t}))})()})();