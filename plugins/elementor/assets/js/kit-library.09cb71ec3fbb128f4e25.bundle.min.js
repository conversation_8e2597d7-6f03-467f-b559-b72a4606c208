/*! elementor - v3.29.0 - 04-06-2025 */
(self.webpackChunkelementor=self.webpackChunkelementor||[]).push([[435],{68123:()=>{},23487:()=>{},40915:()=>{},4669:()=>{},10671:()=>{},18897:()=>{},7451:()=>{},47749:()=>{},26425:()=>{},25613:()=>{},78873:()=>{},16719:()=>{},58662:()=>{},47453:()=>{},82101:()=>{},50512:()=>{},82477:()=>{},59955:()=>{},6121:()=>{},38832:(e,t,r)=>{"use strict";var n=r(62688),a=r(12470).__,i=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=ElementorLoading;var o=i(r(41594));function ElementorLoading(e){return o.default.createElement("div",{className:"elementor-loading"},o.default.createElement("div",{className:"elementor-loader-wrapper"},o.default.createElement("div",{className:"elementor-loader"},o.default.createElement("div",{className:"elementor-loader-boxes"},o.default.createElement("div",{className:"elementor-loader-box"}),o.default.createElement("div",{className:"elementor-loader-box"}),o.default.createElement("div",{className:"elementor-loader-box"}),o.default.createElement("div",{className:"elementor-loader-box"}))),o.default.createElement("div",{className:"elementor-loading-title"},e.loadingText)))}ElementorLoading.propTypes={loadingText:n.string},ElementorLoading.defaultProps={loadingText:a("Loading","elementor")}},34744:(e,t,r)=>{"use strict";var n=r(62688),a=r(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=PopoverDialog;var i=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=a(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&{}.hasOwnProperty.call(e,o)){var l=i?Object.getOwnPropertyDescriptor(e,o):null;l&&(l.get||l.set)?Object.defineProperty(n,o,l):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}(r(41594));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}function PopoverDialog(e){var t=e.targetRef,r=e.offsetTop,n=e.offsetLeft,a=e.wrapperClass,o=e.trigger,l=e.hideAfter,u=(0,i.useCallback)((function(e){var a=null==t?void 0:t.current;if(a&&e){var i=function showPopover(){e.style.display="block",e.setAttribute("aria-expanded",!0);var t=a.getBoundingClientRect(),i=e.getBoundingClientRect(),o=i.width-t.width;e.style.top=t.bottom+r+"px",e.style.left=t.left-o/2-n+"px",e.style.setProperty("--popover-arrow-offset-end",(i.width-16)/2+"px")},u=function hidePopover(){e.style.display="none",e.setAttribute("aria-expanded",!1)};"hover"===o?function handlePopoverHover(){var t=!0,r=null;a.addEventListener("mouseover",(function(){t=!0,i()})),a.addEventListener("mouseleave",(function(){r=setTimeout((function(){t&&"block"===e.style.display&&u()}),l)})),e.addEventListener("mouseover",(function(){t=!1,r&&(clearTimeout(r),r=null)})),e.addEventListener("mouseleave",(function(){r=setTimeout((function(){t&&"block"===e.style.display&&u()}),l),t=!0}))}():"click"===o&&function handlePopoverClick(){var t=!1;a.addEventListener("click",(function(e){e.preventDefault(),e.stopPropagation(),t?(u(),t=!1):(i(),t=!0)})),e.addEventListener("click",(function(e){e.stopPropagation()})),document.body.addEventListener("click",(function(){t&&(u(),t=!1)}))}()}}),[t]),c="e-app__popover";return a&&(c+=" "+a),i.default.createElement("div",{className:c,ref:u},e.children)}PopoverDialog.propTypes={targetRef:n.oneOfType([n.func,n.shape({current:n.any})]).isRequired,trigger:n.string,direction:n.string,offsetTop:n.oneOfType([n.string,n.number]),offsetLeft:n.oneOfType([n.string,n.number]),wrapperClass:n.string,children:n.any,hideAfter:n.number},PopoverDialog.defaultProps={direction:"bottom",trigger:"hover",offsetTop:10,offsetLeft:0,hideAfter:300}},67822:(e,t,r)=>{"use strict";var n=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function App(){return a.default.createElement("div",{className:"e-kit-library"},a.default.createElement(s.QueryClientProvider,{client:m},a.default.createElement(p.SettingsProvider,{value:elementorAppConfig["kit-library"]},a.default.createElement(c.LastFilterProvider,null,a.default.createElement(f.Router,null,a.default.createElement(o.default,{path:"/"}),a.default.createElement(i.default,{path:"/favorites"}),a.default.createElement(u.default,{path:"/preview/:id"}),a.default.createElement(l.default,{path:"/overview/:id"})))),elementorCommon.config.isElementorDebug&&a.default.createElement(d.ReactQueryDevtools,{initialIsOpen:!1})))};var a=n(r(41594)),i=n(r(94881)),o=n(r(8741)),l=n(r(96285)),u=n(r(53673)),c=r(75750),s=r(89994),d=r(6111),f=r(83040),p=r(43082),m=new s.QueryClient({defaultOptions:{queries:{refetchOnWindowFocus:!1,retry:!1,staleTime:18e5}}})},48840:(e,t,r)=>{"use strict";var n=r(12470).__,a=r(62688),i=r(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=ApplyKitDialog;var o=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=i(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&{}.hasOwnProperty.call(e,o)){var l=a?Object.getOwnPropertyDescriptor(e,o):null;l&&(l.get||l.set)?Object.defineProperty(n,o,l):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}(r(41594)),l=r(83040),u=r(57401);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}function ApplyKitDialog(e){var t=(0,l.useNavigate)(),r=(0,o.useCallback)((function(){var r=arguments.length>0&&void 0!==arguments[0]&&arguments[0],n="/import/process"+"?id=".concat(e.id)+"&file_url=".concat(encodeURIComponent(e.downloadLink))+"&nonce=".concat(e.nonce,"&referrer=kit-library");r&&(n+="&action_type=apply-all"),t(n)}),[e.downloadLink,e.nonce]);return o.default.createElement(u.Dialog,{title:n("Apply %s?","elementor").replace("%s",e.title),text:o.default.createElement(o.default.Fragment,null,n("You can use everything in this kit, or Customize to only include some items.","elementor"),o.default.createElement("br",null),o.default.createElement("br",null),n("By applying the entire kit, you'll override any styles, settings or content already on your site.","elementor")),approveButtonText:n("Apply All","elementor"),approveButtonColor:"primary",approveButtonOnClick:function approveButtonOnClick(){return r(!0)},dismissButtonText:n("Customize","elementor"),dismissButtonOnClick:function dismissButtonOnClick(){return r(!1)},onClose:e.onClose})}ApplyKitDialog.propTypes={id:a.string.isRequired,downloadLink:a.string.isRequired,nonce:a.string.isRequired,onClose:a.func.isRequired,title:a.string},ApplyKitDialog.defaultProps={title:"Kit"}},89249:(e,t,r)=>{"use strict";var n=r(62688),a=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=Badge;var i=a(r(41594));function Badge(e){return i.default.createElement("span",{className:"eps-badge eps-badge--".concat(e.variant," ").concat(e.className),style:e.style},e.children)}r(68123),Badge.propTypes={children:n.node,className:n.string,style:n.object,variant:n.oneOf(["sm","md"])},Badge.defaultProps={className:"",style:{},variant:"md"}},79891:(e,t,r)=>{"use strict";var n=r(62688),a=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=Collapse;var i=a(r(41594));function Collapse(e){return i.default.createElement("div",{className:"eps-collapse ".concat(e.className),"data-open":e.isOpen||void 0},i.default.createElement("button",{className:"eps-collapse__title",onClick:function onClick(){var t;e.onChange((function(e){return!e})),null===(t=e.onClick)||void 0===t||t.call(e,e.isOpen,e.title)}},i.default.createElement("span",null,e.title),i.default.createElement("i",{className:"eicon-chevron-right eps-collapse__icon"})),i.default.createElement("div",{className:"eps-collapse__content"},e.children))}r(23487),Collapse.propTypes={isOpen:n.bool,onChange:n.func,className:n.string,title:n.node,onClick:n.func,children:n.oneOfType([n.node,n.arrayOf(n.node)])},Collapse.defaultProps={className:"",isOpen:!1}},93941:(e,t,r)=>{"use strict";var n=r(12470).__,a=r(62688),i=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=ConnectDialog;var o=i(r(41594)),l=r(57401),u=r(43082),c=o.default,s=c.useEffect,d=c.useRef;function ConnectDialog(e){var t=(0,u.useSettingsContext)().settings,r=d();return s((function(){jQuery(r.current).elementorConnect({success:function success(t,r){return e.onSuccess(r)},error:function error(){return e.onError(n("Unable to connect","elementor"))},parseUrl:function parseUrl(t){return t.replace("%%page%%",e.pageId)}})}),[]),o.default.createElement(l.Dialog,{title:n("Connect to Template Library","elementor"),text:n("Access this template and our entire library by creating a free personal account","elementor"),approveButtonText:n("Get Started","elementor"),approveButtonUrl:t.library_connect_url,approveButtonOnClick:function approveButtonOnClick(){return e.onClose()},approveButtonColor:"primary",approveButtonRef:r,dismissButtonText:n("Cancel","elementor"),dismissButtonOnClick:function dismissButtonOnClick(){return e.onClose()},onClose:function onClose(){return e.onClose()}})}ConnectDialog.propTypes={onClose:a.func.isRequired,onError:a.func.isRequired,onSuccess:a.func.isRequired,pageId:a.string}},84051:(e,t,r)=>{"use strict";var n=r(12470).__,a=r(62688),i=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=EnvatoPromotion;var o=i(r(41594)),l=r(57401),u=r(3073);function EnvatoPromotion(e){return o.default.createElement(l.Text,{className:"e-kit-library-promotion",variant:"xl"},n("Looking for more Kits?","elementor")," "," ",o.default.createElement(l.Button,{variant:"underlined",color:"link",url:"https://go.elementor.com/app-envato-kits/",target:"_blank",rel:"noreferrer",text:n("Check out Elementor Template Kits on ThemeForest","elementor"),onClick:function onClick(){return function eventTracking(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"click";(0,u.appsEventTrackingDispatch)(t,{page_source:"home page",element_position:"library_bottom_promotion",category:e.category&&("/favorites"===e.category?"favorites":"all kits"),event_type:r})}("kit-library/check-kits-on-theme-forest")}}))}r(40915),EnvatoPromotion.propTypes={category:a.string}},32965:(e,t,r)=>{"use strict";var n=r(62688),a=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=ErrorScreen;var i=a(r(41594)),o=r(57401),l=r(3073);function ErrorScreen(e){return i.default.createElement(o.Grid,{container:!0,alignItems:"center",justify:"center",direction:"column",className:"e-kit-library__error-screen"},i.default.createElement("img",{src:"".concat(elementorAppConfig.assets_url,"images/no-search-results.svg")}),i.default.createElement(o.Heading,{tag:"h3",variant:"display-1",className:"e-kit-library__error-screen-title"},e.title),i.default.createElement(o.Text,{variant:"xl",className:"e-kit-library__error-screen-description"},e.description," "," ",i.default.createElement(o.Button,{text:e.button.text,color:"link",onClick:function onClick(){(0,l.appsEventTrackingDispatch)("kit-library/go-back-to-view-kits",{page_source:"home page",element_position:"empty state",category:e.button.category&&("/favorites"===e.button.category?"favorites":"all")}),e.button.action()},url:e.button.url,target:e.button.target})))}r(4669),ErrorScreen.propTypes={title:n.string,description:n.string,button:n.shape({text:n.string,action:n.func,url:n.string,target:n.string,category:n.string})}},25265:(e,t,r)=>{"use strict";var n=r(12470).__,a=r(62688),i=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=FavoritesActions;var o=i(r(41594)),l=r(83321),u=r(57401),c=r(3073);function FavoritesActions(e){var t=(0,l.useKitFavoritesMutations)(),r=t.addToFavorites,a=t.removeFromFavorites,i=t.isLoading,s=i?"e-kit-library__kit-favorite-actions--loading":"",d=function eventTracking(e,t,r){var n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null;(0,c.appsEventTrackingDispatch)("kit-library/favorite-icon",{grid_location:n,search_term:a,kit_name:e,page_source:t&&("/"===t?"home page":"overview"),element_location:t&&"overview"===t?"app_sidebar":null,action:r})};return e.isFavorite?o.default.createElement(u.Button,{text:n("Remove from Favorites","elementor"),hideText:!0,icon:"eicon-heart",className:"e-kit-library__kit-favorite-actions e-kit-library__kit-favorite-actions--active ".concat(s),onClick:function onClick(){!i&&a.mutate(e.id),d(null==e?void 0:e.name,null==e?void 0:e.source,"uncheck")}}):o.default.createElement(u.Button,{text:n("Add to Favorites","elementor"),hideText:!0,icon:"eicon-heart-o",className:"e-kit-library__kit-favorite-actions ".concat(s),onClick:function onClick(){!i&&r.mutate(e.id),d(null==e?void 0:e.name,null==e?void 0:e.source,"check",null==e?void 0:e.index,null==e?void 0:e.queryParams)}})}r(10671),FavoritesActions.propTypes={isFavorite:a.bool,id:a.string,name:a.string,source:a.string,index:a.number,queryParams:a.string}},2869:(e,t,r)=>{"use strict";var n=r(12470).__,a=r(62688),i=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=FilterIndicationText;var o=i(r(41594)),l=i(r(59979)),u=i(r(89249)),c=r(12470),s=r(57401),d=r(3073);r(18897);var f=r(31627);function FilterIndicationText(e){var t=(0,l.default)(e.queryParams.taxonomies),r=function eventTracking(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"click";(0,d.appsEventTrackingDispatch)("kit-library/clear-filter",{tag:e,page_source:"home page",event_type:t})};return o.default.createElement(s.Grid,{container:!0,className:"e-kit-library__filter-indication"},o.default.createElement(s.Text,{className:"e-kit-library__filter-indication-text"},(0,c.sprintf)((0,c._n)("Showing %s result for","Showing %s results for",e.resultCount,"elementor"),e.resultCount?e.resultCount:n("no","elementor"))," ",e.queryParams.search&&'"'.concat(e.queryParams.search,'"')," ",t.length>0&&o.default.createElement(o.default.Fragment,null,t.map((function(t){return o.default.createElement(u.default,{key:t,className:"e-kit-library__filter-indication-badge"},f.NewPlanTexts[t]||t,o.default.createElement(s.Button,{text:n("Remove","elementor"),hideText:!0,icon:"eicon-editor-close",className:"e-kit-library__filter-indication-badge-remove",onClick:function onClick(){r(t),e.onRemoveTag(t)}}))})))),o.default.createElement(s.Button,{className:"e-kit-library__filter-indication-button",text:n("Clear all","elementor"),variant:"underlined",onClick:function onClick(){r("all"),e.onClear()}}))}FilterIndicationText.propTypes={queryParams:a.shape({search:a.string,taxonomies:a.objectOf(a.arrayOf(a.string)),favorite:a.bool}),resultCount:a.number.isRequired,onClear:a.func.isRequired,onRemoveTag:a.func.isRequired}},91289:(e,t,r)=>{"use strict";var n=r(12470).__,a=r(62688),i=r(96784),o=r(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=ItemHeader;var l=_interopRequireWildcard(r(41594)),u=i(r(78304)),c=i(r(10906)),s=i(r(18821)),d=i(r(81123)),f=i(r(93941)),p=i(r(80876)),m=i(r(28299)),v=i(r(41084)),y=i(r(80143)),_=_interopRequireWildcard(r(50474)),g=i(r(93639)),b=r(57401),h=r(43082),k=r(3073),C=r(87437);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=o(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&{}.hasOwnProperty.call(e,i)){var l=a?Object.getOwnPropertyDescriptor(e,i):null;l&&(l.get||l.set)?Object.defineProperty(n,i,l):n[i]=e[i]}return n.default=e,r&&r.set(e,n),n}function ItemHeader(e){var t=(0,h.useSettingsContext)().updateSettings,r=(0,l.useState)(!1),a=(0,s.default)(r,2),i=a[0],o=a[1],v=(0,l.useState)(null),O=(0,s.default)(v,2),P=O[0],w=O[1],E=(0,l.useState)(!1),T=(0,s.default)(E,2),j=T[0],x=T[1],R={kitName:e.model.title,pageId:e.pageId},S=(0,y.default)(e.model,{onSuccess:function onSuccess(e){var t=e.data;return w(t)},onError:function onError(e){if(401===e.code)return elementorCommon.config.library_connect.is_connected=!1,elementorCommon.config.library_connect.current_access_level=0,elementorCommon.config.library_connect.current_access_tier=C.TIERS.free,t({is_library_connected:!1,access_level:0,access_tier:C.TIERS.free}),void o(!0);x({code:e.code,message:n("Something went wrong.","elementor")})}}),M=S.mutate,N=S.isLoading,W=function useKitCallToActionButton(e,t){var r=t.apply,a=t.isApplyLoading,i=t.onConnect,o=t.onClick,u=(0,_.default)(e.accessTier),c=u.type,s=u.subscriptionPlan,d=(0,g.default)(s.promotion_url,e.id,e.title),f=(0,h.useSettingsContext)().settings;return(0,l.useMemo)((function(){return c===_.TYPE_CONNECT?{id:"connect",text:n("Apply Kit","elementor"),hideText:!1,variant:"contained",color:"primary",size:"sm",onClick:function onClick(e){i(e),null==o||o(e)},includeHeaderBtnClass:!1}:c===_.TYPE_PROMOTION&&s?{id:"promotion",text:f.is_pro?"Upgrade":"Go ".concat(s.label),hideText:!1,variant:"contained",color:"cta",size:"sm",url:d,target:"_blank",includeHeaderBtnClass:!1}:{id:"apply",text:n("Apply Kit","elementor"),className:"e-kit-library__apply-button",icon:a?"eicon-loading eicon-animation-spin":"",hideText:!1,variant:"contained",color:a?"disabled":"primary",size:"sm",onClick:function onClick(e){a||r(e),null==o||o(e)},includeHeaderBtnClass:!1}}),[c,s,a,r])}(e.model,{onConnect:function onConnect(){return o(!0)},apply:M,isApplyLoading:N,onClick:function onClick(){return(0,k.appsEventTrackingDispatch)("kit-library/apply-kit",{kit_name:e.model.title,element_position:"app_header",page_source:e.pageId,event_type:"click"})}}),q=(0,l.useMemo)((function(){return[W].concat((0,c.default)(e.buttons))}),[e.buttons,W]);return l.default.createElement(l.default.Fragment,null,j&&l.default.createElement(b.Dialog,{title:j.message,text:n("Go to the pages screen to make sure your kit pages have been imported successfully. If not, try again.","elementor"),approveButtonText:n("Go to pages","elementor"),approveButtonColor:"primary",approveButtonUrl:elementorAppConfig.admin_url+"edit.php?post_type=page",approveButtonOnClick:function approveButtonOnClick(){return x(!1)},dismissButtonText:n("Got it","elementor"),dismissButtonOnClick:function dismissButtonOnClick(){return x(!1)},onClose:function onClose(){return x(!1)}}),P&&l.default.createElement(d.default,{id:e.model.id,downloadLinkData:P,onClose:function onClose(){return w(null)}}),i&&l.default.createElement(f.default,{pageId:e.pageId,onClose:function onClose(){return o(!1)},onSuccess:function onSuccess(r){var n=r.kits_access_level||r.access_level||0,a=r.access_tier;elementorCommon.config.library_connect.is_connected=!0,elementorCommon.config.library_connect.current_access_level=n,elementorCommon.config.library_connect.current_access_tier=a,t({is_library_connected:!0,access_level:n,access_tier:a}),r.access_level<e.model.accessLevel||(0,C.isTierAtLeast)(a,e.model.accessTier)&&M()},onError:function onError(e){return x({message:e})}}),l.default.createElement(p.default,(0,u.default)({startColumn:l.default.createElement(m.default,R),centerColumn:e.centerColumn,buttons:q},R)))}r(7451),ItemHeader.propTypes={model:a.instanceOf(v.default).isRequired,centerColumn:a.node,buttons:a.arrayOf(a.object),pageId:a.string}},61670:(e,t,r)=>{"use strict";var n=r(12470).__,a=r(62688),i=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=KitAlreadyAppliedDialog;var o=i(r(41594)),l=r(57401);function KitAlreadyAppliedDialog(e){return o.default.createElement(l.Dialog,{title:n("You've already applied a Kit.","elementor"),text:o.default.createElement(o.default.Fragment,null,n("Applying two Kits on the same website will mix global styles and colors and hurt your site's performance.","elementor"),o.default.createElement("br",null),o.default.createElement("br",null),n("Remove the existing Kit before applying a new one.","elementor")),approveButtonText:n("Remove existing Kit","elementor"),approveButtonColor:"primary",approveButtonOnClick:function approveButtonOnClick(){return location.href=function getRemoveKitUrl(){var t=elementorAppConfig["import-export"].tools_url,r=new URL(t);return r.searchParams.append("referrer_kit",e.id),r.hash="tab-import-export-kit",r.toString()}()},dismissButtonText:n("Apply anyway","elementor"),dismissButtonOnClick:e.dismissButtonOnClick,onClose:e.onClose})}KitAlreadyAppliedDialog.propTypes={id:a.string.isRequired,dismissButtonOnClick:a.func.isRequired,onClose:a.func.isRequired}},81123:(e,t,r)=>{"use strict";var n=r(62688),a=r(96784),i=r(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=KitDialog;var o=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=i(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&{}.hasOwnProperty.call(e,o)){var l=a?Object.getOwnPropertyDescriptor(e,o):null;l&&(l.get||l.set)?Object.defineProperty(n,o,l):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}(r(41594)),l=a(r(18821)),u=a(r(61670)),c=a(r(48840));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}function KitDialog(e){var t=(0,o.useState)(!1),r=(0,l.default)(t,2),n=r[0],a=r[1];return!!elementorAppConfig["import-export"].lastImportedSession.session_id&&!n?o.default.createElement(u.default,{id:e.id,dismissButtonOnClick:function dismissButtonOnClick(){return a(!0)},onClose:e.onClose}):o.default.createElement(c.default,{id:e.id,downloadLink:e.downloadLinkData.data.download_link,nonce:e.downloadLinkData.meta.nonce,onClose:e.onClose})}KitDialog.propTypes={id:n.string.isRequired,downloadLinkData:n.object.isRequired,onClose:n.func.isRequired}},29883:(e,t,r)=>{"use strict";var n=r(62688),a=r(96784),i=r(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=a(r(41594)),l=a(r(89249)),u=a(r(25265)),c=a(r(41084)),s=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=i(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&{}.hasOwnProperty.call(e,o)){var l=a?Object.getOwnPropertyDescriptor(e,o):null;l&&(l.get||l.set)?Object.defineProperty(n,o,l):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}(r(50474)),d=a(r(93639)),f=r(57401),p=r(3073),m=r(12470);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}r(47749);var v=function KitListItem(e){var t=(0,s.default)(e.model.accessTier),r=t.type,n=t.subscriptionPlan,a=(0,d.default)(n.promotion_url,e.model.id,e.model.title),i=(0,m.__)("Upgrade","elementor"),c=s.TYPE_PROMOTION===r;return o.default.createElement(f.Card,{className:"e-kit-library__kit-item"},o.default.createElement(f.CardHeader,null,o.default.createElement(f.Heading,{tag:"h3",title:e.model.title,variant:"h5",className:"eps-card__headline"},e.model.title),o.default.createElement(u.default,{id:e.model.id,isFavorite:e.model.isFavorite,index:e.index,name:e.model.title,queryParams:e.queryParams,source:e.source})),o.default.createElement(f.CardBody,null,o.default.createElement(f.CardImage,{alt:e.model.title,src:e.model.thumbnailUrl||""},o.default.createElement(l.default,{variant:"sm",className:"e-kit-library__kit-item-subscription-plan-badge ".concat(n.isPromoted?"promoted":"")},n.label),o.default.createElement(f.CardOverlay,null,o.default.createElement(f.Grid,{container:!0,direction:"column",className:"e-kit-library__kit-item-overlay"},o.default.createElement(f.Button,{className:"e-kit-library__kit-item-overlay-overview-button",text:(0,m.__)("View Demo","elementor"),icon:"eicon-preview-medium",url:"/kit-library/preview/".concat(e.model.id),onClick:function onClick(){return function eventTracking(t){(0,p.appsEventTrackingDispatch)(t,{kit_name:e.model.title,grid_location:e.index,search_term:e.queryParams,page_source:e.source&&"/"===e.source?"all kits":"favorites"})}("kit-library/check-out-kit")}}),c&&o.default.createElement(f.Button,{className:"e-kit-library__kit-item-overlay-promotion-button",text:i,icon:"eicon-external-link-square",url:a,target:"_blank"}))))))};v.propTypes={model:n.instanceOf(c.default).isRequired,index:n.number,queryParams:n.string,source:n.string};t.default=o.default.memo(v)},68843:(e,t,r)=>{"use strict";var n=r(62688),a=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=KitList;var i=a(r(41594)),o=r(83040),l=a(r(41084)),u=a(r(29883)),c=a(r(8203)),s=r(57401);function KitList(e){var t,r=(0,o.useLocation)(),n=new URLSearchParams(null===(t=r.pathname.split("?"))||void 0===t?void 0:t[1]).get("referrer");return i.default.createElement(s.CssGrid,{spacing:24,colMinWidth:290},"onboarding"===n&&i.default.createElement(c.default,null),e.data.map((function(t,r){var n;return i.default.createElement(u.default,{key:t.id,model:t,index:r+1,queryParams:null===(n=e.queryParams)||void 0===n?void 0:n.search,source:e.source})})))}KitList.propTypes={data:n.arrayOf(n.instanceOf(l.default)),queryParams:n.shape({search:n.string}),source:n.string}},28299:(e,t,r)=>{"use strict";var n=r(12470).__,a=r(62688),i=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=HeaderBackButton;var o=i(r(41594)),l=r(57401),u=r(75750),c=r(83040),s=r(3073);function HeaderBackButton(e){var t=(0,c.useNavigate)(),r=(0,u.useLastFilterContext)().lastFilter;return o.default.createElement("div",{className:"e-kit-library__header-back-container"},o.default.createElement(l.Button,{className:"e-kit-library__header-back",icon:"eicon-chevron-left",text:n("Back to Library","elementor"),onClick:function onClick(){!function eventTracking(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"click";(0,s.appsEventTrackingDispatch)(t,{page_source:e.pageId,kit_name:e.kitName,element_position:"app_header",event_type:r})}("kit-library/back-to-library"),t(wp.url.addQueryArgs("/kit-library",r))}}))}r(26425),HeaderBackButton.propTypes={pageId:a.string.isRequired,kitName:a.string.isRequired}},80876:(e,t,r)=>{"use strict";var n=r(12470).__,a=r(62688),i=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=Header;var o=i(r(41594)),l=r(57401),u=i(r(6056)),c=r(3073);function Header(e){var t=function eventTracking(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"home page",r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"click";return(0,c.appsEventTrackingDispatch)(e,{page_source:t,element_position:"app_header",kit_name:r,event_type:n})};return o.default.createElement(l.Grid,{container:!0,alignItems:"center",justify:"space-between",className:"eps-app__header"},e.startColumn||o.default.createElement("a",{className:"eps-app__logo-title-wrapper",href:"#/kit-library",onClick:function onClick(){return t("kit-library/logo")}},o.default.createElement("i",{className:"eps-app__logo eicon-elementor"}),o.default.createElement("h1",{className:"eps-app__title"},n("Kit Library","elementor"))),e.centerColumn||o.default.createElement("span",null),e.endColumn||o.default.createElement("div",{style:{flex:1}},o.default.createElement(u.default,{buttons:e.buttons,onClose:function onClose(){t("kit-library/close",null==e?void 0:e.pageId,null==e?void 0:e.kitName),window.top.location=elementorAppConfig.admin_url}})))}Header.propTypes={startColumn:a.node,endColumn:a.node,centerColumn:a.node,buttons:a.arrayOf(a.object),kitName:a.string,pageId:a.string}},86845:(e,t,r)=>{"use strict";var n=r(62688),a=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=Index;var i=a(r(41594)),o=a(r(24017));function Index(e){return i.default.createElement("div",{className:"eps-app__lightbox"},i.default.createElement("div",{className:"eps-app"},e.header,i.default.createElement("div",{className:"eps-app__main"},e.sidebar&&i.default.createElement(o.default,null,e.sidebar),e.children)))}Index.propTypes={header:n.node,sidebar:n.node,children:n.node}},3471:(e,t,r)=>{"use strict";var n=r(62688),a=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=PageLoader;var i=a(r(41594)),o=r(57401);function PageLoader(e){return i.default.createElement("div",{className:"e-kit-library__page-loader ".concat(e.className)},i.default.createElement(o.Icon,{className:"eicon-loading eicon-animation-spin"}))}r(25613),PageLoader.propTypes={className:n.string},PageLoader.defaultProps={className:""}},78013:(e,t,r)=>{"use strict";var n=r(12470).__,a=r(62688),i=r(96784),o=r(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=SearchInput;var l=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=o(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&{}.hasOwnProperty.call(e,i)){var l=a?Object.getOwnPropertyDescriptor(e,i):null;l&&(l.get||l.set)?Object.defineProperty(n,i,l):n[i]=e[i]}return n.default=e,r&&r.set(e,n),n}(r(41594)),u=i(r(18821)),c=i(r(89889)),s=r(57401);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}function SearchInput(e){var t=(0,l.useState)(e.value||""),r=(0,u.default)(t,2),a=r[0],i=r[1],o=(0,c.default)((function(t){return e.onChange(t)}),e.debounceTimeout);return(0,l.useEffect)((function(){e.value!==a&&i(e.value)}),[e.value]),l.default.createElement("div",{className:"eps-search-input__container ".concat(e.className)},l.default.createElement("input",{className:"eps-search-input eps-search-input--".concat(e.size),placeholder:e.placeholder,value:a,onChange:function onChange(e){i(e.target.value),o(e.target.value)}}),l.default.createElement(s.Icon,{className:"eicon-search-bold eps-search-input__icon eps-search-input__icon--".concat(e.size)}),e.value&&l.default.createElement(s.Button,{text:n("Clear","elementor"),hideText:!0,className:"eicon-close-circle eps-search-input__clear-icon eps-search-input__clear-icon--".concat(e.size),onClick:function onClick(){return e.onChange("")}}))}r(78873),SearchInput.propTypes={placeholder:a.string,value:a.string.isRequired,onChange:a.func.isRequired,className:a.string,size:a.oneOf(["md","sm"]),debounceTimeout:a.number},SearchInput.defaultProps={className:"",size:"md",debounceTimeout:300}},14025:(e,t,r)=>{"use strict";var n=r(12470).__,a=r(62688),i=r(96784),o=r(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=SortSelect;var l=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=o(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&{}.hasOwnProperty.call(e,i)){var l=a?Object.getOwnPropertyDescriptor(e,i):null;l&&(l.get||l.set)?Object.defineProperty(n,i,l):n[i]=e[i]}return n.default=e,r&&r.set(e,n),n}(r(41594)),u=i(r(18821)),c=r(57401);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}function SortSelect(e){var t=function getSelectedOptionDetails(t){return e.options.find((function(e){return e.value===t}))},r=(0,l.useState)(t(e.value.by)),a=(0,u.default)(r,2),i=a[0],o=a[1];return(0,l.useEffect)((function(){var t;e.onChange({by:i.value,direction:null!==(t=i.defaultOrder)&&void 0!==t?t:e.value.direction})}),[i]),l.default.createElement("div",{className:"eps-sort-select"},l.default.createElement("div",{className:"eps-sort-select__select-wrapper"},l.default.createElement(c.Select,{options:e.options,value:e.value.by,onChange:function onChange(r){var n,a=r.target.value;o(t(a)),null===(n=e.onChangeSortValue)||void 0===n||n.call(e,a)},className:"eps-sort-select__select",onClick:function onClick(){var t;e.onChange({by:e.value.by,direction:e.value.direction}),null===(t=e.onSortSelectOpen)||void 0===t||t.call(e)}})),!i.orderDisabled&&l.default.createElement(c.Button,{text:"asc"===e.value.direction?n("Sort Descending","elementor"):n("Sort Ascending","elementor"),hideText:!0,icon:"asc"===e.value.direction?"eicon-arrow-up":"eicon-arrow-down",className:"eps-sort-select__button",onClick:function onClick(){var t=e.value.direction&&"asc"===e.value.direction?"desc":"asc";e.onChangeSortDirection&&e.onChangeSortDirection(t),e.onChange({by:e.value.by,direction:t})}}))}r(16719),SortSelect.propTypes={options:a.arrayOf(a.shape({label:a.string.isRequired,value:a.oneOfType([a.string,a.number]).isRequired})).isRequired,value:a.shape({direction:a.oneOf(["asc","desc"]).isRequired,by:a.string.isRequired}).isRequired,onChange:a.func.isRequired,onChangeSortValue:a.func,onSortSelectOpen:a.func,onChangeSortDirection:a.func}},42523:(e,t,r)=>{"use strict";var n=r(12470).__,a=r(62688),i=r(96784),o=r(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var l=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=o(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&{}.hasOwnProperty.call(e,i)){var l=a?Object.getOwnPropertyDescriptor(e,i):null;l&&(l.get||l.set)?Object.defineProperty(n,i,l):n[i]=e[i]}return n.default=e,r&&r.set(e,n),n}(r(41594)),u=i(r(10906)),c=i(r(18821)),s=i(r(68145)),d=i(r(79891)),f=i(r(78013)),p=r(57401),m=r(12470),v=r(3073);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}var y=function TaxonomiesFilterList(e){var t=(0,l.useState)(e.taxonomiesByType.isOpenByDefault),r=(0,c.default)(t,2),a=r[0],i=r[1],o=(0,l.useState)(""),s=(0,c.default)(o,2),y=s[0],_=s[1],g=(0,l.useMemo)((function(){if(!y)return e.taxonomiesByType.data;var t=y.toLowerCase();return e.taxonomiesByType.data.filter((function(e){return e.text.toLowerCase().includes(t)}))}),[e.taxonomiesByType.data,y]);return l.default.createElement(d.default,{className:"e-kit-library__tags-filter-list",title:e.taxonomiesByType.label,isOpen:a,onChange:i,onClick:function onClick(t,r){var n;null===(n=e.onCollapseChange)||void 0===n||n.call(e,t,r)}},e.taxonomiesByType.data.length>=15&&l.default.createElement(f.default,{size:"sm",className:"e-kit-library__tags-filter-list-search",placeholder:(0,m.sprintf)(n("Search %s...","elementor"),e.taxonomiesByType.label),value:y,onChange:function onChange(t){var r;(_(t),t)&&(null===(r=e.onChange)||void 0===r||r.call(e,t))}}),l.default.createElement("div",{className:"e-kit-library__tags-filter-list-container"},0===g.length&&l.default.createElement(p.Text,null,n("No Results Found","elementor")),g.map((function(t){var r;return l.default.createElement("label",{key:t.text,className:"e-kit-library__tags-filter-list-item"},l.default.createElement(p.Checkbox,{checked:!(null===(r=e.selected[t.type])||void 0===r||!r.includes(t.id||t.text)),onChange:function onChange(r){var n=r.target.checked;!function eventTracking(t,r,n,a){var i=e.category&&("/favorites"===e.category?"favorites":"all kits");(0,v.appsEventTrackingDispatch)(t,{page_source:"home page",element_location:"app_sidebar",category:i,section:r,item:a,action:n?"checked":"unchecked"})}("kit-library/filter",t.type,n,t.text),e.onSelect(t.type,(function(e){return n?[].concat((0,u.default)(e),[t.id||t.text]):e.filter((function(e){return![t.id,t.text].includes(e)}))}))}}),t.text)}))))};y.propTypes={taxonomiesByType:a.shape({key:a.string,label:a.string,data:a.arrayOf(a.instanceOf(s.default)),isOpenByDefault:a.bool}),selected:a.objectOf(a.arrayOf(a.string)),onSelect:a.func,onCollapseChange:a.func,category:a.string,onChange:a.func};t.default=l.default.memo(y)},33554:(e,t,r)=>{"use strict";var n=r(62688),a=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=TaxonomiesFilter;var i=a(r(41594)),o=a(r(42523)),l=a(r(68145)),u=r(31627),c=r(3073);r(58662);var s=i.default.useMemo;function TaxonomiesFilter(e){var t=s((function(){return(0,u.getTaxonomyFilterItems)(e.taxonomies)}),[e.taxonomies]),r=function eventTracking(t,r,n){var a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"click";return(0,c.appsEventTrackingDispatch)(t,{page_source:"home page",element_location:"app_sidebar",category:e.category&&("/favorites"===e.category?"favorites":"all kits"),section:n,search_term:r,event_type:a})};return i.default.createElement("div",{className:"e-kit-library__tags-filter"},t.map((function(t){return i.default.createElement(o.default,{key:t.key,taxonomiesByType:t,selected:e.selected,onSelect:e.onSelect,onCollapseChange:function onCollapseChange(e,t){r(e?"kit-library/collapse":"kit-library/expand",null,t)},onChange:function onChange(e){r("kit-library/filter",e,t.label,"search")},category:e.category})})))}TaxonomiesFilter.propTypes={selected:n.objectOf(n.arrayOf(n.string)),onSelect:n.func,taxonomies:n.arrayOf(n.instanceOf(l.default)),category:n.string}},75750:(e,t,r)=>{"use strict";var n=r(62688),a=r(96784),i=r(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.LastFilterProvider=LastFilterProvider,t.useLastFilterContext=function useLastFilterContext(){return(0,o.useContext)(u)};var o=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=i(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&{}.hasOwnProperty.call(e,o)){var l=a?Object.getOwnPropertyDescriptor(e,o):null;l&&(l.get||l.set)?Object.defineProperty(n,o,l):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}(r(41594)),l=a(r(18821));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}var u=(0,o.createContext)({});function LastFilterProvider(e){var t=(0,o.useState)({}),r=(0,l.default)(t,2),n=r[0],a=r[1];return o.default.createElement(u.Provider,{value:{lastFilter:n,setLastFilter:a}},e.children)}LastFilterProvider.propTypes={children:n.any}},43082:(e,t,r)=>{"use strict";var n=r(62688),a=r(96784),i=r(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.SettingsProvider=SettingsProvider,t.useSettingsContext=function useSettingsContext(){return(0,o.useContext)(c)};var o=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=i(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&{}.hasOwnProperty.call(e,o)){var l=a?Object.getOwnPropertyDescriptor(e,o):null;l&&(l.get||l.set)?Object.defineProperty(n,o,l):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}(r(41594)),l=a(r(85707)),u=a(r(18821));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}function ownKeys(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(r),!0).forEach((function(t){(0,l.default)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ownKeys(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var c=(0,o.createContext)({});function SettingsProvider(e){var t=(0,o.useState)({}),r=(0,u.default)(t,2),n=r[0],a=r[1],i=(0,o.useCallback)((function(e){a((function(t){return _objectSpread(_objectSpread({},t),e)}))}),[a]);return(0,o.useEffect)((function(){a(e.value)}),[a]),o.default.createElement(c.Provider,{value:{settings:n,setSettings:a,updateSettings:i}},e.children)}SettingsProvider.propTypes={children:n.any,value:n.object.isRequired}},93639:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function useAddKitPromotionUTM(e,t,r){if(!e)return"";var n;try{n=new URL(e)}catch(e){return""}if(r&&"string"==typeof r){var a=r.trim().replace(/\s+/g,"-").replace(/[^\w-]/g,"").toLowerCase();n.searchParams.set("utm_term",a)}t&&"string"==typeof t&&n.searchParams.set("utm_content",t);return n.toString()}},91437:(e,t,r)=>{"use strict";var n=r(12470).__,a=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.KEY=void 0,t.default=function useContentTypes(){var e=(0,l.useSettingsContext)().settings;return(0,o.useQuery)([c,e],(function(){return function fetchContentTypes(e){var t=[{id:"page",label:n("Pages","elementor"),doc_types:["wp-page"],order:0},{id:"site-parts",label:n("Site Parts","elementor"),doc_types:["archive","error-404","footer","header","search-results","single-page","single-post","product","product-archive","404","single"],order:1}],r=e.access_tier,a=e.is_pro&&e.is_library_connected;a&&r===u.TIERS.free&&(r=u.TIERS["essential-oct2023"]);var o=u.TIERS["essential-oct2023"];(0,u.isTierAtLeast)(r,o)&&t.push({id:"popup",label:n("Popups","elementor"),doc_types:["popup"],order:2});return Promise.resolve(t).then((function(e){return e.map((function(e){return i.default.createFromResponse(e)}))}))}(e)}))};var i=a(r(55942)),o=r(89994),l=r(43082),u=r(87437),c=t.KEY="content-types"},89889:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function useDebouncedCallback(e,t){var r=(0,n.useRef)();return(0,n.useCallback)((function(){for(var n=arguments.length,a=new Array(n),i=0;i<n;i++)a[i]=arguments[i];clearTimeout(r.current),r.current=setTimeout((function later(){clearTimeout(r.current),e.apply(void 0,a)}),t)}),[e,t])};var n=r(41594)},80143:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function useDownloadLinkMutation(e,t){var r=t.onError,i=t.onSuccess,o=(0,n.useCallback)((function(){return $e.data.get("kits/download-link",{id:e.id},{refresh:!0})}),[e]);return(0,a.useMutation)(o,{onSuccess:i,onError:r})};var n=r(41594),a=r(89994)},50474:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.TYPE_PROMOTION=t.TYPE_CONNECT=t.TYPE_APPLY=void 0,t.default=function useKitCallToAction(e){var t=(0,a.useSettingsContext)().settings,r=t.access_tier,s=o.TierToKeyMap[e];t.is_pro&&t.is_library_connected&&r===i.TIERS.free&&(r=i.TIERS["essential-oct2023"]);var d=(0,n.useMemo)((function(){var r;return null===(r=t.subscription_plans)||void 0===r?void 0:r[e]}),[t,e]);return d.label=o.PromotionChipText[s],d.isPromoted=i.TIERS.free!==e,{type:(0,n.useMemo)((function(){var n=(0,i.isTierAtLeast)(r,e);return t.is_library_connected||!t.is_pro&&!n?n?c:u:l}),[t,e]),subscriptionPlan:d}};var n=r(41594),a=r(43082),i=r(87437),o=r(31627),l=t.TYPE_CONNECT="connect",u=t.TYPE_PROMOTION="promotion",c=t.TYPE_APPLY="apply"},41533:(e,t,r)=>{"use strict";var n=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function useKitDocumentByType(e){var t=(0,i.default)(),r=(0,o.useMemo)((function(){return e&&t.data?e.getDocumentsByTypes(t.data).sort((function(e,t){return e.order-t.order})):[]}),[e,t.data]);return _objectSpread(_objectSpread({},t),{},{data:r})};var a=n(r(85707)),i=n(r(91437)),o=r(41594);function ownKeys(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(r),!0).forEach((function(t){(0,a.default)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ownKeys(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}},83321:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.useKitFavoritesMutations=function useKitFavoritesMutations(){var e=(0,o.useQueryClient)(),t=(0,n.useCallback)((function(t){var r=t.data,n=r.data.id,o=r.data.is_favorite;e.getQueryData([a.KEY])&&e.setQueryData([a.KEY],(function(e){return e?e.map((function(e){return e.id===n?(e.isFavorite=o,e.clone()):e})):e})),e.getQueryData([i.KEY,n])&&e.setQueryData([i.KEY,n],(function(e){return e.isFavorite=o,e.clone()}))}),[e]),r=(0,o.useMutation)((function(e){return $e.data.create("kits/favorites",{},{id:e})}),{onSuccess:t}),l=(0,o.useMutation)((function(e){return $e.data.delete("kits/favorites",{id:e})}),{onSuccess:t});return{addToFavorites:r,removeFromFavorites:l,isLoading:r.isLoading||l.isLoading}};var n=r(41594),a=r(45075),i=r(70172),o=r(89994)},70172:(e,t,r)=>{"use strict";var n=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.KEY=void 0,t.default=function useKit(e){var t=function usePlaceholderDataCallback(e){var t=(0,u.useQueryClient)();return(0,l.useCallback)((function(){var r,n=null===(r=t.getQueryData(o.KEY))||void 0===r?void 0:r.find((function(t){return t.id===e}));if(n)return n}),[t,e])}(e);return(0,u.useQuery)([c,e],fetchKitItem,{placeholderData:t})};var a=n(r(18821)),i=n(r(41084)),o=r(45075),l=r(41594),u=r(89994),c=t.KEY="kit";function fetchKitItem(e){var t=(0,a.default)(e.queryKey,2),r=(t[0],t[1]);return $e.data.get("kits/index",{id:r},{refresh:!0}).then((function(e){return e.data})).then((function(e){var t=e.data;return i.default.createFromResponse(t)}))}},45075:(e,t,r)=>{"use strict";var n=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.KEY=void 0,t.default=function useKits(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=(0,f.useState)(!1),r=(0,a.default)(t,2),n=r[0],o=r[1],c=(0,f.useState)((function(){return _objectSpread(_objectSpread({ready:!1},m),e)})),s=(0,a.default)(c,2),y=s[0],_=s[1],g=(0,f.useCallback)((function(){return o(!0)}),[o]),b=(0,f.useCallback)((function(){return _(_objectSpread(_objectSpread({ready:!0},m),e))}),[_]),h=(0,d.useQuery)([p],(function(){return function fetchKits(e){return $e.data.get("kits/index",{force:e?1:void 0},{refresh:!0}).then((function(e){return e.data})).then((function(e){return e.data.map((function(e){return l.default.createFromResponse(e)}))}))}(n)})),k=(0,f.useMemo)((function(){return h.data?pipe.apply(void 0,(0,i.default)(Object.values(v)))((0,i.default)(h.data),y):[]}),[h.data,y]),C=(0,u.default)(y.taxonomies),O=(0,f.useMemo)((function(){return!!y.search||!!C.length}),[y]);return(0,f.useEffect)((function(){n&&h.refetch().then((function(){return o(!1)}))}),[n]),_objectSpread(_objectSpread({},h),{},{data:k,queryParams:y,setQueryParams:_,clearQueryParams:b,forceRefetch:g,isFilterActive:O})},t.defaultQueryParams=void 0;var a=n(r(18821)),i=n(r(10906)),o=n(r(85707)),l=n(r(41084)),u=n(r(59979)),c=r(68145),s=r(31627),d=r(89994),f=r(41594);function ownKeys(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(r),!0).forEach((function(t){(0,o.default)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ownKeys(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var p=t.KEY="kits",m=t.defaultQueryParams={favorite:!1,search:"",taxonomies:c.TaxonomyTypes.reduce((function(e,t){var r=t.key;return _objectSpread(_objectSpread({},e),{},(0,o.default)({},r,[]))}),{}),order:{direction:"asc",by:"featuredIndex"},referrer:null},v={favoriteFilter:function favoriteFilter(e,t){return t.favorite?e.filter((function(e){return e.isFavorite})):e},searchFilter:function searchFilter(e,t){return t.search?e.filter((function(e){var r=[].concat((0,i.default)(e.keywords),(0,i.default)(e.taxonomies),[e.title]),n=t.search.toLowerCase();return r.some((function(e){return e.toLowerCase().includes(n)}))})):e},taxonomiesFilter:function taxonomiesFilter(e,t){var r=Object.keys(t.taxonomies).filter((function(e){return t.taxonomies[e].length}));return r.length?e.filter((function(e){return r.some((function(r){return(0,s.isKitInTaxonomy)(e,r,t.taxonomies[r])}))})):e},sort:function sort(e,t){var r=t.order;return e.sort((function(e,t){return"asc"===r.direction?e[r.by]-t[r.by]:t[r.by]-e[r.by]}))}};function pipe(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return function(e){for(var r=arguments.length,n=new Array(r>1?r-1:0),a=1;a<r;a++)n[a-1]=arguments[a];return t.reduce((function(e,t){return t.apply(void 0,[e].concat(n))}),e)}}},59979:(e,t,r)=>{"use strict";var n=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function useSelectedTaxonomies(e){return(0,i.useMemo)((function(){return Object.values(e).reduce((function(e,t){return[].concat((0,a.default)(e),(0,a.default)(t))}))}),[e])};var a=n(r(10906)),i=r(41594)},29217:(e,t,r)=>{"use strict";var n=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.KEY=void 0,t.default=function useTaxonomies(){var e=(0,u.useState)(!1),t=(0,i.default)(e,2),r=t[0],n=t[1],a=(0,u.useCallback)((function(){return n(!0)}),[n]),s=(0,l.useQuery)([c],(function(){return function fetchTaxonomies(e){return $e.data.get("kit-taxonomies/index",{force:e?1:void 0},{refresh:!0}).then((function(e){return e.data})).then((function(e){return e.data.map((function(e){return o.default.createFromResponse(e)}))}))}(r)}));return(0,u.useEffect)((function(){r&&s.refetch().then((function(){return n(!1)}))}),[r]),_objectSpread(_objectSpread({},s),{},{forceRefetch:a})};var a=n(r(85707)),i=n(r(18821)),o=n(r(68145)),l=r(89994),u=r(41594);function ownKeys(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(r),!0).forEach((function(t){(0,a.default)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ownKeys(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var c=t.KEY="tags"},32195:(e,t,r)=>{"use strict";var n=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=n(r(18821)),i=n(r(39805)),o=n(r(40989));t.default=function(){return(0,o.default)((function BaseModel(){(0,i.default)(this,BaseModel)}),[{key:"clone",value:function clone(){var e=this,t=new this.constructor;return Object.keys(this).forEach((function(r){t[r]=e[r]})),t}},{key:"init",value:function init(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object.entries(t).forEach((function(t){var r=(0,a.default)(t,2),n=r[0],i=r[1];e[n]=i})),this}}])}()},55942:(e,t,r)=>{"use strict";var n=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=n(r(39805)),i=n(r(40989)),o=n(r(15118)),l=n(r(29402)),u=n(r(87861)),c=n(r(85707)),s=n(r(32195));function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!e})()}t.default=function(e){function ContentType(){var e;(0,a.default)(this,ContentType);for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];return e=function _callSuper(e,t,r){return t=(0,l.default)(t),(0,o.default)(e,_isNativeReflectConstruct()?Reflect.construct(t,r||[],(0,l.default)(e).constructor):t.apply(e,r))}(this,ContentType,[].concat(r)),(0,c.default)(e,"id",""),(0,c.default)(e,"label",""),(0,c.default)(e,"documentTypes",[]),(0,c.default)(e,"documents",[]),(0,c.default)(e,"order",0),e}return(0,u.default)(ContentType,e),(0,i.default)(ContentType,null,[{key:"createFromResponse",value:function createFromResponse(e){return(new ContentType).init({id:e.id,label:e.label,documentTypes:e.doc_types,order:e.order,documents:[]})}}])}(s.default)},21243:(e,t,r)=>{"use strict";var n=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=n(r(39805)),i=n(r(40989)),o=n(r(15118)),l=n(r(29402)),u=n(r(87861)),c=n(r(85707)),s=n(r(32195));function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!e})()}t.default=function(e){function Document(){var e;(0,a.default)(this,Document);for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];return e=function _callSuper(e,t,r){return t=(0,l.default)(t),(0,o.default)(e,_isNativeReflectConstruct()?Reflect.construct(t,r||[],(0,l.default)(e).constructor):t.apply(e,r))}(this,Document,[].concat(r)),(0,c.default)(e,"id",""),(0,c.default)(e,"title",""),(0,c.default)(e,"documentType",""),(0,c.default)(e,"thumbnailUrl",""),(0,c.default)(e,"previewUrl",""),e}return(0,u.default)(Document,e),(0,i.default)(Document,null,[{key:"createFromResponse",value:function createFromResponse(e){return(new Document).init({id:e.id,title:e.title,documentType:e.doc_type,thumbnailUrl:e.thumbnail_url,previewUrl:e.preview_url})}}])}(s.default)},41084:(e,t,r)=>{"use strict";var n=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=n(r(39805)),i=n(r(40989)),o=n(r(15118)),l=n(r(29402)),u=n(r(87861)),c=n(r(85707)),s=n(r(32195)),d=n(r(21243));function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!e})()}t.default=function(e){function Kit(){var e;(0,a.default)(this,Kit);for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];return e=function _callSuper(e,t,r){return t=(0,l.default)(t),(0,o.default)(e,_isNativeReflectConstruct()?Reflect.construct(t,r||[],(0,l.default)(e).constructor):t.apply(e,r))}(this,Kit,[].concat(r)),(0,c.default)(e,"id",""),(0,c.default)(e,"title",""),(0,c.default)(e,"description",""),(0,c.default)(e,"isFavorite",!1),(0,c.default)(e,"thumbnailUrl",null),(0,c.default)(e,"previewUrl",""),(0,c.default)(e,"accessLevel",0),(0,c.default)(e,"trendIndex",null),(0,c.default)(e,"popularityIndex",null),(0,c.default)(e,"featuredIndex",null),(0,c.default)(e,"createdAt",null),(0,c.default)(e,"updatedAt",null),(0,c.default)(e,"keywords",[]),(0,c.default)(e,"taxonomies",[]),(0,c.default)(e,"documents",[]),e}return(0,u.default)(Kit,e),(0,i.default)(Kit,[{key:"getDocumentsByTypes",value:function getDocumentsByTypes(e){var t=this;return e.map((function(e){return(e=e.clone()).documents=t.documents.filter((function(t){return e.documentTypes.includes(t.documentType)})),e}))}}],[{key:"createFromResponse",value:function createFromResponse(e){return(new Kit).init({id:e.id,title:e.title,description:e.description,isFavorite:e.is_favorite,thumbnailUrl:e.thumbnail_url,previewUrl:e.preview_url,accessLevel:e.access_level,accessTier:e.access_tier,trendIndex:e.trend_index,popularityIndex:e.popularity_index,featuredIndex:e.featured_index,createdAt:e.created_at?new Date(e.created_at):null,updatedAt:e.updated_at?new Date(e.updated_at):null,keywords:e.keywords,taxonomies:e.taxonomies,documents:e.documents?e.documents.map((function(e){return d.default.createFromResponse(e)})):[]})}}])}(s.default)},31627:(e,t,r)=>{"use strict";var n=r(96784),a=r(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.TierToKeyMap=t.PromotionChipText=t.OldPlanTexts=t.NewPlanTexts=void 0,t.getTaxonomyFilterItems=function getTaxonomyFilterItems(e){var t=(e=e?(0,i.default)(e):[]).reduce((function(e,t){var r=function _getFormattedTaxonomyItem(e){if(e.type===c.SUBSCRIPTION_PLAN)return function _getFormattedSubscriptionByPlanTaxonomy(e){var t=new c.default;return t.id=function _getFormattedTaxonomyId(e){return _[e]||e}(function _getTaxonomyIdByText(e){return Object.keys(v).find((function(t){return v[t]===e}))}(e.text)),t.text=y[t.id]||e.text,t.type=e.type,t}(e);return e}(t),n=c.TaxonomyTypes.find((function(e){return e.key===r.type}));return n?(e[r.type]||(e[r.type]=function _objectSpread(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(r),!0).forEach((function(t){(0,o.default)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ownKeys(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}({},n)),e[r.type].data.find((function(e){return e.text===r.text}))||e[r.type].data.push(r),e):e}),{});return c.TaxonomyTypes.reduce((function(e,r){var n;return null!==(n=t[r.key])&&void 0!==n&&null!==(n=n.data)&&void 0!==n&&n.length&&e.push(t[r.key]),e}),[])},t.isKitInTaxonomy=function isKitInTaxonomy(e,t,r){return c.SUBSCRIPTION_PLAN===t?r.includes(g[e.accessTier]):r.some((function(t){return e.taxonomies.includes(t)}))};var i=n(r(10906)),o=n(r(85707)),l=r(87437),u=r(12470),c=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=a(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&{}.hasOwnProperty.call(e,o)){var l=i?Object.getOwnPropertyDescriptor(e,o):null;l&&(l.get||l.set)?Object.defineProperty(n,o,l):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}(r(68145));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}function ownKeys(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}var s="free",d="essential",f="advanced",p="expert,",m="agency",v=t.OldPlanTexts=(0,o.default)((0,o.default)((0,o.default)((0,o.default)((0,o.default)({},s,(0,u.__)("Free","elementor")),"pro",(0,u.__)("Pro","elementor")),f,(0,u.__)("Advanced","elementor")),p,(0,u.__)("Expert","elementor")),m,(0,u.__)("Agency","elementor")),y=t.NewPlanTexts=(0,o.default)((0,o.default)((0,o.default)({},s,(0,u.__)("Free","elementor")),d,(0,u.__)("Essential","elementor")),f,(0,u.__)("Advanced & Higher","elementor")),_=(0,o.default)((0,o.default)((0,o.default)({},"pro",d),p,f),m,f),g=t.TierToKeyMap=(0,o.default)((0,o.default)((0,o.default)((0,o.default)((0,o.default)({},l.TIERS.free,s),l.TIERS.essential,d),l.TIERS["essential-oct2023"],f),l.TIERS.expert,f),l.TIERS.agency,f);t.PromotionChipText=(0,o.default)((0,o.default)((0,o.default)({},s,(0,u.__)("Free","elementor")),d,(0,u.__)("Essential","elementor")),f,(0,u.__)("Advanced","elementor"))},68145:(e,t,r)=>{"use strict";var n=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.TaxonomyTypes=t.TAG=t.SUBSCRIPTION_PLAN=t.FEATURE=t.CATEGORY=void 0;var a=n(r(39805)),i=n(r(40989)),o=n(r(15118)),l=n(r(29402)),u=n(r(87861)),c=n(r(85707)),s=n(r(32195)),d=r(12470);function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!e})()}t.CATEGORY="categories",t.TAG="tags",t.FEATURE="features";var f=t.SUBSCRIPTION_PLAN="subscription_plans";t.TaxonomyTypes=[{key:"categories",label:(0,d.__)("Categories","elementor"),isOpenByDefault:!0,data:[]},{key:"tags",label:(0,d.__)("Tags","elementor"),data:[]},{key:"features",label:(0,d.__)("Features","elementor"),data:[]},{key:f,label:(0,d.__)("Kits by plan","elementor"),data:[]}],t.default=function(e){function Taxonomy(){var e;(0,a.default)(this,Taxonomy);for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];return e=function _callSuper(e,t,r){return t=(0,l.default)(t),(0,o.default)(e,_isNativeReflectConstruct()?Reflect.construct(t,r||[],(0,l.default)(e).constructor):t.apply(e,r))}(this,Taxonomy,[].concat(r)),(0,c.default)(e,"text",""),(0,c.default)(e,"type","tag"),(0,c.default)(e,"id",null),e}return(0,u.default)(Taxonomy,e),(0,i.default)(Taxonomy,null,[{key:"createFromResponse",value:function createFromResponse(e){return(new Taxonomy).init({text:e.text,type:e.type,id:e.id||null})}}])}(s.default)},94881:(e,t,r)=>{"use strict";var n=r(12470).__,a=r(62688),i=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=Favorites;var o=i(r(41594)),l=i(r(8741)),u=i(r(32965)),c=r(83040);function Favorites(e){var t=(0,c.useNavigate)(),r=o.default.createElement(u.default,{title:n("No favorites here yet...","elementor"),description:n("Use the heart icon to save kits that inspire you. You'll be able to find them here.","elementor"),button:{text:n("Continue browsing.","elementor"),action:function action(){return t("/kit-library")}}});return o.default.createElement(l.default,{path:e.path,initialQueryParams:{favorite:!0},renderNoResultsComponent:function renderNoResultsComponent(e){var t=e.defaultComponent;return e.isFilterActive?t:r}})}Favorites.propTypes={path:a.string}},59679:(e,t,r)=>{"use strict";var n=r(12470).__,a=r(62688),i=r(96784),o=r(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=IndexHeader;var l=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=o(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&{}.hasOwnProperty.call(e,i)){var l=a?Object.getOwnPropertyDescriptor(e,i):null;l&&(l.get||l.set)?Object.defineProperty(n,i,l):n[i]=e[i]}return n.default=e,r&&r.set(e,n),n}(r(41594)),u=i(r(18821)),c=i(r(80876)),s=r(57401),d=r(83040),f=i(r(34744)),p=r(3073);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}function IndexHeader(e){var t,r,a=(0,d.useNavigate)(),i=(0,l.useState)(!1),o=(0,u.default)(i,2),m=o[0],v=o[1],y=(0,l.useRef)(),_=function eventTracking(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"click",n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;(0,p.appsEventTrackingDispatch)(e,{element:t,event_type:r,page_source:"home page",element_position:"app_header",modal_type:n})},g=elementorAppConfig.user.is_administrator||null!==(t=null===(r=elementorAppConfig.user.restrictions)||void 0===r?void 0:r.includes("json-upload"))&&void 0!==t&&t,b=(0,l.useMemo)((function(){return[{id:"info",text:n("Info","elementor"),hideText:!0,icon:"eicon-info-circle-o",onClick:function onClick(){_("kit-library/seek-more-info"),v(!0)}},{id:"refetch",text:n("Refetch","elementor"),hideText:!0,icon:"eicon-sync ".concat(e.isFetching?"eicon-animation-spin":""),onClick:function onClick(){_("kit-library/refetch"),e.refetch()}},g&&{id:"import",text:n("Import","elementor"),hideText:!0,icon:"eicon-upload-circle-o",elRef:y,onClick:function onClick(){_("kit-library/kit-import"),a("/import?referrer=kit-library")}}]}),[e.isFetching,e.refetch,g]);return l.default.createElement(l.default.Fragment,null,l.default.createElement(c.default,{buttons:b}),l.default.createElement(f.default,{targetRef:y,wrapperClass:"e-kit-library__tooltip"},n("Import Kit","elementor")),l.default.createElement(s.ModalProvider,{title:n("Welcome to the Library","elementor"),show:m,setShow:v,onOpen:function onOpen(){return _("kit-library/modal-open",null,"load","info")},onClose:function onClose(e){return function onClose(e){var t=e.target.classList.contains("eps-modal__overlay")?"overlay":"x";_("kit-library/modal-close",t,null,"info")}(e)}},l.default.createElement("div",{className:"e-kit-library-header-info-modal-container"},l.default.createElement(s.Heading,{tag:"h3",variant:"h3"},n("What's a Website Kit?","elementor")),l.default.createElement(s.Text,null,n("A Website Kit is full, ready-made design that you can apply to your site. It includes all the pages, parts, settings and content that you'd expect in a fully functional website.","elementor"))),l.default.createElement("div",{className:"e-kit-library-header-info-modal-container"},l.default.createElement(s.Heading,{tag:"h3",variant:"h3"},n("What's going on in the Kit Library?","elementor")),l.default.createElement(s.Text,null,n("Search & filter for kits by category and tags, or browse through individual kits to see what's inside.","elementor"),l.default.createElement("br",null),n("Once you've picked a winner, apply it to your site!","elementor"))),l.default.createElement("div",null,l.default.createElement(s.Heading,{tag:"h3",variant:"h3"},n("Happy browsing!","elementor")),l.default.createElement(s.Text,null,l.default.createElement(s.Button,{url:"https://go.elementor.com/app-kit-library-how-to-use-kits/",target:"_blank",rel:"noreferrer",text:n("Learn more","elementor"),color:"link",onClick:function onClick(){_("kit-library/seek-more-info","text link",null,"info")}})," ",n("about using templates","elementor")))))}r(47453),IndexHeader.propTypes={refetch:a.func.isRequired,isFetching:a.bool}},17348:(e,t,r)=>{"use strict";var n=r(62688),a=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=IndexSidebar;var i=a(r(41594)),o=r(57401),l=r(3073);function IndexSidebar(e){return i.default.createElement(i.default.Fragment,null,e.menuItems.map((function(e){return i.default.createElement(o.MenuItem,{key:e.label,text:e.label,className:"eps-menu-item__link ".concat(e.isActive?"eps-menu-item--active":""),icon:e.icon,url:e.url,onClick:function onClick(){return function eventTracking(e,t,r){var n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"click";return(0,l.appsEventTrackingDispatch)(e,{category:t,source:r,element_location:"app_sidebar",event_type:n})}(e.trackEventData.command,e.trackEventData.category,"home page")}})})),e.tagsFilterSlot)}IndexSidebar.propTypes={tagsFilterSlot:n.node,menuItems:n.arrayOf(n.shape({label:n.string,icon:n.string,isActive:n.bool,url:n.string}))}},8741:(e,t,r)=>{"use strict";var n=r(12470).__,a=r(62688),i=r(96784),o=r(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=Index;var l=_interopRequireWildcard(r(41594)),u=i(r(10906)),c=i(r(18821)),s=i(r(85707)),d=i(r(25368)),f=i(r(84051)),p=i(r(32965)),m=i(r(2869)),v=i(r(59679)),y=i(r(17348)),_=i(r(68843)),g=i(r(86845)),b=i(r(3471)),h=i(r(78013)),k=i(r(14025)),C=i(r(33554)),O=_interopRequireWildcard(r(45075)),P=i(r(80791)),w=i(r(29217)),E=r(57401),T=r(75750),j=r(83040),x=r(3073);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=o(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&{}.hasOwnProperty.call(e,i)){var l=a?Object.getOwnPropertyDescriptor(e,i):null;l&&(l.get||l.set)?Object.defineProperty(n,i,l):n[i]=e[i]}return n.default=e,r&&r.set(e,n),n}function ownKeys(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(r),!0).forEach((function(t){(0,s.default)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ownKeys(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Index(e){(0,P.default)({title:n("Kit Library","elementor")});var t=function useMenuItems(e){return(0,l.useMemo)((function(){var t=e.replace("/","");return[{label:n("All Website Kits","elementor"),icon:"eicon-filter",isActive:!t,url:"/kit-library",trackEventData:{command:"kit-library/select-organizing-category",category:"all"}},{label:n("Favorites","elementor"),icon:"eicon-heart-o",isActive:"favorites"===t,url:"/kit-library/favorites",trackEventData:{command:"kit-library/select-organizing-category",category:"favorites"}}]}),[e])}(e.path),r=(0,O.default)(e.initialQueryParams),a=r.data,i=r.isSuccess,o=r.isLoading,R=r.isFetching,S=r.isError,M=r.queryParams,N=r.setQueryParams,W=r.clearQueryParams,q=r.forceRefetch,D=r.isFilterActive;!function useRouterQueryParams(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],n=(0,j.useLocation)(),a=(0,T.useLastFilterContext)().setLastFilter;(0,l.useEffect)((function(){var t=Object.fromEntries(Object.entries(e).filter((function(e){var t=(0,c.default)(e,2),n=t[0],a=t[1];return!r.includes(n)&&a})));a(t),history.replaceState(null,"",decodeURI("#".concat(wp.url.addQueryArgs(n.pathname.split("?")[0]||"/",t))))}),[e]),(0,l.useEffect)((function(){var e=Object.keys(O.defaultQueryParams).reduce((function(e,t){var r=wp.url.getQueryArg(n.pathname,t);return r?_objectSpread(_objectSpread({},e),{},(0,s.default)({},t,r)):e}),{});t((function(t){return _objectSpread(_objectSpread(_objectSpread({},t),e),{},{taxonomies:_objectSpread(_objectSpread({},t.taxonomies),e.taxonomies),ready:!0})}))}),[])}(M,N,["ready"].concat((0,u.default)(Object.keys(e.initialQueryParams))));var I=(0,w.default)(),B=I.data,F=I.forceRefetch,L=I.isFetching,K=function useTaxonomiesSelection(e){return[(0,l.useCallback)((function(t,r){return e((function(e){var n=_objectSpread({},e.taxonomies);return n[t]=r(e.taxonomies[t]),_objectSpread(_objectSpread({},e),{},{taxonomies:n})}))}),[e]),(0,l.useCallback)((function(t){return e((function(e){var r=Object.entries(e.taxonomies).reduce((function(e,r){var n=(0,c.default)(r,2),a=n[0],i=n[1];return _objectSpread(_objectSpread({},e),{},(0,s.default)({},a,i.filter((function(e){return e!==t}))))}),{});return _objectSpread(_objectSpread({},e),{},{taxonomies:r})}))}),[e])]}(N),A=(0,c.default)(K,2),U=A[0],H=A[1],Q=function eventTracking(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,i=arguments.length>5&&void 0!==arguments[5]?arguments[5]:null,o=arguments.length>6&&void 0!==arguments[6]?arguments[6]:"click";(0,x.appsEventTrackingDispatch)(e,{page_source:"home page",element_position:t,search_term:r,sort_direction:n,sort_type:a,event_type:o,action:i})};return l.default.createElement(g.default,{sidebar:l.default.createElement(y.default,{tagsFilterSlot:l.default.createElement(C.default,{selected:M.taxonomies,onSelect:U,taxonomies:B,category:e.path}),menuItems:t}),header:l.default.createElement(v.default,{refetch:function refetch(){q(),F()},isFetching:R||L})},l.default.createElement("div",{className:"e-kit-library__index-layout-container"},l.default.createElement(E.Grid,{container:!0,className:"e-kit-library__index-layout-heading"},l.default.createElement(E.Grid,{item:!0,className:"e-kit-library__index-layout-heading-search"},l.default.createElement(h.default,{placeholder:n("Search all Website Kits...","elementor"),value:M.search,onChange:function onChange(e){N((function(t){return _objectSpread(_objectSpread({},t),{},{search:e})})),Q("kit-library/kit-free-search","top_area_search",e,null,null,null,"search")}}),D&&l.default.createElement(m.default,{queryParams:M,resultCount:a.length||0,onClear:W,onRemoveTag:H})),l.default.createElement(E.Grid,{item:!0,className:"e-kit-library__index-layout-heading-sort"},l.default.createElement(k.default,{options:[{label:n("Featured","elementor"),value:"featuredIndex",defaultOrder:"asc",orderDisabled:!0},{label:n("New","elementor"),value:"createdAt",defaultOrder:"desc"},{label:n("Popular","elementor"),value:"popularityIndex",defaultOrder:"desc"},{label:n("Trending","elementor"),value:"trendIndex",defaultOrder:"desc"}],value:M.order,onChange:function onChange(e){return N((function(t){return _objectSpread(_objectSpread({},t),{},{order:e})}))},onChangeSortDirection:function onChangeSortDirection(e){return Q("kit-library/change-sort-direction","top_area_sort",null,e)},onChangeSortValue:function onChangeSortValue(e){return Q("kit-library/change-sort-value","top_area_sort",null,null,e)},onSortSelectOpen:function onSortSelectOpen(){return Q("kit-library/change-sort-type","top_area_sort",null,null,null,"expand")}}))),l.default.createElement(d.default,{className:"e-kit-library__index-layout-main"},l.default.createElement(l.default.Fragment,null,o&&l.default.createElement(b.default,null),S&&l.default.createElement(p.default,{title:n("Something went wrong.","elementor"),description:n("Nothing to worry about, use 🔄 on the top corner to try again. If the problem continues, head over to the Help Center.","elementor"),button:{text:n("Learn More","elementor"),url:"https://go.elementor.com/app-kit-library-error/",target:"_blank"}}),i&&0<a.length&&M.ready&&l.default.createElement(_.default,{data:a,queryParams:M,source:e.path}),i&&0===a.length&&M.ready&&e.renderNoResultsComponent({defaultComponent:l.default.createElement(p.default,{title:n("No results matched your search.","elementor"),description:n("Try different keywords or ","elementor"),button:{text:n("Continue browsing.","elementor"),action:W,category:e.path}}),isFilterActive:D}),l.default.createElement(f.default,{category:e.path})))))}r(82101),Index.propTypes={path:a.string,initialQueryParams:a.object,renderNoResultsComponent:a.func},Index.defaultProps={initialQueryParams:{},renderNoResultsComponent:function renderNoResultsComponent(e){return e.defaultComponent}}},26883:(e,t,r)=>{"use strict";var n=r(12470).__,a=r(62688),i=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=OverviewContentGroupItem;var o=i(r(41594)),l=i(r(21243)),u=r(57401),c=r(3073);function OverviewContentGroupItem(e){return o.default.createElement(u.Card,null,o.default.createElement(u.CardHeader,null,o.default.createElement(u.Heading,{tag:"h3",title:e.document.title,variant:"h5",className:"eps-card__headline"},e.document.title)),o.default.createElement(u.CardBody,null,o.default.createElement(u.CardImage,{alt:e.document.title,src:e.document.thumbnailUrl||""},e.document.previewUrl&&o.default.createElement(u.CardOverlay,null,o.default.createElement(u.Button,{className:"e-kit-library__kit-item-overlay-overview-button",text:n("View Demo","elementor"),icon:"eicon-preview-medium",url:"/kit-library/preview/".concat(e.kitId,"?document_id=").concat(e.document.id),onClick:function onClick(){return function eventTracking(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"click";(0,c.appsEventTrackingDispatch)(t,{kit_name:e.kitTitle,document_type:e.groupData.id,document_name:"".concat(e.groupData.label,"-").concat(e.document.title),page_source:"overview",element_position:"content_overview",event_type:r})}("kit-library/view-demo-part")}})))))}OverviewContentGroupItem.propTypes={document:a.instanceOf(l.default).isRequired,kitId:a.string.isRequired,kitTitle:a.string.isRequired,groupData:a.shape({label:a.string,id:a.string}).isRequired}},2163:(e,t,r)=>{"use strict";var n=r(62688),a=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=OverviewContentGroup;var i=a(r(41594)),o=a(r(55942)),l=a(r(26883)),u=r(57401);function OverviewContentGroup(e){var t;return(null===(t=e.contentType)||void 0===t||null===(t=t.documents)||void 0===t?void 0:t.length)<=0?"":i.default.createElement("div",{className:"e-kit-library__content-overview-group-item"},i.default.createElement(u.Heading,{tag:"h3",variant:"h3",className:"e-kit-library__content-overview-group-title"},e.contentType.label),i.default.createElement(u.CssGrid,{spacing:24,colMinWidth:250},e.contentType.documents.map((function(t){return i.default.createElement(l.default,{key:t.id,document:t,kitId:e.kitId,kitTitle:e.kitTitle,groupData:e.contentType})}))))}OverviewContentGroup.propTypes={contentType:n.instanceOf(o.default),kitId:n.string.isRequired,kitTitle:n.string.isRequired}},78396:(e,t,r)=>{"use strict";var n=r(12470).__,a=r(62688),i=r(96784),o=r(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=OverviewSidebar;var l=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=o(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&{}.hasOwnProperty.call(e,i)){var l=a?Object.getOwnPropertyDescriptor(e,i):null;l&&(l.get||l.set)?Object.defineProperty(n,i,l):n[i]=e[i]}return n.default=e,r&&r.set(e,n),n}(r(41594)),u=i(r(18821)),c=i(r(79891)),s=i(r(55942)),d=i(r(25265)),f=i(r(41084)),p=r(57401),m=r(3073);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}function OverviewSidebar(e){var t,r=(0,l.useState)(!0),a=(0,u.default)(r,2),i=a[0],o=a[1];return l.default.createElement("div",{className:"e-kit-library__item-sidebar"},l.default.createElement("div",{className:"e-kit-library__item-sidebar-header"},l.default.createElement(p.Heading,{tag:"h1",variant:"h5",className:"e-kit-library__item-sidebar-header-title"},e.model.title),l.default.createElement(d.default,{isFavorite:e.model.isFavorite,id:e.model.id})),l.default.createElement(p.CardImage,{className:"e-kit-library__item-sidebar-thumbnail",alt:e.model.title,src:e.model.thumbnailUrl||""}),l.default.createElement(p.Text,{className:"e-kit-library__item-sidebar-description"},e.model.description||""),(null===(t=e.groupedKitContent)||void 0===t?void 0:t.length)>0&&e.model.documents.length>0&&l.default.createElement(c.default,{isOpen:i,onChange:o,title:n("WHAT'S INSIDE","elementor"),className:"e-kit-library__item-sidebar-collapse-info",onClick:function onClick(e,t){!function eventTracking(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,i=arguments.length>5&&void 0!==arguments[5]?arguments[5]:"click";"boolean"==typeof a&&(e="kit-library/".concat(a&&a?"collapse":"expand")),(0,m.appsEventTrackingDispatch)(e,{page_source:"overview",element_location:"app_sidebar",kit_name:r,tag:n,section:t,event_type:i})}(null,t,null,null,e)}},e.groupedKitContent.map((function(e){return e.documents<=0?"":l.default.createElement(p.Text,{className:"e-kit-library__item-information-text",key:e.id},e.documents.length," ",e.label)}))))}r(50512),OverviewSidebar.propTypes={model:a.instanceOf(f.default).isRequired,index:a.number,groupedKitContent:a.arrayOf(a.instanceOf(s.default))}},96285:(e,t,r)=>{"use strict";var n=r(12470).__,a=r(62688),i=r(96784),o=r(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=Overview;var l=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=o(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&{}.hasOwnProperty.call(e,i)){var l=a?Object.getOwnPropertyDescriptor(e,i):null;l&&(l.get||l.set)?Object.defineProperty(n,i,l):n[i]=e[i]}return n.default=e,r&&r.set(e,n),n}(r(41594)),u=i(r(25368)),c=i(r(38832)),s=i(r(91289)),d=i(r(86845)),f=i(r(2163)),p=i(r(78396)),m=i(r(70172)),v=i(r(41533)),y=i(r(80791)),_=r(83040),g=r(3073);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}function Overview(e){var t=(0,m.default)(e.id),r=t.data,a=t.isError,i=t.isLoading,o=(0,v.default)(r).data,b=function useHeaderButtons(e,t){var r=(0,_.useNavigate)();return(0,l.useMemo)((function(){return[{id:"view-demo",text:n("View Demo","elementor"),hideText:!1,variant:"outlined",color:"secondary",size:"sm",onClick:function onClick(){(0,g.appsEventTrackingDispatch)("kit-library/view-demo-page",{kit_name:t,page_source:"overview",element_position:"app_header",view_type_clicked:"demo"}),r("/kit-library/preview/".concat(e))},includeHeaderBtnClass:!1}]}),[e])}(e.id,r&&r.title);if((0,y.default)({title:r?"".concat(n("Kit Library","elementor")," | ").concat(r.title):n("Loading...","elementor")}),a)throw new Error;return i?l.default.createElement(c.default,null):l.default.createElement(d.default,{header:l.default.createElement(s.default,{model:r,buttons:b,pageId:"overview"}),sidebar:l.default.createElement(p.default,{model:r,groupedKitContent:o})},o.length>0&&l.default.createElement(u.default,null,o.map((function(t){return l.default.createElement(f.default,{key:t.id,contentType:t,kitId:e.id,kitTitle:r.title})}))))}r(82477),Overview.propTypes={id:a.string}},83862:(e,t,r)=>{"use strict";var n=r(62688),a=r(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.PreviewIframe=PreviewIframe;var i=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=a(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&{}.hasOwnProperty.call(e,o)){var l=i?Object.getOwnPropertyDescriptor(e,o):null;l&&(l.get||l.set)?Object.defineProperty(n,o,l):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}(r(41594)),o=r(57401);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}function PreviewIframe(e){var t=(0,i.useRef)();return(0,i.useEffect)((function(){if(t.current){var r=function listener(){return e.onLoaded()};return t.current.addEventListener("load",r),function(){return t.current&&t.current.removeEventListener("load",r)}}}),[t.current,e.previewUrl]),i.default.createElement(o.Grid,{container:!0,justify:"center",className:"e-kit-library__preview-iframe-container"},i.default.createElement("iframe",{className:"e-kit-library__preview-iframe",src:e.previewUrl,style:e.style,ref:t}))}PreviewIframe.propTypes={previewUrl:n.string.isRequired,style:n.object,onLoaded:n.func},PreviewIframe.defaultProps={style:{width:"100%",height:"100%"},onLoaded:function onLoaded(){}}},26303:(e,t,r)=>{"use strict";var n=r(62688),a=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=PreviewResponsiveControls;var i=a(r(41594)),o=r(53673),l=r(57401);function PreviewResponsiveControls(e){return i.default.createElement(l.Grid,{container:!0,alignItems:"center",justify:"center",className:"e-kit-library__preview-responsive-controls"},o.breakpoints.map((function(t){var r=t.label,n=t.value,a="e-kit-library__preview-responsive-controls-item";return e.active===n&&(a+=" e-kit-library__preview-responsive-controls-item--active"),i.default.createElement(l.Button,{key:n,text:r,hideText:!0,className:a,icon:"eicon-device-".concat(n),onClick:function onClick(){return e.onChange(n)}})})))}r(59955),PreviewResponsiveControls.propTypes={active:n.string,onChange:n.func.isRequired},PreviewResponsiveControls.defaultProps={active:"desktop"}},53673:(e,t,r)=>{"use strict";var n=r(12470).__,a=r(62688),i=r(96784),o=r(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.breakpoints=void 0,t.default=Preview;var l=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=o(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&{}.hasOwnProperty.call(e,i)){var l=a?Object.getOwnPropertyDescriptor(e,i):null;l&&(l.get||l.set)?Object.defineProperty(n,i,l):n[i]=e[i]}return n.default=e,r&&r.set(e,n),n}(r(41594)),u=i(r(18821)),c=i(r(38832)),s=i(r(91289)),d=i(r(86845)),f=i(r(3471)),p=i(r(26303)),m=i(r(70172)),v=i(r(80791)),y=r(83862),_=r(83040),g=r(3073);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}r(6121);var b=t.breakpoints=[{value:"desktop",label:n("Desktop","elementor"),style:{width:"100%",height:"100%"}},{value:"tablet",label:n("Tablet","elementor"),style:{marginBlockStart:"30px",marginBlockEnd:"30px",width:"768px",height:"1024px"}},{value:"mobile",label:n("Mobile","elementor"),style:{marginBlockStart:"30px",marginBlockEnd:"30px",width:"375px",height:"667px"}}];function useHeaderButtons(e,t){var r=(0,_.useNavigate)();return(0,l.useMemo)((function(){return[{id:"overview",text:n("Overview","elementor"),hideText:!1,variant:"outlined",color:"secondary",size:"sm",onClick:function onClick(){!function eventTracking(e,r){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"click";(0,g.appsEventTrackingDispatch)(e,{kit_name:t,element_position:"app_header",page_source:"view demo",view_type_clicked:r,event_type:n})}("kit-library/view-overview-page","overview"),r("/kit-library/overview/".concat(e))},includeHeaderBtnClass:!1}]}),[e])}function Preview(e){var t=(0,m.default)(e.id),r=t.data,a=t.isError,i=t.isLoading,o=(0,l.useState)(!0),h=(0,u.default)(o,2),k=h[0],C=h[1],O=useHeaderButtons(e.id,r&&r.title),P=function usePreviewUrl(e){var t=(0,_.useLocation)();return(0,l.useMemo)((function(){var r,n;if(!e)return null;var a=new URLSearchParams(null===(r=t.pathname.split("?"))||void 0===r?void 0:r[1]).get("document_id"),i="?utm_source=kit-library&utm_medium=wp-dash&utm_campaign=preview",o=e.previewUrl?e.previewUrl+i:e.previewUrl;if(!a)return o;var l=(null===(n=e.documents.find((function(e){return e.id===parseInt(a)})))||void 0===n?void 0:n.previewUrl)||o;return l?l+i:l}),[t,e])}(r),w=(0,l.useState)("desktop"),E=(0,u.default)(w,2),T=E[0],j=E[1],x=(0,l.useMemo)((function(){return b.find((function(e){return e.value===T})).style}),[T]),R=function onChange(e){j(e),function eventTracking(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"click";(0,g.appsEventTrackingDispatch)(e,{kit_name:r.title,page_source:"view demo",layout:t,element_position:n,event_type:a})}("kit-library/responsive-controls",e,"app_header")};if((0,v.default)({title:r?"".concat(n("Kit Library","elementor")," | ").concat(r.title):n("Loading...","elementor")}),a)throw new Error;return i?l.default.createElement(c.default,null):l.default.createElement(d.default,{header:l.default.createElement(s.default,{model:r,buttons:O,centerColumn:l.default.createElement(p.default,{active:T,onChange:function onChange(e){return R(e)},kitName:r.title}),pageId:"demo"})},k&&l.default.createElement(f.default,{className:"e-kit-library__preview-loader"}),P&&l.default.createElement(y.PreviewIframe,{previewUrl:P,style:x,onLoaded:function onLoaded(){return C(!1)}}))}Preview.propTypes={id:a.string}},8203:(e,t,r)=>{"use strict";var n=r(12470).__,a=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=a(r(41594)),o=r(57401);r(47749);t.default=i.default.memo((function NewPageKitListItem(){return i.default.createElement(o.Card,{className:"e-onboarding__kit-library-card e-kit-library__kit-item"},i.default.createElement(o.CardHeader,null,i.default.createElement(o.Heading,{tag:"h3",title:n("Blank Canvas","elementor"),variant:"h5",className:"eps-card__headline"},n("Blank Canvas","elementor"))),i.default.createElement(o.CardBody,null,i.default.createElement(o.CardImage,{alt:n("Blank Canvas","elementor"),src:elementorCommon.config.urls.assets+"images/app/onboarding/Blank_Preview.jpg"||0},i.default.createElement(o.CardOverlay,null,i.default.createElement(o.Grid,{container:!0,direction:"column",className:"e-kit-library__kit-item-overlay"},i.default.createElement(o.Button,{className:"e-kit-library__kit-item-overlay-overview-button",text:n("Create New Elementor Page","elementor"),icon:"eicon-single-page",url:elementorAppConfig.onboarding.urls.createNewPage}))))))}))},87437:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isTierAtLeast=t.TIERS_PRIORITY=t.TIERS=void 0;var r=t.TIERS_PRIORITY=Object.freeze(["free","essential","essential-oct2023","advanced","expert","agency"]);t.TIERS=Object.freeze(r.reduce((function(e,t){return e[t]=t,e}),{})),t.isTierAtLeast=function isTierAtLeast(e,t){var n=r.indexOf(e),a=r.indexOf(t);return-1!==n&&-1!==a&&n>=a}}}]);